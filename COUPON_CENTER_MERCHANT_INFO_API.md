# 优惠券中心商家信息API文档

## 📋 API概述

优惠券中心API现已支持商家名称和商家Logo展示，用户可以清楚地识别优惠券的来源商家。

## 🔗 API接口

### 获取优惠券中心列表

**接口地址**: `GET /api/v1/user/takeout/coupons/center`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10 |
| category | string | 否 | 分类筛选，默认为空（全部） |

**请求头**:
```
Authorization: Bearer {user_jwt_token}
Content-Type: application/json
```

**请求示例**:
```bash
GET /api/v1/user/takeout/coupons/center?page=1&page_size=10&category=food
```

## 📤 响应格式

### 成功响应

**HTTP状态码**: 200

**响应结构**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 15,
    "list": [
      {
        "id": 1,
        "promotion_id": 0,
        "merchant_id": 1,
        "merchant_name": "美味餐厅",           // ✅ 商家名称
        "merchant_logo": "https://example.com/logo.jpg", // ✅ 商家Logo
        "name": "满50减10优惠券",
        "description": "满50元减10元，适用于所有商品",
        "type": 1,
        "type_text": "满减券",
        "amount": 10.0,
        "min_order_amount": 50.0,
        "max_discount_amount": 10.0,
        "apply_to_all": true,
        "apply_to_categories": "",
        "apply_to_foods": "",
        "exclude_foods": "",
        "user_level_limit": 0,
        "per_user_limit": 1,
        "daily_limit": 100,
        "total_limit": 1000,
        "start_time": "2025-01-01T00:00:00Z",
        "end_time": "2025-12-31T23:59:59Z",
        "status": 1,
        "status_text": "已发布",
        "created_at": "2025-01-01T10:00:00Z",
        "can_claim": true,                     // ✅ 是否可领取
        "claim_status_text": "立即领取"        // ✅ 领取状态文本
      }
    ]
  }
}
```

### 字段说明

#### 商家信息字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| merchant_id | int64 | 商家ID |
| merchant_name | string | 商家名称，获取失败时显示"未知商家" |
| merchant_logo | string | 商家Logo URL，获取失败时为空字符串 |

#### 领取状态字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| can_claim | bool | 当前用户是否可以领取该优惠券 |
| claim_status_text | string | 领取状态描述文本 |

#### 领取状态说明
| 状态文本 | 说明 |
|----------|------|
| "立即领取" | 用户可以领取该优惠券 |
| "已领取" | 用户已经领取过该优惠券 |
| "已领完" | 优惠券已达到发放上限 |

## 🚫 错误响应

### 401 未授权
```json
{
  "code": 401,
  "message": "用户身份验证失败",
  "data": null
}
```

### 500 服务器错误
```json
{
  "code": 500,
  "message": "获取优惠券列表失败",
  "data": null
}
```

## 🔧 技术实现

### 1. 批量查询优化
- 收集所有优惠券涉及的商家ID
- 批量获取商家信息，避免N+1查询问题
- 使用缓存机制提升性能

### 2. 错误处理
- 商家信息获取失败时使用默认值
- 不因商家信息问题影响整体API响应
- 详细的日志记录便于问题排查

### 3. 数据过滤
- 只返回已发布状态的优惠券
- 过滤已领完的优惠券
- 按创建时间倒序排列

## 📱 前端集成

### JavaScript示例
```javascript
// 获取优惠券中心列表
async function getCouponCenter(page = 1, pageSize = 10) {
  try {
    const response = await fetch('/api/v1/user/takeout/coupons/center?page=' + page + '&page_size=' + pageSize, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + userToken,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (data.code === 0) {
      // 处理优惠券列表
      data.data.list.forEach(coupon => {
        console.log('商家:', coupon.merchant_name);
        console.log('Logo:', coupon.merchant_logo);
        console.log('优惠券:', coupon.name);
        console.log('可领取:', coupon.can_claim);
      });
    }
  } catch (error) {
    console.error('获取优惠券中心失败:', error);
  }
}
```

### Vue.js示例
```vue
<template>
  <div class="coupon-center">
    <div v-for="coupon in coupons" :key="coupon.id" class="coupon-item">
      <div class="merchant-info">
        <img :src="coupon.merchant_logo" :alt="coupon.merchant_name" class="merchant-logo">
        <span class="merchant-name">{{ coupon.merchant_name }}</span>
      </div>
      <div class="coupon-info">
        <h3>{{ coupon.name }}</h3>
        <p>{{ coupon.description }}</p>
        <button 
          :disabled="!coupon.can_claim" 
          @click="claimCoupon(coupon.id)"
        >
          {{ coupon.claim_status_text }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      coupons: []
    }
  },
  async mounted() {
    await this.loadCoupons();
  },
  methods: {
    async loadCoupons() {
      // 调用API获取优惠券列表
      const response = await this.$http.get('/api/v1/user/takeout/coupons/center');
      this.coupons = response.data.list;
    }
  }
}
</script>
```

## 🧪 测试验证

### 1. 基本功能测试
- ✅ API返回正确的HTTP状态码
- ✅ 响应包含商家名称和Logo
- ✅ 领取状态正确显示
- ✅ 分页功能正常

### 2. 边界情况测试
- ✅ 商家信息获取失败时的默认值处理
- ✅ 空列表的正确响应
- ✅ 无效参数的错误处理

### 3. 性能测试
- ✅ 批量查询避免N+1问题
- ✅ 响应时间在可接受范围内

## 📝 更新日志

### v1.1.0 (2025-01-20)
- ✅ 新增商家名称和Logo字段
- ✅ 优化批量查询性能
- ✅ 完善错误处理机制
- ✅ 更新API文档

### v1.0.0 (2025-01-01)
- 基础优惠券中心功能
- 优惠券列表展示
- 领取状态管理
