<template>
  <div class="coupon-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>优惠券中心</h1>
      <p>精选优质商家优惠券</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <uni-load-more status="loading" />
    </div>

    <!-- 优惠券列表 -->
    <div v-else class="coupon-list">
      <div 
        v-for="coupon in coupons" 
        :key="coupon.id" 
        class="coupon-item"
        @click="handleCouponClick(coupon)"
      >
        <!-- 商家信息 -->
        <div class="merchant-info">
          <image 
            v-if="coupon.merchant_logo" 
            :src="coupon.merchant_logo" 
            class="merchant-logo"
            mode="aspectFill"
            @error="handleImageError"
          />
          <div v-else class="merchant-logo-placeholder">
            <text class="placeholder-text">店</text>
          </div>
          <text class="merchant-name">{{ coupon.merchant_name || '未知商家' }}</text>
        </div>

        <!-- 优惠券内容 -->
        <div class="coupon-content">
          <div class="coupon-main">
            <div class="coupon-amount">
              <text class="amount-symbol">¥</text>
              <text class="amount-value">{{ coupon.amount }}</text>
            </div>
            <div class="coupon-info">
              <text class="coupon-name">{{ coupon.name }}</text>
              <text class="coupon-condition">满{{ coupon.min_order_amount }}元可用</text>
              <text class="coupon-time">{{ formatTime(coupon.end_time) }}到期</text>
            </div>
          </div>

          <!-- 领取按钮 -->
          <div class="coupon-action">
            <button 
              :class="['claim-btn', { 'disabled': !coupon.can_claim }]"
              :disabled="!coupon.can_claim"
              @click.stop="handleClaimCoupon(coupon)"
            >
              {{ coupon.claim_status_text }}
            </button>
          </div>
        </div>

        <!-- 优惠券类型标签 -->
        <div class="coupon-tag">
          <text class="tag-text">{{ coupon.type_text }}</text>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && coupons.length === 0" class="empty-state">
      <image src="/static/images/empty-coupon.png" class="empty-image" />
      <text class="empty-text">暂无可领取的优惠券</text>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && !loading" class="load-more" @click="loadMore">
      <text>加载更多</text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useCouponStore } from '@/store/coupon'
import type { ICouponCenterItem } from '@/api/coupon.typings'

// Store
const couponStore = useCouponStore()

// 响应式数据
const loading = ref(false)
const coupons = ref<ICouponCenterItem[]>([])
const currentPage = ref(1)
const hasMore = ref(true)

// 生命周期
onMounted(() => {
  loadCoupons()
})

// 方法
const loadCoupons = async (refresh = false) => {
  if (loading.value) return

  loading.value = true
  
  try {
    if (refresh) {
      currentPage.value = 1
      coupons.value = []
    }

    const response = await couponStore.getCouponCenter({
      page: currentPage.value,
      page_size: 10
    })

    if (refresh) {
      coupons.value = response.list
    } else {
      coupons.value.push(...response.list)
    }

    hasMore.value = response.list.length === 10
    currentPage.value++
  } catch (error) {
    console.error('加载优惠券失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  loadCoupons()
}

const handleClaimCoupon = async (coupon: ICouponCenterItem) => {
  if (!coupon.can_claim) return

  try {
    await couponStore.claimCoupon(coupon.id)
    
    // 更新本地状态
    const index = coupons.value.findIndex(c => c.id === coupon.id)
    if (index !== -1) {
      coupons.value[index].can_claim = false
      coupons.value[index].claim_status_text = '已领取'
    }

    uni.showToast({
      title: '领取成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('领取优惠券失败:', error)
  }
}

const handleCouponClick = (coupon: ICouponCenterItem) => {
  // 点击优惠券查看详情
  uni.navigateTo({
    url: `/pages/coupon/detail?id=${coupon.id}`
  })
}

const handleImageError = () => {
  console.log('商家Logo加载失败')
}

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = date.getTime() - now.getTime()
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
  
  if (days <= 0) {
    return '已过期'
  } else if (days === 1) {
    return '明天'
  } else if (days <= 7) {
    return `${days}天后`
  } else {
    return date.toLocaleDateString()
  }
}
</script>

<style lang="scss" scoped>
.coupon-center {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  
  h1 {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  p {
    font-size: 28rpx;
    color: #666;
  }
}

.coupon-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.merchant-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  .merchant-logo {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .merchant-logo-placeholder {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    
    .placeholder-text {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .merchant-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }
}

.coupon-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.coupon-main {
  display: flex;
  align-items: center;
  flex: 1;
}

.coupon-amount {
  display: flex;
  align-items: baseline;
  margin-right: 30rpx;
  
  .amount-symbol {
    font-size: 32rpx;
    color: #ff4757;
    font-weight: bold;
  }
  
  .amount-value {
    font-size: 48rpx;
    color: #ff4757;
    font-weight: bold;
  }
}

.coupon-info {
  flex: 1;
  
  .coupon-name {
    display: block;
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 8rpx;
  }
  
  .coupon-condition {
    display: block;
    font-size: 24rpx;
    color: #666;
    margin-bottom: 4rpx;
  }
  
  .coupon-time {
    display: block;
    font-size: 24rpx;
    color: #999;
  }
}

.claim-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ff4757, #ff3742);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  
  &.disabled {
    background: #ccc;
    color: #999;
  }
}

.coupon-tag {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff4757;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 0 16rpx 0 16rpx;
  
  .tag-text {
    font-size: 20rpx;
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.load-more {
  text-align: center;
  padding: 30rpx;
  color: #666;
  font-size: 28rpx;
}
</style>
