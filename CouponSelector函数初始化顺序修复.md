# 🔧 CouponSelector函数初始化顺序修复

## ❌ 错误现象

**控制台错误信息**:
```
CouponSelector.vue:321 [Vue warn]: Unhandled error during execution of watcher callback 
CouponSelector.vue:321 ReferenceError: Cannot access 'loadCoupons' before initialization
    at watch.immediate (CouponSelector.vue:330:7)
    at setup (CouponSelector.vue:321:1)
```

**问题分析**:
- ✅ 组件正常挂载
- ❌ `watch` 回调函数中无法访问 `loadCoupons` 函数
- ❌ 错误发生在组件初始化阶段

## 🔍 问题根因

### JavaScript变量提升和初始化顺序问题

#### 问题代码结构:
```typescript
// 第321行：watch定义（设置了immediate: true）
watch(
  [() => props.merchantId, () => props.totalAmount, () => props.foodIds],
  () => {
    if (props.merchantId && props.totalAmount > 0) {
      loadCoupons()  // ❌ 此时loadCoupons还未定义
    }
  },
  { immediate: true },  // ❌ 立即执行，但loadCoupons还未初始化
)

// 第336行：loadCoupons函数定义
const loadCoupons = async () => {
  // 函数实现
}
```

#### 执行顺序分析:
```
1. Vue组件setup()函数开始执行
2. 执行到第321行：创建watch监听器
3. 由于immediate: true，watch立即执行回调函数
4. 回调函数尝试调用loadCoupons()
5. ❌ 错误：loadCoupons还未定义（在第336行才定义）
6. 抛出ReferenceError: Cannot access 'loadCoupons' before initialization
```

### JavaScript变量提升规则

#### const/let声明的特点:
- **不会被提升**: 与`var`不同，`const`和`let`声明不会被提升到作用域顶部
- **暂时性死区**: 在声明之前访问会抛出ReferenceError
- **初始化顺序**: 必须按照代码书写顺序进行初始化

#### 对比var和const:
```javascript
// var的情况（会提升，但值为undefined）
console.log(myVar)  // undefined（不会报错）
var myVar = 'hello'

// const/let的情况（不会提升，存在暂时性死区）
console.log(myConst)  // ReferenceError: Cannot access 'myConst' before initialization
const myConst = 'hello'
```

## 🛠️ 修复方案

### 修复策略: 调整函数定义顺序

#### 修复前的错误结构:
```typescript
// ❌ 错误：watch在loadCoupons定义之前
const navigateToCouponCenter = () => {
  // ...
}

// 第321行：watch定义（immediate: true）
watch(
  [() => props.merchantId, () => props.totalAmount, () => props.foodIds],
  () => {
    if (props.merchantId && props.totalAmount > 0) {
      loadCoupons()  // ❌ loadCoupons还未定义
    }
  },
  { immediate: true },
)

// 第336行：loadCoupons函数定义
const loadCoupons = async () => {
  // 函数实现
}
```

#### 修复后的正确结构:
```typescript
// ✅ 正确：先定义函数，再使用
const navigateToCouponCenter = () => {
  // ...
}

// 先定义loadCoupons函数
const loadCoupons = async () => {
  try {
    console.log('🎫 开始加载优惠券:', {
      merchantId: props.merchantId,
      totalAmount: props.totalAmount,
      foodIds: props.foodIds
    })
    
    await couponStore.fetchAvailableCouponsForOrder({
      merchant_id: props.merchantId,
      total_amount: props.totalAmount,
      food_ids: props.foodIds,
    })
    
    console.log('🎫 优惠券加载完成:', {
      available: availableCount.value,
      total: availableCoupons.value.length + unavailableCoupons.value.length
    })
  } catch (error) {
    console.error('❌ 加载优惠券失败:', error)
    uni.showToast({
      title: '加载优惠券失败',
      icon: 'none',
      duration: 2000
    })
  }
}

// 然后定义watch（现在可以安全地使用loadCoupons）
watch(
  [() => props.merchantId, () => props.totalAmount, () => props.foodIds],
  () => {
    if (props.merchantId && props.totalAmount > 0) {
      console.log('🎫 CouponSelector 参数变化，重新加载优惠券:', {
        merchantId: props.merchantId,
        totalAmount: props.totalAmount,
        foodIds: props.foodIds
      })
      loadCoupons()  // ✅ 现在loadCoupons已经定义
    }
  },
  { immediate: true },
)
```

## 🔄 修复验证

### 修复前的执行流程:
```
1. setup()开始执行
2. 创建watch（immediate: true）
3. 立即执行watch回调
4. 尝试调用loadCoupons()
5. ❌ ReferenceError: Cannot access 'loadCoupons' before initialization
6. 组件初始化失败
```

### 修复后的执行流程:
```
1. setup()开始执行
2. 定义loadCoupons函数
3. 创建watch（immediate: true）
4. 立即执行watch回调
5. 成功调用loadCoupons()
6. ✅ 组件正常初始化和工作
```

## 📝 Vue Composition API最佳实践

### 1. 函数定义顺序
```typescript
// ✅ 推荐顺序
// 1. 响应式数据
const showModal = ref(false)
const loading = ref(false)

// 2. 计算属性
const availableCount = computed(() => ...)

// 3. 方法定义
const loadData = async () => { ... }
const handleClick = () => { ... }

// 4. 监听器（在方法定义之后）
watch(someRef, () => {
  loadData()  // 现在可以安全调用
}, { immediate: true })

// 5. 生命周期钩子
onMounted(() => {
  loadData()
})
```

### 2. 避免循环依赖
```typescript
// ❌ 避免：函数之间的循环依赖
const funcA = () => {
  funcB()  // 依赖funcB
}

const funcB = () => {
  funcA()  // 依赖funcA，形成循环
}

// ✅ 推荐：清晰的依赖关系
const utilFunc = () => { ... }

const mainFunc = () => {
  utilFunc()  // 单向依赖
}
```

### 3. immediate watch的注意事项
```typescript
// ❌ 危险：immediate watch依赖后定义的函数
watch(someRef, () => {
  laterDefinedFunc()  // 可能未定义
}, { immediate: true })

const laterDefinedFunc = () => { ... }

// ✅ 安全：先定义函数，再创建watch
const myFunc = () => { ... }

watch(someRef, () => {
  myFunc()  // 安全调用
}, { immediate: true })
```

## 🧪 测试验证

### 验证步骤:
1. **刷新页面**: 强制重新初始化组件
2. **查看控制台**: 确认不再出现初始化错误
3. **测试功能**: 验证优惠券加载功能正常

### 预期结果:
```javascript
// 应该看到正常的初始化日志，而不是错误
🎫 CouponSelector 参数变化，重新加载优惠券: {
  merchantId: 1,
  totalAmount: 27,
  foodIds: "1,6,1"
}

🎫 开始加载优惠券: {
  merchantId: 1,
  totalAmount: 27,
  foodIds: "1,6,1"
}

🎫 优惠券加载完成: {
  available: 2,
  total: 3
}
```

## 📚 经验总结

### 1. JavaScript基础知识
- 理解变量提升和暂时性死区
- 掌握const/let与var的区别
- 注意函数定义和调用的顺序

### 2. Vue Composition API
- 合理安排setup函数中的代码顺序
- immediate watch需要特别注意依赖关系
- 避免在初始化阶段调用未定义的函数

### 3. 调试技巧
- 遇到"before initialization"错误时，检查定义顺序
- 使用console.log追踪函数调用时机
- 理解Vue组件的初始化流程

---

*通过调整函数定义顺序，CouponSelector组件现在可以正常初始化，不再出现"Cannot access before initialization"错误。*
