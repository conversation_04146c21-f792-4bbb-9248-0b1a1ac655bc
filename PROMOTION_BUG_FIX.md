# 促销活动Bug修复报告

## 问题描述

前端在调用CreateOrder API时，传递了促销活动参数 `promotionID: 1`，但后端在计算订单金额时没有应用促销折扣。

### 前端传递的参数格式
```json
{
  "merchantOrders": [
    {
      "merchantID": 1,
      "cartItemIDs": [51],
      "remark": "",
      "promotionID": 1
    }
  ],
  "paymentMethod": "balance",
  "takeoutAddressID": 3
}
```

## 问题根本原因

1. **参数格式不匹配**: 前端传递的是单个 `promotionID` 字段，但后端DTO结构期望的是 `promotionIDs` 数组格式。

2. **参数映射缺失**: 在 `groupItemsByMerchantFromRequest` 方法中，代码只处理了 `merchantOrder.PromotionIDs` 数组，没有处理单个 `promotionID` 字段。

3. **兼容性问题**: 系统设计支持多个促销活动，但前端传递的是单个促销活动ID。

## 修复方案

### 1. 更新DTO结构

在 `modules/takeout/dto/takeout_order_dto.go` 中的 `MerchantOrderRequest` 结构体添加了 `PromotionID` 字段：

```go
type MerchantOrderRequest struct {
    MerchantID   int64   `json:"merchantID"`
    CartItemIDs  []int64 `json:"cartItemIDs"`
    CouponID     int64   `json:"couponID"`
    PromotionID  int64   `json:"promotionID"`  // 新增：兼容前端单个ID格式
    PromotionIDs []int64 `json:"promotionIDs"` // 原有：支持多个促销活动
    DeliveryTime string  `json:"deliveryTime"`
    Remark       string  `json:"remark"`
}
```

### 2. 更新参数处理逻辑

在 `modules/takeout/services/multi_merchant_order_service.go` 的 `groupItemsByMerchantFromRequest` 方法中添加了兼容处理：

```go
// 处理促销活动ID：支持单个promotionID和多个promotionIDs
var promotionIDs []int64
if len(merchantOrder.PromotionIDs) > 0 {
    // 如果有PromotionIDs数组，使用数组
    promotionIDs = merchantOrder.PromotionIDs
    logs.Info("使用PromotionIDs数组 - 商家ID: %d, 促销活动: %v", merchantOrder.MerchantID, promotionIDs)
} else if merchantOrder.PromotionID > 0 {
    // 如果只有单个PromotionID，转换为数组
    promotionIDs = []int64{merchantOrder.PromotionID}
    logs.Info("转换单个PromotionID为数组 - 商家ID: %d, 促销活动ID: %d, 转换后: %v", merchantOrder.MerchantID, merchantOrder.PromotionID, promotionIDs)
} else {
    logs.Info("没有促销活动 - 商家ID: %d", merchantOrder.MerchantID)
}
```

### 3. 添加调试日志

在 `modules/takeout/controllers/takeout_order_controller.go` 中添加了详细的参数日志：

```go
// 记录原始请求参数用于调试
logs.Info("原始请求参数: %s", string(c.Ctx.Input.RequestBody))

// 详细记录每个商家订单的促销信息
for i, merchantOrder := range req.MerchantOrders {
    logs.Info("商家订单[%d] - 商家ID: %d, 优惠券ID: %d, 单个促销ID: %d, 促销ID数组: %v", 
        i, merchantOrder.MerchantID, merchantOrder.CouponID, merchantOrder.PromotionID, merchantOrder.PromotionIDs)
}
```

### 4. 更新API文档

更新了 `modules/takeout/docs/create_order_api_detailed.md` 文档，说明了两种参数格式都支持，并提供了相应的示例。

## 修复验证

通过测试验证，修复后的系统能够正确处理前端传递的单个 `promotionID` 参数：

- 前端传递 `promotionID: 1`
- 后端正确解析并转换为 `promotionIDs: [1]`
- 促销折扣计算逻辑正常工作

## 兼容性说明

此修复保持了向后兼容性：
- 支持原有的 `promotionIDs` 数组格式
- 新增支持单个 `promotionID` 格式
- 优先级：`promotionIDs` 数组 > 单个 `promotionID`

## 影响范围

- ✅ 修复了促销活动不生效的问题
- ✅ 保持了API的向后兼容性
- ✅ 添加了详细的调试日志
- ✅ 更新了API文档

## 测试建议

建议测试以下场景：
1. 使用单个 `promotionID` 创建订单
2. 使用 `promotionIDs` 数组创建订单
3. 同时传递两个字段时的优先级处理
4. 不传递促销参数时的正常处理
