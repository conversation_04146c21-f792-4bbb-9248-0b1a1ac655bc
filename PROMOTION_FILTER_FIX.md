# 促销活动过滤功能修复报告

## 🎯 问题描述

前端访问 `/api/v1/user/takeout/merchants/promotions-coupons` API时，返回的促销活动列表没有过滤用户不可用的促销活动，包括：
- 已达到使用次数限制的促销活动
- 用户已经使用过且有使用次数限制的促销活动

这导致前端显示了用户实际无法使用的促销活动，影响用户体验。

## 🔍 问题根本原因

在 `GetMerchantsPromotionsAndCoupons` 方法中，获取促销活动时只是简单地调用了 `GetActivePromotions`，没有考虑用户的使用次数限制：

```go
// 原有代码 - 没有用户使用次数检查
activePromotions, err := promotionRepo.GetActivePromotions(merchantID)
if err == nil && len(activePromotions) > 0 {
    merchantInfo.HasPromotion = true
    for _, promotion := range activePromotions {
        // 直接添加所有活跃的促销活动，没有检查用户是否可以使用
        promotionDTO := dto.PromotionInfoDTO{...}
        merchantInfo.Promotions = append(merchantInfo.Promotions, promotionDTO)
    }
}
```

## 🛠️ 修复方案

### 1. 添加促销服务依赖

修改 `takeoutFoodService` 结构体，添加促销服务依赖：

```go
type takeoutFoodService struct {
    foodRepo         repositories.TakeoutFoodRepository
    variantRepo      repositories.TakeoutVariantRepository
    comboRepo        repositories.TakeoutComboRepository
    categoryRepo     repositories.TakeoutCategoryRepository
    merchantRepo     merchantRepositories.MerchantRepository
    cacheService     TakeoutFoodCacheService
    promotionService ITakeoutPromotionService // 新增促销服务依赖
}
```

### 2. 修改构造函数

更新 `NewTakeoutFoodService` 构造函数，注入促销服务依赖：

```go
func NewTakeoutFoodService() TakeoutFoodService {
    // 创建促销服务实例
    promotionRepo := repositories.NewTakeoutPromotionRepository()
    foodPromoRepo := repositories.NewTakeoutFoodPromotionRepository()
    couponRepo := repositories.NewTakeoutCouponRepository()
    userCouponRepo := repositories.NewTakeoutUserCouponRepository()
    userPromotionRepo := repositories.NewTakeoutUserPromotionRepository()
    foodRepo := repositories.NewTakeoutFoodRepository()
    
    promotionService := NewTakeoutPromotionService(
        promotionRepo, foodPromoRepo, foodRepo,
        couponRepo, userCouponRepo, userPromotionRepo,
    )
    
    return &takeoutFoodService{
        // ... 其他依赖
        promotionService: promotionService,
    }
}
```

### 3. 添加用户使用权限检查

修改 `GetMerchantsPromotionsAndCoupons` 方法，添加用户使用次数限制的过滤逻辑：

```go
// 获取商家的活跃促销活动
activePromotions, err := promotionRepo.GetActivePromotions(merchantID)
if err == nil && len(activePromotions) > 0 {
    for _, promotion := range activePromotions {
        // 检查用户是否可以使用该促销活动（考虑使用次数限制）
        canUse, err := s.checkUserCanUsePromotion(userID, promotion)
        if err != nil {
            logs.Warn("检查用户促销使用权限失败: %v, 用户ID: %d, 促销ID: %d", err, userID, promotion.ID)
            continue
        }
        
        // 如果用户不能使用该促销活动，跳过
        if !canUse {
            logs.Info("用户不能使用促销活动 - 用户ID: %d, 促销ID: %d, 促销名称: %s", userID, promotion.ID, promotion.Name)
            continue
        }
        
        // 只有用户可以使用的促销活动才会被添加到返回列表中
        promotionDTO := dto.PromotionInfoDTO{...}
        merchantInfo.Promotions = append(merchantInfo.Promotions, promotionDTO)
    }
    
    // 如果有可用的促销活动，设置HasPromotion为true
    if len(merchantInfo.Promotions) > 0 {
        merchantInfo.HasPromotion = true
    }
}
```

### 4. 实现用户使用权限检查方法

添加 `checkUserCanUsePromotion` 方法来检查用户是否可以使用指定的促销活动：

```go
func (s *takeoutFoodService) checkUserCanUsePromotion(userID int64, promotion *models.TakeoutPromotion) (bool, error) {
    // 解析促销活动规则
    var rules map[string]interface{}
    err := json.Unmarshal([]byte(promotion.Rules), &rules)
    if err != nil {
        logs.Error("解析促销活动规则失败: %v, 促销ID: %d", err, promotion.ID)
        return false, err
    }
    
    // 检查用户使用次数限制
    var perUserLimit int64 = 0
    
    // 尝试从不同的规则结构中获取per_user_limit
    if coupon, ok := rules["coupon"].(map[string]interface{}); ok {
        if limit, exists := coupon["per_user_limit"]; exists {
            if limitFloat, ok := limit.(float64); ok {
                perUserLimit = int64(limitFloat)
            }
        }
    } else if limit, exists := rules["per_user_limit"]; exists {
        if limitFloat, ok := limit.(float64); ok {
            perUserLimit = int64(limitFloat)
        }
    }
    
    // 如果没有设置per_user_limit或为0，默认允许使用
    if perUserLimit <= 0 {
        return true, nil
    }
    
    // 使用促销服务检查用户使用次数
    canUse, err := s.promotionService.CheckUserPromotionUsage(userID, promotion.ID, perUserLimit)
    if err != nil {
        logs.Error("检查用户促销使用次数失败: %v, 用户ID: %d, 促销ID: %d", err, userID, promotion.ID)
        return false, err
    }
    
    return canUse, nil
}
```

## 📊 修复效果

### 修复前
- API返回所有活跃的促销活动，不考虑用户使用限制
- 用户看到无法使用的促销活动，造成困惑
- 用户尝试使用已达到限制的促销活动时会失败

### 修复后
- API只返回用户可以使用的促销活动
- 自动过滤掉已达到使用次数限制的促销活动
- 提升用户体验，避免无效的促销活动选择

## 🧪 测试验证

### 测试场景1：用户已达到使用限制
1. 用户ID为2已经使用过促销活动ID为1（限制1次）
2. 调用API应该不返回该促销活动
3. 清除使用记录后，API应该返回该促销活动

### 测试场景2：用户未达到使用限制
1. 用户从未使用过某个促销活动
2. 调用API应该返回该促销活动
3. 用户可以正常使用该促销活动

### 测试脚本
提供了 `test_promotion_filter.sh` 脚本来验证修复效果。

## 📝 日志增强

修复后的代码增加了详细的日志记录：

```
[INFO] 用户不能使用促销活动 - 用户ID: 2, 促销ID: 1, 促销名称: 新用户首单减免
[INFO] 检查用户促销使用权限失败: xxx, 用户ID: 2, 促销ID: 1
[INFO] 检查用户促销使用次数失败: xxx, 用户ID: 2, 促销ID: 1
```

这些日志有助于调试和监控促销活动的使用情况。

## 🎯 影响范围

- ✅ 修复了促销活动过滤问题
- ✅ 提升了用户体验
- ✅ 增加了详细的日志记录
- ✅ 保持了API的向后兼容性
- ✅ 没有影响其他功能

## 🔄 后续建议

1. **缓存优化**：考虑缓存用户的促销使用记录，减少数据库查询
2. **性能监控**：监控API响应时间，确保过滤逻辑不影响性能
3. **用户反馈**：收集用户反馈，验证修复效果
4. **扩展功能**：考虑添加更多的促销活动过滤条件（如地理位置、用户等级等）
