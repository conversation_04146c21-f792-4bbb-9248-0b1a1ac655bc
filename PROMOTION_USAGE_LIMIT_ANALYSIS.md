# 促销活动使用次数限制问题分析与解决方案

## 🔍 问题现象

前端调用创建订单接口时，传递了促销活动参数：
```json
{
  "merchantOrders": [
    {
      "merchantID": 1,
      "cartItemIDs": [185],
      "remark": "",
      "couponID": 2,
      "promotionID": 1
    }
  ],
  "paymentMethod": "balance",
  "takeoutAddressID": 3
}
```

但后端日志显示促销活动没有被应用，只应用了优惠券。

## 📊 日志分析

从后端日志中可以看到关键信息：

```
2025/07/23 00:56:16.806 [I] [takeout_promotion_service.go:601]  用户促销使用次数检查 - 用户ID: 2, 促销ID: 1, 已使用: 1, 限制: 1, 可使用: false
2025/07/23 00:56:16.806 [I] [takeout_promotion_service.go:545]  用户已达到促销使用次数限制 - 促销ID: 1, 用户ID: 2, 限制: 1
2025/07/23 00:56:16.806 [I] [multi_merchant_order_service.go:453]  获取到符合条件的促销活动数量: 0
2025/07/23 00:56:16.806 [I] [multi_merchant_order_service.go:474]  请求的促销活动ID 1 不在符合条件的列表中
```

## 🎯 问题根本原因

**用户已达到促销活动的使用次数限制**：
- 用户ID为2的用户已经使用过促销活动ID为1的促销活动1次
- 该促销活动的规则中设置了 `per_user_limit: 1`（每个用户限制使用1次）
- 系统正确地检查了用户的使用历史，发现已达到限制，因此拒绝再次应用该促销活动

## 🔧 解决方案

### 方案1：清除用户使用记录（测试环境）

如果这是测试环境，可以清除用户的促销使用记录：

#### 使用API接口（推荐）
```bash
# 查看用户使用记录
curl -X GET "http://localhost:8181/api/v1/takeout/promotions/user-usage?user_id=2&promotion_id=1"

# 清除用户使用记录
curl -X DELETE "http://localhost:8181/api/v1/takeout/promotions/clear-user-usage?user_id=2&promotion_id=1"
```

#### 使用SQL（直接操作数据库）
```sql
-- 查看用户使用记录
SELECT * FROM takeout_user_promotion WHERE user_id = 2 AND promotion_id = 1;

-- 清除用户使用记录
DELETE FROM takeout_user_promotion WHERE user_id = 2 AND promotion_id = 1;
```

### 方案2：修改促销活动限制

修改促销活动的使用限制，允许用户多次使用：

```sql
-- 将per_user_limit从1改为2（或更大的数值）
UPDATE takeout_promotion 
SET rules = REPLACE(rules, '"per_user_limit":1', '"per_user_limit":2')
WHERE id = 1;

-- 或者设为0表示不限制使用次数
UPDATE takeout_promotion 
SET rules = REPLACE(rules, '"per_user_limit":1', '"per_user_limit":0')
WHERE id = 1;
```

### 方案3：创建新的促销活动

为测试创建一个新的促销活动，避免使用次数限制问题。

## 🛠️ 新增的管理工具

为了方便管理促销使用记录，我添加了以下API接口：

### 1. 查看用户促销使用记录
```
GET /api/v1/takeout/promotions/user-usage?user_id={user_id}&promotion_id={promotion_id}
```

### 2. 清除用户促销使用记录
```
DELETE /api/v1/takeout/promotions/clear-user-usage?user_id={user_id}&promotion_id={promotion_id}
```

## 📝 测试步骤

1. **查看当前使用记录**：
   ```bash
   curl -X GET "http://localhost:8181/api/v1/takeout/promotions/user-usage?user_id=2&promotion_id=1"
   ```

2. **清除使用记录**：
   ```bash
   curl -X DELETE "http://localhost:8181/api/v1/takeout/promotions/clear-user-usage?user_id=2&promotion_id=1"
   ```

3. **重新测试订单创建**：
   使用相同的参数重新调用创建订单接口，此时促销活动应该能够正常应用。

## 🔍 验证方法

清除使用记录后，重新创建订单时应该看到以下日志：

```
[I] 用户促销使用次数检查 - 用户ID: 2, 促销ID: 1, 已使用: 0, 限制: 1, 可使用: true
[I] 获取到符合条件的促销活动数量: 1
[I] 促销活动应用成功 - 订单ID: XX, 促销ID: 1, 促销名称: XX, 折扣金额: XX
```

## 📋 系统设计说明

这个问题实际上证明了系统的促销使用次数限制功能是**正常工作**的：

1. ✅ 系统正确记录了用户的促销使用历史
2. ✅ 系统正确检查了用户的使用次数限制
3. ✅ 系统正确拒绝了超出限制的促销使用请求
4. ✅ 系统提供了详细的日志记录便于调试

这是一个**功能正常**的表现，而不是bug。在生产环境中，这种限制是必要的，可以防止用户滥用促销活动。

## 🎯 建议

1. **测试环境**：使用提供的API接口清除使用记录进行测试
2. **生产环境**：根据业务需求合理设置促销活动的使用限制
3. **监控**：定期检查促销活动的使用情况和效果
4. **用户体验**：在前端适当提示用户促销活动的使用限制和剩余次数
