# O_Mall 多商家电商平台

## 项目概述

O_Mall是一个功能全面的多商家电商平台系统，支持多种商品类别、多样化配送方式和跑腿业务。本系统采用Go语言和Beego框架开发，遵循模块化设计原则，各功能模块独立运行，便于维护和扩展。

## 技术架构

- **开发语言**：Go
- **Web框架**：Beego
- **数据库**：MySQL (主数据存储), Redis (缓存和会话管理)
- **API设计**：RESTful API
- **架构模式**：模块化分层架构

## 项目结构

```bash
O_Mall/
├── conf/                 # 配置文件目录
│   ├── app.conf         # 主配置文件
│   └── env/             # 环境特定配置
├── common/              # 公共组件
│   ├── result/          # 统一响应处理
│   ├── controllers/     # 基础控制器
│   └── utils/           # 工具函数
├── controllers/         # 控制器层（按功能模块分组）
├── models/              # 数据模型
├── repositories/        # 数据访问层
├── services/            # 业务逻辑层
├── routers/             # 路由配置
├── middlewares/         # 中间件
├── dto/                 # 数据传输对象
├── utils/               # 工具函数
├── static/              # 静态资源
├── tests/               # 测试代码
├── modules/             # 功能模块（核心）
├── database/            # 数据库迁移和初始化
├── docs/                # API文档
├── scripts/             # 运维脚本
├── main.go              # 应用入口
└── README.md            # 项目说明文档
```

### 模块化结构

系统采用模块化设计，每个功能模块都有自己独立的MVC结构：

```go
modules/
├── user/                # 用户模块
│   ├── controllers/     # 用户控制器
│   ├── models/          # 用户数据模型
│   ├── repositories/    # 用户数据访问
│   ├── services/        # 用户业务逻辑
│   │   ├── impl/        # 接口实现
│   │   └── interface.go # 接口定义
│   ├── dto/             # 用户数据传输对象
│   └── constants/       # 用户模块常量
│
├── product/             # 商品模块
├── order/               # 订单模块
├── payment/             # 支付模块
├── delivery/            # 配送模块
├── merchant/            # 商家模块
├── runner/              # 跑腿模块
├── admin/               # 管理员模块
├── points/              # 积分模块
├── giftcard/            # 礼品卡模块
├── takeout/             # 外卖模块
├── scheduler/           # 定时执行模块
├── apidoc/              # API文档模块
└── mcp/                 # MCP服务器模块
```

## 主要功能

### 定时执行系统

- 任务管理与调度
  - 一次性定时任务：指定时间执行特定操作
  - 周期性定时任务：基于Cron表达式的重复执行
  - 任务失败自动重试机制
  - 任务执行历史与结果记录
- 商家营业时间管理
  - 根据商家设置的营业时间自动创建定时任务
  - 自动在开始时间将商家状态设置为“营业中”
  - 自动在结束时间将商家状态设置为“休息中”
  - 支持按周几设置不同的营业时间
- 其他应用场景
  - 订单状态通知：在指定时间发送订单状态提醒
  - 促销活动自动开始与结束
  - 定时统计报表生成
  - 系统维护任务调度

### 用户系统

- 用户注册、登录、认证
  - 多种注册方式
    - 用户名+密码注册：传统账号注册方式
    - 手机号+短信验证码注册：通过短信验证用户身份，支持推荐关系
    - 第三方平台授权注册：支持微信、支付宝等第三方平台授权注册
  - 多种登录方式
    - 用户名+密码登录：传统登录方式
    - 手机号+短信验证码登录：免密登录
    - 第三方平台授权登录：一键登录
    - 微信小程序一键登录：无需输入账号密码，通过微信小程序 code 获取用户信息并登录
  - 双令牌认证系统
    - Access Token：短期访问令牌，用于API访问认证
    - Refresh Token：长期刷新令牌，用于获取新的Access Token
    - 令牌刷新机制：使用Refresh Token获取新的令牌对
    - 安全登出：撤销所有活跃令牌
- 多角色管理（用户、商家、跑腿员、管理员）
- 收货地址管理（一对多）
- 用户信息管理
- 账户安全（多因素认证、异常登录检测）

### 商家模块

- 商家入驻与管理
- 商家分类管理
- 商家审核流程
- 商家数据统计
- 商家结算系统

### 商品管理

- 商品信息管理
- 商品分类体系
- 商品库存管理
- 商品搜索与筛选
- 商品上下架管理

### 购物车管理

- 添加商品到购物车
- 修改购物车商品数量
- 删除购物车商品
- 购物车商品统计
- 购物车商品结算
- 购物车同步（登录与未登录状态）

### 订单管理

- 订单创建与流转
- 订单状态追踪
- 订单支付流程
  - 支持订单创建时直接完成余额支付
  - 多种支付方式参数格式兼容（数字、字符串、描述性文本）
  - 余额支付自动扣减用户余额并更新订单状态
- 订单取消与退款
- 订单评价入口

### 配送系统

- 多种配送方式（快递、送货上门、自取）
- 配送区域与费用计算
- 配送状态追踪
- 配送时效管理

### 跑腿业务

- 跑腿员注册与管理
- 跑腿订单分配
- 跑腿费用计算（固定价格或按距离）
- 跑腿员评价系统
- 跑腿员结算系统

### 管理员系统

- 多级权限管理
  - 超级管理员：拥有全部权限
  - 财务管理员：负责财务审核、结算
  - 商品管理员：负责商品审核
  - 用户管理员：负责用户管理和客服
- 管理员操作日志
- 系统配置管理
- 敏感操作多级审批
- 用户管理功能
  - 用户信息查看与编辑
  - 用户状态管理（启用/禁用）
  - 用户密码重置
  - 用户列表查询（支持多条件筛选）

#### 管理员API接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/login` | POST | 管理员登录 |
| `/api/v1/admin/refresh-token` | POST | 刷新访问令牌 |
| `/api/v1/admin/secured/logout` | POST | 管理员登出 |
| `/api/v1/admin/secured/info` | GET | 获取管理员信息 |
| `/api/v1/admin/secured/info` | PUT | 更新管理员信息 |
| `/api/v1/admin/secured/password` | PUT | 修改管理员密码 |
| `/api/v1/admin/secured/admins` | POST | 创建管理员 |
| `/api/v1/admin/secured/admins` | GET | 获取管理员列表 |
| `/api/v1/admin/secured/admins/:id` | GET | 获取管理员详情 |
| `/api/v1/admin/secured/admins/:id` | PUT | 更新管理员信息 |
| `/api/v1/admin/secured/admins/:id` | DELETE | 删除管理员 |
| `/api/v1/admin/secured/admins/:id/disable` | PUT | 禁用管理员 |
| `/api/v1/admin/secured/admins/:id/enable` | PUT | 启用管理员 |
| `/api/v1/admin/secured/admins/:id/password/reset` | PUT | 重置管理员密码 |

#### 用户管理API接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/secured/users` | GET | 获取用户列表 |
| `/api/v1/admin/secured/users/:id` | GET | 获取用户详情 |
| `/api/v1/admin/secured/users/:id` | PUT | 更新用户信息 |
| `/api/v1/admin/secured/users/:id/disable` | PUT | 禁用用户 |
| `/api/v1/admin/secured/users/:id/enable` | PUT | 启用用户 |
| `/api/v1/admin/secured/users/:id/password` | PUT | 重置用户密码 |

#### 商户管理API接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/secured/merchants` | GET | 获取商户列表 |
| `/api/v1/admin/secured/merchants/:id` | GET | 获取商户详情 |
| `/api/v1/admin/secured/merchants/:id` | PUT | 更新商户信息 |
| `/api/v1/admin/secured/merchants/:id/disable` | PUT | 禁用商户 |
| `/api/v1/admin/secured/merchants/:id/enable` | PUT | 启用商户 |
| `/api/v1/admin/secured/merchants/:id/password` | PUT | 重置商户密码 |
| `/api/v1/admin/secured/merchants/:id/audit` | PUT | 审核商户入驻申请 |

### 支付系统

- 多种支付方式
  - 微信支付
  - 支付宝
  - 余额支付
- 支付流程处理
  - 订单支付发起
  - 支付状态查询
  - 支付回调处理
  - 支付交易记录
- 退款流程
  - 退款申请
  - 退款状态查询
  - 退款回调处理
  - 退款记录管理
- 财务对账
  - 支付对账
  - 退款对账
  - 交易流水查询
- 交易安全保障
  - 签名验证
  - 交易防重复提交
  - 金额校验
  - 交易限额控制

#### 支付API接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/orders/:order_id/payment` | POST | 发起订单支付 |
| `/api/v1/orders/:order_id/payment/status` | GET | 查询支付状态 |
| `/api/v1/orders/:order_id/refund` | POST | 发起订单退款 |
| `/api/payment/callback/:method/:order_no` | POST | 处理支付平台回调 |
| `/api/refund/callback/:method/:order_no` | POST | 处理退款平台回调 |

#### 支付流程

1. **发起支付**
   - 用户选择商品下单
   - 系统生成订单
   - 用户选择支付方式
   - 调用支付接口生成支付参数
   - 前端根据返回参数调起支付

2. **支付结果处理**
   - 支付平台异步通知支付结果
   - 系统接收回调并验证签名
   - 更新订单支付状态
   - 触发后续业务流程（库存扣减、佣金计算等）

3. **退款处理**
   - 用户或商家发起退款申请
   - 系统验证退款条件
   - 调用支付平台退款接口
   - 接收退款结果回调
   - 更新订单退款状态

### 积分与礼品卡

#### 积分系统

- 积分获取方式
  - 购物消费获取积分
  - 评价商品获取积分
  - 首次注册奖励积分
  - 每日签到积分
- 积分使用方式
  - 积分兑换优惠券
  - 积分直接抵扣订单金额
  - 积分兑换礼品卡
  - 积分兑换实物礼品
- 积分规则管理
  - 积分有效期管理
  - 积分兑换比例设置

#### 礼品卡系统

- 礼品卡类型
  - 面值型礼品卡
  - 商品型礼品卡
  - 商家型礼品卡
- 礼品卡获取与管理
- 礼品卡使用记录

### 评价系统

- 商品评价
- 商家评价
- 跑腿员评价
- 评价激励机制
- 评价内容审核

### 消息通知

- 系统通知
- 订单状态通知
- 促销信息推送
- 消息偏好设置

### 聊天系统

- 用户与商家实时沟通
  - 文本消息支持
  - 图片、文件和语音消息
  - 会话管理与历史消息
- WebSocket实时通信
  - 消息即时推送
  - 在线状态管理
  - 会话状态同步
- 多端消息同步
  - 消息已读状态
  - 离线消息推送
- 详细文档参考 `/modules/chat/docs/`

### 财务系统

- 账户管理
  - 平台账户
  - 商家账户
  - 用户账户
  - 跑腿员账户
- 交易记录管理
- 结算系统
  - 商家结算周期（如T+1、周结、月结）
  - 跑腿员结算（可日结）
- 财务报表生成

### 认证系统

- 双Token认证机制
  - Access Token：短期访问令牌，用于API访问认证
  - Refresh Token：长期刷新令牌，用于获取新的Access Token
- 多角色认证
  - 用户认证
  - 商家认证
  - 管理员认证
  - 跑腿员认证
- 认证流程
  1. 用户登录获取Token对
  2. 使用Access Token访问API
  3. Access Token过期后使用Refresh Token获取新Token对
  4. 登出时撤销所有Token
- 安全特性
  - Token黑名单机制
  - 刷新令牌单设备登录
  - 异常登录检测
  - 敏感操作二次认证
  
### UI配置模块

- 存储前端页面JSON配置
  - 支持JSONForms表单配置
  - 支持AG Grid表格配置
- 版本控制机制
  - 每次配置更新自动生成版本识别号
  - 前端可根据版本号识别是否需要更新缓存
- 配置内容验证
  - 格式验证确保JSON有效
  - 特定类型验证检查必要字段
- 灵活的查询支持
  - 按前端路径查询配置
  - 按配置类型查询配置
  - 按配置键获取特定配置

#### UI配置API接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/ui-configs` | POST | 创建UI配置 |
| `/api/v1/ui-configs` | GET | 分页查询UI配置 |
| `/api/v1/ui-configs/:id` | GET | 获取UI配置详情 |
| `/api/v1/ui-configs/:id` | PUT | 更新UI配置 |
| `/api/v1/ui-configs/:id` | DELETE | 删除UI配置 |
| `/api/v1/ui-configs/key` | GET | 根据Key获取UI配置 |
| `/api/v1/ui-configs/path` | GET | 根据前端路径获取配置 |
| `/api/v1/ui-configs/:id/version` | PUT | 更新配置版本号 |
| `/api/v1/admin/secured/grid-infos/set-batch-ui` | PUT | 批量设置UI配置与网格布局关联 |

### 系统安全设计

### 数据安全

- 敏感数据加密（用户密码、身份证号、银行卡信息等）
- 数据分级存储与保护
- 定期数据备份
- 全站HTTPS加密传输
- 信息脱敏处理

### 接口安全

- API认证机制（JWT/OAuth2.0）
- 接口限流与防刷
- 参数验证与过滤
- 幂等性设计
- 异常访问监控

### 用户账户安全

- 强密码策略
- 多因素认证
- 登录异常检测
- 账户锁定机制
- 权限最小化原则

### 支付安全

- 交易全程加密
- 防重复支付机制
- 实时交易监控
- 资金流向追踪
- 多人复核机制

### 合规与审计

- 数据合规（《网络安全法》、《数据安全法》等）
- 完整日志记录
- 操作审计跟踪
- 安全事件响应机制

## 配置管理

### 配置文件结构

O_Mall采用分层配置文件结构，便于管理不同环境和功能模块的配置：

```bash
conf/
├── app.conf           # 主配置文件，包含基础配置
├── env/               # 环境特定配置
│   ├── dev.conf       # 开发环境配置
│   ├── test.conf      # 测试环境配置
│   └── prod.conf      # 生产环境配置
└── modules/           # 模块特定配置
    ├── storage.conf   # 存储配置（本地/OSS/CDN）
    ├── sms.conf       # 短信服务配置
    ├── payment.conf   # 支付相关配置
    └── push.conf      # 推送通知配置
```

### 核心配置项

在主配置文件`app.conf`中设置基本配置：

```ini
# 基础配置
appname = O_Mall
httpport = 8080
runmode = dev  # 可选: dev, test, prod
autorender = false
copyrequestbody = true
EnableDocs = true

# 数据库配置
mysqluser = root
mysqlpass = password
mysqlhost = localhost
mysqlport = 3306
mysqldb   = o_mall
mysqltz   = Asia/Shanghai

# Redis配置
redishost = localhost
redisport = 6379
redispass = 
redisdb = 0

# 日志配置
log_level = info
log_file = logs/app.log
```

### 可选服务配置

#### 存储与CDN配置 (storage.conf)

支持多种存储方式，可根据需求配置本地存储或云存储与CDN加速：

```ini
# 存储模式: local, oss, cos, s3
storage_mode = local

# 重要：确保正确加载模块配置
imported = true

# CDN配置
enable_cdn = false
cdn_domain = cdn.example.com
cdn_http_port = 80
cdn_https_port = 443
cdn_access_key = your_access_key
cdn_secret_key = your_secret_key

# 阿里云OSS配置（如果storage_mode=oss）
oss_endpoint = oss-cn-hangzhou.aliyuncs.com
oss_bucket = your-bucket-name
oss_access_key = your-access-key
oss_access_secret = your-access-secret
oss_domain = your-oss-domain.com

# 腾讯云COS配置（如果storage_mode=cos）
cos_region = ap-guangzhou
cos_bucket = your-bucket-name
cos_secret_id = your-secret-id
cos_secret_key = your-secret-key
cos_domain = your-cos-domain.com

# AWS S3配置（如果storage_mode=s3）
s3_region = us-west-2
s3_bucket = your-bucket-name
s3_access_key = your-access-key
s3_secret_key = your-secret-key
s3_domain = your-s3-domain.com
```

#### 短信服务配置 (sms.conf)

支持多种短信服务提供商：

```ini
# 短信服务配置
# 短信服务提供商: aliyun, tencent, yunpian, custom
sms_provider = aliyun

# 重要：确保正确加载模块配置
imported = true

# 阿里云短信配置
aliyun_access_key = your_access_key
aliyun_access_secret = your_access_secret
aliyun_sign_name = 您的公司名称
aliyun_template_code_register = SMS_1234567890
aliyun_template_code_login = SMS_0987654321
aliyun_template_code_reset_pwd = SMS_1357924680

# 腾讯云短信配置
tencent_app_id = your_app_id
tencent_app_key = your_app_key
tencent_sign_name = 您的公司名称
tencent_template_id_register = 123456
tencent_template_id_login = 654321
tencent_template_id_reset_pwd = 135792

# 云片短信配置
yunpian_api_key = your_api_key
yunpian_sign_name = 您的公司名称
yunpian_template_register = 您的验证码是{code}，10分钟内有效
yunpian_template_login = 您的登录验证码是{code}，10分钟内有效
yunpian_template_reset_pwd = 您的密码重置验证码是{code}，10分钟内有效

# 自定义短信API配置
custom_sms_api_url = https://api.custom-sms.com/send
custom_sms_api_method = POST
custom_sms_api_headers = Content-Type:application/json
custom_sms_api_body_template = {"mobile":"{phone}","content":"您的验证码是{code}","type":"{type}"}
custom_sms_api_success_code = 200
```

### 配置加载与使用

在启动时，系统会按以下顺序加载配置：
1. 加载主配置文件`app.conf`
2. 根据`runmode`加载对应的环境配置文件
3. 加载各模块特定配置文件
4. 命令行参数覆盖（优先级最高）

**重要提示**：所有模块配置文件（如 storage.conf、sms.conf 等）必须包含 `imported = true` 配置项。系统使用此配置项作为验证点，以确认模块配置文件是否被正确加载。如果缺少此配置项，相应模块的配置可能无法正常加载，导致功能异常。

在代码中可通过以下方式获取配置：

```go
// 获取普通配置项
httpPort, _ := web.AppConfig.Int("httpport")

// 获取数据库配置
mysqlUser, _ := web.AppConfig.String("mysqluser") 
mysqlHost, _ := web.AppConfig.String("mysqlhost")

// 获取Redis配置
redisHost, _ := web.AppConfig.String("redishost") 
redisPort, _ := web.AppConfig.String("redisport")

// 获取模块特定配置
storageMode := web.AppConfig.String("storage::storage_mode")
enableCDN, _ := web.AppConfig.Bool("storage::enable_cdn")

// 获取短信配置
smsProvider := web.AppConfig.String("sms::sms_provider")
```

### 配置环境变量覆盖

为保证安全性和灵活部署，支持使用环境变量覆盖配置文件中的敏感信息：

```bash
# 数据库配置环境变量
export O_MALL_DB_HOST=mysql.example.com
export O_MALL_DB_PASS=secure_password

# 短信配置环境变量
export O_MALL_SMS_ALIYUN_ACCESS_KEY=real_access_key
export O_MALL_SMS_ALIYUN_ACCESS_SECRET=real_access_secret

# CDN配置环境变量
export O_MALL_CDN_ACCESS_KEY=real_cdn_key
export O_MALL_CDN_SECRET_KEY=real_cdn_secret
```

### 配置热重载

在不重启应用的情况下，系统支持热重载部分配置：

```bash
# 通过API触发配置重载
curl -X POST http://localhost:8080/api/v1/admin/config/reload -H "Authorization: Bearer YOUR_TOKEN"
```

## 环境要求

- Go 1.16+
- MySQL 5.7+
- Redis 6.0+
- Nginx (生产环境)

## 常见问题与解决方案

### 配置加载问题

1. **模块配置文件无法加载**

   **症状**：启动日志中出现类似 "模块配置似乎未正确加载: conf/modules/storage.conf (测试键 storage::imported = )" 的警告

   **原因**：模块配置文件中缺少 `imported = true` 配置项，系统使用该配置项作为验证点来确认配置文件是否被正确加载

   **解决方案**：在每个模块配置文件（storage.conf、sms.conf、payment.conf、push.conf 等）的顶部添加以下配置：
   ```ini
   # 确保配置正确加载
   imported = true
   ```

2. **配置文件不存在**

   **症状**：启动日志中出现类似 "模块配置文件不存在: conf/modules/payment.conf" 的警告

   **解决方案**：根据模块需求创建相应的配置文件，确保每个文件都包含 `imported = true` 配置项

## 安装与部署

1. 克隆代码库

   ```bash
   git clone https://github.com/yourusername/O_Mall.git
   cd O_Mall
   ```

2. 安装依赖

   ```bash
   go mod tidy
   ```

3. 配置数据库

   ```bash
   # 编辑配置文件
   vim conf/app.conf
   ```

4. 初始化数据库

   ```bash
   go run scripts/db_init.go
   ```

5. 启动应用

   ```bash
   go run main.go
   ```

## 开发计划

- 第一阶段：核心功能开发（用户、商品、订单系统）
- 第二阶段：商家管理和支付系统
- 第三阶段：配送和跑腿系统
- 第四阶段：积分、礼品卡和评价系统
- 第五阶段：管理员系统和数据分析
- 第六阶段：性能优化和安全加固

## 贡献指南

1. Fork 这个仓库
2. 创建功能分支 (`git checkout -b feature/your-feature`)
3. 提交更改 (`git commit -m 'Add some feature'`)
4. 推送到分支 (`git push origin feature/your-feature`)
5. 创建一个 Pull Request

## 许可证

本项目采用 [MIT License](LICENSE)。

## 项目规范

### 日志处理规范

- **日志格式统一**
  - 所有日志必须包含方法名作为前缀，格式为 `[MethodName]`
  - 错误日志必须包含详细的错误信息和上下文
  - 使用 `logs.Error()` 记录错误日志
  - 日志信息应该清晰描述问题，便于定位和排查

示例：
```go
logs.Error("[CreateIncomeLog] 创建跑腿员收入记录失败: %v", err)
```

### 事务处理规范

- **事务操作流程**
  1. 开启事务：使用 `tx, err := o.Begin()`
  2. 错误处理：所有事务操作都需要进行错误处理
  3. 回滚处理：发生错误时必须调用 `tx.Rollback()`
  4. 提交事务：成功完成后调用 `tx.Commit()`

- **事务代码模板**
```go
o := orm.NewOrm()
tx, err := o.Begin()
if err != nil {
    logs.Error("[MethodName] 开启事务失败: %v", err)
    return fmt.Errorf("操作失败: %v", err)
}

// 执行数据库操作
_, err = tx.Insert(entity)
if err != nil {
    tx.Rollback()
    logs.Error("[MethodName] 数据库操作失败: %v", err)
    return fmt.Errorf("操作失败: %v", err)
}

// 提交事务
err = tx.Commit()
if err != nil {
    tx.Rollback()
    logs.Error("[MethodName] 提交事务失败: %v", err)
    return fmt.Errorf("操作失败: %v", err)
}
```

- **事务使用原则**
  1. 涉及多个数据库操作时必须使用事务
  2. 事务应该尽可能短，避免长事务
  3. 事务中避免调用外部服务
  4. 确保所有错误情况都能正确回滚

### 错误处理规范

- **错误返回格式**
  - 使用 `fmt.Errorf()` 包装错误信息
  - 错误信息应该简洁明了
  - 包含必要的上下文信息

- **错误日志记录**
  - 在返回错误之前记录详细日志
  - 包含方法名、错误原因和相关参数
  - 便于问题定位和追踪

## 统一错误处理范式

为保证API响应的一致性，系统采用统一的错误处理机制。所有接口返回标准的JSON格式，错误码和消息统一管理。

### 响应格式

```json
{
  "code": 200,       // 状态码，200表示成功，其他表示错误
  "message": "消息",  // 状态消息，成功时为"success"，失败时为错误描述
  "data": {}         // 返回数据，可以是对象、数组或null
}
```

### 预定义错误码

| 错误码 | 常量名 | 描述 |
|-------|--------|------|
| 200 | CodeSuccess | 请求成功 |
| 400 | CodeInvalidParam | 参数错误或请求格式错误 |
| 401 | CodeUnauthorized | 未授权，需要登录 |
| 403 | CodeForbidden | 禁止访问，无权限 |
| 404 | CodeNotFound | 资源不存在 |
| 500 | CodeError | 服务器内部错误 |

### 错误处理方法

在控制器中，使用以下方法处理错误和成功响应：

```go
// 成功响应
result.OK(ctx, data)

// 分页成功响应
result.OKWithPagination(ctx, data, total)

// 错误响应
result.HandleError(ctx, result.ErrInvalidParam)
result.HandleError(ctx, result.ErrInternal)
result.HandleError(ctx, result.ErrUnauthorized)
result.HandleError(ctx, result.ErrForbidden)
result.HandleError(ctx, result.ErrNotFound)

// 自定义错误
customErr := result.NewError(400, "自定义错误消息")
result.HandleError(ctx, customErr)
```

### 实现示例

```go
// 控制器示例
func (c *UserController) Get() {
    // 解析参数
    idStr := c.GetString(":id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil {
        result.HandleError(c.Ctx, result.ErrInvalidParam)
        return
    }

    // 调用服务
    user, err := c.userService.GetUser(c.Ctx.Request.Context(), id)
    if err != nil {
        logs.Error("获取用户失败: %v", err)
        result.HandleError(c.Ctx, result.ErrInternal)
        return
    }

    // 检查结果
    if user == nil {
        result.HandleError(c.Ctx, result.ErrNotFound)
        return
    }

    // 返回成功结果
    result.OK(c.Ctx, user)
}
```

所有API接口都应遵循这一错误处理规范，确保前端收到一致的响应格式，便于错误处理和用户体验优化。

## 权限管理

### 权限管理

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取权限列表 | GET | /api/v1/permissions | 获取权限列表，支持分页和条件查询 |
| 获取权限详情 | GET | /api/v1/permissions/:id | 获取权限详情 |
| 创建权限 | POST | /api/v1/permissions | 创建权限 |
| 更新权限 | PUT | /api/v1/permissions/:id | 更新权限 |
| 删除权限 | DELETE | /api/v1/permissions/:id | 删除权限 |

### 角色管理

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取角色列表 | GET | /api/v1/roles | 获取角色列表，支持分页和条件查询 |
| 获取角色详情 | GET | /api/v1/roles/:id | 获取角色详情 |
| 创建角色 | POST | /api/v1/roles | 创建角色 |
| 更新角色 | PUT | /api/v1/roles/:id | 更新角色 |
| 删除角色 | DELETE | /api/v1/roles/:id | 删除角色 |

### 角色分配

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 分配角色 | POST | /api/v1/roles/assign | 分配角色 |
| 分配权限 | PUT | /api/v1/roles/:id/permissions | 分配权限 |

### 用户权限

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取用户权限列表 | GET | /api/v1/users/:user_id/permissions | 获取用户权限列表 |
| 获取用户角色列表 | GET | /api/v1/users/:user_id/roles | 获取用户角色列表 |

## 开发与测试

### 配置开发环境

1. 安装Go 1.19或更高版本

2. 安装MySQL和Redis

3. 克隆代码库并安装依赖
```bash
git clone https://github.com/yourusername/o_mall.git
cd o_mall
go mod tidy
```

4. 配置环境变量或app.conf
```
runmode = "dev"
mysqluser = "root"
mysqlpass = "your-password"
mysqlhost = "localhost"
mysqlport = "3306"
mysqldb = "o_mall_dev"
redishost = "localhost"
redisport = "6379"
redispass = ""
redisdb = "0"
```

5. 启动应用
```bash
go run main.go
```

### API文档

应用内置了API文档模块，可以实时查看和测试API接口。API文档模块自动收集各模块暴露的API和对应的参数结构，便于前端开发人员了解接口情况。

#### API文档模块功能

- API信息自动收集与展示
- 按模块分类展示API接口
- 提供API请求/响应数据结构查询
- 实时同步后端API变更
- 支持多环境API文档管理

#### API文档模块接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/apidoc/modules` | GET | 获取所有模块列表 |
| `/api/v1/apidoc/modules/:module/apis` | GET | 获取指定模块的API列表 |
| `/api/v1/apidoc/modules/:module/dtos` | GET | 获取指定模块的DTO列表 |
| `/api/v1/apidoc/apis` | GET | 获取所有API列表（支持分页和筛选） |
| `/api/v1/apidoc/apis/:id` | GET | 获取API详情 |
| `/api/v1/apidoc/apis/:id/dto` | GET | 获取API及其关联DTO详情 |
| `/api/v1/apidoc/dtos/name/:name` | GET | 根据DTO名称获取结构详情 |

#### 使用方法

1. 启动应用后访问API文档地址（开发环境）：
   - `http://localhost:8080/api/v1/apidoc/modules` - 查看所有模块
   - `http://localhost:8080/api/v1/apidoc/apis` - 查看所有API

2. 管理和维护API文档：
   - 添加新API: POST `/api/v1/apidoc/apis`
   - 添加新DTO: POST `/api/v1/apidoc/dtos`
   - 更新API信息: PUT `/api/v1/apidoc/apis/:id`
   - 更新DTO信息: PUT `/api/v1/apidoc/dtos/:id`

3. 清除缓存（当API有重大变更时）:
   - POST `/api/v1/apidoc/cache/clear`
