-- 查看用户促销使用情况的SQL脚本

-- 1. 查看用户ID为2对促销活动ID为1的使用记录
SELECT 
    id,
    user_id,
    promotion_id,
    order_id,
    merchant_id,
    discount_amount,
    used_time,
    created_at
FROM takeout_user_promotion 
WHERE user_id = 2 AND promotion_id = 1
ORDER BY created_at DESC;

-- 2. 统计用户ID为2对促销活动ID为1的使用次数
SELECT 
    COUNT(*) as usage_count
FROM takeout_user_promotion 
WHERE user_id = 2 AND promotion_id = 1;

-- 3. 查看促销活动ID为1的详细信息
SELECT 
    id,
    merchant_id,
    name,
    description,
    type,
    status,
    rules,
    max_usage_count,
    usage_count,
    start_time,
    end_time,
    created_at
FROM takeout_promotion 
WHERE id = 1;

-- 4. 查看所有用户对促销活动ID为1的使用统计
SELECT 
    user_id,
    COUNT(*) as usage_count,
    SUM(discount_amount) as total_discount,
    MIN(used_time) as first_used,
    MAX(used_time) as last_used
FROM takeout_user_promotion 
WHERE promotion_id = 1
GROUP BY user_id
ORDER BY usage_count DESC;

-- 5. 如果需要清除用户ID为2对促销活动ID为1的使用记录（测试用）
-- DELETE FROM takeout_user_promotion WHERE user_id = 2 AND promotion_id = 1;

-- 6. 如果需要修改促销活动的使用限制（将per_user_limit从1改为2）
-- UPDATE takeout_promotion 
-- SET rules = REPLACE(rules, '"per_user_limit":1', '"per_user_limit":2')
-- WHERE id = 1;
