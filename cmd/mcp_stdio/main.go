/**
 * MCP标准输入输出服务
 *
 * 通过标准输入输出方式提供MCP服务，可以作为子进程被其他应用调用，
 * 或者在命令行中直接运行。提供与HTTP服务相同的功能。
 */

package main

import (
	"context"
	"fmt"
	"log"
	"os"
	
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// 主函数 - 启动标准输入输出MCP服务
func main() {
	// 创建MCP服务器
	s := server.NewMCPServer(
		"O_Mall MCP服务", 
		"1.0.1",
		server.WithToolCapabilities(true),
	)

	// 注册示例工具
	registerHelloTool(s)
	
	// 启动标准输入输出服务
	fmt.Fprintln(os.Stderr, "启动O_Mall MCP标准输入输出服务...")
	if err := server.ServeStdio(s); err != nil {
		log.Fatalf("服务运行失败: %v", err)
	}
}

// registerHelloTool 注册打招呼工具
func registerHelloTool(s *server.MCPServer) {
	// 创建工具
	tool := mcp.NewTool("hello_world",
		mcp.WithDescription("向用户打招呼"),
		mcp.WithString("name",
			mcp.Required(),
			mcp.Description("用户名称"),
		),
	)
	
	// 添加工具处理器
	s.AddTool(tool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// 获取参数
		name, err := request.RequireString("name")
		if err != nil {
			return mcp.NewToolResultError(err.Error()), nil
		}
		
		// 返回结果
		return mcp.NewToolResultText(fmt.Sprintf("你好，%s！欢迎使用O_Mall MCP服务！", name)), nil
	})
}
