/**
 * auth.go
 * 认证工具包
 *
 * 本文件提供了认证相关的工具函数，用于从请求上下文中获取用户信息
 */

package auth

import (
	"errors"

	"github.com/beego/beego/v2/server/web/context"
)

// GetUserIDFromContext 从上下文中获取用户ID
func GetUserIDFromContext(ctx *context.Context) (int64, error) {
	// 从上下文中获取用户ID
	userID, ok := ctx.Input.GetData("userID").(int64)
	if !ok || userID <= 0 {
		return 0, errors.New("未获取到有效的用户ID")
	}
	return userID, nil
}

// GetMerchantIDFromContext 从上下文中获取商家ID
func GetMerchantIDFromContext(ctx *context.Context) (int64, error) {
	// 从上下文中获取商家ID
	merchantID, ok := ctx.Input.GetData("merchantID").(int64)
	if !ok || merchantID <= 0 {
		return 0, errors.New("未获取到有效的商家ID")
	}
	return merchantID, nil
}