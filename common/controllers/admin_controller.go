/**
 * admin_controller.go
 * 管理员控制器
 *
 * 本文件定义了管理员控制器的基础功能，包括权限验证等通用方法
 */

package controllers

import (
	"strconv"
	//"strings" // 导入 strings 包

	"o_mall_backend/common/result"
	//"o_mall_backend/modules/admin/dto" // 导入 dto 包

	//"o_mall_backend/modules/admin/services" // 导入 admin_service 包
	//"o_mall_backend/utils" // 导入 utils 包

	"github.com/beego/beego/v2/core/logs"
)

// AdminController 管理员控制器
type AdminController struct {
	BaseController
}

// Prepare 预处理
func (c *AdminController) Prepare() {
	// 调用父类的Prepare方法
	c.BaseController.Prepare()

	// 解析 multipart/form-data 格式的请求
	err := c.Ctx.Request.ParseMultipartForm(32 << 20) // 限制最大内存为 32MB
	if err != nil {
		logs.Error("ParseMultipartForm error: %v", err)
		c.Response<PERSON>rror(500, "Failed to parse multipart form")
		c.<PERSON>()
		return
	}
}

// IsAdmin 判断当前用户是否为管理员
func (c *AdminController) IsAdmin() bool {
	// 从请求头中获取管理员ID
	adminIDStr := c.Ctx.Input.Header("X-Admin-ID")
	if adminIDStr == "" {
		return false
	}

	// 转换为整数
	adminID, err := strconv.ParseInt(adminIDStr, 10, 64)
	if err != nil || adminID <= 0 {
		return false
	}

	// TODO: 可以添加更多的权限验证逻辑

	return true
}

// IsRunner 判断当前用户是否为跑腿员
func (c *AdminController) IsRunner() bool {
	// 从请求头中获取跑腿员ID
	runnerIDStr := c.Ctx.Input.Header("X-Runner-ID")
	if runnerIDStr == "" {
		return false
	}

	// 转换为整数
	runnerID, err := strconv.ParseInt(runnerIDStr, 10, 64)
	if err != nil || runnerID <= 0 {
		return false
	}

	return true
}

// IsShop 判断当前用户是否为商家
func (c *AdminController) IsShop() bool {
	// 从请求头中获取商家ID
	shopIDStr := c.Ctx.Input.Header("X-Shop-ID")
	if shopIDStr == "" {
		return false
	}

	// 转换为整数
	shopID, err := strconv.ParseInt(shopIDStr, 10, 64)
	if err != nil || shopID <= 0 {
		return false
	}

	return true
}

// GetCurrentUserID 获取当前用户ID
func (c *AdminController) GetCurrentUserID() int64 {
	return c.GetUserID()
}

// GetShopID 获取当前商家ID
func (c *AdminController) GetShopID() int64 {
	// 从请求头中获取商家ID
	shopIDStr := c.Ctx.Input.Header("X-Shop-ID")
	if shopIDStr == "" {
		return 0
	}

	// 转换为整数
	shopID, err := strconv.ParseInt(shopIDStr, 10, 64)
	if err != nil {
		return 0
	}

	return shopID
}

// ParseAndValidate 解析并验证请求参数
func (c *AdminController) ParseAndValidate(v interface{}) error {
	return c.ParseAndValidateJSON(v)
}

// ResponseError 错误响应，扩展基础控制器的错误响应方法
func (c *AdminController) ResponseError(errCode int, errMsg string) {
	logs.Error("管理员操作错误: %s", errMsg)
	result.HandleError(c.Ctx, result.NewError(errCode, errMsg))
}

// ResponseSuccess 成功响应，扩展基础控制器的成功响应方法
func (c *AdminController) ResponseSuccess(message string, data interface{}) {
	resp := map[string]interface{}{
		"message": message,
		"data":    data,
	}
	c.BaseController.ResponseSuccess(resp)
}
