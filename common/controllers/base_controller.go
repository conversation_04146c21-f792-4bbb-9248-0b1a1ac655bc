/**
 * base_controller.go
 * 基础控制器
 *
 * 本文件定义了控制器的基础功能，包括参数验证、响应处理等通用方法
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"o_mall_backend/common/result"

	"github.com/beego/beego/v2/server/web"
)

// BaseController 基础控制器
type BaseController struct {
	web.Controller
}

// Response 基础响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// Prepare 预处理
func (c *BaseController) Prepare() {
	// 可以在这里添加通用的预处理逻辑
}

// ResponseSuccess 成功响应
func (c *BaseController) ResponseSuccess(data interface{}) {
	result.OK(c.Ctx, data)
}

// ResponseSuccessWithPagination 分页成功响应
func (c *BaseController) ResponseSuccessWithPagination(data interface{}, total int64, page int, pageSize int) {
	result.OKWithPagination(c.Ctx, data, total, page, pageSize)
}

// ResponseError 错误响应
func (c *BaseController) ResponseError(message string) {
	result.HandleError(c.Ctx, result.NewError(result.CodeError, message))
}

// GetUserID 获取当前用户ID
func (c *BaseController) GetUserID() int64 {
	userIDStr := c.Ctx.Input.Header("X-User-ID")
	if userIDStr == "" {
		return 0
	}
	userID, _ := strconv.ParseInt(userIDStr, 10, 64)
	return userID
}

// GetRunnerID 获取跑腿员ID
func (c *BaseController) GetRunnerID() int64 {
	runnerIDStr := c.Ctx.Input.Header("X-Runner-ID")
	if runnerIDStr == "" {
		return 0
	}
	runnerID, _ := strconv.ParseInt(runnerIDStr, 10, 64)
	return runnerID
}

// ParseAndValidateJSON 解析并验证JSON请求
func (c *BaseController) ParseAndValidateJSON(v interface{}) error {
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, v); err != nil {
		return err
	}
	// TODO: 添加验证逻辑
	return nil
}

// BindJSON 绑定JSON请求到结构体
func (c *BaseController) BindJSON(v interface{}) error {
	return json.Unmarshal(c.Ctx.Input.RequestBody, v)
}
