/*
 * 通用文件上传控制器
 *
 * 该文件提供了通用的文件上传API接口，可被各模块复用。
 * 统一处理文件上传、查询、删除等操作的HTTP请求。
 */

package controllers

import (
	"strconv"
	"strings"

	"github.com/beego/beego/v2/core/logs"

	commonServices "o_mall_backend/common/services"
	"o_mall_backend/common/response"
	"o_mall_backend/utils/config"
	chatControllers "o_mall_backend/modules/chat/controllers"
)

// CommonUploadController 通用文件上传控制器
type CommonUploadController struct {
	chatControllers.BaseController
	uploadService commonServices.CommonUploadService
	module        string // 模块名称
}

// NewCommonUploadController 创建通用上传控制器实例
func NewCommonUploadController(module string) *CommonUploadController {
	return &CommonUploadController{
		uploadService: commonServices.NewCommonUploadService(),
		module:        module,
	}
}

// Prepare 预处理方法
func (c *CommonUploadController) Prepare() {
	// 设置CORS头
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
	
	// 处理OPTIONS请求
	if c.Ctx.Request.Method == "OPTIONS" {
		c.Ctx.Output.SetStatus(200)
		return
	}
}

// Upload 文件上传接口
// @Title 文件上传
// @Description 上传文件到服务器
// @Param file formData file true "上传的文件"
// @Param file_usage formData string true "文件用途"
// @Param is_anonymous formData bool false "是否匿名上传"
// @Success 200 {object} commonServices.CommonUploadResponse
// @Failure 400 {object} response.ErrorResponse
// @router /upload [post]
func (c *CommonUploadController) Upload() {
	ctx := c.Ctx.Request.Context()
	
	// 获取上传的文件
	file, fileHeader, err := c.Ctx.Request.FormFile("file")
	if err != nil {
		logs.Error("获取上传文件失败: %v", err)
		response.ErrorBadRequest(c.Ctx, "获取上传文件失败")
		return
	}
	defer file.Close()
	
	// 解析请求参数
	fileUsage := c.GetString("file_usage")
	isAnonymousStr := c.GetString("is_anonymous", "false")
	isAnonymous := strings.ToLower(isAnonymousStr) == "true"
	
	// 获取认证token
	token := c.Ctx.Request.Header.Get("Authorization")
	if token != "" && strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}
	
	// 匿名上传逻辑：
	// 1. 如果明确指定is_anonymous=true，则进行匿名上传（忽略token）
	// 2. 如果没有token且没有明确指定is_anonymous，则自动设置为匿名上传
	if token == "" && !strings.Contains(strings.ToLower(isAnonymousStr), "true") {
		isAnonymous = true
	}
	
	// 添加调试日志
	logs.Info("Upload Controller - token存在: %v, isAnonymousStr: %s, isAnonymous: %v, fileUsage: %s", token != "", isAnonymousStr, isAnonymous, fileUsage)
	
	// 验证必要参数
	if fileUsage == "" {
		response.ErrorBadRequest(c.Ctx, "文件用途不能为空")
		return
	}
	
	// 构建上传请求
	uploadReq := &commonServices.CommonUploadRequest{
		FileUsage:   fileUsage,
		IsAnonymous: isAnonymous,
		Module:      c.module,
	}
	
	// 添加调试日志
	logs.Info("Upload Controller - 构建的请求: FileUsage=%s, IsAnonymous=%v, Module=%s", uploadReq.FileUsage, uploadReq.IsAnonymous, uploadReq.Module)
	
	// 清除配置缓存，确保使用最新的数据库配置（开发调试用）
	config.ClearUploadConfigCache()
	
	// 验证上传权限
	if err := c.uploadService.ValidateUploadPermission(ctx, uploadReq, token); err != nil {
		logs.Error("上传权限验证失败: %v", err)
		response.ErrorBadRequest(c.Ctx, err.Error())
		return
	}
	
	// 执行文件上传
	result, err := c.uploadService.Upload(ctx, file, fileHeader, uploadReq, token)
	if err != nil {
		logs.Error("文件上传失败: %v", err)
		response.ErrorBadRequest(c.Ctx, "文件上传失败: "+err.Error())
		return
	}
	
	logs.Info("文件上传成功: %s, 模块: %s", result.FileName, c.module)
	response.Success(c.Ctx, result)
}

// GetFile 获取文件信息
func (c *CommonUploadController) GetFile() {
	// 处理CORS
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if c.Ctx.Request.Method == "OPTIONS" {
		c.Ctx.Output.SetStatus(200)
		return
	}

	// 获取文件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ErrorBadRequest(c.Ctx, "无效的文件ID")
		return
	}

	// 获取文件信息
	result, err := c.uploadService.GetByID(c.Ctx.Request.Context(), id)
	if err != nil {
		response.ErrorBadRequest(c.Ctx, err.Error())
		return
	}

	response.Success(c.Ctx, result)
}

// List 获取文件列表
func (c *CommonUploadController) List() {
	// 处理CORS
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if c.Ctx.Request.Method == "OPTIONS" {
		c.Ctx.Output.SetStatus(200)
		return
	}

	// 获取查询参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)
	fileUsage := c.GetString("file_usage")

	// 构建查询请求
	req := &commonServices.CommonUploadQueryRequest{
		Page:      page,
		PageSize:  pageSize,
		FileUsage: fileUsage,
		Module:    c.module,
	}

	// 获取文件列表
	result, total, err := c.uploadService.List(c.Ctx.Request.Context(), req)
	if err != nil {
		response.ErrorBadRequest(c.Ctx, err.Error())
		return
	}

	// 构建响应
	responseData := map[string]interface{}{
		"list":  result,
		"total": total,
		"page":  page,
		"page_size": pageSize,
	}

	response.Success(c.Ctx, responseData)
}

// Delete 删除文件
func (c *CommonUploadController) Delete() {
	// 处理CORS
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "DELETE, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if c.Ctx.Request.Method == "OPTIONS" {
		c.Ctx.Output.SetStatus(200)
		return
	}

	// 获取文件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ErrorBadRequest(c.Ctx, "无效的文件ID")
		return
	}

	// 删除文件
	err = c.uploadService.Delete(c.Ctx.Request.Context(), id)
	if err != nil {
		response.ErrorBadRequest(c.Ctx, err.Error())
		return
	}

	response.Success(c.Ctx, "删除成功")
}

// GetConfig 获取上传配置
func (c *CommonUploadController) GetConfig() {
	// 处理CORS
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if c.Ctx.Request.Method == "OPTIONS" {
		c.Ctx.Output.SetStatus(200)
		return
	}

	// 获取上传配置
	result, err := c.uploadService.GetUploadConfig(c.Ctx.Request.Context(), c.module)
	if err != nil {
		response.ErrorBadRequest(c.Ctx, err.Error())
		return
	}

	response.Success(c.Ctx, result)
}