/**
 * response.go
 * 响应工具包
 *
 * 本文件提供了HTTP响应相关的工具函数，用于统一处理API响应格式
 */

package response

import (
	"github.com/beego/beego/v2/server/web/context"
)

// ApiResponse API响应结构
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// Success 返回成功响应
func Success(ctx *context.Context, data interface{}) {
	resp := ApiResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	}
	ctx.Output.SetStatus(200)
	ctx.Output.JSON(resp, false, false)
}

// ErrorBadRequest 返回400错误响应
func ErrorBadRequest(ctx *context.Context, message string) {
	resp := ApiResponse{
		Code:    400,
		Message: message,
	}
	ctx.Output.SetStatus(400)
	ctx.Output.JSON(resp, false, false)
}

// ErrorUnauthorized 返回401错误响应
func ErrorUnauthorized(ctx *context.Context) {
	resp := ApiResponse{
		Code:    401,
		Message: "未授权的请求",
	}
	ctx.Output.SetStatus(401)
	ctx.Output.JSON(resp, false, false)
}

// ErrorForbidden 返回403错误响应
func ErrorForbidden(ctx *context.Context) {
	resp := ApiResponse{
		Code:    403,
		Message: "禁止访问",
	}
	ctx.Output.SetStatus(403)
	ctx.Output.JSON(resp, false, false)
}

// ErrorNotFound 返回404错误响应
func ErrorNotFound(ctx *context.Context) {
	resp := ApiResponse{
		Code:    404,
		Message: "资源不存在",
	}
	ctx.Output.SetStatus(404)
	ctx.Output.JSON(resp, false, false)
}

// ErrorInternal 返回500错误响应
func ErrorInternal(ctx *context.Context, message string) {
	resp := ApiResponse{
		Code:    500,
		Message: message,
	}
	ctx.Output.SetStatus(500)
	ctx.Output.JSON(resp, false, false)
}
