/**
 * 通用响应处理
 *
 * 该文件定义了统一的响应处理方法，包括成功响应和错误响应的处理。
 */

package result

import (
	"github.com/beego/beego/v2/server/web/context"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`    // 状态码
	Message string      `json:"message"` // 状态消息
	Data    interface{} `json:"data"`    // 响应数据
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	List     interface{} `json:"list"`     // 数据列表
	Total    int64       `json:"total"`    // 总记录数
	Page     int         `json:"page"`     // 当前页码
	PageSize int         `json:"pageSize"` // 每页数量
}

// OK 返回成功响应
func OK(ctx *context.Context, data interface{}) {
	resp := &Response{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
	}
	ctx.Output.JSON(resp, false, false)
}

// OKWithPagination 返回分页成功响应
func OKWithPagination(ctx *context.Context, list interface{}, total int64, page int, pageSize int) {
	resp := &Response{
		Code:    CodeSuccess,
		Message: "success",
		Data: &PaginationResponse{
			List:     list,
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		},
	}
	ctx.Output.JSON(resp, false, false)
}

// HandleError 错误响应处理
func HandleError(ctx *context.Context, err error, args ...interface{}) {
	var result *Result

	switch e := err.(type) {
	case *Error:
		result = &Result{
			Code:    e.Code,
			Message: e.Message,
		}
		if e.Details != "" {
			result.Data = map[string]string{
				"details": e.Details,
			}
		}
	default:
		if len(args) > 0 {
			if msg, ok := args[0].(string); ok {
				result = &Result{
					Code:    CodeError,
					Message: msg,
				}
			}
		} else {
			result = &Result{
				Code:    CodeError,
				Message: err.Error(),
			}
		}
	}

	ctx.Output.JSON(result, false, false)
}
