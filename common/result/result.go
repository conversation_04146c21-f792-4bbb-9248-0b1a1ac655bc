/**
 * result.go
 * 通用响应结构和错误处理
 *
 * 本文件定义了API响应的标准格式和常用错误类型
 */

package result

// "o_-mall_backend/common/result/errors"

// Result 统一API响应结构
type Result struct {
	Code    int         `json:"code"`    // 响应状态码
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// 预定义错误
var (
	ErrInternal      = NewError(CodeError, "服务器内部错误")
	ErrInvalidParam  = NewError(CodeInvalidParams, "参数错误")
	ErrUnauthorized  = NewError(CodeUnauthorized, "未授权")
	ErrForbidden     = NewError(CodeForbidden, "禁止访问")
	ErrNotFound      = NewError(CodeNotFound, "资源不存在")
	ErrInvalidParams = NewError(CodeInvalidParams, "无效的请求参数")
	ErrInternalError = NewError(CodeInternalError, "服务器内部错误")
)

// Error 自定义错误类型
type Error struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"` // 详细错误信息
}

// Error 实现error接口
func (e *Error) Error() string {
	if e.Details != "" {
		return e.Message + ": " + e.Details
	}
	return e.Message
}

// WithDetails 添加错误详情
func (e *Error) WithDetails(details string) *Error {
	return &Error{
		Code:    e.Code,
		Message: e.Message,
		Details: details,
	}
}

// NewError 创建新的错误
func NewError(code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}
