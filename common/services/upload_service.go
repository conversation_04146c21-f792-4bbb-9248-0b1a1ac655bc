/*
 * 通用文件上传服务接口
 *
 * 该文件定义了通用的文件上传服务接口，用于统一各模块的文件上传功能。
 * 提供标准化的文件上传、查询、删除等操作接口。
 */

package services

import (
	"context"
	"mime/multipart"
	"time"
)

// CommonUploadRequest 通用文件上传请求
type CommonUploadRequest struct {
	FileUsage      string `form:"file_usage" json:"file_usage"`     // 文件用途
	IsAnonymous    bool   `form:"is_anonymous" json:"is_anonymous"` // 是否匿名上传(默认false)
	AllowAnonymous bool   `form:"-" json:"-"`                       // 是否允许匿名上传(内部字段)
	Module         string `form:"module" json:"module"`             // 模块名称(admin/merchant/user等)
}

// CommonUploadResponse 通用文件上传响应
type CommonUploadResponse struct {
	ID          int64     `json:"id"`           // 文件ID
	FileName    string    `json:"file_name"`    // 文件名
	FilePath    string    `json:"file_path"`    // 文件路径
	FileURL     string    `json:"file_url"`     // 文件访问URL
	FileSize    int64     `json:"file_size"`    // 文件大小(字节)
	FileType    string    `json:"file_type"`    // 文件类型(MIME类型)
	FileExt     string    `json:"file_ext"`     // 文件扩展名
	FileUsage   string    `json:"file_usage"`   // 文件用途
	IsAnonymous bool      `json:"is_anonymous"` // 是否匿名上传
	Storage     string    `json:"storage"`      // 存储位置
	Module      string    `json:"module"`       // 模块名称
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
}

// CommonUploadQueryRequest 通用文件查询请求
type CommonUploadQueryRequest struct {
	FileUsage   string    `form:"file_usage" json:"file_usage"`     // 文件用途
	UserType    string    `form:"user_type" json:"user_type"`       // 上传者类型
	UserID      int64     `form:"user_id" json:"user_id"`           // 上传者ID
	Username    string    `form:"username" json:"username"`         // 上传者用户名
	IsAnonymous *bool     `form:"is_anonymous" json:"is_anonymous"` // 是否匿名上传
	Module      string    `form:"module" json:"module"`             // 模块名称
	StartTime   time.Time `form:"start_time" json:"start_time"`     // 开始时间
	EndTime     time.Time `form:"end_time" json:"end_time"`         // 结束时间
	Page        int       `form:"page" json:"page"`                 // 页码
	PageSize    int       `form:"page_size" json:"page_size"`       // 每页数量
}

// CommonUploadListResponse 通用文件列表响应
type CommonUploadListResponse struct {
	Total int64                    `json:"total"` // 总数
	List  []*CommonUploadResponse `json:"list"`  // 文件列表
}

// CommonUploadConfigResponse 通用上传配置响应
type CommonUploadConfigResponse struct {
	AllowAnonymous      bool     `json:"allow_anonymous"`       // 是否允许匿名上传
	MaxFileSize         int64    `json:"max_file_size"`         // 最大文件大小(字节)
	AllowedFileTypes    []string `json:"allowed_file_types"`    // 允许的文件类型
	StorageMode         string   `json:"storage_mode"`          // 存储模式
	UploadPath          string   `json:"upload_path"`           // 上传路径
	AllowedUsageTypes   []string `json:"allowed_usage_types"`   // 允许的用途类型
	AnonymousUsageTypes []string `json:"anonymous_usage_types"` // 匿名上传允许的用途类型
}

// CommonUploadService 通用文件上传服务接口
type CommonUploadService interface {
	// Upload 上传文件
	Upload(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader, req *CommonUploadRequest, token string) (*CommonUploadResponse, error)
	
	// GetByID 通过ID获取文件
	GetByID(ctx context.Context, id int64) (*CommonUploadResponse, error)
	
	// List 获取文件列表
	List(ctx context.Context, req *CommonUploadQueryRequest) ([]*CommonUploadResponse, int64, error)
	
	// Delete 删除文件
	Delete(ctx context.Context, id int64) error
	
	// GetUploadConfig 获取上传配置
	GetUploadConfig(ctx context.Context, module string) (*CommonUploadConfigResponse, error)
	
	// ValidateUploadPermission 验证上传权限
	ValidateUploadPermission(ctx context.Context, req *CommonUploadRequest, token string) error
	
	// GetUploadPath 获取上传路径
	GetUploadPath(fileUsage string, module string) string
}