/*
 * 通用文件上传服务实现
 *
 * 该文件实现了通用的文件上传服务接口，整合各模块的文件上传功能。
 * 提供统一的文件上传、查询、删除等操作实现。
 */

package services

import (
	"context"
	"fmt"
	"mime/multipart"

	adminDto "o_mall_backend/modules/admin/dto"
	adminRepos "o_mall_backend/modules/admin/repositories"
	adminServices "o_mall_backend/modules/admin/services"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// commonUploadServiceImpl 通用文件上传服务实现
type commonUploadServiceImpl struct {
	adminUploadService adminServices.UploadFileService
	uploadFileRepo     adminRepos.UploadFileRepository
}

// NewCommonUploadService 创建通用文件上传服务实例
func NewCommonUploadService() CommonUploadService {
	return &commonUploadServiceImpl{
		adminUploadService: adminServices.NewUploadFileService(),
		uploadFileRepo:     adminRepos.NewUploadFileRepository(),
	}
}

// Upload 上传文件
func (s *commonUploadServiceImpl) Upload(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader, req *CommonUploadRequest, token string) (*CommonUploadResponse, error) {
	// 根据模块转换请求格式
	switch req.Module {
	case "admin", "merchant", "user":
		// 转换为admin模块的请求格式
		adminReq := &adminDto.UploadFileRequest{
			FileUsage:      req.FileUsage,
			IsAnonymous:    req.IsAnonymous,
			AllowAnonymous: req.AllowAnonymous,
		}
		
		// 添加调试日志
		logs.Info("Common Upload Service - 原始请求: FileUsage=%s, IsAnonymous=%v, Module=%s", req.FileUsage, req.IsAnonymous, req.Module)
		logs.Info("Common Upload Service - 转换后的admin请求: FileUsage=%s, IsAnonymous=%v, AllowAnonymous=%v", adminReq.FileUsage, adminReq.IsAnonymous, adminReq.AllowAnonymous)
		
		// 调用admin模块的上传服务
		adminResp, err := s.adminUploadService.Upload(ctx, file, fileHeader, adminReq, token)
		if err != nil {
			return nil, err
		}
		
		// 转换响应格式
		return s.convertAdminResponse(adminResp, req.Module), nil
		
	default:
		return nil, fmt.Errorf("不支持的模块: %s", req.Module)
	}
}

// GetByID 通过ID获取文件
func (s *commonUploadServiceImpl) GetByID(ctx context.Context, id int64) (*CommonUploadResponse, error) {
	adminResp, err := s.adminUploadService.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	
	return s.convertAdminResponse(adminResp, "admin"), nil
}

// List 获取文件列表
func (s *commonUploadServiceImpl) List(ctx context.Context, req *CommonUploadQueryRequest) ([]*CommonUploadResponse, int64, error) {
	// 转换查询请求
	adminReq := &adminDto.UploadFileQueryRequest{
		FileUsage:   req.FileUsage,
		UserType:    req.UserType,
		UserID:      req.UserID,
		Username:    req.Username,
		IsAnonymous: req.IsAnonymous,
		StartTime:   req.StartTime,
		EndTime:     req.EndTime,
		Page:        req.Page,
		PageSize:    req.PageSize,
	}
	
	adminList, total, err := s.adminUploadService.List(ctx, adminReq)
	if err != nil {
		return nil, 0, err
	}
	
	// 转换响应列表
	commonList := make([]*CommonUploadResponse, len(adminList))
	for i, item := range adminList {
		commonList[i] = s.convertAdminResponse(item, req.Module)
	}
	
	return commonList, total, nil
}

// Delete 删除文件
func (s *commonUploadServiceImpl) Delete(ctx context.Context, id int64) error {
	return s.adminUploadService.Delete(ctx, id)
}

// GetUploadConfig 获取上传配置
func (s *commonUploadServiceImpl) GetUploadConfig(ctx context.Context, module string) (*CommonUploadConfigResponse, error) {
	adminConfig, err := s.adminUploadService.GetUploadConfig(ctx)
	if err != nil {
		return nil, err
	}
	
	// 转换配置响应
	commonConfig := &CommonUploadConfigResponse{
		AllowAnonymous:      adminConfig.AllowAnonymous,
		MaxFileSize:         adminConfig.MaxFileSize,
		AllowedFileTypes:    adminConfig.AllowedFileTypes,
		StorageMode:         adminConfig.StorageMode,
		UploadPath:          adminConfig.UploadPath,
		AllowedUsageTypes:   adminConfig.AllowedUsageTypes,
		AnonymousUsageTypes: adminConfig.AnonymousUsageTypes,
	}
	
	// 注意：匿名上传配置现在完全依赖数据库中的system_file_usage_config表
	// 不再在代码中硬编码特定的匿名上传类型
	// 如需添加新的匿名上传类型，请在数据库中设置allow_anonymous=1
	
	return commonConfig, nil
}

// ValidateUploadPermission 验证上传权限
func (s *commonUploadServiceImpl) ValidateUploadPermission(ctx context.Context, req *CommonUploadRequest, token string) error {
	// 获取上传配置
	config, err := s.GetUploadConfig(ctx, req.Module)
	if err != nil {
		return fmt.Errorf("获取上传配置失败: %v", err)
	}

	// 添加调试日志
	logs.Info("上传权限验证 - 模块: %s, 文件用途: %s, 匿名: %v, Token: %s", req.Module, req.FileUsage, req.IsAnonymous, token != "")
	logs.Info("配置信息 - 允许匿名: %v, 匿名用途类型: %v, 允许用途类型: %v", config.AllowAnonymous, config.AnonymousUsageTypes, config.AllowedUsageTypes)

	// 检查是否允许匿名上传
	if req.IsAnonymous {
		if !config.AllowAnonymous {
			logs.Error("系统不允许匿名上传")
			return fmt.Errorf("系统不允许匿名上传")
		}
		
		// 检查文件用途是否允许匿名上传
		if !contains(config.AnonymousUsageTypes, req.FileUsage) {
			logs.Error("文件用途 %s 不允许匿名上传，允许的匿名用途: %v", req.FileUsage, config.AnonymousUsageTypes)
			return fmt.Errorf("文件用途 %s 不允许匿名上传", req.FileUsage)
		}
	} else {
		// 非匿名上传需要验证token
		if token == "" {
			logs.Error("非匿名上传缺少认证token")
			return fmt.Errorf("缺少认证token")
		}
	}

	// 检查文件用途是否在允许列表中
	if !contains(config.AllowedUsageTypes, req.FileUsage) {
		logs.Error("不支持的文件用途: %s，允许的用途: %v", req.FileUsage, config.AllowedUsageTypes)
		return fmt.Errorf("不支持的文件用途: %s", req.FileUsage)
	}

	logs.Info("上传权限验证通过")
	return nil
}

// GetUploadPath 获取上传路径
func (s *commonUploadServiceImpl) GetUploadPath(module, fileUsage string) string {
	basePath, _ := web.AppConfig.String("upload.base_path")
	if basePath == "" {
		basePath = "./uploads"
	}
	return fmt.Sprintf("%s/%s/%s/", basePath, module, fileUsage)
}

// convertAdminResponse 转换admin模块响应为通用响应
func (s *commonUploadServiceImpl) convertAdminResponse(adminResp *adminDto.UploadFileResponse, module string) *CommonUploadResponse {
	return &CommonUploadResponse{
		ID:          adminResp.ID,
		FileName:    adminResp.FileName,
		FilePath:    adminResp.FilePath,
		FileURL:     adminResp.FileURL,
		FileSize:    adminResp.FileSize,
		FileType:    adminResp.FileType,
		FileExt:     adminResp.FileExt,
		FileUsage:   adminResp.FileUsage,
		IsAnonymous: adminResp.IsAnonymous,
		Storage:     adminResp.Storage,
		Module:      module,
		CreatedAt:   adminResp.CreatedAt,
	}
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}