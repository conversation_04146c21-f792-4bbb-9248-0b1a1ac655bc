/**
 * service_utils.go
 * 服务工具函数
 *
 * 本文件提供了服务相关的工具函数，包括服务实例获取等功能
 */

package utils

import (
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/services"
)

// 服务实例缓存
var serviceInstances = make(map[string]interface{})

// GetServiceInstance 获取服务实例
func GetServiceInstance(serviceName string) interface{} {
	// 如果已经有缓存的实例，直接返回
	if instance, ok := serviceInstances[serviceName]; ok {
		return instance
	}

	// 根据服务名称创建对应的服务实例
	var instance interface{}

	switch serviceName {
	case "deliveryAreaService":
		instance = services.NewDeliveryAreaService()
	case "deliveryMethodService":
		instance = services.NewDeliveryMethodService()
	case "deliveryRuleService":
		instance = services.NewDeliveryRuleService()
	case "deliveryTimeSlotService":
		instance = services.NewDeliveryTimeSlotService()
	case "deliveryOrderService":
		instance = services.NewDeliveryOrderService()
	default:
		logs.Error("未知的服务名称: %s", serviceName)
		return nil
	}

	// 缓存服务实例
	serviceInstances[serviceName] = instance

	return instance
}
