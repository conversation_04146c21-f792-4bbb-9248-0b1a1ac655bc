/**
 * string_utils.go
 * 字符串工具函数
 *
 * 本文件提供了字符串相关的工具函数，包括类型转换等功能
 */

package utils

import (
	"strconv"
)

// StrToInt64 将字符串转换为int64类型
func StrToInt64(str string) (int64, error) {
	if str == "" {
		return 0, nil
	}

	val, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		return 0, err
	}

	return val, nil
}

// StrToInt 将字符串转换为int类型
func StrToInt(str string) int {
	if str == "" {
		return 0
	}

	val, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}

	return val
}

// StrToFloat64 将字符串转换为float64类型
func StrToFloat64(str string) float64 {
	if str == "" {
		return 0
	}

	val, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0
	}

	return val
}

// StrToBool 将字符串转换为bool类型
func StrToBool(str string) bool {
	if str == "" {
		return false
	}

	val, err := strconv.ParseBool(str)
	if err != nil {
		return false
	}

	return val
}
