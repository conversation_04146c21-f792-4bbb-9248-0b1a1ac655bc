/**
 * utils.go
 * 通用工具函数
 *
 * 本文件提供了项目中常用的工具函数，包括字符串处理、时间处理、加密等功能
 */

package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"math/rand"
	"strings"
	"time"
)

// GenerateOrderNo 生成订单号
func GenerateOrderNo(prefix string) string {
	now := time.Now()
	timestamp := now.Format("20060102150405")
	random := rand.New(rand.NewSource(now.UnixNano()))
	randomNum := random.Intn(10000)
	return prefix + timestamp + fmt.Sprintf("%04d", randomNum)
}

// MD5 计算字符串的MD5值
func MD5(text string) string {
	hash := md5.New()
	hash.Write([]byte(text))
	return hex.EncodeToString(hash.Sum(nil))
}

// FormatTime 格式化时间
func FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// ParseTime 解析时间字符串（使用本地时区）
func ParseTime(timeStr string) (time.Time, error) {
	return time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local)
}

// Contains 检查切片是否包含某个元素
func Contains[T comparable](slice []T, element T) bool {
	for _, item := range slice {
		if item == element {
			return true
		}
	}
	return false
}

// RemoveDuplicates 移除切片中的重复元素
func RemoveDuplicates[T comparable](slice []T) []T {
	seen := make(map[T]bool)
	result := make([]T, 0)
	
	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// IsEmpty 检查字符串是否为空
func IsEmpty(s string) bool {
	return len(s) == 0
}

// IsBlank 检查字符串是否为空白
func IsBlank(s string) bool {
	return len(strings.TrimSpace(s)) == 0
}

// DefaultIfEmpty 如果字符串为空则返回默认值
func DefaultIfEmpty(s string, defaultValue string) string {
	if IsEmpty(s) {
		return defaultValue
	}
	return s
}