# O_Mall 主配置文件
# 应用基本配置
appname = O_Mall
httpport = 8181
runmode = dev
autorender = false
copyrequestbody = true
EnableDocs = true
dev_mode = true
# 接口配置
api_prefix = api
api_version = v1

# 是否刷新API文档
flushapidoc = false
# 数据库配置
mysqluser = o_mall_dev
mysqlpass = 4wk2HTRWkxKxwhHX
mysqlhost = *************
mysqlport = 3306
mysqldb   = o_mall_dev

runmodeabc = dev

# Redis配置
redishost = 127.0.0.1
redisport = 6379
redispass = CnCmQFCiYS34ox
redisdb = 1

# 日志配置
logfilepath = "./logs/o_mall.log"
loglevel = "debug"

# JWT配置
jwtSecret = "o_mall_secret_key"
jwtExpireHour = 24

# 上传文件配置
uploadPath = "./uploads"
uploadMaxSize = 10485760 # 10MB

# 跑腿员配置
runnerBaseFee = 5 # 基础费用，单位：元
runnerKmFee = 2   # 每公里费用，单位：元/公里

# 微信小程序配置
[wechat.miniapp]
enabled = true
app_id = wx319899ca2853d6ba
app_secret = 308fab5a841450110b53679b5cd76c68

# CORS跨域配置
[cors]
# 允许的源，多个源用逗号分隔，*表示允许所有源
allow_origins = "*"
# 允许的HTTP方法
allow_methods = "GET,POST,PUT,DELETE,OPTIONS,PATCH"
# 允许的HTTP头
allow_headers = "Origin,Content-Type,Accept,Authorization,X-Requested-With,platform"
# 是否允许携带凭证（cookies等）
allow_credentials = true
# 预检请求缓存时间（秒）
max_age = 86400
# 允许前端获取的响应头
expose_headers = "Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type"

# 存储配置
# [storage]
# mode = qiniu
# enable_cdn = false

# [qiniu]
# access_key = oKYsL8bDTr2W-FM0tysAxiS5X5IpDcTgDV2ZUXcC
# secret_key = aGiQNOi29_yKU1dWp_kDGeoctiHiByPc3MfWtrNw
# bucket = omall-img
# domain = omaillimg.qwyx.shop
# zone = z2
# use_https = false

# # OSS配置
# [storage.oss]
# endpoint = "oss-cn-hangzhou.aliyuncs.com"

# # COS配置
# [storage.cos]
# region = "ap-beijing"

# # S3配置
# [storage.s3]
# region = us-east-1
# access_key = your_access_key
# secret_key = your_secret_key
# bucket = your_bucket
# domain = your_domain
