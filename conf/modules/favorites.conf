# 用户收藏模块配置

# 收藏保留天数
favorites.retention_days = 365

# 收藏清理任务配置
favorites.cleanup.enabled = true
favorites.cleanup.cron = "0 3 * * *"  # 每天凌晨3点执行清理任务

# 收藏缓存配置
favorites.cache.enabled = true
favorites.cache.ttl = 600  # 缓存10分钟

# 收藏分页配置
favorites.page.default_size = 20
favorites.page.max_size = 100

# 收藏统计配置
favorites.statistics.enabled = true
favorites.statistics.cache_ttl = 1800  # 统计数据缓存30分钟

# 收藏导出配置
favorites.export.enabled = true
favorites.export.max_records = 10000  # 单次导出最大记录数

# 收藏类型配置
favorites.types.takeout_food.enabled = true
favorites.types.mall_product.enabled = true
favorites.types.merchant.enabled = true
favorites.types.category.enabled = true
favorites.types.combo.enabled = true
favorites.types.coupon.enabled = true

# 收藏夹配置
favorites.folder.max_count = 50        # 每个用户最大收藏夹数量
favorites.folder.default_name = "默认收藏夹"
favorites.folder.default_icon = "heart"
favorites.folder.default_color = "#ff6b6b"

# 收藏安全配置
favorites.security.max_favorites_per_user = 10000  # 每个用户最大收藏数
favorites.security.rate_limit = 60                 # 每分钟最大添加次数
favorites.security.duplicate_check = true          # 是否检查重复收藏

# 收藏分享配置
favorites.share.enabled = true
favorites.share.expire_days = 30       # 分享链接过期天数
favorites.share.qr_enabled = true      # 是否启用二维码分享

# 收藏同步配置
favorites.sync.enabled = false         # 是否启用跨设备同步
favorites.sync.interval = 300          # 同步间隔(秒)

# 收藏推荐配置
favorites.recommendation.enabled = true
favorites.recommendation.algorithm = "collaborative"  # 推荐算法：collaborative, content_based
favorites.recommendation.max_items = 20               # 最大推荐数量
