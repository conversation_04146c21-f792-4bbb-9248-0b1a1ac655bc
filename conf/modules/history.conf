# 用户历史记录模块配置

# 历史记录保留天数
history.retention_days = 365

# 历史记录清理任务配置
history.cleanup.enabled = true
history.cleanup.cron = "0 2 * * *"  # 每天凌晨2点执行清理任务

# 历史记录缓存配置
history.cache.enabled = true
history.cache.ttl = 300  # 缓存5分钟

# 历史记录分页配置
history.page.default_size = 20
history.page.max_size = 100

# 历史记录统计配置
history.statistics.enabled = true
history.statistics.cache_ttl = 600  # 统计数据缓存10分钟

# 历史记录导出配置
history.export.enabled = true
history.export.max_records = 10000  # 单次导出最大记录数

# 历史记录类型配置
history.types.takeout_food.enabled = true
history.types.mall_product.enabled = true
history.types.merchant.enabled = true
history.types.category.enabled = true
history.types.search.enabled = true
history.types.page.enabled = true

# 历史记录详情配置
history.detail.enabled = true
history.detail.retention_days = 30  # 详情记录保留30天

# 历史记录安全配置
history.security.max_records_per_user = 10000  # 每个用户最大历史记录数
history.security.rate_limit = 100  # 每分钟最大添加次数
