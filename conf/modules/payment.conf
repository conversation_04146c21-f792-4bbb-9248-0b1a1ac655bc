# 支付服务配置文件
# 此文件管理所有与支付服务相关的配置

# 标记配置是否已导入
imported = true

# 支付方式配置
# 可选值: alipay, wechat, paypal, stripe, custom
payment_methods = alipay,wechat

# 阿里支付配置
alipay_app_id = your_app_id
alipay_private_key = your_private_key
alipay_public_key = your_public_key
alipay_notify_url = https://example.com/api/v1/payment/alipay/notify
alipay_return_url = https://example.com/payment/callback
alipay_gateway = https://openapi.alipay.com/gateway.do
alipay_sandbox = true

# 微信支付配置
wechat_app_id = your_app_id
wechat_mch_id = your_mch_id
wechat_api_key = your_api_key
wechat_notify_url = https://example.com/api/v1/payment/wechat/notify
wechat_sandbox = true

# PayPal配置
paypal_client_id = your_client_id
paypal_client_secret = your_client_secret
paypal_mode = sandbox # 可选: sandbox, live
paypal_return_url = https://example.com/payment/success
paypal_cancel_url = https://example.com/payment/cancel

# Stripe配置
stripe_api_key = your_api_key
stripe_webhook_secret = your_webhook_secret
stripe_public_key = your_public_key
stripe_success_url = https://example.com/payment/success
stripe_cancel_url = https://example.com/payment/cancel
