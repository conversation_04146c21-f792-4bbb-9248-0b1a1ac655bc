# 推送通知配置文件
# 此文件管理所有与推送通知服务相关的配置

# 标记配置是否已导入
imported = true

# 推送通知提供商配置
# 可选值: jpush, getui, umeng, firebase, apns, custom
push_provider = jpush

# 极光推送配置
jpush_app_key = your_app_key
jpush_master_secret = your_master_secret
jpush_production = false # 是否为生产环境

# 个推配置
getui_app_id = your_app_id
getui_app_key = your_app_key
getui_master_secret = your_master_secret
getui_production = false # 是否为生产环境

# 友盟推送配置
umeng_android_app_key = your_android_app_key
umeng_android_app_secret = your_android_app_secret
umeng_ios_app_key = your_ios_app_key
umeng_ios_app_secret = your_ios_app_secret
umeng_production = false # 是否为生产环境

# Firebase推送配置
firebase_server_key = your_server_key
firebase_sender_id = your_sender_id
firebase_service_account_json = /path/to/service-account.json

# 苹果APNS推送配置
apns_cert_file = /path/to/cert.p12
apns_cert_password = your_cert_password
apns_is_production = false # 是否为生产环境
apns_topic = your.bundle.id

# 自定义推送API配置
custom_push_api_url = https://api.custom-push.com/send
custom_push_api_method = POST
custom_push_api_headers = Content-Type:application/json,Authorization:Bearer your_token
custom_push_api_body_template = {"token":"{token}","title":"{title}","body":"{body}","data":"{data}"}
custom_push_api_success_code = 200
