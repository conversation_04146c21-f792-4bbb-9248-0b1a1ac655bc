# 社区地址API文档

## 概述

社区地址API提供了对社区地址资源的管理功能，包括社区、楼栋、单元等三级层次结构的地址配置。每个地址节点都包含经纬度坐标，可用于计算距离和规划路径。

这些API需要管理员权限("super"角色)才能访问。

## API 列表

### 1. 创建社区地址

- **URL**: `/api/v1/system/secured/addresses`
- **方法**: `POST`
- **权限**: 需要超级管理员权限
- **功能**: 创建新的社区地址（小区、楼栋或单元）
- **请求体示例**:

```json
{
  "name": "阳光小区",
  "parentId": 0,
  "level": 1,
  "longitude": 116.397128,
  "latitude": 39.916527,
  "sort": 1,
  "status": 1,
  "description": "位于城市中心的高档小区"
}
```

- **参数说明**:
  - `name`: 地址名称
  - `parentId`: 父级ID（0表示顶级地址）
  - `level`: 地址级别（1=小区，2=楼栋，3=单元）
  - `longitude`: 经度
  - `latitude`: 纬度
  - `sort`: 排序值
  - `status`: 状态（0=禁用，1=启用）
  - `description`: 描述信息

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 更新社区地址

- **URL**: `/api/v1/system/secured/addresses`
- **方法**: `PUT`
- **权限**: 需要超级管理员权限
- **功能**: 更新现有社区地址信息
- **请求体示例**:

```json
{
  "id": 1,
  "name": "阳光花园小区",
  "parentId": 0,
  "level": 1,
  "longitude": 116.397128,
  "latitude": 39.916527,
  "sort": 1,
  "status": 1,
  "description": "位于城市中心的高档花园小区"
}
```

- **参数说明**:
  - `id`: 地址ID
  - 其他参数同创建接口

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 3. 删除社区地址

- **URL**: `/api/v1/system/secured/addresses/:id`
- **方法**: `DELETE`
- **权限**: 需要超级管理员权限
- **功能**: 删除指定ID的社区地址（前提是没有子地址）
- **参数说明**:
  - `id`: 地址ID

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 4. 获取社区地址详情

- **URL**: `/api/v1/system/secured/addresses/:id`
- **方法**: `GET`
- **权限**: 需要超级管理员权限
- **功能**: 获取指定ID的社区地址详情
- **参数说明**:
  - `id`: 地址ID

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "阳光小区",
    "parentId": 0,
    "level": 1,
    "longitude": 116.397128,
    "latitude": 39.916527,
    "fullPath": "阳光小区",
    "sort": 1,
    "status": 1,
    "description": "位于城市中心的高档小区",
    "createdAt": "2025-06-06T12:00:00Z",
    "updatedAt": "2025-06-06T12:00:00Z"
  }
}
```

### 5. 获取社区地址列表

- **URL**: `/api/v1/system/secured/addresses`
- **方法**: `GET`
- **权限**: 需要超级管理员权限
- **功能**: 分页获取社区地址列表
- **查询参数**:
  - `page`: 页码，默认1
  - `pageSize`: 每页数量，默认10
  - `name`: 地址名称，支持模糊查询
  - `level`: 地址级别（1=小区，2=楼栋，3=单元）
  - `parentId`: 父级ID
  - `status`: 状态（-1=全部，0=禁用，1=启用）

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "阳光小区",
        "parentId": 0,
        "level": 1,
        "longitude": 116.397128,
        "latitude": 39.916527,
        "fullPath": "阳光小区",
        "sort": 1,
        "status": 1,
        "description": "位于城市中心的高档小区",
        "createdAt": "2025-06-06T12:00:00Z",
        "updatedAt": "2025-06-06T12:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

### 6. 获取社区地址树形结构

- **URL**: `/api/v1/system/secured/addresses/tree`
- **方法**: `GET`
- **权限**: 需要超级管理员权限
- **功能**: 获取社区地址的树形层次结构
- **查询参数**:
  - `parentId`: 父级ID，默认0表示获取顶级地址

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "阳光小区",
        "parentId": 0,
        "level": 1,
        "longitude": 116.397128,
        "latitude": 39.916527,
        "fullPath": "阳光小区",
        "sort": 1,
        "status": 1,
        "description": "位于城市中心的高档小区",
        "createdAt": "2025-06-06T12:00:00Z",
        "updatedAt": "2025-06-06T12:00:00Z",
        "children": [
          {
            "id": 2,
            "name": "1号楼",
            "parentId": 1,
            "level": 2,
            "longitude": 116.397228,
            "latitude": 39.916627,
            "fullPath": "阳光小区/1号楼",
            "sort": 1,
            "status": 1,
            "description": "",
            "createdAt": "2025-06-06T12:00:00Z",
            "updatedAt": "2025-06-06T12:00:00Z",
            "children": [
              {
                "id": 3,
                "name": "1单元",
                "parentId": 2,
                "level": 3,
                "longitude": 116.397238,
                "latitude": 39.916637,
                "fullPath": "阳光小区/1号楼/1单元",
                "sort": 1,
                "status": 1,
                "description": "",
                "createdAt": "2025-06-06T12:00:00Z",
                "updatedAt": "2025-06-06T12:00:00Z",
                "children": []
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 7. 获取地址选择器选项

- **URL**: `/api/v1/system/secured/addresses/options`
- **方法**: `GET`
- **权限**: 需要超级管理员权限
- **功能**: 获取适用于级联选择器的地址选项

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "options": [
      {
        "label": "阳光小区",
        "value": 1,
        "longitude": 116.397128,
        "latitude": 39.916527,
        "level": 1,
        "children": [
          {
            "label": "1号楼",
            "value": 2,
            "longitude": 116.397228,
            "latitude": 39.916627,
            "level": 2,
            "children": [
              {
                "label": "1单元",
                "value": 3,
                "longitude": 116.397238,
                "latitude": 39.916637,
                "level": 3,
                "children": []
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 8. 根据父级获取子地址列表

- **URL**: `/api/v1/system/secured/addresses/parent/:parentId`
- **方法**: `GET`
- **权限**: 需要超级管理员权限
- **功能**: 获取指定父级ID下的子地址
- **参数说明**:
  - `parentId`: 父级地址ID
  - `level`: 查询参数，筛选特定级别的子地址

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 2,
      "name": "1号楼",
      "parentId": 1,
      "level": 2,
      "longitude": 116.397228,
      "latitude": 39.916627,
      "fullPath": "阳光小区/1号楼",
      "sort": 1,
      "status": 1,
      "description": "",
      "createdAt": "2025-06-06T12:00:00Z",
      "updatedAt": "2025-06-06T12:00:00Z"
    }
  ]
}
```

### 9. 获取完整地址信息

- **URL**: `/api/v1/system/secured/addresses/full-info`
- **方法**: `POST`
- **权限**: 需要超级管理员权限
- **功能**: 根据选择的地址ID获取完整的地址信息
- **请求体示例**:

```json
{
  "unitId": 3
}
```

- **参数说明**:
  - `unitId`: 单元ID，必须是3级地址

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "fullPath": "阳光小区/1号楼/1单元",
    "longitude": 116.397238,
    "latitude": 39.916637
  }
}
```

### 10. 刷新地址缓存

- **URL**: `/api/v1/system/secured/addresses/cache/refresh`
- **方法**: `POST`
- **权限**: 需要超级管理员权限
- **功能**: 手动刷新社区地址缓存

- **响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "社区地址缓存已刷新"
  }
}
```

## 使用流程

### 典型使用场景

1. **地址管理**:
   - 使用地址CRUD操作管理社区地址库
   - 一般顺序: 先添加小区 -> 添加楼栋 -> 添加单元

2. **地址选择器**:
   - 前端使用`/addresses/options`获取级联选择器数据
   - 用户选择后使用`/addresses/full-info`获取完整地址信息

3. **基于地理位置的功能**:
   - 使用地址中的经纬度坐标计算两地之间的距离
   - 用于实现邻近服务、路径规划等功能

## 数据结构

社区地址采用三级层次结构:
1. **小区(level=1)**: 最顶级的地址单元
2. **楼栋(level=2)**: 属于小区的子单元
3. **单元(level=3)**: 属于楼栋的子单元

每个地址节点都包含:
- 基本信息(名称、描述等)
- 层级关系(父级ID、层级值)
- 地理坐标(经度、纬度)
- 完整路径(如"阳光小区/1号楼/1单元")

## 注意事项

1. **层级限制**:
   - 创建地址时必须遵循层级规则(父级地址层级必须小于当前地址层级)
   - 单元级(level=3)是当前系统支持的最低级别

2. **删除限制**:
   - 不能删除有子地址的地址节点
   - 必须先删除所有子节点

3. **缓存机制**:
   - 地址数据会被缓存以提高性能
   - 在大量更新后可使用缓存刷新API确保数据一致性

4. **权限要求**:
   - 所有API都需要管理员权限("super"角色)
