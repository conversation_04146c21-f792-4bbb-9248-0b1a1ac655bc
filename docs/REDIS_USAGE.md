# O_Mall Redis使用指南

## 配置说明

O_Mall系统使用Redis作为缓存和会话管理。Redis的配置已经更新为使用标准的Section格式，在配置文件中的结构如下：

```ini
# Redis配置
redishost = next.qwyx.shop
redisport = 6379
redispass = ocars123456!
redisdb = 1
```

## 配置读取

在代码中读取Redis配置的方法：

```go
// 读取Redis配置
host, err := web.AppConfig.String("redishost")
port, err := web.AppConfig.String("redisport")
pass, err := web.AppConfig.String("redispass")
dbStr, err := web.AppConfig.String("redisdb")
```

## Redis工具模块

系统提供了Redis工具模块（`utils/redis/redis.go`），封装了Redis的常用操作，使用方法如下：

```go
import "o_mall_backend/utils/redis"

// 初始化Redis（程序启动时会自动调用）
err := redis.Init()

// 字符串操作
err := redis.Set("key", "value", time.Hour)  // 设置值，有效期1小时
value, err := redis.Get("key")  // 获取值

// 哈希表操作
_, err := redis.HSet("hash_key", "field1", "value1", "field2", "value2")
value, err := redis.HGet("hash_key", "field1")
allFields, err := redis.HGetAll("hash_key")

// 列表操作
_, err := redis.LPush("list_key", "value1", "value2")
_, err := redis.RPush("list_key", "value3")
value, err := redis.LPop("list_key")
values, err := redis.LRange("list_key", 0, -1)  // 获取所有元素

// 集合操作
_, err := redis.SAdd("set_key", "member1", "member2")
members, err := redis.SMembers("set_key")

// 有序集合操作
_, err := redis.ZAdd("zset_key", redis.Z{Score: 1, Member: "member1"})
members, err := redis.ZRange("zset_key", 0, -1)

// 计数器操作
count, err := redis.Incr("counter_key")
count, err := redis.IncrBy("counter_key", 10)

// 获取原始客户端
client := redis.GetClient()
// 使用原始客户端进行更高级的操作
```

## 环境变量支持

如果配置文件中的Redis配置不完整，系统会尝试从环境变量读取：

1. 首先尝试使用REDIS_开头的环境变量：
   - `REDIS_HOST`
   - `REDIS_PORT`
   - `REDIS_PASS`
   - `REDIS_DB`

2. 如果仍不成功，尝试使用O_MALL_格式的环境变量：
   - `O_MALL_REDIS_HOST`
   - `O_MALL_REDIS_PORT`
   - `O_MALL_REDIS_PASS`
   - `O_MALL_REDIS_DB`

## 默认值（仅开发环境）

在开发环境下，如果无法从配置文件或环境变量获取完整的Redis配置，系统会使用以下默认值：

- host: localhost
- port: 6379
- db: 0

## 连接管理

系统在启动时自动初始化Redis连接，在程序结束时自动关闭连接：

```go
// 在main.go中
func main() {
    // ... 其他初始化代码 ...
    
    // 初始化Redis连接
    initRedis()
    
    // 程序结束时关闭Redis连接
    defer func() {
        if err := redis.Close(); err != nil {
            logs.Error("关闭Redis连接失败: %v", err)
        }
    }()
    
    // ... 其他代码 ...
}
```

## 使用示例

以下是Redis在系统中的典型使用场景：

### 1. 用户会话管理

```go
// 保存用户会话
func SaveUserSession(userID int64, token string) error {
    key := fmt.Sprintf("user:session:%d", userID)
    return redis.Set(key, token, 24*time.Hour)  // 24小时有效期
}

// 验证用户会话
func ValidateUserSession(userID int64, token string) bool {
    key := fmt.Sprintf("user:session:%d", userID)
    storedToken, err := redis.Get(key)
    if err != nil || storedToken != token {
        return false
    }
    return true
}
```

### 2. 商品库存缓存

```go
// 设置商品库存缓存
func SetProductStock(productID int64, stock int) error {
    key := fmt.Sprintf("product:stock:%d", productID)
    return redis.Set(key, stock, 10*time.Minute)  // 10分钟缓存
}

// 获取商品库存（缓存优先）
func GetProductStock(productID int64) (int, error) {
    key := fmt.Sprintf("product:stock:%d", productID)
    stockStr, err := redis.Get(key)
    if err == nil {
        // 缓存命中
        stock, err := strconv.Atoi(stockStr)
        return stock, err
    }
    
    // 缓存未命中，从数据库获取
    stock, err := queryStockFromDB(productID)
    if err != nil {
        return 0, err
    }
    
    // 更新缓存
    _ = SetProductStock(productID, stock)
    return stock, nil
}

// 减少商品库存（使用原子操作）
func DecrProductStock(productID int64, amount int) (int, error) {
    key := fmt.Sprintf("product:stock:%d", productID)
    
    // 使用Lua脚本确保原子性
    script := `
    local current = tonumber(redis.call('get', KEYS[1]) or "0")
    local decr = tonumber(ARGV[1])
    if current >= decr then
        redis.call('decrby', KEYS[1], decr)
        return current - decr
    else
        return -1
    end
    `
    
    client := redis.GetClient()
    result, err := client.Eval(ctx, script, []string{key}, amount).Result()
    if err != nil {
        return 0, err
    }
    
    newStock := int(result.(int64))
    if newStock < 0 {
        return 0, fmt.Errorf("库存不足")
    }
    
    return newStock, nil
}
```

### 3. 接口限流

```go
// 限制用户接口调用频率
func RateLimitCheck(userID int64, api string, maxCount int, period time.Duration) error {
    key := fmt.Sprintf("ratelimit:%s:%d", api, userID)
    
    // 获取当前计数
    count, err := redis.Incr(key)
    if err != nil {
        return err
    }
    
    // 如果是第一次设置，设置过期时间
    if count == 1 {
        redis.Expire(key, period)
    }
    
    // 检查是否超出限制
    if count > int64(maxCount) {
        return fmt.Errorf("请求过于频繁，请稍后再试")
    }
    
    return nil
}
```

### 4. 分布式锁

```go
// 获取分布式锁
func AcquireLock(resource string, timeout time.Duration) (string, error) {
    lockKey := fmt.Sprintf("lock:%s", resource)
    // 生成唯一标识
    lockValue := uuid.New().String()
    
    // 尝试获取锁
    ok, err := redis.SetNX(lockKey, lockValue, timeout)
    if err != nil || !ok {
        return "", fmt.Errorf("无法获取锁: %v", err)
    }
    
    return lockValue, nil
}

// 释放分布式锁
func ReleaseLock(resource, lockValue string) error {
    lockKey := fmt.Sprintf("lock:%s", resource)
    
    // 使用Lua脚本确保原子性操作
    script := `
    if redis.call('get', KEYS[1]) == ARGV[1] then
        return redis.call('del', KEYS[1])
    else
        return 0
    end
    `
    
    client := redis.GetClient()
    result, err := client.Eval(ctx, script, []string{lockKey}, lockValue).Result()
    if err != nil {
        return err
    }
    
    if result.(int64) == 0 {
        return fmt.Errorf("锁已被其他进程释放")
    }
    
    return nil
}
```

## 性能考虑

- Redis客户端设置了连接池，默认池大小为10，最小空闲连接为5
- 设置了超时控制：连接超时5秒，读写超时3秒
- 长时间运行的脚本应避免阻塞Redis
- 避免存储大量数据在单个键中
- 定期清理过期数据 