# 外卖API性能优化文档

## 优化背景

在系统运行过程中发现两个外卖管理API存在严重的性能问题：
- `/api/v1/admin/takeout/categories` - 分类列表API，响应时间约3.1秒
- `/api/v1/admin/takeout/foods` - 食品列表API，响应时间约4.2秒

## 问题分析

通过代码审查发现了两个主要的性能瓶颈：

### 1. 分类列表API的N+1查询问题

**问题位置**: `modules/takeout/services/takeout_category_service.go:GetCategoriesPage`

**问题描述**: 
- 在获取分类列表后，对每个分类单独查询其下的食品数量
- 如果有N个分类，就会执行N+1次数据库查询（1次获取分类列表 + N次统计食品数量）

**原始代码**:
```go
for _, category := range pagedCategories {
    // 统计分类下的商品数量
    foodCount, _ := s.foodRepo.CountByCategory(category.ID)
    // ...
}
```

### 2. 食品列表API的N+1查询问题

**问题位置**: `modules/takeout/services/admin_takeout_food_service.go:GetFoodsPageForAdmin`

**问题描述**:
- 在获取食品列表后，对每个食品单独查询其分类信息
- 如果有N个食品，就会执行N+1次数据库查询（1次获取食品列表 + N次获取分类信息）

**原始代码**:
```go
for _, food := range foods {
    // 获取分类名称
    categoryName := ""
    category, err := s.categoryRepo.GetByID(food.CategoryID)
    if err == nil && category != nil {
        categoryName = category.Name
    }
    // ...
}
```

## 优化方案

### 1. 分类列表API优化

#### 新增批量统计方法

在 `TakeoutFoodRepository` 接口中新增 `CountByCategoryBatch` 方法：

```go
// CountByCategoryBatch 批量统计多个分类下的食品数量
// 返回map[categoryID]count的形式，避免N+1查询问题
func (r *takeoutFoodRepository) CountByCategoryBatch(categoryIDs []int64) (map[int64]int, error)
```

#### 实现批量查询

使用单次SQL查询获取所有分类的食品数量：

```sql
SELECT category_id, COUNT(*) as count 
FROM takeout_food 
WHERE category_id IN (?, ?, ?, ...) 
GROUP BY category_id
```

#### 优化后的服务层代码

```go
// 批量获取分类下的商品数量，避免N+1查询问题
categoryIDs := make([]int64, 0, len(pagedCategories))
for _, category := range pagedCategories {
    categoryIDs = append(categoryIDs, category.ID)
}

// 批量查询所有分类的商品数量
foodCounts, err := s.foodRepo.CountByCategoryBatch(categoryIDs)
if err != nil {
    logs.Error("批量查询分类商品数量失败: %v", err)
    foodCounts = make(map[int64]int)
}

// 转换为DTO
for _, category := range pagedCategories {
    // 从批量查询结果中获取商品数量
    foodCount := foodCounts[category.ID]
    // ...
}
```

### 2. 食品列表API优化

#### 新增批量获取分类方法

在 `TakeoutCategoryRepository` 接口中新增 `GetByIDs` 方法：

```go
// GetByIDs 批量根据ID获取外卖分类
// 返回map[categoryID]*TakeoutCategory的形式，避免N+1查询问题
func (r *takeoutCategoryRepository) GetByIDs(ids []int64) (map[int64]*models.TakeoutCategory, error)
```

#### 实现批量查询

使用ORM的`Filter("id__in", ids)`进行批量查询：

```go
var categories []*models.TakeoutCategory
_, err := r.ormer.QueryTable(new(models.TakeoutCategory)).Filter("id__in", ids).All(&categories)
```

#### 优化后的服务层代码

```go
// 批量获取分类信息，避免N+1查询问题
categoryIDs := make([]int64, 0)
categoryIDSet := make(map[int64]bool)
for _, food := range foods {
    if !categoryIDSet[food.CategoryID] {
        categoryIDs = append(categoryIDs, food.CategoryID)
        categoryIDSet[food.CategoryID] = true
    }
}

// 批量查询分类信息
categoryMap := make(map[int64]string)
if len(categoryIDs) > 0 {
    categories, err := s.categoryRepo.GetByIDs(categoryIDs)
    if err == nil {
        for categoryID, category := range categories {
            categoryMap[categoryID] = category.Name
        }
    }
}

// 构造响应DTO
for _, food := range foods {
    // 从批量查询结果中获取分类名称
    categoryName := categoryMap[food.CategoryID]
    // ...
}
```

## 优化效果

### 查询次数对比

#### 分类列表API
- **优化前**: 1 + N 次查询（N为分类数量）
- **优化后**: 2 次查询（1次获取分类列表 + 1次批量统计食品数量）

#### 食品列表API
- **优化前**: 1 + M 次查询（M为不同分类的数量）
- **优化后**: 2 次查询（1次获取食品列表 + 1次批量获取分类信息）

### 性能提升预期

假设分类列表有10个分类，食品列表有20个食品分布在5个不同分类中：

- **分类列表API**: 从11次查询减少到2次查询，减少约82%的数据库查询
- **食品列表API**: 从6次查询减少到2次查询，减少约67%的数据库查询

预期响应时间将从3-4秒降低到500ms以内。

## 代码变更文件

1. `modules/takeout/repositories/takeout_food_repository.go`
   - 新增 `CountByCategoryBatch` 接口方法
   - 实现批量统计分类食品数量的功能

2. `modules/takeout/repositories/takeout_category_repository.go`
   - 新增 `GetByIDs` 接口方法
   - 实现批量获取分类信息的功能

3. `modules/takeout/services/takeout_category_service.go`
   - 优化 `GetCategoriesPage` 方法，使用批量查询

4. `modules/takeout/services/admin_takeout_food_service.go`
   - 优化 `GetFoodsPageForAdmin` 方法，使用批量查询

## 最佳实践总结

1. **识别N+1查询问题**: 在循环中进行数据库查询是性能问题的常见原因
2. **批量查询优化**: 使用IN查询或批量接口减少数据库往返次数
3. **数据预加载**: 提前获取所需的关联数据，避免懒加载导致的性能问题
4. **缓存策略**: 对于频繁查询的数据，可以考虑添加缓存层
5. **性能监控**: 定期监控API响应时间，及时发现性能问题

## 后续优化建议

1. **添加数据库索引**: 确保 `category_id` 字段有适当的索引
2. **引入缓存**: 对分类信息等相对稳定的数据添加Redis缓存
3. **分页优化**: 在repository层实现真正的数据库分页，避免内存分页
4. **连接池优化**: 检查数据库连接池配置，确保连接数充足
5. **SQL查询优化**: 使用EXPLAIN分析SQL执行计划，优化慢查询