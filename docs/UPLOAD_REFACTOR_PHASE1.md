# 文件上传系统重构 - 第一阶段

## 概述

本文档描述了O-Mall文件上传系统第一阶段重构的实施内容。第一阶段的目标是创建通用的上传服务接口和统一的上传请求结构，为后续的完全统一奠定基础。

## 重构内容

### 1. 通用服务接口

#### 1.1 CommonUploadService 接口

位置：`common/services/upload_service.go`

定义了统一的文件上传服务接口，包括：
- `Upload()` - 文件上传
- `GetByID()` - 获取文件信息
- `List()` - 获取文件列表
- `Delete()` - 删除文件
- `GetUploadConfig()` - 获取上传配置
- `ValidateUploadPermission()` - 验证上传权限
- `GetUploadPath()` - 获取上传路径

#### 1.2 统一的数据结构

- `CommonUploadRequest` - 通用上传请求
- `CommonUploadResponse` - 通用上传响应
- `CommonUploadQueryRequest` - 通用查询请求
- `CommonUploadListResponse` - 通用列表响应
- `CommonUploadConfigResponse` - 通用配置响应

### 2. 通用服务实现

位置：`common/services/upload_service_impl.go`

实现了 `CommonUploadService` 接口，当前阶段通过适配器模式复用现有的admin模块上传服务，支持：
- admin模块和merchant模块的文件上传
- 自动转换请求和响应格式
- 根据模块调整上传配置

### 3. 通用控制器

位置：`common/controllers/upload_controller.go`

提供了统一的HTTP API接口：
- `POST /upload` - 文件上传
- `GET /:id` - 获取文件信息
- `GET /list` - 获取文件列表
- `DELETE /:id` - 删除文件
- `GET /config` - 获取上传配置

### 4. 模块控制器重构

#### 4.1 Admin模块控制器

位置：`modules/admin/controllers/upload_file_controller.go`

- 保持原有API路径不变，确保向后兼容
- 内部委托给通用上传控制器处理
- 简化代码，移除重复逻辑

#### 4.2 Merchant模块控制器

位置：`modules/merchant/controllers/upload_file_controller.go`

- 保持原有API路径不变
- 移除了原有的 `directUpload` 方法
- 委托给通用上传控制器处理

## 使用方法

### 1. 直接使用通用服务

```go
import commonServices "o_mall_backend/common/services"

// 创建通用上传服务
uploadService := commonServices.NewCommonUploadService()

// 构建上传请求
req := &commonServices.CommonUploadRequest{
    FileUsage:   "product_image",
    IsAnonymous: true,
    Module:      "merchant",
}

// 执行上传
result, err := uploadService.Upload(ctx, file, fileHeader, req, token)
```

### 2. 使用通用控制器

```go
import commonControllers "o_mall_backend/common/controllers"

// 创建通用上传控制器
uploadController := commonControllers.NewCommonUploadController("admin")

// 在路由中使用
beego.Router("/api/upload", uploadController, "post:Upload")
```

### 3. 继承通用控制器

```go
type MyUploadController struct {
    controllers.BaseController
    commonUpload *commonControllers.CommonUploadController
}

func (c *MyUploadController) Prepare() {
    c.BaseController.Prepare()
    c.commonUpload = commonControllers.NewCommonUploadController("my_module")
    c.commonUpload.Controller = c.Controller
}

func (c *MyUploadController) Upload() {
    // 委托给通用控制器
    c.commonUpload.Upload()
}
```

## 配置说明

### 模块特定配置

通用服务会根据不同模块自动调整配置：

- **merchant模块**：自动添加 `product_image` 到匿名上传允许类型
- **user模块**：自动添加 `avatar` 到匿名上传允许类型
- **admin模块**：使用默认配置

### 上传路径规则

文件上传路径按以下规则生成：
```
{base_path}/{module}/{file_usage}/
```

例如：
- admin模块头像：`./uploads/admin/avatar/`
- merchant模块商品图片：`./uploads/merchant/product_image/`
- user模块头像：`./uploads/user/avatar/`

## 兼容性

### API兼容性

- 所有现有API路径保持不变
- 请求和响应格式保持兼容
- 现有客户端代码无需修改

### 功能兼容性

- 保持所有现有功能
- 匿名上传逻辑保持不变
- 权限验证逻辑保持不变

## 优势

1. **代码复用**：消除了重复的上传逻辑
2. **统一接口**：提供了标准化的上传服务接口
3. **易于扩展**：新模块可以轻松集成上传功能
4. **向后兼容**：不影响现有功能和API
5. **渐进式重构**：可以逐步迁移各模块

## 下一阶段计划

第二阶段将实施：
1. 完全统一数据库模型
2. 统一配置管理
3. 优化存储层接口
4. 添加更多高级功能（如文件压缩、水印等）

## 注意事项

1. 当前阶段仍依赖admin模块的服务实现
2. 数据库表结构暂未统一
3. 配置管理仍使用原有方式
4. 建议在测试环境充分验证后再部署到生产环境