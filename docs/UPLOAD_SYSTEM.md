# O-Mall 文件上传系统分析文档

## 1. 功能概述

O-Mall系统的文件上传模块提供了灵活、安全的文件上传服务，支持多种用途的文件上传、查询、删除等操作，以及多种存储方式（本地存储和云存储）的配置。

主要功能：

- 支持匿名上传和认证用户上传两种模式
- 支持配置不同用途的文件上传（头像、商品图、投诉附件等）
- 文件格式、大小限制和验证
- 支持多种存储方式：本地存储、阿里云OSS、腾讯云COS、七牛云等
- 支持CDN加速
- 上传文件管理（上传、查询、删除）
- 动态配置获取

## 2. 架构设计与数据流向

### 2.1 整体架构

上传功能采用典型的三层架构设计：

1. **控制层（Controller）**：处理HTTP请求，参数解析和响应封装
2. **服务层（Service）**：业务逻辑处理，包括上传验证、文件处理、数据库操作
3. **仓储层（Repository）**：数据持久化，与数据库交互

### 2.2 详细数据流向

#### 2.2.1 文件上传流程

```
客户端 → 控制器 → 服务层 → 存储工具 → 物理存储 → 数据库记录
```

详细步骤：

1. **客户端发起请求**
   - 通过HTTP POST请求，提交multipart/form-data格式的文件数据
   - 携带参数：file（文件内容）、file_usage（用途）、is_anonymous（是否匿名上传）
   - 认证用户需在请求头携带Authorization token

2. **控制器处理（UploadFileController.Upload）**
   - 解析请求参数（文件用途、是否匿名上传）
   - 获取并验证上传文件
   - 验证认证token（非匿名上传必须）
   - 构建上下文（含用户IP等信息）
   - 调用上传服务处理上传

3. **服务层处理（uploadFileServiceImpl.Upload）**
   - 获取并验证上传配置（允许匿名上传、大小限制、类型限制）
   - 验证文件信息（大小、类型）
   - 解析用户信息（非匿名上传）
   - 根据存储配置选择存储方式
   - 调用存储工具保存文件
   - 生成文件访问URL
   - 创建并保存上传记录到数据库
   - 返回文件信息响应对象

4. **存储层处理（storage包）**
   - 根据配置的存储模式选择对应的存储提供者（本地、OSS、COS等）
   - 生成唯一文件名并保存文件
   - 返回文件路径
   - 生成访问URL

5. **数据持久化**
   - 将文件信息记录保存到数据库
   - 包括：文件名、路径、URL、大小、类型、用途、上传者等

#### 2.2.2 文件查询流程

```
客户端 → 控制器 → 服务层 → 仓储层 → 数据库 → 响应
```

1. 控制器解析查询参数（ID、分页等）
2. 调用服务层查询
3. 服务层调用仓储层从数据库获取记录
4. 转换为响应对象并返回

#### 2.2.3 文件删除流程

```
客户端 → 控制器 → 服务层 → 存储工具 → 物理删除文件 → 数据库更新
```

1. 控制器验证权限（仅管理员可删除）
2. 调用服务层删除
3. 服务层先获取文件信息
4. 调用存储工具删除物理文件
5. 更新数据库记录（软删除）

## 3. 配置管理

### 3.1 上传配置

上传配置通过`config.GetUploadConfig()`方法获取，包括：

- **allowAnonymous**：是否允许匿名上传
- **maxFileSize**：最大文件大小限制（单位：字节）
- **allowedFileTypes**：允许的文件MIME类型列表
- **anonymousUsageTypes**：允许匿名上传的文件用途类型列表

配置来源：
- 默认从app.conf配置文件中读取
- 若配置文件中未设置，则使用以下默认值：
  - allowAnonymous: false
  - maxFileSize: 10MB
  - allowedFileTypes: 图片、文档等常见类型
  - anonymousUsageTypes: 仅complaint（投诉）

### 3.2 存储配置

存储配置通过`config.GetStorageConfig()`方法获取：

- **storageMode**：存储模式（local、oss、cos、qiniu等）
- **enableCDN**：是否启用CDN加速

根据不同存储模式，还会加载对应的特定配置：

#### 本地存储配置
- uploadPath：上传文件保存路径
- cdnDomain：CDN域名（可选）

#### 阿里云OSS配置
- endpoint、bucket、accessKey、accessSecret、domain

#### 腾讯云COS配置
- region、bucket、secretID、secretKey、domain

#### 七牛云配置
- accessKey、secretKey、bucket、domain、zone

## 4. 存储实现

系统通过`StorageProvider`接口实现了存储功能的抽象，支持多种存储方式：

```go
// 存储提供者接口
type StorageProvider interface {
    // 保存文件到存储
    Save(file multipart.File, fileName string, fileDir string) (string, error)
    // 获取文件访问URL
    GetURL(filePath string) string
    // 删除文件
    Delete(filePath string) error
}
```

### 4.1 本地存储实现

本地存储实现通过`LocalStorage`结构体，提供基于文件系统的存储实现：

1. **保存文件**：创建目录→创建文件→复制内容→返回相对路径
2. **获取URL**：根据是否启用CDN返回不同格式的URL
3. **删除文件**：直接调用系统删除文件

### 4.2 云存储实现

系统还支持多种云存储实现（在单独的文件中）：
- 阿里云OSS存储：`aliyun_oss.go`
- 腾讯云COS存储：`tencent_cos.go`
- 七牛云存储：`qiniu_storage.go`

每种云存储实现都遵循`StorageProvider`接口，提供对应云服务的具体实现。

## 5. 安全与限制

上传系统实现了多重安全控制：

1. **身份验证**：
   - 区分匿名上传和认证用户上传
   - 匿名上传受全局开关和用途限制双重控制
   - 非匿名上传需要验证用户token

2. **文件验证**：
   - 文件大小限制
   - 文件类型检查（MIME类型）
   - 文件扩展名验证

3. **权限控制**：
   - 删除操作仅限管理员权限

4. **其他安全措施**：
   - 生成唯一文件名，避免文件名冲突
   - 敏感信息（如密钥）在日志中掩码处理

## 6. 扩展性设计

文件上传系统设计具有良好的扩展性：

1. **存储方式扩展**：
   - 通过`StorageProvider`接口，可轻松添加新的存储实现
   - 只需实现`Save`、`GetURL`和`Delete`方法

2. **配置动态调整**：
   - 允许通过配置文件修改各种参数
   - 无需修改代码即可调整上传行为

3. **文件用途扩展**：
   - 系统设计支持不同用途的文件上传
   - 可根据业务需要添加新的用途类型

## 7. 接口文档

### 7.1 上传文件

```
POST /upload
```

**参数：**
- `file`：要上传的文件（必需）
- `file_usage`：文件用途，如avatar、product、complaint等（可选）
- `is_anonymous`：是否匿名上传，默认为false（可选）

**Header：**
- `Authorization`：Bearer {token}（非匿名上传必需）

**响应：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 123,
    "file_name": "example.jpg",
    "file_path": "avatar/1623456789_abcdef.jpg",
    "file_url": "https://cdn.example.com/avatar/1623456789_abcdef.jpg",
    "file_size": 102400,
    "file_type": "image/jpeg",
    "file_ext": ".jpg",
    "file_usage": "avatar",
    "is_anonymous": false,
    "storage": "local",
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

### 7.2 获取文件信息

```
GET /files/:id
```

**参数：**
- `id`：文件ID（路径参数）

**响应：**
与上传文件接口响应格式相同

### 7.3 获取文件列表

```
GET /files
```

**参数：**
- `file_usage`：文件用途过滤（可选）
- `user_type`：用户类型过滤（可选）
- `user_id`：用户ID过滤（可选）
- `username`：用户名过滤（可选）
- `is_anonymous`：是否匿名过滤（可选）
- `start_time`、`end_time`：时间范围过滤（可选）
- `page`、`pageSize`：分页参数（可选，默认page=1, pageSize=10）

**响应：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 123,
        "file_name": "example.jpg",
        "file_url": "https://cdn.example.com/avatar/1623456789_abcdef.jpg",
        // 其他字段与上传响应相同
      }
    ]
  }
}
```

### 7.4 删除文件

```
DELETE /files/:id
```

**参数：**
- `id`：文件ID（路径参数）

**Header：**
- `Authorization`：Bearer {token}（必需，且必须是管理员token）

**响应：**
```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 7.5 获取上传配置

```
GET /config
```

**响应：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "allow_anonymous": false,
    "max_file_size": 10485760,
    "allowed_file_types": ["image/jpeg", "image/png", "..."],
    "storage_mode": "local",
    "upload_path": "./uploads",
    "allowed_usage_types": ["avatar", "product", "complaint", "..."],
    "anonymous_usage_types": ["complaint"]
  }
}
```

## 8. 总结

O-Mall系统的文件上传功能设计全面、灵活，同时兼顾了安全性和可扩展性。通过分层架构和抽象接口设计，系统能够适应多种存储需求和业务场景，便于未来功能扩展和维护。

主要优势：
1. 支持多种存储方式，可根据需求灵活配置
2. 完善的安全控制机制
3. 功能全面，支持文件的完整生命周期管理
4. 配置灵活，可通过配置文件调整系统行为
5. 良好的抽象设计，便于扩展

建议改进：
1. 考虑增加文件内容安全检测功能
2. 添加文件处理功能（如图片压缩、水印等）
3. 完善错误处理和日志记录机制
4. 考虑添加分布式存储支持以提高可用性
