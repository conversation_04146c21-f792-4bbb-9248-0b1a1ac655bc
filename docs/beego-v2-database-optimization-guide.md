# Beego v2 数据库性能优化指南

## 概述

本文档介绍如何在Beego v2框架中进行数据库性能优化，特别是索引管理和ORM优化。

## 🏗️ Beego v2 索引管理

### 1. 模型文件索引定义

在Beego v2中，数据库索引通过模型文件的 `TableIndex()` 方法定义：

```go
// TableIndex 设置索引
func (m *Model) TableIndex() [][]string {
    return [][]string{
        {"field1"},                    // 单字段索引
        {"field1", "field2"},          // 复合索引
        {"field1", "field2", "field3"}, // 多字段复合索引
    }
}
```

### 2. 索引类型和用途

#### 单字段索引
```go
{"user_id"},     // 用户查询
{"status"},      // 状态过滤
{"created_at"},  // 时间排序
```

#### 复合索引
```go
{"user_id", "status"},           // 用户状态查询
{"user_id", "status", "selected"}, // 购物车选择操作优化
{"user_id", "created_at"},       // 用户时间范围查询
```

### 3. 索引设计原则

1. **最左前缀原则**: 复合索引按查询频率排序字段
2. **覆盖索引**: 包含查询所需的所有字段
3. **选择性原则**: 优先为高选择性字段建索引
4. **避免过度索引**: 平衡查询性能和写入性能

## 🔧 购物车模块索引优化

### 1. 主购物车表 (cart_items)

```go
// modules/cart/models/cart.go
func (ci *CartItem) TableIndex() [][]string {
    return [][]string{
        {"user_id", "status"},               // 基础用户状态查询
        {"user_id", "product_id", "sku_id"}, // 商品去重和查询
        {"user_id", "status", "selected"},   // 批量选择操作优化 ⭐
        {"user_id", "id", "status"},         // 批量ID权限验证优化 ⭐
        {"user_id", "status", "created_at"}, // 列表查询排序优化 ⭐
    }
}
```

**优化说明**:
- `{"user_id", "status", "selected"}`: 优化购物车选择API的批量更新操作
- `{"user_id", "id", "status"}`: 优化批量ID查询和权限验证
- `{"user_id", "status", "created_at"}`: 优化购物车列表查询和排序

### 2. 外卖购物车扩展表 (takeout_cart_item)

```go
// modules/takeout/models/takeout_cart.go
func (t *TakeoutCartItem) TableIndex() [][]string {
    return [][]string{
        {"cart_item_id"},          // 关联查询优化
        {"food_id"},               // 商品查询优化
        {"variant_id"},            // 规格查询优化
        {"food_id", "variant_id"}, // 商品规格复合查询
        {"created_at"},            // 时间范围查询
    }
}
```

### 3. 操作日志表 (takeout_cart_item_log)

```go
func (t *TakeoutCartItemLog) TableIndex() [][]string {
    return [][]string{
        {"user_id"},               // 用户日志查询
        {"cart_item_id"},          // 关联查询
        {"food_id"},               // 商品操作日志
        {"user_id", "created_at"}, // 用户操作历史
        {"action", "created_at"},  // 操作统计分析
        {"created_at"},            // 日志清理
    }
}
```

## 🚀 数据库同步和部署

### 1. 开发环境同步

```go
// main.go 中添加
import "github.com/beego/beego/v2/client/orm"

func init() {
    // 注册数据库
    orm.RegisterDataBase("default", "mysql", "user:password@tcp(localhost:3306)/database?charset=utf8mb4")
    
    // 注册模型
    orm.RegisterModel(new(models.CartItem))
    orm.RegisterModel(new(models.TakeoutCartItem))
    orm.RegisterModel(new(models.TakeoutCartItemLog))
    
    // 开发环境自动同步数据库结构
    if beego.BConfig.RunMode == "dev" {
        orm.RunSyncdb("default", false, true)
    }
}
```

### 2. 生产环境部署

#### 方法1: 使用bee工具
```bash
# 生成迁移文件
bee generate migration create_cart_indexes

# 执行迁移
bee migrate -driver=mysql -conn="user:password@tcp(localhost:3306)/database"
```

#### 方法2: 手动同步
```go
// 在部署脚本中执行
func syncDatabase() {
    o := orm.NewOrm()
    
    // 同步表结构和索引
    err := orm.RunSyncdb("default", false, true)
    if err != nil {
        logs.Error("数据库同步失败: %v", err)
        return
    }
    
    logs.Info("数据库结构同步完成")
}
```

### 3. 索引验证

```sql
-- 检查索引创建情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'your_database' 
AND TABLE_NAME IN ('cart_items', 'takeout_cart_item', 'takeout_cart_item_log')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
```

## 📊 性能监控

### 1. 慢查询监控

```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 0.5;

-- 查看慢查询
SELECT 
    sql_text,
    exec_count,
    avg_timer_wait/1000000000 as avg_time_sec
FROM performance_schema.events_statements_summary_by_digest 
WHERE sql_text LIKE '%cart_items%'
ORDER BY avg_timer_wait DESC 
LIMIT 10;
```

### 2. 索引使用情况

```sql
-- 查看索引使用统计
SELECT 
    object_schema,
    object_name,
    index_name,
    count_read,
    count_insert,
    count_update,
    count_delete
FROM performance_schema.table_io_waits_summary_by_index_usage
WHERE object_schema = 'your_database'
AND object_name IN ('cart_items', 'takeout_cart_item')
ORDER BY count_read DESC;
```

### 3. 应用层监控

```go
// 在服务层添加性能监控
func (s *CartService) SelectCartItems(ctx context.Context, req *dto.SelectCartItemsRequest) error {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        logs.Info("SelectCartItems执行时间: %v, 用户ID: %d, 商品数量: %d", 
            duration, req.UserID, len(req.IDs))
        
        // 记录慢操作
        if duration > 500*time.Millisecond {
            logs.Warn("SelectCartItems执行缓慢: %v, 请求: %+v", duration, req)
        }
    }()
    
    // 业务逻辑...
    return nil
}
```

## 🔍 性能优化最佳实践

### 1. 查询优化

```go
// ❌ 避免N+1查询
for _, id := range ids {
    item := &models.CartItem{ID: id}
    o.Read(item)
}

// ✅ 使用批量查询
var items []models.CartItem
o.QueryTable("cart_items").Filter("id__in", ids).All(&items)
```

### 2. 索引优化

```go
// ✅ 合理设计复合索引
{"user_id", "status", "selected"}  // 支持多种查询组合

// ❌ 避免冗余索引
{"user_id"}           // 已被上面的复合索引覆盖
{"user_id", "status"} // 已被上面的复合索引覆盖
```

### 3. 缓存策略

```go
// ✅ 异步缓存更新
go func() {
    time.Sleep(50 * time.Millisecond)
    cache.Refresh(userID)
}()

// ❌ 避免同步缓存更新阻塞响应
cache.Refresh(userID) // 阻塞API响应
```

## 📋 检查清单

### 部署前检查
- [ ] 模型文件索引定义正确
- [ ] 数据库连接配置正确
- [ ] 测试环境验证通过
- [ ] 性能测试达标

### 部署后验证
- [ ] 索引创建成功
- [ ] API响应时间改善
- [ ] 数据库负载降低
- [ ] 错误率保持正常

### 监控指标
- [ ] 平均响应时间 < 200ms
- [ ] P95响应时间 < 500ms
- [ ] 错误率 < 0.5%
- [ ] 数据库连接数正常

通过遵循这些最佳实践，可以在Beego v2框架中实现高效的数据库性能优化。
