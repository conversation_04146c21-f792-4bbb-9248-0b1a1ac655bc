# 购物车配送费调试信息一键复制功能

## 🎯 功能概述

为购物车页面的配送费调试信息区域添加了一键复制功能，方便开发者和测试人员快速复制调试信息到其他地方进行分析和分享。

## ✨ 功能特性

### 1. 一键复制按钮
- 在配送费调试信息标题旁边添加了复制按钮
- 按钮包含复制图标和"复制"文字
- 点击后自动复制所有调试信息到剪贴板

### 2. 格式化输出
复制的内容包含：
- 调试信息标题和分隔线
- 完整的调试信息列表
- 复制时间戳
- 清晰的格式化布局

### 3. 用户反馈
- 复制成功时显示成功提示
- 复制失败时显示错误提示
- 控制台输出详细的复制日志

## 🎨 界面设计

### 调试信息区域布局
```
🚚 配送费调试信息                    [📋 复制]
=====================================
📍 商家ID: 123
💰 订单金额: ¥25.80
🚚 配送费: ¥2.00
📏 配送距离: 1.2km
🏠 用户坐标: 39.9042, 116.4074
🏪 商家坐标: 39.9142, 116.4174
⚙️ 基础配送费: ¥2
📏 距离费用: ¥2/km
=====================================
```

### 复制按钮样式
- 白色背景，灰色边框
- 包含复制图标和文字
- 悬停时高亮显示
- 点击时有缩放动画效果

## 🔧 技术实现

### 1. 模板结构修改
```vue
<!-- 配送费调试信息 -->
<view v-if="showDeliveryDebugInfo(group.merchantId)" class="delivery-debug">
  <view class="debug-header">
    <text class="debug-title">🚚 配送费调试信息</text>
    <view class="debug-actions">
      <view class="copy-btn" @click="copyDeliveryDebugInfo(group.merchantId)">
        <wd-icon name="copy" size="16" color="#666" />
        <text class="copy-text">复制</text>
      </view>
    </view>
  </view>
  <view class="debug-content">
    <view class="debug-item" v-for="(info, index) in getDeliveryDebugInfo(group.merchantId)" :key="index">
      <text class="debug-text">{{ info }}</text>
    </view>
  </view>
</view>
```

### 2. 复制功能实现
```typescript
const copyDeliveryDebugInfo = (merchantId: number) => {
  try {
    const debugInfo = getDeliveryDebugInfo(merchantId)
    const debugText = [
      '🚚 配送费调试信息',
      '='.repeat(30),
      ...debugInfo,
      '='.repeat(30),
      `复制时间: ${new Date().toLocaleString()}`
    ].join('\n')
    
    uni.setClipboardData({
      data: debugText,
      success: () => {
        uni.showToast({
          title: '调试信息已复制',
          icon: 'success',
          duration: 2000
        })
      },
      fail: (error) => {
        uni.showToast({
          title: '复制失败',
          icon: 'error',
          duration: 2000
        })
      }
    })
  } catch (error) {
    console.error('复制调试信息失败:', error)
  }
}
```

### 3. 样式设计
```scss
.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.copy-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6rpx;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f5f5;
    border-color: #40a9ff;
  }

  &:active {
    transform: scale(0.95);
  }
}
```

## 📋 复制内容示例

点击复制按钮后，剪贴板中的内容格式如下：

```
🚚 配送费调试信息
==============================
📍 商家ID: 123
💰 订单金额: ¥25.80
🚚 配送费: ¥2.00
📏 配送距离: 1.2km
🏠 用户坐标: 39.9042, 116.4074
🏪 商家坐标: 39.9142, 116.4174
⚙️ 基础配送费: ¥2
🆓 免费门槛: ¥30
💸 折扣门槛: ¥20 (80%折)
📏 距离费用: ¥2/km
==============================
复制时间: 2025/1/18 下午2:30:45
```

## 🧪 使用方法

### 1. 启用调试模式
调试信息只在以下情况下显示：
- 开发环境 (`import.meta.env.DEV`)
- 或者本地存储中设置了 `showDeliveryDebug` 为 `'true'`

```javascript
// 在控制台中启用调试模式
uni.setStorageSync('showDeliveryDebug', 'true')
```

### 2. 查看调试信息
1. 打开购物车页面
2. 确保购物车中有商品
3. 在商家小计区域找到"🚚 配送费调试信息"部分

### 3. 复制调试信息
1. 点击调试信息标题旁边的"📋 复制"按钮
2. 等待"调试信息已复制"提示
3. 在任何地方粘贴使用复制的内容

## 🎯 使用场景

### 1. 开发调试
- 快速复制配送费计算的详细信息
- 分析配送费计算逻辑是否正确
- 检查地址坐标和距离计算

### 2. 问题排查
- 复制调试信息发送给开发团队
- 记录特定场景下的配送费计算结果
- 对比不同条件下的计算差异

### 3. 测试验证
- 复制测试结果到测试报告
- 验证配送费配置是否生效
- 检查优惠规则是否正确应用

## 🔍 调试信息内容说明

| 信息项 | 说明 | 示例 |
|--------|------|------|
| 📍 商家ID | 当前商家的唯一标识 | 123 |
| 💰 订单金额 | 选中商品的小计金额 | ¥25.80 |
| 🚚 配送费 | 最终计算的配送费 | ¥2.00 |
| 📏 配送距离 | 用户到商家的距离 | 1.2km |
| 🏠 用户坐标 | 用户地址的经纬度 | 39.9042, 116.4074 |
| 🏪 商家坐标 | 商家地址的经纬度 | 39.9142, 116.4174 |
| ⚙️ 基础配送费 | 系统配置的基础费用 | ¥2 |
| 🆓 免费门槛 | 免配送费的订单金额门槛 | ¥30 |
| 💸 折扣门槛 | 配送费折扣的订单金额门槛 | ¥20 (80%折) |
| 📏 距离费用 | 基于距离的额外费用 | ¥2/km |
| 🎉 优惠信息 | 当前享受的优惠 | 享受免配送费优惠 |

## 🎊 总结

这个一键复制功能大大提升了配送费调试的效率，让开发者和测试人员能够：

1. **快速获取调试信息** - 一键复制所有相关数据
2. **方便分享和分析** - 格式化的输出便于阅读和分析
3. **提高调试效率** - 减少手动记录和整理的时间
4. **改善协作体验** - 便于团队间分享调试信息

通过这个功能，配送费相关的问题排查和优化工作将变得更加高效和便捷。
