# 配送费计算调试功能说明

## 🎯 **功能概述**

为商家详情页面添加了配送费计算的调试信息显示功能，帮助开发者和测试人员深入了解配送费的计算过程。

## ✨ **功能特性**

### **1. 调试信息按钮**
- 在配送费显示旁边添加了信息图标按钮
- 仅在开发环境下显示（`isDevelopment = true`）
- 点击可展开/收起调试信息面板

### **2. 详细调试信息面板**
- **计算摘要**：显示关键计算结果
- **计算过程**：显示详细的计算步骤日志
- **复制功能**：一键复制所有调试信息到剪贴板

### **3. 增强的调试日志**
- 计算时间戳和耗时统计
- 配送费配置详情
- 距离计算过程
- 优惠应用逻辑
- 最终结果摘要

## 🎨 **界面设计**

### **调试按钮**
```
配送费¥6.00 [ℹ️]  ← 点击信息图标展开调试面板
```

### **调试信息面板**
```
🚚 配送费计算调试信息                    [复制] [关闭]
═══════════════════════════════════════════════════════

📊 计算摘要:
最终配送费: ¥6.00
原始费用: ¥6.00  
配送距离: 3.2km
免费配送: 否
享受折扣: 否

📋 计算过程:
🚚 开始计算配送费 - 商家ID: 1
📱 计算场景: merchant_detail
🏪 商家名称: 测试商家
⏰ 计算时间: 2024-01-15 14:30:25
⚙️ 配送费配置获取成功
📍 基础配送费: ¥2
📏 距离费用: ¥2/km
🆓 免费门槛: ¥30 (禁用)
💸 折扣门槛: ¥20 (禁用)
📊 折扣比例: 80%
📏 配送距离: 3.2km
🏠 用户坐标: (26.393492, 106.639598)
🏪 商家坐标: (26.371783, 106.641060)
📐 距离计算: 超出3km，额外0.2km，向上取整1km
💰 距离费用: ¥2 + 1km × ¥2/km = ¥4.00
📱 计算场景: 商家详情页，不应用订单金额优惠
✅ 最终配送费: ¥4.00
⏱️ 计算耗时: 15ms
📋 计算摘要:
   - 基础费用: ¥2
   - 距离费用: ¥2.00
   - 优惠金额: ¥0.00
   - 节省比例: 0.0%
```

## 🔧 **技术实现**

### **1. 调试按钮组件**
```vue
<!-- 调试信息按钮 -->
<view v-if="deliveryFeeResult && isDevelopment" class="debug-btn" @click="toggleDebugInfo">
  <wd-icon name="info" size="12" color="#007AFF" />
</view>
```

### **2. 调试信息面板**
```vue
<!-- 配送费调试信息 -->
<view v-if="showDebugInfo && deliveryFeeResult && isDevelopment" class="debug-info-panel">
  <view class="debug-header">
    <text class="debug-title">🚚 配送费计算调试信息</text>
    <view class="debug-actions">
      <wd-button size="small" type="primary" @click="copyDebugInfo">复制</wd-button>
      <wd-button size="small" @click="toggleDebugInfo">关闭</wd-button>
    </view>
  </view>
  
  <view class="debug-content">
    <!-- 计算摘要 -->
    <view class="debug-summary">
      <view class="debug-item">
        <text class="debug-label">最终配送费:</text>
        <text class="debug-value">¥{{ deliveryFeeResult.deliveryFee.toFixed(2) }}</text>
      </view>
      <!-- 更多摘要项... -->
    </view>
    
    <!-- 计算过程日志 -->
    <view class="debug-logs">
      <text class="debug-logs-title">计算过程:</text>
      <view v-for="(log, index) in deliveryFeeResult.debugInfo" :key="index" class="debug-log-item">
        <text class="debug-log-text">{{ log }}</text>
      </view>
    </view>
  </view>
</view>
```

### **3. 复制功能实现**
```typescript
async function copyDebugInfo() {
  if (!deliveryFeeResult.value) return
  
  try {
    const debugText = [
      '🚚 配送费计算调试信息',
      '='.repeat(30),
      ...deliveryFeeResult.value.debugInfo,
      '='.repeat(30),
      `最终结果: ¥${deliveryFeeResult.value.deliveryFee.toFixed(2)}`,
      `原始费用: ¥${deliveryFeeResult.value.originalFee.toFixed(2)}`,
      `配送距离: ${deliveryFeeResult.value.distance.toFixed(2)}km`,
      `免费配送: ${deliveryFeeResult.value.freeDelivery ? '是' : '否'}`,
      `享受折扣: ${deliveryFeeResult.value.discounted ? '是' : '否'}`,
      '',
      `生成时间: ${new Date().toLocaleString()}`
    ].join('\n')
    
    await uni.setClipboardData({ data: debugText })
    uni.showToast({ title: '调试信息已复制', icon: 'success' })
  } catch (error) {
    console.error('复制调试信息失败:', error)
    uni.showToast({ title: '复制失败', icon: 'none' })
  }
}
```

### **4. 增强的调试日志**
```typescript
export async function calculateDeliveryFee(params: DeliveryFeeCalculationParams): Promise<DeliveryFeeResult> {
  const debugInfo: string[] = []
  const startTime = Date.now()
  
  try {
    debugInfo.push(`🚚 开始计算配送费 - 商家ID: ${params.merchantId}`)
    debugInfo.push(`📱 计算场景: ${params.scenario || 'merchant_detail'}`)
    debugInfo.push(`🏪 商家名称: ${params.merchantName || '未知'}`)
    debugInfo.push(`⏰ 计算时间: ${new Date().toLocaleString()}`)
    
    // ... 计算逻辑 ...
    
    const endTime = Date.now()
    const calculationTime = endTime - startTime
    
    debugInfo.push(`✅ 最终配送费: ¥${deliveryFee.toFixed(2)}`)
    debugInfo.push(`⏱️ 计算耗时: ${calculationTime}ms`)
    debugInfo.push(`📋 计算摘要:`)
    debugInfo.push(`   - 基础费用: ¥${config.deliveryBaseFee}`)
    debugInfo.push(`   - 距离费用: ¥${(originalFee - config.deliveryBaseFee).toFixed(2)}`)
    debugInfo.push(`   - 优惠金额: ¥${(originalFee - deliveryFee).toFixed(2)}`)
    debugInfo.push(`   - 节省比例: ${originalFee > 0 ? (((originalFee - deliveryFee) / originalFee) * 100).toFixed(1) : 0}%`)
    
    return { deliveryFee, originalFee, distance, freeDelivery, discounted, debugInfo }
  } catch (error) {
    // 错误处理...
  }
}
```

## 🧪 **使用场景**

### **1. 开发调试**
- 验证配送费计算逻辑是否正确
- 检查距离计算是否准确
- 确认优惠规则是否正确应用

### **2. 测试验证**
- 测试不同距离的配送费计算
- 验证满额优惠功能
- 检查边界条件处理

### **3. 问题排查**
- 用户反馈配送费异常时的问题定位
- 配送费配置变更后的影响分析
- 性能问题排查（计算耗时）

### **4. 产品演示**
- 向产品经理展示配送费计算逻辑
- 向客户说明配送费的计算依据
- 培训客服人员了解配送费规则

## 📊 **调试信息内容**

### **基础信息**
- 商家ID和名称
- 计算场景（商家详情/购物车/结算）
- 计算时间戳

### **配置信息**
- 基础配送费
- 距离费用标准
- 免费配送门槛和状态
- 折扣门槛和比例

### **计算过程**
- 用户和商家坐标
- 距离计算详情
- 基础费用计算
- 距离费用计算
- 优惠应用过程

### **结果摘要**
- 最终配送费
- 原始费用
- 优惠金额
- 节省比例
- 计算耗时

## 🎯 **开发环境控制**

```typescript
// 开发环境检查
const isDevelopment = ref(true) // 生产环境设为 false

// 调试按钮显示条件
v-if="deliveryFeeResult && isDevelopment"
```

**生产环境部署时**：
- 将 `isDevelopment` 设为 `false`
- 或通过环境变量控制：`process.env.NODE_ENV === 'development'`

## 🚀 **使用方法**

### **1. 启用调试模式**
```typescript
// 在商家详情页面中
const isDevelopment = ref(true) // 开启调试模式
```

### **2. 查看调试信息**
1. 打开商家详情页面
2. 在配送费旁边点击信息图标 [ℹ️]
3. 查看详细的计算过程和结果

### **3. 复制调试信息**
1. 在调试面板中点击"复制"按钮
2. 调试信息会复制到剪贴板
3. 可以粘贴到文档或聊天工具中分享

### **4. 关闭调试面板**
- 点击"关闭"按钮
- 或再次点击配送费旁的信息图标

## 🎊 **功能优势**

### **1. 开发效率**
- ✅ 快速定位配送费计算问题
- ✅ 实时查看计算过程和结果
- ✅ 无需查看控制台日志

### **2. 测试便利**
- ✅ 直观的界面展示计算详情
- ✅ 一键复制功能便于记录和分享
- ✅ 详细的计算步骤便于验证

### **3. 问题排查**
- ✅ 完整的计算过程追踪
- ✅ 性能数据（计算耗时）
- ✅ 配置信息一目了然

### **4. 用户体验**
- ✅ 不影响正常用户使用
- ✅ 仅在开发环境显示
- ✅ 界面美观，操作简单

这个调试功能将大大提升配送费相关功能的开发和维护效率！🎉
