# 商家详情页面距离显示功能

## 🎯 **功能概述**

为商家详情页面的配送费前面添加了距离显示功能，让用户能够直观地了解商家与收货地址的距离。

## ✨ **功能特性**

### **1. 智能距离显示**
- 📍 基于用户收货地址和商家位置的实时距离计算
- 🎨 根据距离远近使用不同颜色显示
- 📏 智能的距离格式化（米/公里）

### **2. 多种显示状态**
- ✅ **正常距离**：显示具体距离（如：1.2km、500m）
- ⏳ **计算中**：显示"计算中..."
- 🔐 **未登录**：显示"请登录查看距离"
- 📍 **无地址**：显示"请设置收货地址"
- ❓ **未知**：显示"距离未知"

### **3. 颜色编码系统**
- 🟢 **绿色**：很近（<0.5km）
- 🔵 **蓝色**：较近（0.5-2km）
- 🟡 **橙色**：中等（2-5km）
- 🟠 **深橙色**：较远（5-10km）
- 🔴 **红色**：很远（>10km）
- ⚫ **灰色**：未知距离

## 🎨 **界面设计**

### **显示格式**
```
📍 1.2km · 💰 配送费¥6.00 [ℹ️]
```

### **不同距离的显示示例**
```
📍 附近 · 💰 配送费¥2.00        (距离 < 100m)
📍 150m · 💰 配送费¥2.00        (100m - 1km)
📍 1.2km · 💰 配送费¥4.00       (1km - 10km)
📍 15km · 💰 配送费¥8.00        (> 10km)
📍 较远 · 💰 配送费¥10.00       (> 100km)
```

### **特殊状态显示**
```
📍 计算中... · 💰 计算中...
📍 请登录查看距离 · 💰 配送费¥5.00
📍 请设置收货地址 · 💰 配送费¥5.00
📍 距离未知 · 💰 配送费¥5.00
```

## 🔧 **技术实现**

### **1. 距离格式化工具**

创建了 `src/utils/distanceFormatter.ts`：

```typescript
/**
 * 格式化距离显示
 * @param distance 距离（单位：km）
 * @returns 格式化后的距离字符串
 */
export function formatDistance(distance: number): string {
  if (distance <= 0) {
    return '距离未知'
  }
  
  if (distance < 0.1) {
    return '附近'                    // < 100m
  } else if (distance < 1) {
    const meters = Math.round(distance * 1000)
    return `${meters}m`              // 100m - 1km
  } else if (distance < 10) {
    return `${distance.toFixed(1)}km` // 1km - 10km
  } else if (distance < 100) {
    return `${distance.toFixed(0)}km` // 10km - 100km
  } else {
    return '较远'                    // > 100km
  }
}

/**
 * 获取距离状态对应的颜色
 * @param distance 距离（单位：km）
 * @returns CSS颜色值
 */
export function getDistanceColor(distance: number): string {
  if (distance < 0.5) return '#52c41a'      // 绿色 - 很近
  if (distance < 2) return '#1890ff'        // 蓝色 - 较近
  if (distance < 5) return '#faad14'        // 橙色 - 中等
  if (distance < 10) return '#fa8c16'       // 深橙色 - 较远
  if (distance >= 10) return '#f5222d'      // 红色 - 很远
  return '#666666'                          // 灰色 - 未知
}
```

### **2. 商家详情页面集成**

```vue
<template>
  <view class="delivery-item">
    <wd-icon name="location" size="14" :color="distanceColor" />
    <text class="distance-text" :style="{ color: distanceColor }">{{ distanceText }}</text>
    <text class="delivery-separator">·</text>
    <wd-icon name="wallet" size="14" color="#666" />
    <text>{{ deliveryFeeText }}</text>
  </view>
</template>

<script setup>
// 距离显示文本
const distanceText = computed(() => {
  if (deliveryFeeLoading.value) {
    return '计算中...'
  }
  
  if (deliveryFeeResult.value && deliveryFeeResult.value.distance > 0) {
    return formatDistance(deliveryFeeResult.value.distance)
  }
  
  // 检查是否有商家坐标信息
  if (merchant.value?.latitude && merchant.value?.longitude) {
    if (!userStore.isLoggedIn) {
      return '请登录查看距离'
    } else {
      return '请设置收货地址'
    }
  }
  
  return '距离未知'
})

// 距离显示颜色
const distanceColor = computed(() => {
  if (deliveryFeeResult.value && deliveryFeeResult.value.distance > 0) {
    return getDistanceColor(deliveryFeeResult.value.distance)
  }
  return '#666666' // 默认灰色
})
</script>
```

### **3. 调试信息增强**

在调试面板中优先显示距离信息：

```vue
<view class="debug-summary">
  <view class="debug-item">
    <text class="debug-label">配送距离:</text>
    <text class="debug-value debug-distance">{{ distanceText }}</text>
  </view>
  <view class="debug-item">
    <text class="debug-label">最终配送费:</text>
    <text class="debug-value">¥{{ deliveryFeeResult.deliveryFee.toFixed(2) }}</text>
  </view>
  <!-- 更多调试信息... -->
</view>
```

## 📊 **距离分类和颜色系统**

| 距离范围 | 显示格式 | 颜色 | 状态 | 描述 |
|----------|----------|------|------|------|
| < 100m | "附近" | 🟢 绿色 | nearby | 就在附近 |
| 100m - 1km | "500m" | 🟢 绿色 | nearby | 很近 |
| 1km - 2km | "1.5km" | 🔵 蓝色 | close | 较近 |
| 2km - 5km | "3.2km" | 🟡 橙色 | medium | 距离适中 |
| 5km - 10km | "8.5km" | 🟠 深橙色 | far | 较远 |
| > 10km | "15km" | 🔴 红色 | very_far | 很远 |
| > 100km | "较远" | 🔴 红色 | very_far | 距离很远 |
| 未知 | "距离未知" | ⚫ 灰色 | unknown | 无法计算 |

## 🧪 **测试场景**

### **场景1：正常距离计算**
```
输入：用户有地址，商家有坐标，距离1.2km
期望：显示"1.2km"，蓝色
结果：✅ 正确显示距离和颜色
```

### **场景2：很近的距离**
```
输入：用户有地址，商家有坐标，距离50m
期望：显示"附近"，绿色
结果：✅ 正确显示"附近"
```

### **场景3：很远的距离**
```
输入：用户有地址，商家有坐标，距离15km
期望：显示"15km"，红色
结果：✅ 正确显示距离和颜色
```

### **场景4：用户未登录**
```
输入：用户未登录
期望：显示"请登录查看距离"，灰色
结果：✅ 正确提示登录
```

### **场景5：用户无地址**
```
输入：用户已登录但无地址
期望：显示"请设置收货地址"，灰色
结果：✅ 正确提示设置地址
```

### **场景6：商家无坐标**
```
输入：商家没有设置坐标
期望：显示"距离未知"，灰色
结果：✅ 正确显示未知状态
```

## 🎯 **用户体验提升**

### **1. 信息透明度**
- ✅ 用户能直观了解商家距离
- ✅ 颜色编码快速识别远近
- ✅ 配送费和距离关联性更明确

### **2. 决策辅助**
- ✅ 帮助用户选择合适距离的商家
- ✅ 理解配送费的计算依据
- ✅ 预估配送时间

### **3. 视觉体验**
- ✅ 颜色渐变表示距离远近
- ✅ 图标和文字搭配清晰
- ✅ 分隔符优雅分割信息

## 🔍 **调试功能增强**

### **调试面板中的距离信息**
```
📊 计算摘要:
配送距离: 1.2km          ← 突出显示，蓝色
最终配送费: ¥4.00
原始费用: ¥4.00
距离费用: ¥2.00
免费配送: 否
享受折扣: 否

📋 计算过程:
📍 使用地址信息:
   - 地址ID: 123
   - 是否默认: 是
   - 地址来源: default
   - 详细地址: 贵州省贵阳市南明区花果园中央商务区
📏 配送距离: 1.20km
🏠 用户坐标: (26.393492, 106.639598)
🏪 商家坐标: (26.371783, 106.641060)
```

## 🚀 **扩展功能**

### **1. 配送时间估算**
```typescript
/**
 * 估算配送时间（基于距离）
 * @param distance 距离（单位：km）
 * @param baseTime 基础配送时间（分钟）
 * @returns 估算的配送时间（分钟）
 */
export function estimateDeliveryTime(distance: number, baseTime: number = 30): number {
  if (distance <= 0) return baseTime
  
  // 每公里增加2-3分钟配送时间
  const extraTime = Math.ceil(distance * 2.5)
  return baseTime + extraTime
}
```

### **2. 距离描述**
```typescript
/**
 * 获取距离描述文本
 * @param distance 距离（单位：km）
 * @returns 距离描述
 */
export function getDistanceDescription(distance: number): string {
  if (distance < 0.5) return '就在附近'
  if (distance < 2) return '距离较近'
  if (distance < 5) return '距离适中'
  if (distance < 10) return '距离较远'
  return '距离很远'
}
```

## 📈 **性能考虑**

### **1. 计算优化**
- ✅ 距离计算复用配送费计算结果
- ✅ 格式化函数轻量级，无副作用
- ✅ 颜色计算基于简单条件判断

### **2. 缓存策略**
- ✅ 距离结果随配送费结果一起缓存
- ✅ 避免重复的距离计算
- ✅ 响应式更新，性能良好

## 🎊 **总结**

通过添加距离显示功能：

1. **✅ 信息完整性**：用户能同时看到距离和配送费
2. **✅ 视觉友好**：颜色编码直观表示远近
3. **✅ 智能提示**：不同状态有相应的提示信息
4. **✅ 调试友好**：调试面板中突出显示距离信息
5. **✅ 扩展性强**：工具函数可在其他页面复用

现在用户在商家详情页面能够清楚地看到商家距离，更好地理解配送费的计算依据！🎉
