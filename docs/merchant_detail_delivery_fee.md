# 商家详情页面动态配送费计算功能

## 🎯 **功能概述**

为商家详情页面添加了基于用户地址的动态配送费计算功能，替代了原有的固定配送费显示。

## ✨ **功能特性**

### **1. 动态配送费计算**
- 基于用户默认地址和商家位置计算实际配送距离
- 使用统一的配送费计算逻辑（与购物车页面一致）
- 支持距离费用、满额优惠等所有配送费规则

### **2. 智能地址获取**
- 自动从用户地址store获取默认地址
- 地址信息缓存，避免重复请求
- 未设置地址时降级使用商家固定配送费

### **3. 统一计算工具**
- 创建了独立的配送费计算工具 `deliveryFeeCalculator.ts`
- 可在多个页面复用（商家详情、购物车、订单确认等）
- 提供详细的计算过程日志

## 🔧 **技术实现**

### **1. 配送费计算工具**

创建了 `src/utils/deliveryFeeCalculator.ts`：

```typescript
export async function calculateDeliveryFee(params: DeliveryFeeCalculationParams): Promise<DeliveryFeeResult> {
  // 1. 获取配送费配置
  const config = await getDeliveryConfig()
  
  // 2. 计算距离
  const distance = calculateDistance(userLat, userLng, merchantLat, merchantLng)
  
  // 3. 基于距离计算配送费
  let deliveryFee = config.deliveryBaseFee
  if (distance > 3) {
    const extraDistance = Math.ceil(distance - 3)
    deliveryFee += extraDistance * config.deliveryKmFee
  }
  
  // 4. 应用订单金额优惠
  if (config.deliveryFreeEnabled && orderAmount >= config.deliveryFreeAmount) {
    deliveryFee = 0
  } else if (config.deliveryDiscountEnabled && orderAmount >= config.deliveryDiscountAmount) {
    deliveryFee *= config.deliveryDiscountRate
  }
  
  return { deliveryFee, distance, freeDelivery, discounted, debugInfo }
}
```

### **2. 地址Store集成**

使用现有的地址store获取用户默认地址：

```typescript
const addressStore = useAddressStore()

// 获取默认地址
const defaultAddress = await addressStore.fetchDefaultAddress()
```

### **3. 商家详情页面集成**

在商家详情页面中：

```typescript
// 添加配送费相关状态
const deliveryFeeResult = ref<DeliveryFeeResult | null>(null)
const deliveryFeeLoading = ref(false)

// 计算配送费
async function calculateMerchantDeliveryFee() {
  const defaultAddress = await addressStore.fetchDefaultAddress()
  
  const result = await calculateDeliveryFee({
    merchantId: merchant.value.id,
    merchantLat: merchant.value.latitude,
    merchantLng: merchant.value.longitude,
    userLat: defaultAddress.location_latitude,
    userLng: defaultAddress.location_longitude,
    orderAmount: 0
  })
  
  deliveryFeeResult.value = result
}

// 配送费显示文本
const deliveryFeeText = computed(() => {
  if (deliveryFeeLoading.value) return '计算中...'
  if (deliveryFeeResult.value) return formatDeliveryFeeText(deliveryFeeResult.value)
  return `配送费¥${(merchant.value?.delivery_fee || 0).toFixed(2)}`
})
```

### **4. 类型定义更新**

更新了商家类型定义，添加经纬度字段：

```typescript
export interface Merchant {
  // ... 其他字段
  longitude?: number // 经度 GCJ02坐标系
  latitude?: number  // 纬度 GCJ02坐标系
  // ... 其他字段
}
```

## 📊 **计算逻辑**

### **配送费计算流程**

```
1. 获取用户默认地址
   ↓
2. 获取商家位置坐标
   ↓
3. 计算配送距离
   ↓
4. 基于距离计算配送费
   - 距离 ≤ 3km: 基础配送费
   - 距离 > 3km: 基础费 + Math.ceil(超出距离) × 距离费
   ↓
5. 应用订单金额优惠（商家详情页暂不考虑）
   ↓
6. 返回最终配送费
```

### **降级策略**

```
用户未设置地址 → 使用商家固定配送费
商家无坐标信息 → 使用商家固定配送费
计算过程出错 → 使用商家固定配送费
```

## 🎨 **用户界面**

### **配送费显示状态**

| 状态 | 显示文本 | 说明 |
|------|----------|------|
| 计算中 | "计算中..." | 正在获取地址和计算配送费 |
| 免配送费 | "免配送费" | 满足免费条件 |
| 有优惠 | "配送费¥4.00(已优惠)" | 享受折扣优惠 |
| 标准费用 | "配送费¥6.00" | 标准配送费 |
| 降级显示 | "配送费¥5.00" | 使用商家固定费用 |

### **计算过程日志**

```
🚚 开始计算配送费 - 商家ID: 1
⚙️ 配送费配置获取成功
📍 基础配送费: ¥2
📏 距离费用: ¥2/km
🆓 免费门槛: ¥30 (禁用)
💸 折扣门槛: ¥20 (禁用)
📏 配送距离: 3.2km
🏠 用户坐标: (26.393492, 106.639598)
🏪 商家坐标: (26.371783, 106.641060)
📐 距离计算: 超出3km，额外0.2km，向上取整1km
💰 距离费用: ¥2 + 1km × ¥2/km = ¥4.00
✅ 最终配送费: ¥4.00
```

## 🧪 **测试场景**

### **场景1：用户有默认地址**
```
输入：用户有默认地址，商家有坐标
期望：显示基于距离计算的配送费
结果：✅ 显示"配送费¥4.00"
```

### **场景2：用户无默认地址**
```
输入：用户未设置默认地址
期望：显示商家固定配送费
结果：✅ 显示"配送费¥5.00"
```

### **场景3：商家无坐标**
```
输入：商家latitude/longitude为0或null
期望：显示商家固定配送费
结果：✅ 显示"配送费¥5.00"
```

### **场景4：计算过程出错**
```
输入：网络错误或API异常
期望：降级显示商家固定配送费
结果：✅ 显示"配送费¥5.00"
```

## 🚀 **性能优化**

### **1. 地址缓存**
- 地址store自动缓存默认地址5分钟
- 避免重复的API请求
- 提高页面加载速度

### **2. 异步计算**
- 配送费计算不阻塞页面渲染
- 显示"计算中..."状态提升用户体验
- 计算失败时有合理的降级策略

### **3. 统一工具**
- 配送费计算逻辑复用
- 减少代码重复
- 便于维护和更新

## 📝 **使用方法**

### **在其他页面中使用配送费计算工具**

```typescript
import { calculateDeliveryFee, formatDeliveryFeeText } from '@/utils/deliveryFeeCalculator'
import { useAddressStore } from '@/store/address'

const addressStore = useAddressStore()

// 计算配送费
const result = await calculateDeliveryFee({
  merchantId: 1,
  merchantLat: 26.371783,
  merchantLng: 106.641060,
  userLat: 26.393492,
  userLng: 106.639598,
  orderAmount: 25.80
})

// 格式化显示文本
const feeText = formatDeliveryFeeText(result)
console.log(feeText) // "配送费¥4.00"
```

## 🎊 **总结**

通过这次功能开发，实现了：

1. **✅ 动态配送费计算** - 基于用户地址和商家位置的实时计算
2. **✅ 统一计算逻辑** - 创建了可复用的配送费计算工具
3. **✅ 智能降级策略** - 确保在各种异常情况下都有合理的显示
4. **✅ 用户体验优化** - 加载状态、错误处理、缓存机制
5. **✅ 代码可维护性** - 独立的工具函数，便于测试和维护

现在商家详情页面的配送费显示更加准确和智能，为用户提供了更好的购物体验。
