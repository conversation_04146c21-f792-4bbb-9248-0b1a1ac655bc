-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-07-01 17:48:04
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `o_mall_dev`
--

-- --------------------------------------------------------

--
-- 表的结构 `system_file_usage_config`
--

CREATE TABLE `system_file_usage_config` (
  `id` bigint(20) NOT NULL,
  `usage_code` varchar(50) NOT NULL DEFAULT '',
  `usage_name` varchar(100) NOT NULL DEFAULT '',
  `description` varchar(500) NOT NULL DEFAULT '',
  `allow_anonymous` tinyint(4) NOT NULL DEFAULT '0',
  `max_file_size` bigint(20) NOT NULL DEFAULT '0',
  `allowed_types` varchar(500) NOT NULL DEFAULT '',
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `remark` varchar(500) NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- 转存表中的数据 `system_file_usage_config`
--

INSERT INTO `system_file_usage_config` (`id`, `usage_code`, `usage_name`, `description`, `allow_anonymous`, `max_file_size`, `allowed_types`, `sort_order`, `status`, `remark`, `created_at`, `updated_at`) VALUES
(7, 'avatar', '头像', '用户头像图片', 0, 0, '', 1, 1, '', '2025-06-18 15:29:48', '2025-06-18 15:29:48'),
(8, 'product', '商品图片', '商品展示图片', 0, 0, '', 2, 1, '', '2025-06-18 15:29:48', '2025-06-18 15:29:48'),
(9, 'merchant', '商户图片', '商户相关图片', 0, 0, '', 3, 1, '', '2025-06-18 15:29:48', '2025-06-18 15:29:48'),
(10, 'merchant_logo', '商户Logo', '商户品牌Logo图片', 0, 0, '', 4, 1, '', '2025-06-18 15:29:48', '2025-06-18 15:29:48'),
(11, 'complaint', '投诉图片', '用户投诉相关图片', 1, 0, '', 5, 1, '', '2025-06-18 15:29:48', '2025-06-18 15:29:48'),
(12, 'other', '其他用途', '其他类型的文件', 0, 0, '', 6, 1, '', '2025-06-18 15:29:49', '2025-06-18 15:29:49'),
(13, 'group_avatar', '群组头像', '', 0, 2097152, '', 10, 1, '', '2025-06-18 16:39:41', '2025-06-18 16:39:41'),
(14, 'chat_file', '聊天发送的文件', '', 0, 0, '', 10, 1, '', '2025-06-19 15:49:35', '2025-06-19 15:49:35'),
(15, 'chat_image', '聊天发送的图片', '', 0, 0, '', 10, 1, '', '2025-06-19 15:49:52', '2025-06-19 15:49:52'),
(16, 'chat_video', '聊天发送的视频', '', 0, 0, '', 10, 1, '', '2025-06-19 15:50:09', '2025-06-19 15:50:09'),
(17, 'runner-id-front', '跑腿员注册用身份证正面', '跑腿员注册用身份证正面', 0, 0, '', 10, 1, '', '2025-06-21 17:08:34', '2025-06-21 17:08:34'),
(18, 'runner-id-back', '跑腿员注册用身份证背面', '跑腿员注册用身份证背面', 0, 0, '', 10, 1, '', '2025-06-21 17:09:08', '2025-06-21 17:09:08'),
(19, 'runner-face', '跑腿员注册用人脸照片', '跑腿员注册用人脸照片', 0, 0, '', 10, 1, '', '2025-06-21 17:09:39', '2025-06-21 17:09:39'),
(20, 'banner', '首页banner图片', '首页banner图片', 0, 0, '', 10, 1, '', '2025-06-25 18:16:33', '2025-06-25 18:16:33'),
(21, 'appmenu', 'appmenu图片', 'appmenu图片', 0, 0, '', 10, 1, '', '2025-06-25 18:17:51', '2025-06-25 18:17:51'),
(22, 'license', '商户营业执照', '商户营业执照', 1, 0, '', 10, 1, '商户营业执照', '2025-07-01 17:14:42', '2025-07-01 17:15:19');

--
-- 转储表的索引
--

--
-- 表的索引 `system_file_usage_config`
--
ALTER TABLE `system_file_usage_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `usage_code` (`usage_code`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `system_file_usage_config`
--
ALTER TABLE `system_file_usage_config`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
