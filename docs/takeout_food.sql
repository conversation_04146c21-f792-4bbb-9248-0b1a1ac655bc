-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-07-03 13:55:32
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `o_mall_dev`
--

-- --------------------------------------------------------

--
-- 表的结构 `takeout_food`
--

CREATE TABLE `takeout_food` (
  `id` bigint(20) NOT NULL,
  `merchant_id` bigint(20) NOT NULL DEFAULT '0',
  `category_id` bigint(20) NOT NULL DEFAULT '0',
  `name` varchar(100) NOT NULL DEFAULT '',
  `description` longtext NOT NULL,
  `brief` varchar(200) NOT NULL DEFAULT '',
  `image` varchar(255) NOT NULL DEFAULT '',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `original_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `preparation_time` int(11) NOT NULL DEFAULT '15',
  `packaging_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
  `is_combination` tinyint(1) NOT NULL DEFAULT '0',
  `is_spicy` tinyint(1) NOT NULL DEFAULT '0',
  `has_variants` tinyint(1) NOT NULL DEFAULT '0',
  `sold_out` tinyint(1) NOT NULL DEFAULT '0',
  `daily_limit` int(11) NOT NULL DEFAULT '0',
  `sold_count` int(11) NOT NULL DEFAULT '0',
  `total_sold` int(11) NOT NULL DEFAULT '0',
  `tags` varchar(255) NOT NULL DEFAULT '',
  `keywords` varchar(255) NOT NULL DEFAULT '',
  `is_visible` tinyint(1) NOT NULL DEFAULT '1',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0',
  `status` int(11) NOT NULL DEFAULT '0',
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `view_count` int(11) NOT NULL DEFAULT '0',
  `comment_count` int(11) NOT NULL DEFAULT '0',
  `favorite_count` int(11) NOT NULL DEFAULT '0',
  `rating` decimal(10,2) NOT NULL DEFAULT '0.00',
  `rating_total` int(11) NOT NULL DEFAULT '0',
  `rating_count` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `audit_status` int(11) NOT NULL DEFAULT '0',
  `auditor_id` bigint(20) NOT NULL DEFAULT '0',
  `audit_reason` varchar(255) NOT NULL DEFAULT '',
  `audit_time` datetime DEFAULT NULL,
  `global_category_id` bigint(20) NOT NULL DEFAULT '0',
  `sales_count` int(11) NOT NULL DEFAULT '0',
  `weight` decimal(10,3) NOT NULL DEFAULT '0.000'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- 转存表中的数据 `takeout_food`
--

INSERT INTO `takeout_food` (`id`, `merchant_id`, `category_id`, `name`, `description`, `brief`, `image`, `price`, `original_price`, `preparation_time`, `packaging_fee`, `is_combination`, `is_spicy`, `has_variants`, `sold_out`, `daily_limit`, `sold_count`, `total_sold`, `tags`, `keywords`, `is_visible`, `is_recommend`, `status`, `sort_order`, `view_count`, `comment_count`, `favorite_count`, `rating`, `rating_total`, `rating_count`, `created_at`, `updated_at`, `audit_status`, `auditor_id`, `audit_reason`, `audit_time`, `global_category_id`, `sales_count`, `weight`) VALUES
(1, 1, 1, '油条', '早餐油条，美味无添加；这是是更加详细的商品介绍，用来在详情页中展示详情；', '早餐油条，美味无添加；这是简介，用来在列表中显示。', 'http://omallimg.qwyx.shop/takeout_food/2025/05/18/1747559803886599000_0CjxF3xA.png', '5.00', '5.00', 15, '1.00', 0, 0, 1, 0, 100, 0, 0, '热销,推荐', '油条,早餐', 1, 0, 1, 10, 272, 0, 0, '0.00', 0, 0, '2025-05-17 15:48:25', '2025-06-14 07:18:10', 1, 1, '', '2025-05-21 10:49:33', 5, 0, '0.000'),
(2, 6, 3, '牛肉串', '', '', 'http://omallimg.qwyx.shop/takeout_food/2025/05/21/1747813676576199001_pnEmMfyw.jpg', '8.00', '10.00', 15, '1.00', 0, 0, 0, 0, 0, 0, 0, '', '', 1, 0, 0, 10, 10, 0, 0, '0.00', 0, 0, '2025-05-21 15:48:29', '2025-05-21 15:52:04', 1, 1, '', '2025-05-21 15:49:31', 0, 0, '0.000'),
(3, 6, 4, '小白菜', '', '', 'http://omallimg.qwyx.shop/takeout_food/2025/05/21/1747813949315448462_1x78jQCC.jpg', '2.00', '5.00', 15, '1.00', 0, 0, 0, 0, 0, 0, 0, '', '', 1, 0, 0, 10, 1, 0, 0, '0.00', 0, 0, '2025-05-21 15:53:09', '2025-05-21 15:53:37', 1, 1, '', '2025-05-21 15:53:37', 0, 0, '0.000'),
(4, 5, 9, '折耳根美式', '大大撒旦', '大苏打撒旦', 'http://omallimg.qwyx.shop/takeout_food/2025/05/21/1747814069835061445_zGJvWktn.png', '9.90', '9.90', 15, '2.00', 0, 1, 1, 0, 0, 0, 0, '热销,新品,推荐', '', 1, 0, 1, 1, 24, 0, 0, '0.00', 0, 0, '2025-05-21 15:56:27', '2025-05-21 17:16:44', 1, 1, '', '2025-05-21 16:09:38', 0, 0, '0.000'),
(5, 4, 2, 'mao', '', '', 'http://omallimg.qwyx.shop/takeout_food/2025/05/21/1747817154732965021_ARiNmP5J.jpg', '200.00', '200.00', 15, '0.00', 0, 0, 0, 0, 1000, 0, 0, '', '', 1, 0, 1, 10, 6, 0, 0, '0.00', 0, 0, '2025-05-21 16:45:58', '2025-05-21 17:17:55', 1, 1, '', '2025-05-21 16:46:51', 0, 0, '0.000'),
(6, 1, 1, 'asdg', 'wfgwef', 'awefwe', 'http://omallimg.qwyx.shop/takeout_food/2025/05/21/1747817656771573000_gsaYtLO2.png', '1.00', '1.50', 5, '0.50', 0, 0, 0, 0, 0, 0, 0, '新品', '', 1, 0, 1, 10, 36, 0, 0, '0.00', 0, 0, '2025-05-21 16:54:47', '2025-06-05 23:20:59', 1, 1, '', '2025-05-21 17:04:14', 0, 0, '0.000'),
(7, 1, 1, '下架商品', 'adsfvgdsf', 'rwgwrfwef', 'http://omallimg.qwyx.shop/takeout_food/2025/05/21/1747818280607688000_a5wRmoiV.png', '14.00', '20.00', 15, '1.00', 0, 0, 0, 0, 0, 0, 0, '新品', '', 1, 0, 0, 10, 39, 0, 0, '0.00', 0, 0, '2025-05-21 17:04:58', '2025-06-30 23:05:00', 0, 0, '', NULL, 5, 0, '0.000');

--
-- 转储表的索引
--

--
-- 表的索引 `takeout_food`
--
ALTER TABLE `takeout_food`
  ADD PRIMARY KEY (`id`),
  ADD KEY `takeout_food_merchant_id` (`merchant_id`),
  ADD KEY `takeout_food_category_id` (`category_id`),
  ADD KEY `takeout_food_global_category_id` (`global_category_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `takeout_food`
--
ALTER TABLE `takeout_food`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
