-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-07-01 15:28:08
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `o_mall_dev`
--

-- --------------------------------------------------------

--
-- 表的结构 `user_account_transaction`
--

CREATE TABLE `user_account_transaction` (
  `i_d` bigint(20) NOT NULL COMMENT '交易ID',
  `user_i_d` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `account_i_d` bigint(20) NOT NULL DEFAULT '0' COMMENT '账户ID',
  `transaction_no` varchar(64) NOT NULL DEFAULT '' COMMENT '交易流水号',
  `related_i_d` bigint(20) NOT NULL DEFAULT '0' COMMENT '关联ID，如订单ID',
  `related_type` varchar(32) NOT NULL DEFAULT '' COMMENT '关联类型，如order',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '交易金额',
  `before_balance` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '交易前余额',
  `after_balance` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '交易后余额',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '交易类型',
  `operation` varchar(32) NOT NULL DEFAULT '' COMMENT '操作类型:增加/减少',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '交易状态:1成功,0失败,-1处理中',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '交易描述',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `operator_i_d` bigint(20) NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `client_i_p` varchar(40) NOT NULL DEFAULT '' COMMENT '客户端IP',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- 转存表中的数据 `user_account_transaction`
--

INSERT INTO `user_account_transaction` (`i_d`, `user_i_d`, `account_i_d`, `transaction_no`, `related_i_d`, `related_type`, `amount`, `before_balance`, `after_balance`, `type`, `operation`, `status`, `description`, `remark`, `operator_i_d`, `client_i_p`, `created_at`, `updated_at`) VALUES
(1, 2, 1, 'ADJ_2_1749041343', 0, 'admin_adjust', '500.00', '0.00', '500.00', 'adjust', 'increase', 1, '管理员调整账户余额', '管理员调整余额从0.00到500.00', 0, '', '2025-06-04 20:49:04', '2025-06-04 20:49:04'),
(2, 2, 0, '', 4, 'payment', '10.40', '500.00', '489.60', 'payment', 'decrease', 0, '', '订单支付: 14', 0, '', '2025-06-04 20:49:26', '2025-06-04 20:49:26'),
(4, 2, 0, 'BALTRANS2025060421330623587', 6, 'payment', '19.80', '489.60', '469.80', 'payment', 'decrease', 0, '', '订单支付: 5', 0, '', '2025-06-04 21:33:06', '2025-06-04 21:33:06'),
(5, 2, 0, 'BALTRANS2025060421334421646', 7, 'payment', '20.00', '469.80', '449.80', 'payment', 'decrease', 0, '', '订单支付: 6', 0, '', '2025-06-04 21:33:44', '2025-06-04 21:33:44'),
(6, 2, 0, 'BALTRANS2025063023480724811', 9, 'payment', '40.00', '449.80', '409.80', 'payment', 'decrease', 0, '', '订单支付: 42', 0, '', '2025-06-30 23:48:07', '2025-06-30 23:48:07'),
(7, 2, 0, 'BALTRANS2025070110403227486', 10, 'payment', '11.90', '409.80', '397.90', 'payment', 'decrease', 0, '', '订单支付: 43', 0, '', '2025-07-01 10:40:32', '2025-07-01 10:40:32'),
(8, 2, 0, 'BALTRANS2025070111145520635', 11, 'payment', '40.00', '397.90', '357.90', 'payment', 'decrease', 0, '', '订单支付: 44', 0, '', '2025-07-01 11:14:56', '2025-07-01 11:14:56'),
(9, 2, 0, 'BALTRANS2025070111401229601', 12, 'payment', '40.00', '357.90', '317.90', 'payment', 'decrease', 0, '', '订单支付: 46', 0, '', '2025-07-01 11:40:13', '2025-07-01 11:40:13'),
(10, 2, 0, 'BALTRANS2025070111442828396', 13, 'payment', '11.90', '317.90', '306.00', 'payment', 'decrease', 0, '', '订单支付: 47', 0, '', '2025-07-01 11:44:29', '2025-07-01 11:44:29'),
(11, 2, 0, 'BALTRANS2025070112021928151', 14, 'payment', '11.90', '306.00', '294.10', 'payment', 'decrease', 0, '', '订单支付: 45', 0, '', '2025-07-01 12:02:20', '2025-07-01 12:02:20'),
(12, 2, 0, 'BALTRANS2025070112453925804', 15, 'payment', '40.00', '294.10', '254.10', 'payment', 'decrease', 0, '', '订单支付: 48', 0, '', '2025-07-01 12:45:39', '2025-07-01 12:45:39');

--
-- 转储表的索引
--

--
-- 表的索引 `user_account_transaction`
--
ALTER TABLE `user_account_transaction`
  ADD PRIMARY KEY (`i_d`),
  ADD UNIQUE KEY `transaction_no` (`transaction_no`),
  ADD KEY `user_account_transaction_user_i_d` (`user_i_d`),
  ADD KEY `user_account_transaction_account_i_d` (`account_i_d`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `user_account_transaction`
--
ALTER TABLE `user_account_transaction`
  MODIFY `i_d` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '交易ID', AUTO_INCREMENT=13;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
