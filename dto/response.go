/**
 * 通用响应数据结构
 *
 * 该文件定义了API响应的标准格式，确保所有API返回格式一致，
 * 包括状态码、消息和数据部分，便于前端统一处理。
 */

package dto

import (
	"o_mall_backend/utils"
)

// Response 标准API响应结构
type Response struct {
	Code    int         `json:"code"`           // 状态码，200表示成功，其他值表示错误
	Message string      `json:"message"`        // 响应消息，成功或错误描述
	Data    interface{} `json:"data,omitempty"` // 响应数据，可选字段
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}) *Response {
	return &Response{
		Code:    200,
		Message: "操作成功",
		Data:    data,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, message string) *Response {
	if code == 200 {
		code = 500 // 避免错误响应使用成功状态码
	}

	return &Response{
		Code:    code,
		Message: message,
	}
}

// NewErrorResponseWithError 从错误信息创建错误响应
func NewErrorResponseWithError(err *utils.AppError) *Response {
	return &Response{
		Code:    err.Code,
		Message: err.Message,
	}
}

// 预定义错误码常量
const (
	// 基础错误码 (400-403)
	CodeInvalidParams = 400 // 无效的参数
	CodeUnauthorized  = 401 // 未认证
	CodeForbidden     = 403 // 禁止访问

	// 通用错误码 (500-599)
	CodeInternalError = 500 // 内部错误
	CodeUnknownError  = 599 // 未知错误

	// 系统级错误码 (1000-1999)
	CodeSystemError     = 1000 // 系统内部错误
	CodeDatabaseError   = 1001 // 数据库错误
	CodeCacheError      = 1002 // 缓存错误
	CodeValidationError = 1003 // 数据验证错误
	CodeNotFoundError   = 1004 // 资源不存在

	// 用户相关错误码 (2000-2999)
	CodeUserNotFound     = 2000 // 用户不存在
	CodePasswordError    = 2001 // 密码错误
	CodeTokenInvalid     = 2002 // Token无效
	CodePermissionDenied = 2003 // 权限不足

	// 商品相关错误码 (3000-3999)
	CodeProductNotFound = 3000 // 商品不存在
	CodeOutOfStock      = 3001 // 商品库存不足

	// 订单相关错误码 (4000-4999)
	CodeOrderNotFound    = 4000 // 订单不存在
	CodeOrderStatusError = 4001 // 订单状态错误

	// 支付相关错误码 (5000-5999)
	CodePaymentFailed = 5000 // 支付失败

	// 通用错误码
	CodeConflict = 409 // 资源冲突

	// 其他模块错误码可以继续按规则添加
)
