-- 优惠券已发放数量修复SQL脚本
-- 用于修复历史数据中的issued_count字段

-- =====================================================
-- 1. 数据一致性检查
-- =====================================================

-- 检查当前数据不一致的情况
SELECT 
    tc.id,
    tc.name,
    tc.merchant_id,
    tc.issued_count AS current_issued_count,
    COALESCE(actual_count.count, 0) AS actual_issued_count,
    tc.total_limit,
    CASE 
        WHEN tc.issued_count != COALESCE(actual_count.count, 0) THEN '不一致'
        ELSE '一致'
    END AS status
FROM takeout_coupon tc
LEFT JOIN (
    SELECT 
        coupon_id,
        COUNT(*) as count
    FROM takeout_user_coupon 
    GROUP BY coupon_id
) actual_count ON tc.id = actual_count.coupon_id
WHERE tc.issued_count != COALESCE(actual_count.count, 0)
ORDER BY tc.id;

-- =====================================================
-- 2. 备份当前数据
-- =====================================================

-- 创建备份表（如果不存在）
CREATE TABLE IF NOT EXISTS takeout_coupon_backup_20250120 AS 
SELECT * FROM takeout_coupon WHERE 1=0;

-- 备份需要修复的数据
INSERT INTO takeout_coupon_backup_20250120
SELECT * FROM takeout_coupon 
WHERE id IN (
    SELECT tc.id
    FROM takeout_coupon tc
    LEFT JOIN (
        SELECT coupon_id, COUNT(*) as count
        FROM takeout_user_coupon 
        GROUP BY coupon_id
    ) actual_count ON tc.id = actual_count.coupon_id
    WHERE tc.issued_count != COALESCE(actual_count.count, 0)
);

-- =====================================================
-- 3. 修复已发放数量
-- =====================================================

-- 方法1：使用UPDATE语句修复
UPDATE takeout_coupon tc 
SET 
    issued_count = COALESCE((
        SELECT COUNT(*) 
        FROM takeout_user_coupon tuc 
        WHERE tuc.coupon_id = tc.id
    ), 0),
    updated_at = NOW()
WHERE tc.issued_count != COALESCE((
    SELECT COUNT(*) 
    FROM takeout_user_coupon tuc2 
    WHERE tuc2.coupon_id = tc.id
), 0);

-- =====================================================
-- 4. 验证修复结果
-- =====================================================

-- 验证修复后的数据一致性
SELECT 
    '修复后数据验证' as check_type,
    COUNT(*) as total_coupons,
    SUM(CASE WHEN tc.issued_count = COALESCE(actual_count.count, 0) THEN 1 ELSE 0 END) as consistent_count,
    SUM(CASE WHEN tc.issued_count != COALESCE(actual_count.count, 0) THEN 1 ELSE 0 END) as inconsistent_count
FROM takeout_coupon tc
LEFT JOIN (
    SELECT 
        coupon_id,
        COUNT(*) as count
    FROM takeout_user_coupon 
    GROUP BY coupon_id
) actual_count ON tc.id = actual_count.coupon_id;

-- 显示修复后的详细情况
SELECT 
    tc.id,
    tc.name,
    tc.merchant_id,
    tc.issued_count,
    tc.total_limit,
    CASE 
        WHEN tc.total_limit > 0 THEN 
            CONCAT(ROUND((tc.issued_count * 100.0 / tc.total_limit), 2), '%')
        ELSE '无限制'
    END AS issue_rate,
    tc.status,
    tc.updated_at
FROM takeout_coupon tc
WHERE tc.issued_count > 0
ORDER BY tc.issued_count DESC
LIMIT 20;

-- =====================================================
-- 5. 数据统计报告
-- =====================================================

-- 总体统计
SELECT 
    '总体统计' as report_type,
    COUNT(*) as total_coupons,
    SUM(tc.issued_count) as total_issued,
    AVG(tc.issued_count) as avg_issued_per_coupon,
    MAX(tc.issued_count) as max_issued,
    COUNT(CASE WHEN tc.issued_count > 0 THEN 1 END) as coupons_with_issues
FROM takeout_coupon tc;

-- 按商家统计
SELECT 
    tc.merchant_id,
    COUNT(*) as coupon_count,
    SUM(tc.issued_count) as total_issued,
    AVG(tc.issued_count) as avg_issued,
    MAX(tc.issued_count) as max_issued
FROM takeout_coupon tc
GROUP BY tc.merchant_id
HAVING SUM(tc.issued_count) > 0
ORDER BY total_issued DESC
LIMIT 10;

-- 按状态统计
SELECT 
    tc.status,
    CASE 
        WHEN tc.status = 1 THEN '待发布'
        WHEN tc.status = 2 THEN '已发布'
        WHEN tc.status = 3 THEN '已使用'
        WHEN tc.status = 4 THEN '已过期'
        WHEN tc.status = 5 THEN '已禁用'
        ELSE '未知状态'
    END as status_text,
    COUNT(*) as coupon_count,
    SUM(tc.issued_count) as total_issued
FROM takeout_coupon tc
GROUP BY tc.status
ORDER BY tc.status;

-- =====================================================
-- 6. 清理备份表（可选）
-- =====================================================

-- 如果修复成功，可以删除备份表
-- DROP TABLE IF EXISTS takeout_coupon_backup_20250120;

-- =====================================================
-- 7. 创建监控视图（可选）
-- =====================================================

-- 创建数据一致性监控视图
CREATE OR REPLACE VIEW v_coupon_consistency_check AS
SELECT 
    tc.id,
    tc.name,
    tc.merchant_id,
    tc.issued_count AS db_issued_count,
    COALESCE(actual_count.count, 0) AS actual_issued_count,
    tc.total_limit,
    CASE 
        WHEN tc.issued_count = COALESCE(actual_count.count, 0) THEN 'OK'
        ELSE 'INCONSISTENT'
    END AS consistency_status,
    ABS(tc.issued_count - COALESCE(actual_count.count, 0)) AS difference
FROM takeout_coupon tc
LEFT JOIN (
    SELECT 
        coupon_id,
        COUNT(*) as count
    FROM takeout_user_coupon 
    GROUP BY coupon_id
) actual_count ON tc.id = actual_count.coupon_id;

-- 使用监控视图检查数据一致性
-- SELECT * FROM v_coupon_consistency_check WHERE consistency_status = 'INCONSISTENT';

-- =====================================================
-- 执行说明
-- =====================================================

/*
执行步骤：
1. 先执行数据一致性检查，了解当前问题规模
2. 执行备份操作，确保数据安全
3. 执行修复SQL，更新issued_count字段
4. 执行验证查询，确认修复效果
5. 查看统计报告，了解修复后的数据情况

注意事项：
1. 建议在维护时间窗口执行
2. 执行前请确保数据库备份
3. 可以分批执行，避免长时间锁表
4. 执行后验证应用功能是否正常

回滚方案：
如果修复出现问题，可以从备份表恢复：
UPDATE takeout_coupon tc 
SET issued_count = backup.issued_count
FROM takeout_coupon_backup_20250120 backup
WHERE tc.id = backup.id;
*/
