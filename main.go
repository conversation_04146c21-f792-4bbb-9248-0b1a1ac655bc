/**
 * O_Mall 多商家电商平台 - 主程序入口
 *
 * 该文件是应用程序的入口点，负责初始化配置、数据库连接、
 * 注册路由和启动HTTP服务器等核心功能。
 */

package main

import (
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	_ "github.com/go-sql-driver/mysql"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/apidoc"
	_ "o_mall_backend/modules/runner" // 导入runner模块以注册ORM模型
	"o_mall_backend/routers"
	"o_mall_backend/utils/config"
	"o_mall_backend/utils/redis"
	"o_mall_backend/utils/sms"
	"o_mall_backend/utils/storage"
)

// 存储全局配置的映射表
var globalConfig = make(map[string]string)

// 初始化日志
func initLogs() {
	logConfig, _ := web.AppConfig.String("logfilepath")
	logLevel, err := web.AppConfig.Int("loglevel")
	if err != nil {
		// 设置为 Debug 级别，确保所有日志都会输出
		logLevel = logs.LevelDebug
		logs.Warn("未配置日志级别或配置错误，使用默认值：Debug")
	}

	logs.SetLogger(logs.AdapterFile, fmt.Sprintf(`{"filename":"%s","level":%d}`, logConfig, logLevel))
	logs.EnableFuncCallDepth(true)
	logs.SetLogFuncCallDepth(3)
	logs.Async()

	logs.Info("日志系统初始化完成，日志级别：%d", logLevel)
}

// 初始化数据库连接
func initDatabase() {
	// 记录当前运行模式
	runmode := web.AppConfig.DefaultString("runmode", "")
	logs.Info("initDatabase: 当前运行模式 = %s", runmode)

	var (
		user, pass, host, port, dbName string
	)

	// 读取配置文件中的数据库配置
	user = web.AppConfig.DefaultString("mysqluser", "")
	pass = web.AppConfig.DefaultString("mysqlpass", "")
	host = web.AppConfig.DefaultString("mysqlhost", "")
	port = web.AppConfig.DefaultString("mysqlport", "")
	dbName = web.AppConfig.DefaultString("mysqldb", "")

	logs.Info("数据库配置(从配置文件): user=[%s], host=[%s], port=[%s], dbName=[%s]", user, host, port, dbName)

	// 检查配置是否完整
	configComplete := user != "" && host != "" && port != "" && dbName != ""

	// 如果配置不完整，尝试从环境变量读取
	if !configComplete {
		logs.Warn("配置文件中的数据库配置不完整，尝试从环境变量获取...")

		// 先尝试使用MYSQL_开头的环境变量
		if user == "" {
			user = os.Getenv("MYSQL_USER")
		}
		if pass == "" {
			pass = os.Getenv("MYSQL_PASS")
		}
		if host == "" {
			host = os.Getenv("MYSQL_HOST")
		}
		if port == "" {
			port = os.Getenv("MYSQL_PORT")
		}
		if dbName == "" {
			dbName = os.Getenv("MYSQL_DB")
		}

		// 如果仍不成功，尝试使用O_MALL_格式的环境变量
		if user == "" {
			user = os.Getenv("O_MALL_DATABASE_MYSQLUSER")
		}
		if pass == "" {
			pass = os.Getenv("O_MALL_DATABASE_MYSQLPASS")
		}
		if host == "" {
			host = os.Getenv("O_MALL_DATABASE_MYSQLHOST")
		}
		if port == "" {
			port = os.Getenv("O_MALL_DATABASE_MYSQLPORT")
		}
		if dbName == "" {
			dbName = os.Getenv("O_MALL_DATABASE_MYSQLDB")
		}

		logs.Info("数据库配置(包含环境变量): user=[%s], host=[%s], port=[%s], dbName=[%s]", user, host, port, dbName)

		// 再次检查配置完整性
		configComplete = user != "" && host != "" && port != "" && dbName != ""
	}

	// 如果仍不完整，使用硬编码默认值（仅开发环境）
	if !configComplete {
		if runmode == "dev" {
			logs.Warn("无法从配置文件或环境变量获取完整的数据库配置，在开发模式下使用默认值")

			if user == "" {
				user = "root"
			}
			if host == "" {
				host = "localhost"
			}
			if port == "" {
				port = "3306"
			}
			if dbName == "" {
				dbName = "o_mall_dev"
			}

			logs.Info("数据库配置(使用默认值): user=[%s], host=[%s], port=[%s], dbName=[%s]", user, host, port, dbName)
		} else {
			logs.Error("无法获取完整的数据库配置，应用将无法连接数据库")
			return
		}
	}

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=true&loc=Local",
		user, pass, host, port, dbName)

	// 注册数据库别名和连接（新版本Beego）
	err := orm.RegisterDataBase("default", "mysql", dsn)
	if err != nil {
		logs.Error("注册数据库失败: %v", err)
		return
	}

	// 同时为旧版本的Beego ORM也注册数据库连接
	// 这是为了兼容使用旧版Beego ORM的模块（如merchant模块）
	//oldErr := oldOrm.RegisterDataBase("default", "mysql", dsn)
	//if oldErr != nil {
	//	logs.Error("旧版ORM注册数据库失败: %v", oldErr)
	//	return
	//}

	// 注册数据库驱动（新版本Beego）
	err = orm.RegisterDriver("mysql", orm.DRMySQL)
	if err != nil {
		logs.Error("注册数据库驱动失败: %v", err)
		return
	}

	// 为旧版本Beego ORM也注册数据库驱动
	//oldErr = oldOrm.RegisterDriver("mysql", oldOrm.DRMySQL)
	//if oldErr != nil {
	//	logs.Error("旧版ORM注册数据库驱动失败: %v", oldErr)
	//	return
	//}

	// 处理数据库连接出现的"DataBase alias name already registered"问题
	defer func() {
		if r := recover(); r != nil {
			logs.Error("注册数据库时发生错误: %v", r)
		}
	}()

	// 添加重试机制
	maxRetries := 3
	var connectErr error

	for i := 0; i < maxRetries; i++ {
		// 测试数据库连接
		o := orm.NewOrm()
		var result int
		connectErr = o.Raw("SELECT 1").QueryRow(&result)
		if connectErr != nil {
			logs.Warn("数据库连接测试失败，第 %d 次尝试: %v", i+1, connectErr)
			time.Sleep(time.Second)
			continue
		}

		if result != 1 {
			logs.Warn("数据库连接测试结果异常: %d", result)
			connectErr = fmt.Errorf("数据库连接测试结果异常")
			continue
		}

		// 连接成功
		logs.Info("数据库连接成功!")
		break
	}

	// 检查是否成功连接
	if connectErr != nil {
		logs.Error("经过 %d 次尝试后仍无法连接数据库: %v", maxRetries, connectErr)
		return
	}

	// 设置连接池参数
	orm.SetMaxIdleConns("default", 10)
	orm.SetMaxOpenConns("default", 30)

	// 标记数据库初始化完成，让配置模块知道可以安全访问数据库
	config.SetDBInitialized()

	logs.Info("数据库连接初始化完成，DSN: %s", dsn)
}

// 初始化中间件
func initMiddlewares() {
	// 注册CORS中间件，处理所有请求的跨域问题
	web.InsertFilter("*", web.BeforeRouter, middlewares.CORSMiddleware)
	logs.Info("CORS中间件注册完成")
}

// 初始化Redis连接
func initRedis() {
	// 使用redis模块初始化Redis客户端
	err := redis.Init()
	if err != nil {
		logs.Error("初始化Redis客户端失败: %v", err)
		return
	}

	logs.Info("Redis客户端初始化完成")
}

// 初始化短信服务
func initSMS() {
	// 先加载短信服务配置
	// smsProvider := web.AppConfig.DefaultString("sms::sms_provider", "")
	// if smsProvider == "" {
	// 	logs.Warn("短信服务提供商配置为空，将使用模拟短信发送方式")
	// }

	// 初始化短信服务
	sms.Init()
	// logs.Info("短信服务初始化完成，服务提供商: %s", smsProvider)
}

// 应用启动后，同步所有模块的API文档
func syncAllModuleAPIs() {
	// 定义需要同步API文档的模块列表
	modules := []string{"admin", "system", "merchant", "user", "order", "apidoc", "payment", "ui_config", "delivery", "history", "favorites"}

	// 创建WaitGroup，用于等待所有goroutine完成
	var wg sync.WaitGroup

	// 先同步每个模块的API信息（并发执行）
	logs.Info("开始并发同步所有模块的API文档...")
	wg.Add(len(modules))
	for _, module := range modules {
		// 为每个模块创建一个goroutine
		go func(moduleName string) {
			// 函数结束时通知WaitGroup减少计数
			defer wg.Done()

			logs.Info("开始同步模块 %s 的API文档...", moduleName)
			err := apidoc.SyncAPIDoc(moduleName)
			if err != nil {
				logs.Error("同步模块 %s 的API文档失败: %v", moduleName, err)
				return
			}
			logs.Info("模块 %s 的API文档同步完成", moduleName)
		}(module) // 通过参数传递module值避免闭包问题
	}
	// 等待所有API文档同步完成
	wg.Wait()
	logs.Info("所有模块的API文档同步已完成")

	// 然后同步每个模块的DTO信息（并发执行）
	logs.Info("开始并发同步所有模块的DTO文档...")
	wg.Add(len(modules))
	for _, module := range modules {
		// 为每个模块创建一个goroutine
		go func(moduleName string) {
			// 函数结束时通知WaitGroup减少计数
			defer wg.Done()

			logs.Info("开始同步模块 %s 的DTO文档...", moduleName)
			err := apidoc.SyncDTODoc(moduleName)
			if err != nil {
				logs.Error("同步模块 %s 的DTO文档失败: %v", moduleName, err)
				return
			}
			logs.Info("模块 %s 的DTO文档同步完成", moduleName)
		}(module) // 通过参数传递module值避免闭包问题
	}
	// 等待所有DTO文档同步完成
	wg.Wait()
	logs.Info("所有模块的DTO文档同步已完成")

	// 最后同步每个模块的控制器信息（并发执行）
	logs.Info("开始并发同步所有模块的控制器文档...")
	wg.Add(len(modules))
	for _, module := range modules {
		// 为每个模块创建一个goroutine
		go func(moduleName string) {
			// 函数结束时通知WaitGroup减少计数
			defer wg.Done()

			logs.Info("开始同步模块 %s 的控制器文档...", moduleName)
			err := apidoc.SyncControllerDoc(moduleName)
			if err != nil {
				logs.Error("同步模块 %s 的控制器文档失败: %v", moduleName, err)
				return
			}
			logs.Info("模块 %s 的控制器文档同步完成", moduleName)
		}(module) // 通过参数传递module值避免闭包问题
	}
	// 等待所有控制器文档同步完成
	wg.Wait()
	logs.Info("所有模块的控制器文档同步已完成")
}

// 设置定时同步API文档的任务
func setupAPISyncTask() {
	// 每天凌晨2点同步一次API文档
	go func() {
		for {
			now := time.Now()
			next := time.Date(now.Year(), now.Month(), now.Day()+1, 2, 0, 0, 0, now.Location())
			time.Sleep(next.Sub(now))

			logs.Info("开始定时同步API文档...")
			syncAllModuleAPIs()
			logs.Info("定时同步API文档完成")
		}
	}()
}

// 主函数
func main() {
	// 初始化日志系统
	initLogs()

	// 初始化配置（优先）
	err := config.InitConfig()
	if err != nil {
		logs.Error("初始化配置失败: %v", err)
		return
	}
	logs.Info("配置初始化完成")

	// 验证runmode是否正确设置
	runmode := web.AppConfig.DefaultString("runmode", "")
	logs.Info("主程序获取到的运行模式: %s", runmode)
	if runmode == "" {
		logs.Error("运行模式未正确设置，将使用默认值 'dev'")
		err = web.AppConfig.Set("runmode", "dev")
		if err != nil {
			logs.Error("设置默认运行模式失败: %v", err)
		}
	}

	// 添加小延时，确保配置完全加载
	time.Sleep(time.Millisecond * 100)

	// 初始化存储模块
	storage.Init()
	logs.Info("存储模块初始化完成")

	// 初始化数据库连接（使用defer-recover捕获可能的panic）
	func() {
		defer func() {
			if r := recover(); r != nil {
				logs.Error("初始化数据库连接时发生panic: %v", r)
			}
		}()

		initDatabase()
	}()

	// 初始化Redis连接
	func() {
		defer func() {
			if r := recover(); r != nil {
				logs.Error("初始化Redis连接时发生panic: %v", r)
			}
		}()

		initRedis()
	}()

	// 在程序结束时关闭Redis连接
	defer func() {
		if err := redis.Close(); err != nil {
			logs.Error("关闭Redis连接失败: %v", err)
		} else {
			logs.Info("Redis连接已关闭")
		}
	}()

	// 注册中间件
	initMiddlewares()

	// 注册路由（这会注册所有模型）
	routers.Init()

	// 同步数据库表结构（开发模式下）
	// 注意：在生产环境中，不要使用自动创建表的功能
	logs.Info("检查并自动同步数据库表结构...")
	runmode = web.AppConfig.DefaultString("runmode", "")
	logs.Info("同步表结构时的运行模式: %s", runmode)
	if runmode == "dev" {
		err := orm.RunSyncdb("default", false, true)
		if err != nil {
			logs.Error("自动同步数据库表结构失败: %v", err)
			// 不中断程序执行，只记录错误
		} else {
			logs.Info("数据库表结构同步完成")
		}
	}

	// 初始化短信服务（在数据库表创建之后）
	func() {
		defer func() {
			if r := recover(); r != nil {
				logs.Error("初始化短信服务时发生panic: %v", r)
			}
		}()

		initSMS()
	}()

	// 先初始化API文档模块
	apidoc.Init()

	flushapidoc := web.AppConfig.DefaultString("flushapidoc", "false")
	if flushapidoc == "true" {
		// 然后再同步API文档
		syncAllModuleAPIs()
	}

	// 设置定时同步任务
	//setupAPISyncTask()

	// 初始化并启动定时任务模块
	// scheduler.Init()
	// logs.Info("定时任务模块已初始化")

	// 启动HTTP服务
	logs.Info("O_Mall 服务准备启动...")
	web.Run()
}
