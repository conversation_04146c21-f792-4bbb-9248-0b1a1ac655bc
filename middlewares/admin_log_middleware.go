/**
 * admin_log_middleware.go
 * 管理员日志中间件
 *
 * 本文件实现了管理员操作日志记录的中间件功能，自动记录管理员的所有操作。
 * 通过拦截请求和响应，收集操作信息并异步写入日志，确保系统安全审计和问题追踪。
 */

package middlewares

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"

	adminDto "o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/models"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// AdminLogMiddleware 管理员日志中间件
// 自动记录管理员操作日志
func AdminLogMiddleware() func(ctx *context.Context) {
	return func(ctx *context.Context) {
		logs.Info("====== 管理员日志中间件触发 ======")
		logs.Info("请求路径: %s, 方法: %s", ctx.Request.URL.Path, ctx.Request.Method)

		// 检查请求路径是否需要记录日志
		// 登录接口单独处理，跳过日志中间件
		if strings.Contains(ctx.Request.URL.Path, "/admin/login") ||
			strings.Contains(ctx.Request.URL.Path, "/admin/refresh-token") {
			// 登录和刷新token由控制器内部记录
			logs.Info("登录或刷新token接口，由控制器内部记录日志")
			return
		}

		// 检查是否应该跳过日志记录
		if shouldSkipLogging(ctx) {
			logs.Info("符合跳过日志记录的条件，不记录日志")
			return
		}

		// 获取管理员信息
		adminID, username := getAdminInfo(ctx)
		logs.Info("获取到管理员信息: ID=%d, 用户名=%s", adminID, username)

		if adminID <= 0 { // 未认证的请求不记录日志
			logs.Warn("未获取到有效的管理员ID，不记录日志")
			return
		}

		// 获取请求方法和路径，确定操作类型
		method := ctx.Request.Method
		path := ctx.Request.URL.Path
		operationType := getOperationType(method)
		targetType, targetID := getTargetInfo(path)
		module := getModule(path)

		logs.Info("操作类型: %s, 目标类型: %s, 目标ID: %d, 模块: %s",
			operationType, targetType, targetID, module)

		// 构建操作内容描述
		content := buildOperationContent(method, path, ctx.Input.RequestBody)

		// 准备日志数据
		logReq := &adminDto.AdminLogCreateRequest{
			AdminID:     adminID,
			Username:    username,
			Module:      module,
			Type:        operationType,
			Content:     content,
			RequestURL:  path,
			RequestData: string(ctx.Input.RequestBody),
			IP:          ctx.Input.IP(),
			UserAgent:   ctx.Input.UserAgent(),
			TargetID:    targetID,
			TargetType:  targetType,
			Status:      1, // 默认成功状态
			Remark:      "操作成功",
		}

		logs.Info("准备记录日志: %s", content)

		// 处理后续处理逻辑（注意：Beego中间件不使用ctx.Next()）

		// 记录原始响应状态，用于判断请求是否成功
		originalStatus := ctx.ResponseWriter.Status
		logs.Info("原始响应状态码: %d", originalStatus)

		// 请求完成后，根据响应状态更新日志状态
		if ctx.ResponseWriter.Status >= 400 {
			logReq.Status = 0 // 失败状态
			logReq.Remark = fmt.Sprintf("操作失败，状态码：%d", ctx.ResponseWriter.Status)
			logs.Warn("操作失败，状态码: %d", ctx.ResponseWriter.Status)
		}

		// 异步记录日志
		go func(req *adminDto.AdminLogCreateRequest) {
			logService := services.NewAdminLogService()
			result, err := logService.CreateLog(utils.CreateContext(), req)
			if err != nil {
				logs.Error("记录管理员操作日志失败: %v", err)
			} else {
				logs.Info("成功记录管理员操作日志，日志ID: %d", result)
			}
		}(logReq)

		logs.Info("====== 管理员日志中间件处理完成 ======")
	}
}

// 判断是否应该跳过日志记录
func shouldSkipLogging(ctx *context.Context) bool {
	// 静态资源请求不记录日志
	if strings.HasPrefix(ctx.Request.URL.Path, "/static/") {
		logs.Info("静态资源请求，跳过日志记录")
		return true
	}

	// 健康检查、OPTIONS请求等不记录日志
	if ctx.Request.Method == "OPTIONS" ||
		strings.Contains(ctx.Request.URL.Path, "/health") ||
		strings.Contains(ctx.Request.URL.Path, "/ping") {
		logs.Info("健康检查或OPTIONS请求，跳过日志记录")
		return true
	}

	// 其他需要跳过的路径
	skipPaths := []string{
		"/api/v1/admin/user/info", // 获取个人信息接口，频繁调用，可选择不记录
		"/api/v1/admin/logs",      // 日志查询接口，避免递归记录
	}

	for _, path := range skipPaths {
		if strings.Contains(ctx.Request.URL.Path, path) {
			logs.Info("匹配到跳过路径: %s，跳过日志记录", path)
			return true
		}
	}

	return false
}

// 从上下文获取管理员信息
func getAdminInfo(ctx *context.Context) (int64, string) {
	// 从头部获取管理员ID
	adminIDStr := ctx.Input.Header("X-Admin-ID")
	if adminIDStr == "" {
		logs.Warn("请求头中未包含X-Admin-ID")
		return 0, ""
	}

	logs.Info("从请求头获取到X-Admin-ID: %s", adminIDStr)

	// 直接解析请求头中的管理员ID，不依赖配置文件
	adminID := common.ParseInt64(adminIDStr, 0)

	// 从头部获取用户名
	username := ctx.Input.Header("X-Admin-Username")
	if username == "" {
		username = "unknown"
		logs.Warn("请求头中未包含X-Admin-Username，使用默认值: unknown")
	} else {
		logs.Info("从请求头获取到X-Admin-Username: %s", username)
	}

	return adminID, username
}

// 根据HTTP方法确定操作类型
func getOperationType(method string) string {
	switch method {
	case "GET":
		return models.AdminLogTypeView
	case "POST":
		return models.AdminLogTypeCreate
	case "PUT":
		return models.AdminLogTypeUpdate
	case "DELETE":
		return models.AdminLogTypeDelete
	default:
		return "other"
	}
}

// 从路径获取目标信息
func getTargetInfo(path string) (string, int64) {
	// 解析路径，尝试提取目标类型和ID
	parts := strings.Split(path, "/")
	logs.Info("路径解析: %v", parts)

	if len(parts) < 4 {
		logs.Warn("路径部分少于4个元素，无法准确确定目标类型和ID")
		return models.AdminLogTargetTypeSystem, 0
	}

	// 确定目标类型
	targetType := models.AdminLogTargetTypeSystem
	
	// 更精确地分析路径，优先匹配更具体的资源
	// 先检查路径中是否包含"merchants"
	if strings.Contains(path, "/merchants/") || strings.Contains(path, "/merchants") {
		targetType = models.AdminLogTargetTypeMerchant
	} else if strings.Contains(path, "/products/") || strings.Contains(path, "/products") {
		targetType = models.AdminLogTargetTypeProduct
	} else if strings.Contains(path, "/orders/") || strings.Contains(path, "/orders") {
		targetType = models.AdminLogTargetTypeOrder
	} else if strings.Contains(path, "/categories/") || strings.Contains(path, "/categories") {
		targetType = models.AdminLogTargetTypeCategory
	} else if strings.Contains(path, "/users/") || strings.Contains(path, "/users") {
		targetType = models.AdminLogTargetTypeUser
	} else if strings.Contains(path, "/roles/") || strings.Contains(path, "/roles") {
		targetType = models.AdminLogTargetTypeRole
	} else if strings.Contains(path, "/permissions/") || strings.Contains(path, "/permissions") {
		targetType = models.AdminLogTargetTypePermission
	} else if strings.Contains(path, "/menus/") || strings.Contains(path, "/menus") {
		targetType = models.AdminLogTargetTypeMenu
	} else if strings.Contains(path, "/admin/") {
		targetType = models.AdminLogTargetTypeAdmin
	} else {
		// 兼容旧格式的路径
		if strings.Contains(path, "/merchant/") {
			targetType = models.AdminLogTargetTypeMerchant
		} else if strings.Contains(path, "/product/") {
			targetType = models.AdminLogTargetTypeProduct
		} else if strings.Contains(path, "/order/") {
			targetType = models.AdminLogTargetTypeOrder
		} else if strings.Contains(path, "/category/") {
			targetType = models.AdminLogTargetTypeCategory
		} else if strings.Contains(path, "/user/") {
			targetType = models.AdminLogTargetTypeUser
		} else if strings.Contains(path, "/role/") {
			targetType = models.AdminLogTargetTypeRole
		} else if strings.Contains(path, "/admin/") {
			targetType = models.AdminLogTargetTypeAdmin
		}
	}

	logs.Info("确定目标类型: %s", targetType)

	// 尝试提取目标ID
	targetID := int64(0)
	for i, part := range parts {
		if i > 0 && common.IsNumeric(part) {
			targetID = common.ParseInt64(part, 0)
			logs.Info("找到目标ID: %d (从路径部分: %s)", targetID, part)
			break
		}
	}

	return targetType, targetID
}

// 从路径获取操作模块
func getModule(path string) string {
	// 确定操作模块
	module := "other"

	// 更精确地分析路径，优先匹配更具体的资源
	if strings.Contains(path, "/merchants/") || strings.Contains(path, "/merchants") {
		module = models.AdminLogModuleMerchant
	} else if strings.Contains(path, "/products/") || strings.Contains(path, "/products") {
		module = models.AdminLogModuleProduct
	} else if strings.Contains(path, "/orders/") || strings.Contains(path, "/orders") {
		module = models.AdminLogModuleOrder
	} else if strings.Contains(path, "/categories/") || strings.Contains(path, "/categories") {
		module = models.AdminLogModuleCategory
	} else if strings.Contains(path, "/users/") || strings.Contains(path, "/users") {
		module = models.AdminLogModuleUser
	} else if strings.Contains(path, "/roles/") || strings.Contains(path, "/roles") {
		module = models.AdminLogModuleRole
	} else if strings.Contains(path, "/permissions/") || strings.Contains(path, "/permissions") {
		module = models.AdminLogModulePermission
	} else if strings.Contains(path, "/menus/") || strings.Contains(path, "/menus") {
		module = models.AdminLogModuleMenu
	} else if strings.Contains(path, "/system/") {
		module = models.AdminLogModuleSystem
	} else if strings.Contains(path, "/admin/") {
		module = models.AdminLogModuleAdmin
	} else {
		// 兼容旧格式的路径
		if strings.Contains(path, "/merchant/") {
			module = models.AdminLogModuleMerchant
		} else if strings.Contains(path, "/product/") {
			module = models.AdminLogModuleProduct
		} else if strings.Contains(path, "/order/") {
			module = models.AdminLogModuleOrder
		} else if strings.Contains(path, "/category/") {
			module = models.AdminLogModuleCategory
		} else if strings.Contains(path, "/user/") {
			module = models.AdminLogModuleUser
		} else if strings.Contains(path, "/role/") {
			module = models.AdminLogModuleRole
		} else if strings.Contains(path, "/menu/") {
			module = models.AdminLogModuleMenu
		} else if strings.Contains(path, "/permission/") {
			module = models.AdminLogModulePermission
		} else if strings.Contains(path, "/admin/") {
			module = models.AdminLogModuleAdmin
		}
	}

	logs.Info("确定操作模块: %s", module)
	return module
}

// 构建操作内容描述
func buildOperationContent(method, path string, requestBody []byte) string {
	// 基本操作描述
	var operation string
	switch method {
	case "GET":
		operation = "查看"
	case "POST":
		operation = "创建"
	case "PUT":
		operation = "更新"
	case "DELETE":
		operation = "删除"
	default:
		operation = "操作"
	}

	// 确定操作对象
	var target string
	
	// 更精确地分析路径，优先匹配更具体的资源
	if strings.Contains(path, "/merchants/") || strings.Contains(path, "/merchants") {
		target = "商户"
	} else if strings.Contains(path, "/products/") || strings.Contains(path, "/products") {
		target = "产品"
	} else if strings.Contains(path, "/orders/") || strings.Contains(path, "/orders") {
		target = "订单"
	} else if strings.Contains(path, "/categories/") || strings.Contains(path, "/categories") {
		target = "分类"
	} else if strings.Contains(path, "/users/") || strings.Contains(path, "/users") {
		target = "用户"
	} else if strings.Contains(path, "/roles/") || strings.Contains(path, "/roles") {
		target = "角色"
	} else if strings.Contains(path, "/permissions/") || strings.Contains(path, "/permissions") {
		target = "权限"
	} else if strings.Contains(path, "/menus/") || strings.Contains(path, "/menus") {
		target = "菜单"
	} else if strings.Contains(path, "/admin/") {
		target = "管理员"
	} else {
		// 兼容旧格式的路径
		if strings.Contains(path, "/merchant/") {
			target = "商户"
		} else if strings.Contains(path, "/product/") {
			target = "产品"
		} else if strings.Contains(path, "/order/") {
			target = "订单"
		} else if strings.Contains(path, "/category/") {
			target = "分类"
		} else if strings.Contains(path, "/user/") {
			target = "用户"
		} else if strings.Contains(path, "/role/") {
			target = "角色"
		} else if strings.Contains(path, "/admin/") {
			target = "管理员"
		} else if strings.Contains(path, "/menu/") {
			target = "菜单"
		} else if strings.Contains(path, "/permission/") {
			target = "权限"
		} else if strings.Contains(path, "/system/") {
			target = "系统配置"
		} else {
			target = "系统"
		}
	}

	// 构建基本描述
	content := operation + target

	// 对于POST/PUT请求，尝试提取关键信息
	if (method == "POST" || method == "PUT") && len(requestBody) > 0 {
		var reqData map[string]interface{}
		if err := json.Unmarshal(requestBody, &reqData); err == nil {
			// 安全清理敏感信息
			sanitizeRequestData(reqData)

			// 添加请求参数摘要
			if jsonStr, err := json.Marshal(reqData); err == nil && len(jsonStr) < 200 {
				content += "，参数：" + string(jsonStr)
			}
		}
	}

	return content
}

// 清理请求数据中的敏感信息
func sanitizeRequestData(data map[string]interface{}) {
	// 需要屏蔽的敏感字段列表
	sensitiveFields := []string{
		"password", "old_password", "new_password", "confirm_password",
		"token", "refresh_token", "access_token", "secret", "key",
	}

	// 替换敏感信息
	for _, field := range sensitiveFields {
		if _, exists := data[field]; exists {
			data[field] = "******"
		}
	}
}
