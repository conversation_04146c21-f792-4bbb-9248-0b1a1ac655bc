/**
 * auth.go
 * 认证中间件
 *
 * 本文件实现了API接口的认证中间件，用于验证请求的合法性
 * 支持双token认证模式：access token用于接口认证，refresh token用于刷新access token
 */

package middlewares

import (
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"

	"o_mall_backend/common/result"
	"o_mall_backend/utils"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(ctx *context.Context) {
	// 跳过不需要认证的路径
	if isSkipAuth(ctx.Request.URL.Path) {
		return
	}

	// 获取Token
	token := utils.GetTokenFromRequest(ctx)
	if token == "" {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 检查token是否被撤销（在黑名单中）
	revoked, err := utils.IsTokenRevoked(token)
	if err != nil {
		logs.Error("检查token是否被撤销失败: %v", err)
	}
	if revoked {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 验证Token
	claims, err := utils.ParseToken(token)
	if err != nil {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 验证Token类型（只接受access token）
	if claims.Type != utils.TokenTypeAccess {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 设置用户信息到上下文
	utils.SetUserContext(ctx, map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
	})
}

// JWTMiddleware JWT认证中间件
func JWTMiddleware(ctx *context.Context) {
	// 跳过OPTIONS请求（CORS预检请求）
	if ctx.Input.Method() == "OPTIONS" {
		return
	}

	// 获取Token
	token := utils.GetTokenFromRequest(ctx)
	if token == "" {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 检查token是否被撤销（在黑名单中）
	revoked, err := utils.IsTokenRevoked(token)
	if err != nil {
		logs.Error("检查token是否被撤销失败: %v", err)
	}
	if revoked {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 验证Token
	claims, err := utils.ParseToken(token)
	if err != nil {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 验证Token类型（只接受access token）
	if claims.Type != utils.TokenTypeAccess {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 设置用户信息到上下文
	utils.SetUserContext(ctx, map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
	})

	// 将完整的 claims 数据设置到上下文中，供各种中间件使用
	ctx.Input.SetData("jwt_claims", map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
	})
}

// RefreshTokenMiddleware 刷新令牌中间件（专用于刷新access token的接口）
func RefreshTokenMiddleware(ctx *context.Context) {
	// 获取Token
	token := utils.GetTokenFromRequest(ctx)
	if token == "" {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 验证Token
	claims, err := utils.ParseToken(token)
	if err != nil {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 验证Token类型（只接受refresh token）
	if claims.Type != utils.TokenTypeRefresh {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 验证Token是否与存储中的一致
	storedToken, err := utils.GetRefreshToken(claims.UserID)
	if err != nil || token != storedToken {
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 设置用户信息到上下文
	utils.SetUserContext(ctx, map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
	})
}

// MerchantAuthMiddleware 商家认证中间件
func MerchantAuthMiddleware(ctx *context.Context) {
	// 获取角色
	role, ok := ctx.Input.GetData("role").(string)
	if !ok || role != "merchant" {
		result.HandleError(ctx, result.ErrForbidden)
		return
	}

	// 获取用户ID并设置为商家ID
	userID, ok := ctx.Input.GetData("user_id").(int64)
	if !ok {
		logs.Error("MerchantAuthMiddleware: 无法获取用户ID")
		result.HandleError(ctx, result.ErrUnauthorized)
		return
	}

	// 设置商家ID到上下文中
	ctx.Input.SetData("merchantID", userID)
}

// AdminAuthMiddleware 管理员认证中间件
func AdminAuthMiddleware(ctx *context.Context) {
	// 获取角色
	role, ok := ctx.Input.GetData("role").(string)
	if !ok || role != "super" {
		result.HandleError(ctx, result.ErrForbidden)
		return
	}
}

// isSkipAuth 判断是否跳过认证
func isSkipAuth(path string) bool {
	// 获取不需要认证的路径配置
	skipPaths, err := web.AppConfig.String("auth::skip_paths")
	if err != nil {
		return false
	}

	// 分割路径配置
	paths := strings.Split(skipPaths, ",")
	for _, p := range paths {
		if strings.TrimSpace(p) == path {
			return true
		}
	}

	return isExcludedPath(path)
}

// 判断是否为排除认证的路径
func isExcludedPath(path string) bool {
	// 不需要认证的路径列表
	excludedPaths := []string{
		"/api/v1/user/login",
		"/api/v1/user/register",
		"/api/v1/user/wx-login",
		// "/api/v1/user/register/send-code",
		// "/api/v1/user/register/verify-code",

		"/api/v1/user/refresh-token",          // 刷新token不需要认证中间件
		"/api/v1/user/send-verification-code", // 发送验证码不需要认证
		"/api/v1/user/login/verify-code",      // 验证码登录不需要认证
		"/api/v1/user/register/send-code",     // 发送注册验证码不需要认证
		"/api/v1/user/register/verify-code",   // 验证码注册不需要认证
		"/api/v1/products",                    // 商品列表可公开访问
		"/swagger",                            // Swagger文档
		"/api/v1/health",                      // 健康检查
		// 外卖模块公开API
		"/api/v1/user/takeout/categories",
		"/api/v1/user/takeout/categories/",
		"/api/v1/user/takeout/merchants/",
		"/api/v1/user/takeout/merchants/",
		"/api/v1/user/takeout/global-categories",
		"/api/v1/user/takeout/global-categories/tree",
		"/api/v1/user/takeout/foods/",
		"/api/v1/user/takeout/merchants/promotions-coupons",
		"/api/v1/merchant/upload", // 商户匿名上传，提交申请时用
	}

	// 检查前缀匹配
	for _, ep := range excludedPaths {
		if strings.HasPrefix(path, ep) {
			return true
		}
	}

	return false
}
