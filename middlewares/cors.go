/**
 * cors.go
 * 跨域资源共享(CORS)中间件
 *
 * 本文件实现了处理跨域请求的中间件，允许前端应用从不同源访问API
 */

package middlewares

import (
	"fmt"
	"os"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
)

// CORSMiddleware CORS跨域中间件
// 处理跨域资源共享，允许前端应用从不同的域名/端口访问API
func CORSMiddleware(ctx *context.Context) {
	// 直接输出到控制台
	fmt.Println("======= CORS中间件被调用 =======")
	fmt.Println("请求方法:", ctx.Input.Method())
	fmt.Println("请求URL:", ctx.Input.URL())
	fmt.Println("请求来源:", ctx.Input.Header("Origin"))

	// 直接写入文件，确认中间件被调用
	f, err := os.OpenFile("cors_middleware_test.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err == nil {
		defer f.Close()
		f.WriteString(fmt.Sprintf("CORS中间件被调用: %s %s\n", ctx.Input.Method(), ctx.Input.URL()))
	}

	// 添加请求信息日志
	logs.Info("[CORSMiddleware] 处理请求: %s %s，来源: %s",
		ctx.Input.Method(), ctx.Input.URL(), ctx.Input.Header("Origin"))

	// 获取配置的允许源
	origin := ctx.Input.Header("Origin")
	if origin == "" {
		logs.Info("[CORSMiddleware] 请求没有Origin头")
	}

	// 检查配置是否可以直接访问
	fmt.Println("尝试直接获取配置...")
	fmt.Printf("cors.allow_origins = %s\n", web.AppConfig.DefaultString("cors.allow_origins", "未找到"))

	// 修复 CORS 配置获取方式
	allowOrigins := web.AppConfig.DefaultString("cors.allow_origins", "*")
	// 去掉引号，如果有的话
	allowOrigins = strings.Trim(allowOrigins, "\"")
	fmt.Println("处理后的允许源:", allowOrigins)
	logs.Info("[CORSMiddleware] 配置的允许源: %s", allowOrigins)

	// 判断请求源是否允许
	allowAll := allowOrigins == "*"
	allowed := allowAll

	if !allowAll && origin != "" {
		// 如果不是允许所有源，检查当前源是否在允许列表中
		originList := strings.Split(allowOrigins, ",")
		for _, allowedOrigin := range originList {
			cleanOrigin := strings.TrimSpace(allowedOrigin)
			fmt.Printf("比较源: [%s] 和 [%s]\n", cleanOrigin, origin)
			if cleanOrigin == origin {
				allowed = true
				logs.Info("[CORSMiddleware] 源 %s 在允许列表中", origin)
				break
			}
		}
	}

	logs.Info("[CORSMiddleware] 是否允许源 %s 访问: %v", origin, allowed)

	// 设置CORS头
	// 允许的源
	if allowAll {
		ctx.Output.Header("Access-Control-Allow-Origin", "*")
		logs.Info("[CORSMiddleware] 设置 Access-Control-Allow-Origin: *")
	} else if origin != "" {
		ctx.Output.Header("Access-Control-Allow-Origin", origin)
		logs.Info("[CORSMiddleware] 设置 Access-Control-Allow-Origin: %s", origin)
	}

	// 允许的方法
	allowMethods := web.AppConfig.DefaultString("cors.allow_methods", "GET,POST,PUT,DELETE,OPTIONS,PATCH")
	allowMethods = strings.Trim(allowMethods, "\"")
	ctx.Output.Header("Access-Control-Allow-Methods", allowMethods)
	logs.Info("[CORSMiddleware] 设置 Access-Control-Allow-Methods: %s", allowMethods)

	// 允许的头部
	
	allowHeaders := web.AppConfig.DefaultString("cors.allow_headers", "Origin,Content-Type,Accept,Authorization,X-Requested-With,platform")
	allowHeaders = strings.Trim(allowHeaders, "\"")
	ctx.Output.Header("Access-Control-Allow-Headers", allowHeaders)
	logs.Info("[CORSMiddleware] 设置 Access-Control-Allow-Headers: %s", allowHeaders)

	// 允许携带凭证（cookies等）
	allowCredentials, _ := web.AppConfig.Bool("cors.allow_credentials")
	if allowCredentials {
		ctx.Output.Header("Access-Control-Allow-Credentials", "true")
		logs.Info("[CORSMiddleware] 设置 Access-Control-Allow-Credentials: true")
	}

	// 允许暴露的头部
	exposeHeaders := web.AppConfig.DefaultString("cors.expose_headers", "")
	exposeHeaders = strings.Trim(exposeHeaders, "\"")
	if exposeHeaders != "" {
		ctx.Output.Header("Access-Control-Expose-Headers", exposeHeaders)
		logs.Info("[CORSMiddleware] 设置 Access-Control-Expose-Headers: %s", exposeHeaders)
	}

	// 预检请求缓存时间
	maxAge := web.AppConfig.DefaultString("cors.max_age", "86400")
	maxAge = strings.Trim(maxAge, "\"")
	ctx.Output.Header("Access-Control-Max-Age", maxAge)
	logs.Info("[CORSMiddleware] 设置 Access-Control-Max-Age: %s", maxAge)

	// 如果是预检请求，直接返回200并终止后续处理
	if ctx.Input.Method() == "OPTIONS" {
		logs.Info("[CORSMiddleware] 处理 OPTIONS 预检请求，返回 200")
		// 设置状态码并写入响应
		ctx.Output.SetStatus(200)
		// 调用Abort()终止后续中间件和路由处理
		ctx.Abort(200, "")
		return
	}

	logs.Info("[CORSMiddleware] 完成 CORS 头设置，继续处理请求")
}
