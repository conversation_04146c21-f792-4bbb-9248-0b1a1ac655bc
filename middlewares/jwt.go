/**
 * JWT和角色过滤器
 *
 * 本文件实现了JWT认证和角色控制过滤器，用于请求认证和权限控制
 * JWT过滤器用于验证请求的token有效性
 * 角色过滤器用于检查用户是否具有特定角色
 * NoAuth过滤器用于排除某些路径的认证要求
 * 还提供了从上下文获取商家ID等实用函数
 */

package middlewares

import (
	"o_mall_backend/common/result"
	"o_mall_backend/utils"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"
)

// JWTFilter JWT验证过滤器
func JWTFilter(ctx *context.Context) {
	logs.Info("[JWT] JWTFilter 被调用，路径: %s, 方法: %s", ctx.Request.URL.Path, ctx.Input.Method())

	// 跳过OPTIONS请求（CORS预检请求）
	if ctx.Input.Method() == "OPTIONS" {
		logs.Info("[JWT] 跳过 OPTIONS 请求")
		return
	}

	// 获取Token
	token := utils.GetTokenFromRequest(ctx)
	if token == "" {
		result.HandleError(ctx, result.ErrUnauthorized)
		ctx.ResponseWriter.WriteHeader(401)
		return
	}

	// 检查token是否被撤销（在黑名单中）
	revoked, err := utils.IsTokenRevoked(token)
	if err != nil {
		logs.Error("检查token是否被撤销失败: %v", err)
	}
	if revoked {
		result.HandleError(ctx, result.ErrUnauthorized)
		ctx.ResponseWriter.WriteHeader(401)
		return
	}

	// 验证Token
	claims, err := utils.ParseToken(token)
	if err != nil {
		result.HandleError(ctx, result.ErrUnauthorized)
		ctx.ResponseWriter.WriteHeader(401)
		return
	}

	// 验证Token类型（只接受access token）
	if claims.Type != utils.TokenTypeAccess {
		result.HandleError(ctx, result.ErrUnauthorized)
		ctx.ResponseWriter.WriteHeader(401)
		return
	}

	// 设置用户信息到上下文
	utils.SetUserContext(ctx, map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
	})

	// 将用户ID直接设置到上下文中，兼容旧代码
	ctx.Input.SetData("userID", claims.UserID)

	// 将完整的 claims 数据设置到上下文中，供各种中间件使用
	ctx.Input.SetData("jwt_claims", map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
	})

	logs.Info("[JWT] JWT验证成功，用户ID: %d, 用户名: %s, 角色: %s", claims.UserID, claims.Username, claims.Role)

	// 设置管理员ID和用户名到请求头，供日志中间件使用
	ctx.Request.Header.Set("X-Admin-ID", strconv.FormatInt(claims.UserID, 10))
	ctx.Request.Header.Set("X-Admin-Username", claims.Username)
}

// NoAuthFilter 排除认证的过滤器
func NoAuthFilter(ctx *context.Context) {
	// 跳过验证，直接返回，允许请求继续处理
}

// RoleFilter 角色验证过滤器
// 检查用户是否具有指定的角色
func RoleFilter(requiredRole string) func(ctx *context.Context) {
	return func(ctx *context.Context) {
		// 从上下文获取用户角色
		role, ok := ctx.Input.GetData("role").(string)
		if !ok || role != requiredRole {
			result.HandleError(ctx, result.ErrForbidden)
			ctx.ResponseWriter.WriteHeader(403)
			return
		}
	}
}
