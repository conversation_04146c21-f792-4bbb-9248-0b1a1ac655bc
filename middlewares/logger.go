/**
 * 日志中间件
 *
 * 该文件提供HTTP请求日志记录中间件，用于记录所有API请求的详细信息，
 * 包括请求URL、方法、IP、耗时、状态码等，便于问题排查和性能分析。
 */

package middlewares

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"
)

// LoggerMiddleware 日志中间件
func LoggerMiddleware(ctx *context.Context) {
	// 记录开始时间
	startTime := time.Now()

	// 获取请求信息
	method := ctx.Request.Method
	path := ctx.Request.URL.Path
	ip := ctx.Input.IP()

	// 记录请求日志
	logs.Info("[Request] %s %s from %s", method, path, ip)

	// 获取请求体 (仅记录小于1MB的请求体)
	var requestBody string
	if ctx.Request.ContentLength > 0 && ctx.Request.ContentLength < 1024*1024 {
		if ctx.Request.Body != nil {
			bodyBytes, _ := ioutil.ReadAll(ctx.Request.Body)
			ctx.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))
			requestBody = string(bodyBytes)
		}
	}

	// 处理请求（这里不需要调用Next，因为beego会自动处理）
	// 在请求处理完成后记录响应信息
	defer func() {
		// 计算处理时间
		duration := time.Since(startTime)

		// 获取响应状态
		statusCode := ctx.ResponseWriter.Status

		// 记录请求日志
		logData := map[string]interface{}{
			"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
			"latency_ms":  duration.Milliseconds(),
			"client_ip":   ip,
			"method":      method,
			"path":        path,
			"status_code": statusCode,
			"request_id":  ctx.Input.Header("X-Request-ID"),
			"user_id":     ctx.Input.GetData("user_id"),
		}

		// 敏感信息过滤
		if len(requestBody) > 0 {
			var bodyMap map[string]interface{}
			if err := json.Unmarshal([]byte(requestBody), &bodyMap); err == nil {
				// 过滤敏感字段
				if _, exists := bodyMap["password"]; exists {
					bodyMap["password"] = "******"
				}
				if _, exists := bodyMap["card_number"]; exists {
					bodyMap["card_number"] = "******"
				}

				if filteredBody, err := json.Marshal(bodyMap); err == nil {
					logData["request_body"] = string(filteredBody)
				}
			} else {
				// 如果不是JSON格式，简单记录长度
				logData["request_body_length"] = len(requestBody)
			}
		}

		// 记录日志
		logJSON, _ := json.Marshal(logData)
		if statusCode >= 400 {
			logs.Warn("API请求: %s", string(logJSON))
		} else {
			logs.Info("API请求: %s", string(logJSON))
		}
	}()
}
