/**
 * merchant_log_middleware.go
 * 商家日志中间件
 *
 * 本文件实现了商家操作日志记录的中间件功能，自动记录商家和管理员对商家的所有操作。
 * 通过拦截请求和响应，收集操作信息并异步写入日志，确保系统安全审计和问题追踪。
 */

package middlewares

import (
	"encoding/json"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"

	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// MerchantLogMiddleware 商家日志中间件
// 自动记录商家操作日志
func MerchantLogMiddleware() func(ctx *context.Context) {
	return func(ctx *context.Context) {
		logs.Info("====== 商家日志中间件触发 ======")
		logs.Info("请求路径: %s, 方法: %s", ctx.Request.URL.Path, ctx.Request.Method)

		// 检查请求路径是否需要记录日志
		// 登录接口单独处理，跳过日志中间件
		if strings.Contains(ctx.Request.URL.Path, "/merchant/public/login") ||
			strings.Contains(ctx.Request.URL.Path, "/merchant/public/refresh-token") {
			// 登录和刷新token由控制器内部记录
			logs.Info("登录或刷新token接口，由控制器内部记录日志")
			return
		}

		// 检查是否应该跳过日志记录
		if shouldSkipMerchantLogging(ctx) {
			logs.Info("符合跳过日志记录的条件，不记录日志")
			return
		}

		// 判断操作人类型
		operatorType := getMerchantOperatorType(ctx)
		operatorID, merchantID := getMerchantOperatorInfo(ctx, operatorType)

		logs.Info("操作人类型: %d, 操作人ID: %d, 商家ID: %d", operatorType, operatorID, merchantID)

		if operatorID <= 0 || merchantID <= 0 { // 未认证的请求不记录日志
			logs.Warn("未获取到有效的操作人ID或商家ID，不记录日志")
			return
		}

		// 将商家ID设置到上下文中，供后续控制器使用
		ctx.Input.SetData("merchantID", merchantID)
		logs.Info("已将商家ID设置到上下文中: %d", merchantID)

		// 获取请求方法和路径，确定操作类型
		method := ctx.Request.Method
		path := ctx.Request.URL.Path
		operationType := getMerchantOperationType(method, path)
		content := buildMerchantOperationContent(method, path, operationType, ctx.Input.RequestBody)

		logs.Info("操作类型: %d, 操作内容: %s", operationType, content)

		// 准备日志数据
		logReq := &merchantDto.MerchantLogCreateRequest{
			MerchantID:    merchantID,
			OperatorID:    operatorID,
			OperatorType:  operatorType,
			OperationType: operationType,
			Content:       content,
			RequestURL:    path,
			RequestData:   string(ctx.Input.RequestBody),
			IP:            ctx.Input.IP(),
			UserAgent:     ctx.Input.UserAgent(),
		}

		logs.Info("准备记录商家日志: %s", content)

		// 异步记录日志
		go func(req *merchantDto.MerchantLogCreateRequest) {
			logService := services.NewMerchantLogService()
			result, err := logService.CreateLog(utils.CreateContext(), req)
			if err != nil {
				logs.Error("记录商家操作日志失败: %v", err)
			} else {
				logs.Info("成功记录商家操作日志，日志ID: %d", result)
			}
		}(logReq)

		logs.Info("====== 商家日志中间件处理完成 ======")
	}
}

// 判断是否应该跳过日志记录
func shouldSkipMerchantLogging(ctx *context.Context) bool {
	// 静态资源请求不记录日志
	if strings.HasPrefix(ctx.Request.URL.Path, "/static/") {
		logs.Info("静态资源请求，跳过日志记录")
		return true
	}

	// 健康检查、OPTIONS请求等不记录日志
	if ctx.Request.Method == "OPTIONS" ||
		strings.Contains(ctx.Request.URL.Path, "/health") ||
		strings.Contains(ctx.Request.URL.Path, "/ping") {
		logs.Info("健康检查或OPTIONS请求，跳过日志记录")
		return true
	}

	// 其他需要跳过的路径
	skipPaths := []string{
		"/api/v1/merchant/public", // 公开接口不记录
	}

	for _, path := range skipPaths {
		if strings.Contains(ctx.Request.URL.Path, path) {
			logs.Info("匹配到跳过路径: %s，跳过日志记录", path)
			return true
		}
	}

	return false
}

// 获取操作人类型：1-商家，2-管理员
func getMerchantOperatorType(ctx *context.Context) int {
	// 根据路径判断操作人类型
	if strings.Contains(ctx.Request.URL.Path, "/merchant/admin/") {
		return 2 // 管理员操作
	}
	return 1 // 商家操作
}

// 从上下文获取操作人信息
func getMerchantOperatorInfo(ctx *context.Context, operatorType int) (int64, int64) {
	// 操作人ID
	operatorID := int64(0)
	// 商家ID
	merchantID := int64(0)

	if operatorType == 1 { // 商家操作
		// 输出调试信息：获取Authorization头
		authHeader := ctx.Input.Header("Authorization")
		logs.Info("请求头中Authorization: %s", authHeader)

		// 获取已设置的上下文数据
		userID := ctx.Input.GetData("user_id")
		username := ctx.Input.GetData("username")
		role := ctx.Input.GetData("role")
		logs.Info("上下文中的数据: user_id=%v, username=%v, role=%v", userID, username, role)

		// 从 JWT 获取商家ID
		claims := ctx.Input.GetData("jwt_claims")
		logs.Info("获取到的jwt_claims: %+v", claims)
		if claims != nil {
			if m, ok := claims.(map[string]interface{}); ok {
				logs.Info("解析jwt_claims成功，内容: %+v", m)
				if id, exists := m["user_id"]; exists {
					logs.Info("找到user_id: %v, 类型: %T", id, id)
					// 处理不同类型的user_id
					switch v := id.(type) {
					case float64:
						operatorID = int64(v)
						merchantID = operatorID // 商家自己操作时，操作人ID等于商家ID
						logs.Info("成功从浮点数转换user_id为int64: %d", operatorID)
					case int64:
						operatorID = v
						merchantID = operatorID
						logs.Info("直接使用int64类型的user_id: %d", operatorID)
					case int:
						operatorID = int64(v)
						merchantID = operatorID
						logs.Info("从int转换user_id为int64: %d", operatorID)
					default:
						logs.Warn("user_id是不支持的类型: %T", id)
					}
				} else {
					logs.Warn("jwt_claims中没有user_id字段")
				}
			} else {
				logs.Warn("jwt_claims不是map[string]interface{}类型: %T", claims)
			}
		} else {
			logs.Warn("jwt_claims为空")

			// 尝试从头部获取ID，类似管理员中间件
			merchantIDStr := ctx.Input.Header("X-Merchant-ID")
			if merchantIDStr != "" {
				operatorID = common.ParseInt64(merchantIDStr, 0)
				merchantID = operatorID
				logs.Info("从头部获取到商家ID: %d", merchantID)
			}
		}
	} else if operatorType == 2 { // 管理员操作
		// 从路径获取商家ID
		parts := strings.Split(ctx.Request.URL.Path, "/")
		for i, part := range parts {
			if i > 0 && common.IsNumeric(part) {
				merchantID = common.ParseInt64(part, 0)
				break
			}
		}

		// 从请求头获取管理员ID
		adminIDStr := ctx.Input.Header("X-Admin-ID")
		if adminIDStr != "" {
			operatorID = common.ParseInt64(adminIDStr, 0)
		}
	}

	return operatorID, merchantID
}

// 根据HTTP方法和路径确定操作类型
func getMerchantOperationType(method, path string) int {
	// 开始营业
	if method == "PUT" && strings.Contains(path, "/operation-status-start") {
		return models.MerchantLogTypeOpenBusiness
	}

	// 结束营业
	if method == "PUT" && strings.Contains(path, "/operation-status-stop") {
		return models.MerchantLogTypeCloseBusiness
	}

	// 登录
	if strings.Contains(path, "/login") {
		return models.MerchantLogTypeLogin
	}

	// 修改密码
	if strings.Contains(path, "/password") {
		return models.MerchantLogTypePassword
	}

	// 审核
	if strings.Contains(path, "/audit") {
		return models.MerchantLogTypeAudit
	}

	// 余额变更
	if strings.Contains(path, "/balance") {
		return models.MerchantLogTypeBalance
	}

	// 根据HTTP方法判断
	switch method {
	case "PUT":
		return models.MerchantLogTypeUpdate
	case "POST", "DELETE":
		if strings.Contains(path, "/status") {
			return models.MerchantLogTypeStatusChange
		}
		return models.MerchantLogTypeUpdate
	default:
		return 0 // 其他操作
	}
}

// 构建操作内容描述
func buildMerchantOperationContent(method, path string, operationType int, requestBody []byte) string {
	// 基本描述
	var baseContent string

	switch operationType {
	case models.MerchantLogTypeLogin:
		baseContent = "商家登录"
	case models.MerchantLogTypeUpdate:
		baseContent = "更新商家信息"
	case models.MerchantLogTypePassword:
		baseContent = "修改密码"
	case models.MerchantLogTypeAudit:
		baseContent = "审核商家"
	case models.MerchantLogTypeBalance:
		baseContent = "修改商家余额"
	case models.MerchantLogTypeStatusChange:
		baseContent = "修改商家状态"
	default:
		if method == "GET" {
			baseContent = "查看商家信息"
		} else {
			baseContent = "操作商家"
		}
	}

	// 对于非GET请求，尝试提取关键信息
	content := baseContent
	if method != "GET" && len(requestBody) > 0 {
		var reqData map[string]interface{}
		if err := json.Unmarshal(requestBody, &reqData); err == nil {
			// 安全清理敏感信息
			sanitizeMerchantRequestData(reqData)

			// 添加请求参数摘要
			if jsonStr, err := json.Marshal(reqData); err == nil && len(jsonStr) < 200 {
				content += "，参数：" + string(jsonStr)
			}
		}
	}

	return content
}

// 清理请求数据中的敏感信息
func sanitizeMerchantRequestData(data map[string]interface{}) {
	// 需要屏蔽的敏感字段列表
	sensitiveFields := []string{
		"password", "old_password", "new_password", "confirm_password",
		"token", "refresh_token", "access_token", "secret", "key",
	}

	// 替换敏感信息
	for _, field := range sensitiveFields {
		if _, exists := data[field]; exists {
			data[field] = "******"
		}
	}
}
