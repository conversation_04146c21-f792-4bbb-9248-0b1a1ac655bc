/**
 * 性能监控中间件
 *
 * 本文件实现了API性能监控中间件，用于记录API响应时间、检测慢查询、
 * 统计API调用频率等性能指标，便于后续优化和问题排查。
 */

package middlewares

import (
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
)

// PerformanceConfig 性能监控配置
type PerformanceConfig struct {
	// 慢查询阈值（毫秒）
	SlowQueryThreshold int64
	// 是否启用详细日志
	EnableDetailLog bool
	// 是否启用统计
	EnableStats bool
	// 监控的路径前缀
	MonitorPaths []string
}

// 默认配置
var defaultConfig = &PerformanceConfig{
	SlowQueryThreshold: 500, // 500ms
	EnableDetailLog:    true,
	EnableStats:        true,
	MonitorPaths: []string{
		"/api/v1/orders/",
		"/api/v1/takeout/",
		"/api/v1/runner/",
		"/api/v1/delivery/",
	},
}

// 性能统计数据
type PerformanceStats struct {
	TotalRequests  int64   `json:"total_requests"`   // 总请求数
	SlowRequests   int64   `json:"slow_requests"`    // 慢请求数
	AverageTime    float64 `json:"average_time"`     // 平均响应时间(ms)
	MaxTime        int64   `json:"max_time"`         // 最大响应时间(ms)
	MinTime        int64   `json:"min_time"`         // 最小响应时间(ms)
	LastUpdateTime string  `json:"last_update_time"` // 最后更新时间
}

// 全局统计数据（简单内存存储，生产环境建议使用Redis）
var globalStats = make(map[string]*PerformanceStats)

// PerformanceMiddleware 性能监控中间件
func PerformanceMiddleware(config ...*PerformanceConfig) web.FilterFunc {
	cfg := defaultConfig
	if len(config) > 0 && config[0] != nil {
		cfg = config[0]
	}

	return func(ctx *context.Context) {
		// 检查是否需要监控此路径
		if !shouldMonitor(ctx.Request.URL.Path, cfg.MonitorPaths) {
			return
		}

		// 记录开始时间
		startTime := time.Now()

		// 获取请求信息
		method := ctx.Request.Method
		path := ctx.Request.URL.Path
		userAgent := ctx.Request.Header.Get("User-Agent")
		userID := getUserIDStringFromContext(ctx)

		// 记录请求开始日志
		if cfg.EnableDetailLog {
			logs.Info("[性能监控] 请求开始 - %s %s, 用户ID: %s, UserAgent: %s",
				method, path, userID, userAgent)
		}

		// 继续处理请求
		defer func() {
			// 计算响应时间
			duration := time.Since(startTime)
			durationMs := duration.Milliseconds()

			// 获取响应状态码
			statusCode := ctx.ResponseWriter.Status
			if statusCode == 0 {
				statusCode = 200 // 默认200
			}

			// 记录性能日志
			logLevel := "INFO"
			if durationMs > cfg.SlowQueryThreshold {
				logLevel = "WARN"
			}

			if cfg.EnableDetailLog {
				logs.Info("[性能监控] 请求完成 - %s %s, 响应时间: %dms, 状态码: %d, 用户ID: %s, 级别: %s",
					method, path, durationMs, statusCode, userID, logLevel)
			}

			// 慢查询告警
			if durationMs > cfg.SlowQueryThreshold {
				logs.Warn("[性能监控] 慢查询检测 - %s %s, 响应时间: %dms, 阈值: %dms, 用户ID: %s",
					method, path, durationMs, cfg.SlowQueryThreshold, userID)
			}

			// 更新统计数据
			if cfg.EnableStats {
				updateStats(method+" "+path, durationMs, durationMs > cfg.SlowQueryThreshold)
			}

			// 记录特殊情况
			if statusCode >= 400 {
				logs.Error("[性能监控] 错误响应 - %s %s, 状态码: %d, 响应时间: %dms, 用户ID: %s",
					method, path, statusCode, durationMs, userID)
			}
		}()
	}
}

// shouldMonitor 检查是否需要监控此路径
func shouldMonitor(path string, monitorPaths []string) bool {
	if len(monitorPaths) == 0 {
		return true // 如果没有配置，监控所有路径
	}

	for _, prefix := range monitorPaths {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}
	return false
}

// getUserIDStringFromContext 从上下文获取用户ID（字符串格式）
func getUserIDStringFromContext(ctx *context.Context) string {
	// 尝试从JWT上下文获取
	if userIDInterface := ctx.Input.GetData("userID"); userIDInterface != nil {
		if userID, ok := userIDInterface.(int64); ok {
			return strconv.FormatInt(userID, 10)
		}
	}

	// 尝试从Header获取
	if userID := ctx.Input.Header("User-ID"); userID != "" {
		return userID
	}

	// 尝试从Query参数获取
	if userID := ctx.Input.Query("user_id"); userID != "" {
		return userID
	}

	return "unknown"
}

// updateStats 更新统计数据
func updateStats(endpoint string, durationMs int64, isSlow bool) {
	stats, exists := globalStats[endpoint]
	if !exists {
		stats = &PerformanceStats{
			MinTime: durationMs,
			MaxTime: durationMs,
		}
		globalStats[endpoint] = stats
	}

	// 更新统计
	stats.TotalRequests++
	if isSlow {
		stats.SlowRequests++
	}

	// 更新最大最小时间
	if durationMs > stats.MaxTime {
		stats.MaxTime = durationMs
	}
	if durationMs < stats.MinTime {
		stats.MinTime = durationMs
	}

	// 计算平均时间（简单移动平均）
	stats.AverageTime = (stats.AverageTime*float64(stats.TotalRequests-1) + float64(durationMs)) / float64(stats.TotalRequests)

	// 更新时间
	stats.LastUpdateTime = time.Now().Format("2006-01-02 15:04:05")
}

// GetPerformanceStats 获取性能统计数据
func GetPerformanceStats() map[string]*PerformanceStats {
	return globalStats
}

// ResetPerformanceStats 重置性能统计数据
func ResetPerformanceStats() {
	globalStats = make(map[string]*PerformanceStats)
	logs.Info("[性能监控] 统计数据已重置")
}

// LogPerformanceStats 输出性能统计报告
func LogPerformanceStats() {
	if len(globalStats) == 0 {
		logs.Info("[性能监控] 暂无统计数据")
		return
	}

	logs.Info("[性能监控] ========== 性能统计报告 ==========")
	for endpoint, stats := range globalStats {
		slowRate := float64(stats.SlowRequests) / float64(stats.TotalRequests) * 100
		logs.Info("[性能监控] 接口: %s", endpoint)
		logs.Info("[性能监控]   总请求数: %d", stats.TotalRequests)
		logs.Info("[性能监控]   慢请求数: %d (%.2f%%)", stats.SlowRequests, slowRate)
		logs.Info("[性能监控]   平均响应时间: %.2fms", stats.AverageTime)
		logs.Info("[性能监控]   最大响应时间: %dms", stats.MaxTime)
		logs.Info("[性能监控]   最小响应时间: %dms", stats.MinTime)
		logs.Info("[性能监控]   最后更新: %s", stats.LastUpdateTime)
		logs.Info("[性能监控] ----------------------------------------")
	}
	logs.Info("[性能监控] ========== 报告结束 ==========")
}

// OrderListPerformanceMiddleware 订单列表专用性能监控中间件
func OrderListPerformanceMiddleware() web.FilterFunc {
	return PerformanceMiddleware(&PerformanceConfig{
		SlowQueryThreshold: 200, // 订单列表要求更严格，200ms
		EnableDetailLog:    true,
		EnableStats:        true,
		MonitorPaths: []string{
			"/api/v1/orders/list",
			"/api/v1/orders/simple/list",
			"/api/v1/orders/simple/high-performance",
		},
	})
}
