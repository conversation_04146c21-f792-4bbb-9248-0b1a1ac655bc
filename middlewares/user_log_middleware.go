/**
 * user_log_middleware.go
 * 用户日志中间件
 *
 * 本文件实现了用户敏感操作日志记录的中间件功能，自动记录用户的敏感操作。
 * 与管理员日志不同，用户日志只记录敏感操作（如密码修改、登录等），而不是所有操作。
 */

package middlewares

import (
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"

	userDto "o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/services"
	"o_mall_backend/utils"
)

// 需要记录日志的敏感操作路径
var sensitiveUserPaths = []string{
	"/api/v1/user/login",                  // 登录
	"/api/v1/user/register",               // 注册
	"/api/v1/user/secured/logout",         // 登出
	"/api/v1/user/secured/password",       // 密码修改
	"/api/v1/user/secured/info",           // 用户信息修改
	"/api/v1/user/secured/addresses/", // 地址相关操作
}

// UserLogMiddleware 用户日志中间件
// 自动记录用户敏感操作日志
func UserLogMiddleware() func(ctx *context.Context) {
	return func(ctx *context.Context) {
		path := ctx.Request.URL.Path
		method := ctx.Request.Method

		// 检查是否是需要记录的敏感操作
		if !isUserSensitiveOperation(path, method) {
			return
		}

		logs.Info("====== 用户日志中间件触发 ======")
		logs.Info("请求路径: %s, 方法: %s", path, method)

		// 对特殊路径的处理（如登录、注册）
		if path == "/api/v1/user/login" || path == "/api/v1/user/register" {
			// 登录、注册由控制器内部记录日志，这里跳过
			logs.Info("登录或注册接口，由控制器内部记录日志")
			return
		}

		// 获取用户ID
		userID := getUserIDFromContext(ctx)
		if userID <= 0 {
			logs.Warn("未获取到有效的用户ID，不记录日志")
			return
		}

		// 获取用户名
		username := getUsernameFromContext(ctx)

		// 确定操作类型和内容
		operationType, content := getUserOperationInfo(path, method)
		logs.Info("操作类型: %d, 内容: %s", operationType, content)

		// 准备日志数据
		logReq := &userDto.UserLogCreateRequest{
			UserID:       userID,
			Username:     username,
			OperationType: operationType,
			Content:      content,
			RequestURL:   path,
			RequestData:  string(ctx.Input.RequestBody),
			IP:           ctx.Input.IP(),
			UserAgent:    ctx.Input.UserAgent(),
			Status:       1, // 默认成功状态
			Remark:       "操作成功",
		}

		// 异步记录日志
		go func(req *userDto.UserLogCreateRequest) {
			logService := services.NewUserLogService()
			result, err := logService.CreateLog(utils.CreateContext(), req)
			if err != nil {
				logs.Error("记录用户操作日志失败: %v", err)
			} else {
				logs.Info("成功记录用户操作日志，日志ID: %d", result)
			}
		}(logReq)

		logs.Info("====== 用户日志中间件处理完成 ======")
	}
}

// isUserSensitiveOperation 判断是否是需要记录日志的敏感操作
func isUserSensitiveOperation(path, method string) bool {
	// 如果是GET请求，一般不是敏感操作（除了登出）
	if method == "GET" && !strings.Contains(path, "/logout") {
		return false
	}

	// 检查是否匹配敏感路径
	for _, sensitivePath := range sensitiveUserPaths {
		if strings.HasPrefix(path, sensitivePath) {
			// 特殊处理地址操作，只记录设置默认地址
			if strings.Contains(path, "/addresses/") {
				return strings.Contains(path, "/default") || method == "DELETE"
			}
			return true
		}
	}
	
	return false
}

// getUserIDFromContext 从上下文获取用户ID
func getUserIDFromContext(ctx *context.Context) int64 {
	// 从JWT获取用户ID
	claims := ctx.Input.GetData("jwt_claims")
	if claims != nil {
		if m, ok := claims.(map[string]interface{}); ok {
			if id, exists := m["user_id"]; exists {
				if idFloat, ok := id.(float64); ok {
					return int64(idFloat)
				}
			}
		}
	}
	return 0
}

// getUsernameFromContext 从上下文获取用户名
func getUsernameFromContext(ctx *context.Context) string {
	// 从JWT获取用户名
	claims := ctx.Input.GetData("jwt_claims")
	if claims != nil {
		if m, ok := claims.(map[string]interface{}); ok {
			if username, exists := m["username"]; exists {
				if usernameStr, ok := username.(string); ok {
					return usernameStr
				}
			}
		}
	}
	return "unknown"
}

// getUserOperationInfo 获取用户操作类型和内容
func getUserOperationInfo(path, method string) (int, string) {
	// 根据路径和方法确定操作类型和内容
	
	// 登出
	if strings.Contains(path, "/logout") {
		return models.UserLogTypeLogout, "用户登出"
	}
	
	// 密码修改
	if strings.Contains(path, "/password") {
		return models.UserLogTypeChangePassword, "修改密码"
	}
	
	// 用户信息修改
	if strings.Contains(path, "/info") && method == "PUT" {
		return models.UserLogTypeUpdateInfo, "更新个人信息"
	}
	
	// 设置默认地址
	if strings.Contains(path, "/addresses/") && strings.Contains(path, "/default") {
		return models.UserLogTypeSetDefault, "设置默认地址"
	}
	
	// 删除地址
	if strings.Contains(path, "/addresses/") && method == "DELETE" {
		return models.UserLogTypeUpdateInfo, "删除收货地址"
	}
	
	// 默认返回更新信息类型
	return models.UserLogTypeUpdateInfo, "更新信息"
}
