/**
 * 管理员控制器
 *
 * 该文件实现了管理员相关的API接口控制器，处理管理员登录、创建、管理等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"fmt"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/dto"
	adminDto "o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/models"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// AdminController 管理员控制器
type AdminController struct {
	web.Controller
	adminService services.AdminService
	logService   services.AdminLogService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *AdminController) Prepare() {
	c.adminService = services.NewAdminService()
	c.logService = services.NewAdminLogService()
	// 解析 multipart/form-data 格式的请求
	/*
		err := c.Ctx.Request.ParseMultipartForm(32 << 20) // 限制最大内存为 32MB
		if err != nil {
			logs.Error("ParseMultipartForm error: %v", err)
			utils.ResponseError("Failed to parse multipart form", 500)
			c.StopRun()
			return
		}
	*/
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *AdminController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// Login 管理员登录
// @Title 管理员登录
// @Description 管理员账号登录，获取认证Token
// @Param	body	body	adminDto.AdminLoginRequest	true	"登录信息"
// @Success 200 {object} dto.Response 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /login [post]
func (c *AdminController) Login() {
	// 解析请求体
	var req adminDto.AdminLoginRequest

	// 使用通用请求参数解析方法
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	logs.Info(&req)
	logs.Info(c.Ctx.Request)

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 获取登录IP
	loginIP := c.Ctx.Input.IP()

	// 调用服务层处理登录逻辑
	resp, err := c.adminService.Login(c.Ctx.Request.Context(), &req, loginIP)

	// 记录登录日志
	var logStatus int = 1 // 成功状态
	var logRemark string = "登录成功"

	if err != nil {
		// 登录失败时的日志状态
		logStatus = 0
		logRemark = "登录失败：" + err.Error()
	}

	// 创建日志请求
	logReq := &adminDto.AdminLogCreateRequest{
		AdminID:    0, // 登录失败时可能没有AdminID
		Username:   req.Username,
		Module:     models.AdminLogModuleAdmin,
		Type:       models.AdminLogTypeLogin,
		Content:    "管理员登录系统",
		IP:         loginIP,
		UserAgent:  c.Ctx.Input.UserAgent(),
		TargetID:   0,
		TargetType: models.AdminLogTargetTypeSystem,
		Status:     logStatus,
		Remark:     logRemark,
	}

	// 如果登录成功，更新AdminID
	if err == nil && resp != nil {
		logReq.AdminID = resp.Admin.ID
	}

	// 异步记录日志
	go func() {
		_, logErr := c.logService.CreateLog(utils.CreateContext(), logReq)
		if logErr != nil {
			logs.Error("记录管理员登录日志失败: %v", logErr)
		}
	}()

	// 检查登录结果
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		// 对于登录错误，统一返回401状态码
		if appErr.Code == dto.CodeUnknownError {
			appErr.Code = dto.CodeUnauthorized
		}
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(resp)
	c.ServeJSON()
}

// RefreshToken 刷新访问令牌
// @Title 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Param	body	body	adminDto.RefreshTokenRequest	true	"刷新令牌请求"
// @Success 200 {object} dto.Response 成功返回新的令牌信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /refresh-token [post]
func (c *AdminController) RefreshToken() {
	// 解析请求体
	var req adminDto.RefreshTokenRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 调用刷新token接口
	tokenPair, err := utils.RefreshUserTokens(req.RefreshToken)
	if err != nil {
		var code int
		switch err {
		case utils.ErrTokenExpired:
			code = dto.CodeUnauthorized
		case utils.ErrTokenInvalid, utils.ErrInvalidTokenType:
			code = dto.CodeUnauthorized
		case utils.ErrTokenNotFound:
			code = dto.CodeUnauthorized
		default:
			code = dto.CodeInternalError
		}
		c.Data["json"] = dto.NewErrorResponse(code, err.Error())
		c.ServeJSON()
		return
	}

	// 创建响应
	resp := &adminDto.TokenResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
		TokenType:    "Bearer",
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(resp)
	c.ServeJSON()
}

// Logout 管理员退出登录
// @Title 管理员退出登录
// @Description 管理员退出登录，撤销已颁发的令牌
// @Success 200 {object} dto.Response 成功退出登录
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /logout [post]
func (c *AdminController) Logout() {
	// 从请求中获取令牌
	token := utils.GetTokenFromRequest(c.Ctx)
	if token == "" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未提供认证令牌")
		c.ServeJSON()
		return
	}

	// 解析token，获取用户ID
	claims, err := utils.ParseToken(token)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的认证令牌")
		c.ServeJSON()
		return
	}

	// 记录退出日志
	loginIP := c.Ctx.Input.IP()
	logReq := &adminDto.AdminLogCreateRequest{
		AdminID:    claims.UserID,
		Username:   claims.Username,
		Module:     models.AdminLogModuleAdmin,
		Type:       models.AdminLogTypeLogout,
		Content:    "管理员退出系统",
		IP:         loginIP,
		UserAgent:  c.Ctx.Input.UserAgent(),
		TargetID:   0,
		TargetType: models.AdminLogTargetTypeSystem,
		Status:     1, // 成功状态
		Remark:     "退出成功",
	}

	// 异步记录日志
	go func() {
		_, logErr := c.logService.CreateLog(utils.CreateContext(), logReq)
		if logErr != nil {
			logs.Error("记录管理员退出日志失败: %v", logErr)
		}
	}()

	// 撤销所有token
	err = utils.RevokeAllUserTokens(claims.UserID, token)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "登出失败："+err.Error())
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(nil)
	c.ServeJSON()
}

// GetCurrentAdmin 获取当前管理员信息
// @Title 获取当前管理员信息
// @Description 从token中解析用户ID并获取当前登录管理员的详细信息
// @Success 200 {object} dto.Response 成功返回管理员信息
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /current [get]
func (c *AdminController) GetCurrentAdmin() {
	// 从请求中获取token
	token := utils.GetTokenFromRequest(c.Ctx)
	if token == "" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未提供认证令牌")
		c.ServeJSON()
		return
	}

	// 解析token，获取管理员ID
	claims, err := utils.ParseToken(token)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的认证令牌")
		c.ServeJSON()
		return
	}

	// 调用服务层获取管理员信息
	adminInfo, err := c.adminService.GetAdminByID(c.Ctx.Request.Context(), claims.UserID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(adminInfo)
	c.ServeJSON()
}

// GetAdminInfo 获取管理员信息
// @Title 获取管理员信息
// @Description 获取当前登录管理员的详细信息
// @Success 200 {object} dto.Response 成功返回管理员信息
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /info [get]
func (c *AdminController) GetAdminInfo() {
	// 从上下文获取当前管理员ID
	adminID, ok := c.Ctx.Input.GetData("adminID").(int64)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 调用服务层获取管理员信息
	adminInfo, err := c.adminService.GetAdminByID(c.Ctx.Request.Context(), adminID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(adminInfo)
	c.ServeJSON()
}

// CreateAdmin 创建管理员
// @Title 创建管理员
// @Description 创建新的管理员账号
// @Param	body	body	adminDto.CreateAdminRequest	true	"管理员信息"
// @Success 200 {object} dto.Response 成功返回管理员ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [post]
func (c *AdminController) CreateAdmin() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 从jwt_claims中获取角色
	role, ok := claimsData["role"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户角色信息")
		c.ServeJSON()
		return
	}

	// 获取当前管理员ID
	adminID, ok := claimsData["user_id"].(int64)
	if !ok {
		// 如果user_id是float64类型（JSON数字默认类型），则进行转换
		if id, ok := claimsData["user_id"].(float64); ok {
			adminID = int64(id)
		} else {
			c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户ID信息")
			c.ServeJSON()
			return
		}
	}

	// 获取当前管理员用户名
	username, ok := claimsData["username"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户名信息")
		c.ServeJSON()
		return
	}

	// 检查权限（只有超级管理员可以创建账号）
	if role != "super" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "无权限执行此操作，需要超级管理员权限")
		c.ServeJSON()
		return
	}

	// 解析请求体
	var req adminDto.CreateAdminRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 调用服务层创建管理员
	newAdminID, err := c.adminService.CreateAdmin(c.Ctx.Request.Context(), &req)

	// 记录管理员创建日志
	logStatus := 1 // 成功状态
	logRemark := "创建成功"

	if err != nil {
		logStatus = 0
		logRemark = "创建失败：" + err.Error()
	}

	// 异步记录操作日志
	go func() {
		content := "创建管理员，用户名：" + req.Username + "，角色：" + req.Role
		logReq := &adminDto.AdminLogCreateRequest{
			AdminID:    adminID,
			Username:   username,
			Module:     models.AdminLogModuleAdmin,
			Type:       models.AdminLogTypeCreate,
			Content:    content,
			IP:         c.Ctx.Input.IP(),
			UserAgent:  c.Ctx.Input.UserAgent(),
			TargetID:   newAdminID, // 如果创建失败，这里可能是0
			TargetType: models.AdminLogTargetTypeAdmin,
			Status:     logStatus,
			Remark:     logRemark,
		}

		_, logErr := c.logService.CreateLog(utils.CreateContext(), logReq)
		if logErr != nil {
			logs.Error("记录创建管理员日志失败: %v", logErr)
		}
	}()

	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = appErr
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, map[string]interface{}{
		"id": newAdminID,
	})
}

// CountAdmins 获取已启用的管理员数量
// @Title 获取已启用的管理员数量
// @Description 获取数据库中已启用的管理员数量
// @Success 200 {object} dto.Response 成功返回已启用的管理员数量
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /count [get]
func (c *AdminController) CountAdmins() {
	// 调用服务层获取已启用的管理员数量
	count, err := c.adminService.CountAdmins(c.Ctx.Request.Context())
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, count)
}

// UpdateAdmin 更新管理员信息
// @Title 更新管理员信息
// @Description 更新管理员的基本信息
// @Param	id	path	int	true	"管理员ID"
// @Param	body	body	adminDto.UpdateAdminRequest	true	"管理员信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /admin/:id [put]
func (c *AdminController) UpdateAdmin() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 从jwt_claims中获取角色
	role, ok := claimsData["role"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户角色信息")
		c.ServeJSON()
		return
	}

	// 获取当前管理员ID
	adminID, ok := claimsData["user_id"].(int64)
	if !ok {
		// 如果user_id是float64类型（JSON数字默认类型），则进行转换
		if id, ok := claimsData["user_id"].(float64); ok {
			adminID = int64(id)
		} else {
			c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户ID信息")
			c.ServeJSON()
			return
		}
	}

	// 从URL路径中获取要更新的管理员ID
	idStr := c.Ctx.Input.Param(":id")
	if idStr == "" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "ID不能为空")
		c.ServeJSON()
		return
	}

	// 将字符串ID转换为int64
	updateID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "ID格式错误")
		c.ServeJSON()
		return
	}

	// 解析请求体
	var req adminDto.UpdateAdminRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数: "+err.Error())
		c.ServeJSON()
		return
	}

	// 设置要更新的管理员ID
	req.ID = updateID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败: "+err.Error())
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 检查权限
	// 普通管理员只能修改自己的信息，且不能修改角色和权限
	// 超级管理员可以修改任何管理员的信息
	if role != "super" && (req.ID != adminID || req.Role != "") {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "无权限执行此操作，需要超级管理员权限")
		c.ServeJSON()
		return
	}

	// 调用服务层更新管理员信息
	err = c.adminService.UpdateAdmin(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateSelfInfo 更新当前管理员的个人信息
// @Title 更新当前管理员的个人信息
// @Description 当前登录管理员在个人中心中修改自己的信息
// @Param	body	body	adminDto.UpdateAdminRequest	true	"管理员信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /self [put]
func (c *AdminController) UpdateSelfInfo() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 获取当前管理员ID
	adminID, ok := claimsData["user_id"].(int64)
	if !ok {
		// 如果user_id是float64类型（JSON数字默认类型），则进行转换
		if id, ok := claimsData["user_id"].(float64); ok {
			adminID = int64(id)
		} else {
			c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户ID信息")
			c.ServeJSON()
			return
		}
	}
	// 解析请求体
	var req adminDto.UpdateAdminRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 设置管理员ID，确保只能修改自己的信息
	req.ID = adminID

	logs.Info("更新管理员信息: %v", req)
	// 调用服务层更新管理员信息
	err = c.adminService.UpdateAdmin(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateAvatar 更新当前管理员的头像
// @Title 更新当前管理员的头像
// @Description 当前登录管理员更新自己的头像
// @Param	body	body	adminDto.UpdateAvatarRequest	true	"头像信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /avatar [put]
func (c *AdminController) UpdateAvatar() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 获取当前管理员ID
	adminID, ok := claimsData["user_id"].(int64)
	if !ok {
		// 如果user_id是float64类型（JSON数字默认类型），则进行转换
		if id, ok := claimsData["user_id"].(float64); ok {
			adminID = int64(id)
		} else {
			c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户ID信息")
			c.ServeJSON()
			return
		}
	}

	// 获取用户名
	username, ok := claimsData["username"].(string)
	if !ok || username == "" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户名信息")
		c.ServeJSON()
		return
	}

	// 解析请求体
	var req adminDto.UpdateAvatarRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 创建更新管理员请求
	updateReq := &adminDto.UpdateAdminRequest{
		ID:     adminID,
		Avatar: req.AvatarUrl,
	}

	// 调用服务层更新管理员头像
	if err := c.adminService.UpdateAdmin(c.Ctx.Request.Context(), updateReq); err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 异步记录头像更新日志
	go func() {
		logReq := &adminDto.AdminLogCreateRequest{
			AdminID:    adminID,
			Username:   username,
			Module:     models.AdminLogModuleAdmin,
			Type:       "update_avatar",
			Content:    "管理员更新头像",
			IP:         c.Ctx.Input.IP(),
			UserAgent:  c.Ctx.Input.UserAgent(),
			TargetID:   adminID,
			TargetType: models.AdminLogTargetTypeAdmin,
			Status:     1, // 成功状态
			Remark:     "头像更新成功",
		}

		_, logErr := c.logService.CreateLog(utils.CreateContext(), logReq)
		if logErr != nil {
			logs.Error("记录头像更新日志失败: %v", logErr)
		}
	}()

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// ChangePassword 修改密码
// @Title 修改密码
// @Description 修改当前登录管理员的密码
// @Param	body	body	adminDto.ChangePasswordRequest	true	"密码信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /password [put]
func (c *AdminController) ChangePassword() {
	// 从上下文获取当前管理员ID
	adminID, ok := c.Ctx.Input.GetData("adminID").(int64)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 解析请求体
	var req adminDto.ChangePasswordRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 调用服务层修改密码
	err = c.adminService.ChangePassword(c.Ctx.Request.Context(), adminID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(nil)
	c.ServeJSON()
}

// ResetPassword 重置密码
// @Title 重置密码
// @Description 重置指定管理员的密码
// @Param	body	body	adminDto.ResetPasswordRequest	true	"密码信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /password/reset [put]
func (c *AdminController) ResetPassword() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 从jwt_claims中获取角色
	role, ok := claimsData["role"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户角色信息")
		c.ServeJSON()
		return
	}

	// 获取当前管理员ID
	adminID, ok := claimsData["user_id"].(int64)
	if !ok {
		// 如果user_id是float64类型（JSON数字默认类型），则进行转换
		if id, ok := claimsData["user_id"].(float64); ok {
			adminID = int64(id)
		} else {
			c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户ID信息")
			c.ServeJSON()
			return
		}
	}

	// 获取当前管理员用户名
	username, ok := claimsData["username"].(string)
	if !ok || username == "" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户名信息")
		c.ServeJSON()
		return
	}

	// 检查权限（只有超级管理员可以重置密码）
	if role != "super" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "无权限执行此操作，需要超级管理员权限")
		c.ServeJSON()
		return
	}

	// 解析请求体
	var req adminDto.ResetPasswordRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 调用服务层重置密码
	err = c.adminService.ResetPassword(c.Ctx.Request.Context(), &req)

	// 记录重置密码日志
	logStatus := 1 // 成功状态
	logRemark := "重置密码成功"

	if err != nil {
		logStatus = 0
		logRemark = "重置密码失败：" + err.Error()
	}

	// 异步记录操作日志
	go func() {
		content := "重置管理员密码，管理员ID：" + fmt.Sprintf("%d", req.ID)
		logReq := &adminDto.AdminLogCreateRequest{
			AdminID:    adminID,
			Username:   username,
			Module:     models.AdminLogModuleAdmin,
			Type:       "reset_pwd", // 使用重置密码专用类型
			Content:    content,
			IP:         c.Ctx.Input.IP(),
			UserAgent:  c.Ctx.Input.UserAgent(),
			TargetID:   req.ID,
			TargetType: models.AdminLogTargetTypeAdmin,
			Status:     logStatus,
			Remark:     logRemark,
		}

		_, logErr := c.logService.CreateLog(utils.CreateContext(), logReq)
		if logErr != nil {
			logs.Error("记录重置密码日志失败: %v", logErr)
		}
	}()

	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// ListAdmins 获取管理员列表
// @Title 获取管理员列表
// @Description 分页获取管理员列表
// @Param	page	query	int	false	"页码"
// @Param	pageSize	query	int	false	"每页数量"
// @Param	username	query	string	false	"用户名搜索"
// @Param	nickname	query	string	false	"昵称搜索"
// @Param	role	query	string	false	"角色筛选"
// @Param	status	query	int	false	"状态筛选"
// @Success 200 {object} dto.Response 成功返回管理员列表和总数
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [get]
func (c *AdminController) ListAdmins() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 从jwt_claims中获取角色
	role, ok := claimsData["role"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户角色信息")
		c.ServeJSON()
		return
	}

	// 检查权限（只有超级管理员可以查看管理员列表）
	if role != "super" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "无权限执行此操作，需要超级管理员权限")
		c.ServeJSON()
		return
	}

	// 获取查询参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)
	username := c.GetString("username", "")
	nickname := c.GetString("nickname", "")
	roleFilter := c.GetString("role", "")
	status, _ := c.GetInt("status", -1)

	// 创建查询请求
	req := &adminDto.AdminQueryRequest{
		Page:     page,
		PageSize: pageSize,
		Username: username,
		Nickname: nickname,
		Role:     roleFilter,
		Status:   status,
	}

	// 调用服务层获取管理员列表
	admins, total, err := c.adminService.ListAdmins(c.Ctx.Request.Context(), req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OKWithPagination(c.Ctx, admins, total, req.Page, req.PageSize)
}

// DisableAdmin 禁用管理员
// @Title 禁用管理员
// @Description 禁用指定管理员账号
// @Param	id	path	int	true	"管理员ID"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id/disable [put]
func (c *AdminController) DisableAdmin() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 从jwt_claims中获取角色
	role, ok := claimsData["role"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户角色信息")
		c.ServeJSON()
		return
	}

	// 检查权限（只有超级管理员可以禁用管理员）
	if role != "super" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "无权限执行此操作，需要超级管理员权限")
		c.ServeJSON()
		return
	}

	// 获取管理员ID
	id, err := c.GetInt64(":id")
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的管理员ID")
		c.ServeJSON()
		return
	}

	// 调用服务层禁用管理员
	err = c.adminService.DisableAdmin(c.Ctx.Request.Context(), id)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(nil)
	c.ServeJSON()
}

// EnableAdmin 启用管理员
// @Title 启用管理员
// @Description 启用指定管理员账号
// @Param	id	path	int	true	"管理员ID"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id/enable [put]
func (c *AdminController) EnableAdmin() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 从jwt_claims中获取角色
	role, ok := claimsData["role"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户角色信息")
		c.ServeJSON()
		return
	}

	// 检查权限（只有超级管理员可以启用管理员）
	if role != "super" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "无权限执行此操作，需要超级管理员权限")
		c.ServeJSON()
		return
	}

	// 获取管理员ID
	id, err := c.GetInt64(":id")
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的管理员ID")
		c.ServeJSON()
		return
	}

	// 调用服务层启用管理员
	err = c.adminService.EnableAdmin(c.Ctx.Request.Context(), id)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteAdmin 删除管理员
// @Title 删除管理员
// @Description 删除指定管理员账号
// @Param	id	path	int	true	"管理员ID"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [delete]
func (c *AdminController) DeleteAdmin() {
	// 从上下文中获取jwt_claims
	claimsData, ok := c.Ctx.Input.GetData("jwt_claims").(map[string]interface{})
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 从jwt_claims中获取角色
	role, ok := claimsData["role"].(string)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "无效的用户角色信息")
		c.ServeJSON()
		return
	}

	// 检查权限（只有超级管理员可以删除管理员）
	if role != "super" {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "无权限执行此操作，需要超级管理员权限")
		c.ServeJSON()
		return
	}

	// 获取管理员ID
	id, err := c.GetInt64(":id")
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的管理员ID")
		c.ServeJSON()
		return
	}

	// 调用服务层删除管理员
	err = c.adminService.DeleteAdmin(c.Ctx.Request.Context(), id)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// InitAdmin 初始化管理员
// @Title 初始化管理员
// @Description 在系统中没有管理员的情况下创建初始管理员账号
// @Param	body	body	adminDto.CreateAdminRequest	true	"初始管理员信息"
// @Success 200 {object} dto.Response 成功返回管理员ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 409 {object} dto.Response 管理员已存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /initadmin [post]
func (c *AdminController) InitAdmin() {
	// 解析请求体
	var req adminDto.CreateAdminRequest
	if err := c.ParseRequest(&req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 检查是否已存在管理员
	hasAdmin, err := c.adminService.CheckAdminExists(c.Ctx.Request.Context())
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 如果已存在管理员，返回错误
	if hasAdmin > 0 {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeConflict, "管理员已存在，无法初始化")
		c.ServeJSON()
		return
	}

	// 设置为超级管理员角色
	req.Role = "super"

	// 调用服务层创建管理员
	adminID, err := c.adminService.CreateAdmin(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, adminID)
}
