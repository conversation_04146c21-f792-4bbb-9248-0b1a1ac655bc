/**
 * 管理员日志控制器
 *
 * 该文件实现了管理员日志相关的API接口，提供日志查询、统计等功能。
 * 日志记录是系统审计和安全追踪的重要组成部分。
 */

package controllers

import (
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/dto"
	adminDto "o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils"
)

// AdminLogController 管理员日志控制器
type AdminLogController struct {
	web.Controller
	adminLogService services.AdminLogService
}

// Prepare 初始化控制器
func (c *AdminLogController) Prepare() {
	c.adminLogService = services.NewAdminLogService()
}

// List 获取日志列表
// @Title 获取管理员日志列表
// @Description 获取管理员操作日志列表，支持分页和条件筛选
// @Param admin_id query int false "管理员ID"
// @Param username query string false "管理员用户名"
// @Param module query string false "操作模块"
// @Param type query string false "操作类型"
// @Param ip query string false "操作IP"
// @Param status query int false "操作状态(0:失败,1:成功,-1:全部)"
// @Param start_time query string false "开始时间(格式:2006-01-02 15:04:05)"
// @Param end_time query string false "结束时间(格式:2006-01-02 15:04:05)"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} dto.Response
// @Failure 400 {object} dto.Response
// @Failure 500 {object} dto.Response
// @router /list [get]
func (c *AdminLogController) List() {
	// 获取请求参数
	adminIDStr := c.GetString("admin_id", "0")
	adminID, _ := strconv.ParseInt(adminIDStr, 10, 64)
	username := c.GetString("username", "")
	module := c.GetString("module", "")
	logType := c.GetString("type", "")
	ip := c.GetString("ip", "")
	statusStr := c.GetString("status", "-1")
	status, _ := strconv.Atoi(statusStr)
	startTimeStr := c.GetString("start_time", "")
	endTimeStr := c.GetString("end_time", "")
	pageStr := c.GetString("page", "1")
	page, _ := strconv.Atoi(pageStr)
	pageSizeStr := c.GetString("pageSize", "10")
	pageSize, _ := strconv.Atoi(pageSizeStr)

	// 解析时间参数
	var startTime, endTime time.Time
	var err error
	if startTimeStr != "" {
		startTime, err = time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
		if err != nil {
			logs.Error("解析开始时间失败: %v", err)
			c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "开始时间格式不正确")
			c.ServeJSON()
			return
		}
	}
	if endTimeStr != "" {
		endTime, err = time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
		if err != nil {
			logs.Error("解析结束时间失败: %v", err)
			c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "结束时间格式不正确")
			c.ServeJSON()
			return
		}
	}

	// 构建查询参数
	queryReq := &adminDto.AdminLogQueryRequest{
		AdminID:   adminID,
		Username:  username,
		Module:    module,
		Type:      logType,
		IP:        ip,
		Status:    status,
		StartTime: startTime,
		EndTime:   endTime,
		Page:      page,
		PageSize:  pageSize,
	}

	// 调用服务获取数据
	adminLogs, total, err := c.adminLogService.ListLogs(c.Ctx.Request.Context(), queryReq)
	if err != nil {
		logs.Error("获取管理员日志列表失败: %v", err)
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "获取日志列表失败")
		c.ServeJSON()
		return
	}

	// 构建响应数据
	result := &adminDto.AdminLogListResponse{
		Total: total,
		List:  adminLogs,
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(result)
	c.ServeJSON()
}

// Detail 获取日志详情
// @Title 获取管理员日志详情
// @Description 根据ID获取管理员操作日志详情
// @Param id path int true "日志ID"
// @Success 200 {object} dto.Response
// @Failure 400 {object} dto.Response
// @Failure 404 {object} dto.Response
// @Failure 500 {object} dto.Response
// @router /:id [get]
func (c *AdminLogController) Detail() {
	// 获取ID参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析日志ID失败: %v", err)
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的日志ID")
		c.ServeJSON()
		return
	}

	// 调用服务获取详情
	adminLog, err := c.adminLogService.GetLogByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取管理员日志详情失败: %v", err)
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "获取日志详情失败")
		c.ServeJSON()
		return
	}

	if adminLog == nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeNotFoundError, "日志不存在")
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(adminLog)
	c.ServeJSON()
}

// GetLatestLogs 获取当前管理员最近的操作日志
// @Title 获取当前管理员最近操作日志
// @Description 获取当前登录管理员最近的操作日志
// @Param limit query int false "限制数量"
// @Success 200 {object} dto.Response
// @Failure 401 {object} dto.Response
// @Failure 500 {object} dto.Response
// @router /latest [get]
func (c *AdminLogController) GetLatestLogs() {
	// 从JWT中获取管理员ID
	// 这里从请求头中获取，实际项目可能需要从JWT中解析
	adminIDStr := c.Ctx.Input.Header("X-Admin-ID")
	adminID, err := strconv.ParseInt(adminIDStr, 10, 64)
	if err != nil || adminID <= 0 {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未登录或登录已过期")
		c.ServeJSON()
		return
	}

	// 获取限制数量参数
	limitStr := c.GetString("limit", "10")
	limit, _ := strconv.Atoi(limitStr)
	if limit <= 0 || limit > 50 {
		limit = 10 // 默认10条，最多不超过50条
	}

	// 调用服务获取最近日志
	adminLogs, err := c.adminLogService.GetLatestLogsByAdminID(c.Ctx.Request.Context(), adminID, limit)
	if err != nil {
		logs.Error("获取管理员最近日志失败: %v", err)
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "获取最近操作日志失败")
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(adminLogs)
	c.ServeJSON()
}

// CreateLog 记录管理员操作日志（内部方法）
// 用于在控制器中记录操作日志，不作为API接口暴露
func (c *AdminLogController) CreateLog(adminID int64, username, module, logType, content, targetType string, targetID int64, status int, remark string) {
	// 构建日志创建请求
	req := &adminDto.AdminLogCreateRequest{
		AdminID:    adminID,
		Username:   username,
		Module:     module,
		Type:       logType,
		Content:    content,
		IP:         c.Ctx.Input.IP(),
		UserAgent:  c.Ctx.Input.UserAgent(),
		TargetID:   targetID,
		TargetType: targetType,
		Status:     status,
		Remark:     remark,
	}

	// 异步记录日志，避免影响主流程
	go func() {
		_, err := c.adminLogService.CreateLog(utils.CreateContext(), req)
		if err != nil {
			logs.Error("记录管理员操作日志失败: %v", err)
		}
	}()
}
