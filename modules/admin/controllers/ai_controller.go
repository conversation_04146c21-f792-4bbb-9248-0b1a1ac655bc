/**
 * AI控制器
 *
 * 本文件实现了AI相关的控制器，提供了AI配置管理功能。
 */

package controllers

import (
	"encoding/json"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services/impl"
)

// AIController AI控制器
type AIController struct {
	web.Controller
}

// GetConfig 获取AI配置
// @Title GetConfig
// @Description 获取AI配置信息
// @Success 200 {object} result.Result 成功
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ai [get]
func (c *AIController) GetConfig() {
	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 获取AI配置
	config, err := aiService.GetAIConfig()
	if err != nil {
		logs.Error("获取AI配置失败: %v", err)
		result.HandleError(c.Ctx, err, "获取AI配置失败: "+err.Error())
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, map[string]interface{}{
		"list":  []dto.AIConfigDTO{*config},
		"total": 1,
	})
}

// SaveConfig 保存AI配置
// @Title SaveConfig
// @Description 保存AI配置信息
// @Param   body     body    dto.AIConfigDTO  true   "AI配置参数"
// @Success 200 {object} result.Result 成功
// @Failure 400 {object} result.Result 请求参数错误
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ai [post]
func (c *AIController) SaveConfig() {
	// 解析请求体
	var config dto.AIConfigDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &config); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, err, "请求参数错误")
		return
	}

	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 保存AI配置
	if err := aiService.SaveAIConfig(&config); err != nil {
		logs.Error("保存AI配置失败: %v", err)
		result.HandleError(c.Ctx, err, "保存AI配置失败: "+err.Error())
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, "保存AI配置成功")
}

// RefreshCache 刷新AI配置缓存
// @Title RefreshCache
// @Description 刷新AI配置缓存
// @Success 200 {object} result.Result 成功
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ai/refresh [post]
func (c *AIController) RefreshCache() {
	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 刷新缓存
	if err := aiService.RefreshAIConfig(); err != nil {
		logs.Error("刷新AI配置缓存失败: %v", err)
		result.HandleError(c.Ctx, err, "刷新AI配置缓存失败: "+err.Error())
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, "刷新AI配置缓存成功")
}
