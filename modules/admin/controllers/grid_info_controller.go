/**
 * GridInfo 管理控制器
 *
 * 该文件实现了管理员模块下网格布局相关的控制器功能
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils/common"
)

// GridInfoController 网格布局管理控制器
type GridInfoController struct {
	web.Controller
	gridInfoService services.AdminGridInfoService
}

// Prepare 初始化控制器
func (c *GridInfoController) Prepare() {
	// 初始化服务
	c.gridInfoService = services.NewAdminGridInfoService()
}

// ParseRequest 通用请求参数解析方法
func (c *GridInfoController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// List 获取网格布局列表
// @Title List
// @Description 获取网格布局列表
// @Param ui_config_id query int false "关联的UI配置ID"
// @Param name query string false "组件名称"
// @Param status query int false "状态：1-启用，0-禁用"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [get]
func (c *GridInfoController) List() {
	var queryDTO dto.AdminGridInfoQueryDTO
	if err := c.ParseRequest(&queryDTO); err != nil {
		logs.Error("[List] 解析请求参数失败: %v", err)
		logs.Info("[List] 原始请求参数: %v", c.Ctx.Request.URL.RawQuery)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 增加日志，输出解析后的分页参数
	logs.Info("[List] 解析后分页参数: page=%d, pageSize=%d", queryDTO.Page, queryDTO.PageSize)
	logs.Info("[List] 解析后全部参数: %+v", queryDTO)

	// 设置默认分页参数
	if queryDTO.Page <= 0 {
		queryDTO.Page = 1
	}
	if queryDTO.PageSize <= 0 {
		queryDTO.PageSize = 10
	}

	// 使用管理员模块的服务获取网格布局列表
	gridItems, total, err := c.gridInfoService.ListGridInfos(c.Ctx.Request.Context(), &queryDTO)
	if err != nil {
		logs.Error("[List] 获取网格布局列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回分页数据
	result.OKWithPagination(c.Ctx, gridItems, total, queryDTO.Page, queryDTO.PageSize)
}

// Get 获取网格布局详情
// @Title Get
// @Description 获取网格布局详情
// @Param id path int true "网格布局ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id [get]
func (c *GridInfoController) Get() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[Get] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务获取网格布局详情
	gridItem, err := c.gridInfoService.GetGridInfo(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[Get] 获取网格布局详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	if gridItem == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, gridItem)
}

// Create 创建网格布局
// @Title Create
// @Description 创建网格布局
// @Param body body dto.AdminGridInfoCreateDTO true "创建参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [post]
func (c *GridInfoController) Create() {
	var createDTO dto.AdminGridInfoCreateDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &createDTO); err != nil {
		logs.Error("[Create] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务创建网格布局
	id, err := c.gridInfoService.CreateGridInfo(c.Ctx.Request.Context(), &createDTO)
	if err != nil {
		logs.Error("[Create] 创建网格布局失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{"id": id})
}

// Update 更新网格布局
// @Title Update
// @Description 更新网格布局
// @Param id path int true "网格布局ID"
// @Param body body dto.AdminGridInfoUpdateDTO true "更新参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id [put]
func (c *GridInfoController) Update() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[Update] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	var updateDTO dto.AdminGridInfoUpdateDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &updateDTO); err != nil {
		logs.Error("[Update] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 添加调试日志，输出前端传递的step字段
	logs.Info("[Update] 收到的Step字段值: %+v", updateDTO.Step)
	logs.Info("[Update] 请求体: %s", string(c.Ctx.Input.RequestBody))

	// 使用管理员模块的服务更新网格布局
	err = c.gridInfoService.UpdateGridInfo(c.Ctx.Request.Context(), id, &updateDTO)
	if err != nil {
		logs.Error("[Update] 更新网格布局失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{"id": id})
}

// Delete 删除网格布局
// @Title Delete
// @Description 删除网格布局
// @Param id path int true "网格布局ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id [delete]
func (c *GridInfoController) Delete() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[Delete] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务删除网格布局
	err = c.gridInfoService.DeleteGridInfo(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[Delete] 删除网格布局失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, nil)
}

// UpdateStatus 更新网格布局状态
// @Title UpdateStatus
// @Description 更新网格布局状态
// @Param id path int true "网格布局ID"
// @Param body body dto.AdminGridInfoStatusUpdateDTO true "状态更新参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id/status [put]
func (c *GridInfoController) UpdateStatus() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[UpdateStatus] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	var statusDTO dto.AdminGridInfoStatusUpdateDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &statusDTO); err != nil {
		logs.Error("[UpdateStatus] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务更新网格布局状态
	err = c.gridInfoService.UpdateGridInfoStatus(c.Ctx.Request.Context(), id, statusDTO.Status)
	if err != nil {
		logs.Error("[UpdateStatus] 更新网格布局状态失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, nil)
}

// BatchUpdatePosition 批量更新网格布局位置
// @Title BatchUpdatePosition
// @Description 批量更新网格布局位置
// @Param body body dto.AdminGridInfoBatchUpdatePositionDTO true "批量更新位置参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /batch/position [put]
func (c *GridInfoController) BatchUpdatePosition() {
	var batchDTO dto.AdminGridInfoBatchUpdatePositionDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &batchDTO); err != nil {
		logs.Error("[BatchUpdatePosition] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务批量更新网格布局位置
	err := c.gridInfoService.BatchUpdateGridInfoPosition(c.Ctx.Request.Context(), &batchDTO)
	if err != nil {
		logs.Error("[BatchUpdatePosition] 批量更新网格布局位置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, nil)
}

// ListUIConfigs 获取网格布局关联的UI配置列表
// @Title ListUIConfigs
// @Description 获取网格布局关联的UI配置列表
// @Param id path int true "网格布局ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id/ui-configs [get]
func (c *GridInfoController) ListUIConfigs() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[ListUIConfigs] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务获取网格布局关联的UI配置列表
	uiConfigs, err := c.gridInfoService.ListUIConfigsByGridInfoID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[ListUIConfigs] 获取网格布局关联的UI配置列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, uiConfigs)
}

// AddUIConfigs 为网格布局添加UI配置关联
// @Title AddUIConfigs
// @Description 为网格布局添加UI配置关联
// @Param id path int true "网格布局ID"
// @Param body body dto.AdminGridInfoUIConfigRelationDTO true "UI配置关联参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id/ui-configs [post]
func (c *GridInfoController) AddUIConfigs() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[AddUIConfigs] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var relationDTO dto.AdminGridInfoUIConfigRelationDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &relationDTO); err != nil {
		logs.Error("[AddUIConfigs] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置网格布局ID
	relationDTO.GridInfoID = id

	// 使用管理员模块的服务添加UI配置关联
	err = c.gridInfoService.AddUIConfigsToGridInfo(c.Ctx.Request.Context(), &relationDTO)
	if err != nil {
		logs.Error("[AddUIConfigs] 添加UI配置关联失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, nil)
}

// RemoveUIConfigs 从网格布局移除UI配置关联
// @Title RemoveUIConfigs
// @Description 从网格布局移除UI配置关联
// @Param id path int true "网格布局ID"
// @Param body body dto.AdminGridInfoUIConfigRelationDTO true "UI配置关联参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id/ui-configs/remove [post]
func (c *GridInfoController) RemoveUIConfigs() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[RemoveUIConfigs] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var relationDTO dto.AdminGridInfoUIConfigRelationDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &relationDTO); err != nil {
		logs.Error("[RemoveUIConfigs] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置网格布局ID
	relationDTO.GridInfoID = id

	// 使用管理员模块的服务移除UI配置关联
	err = c.gridInfoService.RemoveUIConfigsFromGridInfo(c.Ctx.Request.Context(), &relationDTO)
	if err != nil {
		logs.Error("[RemoveUIConfigs] 移除UI配置关联失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, nil)
}

// UpdateUIConfigs 更新网格布局关联的UI配置
// @Title UpdateUIConfigs
// @Description 更新网格布局关联的UI配置
// @Param id path int true "网格布局ID"
// @Param body body dto.AdminGridInfoUIConfigRelationDTO true "UI配置关联参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id/ui-configs [put]
func (c *GridInfoController) UpdateUIConfigs() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[UpdateUIConfigs] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var relationDTO dto.AdminGridInfoUIConfigRelationDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &relationDTO); err != nil {
		logs.Error("[UpdateUIConfigs] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置网格布局ID
	relationDTO.GridInfoID = id

	// 使用管理员模块的服务更新UI配置关联
	err = c.gridInfoService.UpdateGridInfoUIConfigs(c.Ctx.Request.Context(), &relationDTO)
	if err != nil {
		logs.Error("[UpdateUIConfigs] 更新UI配置关联失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, nil)
}

// SetUIConfigRelation 快速设置网格布局与UI配置的关联关系
// @Title SetUIConfigRelation
// @Description 快速设置网格布局与UI配置的关联关系
// @Param body body dto.AdminGridInfoUIConfigRelationDTO true "关联关系参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /set-ui-configs [post]
func (c *GridInfoController) SetUIConfigRelation() {
	// 解析请求参数
	var relationDTO dto.AdminGridInfoUIConfigRelationDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &relationDTO); err != nil {
		logs.Error("[SetUIConfigRelation] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证参数
	if relationDTO.GridInfoID <= 0 {
		logs.Error("[SetUIConfigRelation] 缺少网格布局ID")
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务更新UI配置关联
	err := c.gridInfoService.UpdateGridInfoUIConfigs(c.Ctx.Request.Context(), &relationDTO)
	if err != nil {
		logs.Error("[SetUIConfigRelation] 设置UI配置关联关系失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, map[string]interface{}{
		"grid_info_id":  relationDTO.GridInfoID,
		"ui_config_ids": relationDTO.UIConfigIDs,
		"message":       "设置关联关系成功",
	})
}

// DeleteUIConfigRelation 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
// @Title DeleteUIConfigRelation
// @Description 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
// @Param body body struct{GridInfoID int `json:"grid_info_id"`; UIConfigID int `json:"ui_config_id"`} true "删除参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /delete-ui-configs [post]
func (c *GridInfoController) DeleteUIConfigRelation() {
	var req struct {
		GridInfoID int `json:"grid_info_id"`
		UIConfigID int `json:"ui_config_id"`
	}
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logs.Error("[DeleteUIConfigRelation] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	if req.GridInfoID <= 0 || req.UIConfigID <= 0 {
		logs.Error("[DeleteUIConfigRelation] 缺少必要参数")
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	err := c.gridInfoService.DeleteUIConfigGridRelation(c.Ctx.Request.Context(), req.GridInfoID, req.UIConfigID)
	if err != nil {
		logs.Error("[DeleteUIConfigRelation] 删除UIConfigGridRelation失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	result.OK(c.Ctx, nil)
}

// SetBatchUIConfigGridRelation 批量设置UI配置与多个网格布局的关联关系
// @Title SetBatchUIConfigGridRelation
// @Description 批量将一个UI配置与多个网格布局建立关联
// @Param body body dto.AdminBatchUIConfigGridRelationDTO true "批量关联参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /set-batch-ui-config-relation [post]
func (c *GridInfoController) SetBatchUIConfigGridRelation() {
	// 解析请求参数
	var batchDTO dto.AdminBatchUIConfigGridRelationDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &batchDTO); err != nil {
		logs.Error("[SetBatchUIConfigGridRelation] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	if batchDTO.UIConfigId <= 0 || len(batchDTO.GridInfoIds) == 0 {
		logs.Error("[SetBatchUIConfigGridRelation] 缺少UIConfigId或GridInfoIds")
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	// 调用服务层批量建立关联
	err := c.gridInfoService.UpdateBatchUIConfigGridRelation(c.Ctx.Request.Context(), &batchDTO)
	if err != nil {
		logs.Error("[SetBatchUIConfigGridRelation] 批量设置关联失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	result.OK(c.Ctx, map[string]interface{}{
		"ui_config_id": batchDTO.UIConfigId,
		"grid_info_ids": batchDTO.GridInfoIds,
		"message": "批量设置关联关系成功",
	})
}
