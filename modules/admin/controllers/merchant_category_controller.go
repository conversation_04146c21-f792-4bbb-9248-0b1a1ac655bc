/**
 * 管理员商家分类控制器
 *
 * 该文件实现了管理员模块中的商家分类管理相关API接口。
 * 包括商家分类的查询、创建、更新、删除等功能。
 */

package controllers

import (
	"encoding/json"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	adminDto "o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils/common"
)

// MerchantCategoryController 商家分类控制器
type MerchantCategoryController struct {
	web.Controller
	categoryService services.AdminMerchantCategoryService
}

// Prepare 初始化控制器
func (c *MerchantCategoryController) Prepare() {
	c.categoryService = services.NewAdminMerchantCategoryService()
}

// ParseRequest 通用请求参数解析方法
func (c *MerchantCategoryController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// ListCategories 获取商家分类列表
// @Title 获取商家分类列表
// @Description 分页获取商家分类列表，支持按名称和显示状态筛选
// @Tags 商家分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param name query string false "分类名称"
// @Param is_show query bool false "是否显示"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} result.Response{data=adminDto.MerchantCategoryListResponse} "成功"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchant-categories [get]
func (c *MerchantCategoryController) ListCategories() {
	// 解析查询参数
	var req adminDto.MerchantCategoryQueryRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 调用服务层获取分类列表
	resp, err := c.categoryService.ListCategories(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应，包含分页信息
	result.OKWithPagination(c.Ctx, resp.Items, resp.Total, req.Page, req.PageSize)
}

// GetAllCategories 获取所有商家分类
// @Title 获取所有商家分类
// @Description 获取所有商家分类，可选择只返回显示的分类
// @Tags 商家分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param only_show query bool false "是否只返回显示的分类，默认false"
// @Success 200 {object} result.Response{data=[]adminDto.MerchantCategoryResponse} "成功"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchant-categories/all [get]
func (c *MerchantCategoryController) GetAllCategories() {
	// 解析查询参数
	onlyShow, _ := c.GetBool("only_show", false)

	// 调用服务层获取所有分类
	categories, err := c.categoryService.GetAllCategories(c.Ctx.Request.Context(), onlyShow)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, categories)
}

// GetCategoryByID 获取商家分类详情
// @Title 获取商家分类详情
// @Description 根据ID获取商家分类详情
// @Tags 商家分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "分类ID"
// @Success 200 {object} result.Response{data=adminDto.MerchantCategoryResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "分类不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchant-categories/{id} [get]
func (c *MerchantCategoryController) GetCategoryByID() {
	// 获取分类ID
	id, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层获取分类详情
	category, err := c.categoryService.GetCategoryByID(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, category)
}

// CreateCategory 创建商家分类
// @Title 创建商家分类
// @Description 创建新的商家分类
// @Tags 商家分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body adminDto.CreateMerchantCategoryRequest true "分类信息"
// @Success 200 {object} result.Response{data=int64} "成功返回分类ID"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchant-categories [post]
func (c *MerchantCategoryController) CreateCategory() {
	// 解析请求体
	var req adminDto.CreateMerchantCategoryRequest
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	if req.Name == "" {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "分类名称不能为空"))
		return
	}

	// 调用服务层创建分类
	id, err := c.categoryService.CreateCategory(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// UpdateCategory 更新商家分类
// @Title 更新商家分类
// @Description 更新商家分类信息
// @Tags 商家分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "分类ID"
// @Param body body adminDto.UpdateMerchantCategoryRequest true "分类更新信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "分类不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchant-categories/{id} [put]
func (c *MerchantCategoryController) UpdateCategory() {
	// 获取分类ID
	id, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求体
	var req adminDto.UpdateMerchantCategoryRequest
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层更新分类
	err = c.categoryService.UpdateCategory(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteCategory 删除商家分类
// @Title 删除商家分类
// @Description 删除商家分类
// @Tags 商家分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "分类ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "分类不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchant-categories/{id} [delete]
func (c *MerchantCategoryController) DeleteCategory() {
	// 获取分类ID
	id, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层删除分类
	err = c.categoryService.DeleteCategory(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
