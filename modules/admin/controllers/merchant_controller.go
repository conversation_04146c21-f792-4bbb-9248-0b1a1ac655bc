/**
 * 商户管理控制器
 *
 * 该文件实现了管理员模块中的商户管理相关API接口。
 * 包括商户查询、更新、审核、状态管理等功能。
 */

package controllers

import (
	"log"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	adminDto "o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/utils/common"
)

// MerchantController 商户管理控制器
type MerchantController struct {
	web.Controller
	merchantService     services.AdminMerchantService
	takeoutStatsService services.TakeoutStatisticsService
}

// Prepare 初始化控制器
func (c *MerchantController) Prepare() {
	c.merchantService = services.NewAdminMerchantService()
	c.takeoutStatsService = services.NewTakeoutStatisticsService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *MerchantController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// ListMerchants 获取商户列表
// @Title 获取商户列表
// @Description 分页获取商户列表，支持按商户名称、联系电话、邮箱、状态、审核状态和地理位置筛选
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param name query string false "商户名称"
// @Param mobile query string false "联系电话"
// @Param email query string false "联系邮箱"
// @Param status query int false "状态：0-禁用，1-正常"
// @Param audit_status query int false "审核状态：0-待审核，1-已通过，2-已拒绝"
// @Param min_longitude query number false "最小经度范围"
// @Param max_longitude query number false "最大经度范围"
// @Param min_latitude query number false "最小纬度范围"
// @Param max_latitude query number false "最大纬度范围"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} result.Response{data=[]adminDto.MerchantResponse} "成功"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants [get]
func (c *MerchantController) ListMerchants() {
	// 解析查询参数
	var req adminDto.MerchantQueryRequest

	// 打印原始请求参数，用于调试
	queryParams := c.Ctx.Request.URL.Query()
	logs.Info("接收到的原始查询参数: %v", queryParams)
	logs.Info("原始 audit_status 参数: %v", queryParams.Get("audit_status"))

	if err := c.ParseRequest(&req); err != nil {
		logs.Error("参数解析错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 打印解析后的请求对象，查看参数是否被正确解析
	logs.Info("解析后的请求对象: %+v", req)
	logs.Info("解析后的 AuditStatus 值: %d", req.AuditStatus)

	// 标记是否明确指定了auditStatus参数
	// 如果请求中没有指定审核状态，则将其设置为-1，表示不过滤审核状态

	// 注意：前端发送的是auditStatus（无下划线），而不是audit_status（有下划线）
	audStatusParam := c.Ctx.Input.Query("auditStatus")
	logs.Info("从 Input 中获取的 auditStatus 参数: %s", audStatusParam)

	// 也检查另一种可能的格式以兼容之前的请求
	if audStatusParam == "" {
		audStatusParam = c.Ctx.Input.Query("audit_status")
		logs.Info("使用audit_status格式获取的参数: %s", audStatusParam)
	}

	if audStatusParam == "" {
		// 两种格式都没有获取到参数，则使用-1
		req.AuditStatus = -1 // 使用-1表示不过滤审核状态
		logs.Info("未指定审核状态参数，设置为-1")
	} else {
		// 格式转换
		auditStatusInt, err := strconv.Atoi(audStatusParam)
		if err == nil {
			// 只有成功转换为数字时才覆盖req.AuditStatus值
			req.AuditStatus = auditStatusInt
			logs.Info("成功转换审核状态参数为数字: %d", req.AuditStatus)
		} else {
			logs.Error("审核状态参数转换失败: %v", err)
		}
	}
	
	// 处理IsRecommended参数
	isRecommendedParam := c.Ctx.Input.Query("isRecommended")
	if isRecommendedParam == "" {
		isRecommendedParam = c.Ctx.Input.Query("is_recommended")
	}
	
	if isRecommendedParam == "" {
		req.IsRecommended = -1 // 使用-1表示不过滤推荐状态
		logs.Info("未指定推荐状态参数，设置为-1")
	} else {
		isRecommendedInt, err := strconv.Atoi(isRecommendedParam)
		if err == nil {
			req.IsRecommended = isRecommendedInt
			logs.Info("成功转换推荐状态参数为数字: %d", req.IsRecommended)
		} else {
			logs.Error("推荐状态参数转换失败: %v", err)
		}
	}
	
	// 处理Level参数
	levelParam := c.Ctx.Input.Query("level")
	if levelParam == "" {
		req.Level = 0 // 使用0表示不过滤等级
		logs.Info("未指定等级参数，设置为0")
	} else {
		levelInt, err := strconv.Atoi(levelParam)
		if err == nil {
			req.Level = levelInt
			logs.Info("成功转换等级参数为数字: %d", req.Level)
		} else {
			logs.Error("等级参数转换失败: %v", err)
		}
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 调用服务层获取商户列表
	merchants, total, err := c.merchantService.ListMerchants(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OKWithPagination(c.Ctx, merchants, total, req.Page, req.PageSize)
}

// GetMerchant 获取商户详情
// @Title 获取商户详情
// @Description 根据商户ID获取商户详细信息，包含外卖食品和订单统计信息
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Success 200 {object} result.Response{data=adminDto.MerchantResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id} [get]
func (c *MerchantController) GetMerchant() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层获取商户基本信息
	merchant, err := c.merchantService.GetMerchantByID(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用外卖模块的服务获取商户外卖统计信息
	takeoutStats, err := c.takeoutStatsService.GetMerchantTakeoutStatistics(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		// 失败仅记录日志，不阻止返回回商户基本信息
		logs.Error("获取商户外卖统计信息失败: %v, 商户ID: %d", err, merchantID)
	} else {
		// 将外卖统计信息添加到商户响应中
		merchant.TakeoutStatistics = takeoutStats
	}

	// 返回成功响应
	result.OK(c.Ctx, merchant)
}

// UpdateMerchant 更新商户信息
// @Title 更新商户信息
// @Description 更新商户的基本信息
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body adminDto.UpdateMerchantRequest true "商户信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id} [put]
func (c *MerchantController) UpdateMerchant() {
	// 解析请求体
	var req adminDto.UpdateMerchantRequest
	
	// 创建一个map来记录前端实际传递了哪些字段
	rawData := make(map[string]interface{})
	if err := c.Ctx.BindJSON(&rawData); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 然后再解析到结构体
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	req.ID = merchantID

	// 记录请求内容，方便调试
	log.Printf("更新商户请求数据: %+v", req)
	
	// 创建一个结构体来存储需要更新的字段信息
	var fieldsToUpdate struct {
		UpdateAuditStatus   bool
		UpdateStatus        bool
		UpdateLevel         bool
		UpdateIsRecommended bool
	}
	
	// 检查原始请求中是否包含这些字段
	_, fieldsToUpdate.UpdateAuditStatus = rawData["audit_status"]
	_, fieldsToUpdate.UpdateStatus = rawData["status"]
	_, fieldsToUpdate.UpdateLevel = rawData["level"]
	_, fieldsToUpdate.UpdateIsRecommended = rawData["is_recommended"]

	// 确保AuditStatus和Status不会被视为"空"
	// 如果验证需要这些字段但前端未传递，我们临时设置为-999
	if !fieldsToUpdate.UpdateAuditStatus {
		// 如果前端没有传递该字段，设置为-999表示不更新该字段
		req.AuditStatus = -999
	} else if req.AuditStatus == 0 {
		// 如果前端明确传了0，我们需要标记这是一个有效值
		req.AuditStatus = -998 // 表示前端传递了0值
	}
	
	if !fieldsToUpdate.UpdateStatus {
		req.Status = -999 // 表示不更新该字段
	} else if req.Status == 0 {
		req.Status = -998 // 表示前端传递了0值
	}

	if !fieldsToUpdate.UpdateLevel {
		req.Level = -999 // 表示不更新该字段
	} else if req.Level == 0 {
		req.Level = -998 // 表示前端传递了0值
	}
	
	if !fieldsToUpdate.UpdateIsRecommended {
		req.IsRecommended = -999 // 表示不更新该字段
	} else if req.IsRecommended == 0 {
		req.IsRecommended = -998 // 表示前端传递了0值
	}
	
	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 将临时值恢复
	// -999 表示字段未被传递，应保持原值，我们将其设为-1来标识服务层不更新此字段
	// -998 表示前端传递了0值，应更新为0
	if req.Level == -999 {
		req.Level = -1 // 特殊值，表示保持原值
	} else if req.Level == -998 {
		req.Level = 0 // 恢复为前端传递的0
	}
	
	if req.AuditStatus == -999 {
		req.AuditStatus = -1 // 特殊值，表示保持原值
	} else if req.AuditStatus == -998 {
		req.AuditStatus = 0 // 恢复为前端传递的0
	}
	
	if req.Status == -999 {
		req.Status = -1 // 特殊值，表示保持原值 
	} else if req.Status == -998 {
		req.Status = 0 // 恢复为前端传递的0
	}
	
	if req.IsRecommended == -999 {
		req.IsRecommended = -1 // 特殊值，表示保持原值
	} else if req.IsRecommended == -998 {
		req.IsRecommended = 0 // 恢复为前端传递的0
	}

	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Field + " " + err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层更新商户信息
	err = c.merchantService.UpdateMerchant(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DisableMerchant 禁用商户
// @Title 禁用商户
// @Description 禁用指定商户的账号
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/disable [put]
func (c *MerchantController) DisableMerchant() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层禁用商户
	err = c.merchantService.DisableMerchant(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// EnableMerchant 启用商户
// @Title 启用商户
// @Description 启用指定商户的账号
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/enable [put]
func (c *MerchantController) EnableMerchant() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层启用商户
	err = c.merchantService.EnableMerchant(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// ResetPassword 重置商户密码
// @Title 重置商户密码
// @Description 重置指定商户的登录密码
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body adminDto.ResetMerchantPasswordRequest true "新密码信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/password [put]
func (c *MerchantController) ResetPassword() {
	// 解析请求体
	var req adminDto.ResetMerchantPasswordRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	req.ID = merchantID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 验证两次密码是否一致
	if req.NewPassword != req.ConfirmPassword {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "两次输入的密码不一致"))
		return
	}

	// 调用服务层重置密码
	err = c.merchantService.ResetMerchantPassword(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// AuditMerchant 审核商户
// @Title 审核商户
// @Description 审核商户的入驻申请
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body adminDto.AuditMerchantRequest true "审核信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/audit [put]
func (c *MerchantController) AuditMerchant() {
	// 解析请求体
	var req adminDto.AuditMerchantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	req.ID = merchantID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层审核商户
	err = c.merchantService.AuditMerchant(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// CreateMerchant 创建商户
// @Title 创建商户
// @Description 管理员直接创建商户账号，创建的商户默认为已审核状态
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body adminDto.CreateMerchantRequest true "商户信息"
// @Success 200 {object} result.Response{data=int64} "成功返回商户ID"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants [post]

// UpdateMerchantAudit 更新商户审核状态
// @Title 更新商户审核状态
// @Description 更新指定商户的审核状态
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body adminDto.AuditMerchantRequest true "商户审核信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/audit [put]
func (c *MerchantController) CreateMerchant() {
	// 解析请求体
	var req adminDto.CreateMerchantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	// 如果没有password，那么password默认为手机号后六位
	if req.Password == "" {
		if len(req.ContactMobile) >= 6 {
			req.Password = req.ContactMobile[len(req.ContactMobile)-6:]
		} else {
			req.Password = "123456" // 默认密码
		}
	}
	log.Println("req", req)
	//参数校验
	valid := validation.Validation{}
	valid.Required(req.Name, "name").Message("商户名称不能为空")
	valid.Required(req.Username, "username").Message("用户名不能为空")
	valid.Required(req.Password, "password").Message("密码不能为空")
	valid.Required(req.ContactName, "contact_name").Message("联系人姓名不能为空")
	valid.Required(req.ContactMobile, "contact_mobile").Message("联系电话不能为空")
	valid.Required(req.BusinessLicense, "business_license").Message("营业执照不能为空")
	valid.Required(req.Address, "address").Message("商户地址不能为空")

	if req.ContactEmail != "" {
		valid.Email(req.ContactEmail, "contact_email").Message("联系邮箱格式不正确")
	}

	if valid.HasErrors() {
		for _, err := range valid.Errors {
			logs.Error("验证失败: %s", err.String())
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层创建商户
	merchantID, err := c.merchantService.CreateMerchant(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, merchantID)
}

// UpdateMerchantStatus 更新商户状态
// @Title 更新商户状态
// @Description 更新指定商户的状态(启用/禁用)
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body adminDto.UpdateMerchantRequest true "商户状态信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/status [put]
func (c *MerchantController) UpdateMerchantStatus() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求体
	var req adminDto.UpdateMerchantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证参数
	valid := validation.Validation{}
	valid.Required(req.Status, "status").Message("商户状态不能为空")

	if valid.HasErrors() {
		for _, err := range valid.Errors {
			logs.Error("验证失败: %s", err.String())
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层更新商户状态
	err = c.merchantService.UpdateMerchantStatus(c.Ctx.Request.Context(), merchantID, req.Status)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateMerchantOperationStatus 更新商户营业状态
// @Title 更新商户营业状态
// @Description 更新指定商户的营业状态(休息中/营业中)
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body merchantDto.UpdateOperationStatusRequest true "商户营业状态信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/operation-status [put]
func (c *MerchantController) UpdateMerchantOperationStatus() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求体
	var req merchantDto.UpdateOperationStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证参数
	valid := validation.Validation{}
	valid.Required(req.OperationStatus, "operation_status").Message("商户营业状态不能为空")
	valid.Range(req.OperationStatus, 0, 1, "operation_status").Message("商户营业状态无效")

	if valid.HasErrors() {
		for _, err := range valid.Errors {
			logs.Error("验证失败: %s", err.String())
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层更新商户营业状态
	err = c.merchantService.UpdateMerchantOperationStatus(c.Ctx.Request.Context(), merchantID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateMerchantCoordinates 更新商户经纬度坐标
// @Title 更新商户经纬度坐标
// @Description 更新指定商户的经纬度坐标信息(GCJ02坐标系)
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body adminDto.UpdateMerchantCoordinatesRequest true "商户坐标信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/coordinates [put]
func (c *MerchantController) UpdateMerchantCoordinates() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	logs.Info("开始处理更新商户坐标请求，商户ID: %d", merchantID)

	// 解析请求体
	var req adminDto.UpdateMerchantCoordinatesRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证参数
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		logs.Error("参数校验过程错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	if !b {
		for _, err := range valid.Errors {
			logs.Error("参数校验错误: %s", err.String())
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 转换为服务层请求对象
	updateReq := &adminDto.UpdateMerchantRequest{
		ID:        merchantID,
		Longitude: req.Longitude,
		Latitude:  req.Latitude,
	}

	// 调用服务层更新商户坐标
	err = c.merchantService.UpdateMerchantCoordinates(c.Ctx.Request.Context(), updateReq)
	if err != nil {
		logs.Error("更新商户坐标失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateMerchantAudit 更新商户审核状态
func (c *MerchantController) UpdateMerchantAudit() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	logs.Info("开始处理商户审核请求，商户ID: %d", merchantID)

	// 解析请求体
	var req adminDto.AuditMerchantRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	logs.Info("正常解析后的请求对象: %+v", req)

	// 验证参数
	valid := validation.Validation{}
	valid.Required(req.AuditStatus, "audit_status").Message("审核状态不能为空")
	if valid.HasErrors() {
		for _, err := range valid.Errors {
			logs.Error("验证失败: %s", err.String())
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置商户ID
	req.ID = merchantID
	logs.Info("开始调用AuditMerchant服务，参数: %+v", req)

	// 调用服务层更新商户审核状态
	err = c.merchantService.AuditMerchant(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("调用AuditMerchant失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("商户审核状态更新成功，商户ID: %d", merchantID)
	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateMerchantRecommendStatus 更新商户推荐状态
// @Title 更新商户推荐状态
// @Description 更新指定商户的推荐状态
// @Tags 商户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "商户ID"
// @Param body body adminDto.UpdateMerchantRecommendRequest true "商户推荐状态信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "商户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/merchants/{id}/recommend [put]
func (c *MerchantController) UpdateMerchantRecommendStatus() {
	// 获取商户ID
	merchantID, err := c.GetInt64(":id")
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求体
	var req adminDto.UpdateMerchantRecommendRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证参数
	valid := validation.Validation{}
	valid.Range(req.IsRecommended, 0, 1, "is_recommended").Message("推荐状态必须为0或1")

	if valid.HasErrors() {
		for _, err := range valid.Errors {
			logs.Error("验证失败: %s", err.String())
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层更新商户推荐状态
	err = c.merchantService.UpdateMerchantRecommendStatus(c.Ctx.Request.Context(), merchantID, req.IsRecommended)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
