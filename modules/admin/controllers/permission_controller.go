/**
 * 权限控制器
 *
 * 该文件实现了权限相关的API接口。
 * 包括权限管理、角色管理、权限分配等功能。
 */

package controllers

import (
	"context"
	"log"
	"strconv"

	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// PermissionController 权限控制器
type PermissionController struct {
	web.Controller
	permissionService services.AdminPermissionService
}

// Prepare 初始化
func (c *PermissionController) Prepare() {
	c.permissionService = services.NewAdminPermissionService()
}

// ParseRequest 解析请求
func (c *PermissionController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// ListPermissions 获取权限列表
// @Title 获取权限列表
// @Description 获取权限列表，支持分页和条件查询
// @Param   name        query   string  false   "权限名称"
// @Param   code        query   string  false   "权限编码"
// @Param   type        query   int     false   "权限类型"
// @Param   level       query   int     false   "权限级别"
// @Param   status      query   int     false   "状态"
// @Param   page        query   int     true    "页码"
// @Param   pageSize   query   int     true    "每页数量"
// @Success 200 {object} utils.Response
// @router /permissions [get]
func (c *PermissionController) ListPermissions() {
	req := &dto.PermissionQueryRequest{}
	if err := c.ParseForm(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 查询权限列表
	permissions, total, err := c.permissionService.ListPermissions(context.Background(), req)
	if err != nil {
		utils.ResponseError("获取权限列表失败", 401)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{
		"list":  permissions,
		"total": total,
	})
}

// GetPermission 获取权限详情
// @Title 获取权限详情
// @Description 获取权限详情
// @Param   id          path    int     true    "权限ID"
// @Success 200 {object} utils.Response
// @router /permissions/:id [get]
func (c *PermissionController) GetPermission() {
	id, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	permission, err := c.permissionService.GetPermissionByID(context.Background(), id)
	if err != nil {
		utils.ResponseError("获取权限详情失败", 401)
		return
	}

	result.OK(c.Ctx, permission)
}

// CreatePermission 创建权限
// @Title 创建权限
// @Description 创建权限
// @Param   parent_id   body    int64   false   "父权限ID"
// @Param   name        body    string  true    "权限名称"
// @Param   code        body    string  true    "权限编码"
// @Param   type        body    int     true    "权限类型"
// @Param   level       body    int     true    "权限级别"
// @Param   path        body    string  false   "路由路径"
// @Param   component   body    string  false   "组件路径"
// @Param   icon        body    string  false   "图标"
// @Param   sort        body    int     false   "排序"
// @Param   description body    string  false   "描述"
// @Success 200 {object} utils.Response
// @router /permissions [post]
func (c *PermissionController) CreatePermission() {
	req := &dto.CreatePermissionRequest{}
	if err := c.ParseRequest(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	log.Println("接收到的数据req为：", req)
	// 参数验证
	if req.Name == "" {
		utils.ResponseError("权限名称不能为空", 401)
		return
	}
	if req.Code == "" {
		utils.ResponseError("权限编码不能为空", 401)
		return
	}
	if req.Type == 0 {
		utils.ResponseError("权限类型不能为空", 401)
		return
	}
	if req.Level == 0 {
		utils.ResponseError("权限级别不能为空", 401)
		return
	}

	err := c.permissionService.CreatePermission(context.Background(), req)
	if err != nil {
		utils.ResponseError("创建权限失败", 402)
		return
	}

	result.OK(c.Ctx, nil)
}

// UpdatePermission 更新权限
// @Title 更新权限
// @Description 更新权限
// @Param   id          path    int     true    "权限ID"
// @Param   parent_id   body    int64   false   "父权限ID"
// @Param   name        body    string  true    "权限名称"
// @Param   code        body    string  true    "权限编码"
// @Param   type        body    int     true    "权限类型"
// @Param   level       body    int     true    "权限级别"
// @Param   path        body    string  false   "路由路径"
// @Param   component   body    string  false   "组件路径"
// @Param   icon        body    string  false   "图标"
// @Param   sort        body    int     false   "排序"
// @Param   status      body    int     true    "状态"
// @Param   description body    string  false   "描述"
// @Success 200 {object} utils.Response
// @router /permissions/:id [put]
func (c *PermissionController) UpdatePermission() {
	id, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	req := &dto.UpdatePermissionRequest{
		ID: id,
	}
	if err := c.ParseRequest(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	// 参数验证
	if req.Name == "" {
		utils.ResponseError("权限名称不能为空", 401)
		return
	}
	if req.Code == "" {
		utils.ResponseError("权限编码不能为空", 401)
		return
	}
	if req.Type == 0 {
		utils.ResponseError("权限类型不能为空", 401)
		return
	}
	if req.Level == 0 {
		utils.ResponseError("权限级别不能为空", 401)
		return
	}
	if req.Status == 0 {
		utils.ResponseError("状态不能为空", 401)
		return
	}

	err = c.permissionService.UpdatePermission(context.Background(), req)
	if err != nil {
		utils.ResponseError("更新权限失败", 400)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeletePermission 删除权限
// @Title 删除权限
// @Description 删除权限
// @Param   id          path    int     true    "权限ID"
// @Success 200 {object} utils.Response
// @router /permissions/:id [delete]
func (c *PermissionController) DeletePermission() {
	id, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	err = c.permissionService.DeletePermission(context.Background(), id)
	if err != nil {
		utils.ResponseError("删除权限失败", 402)
		return
	}

	result.OK(c.Ctx, nil)
}

// ListRoles 获取角色列表
// @Title 获取角色列表
// @Description 获取角色列表，支持分页和条件查询
// @Param   name        query   string  false   "角色名称"
// @Param   code        query   string  false   "角色编码"
// @Param   status      query   int     false   "状态"
// @Param   page        query   int     true    "页码"
// @Param   pageSize   query   int     true    "每页数量"
// @Success 200 {object} utils.Response
// @router /roles [get]
func (c *PermissionController) ListRoles() {
	req := &dto.RoleQueryRequest{}
	if err := c.ParseForm(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 查询角色列表
	roles, total, err := c.permissionService.ListRoles(context.Background(), req)
	if err != nil {
		utils.ResponseError("获取角色列表失败", 401)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{
		"list":  roles,
		"total": total,
	})
}

// GetRole 获取角色详情
// @Title 获取角色详情
// @Description 获取角色详情
// @Param   id          path    int     true    "角色ID"
// @Success 200 {object} utils.Response
// @router /roles/:id [get]
func (c *PermissionController) GetRole() {
	id, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 401)
		return
	}

	role, err := c.permissionService.GetRoleByID(context.Background(), id)
	if err != nil {
		utils.ResponseError("获取角色详情失败", 401)
		return
	}

	result.OK(c.Ctx, role)
}

// CreateRole 创建角色
// @Title 创建角色
// @Description 创建角色
// @Param   name        body    string  true    "角色名称"
// @Param   code        body    string  true    "角色编码"
// @Param   description body    string  false   "描述"
// @Success 200 {object} utils.Response
// @router /roles [post]
func (c *PermissionController) CreateRole() {
	req := &dto.CreateRoleRequest{}
	if err := c.ParseRequest(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	// 参数验证
	if req.Name == "" {
		utils.ResponseError("角色名称不能为空", 401)
		return
	}
	if req.Code == "" {
		utils.ResponseError("角色编码不能为空", 401)
		return
	}

	err := c.permissionService.CreateRole(context.Background(), req)
	if err != nil {
		utils.ResponseError("创建角色失败", 402)
		return
	}

	result.OK(c.Ctx, nil)
}

// UpdateRole 更新角色
// @Title 更新角色
// @Description 更新角色
// @Param   id          path    int     true    "角色ID"
// @Param   name        body    string  true    "角色名称"
// @Param   code        body    string  true    "角色编码"
// @Param   status      body    int     true    "状态"
// @Param   description body    string  false   "描述"
// @Success 200 {object} utils.Response
// @router /roles/:id [put]
func (c *PermissionController) UpdateRole() {
	id, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	req := &dto.UpdateRoleRequest{
		ID: id,
	}
	if err := c.ParseForm(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	// 参数验证
	if req.Name == "" {
		utils.ResponseError("角色名称不能为空", 401)
		return
	}
	if req.Code == "" {
		utils.ResponseError("角色编码不能为空", 401)
		return
	}
	if req.Status == 0 {
		utils.ResponseError("状态不能为空", 401)
		return
	}

	err = c.permissionService.UpdateRole(context.Background(), req)
	if err != nil {
		utils.ResponseError("更新角色失败", 400)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeleteRole 删除角色
// @Title 删除角色
// @Description 删除角色
// @Param   id          path    int     true    "角色ID"
// @Success 200 {object} utils.Response
// @router /roles/:id [delete]
func (c *PermissionController) DeleteRole() {
	id, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	err = c.permissionService.DeleteRole(context.Background(), id)
	if err != nil {
		utils.ResponseError("删除角色失败", 402)
		return
	}

	result.OK(c.Ctx, nil)
}

// AssignRole 分配角色
// @Title 分配角色
// @Description 分配角色
// @Param   user_id     body    int64   true    "用户ID"
// @Param   user_type   body    string  true    "用户类型"
// @Param   role_id     body    int64   true    "角色ID"
// @Success 200 {object} utils.Response
// @router /roles/assign [post]
func (c *PermissionController) AssignRole() {
	req := &dto.AssignRoleRequest{}
	if err := c.ParseForm(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	// 参数验证
	if req.UserID == 0 {
		utils.ResponseError("用户ID不能为空", 401)
		return
	}
	if req.UserType == "" {
		utils.ResponseError("用户类型不能为空", 401)
		return
	}
	if req.RoleID == 0 {
		utils.ResponseError("角色ID不能为空", 401)
		return
	}

	err := c.permissionService.AssignRole(context.Background(), req)
	if err != nil {
		utils.ResponseError("分配角色失败", 402)
		return
	}

	result.OK(c.Ctx, nil)
}

// AssignPermissions 分配权限
// @Title 分配权限
// @Description 分配权限
// @Param   role_id         body    int64   true    "角色ID"
// @Param   permission_ids  body    []int64 true    "权限ID列表"
// @Success 200 {object} utils.Response
// @router /roles/:id/permissions [put]
func (c *PermissionController) AssignPermissions() {
	id, err := strconv.ParseInt(c.Ctx.Input.Param(":id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	req := &dto.AssignPermissionRequest{
		RoleID: id,
	}
	if err := c.ParseForm(req); err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	// 参数验证
	if len(req.PermissionIDs) == 0 {
		utils.ResponseError("权限ID列表不能为空", 401)
		return
	}

	err = c.permissionService.AssignPermissions(context.Background(), req)
	if err != nil {
		utils.ResponseError("分配权限失败", 402)
		return
	}

	result.OK(c.Ctx, nil)
}

// GetUserPermissions 获取用户权限列表
// @Title 获取用户权限列表
// @Description 获取用户权限列表
// @Param   user_id     query   int64   true    "用户ID"
// @Param   user_type   query   string  true    "用户类型"
// @Success 200 {object} utils.Response
// @router /users/:user_id/permissions [get]
func (c *PermissionController) GetUserPermissions() {
	userID, err := strconv.ParseInt(c.Ctx.Input.Query("user_id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	userType := c.Ctx.Input.Query("user_type")
	if userType == "" {
		utils.ResponseError("用户类型不能为空", 401)
		return
	}

	permissions, err := c.permissionService.GetUserPermissions(context.Background(), userID, userType)
	if err != nil {
		utils.ResponseError("获取用户权限列表失败", 402)
		return
	}

	result.OK(c.Ctx, permissions)
}

// GetUserRoles 获取用户角色列表
// @Title 获取用户角色列表
// @Description 获取用户角色列表
// @Param   user_id     query   int64   true    "用户ID"
// @Param   user_type   query   string  true    "用户类型"
// @Success 200 {object} utils.Response
// @router /users/:user_id/roles [get]
func (c *PermissionController) GetUserRoles() {
	userID, err := strconv.ParseInt(c.Ctx.Input.Query("user_id"), 10, 64)
	if err != nil {
		utils.ResponseError("参数解析失败", 400)
		return
	}

	userType := c.Ctx.Input.Query("user_type")
	if userType == "" {
		utils.ResponseError("用户类型不能为空", 401)
		return
	}

	roles, err := c.permissionService.GetUserRoles(context.Background(), userID, userType)
	if err != nil {
		utils.ResponseError("获取用户角色列表失败", 402)
		return
	}

	result.OK(c.Ctx, roles)
}

// GetCurrentUserPermissions 获取当前用户权限
// @Title 获取当前用户权限
// @Description 获取当前用户或商户的权限列表
// @Success 200 {object} utils.Response
// @router /current/permissions [get]
func (c *PermissionController) GetCurrentUserPermissions() {
	token := c.Ctx.Input.Header("Authorization")
	userType := c.Ctx.Input.Header("User-Type")
	if token == "" {
		utils.ResponseError("未提供token", 401)
		return
	}
	claims, err := utils.ParseToken(token)
	if err != nil {
		utils.ResponseError("token无效", 401)
		return
	}
	userID := claims.UserID
	permissions, err := c.permissionService.GetUserPermissions(context.Background(), userID, userType)
	if err != nil {
		utils.ResponseError("获取用户权限列表失败", 402)
		return
	}
	result.OK(c.Ctx, permissions)
}

// GetCurrentUserRoles 获取当前用户角色
// @Title 获取当前用户角色
// @Description 获取当前用户或商户的角色列表
// @Success 200 {object} utils.Response
// @router /current/roles [get]
func (c *PermissionController) GetCurrentUserRoles() {
	token := c.Ctx.Input.Header("Authorization")
	userType := c.Ctx.Input.Header("User-Type")
	if token == "" {
		utils.ResponseError("未提供token", 401)
		return
	}
	claims, err := utils.ParseToken(token)
	if err != nil {
		utils.ResponseError("token无效", 401)
		return
	}
	userID := claims.UserID
	roles, err := c.permissionService.GetUserRoles(context.Background(), userID, userType)
	if err != nil {
		utils.ResponseError("获取用户角色列表失败", 402)
		return
	}
	result.OK(c.Ctx, roles)
}
