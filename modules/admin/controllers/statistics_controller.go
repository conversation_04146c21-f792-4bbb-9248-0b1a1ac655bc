/**
 * 统计数据控制器
 *
 * 该文件实现了系统统计数据的API接口，包括用户、商家、商品、订单等数据的统计。
 * 主要用于管理员后台面板显示系统概览信息。
 */

package controllers

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/merchant/models"
	orderModels "o_mall_backend/modules/order/models"
	productModels "o_mall_backend/modules/product/models"
	takeoutFoodModels "o_mall_backend/modules/takeout/models"
	userModels "o_mall_backend/modules/user/models"
)

// StatisticsController 统计数据控制器
type StatisticsController struct {
	web.Controller
}

// GetStatistics 获取系统统计数据
// @Title GetStatistics
// @Description 获取系统统计数据，包括用户数、商家数、商品数、订单数等
// @Success 200 {object} dto.StatisticsResponse "获取成功"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @router /statistics [get]
func (c *StatisticsController) GetStatistics() {
	var resp dto.StatisticsResponse
	o := orm.NewOrm()

	// 1. 获取总用户数
	userCount, err := o.QueryTable(new(userModels.User)).Count()
	if err != nil {
		logs.Error("获取总用户数失败: %v", err)
		resp.Code = 500
		resp.Message = "获取统计数据失败"
		c.Data["json"] = resp
		c.ServeJSON()
		return
	}

	// 2. 获取总商家数
	merchantCount, err := o.QueryTable(new(models.Merchant)).Count()
	if err != nil {
		logs.Error("获取总商家数失败: %v", err)
		resp.Code = 500
		resp.Message = "获取统计数据失败"
		c.Data["json"] = resp
		c.ServeJSON()
		return
	}

	// 3. 获取总商品数
	productCount, err := o.QueryTable(new(productModels.Product)).Count()
	if err != nil {
		logs.Error("获取总商品数失败: %v", err)
		resp.Code = 500
		resp.Message = "获取统计数据失败"
		c.Data["json"] = resp
		c.ServeJSON()
		return
	}

	// 4. 获取总订单数
	orderCount, err := o.QueryTable(new(orderModels.Order)).Count()
	if err != nil {
		logs.Error("获取总订单数失败: %v", err)
		resp.Code = 500
		resp.Message = "获取统计数据失败"
		c.Data["json"] = resp
		c.ServeJSON()
		return
	}

	// 5. 获取今日订单数
	today := time.Now().Format("2006-01-02")
	todayStart, _ := time.Parse("2006-01-02", today)
	todayEnd := todayStart.Add(24 * time.Hour)

	todayOrderCount, err := o.QueryTable(new(orderModels.Order)).
		Filter("created_at__gte", todayStart).
		Filter("created_at__lt", todayEnd).
		Count()
	if err != nil {
		logs.Error("获取今日订单数失败: %v", err)
		resp.Code = 500
		resp.Message = "获取统计数据失败"
		c.Data["json"] = resp
		c.ServeJSON()
		return
	}

	// 6. 获取今日销售额
	todaySalesAmount := 0.0
	
	// 获取今日已付款的订单
	var orders []*orderModels.Order
	_, err = o.QueryTable(new(orderModels.Order)).
		Filter("created_at__gte", todayStart).
		Filter("created_at__lt", todayEnd).
		Filter("status__in", 20, 30, 40). // 已付款(待发货)、待收货、已完成
		All(&orders)
	
	if err != nil {
		logs.Error("获取今日订单失败: %v", err)
	} else {
		// 手动计算总金额
		for _, order := range orders {
			todaySalesAmount += order.TotalAmount
		}
	}

	// 7. 获取总外卖食品数
	takeoutFoodCount, err := o.QueryTable(new(takeoutFoodModels.TakeoutFood)).Count()
	if err != nil {
		logs.Error("获取总外卖食品数失败: %v", err)
		takeoutFoodCount = 0
	}

	// 8. 获取总外卖订单数
	takeoutOrderCount, err := o.QueryTable(new(takeoutFoodModels.TakeoutOrderExtension)).Count()
	if err != nil {
		logs.Error("获取总外卖订单数失败: %v", err)
		takeoutOrderCount = 0
	}

	// 9. 获取今日外卖订单数
	todayTakeoutOrderCount, err := o.QueryTable(new(takeoutFoodModels.TakeoutOrderExtension)).
		Filter("created_at__gte", todayStart).
		Filter("created_at__lt", todayEnd).
		Count()
	if err != nil {
		logs.Error("获取今日外卖订单数失败: %v", err)
		todayTakeoutOrderCount = 0
	}

	// 10. 获取今日外卖销售额
	todayTakeoutSalesAmount := 0.0

	// 获取外卖订单ID
	var takeoutOrderExtensions []*takeoutFoodModels.TakeoutOrderExtension
	_, err = o.QueryTable(new(takeoutFoodModels.TakeoutOrderExtension)).
		Filter("created_at__gte", todayStart).
		Filter("created_at__lt", todayEnd).
		All(&takeoutOrderExtensions)

	if err != nil {
		logs.Error("获取今日外卖订单失败: %v", err)
	} else if len(takeoutOrderExtensions) > 0 {
		// 获取对应的订单ID列表
		orderIDs := make([]int64, len(takeoutOrderExtensions))
		for i, ext := range takeoutOrderExtensions {
			orderIDs[i] = ext.OrderID
		}

		// 查询这些订单的销售额
		var takeoutOrders []*orderModels.Order
		// 将int64切片转换为接口切片
		orderIDsInterface := make([]interface{}, len(orderIDs))
		for i, id := range orderIDs {
			orderIDsInterface[i] = id
		}
		_, err = o.QueryTable(new(orderModels.Order)).
			Filter("id__in", orderIDsInterface...).
			Filter("status__in", 20, 30, 40). // 已付款(待发货)、待收货、已完成
			All(&takeoutOrders)

		if err != nil {
			logs.Error("获取外卖订单详情失败: %v", err)
		} else {
			// 手动计算外卖订单总金额
			for _, order := range takeoutOrders {
				todayTakeoutSalesAmount += order.TotalAmount
			}
		}
	}

	// 构建响应数据
	resp.Code = 200
	resp.Message = "获取统计数据成功"
	resp.Data = dto.StatisticsData{
		UserCount:              userCount,
		MerchantCount:          merchantCount,
		ProductCount:           productCount,
		OrderCount:             orderCount,
		TodayOrderCount:        todayOrderCount,
		TodaySalesAmount:       todaySalesAmount,
		TakeoutFoodCount:       takeoutFoodCount,
		TakeoutOrderCount:      takeoutOrderCount,
		TodayTakeoutOrderCount: todayTakeoutOrderCount,
		TodayTakeoutSalesAmount: todayTakeoutSalesAmount,
	}

	c.Data["json"] = resp
	c.ServeJSON()
}
