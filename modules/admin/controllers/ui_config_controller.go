/**
 * UIConfig 管理控制器
 *
 * 该文件实现了管理员模块下UI配置相关的控制器功能
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils/common"
)

// UIConfigController UI配置管理控制器
type UIConfigController struct {
	web.Controller
	uiConfigService services.AdminUIConfigService
	gridInfoService services.AdminGridInfoService
}

// Prepare 初始化控制器
func (c *UIConfigController) Prepare() {
	// 初始化服务
	c.uiConfigService = services.NewAdminUIConfigService()
	c.gridInfoService = services.NewAdminGridInfoService()
}

// ParseRequest 通用请求参数解析方法
func (c *UIConfigController) ParseRequest(req interface{}) error {
	return common.ParseFormRequest(c.Ctx, req)
}

// List 获取UI配置列表
// @Title List
// @Description 获取UI配置列表
// @Param module query string false "模块名称"
// @Param config_type query string false "配置类型"
// @Param title query string false "配置标题"
// @Param status query int false "状态：1-启用，0-禁用"
// @Param group query string false "配置分组"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [get]
func (c *UIConfigController) List() {
	var queryDTO dto.AdminUIConfigQueryDTO
	if err := c.ParseRequest(&queryDTO); err != nil {
		logs.Error("[List] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置默认分页参数
	if queryDTO.Page <= 0 {
		queryDTO.Page = 1
	}
	if queryDTO.PageSize <= 0 {
		queryDTO.PageSize = 10
	}

	// 使用管理员模块的服务获取UI配置列表
	configs, total, err := c.uiConfigService.ListUIConfigs(c.Ctx.Request.Context(), &queryDTO)
	if err != nil {
		logs.Error("[List] 获取UI配置列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回分页数据
	result.OKWithPagination(c.Ctx, configs, total, queryDTO.Page, queryDTO.PageSize)
}

// Get 获取UI配置详情
// @Title Get
// @Description 获取UI配置详情
// @Param id path int true "配置ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id [get]
func (c *UIConfigController) Get() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[Get] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务获取UI配置详情（包含网格布局项目）
	config, err := c.uiConfigService.GetUIConfig(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[Get] 获取UI配置详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	if config == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, config)
}

// Create 创建UI配置
// @Title Create
// @Description 创建UI配置
// @Param body body dto.AdminUIConfigCreateDTO true "创建参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [post]
func (c *UIConfigController) Create() {
	var createDTO dto.AdminUIConfigCreateDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &createDTO); err != nil {
		logs.Error("[Create] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务创建UI配置
	id, err := c.uiConfigService.CreateUIConfig(c.Ctx.Request.Context(), &createDTO)
	if err != nil {
		logs.Error("[Create] 创建UI配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{"id": id})
}

// Update 更新UI配置
// @Title Update
// @Description 更新UI配置
// @Param id path int true "配置ID"
// @Param body body dto.AdminUIConfigUpdateDTO true "更新参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id [put]
func (c *UIConfigController) Update() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[Update] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	var updateDTO dto.AdminUIConfigUpdateDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &updateDTO); err != nil {
		logs.Error("[Update] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务更新UI配置
	err = c.uiConfigService.UpdateUIConfig(c.Ctx.Request.Context(), id, &updateDTO)
	if err != nil {
		logs.Error("[Update] 更新UI配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, nil)
}

// Delete 删除UI配置
// @Title Delete
// @Description 删除UI配置
// @Param id path int true "配置ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id [delete]
func (c *UIConfigController) Delete() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[Delete] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务删除UI配置
	err = c.uiConfigService.DeleteUIConfig(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[Delete] 删除UI配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, nil)
}

// UpdateStatus 更新UI配置状态
// @Title UpdateStatus
// @Description 更新UI配置状态
// @Param id path int true "配置ID"
// @Param body body dto.AdminUIConfigStatusUpdateDTO true "状态更新参数"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id/status [put]
func (c *UIConfigController) UpdateStatus() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[UpdateStatus] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	var statusDTO dto.AdminUIConfigStatusUpdateDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &statusDTO); err != nil {
		logs.Error("[UpdateStatus] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用管理员模块的服务更新UI配置状态
	err = c.uiConfigService.UpdateUIConfigStatus(c.Ctx.Request.Context(), id, statusDTO.Status)
	if err != nil {
		logs.Error("[UpdateStatus] 更新UI配置状态失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	result.OK(c.Ctx, nil)
}

// ListGridInfos 获取UI配置关联的网格布局列表
// @Title ListGridInfos
// @Description 获取UI配置关联的网格布局列表
// @Param id path int true "UI配置ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /:id/grid-infos [get]
func (c *UIConfigController) ListGridInfos() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[ListGridInfos] 无效的ID: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用网格布局服务获取UI配置关联的网格布局列表
	gridItems, err := c.gridInfoService.ListGridInfosByUIConfigID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[ListGridInfos] 获取UI配置关联的网格布局列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, gridItems)
}
