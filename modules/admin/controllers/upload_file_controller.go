/*
 * 文件上传控制器
 *
 * 该文件处理文件上传相关的HTTP请求，基于通用上传控制器实现。
 * 提供admin模块专用的文件上传功能，同时保持向后兼容性。
 */

package controllers

import (
	"github.com/beego/beego/v2/server/web"
	adminServices "o_mall_backend/modules/admin/services"
	commonControllers "o_mall_backend/common/controllers"
)

// UploadFileController 文件上传控制器
type UploadFileController struct {
	web.Controller
	uploadFileService adminServices.UploadFileService
	commonUpload     *commonControllers.CommonUploadController
}

// Prepare 初始化准备
func (c *UploadFileController) Prepare() {
	c.uploadFileService = adminServices.NewUploadFileService()
	c.commonUpload = commonControllers.NewCommonUploadController("admin")
	// 设置通用控制器的上下文
	c.commonUpload.Ctx = c.Ctx
}

// Upload 文件上传
// @Title 文件上传
// @Description 上传文件
// @Param file formData file true "上传的文件"
// @Param file_usage formData string true "文件用途"
// @Param is_anonymous formData bool false "是否匿名上传"
// @Success 200 {object} adminDto.UploadFileResponse
// @Failure 400 {object} response.ErrorResponse
// @router /upload [post]
func (c *UploadFileController) Upload() {
	// 委托给通用上传控制器处理
	c.commonUpload.Upload()
}

// GetFile 获取文件信息
// @Title 获取文件信息
// @Description 根据文件ID获取文件详细信息
// @Param id path int true "文件ID"
// @Success 200 {object} adminDto.UploadFileResponse
// @Failure 400 {object} response.ErrorResponse
// @router /:id [get]
func (c *UploadFileController) GetFile() {
	// 委托给通用上传控制器处理
	c.commonUpload.GetFile()
}

// List 获取文件列表
// @Title 获取文件列表
// @Description 根据条件查询文件列表
// @Param file_usage query string false "文件用途"
// @Param user_type query string false "上传者类型"
// @Param user_id query int false "上传者ID"
// @Param is_anonymous query bool false "是否匿名上传"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} adminDto.UploadFileListResponse
// @Failure 400 {object} response.ErrorResponse
// @router /list [get]
func (c *UploadFileController) List() {
	// 委托给通用上传控制器处理
	c.commonUpload.List()
}

// Delete 删除文件
// @Title 删除文件
// @Description 根据文件ID删除文件
// @Param id path int true "文件ID"
// @Success 200 {object} response.SuccessResponse
// @Failure 400 {object} response.ErrorResponse
// @router /:id [delete]
func (c *UploadFileController) Delete() {
	// 委托给通用上传控制器处理
	c.commonUpload.Delete()
}

// GetConfig 获取上传配置
// @Title 获取上传配置
// @Description 获取上传配置信息
// @Success 200 {object} adminDto.UploadConfigResponse
// @Failure 400 {object} response.ErrorResponse
// @router /config [get]
func (c *UploadFileController) GetConfig() {
	// 委托给通用上传控制器处理
	c.commonUpload.GetConfig()
}

// IsAdmin 判断当前用户是否为管理员
func (c *UploadFileController) IsAdmin() bool {
	// 获取当前用户角色
	userRole := c.Ctx.Input.GetData("role")
	if userRole == nil {
		return false
	}

	role, ok := userRole.(string)
	if !ok {
		return false
	}

	return role == "admin" || role == "super"
}
