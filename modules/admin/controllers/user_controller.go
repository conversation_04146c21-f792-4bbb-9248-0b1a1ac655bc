/**
 * 用户管理控制器
 *
 * 该文件实现了管理员模块中的用户管理相关API接口。
 * 包括用户查询、更新、状态管理等功能。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	adminDto "o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/services"
	"o_mall_backend/utils/common"
	"github.com/beego/beego/v2/core/logs"
)

// UserController 用户管理控制器
type UserController struct {
	web.Controller
	userService services.AdminUserService
}

// Prepare 初始化控制器
func (c *UserController) Prepare() {
	c.userService = services.NewAdminUserService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *UserController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// CreateUser 创建新用户
// @Title 创建新用户
// @Description 创建一个新的用户账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body adminDto.CreateUserRequest true "用户信息"
// @Success 200 {object} result.Response{data=int64} "成功，返回用户ID"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/users [post]
func (c *UserController) CreateUser() {
	// 解析请求参数
	var req adminDto.CreateUserRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层创建用户
	id, err := c.userService.CreateUser(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, id)
}

// ListUsers 获取用户列表
// @Title 获取用户列表
// @Description 分页获取用户列表，支持按用户名、手机号、邮箱和状态筛选
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param username query string false "用户名"
// @Param mobile query string false "手机号"
// @Param email query string false "邮箱"
// @Param status query int false "状态：0-禁用，1-正常"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} result.Response{data=[]adminDto.UserResponse} "成功"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/users [get]
func (c *UserController) ListUsers() {
	// 解析查询参数
	var req adminDto.UserQueryRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 调用服务层获取用户列表
	users, total, err := c.userService.ListUsers(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OKWithPagination(c.Ctx, users, total, req.Page, req.PageSize)
}

// GetUser 获取用户详情
// @Title 获取用户详情
// @Description 根据用户ID获取用户详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} result.Response{data=adminDto.UserResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "用户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/users/{id} [get]
func (c *UserController) GetUser() {
	// 获取用户ID
	userID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层获取用户信息
	user, err := c.userService.GetUserByID(c.Ctx.Request.Context(), userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, user)
}

// UpdateUser 更新用户信息
// @Title 更新用户信息
// @Description 更新用户的基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param body body adminDto.UpdateUserRequest true "用户信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "用户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/users/{id} [put]
func (c *UserController) UpdateUser() {
	// 解析请求体
	var req adminDto.UpdateUserRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取用户ID
	userID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	req.ID = userID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层更新用户信息
	err = c.userService.UpdateUser(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DisableUser 禁用用户
// @Title 禁用用户
// @Description 禁用指定用户的账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "用户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/users/{id}/disable [put]
func (c *UserController) DisableUser() {
	// 获取用户ID
	userID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层禁用用户
	err = c.userService.DisableUser(c.Ctx.Request.Context(), userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// EnableUser 启用用户
// @Title 启用用户
// @Description 启用指定用户的账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "用户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/users/{id}/enable [put]
func (c *UserController) EnableUser() {
	// 获取用户ID
	userID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务层启用用户
	err = c.userService.EnableUser(c.Ctx.Request.Context(), userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateUserStatus 更新用户状态
// @Title 更新用户状态
// @Description 更新指定用户的账号状态（启用/禁用）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param body body adminDto.UpdateUserStatusRequest true "状态信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "用户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/users/{id}/status [put]
func (c *UserController) UpdateUserStatus() {
	// 解析请求体
	var req adminDto.UpdateUserStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取用户ID
	userID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	req.ID = userID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层更新用户状态
	err = c.userService.UpdateUserStatus(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// ResetPassword 重置用户密码
// @Title 重置用户密码
// @Description 重置指定用户的登录密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param body body adminDto.ResetUserPasswordRequest true "新密码信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "用户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/secured/users/{id}/password [put]
func (c *UserController) ResetPassword() {
	// 解析请求体
	var req adminDto.ResetUserPasswordRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取用户ID
	userID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	req.ID = userID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层重置密码
	err = c.userService.ResetUserPassword(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateUserBalanceAndPoints 更新用户余额和积分
// @Title 更新用户余额和积分
// @Description 更新用户的账户余额和积分
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param body body adminDto.UpdateUserBalanceAndPointsRequest true "余额和积分信息"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "用户不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /api/v1/admin/secured/users/{id}/balance-points [put]
func (c *UserController) UpdateUserBalanceAndPoints() {
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 开始处理请求")

	// 解析请求体
	var req adminDto.UpdateUserBalanceAndPointsRequest
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 准备解析请求体")
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("[UserController] UpdateUserBalanceAndPoints: 解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 请求体解析成功: %+v", req)

	// 获取用户ID
	userID, err := c.GetInt64(":id")
	if err != nil {
		logs.Error("[UserController] UpdateUserBalanceAndPoints: 获取用户ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	req.ID = userID
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 获取到用户ID: %d", userID)

	// 手动参数校验
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 准备手动校验参数: %+v", req)
	
	// 验证 Balance 必须大于等于 0
	if req.Balance < 0 {
		logs.Error("[UserController] UpdateUserBalanceAndPoints: Balance 必须大于等于 0，当前值: %.2f", req.Balance)
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "账户余额必须大于等于0"))
		return
	}
	
	// 验证 Points 必须大于等于 0（虽然有 valid:"Min(0)" 标签，但为了完整性也手动检查）
	if req.Points < 0 {
		logs.Error("[UserController] UpdateUserBalanceAndPoints: Points 必须大于等于 0，当前值: %d", req.Points)
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "积分必须大于等于0"))
		return
	}
	
	// 其他验证规则可以根据需要添加
	
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 参数校验成功")

	// 调用服务层更新余额和积分
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 准备调用服务层 UpdateUserBalanceAndPoints, 请求: %+v", req)
	err = c.userService.UpdateUserBalanceAndPoints(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[UserController] UpdateUserBalanceAndPoints: 服务层返回错误: %v", err)
		result.HandleError(c.Ctx, err) // 使用服务层返回的原始错误
		return
	}
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 服务层处理成功")

	// 返回成功响应
	result.OK(c.Ctx, nil) // 通常更新操作成功后不返回特定数据体，或返回更新后的资源（可选）
	logs.Info("[UserController] UpdateUserBalanceAndPoints: 请求处理完毕，返回成功响应")
}
