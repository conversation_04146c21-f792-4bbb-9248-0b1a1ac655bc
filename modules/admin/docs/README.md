# 管理员模块 (Admin Module)

## 模块概述

管理员模块是O_Mall多商家电商平台的核心管理后台组件，提供了平台管理员、用户管理、商户管理等功能，是整个平台的管理中心。管理员可以通过此模块进行平台的各项管理工作，确保平台的正常运行和业务发展。

## 模块结构

管理员模块采用标准的MVC架构，结构清晰，便于维护和扩展：

```
modules/admin/
├── constants/     # 常量定义
├── controllers/   # 控制器层，处理HTTP请求
│   ├── admin_controller.go        # 管理员控制器
│   ├── admin_log_controller.go    # 管理员日志控制器
│   ├── merchant_controller.go     # 商户管理控制器
│   └── user_controller.go         # 用户管理控制器
├── dto/           # 数据传输对象
│   ├── admin_dto.go               # 管理员相关DTO
│   ├── admin_log.go               # 管理员日志相关DTO
│   ├── merchant_dto.go            # 商户相关DTO
│   └── user_dto.go                # 用户相关DTO
├── models/        # 数据模型
│   ├── admin.go                   # 管理员模型
│   └── admin_log.go               # 管理员日志模型
├── repositories/  # 数据访问层
│   ├── admin_repository.go        # 管理员数据仓库
│   └── admin_log_repository.go    # 管理员日志数据仓库
├── services/      # 业务逻辑层
│   ├── admin_service.go                   # 管理员服务实现
│   ├── admin_service_interface.go         # 管理员服务接口
│   ├── admin_log_service.go               # 管理员日志服务实现
│   ├── admin_user_service.go              # 用户管理服务实现
│   ├── admin_user_service_interface.go    # 用户管理服务接口
│   ├── admin_merchant_service.go          # 商户管理服务实现
│   ├── admin_merchant_service_interface.go # 商户管理服务接口
│   └── interface.go                       # 基础服务接口
├── routers/       # 路由定义
│   └── router.go               # 路由配置
└── init.go        # 模块初始化
```

## 核心功能

### 1. 管理员管理

- **管理员认证**：登录、登出、令牌刷新
- **管理员信息管理**：获取/更新管理员信息
- **密码管理**：修改密码、重置密码
- **管理员CRUD**：创建、查询、更新、删除管理员
- **管理员状态管理**：启用/禁用管理员

### 2. 用户管理

- **用户查询**：按条件分页查询用户列表
- **用户详情**：获取用户详细信息
- **用户创建**：创建新用户账号
- **用户更新**：更新用户基本信息
- **用户状态管理**：启用/禁用用户
- **密码重置**：重置用户登录密码

### 3. 商户管理

- **商户查询**：按条件分页查询商户列表
- **商户详情**：获取商户详细信息
- **商户更新**：更新商户基本信息
- **商户状态管理**：启用/禁用商户
- **商户审核**：审核商户入驻申请
- **商户创建**：平台创建商户账号
- **密码重置**：重置商户登录密码

### 4. 管理员日志

- **日志记录**：自动记录管理员的登录、登出、操作等行为
- **日志查询**：支持按条件查询操作日志
- **日志详情**：查看日志详细信息
- **最近日志**：获取当前管理员的最近操作记录
- **安全审计**：提供安全审计和追踪功能

## API接口文档

### 管理员认证接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/login` | POST | 管理员登录 |
| `/api/v1/admin/refresh-token` | POST | 刷新访问令牌 |
| `/api/v1/admin/secured/logout` | POST | 管理员登出 |

### 管理员管理接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/secured/info` | GET | 获取管理员信息 |
| `/api/v1/admin/secured/info` | PUT | 更新管理员信息 |
| `/api/v1/admin/secured/password` | PUT | 修改管理员密码 |
| `/api/v1/admin/secured/admins` | POST | 创建管理员 |
| `/api/v1/admin/secured/admins` | GET | 获取管理员列表 |
| `/api/v1/admin/secured/admins/:id` | GET | 获取管理员详情 |
| `/api/v1/admin/secured/admins/:id` | PUT | 更新管理员信息 |
| `/api/v1/admin/secured/admins/:id` | DELETE | 删除管理员 |
| `/api/v1/admin/secured/admins/:id/disable` | PUT | 禁用管理员 |
| `/api/v1/admin/secured/admins/:id/enable` | PUT | 启用管理员 |
| `/api/v1/admin/secured/admins/:id/password` | PUT | 重置管理员密码 |
| `/api/v1/admin/count` | GET | 获取管理员数量 |
| `/api/v1/admin/initadmin` | POST | 初始化管理员 |

### 用户管理接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/secured/users` | GET | 获取用户列表 |
| `/api/v1/admin/secured/users` | POST | 创建新用户 |
| `/api/v1/admin/secured/users/:id` | GET | 获取用户详情 |
| `/api/v1/admin/secured/users/:id` | PUT | 更新用户信息 |
| `/api/v1/admin/secured/users/:id/disable` | PUT | 禁用用户 |
| `/api/v1/admin/secured/users/:id/enable` | PUT | 启用用户 |
| `/api/v1/admin/secured/users/:id/password` | PUT | 重置用户密码 |

### 商户管理接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/secured/merchants` | GET | 获取商户列表 |
| `/api/v1/admin/secured/merchants/add` | POST | 创建新商户 |
| `/api/v1/admin/secured/merchants/:id` | GET | 获取商户详情 |
| `/api/v1/admin/secured/merchants/:id` | PUT | 更新商户信息 |
| `/api/v1/admin/secured/merchants/:id/disable` | PUT | 禁用商户 |
| `/api/v1/admin/secured/merchants/:id/enable` | PUT | 启用商户 |
| `/api/v1/admin/secured/merchants/:id/password` | PUT | 重置商户密码 |
| `/api/v1/admin/secured/merchants/:id/audit` | PUT | 审核商户入驻申请 |

### 管理员日志接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/secured/logs/list` | GET | 获取管理员日志列表 |
| `/api/v1/admin/secured/logs/:id` | GET | 获取日志详情 |
| `/api/v1/admin/secured/logs/latest` | GET | 获取当前管理员最近的操作日志 |

## 用法示例

### 管理员登录

```bash
curl -X POST http://localhost:8080/api/v1/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}'
```

### 获取管理员日志

```bash
curl -X GET http://localhost:8080/api/v1/admin/secured/logs/list \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json"
```

## 安全与权限

管理员模块采用JWT (JSON Web Token) 进行认证和授权：

1. **认证流程**：
   - 管理员通过用户名和密码登录获取令牌对（Access Token和Refresh Token）
   - 使用Access Token访问需要认证的API
   - Access Token过期后使用Refresh Token获取新的令牌对
   - 登出时撤销所有令牌

2. **安全特性**：
   - 令牌黑名单机制，确保已登出的令牌无法再被使用
   - 多设备登录控制
   - 访问控制，确保管理员只能访问其权限范围内的资源

## 集成与扩展

### 与其他模块的集成

管理员模块主要与以下模块进行交互：

- **用户模块**：管理用户信息、账户状态等
- **商户模块**：管理商户信息、审核商户申请等
- **权限模块**：管理角色和权限分配

### 扩展指南

要扩展管理员模块的功能，请遵循以下步骤：

1. **添加新的数据传输对象 (DTO)**：
   - 在 `dto/` 目录下定义新的请求和响应结构

2. **扩展服务接口和实现**：
   - 在 `services/` 目录下的相应接口中添加新方法
   - 在服务实现类中实现新方法

3. **添加控制器方法**：
   - 在 `controllers/` 目录下的控制器中添加新方法处理HTTP请求

4. **注册路由**：
   - 在 `routers/router.go` 中注册新的路由

5. **更新文档**：
   - 更新本README文档，添加新的API接口说明

## 日志与监控

管理员模块的所有关键操作都会记录日志，便于审计和故障排查：

- **登录/登出记录**：记录管理员的登录和登出时间、IP地址等信息
- **敏感操作日志**：记录管理员执行的敏感操作，如创建/禁用用户、重置密码等
- **错误日志**：记录系统异常和错误，便于排查问题
- **性能监控**：监控API响应时间和资源使用情况

## 错误处理

管理员模块采用统一的错误处理机制，所有接口返回标准的JSON格式，错误码和消息统一管理：

```json
{
  "code": 400,       // 错误码
  "message": "参数错误"  // 错误消息
}
```

常见错误码：

- 400：参数错误或请求格式错误
- 401：未授权，需要登录
- 403：禁止访问，无权限
- 404：资源不存在
- 500：服务器内部错误

## 最佳实践

1. **API访问**：
   - 所有需要认证的API请求都应在Header中包含有效的JWT令牌
   - 令牌失效时应自动使用Refresh Token获取新令牌

2. **错误处理**：
   - 前端应根据错误码进行相应处理
   - 对于401错误，应重定向到登录页面
   - 对于403错误，应显示权限不足提示

3. **安全建议**：
   - 管理员密码应使用强密码策略
   - 定期更换密码和令牌密钥
   - 敏感操作应添加二次确认机制

## 贡献指南

要为管理员模块贡献代码，请参考以下指南：

1. 确保代码符合项目的编码规范
2. 为新功能编写完善的单元测试
3. 更新相关文档，包括本README文件
4. 提交前使用`go fmt`和`go vet`检查代码
5. 提交PR前先在本地测试功能是否正常