# 获取管理员信息

## 接口说明

该接口用于获取当前登录管理员的详细信息。

## 请求信息

- 请求路径：`/api/v1/admin/secured/info`
- 请求方法：`GET`
- 认证要求：需要认证

## 请求参数

无请求参数，通过Token识别当前管理员。

## 响应信息

### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | object | 响应数据 |
| data.id | int | 管理员ID |
| data.username | string | 管理员用户名 |
| data.nickname | string | 管理员昵称 |
| data.avatar | string | 管理员头像URL |
| data.mobile | string | 管理员手机号 |
| data.email | string | 管理员邮箱 |
| data.role | string | 管理员角色 |
| data.permissions | array | 管理员权限列表 |
| data.status | int | 管理员状态，1-正常，0-禁用 |
| data.last_login_at | string | 最后登录时间 |
| data.created_at | string | 创建时间 |

### 成功响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "超级管理员",
    "avatar": "https://example.com/avatar.jpg",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "role": "super",
    "permissions": ["*"],
    "status": 1,
    "last_login_at": "2023-01-01T12:00:00Z",
    "created_at": "2022-01-01T00:00:00Z"
  }
}
```

### 错误响应示例

```json
{
  "code": 401,
  "message": "未认证的请求",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 401 | 未认证 |
| 500 | 服务器内部错误 |