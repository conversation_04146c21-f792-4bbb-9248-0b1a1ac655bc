# AI功能使用指南

**文档版本**: 1.0.0
**更新日期**: 2025-04-09
**适用版本**: o_mall_backend v1.0+

## 目录

- [1. 功能概述](#1-功能概述)
- [2. AI配置管理](#2-ai配置管理)
  - [2.1 配置原理](#21-配置原理)
  - [2.2 配置参数说明](#22-配置参数说明)
  - [2.3 API接口说明](#23-api接口说明)
  - [2.4 使用示例](#24-使用示例)
- [3. UI生成器](#3-ui生成器)
  - [3.1 工作原理](#31-工作原理)
  - [3.2 基础模板使用](#32-基础模板使用)
  - [3.3 自定义模板高级功能](#33-自定义模板高级功能)
  - [3.4 API接口说明](#34-api接口说明)
  - [3.5 使用示例](#35-使用示例)
- [4. 聊天完成功能](#4-聊天完成功能)
  - [4.1 工作原理](#41-工作原理)
  - [4.2 API接口说明](#42-api接口说明)
  - [4.3 使用示例](#43-使用示例)
- [5. 故障排除](#5-故障排除)
- [6. 最佳实践](#6-最佳实践)

## 1. 功能概述

O-Mall后台系统集成了多项AI功能，主要包括：

1. **AI配置管理**：集中管理系统中使用的AI服务配置，包括API密钥、基础URL和默认模型等。
2. **UI生成器**：基于数据模型自动生成前端UI配置，支持多种UI框架和组件库。
3. **聊天完成功能**：提供基于大语言模型的对话功能，可用于客服、搜索增强等场景。

这些功能均基于DeepSeek AI API实现，通过统一的配置管理和服务调用，为系统提供智能化能力。

## 2. AI配置管理

### 2.1 配置原理

系统使用集中式的AI配置管理机制，所有配置存储在数据库的`system_ai_config`表中，通过缓存机制减少数据库查询，提高性能。

配置流程：
1. 系统启动时，从数据库加载AI配置
2. 如果配置不存在，则创建默认配置
3. 配置被缓存到内存中，默认过期时间为3600秒
4. 配置修改后，自动刷新缓存

### 2.2 配置参数说明

AI配置的主要参数包括：

| 参数名 | 说明 | 默认值 |
|-------|------|-------|
| APIKey | DeepSeek API密钥 | 从环境变量获取 |
| BaseURL | API基础URL | https://api.deepseek.com/v1 |
| DefaultModel | 默认使用的模型 | deepseek-chat |
| Provider | AI提供商标识 | deepseek |
| Enabled | 是否启用 | true |

### 2.3 API接口说明

#### 获取AI配置

```
GET /api/v1/admin/ai
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "deepseek_api_key": "sk-xxxx",
        "base_url": "https://api.deepseek.com/v1",
        "default_model": "deepseek-chat"
      }
    ],
    "total": 1
  }
}
```

#### 保存AI配置

```
POST /api/v1/admin/ai
```

请求体：
```json
{
  "deepseek_api_key": "sk-xxxx",
  "base_url": "https://api.deepseek.com/v1",
  "default_model": "deepseek-chat"
}
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": "保存AI配置成功"
}
```

#### 刷新AI配置缓存

```
POST /api/v1/admin/ai/refresh
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": "刷新AI配置缓存成功"
}
```

### 2.4 使用示例

```javascript
// 前端代码示例 - 获取配置
async function getAIConfig() {
  const response = await fetch('/api/v1/admin/ai', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
}

// 前端代码示例 - 保存配置
async function saveAIConfig(config) {
  const response = await fetch('/api/v1/admin/ai', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(config)
  });
  return response.json();
}
```

## 3. UI生成器

### 3.1 工作原理

UI生成器通过解析后端数据模型，调用DeepSeek AI API生成符合特定UI框架和组件库规范的前端配置。生成过程如下：

1. 接收原始数据模型和模板配置
2. 构建AI提示内容，包含模板指令和数据模型
3. 发送请求到DeepSeek AI API
4. 处理AI响应，提取JSON配置
5. 返回生成的UI配置给前端

支持的UI框架和组件库：
- Ant Design (React)
- Element UI (Vue)
- 自定义框架和组件库

### 3.2 基础模板使用

系统内置了三种基础模板：

1. **ProTable模板**：生成适用于Ant Design Pro的Table和Form配置
2. **Ant Design基础模板**：生成标准Ant Design组件配置
3. **Element UI模板**：生成Element UI框架的组件配置

使用基础模板时，只需提供数据模型和模板类型即可，系统会根据模板类型选择相应的预设模板。

### 3.3 自定义模板高级功能

自定义模板提供了更灵活的配置选项，可以根据需要调整模板内容、规则和格式要求等。主要参数包括：

| 参数名 | 说明 | 示例值 |
|-------|------|-------|
| ModelData | 原始数据模型 | JSON格式的数据模型定义 |
| Template | 自定义模板内容 | 基础模板指令 |
| PromptRules | 组件选择和布局规则 | "表格需要支持排序和过滤" |
| FormatSettings | 输出格式要求 | "必须是有效的JSON，包含特定结构" |
| Requirements | 功能和设计要求 | "支持响应式布局和暗黑模式" |
| Examples | 参考示例配置 | 示例JSON配置 |
| ComponentTypes | 使用的组件库 | "Ant Design" |
| UIFramework | UI框架 | "React" |
| SpecialFields | 特殊处理的字段列表 | ["avatar", "status"] |
| TemplateType | 模板类型 | "advanced" |

自定义模板支持多轮对话格式，可以通过如下语法定义不同角色的指令：

```
system:这是系统角色指令
user:这是用户角色指令
assistant:这是助手角色回复
```

### 3.4 API接口说明

#### 基础模板生成

```
POST /api/v1/system/ui/generate
```

请求体：
```json
{
  "model_data": "JSON格式的数据模型",
  "template_type": "ant_design"
}
```

#### 自定义模板生成

```
POST /api/v1/system/ui/generate/custom
```

请求体：
```json
{
  "model_data": "JSON格式的数据模型",
  "ui_framework": "React",
  "component_types": "Ant Design",
  "prompt_rules": "表格需要支持排序和过滤\n状态字段使用彩色标签展示",
  "format_settings": "输出必须是有效的JSON，包含columns、formItems和actions三个主要部分",
  "requirements": "支持响应式布局\n支持暗黑模式切换\n必须有表单验证",
  "examples": "以下是一个理想的配置示例：\n{...示例配置...}",
  "special_fields": ["avatar", "status", "createdAt"],
  "template_type": "advanced"
}
```

响应格式：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "config": {
      // 生成的UI配置
    },
    "status": "success",
    "raw_content": "AI原始响应内容"
  }
}
```

状态说明：
- success：成功生成JSON配置
- partial：未能完全解析为JSON
- raw：原始内容，未找到JSON

### 3.5 使用示例

#### 基础模板示例

```javascript
// 前端代码 - 使用基础模板
async function generateUIConfig(modelData) {
  const response = await fetch('/api/v1/system/ui/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      model_data: modelData,
      template_type: 'ant_design'
    })
  });
  return response.json();
}
```

#### 自定义模板示例

```javascript
// 前端代码 - 使用自定义模板
async function generateCustomUIConfig(params) {
  const response = await fetch('/api/v1/system/ui/generate/custom', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      model_data: params.modelData,
      ui_framework: 'React',
      component_types: 'Ant Design',
      prompt_rules: '表格需要支持排序和过滤\n状态字段使用彩色标签展示',
      requirements: '支持响应式布局\n必须有表单验证',
      special_fields: ['avatar', 'status']
    })
  });
  return response.json();
}
```

#### 多轮对话模板示例

```javascript
// 使用多轮对话格式的自定义模板
const template = `
system:你是专业的前端UI配置生成专家，擅长React和Ant Design

user:请根据以下数据模型生成ProTable配置
要求：
1. 支持分页和排序
2. 状态字段使用Badge组件
3. 图片字段支持预览

assistant:我会根据你的要求生成配置，请提供数据模型

user:我的数据模型如下
`;

async function generateWithConversation(modelData) {
  const response = await fetch('/api/v1/system/ui/generate/custom', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      model_data: modelData,
      template: template
    })
  });
  return response.json();
}
```

## 4. 聊天完成功能

### 4.1 工作原理

聊天完成功能基于DeepSeek AI API的聊天接口实现，支持多轮对话，可用于实现客服、搜索增强等场景。

工作流程：
1. 客户端发送聊天请求，包含消息历史
2. 服务器验证请求参数
3. 调用DeepSeek AI API获取回复
4. 返回AI回复给客户端

### 4.2 API接口说明

#### 聊天完成接口

```
POST /api/v1/system/chat/completion
```

请求体：
```json
{
  "messages": [
    {
      "role": "system",
      "content": "你是一个智能客服助手"
    },
    {
      "role": "user",
      "content": "如何查看我的订单状态？"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 1000
}
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "chat-123456",
    "object": "chat.completion",
    "created": 1712637046,
    "model": "deepseek-chat",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "您好！查看订单状态很简单，请按照以下步骤操作：\n\n1. 登录您的账户\n2. 点击右上角的\"我的订单\"\n3. 在订单列表中找到您想查询的订单\n4. 点击订单号可以查看详细状态\n\n如果您有任何问题，随时可以联系我们的客服团队。"
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 38,
      "completion_tokens": 102,
      "total_tokens": 140
    }
  }
}
```

### 4.3 使用示例

```javascript
// 前端代码 - 聊天完成
async function chatCompletion(messages) {
  const response = await fetch('/api/v1/system/chat/completion', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      messages: messages,
      temperature: 0.7,
      max_tokens: 1000
    })
  });
  return response.json();
}

// 使用示例
const messages = [
  {
    role: 'system',
    content: '你是一个专业的电商客服助手，提供简洁清晰的回答。'
  },
  {
    role: 'user',
    content: '如何更改我的收货地址？'
  }
];

chatCompletion(messages).then(response => {
  console.log(response.data.choices[0].message.content);
});
```

## 5. 故障排除

### 常见问题和解决方案

1. **API密钥无效或过期**
   - 检查AI配置中的API密钥是否正确
   - 在DeepSeek控制台检查密钥状态
   - 使用管理接口更新API密钥

2. **生成的UI配置格式错误**
   - 检查数据模型格式是否正确
   - 尝试使用更详细的FormatSettings指定输出格式
   - 使用Examples提供正确的示例配置

3. **接口响应超时**
   - 检查网络连接状态
   - 减少请求的max_tokens参数
   - 确保数据模型不过于复杂

4. **缓存数据不更新**
   - 调用刷新缓存接口(/api/v1/admin/ai/refresh)
   - 检查缓存配置是否正确
   - 重启应用服务器

### 日志排查

系统日志包含详细的AI调用信息，可以通过日志定位问题：

- 日志路径：`/var/log/o_mall/app.log`
- 日志级别：默认为Debug (7)
- 关键日志前缀：
  - `[AIServiceImpl]` - AI服务相关日志
  - `[UIGeneratorController]` - UI生成器相关日志

## 6. 最佳实践

### AI配置管理

1. **安全存储API密钥**
   - 不要在前端代码中硬编码API密钥
   - 使用环境变量或配置文件存储敏感信息
   - 定期轮换API密钥

2. **优化缓存配置**
   - 根据系统负载调整缓存过期时间
   - 监控缓存命中率，必要时进行调整
   - 在业务高峰期前预热缓存

### UI生成器

1. **数据模型优化**
   - 提供清晰的字段名称和注释
   - 包含字段类型和验证规则信息
   - 移除不需要展示的敏感字段

2. **模板设计**
   - 为不同场景准备专用模板
   - 使用多轮对话格式提供详细指导
   - 使用Examples展示期望的输出格式

3. **渐进式生成**
   - 先生成基础配置，再逐步细化
   - 保存成功的生成结果作为Examples
   - 通过SpecialFields指定需要特殊处理的字段

### 聊天完成

1. **系统提示优化**
   - 提供清晰的角色定义和行为约束
   - 包含业务背景和常见问题答案
   - 限定回答范围和格式

2. **对话历史管理**
   - 控制对话历史长度，避免超过token限制
   - 保留关键上下文信息
   - 定期总结对话历史，压缩token使用

3. **响应处理**
   - 设置合理的temperature参数控制回答随机性
   - 使用max_tokens限制回答长度
   - 实现流式响应提升用户体验
