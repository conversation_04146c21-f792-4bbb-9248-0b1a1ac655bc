# 创建管理员

## 接口说明

该接口用于创建新的管理员账号，仅超级管理员可以操作。

## 请求信息

- 请求路径：`/api/v1/admin/secured/`
- 请求方法：`POST`
- 认证要求：需要认证，且需要超级管理员权限

## 请求参数

### 请求体（JSON格式）

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| username | string | 是 | 管理员用户名 | "admin2" |
| password | string | 是 | 管理员密码 | "password123" |
| nickname | string | 是 | 管理员昵称 | "商品管理员" |
| mobile | string | 否 | 管理员手机号 | "13800138001" |
| email | string | 否 | 管理员邮箱 | "<EMAIL>" |
| role | string | 是 | 管理员角色 | "product" |
| permissions | array | 否 | 管理员权限列表 | ["product.view", "product.edit"] |

### 请求示例

```json
{
  "username": "admin2",
  "password": "password123",
  "nickname": "商品管理员",
  "mobile": "13800138001",
  "email": "<EMAIL>",
  "role": "product",
  "permissions": ["product.view", "product.edit"]
}
```

## 响应信息

### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | int | 新创建的管理员ID |

### 成功响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": 2
}
```

### 错误响应示例

```json
{
  "code": 400,
  "message": "用户名已存在",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 参数错误或用户名已存在 |
| 401 | 未认证 |
| 403 | 无权限 |
| 500 | 服务器内部错误 |