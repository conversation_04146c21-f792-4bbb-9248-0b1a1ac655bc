# 获取管理员列表

## 接口说明

该接口用于分页获取管理员列表，仅超级管理员可以操作。

## 请求信息

- 请求路径：`/api/v1/admin/secured/`
- 请求方法：`GET`
- 认证要求：需要认证，且需要超级管理员权限

## 请求参数

### 查询参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| page | int | 否 | 页码，默认为1 | 1 |
| pageSize | int | 否 | 每页数量，默认为10 | 10 |
| username | string | 否 | 用户名搜索（模糊匹配） | "admin" |
| nickname | string | 否 | 昵称搜索（模糊匹配） | "管理员" |
| role | string | 否 | 角色筛选 | "product" |
| status | int | 否 | 状态筛选，1-正常，0-禁用，-1-全部 | 1 |

## 响应信息

### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | object | 响应数据 |
| data.list | array | 管理员列表 |
| data.total | int | 总记录数 |
| data.page | int | 当前页码 |
| data.size | int | 每页数量 |

### 成功响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "username": "admin",
        "nickname": "超级管理员",
        "avatar": "https://example.com/avatar.jpg",
        "mobile": "13800138000",
        "email": "<EMAIL>",
        "role": "super",
        "permissions": ["*"],
        "status": 1,
        "last_login_at": "2023-01-01T12:00:00Z",
        "created_at": "2022-01-01T00:00:00Z"
      },
      {
        "id": 2,
        "username": "admin2",
        "nickname": "商品管理员",
        "avatar": "https://example.com/avatar2.jpg",
        "mobile": "13800138001",
        "email": "<EMAIL>",
        "role": "product",
        "permissions": ["product.view", "product.edit"],
        "status": 1,
        "last_login_at": "2023-01-02T12:00:00Z",
        "created_at": "2022-01-02T00:00:00Z"
      }
    ],
    "total": 2,
    "page": 1,
    "size": 10
  }
}
```

### 错误响应示例

```json
{
  "code": 403,
  "message": "无权限执行此操作",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 401 | 未认证 |
| 403 | 无权限 |
| 500 | 服务器内部错误 |