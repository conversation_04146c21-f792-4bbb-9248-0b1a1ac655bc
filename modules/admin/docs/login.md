# 管理员登录

## 接口说明

该接口用于管理员账号登录，获取认证Token。

## 请求信息

- 请求路径：`/api/v1/admin/login`
- 请求方法：`POST`
- 认证要求：无需认证

## 请求参数

### 请求体（JSON格式）

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| username | string | 是 | 管理员用户名 | "admin" |
| password | string | 是 | 管理员密码 | "password123" |

### 请求示例

```json
{
  "username": "admin",
  "password": "password123"
}
```

## 响应信息

### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | object | 响应数据 |
| data.token | string | JWT认证令牌 |
| data.admin | object | 管理员信息 |
| data.admin.id | int | 管理员ID |
| data.admin.username | string | 管理员用户名 |
| data.admin.nickname | string | 管理员昵称 |
| data.admin.avatar | string | 管理员头像URL |
| data.admin.mobile | string | 管理员手机号 |
| data.admin.email | string | 管理员邮箱 |
| data.admin.role | string | 管理员角色 |
| data.admin.permissions | array | 管理员权限列表 |
| data.admin.status | int | 管理员状态，1-正常，0-禁用 |
| data.admin.last_login_at | string | 最后登录时间 |
| data.admin.created_at | string | 创建时间 |

### 成功响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwicm9sZSI6InN1cGVyIiwiZXhwIjoxNjE5NTIwMDAwfQ.example_token",
    "admin": {
      "id": 1,
      "username": "admin",
      "nickname": "超级管理员",
      "avatar": "https://example.com/avatar.jpg",
      "mobile": "13800138000",
      "email": "<EMAIL>",
      "role": "super",
      "permissions": ["*"],
      "status": 1,
      "last_login_at": "2023-01-01T12:00:00Z",
      "created_at": "2022-01-01T00:00:00Z"
    }
  }
}
```

### 错误响应示例

```json
{
  "code": 401,
  "message": "用户名或密码错误",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 参数错误 |
| 401 | 用户名或密码错误 |
| 500 | 服务器内部错误 |