# 重置密码

## 接口说明

该接口用于重置指定管理员的密码，仅超级管理员可以操作。

## 请求信息

- 请求路径：`/api/v1/admin/secured/password/reset`
- 请求方法：`PUT`
- 认证要求：需要认证，且需要超级管理员权限

## 请求参数

### 请求体（JSON格式）

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | int | 是 | 管理员ID | 2 |
| new_password | string | 是 | 新密码 | "newpassword123" |

### 请求示例

```json
{
  "id": 2,
  "new_password": "newpassword123"
}
```

## 响应信息

### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | null | 响应数据为空 |

### 成功响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 错误响应示例

```json
{
  "code": 403,
  "message": "无权限执行此操作",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 参数错误 |
| 401 | 未认证 |
| 403 | 无权限 |
| 404 | 管理员不存在 |
| 500 | 服务器内部错误 |