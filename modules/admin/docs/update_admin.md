# 更新管理员信息

## 接口说明

该接口用于更新管理员的基本信息，超级管理员可以更新任何管理员信息，普通管理员只能更新自己的信息且不能修改角色和权限。

## 请求信息

- 请求路径：`/api/v1/admin/secured/`
- 请求方法：`PUT`
- 认证要求：需要认证

## 请求参数

### 请求体（JSON格式）

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | int | 是 | 管理员ID | 1 |
| nickname | string | 是 | 管理员昵称 | "系统管理员" |
| avatar | string | 否 | 管理员头像URL | "https://example.com/avatar.jpg" |
| mobile | string | 否 | 管理员手机号 | "13800138000" |
| email | string | 否 | 管理员邮箱 | "<EMAIL>" |
| role | string | 否 | 管理员角色（仅超级管理员可修改） | "system" |
| permissions | array | 否 | 管理员权限列表（仅超级管理员可修改） | ["system.view", "system.edit"] |

### 请求示例

```json
{
  "id": 1,
  "nickname": "系统管理员",
  "avatar": "https://example.com/avatar.jpg",
  "mobile": "13800138000",
  "email": "<EMAIL>",
  "role": "system",
  "permissions": ["system.view", "system.edit"]
}
```

## 响应信息

### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | null | 响应数据为空 |

### 成功响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 错误响应示例

```json
{
  "code": 403,
  "message": "无权限执行此操作",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 参数错误 |
| 401 | 未认证 |
| 403 | 无权限 |
| 404 | 管理员不存在 |
| 500 | 服务器内部错误 |