# 文件上传管理

## 模块概述

文件上传管理模块提供了完整的文件上传、查询、删除等功能，支持匿名上传和普通上传两种模式。管理员可以通过此模块管理平台所有上传的文件资源。

## 接口列表

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/admin/upload` | POST | 上传文件 |
| `/api/v1/admin/files/:id` | GET | 获取文件信息 |
| `/api/v1/admin/files` | GET | 获取文件列表 |
| `/api/v1/admin/files/:id` | DELETE | 删除文件 |
| `/api/v1/admin/config` | GET | 获取上传配置 |

## 接口详情

### 上传文件

#### 请求信息
- 请求路径：`/api/v1/admin/upload`
- 请求方法：`POST`
- 认证要求：支持匿名上传和认证上传

#### 请求参数
##### 表单参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| file | file | 是 | 要上传的文件 | - |
| file_usage | string | 否 | 文件用途 | avatar |
| is_anonymous | bool | 否 | 是否匿名上传 | false |

#### 响应信息
##### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | object | 文件信息 |

##### 文件信息对象

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| id | int | 文件ID |
| filename | string | 文件名 |
| size | int | 文件大小(字节) |
| mime_type | string | 文件MIME类型 |
| url | string | 文件访问URL |
| file_usage | string | 文件用途 |
| created_at | string | 创建时间 |

#### 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 参数错误或未选择文件 |
| 401 | 未授权（非匿名上传时需要登录） |
| 500 | 服务器内部错误 |

### 获取文件信息

#### 请求信息
- 请求路径：`/api/v1/admin/files/:id`
- 请求方法：`GET`
- 认证要求：无特殊要求

#### 请求参数
##### 路径参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | int | 是 | 文件ID | 1 |

#### 响应信息
同上传文件接口的响应

#### 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 无效的文件ID |
| 404 | 文件不存在 |
| 500 | 服务器内部错误 |

### 获取文件列表

#### 请求信息
- 请求路径：`/api/v1/admin/files`
- 请求方法：`GET`
- 认证要求：需要管理员权限

#### 请求参数
##### 查询参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| file_usage | string | 否 | 文件用途 | avatar |
| user_type | string | 否 | 用户类型 | admin |
| user_id | int | 否 | 用户ID | 1 |
| username | string | 否 | 用户名 | admin |
| is_anonymous | bool | 否 | 是否匿名上传 | false |
| start_time | string | 否 | 开始时间 | 2023-01-01 00:00:00 |
| end_time | string | 否 | 结束时间 | 2023-12-31 23:59:59 |
| page | int | 否 | 页码，默认1 | 1 |
| pageSize | int | 否 | 每页数量，默认10 | 20 |

#### 响应信息
##### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | object | 分页数据 |

##### 分页数据对象

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| total | int | 总记录数 |
| list | array | 文件列表 |

#### 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 401 | 未授权 |
| 403 | 无权限 |
| 500 | 服务器内部错误 |

### 删除文件

#### 请求信息
- 请求路径：`/api/v1/admin/files/:id`
- 请求方法：`DELETE`
- 认证要求：需要管理员权限

#### 请求参数
##### 路径参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| id | int | 是 | 文件ID | 1 |

#### 响应信息
##### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | null | 无数据返回 |

#### 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 400 | 无效的文件ID |
| 401 | 未授权 |
| 403 | 无权限 |
| 404 | 文件不存在 |
| 500 | 服务器内部错误 |

### 获取上传配置

#### 请求信息
- 请求路径：`/api/v1/admin/config`
- 请求方法：`GET`
- 认证要求：无特殊要求

#### 响应信息
##### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态描述 |
| data | object | 上传配置信息 |

##### 上传配置对象

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| max_size | int | 最大文件大小(字节) |
| allowed_types | array | 允许的文件类型 |
| storage_type | string | 存储类型 |
| base_url | string | 文件访问基础URL |

#### 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 500 | 服务器内部错误 |

## 使用示例

### 上传文件

```bash
curl -X POST http://localhost:8080/api/v1/admin/upload \
  -H "Authorization: Bearer your_token_here" \
  -F "file=@/path/to/your/file.jpg" \
  -F "file_usage=avatar" \
  -F "is_anonymous=false"