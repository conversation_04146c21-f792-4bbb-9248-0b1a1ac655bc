/**
 * 管理员数据传输对象
 *
 * 该文件定义了管理员模块的数据传输对象，用于请求和响应的数据结构。
 * 包括管理员登录、注册、查询、更新等操作的请求和响应对象。
 */

package dto

import (
	"time"
)

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	// 管理员用户名
	Username string `json:"username" form:"username" valid:"Required;MaxSize(50)"`
	// 管理员密码
	Password string `json:"password" form:"password" valid:"Required;MinSize(6);MaxSize(20)"`
}

// ... 现有代码 ...

// UpdateAvatarRequest 更新头像请求
type UpdateAvatarRequest struct {
	// 头像URL地址
	AvatarUrl string `json:"avatar_url" valid:"Required;MaxSize(255)"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	// Token信息
	TokenInfo TokenResponse `json:"token_info"`
	// 管理员信息
	Admin AdminResponse `json:"admin"`
}

// TokenResponse Token响应结构
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"` // 过期时间（秒）
	TokenType    string `json:"token_type"` // 令牌类型，固定为Bearer
}

// CreateAdminRequest 创建管理员请求
type CreateAdminRequest struct {
	// 管理员用户名
	Username string `json:"username" form:"username" valid:"Required;MaxSize(50)"`
	// 管理员密码
	Password string `json:"password" form:"password" valid:"Required;MinSize(6);MaxSize(20)"`
	// 管理员昵称
	Nickname string `json:"nickname" form:"nickname" valid:"MaxSize(50)"`
	// 管理员手机号
	Mobile string `json:"mobile" form:"mobile" valid:"Mobile"`
	// 管理员邮箱
	Email string `json:"email" form:"email" valid:"Email"`
	// 管理员角色
	Role string `json:"role" form:"role" valid:"Required;MaxSize(20)"`
	// 管理员权限
	Permissions []string `json:"permissions" form:"permissions"`
}

// UpdateAdminRequest 更新管理员请求
type UpdateAdminRequest struct {
	// 管理员ID
	ID int64 `json:"id" form:"id" valid:"Required"`
	// 管理员昵称
	Nickname string `json:"nickname" form:"nickname" valid:"MaxSize(50)"`
	// 管理员头像
	Avatar string `json:"avatar" form:"avatar" valid:"MaxSize(255)"`
	// 管理员手机号
	Mobile string `json:"mobile" form:"mobile" valid:"Mobile"`
	// 管理员邮箱
	Email string `json:"email" form:"email" valid:"Email"`
	// 管理员角色
	Role string `json:"role" form:"role" valid:"MaxSize(20)"`
	// 管理员权限
	Permissions []string `json:"permissions" form:"permissions"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	// 旧密码
	OldPassword string `json:"old_password" form:"old_password" valid:"Required;MinSize(6);MaxSize(20)"`
	// 新密码
	NewPassword string `json:"new_password" form:"new_password" valid:"Required;MinSize(6);MaxSize(20)"`
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	// 管理员ID
	ID int64 `json:"id" form:"id" valid:"Required"`
	// 新密码
	NewPassword string `json:"new_password" form:"new_password" valid:"Required;MinSize(6);MaxSize(20)"`
}

// AdminQueryRequest 管理员查询请求
type AdminQueryRequest struct {
	// 页码
	Page int `json:"page" form:"page"`
	// 每页数量
	PageSize int `json:"page_size" form:"page_size"`
	// 用户名查询条件（模糊匹配）
	Username string `json:"username" form:"username"`
	// 昵称查询条件（模糊匹配）
	Nickname string `json:"nickname" form:"nickname"`
	// 角色查询条件
	Role string `json:"role" form:"role"`
	// 状态查询条件
	Status int `json:"status" form:"status"`
}

// AdminResponse 管理员信息响应
type AdminResponse struct {
	// 管理员ID
	ID int64 `json:"id"`
	// 管理员用户名
	Username string `json:"username"`
	// 管理员昵称
	Nickname string `json:"nickname"`
	// 管理员头像
	Avatar string `json:"avatar"`
	// 管理员手机号
	Mobile string `json:"mobile"`
	// 管理员邮箱
	Email string `json:"email"`
	// 管理员角色
	Role string `json:"role"`
	// 管理员权限
	Permissions []string `json:"permissions"`
	// 管理员状态
	Status int `json:"status"`
	// 最后登录时间
	LastLoginAt time.Time `json:"last_login_at"`
	// 创建时间
	CreatedAt time.Time `json:"created_at"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" form:"refresh_token" valid:"Required"`
}

// AdminInfoDTO 管理员信息响应 DTO (精简版，用于 token 获取信息)
type AdminInfoDTO struct {
	// 管理员ID
	ID int64 `json:"id"`
	// 管理员用户名
	Username string `json:"username"`
	// 管理员昵称
	Nickname string `json:"nickname"`
	// 管理员头像
	Avatar string `json:"avatar"`
	// 管理员手机号
	Mobile string `json:"mobile"`
	// 管理员邮箱
	Email string `json:"email"`
	// 管理员角色
	Role string `json:"role"`
	// 管理员状态
	Status string `json:"status"`
	// 最后登录时间
	LastLoginAt time.Time `json:"last_login_at"`
	// 创建时间
	CreatedAt time.Time `json:"created_at"`
	// 管理员权限
	Permissions []string `json:"permissions"`
}

// AdminUserResponse 用户信息响应 (for admin module)
type AdminUserResponse struct {
	// 用户ID
	ID int64 `json:"id"`
	// 用户名
	Username string `json:"username"`
	// 昵称
	Nickname string `json:"nickname"`
	// 头像
	Avatar string `json:"avatar"`
	// 手机号
	Mobile string `json:"mobile"`
	// 邮箱
	Email string `json:"email"`
	// 状态
	Status int `json:"status"`
	// 最后登录时间
	LastLoginAt time.Time `json:"last_login_at"`
	// 创建时间
	CreatedAt time.Time `json:"created_at"`
}
