/**
 * 管理员日志DTO
 *
 * 该文件定义了管理员日志相关的数据传输对象，用于服务层与控制层之间的数据交换。
 * 包括日志创建请求、查询请求以及响应数据结构等。
 */

package dto

import (
	"time"
)

// AdminLogCreateRequest 管理员日志创建请求
type AdminLogCreateRequest struct {
	AdminID     int64  `json:"admin_id"`     // 管理员ID
	Username    string `json:"username"`     // 管理员用户名
	Module      string `json:"module"`       // 操作模块
	Type        string `json:"type"`         // 操作类型
	Content     string `json:"content"`      // 操作内容
	RequestURL  string `json:"request_url"`  // 请求URL
	RequestData string `json:"request_data"` // 请求数据（JSON格式）
	IP          string `json:"ip"`           // 操作IP
	UserAgent   string `json:"user_agent"`   // 用户代理
	TargetID    int64  `json:"target_id"`    // 目标ID
	TargetType  string `json:"target_type"`  // 目标类型
	Status      int    `json:"status"`       // 操作状态
	Remark      string `json:"remark"`       // 备注信息
}

// AdminLogQueryRequest 管理员日志查询请求
type AdminLogQueryRequest struct {
	AdminID   int64     `json:"admin_id"`   // 管理员ID
	Username  string    `json:"username"`   // 管理员用户名
	Module    string    `json:"module"`     // 操作模块
	Type      string    `json:"type"`       // 操作类型
	IP        string    `json:"ip"`         // 操作IP
	Status    int       `json:"status"`     // 操作状态，-1表示全部
	StartTime time.Time `json:"start_time"` // 开始时间
	EndTime   time.Time `json:"end_time"`   // 结束时间
	Page      int       `json:"page"`       // 页码
	PageSize  int       `json:"page_size"`   // 每页数量
}

// AdminLogResponse 管理员日志响应
type AdminLogResponse struct {
	ID          int64     `json:"id"`          // 日志ID
	AdminID     int64     `json:"admin_id"`    // 管理员ID
	Username    string    `json:"username"`    // 管理员用户名
	Module      string    `json:"module"`      // 操作模块
	Type        string    `json:"type"`        // 操作类型
	Content     string    `json:"content"`     // 操作内容
	RequestURL  string    `json:"request_url"`  // 请求URL
	RequestData string    `json:"request_data"` // 请求数据（JSON格式）
	IP          string    `json:"ip"`          // 操作IP
	UserAgent   string    `json:"user_agent"`  // 用户代理
	TargetID    int64     `json:"target_id"`   // 目标ID
	TargetType  string    `json:"target_type"` // 目标类型
	Status      int       `json:"status"`      // 操作状态
	StatusText  string    `json:"status_text"` // 操作状态文本
	Remark      string    `json:"remark"`      // 备注信息
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
}

// AdminLogListResponse 管理员日志列表响应
type AdminLogListResponse struct {
	Total int64               `json:"total"` // 总数
	List  []*AdminLogResponse `json:"list"`  // 列表
}

// AdminLogStatisticsRequest 管理员日志统计请求
type AdminLogStatisticsRequest struct {
	StartTime time.Time `json:"start_time"` // 开始时间
	EndTime   time.Time `json:"end_time"`   // 结束时间
	Module    string    `json:"module"`     // 操作模块
}

// ModuleStatisticsItem 模块统计项
type ModuleStatisticsItem struct {
	Module string `json:"module"` // 模块名称
	Count  int64  `json:"count"`  // 操作次数
}

// AdminLogStatisticsResponse 管理员日志统计响应
type AdminLogStatisticsResponse struct {
	TotalCount       int64                   `json:"total_count"`       // 总操作次数
	SuccessCount     int64                   `json:"success_count"`     // 成功操作次数
	FailCount        int64                   `json:"fail_count"`        // 失败操作次数
	ModuleStatistics []*ModuleStatisticsItem `json:"module_statistics"` // 模块统计
}
