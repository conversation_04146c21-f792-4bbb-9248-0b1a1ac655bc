/**
 * GridInfo 管理员DTO
 *
 * 该文件定义了管理员模块下用于网格布局管理的数据传输对象
 *
 * <AUTHOR> 2025-04-18
 * @description 管理员模块网格布局DTO
 * @note Content和DTO字段类型为interface{}，以兼容多种数据格式
 */

package dto

import "time"

// AdminGridInfoDTO 管理员模块网格布局DTO
// 用于管理端网格布局的列表和详情返回
// Content和DTO字段类型为interface{}，以兼容多种数据格式
// 2025-04-18 by zhangerhao
type AdminGridInfoDTO struct {
	ID          int         `json:"id"`                      // 网格布局ID
	UIConfigID  int         `json:"ui_config_id,omitempty"`  // 关联的UI配置ID（向后兼容）
	UIConfigIDs []int       `json:"ui_config_ids,omitempty"` // 关联的UI配置ID列表（多对多关系）
	Name        string      `json:"name"`                    // 组件名称
	Content     interface{} `json:"content"`                 // 支持string或object
	Position    interface{} `json:"position"`                // 位置信息，包含x,y,w,h等自定义字段
	API         string      `json:"api"`                     // 数据API地址
	DTO         interface{} `json:"dto"`                     // 支持string或object
	Remark      string      `json:"remark"`                  // 备注说明
	Status      int         `json:"status"`                  // 状态：1-启用，0-禁用
	CreatedAt   time.Time   `json:"created_at"`              // 创建时间
	UpdatedAt   time.Time   `json:"updated_at"`              // 更新时间
}

// AdminGridInfoResponseDTO 管理员模块用于前端响应的网格布局数据传输对象
type AdminGridInfoResponseDTO struct {
	ID          int                       `json:"id"`                      // 网格布局ID
	UIConfigID  int                       `json:"ui_config_id,omitempty"`  // 关联的UI配置ID（向后兼容）
	UIConfigIDs []int                     `json:"ui_config_ids,omitempty"` // 关联的UI配置ID列表（多对多关系）
	Name        string                    `json:"name"`                    // 组件名称
	Content     interface{}               `json:"content"`                 // 解析后的组件内容配置
	Position    interface{}               `json:"position"`                // 位置信息，包含x,y,w,h等自定义字段
	API         string                    `json:"api"`                     // 数据API地址
	DTO         interface{}               `json:"dto"`                     // 解析后的DTO数据
	Remark      string                    `json:"remark"`                  // 备注说明
	Status      int                       `json:"status"`                  // 状态：1-启用，0-禁用
	UIConfigs   []*AdminUIConfigSimpleDTO `json:"ui_configs,omitempty"`    // 关联的UI配置列表（简化版，只包含基本信息）
}

// AdminUIConfigSimpleDTO 简化版的UI配置DTO，用于展示关联的UI配置基本信息
type AdminUIConfigSimpleDTO struct {
	ID           int    `json:"id"`            // 配置ID
	Module       string `json:"module"`        // 模块名称
	Title        string `json:"title"`         // 配置标题
	ConfigType   string `json:"config_type"`   // 配置类型
	FrontendPath string `json:"frontend_path"` // 前端路径
}

// AdminGridInfoCreateDTO 管理员模块用于创建网格布局的数据传输对象
type AdminGridInfoCreateDTO struct {
	UIConfigID  int         `json:"ui_config_id,omitempty"`   // 关联的UI配置ID（向后兼容）
	UIConfigIDs []int       `json:"ui_config_ids,omitempty"`  // 关联的UI配置ID列表（多对多关系）
	Name        string      `json:"name" valid:"Required"`    // 组件名称，必填
	Content     string      `json:"content" valid:"Required"` // 组件内容配置，JSON格式，必填
	Position    interface{} `json:"position"`                 // 位置信息，JSON格式，包含x,y,w,h等自定义字段
	// 前端使用uiConfigId而非ui_config_id，需要特别注意
	UIConfigIdFrontend int    `json:"ui_config_id,omitempty"`      // 前端传递的UI配置ID
	API                string `json:"api"`                       // 数据API地址
	DTO                string `json:"dto"`                       // DTO配置，JSON格式
	Remark             string `json:"remark"`                    // 备注说明
	Status             int    `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
}

// AdminGridInfoUpdateDTO 管理员模块用于更新网格布局的数据传输对象
type AdminGridInfoUpdateDTO struct {
	UIConfigIDs []int       `json:"ui_config_ids,omitempty"` // 关联的UI配置ID列表（多对多关系）
	Name        string      `json:"name"`                    // 组件名称
	Step        []int       `json:"step"`                    // 步骤（数组格式）
	Permission  []int       `json:"permission"`              // 权限ID列表
	Content     string      `json:"content"`                 // 组件内容配置，JSON格式
	Position    interface{} `json:"position"`                // 位置信息，JSON格式，包含x,y,w,h等自定义字段
	// 前端使用uiConfigId而非ui_config_id，需要特别注意
	UIConfigIdFrontend int    `json:"ui_config_id,omitempty"`      // 前端传递的UI配置ID
	API                string `json:"api"`                       // 数据API地址
	DTO                string `json:"dto"`                       // DTO配置，JSON格式
	Remark             string `json:"remark"`                    // 备注说明
	Status             int    `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
}

// AdminGridInfoQueryDTO 管理员模块用于查询网格布局的数据传输对象
type AdminGridInfoQueryDTO struct {
	UIConfigID int    `json:"ui_config_id" form:"ui_config_id" query:"ui_config_id"`
	Name       string `json:"name" form:"name" query:"name"`
	Status     int    `json:"status" form:"status" query:"status"`
	Page       int    `json:"page" form:"page" query:"page"`
	PageSize   int    `json:"page_size" form:"page_size" query:"page_size"`
}

// AdminGridInfoBatchUpdatePositionDTO 管理员模块用于批量更新网格布局位置的数据传输对象
type AdminGridInfoBatchUpdatePositionDTO struct {
	Items []AdminGridInfoPositionItem `json:"items" valid:"Required"` // 位置项列表
}

// AdminGridInfoPositionItem 管理员模块用于网格布局位置项
type AdminGridInfoPositionItem struct {
	ID         int         `json:"id" valid:"Required"`  // 网格布局ID
	UIConfigID int         `json:"ui_config_id,omitempty"` // 关联的UI配置ID，注意前端传的是uiConfigId而不是ui_config_id
	Position   interface{} `json:"position,omitempty"`   // 完整的Position对象
}

// AdminGridInfoStatusUpdateDTO 管理员模块用于更新网格布局状态的数据传输对象
type AdminGridInfoStatusUpdateDTO struct {
	Status int `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
}

// AdminGridInfoUIConfigRelationDTO 管理员模块用于管理网格布局与UI配置关联关系的数据传输对象
type AdminGridInfoUIConfigRelationDTO struct {
	GridInfoID  int   `json:"grid_info_id" valid:"Required"`  // 网格布局ID
	UIConfigIDs []int `json:"ui_config_ids" valid:"Required"` // UI配置ID列表
}

// AdminBatchUIConfigGridRelationDTO 管理员模块用于批量设置UI配置与多个网格布局关联关系的数据传输对象
// 用于将一个UIConfigId同时关联到多个GridInfoId
// <AUTHOR> 2025-04-18
// @description 用于批量建立UI配置与网格布局的关联
// @field UIConfigId UI配置ID
// @field GridInfoIds 网格布局ID列表
// @example {"uiConfigId":1, "gridInfoIds":[1,2,3]}
type AdminBatchUIConfigGridRelationDTO struct {
	UIConfigId  int   `json:"ui_config_id" valid:"Required"`      // UI配置ID
	GridInfoIds []int `json:"gridInfoIds" valid:"Required"`    // 网格布局ID列表
}
