/**
 * 管理员模块商家分类DTO
 *
 * 该文件定义了管理员管理商家分类相关的数据传输对象
 * 包括商家分类的创建、查询、更新等请求和响应结构
 */

package dto

import (
	"time"
)

// MerchantCategoryResponse 商家分类响应
type MerchantCategoryResponse struct {
	ID          int64     `json:"id" description:"分类ID"`              // 分类ID
	Name        string    `json:"name" description:"分类名称"`            // 分类名称
	Description string    `json:"description" description:"分类描述"`     // 分类描述
	Icon        string    `json:"icon" description:"分类图标URL"`         // 分类图标
	SortOrder   int       `json:"sort_order" description:"排序值，越小越靠前"` // 排序值
	IsShow      bool      `json:"is_show" description:"是否显示"`         // 是否显示
	CreatedAt   time.Time `json:"created_at" description:"创建时间"`      // 创建时间
	UpdatedAt   time.Time `json:"updated_at" description:"更新时间"`      // 更新时间
}

// CreateMerchantCategoryRequest 创建商家分类请求
type CreateMerchantCategoryRequest struct {
	Name        string `json:"name" valid:"Required;MaxSize(100)" description:"分类名称"`    // 分类名称
	Description string `json:"description" valid:"MaxSize(500)" description:"分类描述"`      // 分类描述
	Icon        string `json:"icon" valid:"Required;MaxSize(255)" description:"分类图标URL"` // 分类图标URL
	SortOrder   int    `json:"sort_order" description:"排序值，越小越靠前"`                       // 排序值
	IsShow      bool   `json:"is_show" description:"是否显示"`                               // 是否显示
}

// UpdateMerchantCategoryRequest 更新商家分类请求
type UpdateMerchantCategoryRequest struct {
	Name        string `json:"name" valid:"MaxSize(100)" description:"分类名称"`        // 分类名称
	Description string `json:"description" valid:"MaxSize(500)" description:"分类描述"` // 分类描述
	Icon        string `json:"icon" valid:"MaxSize(255)" description:"分类图标URL"`     // 分类图标URL
	SortOrder   int    `json:"sort_order" description:"排序值，越小越靠前"`                  // 排序值
	IsShow      bool   `json:"is_show" description:"是否显示"`                          // 是否显示
}

// MerchantCategoryQueryRequest 商家分类查询请求
type MerchantCategoryQueryRequest struct {
	Page     int    `json:"page" form:"page" description:"页码"`             // 页码
	PageSize int    `json:"page_size" form:"page_size" description:"每页数量"` // 每页数量
	Name     string `json:"name" form:"name" description:"分类名称"`           // 分类名称
	IsShow   *bool  `json:"is_show" form:"is_show" description:"是否显示"`     // 是否显示
}

// MerchantCategoryListResponse 商家分类列表响应
type MerchantCategoryListResponse struct {
	Total int64                       `json:"total" description:"总数"`   // 总数
	Items []*MerchantCategoryResponse `json:"items" description:"分类列表"` // 分类列表
}
