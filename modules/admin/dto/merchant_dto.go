/**
 * 商户管理DTO
 *
 * 该文件定义了商户管理相关的数据传输对象。
 * 包括商户查询、创建、更新、审核等请求和响应对象。
 */

package dto

import (
	"time"
)

// MerchantQueryRequest 商户查询请求
type MerchantQueryRequest struct {
	Name          string `json:"name" description:"商户名称"`                                       // 商户名称
	Mobile        string `json:"mobile" description:"联系电话"`                                     // 联系电话
	Email         string `json:"email" description:"联系邮箱"`                                      // 联系邮箱
	Status        int    `json:"status" description:"状态：0-禁用,1-正常"`                             // 状态：0-禁用，1-正常
	AuditStatus   int    `json:"audit_status" description:"审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 商家审核状态 0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定
	Level         int    `json:"level" description:"商家等级：0-普通,1-银牌,2-金牌,3-钻石"`                  // 商家等级：0-普通,1-银牌,2-金牌,3-钻石
	IsRecommended int    `json:"is_recommended" description:"是否推荐：0-否,1-是"`                    // 是否推荐：0-否,1-是
	Page          int    `json:"page" description:"页码"`                                         // 页码
	PageSize      int    `json:"page_size" description:"每页数量"`                                  // 每页数量
	// 地理位置相关参数
	MinLongitude float64 `json:"min_longitude" form:"min_longitude" description:"最小经度范围"` // 最小经度范围
	MaxLongitude float64 `json:"max_longitude" form:"max_longitude" description:"最大经度范围"` // 最大经度范围
	MinLatitude  float64 `json:"min_latitude" form:"min_latitude" description:"最小纬度范围"`   // 最小纬度范围
	MaxLatitude  float64 `json:"max_latitude" form:"max_latitude" description:"最大纬度范围"`   // 最大纬度范围
}

// BusinessHourResponse 商家营业时间响应
type BusinessHourResponse struct {
	Weekday   int    `json:"weekday" description:"星期几：0-周日，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六"` // 星期几
	StartTime string `json:"start_time" description:"开始营业时间，格式：HH:MM"`                     // 开始营业时间
	EndTime   string `json:"end_time" description:"结束营业时间，格式：HH:MM"`                       // 结束营业时间
}

// MerchantResponse 商户信息响应
type MerchantResponse struct {
	ID                int64                         `json:"id" description:"商户ID"`                                         // 商户ID
	Name              string                        `json:"name" description:"商户名称"`                                       // 商户名称
	Logo              string                        `json:"logo" description:"商户logo"`                                     // 商户logo
	Balance           float64                       `json:"balance" description:"商户余额"`                                    // 商户余额
	Description       string                        `json:"description" description:"商户描述"`                                // 商户描述
	ContactName       string                        `json:"contact_name" description:"联系人姓名"`                              // 联系人姓名
	ContactMobile     string                        `json:"contact_mobile" description:"联系电话"`                             // 联系电话
	ContactEmail      string                        `json:"contact_email" description:"联系邮箱"`                              // 联系邮箱
	Address           string                        `json:"address" description:"商户地址"`                                    // 商户地址
	Longitude         float64                       `json:"longitude" description:"经度坐标(GCJ02)"`                           // 经度坐标
	Latitude          float64                       `json:"latitude" description:"纬度坐标(GCJ02)"`                            // 纬度坐标
	Level             int                           `json:"level" description:"商家等级：0-普通,1-银牌,2-金牌,3-钻石"`                  // 商家等级：0-普通,1-银牌,2-金牌,3-钻石
	Status            int                           `json:"status" description:"状态：0-禁用,1-正常"`                             // 状态：0-禁用，1-正常
	OperationStatus   int                           `json:"operation_status" description:"商家经营状态：0-休息中,1-营业中"`             // 经营状态
	IsRecommended     int                           `json:"is_recommended" description:"是否推荐：0-否,1-是"`                    // 是否推荐：0-否,1-是
	BusinessHours     []BusinessHourResponse        `json:"business_hours" description:"商家营业时间"`                           // 营业时间
	BusinessLicense   string                        `json:"business_license" description:"营业执照"`                           // 营业执照
	AuditStatus       int                           `json:"audit_status" description:"审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 审核状态 0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定
	AuditRemark       string                        `json:"audit_remark" description:"审核备注"`                               // 审核备注
	AuditTime         time.Time                     `json:"audit_time" description:"审核时间"`                                 // 审核时间
	RejectReason      string                        `json:"reject_reason" description:"拒绝原因"`                              // 拒绝原因
	CreatedAt         time.Time                     `json:"created_at" description:"创建时间"`                                 // 创建时间
	Username          string                        `json:"username" description:"登录用户名"`                                  // 登录用户名
	UpdatedAt         time.Time                     `json:"updated_at" description:"更新时间"`                                 // 更新时间
	TakeoutStatistics *MerchantTakeoutStatisticsDTO `json:"takeout_statistics,omitempty" description:"外卖统计信息"`             // 外卖统计信息
}

// UpdateMerchantRequest 更新商户请求
type UpdateMerchantRequest struct {
	ID              int64   `json:"id" valid:"Required" description:"商户ID"`                                         // 商户ID
	Username        string  `json:"username" valid:"Required" description:"登录用户名"`                                  // 登录用户名
	Password        string  `json:"password" description:"登录密码"`                                                    // 登录密码
	Name            string  `json:"name" valid:"Required" description:"商户名称"`                                       // 商户名称
	Logo            string  `json:"logo" description:"商户logo"`                                                      // 商户logo
	Status          int     `json:"status" valid:"Required" description:"状态：0-禁用，1-正常，2-锁定"`                        // 状态：0-禁用，1-正常
	AuditStatus     int     `json:"audit_status" valid:"Required" description:"审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 审核状态 0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定
	Description     string  `json:"description" description:"商户描述"`                                                 // 商户描述
	BusinessLicense string  `json:"business_license" valid:"Required" description:"营业执照"`                           // 营业执照
	ContactName     string  `json:"contact_name" valid:"Required" description:"联系人姓名"`                              // 联系人姓名
	ContactMobile   string  `json:"contact_mobile" valid:"Required" description:"联系电话"`                             // 联系电话
	ContactEmail    string  `json:"contact_email" valid:"Email" description:"联系邮箱"`                                 // 联系邮箱
	Address         string  `json:"address" valid:"Required" description:"商户地址"`                                    // 商户地址
	Longitude       float64 `json:"longitude" description:"经度坐标(GCJ02)"`                                            // 经度坐标
	Latitude        float64 `json:"latitude" description:"纬度坐标(GCJ02)"`                                             // 纬度坐标
	Level           int     `json:"level" valid:"Required" description:"商家等级：0-普通,1-银牌,2-金牌,3-钻石"`                  // 商家等级：0-普通,1-银牌,2-金牌,3-钻石
	IsRecommended   int     `json:"is_recommended" description:"是否推荐：0-否,1-是"`                                    // 是否推荐：0-否,1-是
}

// UpdateMerchantRecommendRequest 更新商户推荐状态请求
type UpdateMerchantRecommendRequest struct {
	IsRecommended int `json:"is_recommended" valid:"Required" description:"是否推荐：0-否,1-是"` // 是否推荐：0-否,1-是
}

// ResetMerchantPasswordRequest 重置商户密码请求
type ResetMerchantPasswordRequest struct {
	ID              int64  `json:"id" valid:"Required" description:"商户ID"`               // 商户ID
	NewPassword     string `json:"new_password" valid:"Required" description:"新密码"`      // 新密码
	ConfirmPassword string `json:"confirm_password" valid:"Required" description:"确认密码"` // 确认密码
}

// AuditMerchantRequest 审核商户请求
type AuditMerchantRequest struct {
	ID          int64  `json:"id" valid:"Required" description:"商户ID"`                                         // 商户ID
	AuditStatus string `json:"audit_status" valid:"Required" description:"审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定
	Remark      string `json:"remark" description:"审核备注"`                                                      // 审核备注
}

// CreateMerchantRequest 管理员创建商户请求
type CreateMerchantRequest struct {
	Name            string  `json:"name" valid:"Required" description:"商户名称"`                                       // 商户名称
	Logo            string  `json:"logo" description:"商户logo"`                                                      // 商户logo
	Status          int     `json:"status" valid:"Required" description:"状态：0-禁用,1-正常"`                             // 状态：0-禁用，1-正常
	AuditStatus     int     `json:"audit_status" valid:"Required" description:"审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 审核状态：0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定
	Description     string  `json:"description" description:"商户描述"`                                                 // 商户描述
	Username        string  `json:"username" valid:"Required" description:"登录用户名"`                                  // 登录用户名
	Password        string  `json:"password" valid:"Required" description:"登录密码"`                                   // 登录密码
	ContactName     string  `json:"contact_name" valid:"Required" description:"联系人姓名"`                              // 联系人姓名
	ContactMobile   string  `json:"contact_mobile" valid:"Required" description:"联系电话"`                             // 联系电话
	ContactEmail    string  `json:"contact_email" valid:"Email" description:"联系邮箱"`                                 // 联系邮箱
	BusinessLicense string  `json:"business_license" valid:"Required" description:"营业执照"`                           // 营业执照
	Address         string  `json:"address" valid:"Required" description:"商户地址"`                                    // 商户地址
	Longitude       float64 `json:"longitude" description:"经度坐标(GCJ02)"`                                            // 经度坐标
	Latitude        float64 `json:"latitude" description:"纬度坐标(GCJ02)"`                                             // 纬度坐标
	Level           int     `json:"level" valid:"Required" description:"商家等级：0-普通,1-银牌,2-金牌,3-钻石"`                  // 商家等级：0-普通,1-银牌,2-金牌,3-钻石
	IsRecommended   int     `json:"is_recommended" description:"是否推荐：0-否,1-是"`                                    // 是否推荐：0-否,1-是
}
