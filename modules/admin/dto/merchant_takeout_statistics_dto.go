/**
 * 商户外卖统计数据DTO
 *
 * 本文件定义了商户外卖相关的统计数据结构体，包括外卖食品统计和外卖订单统计。
 * 用于在获取商户详情API中返回商户的外卖经营数据。
 */

package dto

// MerchantTakeoutStatisticsDTO 商户外卖统计数据
type MerchantTakeoutStatisticsDTO struct {
	FoodStatistics    FoodStatisticsDTO    `json:"food_statistics" description:"食品统计信息"`    // 食品统计信息
	OrderStatistics   OrderStatisticsDTO   `json:"order_statistics" description:"订单统计信息"`   // 订单统计信息
}

// FoodStatisticsDTO 商户食品统计数据
type FoodStatisticsDTO struct {
	TotalFoods     int64 `json:"total_foods" description:"全部食品数量"`      // 全部食品数量
	OnSaleFoods    int64 `json:"on_sale_foods" description:"上架食品数量"`    // 上架食品数量
	PendingFoods   int64 `json:"pending_foods" description:"待审核食品数量"`   // 待审核食品数量
}

// OrderStatisticsDTO 商户订单统计数据
type OrderStatisticsDTO struct {
	TotalOrders     int64 `json:"total_orders" description:"全部订单数量"`      // 全部订单数量
	CompletedOrders int64 `json:"completed_orders" description:"已完成订单数量"` // 已完成订单数量
	ProcessingOrders int64 `json:"processing_orders" description:"进行中订单数量"` // 进行中订单数量
	CancelledOrders int64 `json:"cancelled_orders" description:"已取消订单数量"` // 已取消订单数量
}
