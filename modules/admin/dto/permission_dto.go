/**
 * 权限DTO
 *
 * 该文件定义了权限相关的数据传输对象。
 * 包括权限查询、创建、更新等请求和响应对象。
 */

package dto

import (
	"time"
)

// PermissionQueryRequest 权限查询请求
type PermissionQueryRequest struct {
	Name     string `json:"name"`                                    // 权限名称
	Code     string `json:"code"`                                    // 权限编码
	Type     int    `json:"type" description:"权限类型 1-菜单，2-按钮，3-API"` // 权限类型
	Level    int    `json:"level" description:"权限级别 1-系统，2-模块，3-功能"` // 权限级别
	Status   int    `json:"status" description:"状态 0-禁用，1-正常"`       // 状态
	Page     int    `json:"page"`                                    // 页码
	PageSize int    `json:"page_size"`                               // 每页数量
}

// PermissionResponse 权限信息响应
type PermissionResponse struct {
	ID          int64     `json:"id"`                                      // 权限ID
	ParentID    int64     `json:"parent_id"`                               // 父权限ID
	Name        string    `json:"name"`                                    // 权限名称
	Code        string    `json:"code"`                                    // 权限编码
	Type        int       `json:"type" description:"权限类型 1-菜单，2-按钮，3-API"` // 权限类型
	Level       int       `json:"level" description:"权限级别 1-系统，2-模块，3-功能"` // 权限级别
	Path        string    `json:"path"`                                    // 权限路径
	Component   string    `json:"component"`                               // 前端组件
	Icon        string    `json:"icon"`                                    // 图标
	Sort        int       `json:"sort"`                                    // 排序
	Status      int       `json:"status" description:"状态 0-禁用，1-正常"`       // 状态
	Description string    `json:"description"`                             // 描述
	CreatedAt   time.Time `json:"created_at"`                              // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`                              // 更新时间
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	ParentID    int64  `json:"parent_id" valid:"Required"`                               // 父权限ID
	Name        string `json:"name" valid:"Required"`                                    // 权限名称
	Code        string `json:"code" valid:"Required"`                                    // 权限编码
	Type        int    `json:"type" valid:"Required" description:"权限类型 1-菜单，2-按钮，3-API"` // 权限类型
	Level       int    `json:"level" valid:"Required" description:"权限级别 1-系统，2-模块，3-功能"` // 权限级别
	Path        string `json:"path"`                                                     // 权限路径
	Component   string `json:"component"`                                                // 前端组件
	Icon        string `json:"icon"`                                                     // 图标
	Sort        int    `json:"sort"`                                                     // 排序
	Status      int    `json:"status" valid:"Required" description:"状态 0-禁用，1-正常"`       // 状态
	Description string `json:"description"`                                              // 描述
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	ID          int64  `json:"id" valid:"Required"`                                      // 权限ID
	ParentID    int64  `json:"parent_id" valid:"Required"`                               // 父权限ID
	Name        string `json:"name" valid:"Required"`                                    // 权限名称
	Code        string `json:"code" valid:"Required"`                                    // 权限编码
	Type        int    `json:"type" valid:"Required" description:"权限类型 1-菜单，2-按钮，3-API"` // 权限类型
	Level       int    `json:"level" valid:"Required" description:"权限级别 1-系统，2-模块，3-功能"` // 权限级别
	Path        string `json:"path"`                                                     // 权限路径
	Component   string `json:"component"`                                                // 前端组件
	Icon        string `json:"icon"`                                                     // 图标
	Sort        int    `json:"sort"`                                                     // 排序
	Status      int    `json:"status" valid:"Required" description:"状态 0-禁用，1-正常"`       // 状态
	Description string `json:"description"`                                              // 描述
}

// RoleQueryRequest 角色查询请求
type RoleQueryRequest struct {
	Name     string `json:"name"`      // 角色名称
	Code     string `json:"code"`      // 角色编码
	Status   int    `json:"status"`    // 状态
	Page     int    `json:"page"`      // 页码
	PageSize int    `json:"page_size"` // 每页数量
}

// RoleResponse 角色信息响应
type RoleResponse struct {
	ID          int64     `json:"id"`          // 角色ID
	Name        string    `json:"name"`        // 角色名称
	Code        string    `json:"code"`        // 角色编码
	Status      int       `json:"status"`      // 状态
	Description string    `json:"description"` // 描述
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" valid:"Required"` // 角色名称
	Code        string `json:"code" valid:"Required"` // 角色编码
	Description string `json:"description"`           // 描述
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	ID          int64  `json:"id" valid:"Required"`                                // 角色ID
	Name        string `json:"name" valid:"Required"`                              // 角色名称
	Code        string `json:"code" valid:"Required"`                              // 角色编码
	Status      int    `json:"status" valid:"Required" description:"状态：0-禁用，1-正常"` // 状态
	Description string `json:"description"`                                        // 描述
}

// AssignRoleRequest 分配角色请求
type AssignRoleRequest struct {
	UserID   int64  `json:"user_id" valid:"Required"`   // 用户ID
	UserType string `json:"user_type" valid:"Required"` // 用户类型
	RoleID   int64  `json:"role_id" valid:"Required"`   // 角色ID
}

// AssignPermissionRequest 分配权限请求
type AssignPermissionRequest struct {
	RoleID        int64   `json:"role_id" valid:"Required"`        // 角色ID
	PermissionIDs []int64 `json:"permission_ids" valid:"Required"` // 权限ID列表
}
