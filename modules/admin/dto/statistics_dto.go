/**
 * 系统统计数据DTO
 *
 * 该文件定义了系统统计数据的数据传输对象，用于管理员后台的数据统计展示。
 * 包括用户数、商家数、商品数、订单数、今日订单数和今日销售额等统计数据。
 */

package dto

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`    // 状态码
	Message string      `json:"message"` // 消息
	Data    interface{} `json:"data"`    // 数据
}

// StatisticsData 统计数据
type StatisticsData struct {
	UserCount              int64   `json:"userCount"`              // 总用户数
	MerchantCount          int64   `json:"merchantCount"`          // 总商家数
	ProductCount           int64   `json:"productCount"`           // 总商品数
	OrderCount             int64   `json:"orderCount"`             // 总订单数
	TodayOrderCount        int64   `json:"todayOrderCount"`        // 今日订单数
	TodaySalesAmount       float64 `json:"todaySalesAmount"`       // 今日销售额
	TakeoutFoodCount       int64   `json:"takeoutFoodCount"`       // 总外卖食品数
	TakeoutOrderCount      int64   `json:"takeoutOrderCount"`      // 总外卖订单数
	TodayTakeoutOrderCount int64   `json:"todayTakeoutOrderCount"` // 今日外卖订单数
	TodayTakeoutSalesAmount float64 `json:"todayTakeoutSalesAmount"` // 今日外卖销售额
}

// StatisticsResponse 统计数据响应
type StatisticsResponse struct {
	Response
	Data StatisticsData `json:"data"` // 覆盖Response中的Data字段
}
