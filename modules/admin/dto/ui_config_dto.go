/**
 * UIConfig 管理员DTO
 *
 * 该文件定义了管理员模块下用于UI配置管理的数据传输对象
 */

package dto

import (
	"time"
)

// AdminUIConfigDTO 管理员模块使用的UI配置数据传输对象
type AdminUIConfigDTO struct {
	ID            int                 `json:"id"`                   // 配置ID
	FrontendPath  string              `json:"frontend_path"`        // 前端路径
	VersionHash   string              `json:"version_hash"`         // 版本识别号
	ConfigType    string              `json:"config_type"`          // 配置类型
	ConfigKey     string              `json:"config_key"`           // 配置唯一标识
	ConfigContent string              `json:"config_content"`       // 配置内容
	Status        int                 `json:"status"`               // 状态
	Remark        string              `json:"remark"`               // 备注
	CreatedAt     time.Time           `json:"created_at"`           // 创建时间
	UpdatedAt     time.Time           `json:"updated_at"`           // 更新时间
	Module        string              `json:"module"`               // 模块名称
	Title         string              `json:"title"`                // 配置标题
	Group         string              `json:"group"`                // 配置分组
	Version       string              `json:"version"`              // 配置版本
	Icon          string              `json:"icon"`                 // 图标名称
	DTO           string              `json:"dto"`                  // DTO数据
	GridItems     []*AdminGridInfoDTO `json:"grid_items,omitempty"` // 关联的网格布局项目
}

// AdminUIConfigCreateDTO 管理员模块用于创建UI配置的传输对象
type AdminUIConfigCreateDTO struct {
	FrontendPath  string `json:"frontend_path" valid:"Required"`  // 前端路径
	ConfigType    string `json:"config_type" valid:"Required"`    // 配置类型
	ConfigKey     string `json:"config_key"`                      // 配置唯一标识，如果为空则自动生成
	ConfigContent string `json:"config_content" valid:"Required"` // 配置内容
	Status        int    `json:"status" valid:"Range(0,1)"`       // 状态
	Remark        string `json:"remark"`                          // 备注
	Module        string `json:"module" valid:"Required"`         // 模块名称
	Title         string `json:"title" valid:"Required"`          // 配置标题
	Group         string `json:"group"`                           // 配置分组
	Version       string `json:"version" valid:"Required"`        // 配置版本
	Icon          string `json:"icon"`                            // 图标名称
	DTO           string `json:"dto"`                             // DTO数据
}

// AdminUIConfigUpdateDTO 管理员模块用于更新UI配置的传输对象
type AdminUIConfigUpdateDTO struct {
	FrontendPath  string `json:"frontend_path"`             // 前端路径
	ConfigContent string `json:"config_content"`            // 配置内容
	Status        int    `json:"status" valid:"Range(0,1)"` // 状态
	Remark        string `json:"remark"`                    // 备注
	Title         string `json:"title"`                     // 配置标题
	Group         string `json:"group"`                     // 配置分组
	Version       string `json:"version"`                   // 配置版本
	Icon          string `json:"icon"`                      // 图标名称
	DTO           string `json:"dto"`                       // DTO数据
}

// AdminUIConfigQueryDTO 管理员模块用于查询UI配置的传输对象
type AdminUIConfigQueryDTO struct {
	Module     string `json:"module" form:"module"`           // 模块名称
	ConfigType string `json:"config_type" form:"config_type"` // 配置类型
	Title      string `json:"title" form:"title"`             // 配置标题
	Status     int    `json:"status" form:"status"`           // 状态
	Group      string `json:"group" form:"group"`             // 配置分组
	Page       int    `json:"page" form:"page"`               // 页码
	PageSize   int    `json:"page_size" form:"page_size"`     // 每页数量
}

// AdminUIConfigStatusUpdateDTO 管理员模块用于更新UI配置状态的传输对象
type AdminUIConfigStatusUpdateDTO struct {
	Status int `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
}
