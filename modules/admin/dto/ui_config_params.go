/**
 * UI配置参数DTO
 *
 * 该文件定义了管理员模块下用于UI配置仓库层的参数DTO
 */

package dto

// UIConfigCreateParams 仓库层创建UI配置参数
type UIConfigCreateParams struct {
	FrontendPath  string `json:"frontend_path"`
	ConfigType    string `json:"config_type"`
	ConfigKey     string `json:"config_key"`
	ConfigContent string `json:"config_content"`
	Status        int    `json:"status"`
	Remark        string `json:"remark"`
	Module        string `json:"module"`
	Title         string `json:"title"`
	Group         string `json:"group"`
	Version       string `json:"version"`
	Icon          string `json:"icon"`
	DTO           string `json:"dto"`
}

// UIConfigUpdateParams 仓库层更新UI配置参数
type UIConfigUpdateParams struct {
	FrontendPath  string `json:"frontend_path"`
	ConfigContent string `json:"config_content"`
	Status        int    `json:"status"`
	Remark        string `json:"remark"`
	Title         string `json:"title"`
	Group         string `json:"group"`
	Version       string `json:"version"`
	Icon          string `json:"icon"`
	DTO           string `json:"dto"`
}
