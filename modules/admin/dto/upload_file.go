/**
 * 文件上传DTO
 *
 * 该文件定义了文件上传相关的数据传输对象，包括请求和响应格式。
 * 作为控制器和服务层之间数据交换的媒介。
 */

package dto

import "time"

// UploadFileRequest 文件上传请求
type UploadFileRequest struct {
	FileUsage      string `form:"file_usage" json:"file_usage"`     // 文件用途
	IsAnonymous    bool   `form:"is_anonymous" json:"is_anonymous"` // 是否匿名上传(默认false)
	AllowAnonymous bool   `form:"-" json:"-"`                       // 是否允许匿名上传(内部字段)
}

// UploadFileResponse 文件上传响应
type UploadFileResponse struct {
	ID          int64     `json:"id"`           // 文件ID
	FileName    string    `json:"file_name"`    // 文件名
	FilePath    string    `json:"file_path"`    // 文件路径
	FileURL     string    `json:"file_url"`     // 文件访问URL
	FileSize    int64     `json:"file_size"`    // 文件大小(字节)
	FileType    string    `json:"file_type"`    // 文件类型(MIME类型)
	FileExt     string    `json:"file_ext"`     // 文件扩展名
	FileUsage   string    `json:"file_usage"`   // 文件用途
	IsAnonymous bool      `json:"is_anonymous"` // 是否匿名上传
	Storage     string    `json:"storage"`      // 存储位置
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
}

// UploadFileBatchRequest 批量文件上传请求
type UploadFileBatchRequest struct {
	FileUsage   string `form:"file_usage" json:"file_usage"`     // 文件用途
	IsAnonymous bool   `form:"is_anonymous" json:"is_anonymous"` // 是否匿名上传
	FileCount   int    `form:"file_count" json:"file_count"`     // 文件数量
}

// UploadFileBatchResponse 批量文件上传响应
type UploadFileBatchResponse struct {
	SuccessCount int                   `json:"success_count"` // 成功上传数量
	FailCount    int                   `json:"fail_count"`    // 失败上传数量
	SuccessFiles []*UploadFileResponse `json:"success_files"` // 成功上传的文件
	FailFiles    []string              `json:"fail_files"`    // 失败上传的文件名
}

// UploadFileQueryRequest 文件查询请求
type UploadFileQueryRequest struct {
	FileUsage   string    `form:"file_usage" json:"file_usage"`     // 文件用途
	UserType    string    `form:"user_type" json:"user_type"`       // 上传者类型
	UserID      int64     `form:"user_id" json:"user_id"`           // 上传者ID
	Username    string    `form:"username" json:"username"`         // 上传者用户名
	IsAnonymous *bool     `form:"is_anonymous" json:"is_anonymous"` // 是否匿名上传
	StartTime   time.Time `form:"start_time" json:"start_time"`     // 开始时间
	EndTime     time.Time `form:"end_time" json:"end_time"`         // 结束时间
	Page        int       `form:"page" json:"page"`                 // 页码
	PageSize    int       `form:"page_size" json:"page_size"`       // 每页数量
}

// UploadFileListResponse 文件列表响应
type UploadFileListResponse struct {
	Total int64                 `json:"total"` // 总数
	List  []*UploadFileResponse `json:"list"`  // 文件列表
}

// UploadFileDeleteRequest 文件删除请求
type UploadFileDeleteRequest struct {
	ID int64 `form:"id" json:"id"` // 文件ID
}

// UploadConfigResponse 上传配置响应
type UploadConfigResponse struct {
	AllowAnonymous      bool     `json:"allow_anonymous"`       // 是否允许匿名上传
	MaxFileSize         int64    `json:"max_file_size"`         // 最大文件大小(字节)
	AllowedFileTypes    []string `json:"allowed_file_types"`    // 允许的文件类型
	StorageMode         string   `json:"storage_mode"`          // 存储模式
	UploadPath          string   `json:"upload_path"`           // 上传路径
	AllowedUsageTypes   []string `json:"allowed_usage_types"`   // 允许的用途类型
	AnonymousUsageTypes []string `json:"anonymous_usage_types"` // 匿名上传允许的用途类型
}
