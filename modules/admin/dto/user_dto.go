/**
 * 用户管理相关的数据传输对象
 *
 * 该文件定义了管理员模块中用户管理相关的请求和响应结构。
 * 包括用户查询、更新、密码重置等操作的数据结构。
 */

package dto

import "time"

// UserQueryRequest 用户查询请求
type UserQueryRequest struct {
	Username string `form:"username" description:"用户名"`
	Mobile   string `form:"mobile" description:"手机号"`
	Email    string `form:"email" description:"邮箱"`
	Status   int    `form:"status" description:"状态：0-禁用，1-正常"`
	Page     int    `form:"page" description:"页码"`
	PageSize int    `form:"page_size" description:"每页数量"`
}

// UserResponse 用户信息响应
type UserResponse struct {
	ID          int64     `json:"id" description:"用户ID"`
	Username    string    `json:"username" description:"用户名"`
	Nickname    string    `json:"nickname" description:"昵称"`
	Avatar      string    `json:"avatar" description:"头像"`
	Mobile      string    `json:"mobile" description:"手机号"`
	Email       string    `json:"email" description:"邮箱"`
	Gender      int       `json:"gender" description:"性别：0-未知，1-男，2-女"`
	Birthday    time.Time `json:"birthday" description:"生日"`
	Balance     float64   `json:"balance" description:"余额"`
	Points      int64     `json:"points" description:"积分"`
	Level       int       `json:"level" description:"等级"`
	Status      int       `json:"status" description:"状态：0-禁用，1-正常"`
	LastLoginAt time.Time `json:"last_login_at" description:"最后登录时间"`
	CreatedAt   time.Time `json:"created_at" description:"创建时间"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string    `form:"username" valid:"Required;MinSize(3);MaxSize(20)" description:"用户名"`
	Password string    `form:"password" valid:"Required;MinSize(6);MaxSize(20)" description:"密码"`
	Nickname string    `form:"nickname" valid:"Required;MaxSize(20)" description:"昵称"`
	Avatar   string    `form:"avatar" description:"头像"`
	Mobile   string    `form:"mobile" valid:"Required;Mobile" description:"手机号"`
	Email    string    `form:"email" valid:"Email" description:"邮箱"`
	Gender   int       `form:"gender" valid:"Range(0,2)" description:"性别：0-未知，1-男，2-女"`
	Birthday time.Time `form:"birthday" description:"生日"`
	Status   int       `form:"status" valid:"Range(0,1)" description:"状态：0-禁用，1-正常"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	ID       int64     `form:"id" valid:"Required" description:"用户ID"`
	Nickname string    `form:"nickname" valid:"MaxSize(20)" description:"昵称"`
	Mobile   string    `form:"mobile" valid:"Mobile" description:"手机号"`
	Avatar   string    `form:"avatar" description:"头像"`
	Gender   int       `form:"gender" valid:"Range(0,2)" description:"性别：0-未知，1-男，2-女"`
	Birthday time.Time `form:"birthday" description:"生日"`
	Email    string    `form:"email" valid:"Email" description:"邮箱"`
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	ID     int64 `form:"id" valid:"Required" description:"用户ID"`
	Status int   `form:"status" valid:"Required;Range(0,1)" description:"状态：0-禁用，1-正常"`
}

// ResetUserPasswordRequest 重置用户密码请求
type ResetUserPasswordRequest struct {
	ID          int64  `json:"id" valid:"Required" description:"用户ID"`
	NewPassword string `json:"new_password" valid:"Required;MinSize(6);MaxSize(20)" description:"新密码"`
}

// UpdateUserBalanceAndPointsRequest 更新用户余额和积分请求
type UpdateUserBalanceAndPointsRequest struct {
	ID      int64   `json:"-" form:"-" valid:"Required" description:"用户ID"`
	Balance float64 `json:"balance" description:"账户余额"` // 移除 valid 标签
	Points  int64   `json:"points" valid:"Min(0)" description:"积分"`
}
