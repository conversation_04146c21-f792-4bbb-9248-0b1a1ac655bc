/**
 * admin模块初始化
 *
 * 本文件负责admin模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保admin模块的功能正常启动。
 */

package admin

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/admin/models"
	"o_mall_backend/modules/admin/routers"
	"o_mall_backend/modules/admin/services/impl"
)

// Init 初始化admin模块
func Init() {
	logs.Info("初始化admin模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitAdminRouter()
	
	// 初始化AI配置
	initAIConfig()

	logs.Info("admin模块初始化完成")
}

// 注册ORM模型
func registerModels() {
	logs.Info("注册admin模块ORM模型...")

	// 注册admin相关模型
	orm.RegisterModel(
		new(models.Admin),
		new(models.AdminLog),
		new(models.AIConfig),
	)
}

// 初始化AI配置
func initAIConfig() {
	logs.Info("初始化admin模块AI配置...")
	
	// 获取AI服务
	aiService := impl.NewAIServiceImpl()
	
	// 初始化AI配置
	config, err := aiService.GetAIConfig()
	if err != nil {
		logs.Error("初始化AI配置失败: %v", err)
	} else {
		logs.Info("AI配置初始化成功: %v", config.DefaultModel)
	}
}
