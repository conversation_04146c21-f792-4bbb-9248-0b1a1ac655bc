/**
 * 管理员模型
 *
 * 该文件定义了管理员模型结构，用于管理员账号信息的存储和管理。
 * 管理员分为超级管理员和普通管理员，具有不同的权限级别。
 */

package models

import (
	"time"
	//"github.com/beego/beego/v2/client/orm"
)

// 管理员状态常量
const (
	AdminStatusNormal   = 1 // 正常状态
	AdminStatusDisabled = 0 // 禁用状态
)

// 管理员角色常量
const (
	AdminRoleSuper         = "super"          // 超级管理员
	AdminRoleNormal        = "normal"         // 普通管理员
	AdminRoleCustomerServ  = "customer_serv"  // 客服人员
	AdminRoleOperations    = "operations"      // 运营人员
)

// Admin 管理员模型
type Admin struct {
	// ID 管理员ID
	// @Column(admin_id)
	// @Type(BIGINT)
	// @Primary(true)
	// @AutoIncrement(true)
	// @Description(管理员唯一标识，自增主键)
	ID int64 `orm:"pk;auto;column(admin_id);description(管理员ID)" json:"id"`

	// Username 管理员用户名
	// @Column(username)
	// @Type(VARCHAR(50))
	// @Unique(true)
	// @NotNull(true)
	// @Description(管理员登录用户名，唯一标识，不可重复)
	Username string `orm:"size(50);unique;column(username);description(管理员用户名)" json:"username"`

	// Password 管理员密码
	// @Column(password)
	// @Type(VARCHAR(255))
	// @NotNull(true)
	// @Description(管理员登录密码，存储加密后的哈希值，不返回前端)
	Password string `orm:"size(255);column(password);description(管理员密码)" json:"-"`

	// Nickname 管理员昵称
	// @Column(nickname)
	// @Type(VARCHAR(50))
	// @NotNull(true)
	// @Description(管理员显示名称，用于界面展示)
	Nickname string `orm:"size(50);column(nickname);description(管理员昵称)" json:"nickname"`

	// Avatar 管理员头像
	// @Column(avatar)
	// @Type(VARCHAR(255))
	// @Default("")
	// @Description(管理员头像URL地址，支持完整的HTTP(S)链接)
	Avatar string `orm:"size(255);default();column(avatar);description(管理员头像)" json:"avatar"`

	// Mobile 管理员手机号
	// @Column(mobile)
	// @Type(VARCHAR(20))
	// @Default("")
	// @Description(管理员联系电话，支持国内外手机号格式)
	Mobile string `orm:"size(20);default();column(mobile);description(管理员手机号)" json:"mobile"`

	// Email 管理员邮箱
	// @Column(email)
	// @Type(VARCHAR(100))
	// @Default("")
	// @Description(管理员联系邮箱，用于系统通知和密码找回)
	Email string `orm:"size(100);default();column(email);description(管理员邮箱)" json:"email"`

	// Role 管理员角色
	// @Column(role)
	// @Type(VARCHAR(20))
	// @Default(normal)
	// @Enum(super,normal)
	// @Description(管理员角色类型，super:超级管理员，normal:普通管理员)
	Role string `orm:"size(20);default(normal);column(role);description(管理员角色) super:超级管理员 normal:普通管理员" json:"role"`

	// Permissions 管理员权限
	// @Column(permissions)
	// @Type(TEXT)
	// @Description(管理员权限配置，使用JSON格式存储权限列表，如["user.view","order.edit"])
	Permissions string `orm:"type(text);column(permissions);description(管理员权限使用JSON格式存储权限列表)" json:"permissions"`

	// Status 管理员状态
	// @Column(status)
	// @Type(TINYINT)
	// @Default(1)
	// @Enum(0,1)
	// @Description(管理员账号状态，1:正常，0:禁用，禁用后不能登录系统)
	Status int `orm:"default(1);column(status);description(管理员状态) 1:正常 0:禁用" json:"status"`

	// LastLoginAt 最后登录时间
	// @Column(last_login_at)
	// @Type(DATETIME)
	// @Nullable(true)
	// @Description(管理员最后一次成功登录系统的时间)
	LastLoginAt time.Time `orm:"type(datetime);null;column(last_login_at);description(最后登录时间)" json:"last_login_at"`

	// LastLoginIP 最后登录IP
	// @Column(last_login_ip)
	// @Type(VARCHAR(50))
	// @Default("")
	// @Description(管理员最后一次登录系统的IP地址，用于安全审计)
	LastLoginIP string `orm:"size(50);default();column(last_login_ip);description(最后登录IP)" json:"last_login_ip"`

	// CreatedAt 创建时间
	// @Column(created_at)
	// @Type(DATETIME)
	// @AutoCreateTime(true)
	// @Description(管理员账号创建时间，系统自动生成)
	CreatedAt time.Time `orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)" json:"created_at"`

	// UpdatedAt 更新时间
	// @Column(updated_at)
	// @Type(DATETIME)
	// @AutoUpdateTime(true)
	// @Description(管理员账号信息最后更新时间，系统自动维护)
	UpdatedAt time.Time `orm:"auto_now;type(datetime);column(updated_at);description(更新时间)" json:"updated_at"`
}

// TableName 指定表名
func (a *Admin) TableName() string {
	return "admin"
}

// 移除此处的init函数，避免重复注册模型
// 初始化模型
// func init() {
// 	orm.RegisterModel(new(Admin))
// }
