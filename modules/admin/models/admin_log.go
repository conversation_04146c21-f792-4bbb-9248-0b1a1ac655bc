/**
 * 管理员日志模型
 *
 * 该文件定义了管理员日志的数据模型，用于记录管理员的操作轨迹。
 * 记录管理员操作是系统安全审计的重要组成部分，便于追踪和排查问题。
 */

package models

import (
	"time"
)

// AdminLog 管理员日志模型
type AdminLog struct {
	ID         int64     `orm:"column(id);auto" json:"id"`                         // 日志ID
	AdminID    int64     `orm:"column(admin_id)" json:"admin_id"`                  // 管理员ID
	Username   string    `orm:"column(username);size(100)" json:"username"`        // 管理员用户名
	Module     string    `orm:"column(module);size(50)" json:"module"`             // 操作模块
	Type       string    `orm:"column(type);size(50)" json:"type"`                 // 操作类型(login,create,update,delete,export等)
	Content    string    `orm:"column(content);type(text)" json:"content"`         // 操作内容描述
	RequestURL string    `orm:"column(request_url);size(255)" json:"request_url"`  // 请求URL
	RequestData string   `orm:"column(request_data);type(text)" json:"request_data"` // 请求数据（JSON格式）
	IP         string    `orm:"column(ip);size(50)" json:"ip"`                     // 操作IP地址
	UserAgent  string    `orm:"column(user_agent);size(255)" json:"user_agent"`    // 用户代理
	TargetID   int64     `orm:"column(target_id);default(0)" json:"target_id"`     // 目标ID，如创建用户的用户ID
	TargetType string    `orm:"column(target_type);size(50)" json:"target_type"`   // 目标类型，如user,role等
	Status     int       `orm:"column(status);default(1)" json:"status"`           // 操作状态(0:失败,1:成功)
	Remark     string    `orm:"column(remark);size(255)" json:"remark"`            // 备注信息
	CreatedAt  time.Time `orm:"column(created_at);auto_now_add" json:"created_at"` // 创建时间
}

// TableName 设置表名
func (m *AdminLog) TableName() string {
	return "admin_log"
}

// 操作模块常量
const (
	AdminLogModuleAdmin      = "admin"      // 管理员模块
	AdminLogModuleRole       = "role"       // 角色模块
	AdminLogModulePermission = "permission" // 权限模块
	AdminLogModuleMenu       = "menu"       // 菜单模块
	AdminLogModuleCategory   = "category"   // 分类模块
	AdminLogModuleProduct    = "product"    // 产品模块
	AdminLogModuleMerchant   = "merchant"   // 商户模块
	AdminLogModuleOrder      = "order"      // 订单模块
	AdminLogModuleUser       = "user"       // 用户模块
	AdminLogModuleSystem     = "system"     // 系统模块
)

// 操作类型常量
const (
	AdminLogTypeLogin   = "login"   // 登录
	AdminLogTypeLogout  = "logout"  // 退出
	AdminLogTypeCreate  = "create"  // 创建
	AdminLogTypeUpdate  = "update"  // 更新
	AdminLogTypeDelete  = "delete"  // 删除
	AdminLogTypeView    = "view"    // 查看
	AdminLogTypeExport  = "export"  // 导出
	AdminLogTypeImport  = "import"  // 导入
	AdminLogTypeEnable  = "enable"  // 启用
	AdminLogTypeDisable = "disable" // 禁用
)

// 目标类型常量
const (
	AdminLogTargetTypeAdmin      = "admin"      // 管理员
	AdminLogTargetTypeRole       = "role"       // 角色
	AdminLogTargetTypePermission = "permission" // 权限
	AdminLogTargetTypeMenu       = "menu"       // 菜单
	AdminLogTargetTypeCategory   = "category"   // 分类
	AdminLogTargetTypeProduct    = "product"    // 产品
	AdminLogTargetTypeOrder      = "order"      // 订单
	AdminLogTargetTypeUser       = "user"       // 用户
	AdminLogTargetTypeMerchant   = "merchant"   // 商户
	AdminLogTargetTypeSystem     = "system"     // 系统
)
