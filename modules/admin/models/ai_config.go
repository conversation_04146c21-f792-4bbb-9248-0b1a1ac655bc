/**
 * AI配置模型
 *
 * 本文件定义了AI配置的模型，用于存储AI相关的配置信息，
 * 例如DeepSeek API的密钥、基础URL等。
 */

package models

import (
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// AIConfig AI配置模型
type AIConfig struct {
	ID           int       `orm:"column(id);auto;pk"`           // 自增ID
	APIKey       string    `orm:"column(api_key);size(255)"`    // API密钥
	BaseURL      string    `orm:"column(base_url);size(255)"`   // API基础URL
	DefaultModel string    `orm:"column(default_model);size(50)"` // 默认使用的模型
	Provider     string    `orm:"column(provider);size(50)"`    // 提供商，如DeepSeek
	Enabled      bool      `orm:"column(enabled);default(true)"` // 是否启用
	CreatedAt    time.Time `orm:"column(created_at);auto_now_add"` // 创建时间
	UpdatedAt    time.Time `orm:"column(updated_at);auto_now"`     // 更新时间
}

// TableName 设置表名
func (a *AIConfig) TableName() string {
	return "system_ai_config"
}

// InsertOrUpdate 插入或更新AI配置
func (a *AIConfig) InsertOrUpdate(o orm.Ormer) error {
	if o == nil {
		return errors.New("orm对象为空")
	}
	
	// 尝试创建表
	if err := CreateAIConfigTable(); err != nil {
		logs.Error("确保AI配置表存在失败: %v", err)
		return err
	}
	
	// 使用直接SQL插入或更新
	if a.ID > 0 {
		// 更新现有记录
		sql := "UPDATE system_ai_config SET api_key=?, base_url=?, default_model=?, provider=?, enabled=? WHERE id=?"
		_, err := o.Raw(sql, a.APIKey, a.BaseURL, a.DefaultModel, a.Provider, a.Enabled, a.ID).Exec()
		if err != nil {
			logs.Error("更新AI配置失败: %v", err)
			return err
		}
		logs.Info("更新AI配置成功，ID: %d", a.ID)
		return nil
	} else {
		// 插入新记录
		sql := "INSERT INTO system_ai_config (api_key, base_url, default_model, provider, enabled, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())"
		result, err := o.Raw(sql, a.APIKey, a.BaseURL, a.DefaultModel, a.Provider, a.Enabled).Exec()
		if err != nil {
			logs.Error("插入AI配置失败: %v", err)
			return err
		}
		
		// 获取插入ID
		id, err := result.LastInsertId()
		if err == nil {
			a.ID = int(id)
			logs.Info("插入AI配置成功，ID: %d", a.ID)
		}
		return nil
	}
}

// GetByProvider 根据提供商获取AI配置
func (a *AIConfig) GetByProvider(o orm.Ormer, provider string) error {
	if o == nil {
		return errors.New("orm对象为空")
	}
	
	if provider == "" {
		return errors.New("提供商不能为空")
	}
	
	logs.Info("查询AI配置，表名: %s, 提供商: %s", a.TableName(), provider)
	
	// 先确保表存在
	if err := CreateAIConfigTable(); err != nil {
		logs.Error("确保AI配置表存在失败: %v", err)
		return err
	}
	
	// 使用直接SQL查询
	var configs []AIConfig
	sql := "SELECT id, api_key, base_url, default_model, provider, enabled, created_at, updated_at FROM system_ai_config WHERE provider = ? LIMIT 1"
	count, err := o.Raw(sql, provider).QueryRows(&configs)
	
	if err != nil {
		logs.Error("查询AI配置失败: %v", err)
		return err
	}
	
	if count == 0 {
		logs.Info("未找到AI配置记录，提供商: %s", provider)
		return orm.ErrNoRows
	}
	
	// 复制查询结果到当前对象
	*a = configs[0]
	logs.Info("成功获取AI配置, ID: %d", a.ID)
	return nil
}

// GetByID 根据ID获取AI配置
func (a *AIConfig) GetByID(o orm.Ormer, id int) error {
	if o == nil {
		return errors.New("orm对象为空")
	}
	
	if id <= 0 {
		return errors.New("ID必须大于0")
	}
	
	// 先确保表存在
	if err := CreateAIConfigTable(); err != nil {
		logs.Error("确保AI配置表存在失败: %v", err)
		return err
	}
	
	// 使用直接SQL查询
	var configs []AIConfig
	sql := "SELECT id, api_key, base_url, default_model, provider, enabled, created_at, updated_at FROM system_ai_config WHERE id = ? LIMIT 1"
	count, err := o.Raw(sql, id).QueryRows(&configs)
	
	if err != nil {
		logs.Error("查询AI配置失败: %v", err)
		return err
	}
	
	if count == 0 {
		logs.Info("未找到AI配置记录，ID: %d", id)
		return orm.ErrNoRows
	}
	
	// 复制查询结果到当前对象
	*a = configs[0]
	logs.Info("成功获取AI配置, ID: %d", a.ID)
	return nil
}

// CreateAIConfigTable 创建AI配置表
func CreateAIConfigTable() error {
	// 获取数据库连接
	db, err := orm.GetDB("default")
	if err != nil {
		logs.Error("获取数据库连接失败: %v", err)
		return err
	}
	
	// 表名
	name := "system_ai_config"
	logs.Info("确保AI配置表存在: %s", name)
	
	// 创建表 - 使用完整的DDL语句，确保表结构正确
	sqlStr := `
CREATE TABLE IF NOT EXISTS ` + name + ` (
  id int(11) NOT NULL AUTO_INCREMENT,
  api_key varchar(255) DEFAULT '',
  base_url varchar(255) DEFAULT '',
  default_model varchar(50) DEFAULT '',
  provider varchar(50) DEFAULT '',
  enabled tinyint(1) DEFAULT 1,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_provider (provider)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
`
	// 执行SQL
	_, err = db.Exec(sqlStr)
	if err != nil {
		logs.Error("创建表失败: %v", err)
		return err
	}
	
	// 检查表是否创建成功
	var tableExists bool
	err = db.QueryRow("SELECT 1 FROM information_schema.tables WHERE table_name = ? AND table_schema = (SELECT DATABASE()) LIMIT 1", name).Scan(&tableExists)
	if err != nil {
		logs.Error("查询表是否存在失败: %v", err)
		return err
	}
	
	if !tableExists {
		logs.Error("表 %s 创建失败，无法在数据库中找到", name)
		return errors.New("表创建失败")
	}
	
	logs.Info("表 %s 已存在或成功创建", name)
	return nil
}
