/**
 * 文件上传模型
 *
 * 该文件定义了文件上传的数据模型，用于记录上传的文件信息及上传者信息。
 * 作为系统审计和文件管理的重要组成部分。
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// 文件上传者类型常量
const (
	UploadUserTypeAnonymous = "anonymous" // 匿名上传
	UploadUserTypeAdmin     = "admin"     // 管理员上传
	UploadUserTypeUser      = "user"      // 普通用户上传
	UploadUserTypeMerchant  = "merchant"  // 商户上传
)

// 文件用途常量
const (
	FileUsageAvatar    = "avatar"    // 头像
	FileUsageProduct   = "product"   // 商品图片
	FileUsageMerchant  = "merchant"  // 商户图片
	FileUsageComplaint = "complaint" // 投诉图片
	FileUsageOther     = "other"     // 其他用途
)

// 文件存储位置常量
const (
	FileStorageLocal = "local" // 本地存储
	FileStorageOSS   = "oss"   // 阿里云OSS
	FileStorageCOS   = "cos"   // 腾讯云COS
	FileStorageS3    = "s3"    // AWS S3
	FileStorageQiniu = "qiniu" // 七牛云
)

// UploadFile 文件上传记录模型
type UploadFile struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id"`                      // 主键ID
	FileName    string    `orm:"size(255);column(file_name)" json:"file_name"`      // 文件名
	FilePath    string    `orm:"size(500);column(file_path)" json:"file_path"`      // 文件路径
	FileURL     string    `orm:"size(500);column(file_url)" json:"file_url"`        // 文件访问URL
	FileSize    int64     `orm:"column(file_size)" json:"file_size"`                // 文件大小(字节)
	FileType    string    `orm:"size(100);column(file_type)" json:"file_type"`      // 文件类型(MIME类型)
	FileExt     string    `orm:"size(20);column(file_ext)" json:"file_ext"`         // 文件扩展名
	FileUsage   string    `orm:"size(50);column(file_usage)" json:"file_usage"`     // 文件用途
	Storage     string    `orm:"size(20);column(storage)" json:"storage"`           // 存储位置
	UploadIP    string    `orm:"size(50);column(upload_ip)" json:"upload_ip"`       // 上传IP
	UserID      int64     `orm:"column(user_id)" json:"user_id"`                    // 上传者ID
	UserType    string    `orm:"size(20);column(user_type)" json:"user_type"`       // 上传者类型
	Username    string    `orm:"size(50);column(username)" json:"username"`         // 上传者用户名
	IsAnonymous bool      `orm:"column(is_anonymous)" json:"is_anonymous"`          // 是否匿名上传
	Status      int       `orm:"column(status)" json:"status"`                      // 状态(0:无效,1:有效)
	CreatedAt   time.Time `orm:"auto_now_add;column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;column(updated_at)" json:"updated_at"`     // 更新时间
	DeletedAt   time.Time `orm:"null;column(deleted_at)" json:"deleted_at"`         // 删除时间(软删除)
}

// TableName 指定表名
func (m *UploadFile) TableName() string {
	return "upload_files"
}

// 注册模型
func init() {
	orm.RegisterModel(new(UploadFile))
}
