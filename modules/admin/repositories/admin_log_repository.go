/**
 * 管理员日志仓库实现
 *
 * 该文件提供对管理员日志数据库操作的封装，实现数据的存储和检索功能。
 * 作为持久层组件，提供给上层服务调用。
 */

package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"

	"o_mall_backend/modules/admin/models"
)

// AdminLogRepository 管理员日志仓库接口
type AdminLogRepository interface {
	// Create 创建日志
	Create(ctx context.Context, log *models.AdminLog) (int64, error)
	// GetByID 通过ID获取日志
	GetByID(ctx context.Context, id int64) (*models.AdminLog, error)
	// List 获取日志列表
	List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.AdminLog, int64, error)
	// GetLatestByAdminID 获取管理员最近的日志
	GetLatestByAdminID(ctx context.Context, adminID int64, limit int) ([]*models.AdminLog, error)
	// CountByCondition 根据条件统计日志数量
	CountByCondition(ctx context.Context, query map[string]interface{}) (int64, error)
}

// adminLogRepositoryImpl 管理员日志仓库实现
type adminLogRepositoryImpl struct{}

// NewAdminLogRepository 创建管理员日志仓库实例
func NewAdminLogRepository() AdminLogRepository {
	return &adminLogRepositoryImpl{}
}

// Create 创建日志
func (r *adminLogRepositoryImpl) Create(ctx context.Context, log *models.AdminLog) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(log)
	if err != nil {
		return 0, fmt.Errorf("创建管理员日志失败: %v", err)
	}
	return id, nil
}

// GetByID 通过ID获取日志
func (r *adminLogRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.AdminLog, error) {
	o := orm.NewOrm()
	log := &models.AdminLog{ID: id}
	err := o.Read(log)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("获取管理员日志失败: %v", err)
	}
	return log, nil
}

// List 获取日志列表
func (r *adminLogRepositoryImpl) List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.AdminLog, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.AdminLog))

	// 应用查询条件
	if adminID, ok := query["admin_id"].(int64); ok && adminID > 0 {
		qs = qs.Filter("admin_id", adminID)
	}
	if username, ok := query["username"].(string); ok && username != "" {
		qs = qs.Filter("username__icontains", username)
	}
	if module, ok := query["module"].(string); ok && module != "" {
		qs = qs.Filter("module", module)
	}
	if logType, ok := query["type"].(string); ok && logType != "" {
		qs = qs.Filter("type", logType)
	}
	if ip, ok := query["ip"].(string); ok && ip != "" {
		qs = qs.Filter("ip__icontains", ip)
	}
	if status, ok := query["status"].(int); ok && status != -1 {
		qs = qs.Filter("status", status)
	}
	if startTime, ok := query["start_time"].(time.Time); ok && !startTime.IsZero() {
		qs = qs.Filter("created_at__gte", startTime)
	}
	if endTime, ok := query["end_time"].(time.Time); ok && !endTime.IsZero() {
		qs = qs.Filter("created_at__lte", endTime)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, fmt.Errorf("统计管理员日志总数失败: %v", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	logs := make([]*models.AdminLog, 0)
	_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&logs)
	if err != nil {
		return nil, 0, fmt.Errorf("查询管理员日志列表失败: %v", err)
	}

	return logs, total, nil
}

// GetLatestByAdminID 获取管理员最近的日志
func (r *adminLogRepositoryImpl) GetLatestByAdminID(ctx context.Context, adminID int64, limit int) ([]*models.AdminLog, error) {
	if limit <= 0 {
		limit = 10 // 默认获取10条
	}

	o := orm.NewOrm()
	logs := make([]*models.AdminLog, 0)
	_, err := o.QueryTable(new(models.AdminLog)).
		Filter("admin_id", adminID).
		OrderBy("-created_at").
		Limit(limit).
		All(&logs)

	if err != nil {
		return nil, fmt.Errorf("获取管理员最近日志失败: %v", err)
	}

	return logs, nil
}

// CountByCondition 根据条件统计日志数量
func (r *adminLogRepositoryImpl) CountByCondition(ctx context.Context, query map[string]interface{}) (int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.AdminLog))

	// 应用查询条件
	if adminID, ok := query["admin_id"].(int64); ok && adminID > 0 {
		qs = qs.Filter("admin_id", adminID)
	}
	if module, ok := query["module"].(string); ok && module != "" {
		qs = qs.Filter("module", module)
	}
	if logType, ok := query["type"].(string); ok && logType != "" {
		qs = qs.Filter("type", logType)
	}
	if status, ok := query["status"].(int); ok && status != -1 {
		qs = qs.Filter("status", status)
	}
	if startTime, ok := query["start_time"].(time.Time); ok && !startTime.IsZero() {
		qs = qs.Filter("created_at__gte", startTime)
	}
	if endTime, ok := query["end_time"].(time.Time); ok && !endTime.IsZero() {
		qs = qs.Filter("created_at__lte", endTime)
	}

	// 获取总数
	count, err := qs.Count()
	if err != nil {
		return 0, fmt.Errorf("统计管理员日志数量失败: %v", err)
	}

	return count, nil
}
