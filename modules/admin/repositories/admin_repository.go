/**
 * 管理员仓库实现
 *
 * 该文件实现了管理员仓库接口，提供管理员数据的存取功能。
 * 通过Beego ORM操作数据库，实现管理员数据的增删改查。
 */

package repositories

import (
	"context"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/admin/models"
)

// AdminRepositoryImpl 管理员仓库实现
type AdminRepositoryImpl struct{}

// NewAdminRepository 创建管理员仓库实例
func NewAdminRepository() AdminRepository {
	return &AdminRepositoryImpl{}
}

// Create 创建管理员
func (r *AdminRepositoryImpl) Create(ctx context.Context, admin *models.Admin) (int64, error) {
	o := orm.NewOrm()

	// 处理权限数据
	if admin.Permissions != "" {
		// 已经是JSON字符串，不需要再处理
	} else {
		// 初始化空权限
		admin.Permissions = "[]"
	}

	// 插入数据
	id, err := o.Insert(admin)
	if err != nil {
		logs.Error("创建管理员失败: %v", err)
		return 0, err
	}

	return id, nil
}

// Update 更新管理员信息
func (r *AdminRepositoryImpl) Update(ctx context.Context, admin *models.Admin) error {
	o := orm.NewOrm()

	// 设置更新时间
	admin.UpdatedAt = time.Now()

	// 更新数据，只更新非零值字段
	_, err := o.Update(admin, "nickname", "avatar", "mobile", "email", "role", "permissions", "updated_at")
	if err != nil {
		logs.Error("更新管理员信息失败: %v", err)
		return err
	}

	return nil
}

// CountAdmins 获取已启用的管理员数量
func (r *AdminRepositoryImpl) CountAdmins(ctx context.Context) (int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.Admin))

	// 过滤已启用的管理员
	qs = qs.Filter("status", models.AdminStatusNormal)

	// 计算总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计已启用的管理员数量失败: %v", err)
		return 0, err
	}

	return total, nil
}

// Delete 删除管理员
func (r *AdminRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 构建查询条件
	admin := &models.Admin{ID: id}

	// 删除数据
	_, err := o.Delete(admin)
	if err != nil {
		logs.Error("删除管理员失败: %v", err)
		return err
	}

	return nil
}

// GetByID 根据ID获取管理员
func (r *AdminRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.Admin, error) {
	o := orm.NewOrm()

	// 构建查询条件
	admin := &models.Admin{ID: id}

	// 查询数据
	err := o.Read(admin)
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有找到数据
			return nil, nil
		}
		logs.Error("查询管理员失败: %v", err)
		return nil, err
	}

	return admin, nil
}

// GetByUsername 根据用户名获取管理员
func (r *AdminRepositoryImpl) GetByUsername(ctx context.Context, username string) (*models.Admin, error) {
	o := orm.NewOrm()

	// 构建查询条件
	admin := &models.Admin{Username: username}

	// 查询数据
	err := o.Read(admin, "username")
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有找到数据
			return nil, nil
		}
		logs.Error("查询管理员失败: %v", err)
		return nil, err
	}

	return admin, nil
}

// List 获取管理员列表
func (r *AdminRepositoryImpl) List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.Admin, int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.Admin))

	// 应用查询条件
	if username, ok := query["username"].(string); ok && username != "" {
		qs = qs.Filter("username__icontains", username)
	}

	if nickname, ok := query["nickname"].(string); ok && nickname != "" {
		qs = qs.Filter("nickname__icontains", nickname)
	}

	if role, ok := query["role"].(string); ok && role != "" {
		qs = qs.Filter("role", role)
	}

	if status, ok := query["status"].(int); ok && status != -1 {
		qs = qs.Filter("status", status)
	}

	// 计算总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计管理员总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize

	// 查询数据
	var admins []*models.Admin
	_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&admins)
	if err != nil {
		logs.Error("查询管理员列表失败: %v", err)
		return nil, 0, err
	}

	return admins, total, nil
}

// UpdatePassword 更新管理员密码
func (r *AdminRepositoryImpl) UpdatePassword(ctx context.Context, id int64, password string) error {
	o := orm.NewOrm()

	// 更新密码和更新时间
	_, err := o.Update(&models.Admin{
		ID:        id,
		Password:  password,
		UpdatedAt: time.Now(),
	}, "password", "updated_at")

	if err != nil {
		logs.Error("更新管理员密码失败: %v", err)
		return err
	}

	return nil
}

// UpdateStatus 更新管理员状态
func (r *AdminRepositoryImpl) UpdateStatus(ctx context.Context, id int64, status int) error {
	o := orm.NewOrm()

	// 更新状态和更新时间
	_, err := o.Update(&models.Admin{
		ID:        id,
		Status:    status,
		UpdatedAt: time.Now(),
	}, "status", "updated_at")

	if err != nil {
		logs.Error("更新管理员状态失败: %v", err)
		return err
	}

	return nil
}

// UpdateLoginInfo 更新管理员登录信息
func (r *AdminRepositoryImpl) UpdateLoginInfo(ctx context.Context, id int64, ip string) error {
	o := orm.NewOrm()

	// 更新登录信息
	_, err := o.Update(&models.Admin{
		ID:          id,
		LastLoginAt: time.Now(),
		LastLoginIP: ip,
		UpdatedAt:   time.Now(),
	}, "last_login_at", "last_login_ip", "updated_at")

	if err != nil {
		logs.Error("更新管理员登录信息失败: %v", err)
		return err
	}

	return nil
}
