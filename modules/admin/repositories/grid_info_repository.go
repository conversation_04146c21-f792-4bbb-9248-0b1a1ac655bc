/**
 * grid_info_repository.go
 * 网格布局仓库实现
 *
 * 该文件实现了管理员模块中的网格布局仓库接口，负责网格布局数据的存储和检索。
 */

// AdminGridInfoRepositoryImpl 管理员模块网格布局仓库实现
// 该文件实现了管理员模块对网格布局（GridInfo）的数据访问逻辑
// 主要负责从底层仓库获取数据并转换为管理员模块的DTO对象
// 2025-04-18 by zhangerhao

package repositories

import (
	"context"
	"encoding/json"
	"log"

	"o_mall_backend/modules/admin/dto"
	uiConfigDTO "o_mall_backend/modules/ui_config/dto"
	gridInfoRepo "o_mall_backend/modules/ui_config/repositories"
	"o_mall_backend/modules/ui_config/models"
)

// AdminGridInfoRepository 管理员模块网格布局仓库接口
type AdminGridInfoRepository interface {
	// ListGridInfos 获取网格布局列表
	ListGridInfos(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.AdminGridInfoDTO, int64, error)

	// GetGridInfo 根据ID获取网格布局详情
	GetGridInfo(ctx context.Context, id int) (*dto.AdminGridInfoResponseDTO, error)

	// CreateGridInfo 创建网格布局
	CreateGridInfo(ctx context.Context, gridInfo *dto.AdminGridInfoCreateDTO) (int, error)

	// UpdateGridInfo 更新网格布局
	UpdateGridInfo(ctx context.Context, id int, gridInfo *dto.AdminGridInfoUpdateDTO) error

	// DeleteGridInfo 删除网格布局
	DeleteGridInfo(ctx context.Context, id int) error

	// UpdateGridInfoStatus 更新网格布局状态
	UpdateGridInfoStatus(ctx context.Context, id int, status int) error

	// BatchUpdateGridInfoPosition 批量更新网格布局位置
	BatchUpdateGridInfoPosition(ctx context.Context, items []*dto.AdminGridInfoPositionItem) error

	// ListGridInfosByUIConfigID 根据UI配置ID获取关联的网格布局列表
	ListGridInfosByUIConfigID(ctx context.Context, uiConfigID int) ([]*dto.AdminGridInfoDTO, error)

	// ListUIConfigsByGridInfoID 根据网格布局ID获取关联的UI配置列表
	ListUIConfigsByGridInfoID(ctx context.Context, gridInfoID int) ([]*dto.AdminUIConfigSimpleDTO, error)

	// AddUIConfigsToGridInfo 为网格布局添加UI配置关联
	AddUIConfigsToGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error

	// RemoveUIConfigsFromGridInfo 从网格布局移除UI配置关联
	RemoveUIConfigsFromGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error

	// UpdateGridInfoUIConfigs 更新网格布局关联的UI配置
	UpdateGridInfoUIConfigs(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error

	// DeleteUIConfigGridRelation 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
	DeleteUIConfigGridRelation(ctx context.Context, gridInfoID int, uiConfigID int) error

	// UpdateBatchUIConfigGridRelation 批量设置UI配置与多个网格布局的关联关系
	// @description 将一个UIConfigId批量关联到多个GridInfoId
	UpdateBatchUIConfigGridRelation(ctx context.Context, batchDTO *dto.AdminBatchUIConfigGridRelationDTO) error
}

// AdminGridInfoRepositoryImpl 管理员模块网格布局仓库实现
type AdminGridInfoRepositoryImpl struct {
	gridInfoRepo gridInfoRepo.GridInfoRepository
}

// NewAdminGridInfoRepository 创建管理员模块网格布局仓库实例
func NewAdminGridInfoRepository() AdminGridInfoRepository {
	return &AdminGridInfoRepositoryImpl{
		gridInfoRepo: gridInfoRepo.NewGridInfoRepository(),
	}
}

// ListGridInfos 获取网格布局列表
// @description 获取网格布局列表并转换为管理员DTO
func (r *AdminGridInfoRepositoryImpl) ListGridInfos(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.AdminGridInfoDTO, int64, error) {
	// 使用网格布局模块的仓库
	gridItems, total, err := r.gridInfoRepo.ListGridInfos(ctx, query, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// 转换为管理员模块的DTO
	adminGridItems := make([]*dto.AdminGridInfoDTO, 0, len(gridItems))
	for _, item := range gridItems {
		// 直接透传Content和DTO字段，无需类型断言
		adminGridItem := &dto.AdminGridInfoDTO{
			ID:         item.ID,
			UIConfigID: item.UIConfigID,
			Name:       item.Name,
			Content:    item.Content, // 直接赋值
			Position:   item.Position, // 使用Position字段替代废弃的位置字段
			API:        item.API,
			DTO:        item.DTO,     // 直接赋值
			Remark:     item.Remark,
			Status:     item.Status,
		}
		adminGridItems = append(adminGridItems, adminGridItem)
	}

	return adminGridItems, total, nil
}

// GetGridInfo 根据ID获取网格布局详情
func (r *AdminGridInfoRepositoryImpl) GetGridInfo(ctx context.Context, id int) (*dto.AdminGridInfoResponseDTO, error) {
	// 使用网格布局模块的仓库
	gridItem, err := r.gridInfoRepo.GetGridInfo(ctx, id)
	if err != nil {
		return nil, err
	}

	if gridItem == nil {
		return nil, nil
	}

	// 转换为管理员模块的响应DTO
	var contentObj interface{} = gridItem.Content
	var dtoObj interface{} = gridItem.DTO

	adminGridItem := &dto.AdminGridInfoResponseDTO{
		ID:         gridItem.ID,
		UIConfigID: gridItem.UIConfigID,
		Name:       gridItem.Name,
		Content:    contentObj,
		Position:   gridItem.Position, // 使用Position字段替代废弃的位置字段
		API:        gridItem.API,
		DTO:        dtoObj,
		Remark:     gridItem.Remark,
		Status:     gridItem.Status,
	}

	return adminGridItem, nil
}

// CreateGridInfo 创建网格布局
func (r *AdminGridInfoRepositoryImpl) CreateGridInfo(ctx context.Context, gridInfo *dto.AdminGridInfoCreateDTO) (int, error) {
	// 处理前端传递的uiConfigId字段
	if gridInfo.UIConfigID == 0 && gridInfo.UIConfigIdFrontend > 0 {
		gridInfo.UIConfigID = gridInfo.UIConfigIdFrontend
	}

	// 将Position字段从interface{}转换为string
	var positionStr string
	if gridInfo.Position != nil {
		// 如果Position是字符串类型，直接使用
		if posStr, ok := gridInfo.Position.(string); ok {
			positionStr = posStr
		} else {
			// 否则将其序列化为JSON字符串
			posBytes, err := json.Marshal(gridInfo.Position)
			if err == nil {
				positionStr = string(posBytes)
			}
		}
	}

	// 转换为网格布局模块的参数
	createParams := &uiConfigDTO.GridInfoCreateParams{
		UIConfigID: gridInfo.UIConfigID,
		Name:       gridInfo.Name,
		Content:    gridInfo.Content,
		Position:   positionStr, // 使用转换后的字符串
		API:        gridInfo.API,
		DTO:        gridInfo.DTO,
		Remark:     gridInfo.Remark,
		Status:     gridInfo.Status,
	}

	// 使用网格布局模块的仓库
	return r.gridInfoRepo.CreateGridInfo(ctx, createParams)
}

// UpdateGridInfo 更新网格布局
func (r *AdminGridInfoRepositoryImpl) UpdateGridInfo(ctx context.Context, id int, gridInfo *dto.AdminGridInfoUpdateDTO) error {
	// 处理前端传递的uiConfigId字段
	if gridInfo.UIConfigIdFrontend > 0 {
		// 如果需要，这里可以处理前端传递的uiConfigId
	}

	// 将Position字段从interface{}转换为string
	var positionStr string
	if gridInfo.Position != nil {
		// 如果Position是字符串类型，直接使用
		if posStr, ok := gridInfo.Position.(string); ok {
			positionStr = posStr
		} else {
			// 否则将其序列化为JSON字符串
			posBytes, err := json.Marshal(gridInfo.Position)
			if err == nil {
				positionStr = string(posBytes)
			} else {
				log.Println("[UpdateGridInfo] 序列化position对象失败: ", err)
			}
		}
	}

	// 将Permission字段从[]int转换为JSON字符串
	var permissionStr string
	if len(gridInfo.Permission) > 0 {
		permBytes, err := json.Marshal(gridInfo.Permission)
		if err == nil {
			permissionStr = string(permBytes)
		}
	}

	// 如果前端传递了 uiConfigId 和 position 字段，则更新 UIConfigGridRelation 表
	if gridInfo.UIConfigIdFrontend > 0 && positionStr != "" {
		err := models.UpdatePositionByUIConfigIdAndGridInfoId(gridInfo.UIConfigIdFrontend, id, positionStr)
		if err != nil {
			log.Println("[UpdateGridInfo] 更新UIConfigGridRelation表position字段失败: ", err)
			// 可根据实际需求决定是否直接返回错误，这里只记录日志
		}
	}

	// 转换为网格布局模块的参数
	updateParams := &uiConfigDTO.GridInfoUpdateParams{
		Name:       gridInfo.Name,
		Content:    gridInfo.Content,
		Position:   positionStr, // 使用转换后的字符串
		API:        gridInfo.API,
		DTO:        gridInfo.DTO,
		Remark:     gridInfo.Remark,
		Status:     gridInfo.Status,
		UIConfigID: gridInfo.UIConfigIdFrontend, // 添加UIConfigID字段传递
	}

	// 处理Step字段，转换为JSON字符串
	if len(gridInfo.Step) > 0 {
		stepBytes, err := json.Marshal(gridInfo.Step)
		if err == nil {
			updateParams.Step = string(stepBytes)
			log.Println("[UpdateGridInfo] 序列化后的Step字段: ", updateParams.Step)
		} else {
			log.Println("[UpdateGridInfo] 序列化step数组失败: ", err)
			// 即使序列化失败，也尝试保存原始数据
			updateParams.Step = "[]"
		}
	} else {
		// 确保即使是空数组也能正确保存
		updateParams.Step = "[]"
		log.Println("[UpdateGridInfo] Step字段为空，使用默认值: []")
	}

	// 使用已转换的权限字符串
	updateParams.Permission = permissionStr

	// 使用网格布局模块的仓库
	return r.gridInfoRepo.UpdateGridInfo(ctx, id, updateParams)
}

// DeleteGridInfo 删除网格布局
func (r *AdminGridInfoRepositoryImpl) DeleteGridInfo(ctx context.Context, id int) error {
	// 使用网格布局模块的仓库
	return r.gridInfoRepo.DeleteGridInfo(ctx, id)
}

// UpdateGridInfoStatus 更新网格布局状态
func (r *AdminGridInfoRepositoryImpl) UpdateGridInfoStatus(ctx context.Context, id int, status int) error {
	// 使用网格布局模块的仓库
	return r.gridInfoRepo.UpdateGridInfoStatus(ctx, id, status)
}

// BatchUpdateGridInfoPosition 批量更新网格布局位置
func (r *AdminGridInfoRepositoryImpl) BatchUpdateGridInfoPosition(ctx context.Context, items []*dto.AdminGridInfoPositionItem) error {
	// 转换为网格布局模块的参数
	posItems := make([]*uiConfigDTO.GridInfoRepositoryPositionItem, 0, len(items))
	for _, item := range items {
		// 将Position字段转换为JSON字符串
		var positionStr string
		if item.Position != nil {
			positionBytes, err := json.Marshal(item.Position)
			if err == nil {
				positionStr = string(positionBytes)
			}
		}

		// 转换 LastUiPosition 数据
		// var lastUiPositionDTO *uiConfigDTO.LastUiPositionDTO
		// if item.Position != nil {
		// 	// 尝试从position对象中提取lastUiPosition
		// 	if posMap, ok := item.Position.(map[string]interface{}); ok {
		// 		if lastPos, ok := posMap["lastUiPosition"].(map[string]interface{}); ok {
		// 			lastUiPositionDTO = &uiConfigDTO.LastUiPositionDTO{}
		// 			if left, ok := lastPos["left"].(float64); ok {
		// 				lastUiPositionDTO.Left = left
		// 			}
		// 			if top, ok := lastPos["top"].(float64); ok {
		// 				lastUiPositionDTO.Top = top
		// 			}
		// 		}
		// 	}
		// }

		posItem := &uiConfigDTO.GridInfoRepositoryPositionItem{
			ID:         item.ID,
			UIConfigID: item.UIConfigID,
			Position:   positionStr,
		}
		posItems = append(posItems, posItem)
	}

	// 使用网格布局模块的仓库
	return r.gridInfoRepo.BatchUpdateGridInfoPosition(ctx, posItems)
}

// ListGridInfosByUIConfigID 根据UI配置ID获取关联的网格布局列表
func (r *AdminGridInfoRepositoryImpl) ListGridInfosByUIConfigID(ctx context.Context, uiConfigID int) ([]*dto.AdminGridInfoDTO, error) {
	// 调用UI配置模块的仓库方法获取网格布局列表
	gridItems, err := r.gridInfoRepo.ListGridInfosByUIConfigID(ctx, uiConfigID)
	if err != nil {
		return nil, err
	}

	// 转换为管理员模块的DTO
	adminGridItems := make([]*dto.AdminGridInfoDTO, 0, len(gridItems))
	for _, item := range gridItems {
		// 处理内容和DTO字段，可能需要特殊处理
		var content string
		if item.Content != nil {
			if contentStr, ok := item.Content.(string); ok {
				content = contentStr
			}
		}

		var dtoStr string
		if item.DTO != nil {
			if dtoValue, ok := item.DTO.(string); ok {
				dtoStr = dtoValue
			}
		}

		// 获取关联的UI配置ID列表
		uiConfigIDs := make([]int, 0)
		if len(item.UIConfigs) > 0 {
			for _, uiConfig := range item.UIConfigs {
				uiConfigIDs = append(uiConfigIDs, uiConfig.ID)
			}
		}

		adminGridItem := &dto.AdminGridInfoDTO{
			ID:          item.ID,
			UIConfigID:  item.UIConfigID, // 向后兼容
			UIConfigIDs: uiConfigIDs,     // 多对多关系
			Name:        item.Name,
			Content:     content,
			Position:    item.Position, // 使用Position字段替代废弃的位置字段
			API:         item.API,
			DTO:         dtoStr,
			Remark:      item.Remark,
			Status:      item.Status,
		}
		adminGridItems = append(adminGridItems, adminGridItem)
	}

	return adminGridItems, nil
}

// ListUIConfigsByGridInfoID 根据网格布局ID获取关联的UI配置列表
func (r *AdminGridInfoRepositoryImpl) ListUIConfigsByGridInfoID(ctx context.Context, gridInfoID int) ([]*dto.AdminUIConfigSimpleDTO, error) {
	// 调用UI配置模块的仓库方法获取关联的UI配置列表
	uiConfigs, err := r.gridInfoRepo.ListUIConfigsByGridInfoID(ctx, gridInfoID)
	if err != nil {
		return nil, err
	}

	// 转换为管理员模块的DTO
	adminUIConfigs := make([]*dto.AdminUIConfigSimpleDTO, 0, len(uiConfigs))
	for _, uiConfig := range uiConfigs {
		adminUIConfig := &dto.AdminUIConfigSimpleDTO{
			ID:           uiConfig.ID,
			Module:       uiConfig.Module,
			Title:        uiConfig.Title,
			ConfigType:   uiConfig.ConfigType,
			FrontendPath: uiConfig.FrontendPath,
		}
		adminUIConfigs = append(adminUIConfigs, adminUIConfig)
	}

	return adminUIConfigs, nil
}

// AddUIConfigsToGridInfo 为网格布局添加UI配置关联
func (r *AdminGridInfoRepositoryImpl) AddUIConfigsToGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error {
	// 调用UI配置模块的仓库方法添加关联关系
	err := r.gridInfoRepo.AddUIConfigsToGridInfo(ctx, relationDTO.GridInfoID, relationDTO.UIConfigIDs)
	if err != nil {
		return err
	}

	return nil
}

// RemoveUIConfigsFromGridInfo 从网格布局移除UI配置关联
func (r *AdminGridInfoRepositoryImpl) RemoveUIConfigsFromGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error {
	// 调用UI配置模块的仓库方法移除关联关系
	err := r.gridInfoRepo.RemoveUIConfigsFromGridInfo(ctx, relationDTO.GridInfoID, relationDTO.UIConfigIDs)
	if err != nil {
		return err
	}

	return nil
}

// UpdateGridInfoUIConfigs 更新网格布局关联的UI配置
func (r *AdminGridInfoRepositoryImpl) UpdateGridInfoUIConfigs(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error {
	// 调用UI配置模块的仓库方法更新关联关系
	err := r.gridInfoRepo.UpdateGridInfoUIConfigs(ctx, relationDTO.GridInfoID, relationDTO.UIConfigIDs)
	if err != nil {
		return err
	}

	return nil
}

// DeleteUIConfigGridRelation 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
func (r *AdminGridInfoRepositoryImpl) DeleteUIConfigGridRelation(ctx context.Context, gridInfoID int, uiConfigID int) error {
	return r.gridInfoRepo.DeleteUIConfigGridRelation(ctx, gridInfoID, uiConfigID)
}

// UpdateBatchUIConfigGridRelation 批量设置UI配置与多个网格布局的关联关系
// @description 将一个UIConfigId批量关联到多个GridInfoId
// 解决：UI配置模块GridInfoRepository接口未声明UpdateBatchUIConfigGridRelation导致的调用错误
// 请在 modules/ui_config/repositories/interfaces.go 的GridInfoRepository接口中添加如下方法声明：
// UpdateBatchUIConfigGridRelation(ctx context.Context, uiConfigId int, gridInfoIds []int) error
// 本文件只负责调用，不重复声明接口。
func (r *AdminGridInfoRepositoryImpl) UpdateBatchUIConfigGridRelation(ctx context.Context, batchDTO *dto.AdminBatchUIConfigGridRelationDTO) error {
	// 调用UI配置模块的仓库方法批量插入关联关系
	return r.gridInfoRepo.UpdateBatchUIConfigGridRelation(ctx, batchDTO.UIConfigId, batchDTO.GridInfoIds)
}
