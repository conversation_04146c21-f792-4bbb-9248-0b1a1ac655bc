/**
 * 管理员仓库接口
 *
 * 该文件定义了管理员模块的仓库层接口，声明了管理员数据访问的方法。
 * 通过接口定义与实现分离，便于单元测试和功能扩展。
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/admin/models"
)

// AdminRepository 管理员仓库接口
type AdminRepository interface {
	// Create 创建管理员
	// 参数：
	//   - ctx: 上下文
	//   - admin: 管理员信息
	// 返回：
	//   - id: 创建的管理员ID
	//   - err: 错误信息
	Create(ctx context.Context, admin *models.Admin) (int64, error)

	// Update 更新管理员信息
	// 参数：
	//   - ctx: 上下文
	//   - admin: 管理员信息
	// 返回：
	//   - err: 错误信息
	Update(ctx context.Context, admin *models.Admin) error

	// Delete 删除管理员
	// 参数：
	//   - ctx: 上下文
	//   - id: 管理员ID
	// 返回：
	//   - err: 错误信息
	Delete(ctx context.Context, id int64) error

	// GetByID 根据ID获取管理员
	// 参数：
	//   - ctx: 上下文
	//   - id: 管理员ID
	// 返回：
	//   - admin: 管理员信息
	//   - err: 错误信息
	GetByID(ctx context.Context, id int64) (*models.Admin, error)

	// GetByUsername 根据用户名获取管理员
	// 参数：
	//   - ctx: 上下文
	//   - username: 管理员用户名
	// 返回：
	//   - admin: 管理员信息
	//   - err: 错误信息
	GetByUsername(ctx context.Context, username string) (*models.Admin, error)

	// List 获取管理员列表
	// 参数：
	//   - ctx: 上下文
	//   - query: 查询条件
	//   - page: 页码
	//   - pageSize: 每页数量
	// 返回：
	//   - admins: 管理员列表
	//   - total: 总数
	//   - err: 错误信息
	List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.Admin, int64, error)

	// UpdatePassword 更新管理员密码
	// 参数：
	//   - ctx: 上下文
	//   - id: 管理员ID
	//   - password: 新密码（已加密）
	// 返回：
	//   - err: 错误信息
	UpdatePassword(ctx context.Context, id int64, password string) error

	// UpdateStatus 更新管理员状态
	// 参数：
	//   - ctx: 上下文
	//   - id: 管理员ID
	//   - status: 新状态
	// 返回：
	//   - err: 错误信息
	UpdateStatus(ctx context.Context, id int64, status int) error

	// UpdateLoginInfo 更新管理员登录信息
	// 参数：
	//   - ctx: 上下文
	//   - id: 管理员ID
	//   - ip: 登录IP
	// 返回：
	//   - err: 错误信息
	UpdateLoginInfo(ctx context.Context, id int64, ip string) error

	// CountAdmins 获取已启用的管理员数量
	// 参数：
	//   - ctx: 上下文
	// 返回：
	//   - total: 已启用的管理员数量
	//   - err: 错误信息
	CountAdmins(ctx context.Context) (int64, error)
}
