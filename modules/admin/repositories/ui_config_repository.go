/**
 * ui_config_repository.go
 * UI配置仓库实现
 *
 * 该文件实现了管理员模块中的UI配置仓库接口，负责UI配置数据的存储和检索。
 */

package repositories

import (
	"context"
	"o_mall_backend/modules/admin/dto"
	uiConfigRepo "o_mall_backend/modules/ui_config/repositories"
	uiConfigDTO "o_mall_backend/modules/ui_config/dto"
	"fmt"
)

// AdminUIConfigRepository 管理员模块UI配置仓库接口
type AdminUIConfigRepository interface {
	// ListUIConfigs 获取UI配置列表
	ListUIConfigs(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.AdminUIConfigDTO, int64, error)

	// GetUIConfig 根据ID获取UI配置详情
	GetUIConfig(ctx context.Context, id int) (*dto.AdminUIConfigDTO, error)

	// CreateUIConfig 创建UI配置
	CreateUIConfig(ctx context.Context, config *dto.AdminUIConfigCreateDTO) (int, error)

	// UpdateUIConfig 更新UI配置
	UpdateUIConfig(ctx context.Context, id int, config *dto.AdminUIConfigUpdateDTO) error

	// DeleteUIConfig 删除UI配置
	DeleteUIConfig(ctx context.Context, id int) error

	// UpdateUIConfigStatus 更新UI配置状态
	UpdateUIConfigStatus(ctx context.Context, id int, status int) error
}

// AdminUIConfigRepositoryImpl 管理员模块UI配置仓库实现
type AdminUIConfigRepositoryImpl struct {
	uiConfigRepo uiConfigRepo.UIConfigRepository
}

// NewAdminUIConfigRepository 创建管理员模块UI配置仓库实例
func NewAdminUIConfigRepository() AdminUIConfigRepository {
	return &AdminUIConfigRepositoryImpl{
		uiConfigRepo: uiConfigRepo.NewUIConfigRepository(),
	}
}

// ListUIConfigs 获取UI配置列表
func (r *AdminUIConfigRepositoryImpl) ListUIConfigs(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.AdminUIConfigDTO, int64, error) {
	// 使用UI配置模块的仓库
	uiConfigs, total, err := r.uiConfigRepo.ListUIConfigs(ctx, query, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// 转换为管理员模块的DTO
	adminUIConfigs := make([]*dto.AdminUIConfigDTO, 0, len(uiConfigs))
	for _, uiConfig := range uiConfigs {
		adminUIConfig := &dto.AdminUIConfigDTO{
			ID:            uiConfig.ID,
			FrontendPath:  uiConfig.FrontendPath,
			VersionHash:   uiConfig.VersionHash,
			ConfigType:    uiConfig.ConfigType,
			ConfigKey:     uiConfig.ConfigKey,
			ConfigContent: uiConfig.ConfigContent,
			Status:        uiConfig.Status,
			Remark:        uiConfig.Remark,
			CreatedAt:     uiConfig.CreatedAt,
			UpdatedAt:     uiConfig.UpdatedAt,
			Module:        uiConfig.Module,
			Title:         uiConfig.Title,
			Group:         uiConfig.Group,
			Version:       uiConfig.Version,
			Icon:          uiConfig.Icon,
			DTO:           uiConfig.DTO,
			GridItems:     make([]*dto.AdminGridInfoDTO, 0), // 初始化一个空数组
		}

		// 转换网格布局项目
		for _, item := range uiConfig.GridItems {
			// 处理内容和DTO字段，可能需要特殊处理
			var content string
			var name string
			var api string
			var dtoStr string
			var remark string
			var status int
			var uiConfigID int
			
			// 从SimpleGridInfoDTO获取基本字段
			id := item.ID
			name = item.Name
			position := item.Position
			status = item.Status
			
			// 尝试获取其他字段，这些字段在SimpleGridInfoDTO中可能不存在
			// 需要通过类型断言或其他方式获取
			
			// 将SimpleGridInfoDTO转换为map以便获取更多字段
			if simpleMap, ok := item.Position.(map[string]interface{}); ok {
				if contentVal, exists := simpleMap["content"]; exists && contentVal != nil {
					if contentStr, ok := contentVal.(string); ok {
						content = contentStr
					}
				}
				
				if dtoVal, exists := simpleMap["dto"]; exists && dtoVal != nil {
					if dtoValue, ok := dtoVal.(string); ok {
						dtoStr = dtoValue
					}
				}
				
				if apiVal, exists := simpleMap["api"]; exists && apiVal != nil {
					if apiStr, ok := apiVal.(string); ok {
						api = apiStr
					}
				}
				
				if remarkVal, exists := simpleMap["remark"]; exists && remarkVal != nil {
					if remarkStr, ok := remarkVal.(string); ok {
						remark = remarkStr
					}
				}
				
				if uiConfigIDVal, exists := simpleMap["ui_config_id"]; exists && uiConfigIDVal != nil {
					if uiConfigIDInt, ok := uiConfigIDVal.(float64); ok {
						uiConfigID = int(uiConfigIDInt)
					}
				}
			}

			adminGridItem := &dto.AdminGridInfoDTO{
				ID:         id,
				UIConfigID: uiConfigID,
				Position:   position,
				Name:       name,
				Content:    content,
				API:        api,
				DTO:        dtoStr,
				Remark:     remark,
				Status:     status,
			}
			adminUIConfig.GridItems = append(adminUIConfig.GridItems, adminGridItem)
		}

		adminUIConfigs = append(adminUIConfigs, adminUIConfig)
	}

	return adminUIConfigs, total, nil
}

// GetUIConfig 根据ID获取UI配置详情
func (r *AdminUIConfigRepositoryImpl) GetUIConfig(ctx context.Context, id int) (*dto.AdminUIConfigDTO, error) {
	// 使用UI配置模块的仓库
	uiConfig, err := r.uiConfigRepo.GetUIConfig(ctx, id)
	if err != nil {
		return nil, err
	}

	if uiConfig == nil {
		return nil, nil
	}

	// 转换为管理员模块的DTO
	adminUIConfig := &dto.AdminUIConfigDTO{
		ID:            uiConfig.ID,
		FrontendPath:  uiConfig.FrontendPath,
		VersionHash:   uiConfig.VersionHash,
		ConfigType:    uiConfig.ConfigType,
		ConfigKey:     uiConfig.ConfigKey,
		ConfigContent: uiConfig.ConfigContent,
		Status:        uiConfig.Status,
		Remark:        uiConfig.Remark,
		CreatedAt:     uiConfig.CreatedAt,
		UpdatedAt:     uiConfig.UpdatedAt,
		Module:        uiConfig.Module,
		Title:         uiConfig.Title,
		Group:         uiConfig.Group,
		Version:       uiConfig.Version,
		Icon:          uiConfig.Icon,
		DTO:           uiConfig.DTO,
	}

	// 转换网格布局项目
	if uiConfig.GridItems != nil && len(uiConfig.GridItems) > 0 {
		adminGridItems := make([]*dto.AdminGridInfoDTO, 0, len(uiConfig.GridItems))
		for _, item := range uiConfig.GridItems {
			// 处理内容和DTO字段，可能需要特殊处理
			var content string
			var name string
			var api string
			var dtoStr string
			var remark string
			var status int
			var uiConfigID int
			
			// 从SimpleGridInfoDTO获取基本字段
			id := item.ID
			name = item.Name
			position := item.Position
			status = item.Status
			
			// 尝试获取其他字段，这些字段在SimpleGridInfoDTO中可能不存在
			// 需要通过类型断言或其他方式获取
			
			// 将SimpleGridInfoDTO转换为map以便获取更多字段
			if simpleMap, ok := item.Position.(map[string]interface{}); ok {
				if contentVal, exists := simpleMap["content"]; exists && contentVal != nil {
					if contentStr, ok := contentVal.(string); ok {
						content = contentStr
					}
				}
				
				if dtoVal, exists := simpleMap["dto"]; exists && dtoVal != nil {
					if dtoValue, ok := dtoVal.(string); ok {
						dtoStr = dtoValue
					}
				}
				
				if apiVal, exists := simpleMap["api"]; exists && apiVal != nil {
					if apiStr, ok := apiVal.(string); ok {
						api = apiStr
					}
				}
				
				if remarkVal, exists := simpleMap["remark"]; exists && remarkVal != nil {
					if remarkStr, ok := remarkVal.(string); ok {
						remark = remarkStr
					}
				}
				
				if uiConfigIDVal, exists := simpleMap["ui_config_id"]; exists && uiConfigIDVal != nil {
					if uiConfigIDInt, ok := uiConfigIDVal.(float64); ok {
						uiConfigID = int(uiConfigIDInt)
					}
				}
			}

			adminGridItem := &dto.AdminGridInfoDTO{
				ID:         id,
				UIConfigID: uiConfigID,
				Position:   position,
				Name:       name,
				Content:    content,
				API:        api,
				DTO:        dtoStr,
				Remark:     remark,
				Status:     status,
			}
			adminGridItems = append(adminGridItems, adminGridItem)
		}
		adminUIConfig.GridItems = adminGridItems
	} else {
		adminUIConfig.GridItems = []*dto.AdminGridInfoDTO{}
	}

	return adminUIConfig, nil
}

// CreateUIConfig 创建UI配置
func (r *AdminUIConfigRepositoryImpl) CreateUIConfig(ctx context.Context, config *dto.AdminUIConfigCreateDTO) (int, error) {
	// 转换为UI配置模块的DTO
	uiConfigCreate := &uiConfigDTO.UIConfigCreateParams{
		FrontendPath:  config.FrontendPath,
		ConfigType:    config.ConfigType,
		ConfigKey:     config.ConfigKey,
		ConfigContent: config.ConfigContent,
		Status:        config.Status,
		Remark:        config.Remark,
		Module:        config.Module,
		Title:         config.Title,
		Group:         config.Group,
		Version:       config.Version,
		Icon:          config.Icon,
		DTO:           config.DTO,
	}

	// 使用UI配置模块的仓库
	return r.uiConfigRepo.CreateUIConfig(ctx, uiConfigCreate)
}

// UpdateUIConfig 更新UI配置
func (r *AdminUIConfigRepositoryImpl) UpdateUIConfig(ctx context.Context, id int, config *dto.AdminUIConfigUpdateDTO) error {
	// 转换为UI配置模块的DTO
	uiConfigUpdate := &uiConfigDTO.UIConfigUpdateParams{
		FrontendPath:  config.FrontendPath,
		ConfigContent: config.ConfigContent,
		Status:        config.Status,
		Remark:        config.Remark,
		Title:         config.Title,
		Group:         config.Group,
		Version:       config.Version,
		Icon:          config.Icon,
		DTO:           config.DTO,
	}

	// 使用UI配置模块的仓库
	return r.uiConfigRepo.UpdateUIConfig(ctx, id, uiConfigUpdate)
}

// DeleteUIConfig 删除UI配置
func (r *AdminUIConfigRepositoryImpl) DeleteUIConfig(ctx context.Context, id int) error {
	// 使用UI配置模块的仓库
	return r.uiConfigRepo.DeleteUIConfig(ctx, id)
}

// UpdateUIConfigStatus 更新UI配置状态
func (r *AdminUIConfigRepositoryImpl) UpdateUIConfigStatus(ctx context.Context, id int, status int) error {
	// 获取现有配置
	uiConfig, err := r.uiConfigRepo.GetUIConfig(ctx, id)
	if err != nil {
		return err
	}
	
	if uiConfig == nil {
		return fmt.Errorf("UI配置不存在: ID=%d", id)
	}
	
	// 创建更新参数
	updateParams := &uiConfigDTO.UIConfigUpdateParams{
		Status: status,
	}
	
	// 调用更新方法
	return r.uiConfigRepo.UpdateUIConfig(ctx, id, updateParams)
}
