/**
 * 文件上传仓库实现
 *
 * 该文件提供对文件上传记录的数据库操作封装，实现数据的存储和检索功能。
 * 作为持久层组件，提供给上层服务调用。
 */

package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/admin/models"
)

// UploadFileRepository 文件上传仓库接口
type UploadFileRepository interface {
	// Create 创建文件上传记录
	Create(ctx context.Context, file *models.UploadFile) (int64, error)
	// GetByID 通过ID获取文件上传记录
	GetByID(ctx context.Context, id int64) (*models.UploadFile, error)
	// List 获取文件上传记录列表
	List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.UploadFile, int64, error)
	// Delete 删除文件上传记录(软删除)
	Delete(ctx context.Context, id int64) error
	// HardDelete 硬删除文件上传记录
	HardDelete(ctx context.Context, id int64) error
	// Update 更新文件上传记录
	Update(ctx context.Context, file *models.UploadFile) error
}

// uploadFileRepositoryImpl 文件上传仓库实现
type uploadFileRepositoryImpl struct{}

// NewUploadFileRepository 创建文件上传仓库实例
func NewUploadFileRepository() UploadFileRepository {
	return &uploadFileRepositoryImpl{}
}

// Create 创建文件上传记录
func (r *uploadFileRepositoryImpl) Create(ctx context.Context, file *models.UploadFile) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(file)
	if err != nil {
		logs.Error("创建文件上传记录失败: %v", err)
		return 0, fmt.Errorf("创建文件上传记录失败: %v", err)
	}
	return id, nil
}

// GetByID 通过ID获取文件上传记录
func (r *uploadFileRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.UploadFile, error) {
	o := orm.NewOrm()
	file := &models.UploadFile{ID: id}
	err := o.Read(file)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("获取文件上传记录失败: %v", err)
		return nil, fmt.Errorf("获取文件上传记录失败: %v", err)
	}
	return file, nil
}

// List 获取文件上传记录列表
func (r *uploadFileRepositoryImpl) List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.UploadFile, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.UploadFile))

	// 默认不查询软删除的记录
	qs = qs.Filter("deleted_at__isnull", true)

	// 应用查询条件
	if fileUsage, ok := query["file_usage"].(string); ok && fileUsage != "" {
		qs = qs.Filter("file_usage", fileUsage)
	}
	if userType, ok := query["user_type"].(string); ok && userType != "" {
		qs = qs.Filter("user_type", userType)
	}
	if userID, ok := query["user_id"].(int64); ok && userID > 0 {
		qs = qs.Filter("user_id", userID)
	}
	if username, ok := query["username"].(string); ok && username != "" {
		qs = qs.Filter("username__icontains", username)
	}
	if isAnonymous, ok := query["is_anonymous"].(*bool); ok && isAnonymous != nil {
		qs = qs.Filter("is_anonymous", *isAnonymous)
	}
	if startTime, ok := query["start_time"].(time.Time); ok && !startTime.IsZero() {
		qs = qs.Filter("created_at__gte", startTime)
	}
	if endTime, ok := query["end_time"].(time.Time); ok && !endTime.IsZero() {
		qs = qs.Filter("created_at__lte", endTime)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计文件上传记录总数失败: %v", err)
		return nil, 0, fmt.Errorf("统计文件上传记录总数失败: %v", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	files := make([]*models.UploadFile, 0)
	_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&files)
	if err != nil {
		logs.Error("查询文件上传记录列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询文件上传记录列表失败: %v", err)
	}

	return files, total, nil
}

// Delete 删除文件上传记录(软删除)
func (r *uploadFileRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	file := &models.UploadFile{ID: id}
	if err := o.Read(file); err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("文件上传记录不存在")
		}
		logs.Error("读取文件上传记录失败: %v", err)
		return fmt.Errorf("读取文件上传记录失败: %v", err)
	}

	// 软删除
	file.DeletedAt = time.Now()
	_, err := o.Update(file, "DeletedAt")
	if err != nil {
		logs.Error("软删除文件上传记录失败: %v", err)
		return fmt.Errorf("删除文件上传记录失败: %v", err)
	}
	return nil
}

// HardDelete 硬删除文件上传记录
func (r *uploadFileRepositoryImpl) HardDelete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	file := &models.UploadFile{ID: id}
	_, err := o.Delete(file)
	if err != nil {
		logs.Error("硬删除文件上传记录失败: %v", err)
		return fmt.Errorf("删除文件上传记录失败: %v", err)
	}
	return nil
}

// Update 更新文件上传记录
func (r *uploadFileRepositoryImpl) Update(ctx context.Context, file *models.UploadFile) error {
	o := orm.NewOrm()
	file.UpdatedAt = time.Now()
	_, err := o.Update(file)
	if err != nil {
		logs.Error("更新文件上传记录失败: %v", err)
		return fmt.Errorf("更新文件上传记录失败: %v", err)
	}
	return nil
}
