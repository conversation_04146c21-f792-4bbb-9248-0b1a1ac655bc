/**
 * 后台管理路由模块
 *
 * 该文件实现了后台管理系统的API路由配置，包括管理员、用户、商品、订单等模块的路由注册。
 * 不需要认证的接口直接放在/api/v1/admin下，需要认证的接口放在/api/v1/admin/secured子路径下。
 */

package routers

import (
	"fmt"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/admin/controllers"
	userControllers "o_mall_backend/modules/user/controllers"
	"o_mall_backend/modules/user/repositories"
	"o_mall_backend/modules/user/services"
)

// InitAdminRouter 初始化后台管理路由
func InitAdminRouter() {
	// 获取配置
	apiPrefix, _ := web.AppConfig.String("api_prefix")
	apiVersion, _ := web.AppConfig.String("api_version")

	// 设置默认值
	if apiPrefix == "" {
		apiPrefix = "api"
	}
	if apiVersion == "" {
		apiVersion = "v1"
	}

	// 初始化分销配置相关的仓库和服务
	referralConfigRepo := repositories.NewReferralConfigRepository()
	referralConfigService := services.NewReferralConfigService(referralConfigRepo)
	referralConfigController := userControllers.NewReferralConfigController(referralConfigService)
	
	// 分销关系管理相关的仓库和服务已迁移到user模块

	// 创建命名空间
	adminPath := fmt.Sprintf("/%s/%s/admin", apiPrefix, apiVersion)
	adminNS := web.NewNamespace(adminPath,
		// 不需要认证的接口
		web.NSRouter("/login", &controllers.AdminController{}, "post:Login"),
		web.NSRouter("/logout", &controllers.AdminController{}, "post:Logout"),
		web.NSRouter("/refresh-token", &controllers.AdminController{}, "post:RefreshToken"),
		web.NSRouter("/init-admin", &controllers.AdminController{}, "post:InitAdmin"),
		web.NSRouter("/merchants", &controllers.MerchantController{}, "get:ListMerchants"),

		// 文件上传接口（匿名上传不需要认证）
		web.NSRouter("/upload", &controllers.UploadFileController{}, "post:Upload"),
		web.NSRouter("/upload/config", &controllers.UploadFileController{}, "get:GetConfig"),

		// 需要认证的接口，放在secured子路径下
		web.NSNamespace("/secured",
			// 管理员相关路由
			web.NSRouter("/info", &controllers.AdminController{}, "get:GetAdminInfo"),
			web.NSRouter("/change-password", &controllers.AdminController{}, "post:ChangePassword"),

			// 管理员管理路由 (需要超级管理员权限)
			web.NSRouter("/admin", &controllers.AdminController{}, "post:CreateAdmin"),
			web.NSRouter("/admin", &controllers.AdminController{}, "get:ListAdmins"),
			web.NSRouter("/admin/:id", &controllers.AdminController{}, "get:GetAdminInfo"),
			web.NSRouter("/admin/:id", &controllers.AdminController{}, "put:UpdateAdmin"),
			web.NSRouter("/admin/:id/status", &controllers.AdminController{}, "put:UpdateAdmin"),
			web.NSRouter("/admin/:id/reset-password", &controllers.AdminController{}, "post:ResetPassword"),

			web.NSRouter("/profile", &controllers.AdminController{}, "get:GetCurrentAdmin"),
			web.NSRouter("/profile", &controllers.AdminController{}, "put:UpdateSelfInfo"),
			web.NSRouter("/current", &controllers.AdminController{}, "get:GetCurrentAdmin"),

			// 在管理员路由组中添加头像更新路由
			web.NSRouter("/avatar", &controllers.AdminController{}, "put:UpdateAvatar"),

			// AI配置管理接口
			web.NSRouter("/ai", &controllers.AIController{}, "get:GetConfig"),
			web.NSRouter("/ai", &controllers.AIController{}, "post:SaveConfig"),
			web.NSRouter("/ai/refresh", &controllers.AIController{}, "post:RefreshCache"),

			// 用户管理路由
			web.NSRouter("/users", &controllers.UserController{}, "get:ListUsers"),                   // 获取用户列表
			web.NSRouter("/users", &controllers.UserController{}, "post:CreateUser"),                 // 创建新用户
			web.NSRouter("/users/:id", &controllers.UserController{}, "get:GetUser"),                 // 获取用户信息
			web.NSRouter("/users/:id", &controllers.UserController{}, "put:UpdateUser"),              // 更新用户信息
			web.NSRouter("/users/:id/status", &controllers.UserController{}, "put:UpdateUserStatus"), // 更新用户状态
			web.NSRouter("/users/:id/password", &controllers.UserController{}, "put:ResetPassword"),  // 重置用户密码
			web.NSRouter("/users/:id/balance-points", &controllers.UserController{}, "put:UpdateUserBalanceAndPoints"),  // 更新用户余额和积分

			// 商户管理路由
			web.NSRouter("/merchants", &controllers.MerchantController{}, "get:ListMerchants"),
			web.NSRouter("/merchants", &controllers.MerchantController{}, "post:CreateMerchant"),
			web.NSRouter("/merchants/:id", &controllers.MerchantController{}, "get:GetMerchant"),
			web.NSRouter("/merchants/:id", &controllers.MerchantController{}, "put:UpdateMerchant"),
			web.NSRouter("/merchants/:id/status", &controllers.MerchantController{}, "put:UpdateMerchantStatus"),
			web.NSRouter("/merchants/:id/password", &controllers.MerchantController{}, "put:ResetPassword"),
			web.NSRouter("/merchants/:id/audit", &controllers.MerchantController{}, "put:UpdateMerchantAudit"),
			web.NSRouter("/merchants/:id/operation-status", &controllers.MerchantController{}, "put:UpdateMerchantOperationStatus"),
			web.NSRouter("/merchants/:id/coordinates", &controllers.MerchantController{}, "put:UpdateMerchantCoordinates"),
			web.NSRouter("/merchants/:id/recommend", &controllers.MerchantController{}, "put:UpdateMerchantRecommendStatus"),
			

			// 商家分类管理路由
			web.NSRouter("/merchant-categories", &controllers.MerchantCategoryController{}, "get:ListCategories;post:CreateCategory"),
			web.NSRouter("/merchant-categories/all", &controllers.MerchantCategoryController{}, "get:GetAllCategories"),
			web.NSRouter("/merchant-categories/:id", &controllers.MerchantCategoryController{}, "get:GetCategoryByID;put:UpdateCategory;delete:DeleteCategory"),

			// 角色和权限管理路由
			web.NSRouter("/roles", &controllers.PermissionController{}, "get:ListRoles"),
			web.NSRouter("/roles", &controllers.PermissionController{}, "post:CreateRole"),
			web.NSRouter("/roles/:id", &controllers.PermissionController{}, "get:GetRole"),
			web.NSRouter("/roles/:id", &controllers.PermissionController{}, "put:UpdateRole"),
			web.NSRouter("/roles/:id", &controllers.PermissionController{}, "delete:DeleteRole"),
			web.NSRouter("/permissions", &controllers.PermissionController{}, "get:ListPermissions"),
			web.NSRouter("/permissions", &controllers.PermissionController{}, "post:CreatePermission"),
			web.NSRouter("/permissions/:id", &controllers.PermissionController{}, "get:GetPermission"),
			web.NSRouter("/permissions/:id", &controllers.PermissionController{}, "put:UpdatePermission"),
			web.NSRouter("/permissions/:id", &controllers.PermissionController{}, "delete:DeletePermission"),

			// 跑腿员管理路由 (待集成 - 需要先实现AdminRunnerController的依赖注入)
			// web.NSRouter("/runners", &controllers.AdminRunnerController{}, "get:ListRunners"),
			// web.NSRouter("/runners/:id", &controllers.AdminRunnerController{}, "get:GetRunnerDetail"),
			// web.NSRouter("/runners/:id/audit", &controllers.AdminRunnerController{}, "put:AuditRunner"),
			// web.NSRouter("/runners/:id/status", &controllers.AdminRunnerController{}, "put:UpdateRunnerStatus"),
			// web.NSRouter("/runners/:id", &controllers.AdminRunnerController{}, "delete:DeleteRunner"),
			// web.NSRouter("/runners/statistics", &controllers.AdminRunnerController{}, "get:GetRunnerStatistics"),
			// web.NSRouter("/runners/:id/remark", &controllers.AdminRunnerController{}, "put:UpdateRunnerRemark"),
			// web.NSRouter("/runners/:id/orders", &controllers.AdminRunnerController{}, "get:GetRunnerOrders"),

			web.NSRouter("/roles/:id/permissions", &controllers.PermissionController{}, "post:AssignPermissions"),

			web.NSRouter("/current/permissions", &controllers.PermissionController{}, "get:GetCurrentUserPermissions"),
			web.NSRouter("/current/roles", &controllers.PermissionController{}, "get:GetCurrentUserRoles"),

			// 管理员操作日志路由
			web.NSRouter("/logs", &controllers.AdminLogController{}, "get:List"),
			web.NSRouter("/logs/:id", &controllers.AdminLogController{}, "get:Detail"),

			// 文件管理路由
			web.NSRouter("/files", &controllers.UploadFileController{}, "get:List"),
			web.NSRouter("/files/:id", &controllers.UploadFileController{}, "get:GetFile"),
			web.NSRouter("/files/:id", &controllers.UploadFileController{}, "delete:Delete"),

			// UI配置管理路由
			web.NSRouter("/ui-configs", &controllers.UIConfigController{}, "get:List"),
			web.NSRouter("/ui-configs", &controllers.UIConfigController{}, "post:Create"),
			web.NSRouter("/ui-configs/:id", &controllers.UIConfigController{}, "get:Get"),
			web.NSRouter("/ui-configs/:id", &controllers.UIConfigController{}, "put:Update"),
			web.NSRouter("/ui-configs/:id", &controllers.UIConfigController{}, "delete:Delete"),
			web.NSRouter("/ui-configs/:id/status", &controllers.UIConfigController{}, "put:UpdateStatus"),

			// 网格布局管理路由
			web.NSRouter("/grid-infos", &controllers.GridInfoController{}, "get:List"),
			web.NSRouter("/grid-infos", &controllers.GridInfoController{}, "post:Create"),
			web.NSRouter("/grid-infos/:id", &controllers.GridInfoController{}, "get:Get"),
			web.NSRouter("/grid-infos/:id", &controllers.GridInfoController{}, "put:Update"),
			web.NSRouter("/grid-infos/:id", &controllers.GridInfoController{}, "delete:Delete"),
			web.NSRouter("/grid-infos/:id/status", &controllers.GridInfoController{}, "put:UpdateStatus"),
			web.NSRouter("/grid-infos/batch/position", &controllers.GridInfoController{}, "put:BatchUpdatePosition"),
			web.NSRouter("/grid-infos/set-ui-configs", &controllers.GridInfoController{}, "post:SetUIConfigRelation"),
			// 网格布局关联UI配置管理路由
			web.NSRouter("/grid-infos/:id/ui-configs", &controllers.GridInfoController{}, "get:ListUIConfigs"),
			web.NSRouter("/grid-infos/:id/ui-configs", &controllers.GridInfoController{}, "post:AddUIConfigs"),
			web.NSRouter("/grid-infos/:id/ui-configs", &controllers.GridInfoController{}, "put:UpdateUIConfigs"),
			web.NSRouter("/grid-infos/:id/ui-configs/remove", &controllers.GridInfoController{}, "post:RemoveUIConfigs"),
			web.NSRouter("/grid-infos/delete-ui-configs", &controllers.GridInfoController{}, "put:DeleteUIConfigRelation"),
			// 添加UIConfig查询GridInfo的API
			web.NSRouter("/ui-configs/:id/grid-infos", &controllers.UIConfigController{}, "get:ListGridInfos"),
			// 新增：批量设置UI配置与网格布局关联关系
			web.NSRouter("/grid-infos/set-batch-ui", &controllers.GridInfoController{}, "put:SetBatchUIConfigGridRelation"),

			// 系统统计数据接口
			web.NSRouter("/statistics", &controllers.StatisticsController{}, "get:GetStatistics"),

			// 分销配置管理路由 (管理员权限)
			
			web.NSRouter("/referral/config/levels", referralConfigController, "get:GetLevelConfig"),
			web.NSRouter("/referral/config/levels", referralConfigController, "put:UpdateLevelConfig"),
			web.NSRouter("/referral/config/commission-rates", referralConfigController, "get:GetCommissionRates"),
			web.NSRouter("/referral/config/commission-rates", referralConfigController, "put:UpdateCommissionRates"),
			web.NSRouter("/referral/configs", referralConfigController, "post:CreateConfig"),
			web.NSRouter("/referral/configs/initialize", referralConfigController, "post:InitializeConfigs"),
			
			web.NSRouter("/referral/configs", referralConfigController, "get:GetConfigs"),
			web.NSRouter("/referral/configs/:id", referralConfigController, "get:GetConfigByID"),
			web.NSRouter("/referral/configs/:id", referralConfigController, "put:UpdateConfig"),
			web.NSRouter("/referral/configs/:id", referralConfigController, "delete:DeleteConfig"),
			
			// 分销关系管理路由已迁移到user模块
		),
	)

	// 注册命名空间
	web.AddNamespace(adminNS)

	logs.Info("注册管理员系统中间件...")

	// 中间件注册顺序非常重要
	// 1. 先注册JWT验证过滤器（需要先验证身份）
	web.InsertFilter("/api/v1/admin/secured/*", web.BeforeRouter, middlewares.JWTFilter)

	// 2. 再注册不同路径下的管理员日志中间件
	// 注册secured路径的日志中间件（这些请求需要先经过JWT中间件）
	web.InsertFilter("/api/v1/admin/secured/merchants/*", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/secured/admin/*", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/secured/users/*", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/secured/roles/*", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/secured/permissions/*", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/secured/ui-configs/*", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/secured/grid-infos/*", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/secured/referral/*", web.BeforeRouter, middlewares.AdminLogMiddleware())

	// 非secured路径的日志中间件（登录等操作）
	web.InsertFilter("/api/v1/admin/login", web.BeforeRouter, middlewares.AdminLogMiddleware())
	web.InsertFilter("/api/v1/admin/logout", web.BeforeRouter, middlewares.AdminLogMiddleware())

	// 3. 最后注册权限验证过滤器
	web.InsertFilter("/api/v1/admin/secured/admins*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/admin/secured/roles*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/admin/secured/permissions*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/admin/secured/ai*", web.BeforeRouter, middlewares.RoleFilter("super"))
}
