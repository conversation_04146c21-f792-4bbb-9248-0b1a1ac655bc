/**
 * 管理员日志服务实现
 *
 * 该文件实现了管理员日志服务接口，提供日志记录和查询功能。
 * 管理员日志是系统安全审计的重要组成部分，记录管理员的各项操作。
 */

package services

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/models"
	"o_mall_backend/modules/admin/repositories"
)

// AdminLogService 管理员日志服务接口
type AdminLogService interface {
	// CreateLog 创建管理员日志
	CreateLog(ctx context.Context, req *dto.AdminLogCreateRequest) (int64, error)
	// GetLogByID 获取日志详情
	GetLogByID(ctx context.Context, id int64) (*dto.AdminLogResponse, error)
	// ListLogs 获取日志列表
	ListLogs(ctx context.Context, req *dto.AdminLogQueryRequest) ([]*dto.AdminLogResponse, int64, error)
	// GetLatestLogsByAdminID 获取管理员最近的日志
	GetLatestLogsByAdminID(ctx context.Context, adminID int64, limit int) ([]*dto.AdminLogResponse, error)
}

// AdminLogServiceImpl 管理员日志服务实现
type AdminLogServiceImpl struct {
	adminLogRepo repositories.AdminLogRepository
}

// NewAdminLogService 创建管理员日志服务实例
func NewAdminLogService() AdminLogService {
	return &AdminLogServiceImpl{
		adminLogRepo: repositories.NewAdminLogRepository(),
	}
}

// CreateLog 创建管理员日志
func (s *AdminLogServiceImpl) CreateLog(ctx context.Context, req *dto.AdminLogCreateRequest) (int64, error) {
	// 创建日志对象
	adminLog := &models.AdminLog{
		AdminID:     req.AdminID,
		Username:    req.Username,
		Module:      req.Module,
		Type:        req.Type,
		Content:     req.Content,
		RequestURL:  req.RequestURL,
		RequestData: req.RequestData,
		IP:          req.IP,
		UserAgent:   req.UserAgent,
		TargetID:    req.TargetID,
		TargetType:  req.TargetType,
		Status:      req.Status,
		Remark:      req.Remark,
		CreatedAt:   time.Now(),
	}

	// 调用仓库创建日志
	id, err := s.adminLogRepo.Create(ctx, adminLog)
	if err != nil {
		logs.Error("创建管理员日志失败: %v", err)
		return 0, fmt.Errorf("创建日志失败: %v", err)
	}

	return id, nil
}

// GetLogByID 获取日志详情
func (s *AdminLogServiceImpl) GetLogByID(ctx context.Context, id int64) (*dto.AdminLogResponse, error) {
	// 调用仓库获取日志
	adminLog, err := s.adminLogRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取管理员日志失败: %v", err)
		return nil, fmt.Errorf("获取日志失败: %v", err)
	}

	if adminLog == nil {
		return nil, fmt.Errorf("日志不存在")
	}

	// 转换为响应对象
	return convertToAdminLogResponse(adminLog), nil
}

// ListLogs 获取日志列表
func (s *AdminLogServiceImpl) ListLogs(ctx context.Context, req *dto.AdminLogQueryRequest) ([]*dto.AdminLogResponse, int64, error) {
	// 构建查询条件
	query := make(map[string]interface{})
	if req.AdminID > 0 {
		query["admin_id"] = req.AdminID
	}
	if req.Username != "" {
		query["username"] = req.Username
	}
	if req.Module != "" {
		query["module"] = req.Module
	}
	if req.Type != "" {
		query["type"] = req.Type
	}
	if req.IP != "" {
		query["ip"] = req.IP
	}
	if req.Status != -1 {
		query["status"] = req.Status
	}
	if !req.StartTime.IsZero() {
		query["start_time"] = req.StartTime
	}
	if !req.EndTime.IsZero() {
		query["end_time"] = req.EndTime
	}

	// 设置默认分页参数
	page := req.Page
	if page < 1 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize < 1 {
		pageSize = 10
	}

	// 调用仓库获取日志列表
	adminLogs, total, err := s.adminLogRepo.List(ctx, query, page, pageSize)
	if err != nil {
		logs.Error("查询管理员日志列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询日志列表失败: %v", err)
	}

	// 转换为响应对象
	responses := make([]*dto.AdminLogResponse, 0, len(adminLogs))
	for _, adminLog := range adminLogs {
		responses = append(responses, convertToAdminLogResponse(adminLog))
	}

	return responses, total, nil
}

// GetLatestLogsByAdminID 获取管理员最近的日志
func (s *AdminLogServiceImpl) GetLatestLogsByAdminID(ctx context.Context, adminID int64, limit int) ([]*dto.AdminLogResponse, error) {
	// 调用仓库获取最近日志
	adminLogs, err := s.adminLogRepo.GetLatestByAdminID(ctx, adminID, limit)
	if err != nil {
		logs.Error("获取管理员最近日志失败: %v", err)
		return nil, fmt.Errorf("获取最近日志失败: %v", err)
	}

	// 转换为响应对象
	responses := make([]*dto.AdminLogResponse, 0, len(adminLogs))
	for _, adminLog := range adminLogs {
		responses = append(responses, convertToAdminLogResponse(adminLog))
	}

	return responses, nil
}

// convertToAdminLogResponse 将日志模型转换为响应对象
func convertToAdminLogResponse(adminLog *models.AdminLog) *dto.AdminLogResponse {
	if adminLog == nil {
		return nil
	}

	// 状态描述
	statusText := "成功"
	if adminLog.Status == 0 {
		statusText = "失败"
	}

	// 返回响应对象
	return &dto.AdminLogResponse{
		ID:          adminLog.ID,
		AdminID:     adminLog.AdminID,
		Username:    adminLog.Username,
		Module:      adminLog.Module,
		Type:        adminLog.Type,
		Content:     adminLog.Content,
		RequestURL:  adminLog.RequestURL,
		RequestData: adminLog.RequestData,
		IP:          adminLog.IP,
		UserAgent:   adminLog.UserAgent,
		TargetID:    adminLog.TargetID,
		TargetType:  adminLog.TargetType,
		Status:      adminLog.Status,
		StatusText:  statusText,
		Remark:      adminLog.Remark,
		CreatedAt:   adminLog.CreatedAt,
	}
}
