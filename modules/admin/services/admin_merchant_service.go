/**
 * 管理员商户管理服务实现
 *
 * 该文件实现了管理员模块中商户管理相关的服务接口。
 * 包括商户查询、审核、状态管理等功能的具体实现。
 */

package services

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/admin/dto"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/utils"
)

// AdminMerchantServiceImpl 管理员商户管理服务实现
type AdminMerchantServiceImpl struct {
	o orm.Ormer
}

// NewAdminMerchantService 创建管理员商户管理服务实例
func NewAdminMerchantService() AdminMerchantService {
	return &AdminMerchantServiceImpl{
		o: orm.NewOrm(),
	}
}

// ListMerchants 获取商户列表
func (s *AdminMerchantServiceImpl) ListMerchants(ctx context.Context, req *dto.MerchantQueryRequest) ([]*dto.MerchantResponse, int64, error) {
	// 构建查询条件
	cond := orm.NewCondition()
	if req.Name != "" {
		cond = cond.And("name__icontains", req.Name)
	}
	if req.Mobile != "" {
		cond = cond.And("mobile__icontains", req.Mobile)
	}
	if req.Email != "" {
		cond = cond.And("email__icontains", req.Email)
	}
	if req.Status != 0 {
		cond = cond.And("status", req.Status)
	}
	// 打印审核状态值，用于调试
	logs.Info("服务层收到的审核状态值 [AuditStatus]: %d", req.AuditStatus)
	
	// 如果审核状态是0或其他运行时指定的值，则添加审核状态的过滤条件
	// 控制器会将未指定审核状态的请求的AuditStatus设置为-1
	if req.AuditStatus != -1 {
		logs.Info("添加审核状态过滤条件: audit_status = %d", req.AuditStatus)
		
		// 使用更直接的SQL条件指定，确保数据库层面正确应用过滤条件
		cond = cond.And("audit_status", req.AuditStatus)
		
		// 打印最终生成的查询条件
		logs.Info("最终构造的查询条件: %v", cond)
	}
	
	// 如果指定了商家等级，则添加等级过滤条件
	if req.Level != 0 {
		cond = cond.And("level", req.Level)
	}
	
	// 如果指定了是否推荐，则添加推荐状态过滤条件
	// 使用-1表示不过滤推荐状态，0表示非推荐，1表示推荐
	if req.IsRecommended != -1 {
		cond = cond.And("is_recommended", req.IsRecommended)
		logs.Info("添加推荐状态过滤条件: is_recommended = %d", req.IsRecommended)
	}

	// 查询总数
	var merchants []*models.Merchant
	total, err := s.o.QueryTable(new(models.Merchant)).SetCond(cond).Count()
	if err != nil {
		logs.Error("[ListMerchants] 查询商户总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	_, err = s.o.QueryTable(new(models.Merchant)).SetCond(cond).
		Offset((req.Page - 1) * req.PageSize).
		Limit(req.PageSize).
		OrderBy("-created_at").
		All(&merchants)
	if err != nil {
		logs.Error("[ListMerchants] 查询商户列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	var result []*dto.MerchantResponse
	for _, merchant := range merchants {
		result = append(result, s.convertToMerchantResponse(merchant))
	}

	return result, total, nil
}

// GetMerchantByID 获取商户详情
func (s *AdminMerchantServiceImpl) GetMerchantByID(ctx context.Context, id int64) (*dto.MerchantResponse, error) {
	merchant := &models.Merchant{ID: id}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return nil, fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[GetMerchantByID] 查询商户失败: %v", err)
		return nil, err
	}

	return s.convertToMerchantResponse(merchant), nil
}

// UpdateMerchant 更新商户信息
func (s *AdminMerchantServiceImpl) UpdateMerchant(ctx context.Context, req *dto.UpdateMerchantRequest) error {
	merchant := &models.Merchant{ID: req.ID}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[UpdateMerchant] 查询商户失败: %v", err)
		return err
	}

	// 更新商户信息
	// 创建一个需要更新的字段列表
	fieldsToUpdate := []string{"UpdatedAt"} // 时间戳总是需要更新的

	// 如果用户名发生变化，检查新用户名是否已被使用
	if req.Username != "" && merchant.Username != req.Username {
		// 检查用户名是否已被其他商户使用
		var existingMerchant models.Merchant
		err = s.o.QueryTable("merchant").Filter("username", req.Username).One(&existingMerchant)
		if err == nil && existingMerchant.ID != req.ID {
			// 找到了其他商户使用相同用户名
			return fmt.Errorf("用户名 %s 已被其他商户使用", req.Username)
		} else if err != nil && err != orm.ErrNoRows {
			// 查询出错了，但不是因为没找到记录
			logs.Error("[UpdateMerchant] 检查用户名唯一性失败: %v", err)
			return err
		}
		// 如果是ErrNoRows则表示用户名可用
		merchant.Username = req.Username
		fieldsToUpdate = append(fieldsToUpdate, "Username")
	}

	// 处理密码更新
	if req.Password != "" {
		// 对密码进行加密处理
		hashedPassword, err := utils.EncryptPassword(req.Password)
		if err != nil {
			logs.Error("[UpdateMerchant] 密码加密失败: %v", err)
			return err
		}
		merchant.Password = hashedPassword
		fieldsToUpdate = append(fieldsToUpdate, "Password")
	}

	// 处理每个字段，如果值为-1则表示不更新该字段（保持原值）
	if req.Name != "" {
		merchant.Name = req.Name
		fieldsToUpdate = append(fieldsToUpdate, "Name")
	}

	// Status字段特殊处理：如果为-1则不更新，否则更新
	if req.Status != -1 {
		merchant.Status = req.Status
		fieldsToUpdate = append(fieldsToUpdate, "Status")
	}

	// AuditStatus字段特殊处理：如果为-1则不更新，否则更新
	if req.AuditStatus != -1 {
		merchant.AuditStatus = req.AuditStatus
		fieldsToUpdate = append(fieldsToUpdate, "AuditStatus")
	}

	// Level字段特殊处理：如果为-1则不更新，否则更新
	if req.Level != -1 {
		merchant.Level = req.Level
		fieldsToUpdate = append(fieldsToUpdate, "Level")
	}

	// IsRecommended字段特殊处理：如果为-1则不更新，否则更新
	if req.IsRecommended != -1 {
		merchant.IsRecommended = req.IsRecommended
		fieldsToUpdate = append(fieldsToUpdate, "IsRecommended")
	}

	// 处理其他可能为空的字段
	if req.Logo != "" {
		merchant.Logo = req.Logo
		fieldsToUpdate = append(fieldsToUpdate, "Logo")
	}

	if req.BusinessLicense != "" {
		merchant.BusinessLicense = req.BusinessLicense
		fieldsToUpdate = append(fieldsToUpdate, "BusinessLicense")
	}

	if req.ContactEmail != "" {
		merchant.ContactEmail = req.ContactEmail
		fieldsToUpdate = append(fieldsToUpdate, "ContactEmail")
	}

	if req.ContactMobile != "" {
		merchant.ContactMobile = req.ContactMobile
		fieldsToUpdate = append(fieldsToUpdate, "ContactMobile")
	}

	if req.Description != "" {
		merchant.Description = req.Description
		fieldsToUpdate = append(fieldsToUpdate, "Description")
	}

	if req.ContactName != "" {
		merchant.ContactName = req.ContactName
		fieldsToUpdate = append(fieldsToUpdate, "ContactName")
	}

	if req.Address != "" {
		merchant.Address = req.Address
		fieldsToUpdate = append(fieldsToUpdate, "Address")
	}

	// 经纬度可能为0（如果商户不提供位置信息）
	// 因此我们不能简单地根据是否为0来判断是否更新
	// 这里我们依赖控制器传入的特殊值，如果经纬度未传递，控制器会将其设为0
	// 所以这里我们默认总是更新这两个字段
	merchant.Longitude = req.Longitude
	merchant.Latitude = req.Latitude
	fieldsToUpdate = append(fieldsToUpdate, "Longitude", "Latitude")

	merchant.UpdatedAt = time.Now()

	// 仅更新指定的字段
	_, err = s.o.Update(merchant, fieldsToUpdate...)
	if err != nil {
		logs.Error("[UpdateMerchant] 更新商户失败: %v", err)
		return err
	}

	return nil
}

// DisableMerchant 禁用商户
func (s *AdminMerchantServiceImpl) DisableMerchant(ctx context.Context, id int64) error {
	merchant := &models.Merchant{ID: id}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[DisableMerchant] 查询商户失败: %v", err)
		return err
	}

	merchant.Status = models.MerchantStatusDisabled
	merchant.UpdatedAt = time.Now()

	_, err = s.o.Update(merchant, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("[DisableMerchant] 禁用商户失败: %v", err)
		return err
	}

	return nil
}

// UpdateMerchantStatus 更新商户状态
func (s *AdminMerchantServiceImpl) UpdateMerchantStatus(ctx context.Context, id int64, status int) error {
	merchant := &models.Merchant{ID: id}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[UpdateMerchantStatus] 查询商户失败: %v", err)
		return err
	}

	merchant.Status = status
	merchant.UpdatedAt = time.Now()

	_, err = s.o.Update(merchant, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("[UpdateMerchantStatus] 更新商户状态失败: %v", err)
		return err
	}

	return nil
}

// EnableMerchant 启用商户
func (s *AdminMerchantServiceImpl) EnableMerchant(ctx context.Context, id int64) error {
	merchant := &models.Merchant{ID: id}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[EnableMerchant] 查询商户失败: %v", err)
		return err
	}

	merchant.Status = models.MerchantStatusApproved
	merchant.UpdatedAt = time.Now()

	_, err = s.o.Update(merchant, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("[EnableMerchant] 启用商户失败: %v", err)
		return err
	}

	return nil
}

// DeleteMerchant 删除商户
func (s *AdminMerchantServiceImpl) DeleteMerchant(ctx context.Context, id int64) error {
	merchant := &models.Merchant{ID: id}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[DeleteMerchant] 查询商户失败: %v", err)
		return err
	}

	_, err = s.o.Delete(merchant)
	if err != nil {
		logs.Error("[DeleteMerchant] 删除商户失败: %v", err)
		return err
	}

	return nil
}

// ResetMerchantPassword 重置商户密码
func (s *AdminMerchantServiceImpl) ResetMerchantPassword(ctx context.Context, req *dto.ResetMerchantPasswordRequest) error {
	merchant := &models.Merchant{ID: req.ID}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[ResetMerchantPassword] 查询商户失败: %v", err)
		return err
	}

	// 生成密码哈希
	hashedPassword, err := utils.EncryptPassword(req.NewPassword)
	if err != nil {
		logs.Error("[ResetMerchantPassword] 生成密码哈希失败: %v", err)
		return err
	}

	merchant.Password = hashedPassword
	merchant.UpdatedAt = time.Now()

	_, err = s.o.Update(merchant, "Password", "UpdatedAt")
	if err != nil {
		logs.Error("[ResetMerchantPassword] 重置密码失败: %v", err)
		return err
	}

	return nil
}

// AuditMerchant 审核商户
func (s *AdminMerchantServiceImpl) AuditMerchant(ctx context.Context, req *dto.AuditMerchantRequest) error {
	// 添加调试日志
	logs.Info("[AuditMerchant] 开始审核商户，参数: %+v", req)

	// 将字符串类型的审核状态转换为整数
	auditStatus, err := strconv.Atoi(req.AuditStatus)
	if err != nil {
		logs.Error("[AuditMerchant] 转换审核状态失败: %v", err)
		return fmt.Errorf("无效的审核状态")
	}

	merchant := &models.Merchant{ID: req.ID}
	err = s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[AuditMerchant] 查询商户失败: %v", err)
		return err
	}

	// 在更新前记录原始状态
	logs.Info("[AuditMerchant] 商户原始状态 - AuditStatus: %d, Status: %d, 商户ID: %d",
		merchant.AuditStatus, merchant.Status, merchant.ID)

	// 重要修改：将审核状态设置到AuditStatus字段，而不是Status字段
	merchant.AuditStatus = auditStatus
	merchant.RejectReason = req.Remark
	merchant.UpdatedAt = time.Now()

	// 如果审核通过，自动启用商户
	if auditStatus == models.MerchantStatusApproved {
		// 注意：如果审核通过，则将Status设置为1（正常），而不是用审核状态的值
		merchant.Status = 1 // 1表示商户状态正常
	}

	// 更新指定字段
	_, err = s.o.Update(merchant, "AuditStatus", "RejectReason", "Status", "UpdatedAt")
	if err != nil {
		logs.Error("[AuditMerchant] 审核商户失败: %v", err)
		return err
	}

	// 更新后再次查询确认
	err = s.o.Read(merchant)
	if err != nil {
		logs.Error("[AuditMerchant] 确认更新后查询失败: %v", err)
	} else {
		logs.Info("[AuditMerchant] 商户更新后状态 - AuditStatus: %d, Status: %d, 商户ID: %d",
			merchant.AuditStatus, merchant.Status, merchant.ID)
	}

	return nil
}

// 商家经营状态常量
const (
	MerchantOperationStatusClosed = 0 // 休息中
	MerchantOperationStatusOpen   = 1 // 营业中
)

// UpdateMerchantOperationStatus 更新商户营业状态
func (s *AdminMerchantServiceImpl) UpdateMerchantOperationStatus(ctx context.Context, id int64, req *merchantDto.UpdateOperationStatusRequest) error {
	logs.Info("[UpdateMerchantOperationStatus] 更新商户营业状态: ID=%d, 状态=%d", id, req.OperationStatus)
	
	// 验证状态值是否有效
	if req.OperationStatus != MerchantOperationStatusOpen && req.OperationStatus != MerchantOperationStatusClosed {
		return fmt.Errorf("无效的商户营业状态: %d", req.OperationStatus)
	}
	
	// 查询商户是否存在
	merchant := &models.Merchant{ID: id}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[UpdateMerchantOperationStatus] 查询商户失败: %v", err)
		return err
	}
	
	// 如果状态没有变化，则不进行更新
	if merchant.OperationStatus == req.OperationStatus {
		logs.Info("[UpdateMerchantOperationStatus] 商户营业状态未变化，无需更新: ID=%d, 状态=%d", id, req.OperationStatus)
		return nil
	}

	// 更新商户营业状态
	merchant.OperationStatus = req.OperationStatus
	merchant.UpdatedAt = time.Now()

	// 只更新营业状态和更新时间字段
	_, err = s.o.Update(merchant, "OperationStatus", "UpdatedAt")
	if err != nil {
		logs.Error("[UpdateMerchantOperationStatus] 更新商户营业状态失败: %v", err)
		return err
	}
	
	// 获取状态文本描述
	statusText := "休息中"
	if req.OperationStatus == MerchantOperationStatusOpen {
		statusText = "营业中"
	}
	
	logs.Info("[UpdateMerchantOperationStatus] 商户 %d 状态已更新为: %s", id, statusText)
	
	// 记录商户状态变更日志
	var operationType int
	if req.OperationStatus == MerchantOperationStatusOpen {
		operationType = models.MerchantLogTypeOpenBusiness
	} else {
		operationType = models.MerchantLogTypeCloseBusiness
	}
	
	// 设置日志记录的基本信息
	requestURL := "admin/api/merchants/operation-status"
	ip := "127.0.0.1"
	userAgent := "admin-service"
	var operatorID int64 = 0  // 系统操作或未知管理员
	operatorType := 2         // 管理员操作
	
	// 创建日志记录
	logEntry := &models.MerchantLog{
		MerchantID:    id,
		OperatorID:    operatorID,
		OperatorType:  operatorType,
		OperationType: operationType,
		Content:       fmt.Sprintf("管理员将商户营业状态更新为: %s", statusText),
		RequestURL:    requestURL,
		RequestData:   fmt.Sprintf("{\"operation_status\": %d}", req.OperationStatus),
		IP:            ip,
		UserAgent:     userAgent,
		CreatedAt:     time.Now(),
	}
	
	// 插入日志记录
	_, err = s.o.Insert(logEntry)
	if err != nil {
		logs.Error("[UpdateMerchantOperationStatus] 记录商户状态变更日志失败: %v", err)
		// 不返回错误，因为这只是辅助功能
	}

	return nil
}

// CreateMerchant 管理员创建商户
// UpdateMerchantCoordinates 更新商户坐标（经纬度）
func (s *AdminMerchantServiceImpl) UpdateMerchantCoordinates(ctx context.Context, req *dto.UpdateMerchantRequest) error {
	// 查询商户是否存在
	merchant := &models.Merchant{ID: req.ID}
	err := s.o.Read(merchant)
	if err == orm.ErrNoRows {
		return fmt.Errorf("商户不存在")
	}
	if err != nil {
		logs.Error("[UpdateMerchantCoordinates] 查询商户失败: %v", err)
		return err
	}

	// 只更新经纬度字段
	merchant.Longitude = req.Longitude
	merchant.Latitude = req.Latitude
	merchant.UpdatedAt = time.Now()

	// 更新商户信息
	_, err = s.o.Update(merchant, "Longitude", "Latitude", "UpdatedAt")
	if err != nil {
		logs.Error("[UpdateMerchantCoordinates] 更新商户坐标失败: %v", err)
		return err
	}

	return nil
}

func (s *AdminMerchantServiceImpl) CreateMerchant(ctx context.Context, req *dto.CreateMerchantRequest) (int64, error) {
	// 检查商户名是否已存在
	//var existMerchant models.Merchant
	exist := s.o.QueryTable(new(models.Merchant)).Filter("username", req.Username).Exist()
	if exist {
		return 0, fmt.Errorf("商户名已存在")
	}

	// 加密密码
	hashedPassword, err := utils.EncryptPassword(req.Password)
	if err != nil {
		logs.Error("[CreateMerchant] 密码加密失败: %v", err)
		return 0, err
	}

	// 创建商家对象
	merchant := &models.Merchant{
		Name:            req.Name,
		Logo:            req.Logo,
		Description:     req.Description,
		Username:        req.Username,
		Password:        hashedPassword,
		ContactName:     req.ContactName,
		ContactMobile:   req.ContactMobile,
		ContactEmail:    req.ContactEmail,
		BusinessLicense: req.BusinessLicense,
		Address:         req.Address,
		Longitude:       req.Longitude,
		Latitude:        req.Latitude,
		Level:           req.Level,
		Balance:         0.00,
		AuditStatus:     req.AuditStatus,
		Status:          req.Status,
		IsRecommended:   req.IsRecommended,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 保存商家信息
	id, err := s.o.Insert(merchant)
	if err != nil {
		logs.Error("[CreateMerchant] 创建商户失败: %v", err)
		return 0, err
	}

	return id, nil
}

// convertToMerchantResponse 将商户模型转换为响应对象
func (s *AdminMerchantServiceImpl) convertToMerchantResponse(merchant *models.Merchant) *dto.MerchantResponse {
	// 解析营业时间数据
	var businessHoursResp []dto.BusinessHourResponse
	businessHours, err := merchant.GetBusinessHours()
	if err == nil && len(businessHours) > 0 {
		// 将模型中的BusinessHour转为DTO中的BusinessHourResponse
		for _, hour := range businessHours {
			businessHoursResp = append(businessHoursResp, dto.BusinessHourResponse{
				Weekday:   hour.Weekday,
				StartTime: hour.StartTime,
				EndTime:   hour.EndTime,
			})
		}
	}

	return &dto.MerchantResponse{
		ID:              merchant.ID,
		Name:            merchant.Name,
		Logo:            merchant.Logo,
		Balance:         merchant.Balance,
		Description:     merchant.Description,
		ContactName:     merchant.ContactName,
		ContactMobile:   merchant.ContactMobile,
		ContactEmail:    merchant.ContactEmail,
		Address:         merchant.Address,
		Longitude:       merchant.Longitude,          // 添加经度
		Latitude:        merchant.Latitude,           // 添加纬度
		Level:           merchant.Level,              // 添加商家等级
		Status:          merchant.Status,
		AuditStatus:     merchant.AuditStatus,
		OperationStatus: merchant.OperationStatus,     // 增加经营状态
		IsRecommended:   merchant.IsRecommended,       // 添加是否推荐
		BusinessHours:   businessHoursResp,           // 增加营业时间
		Username:        merchant.Username,
		BusinessLicense: merchant.BusinessLicense,
		RejectReason:    merchant.RejectReason,
		CreatedAt:       merchant.CreatedAt,
		UpdatedAt:       merchant.UpdatedAt,
	}
}

// UpdateMerchantRecommendStatus 更新商户推荐状态
func (s *AdminMerchantServiceImpl) UpdateMerchantRecommendStatus(ctx context.Context, id int64, isRecommended int) error {
	// 查询商户是否存在
	merchant := &models.Merchant{}
	err := s.o.QueryTable(new(models.Merchant)).Filter("id", id).One(merchant)
	if err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("商户不存在")
		}
		logs.Error("[UpdateMerchantRecommendStatus] 查询商户失败: %v", err)
		return err
	}

	// 更新推荐状态
	merchant.IsRecommended = isRecommended
	merchant.UpdatedAt = time.Now()

	// 保存更新
	_, err = s.o.Update(merchant, "IsRecommended", "UpdatedAt")
	if err != nil {
		logs.Error("[UpdateMerchantRecommendStatus] 更新商户推荐状态失败: %v", err)
		return err
	}

	logs.Info("[UpdateMerchantRecommendStatus] 商户推荐状态更新成功，商户ID: %d, 推荐状态: %d", id, isRecommended)
	return nil
}
