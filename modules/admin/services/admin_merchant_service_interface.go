/**
 * 管理员商户管理服务接口
 *
 * 该文件定义了管理员模块中商户管理相关的服务接口。
 * 包括商户查询、审核、状态管理等功能。
 */

package services

import (
	"context"

	"o_mall_backend/modules/admin/dto"
	merchantDto "o_mall_backend/modules/merchant/dto"
)

// AdminMerchantService 管理员商户管理服务接口
type AdminMerchantService interface {
	// ListMerchants 获取商户列表
	ListMerchants(ctx context.Context, req *dto.MerchantQueryRequest) ([]*dto.MerchantResponse, int64, error)

	// GetMerchantByID 获取商户详情
	GetMerchantByID(ctx context.Context, id int64) (*dto.MerchantResponse, error)

	// UpdateMerchant 更新商户信息
	UpdateMerchant(ctx context.Context, req *dto.UpdateMerchantRequest) error

	// UpdateMerchantStatus 更新商户状态
	UpdateMerchantStatus(ctx context.Context, id int64, status int) error

	// DisableMerchant 禁用商户
	DisableMerchant(ctx context.Context, id int64) error

	// EnableMerchant 启用商户
	EnableMerchant(ctx context.Context, id int64) error

	// DeleteMerchant 删除商户
	DeleteMerchant(ctx context.Context, id int64) error

	// ResetMerchantPassword 重置商户密码
	ResetMerchantPassword(ctx context.Context, req *dto.ResetMerchantPasswordRequest) error

	// AuditMerchant 审核商户
	AuditMerchant(ctx context.Context, req *dto.AuditMerchantRequest) error

	// CreateMerchant 管理员创建商户
	CreateMerchant(ctx context.Context, req *dto.CreateMerchantRequest) (int64, error)

	// UpdateMerchantOperationStatus 更新商户营业状态
	UpdateMerchantOperationStatus(ctx context.Context, id int64, req *merchantDto.UpdateOperationStatusRequest) error

	// UpdateMerchantCoordinates 更新商户坐标（经纬度）
	UpdateMerchantCoordinates(ctx context.Context, req *dto.UpdateMerchantRequest) error

	// UpdateMerchantRecommendStatus 更新商户推荐状态
	UpdateMerchantRecommendStatus(ctx context.Context, id int64, isRecommended int) error
}
