/**
 * 管理员服务实现
 *
 * 该文件实现了管理员服务接口，提供管理员登录、创建、管理等业务逻辑。
 * 管理员服务是系统后台管理的核心服务，负责管理员身份认证和信息管理。
 */

package services

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/models"
	"o_mall_backend/modules/admin/repositories"
	"o_mall_backend/utils"
)

// AdminServiceImpl 管理员服务实现
type AdminServiceImpl struct {
	adminRepo repositories.AdminRepository
}

// NewAdminService 创建管理员服务实例
func NewAdminService() AdminService {
	return &AdminServiceImpl{
		adminRepo: repositories.NewAdminRepository(),
	}
}

// Login 管理员登录
// 处理管理员登录流程，包括身份验证和JWT令牌生成
func (s *AdminServiceImpl) Login(ctx context.Context, req *dto.AdminLoginRequest, loginIP string) (*dto.AdminLoginResponse, error) {
	// 查询管理员
	admin, err := s.adminRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return nil, err
	}
	if admin == nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 检查管理员状态
	if admin.Status != models.AdminStatusNormal {
		return nil, errors.New("账号已被禁用")
	}

	// 验证密码
	if !utils.ValidatePassword(req.Password, admin.Password) {
		return nil, errors.New("用户名或密码错误")
	}

	// 更新登录信息
	if err := s.adminRepo.UpdateLoginInfo(ctx, admin.ID, loginIP); err != nil {
		logs.Error("更新登录信息失败: %v", err)
		// 登录信息更新失败不影响登录流程，继续执行
	}

	// 生成JWT令牌对
	tokenPair, err := utils.GenerateTokenPair(admin.ID, admin.Username, admin.Role)
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 存储refresh token
	err = utils.StoreRefreshToken(admin.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储refresh token失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	adminResp := convertToAdminResponse(admin)
	loginResp := &dto.AdminLoginResponse{
		TokenInfo: dto.TokenResponse{
			AccessToken:  tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresIn:    tokenPair.ExpiresIn,
			TokenType:    "Bearer",
		},
		Admin: *adminResp,
	}

	return loginResp, nil
}

// CreateAdmin 创建管理员
// 创建新的管理员账号，包括参数验证和密码加密
func (s *AdminServiceImpl) CreateAdmin(ctx context.Context, req *dto.CreateAdminRequest) (int64, error) {
	// 检查用户名是否已存在
	existAdmin, err := s.adminRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		logs.Error("查询用户名是否存在失败: %v", err)
		return 0, err
	}
	if existAdmin != nil {
		return 0, errors.New("用户名已存在")
	}

	// 验证角色是否合法
	if req.Role != models.AdminRoleSuper && req.Role != models.AdminRoleNormal {
		return 0, errors.New("无效的管理员角色")
	}

	// 加密密码
	hashedPassword, err := utils.EncryptPassword(req.Password)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return 0, err
	}

	// 处理权限
	permissionsJson, err := json.Marshal(req.Permissions)
	if err != nil {
		logs.Error("权限序列化失败: %v", err)
		return 0, err
	}

	// 创建管理员对象
	admin := &models.Admin{
		Username:    req.Username,
		Password:    hashedPassword,
		Nickname:    req.Nickname,
		Mobile:      req.Mobile,
		Email:       req.Email,
		Role:        req.Role,
		Permissions: string(permissionsJson),
		Status:      models.AdminStatusNormal,
	}

	// 保存管理员信息
	adminID, err := s.adminRepo.Create(ctx, admin)
	if err != nil {
		logs.Error("创建管理员失败: %v", err)
		return 0, err
	}

	return adminID, nil
}

// GetAdminByID 获取管理员信息
// 根据管理员ID获取管理员详细信息
func (s *AdminServiceImpl) GetAdminByID(ctx context.Context, id int64) (*dto.AdminResponse, error) {
	// 查询管理员
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return nil, err
	}
	if admin == nil {
		return nil, errors.New("管理员不存在")
	}

	// 转换为响应对象
	return convertToAdminResponse(admin), nil
}

// UpdateAdmin 更新管理员信息
// 更新管理员的基本信息，如昵称、角色、权限等
func (s *AdminServiceImpl) UpdateAdmin(ctx context.Context, req *dto.UpdateAdminRequest) error {
	// 查询管理员
	admin, err := s.adminRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("管理员不存在")
	}

	// 验证角色是否合法
	if req.Role != "" && (req.Role != models.AdminRoleSuper && req.Role != models.AdminRoleNormal) {
		return errors.New("无效的管理员角色")
	}

	// 只更新传入的字段，未传入的字段保持原值
	if req.Nickname != "" {
		admin.Nickname = req.Nickname
	}

	if req.Avatar != "" {
		admin.Avatar = req.Avatar
	}

	if req.Mobile != "" {
		admin.Mobile = req.Mobile
	}

	if req.Email != "" {
		admin.Email = req.Email
	}

	if req.Role != "" {
		admin.Role = req.Role
	}

	// 只有当权限列表不为空时才更新权限
	if req.Permissions != nil && len(req.Permissions) > 0 {
		permissionsJson, err := json.Marshal(req.Permissions)
		if err != nil {
			logs.Error("权限序列化失败: %v", err)
			return err
		}
		admin.Permissions = string(permissionsJson)
	}

	// 保存更新
	if err := s.adminRepo.Update(ctx, admin); err != nil {
		logs.Error("更新管理员信息失败: %v", err)
		return err
	}

	return nil
}

// ChangePassword 修改密码
// 修改管理员密码，需要验证原密码
func (s *AdminServiceImpl) ChangePassword(ctx context.Context, id int64, req *dto.ChangePasswordRequest) error {
	// 查询管理员
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("管理员不存在")
	}

	// 验证旧密码
	if !utils.ValidatePassword(req.OldPassword, admin.Password) {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := utils.EncryptPassword(req.NewPassword)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return err
	}

	// 更新密码
	if err := s.adminRepo.UpdatePassword(ctx, id, hashedPassword); err != nil {
		logs.Error("更新密码失败: %v", err)
		return err
	}

	return nil
}

// ResetPassword 重置密码
// 管理员重置其他管理员的密码，无需验证原密码
func (s *AdminServiceImpl) ResetPassword(ctx context.Context, req *dto.ResetPasswordRequest) error {
	// 查询管理员
	admin, err := s.adminRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("管理员不存在")
	}

	// 加密新密码
	hashedPassword, err := utils.EncryptPassword(req.NewPassword)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return err
	}

	// 更新密码
	if err := s.adminRepo.UpdatePassword(ctx, req.ID, hashedPassword); err != nil {
		logs.Error("重置密码失败: %v", err)
		return err
	}

	return nil
}

// ListAdmins 获取管理员列表
// 根据查询条件分页获取管理员列表
func (s *AdminServiceImpl) ListAdmins(ctx context.Context, req *dto.AdminQueryRequest) ([]*dto.AdminResponse, int64, error) {
	// 构建查询条件
	query := make(map[string]interface{})
	if req.Username != "" {
		query["username"] = req.Username
	}
	if req.Nickname != "" {
		query["nickname"] = req.Nickname
	}
	if req.Role != "" {
		query["role"] = req.Role
	}
	if req.Status != -1 {
		query["status"] = req.Status
	}

	// 设置默认分页参数
	page := req.Page
	if page < 1 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize < 1 {
		pageSize = 10
	}

	// 查询管理员列表
	admins, total, err := s.adminRepo.List(ctx, query, page, pageSize)
	if err != nil {
		logs.Error("查询管理员列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	adminResponses := make([]*dto.AdminResponse, 0, len(admins))
	for _, admin := range admins {
		adminResponses = append(adminResponses, convertToAdminResponse(admin))
	}

	return adminResponses, total, nil
}

// DisableAdmin 禁用管理员
// 将管理员状态设置为禁用
func (s *AdminServiceImpl) DisableAdmin(ctx context.Context, id int64) error {
	// 查询管理员
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("管理员不存在")
	}

	// 更新状态
	if err := s.adminRepo.UpdateStatus(ctx, id, models.AdminStatusDisabled); err != nil {
		logs.Error("禁用管理员失败: %v", err)
		return err
	}

	return nil
}

// EnableAdmin 启用管理员
// 将管理员状态设置为正常
func (s *AdminServiceImpl) EnableAdmin(ctx context.Context, id int64) error {
	// 查询管理员
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("管理员不存在")
	}

	// 更新状态
	if err := s.adminRepo.UpdateStatus(ctx, id, models.AdminStatusNormal); err != nil {
		logs.Error("启用管理员失败: %v", err)
		return err
	}

	return nil
}

// DisableUser 禁用用户
// 将用户状态设置为禁用
func (s *AdminServiceImpl) DisableUser(ctx context.Context, id int64) error {
	// 查询用户
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("用户不存在")
	}

	// 更新状态
	if err := s.adminRepo.UpdateStatus(ctx, id, models.AdminStatusDisabled); err != nil {
		logs.Error("禁用用户失败: %v", err)
		return err
	}

	return nil
}

// EnableUser 启用用户
// 将用户状态设置为正常
func (s *AdminServiceImpl) EnableUser(ctx context.Context, id int64) error {
	// 查询用户
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("用户不存在")
	}

	// 更新状态
	if err := s.adminRepo.UpdateStatus(ctx, id, models.AdminStatusNormal); err != nil {
		logs.Error("启用用户失败: %v", err)
		return err
	}

	return nil
}

// DeleteAdmin 删除管理员
// 从系统中删除管理员账号
func (s *AdminServiceImpl) DeleteAdmin(ctx context.Context, id int64) error {
	// 查询管理员
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("管理员不存在")
	}

	// 检查是否是超级管理员
	if admin.Role == models.AdminRoleSuper {
		return errors.New("不能删除超级管理员")
	}

	// 删除管理员
	if err := s.adminRepo.Delete(ctx, id); err != nil {
		logs.Error("删除管理员失败: %v", err)
		return err
	}

	return nil
}

// DeleteUser 删除用户
// 从系统中删除用户账号
// 注意：这里只是示例，实际的用户删除可能涉及更多业务逻辑，例如数据清理、权限回收等。
func (s *AdminServiceImpl) DeleteUser(ctx context.Context, id int64) error {
	// 查询用户
	admin, err := s.adminRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return err
	}
	if admin == nil {
		return errors.New("用户不存在")
	}

	// 删除用户
	if err := s.adminRepo.Delete(ctx, id); err != nil {
		logs.Error("删除用户失败: %v", err)
		return err
	}

	return nil
}

// convertToAdminResponse 将管理员模型转换为响应对象
// 内部辅助方法，用于转换数据格式
func convertToAdminResponse(admin *models.Admin) *dto.AdminResponse {
	if admin == nil {
		return nil
	}

	// 解析权限
	var permissions []string
	if admin.Permissions != "" {
		if err := json.Unmarshal([]byte(admin.Permissions), &permissions); err != nil {
			logs.Error("解析管理员权限失败: %v", err)
			permissions = []string{}
		}
	} else {
		permissions = []string{}
	}

	return &dto.AdminResponse{
		ID:          admin.ID,
		Username:    admin.Username,
		Nickname:    admin.Nickname,
		Avatar:      admin.Avatar,
		Mobile:      admin.Mobile,
		Email:       admin.Email,
		Role:        admin.Role,
		Permissions: permissions,
		Status:      admin.Status, // 格式化Status
		LastLoginAt: admin.LastLoginAt,
		CreatedAt:   admin.CreatedAt,
	}
}

// convertToUserResponse 将管理员模型转换为用户响应对象
// 内部辅助方法，用于转换数据格式
func convertToUserResponse(admin *models.Admin) *dto.UserResponse {
	if admin == nil {
		return nil
	}

	return &dto.UserResponse{
		ID:       admin.ID,
		Username: admin.Username,
		Nickname: admin.Nickname,
		Avatar:   admin.Avatar,
		Mobile:   admin.Mobile,
		Email:    admin.Email,
		//Gender:      admin.Gender,   // Assuming gender is stored in admin model as well
		//Birthday:    admin.Birthday, // Assuming birthday is stored in admin model as well
		//Balance:     admin.Balance,  // Assuming balance is stored in admin model as well
		//Points:      admin.Points,   // Assuming points is stored in admin model as well
		//Level:       admin.Level,    // Assuming level is stored in admin model as well
		Status:      admin.Status, // 格式化Status
		LastLoginAt: admin.LastLoginAt,
		CreatedAt:   admin.CreatedAt,
	}
}

// CheckAdminExists 检查是否存在管理员
// 用于系统初始化时检查是否需要创建初始管理员
func (s *AdminServiceImpl) CheckAdminExists(ctx context.Context) (int64, error) {
	// 调用仓库层查询管理员数量
	count, err := s.adminRepo.CountAdmins(ctx)
	if err != nil {
		return 0, err
	}

	// 返回管理员数量
	return count, nil
}

// CountAdmins 获取已启用的管理员数量
func (s *AdminServiceImpl) CountAdmins(ctx context.Context) (int64, error) {
	// 调用仓库层查询管理员数量
	count, err := s.adminRepo.CountAdmins(ctx)
	if err != nil {
		logs.Error("查询管理员数量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// RefreshToken 刷新访问令牌
// 使用有效的刷新令牌生成新的访问令牌和刷新令牌
func (s *AdminServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*dto.TokenResponse, error) {
	// 验证刷新令牌
	claims, err := utils.ValidateRefreshToken(refreshToken)
	if err != nil {
		logs.Error("刷新令牌验证失败: %v", err)
		return nil, errors.New("无效的刷新令牌")
	}

	// 检查刷新令牌是否存在于存储中
	valid, err := utils.CheckRefreshToken(claims.UserID, refreshToken)
	if err != nil || !valid {
		logs.Error("刷新令牌不存在或已失效: %v", err)
		return nil, errors.New("刷新令牌已失效")
	}

	// 查询管理员信息
	admin, err := s.adminRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		logs.Error("查询管理员失败: %v", err)
		return nil, err
	}
	if admin == nil {
		return nil, errors.New("管理员不存在")
	}

	// 检查管理员状态
	if admin.Status != models.AdminStatusNormal {
		return nil, errors.New("账号已被禁用")
	}

	// 生成新的令牌对
	tokenPair, err := utils.GenerateTokenPair(admin.ID, admin.Username, admin.Role)
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 删除旧的刷新令牌
	if err := utils.DeleteRefreshToken(admin.ID); err != nil {
		logs.Error("删除旧刷新令牌失败: %v", err)
		// 继续执行，不影响主流程
	}

	// 存储新的刷新令牌
	err = utils.StoreRefreshToken(admin.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储新刷新令牌失败: %v", err)
		return nil, err
	}

	return &dto.TokenResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
		TokenType:    "Bearer",
	}, nil
}

// Logout 管理员登出
// 使当前管理员的刷新令牌失效
func (s *AdminServiceImpl) Logout(ctx context.Context, adminID int64) error {
	// 删除该管理员的所有刷新令牌
	err := utils.DeleteAllRefreshTokens(adminID)
	if err != nil {
		logs.Error("删除管理员刷新令牌失败: %v", err)
		return err
	}
	return nil
}
