/**
 * 管理员用户管理服务实现
 *
 * 该文件实现了管理员模块中用户管理相关的服务接口。
 * 包括用户查询、更新、状态管理等功能的具体实现。
 */

package services

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/user/models"
	userModels "o_mall_backend/modules/user/models"
	userRepositories "o_mall_backend/modules/user/repositories"
	"o_mall_backend/utils"
)

// AdminUserServiceImpl 管理员用户管理服务实现
type AdminUserServiceImpl struct {
	o orm.Ormer
}

// NewAdminUserService 创建管理员用户管理服务实例
func NewAdminUserService() AdminUserService {
	return &AdminUserServiceImpl{
		o: orm.NewOrm(),
	}
}

// ListUsers 获取用户列表
func (s *AdminUserServiceImpl) ListUsers(ctx context.Context, req *dto.UserQueryRequest) ([]*dto.UserResponse, int64, error) {
	// 构建查询条件
	cond := orm.NewCondition()
	if req.Username != "" {
		cond = cond.And("username__icontains", req.Username)
	}
	if req.Mobile != "" {
		cond = cond.And("mobile__icontains", req.Mobile)
	}
	if req.Email != "" {
		cond = cond.And("email__icontains", req.Email)
	}
	if req.Status != 0 {
		cond = cond.And("status", req.Status)
	}

	// 查询总数
	var users []*models.User
	total, err := s.o.QueryTable(new(models.User)).SetCond(cond).Count()
	if err != nil {
		logs.Error("[ListUsers] 查询用户总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	_, err = s.o.QueryTable(new(models.User)).SetCond(cond).
		Offset((req.Page - 1) * req.PageSize).
		Limit(req.PageSize).
		OrderBy("-created_at").
		All(&users)
	if err != nil {
		logs.Error("[ListUsers] 查询用户列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	var result []*dto.UserResponse
	for _, user := range users {
		result = append(result, s.convertToUserResponse(user))
	}

	return result, total, nil
}

// GetUserByID 获取用户详情
func (s *AdminUserServiceImpl) GetUserByID(ctx context.Context, id int64) (*dto.UserResponse, error) {
	user := &models.User{ID: id}
	err := s.o.Read(user)
	if err == orm.ErrNoRows {
		return nil, fmt.Errorf("用户不存在")
	}
	if err != nil {
		logs.Error("[GetUserByID] 查询用户失败: %v", err)
		return nil, err
	}

	return s.convertToUserResponse(user), nil
}

// CreateUser 创建用户
func (s *AdminUserServiceImpl) CreateUser(ctx context.Context, req *dto.CreateUserRequest) (int64, error) {
	// 检查用户名是否已存在
	exist := s.o.QueryTable(new(models.User)).Filter("username", req.Username).Exist()
	if exist {
		return 0, fmt.Errorf("用户名已存在")
	}

	// 检查手机号是否已存在
	if req.Mobile != "" {
		exist = s.o.QueryTable(new(models.User)).Filter("mobile", req.Mobile).Exist()
		if exist {
			return 0, fmt.Errorf("手机号已被使用")
		}
	}

	// 检查邮箱是否已存在
	if req.Email != "" {
		exist = s.o.QueryTable(new(models.User)).Filter("email", req.Email).Exist()
		if exist {
			return 0, fmt.Errorf("邮箱已被使用")
		}
	}

	// 密码加密
	hashedPassword, err := utils.EncryptPassword(req.Password)
	if err != nil {
		logs.Error("[CreateUser] 密码加密失败: %v", err)
		return 0, fmt.Errorf("创建用户失败")
	}

	// 默认状态处理
	status := models.UserStatusEnabled
	if req.Status == models.UserStatusDisabled {
		status = models.UserStatusDisabled
	}

	// 创建用户实体
	now := time.Now()
	user := &models.User{
		Username:  req.Username,
		Password:  hashedPassword,
		Nickname:  req.Nickname,
		Avatar:    req.Avatar,
		Mobile:    req.Mobile,
		Email:     req.Email,
		Gender:    req.Gender,
		Birthday:  req.Birthday,
		Status:    status,
		Balance:   0,
		Points:    0,
		Level:     1,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// 开启事务
	tx, err := s.o.Begin()
	if err != nil {
		logs.Error("[CreateUser] 开启事务失败: %v", err)
		return 0, fmt.Errorf("创建用户失败")
	}

	// 插入用户记录
	id, err := tx.Insert(user)
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateUser] 创建用户失败: %v", err)
		return 0, fmt.Errorf("创建用户失败")
	}

	// 创建用户钱包(如需要)
	// 添加其他初始化操作...

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateUser] 提交事务失败: %v", err)
		return 0, fmt.Errorf("创建用户失败")
	}

	logs.Info("[CreateUser] 成功创建用户: %s, ID=%d", req.Username, id)
	return id, nil
}

// UpdateUser 更新用户信息
func (s *AdminUserServiceImpl) UpdateUser(ctx context.Context, req *dto.UpdateUserRequest) error {
	user := &models.User{ID: req.ID}
	err := s.o.Read(user)
	if err == orm.ErrNoRows {
		return fmt.Errorf("用户不存在")
	}
	if err != nil {
		logs.Error("[UpdateUser] 查询用户失败: %v", err)
		return err
	}

	// 更新用户信息
	user.Nickname = req.Nickname
	user.Mobile = req.Mobile
	user.Avatar = req.Avatar
	user.Gender = req.Gender
	user.Birthday = req.Birthday
	user.Email = req.Email
	user.UpdatedAt = time.Now()

	_, err = s.o.Update(user)
	if err != nil {
		logs.Error("[UpdateUser] 更新用户失败: %v", err)
		return err
	}

	return nil
}

// DisableUser 禁用用户
func (s *AdminUserServiceImpl) DisableUser(ctx context.Context, id int64) error {
	user := &models.User{ID: id}
	err := s.o.Read(user)
	if err == orm.ErrNoRows {
		return fmt.Errorf("用户不存在")
	}

	return nil
}

// EnableUser 启用用户
func (s *AdminUserServiceImpl) EnableUser(ctx context.Context, id int64) error {
	user := &models.User{ID: id}
	err := s.o.Read(user)
	if err == orm.ErrNoRows {
		return fmt.Errorf("用户不存在")
	}
	if err != nil {
		logs.Error("[EnableUser] 查询用户失败: %v", err)
		return err
	}

	user.Status = models.UserStatusEnabled
	user.UpdatedAt = time.Now()

	_, err = s.o.Update(user, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("[EnableUser] 启用用户失败: %v", err)
		return err
	}

	return nil
}

// DeleteUser 删除用户
func (s *AdminUserServiceImpl) DeleteUser(ctx context.Context, id int64) error {
	user := &models.User{ID: id}
	err := s.o.Read(user)
	if err == orm.ErrNoRows {
		return fmt.Errorf("用户不存在")
	}
	if err != nil {
		logs.Error("[DeleteUser] 查询用户失败: %v", err)
		return err
	}

	_, err = s.o.Delete(user)
	if err != nil {
		logs.Error("[DeleteUser] 删除用户失败: %v", err)
		return err
	}

	return nil
}

// ResetUserPassword 重置用户密码
func (s *AdminUserServiceImpl) ResetUserPassword(ctx context.Context, req *dto.ResetUserPasswordRequest) error {
	user := &models.User{ID: req.ID}
	err := s.o.Read(user)
	if err == orm.ErrNoRows {
		return fmt.Errorf("用户不存在")
	}
	if err != nil {
		logs.Error("[ResetUserPassword] 查询用户失败: %v", err)
		return err
	}

	// 生成密码哈希
	hashedPassword, err := utils.EncryptPassword(req.NewPassword)
	if err != nil {
		logs.Error("[ResetUserPassword] 生成密码哈希失败: %v", err)
		return err
	}

	user.Password = hashedPassword
	user.UpdatedAt = time.Now()

	_, err = s.o.Update(user, "Password", "UpdatedAt")
	if err != nil {
		logs.Error("[ResetUserPassword] 重置密码失败: %v", err)
		return err
	}

	return nil
}

// convertToUserResponse 将用户模型转换为响应对象
func (s *AdminUserServiceImpl) convertToUserResponse(user *models.User) *dto.UserResponse {
	return &dto.UserResponse{
		ID:          user.ID,
		Username:    user.Username,
		Nickname:    user.Nickname,
		Avatar:      user.Avatar,
		Mobile:      user.Mobile,
		Email:       user.Email,
		Gender:      user.Gender,
		Birthday:    user.Birthday,
		Balance:     user.Balance,
		Points:      user.Points,
		Level:       user.Level,
		Status:      user.Status,
		LastLoginAt: user.LastLoginAt,
		CreatedAt:   user.CreatedAt,
	}
}

// UpdateUserStatus 更新用户状态
func (s *AdminUserServiceImpl) UpdateUserStatus(ctx context.Context, req *dto.UpdateUserStatusRequest) error {
	// 查询用户是否存在
	user := &models.User{ID: req.ID}
	err := s.o.Read(user)
	if err == orm.ErrNoRows {
		return fmt.Errorf("用户不存在")
	}
	if err != nil {
		logs.Error("[UpdateUserStatus] 查询用户失败: %v", err)
		return err
	}

	// 更新状态
	user.Status = req.Status
	user.UpdatedAt = time.Now()

	// 保存更改
	_, err = s.o.Update(user, "status", "updated_at")
	if err != nil {
		logs.Error("[UpdateUserStatus] 更新用户状态失败: %v", err)
		return fmt.Errorf("更新用户状态失败")
	}

	// 日志记录
	statusStr := "禁用"
	if req.Status == models.UserStatusEnabled {
		statusStr = "启用"
	}
	logs.Info("[UpdateUserStatus] 成功%s用户: ID=%d", statusStr, req.ID)
	return nil
}

// formatUserStatus 将用户状态码格式化为字符串
// 内部辅助方法，用于将数字状态码转换为易读的字符串
func formatUserStatus(status int) string {
	switch status {
	case models.UserStatusEnabled: // 用户启用状态 // 0:禁用 1:正常
		return "ACTIVE"
	case models.UserStatusDisabled: // 用户禁用状态 // 0:禁用 1:正常
		return "DISABLED"
	default:
		return "LOCKED" // 默认状态为LOCKED，可以根据实际情况调整
	}
}

// UpdateUserBalanceAndPoints 更新用户余额和积分
func (s *AdminUserServiceImpl) UpdateUserBalanceAndPoints(ctx context.Context, req *dto.UpdateUserBalanceAndPointsRequest) error {
	o := s.o // 使用服务中已初始化的 ORM 实例

	// 1. 查询用户是否存在（事务外）
	user := &models.User{ID: req.ID}
	if err := o.Read(user); err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("用户不存在")
		}
		logs.Error("[UpdateUserBalanceAndPoints] 查询用户失败: %v", err)
		return fmt.Errorf("查询用户失败") // 控制器可能会将此转为通用错误
	}

	// 2. 获取用户账户信息
	userAccountRepo := userRepositories.NewUserAccountRepository()
	userAccount, err := userAccountRepo.GetUserAccountByUserID(req.ID)
	if err != nil {
		if err == orm.ErrNoRows {
			// 如果用户账户不存在，创建一个新的账户
			userAccount = &userModels.UserAccount{
				UserID:        req.ID,
				Balance:       0,
				FrozenBalance: 0,
				TotalRecharge: 0,
				TotalConsume:  0,
				Status:        1,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			accountID, err := userAccountRepo.CreateUserAccount(userAccount)
			if err != nil {
				logs.Error("[UpdateUserBalanceAndPoints] 创建用户账户失败: %v", err)
				return fmt.Errorf("创建用户账户失败")
			}
			// 设置账户ID
			userAccount.ID = accountID
			// 重新查询用户账户以确保获取完整信息
			userAccount, err = userAccountRepo.GetUserAccountByUserID(req.ID)
			if err != nil {
				logs.Error("[UpdateUserBalanceAndPoints] 重新查询用户账户失败: %v", err)
				return fmt.Errorf("查询用户账户失败")
			}
		} else {
			logs.Error("[UpdateUserBalanceAndPoints] 查询用户账户失败: %v", err)
			return fmt.Errorf("查询用户账户失败")
		}
	}

	// 3. 开始数据库事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[UpdateUserBalanceAndPoints] 开始事务失败: %v", err)
		return fmt.Errorf("系统错误，请稍后重试") // 控制器可能会将此转为通用错误
	}

	// 4. Defer 事务处理 (Commit 或 Rollback)
	var successInTx bool = false
	defer func() {
		if p := recover(); p != nil {
			// 如果发生 Panic
			logs.Error("[UpdateUserBalanceAndPoints] 处理过程中发生Panic: %v", p)
			rbErr := tx.Rollback()
			if rbErr != nil {
				logs.Error("[UpdateUserBalanceAndPoints] Panic后回滚事务失败: %v", rbErr)
			}
			// 重新抛出 panic，以便上层中间件（如果有）可以捕获它
			panic(p)
		} else if !successInTx {
			// 如果事务没有成功提交 (即 successInTx 为 false)
			logs.Warn("[UpdateUserBalanceAndPoints] 事务未成功提交，执行回滚")
			rbErr := tx.Rollback()
			if rbErr != nil {
				// 记录回滚失败，但原始错误更重要，所以不覆盖它
				logs.Error("[UpdateUserBalanceAndPoints] 事务回滚失败: %v", rbErr)
			}
		}
	}()

	// 5. 计算余额变化并更新用户账户
	// 添加空指针检查
	if userAccount == nil {
		logs.Error("[UpdateUserBalanceAndPoints] 用户账户对象为空")
		return fmt.Errorf("用户账户不存在或获取失败")
	}

	oldBalance := userAccount.Balance
	balanceChange := req.Balance - oldBalance

	// 更新用户账户余额
	userAccount.Balance = req.Balance
	userAccount.UpdatedAt = time.Now()

	// 根据余额变化更新统计字段
	if balanceChange > 0 {
		userAccount.TotalRecharge += balanceChange
		userAccount.LastRecharge = time.Now()
	} else if balanceChange < 0 {
		userAccount.TotalConsume += (-balanceChange)
		userAccount.LastConsume = time.Now()
	}

	// 在事务中更新用户账户
	if _, err = tx.Update(userAccount, "balance", "total_recharge", "total_consume", "last_recharge", "last_consume", "updated_at"); err != nil {
		logs.Error("[UpdateUserBalanceAndPoints] 更新用户账户失败: %v", err)
		return fmt.Errorf("更新用户账户失败")
	}

	// 6. 创建账户交易记录（如果余额有变化）
	if balanceChange != 0 {
		transactionNo := fmt.Sprintf("ADJ_%d_%d", req.ID, time.Now().Unix())
		transactionType := userModels.TransactionTypeAdjust
		operation := userModels.OperationTypeIncrease
		if balanceChange < 0 {
			operation = userModels.OperationTypeDecrease
			balanceChange = -balanceChange // 交易记录中金额为正数
		}

		transaction := &userModels.UserAccountTransaction{
			UserID:        req.ID,
			AccountID:     userAccount.ID,
			TransactionNo: transactionNo,
			RelatedID:     0,
			RelatedType:   "admin_adjust",
			Amount:        balanceChange,
			BeforeBalance: oldBalance,
			AfterBalance:  req.Balance,
			Type:          transactionType,
			Operation:     operation,
			Status:        1,
			Description:   "管理员调整账户余额",
			Remark:        fmt.Sprintf("管理员调整余额从%.2f到%.2f", oldBalance, req.Balance),
			OperatorID:    0,  // 这里可以从context中获取管理员ID
			ClientIP:      "", // 这里可以从context中获取IP
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		if _, err = userAccountRepo.CreateTransactionTx(tx, transaction); err != nil {
			logs.Error("[UpdateUserBalanceAndPoints] 创建交易记录失败: %v", err)
			return fmt.Errorf("创建交易记录失败")
		}
	}

	// 7. 更新用户积分
	user.Points = req.Points
	user.UpdatedAt = time.Now()
	if _, err = tx.Update(user, "points", "updated_at"); err != nil {
		logs.Error("[UpdateUserBalanceAndPoints] 更新用户积分失败: %v", err)
		return fmt.Errorf("更新用户积分失败")
	}

	// 8. 提交事务
	if err = tx.Commit(); err != nil {
		logs.Error("[UpdateUserBalanceAndPoints] 提交事务失败: %v", err)
		// successInTx 保持 false, defer 将执行回滚 (尽管提交已失败，回滚可能有助于清理)
		return fmt.Errorf("系统错误，请稍后重试") // 控制器可能会将此转为通用错误
	}

	successInTx = true // 标记事务成功提交
	return nil
}
