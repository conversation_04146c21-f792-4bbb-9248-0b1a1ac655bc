/**
 * 管理员用户管理服务接口
 *
 * 该文件定义了管理员模块中用户管理相关的服务接口。
 * 包括用户查询、更新、状态管理等功能。
 */

package services

import (
	"context"

	"o_mall_backend/modules/admin/dto"
)

// AdminUserService 管理员用户管理服务接口
type AdminUserService interface {
	// ListUsers 获取用户列表
	ListUsers(ctx context.Context, req *dto.UserQueryRequest) ([]*dto.UserResponse, int64, error)

	// GetUserByID 获取用户详情
	GetUserByID(ctx context.Context, id int64) (*dto.UserResponse, error)

	// CreateUser 创建用户
	CreateUser(ctx context.Context, req *dto.CreateUserRequest) (int64, error)

	// UpdateUser 更新用户信息
	UpdateUser(ctx context.Context, req *dto.UpdateUserRequest) error

	// DisableUser 禁用用户
	DisableUser(ctx context.Context, id int64) error

	// EnableUser 启用用户
	EnableUser(ctx context.Context, id int64) error

	// DeleteUser 删除用户
	DeleteUser(ctx context.Context, id int64) error

	// ResetUserPassword 重置用户密码
	ResetUserPassword(ctx context.Context, req *dto.ResetUserPasswordRequest) error

	// UpdateUserStatus 更新用户状态
	UpdateUserStatus(ctx context.Context, req *dto.UpdateUserStatusRequest) error

	// UpdateUserBalanceAndPoints 更新用户余额和积分
	UpdateUserBalanceAndPoints(ctx context.Context, req *dto.UpdateUserBalanceAndPointsRequest) error
}
