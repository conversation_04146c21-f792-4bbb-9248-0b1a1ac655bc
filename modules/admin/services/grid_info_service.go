/**
 * 网格布局管理员服务
 *
 * 该文件定义了管理员模块中网格布局相关的服务接口和实现
 */

package services

import (
	"context"
	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// AdminGridInfoService 管理员模块网格布局服务接口
type AdminGridInfoService interface {
	// ListGridInfos 获取网格布局列表
	ListGridInfos(ctx context.Context, queryDTO *dto.AdminGridInfoQueryDTO) ([]*dto.AdminGridInfoDTO, int64, error)

	// GetGridInfo 根据ID获取网格布局详情
	GetGridInfo(ctx context.Context, id int) (*dto.AdminGridInfoResponseDTO, error)

	// CreateGridInfo 创建网格布局
	CreateGridInfo(ctx context.Context, createDTO *dto.AdminGridInfoCreateDTO) (int, error)

	// UpdateGridInfo 更新网格布局
	UpdateGridInfo(ctx context.Context, id int, updateDTO *dto.AdminGridInfoUpdateDTO) error

	// DeleteGridInfo 删除网格布局
	DeleteGridInfo(ctx context.Context, id int) error

	// UpdateGridInfoStatus 更新网格布局状态
	UpdateGridInfoStatus(ctx context.Context, id int, status int) error

	// BatchUpdateGridInfoPosition 批量更新网格布局位置
	BatchUpdateGridInfoPosition(ctx context.Context, batchDTO *dto.AdminGridInfoBatchUpdatePositionDTO) error

	// ListGridInfosByUIConfigID 根据UI配置ID获取关联的网格布局列表
	ListGridInfosByUIConfigID(ctx context.Context, uiConfigID int) ([]*dto.AdminGridInfoDTO, error)

	// ListUIConfigsByGridInfoID 根据网格布局ID获取关联的UI配置列表
	ListUIConfigsByGridInfoID(ctx context.Context, gridInfoID int) ([]*dto.AdminUIConfigSimpleDTO, error)

	// AddUIConfigsToGridInfo 为网格布局添加UI配置关联
	AddUIConfigsToGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error

	// RemoveUIConfigsFromGridInfo 从网格布局移除UI配置关联
	RemoveUIConfigsFromGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error

	// UpdateGridInfoUIConfigs 更新网格布局关联的UI配置
	UpdateGridInfoUIConfigs(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error

	// DeleteUIConfigGridRelation 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
	DeleteUIConfigGridRelation(ctx context.Context, gridInfoID int, uiConfigID int) error

	// UpdateBatchUIConfigGridRelation 批量设置UI配置与多个网格布局的关联关系
	UpdateBatchUIConfigGridRelation(ctx context.Context, batchDTO *dto.AdminBatchUIConfigGridRelationDTO) error
}

// AdminGridInfoServiceImpl 管理员模块网格布局服务实现
type AdminGridInfoServiceImpl struct {
	gridInfoRepo repositories.AdminGridInfoRepository
}

// NewAdminGridInfoService 创建管理员模块网格布局服务的实例
func NewAdminGridInfoService() AdminGridInfoService {
	return &AdminGridInfoServiceImpl{
		gridInfoRepo: repositories.NewAdminGridInfoRepository(),
	}
}

// ListGridInfos 获取网格布局列表
func (s *AdminGridInfoServiceImpl) ListGridInfos(ctx context.Context, queryDTO *dto.AdminGridInfoQueryDTO) ([]*dto.AdminGridInfoDTO, int64, error) {
	// 构建查询条件
	query := make(map[string]interface{})
	if queryDTO.UIConfigID > 0 {
		query["ui_config_id"] = queryDTO.UIConfigID
	}
	if queryDTO.Name != "" {
		query["name"] = queryDTO.Name
	}
	if queryDTO.Status > 0 {
		query["status"] = queryDTO.Status
	}

	// 调用仓库层获取网格布局列表
	gridItems, total, err := s.gridInfoRepo.ListGridInfos(ctx, query, queryDTO.Page, queryDTO.PageSize)
	if err != nil {
		logs.Error("[ListGridInfos] 获取网格布局列表失败: %v", err)
		return nil, 0, err
	}

	return gridItems, total, nil
}

// GetGridInfo 获取网格布局详情
func (s *AdminGridInfoServiceImpl) GetGridInfo(ctx context.Context, id int) (*dto.AdminGridInfoResponseDTO, error) {
	// 调用仓库层获取网格布局详情
	gridItem, err := s.gridInfoRepo.GetGridInfo(ctx, id)
	if err != nil {
		logs.Error("[GetGridInfo] 获取网格布局详情失败: %v", err)
		return nil, err
	}

	return gridItem, nil
}

// CreateGridInfo 创建网格布局
func (s *AdminGridInfoServiceImpl) CreateGridInfo(ctx context.Context, createDTO *dto.AdminGridInfoCreateDTO) (int, error) {
	// 调用仓库层创建网格布局
	id, err := s.gridInfoRepo.CreateGridInfo(ctx, createDTO)
	if err != nil {
		logs.Error("[CreateGridInfo] 创建网格布局失败: %v", err)
		return 0, err
	}

	return id, nil
}

// UpdateGridInfo 更新网格布局
func (s *AdminGridInfoServiceImpl) UpdateGridInfo(ctx context.Context, id int, updateDTO *dto.AdminGridInfoUpdateDTO) error {
	// 调用仓库层更新网格布局
	err := s.gridInfoRepo.UpdateGridInfo(ctx, id, updateDTO)
	if err != nil {
		logs.Error("[UpdateGridInfo] 更新网格布局失败: %v", err)
		return err
	}

	return nil
}

// DeleteGridInfo 删除网格布局
func (s *AdminGridInfoServiceImpl) DeleteGridInfo(ctx context.Context, id int) error {
	// 调用仓库层删除网格布局
	err := s.gridInfoRepo.DeleteGridInfo(ctx, id)
	if err != nil {
		logs.Error("[DeleteGridInfo] 删除网格布局失败: %v", err)
		return err
	}

	return nil
}

// UpdateGridInfoStatus 更新网格布局状态
func (s *AdminGridInfoServiceImpl) UpdateGridInfoStatus(ctx context.Context, id int, status int) error {
	// 调用仓库层更新网格布局状态
	err := s.gridInfoRepo.UpdateGridInfoStatus(ctx, id, status)
	if err != nil {
		logs.Error("[UpdateGridInfoStatus] 更新网格布局状态失败: %v", err)
		return err
	}

	return nil
}

// BatchUpdateGridInfoPosition 批量更新网格布局位置
func (s *AdminGridInfoServiceImpl) BatchUpdateGridInfoPosition(ctx context.Context, batchDTO *dto.AdminGridInfoBatchUpdatePositionDTO) error {
	// 转换为所需的指针类型
	posItems := make([]*dto.AdminGridInfoPositionItem, 0, len(batchDTO.Items))
	for i := range batchDTO.Items {
		posItems = append(posItems, &batchDTO.Items[i])
	}
	logs.Info("[BatchUpdateGridInfoPosition] 批量更新网格布局位置=%v", posItems)

	// 调用仓库层批量更新网格布局位置
	err := s.gridInfoRepo.BatchUpdateGridInfoPosition(ctx, posItems)
	if err != nil {
		logs.Error("[BatchUpdateGridInfoPosition] 批量更新网格布局位置失败: %v", err)
		return err
	}

	return nil
}

// ListGridInfosByUIConfigID 根据UI配置ID获取关联的网格布局列表
func (s *AdminGridInfoServiceImpl) ListGridInfosByUIConfigID(ctx context.Context, uiConfigID int) ([]*dto.AdminGridInfoDTO, error) {
	// 调用仓库层获取关联的网格布局列表
	gridItems, err := s.gridInfoRepo.ListGridInfosByUIConfigID(ctx, uiConfigID)
	if err != nil {
		logs.Error("[ListGridInfosByUIConfigID] 获取关联的网格布局列表失败: %v", err)
		return nil, err
	}

	return gridItems, nil
}

// ListUIConfigsByGridInfoID 根据网格布局ID获取关联的UI配置列表
func (s *AdminGridInfoServiceImpl) ListUIConfigsByGridInfoID(ctx context.Context, gridInfoID int) ([]*dto.AdminUIConfigSimpleDTO, error) {
	// 调用仓库层获取关联的UI配置列表
	uiConfigs, err := s.gridInfoRepo.ListUIConfigsByGridInfoID(ctx, gridInfoID)
	if err != nil {
		logs.Error("[ListUIConfigsByGridInfoID] 获取关联的UI配置列表失败: %v", err)
		return nil, err
	}

	return uiConfigs, nil
}

// AddUIConfigsToGridInfo 为网格布局添加UI配置关联
func (s *AdminGridInfoServiceImpl) AddUIConfigsToGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error {
	// 调用仓库层添加UI配置关联
	err := s.gridInfoRepo.AddUIConfigsToGridInfo(ctx, relationDTO)
	if err != nil {
		logs.Error("[AddUIConfigsToGridInfo] 添加UI配置关联失败: %v", err)
		return err
	}

	return nil
}

// RemoveUIConfigsFromGridInfo 从网格布局移除UI配置关联
func (s *AdminGridInfoServiceImpl) RemoveUIConfigsFromGridInfo(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error {
	// 调用仓库层移除UI配置关联
	err := s.gridInfoRepo.RemoveUIConfigsFromGridInfo(ctx, relationDTO)
	if err != nil {
		logs.Error("[RemoveUIConfigsFromGridInfo] 移除UI配置关联失败: %v", err)
		return err
	}

	return nil
}

// UpdateGridInfoUIConfigs 更新网格布局关联的UI配置
func (s *AdminGridInfoServiceImpl) UpdateGridInfoUIConfigs(ctx context.Context, relationDTO *dto.AdminGridInfoUIConfigRelationDTO) error {
	// 调用仓库层更新网格布局关联的UI配置
	err := s.gridInfoRepo.UpdateGridInfoUIConfigs(ctx, relationDTO)
	if err != nil {
		logs.Error("[UpdateGridInfoUIConfigs] 更新网格布局关联的UI配置失败: %v", err)
		return err
	}

	return nil
}

// DeleteUIConfigGridRelation 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
func (s *AdminGridInfoServiceImpl) DeleteUIConfigGridRelation(ctx context.Context, gridInfoID int, uiConfigID int) error {
	err := s.gridInfoRepo.DeleteUIConfigGridRelation(ctx, gridInfoID, uiConfigID)
	if err != nil {
		logs.Error("[DeleteUIConfigGridRelation] 删除UIConfigGridRelation失败: %v", err)
		return err
	}
	return nil
}

// UpdateBatchUIConfigGridRelation 批量设置UI配置与多个网格布局的关联关系
// @description 将一个UIConfigId批量关联到多个GridInfoId
func (s *AdminGridInfoServiceImpl) UpdateBatchUIConfigGridRelation(ctx context.Context, batchDTO *dto.AdminBatchUIConfigGridRelationDTO) error {
	return s.gridInfoRepo.UpdateBatchUIConfigGridRelation(ctx, batchDTO)
}
