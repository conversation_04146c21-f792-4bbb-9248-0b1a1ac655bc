/**
 * AI服务实现
 *
 * 本文件实现了AI服务接口，提供了AI配置管理的具体方法。
 */

package impl

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/client/cache"
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/models"
	"o_mall_backend/modules/admin/services"
)

// 声明缓存对象，用于缓存AI配置
var aiConfigCache cache.Cache

// 常量定义
const (
	AIConfigCacheKey = "system:ai:config:deepseek"   // AI配置缓存键
	CacheExpiration  = 3600 * time.Second            // 缓存过期时间（秒）
	DeepSeekProvider = "deepseek"                    // DeepSeek提供商标识
	DefaultBaseURL   = "https://api.deepseek.com/v1" // 默认API基础URL
	DefaultModel     = "deepseek-chat"               // 默认模型
)

// AIServiceImpl AI服务实现
type AIServiceImpl struct{}

// NewAIServiceImpl 创建AI服务实现实例
func NewAIServiceImpl() services.AIService {
	// 初始化缓存（如果尚未初始化）
	if aiConfigCache == nil {
		var err error
		aiConfigCache, err = cache.NewCache("memory", `{"interval":60}`)
		if err != nil {
			logs.Error("初始化AI配置缓存失败: %v", err)
		}
	}
	return &AIServiceImpl{}
}

// GetAIConfig 获取AI配置
// 首先尝试从缓存获取配置，如果缓存中不存在则从数据库获取
func (s *AIServiceImpl) GetAIConfig() (*dto.AIConfigDTO, error) {
	logs.Info("开始获取AI配置")

	// 确保AI配置表已创建
	if err := models.CreateAIConfigTable(); err != nil {
		logs.Error("创建AI配置表失败: %v", err)
		return createDefaultConfig(nil)
	}

	// 尝试从缓存获取
	var config dto.AIConfigDTO
	ctx := context.Background()

	if aiConfigCache != nil {
		data, err := aiConfigCache.Get(ctx, AIConfigCacheKey)
		if err == nil && data != nil {
			if cachedConfig, ok := data.(dto.AIConfigDTO); ok {
				logs.Info("从缓存获取AI配置成功")
				return &cachedConfig, nil
			}
		}
	}

	logs.Info("缓存中没有AI配置，尝试从数据库获取")

	// 创建orm对象
	o := orm.NewOrm()
	if o == nil {
		err := errors.New("创建ORM对象失败")
		logs.Error(err.Error())
		return createDefaultConfig(nil)
	}

	// 从数据库获取
	aiConfig := &models.AIConfig{}
	err := aiConfig.GetByProvider(o, DeepSeekProvider)

	// 处理错误情况
	if err != nil {
		logs.Info("从数据库获取AI配置失败: %v，将创建默认配置", err)

		// 如果是记录不存在，创建默认配置
		if err == orm.ErrNoRows {
			logs.Info("AI配置记录不存在，创建默认配置")
			return createAndSaveDefaultConfig(o)
		}

		// 其他错误，也尝试创建默认配置
		logs.Error("获取AI配置出错: %v", err)
		return createDefaultConfig(o)
	}

	logs.Info("成功从数据库获取AI配置: %v", aiConfig.DefaultModel)

	// 转换为DTO
	config = dto.AIConfigDTO{
		ID:             aiConfig.ID,
		DeepSeekAPIKey: aiConfig.APIKey,
		BaseURL:        aiConfig.BaseURL,
		DefaultModel:   aiConfig.DefaultModel,
	}

	// 缓存配置
	if aiConfigCache != nil {
		aiConfigCache.Put(ctx, AIConfigCacheKey, config, CacheExpiration)
		logs.Info("AI配置已缓存")
	}

	return &config, nil
}

// createDefaultConfig 创建默认配置（不保存到数据库）
func createDefaultConfig(o orm.Ormer) (*dto.AIConfigDTO, error) {
	logs.Info("创建默认AI配置")

	// 尝试从环境变量获取API密钥
	apiKey, err := web.AppConfig.String("deepseek_api_key")
	if err != nil {
		logs.Warning("无法从配置中获取deepseek_api_key: %v", err)
		apiKey = ""
	}

	// 创建默认配置
	config := &dto.AIConfigDTO{
		DeepSeekAPIKey: apiKey,
		BaseURL:        DefaultBaseURL,
		DefaultModel:   DefaultModel,
	}

	// 如果提供了orm对象，尝试将配置保存到数据库
	if o != nil {
		logs.Info("尝试将默认配置保存到数据库")
		aiConfig := &models.AIConfig{
			Provider:     DeepSeekProvider,
			APIKey:       apiKey,
			BaseURL:      DefaultBaseURL,
			DefaultModel: DefaultModel,
			Enabled:      true,
		}

		if err := aiConfig.InsertOrUpdate(o); err != nil {
			logs.Error("保存默认AI配置失败: %v", err)
			// 即使保存失败，仍返回默认配置
		} else {
			logs.Info("保存默认AI配置成功")
		}
	}

	return config, nil
}

// createAndSaveDefaultConfig 创建默认配置并保存到数据库
func createAndSaveDefaultConfig(o orm.Ormer) (*dto.AIConfigDTO, error) {
	if o == nil {
		logs.Error("ORM对象为空，无法保存配置")
		return createDefaultConfig(nil)
	}

	return createDefaultConfig(o)
}

// SaveAIConfig 保存AI配置
// 将AI配置保存到数据库，并更新缓存
func (s *AIServiceImpl) SaveAIConfig(config *dto.AIConfigDTO) error {
	if config == nil {
		return errors.New("AI配置不能为空")
	}

	logs.Info("开始保存AI配置")

	// 确保AI配置表已创建
	if err := models.CreateAIConfigTable(); err != nil {
		logs.Error("创建AI配置表失败: %v", err)
		return err
	}

	// 创建orm对象
	o := orm.NewOrm()
	if o == nil {
		return errors.New("创建ORM对象失败")
	}

	// 创建或更新数据库记录
	aiConfig := &models.AIConfig{}
	err := aiConfig.GetByProvider(o, DeepSeekProvider)
	if err != nil && err != orm.ErrNoRows {
		logs.Error("查询AI配置失败: %v", err)
		// 即使查询失败，也继续尝试保存新配置
	}

	// 设置值
	aiConfig.Provider = DeepSeekProvider
	aiConfig.APIKey = config.DeepSeekAPIKey
	aiConfig.BaseURL = config.BaseURL
	aiConfig.DefaultModel = config.DefaultModel
	aiConfig.Enabled = true

	// 保存到数据库
	if err := aiConfig.InsertOrUpdate(o); err != nil {
		logs.Error("保存AI配置到数据库失败: %v", err)
		return errors.New("保存AI配置失败: " + err.Error())
	}

	logs.Info("AI配置已保存到数据库")

	// 更新缓存
	ctx := context.Background()
	if aiConfigCache != nil {
		aiConfigCache.Put(ctx, AIConfigCacheKey, *config, CacheExpiration)
		logs.Info("AI配置已更新到缓存")
	}

	return nil
}

// RefreshAIConfig 刷新AI配置缓存
// 从数据库重新加载AI配置并更新缓存
func (s *AIServiceImpl) RefreshAIConfig() error {
	logs.Info("开始刷新AI配置缓存")

	// 确保AI配置表已创建
	if err := models.CreateAIConfigTable(); err != nil {
		logs.Error("创建AI配置表失败: %v", err)
		return err
	}

	// 创建orm对象
	o := orm.NewOrm()
	if o == nil {
		return errors.New("创建ORM对象失败")
	}

	// 从数据库获取
	aiConfig := &models.AIConfig{}
	err := aiConfig.GetByProvider(o, DeepSeekProvider)
	if err != nil {
		logs.Error("刷新AI配置时，从数据库获取失败: %v", err)
		return errors.New("获取AI配置失败: " + err.Error())
	}

	logs.Info("从数据库获取AI配置成功，开始更新缓存")

	// 转换为DTO
	config := dto.AIConfigDTO{
		DeepSeekAPIKey: aiConfig.APIKey,
		BaseURL:        aiConfig.BaseURL,
		DefaultModel:   aiConfig.DefaultModel,
	}

	// 更新缓存
	ctx := context.Background()
	if aiConfigCache != nil {
		aiConfigCache.Put(ctx, AIConfigCacheKey, config, CacheExpiration)
		logs.Info("AI配置缓存已刷新")
	} else {
		logs.Warning("缓存对象为空，无法刷新缓存")
	}

	return nil
}
