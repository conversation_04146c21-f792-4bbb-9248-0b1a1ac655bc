/**
 * 管理员服务接口定义
 *
 * 该文件定义了管理员模块的服务层接口，声明了管理员相关的业务方法。
 * 通过接口定义与实现分离，便于单元测试和功能扩展。
 */

package services

import (
	"context"

	"o_mall_backend/modules/admin/dto"
)

// AdminService 管理员服务接口
type AdminService interface {
	// 管理员认证相关
	Login(ctx context.Context, req *dto.AdminLoginRequest, loginIP string) (*dto.AdminLoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*dto.TokenResponse, error)
	Logout(ctx context.Context, adminID int64) error
	CreateAdmin(ctx context.Context, req *dto.CreateAdminRequest) (int64, error)

	// 管理员信息相关
	GetAdminByID(ctx context.Context, id int64) (*dto.AdminResponse, error)
	UpdateAdmin(ctx context.Context, req *dto.UpdateAdminRequest) error
	ChangePassword(ctx context.Context, id int64, req *dto.ChangePasswordRequest) error
	ResetPassword(ctx context.Context, req *dto.ResetPasswordRequest) error

	// 管理员管理相关
	ListAdmins(ctx context.Context, req *dto.AdminQueryRequest) ([]*dto.AdminResponse, int64, error)
	DisableAdmin(ctx context.Context, id int64) error
	EnableAdmin(ctx context.Context, id int64) error
	DeleteAdmin(ctx context.Context, id int64) error

	CheckAdminExists(ctx context.Context) (int64, error)
	// CountAdmins 获取已启用的管理员数量
	CountAdmins(ctx context.Context) (int64, error)
}
