/**
 * 管理员模块商家分类服务
 *
 * 该文件实现了管理员管理商家分类的服务层逻辑
 * 包括商家分类的创建、查询、更新、删除等功能
 */

package services

import (
	"context"
	"fmt"

	"github.com/beego/beego/v2/core/logs"

	adminDto "o_mall_backend/modules/admin/dto"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/repositories"
)

// AdminMerchantCategoryService 管理员商家分类服务接口
type AdminMerchantCategoryService interface {
	// ListCategories 获取商家分类列表
	ListCategories(ctx context.Context, req *adminDto.MerchantCategoryQueryRequest) (*adminDto.MerchantCategoryListResponse, error)

	// GetCategoryByID 根据ID获取商家分类
	GetCategoryByID(ctx context.Context, id int64) (*adminDto.MerchantCategoryResponse, error)

	// CreateCategory 创建商家分类
	CreateCategory(ctx context.Context, req *adminDto.CreateMerchantCategoryRequest) (int64, error)

	// UpdateCategory 更新商家分类
	UpdateCategory(ctx context.Context, id int64, req *adminDto.UpdateMerchantCategoryRequest) error

	// DeleteCategory 删除商家分类
	DeleteCategory(ctx context.Context, id int64) error

	// GetAllCategories 获取所有商家分类
	GetAllCategories(ctx context.Context, onlyShow bool) ([]*adminDto.MerchantCategoryResponse, error)
}

// AdminMerchantCategoryServiceImpl 管理员商家分类服务实现
type AdminMerchantCategoryServiceImpl struct {
	categoryRepo repositories.MerchantCategoryRepository
}

// NewAdminMerchantCategoryService 创建管理员商家分类服务实例
func NewAdminMerchantCategoryService() AdminMerchantCategoryService {
	return &AdminMerchantCategoryServiceImpl{
		categoryRepo: repositories.NewMerchantCategoryRepository(),
	}
}

// ListCategories 获取商家分类列表
func (s *AdminMerchantCategoryServiceImpl) ListCategories(ctx context.Context, req *adminDto.MerchantCategoryQueryRequest) (*adminDto.MerchantCategoryListResponse, error) {
	// 构建查询参数
	params := &merchantDto.MerchantCategoryQueryParams{
		Name:   req.Name,
		IsShow: req.IsShow,
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询分类列表
	categories, total, err := s.categoryRepo.List(ctx, params, page, pageSize)
	if err != nil {
		logs.Error("获取商家分类列表失败: %v", err)
		return nil, fmt.Errorf("获取商家分类列表失败: %v", err)
	}

	// 转换为响应DTO
	var items []*adminDto.MerchantCategoryResponse
	for _, category := range categories {
		items = append(items, s.convertToResponse(category))
	}

	// 构建响应
	response := &adminDto.MerchantCategoryListResponse{
		Total: total,
		Items: items,
	}

	return response, nil
}

// GetCategoryByID 根据ID获取商家分类
func (s *AdminMerchantCategoryServiceImpl) GetCategoryByID(ctx context.Context, id int64) (*adminDto.MerchantCategoryResponse, error) {
	// 查询分类
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商家分类失败: %v", err)
		return nil, fmt.Errorf("获取商家分类失败: %v", err)
	}

	if category == nil {
		return nil, fmt.Errorf("商家分类不存在")
	}

	// 转换为响应DTO
	response := s.convertToResponse(category)
	return response, nil
}

// CreateCategory 创建商家分类
func (s *AdminMerchantCategoryServiceImpl) CreateCategory(ctx context.Context, req *adminDto.CreateMerchantCategoryRequest) (int64, error) {
	// 构建分类模型
	category := &models.MerchantCategory{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
		IsShow:      req.IsShow,
	}

	// 创建分类
	id, err := s.categoryRepo.Create(ctx, category)
	if err != nil {
		logs.Error("创建商家分类失败: %v", err)
		return 0, fmt.Errorf("创建商家分类失败: %v", err)
	}

	return id, nil
}

// UpdateCategory 更新商家分类
func (s *AdminMerchantCategoryServiceImpl) UpdateCategory(ctx context.Context, id int64, req *adminDto.UpdateMerchantCategoryRequest) error {
	// 先查询分类是否存在
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商家分类失败: %v", err)
		return fmt.Errorf("获取商家分类失败: %v", err)
	}

	if category == nil {
		return fmt.Errorf("商家分类不存在")
	}

	// 更新分类信息
	if req.Name != "" {
		category.Name = req.Name
	}
	if req.Description != "" || req.Description == "" { // 允许将描述设置为空
		category.Description = req.Description
	}
	if req.Icon != "" {
		category.Icon = req.Icon
	}
	
	category.SortOrder = req.SortOrder
	category.IsShow = req.IsShow

	// 更新分类
	err = s.categoryRepo.Update(ctx, category)
	if err != nil {
		logs.Error("更新商家分类失败: %v", err)
		return fmt.Errorf("更新商家分类失败: %v", err)
	}

	return nil
}

// DeleteCategory 删除商家分类
func (s *AdminMerchantCategoryServiceImpl) DeleteCategory(ctx context.Context, id int64) error {
	// 先查询分类是否存在
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商家分类失败: %v", err)
		return fmt.Errorf("获取商家分类失败: %v", err)
	}

	if category == nil {
		return fmt.Errorf("商家分类不存在")
	}

	// 删除分类
	err = s.categoryRepo.Delete(ctx, id)
	if err != nil {
		logs.Error("删除商家分类失败: %v", err)
		return fmt.Errorf("删除商家分类失败: %v", err)
	}

	return nil
}

// GetAllCategories 获取所有商家分类
func (s *AdminMerchantCategoryServiceImpl) GetAllCategories(ctx context.Context, onlyShow bool) ([]*adminDto.MerchantCategoryResponse, error) {
	// 查询所有分类
	categories, err := s.categoryRepo.GetAll(ctx, onlyShow)
	if err != nil {
		logs.Error("获取所有商家分类失败: %v", err)
		return nil, fmt.Errorf("获取所有商家分类失败: %v", err)
	}

	// 转换为响应DTO
	var responses []*adminDto.MerchantCategoryResponse
	for _, category := range categories {
		responses = append(responses, s.convertToResponse(category))
	}

	return responses, nil
}

// convertToResponse 将分类模型转换为响应DTO
func (s *AdminMerchantCategoryServiceImpl) convertToResponse(category *models.MerchantCategory) *adminDto.MerchantCategoryResponse {
	return &adminDto.MerchantCategoryResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsShow:      category.IsShow,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}
