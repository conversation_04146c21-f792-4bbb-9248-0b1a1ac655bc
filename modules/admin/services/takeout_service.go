/**
 * 外卖服务工具类
 *
 * 本文件用于将takeout模块的服务注册到admin模块中使用，
 * 解决跨模块调用的问题。
 */

package services

import (
	"context"
	adminDto "o_mall_backend/modules/admin/dto"
	takeoutServices "o_mall_backend/modules/takeout/services"
)

// TakeoutStatisticsService 外卖统计服务接口
type TakeoutStatisticsService interface {
	// GetMerchantTakeoutStatistics 获取商户外卖统计数据
	GetMerchantTakeoutStatistics(ctx context.Context, merchantID int64) (*adminDto.MerchantTakeoutStatisticsDTO, error)
}

// takeoutStatisticsService 外卖统计服务实现
type takeoutStatisticsService struct {
	statisticsService takeoutServices.MerchantTakeoutStatisticsService
}

// NewTakeoutStatisticsService 创建外卖统计服务实例
func NewTakeoutStatisticsService() TakeoutStatisticsService {
	return &takeoutStatisticsService{
		statisticsService: takeoutServices.NewMerchantTakeoutStatisticsService(),
	}
}

// GetMerchantTakeoutStatistics 获取商户外卖统计数据
func (s *takeoutStatisticsService) GetMerchantTakeoutStatistics(ctx context.Context, merchantID int64) (*adminDto.MerchantTakeoutStatisticsDTO, error) {
	return s.statisticsService.GetMerchantTakeoutStatistics(ctx, merchantID)
}
