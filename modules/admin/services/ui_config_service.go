/**
 * UI配置管理员服务
 *
 * 该文件定义了管理员模块中UI配置相关的服务接口和实现
 */

package services

import (
	"context"
	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// AdminUIConfigService 管理员模块UI配置服务接口
type AdminUIConfigService interface {
	// ListUIConfigs 获取UI配置列表
	ListUIConfigs(ctx context.Context, queryDTO *dto.AdminUIConfigQueryDTO) ([]*dto.AdminUIConfigDTO, int64, error)

	// GetUIConfig 获取UI配置详情
	GetUIConfig(ctx context.Context, id int) (*dto.AdminUIConfigDTO, error)

	// CreateUIConfig 创建UI配置
	CreateUIConfig(ctx context.Context, createDTO *dto.AdminUIConfigCreateDTO) (int, error)

	// UpdateUIConfig 更新UI配置
	UpdateUIConfig(ctx context.Context, id int, updateDTO *dto.AdminUIConfigUpdateDTO) error

	// DeleteUIConfig 删除UI配置
	DeleteUIConfig(ctx context.Context, id int) error

	// UpdateUIConfigStatus 更新UI配置状态
	UpdateUIConfigStatus(ctx context.Context, id int, status int) error
}

// AdminUIConfigServiceImpl 管理员模块UI配置服务实现
type AdminUIConfigServiceImpl struct {
	uiConfigRepo repositories.AdminUIConfigRepository
	gridInfoRepo repositories.AdminGridInfoRepository
}

// NewAdminUIConfigService 创建管理员模块UI配置服务的实例
func NewAdminUIConfigService() AdminUIConfigService {
	return &AdminUIConfigServiceImpl{
		uiConfigRepo: repositories.NewAdminUIConfigRepository(),
		gridInfoRepo: repositories.NewAdminGridInfoRepository(),
	}
}

// ListUIConfigs 获取UI配置列表
func (s *AdminUIConfigServiceImpl) ListUIConfigs(ctx context.Context, queryDTO *dto.AdminUIConfigQueryDTO) ([]*dto.AdminUIConfigDTO, int64, error) {
	// 构建查询条件
	query := make(map[string]interface{})
	if queryDTO.Module != "" {
		query["module"] = queryDTO.Module
	}
	if queryDTO.ConfigType != "" {
		query["config_type"] = queryDTO.ConfigType
	}
	if queryDTO.Title != "" {
		query["title"] = queryDTO.Title
	}
	if queryDTO.Status != 0 {
		query["status"] = queryDTO.Status
	}
	if queryDTO.Group != "" {
		query["group"] = queryDTO.Group
	}

	// 调用仓库层获取UI配置列表
	configs, total, err := s.uiConfigRepo.ListUIConfigs(ctx, query, queryDTO.Page, queryDTO.PageSize)
	if err != nil {
		logs.Error("[ListUIConfigs] 获取UI配置列表失败: %v", err)
		return nil, 0, err
	}

	return configs, total, nil
}

// GetUIConfig 获取UI配置详情
func (s *AdminUIConfigServiceImpl) GetUIConfig(ctx context.Context, id int) (*dto.AdminUIConfigDTO, error) {
	// 调用仓库层获取UI配置详情
	config, err := s.uiConfigRepo.GetUIConfig(ctx, id)
	if err != nil {
		logs.Error("[GetUIConfig] 获取UI配置详情失败: %v", err)
		return nil, err
	}

	if config == nil {
		return nil, nil
	}

	// 获取网格布局项目
	query := map[string]interface{}{"ui_config_id": id}
	gridItems, _, err := s.gridInfoRepo.ListGridInfos(ctx, query, 1, 1000) // 假设一个配置下不会有超过1000个网格项
	if err != nil {
		logs.Error("[GetUIConfig] 获取网格布局项目失败: %v", err)
	} else {
		config.GridItems = gridItems
	}

	return config, nil
}

// CreateUIConfig 创建UI配置
func (s *AdminUIConfigServiceImpl) CreateUIConfig(ctx context.Context, createDTO *dto.AdminUIConfigCreateDTO) (int, error) {
	// 调用仓库层创建UI配置
	id, err := s.uiConfigRepo.CreateUIConfig(ctx, createDTO)
	if err != nil {
		logs.Error("[CreateUIConfig] 创建UI配置失败: %v", err)
		return 0, err
	}

	return id, nil
}

// UpdateUIConfig 更新UI配置
func (s *AdminUIConfigServiceImpl) UpdateUIConfig(ctx context.Context, id int, updateDTO *dto.AdminUIConfigUpdateDTO) error {
	// 调用仓库层更新UI配置
	err := s.uiConfigRepo.UpdateUIConfig(ctx, id, updateDTO)
	if err != nil {
		logs.Error("[UpdateUIConfig] 更新UI配置失败: %v", err)
		return err
	}

	return nil
}

// DeleteUIConfig 删除UI配置
func (s *AdminUIConfigServiceImpl) DeleteUIConfig(ctx context.Context, id int) error {
	// 调用仓库层删除UI配置
	err := s.uiConfigRepo.DeleteUIConfig(ctx, id)
	if err != nil {
		logs.Error("[DeleteUIConfig] 删除UI配置失败: %v", err)
		return err
	}

	return nil
}

// UpdateUIConfigStatus 更新UI配置状态
func (s *AdminUIConfigServiceImpl) UpdateUIConfigStatus(ctx context.Context, id int, status int) error {
	// 调用仓库层更新UI配置状态
	err := s.uiConfigRepo.UpdateUIConfigStatus(ctx, id, status)
	if err != nil {
		logs.Error("[UpdateUIConfigStatus] 更新UI配置状态失败: %v", err)
		return err
	}

	return nil
}
