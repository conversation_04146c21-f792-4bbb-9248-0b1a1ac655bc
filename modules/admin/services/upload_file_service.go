/**
 * 文件上传服务实现
 *
 * 该文件实现了文件上传服务接口，提供文件上传、查询、删除等功能。
 * 支持匿名上传和普通上传两种模式，根据配置使用不同的存储方式。
 */

package services

import (
	"context"
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/beego/beego/v2/core/logs"

	adminDto "o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/admin/models"
	"o_mall_backend/modules/admin/repositories"
	systemServices "o_mall_backend/modules/system/services"
	systemImpl "o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils"
	"o_mall_backend/utils/config"
	"o_mall_backend/utils/storage"
)

// UploadFileService 文件上传服务接口
type UploadFileService interface {
	// Upload 上传文件
	Upload(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader, req *adminDto.UploadFileRequest, token string) (*adminDto.UploadFileResponse, error)
	// GetByID 通过ID获取文件
	GetByID(ctx context.Context, id int64) (*adminDto.UploadFileResponse, error)
	// List 获取文件列表
	List(ctx context.Context, req *adminDto.UploadFileQueryRequest) ([]*adminDto.UploadFileResponse, int64, error)
	// Delete 删除文件
	Delete(ctx context.Context, id int64) error
	// GetUploadConfig 获取上传配置
	GetUploadConfig(ctx context.Context) (*adminDto.UploadConfigResponse, error)
}

// uploadFileServiceImpl 文件上传服务实现
type uploadFileServiceImpl struct {
	uploadFileRepo         repositories.UploadFileRepository
	fileUsageConfigService systemServices.FileUsageConfigService
}

// NewUploadFileService 创建文件上传服务实例
func NewUploadFileService() UploadFileService {
	return &uploadFileServiceImpl{
		uploadFileRepo:         repositories.NewUploadFileRepository(),
		fileUsageConfigService: systemImpl.NewFileUsageConfigServiceImpl(),
	}
}

// Upload 上传文件
func (s *uploadFileServiceImpl) Upload(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader, req *adminDto.UploadFileRequest, token string) (*adminDto.UploadFileResponse, error) {
	// 添加调试日志
	logs.Info("Admin Upload方法 - 文件用途: %s, 匿名: %v, Token: %s", req.FileUsage, req.IsAnonymous, token != "")
	
	// 获取上传配置
	allowAnonymous, maxFileSize, allowedFileTypes, _ := config.GetUploadConfig()

	// 检查是否允许匿名上传
	if req.IsAnonymous {
		// 匿名上传受总开关控制
		if !allowAnonymous {
			return nil, fmt.Errorf("不允许匿名上传")
		}
		// 匿名上传还受用途类型限制
		if !config.IsAnonymousUsageAllowed(req.FileUsage) {
			return nil, fmt.Errorf("当前用途(%s)不允许匿名上传", req.FileUsage)
		}
	}

	// 获取文件信息
	fileSize := fileHeader.Size
	fileName := fileHeader.Filename
	fileExt := strings.ToLower(filepath.Ext(fileName))
	if fileExt == "" {
		return nil, fmt.Errorf("无法识别文件扩展名")
	}

	// 检查文件大小
	if fileSize > maxFileSize {
		return nil, fmt.Errorf("文件大小超过限制: %dMB", maxFileSize/1024/1024)
	}

	// 检查文件类型
	contentType := fileHeader.Header.Get("Content-Type")
	isTypeAllowed := false
	for _, allowedType := range allowedFileTypes {
		if contentType == allowedType || allowedType == "*" {
			isTypeAllowed = true
			break
		}
	}
	if !isTypeAllowed {
		return nil, fmt.Errorf("不支持的文件类型: %s", contentType)
	}

	// 获取当前用户信息
	var userID int64 = 0
	var username string = "匿名用户"
	var userType string = models.UploadUserTypeAnonymous

	// 匿名上传时跳过token验证
	if req.IsAnonymous {
		logs.Info("Admin Upload - 匿名上传，跳过token验证")
	} else if token != "" {
		// 非匿名上传，需要从token解析用户信息
		claims, err := utils.ParseToken(token)
		if err != nil {
			return nil, fmt.Errorf("无效的认证令牌: %v", err)
		}

		userID = claims.UserID
		username = claims.Username
		if claims.Role == "admin" {
			userType = models.UploadUserTypeAdmin
		} else if claims.Role == "user" {
			userType = models.UploadUserTypeUser
		} else if claims.Role == "merchant" {
			userType = models.UploadUserTypeMerchant
		}
	} else {
		// 非匿名上传但没有token，返回错误
		return nil, fmt.Errorf("非匿名上传需要提供有效的认证令牌")
	}

	// 根据用途确定存储目录
	fileDir := req.FileUsage
	if fileDir == "" {
		fileDir = models.FileUsageOther
	}

	// 获取存储模式
	storageMode, _ := config.GetStorageConfig()

	// 保存文件到存储系统
	logs.Info("开始保存文件到存储系统: fileName=%s, storageDir=%s", fileName, fileDir)
	filePath, err := storage.SaveFile(file, fileExt, fileDir)
	if err != nil {
		logs.Error("保存文件失败: fileName=%s, storageDir=%s, error=%v", fileName, fileDir, err)
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}
	logs.Info("文件保存成功: filePath=%s", filePath)

	// 获取文件访问URL
	fileURL := storage.GetFileURL(filePath)
	if fileURL == "" {
		logs.Error("获取文件URL失败: filePath=%s", filePath)
		return nil, fmt.Errorf("获取文件URL失败")
	}
	logs.Info("文件URL获取成功: fileURL=%s", fileURL)

	// 创建上传记录
	uploadFile := &models.UploadFile{
		FileName:    fileName,
		FilePath:    filePath,
		FileURL:     fileURL,
		FileSize:    fileSize,
		FileType:    contentType,
		FileExt:     fileExt,
		FileUsage:   req.FileUsage,
		Storage:     storageMode,
		UploadIP:    utils.GetClientIP(ctx),
		UserID:      userID,
		UserType:    userType,
		Username:    username,
		IsAnonymous: req.IsAnonymous,
		Status:      1, // 有效状态
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 保存到数据库
	id, err := s.uploadFileRepo.Create(ctx, uploadFile)
	if err != nil {
		// 如果数据库保存失败，应该删除已上传的文件
		_ = storage.DeleteFile(filePath)
		return nil, fmt.Errorf("创建上传记录失败: %v", err)
	}

	uploadFile.ID = id

	// 转换为响应对象
	return convertToUploadFileResponse(uploadFile), nil
}

// GetByID 通过ID获取文件
func (s *uploadFileServiceImpl) GetByID(ctx context.Context, id int64) (*adminDto.UploadFileResponse, error) {
	uploadFile, err := s.uploadFileRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取文件失败: %v", err)
	}

	if uploadFile == nil {
		return nil, fmt.Errorf("文件不存在")
	}

	return convertToUploadFileResponse(uploadFile), nil
}

// List 获取文件列表
func (s *uploadFileServiceImpl) List(ctx context.Context, req *adminDto.UploadFileQueryRequest) ([]*adminDto.UploadFileResponse, int64, error) {
	// 构建查询参数
	query := make(map[string]interface{})
	if req.FileUsage != "" {
		query["file_usage"] = req.FileUsage
	}
	if req.UserType != "" {
		query["user_type"] = req.UserType
	}
	if req.UserID > 0 {
		query["user_id"] = req.UserID
	}
	if req.Username != "" {
		query["username"] = req.Username
	}
	if req.IsAnonymous != nil {
		query["is_anonymous"] = req.IsAnonymous
	}
	if !req.StartTime.IsZero() {
		query["start_time"] = req.StartTime
	}
	if !req.EndTime.IsZero() {
		query["end_time"] = req.EndTime
	}

	// 设置默认分页参数
	page := req.Page
	if page < 1 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize < 1 {
		pageSize = 10
	}

	// 调用仓库查询
	uploadFiles, total, err := s.uploadFileRepo.List(ctx, query, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询文件列表失败: %v", err)
	}

	// 转换为响应对象
	responses := make([]*adminDto.UploadFileResponse, 0, len(uploadFiles))
	for _, uploadFile := range uploadFiles {
		responses = append(responses, convertToUploadFileResponse(uploadFile))
	}

	return responses, total, nil
}

// Delete 删除文件
func (s *uploadFileServiceImpl) Delete(ctx context.Context, id int64) error {
	// 先获取文件信息
	uploadFile, err := s.uploadFileRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}

	if uploadFile == nil {
		return fmt.Errorf("文件不存在")
	}

	// 删除物理文件
	if err := storage.DeleteFile(uploadFile.FilePath); err != nil {
		logs.Warn("删除物理文件失败: %v, 仍将继续删除数据库记录", err)
	}

	// 删除数据库记录(软删除)
	if err := s.uploadFileRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("删除文件记录失败: %v", err)
	}

	return nil
}

// GetUploadConfig 获取上传配置
func (s *uploadFileServiceImpl) GetUploadConfig(ctx context.Context) (*adminDto.UploadConfigResponse, error) {
	allowAnonymous, maxFileSize, allowedFileTypes, anonymousUsageTypes := config.GetUploadConfig()
	storageMode, _ := config.GetStorageConfig()
	uploadPath, _ := utils.GetUploadPath()

	// 从文件用途配置服务获取允许的用途类型
	allowedUsageTypes, err := s.fileUsageConfigService.GetAllowedUsageTypes(ctx)
	if err != nil {
		logs.Error("[GetUploadConfig] 获取允许的用途类型失败: %v", err)
		// 如果获取失败，使用默认的硬编码类型作为备用
		allowedUsageTypes = []string{
			models.FileUsageAvatar,
			models.FileUsageProduct,
			models.FileUsageMerchant,
			models.FileUsageComplaint,
			models.FileUsageOther,
		}
	}

	// 从文件用途配置服务获取允许匿名上传的用途类型
	anonymousUsageTypesFromConfig, err := s.fileUsageConfigService.GetAnonymousUsageTypes(ctx)
	if err != nil {
		logs.Error("[GetUploadConfig] 获取匿名用途类型失败: %v", err)
		// 如果获取失败，使用原有的配置
	} else {
		// 合并配置文件中的匿名用途类型和数据库中的配置
		anonymousUsageTypesMap := make(map[string]bool)
		// 添加配置文件中的类型
		for _, usage := range anonymousUsageTypes {
			anonymousUsageTypesMap[usage] = true
		}
		// 添加数据库配置中的类型
		for _, usage := range anonymousUsageTypesFromConfig {
			anonymousUsageTypesMap[usage] = true
		}
		// 转换回切片
		anonymousUsageTypes = make([]string, 0, len(anonymousUsageTypesMap))
		for usage := range anonymousUsageTypesMap {
			anonymousUsageTypes = append(anonymousUsageTypes, usage)
		}
	}

	config := &adminDto.UploadConfigResponse{
		AllowAnonymous:      allowAnonymous,
		MaxFileSize:         maxFileSize,
		AllowedFileTypes:    allowedFileTypes,
		StorageMode:         storageMode,
		UploadPath:          uploadPath,
		AllowedUsageTypes:   allowedUsageTypes,
		AnonymousUsageTypes: anonymousUsageTypes,
	}

	return config, nil
}

// convertToUploadFileResponse 将上传文件模型转换为响应对象
func convertToUploadFileResponse(uploadFile *models.UploadFile) *adminDto.UploadFileResponse {
	if uploadFile == nil {
		return nil
	}

	return &adminDto.UploadFileResponse{
		ID:          uploadFile.ID,
		FileName:    uploadFile.FileName,
		FilePath:    uploadFile.FilePath,
		FileURL:     uploadFile.FileURL,
		FileSize:    uploadFile.FileSize,
		FileType:    uploadFile.FileType,
		FileExt:     uploadFile.FileExt,
		FileUsage:   uploadFile.FileUsage,
		IsAnonymous: uploadFile.IsAnonymous,
		Storage:     uploadFile.Storage,
		CreatedAt:   uploadFile.CreatedAt,
	}
}
