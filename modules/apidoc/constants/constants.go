/**
 * API文档常量定义
 *
 * 该文件定义了API文档模块使用的常量值
 * 包括API状态、DTO类型、缓存键等
 */

package constants

// API状态常量
const (
	// APIStatusDisabled 禁用状态
	APIStatusDisabled = 0
	// APIStatusEnabled 启用状态
	APIStatusEnabled = 1
)

// DTO类型常量
const (
	// DTOTypeRequest 请求类型DTO
	DTOTypeRequest = "request"
	// DTOTypeResponse 响应类型DTO
	DTOTypeResponse = "response"
	// DTOTypeCommon 通用类型DTO
	DTOTypeCommon = "common"
)

// HTTP方法常量
const (
	// HTTPMethodGET GET方法
	HTTPMethodGET = "GET"
	// HTTPMethodPOST POST方法
	HTTPMethodPOST = "POST"
	// HTTPMethodPUT PUT方法
	HTTPMethodPUT = "PUT"
	// HTTPMethodDELETE DELETE方法
	HTTPMethodDELETE = "DELETE"
	// HTTPMethodPATCH PATCH方法
	HTTPMethodPATCH = "PATCH"
	// HTTPMethodHEAD HEAD方法
	HTTPMethodHEAD = "HEAD"
	// HTTPMethodOPTIONS OPTIONS方法
	HTTPMethodOPTIONS = "OPTIONS"
)

// 数据类型常量
const (
	// DataTypeString 字符串类型
	DataTypeString = "string"
	// DataTypeInt 整型
	DataTypeInt = "int"
	// DataTypeFloat 浮点型
	DataTypeFloat = "float"
	// DataTypeBool 布尔型
	DataTypeBool = "bool"
	// DataTypeObject 对象类型
	DataTypeObject = "object"
	// DataTypeArray 数组类型
	DataTypeArray = "array"
	// DataTypeDateTime 日期时间类型
	DataTypeDateTime = "datetime"
	// DataTypeDate 日期类型
	DataTypeDate = "date"
)
