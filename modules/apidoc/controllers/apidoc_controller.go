/**
 * API文档控制器
 *
 * 该文件实现了API文档模块的控制器，处理API文档相关的HTTP请求
 * 包括API信息和DTO信息的查询、创建、更新等操作
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/apidoc/dto"
	"o_mall_backend/modules/apidoc/services"
	"strconv"
)

// ApiDocController API文档控制器
type ApiDocController struct {
	web.Controller
	apiService services.ApiService
}

// apiDocControllerInstance 存储唯一的控制器实例
var apiDocControllerInstance *ApiDocController

// NewApiDocController 创建控制器实例
func NewApiDocController(apiService services.ApiService) *ApiDocController {
	controller := &ApiDocController{
		apiService: apiService,
	}
	// 保存实例
	apiDocControllerInstance = controller
	return controller
}

// GetApiDocController 获取控制器实例
func GetApiDocController() (*ApiDocController, bool) {
	return apiDocControllerInstance, apiDocControllerInstance != nil
}

// Prepare is called prior to the actual controller method execution
func (c *ApiDocController) Prepare() {
	// If this controller instance doesn't have apiService initialized
	// get it from the singleton instance
	if c.apiService == nil {
		if instance, ok := GetApiDocController(); ok {
			c.apiService = instance.apiService
		} else {
			logs.Error("ApiDocController instance not initialized properly.")
		}
	}
}

// CreateAPI 创建API信息
// @Title 创建API信息
// @Description 创建新的API信息
// @Param   body  body  dto.CreateAPIRequest  true  "API信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /apis [post]
func (c *ApiDocController) CreateAPI() {
	var req dto.CreateAPIRequest
	err := c.ParseForm(&req)
	if err != nil {
		logs.Error("[CreateAPI] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	id, err := c.apiService.CreateAPI(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[CreateAPI] 创建API信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]int64{"id": id})
}

// GetAPI 获取API信息
// @Title 获取API信息
// @Description 获取指定ID的API信息
// @Param   id  path  int64  true  "API ID"
// @Success 200 {object} result.Result{data=dto.APIResponse}
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /apis/:id [get]
func (c *ApiDocController) GetAPI() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		logs.Error("[GetAPI] 无效的API ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	api, err := c.apiService.GetAPIByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[GetAPI] 获取API信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if api == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, api)
}

// GetAPIWithDTO 获取API信息及其关联的DTO详情
// @Title 获取API及DTO信息
// @Description 获取指定ID的API信息及其关联的DTO详情
// @Param   id  path  int64  true  "API ID"
// @Success 200 {object} result.Result{data=dto.APIWithDTOResponse}
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /apis/:id/dto [get]
func (c *ApiDocController) GetAPIWithDTO() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		logs.Error("[GetAPIWithDTO] 无效的API ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	apiWithDTO, err := c.apiService.GetAPIWithDTO(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[GetAPIWithDTO] 获取API及DTO信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if apiWithDTO == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, apiWithDTO)
}

// UpdateAPI 更新API信息
// @Title 更新API信息
// @Description 更新指定ID的API信息
// @Param   id   path  int64  true  "API ID"
// @Param   body  body  dto.UpdateAPIRequest  true  "API信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /apis/:id [put]
func (c *ApiDocController) UpdateAPI() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		logs.Error("[UpdateAPI] 无效的API ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	var req dto.UpdateAPIRequest
	err = c.ParseForm(&req)
	if err != nil {
		logs.Error("[UpdateAPI] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}
	req.ID = id

	err = c.apiService.UpdateAPI(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[UpdateAPI] 更新API信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeleteAPI 删除API信息
// @Title 删除API信息
// @Description 删除指定ID的API信息
// @Param   id  path  int64  true  "API ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /apis/:id [delete]
func (c *ApiDocController) DeleteAPI() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		logs.Error("[DeleteAPI] 无效的API ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	err = c.apiService.DeleteAPI(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[DeleteAPI] 删除API信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// ListAPIs 获取API列表
// @Title 获取API列表
// @Description 获取API列表，支持分页和条件查询
// @Param   page  query  int  false  "页码"
// @Param   pageSize  query  int  false  "每页数量"
// @Param   module  query  string  false  "模块名称"
// @Param   path  query  string  false  "API路径"
// @Param   method  query  string  false  "HTTP方法"
// @Param   name  query  string  false  "API名称"
// @Param   require_auth  query  bool  false  "是否需要认证"
// @Success 200 {object} result.Result{data=dto.APIResponse}
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /apis [get]
func (c *ApiDocController) ListAPIs() {
	var req dto.APIQueryRequest
	if err := c.ParseForm(&req); err != nil {
		logs.Error("[ListAPIs] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	logs.Info("[ListAPIs] 请求参数: %v, %v, %v", req.Page, req.PageSize, req.Module)
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 通过service层获取原始数据
	apis, total, err := c.apiService.ListAPIs(
		c.Ctx.Request.Context(),
		&req,
	)
	if err != nil {
		logs.Error("[ListAPIs] 获取API列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 在控制器层进行数据格式化
	apiResponses := make([]*dto.APIResponse, 0, len(apis))
	for _, api := range apis {
		apiResponses = append(apiResponses, &dto.APIResponse{
			ID:             api.ID,
			Module:         api.Module,
			Path:           api.Path,
			Method:         api.Method,
			Name:           api.Name,
			Description:    api.Description,
			RequireAuth:    api.RequireAuth,
			Permissions:    api.Permissions,
			RequestDTO:     api.RequestDTO,
			ResponseDTO:    api.ResponseDTO,
			ControllerName: api.ControllerName,
			ActName:        api.ActName,
			Status:         api.Status,
			CreatedAt:      api.CreatedAt,
			UpdatedAt:      api.UpdatedAt,
		})
	}

	result.OKWithPagination(c.Ctx, apiResponses, total, req.Page, req.PageSize)
}

// GetAPIsByModule 获取指定模块的所有API
// @Title 获取模块API
// @Description 获取指定模块的所有API
// @Param   module  path  string  true  "模块名称"
// @Success 200 {object} result.Result{data=[]dto.APIResponse}
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /modules/:module/apis [get]
func (c *ApiDocController) GetAPIsByModule() {
	module := c.Ctx.Input.Param(":module")
	if module == "" {
		logs.Error("[GetAPIsByModule] 模块名称不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	apis, err := c.apiService.GetAPIsByModule(c.Ctx.Request.Context(), module)
	if err != nil {
		logs.Error("[GetAPIsByModule] 获取模块API列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, apis)
}

// CreateDTO 创建DTO信息
// @Title 创建DTO信息
// @Description 创建新的DTO信息
// @Param   body  body  dto.CreateDTORequest  true  "DTO信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /dtos [post]
func (c *ApiDocController) CreateDTO() {
	var req dto.CreateDTORequest
	err := c.ParseForm(&req)
	if err != nil {
		logs.Error("[CreateDTO] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	id, err := c.apiService.CreateDTO(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[CreateDTO] 创建DTO信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]int64{"id": id})
}

// GetDTO 获取DTO信息
// @Title 获取DTO信息
// @Description 获取指定ID的DTO信息
// @Param   id  path  int64  true  "DTO ID"
// @Success 200 {object} result.Result{data=dto.DTOResponse}
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /dtos/:id [get]
func (c *ApiDocController) GetDTO() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		logs.Error("[GetDTO] 无效的DTO ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	dtoInfo, err := c.apiService.GetDTOByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[GetDTO] 获取DTO信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if dtoInfo == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, dtoInfo)
}

// GetDTOByName 根据名称获取DTO信息
// @Title 根据名称获取DTO
// @Description 根据名称获取DTO信息
// @Param   name  path  string  true  "DTO名称"
// @Success 200 {object} result.Result{data=dto.DTOResponse}
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /dtos/name/:name [get]
func (c *ApiDocController) GetDTOByName() {
	name := c.Ctx.Input.Param(":name")
	if name == "" {
		logs.Error("[GetDTOByName] DTO名称不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	dtoInfo, err := c.apiService.GetDTOByName(c.Ctx.Request.Context(), name)
	if err != nil {
		logs.Error("[GetDTOByName] 获取DTO信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if dtoInfo == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, dtoInfo)
}

// GetDTOByModuleAndName 根据模块和名称获取DTO信息
// @Title 根据模块和名称获取DTO
// @Description 根据模块和名称获取DTO信息
// @Param   module  path  string  true  "模块名称"
// @Param   name  path  string  true  "DTO名称"
// @Success 200 {object} result.Result{data=dto.DTOResponse}
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /dtos/module/:module/name/:name [get]
func (c *ApiDocController) GetDTOByModuleAndName() {
	module := c.Ctx.Input.Param(":module")
	name := c.Ctx.Input.Param(":name")

	if module == "" || name == "" {
		logs.Error("[GetDTOByModuleAndName] 模块名称或DTO名称不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	dtoInfo, err := c.apiService.GetDTOByModuleAndName(c.Ctx.Request.Context(), module, name)
	if err != nil {
		logs.Error("[GetDTOByModuleAndName] 获取DTO信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if dtoInfo == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, dtoInfo)
}

// UpdateDTO 更新DTO信息
// @Title 更新DTO信息
// @Description 更新指定ID的DTO信息
// @Param   id   path  int64  true  "DTO ID"
// @Param   body  body  dto.UpdateDTORequest  true  "DTO信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /dtos/:id [put]
func (c *ApiDocController) UpdateDTO() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		logs.Error("[UpdateDTO] 无效的DTO ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	var req dto.UpdateDTORequest
	err = c.ParseForm(&req)
	if err != nil {
		logs.Error("[UpdateDTO] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}
	req.ID = id

	err = c.apiService.UpdateDTO(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[UpdateDTO] 更新DTO信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeleteDTO 删除DTO信息
// @Title 删除DTO信息
// @Description 删除指定ID的DTO信息
// @Param   id  path  int64  true  "DTO ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /dtos/:id [delete]
func (c *ApiDocController) DeleteDTO() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		logs.Error("[DeleteDTO] 无效的DTO ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	err = c.apiService.DeleteDTO(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[DeleteDTO] 删除DTO信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// ListDTOs 获取DTO列表
// @Title 获取DTO列表
// @Description 获取DTO列表，支持分页和条件查询
// @Param   page  query  int  false  "页码"
// @Param   pageSize  query  int  false  "每页数量"
// @Param   module  query  string  false  "模块名称"
// @Param   name  query  string  false  "DTO名称"
// @Param   type  query  string  false  "DTO类型"
// @Success 200 {object} result.Result{data=dto.DTOResponse}
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /dtos [get]
func (c *ApiDocController) ListDTOs() {
	var req dto.DTOQueryRequest
	if err := c.ParseForm(&req); err != nil {
		logs.Error("[ListDTOs] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	dtos, total, err := c.apiService.ListDTOs(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[ListDTOs] 获取DTO列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OKWithPagination(c.Ctx, dtos, total, req.Page, req.PageSize)
}

// GetDTOsByModule 获取指定模块的所有DTO
// @Title 获取模块DTO
// @Description 获取指定模块的所有DTO
// @Param   module  path  string  true  "模块名称"
// @Success 200 {object} result.Result{data=[]dto.DTOResponse}
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /modules/:module/dtos [get]
func (c *ApiDocController) GetDTOsByModule() {
	module := c.Ctx.Input.Param(":module")
	if module == "" {
		logs.Error("[GetDTOsByModule] 模块名称不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	dtos, err := c.apiService.GetDTOsByModule(c.Ctx.Request.Context(), module)
	if err != nil {
		logs.Error("[GetDTOsByModule] 获取模块DTO列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, dtos)
}

// GetAllModules 获取所有模块信息
// @Title 获取所有模块
// @Description 获取所有模块信息
// @Success 200 {object} result.Result{data=[]dto.ModuleListResponse}
// @Failure 500 {object} result.Result
// @router /modules [get]
func (c *ApiDocController) GetAllModules() {
	modules, err := c.apiService.GetAllModules(c.Ctx.Request.Context())
	if err != nil {
		logs.Error("[GetAllModules] 获取所有模块信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, modules)
}

// ClearCache 清除缓存
// @Title 清除缓存
// @Description 清除缓存
// @Param   module  query  string  false  "模块名称，不指定则清除所有"
// @Success 200 {object} result.Result
// @Failure 500 {object} result.Result
// @router /cache/clear [post]
func (c *ApiDocController) ClearCache() {
	var req dto.ClearCacheRequest
	if err := c.ParseForm(&req); err != nil {
		logs.Error("[ClearCache] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	err := c.apiService.ClearCache(c.Ctx.Request.Context(), req.Module)
	if err != nil {
		logs.Error("[ClearCache] 清除缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// GetAPIsByPageName 根据页面名称获取API列表
// @Title 根据页面名称获取API列表
// @Description 获取指定页面名称的API列表
// @Param   pagename  path  string  true  "页面名称"
// @Success 200 {object} result.Result{data=[]dto.APIResponse}
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /api-pages/:pagename [get]
func (c *ApiDocController) GetAPIsByPageName() {
	pageName := c.Ctx.Input.Param(":pagename")
	if pageName == "" {
		logs.Error("[GetAPIsByPageName] 页面名称为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	apis, err := c.apiService.GetAPIsByPageName(c.Ctx.Request.Context(), pageName)
	if err != nil {
		logs.Error("[GetAPIsByPageName] 获取API列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 将API信息转换为响应格式
	apiResponses := make([]*dto.APIResponse, 0, len(apis))
	for _, api := range apis {
		apiResponses = append(apiResponses, &dto.APIResponse{
			ID:             api.ID,
			Module:         api.Module,
			Path:           api.Path,
			Method:         api.Method,
			Name:           api.Name,
			Description:    api.Description,
			RequireAuth:    api.RequireAuth,
			Permissions:    api.Permissions,
			RequestDTO:     api.RequestDTO,
			ResponseDTO:    api.ResponseDTO,
			ControllerName: api.ControllerName,
			ActName:        api.ActName,
			PageName:       api.PageName,
			Status:         api.Status,
			CreatedAt:      api.CreatedAt,
			UpdatedAt:      api.UpdatedAt,
		})
	}

	result.OK(c.Ctx, apiResponses)
}

// GetPageNamesByModule 获取指定模块的所有页面名称及其API数量
func (c *ApiDocController) GetPageNamesByModule() {
	// 获取模块名
	module := c.Ctx.Input.Param(":module")
	if module == "" {
		module = c.GetString("module")
	}

	if module == "" {
		logs.Error("[GetPageNamesByModule] 模块名不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取页面名称列表
	response, err := c.apiService.GetPageNamesByModule(c.Ctx.Request.Context(), module)
	if err != nil {
		logs.Error("[GetPageNamesByModule] 获取页面名称列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回响应
	result.OK(c.Ctx, response)
}

// ListControllers 获取控制器列表
func (c *ApiDocController) ListControllers() {
	// 解析查询参数
	var req dto.ControllerQueryRequest
	req.Module = c.GetString("module")
	req.ControllerName = c.GetString("controller_name")
	req.MethodName = c.GetString("method_name")

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 20)
	req.Page = page
	req.PageSize = pageSize

	// 调用服务获取控制器列表
	controllers, total, err := c.apiService.ListControllers(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[ListControllers] 获取控制器列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回响应
	result.OKWithPagination(c.Ctx, controllers, total, page, pageSize)
}

// GetController 根据ID获取控制器信息
func (c *ApiDocController) GetController() {
	// 解析控制器ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[GetController] 解析控制器ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取控制器详情
	controllerInfo, err := c.apiService.GetController(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[GetController] 获取控制器信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if controllerInfo == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回响应
	result.OK(c.Ctx, controllerInfo)
}

// GetControllersByModule 获取指定模块的所有控制器
func (c *ApiDocController) GetControllersByModule() {
	// 解析模块名
	module := c.Ctx.Input.Param(":module")
	if module == "" {
		logs.Error("[GetControllersByModule] 模块名不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取模块下的控制器列表
	controllers, err := c.apiService.GetControllersByModule(c.Ctx.Request.Context(), module)
	if err != nil {
		logs.Error("[GetControllersByModule] 获取模块控制器列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回响应
	result.OK(c.Ctx, map[string]interface{}{
		"module":      module,
		"controllers": controllers,
		"total":       len(controllers),
	})
}
