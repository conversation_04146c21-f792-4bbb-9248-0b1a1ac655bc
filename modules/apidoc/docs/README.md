# API文档模块

## 模块概述

API文档模块是O_Mall系统中的核心辅助模块，用于自动收集、管理和展示系统中的所有API接口信息。该模块通过解析各功能模块的路由文件，自动提取API路径、方法、认证需求等信息，并提供统一的查询接口，帮助前端开发人员和测试人员了解后端API的详细信息。

### 主要功能

- **API信息管理**：收集和管理所有模块的API信息，包括路径、方法、认证需求等
- **DTO结构管理**：记录API使用的数据传输对象(DTO)结构，便于前端开发人员了解请求和响应的数据格式
- **模块化展示**：按模块分类展示API，方便查找
- **自动同步**：支持从路由文件自动同步API信息，保持文档的实时更新
- **缓存机制**：使用Redis缓存API和DTO信息，提高查询效率

## 架构设计

API文档模块采用标准的MVC架构，目录结构如下：

```
modules/apidoc/
├── constants/     # 常量定义
├── controllers/   # 控制器层，处理HTTP请求
├── docs/          # 模块文档
├── dto/           # 数据传输对象
├── models/        # 数据模型
├── repositories/  # 数据访问层
├── routers/       # 路由定义
├── services/      # 业务服务层
└── init.go        # 模块初始化
```

### 数据模型

- **ApiInfo**：API信息模型，记录API的路径、方法、认证需求等
- **DTOInfo**：DTO信息模型，记录数据传输对象的结构定义

## API接口说明

### 模块接口

| 接口路径 | 方法 | 描述 |
|---------|------|------|
| `/api/v1/apidoc/modules` | GET | 获取所有模块列表 |
| `/api/v1/apidoc/modules/:module/apis` | GET | 获取指定模块的API列表 |
| `/api/v1/apidoc/modules/:module/dtos` | GET | 获取指定模块的DTO列表 |

### API管理接口

| 接口路径 | 方法 | 描述 |
|---------|------|------|
| `/api/v1/apidoc/apis` | GET | 获取API列表（支持分页和筛选） |
| `/api/v1/apidoc/apis` | POST | 创建新的API信息 |
| `/api/v1/apidoc/apis/:id` | GET | 获取API详情 |
| `/api/v1/apidoc/apis/:id` | PUT | 更新API信息 |
| `/api/v1/apidoc/apis/:id` | DELETE | 删除API信息 |
| `/api/v1/apidoc/apis/:id/dto` | GET | 获取API及其关联DTO详情 |

### DTO管理接口

| 接口路径 | 方法 | 描述 |
|---------|------|------|
| `/api/v1/apidoc/dtos` | GET | 获取DTO列表（支持分页和筛选） |
| `/api/v1/apidoc/dtos` | POST | 创建新的DTO信息 |
| `/api/v1/apidoc/dtos/:id` | GET | 获取DTO详情 |
| `/api/v1/apidoc/dtos/:id` | PUT | 更新DTO信息 |
| `/api/v1/apidoc/dtos/:id` | DELETE | 删除DTO信息 |
| `/api/v1/apidoc/dtos/name/:name` | GET | 根据DTO名称获取结构详情 |

### 缓存管理接口

| 接口路径 | 方法 | 描述 |
|---------|------|------|
| `/api/v1/apidoc/cache/clear` | POST | 清除API文档缓存 |

## 使用方法

### 查看API文档

1. **查看所有模块**
   ```
   GET /api/v1/apidoc/modules
   ```
   返回系统中所有模块的列表，及其API和DTO数量统计。

2. **查看指定模块的API**
   ```
   GET /api/v1/apidoc/modules/admin/apis
   ```
   返回admin模块的所有API信息。

3. **查看API详情**
   ```
   GET /api/v1/apidoc/apis/123
   ```
   返回ID为123的API详细信息。

4. **查看API及其关联DTO**
   ```
   GET /api/v1/apidoc/apis/123/dto
   ```
   返回ID为123的API信息，以及其请求和响应DTO的详细结构。

### 自动同步API信息

API文档模块支持从路由文件自动同步API信息。当后端开发人员添加新的API或修改现有API时，可以通过以下方式更新API文档：

1. **同步指定模块的API信息**
   ```
   POST /api/v1/apidoc/modules/admin/sync?force_update=true
   ```
   从admin模块的路由文件中解析API信息并更新到文档库。参数`force_update`指定是否强制更新已存在的API信息。

2. **同步所有模块的API信息**
   ```
   POST /api/v1/apidoc/modules/sync?force_update=true
   ```
   同步所有模块的API信息。

### 手动管理API信息

除了自动同步外，还可以手动管理API信息：

1. **创建API信息**
   ```
   POST /api/v1/apidoc/apis
   ```
   请求体：
   ```json
   {
     "module": "admin",
     "path": "/api/v1/admin/login",
     "method": "POST",
     "name": "管理员登录",
     "description": "处理管理员登录请求",
     "require_auth": false,
     "request_dto": "AdminLoginRequest",
     "response_dto": "AdminLoginResponse"
   }
   ```

2. **更新API信息**
   ```
   PUT /api/v1/apidoc/apis/123
   ```
   请求体同创建API。

3. **删除API信息**
   ```
   DELETE /api/v1/apidoc/apis/123
   ```

### 管理DTO信息

1. **创建DTO信息**
   ```
   POST /api/v1/apidoc/dtos
   ```
   请求体：
   ```json
   {
     "module": "admin",
     "name": "AdminLoginRequest",
     "description": "管理员登录请求DTO",
     "type": "request",
     "structure": "{\"username\":\"string\",\"password\":\"string\"}"
   }
   ```

2. **更新DTO信息**
   ```
   PUT /api/v1/apidoc/dtos/123
   ```

3. **删除DTO信息**
   ```
   DELETE /api/v1/apidoc/dtos/123
   ```

## 高级功能

### 路由解析

API文档模块可以自动从各功能模块的路由文件（`routers/router.go`）中解析API信息。解析过程中，会识别以下内容：

- 基础命名空间（如`/api/v1/admin`）
- 嵌套命名空间（如`/secured`）
- 路由定义（包括路径、控制器和方法）
- 认证需求（位于`/secured`命名空间下的API会被标记为需要认证）

解析示例：

```go
// 路由文件中的定义
adminNS := web.NewNamespace("/api/v1/admin",
    web.NSRouter("/login", &controllers.AdminController{}, "post:Login"),
    web.NSNamespace("/secured",
        web.NSRouter("/info", &controllers.AdminController{}, "get:GetAdminInfo"),
    ),
)
```

解析结果：
- API 1: `{Path: "/api/v1/admin/login", Method: "POST", RequireAuth: false}`
- API 2: `{Path: "/api/v1/admin/secured/info", Method: "GET", RequireAuth: true}`

### 缓存机制

为提高性能，API文档模块采用Redis缓存以下内容：

- 模块列表
- 模块的API列表
- 模块的DTO列表
- DTO结构详情

当API或DTO信息发生变化时，相关缓存会自动失效。也可以通过`/api/v1/apidoc/cache/clear`接口手动清除缓存。

## 常见问题

### 自动同步不工作

可能的原因：
1. 路由文件路径不正确，确保路由文件位于`modules/{module}/routers/router.go`
2. 路由定义格式不符合解析规则
3. 数据库连接问题

解决方法：
1. 检查路由文件位置和内容
2. 使用手动方式添加API信息
3. 检查日志，查看具体错误信息

### API信息不完整

自动解析只能获取基本的API信息（路径、方法、是否需要认证）。对于请求/响应DTO、详细描述等信息，需要手动补充。

### 缓存问题

如果发现API信息已更新但查询结果未变化，可能是缓存未及时更新。请调用缓存清除接口：
```
POST /api/v1/apidoc/cache/clear
```

## 未来计划

- 支持从控制器代码注释中提取API描述和DTO信息
- 支持API文档的导出（Swagger、Markdown等格式）
- 提供API测试功能
- 增加API版本管理功能 