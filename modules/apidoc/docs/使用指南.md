# API文档模块使用指南

## 简介

API文档模块提供了一套完整的API文档管理系统，可以帮助前端开发人员和测试人员了解后端API的详细信息。本指南将介绍如何使用API文档模块来查看、管理和同步API信息。

## 快速开始

### 1. 查看所有模块

访问以下接口获取系统中所有模块的列表：

```
GET /api/v1/apidoc/modules
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "name": "admin",
      "api_count": 25,
      "dto_count": 15
    },
    {
      "name": "user",
      "api_count": 18,
      "dto_count": 12
    }
  ]
}
```

### 2. 查看指定模块的API

访问以下接口获取指定模块的所有API信息：

```
GET /api/v1/apidoc/modules/admin/apis
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "module": "admin",
      "path": "/api/v1/admin/login",
      "method": "POST",
      "name": "Login",
      "description": "POST /api/v1/admin/login",
      "require_auth": false,
      "permissions": "",
      "request_dto": "AdminLoginRequest",
      "response_dto": "AdminLoginResponse",
      "status": 1,
      "created_at": "2023-03-10T15:30:00Z",
      "updated_at": "2023-03-10T15:30:00Z"
    },
    {
      "id": 2,
      "module": "admin",
      "path": "/api/v1/admin/secured/info",
      "method": "GET",
      "name": "GetAdminInfo",
      "description": "GET /api/v1/admin/secured/info",
      "require_auth": true,
      "permissions": "",
      "request_dto": "",
      "response_dto": "AdminInfoResponse",
      "status": 1,
      "created_at": "2023-03-10T15:30:00Z",
      "updated_at": "2023-03-10T15:30:00Z"
    }
  ]
}
```

### 3. 查看API详情

访问以下接口获取指定API的详细信息：

```
GET /api/v1/apidoc/apis/1
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "module": "admin",
    "path": "/api/v1/admin/login",
    "method": "POST",
    "name": "Login",
    "description": "POST /api/v1/admin/login",
    "require_auth": false,
    "permissions": "",
    "request_dto": "AdminLoginRequest",
    "response_dto": "AdminLoginResponse",
    "status": 1,
    "created_at": "2023-03-10T15:30:00Z",
    "updated_at": "2023-03-10T15:30:00Z"
  }
}
```

### 4. 查看API及其关联DTO

访问以下接口获取API信息及其请求/响应DTO结构：

```
GET /api/v1/apidoc/apis/1/dto
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "api": {
      "id": 1,
      "module": "admin",
      "path": "/api/v1/admin/login",
      "method": "POST",
      "name": "Login",
      "description": "POST /api/v1/admin/login",
      "require_auth": false,
      "permissions": "",
      "request_dto": "AdminLoginRequest",
      "response_dto": "AdminLoginResponse",
      "status": 1,
      "created_at": "2023-03-10T15:30:00Z",
      "updated_at": "2023-03-10T15:30:00Z"
    },
    "request_dto": {
      "id": 1,
      "module": "admin",
      "name": "AdminLoginRequest",
      "description": "管理员登录请求DTO",
      "type": "request",
      "structure": {
        "username": "string",
        "password": "string"
      },
      "created_at": "2023-03-10T15:30:00Z",
      "updated_at": "2023-03-10T15:30:00Z"
    },
    "response_dto": {
      "id": 2,
      "module": "admin",
      "name": "AdminLoginResponse",
      "description": "管理员登录响应DTO",
      "type": "response",
      "structure": {
        "token": "string",
        "refresh_token": "string",
        "admin_id": "int64",
        "username": "string",
        "expires_in": "int"
      },
      "created_at": "2023-03-10T15:30:00Z",
      "updated_at": "2023-03-10T15:30:00Z"
    }
  }
}
```

## 管理API信息

### 1. 自动同步API信息

当您添加或修改了API接口后，可以通过自动同步功能更新API文档：

```
POST /api/v1/apidoc/modules/admin/sync?force_update=true
```

参数说明：
- `force_update`：是否强制更新已存在的API信息，默认为false

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 2. 手动创建API信息

如果自动同步无法满足需求，您也可以手动创建API信息：

```
POST /api/v1/apidoc/apis
```

请求体示例：

```json
{
  "module": "admin",
  "path": "/api/v1/admin/special-endpoint",
  "method": "POST",
  "name": "SpecialEndpoint",
  "description": "特殊端点，用于处理特定逻辑",
  "require_auth": true,
  "permissions": "admin:special",
  "request_dto": "SpecialRequest",
  "response_dto": "SpecialResponse"
}
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 100
  }
}
```

### 3. 更新API信息

当API信息需要修改时，可以使用以下接口：

```
PUT /api/v1/apidoc/apis/100
```

请求体示例：

```json
{
  "name": "UpdatedSpecialEndpoint",
  "description": "更新后的特殊端点描述",
  "require_auth": true,
  "permissions": "admin:special,admin:write",
  "request_dto": "SpecialRequest",
  "response_dto": "UpdatedSpecialResponse",
  "status": 1
}
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 4. 删除API信息

当API被移除时，可以删除其文档：

```
DELETE /api/v1/apidoc/apis/100
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 管理DTO信息

### 1. 创建DTO信息

```
POST /api/v1/apidoc/dtos
```

请求体示例：

```json
{
  "module": "admin",
  "name": "SpecialRequest",
  "description": "特殊请求DTO",
  "type": "request",
  "structure": "{\"special_id\":\"int64\",\"special_name\":\"string\",\"options\":{\"option1\":\"boolean\",\"option2\":\"string\"}}"
}
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 50
  }
}
```

### 2. 更新DTO信息

```
PUT /api/v1/apidoc/dtos/50
```

请求体示例：

```json
{
  "description": "更新后的特殊请求DTO",
  "structure": "{\"special_id\":\"int64\",\"special_name\":\"string\",\"special_type\":\"int\",\"options\":{\"option1\":\"boolean\",\"option2\":\"string\"}}"
}
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 3. 删除DTO信息

```
DELETE /api/v1/apidoc/dtos/50
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 缓存管理

为了提高性能，API文档模块使用Redis缓存API和DTO信息。当您通过接口修改了API或DTO信息后，相关缓存会自动失效。

如果需要手动清除所有缓存，可以使用以下接口：

```
POST /api/v1/apidoc/cache/clear
```

如果只需要清除指定模块的缓存，可以使用：

```
POST /api/v1/apidoc/cache/clear?module=admin
```

响应示例：

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 最佳实践

1. **定期同步API信息**
   
   建议在每次API变更后，通过自动同步功能更新API文档：
   ```
   POST /api/v1/apidoc/modules/admin/sync?force_update=true
   ```

2. **手动补充API描述和DTO信息**
   
   自动同步只能获取基本的API信息，建议手动补充API的详细描述和DTO信息：
   ```
   PUT /api/v1/apidoc/apis/1
   ```

3. **保持DTO结构最新**
   
   当API的请求或响应结构发生变化时，及时更新对应的DTO信息：
   ```
   PUT /api/v1/apidoc/dtos/1
   ```

4. **使用缓存清除接口**
   
   如果发现API文档不是最新状态，可以使用缓存清除接口：
   ```
   POST /api/v1/apidoc/cache/clear
   ```

## 常见问题解答

### Q1: 为什么自动同步没有获取到我的API？

A1: 可能的原因有：
   - 路由文件路径不正确，确保路由文件位于`modules/{module}/routers/router.go`
   - 路由定义格式不符合解析规则，确保使用了Beego标准的命名空间和路由定义方式
   - 检查日志，查看具体错误信息

### Q2: 为什么获取的API信息中没有DTO结构？

A2: 自动同步只能获取API的基本信息（路径、方法、是否需要认证），不能自动关联DTO信息。您需要手动设置API的requestDTO和responseDTO字段。

### Q3: 如何批量导入DTO结构？

A3: 目前API文档模块不直接支持批量导入DTO结构。您可以通过以下方式处理：
   - 为每个DTO编写JSON结构，然后通过创建DTO接口一个个导入
   - 开发自定义脚本，从代码中提取DTO结构并调用API创建

### Q4: 缓存过期时间是多久？

A4: 默认缓存过期时间为1小时（3600秒）。当API或DTO信息发生变化时，相关缓存会自动失效。 