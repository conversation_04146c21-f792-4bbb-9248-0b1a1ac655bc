# API文档模块自动解析原理

## 概述

API文档模块的核心功能之一是能够自动解析各功能模块的路由文件，提取API信息并保存到数据库中。本文档将详细介绍自动解析的原理、实现方式和限制条件。

## 解析原理

API路由解析基于对Beego框架路由定义文件的静态分析。系统通过以下步骤实现自动解析：

1. 定位并读取模块的路由文件（`modules/{module}/routers/router.go`）
2. 使用正则表达式解析文件内容，识别命名空间和路由定义
3. 根据路由定义，提取API路径、HTTP方法和控制器方法名
4. 根据命名空间嵌套关系，确定API是否需要认证
5. 组合所有信息生成API记录
6. 将API记录保存到数据库

## 关键代码实现

### 路由文件解析

路由解析的核心是`parseModuleRouters`函数，它通过正则表达式识别关键的路由定义：

```go
// parseModuleRouters 解析模块路由文件获取API信息
func (s *apiServiceImpl) parseModuleRouters(module string) ([]*models.ApiInfo, error) {
    // 文件读取逻辑...
    
    // 正则表达式定义
    nsPattern := regexp.MustCompile(`web\.NewNamespace\("([^"]+)"`)
    routerPattern := regexp.MustCompile(`NSRouter\("([^"]+)".*"([^:]+):([^"]+)"`)
    securedPattern := regexp.MustCompile(`NSNamespace\("(/secured[^"]*)"`)
    
    // 处理命名空间嵌套...
    
    // 组装API信息...
}
```

### 命名空间识别

解析器首先识别基础命名空间，通常是模块的根路径：

```go
// 例如：web.NewNamespace("/api/v1/admin"
nsMatch := nsPattern.FindStringSubmatch(line)
if len(nsMatch) > 1 && baseNamespace == "" {
    baseNamespace = nsMatch[1]
    namespaces = append(namespaces, baseNamespace)
    continue
}
```

### 认证需求识别

系统通过识别"/secured"嵌套命名空间来判断API是否需要认证：

```go
// 例如：web.NSNamespace("/secured"
securedMatch := securedPattern.FindStringSubmatch(line)
if len(securedMatch) > 1 {
    nestedNS := securedMatch[1]
    namespaces = append(namespaces, nestedNS)
    isSecured = true
    continue
}
```

### 路由定义解析

核心是解析Beego的路由注册格式：

```go
// 例如：web.NSRouter("/login", &controllers.AdminController{}, "post:Login")
routerMatch := routerPattern.FindStringSubmatch(line)
if len(routerMatch) > 3 {
    path := routerMatch[1]          // 路径部分: "/login"
    controllerMethod := routerMatch[3]  // 控制器方法: "Login"
    method := getHTTPMethod(controllerMethod)  // 推断HTTP方法: "POST"
    
    // 构建完整路径和API信息...
}
```

### HTTP方法推断

系统根据控制器方法名推断HTTP方法：

```go
func getHTTPMethod(methodName string) string {
    methodName = strings.ToLower(methodName)
    
    switch {
    case strings.HasPrefix(methodName, "get"):
        return "GET"
    case strings.HasPrefix(methodName, "post"):
        return "POST"
    case strings.HasPrefix(methodName, "put"):
        return "PUT"
    case strings.HasPrefix(methodName, "delete"):
        return "DELETE"
    // 其他方法...
    default:
        return "GET"
    }
}
```

## 解析案例详解

### 示例1: 基本路由

路由定义:
```go
adminNS := web.NewNamespace("/api/v1/admin",
    web.NSRouter("/login", &controllers.AdminController{}, "post:Login"),
)
```

解析结果:
```
路径: /api/v1/admin/login
方法: POST
需要认证: false
控制器方法: Login
```

### 示例2: 嵌套命名空间

路由定义:
```go
adminNS := web.NewNamespace("/api/v1/admin",
    web.NSRouter("/login", &controllers.AdminController{}, "post:Login"),
    web.NSNamespace("/secured",
        web.NSRouter("/info", &controllers.AdminController{}, "get:GetAdminInfo"),
    ),
)
```

解析结果:
```
路径1: /api/v1/admin/login
方法1: POST
需要认证1: false
控制器方法1: Login

路径2: /api/v1/admin/secured/info
方法2: GET
需要认证2: true
控制器方法2: GetAdminInfo
```

### 示例3: 多级嵌套

路由定义:
```go
adminNS := web.NewNamespace("/api/v1/admin",
    web.NSNamespace("/secured",
        web.NSRouter("/info", &controllers.AdminController{}, "get:GetAdminInfo"),
        web.NSNamespace("/users",
            web.NSRouter("", &controllers.UserController{}, "get:ListUsers"),
            web.NSRouter("/:id", &controllers.UserController{}, "get:GetUser"),
        ),
    ),
)
```

解析结果:
```
路径1: /api/v1/admin/secured/info
方法1: GET
需要认证1: true
控制器方法1: GetAdminInfo

路径2: /api/v1/admin/secured/users
方法2: GET
需要认证2: true
控制器方法2: ListUsers

路径3: /api/v1/admin/secured/users/:id
方法3: GET
需要认证3: true
控制器方法3: GetUser
```

## 局限性与注意事项

### 1. 路由格式依赖

自动解析依赖于Beego框架的标准路由定义格式。如果使用了自定义路由注册方式或不标准的写法，可能无法正确识别。

#### 标准格式(可解析):
```go
web.NSRouter("/path", &controllers.Controller{}, "method:Action")
```

#### 非标准格式(无法解析):
```go
customRouter.Add("/path", controllers.Action)
```

### 2. 局限性

自动解析的局限性包括:

- **仅支持静态解析**: 不能解析动态生成的路由
- **不能提取注释信息**: 无法从代码注释中提取API描述
- **不能识别参数类型**: 不能自动识别API的参数和返回值类型
- **不能自动关联DTO**: 需要手动设置API与DTO的关联关系

### 3. 命名规范

为了获得最佳解析结果，建议遵循以下命名规范:

- 控制器方法名以HTTP方法开头，如`GetUser`, `PostLogin`
- 使用标准的命名空间嵌套结构表示认证要求
- 将需要认证的API放在`/secured`命名空间下

## 扩展与优化

### 1. 支持注释解析

未来计划支持从代码注释中提取API描述信息，例如:

```go
// @Title 用户登录
// @Description 处理用户登录请求并返回JWT令牌
// @Param username formData string true "用户名"
// @Param password formData string true "密码"
// @Success 200 {object} dto.LoginResponse
// @Failure 401 {object} result.Result
// @router /login [post]
func (c *UserController) Login() {
    // ...
}
```

### 2. 自动关联DTO

计划开发自动关联DTO的功能，通过解析控制器方法的参数和返回值类型来确定请求和响应DTO。

### 3. 支持更多路由格式

未来将支持更多种类的路由注册格式，以提高兼容性。

## 总结

API文档模块的自动解析功能通过静态分析路由文件，快速生成API信息，大大减轻了手动维护API文档的工作量。虽然有一定的局限性，但通过遵循良好的命名规范和结构化的路由定义，可以使自动解析功能发挥最大效益。 