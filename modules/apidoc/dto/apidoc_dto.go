/**
 * API文档数据传输对象
 *
 * 该文件定义了API文档模块的数据传输对象，用于请求和响应的数据结构。
 * 包括API信息查询、添加、更新和DTO信息管理的相关对象。
 */

package dto

import (
	"time"
)

// APIQueryRequest API信息查询请求
type APIQueryRequest struct {
	// 页码
	Page int `json:"page" form:"page"`
	// 每页数量
	PageSize int `json:"pageSize" form:"pageSize"`
	// 模块名称
	Module string `json:"module" form:"module"`
	// API路径
	Path string `json:"path" form:"path"`
	// HTTP方法
	Method string `json:"method" form:"method"`
	// API名称
	Name string `json:"name" form:"name"`
	// 是否需要认证
	RequireAuth *bool `json:"require_auth" form:"require_auth"`
}

// CreateAPIRequest 创建API信息请求
type CreateAPIRequest struct {
	// 模块名称
	Module string `json:"module" form:"module" valid:"Required;MaxSize(50)"`
	// API路径
	Path string `json:"path" form:"path" valid:"Required;MaxSize(200)"`
	// HTTP方法
	Method string `json:"method" form:"method" valid:"Required;MaxSize(10)"`
	// API名称/标题
	Name string `json:"name" form:"name" valid:"Required;MaxSize(100)"`
	// API描述
	Description string `json:"description" form:"description" valid:"MaxSize(500)"`
	// 是否需要认证
	RequireAuth bool `json:"require_auth" form:"require_auth"`
	// 所需权限（逗号分隔的权限列表）
	Permissions string `json:"permissions" form:"permissions" valid:"MaxSize(255)"`
	// 请求DTO类型名称
	RequestDTO string `json:"request_dto" form:"request_dto" valid:"MaxSize(100)"`
	// 响应DTO类型名称
	ResponseDTO string `json:"response_dto" form:"response_dto" valid:"MaxSize(100)"`
	// 控制器名称
	ControllerName string `json:"controller_name" form:"controller_name" valid:"MaxSize(100)"`
	// 方法名称
	ActName string `json:"act_name" form:"act_name" valid:"MaxSize(100)"`
}

// UpdateAPIRequest 更新API信息请求
type UpdateAPIRequest struct {
	// API ID
	ID int64 `json:"id" form:"id" valid:"Required"`
	// API名称/标题
	Name string `json:"name" form:"name" valid:"Required;MaxSize(100)"`
	// API描述
	Description string `json:"description" form:"description" valid:"MaxSize(500)"`
	// 是否需要认证
	RequireAuth bool `json:"require_auth" form:"require_auth"`
	// 所需权限（逗号分隔的权限列表）
	Permissions string `json:"permissions" form:"permissions" valid:"MaxSize(255)"`
	// 请求DTO类型名称
	RequestDTO string `json:"request_dto" form:"request_dto" valid:"MaxSize(100)"`
	// 响应DTO类型名称
	ResponseDTO string `json:"response_dto" form:"response_dto" valid:"MaxSize(100)"`
	// 控制器名称
	ControllerName string `json:"controller_name" form:"controller_name" valid:"MaxSize(100)"`
	// 方法名称
	ActName string `json:"act_name" form:"act_name" valid:"MaxSize(100)"`
	// API状态 (0-禁用, 1-启用)
	Status int `json:"status" form:"status" valid:"Range(0,1)"`
}

// APIResponse API信息响应
type APIResponse struct {
	// API ID
	ID int64 `json:"id"`
	// 模块名称
	Module string `json:"module"`
	// API路径
	Path string `json:"path"`
	// HTTP方法
	Method string `json:"method"`
	// API名称/标题
	Name string `json:"name"`
	// API描述
	Description string `json:"description"`
	// 是否需要认证
	RequireAuth bool `json:"require_auth"`
	// 所需权限（逗号分隔的权限列表）
	Permissions string `json:"permissions"`
	// 请求DTO类型名称
	RequestDTO string `json:"request_dto"`
	// 响应DTO类型名称
	ResponseDTO string `json:"response_dto"`
	// 控制器名称
	ControllerName string `json:"controller_name"`
	// 方法名称
	ActName string `json:"act_name"`
	// 页面名称
	PageName string `json:"page_name"`
	// API状态 (0-禁用, 1-启用)
	Status int `json:"status"`
	// 创建时间
	CreatedAt time.Time `json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

// DTOQueryRequest DTO信息查询请求
type DTOQueryRequest struct {
	// 页码
	Page int `json:"page" form:"page"`
	// 每页数量
	PageSize int `json:"pageSize" form:"pageSize"`
	// 模块名称
	Module string `json:"module" form:"module"`
	// DTO名称
	Name string `json:"name" form:"name"`
	// DTO类型
	Type string `json:"type" form:"type"`
}

// CreateDTORequest 创建DTO信息请求
type CreateDTORequest struct {
	// 模块名称
	Module string `json:"module" form:"module" valid:"Required;MaxSize(50)"`
	// DTO名称
	Name string `json:"name" form:"name" valid:"Required;MaxSize(100)"`
	// DTO描述
	Description string `json:"description" form:"description" valid:"MaxSize(500)"`
	// DTO类型 (request-请求对象, response-响应对象, common-通用对象)
	Type string `json:"type" form:"type" valid:"Required;MaxSize(20)"`
	// DTO结构定义 (JSON格式)
	Structure string `json:"structure" form:"structure" valid:"Required"`
}

// UpdateDTORequest 更新DTO信息请求
type UpdateDTORequest struct {
	// DTO ID
	ID int64 `json:"id" form:"id" valid:"Required"`
	// DTO描述
	Description string `json:"description" form:"description" valid:"MaxSize(500)"`
	// DTO结构定义 (JSON格式)
	Structure string `json:"structure" form:"structure" valid:"Required"`
}

// DTOResponse DTO信息响应
type DTOResponse struct {
	// DTO ID
	ID int64 `json:"id"`
	// 模块名称
	Module string `json:"module"`
	// DTO名称
	Name string `json:"name"`
	// DTO描述
	Description string `json:"description"`
	// DTO类型
	Type string `json:"type"`
	// DTO结构定义
	Structure interface{} `json:"structure"`
	// 创建时间
	CreatedAt time.Time `json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

// ModuleAPIRequest 获取模块API请求
type ModuleAPIRequest struct {
	// 模块名称
	Module string `json:"module" form:"module" valid:"Required"`
}

// APIWithDTOResponse 包含DTO详细信息的API响应
type APIWithDTOResponse struct {
	// API基本信息
	API APIResponse `json:"api"`
	// 请求DTO信息
	RequestDTO *DTOResponse `json:"request_dto,omitempty"`
	// 响应DTO信息
	ResponseDTO *DTOResponse `json:"response_dto,omitempty"`
}

// ModuleListResponse 模块列表响应
type ModuleListResponse struct {
	// 模块名称
	Name string `json:"name"`
	// API数量
	APICount int `json:"api_count"`
	// DTO数量
	DTOCount int `json:"dto_count"`
}

// SyncModuleRequest 同步模块API和DTO信息请求
type SyncModuleRequest struct {
	// 模块名称
	Module string `json:"module" form:"module" valid:"Required"`
	// 是否强制更新（覆盖已有数据）
	ForceUpdate bool `json:"force_update" form:"force_update"`
}

// ControllerQueryRequest 控制器信息查询请求
type ControllerQueryRequest struct {
	// 页码
	Page int `json:"page" form:"page"`
	// 每页数量
	PageSize int `json:"pageSize" form:"pageSize"`
	// 模块名称
	Module string `json:"module" form:"module"`
	// 控制器名称
	ControllerName string `json:"controller_name" form:"controller_name"`
	// 方法名称
	MethodName string `json:"method_name" form:"method_name"`
}

// ControllerResponse 控制器信息响应
type ControllerResponse struct {
	// 控制器ID
	ID int64 `json:"id"`
	// 模块名称
	Module string `json:"module"`
	// 控制器名称
	ControllerName string `json:"controller_name"`
	// 控制器描述
	Description string `json:"description"`
	// 方法名称
	MethodName string `json:"method_name"`
	// 方法描述
	MethodDescription string `json:"method_description"`
	// 使用的DTO信息
	UsedDTOs []string `json:"used_dtos"`
	// 创建时间
	CreatedAt time.Time `json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

// ControllerDetailResponse 包含DTO详细信息的控制器响应
type ControllerDetailResponse struct {
	// 控制器基本信息
	Controller ControllerResponse `json:"controller"`
	// 关联的DTO信息列表
	DTOs []*DTOResponse `json:"dtos,omitempty"`
}

// GetPageNamesByModuleRequest 获取模块页面名称请求
type GetPageNamesByModuleRequest struct {
	// 模块名称
	Module string `json:"module" form:"module" valid:"Required"`
	// 页码
	Page int `json:"page" form:"page"`
	// 每页大小
	PageSize int `json:"page_size" form:"page_size"`
}

// PageNameInfo 页面名称信息
type PageNameInfo struct {
	// 页面名称
	PageName string `json:"page_name"`
	// API数量
	//ApiCount int64 `json:"api_count"`
}

// GetPageNamesByModuleResponse 获取模块页面名称响应
type GetPageNamesByModuleResponse struct {
	// 模块名称
	Module string `json:"module"`
	// 页面名称列表
	List []PageNameInfo `json:"list"`
	// 总数
	Total int64 `json:"total"`
	// 页码
	Page int `json:"page"`
	// 每页大小
	PageSize int `json:"pageSize"`
}

// ClearCacheRequest 清除缓存请求
type ClearCacheRequest struct {
	// 模块名称，不指定则清除所有
	Module string `json:"module" form:"module"`
}

// GetAPIsByPageNameRequest 根据页面名称获取API列表请求
type GetAPIsByPageNameRequest struct {
	// 页面名称
	PageName string `json:"pagename" form:"pagename" valid:"Required"`
}

// GetAPIsByModuleRequest 获取指定模块的所有API请求
type GetAPIsByModuleRequest struct {
	// 模块名称
	Module string `json:"module" form:"module" valid:"Required"`
}
