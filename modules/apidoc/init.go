/**
 * API文档模块初始化
 *
 * 该文件实现了API文档模块的初始化功能
 */

package apidoc

import (
	"context"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/apidoc/controllers"
	"o_mall_backend/modules/apidoc/repositories"
	"o_mall_backend/modules/apidoc/repositories/impl"
	"o_mall_backend/modules/apidoc/routers"
	"o_mall_backend/modules/apidoc/services"
	serviceImpl "o_mall_backend/modules/apidoc/services/impl"
)

// 保存API服务实例的全局变量
var apiService services.ApiService

// Init 初始化API文档模块
func Init() {
	logs.Info("初始化API文档模块...")

	// 初始化服务实例
	apiRepoImpl := impl.NewApidocRepository()
	var apiRepo repositories.ApiRepository = apiRepoImpl
	apiService = serviceImpl.NewApiService(apiRepo)

	// 初始化控制器
	controllers.NewApiDocController(apiService)

	routers.InitApiDocRouter()
	logs.Info("API文档模块初始化完成")
}

// SyncAPIDoc 同步指定模块的API文档
func SyncAPIDoc(module string) error {
	ctx := context.Background()

	// 使用全局apiService变量
	if apiService == nil {
		logs.Error("[SyncAPIDoc] API服务未初始化")
		return nil
	}

	// 调用同步方法，forceUpdate设置为true表示强制更新已存在的API
	err := apiService.SyncModuleAPI(ctx, module, true)
	if err != nil {
		logs.Error("[SyncAPIDoc] 同步模块API失败: %s, 错误: %v", module, err)
		return err
	}

	logs.Info("[SyncAPIDoc] 模块API同步成功: %s", module)
	return nil
}

// SyncDTODoc 同步指定模块的DTO文档
func SyncDTODoc(module string) error {
	ctx := context.Background()

	// 使用全局apiService变量
	if apiService == nil {
		logs.Error("[SyncDTODoc] API服务未初始化")
		return nil
	}

	// 调用同步方法，forceUpdate设置为true表示强制更新已存在的DTO
	err := apiService.SyncModuleDTOs(ctx, module, true)
	if err != nil {
		logs.Error("[SyncDTODoc] 同步模块DTO失败: %s, 错误: %v", module, err)
		return err
	}

	logs.Info("[SyncDTODoc] 模块DTO同步成功: %s", module)
	return nil
}

// SyncControllerDoc 同步指定模块的控制器文档
func SyncControllerDoc(module string) error {
	ctx := context.Background()

	// 使用全局apiService变量
	if apiService == nil {
		logs.Error("[SyncControllerDoc] API服务未初始化")
		return nil
	}

	// 调用同步方法，forceUpdate设置为true表示强制更新已存在的控制器信息
	err := apiService.SyncModuleControllers(ctx, module, true)
	if err != nil {
		logs.Error("[SyncControllerDoc] 同步模块控制器失败: %s, 错误: %v", module, err)
		return err
	}

	logs.Info("[SyncControllerDoc] 模块控制器同步成功: %s", module)
	return nil
}
