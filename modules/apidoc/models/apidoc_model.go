/**
 * API文档模型
 *
 * 该文件定义了用于存储API信息和DTO结构的数据模型
 * 包括API路径、方法、描述、所需参数结构和响应结构等信息
 */

package models

import (
	"encoding/json"
	"time"
)

// ApiInfo API信息模型
type ApiInfo struct {
	// 主键ID
	ID int64 `orm:"pk;auto;column(id)" json:"id"`
	// 模块名称
	Module string `orm:"column(module);size(50);index" json:"module"`
	// API路径
	Path string `orm:"column(path);size(200);index" json:"path"`
	// HTTP方法
	Method string `orm:"column(method);size(10)" json:"method"`
	// API名称/标题
	Name string `orm:"column(name);size(100)" json:"name"`
	// 页面名称，用于归类API
	PageName string `orm:"column(page_name);size(100);null;index" json:"page_name"`
	// API描述
	Description string `orm:"column(description);size(500);null" json:"description"`
	// 是否需要认证
	RequireAuth bool `orm:"column(require_auth);default(false)" json:"require_auth"`
	// 所需权限（逗号分隔的权限列表）
	Permissions string `orm:"column(permissions);size(255);null" json:"permissions"`
	// 请求DTO类型名称 (对应DTOInfo表中的name字段)
	RequestDTO string `orm:"column(request_dto);size(100);null" json:"request_dto"`
	// 响应DTO类型名称 (对应DTOInfo表中的name字段)
	ResponseDTO string `orm:"column(response_dto);size(100);null" json:"response_dto"`
	// 控制器名称
	ControllerName string `orm:"column(controller_name);size(100);null" json:"controller_name"`
	// 方法名称
	ActName string `orm:"column(act_name);size(100);null" json:"act_name"`
	// API状态 (0-禁用, 1-启用)
	Status int `orm:"column(status);default(1)" json:"status"`
	// 创建时间
	CreatedAt time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updated_at"`
}

// TableName 返回表名
func (a *ApiInfo) TableName() string {
	return "api_info"
}

// DTOInfo 数据传输对象信息模型
type DTOInfo struct {
	// 主键ID
	ID int64 `orm:"pk;auto;column(id)" json:"id"`
	// 模块名称
	Module string `orm:"column(module);size(50);index" json:"module"`
	// DTO名称
	Name string `orm:"column(name);size(100);index" json:"name"`
	// DTO描述
	Description string `orm:"column(description);size(500);null" json:"description"`
	// DTO类型 (request-请求对象, response-响应对象, common-通用对象)
	Type string `orm:"column(type);size(20)" json:"type"`
	// DTO结构定义 (JSON格式)
	Structure string `orm:"column(structure);type(text)" json:"structure"`
	// 创建时间
	CreatedAt time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updated_at"`
}

// TableName 返回表名
func (d *DTOInfo) TableName() string {
	return "dto_info"
}

// DTOField DTO字段结构
type DTOField struct {
	// 字段名称
	Name string `json:"name"`
	// 字段类型
	Type string `json:"type"`
	// 字段描述
	Description string `json:"description"`
	// 是否必填
	Required bool `json:"required"`
	// 字段验证规则
	ValidationRules string `json:"validation_rules,omitempty"`
	// 默认值
	DefaultValue interface{} `json:"default_value,omitempty"`
	// 子字段（如果是对象或数组）
	Fields []DTOField `json:"fields,omitempty"`
}

// UnmarshalDTOFields 将Structure字段解析为DTOField切片
func (d *DTOInfo) UnmarshalDTOFields() ([]DTOField, error) {
	var fields []DTOField
	err := json.Unmarshal([]byte(d.Structure), &fields)
	return fields, err
}

// ApiCache Redis缓存键
const (
	// ApiListCacheKey API列表缓存键（按模块）
	ApiListCacheKey = "apidoc:api_list:%s"
	// ApiInfoCacheKey 单个API信息缓存键
	ApiInfoCacheKey = "apidoc:api_info:%s:%s"
	// DTOInfoCacheKey DTO信息缓存键
	DTOInfoCacheKey = "apidoc:dto_info:%s:%s"
	// ModuleListCacheKey 模块列表缓存键
	ModuleListCacheKey = "apidoc:module_list"
	// CacheExpiration 缓存过期时间（24小时）
	CacheExpiration = 24 * time.Hour
)

// ControllerInfo 控制器信息模型
type ControllerInfo struct {
	// 主键ID
	ID int64 `orm:"pk;auto;column(id)" json:"id"`
	// 模块名称
	Module string `orm:"column(module);size(50);index" json:"module"`
	// 控制器名称
	ControllerName string `orm:"column(controller_name);size(100);index" json:"controller_name"`
	// 控制器描述
	Description string `orm:"column(description);type(text);null" json:"description"`
	// 方法名称
	MethodName string `orm:"column(method_name);size(100);index" json:"method_name"`
	// 方法描述
	MethodDescription string `orm:"column(method_description);type(text);null" json:"method_description"`
	// 使用的DTO IDs，JSON格式存储
	UsedDTOIDs string `orm:"column(used_dto_ids);type(text);null" json:"used_dto_ids"`
	// 方法内容，方法体的原始内容，从开始大括号到结束大括号
	MethodContent string `orm:"column(method_content);type(text);null" json:"method_content"`
	// 创建时间
	CreatedAt time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"created_at"`
	// 更新时间
	UpdatedAt time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updated_at"`
}

// TableName 返回表名
func (c *ControllerInfo) TableName() string {
	return "controller_info"
}

// ControllerCache Redis缓存键
const (
	// ControllerListCacheKey 控制器列表缓存键（按模块）
	ControllerListCacheKey = "apidoc:controller_list:%s"
	// ControllerInfoCacheKey 单个控制器信息缓存键
	ControllerInfoCacheKey = "apidoc:controller_info:%s:%s"
)
