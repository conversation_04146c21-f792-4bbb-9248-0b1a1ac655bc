/**
 * 模型初始化
 *
 * 该文件负责初始化API文档模块的数据模型
 */

package models

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// 包初始化函数，在包被导入时自动执行
func init() {
	// 自动注册模型
	logs.Info("[API文档模块] 自动注册数据模型...")
	orm.RegisterModel(new(ApiInfo))
	orm.RegisterModel(new(DTOInfo))
	orm.RegisterModel(new(ControllerInfo))

	logs.Info("[API文档模块] 数据模型自动注册完成")
}

// Init 初始化API文档模块的数据模型
// 这个函数仅供手动调用，用于额外的初始化工作
func Init() {
	logs.Info("[API文档模块] 执行模型初始化...")

	// 执行其他初始化逻辑，例如数据迁移
	// 注意：模型已经在init()函数中注册，这里不需要重复注册

	logs.Info("[API文档模块] 模型初始化完成")

	// 为DTOInfo表添加module和name的组合唯一索引
	_, err := orm.NewOrm().Raw("CREATE UNIQUE INDEX IF NOT EXISTS idx_module_name ON dto_info(module, name)").Exec()
	if err != nil {
		logs.Error("[API文档模块] 创建组合唯一索引失败: %v", err)
	} else {
		logs.Info("[API文档模块] 成功创建DTOInfo表的module和name组合唯一索引")
	}
}
