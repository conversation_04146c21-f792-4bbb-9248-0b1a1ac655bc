/**
 * API文档仓库接口
 *
 * 该文件定义了API文档模块的数据访问接口
 * 实现数据的增删改查等基本操作
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/apidoc/models"
)

// ApiRepository API文档仓库接口
type ApiRepository interface {
	// CreateAPI 创建API信息
	CreateAPI(ctx context.Context, api *models.ApiInfo) (int64, error)

	// GetAPIByID 根据ID获取API信息
	GetAPIByID(ctx context.Context, id int64) (*models.ApiInfo, error)

	// GetAPIByPathMethod 根据路径和方法获取API信息
	GetAPIByPathMethod(ctx context.Context, path, method string) (*models.ApiInfo, error)

	// UpdateAPI 更新API信息
	UpdateAPI(ctx context.Context, api *models.ApiInfo) error

	// DeleteAPI 删除API信息
	DeleteAPI(ctx context.Context, id int64) error

	// ListAPIs 获取API列表
	ListAPIs(ctx context.Context, module, path, method, name string, requireAuth *bool, page, pageSize int) ([]*models.ApiInfo, int64, error)

	// GetAPIsByModule 获取指定模块的所有API
	GetAPIsByModule(ctx context.Context, module string) ([]*models.ApiInfo, error)

	// CountAPIsByModule 统计指定模块的API数量
	CountAPIsByModule(ctx context.Context, module string) (int, error)

	// CreateDTO 创建DTO信息
	CreateDTO(ctx context.Context, dto *models.DTOInfo) (int64, error)

	// GetDTOByID 根据ID获取DTO信息
	GetDTOByID(ctx context.Context, id int64) (*models.DTOInfo, error)

	// GetDTOByName 根据名称获取DTO信息
	GetDTOByName(ctx context.Context, name string) (*models.DTOInfo, error)

	// GetDTOByModuleAndName 根据模块名和DTO名称获取DTO信息
	GetDTOByModuleAndName(ctx context.Context, module string, name string) (*models.DTOInfo, error)

	// UpdateDTO 更新DTO信息
	UpdateDTO(ctx context.Context, dto *models.DTOInfo) error

	// DeleteDTO 删除DTO信息
	DeleteDTO(ctx context.Context, id int64) error

	// ListDTOs 获取DTO列表
	ListDTOs(ctx context.Context, module, name, dtoType string, page, pageSize int) ([]*models.DTOInfo, int64, error)

	// GetDTOsByModule 获取指定模块的所有DTO
	GetDTOsByModule(ctx context.Context, module string) ([]*models.DTOInfo, error)

	// CountDTOsByModule 统计指定模块的DTO数量
	CountDTOsByModule(ctx context.Context, module string) (int, error)

	// GetAllModules 获取所有模块名称
	GetAllModules(ctx context.Context) ([]string, error)

	// CreateController 创建控制器信息
	CreateController(ctx context.Context, controller *models.ControllerInfo) (int64, error)

	// GetControllerByID 根据ID获取控制器信息
	GetControllerByID(ctx context.Context, id int64) (*models.ControllerInfo, error)

	// GetControllerByNameMethod 根据控制器名称和方法名获取控制器信息
	GetControllerByNameMethod(ctx context.Context, module, controllerName, methodName string) (*models.ControllerInfo, error)

	// UpdateController 更新控制器信息
	UpdateController(ctx context.Context, controller *models.ControllerInfo) error

	// DeleteController 删除控制器信息
	DeleteController(ctx context.Context, id int64) error

	// ListControllers 获取控制器列表
	ListControllers(ctx context.Context, module, controllerName, methodName string, page, pageSize int) ([]*models.ControllerInfo, int64, error)

	// GetControllersByModule 获取指定模块的所有控制器
	GetControllersByModule(ctx context.Context, module string) ([]*models.ControllerInfo, error)

	// CountControllersByModule 统计指定模块的控制器数量
	CountControllersByModule(ctx context.Context, module string) (int, error)

	// GetAPIsByPageName 根据页面名称获取API列表
	GetAPIsByPageName(ctx context.Context, pageName string) ([]*models.ApiInfo, error)

	// GetPageNamesByModule 获取指定模块的所有页面名称及对应的API数量
	GetPageNamesByModule(ctx context.Context, module string) ([]map[string]interface{}, error)
}
