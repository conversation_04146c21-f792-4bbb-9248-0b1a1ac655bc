/**
 * API文档仓库接口实现
 *
 * 该文件实现了API文档模块的数据访问接口
 * 实现对数据库的具体操作
 */

package impl

import (
	"context"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/apidoc/models"
)

// ApidocRepositoryImpl 实现API文档仓库接口
type ApidocRepositoryImpl struct{}

// NewApidocRepository 创建API文档仓库实例
func NewApidocRepository() *ApidocRepositoryImpl {
	return &ApidocRepositoryImpl{}
}

// CreateAPI 创建API信息
func (r *ApidocRepositoryImpl) CreateAPI(ctx context.Context, api *models.ApiInfo) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(api)
	if err != nil {
		logs.Error("[CreateAPI] 创建API信息失败: %v", err)
		return 0, fmt.Errorf("创建API信息失败: %v", err)
	}
	return id, nil
}

// GetAPIByID 根据ID获取API信息
func (r *ApidocRepositoryImpl) GetAPIByID(ctx context.Context, id int64) (*models.ApiInfo, error) {
	o := orm.NewOrm()
	api := &models.ApiInfo{ID: id}
	err := o.Read(api)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[GetAPIByID] 获取API信息失败: %v", err)
		return nil, fmt.Errorf("获取API信息失败: %v", err)
	}
	return api, nil
}

// GetAPIByPathMethod 根据路径和方法获取API信息
func (r *ApidocRepositoryImpl) GetAPIByPathMethod(ctx context.Context, path, method string) (*models.ApiInfo, error) {
	o := orm.NewOrm()
	api := &models.ApiInfo{}
	err := o.QueryTable(new(models.ApiInfo)).
		Filter("path", path).
		Filter("method", method).
		One(api)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[GetAPIByPathMethod] 获取API信息失败: %v", err)
		return nil, fmt.Errorf("获取API信息失败: %v", err)
	}
	return api, nil
}

// UpdateAPI 更新API信息
func (r *ApidocRepositoryImpl) UpdateAPI(ctx context.Context, api *models.ApiInfo) error {
	o := orm.NewOrm()
	_, err := o.Update(api)
	if err != nil {
		logs.Error("[UpdateAPI] 更新API信息失败: %v", err)
		return fmt.Errorf("更新API信息失败: %v", err)
	}
	return nil
}

// DeleteAPI 删除API信息
func (r *ApidocRepositoryImpl) DeleteAPI(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	_, err := o.Delete(&models.ApiInfo{ID: id})
	if err != nil {
		logs.Error("[DeleteAPI] 删除API信息失败: %v", err)
		return fmt.Errorf("删除API信息失败: %v", err)
	}
	return nil
}

// ListAPIs 获取API列表
func (r *ApidocRepositoryImpl) ListAPIs(ctx context.Context, module, path, method, name string, requireAuth *bool, page, pageSize int) ([]*models.ApiInfo, int64, error) {
	o := orm.NewOrm()
	var apis []*models.ApiInfo

	query := o.QueryTable(new(models.ApiInfo))

	if module != "" {
		query = query.Filter("module", module)
	}
	if path != "" {
		query = query.Filter("path__contains", path)
	}
	if method != "" {
		query = query.Filter("method", method)
	}
	if name != "" {
		query = query.Filter("name__contains", name)
	}
	if requireAuth != nil {
		query = query.Filter("require_auth", *requireAuth)
	}

	total, err := query.Count()
	if err != nil {
		logs.Error("[ListAPIs] 统计API总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取API列表失败: %v", err)
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = query.OrderBy("-id").Limit(pageSize, offset).All(&apis)
	} else {
		_, err = query.OrderBy("-id").All(&apis)
	}

	if err != nil {
		logs.Error("[ListAPIs] 获取API列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取API列表失败: %v", err)
	}

	return apis, total, nil
}

// GetAPIsByModule 获取指定模块的所有API
func (r *ApidocRepositoryImpl) GetAPIsByModule(ctx context.Context, module string) ([]*models.ApiInfo, error) {
	o := orm.NewOrm()
	var apis []*models.ApiInfo

	_, err := o.QueryTable(new(models.ApiInfo)).
		Filter("module", module).
		OrderBy("path", "method").
		All(&apis)

	if err != nil {
		logs.Error("[GetAPIsByModule] 获取模块API列表失败: %v", err)
		return nil, fmt.Errorf("获取模块API列表失败: %v", err)
	}

	return apis, nil
}

// CountAPIsByModule 统计指定模块的API数量
func (r *ApidocRepositoryImpl) CountAPIsByModule(ctx context.Context, module string) (int, error) {
	o := orm.NewOrm()

	count, err := o.QueryTable(new(models.ApiInfo)).
		Filter("module", module).
		Count()

	if err != nil {
		logs.Error("[CountAPIsByModule] 统计模块API数量失败: %v", err)
		return 0, fmt.Errorf("统计模块API数量失败: %v", err)
	}

	return int(count), nil
}

// CreateDTO 创建DTO信息
func (r *ApidocRepositoryImpl) CreateDTO(ctx context.Context, dto *models.DTOInfo) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(dto)
	if err != nil {
		logs.Error("[CreateDTO] 创建DTO信息失败: %v", err)
		return 0, fmt.Errorf("创建DTO信息失败: %v", err)
	}
	return id, nil
}

// GetDTOByID 根据ID获取DTO信息
func (r *ApidocRepositoryImpl) GetDTOByID(ctx context.Context, id int64) (*models.DTOInfo, error) {
	o := orm.NewOrm()
	dto := &models.DTOInfo{ID: id}
	err := o.Read(dto)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[GetDTOByID] 获取DTO信息失败: %v", err)
		return nil, fmt.Errorf("获取DTO信息失败: %v", err)
	}
	return dto, nil
}

// GetDTOByName 根据名称获取DTO信息
func (r *ApidocRepositoryImpl) GetDTOByName(ctx context.Context, name string) (*models.DTOInfo, error) {
	o := orm.NewOrm()
	dto := &models.DTOInfo{}
	err := o.QueryTable(new(models.DTOInfo)).
		Filter("name", name).
		One(dto)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[GetDTOByName] 获取DTO信息失败: %v", err)
		return nil, fmt.Errorf("获取DTO信息失败: %v", err)
	}
	return dto, nil
}

// UpdateDTO 更新DTO信息
func (r *ApidocRepositoryImpl) UpdateDTO(ctx context.Context, dto *models.DTOInfo) error {
	o := orm.NewOrm()
	_, err := o.Update(dto)
	if err != nil {
		logs.Error("[UpdateDTO] 更新DTO信息失败: %v", err)
		return fmt.Errorf("更新DTO信息失败: %v", err)
	}
	return nil
}

// DeleteDTO 删除DTO信息
func (r *ApidocRepositoryImpl) DeleteDTO(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	_, err := o.Delete(&models.DTOInfo{ID: id})
	if err != nil {
		logs.Error("[DeleteDTO] 删除DTO信息失败: %v", err)
		return fmt.Errorf("删除DTO信息失败: %v", err)
	}
	return nil
}

// ListDTOs 获取DTO列表
func (r *ApidocRepositoryImpl) ListDTOs(ctx context.Context, module, name, dtoType string, page, pageSize int) ([]*models.DTOInfo, int64, error) {
	o := orm.NewOrm()
	var dtos []*models.DTOInfo

	query := o.QueryTable(new(models.DTOInfo))

	if module != "" {
		query = query.Filter("module", module)
	}
	if name != "" {
		query = query.Filter("name__contains", name)
	}
	if dtoType != "" {
		query = query.Filter("type", dtoType)
	}

	total, err := query.Count()
	if err != nil {
		logs.Error("[ListDTOs] 统计DTO总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取DTO列表失败: %v", err)
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = query.OrderBy("-id").Limit(pageSize, offset).All(&dtos)
	} else {
		_, err = query.OrderBy("-id").All(&dtos)
	}

	if err != nil {
		logs.Error("[ListDTOs] 获取DTO列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取DTO列表失败: %v", err)
	}

	return dtos, total, nil
}

// GetDTOsByModule 获取指定模块的所有DTO
func (r *ApidocRepositoryImpl) GetDTOsByModule(ctx context.Context, module string) ([]*models.DTOInfo, error) {
	o := orm.NewOrm()
	var dtos []*models.DTOInfo

	_, err := o.QueryTable(new(models.DTOInfo)).
		Filter("module", module).
		OrderBy("name").
		All(&dtos)

	if err != nil {
		logs.Error("[GetDTOsByModule] 获取模块DTO列表失败: %v", err)
		return nil, fmt.Errorf("获取模块DTO列表失败: %v", err)
	}

	return dtos, nil
}

// CountDTOsByModule 统计指定模块的DTO数量
func (r *ApidocRepositoryImpl) CountDTOsByModule(ctx context.Context, module string) (int, error) {
	o := orm.NewOrm()

	count, err := o.QueryTable(new(models.DTOInfo)).
		Filter("module", module).
		Count()

	if err != nil {
		logs.Error("[CountDTOsByModule] 统计模块DTO数量失败: %v", err)
		return 0, fmt.Errorf("统计模块DTO数量失败: %v", err)
	}

	return int(count), nil
}

// GetAllModules 获取所有模块名称
func (r *ApidocRepositoryImpl) GetAllModules(ctx context.Context) ([]string, error) {
	o := orm.NewOrm()
	var modules []string

	// 使用DISTINCT查询所有模块名
	_, err := o.Raw("SELECT DISTINCT module FROM api_info ORDER BY module").QueryRows(&modules)
	if err != nil {
		logs.Error("[GetAllModules] 获取所有模块名称失败: %v", err)
		return nil, fmt.Errorf("获取所有模块名称失败: %v", err)
	}

	return modules, nil
}

// CreateController 创建控制器信息
func (r *ApidocRepositoryImpl) CreateController(ctx context.Context, controller *models.ControllerInfo) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(controller)
	if err != nil {
		logs.Error("[CreateController] 创建控制器信息失败: %v", err)
		return 0, fmt.Errorf("创建控制器信息失败: %v", err)
	}
	return id, nil
}

// GetControllerByID 根据ID获取控制器信息
func (r *ApidocRepositoryImpl) GetControllerByID(ctx context.Context, id int64) (*models.ControllerInfo, error) {
	o := orm.NewOrm()
	controller := &models.ControllerInfo{ID: id}
	err := o.Read(controller)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[GetControllerByID] 获取控制器信息失败: %v", err)
		return nil, fmt.Errorf("获取控制器信息失败: %v", err)
	}
	return controller, nil
}

// GetControllerByNameMethod 根据控制器名称和方法名获取控制器信息
func (r *ApidocRepositoryImpl) GetControllerByNameMethod(ctx context.Context, module, controllerName, methodName string) (*models.ControllerInfo, error) {
	o := orm.NewOrm()
	controller := &models.ControllerInfo{}
	err := o.QueryTable(new(models.ControllerInfo)).
		Filter("module", module).
		Filter("controller_name", controllerName).
		Filter("method_name", methodName).
		One(controller)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[GetControllerByNameMethod] 获取控制器信息失败: %v", err)
		return nil, fmt.Errorf("获取控制器信息失败: %v", err)
	}
	return controller, nil
}

// UpdateController 更新控制器信息
func (r *ApidocRepositoryImpl) UpdateController(ctx context.Context, controller *models.ControllerInfo) error {
	o := orm.NewOrm()
	_, err := o.Update(controller)
	if err != nil {
		logs.Error("[UpdateController] 更新控制器信息失败: %v", err)
		return fmt.Errorf("更新控制器信息失败: %v", err)
	}
	return nil
}

// DeleteController 删除控制器信息
func (r *ApidocRepositoryImpl) DeleteController(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	_, err := o.Delete(&models.ControllerInfo{ID: id})
	if err != nil {
		logs.Error("[DeleteController] 删除控制器信息失败: %v", err)
		return fmt.Errorf("删除控制器信息失败: %v", err)
	}
	return nil
}

// ListControllers 获取控制器列表
func (r *ApidocRepositoryImpl) ListControllers(ctx context.Context, module, controllerName, methodName string, page, pageSize int) ([]*models.ControllerInfo, int64, error) {
	o := orm.NewOrm()
	var controllers []*models.ControllerInfo

	query := o.QueryTable(new(models.ControllerInfo))

	if module != "" {
		query = query.Filter("module", module)
	}
	if controllerName != "" {
		query = query.Filter("controller_name__contains", controllerName)
	}
	if methodName != "" {
		query = query.Filter("method_name__contains", methodName)
	}

	total, err := query.Count()
	if err != nil {
		logs.Error("[ListControllers] 统计控制器总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取控制器列表失败: %v", err)
	}

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = query.OrderBy("-id").Limit(pageSize, offset).All(&controllers)
	} else {
		_, err = query.OrderBy("-id").All(&controllers)
	}

	if err != nil {
		logs.Error("[ListControllers] 获取控制器列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取控制器列表失败: %v", err)
	}

	return controllers, total, nil
}

// GetControllersByModule 获取指定模块的所有控制器
func (r *ApidocRepositoryImpl) GetControllersByModule(ctx context.Context, module string) ([]*models.ControllerInfo, error) {
	o := orm.NewOrm()
	var controllers []*models.ControllerInfo

	_, err := o.QueryTable(new(models.ControllerInfo)).
		Filter("module", module).
		OrderBy("controller_name", "method_name").
		All(&controllers)

	if err != nil {
		logs.Error("[GetControllersByModule] 获取模块控制器列表失败: %v", err)
		return nil, fmt.Errorf("获取模块控制器列表失败: %v", err)
	}

	return controllers, nil
}

// CountControllersByModule 统计指定模块的控制器数量
func (r *ApidocRepositoryImpl) CountControllersByModule(ctx context.Context, module string) (int, error) {
	o := orm.NewOrm()

	count, err := o.QueryTable(new(models.ControllerInfo)).
		Filter("module", module).
		Count()

	if err != nil {
		logs.Error("[CountControllersByModule] 统计模块控制器数量失败: %v", err)
		return 0, fmt.Errorf("统计模块控制器数量失败: %v", err)
	}

	return int(count), nil
}

// GetDTOByModuleAndName 根据模块名和DTO名称获取DTO信息
func (r *ApidocRepositoryImpl) GetDTOByModuleAndName(ctx context.Context, module string, name string) (*models.DTOInfo, error) {
	o := orm.NewOrm()
	dto := &models.DTOInfo{}
	err := o.QueryTable(new(models.DTOInfo)).
		Filter("module", module).
		Filter("name", name).
		One(dto)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[GetDTOByModuleAndName] 获取DTO信息失败: %v", err)
		return nil, fmt.Errorf("获取DTO信息失败: %v", err)
	}
	return dto, nil
}

// GetAPIsByPageName 根据页面名称获取API列表
func (r *ApidocRepositoryImpl) GetAPIsByPageName(ctx context.Context, pageName string) ([]*models.ApiInfo, error) {
	o := orm.NewOrm()
	var apis []*models.ApiInfo

	_, err := o.QueryTable(new(models.ApiInfo)).
		Filter("page_name", pageName).
		All(&apis)

	if err != nil {
		return nil, err
	}

	return apis, nil
}

// GetPageNamesByModule 获取指定模块的所有页面名称及对应的API数量
func (r *ApidocRepositoryImpl) GetPageNamesByModule(ctx context.Context, module string) ([]map[string]interface{}, error) {
	o := orm.NewOrm()
	var result []map[string]interface{}

	// 使用SQL查询获取页面名称和API数量
	var rows []orm.Params
	_, err := o.Raw(`SELECT page_name, COUNT(*) AS api_count 
					 FROM api_info 
					 WHERE module = ? AND page_name IS NOT NULL AND page_name != ''
					 GROUP BY page_name 
					 ORDER BY page_name`, module).Values(&rows)

	if err != nil {
		logs.Error("[GetPageNamesByModule] 获取模块页面名称失败: %v", err)
		return nil, fmt.Errorf("获取模块页面名称失败: %v", err)
	}

	// 转换查询结果
	for _, row := range rows {
		item := map[string]interface{}{
			"page_name": row["page_name"],
			"api_count": row["api_count"],
		}
		result = append(result, item)
	}

	return result, nil
}
