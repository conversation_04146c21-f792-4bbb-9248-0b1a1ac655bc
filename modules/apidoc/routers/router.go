/**
 * API文档模块路由
 *
 * 该文件配置API文档模块的路由，将API请求映射到对应的控制器方法
 */

package routers

import (
	"fmt"
	//"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/apidoc/controllers"
)

// InitApiDocRouter 初始化API文档模块路由
func InitApiDocRouter() {
	// 获取已初始化的控制器实例
	apiDocController, ok := controllers.GetApiDocController()
	if !ok || apiDocController == nil {
		panic("API文档控制器未初始化")
	}
	// 获取配置
	apiPrefix, _ := web.AppConfig.String("api_prefix")
	apiVersion, _ := web.AppConfig.String("api_version")

	// 设置默认值
	if apiPrefix == "" {
		apiPrefix = "api"
	}
	if apiVersion == "" {
		apiVersion = "v1"
	}

	// 创建命名空间
	apiDocPath := fmt.Sprintf("/%s/%s/apidoc", apiPrefix, apiVersion)
	apiDocNS := web.NewNamespace(apiDocPath,
		web.NSRouter("/apis", apiDocController, "post:CreateAPI;get:ListAPIs"),
		web.NSRouter("/apis/:id", apiDocController, "get:GetAPI;put:UpdateAPI;delete:DeleteAPI"),
		web.NSRouter("/apis/:id/dto", apiDocController, "get:GetAPIWithDTO"),
		web.NSRouter("/apis/module/:module", apiDocController, "get:GetAPIsByModule"),
		web.NSRouter("/apis/page/:pageName", apiDocController, "get:GetAPIsByPageName"),

		web.NSRouter("/dtos", apiDocController, "post:CreateDTO;get:ListDTOs"),
		web.NSRouter("/dtos/:id", apiDocController, "get:GetDTO;put:UpdateDTO;delete:DeleteDTO"),
		web.NSRouter("/dtos/name/:name", apiDocController, "get:GetDTOByName"),
		web.NSRouter("/dtos/module/:module", apiDocController, "get:GetDTOsByModule"),
		web.NSRouter("/dtos/module/:module/name/:name", apiDocController, "get:GetDTOByModuleAndName"),

		web.NSRouter("/modules", apiDocController, "get:GetAllModules"),
		web.NSRouter("/api-pages", apiDocController, "get:GetPageNamesByModule"),
		web.NSRouter("/api-pages/:module", apiDocController, "get:GetPageNamesByModule"),

		web.NSRouter("/cache", apiDocController, "delete:ClearCache"),

		// 控制器相关的API路由
		web.NSRouter("/controllers", apiDocController, "get:ListControllers"),
		web.NSRouter("/controllers/:id", apiDocController, "get:GetController"),
		web.NSRouter("/controllers/module/:module", apiDocController, "get:GetControllersByModule"),
	)

	// 将命名空间注册到beego
	web.AddNamespace(apiDocNS)
}
