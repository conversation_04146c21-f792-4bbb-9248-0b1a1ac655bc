/**
 * API文档服务接口
 *
 * 该文件定义了API文档模块的业务服务接口
 * 包括API信息和DTO信息的管理、查询和同步等功能
 */

package services

import (
	"context"

	"o_mall_backend/modules/apidoc/dto"
	"o_mall_backend/modules/apidoc/models"
	//"o_mall_backend/modules/apidoc/repositories"
)

// ApiService API文档服务接口
type ApiService interface {
	// CreateAPI 创建API信息
	CreateAPI(ctx context.Context, req *dto.CreateAPIRequest) (int64, error)

	// GetAPIByID 根据ID获取API信息
	GetAPIByID(ctx context.Context, id int64) (*dto.APIResponse, error)

	// GetAPIWithDTO 获取API信息及其关联的DTO详情
	GetAPIWithDTO(ctx context.Context, id int64) (*dto.APIWithDTOResponse, error)

	// UpdateAPI 更新API信息
	UpdateAPI(ctx context.Context, req *dto.UpdateAPIRequest) error

	// DeleteAPI 删除API信息
	DeleteAPI(ctx context.Context, id int64) error

	// ListAPIs 获取API列表
	ListAPIs(ctx context.Context, req *dto.APIQueryRequest) ([]*models.ApiInfo, int64, error)

	// GetAPIList 获取API原始数据列表
	// GetAPIList(ctx context.Context, module, path, method, name string, requireAuth *bool, page, pageSize int) ([]*models.ApiInfo, int64, error)

	// GetAPIsByModule 获取指定模块的所有API
	GetAPIsByModule(ctx context.Context, module string) ([]*dto.APIResponse, error)

	// CreateDTO 创建DTO信息
	CreateDTO(ctx context.Context, req *dto.CreateDTORequest) (int64, error)

	// GetDTOByID 根据ID获取DTO信息
	GetDTOByID(ctx context.Context, id int64) (*dto.DTOResponse, error)

	// GetDTOByName 根据名称获取DTO信息
	GetDTOByName(ctx context.Context, name string) (*dto.DTOResponse, error)

	// GetDTOByModuleAndName 根据模块和名称获取DTO信息
	GetDTOByModuleAndName(ctx context.Context, module string, name string) (*dto.DTOResponse, error)

	// UpdateDTO 更新DTO信息
	UpdateDTO(ctx context.Context, req *dto.UpdateDTORequest) error

	// DeleteDTO 删除DTO信息
	DeleteDTO(ctx context.Context, id int64) error

	// ListDTOs 获取DTO列表
	ListDTOs(ctx context.Context, req *dto.DTOQueryRequest) ([]*dto.DTOResponse, int64, error)

	// GetDTOsByModule 获取指定模块的所有DTO
	GetDTOsByModule(ctx context.Context, module string) ([]*dto.DTOResponse, error)

	// GetAllModules 获取所有模块信息
	GetAllModules(ctx context.Context) ([]*dto.ModuleListResponse, error)

	// SyncModuleAPI 同步指定模块的API和DTO信息
	SyncModuleAPI(ctx context.Context, module string, forceUpdate bool) error

	// ClearCache 清除缓存
	ClearCache(ctx context.Context, module string) error

	// SyncModuleDTOs 同步指定模块的DTO信息
	SyncModuleDTOs(ctx context.Context, module string, forceUpdate bool) error

	// SyncModuleControllers 同步指定模块的控制器信息
	SyncModuleControllers(ctx context.Context, module string, forceUpdate bool) error

	// GetAPIsByPageName 根据页面名称获取API列表
	GetAPIsByPageName(ctx context.Context, pageName string) ([]*models.ApiInfo, error)

	// GetPageNamesByModule 获取指定模块的所有页面名称
	GetPageNamesByModule(ctx context.Context, module string) (*dto.GetPageNamesByModuleResponse, error)

	// GetController 根据ID获取控制器信息
	GetController(ctx context.Context, id int64) (*models.ControllerInfo, error)

	// ListControllers 获取控制器列表
	ListControllers(ctx context.Context, req *dto.ControllerQueryRequest) ([]*models.ControllerInfo, int64, error)

	// GetControllersByModule 获取指定模块的所有控制器
	GetControllersByModule(ctx context.Context, module string) ([]*models.ControllerInfo, error)
}
