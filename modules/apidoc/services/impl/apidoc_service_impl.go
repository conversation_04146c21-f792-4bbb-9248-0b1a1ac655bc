/**
 * API文档服务实现
 *
 * 该文件实现了API文档模块的业务服务接口
 * 包括API信息和DTO信息的管理与查询功能
 */

package impl

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/apidoc/constants"
	"o_mall_backend/modules/apidoc/dto"
	"o_mall_backend/modules/apidoc/models"
	"o_mall_backend/modules/apidoc/repositories"
	"o_mall_backend/utils/redis"
)

// apiServiceImpl 实现ApiService接口
type apiServiceImpl struct {
	apiRepo repositories.ApiRepository
}

// NewApiService 创建API文档服务实例
func NewApiService(apiRepo repositories.ApiRepository) *apiServiceImpl {
	return &apiServiceImpl{
		apiRepo: apiRepo,
	}
}

// CreateAPI 创建API信息
func (s *apiServiceImpl) CreateAPI(ctx context.Context, req *dto.CreateAPIRequest) (int64, error) {
	// 检查路径和方法是否已存在
	existingAPI, err := s.apiRepo.GetAPIByPathMethod(ctx, req.Path, req.Method)
	if err != nil {
		return 0, err
	}
	if existingAPI != nil {
		return 0, fmt.Errorf("指定路径和方法的API已存在")
	}

	// 创建API信息
	apiInfo := &models.ApiInfo{
		Module:         req.Module,
		Path:           req.Path,
		Method:         req.Method,
		Name:           req.Name,
		Description:    req.Description,
		RequireAuth:    req.RequireAuth,
		Permissions:    req.Permissions,
		RequestDTO:     req.RequestDTO,
		ResponseDTO:    req.ResponseDTO,
		ControllerName: req.ControllerName,
		ActName:        req.ActName,
		Status:         constants.APIStatusEnabled,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	id, err := s.apiRepo.CreateAPI(ctx, apiInfo)
	if err != nil {
		return 0, err
	}

	// 清除模块缓存
	s.clearModuleCache(ctx, req.Module)

	return id, nil
}

// GetAPIByID 根据ID获取API信息
func (s *apiServiceImpl) GetAPIByID(ctx context.Context, id int64) (*dto.APIResponse, error) {
	api, err := s.apiRepo.GetAPIByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if api == nil {
		return nil, nil
	}

	return s.convertToAPIResponse(api), nil
}

// GetAPIWithDTO 获取API信息及其关联的DTO详情
func (s *apiServiceImpl) GetAPIWithDTO(ctx context.Context, id int64) (*dto.APIWithDTOResponse, error) {
	// 获取API信息
	api, err := s.apiRepo.GetAPIByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if api == nil {
		return nil, nil
	}

	result := &dto.APIWithDTOResponse{
		API: *s.convertToAPIResponse(api),
	}

	// 获取请求DTO信息
	if api.RequestDTO != "" {
		// 先尝试使用模块特定查询
		// 假设请求DTO通常在同一模块下
		requestDTO, err := s.apiRepo.GetDTOByModuleAndName(ctx, api.Module, api.RequestDTO)
		if err != nil || requestDTO == nil {
			// 如果找不到，尝试使用原始方法（可能返回任何模块中的同名DTO）
			logs.Info("[GetAPIWithDTO] 在模块 %s 中未找到 RequestDTO: %s, 尝试跨模块查找", api.Module, api.RequestDTO)
			requestDTO, err = s.apiRepo.GetDTOByName(ctx, api.RequestDTO)
		}

		if err != nil {
			logs.Warn("[GetAPIWithDTO] 获取请求DTO信息失败: %v", err)
		} else if requestDTO != nil {
			dtoResp, err := s.convertToDTOResponse(requestDTO)
			if err != nil {
				logs.Warn("[GetAPIWithDTO] 转换请求DTO信息失败: %v", err)
			} else {
				result.RequestDTO = dtoResp
			}
		}
	}

	// 获取响应DTO信息
	if api.ResponseDTO != "" {
		// 先尝试使用模块特定查询
		// 假设响应DTO通常在同一模块下
		responseDTO, err := s.apiRepo.GetDTOByModuleAndName(ctx, api.Module, api.ResponseDTO)
		if err != nil || responseDTO == nil {
			// 如果找不到，尝试使用原始方法（可能返回任何模块中的同名DTO）
			logs.Info("[GetAPIWithDTO] 在模块 %s 中未找到 ResponseDTO: %s, 尝试跨模块查找", api.Module, api.ResponseDTO)
			responseDTO, err = s.apiRepo.GetDTOByName(ctx, api.ResponseDTO)
		}

		if err != nil {
			logs.Warn("[GetAPIWithDTO] 获取响应DTO信息失败: %v", err)
		} else if responseDTO != nil {
			dtoResp, err := s.convertToDTOResponse(responseDTO)
			if err != nil {
				logs.Warn("[GetAPIWithDTO] 转换响应DTO信息失败: %v", err)
			} else {
				result.ResponseDTO = dtoResp
			}
		}
	}

	return result, nil
}

// UpdateAPI 更新API信息
func (s *apiServiceImpl) UpdateAPI(ctx context.Context, req *dto.UpdateAPIRequest) error {
	// 获取原有API信息
	api, err := s.apiRepo.GetAPIByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if api == nil {
		return fmt.Errorf("API不存在")
	}

	// 更新API信息
	api.Name = req.Name
	api.Description = req.Description
	api.RequireAuth = req.RequireAuth
	api.Permissions = req.Permissions
	api.RequestDTO = req.RequestDTO
	api.ResponseDTO = req.ResponseDTO
	api.ControllerName = req.ControllerName
	api.ActName = req.ActName
	api.Status = req.Status
	api.UpdatedAt = time.Now()

	err = s.apiRepo.UpdateAPI(ctx, api)
	if err != nil {
		return err
	}

	// 清除缓存
	s.clearModuleCache(ctx, api.Module)

	return nil
}

// DeleteAPI 删除API信息
func (s *apiServiceImpl) DeleteAPI(ctx context.Context, id int64) error {
	// 获取API信息
	api, err := s.apiRepo.GetAPIByID(ctx, id)
	if err != nil {
		return err
	}
	if api == nil {
		return fmt.Errorf("API不存在")
	}

	module := api.Module

	// 删除API
	err = s.apiRepo.DeleteAPI(ctx, id)
	if err != nil {
		return err
	}

	// 清除缓存
	s.clearModuleCache(ctx, module)

	return nil
}

// ListAPIs 获取API列表
func (s *apiServiceImpl) ListAPIs(ctx context.Context, req *dto.APIQueryRequest) ([]*models.ApiInfo, int64, error) {

	// 调用GetAPIList获取原始数据和总数
	apis, total, err := s.apiRepo.ListAPIs(ctx, req.Module, req.Path, req.Method, req.Name, req.RequireAuth, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	// 服务层不再进行DTO转换，直接返回原始数据
	return apis, total, nil
}

// GetAPIsByModule 获取指定模块的所有API
func (s *apiServiceImpl) GetAPIsByModule(ctx context.Context, module string) ([]*dto.APIResponse, error) {
	// 先尝试从缓存获取
	cacheKey := fmt.Sprintf(models.ApiListCacheKey, module)
	cacheData, err := redis.Get(cacheKey)
	if err == nil && cacheData != "" {
		var cachedAPIs []*dto.APIResponse
		err = json.Unmarshal([]byte(cacheData), &cachedAPIs)
		if err == nil {
			logs.Info("[GetAPIsByModule] 从缓存获取模块API列表: %s", module)
			return cachedAPIs, nil
		}
		logs.Warn("[GetAPIsByModule] 解析缓存数据失败: %v", err)
	}

	// 从数据库获取
	apis, err := s.apiRepo.GetAPIsByModule(ctx, module)
	if err != nil {
		return nil, err
	}

	result := make([]*dto.APIResponse, 0, len(apis))
	for _, api := range apis {
		result = append(result, s.convertToAPIResponse(api))
	}

	// 存入缓存
	if len(result) > 0 {
		cacheData, err := json.Marshal(result)
		if err == nil {
			redis.Set(cacheKey, string(cacheData), models.CacheExpiration)
		}
	}

	return result, nil
}

// CreateDTO 创建DTO信息
func (s *apiServiceImpl) CreateDTO(ctx context.Context, req *dto.CreateDTORequest) (int64, error) {
	// 检查DTO名称是否已存在
	existingDTO, err := s.apiRepo.GetDTOByName(ctx, req.Name)
	if err != nil {
		return 0, err
	}
	if existingDTO != nil {
		return 0, fmt.Errorf("指定名称的DTO已存在")
	}

	// 验证DTO结构是否为有效的JSON
	var structureObj interface{}
	err = json.Unmarshal([]byte(req.Structure), &structureObj)
	if err != nil {
		return 0, fmt.Errorf("DTO结构不是有效的JSON: %v", err)
	}

	// 创建DTO信息
	dtoInfo := &models.DTOInfo{
		Module:      req.Module,
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Structure:   req.Structure,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	id, err := s.apiRepo.CreateDTO(ctx, dtoInfo)
	if err != nil {
		return 0, err
	}

	// 清除缓存
	s.clearDTOCache(ctx, req.Module, req.Name)
	s.clearModuleCache(ctx, req.Module)

	return id, nil
}

// GetDTOByID 根据ID获取DTO信息
func (s *apiServiceImpl) GetDTOByID(ctx context.Context, id int64) (*dto.DTOResponse, error) {
	dtoInfo, err := s.apiRepo.GetDTOByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if dtoInfo == nil {
		return nil, nil
	}

	return s.convertToDTOResponse(dtoInfo)
}

// GetDTOByName 根据名称获取DTO信息
func (s *apiServiceImpl) GetDTOByName(ctx context.Context, name string) (*dto.DTOResponse, error) {
	// 添加警告日志，提醒调用者该方法已不推荐使用
	logs.Warn("[GetDTOByName] 警告：此方法已过时，可能会返回任何模块中的同名DTO。请考虑使用GetDTOByModuleAndName代替")

	// 为向后兼容，我们首先尝试使用GetDTOByName方法获取任何模块中匹配该名称的第一个DTO
	// 但这可能返回任何模块中的同名DTO，不保证是哪个模块的

	// 从数据库获取
	dtoInfo, err := s.apiRepo.GetDTOByName(ctx, name)
	if err != nil {
		return nil, err
	}
	if dtoInfo == nil {
		return nil, nil
	}

	// 存在DTO，现在使用特定的模块+名称缓存
	cacheKey := fmt.Sprintf(models.DTOInfoCacheKey, dtoInfo.Module, dtoInfo.Name)
	cacheData, err := redis.Get(cacheKey)
	if err == nil && cacheData != "" {
		var cachedDTO dto.DTOResponse
		err = json.Unmarshal([]byte(cacheData), &cachedDTO)
		if err == nil {
			logs.Info("[GetDTOByName] 从缓存获取DTO信息: %s.%s", dtoInfo.Module, dtoInfo.Name)
			return &cachedDTO, nil
		}
		logs.Warn("[GetDTOByName] 解析缓存数据失败: %v", err)
	}

	response, err := s.convertToDTOResponse(dtoInfo)
	if err != nil {
		return nil, err
	}

	// 存入缓存
	jsonData, err := json.Marshal(response)
	if err == nil {
		redis.Set(cacheKey, string(jsonData), models.CacheExpiration)
	}

	return response, nil
}

// UpdateDTO 更新DTO信息
func (s *apiServiceImpl) UpdateDTO(ctx context.Context, req *dto.UpdateDTORequest) error {
	// 获取原有DTO信息
	dtoInfo, err := s.apiRepo.GetDTOByID(ctx, req.ID)
	if err != nil {
		return err
	}
	if dtoInfo == nil {
		return fmt.Errorf("DTO不存在")
	}

	// 验证DTO结构是否为有效的JSON
	var structureObj interface{}
	err = json.Unmarshal([]byte(req.Structure), &structureObj)
	if err != nil {
		return fmt.Errorf("DTO结构不是有效的JSON: %v", err)
	}

	// 更新DTO信息
	dtoInfo.Description = req.Description
	dtoInfo.Structure = req.Structure
	dtoInfo.UpdatedAt = time.Now()

	err = s.apiRepo.UpdateDTO(ctx, dtoInfo)
	if err != nil {
		return err
	}

	// 清除缓存
	s.clearDTOCache(ctx, dtoInfo.Module, dtoInfo.Name)
	s.clearModuleCache(ctx, dtoInfo.Module)

	return nil
}

// DeleteDTO 删除DTO信息
func (s *apiServiceImpl) DeleteDTO(ctx context.Context, id int64) error {
	// 获取DTO信息
	dtoInfo, err := s.apiRepo.GetDTOByID(ctx, id)
	if err != nil {
		return err
	}
	if dtoInfo == nil {
		return fmt.Errorf("DTO不存在")
	}

	// 检查是否有API使用了该DTO
	apis, _, err := s.apiRepo.ListAPIs(ctx, "", "", "", "", nil, 0, 0)
	if err != nil {
		return err
	}

	for _, api := range apis {
		if api.RequestDTO == dtoInfo.Name || api.ResponseDTO == dtoInfo.Name {
			return fmt.Errorf("该DTO正在被API使用，无法删除")
		}
	}

	module := dtoInfo.Module
	name := dtoInfo.Name

	// 删除DTO
	err = s.apiRepo.DeleteDTO(ctx, id)
	if err != nil {
		return err
	}

	// 清除缓存
	s.clearDTOCache(ctx, module, name)
	s.clearModuleCache(ctx, module)

	return nil
}

// ListDTOs 获取DTO列表
func (s *apiServiceImpl) ListDTOs(ctx context.Context, req *dto.DTOQueryRequest) ([]*dto.DTOResponse, int64, error) {
	dtos, total, err := s.apiRepo.ListDTOs(ctx, req.Module, req.Name, req.Type, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	result := make([]*dto.DTOResponse, 0, len(dtos))
	for _, d := range dtos {
		resp, err := s.convertToDTOResponse(d)
		if err != nil {
			logs.Warn("[ListDTOs] 转换DTO响应失败: %v", err)
			continue
		}
		result = append(result, resp)
	}

	return result, total, nil
}

// GetDTOsByModule 获取指定模块的所有DTO
func (s *apiServiceImpl) GetDTOsByModule(ctx context.Context, module string) ([]*dto.DTOResponse, error) {
	dtos, err := s.apiRepo.GetDTOsByModule(ctx, module)
	if err != nil {
		return nil, err
	}

	result := make([]*dto.DTOResponse, 0, len(dtos))
	for _, d := range dtos {
		resp, err := s.convertToDTOResponse(d)
		if err != nil {
			logs.Warn("[GetDTOsByModule] 转换DTO响应失败: %v", err)
			continue
		}
		result = append(result, resp)
	}

	return result, nil
}

// GetAllModules 获取所有模块信息
func (s *apiServiceImpl) GetAllModules(ctx context.Context) ([]*dto.ModuleListResponse, error) {
	// 先尝试从缓存获取
	cacheData, err := redis.Get(models.ModuleListCacheKey)
	if err == nil && cacheData != "" {
		var cachedModules []*dto.ModuleListResponse
		err = json.Unmarshal([]byte(cacheData), &cachedModules)
		if err == nil {
			logs.Info("[GetAllModules] 从缓存获取模块列表")
			return cachedModules, nil
		}
		logs.Warn("[GetAllModules] 解析缓存数据失败: %v", err)
	}

	// 从数据库获取所有模块名称
	modules, err := s.apiRepo.GetAllModules(ctx)
	if err != nil {
		return nil, err
	}

	result := make([]*dto.ModuleListResponse, 0, len(modules))
	for _, module := range modules {
		// 获取API数量
		apiCount, err := s.apiRepo.CountAPIsByModule(ctx, module)
		if err != nil {
			logs.Warn("[GetAllModules] 统计模块API数量失败: %v", err)
			apiCount = 0
		}

		// 获取DTO数量
		dtoCount, err := s.apiRepo.CountDTOsByModule(ctx, module)
		if err != nil {
			logs.Warn("[GetAllModules] 统计模块DTO数量失败: %v", err)
			dtoCount = 0
		}

		result = append(result, &dto.ModuleListResponse{
			Name:     module,
			APICount: apiCount,
			DTOCount: dtoCount,
		})
	}

	// 存入缓存
	if len(result) > 0 {
		cacheData, err := json.Marshal(result)
		if err == nil {
			redis.Set(models.ModuleListCacheKey, string(cacheData), models.CacheExpiration)
		}
	}

	return result, nil
}

// SyncModuleAPI 同步指定模块的API和DTO信息
func (s *apiServiceImpl) SyncModuleAPI(ctx context.Context, module string, forceUpdate bool) error {
	logs.Info("[SyncModuleAPI] 开始同步模块API信息: %s", module)

	// 解析模块路由文件获取API信息
	apis, err := s.parseModuleRouters(module)
	if err != nil {
		logs.Error("[SyncModuleAPI] 解析模块路由失败: %v", err)
		return fmt.Errorf("解析模块路由失败: %v", err)
	}

	logs.Info("[SyncModuleAPI] 解析到 %d 个API信息", len(apis))

	// 遍历解析得到的API信息，逐个同步到数据库
	for _, api := range apis {
		// 检查路径和方法是否已存在
		existingAPI, err := s.apiRepo.GetAPIByPathMethod(ctx, api.Path, api.Method)
		if err != nil {
			logs.Error("[SyncModuleAPI] 查询API失败: %v", err)
			continue
		}

		// 在更新现有API的代码部分
		if existingAPI != nil {
			// 如果API已存在且不强制更新，则跳过
			if !forceUpdate {
				logs.Info("[SyncModuleAPI] API已存在，跳过: %s %s", api.Method, api.Path)
				continue
			}

			// 更新现有API信息
			existingAPI.Name = api.Name
			existingAPI.Description = api.Description
			existingAPI.RequireAuth = api.RequireAuth
			existingAPI.Permissions = api.Permissions
			existingAPI.ControllerName = api.ControllerName
			existingAPI.ActName = api.ActName
			existingAPI.PageName = api.PageName // 添加PageName字段更新
			existingAPI.UpdatedAt = time.Now()

			err = s.apiRepo.UpdateAPI(ctx, existingAPI)
			if err != nil {
				logs.Error("[SyncModuleAPI] 更新API失败: %v", err)
				continue
			}
			logs.Info("[SyncModuleAPI] 已更新API: %s %s", api.Method, api.Path)
		} else {
			// 新增API信息
			api.Status = constants.APIStatusEnabled
			api.CreatedAt = time.Now()
			api.UpdatedAt = time.Now()

			_, err = s.apiRepo.CreateAPI(ctx, api)
			if err != nil {
				logs.Error("[SyncModuleAPI] 创建API失败: %v", err)
				continue
			}
			logs.Info("[SyncModuleAPI] 已创建API: %s %s", api.Method, api.Path)
		}
	}

	// 清除模块缓存
	s.clearModuleCache(ctx, module)

	logs.Info("[SyncModuleAPI] 模块API信息同步完成: %s", module)
	return nil
}

// parseModuleRouters 解析模块路由文件获取API信息
func (s *apiServiceImpl) parseModuleRouters(module string) ([]*models.ApiInfo, error) {
	var apis []*models.ApiInfo

	// 构建路由文件路径
	routerPath := filepath.Join("modules", module, "routers", "router.go")

	// 检查文件是否存在
	_, err := os.Stat(routerPath)
	if os.IsNotExist(err) {
		return nil, fmt.Errorf("模块路由文件不存在: %s", routerPath)
	}

	// 打开文件
	file, err := os.Open(routerPath)
	if err != nil {
		return nil, fmt.Errorf("打开路由文件失败: %v", err)
	}
	defer file.Close()

	// 读取文件内容
	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取路由文件失败: %v", err)
	}

	// 创建一个map用于存储变量和控制器类型的映射
	controllerMap := make(map[string]string)

	// 首先扫描文件查找控制器变量定义
	controllerDefPattern := regexp.MustCompile(`(\w+)\s*:=\s*&controllers\.(\w+){}`)

	for _, line := range lines {
		matches := controllerDefPattern.FindStringSubmatch(line)
		if len(matches) > 2 {
			varName := matches[1]        // 例如 apiDocController
			controllerType := matches[2] // 例如 ApiDocController
			controllerMap[varName] = controllerType
		}
	}

	// 接下来解析路由定义
	// 首先找到命名空间定义
	var baseNamespace string
	nsPattern := regexp.MustCompile(`web\.NewNamespace\("([^"]+)"`)

	// 用于匹配路由定义的正则
	routerPattern := regexp.MustCompile(`NSRouter\("([^"]+)",\s*(?:&controllers\.(\w+){}|(\w+)),\s*"([^"]+)"`)

	// 用于匹配需要认证的路径
	securedPattern := regexp.MustCompile(`NSNamespace\("(/secured[^"]*)"`)

	// 处理命名空间嵌套
	var namespaces []string
	var isSecured bool

	for _, line := range lines {
		// 查找基础命名空间
		nsMatch := nsPattern.FindStringSubmatch(line)
		if len(nsMatch) > 1 && baseNamespace == "" {
			baseNamespace = nsMatch[1]
			namespaces = append(namespaces, baseNamespace)
			continue
		}

		// 查找嵌套命名空间（例如secured子路径）
		securedMatch := securedPattern.FindStringSubmatch(line)
		if len(securedMatch) > 1 {
			// 进入需要认证的命名空间
			nestedNS := securedMatch[1]
			namespaces = append(namespaces, nestedNS)
			isSecured = true
			continue
		}

		// 如果发现命名空间结束（通常是右括号），则移除最后一个命名空间
		if strings.TrimSpace(line) == ")," && len(namespaces) > 1 {
			// 检查是否退出了secured命名空间
			if namespaces[len(namespaces)-1] == "/secured" {
				isSecured = false
			}
			namespaces = namespaces[:len(namespaces)-1]
			continue
		}

		// 查找路由定义
		routerMatch := routerPattern.FindStringSubmatch(line)
		if len(routerMatch) > 4 {
			path := routerMatch[1]

			// 确定控制器名称
			var controllerType string
			if routerMatch[2] != "" {
				// 如果是直接使用 &controllers.XxxController{} 形式
				controllerType = routerMatch[2]
			} else if routerMatch[3] != "" {
				// 如果是使用变量形式，从映射中查找
				varName := routerMatch[3]
				if typeName, ok := controllerMap[varName]; ok {
					controllerType = typeName
				} else {
					// 如果映射中没有找到，使用变量名作为备选
					controllerType = varName
				}
			}

			// 转换控制器名称为下划线形式
			controllerName := s.camelToSnake(controllerType)

			// 处理method:handler对
			methodHandlers := routerMatch[4]
			for _, methodHandler := range strings.Split(methodHandlers, ";") {
				parts := strings.Split(methodHandler, ":")
				if len(parts) != 2 {
					continue
				}

				httpMethodStr := parts[0] // 例如 post, get
				handlerName := parts[1]   // 例如 Login, CreateAPI

				httpMethod := strings.ToUpper(httpMethodStr)

				// 构建完整路径
				fullPath := strings.Join(namespaces, "")
				if !strings.HasSuffix(fullPath, "/") && !strings.HasPrefix(path, "/") {
					fullPath += "/"
				}
				fullPath += path

				// 构建API名称和描述
				name := handlerName
				description := fmt.Sprintf("%s %s", httpMethod, fullPath)

				// 提取PageName - 只对GET方法且路径中不包含参数的情况
				var pageName string
				if httpMethod == "GET" && !strings.Contains(path, ":") {
					// 获取路径的最后一部分作为pageName
					pathParts := strings.Split(strings.TrimSuffix(path, "/"), "/")
					if len(pathParts) > 0 {
						pageName = pathParts[len(pathParts)-1]
					}
				}

				// 创建API信息
				api := &models.ApiInfo{
					Module:         module,
					Path:           fullPath,
					Method:         httpMethod,
					Name:           name,
					Description:    description,
					RequireAuth:    isSecured, // 在secured命名空间下的API需要认证
					ControllerName: controllerName,
					ActName:        handlerName,
					PageName:       pageName, // 添加PageName字段
				}

				apis = append(apis, api)
			}
		}
	}

	return apis, nil
}

// camelToSnake 将驼峰命名转换为下划线命名
// 例如：ApiDocController -> api_doc_controller
func (s *apiServiceImpl) camelToSnake(name string) string {
	var result strings.Builder
	for i, r := range name {
		if unicode.IsUpper(r) {
			if i > 0 {
				result.WriteRune('_')
			}
			result.WriteRune(unicode.ToLower(r))
		} else {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// ClearCache 清除缓存
func (s *apiServiceImpl) ClearCache(ctx context.Context, module string) error {
	// 清除模块缓存
	if module != "" {
		s.clearModuleCache(ctx, module)
	} else {
		// 清除所有模块的缓存
		modules, err := s.apiRepo.GetAllModules(ctx)
		if err != nil {
			return err
		}

		for _, m := range modules {
			s.clearModuleCache(ctx, m)
		}

		// 清除模块列表缓存
		redis.Del(models.ModuleListCacheKey)
	}

	return nil
}

// convertToAPIResponse 将ApiInfo转换为APIResponse
func (s *apiServiceImpl) convertToAPIResponse(api *models.ApiInfo) *dto.APIResponse {
	return &dto.APIResponse{
		ID:             api.ID,
		Module:         api.Module,
		Path:           api.Path,
		Method:         api.Method,
		Name:           api.Name,
		Description:    api.Description,
		RequireAuth:    api.RequireAuth,
		Permissions:    api.Permissions,
		RequestDTO:     api.RequestDTO,
		ResponseDTO:    api.ResponseDTO,
		ControllerName: api.ControllerName,
		ActName:        api.ActName,
		PageName:       api.PageName,
		Status:         api.Status,
		CreatedAt:      api.CreatedAt,
		UpdatedAt:      api.UpdatedAt,
	}
}

// convertToDTOResponse 将DTOInfo转换为DTOResponse
func (s *apiServiceImpl) convertToDTOResponse(dtoInfo *models.DTOInfo) (*dto.DTOResponse, error) {
	var structure interface{}
	err := json.Unmarshal([]byte(dtoInfo.Structure), &structure)
	if err != nil {
		return nil, err
	}

	return &dto.DTOResponse{
		ID:          dtoInfo.ID,
		Module:      dtoInfo.Module,
		Name:        dtoInfo.Name,
		Description: dtoInfo.Description,
		Type:        dtoInfo.Type,
		Structure:   structure,
		CreatedAt:   dtoInfo.CreatedAt,
		UpdatedAt:   dtoInfo.UpdatedAt,
	}, nil
}

// clearModuleCache 清除模块相关缓存
func (s *apiServiceImpl) clearModuleCache(ctx context.Context, module string) {
	// 清除模块API列表缓存
	cacheKey := fmt.Sprintf(models.ApiListCacheKey, module)
	redis.Del(cacheKey)

	// 清除模块列表缓存
	redis.Del(models.ModuleListCacheKey)

	logs.Info("[clearModuleCache] 已清除模块缓存: %s", module)
}

// clearDTOCache 清除DTO缓存
func (s *apiServiceImpl) clearDTOCache(ctx context.Context, module string, dtoName string) {
	cacheKey := fmt.Sprintf(models.DTOInfoCacheKey, module, dtoName)
	redis.Del(cacheKey)
	logs.Info("[clearDTOCache] 已清除DTO缓存: %s.%s", module, dtoName)
}

// SyncModuleDTOs 同步指定模块的DTO信息
func (s *apiServiceImpl) SyncModuleDTOs(ctx context.Context, module string, forceUpdate bool) error {
	logs.Info("[SyncModuleDTOs] 开始同步模块DTO信息: %s", module)

	// 解析模块DTO文件获取DTO信息
	dtos, err := s.parseModuleDTOs(module)
	if err != nil {
		logs.Error("[SyncModuleDTOs] 解析模块DTO失败: %v", err)
		return fmt.Errorf("解析模块DTO失败: %v", err)
	}

	logs.Info("[SyncModuleDTOs] 解析到 %d 个DTO信息", len(dtos))

	// 遍历解析得到的DTO信息，逐个同步到数据库
	for _, dto := range dtos {
		// 检查DTO名称和模块是否已存在，使用模块和名称的组合来唯一标识DTO
		existingDTO, err := s.apiRepo.GetDTOByModuleAndName(ctx, dto.Module, dto.Name)
		if err != nil {
			logs.Error("[SyncModuleDTOs] 查询DTO失败: %v", err)
			continue
		}

		if existingDTO != nil {
			// 如果DTO已存在且不强制更新，则跳过
			if !forceUpdate {
				logs.Info("[SyncModuleDTOs] DTO已存在，跳过: %s", dto.Name)
				continue
			}

			// 更新现有DTO信息
			existingDTO.Description = dto.Description
			existingDTO.Structure = dto.Structure
			existingDTO.UpdatedAt = time.Now()

			err = s.apiRepo.UpdateDTO(ctx, existingDTO)
			if err != nil {
				logs.Error("[SyncModuleDTOs] 更新DTO失败: %v", err)
				continue
			}
			logs.Info("[SyncModuleDTOs] 已更新DTO: %s", dto.Name)
		} else {
			// 新增DTO信息
			dto.CreatedAt = time.Now()
			dto.UpdatedAt = time.Now()

			_, err = s.apiRepo.CreateDTO(ctx, dto)
			if err != nil {
				logs.Error("[SyncModuleDTOs] 创建DTO失败: %v", err)
				continue
			}
			logs.Info("[SyncModuleDTOs] 已创建DTO: %s", dto.Name)
		}

		// 清除DTO缓存
		s.clearDTOCache(ctx, dto.Module, dto.Name)
	}

	// 清除模块缓存
	s.clearModuleCache(ctx, module)

	logs.Info("[SyncModuleDTOs] 模块DTO信息同步完成: %s", module)
	return nil
}

// parseModuleDTOs 解析模块DTO文件获取DTO信息
func (s *apiServiceImpl) parseModuleDTOs(module string) ([]*models.DTOInfo, error) {
	var dtos []*models.DTOInfo

	// 构建DTO文件夹路径
	dtoPath := filepath.Join("modules", module, "dto")

	// 检查文件夹是否存在
	_, err := os.Stat(dtoPath)
	if os.IsNotExist(err) {
		logs.Warn("[parseModuleDTOs] 模块DTO文件夹不存在: %s", dtoPath)
		return nil, nil // 返回空列表而不是错误，因为可能有些模块没有DTO
	}

	// 遍历DTO文件夹下的所有.go文件
	err = filepath.Walk(dtoPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录和非Go文件
		if info.IsDir() || !strings.HasSuffix(info.Name(), ".go") {
			return nil
		}

		// 解析单个DTO文件
		fileDTOs, err := s.parseDTOFile(module, path)
		if err != nil {
			logs.Error("[parseModuleDTOs] 解析DTO文件失败 %s: %v", path, err)
			return nil // 继续处理其他文件
		}

		dtos = append(dtos, fileDTOs...)
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("遍历DTO文件夹失败: %v", err)
	}

	return dtos, nil
}

// parseDTOFile 解析单个DTO文件
func (s *apiServiceImpl) parseDTOFile(module, filePath string) ([]*models.DTOInfo, error) {
	var dtos []*models.DTOInfo

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 文件内容转为字符串
	fileContent := string(content)

	// 创建一个map用于存储结构体声明
	structMap := make(map[string]bool)

	// 找到所有结构体定义
	structRegex := regexp.MustCompile(`type\s+(\w+)\s+struct\s*{`)
	structMatches := structRegex.FindAllStringSubmatchIndex(fileContent, -1)

	// 先扫描所有结构体名称
	for _, match := range structMatches {
		if len(match) >= 4 {
			// 获取结构体名称
			structNameStart := match[2]
			structNameEnd := match[3]
			structName := fileContent[structNameStart:structNameEnd]
			structMap[structName] = true
		}
	}

	// 现在处理每个结构体定义
	for _, match := range structMatches {
		if len(match) >= 4 {
			// 获取结构体名称和起始位置
			structNameStart := match[2]
			structNameEnd := match[3]
			structStart := match[1] // 结构体关键字'struct'之后的位置

			structName := fileContent[structNameStart:structNameEnd]
			
			// 查找结构体描述 (结构体定义前的注释)
			structDesc := ""
			// 获取结构体定义所在行之前的内容
			beforeStructDef := fileContent[:match[0]]
			lines := strings.Split(beforeStructDef, "\n")
			
			// 从下往上查找注释行
			var commentLines []string
			for i := len(lines) - 1; i >= 0; i-- {
				line := strings.TrimSpace(lines[i])
				if line == "" {
					// 如果遇到空行且已收集到注释，说明注释块结束
					if len(commentLines) > 0 {
						break
					}
					continue
				}
				
				if strings.HasPrefix(line, "//") {
					// 添加到注释集合中（去掉前缀）
					commentLine := strings.TrimSpace(strings.TrimPrefix(line, "//"))
					// 将新注释行添加到列表开头，保持正确顺序
					commentLines = append([]string{commentLine}, commentLines...)
				} else {
					// 遇到非注释行，结束查找
					break
				}
			}
			
			// 合并注释行
			if len(commentLines) > 0 {
				structDesc = strings.Join(commentLines, " ")
				// 移除注释中的星号
				structDesc = regexp.MustCompile(`\s*\*\s*`).ReplaceAllString(structDesc, " ")
				structDesc = strings.TrimSpace(structDesc)
			}

			// 判断DTO类型（请求还是响应）
			dtoType := "common" // 默认为通用类型
			if strings.Contains(structName, "Request") {
				dtoType = "request"
			} else if strings.Contains(structName, "Response") {
				dtoType = "response"
			}

			// 找到结构体主体的结束位置（通过花括号匹配）
			braceCount := 1
			structEnd := -1
			
			// 找到开始花括号的位置
			for i := structStart; i < len(fileContent); i++ {
				if fileContent[i] == '{' {
					structStart = i + 1 // 更新为第一个花括号后的位置
					break
				}
			}
			
			// 然后找到匹配的结束花括号
			for i := structStart; i < len(fileContent); i++ {
				if fileContent[i] == '{' {
					braceCount++
				} else if fileContent[i] == '}' {
					braceCount--
					if braceCount == 0 {
						structEnd = i
						break
					}
				}
			}
			
			if structEnd == -1 {
				logs.Warn("[parseDTOFile] 无法找到结构体 %s 的结束位置", structName)
				continue
			}
			
			// 提取结构体主体（不包括花括号）
			structBody := fileContent[structStart:structEnd]
			
			// 存储每个字段和其描述
			type fieldInfo struct {
				name        string
				fieldType   string
				jsonName    string // JSON标签名
				formName    string // Form标签名
				description string // 字段描述
				validation  string // 验证规则
			}
			
			var fieldInfos []fieldInfo
			var pendingComment string
			
			// 扫描结构体主体中的字段
			bodyLines := strings.Split(structBody, "\n")
			for _, line := range bodyLines {
				line = strings.TrimSpace(line)
				
				// 跳过空行
				if line == "" {
					continue
				}
				
				// 处理注释行
				if strings.HasPrefix(line, "//") {
					pendingComment = strings.TrimSpace(strings.TrimPrefix(line, "//"))
					continue
				}
				
				// 匹配字段定义（带标签的）
				if strings.Contains(line, "`") {
					baseFieldRegex := regexp.MustCompile(`^\s*(\w+)\s+(\w+|\*\w+|\[\]\w+|\[?\]?\*?\w+\.\w+)`)
					baseMatch := baseFieldRegex.FindStringSubmatch(line)
					
					if len(baseMatch) >= 3 {
						fieldName := baseMatch[1]
						fieldType := baseMatch[2]
						
						// 提取标签内容
						tagContent := ""
						tagStart := strings.Index(line, "`")
						tagEnd := strings.LastIndex(line, "`")
						if tagStart != -1 && tagEnd != -1 && tagEnd > tagStart {
							tagContent = line[tagStart+1 : tagEnd]
						}
						
						// 提取各种标签值
						jsonName := s.extractTagValue(tagContent, "json")
						formName := s.extractTagValue(tagContent, "form")
						descriptionValue := s.extractTagValue(tagContent, "description")
						validValue := s.extractTagValue(tagContent, "valid")
						
						// 提取行尾注释
						fieldDesc := ""
						commentIndex := strings.Index(line, "//")
						if commentIndex != -1 {
							fieldDesc = strings.TrimSpace(line[commentIndex+2:])
						} else if pendingComment != "" {
							// 使用前导注释
							fieldDesc = pendingComment
						}
						
						// 如果存在description标签，使用标签值
						if descriptionValue != "" {
							fieldDesc = descriptionValue
						}
						
						// 添加字段信息
						fieldInfos = append(fieldInfos, fieldInfo{
							name:        fieldName,
							fieldType:   fieldType,
							jsonName:    jsonName,
							formName:    formName,
							description: fieldDesc,
							validation:  validValue,
						})
					}
					
					// 重置前导注释
					pendingComment = ""
				}
			}
			
			// 为当前结构体构建字段映射
			fields := make(map[string]interface{})
			for _, info := range fieldInfos {
				// 处理不同的字段类型
				var fieldValue interface{}
				switch {
				case info.fieldType == "string":
					fieldValue = "string"
				case info.fieldType == "int" || info.fieldType == "int32" || info.fieldType == "int64" ||
					info.fieldType == "uint" || info.fieldType == "uint32" || info.fieldType == "uint64":
					fieldValue = "number"
				case info.fieldType == "float32" || info.fieldType == "float64":
					fieldValue = "number"
				case info.fieldType == "bool":
					fieldValue = "boolean"
				case strings.HasPrefix(info.fieldType, "[]"):
					// 数组类型
					baseType := strings.TrimPrefix(info.fieldType, "[]")
					if baseType == "string" {
						fieldValue = []string{}
					} else if baseType == "int" || baseType == "int64" || baseType == "float64" {
						fieldValue = []float64{}
					} else if baseType == "bool" {
						fieldValue = []bool{}
					} else {
						// 自定义结构体数组
						fieldValue = []interface{}{}
					}
				case strings.HasPrefix(info.fieldType, "*") ||
					strings.Contains(info.fieldType, "."):
					// 指针类型或其他类型（如time.Time）
					if info.fieldType == "*time.Time" || info.fieldType == "time.Time" {
						fieldValue = "datetime"
					} else {
						// 引用其他结构体
						fieldValue = "object"
					}
				default:
					// 可能是其他结构体
					fieldValue = "object"
				}
				
				// 存储字段信息
				fieldInfo := map[string]interface{}{
					"type": fieldValue,
				}
				
				// 添加字段描述（如果有）
				if info.description != "" {
					fieldInfo["description"] = info.description
				}
				
				// 如果有validation标签，则添加验证规则
				if info.validation != "" {
					fieldInfo["validation"] = info.validation
				}
				
				// 使用适当的名称作为字段名（优先级：json > form > 字段名的小写形式）
				fieldKey := ""
				if info.jsonName != "" {
					fieldKey = info.jsonName
				} else if info.formName != "" {
					fieldKey = info.formName
				} else {
					// 如果既没有json标签也没有form标签，使用字段名转小写
					fieldKey = strings.ToLower(info.name)
				}
				
				fields[fieldKey] = fieldInfo
			}
			
			// 转换为JSON字符串
			structureJSON, err := json.Marshal(fields)
			if err != nil {
				logs.Error("[parseDTOFile] 转换字段为JSON失败: %v", err)
				continue
			}
			
			// 创建DTO信息
			dtoInfo := &models.DTOInfo{
				Module:      module,
				Name:        structName,
				Description: structDesc,
				Type:        dtoType,
				Structure:   string(structureJSON),
			}
			
			dtos = append(dtos, dtoInfo)
		}
	}

	logs.Info("[parseDTOFile] 从文件 %s 解析到 %d 个DTO结构体", filePath, len(dtos))
	return dtos, nil
}

// extractTagValue 从结构体标签中提取指定键的值
func (s *apiServiceImpl) extractTagValue(tagContent, key string) string {
	re := regexp.MustCompile(key + `:"([^"]+)"`)
	matches := re.FindStringSubmatch(tagContent)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// SyncModuleControllers 同步指定模块的控制器信息
func (s *apiServiceImpl) SyncModuleControllers(ctx context.Context, module string, forceUpdate bool) error {
	logs.Info("[SyncModuleControllers] 开始同步模块控制器信息: %s", module)

	// 解析模块控制器文件获取控制器信息
	controllers, err := s.parseModuleControllers(module)
	if err != nil {
		logs.Error("[SyncModuleControllers] 解析模块控制器失败: %v", err)
		return fmt.Errorf("解析模块控制器失败: %v", err)
	}

	logs.Info("[SyncModuleControllers] 解析到 %d 个控制器方法信息", len(controllers))

	// 遍历解析得到的控制器信息，逐个同步到数据库
	for _, controller := range controllers {
		// 解析UsedDTOIDs中的DTO名称，查询对应的ID
		if controller.UsedDTOIDs != "" {
			var dtoNames []string
			err := json.Unmarshal([]byte(controller.UsedDTOIDs), &dtoNames)
			if err == nil && len(dtoNames) > 0 {
				var dtoIDs []int64

				for _, dtoName := range dtoNames {
					// 从DTO名称中提取模块名和DTO名
					// 例如：adminDto.AdminLoginRequest -> 模块名：admin，DTO名：AdminLoginRequest
					parts := strings.Split(dtoName, ".")
					if len(parts) == 2 {
						// 提取模块名（去除Dto/DTO/dto后缀）
						dtoModuleName := parts[0]
						dtoModuleName = strings.TrimSuffix(dtoModuleName, "Dto")
						dtoModuleName = strings.TrimSuffix(dtoModuleName, "DTO")
						dtoModuleName = strings.TrimSuffix(dtoModuleName, "dto")

						// 获取实际DTO名称
						dtoStructName := parts[1]

						// 使用模块名和DTO名查询数据库获取DTO的ID
						dtoInfo, err := s.apiRepo.GetDTOByModuleAndName(ctx, dtoModuleName, dtoStructName)
						if err != nil {
							logs.Warn("[SyncModuleControllers] 查询DTO失败: 模块=%s, DTO=%s, %v", dtoModuleName, dtoStructName, err)
							continue
						}

						if dtoInfo != nil {
							dtoIDs = append(dtoIDs, dtoInfo.ID)
						} else {
							logs.Warn("[SyncModuleControllers] 未找到DTO: 模块=%s, DTO=%s", dtoModuleName, dtoStructName)
						}
					}
				}

				// 将DTO ID数组转换为JSON字符串
				if len(dtoIDs) > 0 {
					dtoIDsJSON, err := json.Marshal(dtoIDs)
					if err == nil {
						controller.UsedDTOIDs = string(dtoIDsJSON)
					} else {
						logs.Error("[SyncModuleControllers] 序列化DTO ID失败: %v", err)
					}
				} else {
					controller.UsedDTOIDs = "[]" // 空数组
				}
			}
		}

		// 检查控制器名称和方法名是否已存在
		existingController, err := s.apiRepo.GetControllerByNameMethod(ctx, controller.Module, controller.ControllerName, controller.MethodName)
		if err != nil {
			logs.Error("[SyncModuleControllers] 查询控制器失败: %v", err)
			continue
		}

		if existingController != nil {
			// 如果控制器已存在且不强制更新，则跳过
			if !forceUpdate {
				logs.Info("[SyncModuleControllers] 控制器方法已存在，跳过: %s.%s", controller.ControllerName, controller.MethodName)
				continue
			}

			// 更新现有控制器信息
			existingController.Description = controller.Description
			existingController.MethodDescription = controller.MethodDescription
			existingController.UsedDTOIDs = controller.UsedDTOIDs
			existingController.UpdatedAt = time.Now()

			err = s.apiRepo.UpdateController(ctx, existingController)
			if err != nil {
				logs.Error("[SyncModuleControllers] 更新控制器失败: %v", err)
				continue
			}
			logs.Info("[SyncModuleControllers] 已更新控制器方法: %s.%s", controller.ControllerName, controller.MethodName)
		} else {
			// 新增控制器信息
			controller.CreatedAt = time.Now()
			controller.UpdatedAt = time.Now()

			_, err = s.apiRepo.CreateController(ctx, controller)
			if err != nil {
				logs.Error("[SyncModuleControllers] 创建控制器失败: %v", err)
				continue
			}
			logs.Info("[SyncModuleControllers] 已创建控制器方法: %s.%s", controller.ControllerName, controller.MethodName)
		}
	}

	// 清除缓存
	cacheKey := fmt.Sprintf(models.ControllerListCacheKey, module)
	redis.Del(cacheKey)

	logs.Info("[SyncModuleControllers] 模块控制器信息同步完成: %s", module)
	return nil
}

// parseModuleControllers 解析模块控制器文件获取控制器信息
func (s *apiServiceImpl) parseModuleControllers(module string) ([]*models.ControllerInfo, error) {
	var controllers []*models.ControllerInfo

	// 构建controllers文件夹路径
	controllersPath := filepath.Join("modules", module, "controllers")

	// 检查文件夹是否存在
	_, err := os.Stat(controllersPath)
	if os.IsNotExist(err) {
		logs.Warn("[parseModuleControllers] 模块控制器文件夹不存在: %s", controllersPath)
		return nil, nil // 返回空列表而不是错误，因为可能有些模块没有控制器
	}

	// 遍历controllers文件夹下的所有.go文件
	err = filepath.Walk(controllersPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录和非Go文件
		if info.IsDir() || !strings.HasSuffix(info.Name(), ".go") {
			return nil
		}

		// 解析单个控制器文件
		fileControllers, err := s.parseControllerFile(module, path)
		if err != nil {
			logs.Error("[parseModuleControllers] 解析控制器文件失败 %s: %v", path, err)
			return nil // 继续处理其他文件
		}

		controllers = append(controllers, fileControllers...)
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("遍历控制器文件夹失败: %v", err)
	}

	return controllers, nil
}

// parseControllerFile 解析单个控制器文件
func (s *apiServiceImpl) parseControllerFile(module, filePath string) ([]*models.ControllerInfo, error) {
	var controllers []*models.ControllerInfo

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 文件内容转为字符串
	fileContent := string(content)

	// 从文件名提取控制器名称
	controllerFileName := strings.TrimSuffix(filepath.Base(filePath), ".go")

	// 控制器描述正则，匹配文件开头的注释
	controllerDescRegex := regexp.MustCompile(`(?ms)\/\*\*\s*(.*?)\s*\*\/\s*package`)
	controllerDescMatch := controllerDescRegex.FindStringSubmatch(fileContent)
	controllerDesc := ""
	if len(controllerDescMatch) > 1 {
		controllerDesc = strings.TrimSpace(controllerDescMatch[1])
		// 移除注释中的星号
		controllerDesc = regexp.MustCompile(`\s*\*\s*`).ReplaceAllString(controllerDesc, " ")
		controllerDesc = strings.TrimSpace(controllerDesc)
	}

	// 找到控制器结构体
	structRegex := regexp.MustCompile(`type\s+(\w+)\s+struct\s*{`)
	structMatches := structRegex.FindAllStringSubmatch(fileContent, -1)

	for _, match := range structMatches {
		if len(match) < 2 {
			continue
		}

		structName := match[1]

		// 只处理Controller结尾的结构体
		if !strings.HasSuffix(structName, "Controller") {
			continue
		}

		// 改进方法解析正则表达式，更精确地匹配方法定义及其注释
		// 匹配方法定义及其前方的多行或单行注释
		methodRegex := regexp.MustCompile(`(?m)((?:\/\/.*\n)+)?\s*func\s+\(\w+\s+\*` + structName + `\)\s+(\w+)\s*\([^)]*\)`)
		methodMatches := methodRegex.FindAllStringSubmatch(fileContent, -1)

		// 存储所有方法的起始位置，用于确定方法体范围
		methodPositions := make([]int, len(methodMatches))
		methodEndPositions := make([]int, len(methodMatches))
		for i, methodMatch := range methodMatches {
			methodPositions[i] = strings.Index(fileContent, methodMatch[0])
			methodEndPositions[i] = methodPositions[i] + len(methodMatch[0])
		}

		for i, methodMatch := range methodMatches {
			var methodName string
			var methodComment string

			// 根据正则匹配结果的长度判断处理方式
			if len(methodMatch) == 3 {
				// 有注释块的情况
				methodComment = methodMatch[1]
				methodName = methodMatch[2]
			} else {
				continue
			}

			// 提取方法描述
			methodDesc := ""
			if methodComment != "" {
				// 处理多行注释
				commentLines := strings.Split(strings.TrimSpace(methodComment), "\n")
				var cleanedCommentLines []string

				for _, line := range commentLines {
					trimmedLine := strings.TrimSpace(line)
					// 保留 // 后面的内容
					if strings.HasPrefix(trimmedLine, "//") {
						cleanedLine := strings.TrimSpace(strings.TrimPrefix(trimmedLine, "//"))
						// 跳过 @开头的swagger注解行
						if !strings.HasPrefix(cleanedLine, "@") {
							cleanedCommentLines = append(cleanedCommentLines, cleanedLine)
						}
					}
				}

				// 使用所有非注解行作为方法描述
				if len(cleanedCommentLines) > 0 {
					methodDesc = strings.Join(cleanedCommentLines, " ")
				}
			}

			// 查找方法体范围
			methodStartIndex := methodPositions[i]
			if methodStartIndex == -1 {
				continue
			}

			// 找到方法体的开始位置（第一个左花括号）
			methodBodyStartIdx := strings.Index(fileContent[methodEndPositions[i]:], "{")
			if methodBodyStartIdx == -1 {
				continue
			}
			methodBodyStart := methodEndPositions[i] + methodBodyStartIdx

			// 找到方法体的结束位置 - 使用花括号匹配
			braceCount := 0
			methodBodyEnd := methodBodyStart
			inString := false
			inComment := false
			inBlockComment := false

			for j := methodBodyStart; j < len(fileContent); j++ {
				char := fileContent[j]

				// 处理字符串
				if char == '"' && !inComment && !inBlockComment {
					// 检查前一个字符是否为转义字符
					if j > 0 && fileContent[j-1] == '\\' {
						continue
					}
					inString = !inString
					continue
				}

				// 在字符串中忽略括号
				if inString {
					continue
				}

				// 处理行注释
				if char == '/' && j+1 < len(fileContent) && fileContent[j+1] == '/' && !inBlockComment {
					inComment = true
					continue
				}
				if inComment && (char == '\n' || char == '\r') {
					inComment = false
					continue
				}
				if inComment {
					continue
				}

				// 处理块注释
				if char == '/' && j+1 < len(fileContent) && fileContent[j+1] == '*' && !inBlockComment {
					inBlockComment = true
					j++ // 跳过 *
					continue
				}
				if inBlockComment && char == '*' && j+1 < len(fileContent) && fileContent[j+1] == '/' {
					inBlockComment = false
					j++ // 跳过 /
					continue
				}
				if inBlockComment {
					continue
				}

				// 计数括号
				if char == '{' {
					if braceCount == 0 {
						// 记录第一个左花括号的位置
						methodBodyStart = j
					}
					braceCount++
				} else if char == '}' {
					braceCount--
					if braceCount == 0 {
						// 找到匹配的右花括号
						methodBodyEnd = j + 1
						break
					}
				}
			}

			// 如果无法通过花括号匹配找到方法结束位置，则尝试使用下一个方法的开始位置
			if methodBodyEnd == methodBodyStart {
				if i < len(methodPositions)-1 {
					methodBodyEnd = methodPositions[i+1]
				} else {
					// 对于最后一个方法，使用文件结尾作为方法结束位置
					methodBodyEnd = len(fileContent)
				}
			}

			// 提取完整方法体内容，包括左右大括号
			methodBody := fileContent[methodBodyStart:methodBodyEnd]

			// 查找方法中使用的DTO - 仅在当前方法体内查找
			dtoNames := findDTOsInMethodBody(methodBody, methodName)
			usedDTOsJSON, _ := json.Marshal(dtoNames)

			controller := &models.ControllerInfo{
				Module:            module,
				ControllerName:    controllerFileName, // 使用文件名作为控制器名称
				Description:       controllerDesc,
				MethodName:        methodName,
				MethodDescription: methodDesc,
				UsedDTOIDs:        string(usedDTOsJSON),
				MethodContent:     methodBody, // 存储方法原始内容
			}

			controllers = append(controllers, controller)
		}
	}

	return controllers, nil
}

// findMethodBodyEnd 找到方法体的结束位置（大括号配对）
func findMethodBodyEnd(content string, startPos int) int {
	braceCount := 0
	inString := false
	inComment := false
	inBlockComment := false

	for i := startPos; i < len(content); i++ {
		char := content[i]

		// 处理字符串
		if char == '"' && !inComment && !inBlockComment {
			// 检查前一个字符是否为转义字符
			if i > 0 && content[i-1] == '\\' {
				continue
			}
			inString = !inString
			continue
		}

		// 在字符串中忽略括号
		if inString {
			continue
		}

		// 处理行注释
		if char == '/' && i+1 < len(content) && content[i+1] == '/' && !inBlockComment {
			inComment = true
			continue
		}
		if inComment && (char == '\n' || char == '\r') {
			inComment = false
			continue
		}
		if inComment {
			continue
		}

		// 处理块注释
		if char == '/' && i+1 < len(content) && content[i+1] == '*' && !inBlockComment {
			inBlockComment = true
			i++ // 跳过 *
			continue
		}
		if inBlockComment && char == '*' && i+1 < len(content) && content[i+1] == '/' {
			inBlockComment = false
			i++ // 跳过 /
			continue
		}
		if inBlockComment {
			continue
		}

		// 计数括号
		if char == '{' {
			if braceCount == 0 {
				// 记录第一个左花括号的位置
				startPos = i
			}
			braceCount++
		} else if char == '}' {
			braceCount--
			if braceCount == 0 {
				return i + 1
			}
		}
	}

	return -1
}

// findDTOsInMethodBody 在方法体中查找使用的DTO
func findDTOsInMethodBody(methodBody string, methodName string) []string {
	dtoSet := make(map[string]struct{})

	// 首先判断方法名 - 如果方法名是通用方法，如Prepare、ParseRequest等，
	// 则需要特别小心，避免误匹配
	isCommonMethod := methodName == "Prepare" ||
		methodName == "ParseRequest" ||
		methodName == "Finish" ||
		methodName == "Init"

	// 多个正则表达式来匹配不同形式的DTO引用
	// 1. 匹配变量声明: var req adminDto.AdminLoginRequest
	varDeclRegex := regexp.MustCompile(`var\s+\w+\s+(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))`)

	// 2. 匹配结构体实例化: &adminDto.AdminLoginRequest{} 或 adminDto.AdminLoginRequest{}
	structInstRegex := regexp.MustCompile(`[&]?(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))\{`)

	// 3. 匹配类型转换: (*adminDto.AdminLoginRequest)
	typeCastRegex := regexp.MustCompile(`\(\*?(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))\)`)

	// 4. 匹配类型引用: adminDto.AdminLoginRequest
	typeRefRegex := regexp.MustCompile(`(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))`)

	// 5. 匹配赋值表达式: logReq := &adminDto.AdminLogCreateRequest{ 或 resp := &adminDto.TokenResponse{
	assignRegex := regexp.MustCompile(`\w+\s*:=\s*[&]?(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))\{`)

	// 6. 匹配赋值表达式的另一种形式，例如：resp := new(adminDto.TokenResponse)
	newAssignRegex := regexp.MustCompile(`\w+\s*:=\s*new\s*\(\*?(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))\)`)

	// 7. 匹配函数返回值中的DTO: func GetUser() *userDto.UserResponse
	funcReturnRegex := regexp.MustCompile(`func\s+\w+\s*\([^)]*\)\s*\(\*?(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))\)`)
	funcReturnRegex2 := regexp.MustCompile(`func\s+\w+\s*\([^)]*\)\s*\*?(\w+(?:Dto|DTO|dto))\.(\w+(?:Request|Response|DTO|Model))`)

	// 针对通用方法，额外检查，确保DTO引用确实在方法体内部
	if isCommonMethod {
		// 先清除所有单行注释，避免注释中的DTO干扰
		commentRegex := regexp.MustCompile(`\/\/.*$`)
		cleanMethodBody := commentRegex.ReplaceAllString(methodBody, "")

		// 清除多行注释
		multilineCommentRegex := regexp.MustCompile(`(?s)/\*.*?\*/`)
		cleanMethodBody = multilineCommentRegex.ReplaceAllString(cleanMethodBody, "")

		// 1. 针对变量声明，需要确认是在方法体内部，而不是前面的注释中
		varMatches := varDeclRegex.FindAllStringSubmatch(cleanMethodBody, -1)
		for _, match := range varMatches {
			if len(match) > 2 {
				packageName := match[1]
				dtoName := match[2]
				fullDtoName := packageName + "." + dtoName
				dtoSet[fullDtoName] = struct{}{}
			}
		}

		// 2. 针对结构体实例化
		assignMatches := assignRegex.FindAllStringSubmatch(cleanMethodBody, -1)
		for _, match := range assignMatches {
			if len(match) > 2 {
				packageName := match[1]
				dtoName := match[2]
				fullDtoName := packageName + "." + dtoName
				dtoSet[fullDtoName] = struct{}{}
			}
		}

		// 不搜索其他模式，因为它们可能会捕获到方法签名或注释中的内容

	} else {
		// 对于非通用方法，应用所有正则表达式查找DTO
		for _, regex := range []*regexp.Regexp{
			varDeclRegex,
			structInstRegex,
			typeCastRegex,
			typeRefRegex,
			assignRegex,
			newAssignRegex,
			funcReturnRegex,
			funcReturnRegex2,
		} {
			matches := regex.FindAllStringSubmatch(methodBody, -1)
			for _, match := range matches {
				if len(match) > 2 {
					packageName := match[1]
					dtoName := match[2]
					fullDtoName := packageName + "." + dtoName
					dtoSet[fullDtoName] = struct{}{}
				}
			}
		}
	}

	// 转换集合为切片
	var dtoNames []string
	for name := range dtoSet {
		dtoNames = append(dtoNames, name)
	}

	return dtoNames
}

// GetAPIsByPageName 根据页面名称获取API列表
func (s *apiServiceImpl) GetAPIsByPageName(ctx context.Context, pageName string) ([]*models.ApiInfo, error) {
	return s.apiRepo.GetAPIsByPageName(ctx, pageName)
}

// GetPageNamesByModule 获取指定模块的所有页面名称及对应API数量
func (s *apiServiceImpl) GetPageNamesByModule(ctx context.Context, module string) (*dto.GetPageNamesByModuleResponse, error) {
	// 从数据库获取页面名称列表
	pageNames, err := s.apiRepo.GetPageNamesByModule(ctx, module)
	if err != nil {
		logs.Error("[GetPageNamesByModule] 获取页面名称列表失败: %v", err)
		return nil, fmt.Errorf("获取页面名称列表失败: %v", err)
	}

	// 构建响应对象
	response := &dto.GetPageNamesByModuleResponse{
		Module: module,
		List:   make([]dto.PageNameInfo, 0, len(pageNames)),
	}

	// 转换数据格式
	for _, item := range pageNames {
		pageName, ok := item["page_name"].(string)
		if !ok {
			logs.Warn("[GetPageNamesByModule] 页面名称格式错误")
			continue
		}

		response.List = append(response.List, dto.PageNameInfo{
			PageName: pageName,
		})
	}

	response.Total = int64(len(response.List))

	return response, nil
}

// ListControllers 获取控制器列表
func (s *apiServiceImpl) ListControllers(ctx context.Context, req *dto.ControllerQueryRequest) ([]*models.ControllerInfo, int64, error) {
	// 从数据库中获取控制器列表
	controllers, total, err := s.apiRepo.ListControllers(
		ctx,
		req.Module,
		req.ControllerName,
		req.MethodName,
		req.Page,
		req.PageSize,
	)
	if err != nil {
		logs.Error("[ListControllers] 获取控制器列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取控制器列表失败: %v", err)
	}

	return controllers, total, nil
}

// GetController 根据ID获取控制器信息
func (s *apiServiceImpl) GetController(ctx context.Context, id int64) (*models.ControllerInfo, error) {
	// 从数据库中获取控制器信息
	controller, err := s.apiRepo.GetControllerByID(ctx, id)
	if err != nil {
		logs.Error("[GetController] 获取控制器信息失败: %v", err)
		return nil, fmt.Errorf("获取控制器信息失败: %v", err)
	}

	if controller == nil {
		return nil, nil
	}

	return controller, nil
}

// GetControllersByModule 获取指定模块的所有控制器
func (s *apiServiceImpl) GetControllersByModule(ctx context.Context, module string) ([]*models.ControllerInfo, error) {
	// 从数据库中获取指定模块的控制器列表
	controllers, err := s.apiRepo.GetControllersByModule(ctx, module)
	if err != nil {
		logs.Error("[GetControllersByModule] 获取模块控制器列表失败: %v", err)
		return nil, fmt.Errorf("获取模块控制器列表失败: %v", err)
	}

	return controllers, nil
}

// GetDTOByModuleAndName 根据模块和名称获取DTO信息
func (s *apiServiceImpl) GetDTOByModuleAndName(ctx context.Context, module string, name string) (*dto.DTOResponse, error) {
	// 构建缓存键
	cacheKey := fmt.Sprintf(models.DTOInfoCacheKey, module, name)

	// 尝试从缓存获取
	cacheData, err := redis.Get(cacheKey)
	if err == nil && cacheData != "" {
		var cachedDTO dto.DTOResponse
		err = json.Unmarshal([]byte(cacheData), &cachedDTO)
		if err == nil {
			logs.Info("[GetDTOByModuleAndName] 从缓存获取DTO信息: %s.%s", module, name)
			return &cachedDTO, nil
		}
		logs.Warn("[GetDTOByModuleAndName] 解析缓存数据失败: %v", err)
	}

	// 从数据库获取
	dtoInfo, err := s.apiRepo.GetDTOByModuleAndName(ctx, module, name)
	if err != nil {
		return nil, err
	}
	if dtoInfo == nil {
		return nil, nil
	}

	response, err := s.convertToDTOResponse(dtoInfo)
	if err != nil {
		return nil, err
	}

	// 存入缓存
	jsonData, err := json.Marshal(response)
	if err == nil {
		redis.Set(cacheKey, string(jsonData), models.CacheExpiration)
	}

	return response, nil
}
