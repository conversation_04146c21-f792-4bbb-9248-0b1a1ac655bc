/**
 * 购物车常量定义
 *
 * 本文件定义了购物车模块相关的常量，包括购物车状态、消息类型等。
 * 所有与购物车相关的常量应集中在此文件中定义，方便统一管理。
 */

package constants

// 购物车商品状态
const (
	// CartItemStatusValid 商品有效状态
	CartItemStatusValid = 1
	// CartItemStatusInvalid 商品无效状态（如商品已下架、删除等）
	CartItemStatusInvalid = 0
	// CartItemStatusDeleted 商品已从购物车删除
	CartItemStatusDeleted = -1
)

// 购物车类型
const (
	// CartTypeUnlogin 未登录购物车（存储在Cookie/本地存储）
	CartTypeUnlogin = 0
	// CartTypeLogin 已登录购物车（存储在服务端数据库）
	CartTypeLogin = 1
)

// 购物车错误码
const (
	// CartErrorInvalidParam 无效参数错误
	CartErrorInvalidParam = 10001
	// CartErrorProductNotFound 商品不存在错误
	CartErrorProductNotFound = 10002
	// CartErrorQuantityExceedLimit 数量超出限制错误
	CartErrorQuantityExceedLimit = 10003
	// CartErrorItemNotFound 购物车商品不存在错误
	CartErrorItemNotFound = 10004
	// CartErrorDatabaseOperation 数据库操作错误
	CartErrorDatabaseOperation = 10005
)

// 购物车商品数量限制
const (
	// CartItemMaxQuantity 单个商品最大数量
	CartItemMaxQuantity = 999
	// CartItemsLimit 购物车商品最大种类数
	CartItemsLimit = 100
)

// CartStatusMap 购物车商品状态映射
var CartStatusMap = map[int]string{
	CartItemStatusValid:   "有效",
	CartItemStatusInvalid: "无效",
	CartItemStatusDeleted: "已删除",
}
