/**
 * 购物车控制器
 *
 * 本文件实现了购物车模块的控制器，处理用户购物车相关的HTTP请求。
 * 负责参数验证、调用服务层处理业务逻辑，以及响应结果格式化。
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/cart/dto"
	"o_mall_backend/modules/cart/services"
)

// CartController 购物车控制器
type CartController struct {
	controllers.BaseController
	cartService services.CartService
}

// Prepare 执行控制器前置操作
func (c *CartController) Prepare() {
	// 初始化依赖的服务
	c.cartService = services.NewCartService()
}

// getUserInfo 获取用户信息
func (c *CartController) getUserInfo() (int64, string) {
	// 获取用户ID（已登录用户）
	userIDStr := c.Ctx.Input.Header("X-User-ID")
	userID, _ := strconv.ParseInt(userIDStr, 10, 64)

	// 获取会话ID（未登录用户）
	sessionID := c.Ctx.Input.Header("X-Session-ID")

	return userID, sessionID
}

// AddToCart 添加商品到购物车
// @Title 添加商品到购物车
// @Description 将商品添加到购物车
// @Param   body    body   dto.AddToCartRequest  true   "请求参数"
// @Success 200 {object}  result.Result{data=dto.CartItemResponse}  "购物车商品信息"
// @Failure 400 {object}  result.Result   "请求参数错误"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /add [post]
func (c *CartController) AddToCart() {
	// 解析请求参数
	var req dto.AddToCartRequest
	if err := c.BindJSON(&req); err != nil {
		logs.Error("解析添加购物车请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, err)
		return
	}

	// 获取用户信息
	userID, sessionID := c.getUserInfo()
	req.UserID = userID
	req.SessionID = sessionID

	// 参数验证
	if req.ProductID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "商品ID不能为空")
		return
	}

	if req.Quantity <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "商品数量必须大于0")
		return
	}

	// 调用服务
	cartItem, err := c.cartService.AddToCart(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("添加购物车失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, cartItem)
}

// GetCart 获取购物车
// @Title 获取购物车
// @Description 获取用户购物车信息
// @Success 200 {object}  result.Result{data=dto.CartResponse}  "购物车信息"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router / [get]
func (c *CartController) GetCart() {
	// 获取用户信息
	userID, sessionID := c.getUserInfo()

	// 调用服务
	cart, err := c.cartService.GetCart(c.Ctx.Request.Context(), userID, sessionID)
	if err != nil {
		logs.Error("获取购物车失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, cart)
}

// GetCartCount 获取购物车商品数量
// @Title 获取购物车商品数量
// @Description 获取用户购物车商品总数
// @Success 200 {object}  result.Result{data=int}  "购物车商品总数"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /count [get]
func (c *CartController) GetCartCount() {
	// 获取用户信息
	userID, sessionID := c.getUserInfo()

	// 调用服务
	count, err := c.cartService.GetCartCount(c.Ctx.Request.Context(), userID, sessionID)
	if err != nil {
		logs.Error("获取购物车商品数量失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, count)
}

// UpdateCartItem 更新购物车商品
// @Title 更新购物车商品
// @Description 更新购物车商品数量或选中状态
// @Param   body    body   dto.UpdateCartItemRequest  true   "请求参数"
// @Success 200 {object}  result.Result   "操作成功"
// @Failure 400 {object}  result.Result   "请求参数错误"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /update [put]
func (c *CartController) UpdateCartItem() {
	// 解析请求参数
	var req dto.UpdateCartItemRequest
	if err := c.BindJSON(&req); err != nil {
		logs.Error("解析更新购物车请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, err)
		return
	}

	// 获取用户信息
	userID, sessionID := c.getUserInfo()
	req.UserID = userID
	req.SessionID = sessionID

	// 参数验证
	if req.ID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "购物车商品ID不能为空")
		return
	}

	// 调用服务
	err := c.cartService.UpdateCartItem(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("更新购物车商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// DeleteCartItem 删除购物车商品
// @Title 删除购物车商品
// @Description 从购物车删除商品
// @Param   body    body   dto.DeleteCartItemRequest  true   "请求参数"
// @Success 200 {object}  result.Result   "操作成功"
// @Failure 400 {object}  result.Result   "请求参数错误"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /delete [delete]
func (c *CartController) DeleteCartItem() {
	// 解析请求参数
	var req dto.DeleteCartItemRequest
	if err := c.BindJSON(&req); err != nil {
		logs.Error("解析删除购物车请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, err)
		return
	}

	// 获取用户信息
	userID, sessionID := c.getUserInfo()
	req.UserID = userID
	req.SessionID = sessionID

	// 参数验证
	if req.ID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "购物车商品ID不能为空")
		return
	}

	// 调用服务
	err := c.cartService.DeleteCartItem(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("删除购物车商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// SelectCartItems 批量选择/取消选择购物车商品
// @Title 批量选择/取消选择购物车商品
// @Description 批量选择或取消选择购物车商品
// @Param   body    body   dto.SelectCartItemsRequest  true   "请求参数"
// @Success 200 {object}  result.Result   "操作成功"
// @Failure 400 {object}  result.Result   "请求参数错误"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /select [put]
func (c *CartController) SelectCartItems() {
	// 解析请求参数
	var req dto.SelectCartItemsRequest
	if err := c.BindJSON(&req); err != nil {
		logs.Error("解析批量选择购物车请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, err)
		return
	}

	// 获取用户信息
	userID, sessionID := c.getUserInfo()
	req.UserID = userID
	req.SessionID = sessionID

	// 参数验证
	if len(req.IDs) == 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "购物车商品ID列表不能为空")
		return
	}

	// 调用服务
	err := c.cartService.SelectCartItems(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("批量选择购物车商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// SelectAllCartItems 全选/取消全选购物车商品
// @Title 全选/取消全选购物车商品
// @Description 全选或取消全选购物车商品
// @Param   selected    query   bool  true   "是否选中"
// @Success 200 {object}  result.Result   "操作成功"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /select/all [put]
func (c *CartController) SelectAllCartItems() {
	// 获取用户信息
	userID, sessionID := c.getUserInfo()

	// 获取是否选中
	selectedStr := c.GetString("selected")
	selected := selectedStr == "true" || selectedStr == "1"

	// 调用服务
	err := c.cartService.SelectAllCartItems(c.Ctx.Request.Context(), userID, sessionID, selected)
	if err != nil {
		logs.Error("全选/取消全选购物车商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// ClearCart 清空购物车
// @Title 清空购物车
// @Description 清空用户购物车
// @Success 200 {object}  result.Result   "操作成功"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /clear [delete]
func (c *CartController) ClearCart() {
	// 获取用户信息
	userID, sessionID := c.getUserInfo()

	// 封装请求
	req := &dto.ClearCartRequest{
		UserID:    userID,
		SessionID: sessionID,
	}

	// 调用服务
	err := c.cartService.ClearCart(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("清空购物车失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// ClearInvalidItems 清除无效商品
// @Title 清除无效商品
// @Description 清除购物车中的无效商品
// @Success 200 {object}  result.Result   "操作成功"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /clear/invalid [delete]
func (c *CartController) ClearInvalidItems() {
	// 获取用户信息
	userID, sessionID := c.getUserInfo()

	// 调用服务
	err := c.cartService.ClearInvalidItems(c.Ctx.Request.Context(), userID, sessionID)
	if err != nil {
		logs.Error("清除无效商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// MergeCart 合并购物车
// @Title 合并购物车
// @Description 合并未登录状态的购物车到已登录用户的购物车
// @Param   body    body   dto.MergeCartRequest  true   "请求参数"
// @Success 200 {object}  result.Result   "操作成功"
// @Failure 400 {object}  result.Result   "请求参数错误"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /merge [post]
func (c *CartController) MergeCart() {
	// 解析请求参数
	var req dto.MergeCartRequest
	if err := c.BindJSON(&req); err != nil {
		logs.Error("解析合并购物车请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, err)
		return
	}

	// 参数验证
	if req.UserID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "用户ID不能为空")
		return
	}

	if req.SessionID == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "会话ID不能为空")
		return
	}

	// 调用服务
	err := c.cartService.MergeCart(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("合并购物车失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// GetSelectedItems 获取选中的购物车商品
// @Title 获取选中的购物车商品
// @Description 获取用户购物车中选中的商品（用于结算）
// @Success 200 {object}  result.Result{data=dto.CartResponse}  "选中的购物车商品"
// @Failure 500 {object}  result.Result   "服务器内部错误"
// @router /selected [get]
func (c *CartController) GetSelectedItems() {
	// 获取用户信息
	userID, sessionID := c.getUserInfo()

	// 调用服务
	selected, err := c.cartService.GetSelectedItems(c.Ctx.Request.Context(), userID, sessionID)
	if err != nil {
		logs.Error("获取选中的购物车商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, selected)
}

// BindJSON 绑定JSON请求体到结构体
func (c *CartController) BindJSON(obj interface{}) error {
	return c.Ctx.Input.Bind(obj, "json")
}
