/**
 * 购物车数据传输对象
 *
 * 本文件定义了购物车模块中用于数据传输的对象，包括请求和响应结构。
 * 这些DTO用于API接口的参数接收和返回，规范了数据格式。
 */

package dto

import "time"

// AddToCartRequest 添加商品到购物车请求
type AddToCartRequest struct {
	UserID       int64   `json:"user_id,omitempty"`                // 用户ID（可选，未登录时为空）
	ProductID    int64   `json:"product_id" valid:"Required"`      // 商品ID
	SkuID        int64   `json:"sku_id"`                           // SKU ID，可选
	Quantity     int     `json:"quantity" valid:"Required;Min(1)"` // 数量
	Price        float64 `json:"price,omitempty"`                  // 价格（可选，后端会验证）
	ProductName  string  `json:"product_name,omitempty"`           // 商品名称
	ProductImage string  `json:"product_image,omitempty"`          // 商品图片
	Selected     bool    `json:"selected"`                         // 是否选中
	SessionID    string  `json:"session_id,omitempty"`             // 会话ID（未登录用户）
}

// UpdateCartItemRequest 更新购物车商品请求
type UpdateCartItemRequest struct {
	ID        int64  `json:"id" valid:"Required"`  // 购物车项ID
	UserID    int64  `json:"user_id,omitempty"`    // 用户ID（可选，未登录时为空）
	Quantity  int    `json:"quantity,omitempty"`   // 数量
	Selected  *bool  `json:"selected,omitempty"`   // 是否选中
	SessionID string `json:"session_id,omitempty"` // 会话ID（未登录用户）
}

// DeleteCartItemRequest 删除购物车商品请求
type DeleteCartItemRequest struct {
	ID        int64  `json:"id" valid:"Required"`  // 购物车项ID
	UserID    int64  `json:"user_id,omitempty"`    // 用户ID（可选，未登录时为空）
	SessionID string `json:"session_id,omitempty"` // 会话ID（未登录用户）
}

// SelectCartItemsRequest 选择/取消选择购物车商品请求
type SelectCartItemsRequest struct {
	IDs       []int64 `json:"ids" valid:"Required"` // 购物车项ID列表
	UserID    int64   `json:"user_id,omitempty"`    // 用户ID（可选，未登录时为空）
	Selected  bool    `json:"selected"`             // 是否选中
	SessionID string  `json:"session_id,omitempty"` // 会话ID（未登录用户）
}

// ClearCartRequest 清空购物车请求
type ClearCartRequest struct {
	UserID    int64  `json:"user_id,omitempty"`    // 用户ID（可选，未登录时为空）
	SessionID string `json:"session_id,omitempty"` // 会话ID（未登录用户）
}

// MergeCartRequest 合并购物车请求（未登录->已登录）
type MergeCartRequest struct {
	UserID    int64  `json:"user_id" valid:"Required"`    // 用户ID
	SessionID string `json:"session_id" valid:"Required"` // 会话ID
}

// CartItemResponse 购物车商品响应
type CartItemResponse struct {
	ID            int64     `json:"id"`             // 购物车项ID
	ProductID     int64     `json:"product_id"`     // 商品ID
	ProductName   string    `json:"product_name"`   // 商品名称
	ProductImage  string    `json:"product_image"`  // 商品图片
	SkuID         int64     `json:"sku_id"`         // SKU ID
	SkuAttributes string    `json:"sku_attributes"` // SKU属性
	MerchantID    int64     `json:"merchant_id"`    // 商家ID
	MerchantName  string    `json:"merchant_name"`  // 商家名称
	Price         float64   `json:"price"`          // 价格
	OriginalPrice float64   `json:"original_price"` // 原价
	Quantity      int       `json:"quantity"`       // 数量
	Stock         int       `json:"stock"`          // 库存
	Selected      bool      `json:"selected"`       // 是否选中
	Status        int       `json:"status"`         // 状态
	StatusText    string    `json:"status_text"`    // 状态文本
	Subtotal      float64   `json:"subtotal"`       // 小计金额
	IsValid       bool      `json:"is_valid"`       // 是否有效
	InvalidReason string    `json:"invalid_reason"` // 无效原因
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`     // 更新时间
}

// CartResponse 购物车响应
type CartResponse struct {
	Items            []*CartItemResponse `json:"items"`                // 购物车商品列表
	ValidItems       []*CartItemResponse `json:"valid_items"`          // 有效商品列表
	InvalidItems     []*CartItemResponse `json:"invalid_items"`        // 无效商品列表
	TotalQuantity    int                 `json:"total_quantity"`       // 总数量
	SelectedQuantity int                 `json:"selected_quantity"`    // 已选数量
	TotalPrice       float64             `json:"total_price"`          // 总价
	SelectedPrice    float64             `json:"selected_price"`       // 已选总价
	HasInvalid       bool                `json:"has_invalid"`          // 是否有无效商品
	UserID           int64               `json:"user_id,omitempty"`    // 用户ID
	SessionID        string              `json:"session_id,omitempty"` // 会话ID
}
