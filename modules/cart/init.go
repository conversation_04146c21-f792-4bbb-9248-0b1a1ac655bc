/**
 * init.go
 * 购物车模块初始化
 *
 * 本文件负责初始化购物车模块，包括注册路由和初始化数据库等
 */

package cart

import (
	"sync"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/cart/models"
	"o_mall_backend/modules/cart/routes"
)

var (
	initOnce sync.Once
)

// Init 初始化购物车模块
func Init() {
	initOnce.Do(func() {
		logs.Info("初始化购物车模块...")

		// 注册模型
		registerModels()

		// 初始化路由
		routes.InitCartRoutes()

		logs.Info("购物车模块初始化完成")
	})
}

// registerModels 注册购物车相关模型
func registerModels() {
	logs.Info("注册购物车数据模型")

	// 注册购物车模型
	orm.RegisterModel(new(models.Cart))
	orm.RegisterModel(new(models.CartItem))
	orm.RegisterModel(new(models.CartItemLog))
}
