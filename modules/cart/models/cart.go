/**
 * cart.go
 * 购物车模型
 *
 * 本文件定义了购物车相关的数据模型，包括购物车主表和购物车商品表
 */

package models

import (
	"time"
	//"github.com/beego/beego/v2/client/orm"
)

// Cart 购物车模型
type Cart struct {
	ID        int64     `orm:"pk;auto" json:"id" description:"购物车ID，主键自增"`                    // 购物车ID
	UserID    int64     `orm:"index" json:"user_id" description:"用户ID，关联用户表"`                 // 用户ID
	SessionID string    `orm:"size(64);index" json:"session_id" description:"会话ID，用于未登录用户标识"` // 会话ID（未登录用户）
	CreatedAt time.Time `orm:"auto_now_add" json:"created_at" description:"创建时间，自动设置"`        // 创建时间
	UpdatedAt time.Time `orm:"auto_now" json:"updated_at" description:"更新时间，自动更新"`            // 更新时间
}

// CartItem 购物车商品模型
type CartItem struct {
	ID            int64     `orm:"pk;auto" json:"id" description:"购物车商品ID，主键自增"`                          // 购物车商品ID
	CartID        int64     `orm:"index" json:"cart_id" description:"关联的购物车ID"`                           // 购物车ID
	UserID        int64     `orm:"column(user_id);index" json:"user_id" description:"所属用户ID"`             // 用户ID
	ProductID     int64     `orm:"column(product_id);index" json:"product_id" description:"商品ID，关联商品表"`   // 商品ID
	SkuID         int64     `orm:"column(sku_id);default(0)" json:"sku_id" description:"商品SKU ID，关联SKU表"` // SKU ID
	ProductName   string    `orm:"size(128)" json:"product_name" description:"商品名称，冗余存储"`                 // 商品名称
	ProductImage  string    `orm:"size(255)" json:"product_image" description:"商品图片URL"`                  // 商品图片
	SkuAttributes string    `orm:"size(255)" json:"sku_attributes" description:"SKU规格属性JSON字符串"`          // SKU属性（如颜色、尺寸等）
	Price         float64   `orm:"digits(10);decimals(2)" json:"price" description:"商品实际销售价格"`            // 价格
	OriginalPrice float64   `orm:"digits(10);decimals(2)" json:"original_price" description:"商品原始价格"`     // 原价
	Quantity      int       `orm:"default(1)" json:"quantity" description:"购买数量，默认1"`                     // 商品数量
	Selected      bool      `orm:"default(true)" json:"selected" description:"是否选中结算，默认true"`             // 是否选中
	Status        int       `orm:"default(1)" json:"status" description:"状态：1-有效，0-无效，-1-已删除"`            // 状态：1-有效，0-无效，-1-已删除
	CreatedAt     time.Time `orm:"auto_now_add" json:"created_at" description:"创建时间，自动设置"`                // 创建时间
	UpdatedAt     time.Time `orm:"auto_now" json:"updated_at" description:"更新时间，自动更新"`                    // 更新时间
}

// CartItemLog 购物车操作日志
type CartItemLog struct {
	ID         int64     `orm:"pk;auto" json:"id" description:"日志ID，主键自增"`                            // 日志ID
	CartItemID int64     `orm:"index" json:"cart_item_id" description:"关联的购物车商品ID"`                   // 购物车商品ID
	UserID     int64     `orm:"index" json:"user_id" description:"操作用户ID"`                            // 用户ID
	ProductID  int64     `orm:"index" json:"product_id" description:"相关商品ID"`                         // 商品ID
	SkuID      int64     `orm:"default(0)" json:"sku_id" description:"相关SKU ID"`                      // SKU ID
	Action     string    `orm:"size(20)" json:"action" description:"操作类型：add-添加、update-更新、delete-删除"` // 操作类型：add-添加、update-更新、delete-删除
	Quantity   int       `orm:"default(1)" json:"quantity" description:"操作涉及的商品数量"`                   // 操作数量
	IP         string    `orm:"size(50)" json:"ip" description:"操作者IP地址"`                             // 操作IP
	UserAgent  string    `orm:"size(255)" json:"user_agent" description:"操作者浏览器信息"`                   // 用户代理
	CreatedAt  time.Time `orm:"auto_now_add" json:"created_at" description:"操作时间，自动设置"`               // 创建时间
}

// TableName 获取表名
func (c *Cart) TableName() string {
	return "carts"
}

// TableName 获取表名
func (ci *CartItem) TableName() string {
	return "cart_items"
}

// TableName 获取表名
func (cl *CartItemLog) TableName() string {
	return "cart_item_logs"
}

// TableIndex 设置索引
func (ci *CartItem) TableIndex() [][]string {
	return [][]string{
		{"user_id", "status"},               // 用户商品状态索引
		{"user_id", "product_id", "sku_id"}, // 用户商品SKU唯一索引
		{"user_id", "status", "selected"},   // 批量更新优化索引（用于购物车选择操作）
		{"user_id", "id", "status"},         // 批量ID查询优化索引（用于权限验证）
		{"user_id", "status", "created_at"}, // 列表查询优化索引（用于购物车列表排序）
	}
}

// TableUnique 设置唯一键
func (ci *CartItem) TableUnique() [][]string {
	return [][]string{
		{"user_id", "product_id", "sku_id"}, // 同一用户同一商品同一SKU只能有一条记录
	}
}
