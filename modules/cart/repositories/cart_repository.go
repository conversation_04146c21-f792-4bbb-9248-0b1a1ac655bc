/**
 * 购物车仓库接口定义
 *
 * 本文件定义了购物车模块的数据访问接口，提供购物车数据的增删改查操作。
 * 通过接口隔离，使业务逻辑层与数据访问层解耦，便于扩展和测试。
 */

package repositories

import (
	"context"
	"errors"
	"time"

	"o_mall_backend/modules/cart/constants"
	"o_mall_backend/modules/cart/models"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// CartRepository 购物车仓库接口
type CartRepository interface {
	// AddCartItem 添加商品到购物车
	AddCartItem(ctx context.Context, item *models.CartItem) (int64, error)

	// GetCartItem 根据ID获取购物车项
	GetCartItem(ctx context.Context, id int64) (*models.CartItem, error)

	// GetCartItemByProduct 根据用户ID、商品ID和SKU ID获取购物车项
	GetCartItemByProduct(ctx context.Context, userID, productID, skuID int64) (*models.CartItem, error)

	// UpdateCartItem 更新购物车项
	UpdateCartItem(ctx context.Context, item *models.CartItem) error

	// DeleteCartItem 删除购物车项（逻辑删除）
	DeleteCartItem(ctx context.Context, id int64, userID int64) error

	// ListCartItems 获取用户购物车列表
	ListCartItems(ctx context.Context, userID int64, validOnly bool) ([]*models.CartItem, error)

	// CountCartItems 统计用户购物车商品数量
	CountCartItems(ctx context.Context, userID int64, validOnly bool) (int, error)

	// BatchUpdateSelected 批量更新购物车项选中状态
	BatchUpdateSelected(ctx context.Context, ids []int64, userID int64, selected bool) error

	// ClearCart 清空用户购物车
	ClearCart(ctx context.Context, userID int64) error

	// ClearInvalidItems 清除用户购物车中的无效商品
	ClearInvalidItems(ctx context.Context, userID int64) error

	// AddCartItemLog 添加购物车操作日志
	AddCartItemLog(ctx context.Context, log *models.CartItemLog) error
}

// CartRepositoryImpl 购物车仓库实现
type CartRepositoryImpl struct {
	orm orm.Ormer
}

// NewCartRepository 创建购物车仓库实例
func NewCartRepository() CartRepository {
	return &CartRepositoryImpl{
		orm: orm.NewOrm(),
	}
}

// AddCartItem 添加商品到购物车
func (r *CartRepositoryImpl) AddCartItem(ctx context.Context, item *models.CartItem) (int64, error) {
	// 先查询是否已存在相同商品
	existItem, err := r.GetCartItemByProduct(ctx, item.UserID, item.ProductID, item.SkuID)
	if err != nil && err != orm.ErrNoRows {
		return 0, err
	}

	if existItem != nil {
		// 已存在则更新数量
		existItem.Quantity += item.Quantity
		existItem.Price = item.Price
		existItem.Selected = item.Selected
		existItem.Status = item.Status
		existItem.UpdatedAt = time.Now()

		// 更新商品
		_, err = r.orm.Update(existItem, "Quantity", "Price", "Selected", "Status", "UpdatedAt")
		if err != nil {
			return 0, err
		}
		return existItem.ID, nil
	}

	// 不存在则创建新记录
	id, err := r.orm.Insert(item)
	if err != nil {
		return 0, err
	}

	return id, nil
}

// GetCartItem 根据ID获取购物车项
func (r *CartRepositoryImpl) GetCartItem(ctx context.Context, id int64) (*models.CartItem, error) {
	item := &models.CartItem{ID: id}
	err := r.orm.Read(item)
	if err != nil {
		return nil, err
	}

	return item, nil
}

// GetCartItemByProduct 根据用户ID、商品ID和SKU ID获取购物车项
func (r *CartRepositoryImpl) GetCartItemByProduct(ctx context.Context, userID, productID, skuID int64) (*models.CartItem, error) {
	item := &models.CartItem{}

	err := r.orm.QueryTable(new(models.CartItem)).
		Filter("user_id", userID).
		Filter("product_id", productID).
		Filter("sku_id", skuID).
		Filter("status__gte", 0).
		One(item)

	if err != nil {
		return nil, err
	}

	return item, nil
}

// UpdateCartItem 更新购物车项
func (r *CartRepositoryImpl) UpdateCartItem(ctx context.Context, item *models.CartItem) error {
	item.UpdatedAt = time.Now()
	_, err := r.orm.Update(item)
	return err
}

// DeleteCartItem 删除购物车项（物理删除）
func (r *CartRepositoryImpl) DeleteCartItem(ctx context.Context, id int64, userID int64) error {
	// 先查询确保存在且属于该用户
	item := &models.CartItem{ID: id}
	err := r.orm.Read(item)
	if err != nil {
		return err
	}

	if item.UserID != userID {
		return errors.New("无权操作此购物车项")
	}

	// 物理删除购物车项
	_, err = r.orm.Delete(item)
	return err
}

// ListCartItems 获取用户购物车列表
func (r *CartRepositoryImpl) ListCartItems(ctx context.Context, userID int64, validOnly bool) ([]*models.CartItem, error) {
	var items []*models.CartItem
	query := r.orm.QueryTable(new(models.CartItem)).
		Filter("user_id", userID).
		Filter("status__gte", 0)

	if validOnly {
		query = query.Filter("status", constants.CartItemStatusValid)
	}

	_, err := query.OrderBy("-created_at").All(&items)
	if err != nil {
		return nil, err
	}

	return items, nil
}

// CountCartItems 统计用户购物车商品数量
func (r *CartRepositoryImpl) CountCartItems(ctx context.Context, userID int64, validOnly bool) (int, error) {
	query := r.orm.QueryTable(new(models.CartItem)).
		Filter("user_id", userID).
		Filter("status__gte", 0)

	if validOnly {
		query = query.Filter("status", constants.CartItemStatusValid)
	}

	count, err := query.Count()
	if err != nil {
		return 0, err
	}

	return int(count), nil
}

// BatchUpdateSelected 批量更新购物车项选中状态（优化版本）
func (r *CartRepositoryImpl) BatchUpdateSelected(ctx context.Context, ids []int64, userID int64, selected bool) error {
	if len(ids) == 0 {
		return nil
	}

	// 使用Beego ORM的QueryTable方式进行批量操作，避免硬编码SQL
	qs := r.orm.QueryTable(new(models.CartItem))

	// 先验证所有ID都属于该用户
	count, err := qs.Filter("id__in", ids).Filter("user_id", userID).Filter("status__gte", 0).Count()
	if err != nil {
		logs.Error("验证购物车项所有权失败: %v", err)
		return err
	}

	if count != int64(len(ids)) {
		logs.Error("部分购物车项不存在或无权操作，请求ID数量: %d, 实际找到: %d", len(ids), count)
		return errors.New("部分购物车项不存在或无权操作")
	}

	// 执行批量更新
	updateData := map[string]any{
		"selected":   selected,
		"updated_at": time.Now(),
	}

	num, err := qs.Filter("id__in", ids).Filter("user_id", userID).Update(updateData)
	if err != nil {
		logs.Error("批量更新购物车项选中状态失败: %v", err)
		return err
	}

	logs.Info("批量更新购物车项选中状态成功, 用户ID: %d, 更新数量: %d", userID, num)
	return nil
}

// ClearCart 清空用户购物车
func (r *CartRepositoryImpl) ClearCart(ctx context.Context, userID int64) error {
	// 逻辑删除用户的所有购物车项
	_, err := r.orm.QueryTable(new(models.CartItem)).
		Filter("user_id", userID).
		Filter("status__gte", 0).
		Update(orm.Params{
			"status":     constants.CartItemStatusDeleted,
			"updated_at": time.Now(),
		})

	return err
}

// ClearInvalidItems 清除用户购物车中的无效商品
func (r *CartRepositoryImpl) ClearInvalidItems(ctx context.Context, userID int64) error {
	// 逻辑删除用户的所有无效购物车项
	_, err := r.orm.QueryTable(new(models.CartItem)).
		Filter("user_id", userID).
		Filter("status", constants.CartItemStatusInvalid).
		Update(orm.Params{
			"status":     constants.CartItemStatusDeleted,
			"updated_at": time.Now(),
		})

	return err
}

// AddCartItemLog 添加购物车操作日志
func (r *CartRepositoryImpl) AddCartItemLog(ctx context.Context, log *models.CartItemLog) error {
	_, err := r.orm.Insert(log)
	return err
}
