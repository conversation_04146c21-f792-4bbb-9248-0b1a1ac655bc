/**
 * 购物车路由定义
 *
 * 本文件定义了购物车模块的API路由，将控制器中的方法与特定的URL路径进行映射。
 * 通过这些路由，客户端可以访问购物车的各种功能。
 */

package routes

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/cart/controllers"
)

// InitCartRoutes 初始化购物车路由
func InitCartRoutes() {
	// 创建购物车控制器
	cartController := &controllers.CartController{}

	// 注册路由
	ns := web.NewNamespace("/api/v1/cart",
		// 需要登录的接口
		web.NSRouter("/add", cartController, "post:AddToCart"),
		web.NSRouter("/", cartController, "get:GetCart"),
		web.NSRouter("/count", cartController, "get:GetCartCount"),
		web.NSRouter("/update", cartController, "put:UpdateCartItem"),
		web.NSRouter("/delete", cartController, "delete:DeleteCartItem"),
		web.NSRouter("/select", cartController, "put:SelectCartItems"),
		web.NSRouter("/select/all", cartController, "put:SelectAllCartItems"),
		web.NSRouter("/clear", cartController, "delete:ClearCart"),
		web.NSRouter("/clear/invalid", cartController, "delete:ClearInvalidItems"),
		web.NSRouter("/merge", cartController, "post:MergeCart"),
		web.NSRouter("/selected", cartController, "get:GetSelectedItems"),
	)

	// 注册命名空间
	web.AddNamespace(ns)
}
