/**
 * 购物车服务实现
 *
 * 本文件实现了购物车服务接口，提供完整的购物车业务逻辑处理功能。
 * 包括添加商品到购物车、更新购物车、删除购物车等业务操作。
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/cart/constants"
	"o_mall_backend/modules/cart/dto"
	"o_mall_backend/modules/cart/models"
	"o_mall_backend/modules/cart/repositories"
)

// CartServiceImpl 购物车服务实现
type CartServiceImpl struct {
	cartRepo repositories.CartRepository
	// TODO: 添加商品服务接口依赖，用于获取商品信息
	// productService product.ProductService
}

// NewCartService 创建购物车服务
func NewCartService() CartService {
	return &CartServiceImpl{
		cartRepo: repositories.NewCartRepository(),
	}
}

// AddToCart 添加商品到购物车
func (s *CartServiceImpl) AddToCart(ctx context.Context, req *dto.AddToCartRequest) (*dto.CartItemResponse, error) {
	logs.Info("添加商品到购物车: %+v", req)

	if req == nil {
		return nil, errors.New("请求参数不能为空")
	}

	if req.ProductID <= 0 {
		return nil, errors.New("商品ID不能为空")
	}

	if req.Quantity <= 0 {
		return nil, errors.New("商品数量必须大于0")
	}

	if req.Quantity > constants.CartItemMaxQuantity {
		return nil, fmt.Errorf("商品数量不能超过%d", constants.CartItemMaxQuantity)
	}

	// 如果是已登录用户，则检查购物车商品数量限制
	if req.UserID > 0 {
		count, err := s.cartRepo.CountCartItems(ctx, req.UserID, true)
		if err != nil {
			logs.Error("统计购物车商品数量失败: %v", err)
			return nil, err
		}

		if count >= constants.CartItemsLimit {
			return nil, fmt.Errorf("购物车商品种类不能超过%d", constants.CartItemsLimit)
		}
	}

	// 使用请求中的商品名称和图片，如果未提供则使用默认值
	productName := req.ProductName
	if productName == "" {
		productName = "测试商品"
	}

	productImage := req.ProductImage
	if productImage == "" {
		productImage = "https://example.com/image.jpg"
	}

	skuAttributes := ""
	merchantID := int64(1)
	merchantName := "测试商家"
	originalPrice := req.Price

	// 创建购物车项
	cartItem := &models.CartItem{
		UserID:        req.UserID,
		ProductID:     req.ProductID,
		ProductName:   productName,
		ProductImage:  productImage,
		SkuID:         req.SkuID,
		SkuAttributes: skuAttributes,
		Price:         req.Price,
		OriginalPrice: originalPrice,
		Quantity:      req.Quantity,
		Selected:      true, // 默认选中
		Status:        constants.CartItemStatusValid,
	}

	// 保存到数据库
	id, err := s.cartRepo.AddCartItem(ctx, cartItem)
	if err != nil {
		logs.Error("保存购物车项失败: %v", err)
		return nil, err
	}

	// 添加操作日志
	logItem := &models.CartItemLog{
		UserID:     req.UserID,
		CartItemID: id,
		ProductID:  req.ProductID,
		SkuID:      req.SkuID,
		Action:     "add",
		Quantity:   req.Quantity,
		// IP和设备信息可以从ctx中获取
	}

	err = s.cartRepo.AddCartItemLog(ctx, logItem)
	if err != nil {
		logs.Error("添加购物车日志失败: %v", err)
		// 不影响添加购物车的结果
	}

	// 构建返回结果
	resp := &dto.CartItemResponse{
		ID:            id,
		ProductID:     req.ProductID,
		ProductName:   productName,
		ProductImage:  productImage,
		SkuID:         req.SkuID,
		SkuAttributes: skuAttributes,
		MerchantID:    merchantID,
		MerchantName:  merchantName,
		Price:         req.Price,
		OriginalPrice: originalPrice,
		Quantity:      req.Quantity,
		Stock:         999, // TODO: 从商品服务获取
		Selected:      true,
		Status:        constants.CartItemStatusValid,
		StatusText:    constants.CartStatusMap[constants.CartItemStatusValid],
		Subtotal:      req.Price * float64(req.Quantity),
		IsValid:       true,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	return resp, nil
}

// GetCart 获取购物车详情
func (s *CartServiceImpl) GetCart(ctx context.Context, userID int64, sessionID string) (*dto.CartResponse, error) {
	logs.Info("获取购物车: userID=%d, sessionID=%s", userID, sessionID)

	// 已登录用户从数据库获取购物车
	if userID > 0 {
		// 获取购物车项
		items, err := s.cartRepo.ListCartItems(ctx, userID, false)
		if err != nil {
			logs.Error("获取购物车列表失败: %v", err)
			return nil, err
		}

		return s.buildCartResponse(ctx, items, userID, sessionID)
	}

	// 未登录用户的购物车处理逻辑
	// TODO: 实现未登录用户的购物车逻辑，可以存储在Redis中
	return &dto.CartResponse{
		Items:         make([]*dto.CartItemResponse, 0),
		ValidItems:    make([]*dto.CartItemResponse, 0),
		InvalidItems:  make([]*dto.CartItemResponse, 0),
		TotalQuantity: 0,
		TotalPrice:    0,
		HasInvalid:    false,
		SessionID:     sessionID,
	}, nil
}

// GetCartCount 获取购物车商品数量
func (s *CartServiceImpl) GetCartCount(ctx context.Context, userID int64, sessionID string) (int, error) {
	logs.Info("获取购物车商品数量: userID=%d, sessionID=%s", userID, sessionID)

	// 已登录用户
	if userID > 0 {
		return s.cartRepo.CountCartItems(ctx, userID, true)
	}

	// TODO: 未登录用户的购物车数量获取
	return 0, nil
}

// UpdateCartItem 更新购物车商品数量或选中状态
func (s *CartServiceImpl) UpdateCartItem(ctx context.Context, req *dto.UpdateCartItemRequest) error {
	logs.Info("更新购物车商品: %+v", req)

	if req == nil || req.ID <= 0 {
		return errors.New("参数错误：购物车商品ID无效")
	}

	// 获取购物车项
	item, err := s.cartRepo.GetCartItem(ctx, req.ID)
	if err != nil {
		logs.Error("获取购物车项失败: %v", err)
		return err
	}

	if item == nil {
		return errors.New("购物车商品不存在")
	}

	// 检查所有权
	if req.UserID > 0 && item.UserID != req.UserID {
		return errors.New("无权操作此购物车商品")
	}

	// 更新数量
	if req.Quantity > 0 {
		if req.Quantity > constants.CartItemMaxQuantity {
			return fmt.Errorf("商品数量不能超过%d", constants.CartItemMaxQuantity)
		}

		// 记录旧数量用于日志
		oldQuantity := item.Quantity
		item.Quantity = req.Quantity

		// 添加操作日志
		logItem := &models.CartItemLog{
			UserID:     item.UserID,
			CartItemID: item.ID,
			ProductID:  item.ProductID,
			SkuID:      item.SkuID,
			Action:     "update_quantity",
			Quantity:   req.Quantity - oldQuantity,
		}

		err = s.cartRepo.AddCartItemLog(ctx, logItem)
		if err != nil {
			logs.Error("添加购物车日志失败: %v", err)
			// 不影响更新操作
		}
	}

	// 更新选中状态
	if req.Selected != nil {
		item.Selected = *req.Selected

		// 添加操作日志
		action := "select"
		if !*req.Selected {
			action = "unselect"
		}

		logItem := &models.CartItemLog{
			UserID:     item.UserID,
			CartItemID: item.ID,
			ProductID:  item.ProductID,
			SkuID:      item.SkuID,
			Action:     action,
			Quantity:   0,
		}

		err = s.cartRepo.AddCartItemLog(ctx, logItem)
		if err != nil {
			logs.Error("添加购物车日志失败: %v", err)
			// 不影响更新操作
		}
	}

	// 更新购物车项
	return s.cartRepo.UpdateCartItem(ctx, item)
}

// DeleteCartItem 删除购物车商品
func (s *CartServiceImpl) DeleteCartItem(ctx context.Context, req *dto.DeleteCartItemRequest) error {
	logs.Info("删除购物车商品: %+v", req)

	if req == nil || req.ID <= 0 {
		return errors.New("参数错误：购物车商品ID无效")
	}

	// 查询购物车项，确保存在
	item, err := s.cartRepo.GetCartItem(ctx, req.ID)
	if err != nil {
		logs.Error("获取购物车项失败: %v", err)
		return err
	}

	if item == nil {
		return errors.New("购物车商品不存在")
	}

	// 检查权限
	if req.UserID > 0 && item.UserID != req.UserID {
		return errors.New("无权操作此购物车商品")
	}

	// 添加操作日志
	logItem := &models.CartItemLog{
		UserID:     item.UserID,
		CartItemID: item.ID,
		ProductID:  item.ProductID,
		SkuID:      item.SkuID,
		Action:     "delete",
		Quantity:   -item.Quantity, // 负数表示减少
	}

	err = s.cartRepo.AddCartItemLog(ctx, logItem)
	if err != nil {
		logs.Error("添加购物车日志失败: %v", err)
		// 不影响删除操作
	}

	// 删除购物车项（逻辑删除）
	return s.cartRepo.DeleteCartItem(ctx, req.ID, req.UserID)
}

// SelectCartItems 批量选择/取消选择购物车商品
func (s *CartServiceImpl) SelectCartItems(ctx context.Context, req *dto.SelectCartItemsRequest) error {
	logs.Info("批量选择/取消选择购物车商品: %+v", req)

	if req == nil || len(req.IDs) == 0 {
		return errors.New("参数错误：购物车商品ID列表为空")
	}

	// 执行批量更新
	return s.cartRepo.BatchUpdateSelected(ctx, req.IDs, req.UserID, req.Selected)
}

// SelectAllCartItems 全选/取消全选购物车商品
func (s *CartServiceImpl) SelectAllCartItems(ctx context.Context, userID int64, sessionID string, selected bool) error {
	logs.Info("全选/取消全选购物车商品: userID=%d, sessionID=%s, selected=%v", userID, sessionID, selected)

	// 已登录用户
	if userID > 0 {
		// 获取所有有效的购物车项
		items, err := s.cartRepo.ListCartItems(ctx, userID, true)
		if err != nil {
			logs.Error("获取购物车列表失败: %v", err)
			return err
		}

		// 收集所有ID
		ids := make([]int64, 0, len(items))
		for _, item := range items {
			ids = append(ids, item.ID)
		}

		if len(ids) == 0 {
			return nil
		}

		// 执行批量更新
		return s.cartRepo.BatchUpdateSelected(ctx, ids, userID, selected)
	}

	// TODO: 未登录用户的购物车全选逻辑
	return nil
}

// ClearCart 清空购物车
func (s *CartServiceImpl) ClearCart(ctx context.Context, req *dto.ClearCartRequest) error {
	logs.Info("清空购物车: %+v", req)

	if req == nil {
		return errors.New("请求参数不能为空")
	}

	// 已登录用户
	if req.UserID > 0 {
		return s.cartRepo.ClearCart(ctx, req.UserID)
	}

	// TODO: 未登录用户的购物车清空逻辑
	return nil
}

// ClearInvalidItems 清除无效商品
func (s *CartServiceImpl) ClearInvalidItems(ctx context.Context, userID int64, sessionID string) error {
	logs.Info("清除无效商品: userID=%d, sessionID=%s", userID, sessionID)

	// 已登录用户
	if userID > 0 {
		return s.cartRepo.ClearInvalidItems(ctx, userID)
	}

	// TODO: 未登录用户的清除无效商品逻辑
	return nil
}

// MergeCart 合并购物车（未登录 -> 已登录）
func (s *CartServiceImpl) MergeCart(ctx context.Context, req *dto.MergeCartRequest) error {
	logs.Info("合并购物车: %+v", req)

	if req == nil || req.UserID <= 0 || req.SessionID == "" {
		return errors.New("请求参数不完整")
	}

	// TODO: 获取未登录购物车数据
	// 这里假设未登录购物车数据存储在Redis中，通过SessionID获取
	// 实际实现时需要从相应的存储中获取数据

	// TODO: 遍历未登录购物车数据，合并到已登录用户的购物车中
	// 对于相同商品，需要合并数量
	// 示例代码：
	/*
		unloginCart, err := s.getUnloginCart(ctx, req.SessionID)
		if err != nil {
			logs.Error("获取未登录购物车失败: %v", err)
			return err
		}

		for _, item := range unloginCart.Items {
			// 转换为AddToCartRequest
			addReq := &dto.AddToCartRequest{
				UserID:    req.UserID,
				ProductID: item.ProductID,
				SkuID:     item.SkuID,
				Quantity:  item.Quantity,
				Price:     item.Price,
				Selected:  item.Selected,
			}

			// 添加到已登录用户的购物车
			_, err := s.AddToCart(ctx, addReq)
			if err != nil {
				logs.Error("合并购物车项失败: %v", err)
				// 继续处理下一个商品
				continue
			}
		}

		// 清除未登录购物车数据
		err = s.clearUnloginCart(ctx, req.SessionID)
		if err != nil {
			logs.Error("清除未登录购物车失败: %v", err)
			// 不影响合并结果
		}
	*/

	return nil
}

// GetSelectedItems 获取选中的购物车商品（用于结算）
func (s *CartServiceImpl) GetSelectedItems(ctx context.Context, userID int64, sessionID string) (*dto.CartResponse, error) {
	logs.Info("获取选中的购物车商品: userID=%d, sessionID=%s", userID, sessionID)

	cart, err := s.GetCart(ctx, userID, sessionID)
	if err != nil {
		return nil, err
	}

	// 过滤出已选中且有效的商品
	selectedItems := make([]*dto.CartItemResponse, 0)
	totalPrice := 0.0
	totalQuantity := 0

	for _, item := range cart.ValidItems {
		if item.Selected {
			selectedItems = append(selectedItems, item)
			totalPrice += item.Subtotal
			totalQuantity += item.Quantity
		}
	}

	// 构建结算购物车
	settlement := &dto.CartResponse{
		Items:            selectedItems,
		ValidItems:       selectedItems,
		InvalidItems:     []*dto.CartItemResponse{},
		TotalQuantity:    totalQuantity,
		SelectedQuantity: totalQuantity,
		TotalPrice:       totalPrice,
		SelectedPrice:    totalPrice,
		HasInvalid:       false,
		UserID:           userID,
		SessionID:        sessionID,
	}

	return settlement, nil
}

// buildCartResponse 构建购物车响应
func (s *CartServiceImpl) buildCartResponse(ctx context.Context, items []*models.CartItem, userID int64, sessionID string) (*dto.CartResponse, error) {
	resp := &dto.CartResponse{
		Items:         make([]*dto.CartItemResponse, 0, len(items)),
		ValidItems:    make([]*dto.CartItemResponse, 0),
		InvalidItems:  make([]*dto.CartItemResponse, 0),
		TotalQuantity: 0,
		TotalPrice:    0,
		HasInvalid:    false,
		UserID:        userID,
		SessionID:     sessionID,
	}

	totalSelected := 0
	selectedPrice := 0.0

	for _, item := range items {
		// TODO: 从商品服务获取商品最新价格、库存等信息，判断商品是否有效
		// 这里简化处理，直接使用数据库中的状态
		isValid := item.Status == constants.CartItemStatusValid

		// 构建购物车商品响应
		cartItem := &dto.CartItemResponse{
			ID:            item.ID,
			ProductID:     item.ProductID,
			ProductName:   item.ProductName,
			ProductImage:  item.ProductImage,
			SkuID:         item.SkuID,
			SkuAttributes: item.SkuAttributes,
			MerchantID:    0,  // TODO: 商品服务获取
			MerchantName:  "", // TODO: 商品服务获取
			Price:         item.Price,
			OriginalPrice: item.OriginalPrice,
			Quantity:      item.Quantity,
			Stock:         999, // TODO: 商品服务获取
			Selected:      item.Selected,
			Status:        item.Status,
			StatusText:    constants.CartStatusMap[item.Status],
			Subtotal:      item.Price * float64(item.Quantity),
			IsValid:       isValid,
			CreatedAt:     item.CreatedAt,
			UpdatedAt:     item.UpdatedAt,
		}

		// 设置无效原因
		if !isValid {
			cartItem.InvalidReason = "商品已下架或库存不足"
			resp.HasInvalid = true
		}

		// 添加到响应列表
		resp.Items = append(resp.Items, cartItem)

		// 根据商品状态分类
		if isValid {
			resp.ValidItems = append(resp.ValidItems, cartItem)
			resp.TotalQuantity += item.Quantity
			resp.TotalPrice += cartItem.Subtotal

			// 计算选中商品
			if item.Selected {
				totalSelected += item.Quantity
				selectedPrice += cartItem.Subtotal
			}
		} else {
			resp.InvalidItems = append(resp.InvalidItems, cartItem)
		}
	}

	// 设置选中数量和价格
	resp.SelectedQuantity = totalSelected
	resp.SelectedPrice = selectedPrice

	return resp, nil
}
