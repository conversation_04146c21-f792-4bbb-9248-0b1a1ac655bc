/**
 * 购物车服务接口定义
 *
 * 本文件定义了购物车模块的服务层接口，提供购物车业务逻辑的处理方法。
 * 包括添加商品到购物车、更新购物车、删除购物车等功能接口定义。
 */

package services

import (
	"context"

	"o_mall_backend/modules/cart/dto"
)

// CartService 购物车服务接口
type CartService interface {
	// 添加商品到购物车
	AddToCart(ctx context.Context, req *dto.AddToCartRequest) (*dto.CartItemResponse, error)

	// 获取购物车详情
	GetCart(ctx context.Context, userID int64, sessionID string) (*dto.CartResponse, error)

	// 获取购物车商品数量
	GetCartCount(ctx context.Context, userID int64, sessionID string) (int, error)

	// 更新购物车商品数量或选中状态
	UpdateCartItem(ctx context.Context, req *dto.UpdateCartItemRequest) error

	// 删除购物车商品
	DeleteCartItem(ctx context.Context, req *dto.DeleteCartItemRequest) error

	// 批量选择/取消选择购物车商品
	SelectCartItems(ctx context.Context, req *dto.SelectCartItemsRequest) error

	// 全选/取消全选购物车商品
	SelectAllCartItems(ctx context.Context, userID int64, sessionID string, selected bool) error

	// 清空购物车
	ClearCart(ctx context.Context, req *dto.ClearCartRequest) error

	// 清除无效商品
	ClearInvalidItems(ctx context.Context, userID int64, sessionID string) error

	// 合并购物车（未登录 -> 已登录）
	MergeCart(ctx context.Context, req *dto.MergeCartRequest) error

	// 获取选中的购物车商品（用于结算）
	GetSelectedItems(ctx context.Context, userID int64, sessionID string) (*dto.CartResponse, error)
}
