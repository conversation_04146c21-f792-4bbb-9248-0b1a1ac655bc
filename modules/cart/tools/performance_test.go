/**
 * 购物车性能测试工具
 * 
 * 用于测试购物车选择API的性能优化效果
 */

package tools

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/cart/dto"
	"o_mall_backend/modules/cart/services"
)

// PerformanceTestConfig 性能测试配置
type PerformanceTestConfig struct {
	UserID           int64         // 测试用户ID
	CartItemCount    int           // 购物车项数量
	BatchSize        int           // 批量操作大小
	ConcurrentUsers  int           // 并发用户数
	TestDuration     time.Duration // 测试持续时间
	WarmupDuration   time.Duration // 预热时间
}

// PerformanceTestResult 性能测试结果
type PerformanceTestResult struct {
	TotalRequests    int64         // 总请求数
	SuccessRequests  int64         // 成功请求数
	FailedRequests   int64         // 失败请求数
	AverageLatency   time.Duration // 平均延迟
	MinLatency       time.Duration // 最小延迟
	MaxLatency       time.Duration // 最大延迟
	P95Latency       time.Duration // 95分位延迟
	P99Latency       time.Duration // 99分位延迟
	ThroughputQPS    float64       // 吞吐量(QPS)
	ErrorRate        float64       // 错误率
}

// CartPerformanceTester 购物车性能测试器
type CartPerformanceTester struct {
	cartService services.CartService
	config      PerformanceTestConfig
	results     []time.Duration
	mutex       sync.Mutex
}

// NewCartPerformanceTester 创建购物车性能测试器
func NewCartPerformanceTester(cartService services.CartService, config PerformanceTestConfig) *CartPerformanceTester {
	return &CartPerformanceTester{
		cartService: cartService,
		config:      config,
		results:     make([]time.Duration, 0),
	}
}

// RunSelectPerformanceTest 运行选择操作性能测试
func (t *CartPerformanceTester) RunSelectPerformanceTest() (*PerformanceTestResult, error) {
	logs.Info("开始购物车选择操作性能测试")
	logs.Info("测试配置: %+v", t.config)

	// 准备测试数据
	cartItemIDs, err := t.prepareTestData()
	if err != nil {
		return nil, fmt.Errorf("准备测试数据失败: %v", err)
	}

	// 预热阶段
	logs.Info("开始预热阶段，持续时间: %v", t.config.WarmupDuration)
	t.warmup(cartItemIDs)

	// 重置结果
	t.mutex.Lock()
	t.results = make([]time.Duration, 0)
	t.mutex.Unlock()

	// 正式测试阶段
	logs.Info("开始正式测试阶段，持续时间: %v", t.config.TestDuration)
	result := t.runConcurrentTest(cartItemIDs)

	logs.Info("性能测试完成")
	return result, nil
}

// prepareTestData 准备测试数据
func (t *CartPerformanceTester) prepareTestData() ([]int64, error) {
	logs.Info("准备测试数据，购物车项数量: %d", t.config.CartItemCount)

	// 模拟创建购物车项
	cartItemIDs := make([]int64, 0, t.config.CartItemCount)
	
	for i := 0; i < t.config.CartItemCount; i++ {
		// 这里应该调用实际的添加购物车API
		// 为了测试，我们使用模拟的ID
		cartItemIDs = append(cartItemIDs, int64(i+1))
	}

	logs.Info("测试数据准备完成，购物车项ID: %v", cartItemIDs[:min(10, len(cartItemIDs))])
	return cartItemIDs, nil
}

// warmup 预热阶段
func (t *CartPerformanceTester) warmup(cartItemIDs []int64) {
	warmupEnd := time.Now().Add(t.config.WarmupDuration)
	
	for time.Now().Before(warmupEnd) {
		// 随机选择一批购物车项进行操作
		batchIDs := t.getRandomBatch(cartItemIDs)
		selected := rand.Intn(2) == 1
		
		ctx := context.Background()
		req := &dto.SelectCartItemsRequest{
			IDs:      batchIDs,
			UserID:   t.config.UserID,
			Selected: selected,
		}
		
		// 执行选择操作（忽略结果）
		t.cartService.SelectCartItems(ctx, req)
		
		// 短暂休息
		time.Sleep(10 * time.Millisecond)
	}
}

// runConcurrentTest 运行并发测试
func (t *CartPerformanceTester) runConcurrentTest(cartItemIDs []int64) *PerformanceTestResult {
	var wg sync.WaitGroup
	var totalRequests, successRequests, failedRequests int64
	
	testEnd := time.Now().Add(t.config.TestDuration)
	startTime := time.Now()
	
	// 启动并发用户
	for i := 0; i < t.config.ConcurrentUsers; i++ {
		wg.Add(1)
		go func(userIndex int) {
			defer wg.Done()
			
			for time.Now().Before(testEnd) {
				// 随机选择一批购物车项
				batchIDs := t.getRandomBatch(cartItemIDs)
				selected := rand.Intn(2) == 1
				
				// 执行选择操作并测量时间
				start := time.Now()
				err := t.executeSelectOperation(batchIDs, selected)
				latency := time.Since(start)
				
				// 记录结果
				t.mutex.Lock()
				t.results = append(t.results, latency)
				totalRequests++
				if err != nil {
					failedRequests++
				} else {
					successRequests++
				}
				t.mutex.Unlock()
				
				// 短暂休息模拟真实用户行为
				time.Sleep(time.Duration(rand.Intn(100)) * time.Millisecond)
			}
		}(i)
	}
	
	wg.Wait()
	
	// 计算测试结果
	return t.calculateResults(totalRequests, successRequests, failedRequests, time.Since(startTime))
}

// executeSelectOperation 执行选择操作
func (t *CartPerformanceTester) executeSelectOperation(cartItemIDs []int64, selected bool) error {
	ctx := context.Background()
	req := &dto.SelectCartItemsRequest{
		IDs:      cartItemIDs,
		UserID:   t.config.UserID,
		Selected: selected,
	}
	
	return t.cartService.SelectCartItems(ctx, req)
}

// getRandomBatch 获取随机批次
func (t *CartPerformanceTester) getRandomBatch(cartItemIDs []int64) []int64 {
	batchSize := min(t.config.BatchSize, len(cartItemIDs))
	if batchSize <= 0 {
		return []int64{}
	}
	
	// 随机选择起始位置
	start := rand.Intn(len(cartItemIDs) - batchSize + 1)
	return cartItemIDs[start : start+batchSize]
}

// calculateResults 计算测试结果
func (t *CartPerformanceTester) calculateResults(total, success, failed int64, duration time.Duration) *PerformanceTestResult {
	if len(t.results) == 0 {
		return &PerformanceTestResult{}
	}
	
	// 排序延迟数据
	latencies := make([]time.Duration, len(t.results))
	copy(latencies, t.results)
	
	// 简单排序
	for i := 0; i < len(latencies); i++ {
		for j := i + 1; j < len(latencies); j++ {
			if latencies[i] > latencies[j] {
				latencies[i], latencies[j] = latencies[j], latencies[i]
			}
		}
	}
	
	// 计算统计数据
	var totalLatency time.Duration
	for _, latency := range latencies {
		totalLatency += latency
	}
	
	avgLatency := totalLatency / time.Duration(len(latencies))
	minLatency := latencies[0]
	maxLatency := latencies[len(latencies)-1]
	p95Index := int(float64(len(latencies)) * 0.95)
	p99Index := int(float64(len(latencies)) * 0.99)
	p95Latency := latencies[p95Index]
	p99Latency := latencies[p99Index]
	
	throughput := float64(total) / duration.Seconds()
	errorRate := float64(failed) / float64(total) * 100
	
	result := &PerformanceTestResult{
		TotalRequests:   total,
		SuccessRequests: success,
		FailedRequests:  failed,
		AverageLatency:  avgLatency,
		MinLatency:      minLatency,
		MaxLatency:      maxLatency,
		P95Latency:      p95Latency,
		P99Latency:      p99Latency,
		ThroughputQPS:   throughput,
		ErrorRate:       errorRate,
	}
	
	// 打印结果
	t.printResults(result)
	
	return result
}

// printResults 打印测试结果
func (t *CartPerformanceTester) printResults(result *PerformanceTestResult) {
	logs.Info("=== 购物车选择操作性能测试结果 ===")
	logs.Info("总请求数: %d", result.TotalRequests)
	logs.Info("成功请求数: %d", result.SuccessRequests)
	logs.Info("失败请求数: %d", result.FailedRequests)
	logs.Info("平均延迟: %v", result.AverageLatency)
	logs.Info("最小延迟: %v", result.MinLatency)
	logs.Info("最大延迟: %v", result.MaxLatency)
	logs.Info("P95延迟: %v", result.P95Latency)
	logs.Info("P99延迟: %v", result.P99Latency)
	logs.Info("吞吐量: %.2f QPS", result.ThroughputQPS)
	logs.Info("错误率: %.2f%%", result.ErrorRate)
	logs.Info("=====================================")
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
