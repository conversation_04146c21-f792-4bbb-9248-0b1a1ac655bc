/**
 * base_controller.go
 * 聊天模块控制器基类
 *
 * 该文件定义了聊天模块所有控制器的基类，包含基本请求处理、响应格式化等公共功能
 */

package controllers

import (
	"encoding/json"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
)

// BaseController 聊天模块共用控制器基类
type BaseController struct {
	web.Controller
}

// Prepare 请求处理前的准备工作，所有控制器方法执行前都会调用该方法
func (c *BaseController) Prepare() {
	// 日志记录请求路径
	logs.Debug("[BaseController.Prepare] 初始化控制器: %s", c.Ctx.Request.URL.Path)
}

// responseSuccess 响应成功
// 使用common/result包的OK方法提供统一响应
func (c *BaseController) responseSuccess(data interface{}) {
	result.OK(c.Ctx, data)
}

// responseError 响应错误
// 使用common/result包的HandleError方法提供统一错误响应
func (c *BaseController) responseError(code int, message, detail string) {
	err := result.NewError(code, message).WithDetails(detail)
	result.HandleError(c.Ctx, err)
}

// GetCurrentUserID 获取当前用户ID
func (c *BaseController) GetCurrentUserID() (int64, bool) {
	// 从上下文中获取user_id
	userID, ok := c.Ctx.Input.GetData("user_id").(int64)
	if !ok {
		// 如果user_id是float64类型（JSON数字默认类型），则进行转换
		if id, ok := c.Ctx.Input.GetData("user_id").(float64); ok {
			userID = int64(id)
			return userID, true
		}
		return 0, false
	}
	return userID, true
}

// GetCurrentUsername 获取当前用户名
func (c *BaseController) GetCurrentUsername() (string, bool) {
	username, ok := c.Ctx.Input.GetData("user_name").(string)
	return username, ok
}

// GetCurrentUserRole 获取当前用户角色
func (c *BaseController) GetCurrentUserRole() (string, bool) {
	role, ok := c.Ctx.Input.GetData("user_role").(string)
	return role, ok
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *BaseController) ParseRequest(req interface{}) error {
	// 获取请求的Content-Type
	contentType := c.Ctx.Request.Header.Get("Content-Type")
	logs.Info("请求Content-Type: %s", contentType)

	// 根据Content-Type处理不同格式的请求
	if strings.Contains(contentType, "application/json") {
		// 处理JSON格式请求
		if err := json.NewDecoder(c.Ctx.Request.Body).Decode(req); err != nil {
			logs.Error("JSON解析错误: %v", err)
			return err
		}
	} else if strings.Contains(contentType, "multipart/form-data") {
		// 处理multipart/form-data格式请求
		if err := c.Ctx.Request.ParseMultipartForm(32 << 20); err != nil {
			logs.Error("ParseMultipartForm错误: %v", err)
			return err
		}
		
		// 使用Beego自带的解析方法
		if err := c.Ctx.Input.Bind(req, "form"); err != nil {
			logs.Error("表单绑定错误: %v", err)
			return err
		}
	} else {
		// 处理application/x-www-form-urlencoded格式请求
		if err := c.Ctx.Request.ParseForm(); err != nil {
			logs.Error("ParseForm错误: %v", err)
			return err
		}
		
		// 使用Beego自带的解析方法
		if err := c.Ctx.Input.Bind(req, "form"); err != nil {
			logs.Error("表单绑定错误: %v", err)
			return err
		}
	}

	logs.Info("解析后的请求: %+v", req)
	return nil
}
