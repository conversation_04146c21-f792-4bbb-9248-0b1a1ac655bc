/**
 * blacklist_controller.go
 * 黑名单控制器
 *
 * 该文件提供黑名单相关的HTTP API处理逻辑，包括添加黑名单、移除黑名单、获取黑名单列表等功能
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/services"
)

// BlacklistController 黑名单控制器
type BlacklistController struct {
	BaseController
	blacklistService *services.BlacklistService
}

// Prepare 重写BaseController的Prepare方法，确保每次请求都获取正确的服务实例
func (c *BlacklistController) Prepare() {
	// 先调用基类的Prepare方法
	c.BaseController.Prepare()
	
	// 从服务容器获取服务实例
	container := services.GetServiceContainer()
	if container != nil {
		// 获取BlacklistService
		c.blacklistService = container.GetBlacklistService()
		if c.blacklistService == nil {
			logs.Error("[BlacklistController.Prepare] 从服务容器获取blacklistService失败")
		} else {
			logs.Debug("[BlacklistController.Prepare] 成功获取blacklistService实例")
		}
	}
}

// NewBlacklistController 创建黑名单控制器实例
func NewBlacklistController(blacklistService *services.BlacklistService) *BlacklistController {
	return &BlacklistController{
		blacklistService: blacklistService,
	}
}

// AddToBlacklist 添加用户到黑名单
// @Title 添加用户到黑名单
// @Description 将指定用户添加到当前用户的黑名单中
// @Param body body dto.AddBlacklistRequest true "黑名单信息"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /blacklist [post]
func (c *BlacklistController) AddToBlacklist() {
	// 获取当前用户ID
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户信息")
		return
	}
	
	// 获取用户角色
	role, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色信息")
		return
	}

	// 解析请求参数
	var req dto.AddBlacklistRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 添加到黑名单
	err := c.blacklistService.AddToBlacklist(userID, role, &req)
	if err != nil {
		c.responseError(result.CodeInternalError, "添加黑名单失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "添加成功",
	})
}

// RemoveFromBlacklist 从黑名单中移除用户
// @Title 从黑名单中移除用户
// @Description 将指定用户从当前用户的黑名单中移除
// @Param blacklist_id path int true "黑名单记录ID"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /blacklist/:blacklist_id [delete]
func (c *BlacklistController) RemoveFromBlacklist() {
	// 获取当前用户ID
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户信息")
		return
	}
	
	// 获取用户角色
	role, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色信息")
		return
	}

	// 获取黑名单ID
	blacklistIDStr := c.Ctx.Input.Param(":blacklist_id")
	blacklistID, err := strconv.ParseInt(blacklistIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的黑名单ID")
		return
	}

	// 从黑名单中移除
	err = c.blacklistService.RemoveFromBlacklist(userID, role, blacklistID, "")
	if err != nil {
		c.responseError(result.CodeInternalError, "移除黑名单失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "移除成功",
	})
}

// GetBlacklist 获取黑名单列表
// @Title 获取黑名单列表
// @Description 获取当前用户的黑名单列表
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} result.Response{data=dto.BlacklistListResponse} "黑名单列表"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /blacklist [get]
func (c *BlacklistController) GetBlacklist() {
	// 获取当前用户ID
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户信息")
		return
	}
	
	// 获取用户角色
	role, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色信息")
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 获取黑名单列表
	response, err := c.blacklistService.GetBlacklist(userID, role, page, pageSize)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取黑名单列表失败", err.Error())
		return
	}

	// 返回黑名单列表
	c.responseSuccess(map[string]interface{}{
		"message": "获取成功",
		"data":    response,
	})
}

// IsBlocked 检查用户是否被拉黑
// @Title 检查用户是否被拉黑
// @Description 检查指定用户是否在当前用户的黑名单中
// @Param target_id query int true "目标用户ID"
// @Param target_type query string false "目标用户类型，默认与当前用户类型相同"
// @Success 200 {object} result.Response{data=dto.IsBlockedResponse} "检查结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /blacklist/check [get]
func (c *BlacklistController) IsBlocked() {
	// 获取当前用户ID
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户信息")
		return
	}
	
	// 获取用户角色
	role, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色信息")
		return
	}

	// 获取目标用户ID
	targetIDStr := c.GetString("target_id")
	if targetIDStr == "" {
		c.responseError(result.CodeInvalidParams, "参数错误", "未提供目标用户ID")
		return
	}

	targetID, err := strconv.ParseInt(targetIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的目标用户ID")
		return
	}

	// 获取目标用户类型
	targetType := c.GetString("target_type")
	if targetType == "" {
		targetType = role // 如果未指定，默认与当前用户类型相同
	}

	// 检查是否被拉黑
	isBlocked, err := c.blacklistService.IsBlocked(userID, role, targetID, targetType)
	if err != nil {
		c.responseError(result.CodeInternalError, "检查黑名单状态失败", err.Error())
		return
	}

	// 返回检查结果
	c.responseSuccess(map[string]interface{}{
		"message": "检查成功",
		"data": dto.IsBlockedResponse{
			IsBlocked: isBlocked,
		},
	})
}

// Options 处理CORS预检请求
// @Title 处理CORS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Router /api/v1/chat/blacklist [options]
// @Router /api/v1/chat/blacklist/{target_id} [options]
// @Router /api/v1/chat/blacklist/check/{target_id} [options]
// @Success 200 {string} string "OK"
func (c *BlacklistController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}




