/**
 * friend_controller.go
 * 好友控制器
 *
 * 该文件提供好友关系相关的HTTP API处理逻辑，包括发送好友请求、接受/拒绝请求、管理好友列表等功能
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/services"
)

// FriendController 好友控制器
type FriendController struct {
	BaseController
	friendService *services.FriendService
}

// Prepare 重写BaseController的Prepare方法，确保每次请求都获取正确的服务实例
func (c *FriendController) Prepare() {
	// 先调用基类的Prepare方法
	c.BaseController.Prepare()
	
	// 从服务容器获取服务实例
	container := services.GetServiceContainer()
	if container != nil {
		// 获取FriendService
		c.friendService = container.GetFriendService()
		if c.friendService == nil {
			logs.Error("[FriendController.Prepare] 从服务容器获取friendService失败")
		} else {
			logs.Debug("[FriendController.Prepare] 成功获取friendService实例")
		}
	}
}

// NewFriendController 创建好友控制器实例
func NewFriendController(friendService *services.FriendService) *FriendController {
	return &FriendController{
		friendService: friendService,
	}
}

// SendFriendRequest 发送好友请求
// @Title 发送好友请求
// @Description 向指定用户发送好友请求
// @Param body body dto.SendFriendRequestDTO true "好友请求信息"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /friends/requests [post]
func (c *FriendController) SendFriendRequest() {
	logs.Info("发送好友请求")
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 解析请求参数
	var req dto.SendFriendRequestDTO
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 发送好友请求
	// 转换为AddFriendRequest结构体
	addReq := &dto.AddFriendRequest{
		FriendID:   req.FriendID,
		FriendType: req.FriendType,
		Message:    req.Message,
	}

	err := c.friendService.SendFriendRequest(userID, userRole, addReq)
	if err != nil {
		c.responseError(result.CodeInternalError, "发送好友请求失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "发送成功",
	})
}

// HandleFriendRequest 处理好友请求（接受/拒绝）
// @Title 处理好友请求
// @Description 接受或拒绝好友请求
// @Param request_id path int true "请求ID"
// @Param body body dto.HandleFriendRequestDTO true "处理决定"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /friends/requests/:request_id [post]
func (c *FriendController) HandleFriendRequest() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取请求ID
	requestIDStr := c.Ctx.Input.Param(":request_id")
	requestID, err := strconv.ParseInt(requestIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的请求ID")
		return
	}

	// 解析请求参数
	var req dto.HandleFriendRequestDTO
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 处理好友请求
	// 转换为HandleFriendRequestRequest结构体
	handleReq := &dto.HandleFriendRequestRequest{
		Action: req.Action,
		Reason: req.Reason,
	}
	logs.Info(userID)
	err = c.friendService.HandleFriendRequest(requestID, userID, userRole, handleReq)
	if err != nil {
		c.responseError(result.CodeInternalError, "处理好友请求失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "处理成功",
	})
}

// GetFriendList 获取好友列表
// @Title 获取好友列表
// @Description 获取当前用户的好友列表
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} result.Response{data=dto.FriendListResponse} "好友列表"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /friends [get]
func (c *FriendController) GetFriendList() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 获取好友列表
	response, err := c.friendService.GetFriendList(userID, userRole, page, pageSize)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取好友列表失败", err.Error())
		return
	}

	// 返回好友列表，使用标准分页格式返回
	result.OKWithPagination(c.Ctx, response.List, response.Total, page, pageSize)
}

// GetFriendRequestList 获取好友请求列表
// @Title 获取好友请求列表
// @Description 获取当前用户的好友请求列表
// @Param type query string false "请求类型：received(收到的)或sent(发送的)，默认received"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} result.Response{data=dto.FriendRequestListResponse} "好友请求列表"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /friends/requests [get]
func (c *FriendController) GetFriendRequestList() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取请求类型
	requestType := c.GetString("type", "received")
	if requestType != "received" && requestType != "sent" {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的请求类型")
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 获取好友请求列表
	var response interface{}
	var err error

	// 请求状态值 -1 表示不过滤状态，获取所有状态的请求
	// 实际状态值: 0-等待验证, 1-已接受, 2-已拒绝
	const statusAll = -1

	if requestType == "sent" {
		// 使用GetSentFriendRequestList方法获取发送的请求
		response, err = c.friendService.GetSentFriendRequestList(userID, userRole, statusAll, page, pageSize)
	} else {
		// 使用GetFriendRequestList方法获取接收的请求
		response, err = c.friendService.GetFriendRequestList(userID, userRole, statusAll, page, pageSize)
	}

	if err != nil {
		c.responseError(result.CodeInternalError, "获取好友请求列表失败", err.Error())
		return
	}

	// 输出调试日志
	if friendResponseList, ok := response.(*dto.FriendRequestListResponse); ok {
		logs.Debug("[GetFriendRequestList] 总记录数: %d", friendResponseList.Total)
		if len(friendResponseList.List) > 0 {
			for i, req := range friendResponseList.List {
				logs.Debug("[GetFriendRequestList] 好友请求 #%d: ID=%d, SenderName=%s, SenderAvatar=%s", 
					i+1, req.ID, req.SenderName, req.SenderAvatar)
			}
		}
	} else {
		logs.Error("[GetFriendRequestList] 响应类型不是 *dto.FriendRequestListResponse")
	}

	// 直接返回响应对象，不要嵌套在data字段中
	c.responseSuccess(response)
}

// DeleteFriend 删除好友
// @Title 删除好友
// @Description 删除指定好友
// @Param friend_id path int true "好友ID"
// @Param friend_type query string false "好友类型，默认与当前用户类型相同"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /friends/:friend_id [delete]
func (c *FriendController) DeleteFriend() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取要删除的好友ID
	friendIDStr := c.Ctx.Input.Param(":friend_id")
	friendID, err := strconv.ParseInt(friendIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的好友ID")
		return
	}

	// 获取好友类型
	friendType := c.GetString("friend_type")
	if friendType == "" {
		friendType = userRole // 如果未指定，默认与当前用户类型相同
	}

	// 删除好友
	err = c.friendService.DeleteFriend(userID, userRole, friendID, friendType)
	if err != nil {
		c.responseError(result.CodeInternalError, "删除好友失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "删除成功",
	})
}

// UpdateFriend 更新好友信息（备注等）
// @Title 更新好友信息
// @Description 更新好友备注等信息
// @Param friend_id path int true "好友ID"
// @Param friend_type query string false "好友类型，默认与当前用户类型相同"
// @Param body body dto.UpdateFriendDTO true "更新信息"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /friends/:friend_id [put]
func (c *FriendController) UpdateFriend() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取好友ID
	friendIDStr := c.Ctx.Input.Param(":friend_id")
	friendID, err := strconv.ParseInt(friendIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的好友ID")
		return
	}

	// 获取好友类型
	friendType := c.GetString("friend_type")
	if friendType == "" {
		friendType = userRole // 如果未指定，默认与当前用户类型相同
	}

	// 解析请求参数
	var req dto.UpdateFriendDTO
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 更新好友信息
	// 转换为UpdateFriendRequest结构体
	updateReq := &dto.UpdateFriendRequest{
		Remark: req.Remark,
		Status: req.Status,
	}

	err = c.friendService.UpdateFriend(userID, userRole, friendID, friendType, updateReq)
	if err != nil {
		c.responseError(result.CodeInternalError, "更新好友信息失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "更新成功",
	})
}

// SearchUser 搜索用户
// @Title 搜索用户
// @Description 根据用户名或手机号搜索用户，用于添加好友
// @Param keyword query string true "搜索关键词（用户名或手机号）"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} result.Response{data=dto.UserSearchResponse} "用户搜索结果"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /users/search [get]
func (c *FriendController) SearchUser() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取搜索参数
	keyword := c.GetString("keyword")
	if keyword == "" {
		c.responseError(result.CodeInvalidParams, "参数错误", "搜索关键词不能为空")
		return
	}

	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 构建搜索请求
	req := &dto.UserSearchRequest{
		Keyword:  keyword,
		Page:     page,
		PageSize: pageSize,
	}

	// 执行搜索
	response, err := c.friendService.SearchUsers(userID, userRole, req)
	if err != nil {
		c.responseError(result.CodeInternalError, "搜索用户失败", err.Error())
		return
	}

	// 返回搜索结果
	c.responseSuccess(map[string]interface{}{
		"message": "搜索成功",
		"data":    response,
	})
}

// Options 处理CORS预检请求
// @Title 处理CORS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Router /api/v1/chat/friends [options]
// @Router /api/v1/chat/friends/requests [options]
// @Router /api/v1/chat/friends/requests/{request_id}/accept [options]
// @Router /api/v1/chat/friends/requests/{request_id}/reject [options]
// @Router /api/v1/chat/friends/{friend_id} [options]
// @Router /api/v1/chat/friends/search [options]
// @Success 200 {string} string "OK"
func (c *FriendController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}
