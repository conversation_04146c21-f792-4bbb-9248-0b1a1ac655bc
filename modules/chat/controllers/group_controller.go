/**
 * group_controller.go
 * 群聊控制器
 *
 * 该文件提供群聊相关的HTTP API处理逻辑，包括群聊创建、查询、更新、删除等功能
 */

package controllers

import (
	"strconv"
	
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/services"
)

// GroupController 群聊控制器
type GroupController struct {
	BaseController
	groupService       *services.GroupService
	groupMemberService *services.GroupMemberService
}

// Prepare 重写BaseController的Prepare方法，确保每次请求都获取正确的服务实例
func (c *GroupController) Prepare() {
	// 先调用基类的Prepare方法
	c.BaseController.Prepare()
	
	// 从服务容器获取服务实例
	container := services.GetServiceContainer()
	if container != nil {
		// 获取GroupService
		c.groupService = container.GetGroupService()
		if c.groupService == nil {
			logs.Error("[GroupController.Prepare] 从服务容器获取groupService失败")
		} else {
			logs.Debug("[GroupController.Prepare] 成功获取groupService实例")
		}
		
		// 获取GroupMemberService
		c.groupMemberService = container.GetGroupMemberService()
		if c.groupMemberService == nil {
			logs.Error("[GroupController.Prepare] 从服务容器获取groupMemberService失败")
		} else {
			logs.Debug("[GroupController.Prepare] 成功获取groupMemberService实例")
		}
	}
}

// NewGroupController 创建群聊控制器实例
func NewGroupController(groupService *services.GroupService, groupMemberService *services.GroupMemberService) *GroupController {
	return &GroupController{
		groupService:       groupService,
		groupMemberService: groupMemberService,
	}
}

// CreateGroup 创建群聊
// @Title 创建群聊
// @Description 创建新的群聊
// @Param body body dto.CreateGroupRequest true "群聊信息"
// @Success 200 {object} result.Response{data=dto.GroupDTO} "群聊信息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups [post]
func (c *GroupController) CreateGroup() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 解析请求参数
	var req dto.CreateGroupRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 创建群聊
	groupDTO, err := c.groupService.CreateGroup(userID, userRole, &req)
	if err != nil {
		c.responseError(result.CodeInternalError, "创建群聊失败", err.Error())
		return
	}

	// 返回群聊信息
	c.responseSuccess(map[string]interface{}{
		"message": "创建成功",
		"data":    groupDTO,
	})
}

// GetGroupInfo 获取群聊信息
// @Title 获取群聊信息
// @Description 获取指定群聊的详细信息
// @Param group_id path int true "群ID"
// @Success 200 {object} result.Response{data=dto.GroupDTO} "群聊信息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id [get]
func (c *GroupController) GetGroupInfo() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取群ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	// 验证用户是否为群成员
	_, err = c.groupMemberService.GetMemberByUserID(groupID, userID, userRole)
	if err != nil {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该群聊")
		return
	}

	// 获取群聊信息
	groupDTO, err := c.groupService.GetGroupByID(groupID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取群聊信息失败", err.Error())
		return
	}

	// 返回群聊信息
	c.responseSuccess(map[string]interface{}{
		"message": "获取成功",
		"data":    groupDTO,
	})
}

// UpdateGroupInfo 更新群聊信息
// @Title 更新群聊信息
// @Description 更新指定群聊的信息
// @Param group_id path int true "群ID"
// @Param body body dto.UpdateGroupRequest true "群聊更新信息"
// @Success 200 {object} result.Response{data=dto.GroupDTO} "更新后的群聊信息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id [put]
func (c *GroupController) UpdateGroupInfo() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取群ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	// 解析请求参数
	var req dto.UpdateGroupRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 更新群聊信息
	groupDTO, err := c.groupService.UpdateGroup(groupID, userID, userRole, &req)
	if err != nil {
		c.responseError(result.CodeInternalError, "更新群聊信息失败", err.Error())
		return
	}

	// 返回更新后的群聊信息
	c.responseSuccess(map[string]interface{}{
		"message": "更新成功",
		"data":    groupDTO,
	})
}

// DismissGroup 解散群聊
// @Title 解散群聊
// @Description 解散指定的群聊
// @Param group_id path int true "群ID"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id/dismiss [post]
func (c *GroupController) DismissGroup() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取群ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	// 解散群聊
	err = c.groupService.DismissGroup(groupID, userID, userRole)
	if err != nil {
		c.responseError(result.CodeInternalError, "解散群聊失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "解散成功",
	})
}

// GetUserGroups 获取用户加入的群聊列表
// @Title 获取用户群聊列表
// @Description 获取当前用户加入的群聊列表
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} result.Response{data=dto.GroupListResponse} "群聊列表"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups [get]
func (c *GroupController) GetUserGroups() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 获取群聊列表
	response, err := c.groupService.GetUserGroups(userID, userRole, page, pageSize)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取群聊列表失败", err.Error())
		return
	}

	// 返回群聊列表，使用标准分页格式返回
	result.OKWithPagination(c.Ctx, response.List, response.Total, page, pageSize)
}

// Options 处理CORS预检请求
// @Title 处理CORS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Router /api/v1/chat/groups [options]
// @Router /api/v1/chat/groups/{group_id} [options]
// @Router /api/v1/chat/groups/{group_id}/dismiss [options]
// @Success 200 {string} string "OK"
func (c *GroupController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}
