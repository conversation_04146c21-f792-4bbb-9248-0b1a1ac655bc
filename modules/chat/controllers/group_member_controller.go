/**
 * group_member_controller.go
 * 群聊成员控制器
 *
 * 该文件提供群聊成员相关的HTTP API处理逻辑，包括成员添加、移除、更新和查询等功能
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/services"
)

// GroupMemberController 群聊成员控制器
type GroupMemberController struct {
	BaseController
	groupMemberService *services.GroupMemberService
}

// Prepare 重写BaseController的Prepare方法，确保每次请求都获取正确的服务实例
func (c *GroupMemberController) Prepare() {
	// 先调用基类的Prepare方法
	c.BaseController.Prepare()
	
	// 从服务容器获取服务实例
	container := services.GetServiceContainer()
	if container != nil {
		// 获取GroupMemberService
		c.groupMemberService = container.GetGroupMemberService()
		if c.groupMemberService == nil {
			logs.Error("[GroupMemberController.Prepare] 从服务容器获取groupMemberService失败")
		} else {
			logs.Debug("[GroupMemberController.Prepare] 成功获取groupMemberService实例")
		}
	}
}

// NewGroupMemberController 创建群聊成员控制器实例
func NewGroupMemberController(groupMemberService *services.GroupMemberService) *GroupMemberController {
	return &GroupMemberController{
		groupMemberService: groupMemberService,
	}
}

// GetGroupMembers 获取群成员列表
// @Title 获取群成员列表
// @Description 获取指定群聊的成员列表
// @Param group_id path int true "群ID"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认50"
// @Success 200 {object} result.Response{data=dto.GroupMemberListResponse} "成员列表"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id/members [get]
func (c *GroupMemberController) GetGroupMembers() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, _ := c.GetCurrentUserRole()

	// 获取群ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	// 验证用户是否为群成员
	_, err = c.groupMemberService.GetMemberByUserID(groupID, userID, userRole)
	if err != nil {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该群聊")
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 50)

	// 获取群成员列表
	response, err := c.groupMemberService.GetMembers(groupID, page, pageSize)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取群成员列表失败", err.Error())
		return
	}

	// 返回群成员列表
	c.responseSuccess(map[string]interface{}{
		"message": "获取成功",
		"data":    response,
	})
}

// AddGroupMember 添加群成员
// @Title 添加群成员
// @Description 向群聊中添加新成员
// @Param group_id path int true "群ID"
// @Param body body dto.AddGroupMemberRequest true "成员信息"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id/members [post]
func (c *GroupMemberController) AddGroupMember() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, _ := c.GetCurrentUserRole()

	// 获取群ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	// 解析请求参数
	var req dto.AddGroupMemberRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 添加群成员
	err = c.groupMemberService.AddMember(groupID, userID, userRole, &req)
	if err != nil {
		c.responseError(result.CodeInternalError, "添加群成员失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "添加成功",
		"data":    nil,
	})
}

// RemoveGroupMember 移除群成员
// @Title 移除群成员
// @Description 从群聊中移除指定成员
// @Param group_id path int true "群ID"
// @Param member_id path int true "成员ID"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id/members/:member_id [delete]
func (c *GroupMemberController) RemoveGroupMember() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, _ := c.GetCurrentUserRole()

	// 获取群ID和成员ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	memberIDStr := c.Ctx.Input.Param(":member_id")
	
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	memberID, err := strconv.ParseInt(memberIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "成员ID无效")
		return
	}

	// 移除群成员
	// 这里假设memberID也是用户，类型为"user"
	err = c.groupMemberService.RemoveMember(groupID, userID, memberID, userRole, "user")
	if err != nil {
		c.responseError(result.CodeInternalError, "移除群成员失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "移除成功",
	})
}

// UpdateGroupMember 更新群成员信息
// @Title 更新群成员信息
// @Description 更新群聊中指定成员的信息
// @Param group_id path int true "群ID"
// @Param member_id path int true "成员ID"
// @Param member_type query string false "成员类型"
// @Param body body dto.UpdateGroupMemberRequest true "成员信息"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id/members/:member_id [put]
func (c *GroupMemberController) UpdateGroupMember() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, _ := c.GetCurrentUserRole()

	// 获取群ID和成员ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	memberIDStr := c.Ctx.Input.Param(":member_id")
	
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	memberID, err := strconv.ParseInt(memberIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "成员ID无效")
		return
	}

	// 从URL参数获取成员类型
	memberType := c.GetString("member_type")
	if memberType == "" {
		memberType = userRole // 如果未指定，默认与当前用户类型相同
	}

	// 解析请求参数
	var req dto.UpdateGroupMemberRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 更新群成员信息
	err = c.groupMemberService.UpdateMember(groupID, userID, memberID, userRole, memberType, &req)
	if err != nil {
		c.responseError(result.CodeInternalError, "更新群成员信息失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "更新成功",
	})
}

// TransferOwner 转让群主
// @Title 转让群主
// @Description 将群主权限转让给指定成员
// @Param group_id path int true "群ID"
// @Param new_owner_id query int true "新群主ID"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /groups/:group_id/transfer [post]
func (c *GroupMemberController) TransferOwner() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, _ := c.GetCurrentUserRole()

	// 获取群ID和新群主ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	newOwnerIDStr := c.GetString("new_owner_id")
	
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群ID无效")
		return
	}

	newOwnerID, err := strconv.ParseInt(newOwnerIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "新群主ID无效")
		return
	}

	// 转让群主
	err = c.groupMemberService.TransferOwner(groupID, userID, newOwnerID, userRole)
	if err != nil {
		c.responseError(result.CodeInternalError, "转让群主失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "转让成功",
	})
}

// Options 处理CORS预检请求
// @Title 处理CORS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Router /api/v1/chat/groups/{group_id}/transfer [options]
// @Router /api/v1/chat/groups/{group_id}/members [options]
// @Router /api/v1/chat/groups/{group_id}/members/{member_id} [options]
// @Success 200 {string} string "OK"
func (c *GroupMemberController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}
