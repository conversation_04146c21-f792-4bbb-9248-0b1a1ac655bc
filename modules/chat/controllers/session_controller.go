/**
 * session_controller.go
 * 聊天会话控制器
 * 
 * 该文件实现了处理聊天会话相关的HTTP请求，包括创建会话、获取会话列表等
 */

package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
	"o_mall_backend/modules/chat/services"
)

// SessionController 处理聊天会话相关的API请求
type SessionController struct {
	BaseController
	chatService services.ChatService
}

// Prepare 重写BaseController的Prepare方法，确保每次请求都获取正确的服务实例
func (c *SessionController) Prepare() {
	// 先调用基类的Prepare方法
	c.BaseController.Prepare()
	
	// 从服务容器获取chatService实例
	container := services.GetServiceContainer()
	if container != nil {
		c.chatService = container.GetChatService()
		if c.chatService == nil {
			logs.Error("[SessionController.Prepare] 从服务容器获取chatService失败")
		} else {
			logs.Debug("[SessionController.Prepare] 成功获取chatService实例")
		}
	}
}

// NewSessionController 创建会话控制器实例
func NewSessionController(chatService services.ChatService) *SessionController {
	return &SessionController{
		chatService: chatService,
	}
}

// CreateSession 创建新的聊天会话
// @Title 创建会话
// @Description 创建新的聊天会话或获取已有会话
// @Param body body dto.CreateSessionRequest true "接收者信息"
// @Success 200 {object} result.Response{data=models.ChatSession} "会话信息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions [post]
func (c *SessionController) CreateSession() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	userRole, _ := c.GetCurrentUserRole()

	// 解析请求参数
	var req dto.CreateSessionRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 验证参数
	if req.ReceiverID <= 0 {
		c.responseError(result.CodeInvalidParams, "参数错误", "接收者ID无效")
		return
	}
	if req.ReceiverType == "" {
		c.responseError(result.CodeInvalidParams, "参数错误", "接收者类型不能为空")
		return
	}

	// 创建会话
	session, err := c.chatService.FindOrCreateSession(
		context.Background(),
		userID,
		userRole,
		req.ReceiverID,
		req.ReceiverType,
	)
	if err != nil {
		c.responseError(result.CodeInternalError, "创建会话失败", err.Error())
		return
	}

	// 返回会话信息
	c.responseSuccess(session)
}

// GetSessionList 获取用户的会话列表
// @Title 获取会话列表
// @Description 获取当前用户的会话列表
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} result.Response{data=dto.SessionListResponse} "会话列表"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions [get]
func (c *SessionController) GetSessionList() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	// 获取用户角色
	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 解析分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 获取会话列表 - 使用实际的用户角色而不是硬编码的"user"
	sessions, total, err := c.chatService.GetUserSessions(
		context.Background(),
		userID,
		userRole, // 使用从JWT token中获取的用户角色
		page,
		pageSize,
	)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话列表失败", err.Error())
		return
	}

	// 返回会话列表
	c.responseSuccess(map[string]interface{}{
		"list":       sessions,
		"total":      total,
		"page":       page,
		"page_size":  pageSize,
		"page_count": (total + int64(pageSize) - 1) / int64(pageSize),
	})
}

// JoinGroupSession 加入群聊会话
// @Title 加入群聊会话
// @Description 用户加入群聊会话
// @Param group_id path int true "群聊ID"
// @Success 200 {object} result.Response "加入成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 403 {object} result.Response "权限不足"
// @Failure 500 {object} result.Response "服务器错误"
// @router /groups/:group_id/join [post]
func (c *SessionController) JoinGroupSession() {
	// 获取用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "用户未登录", "无法获取用户ID")
		return
	}

	userRole, _ := c.GetCurrentUserRole()

	// 获取群聊ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群聊ID格式错误")
		return
	}

	// 调用服务加入群聊会话
	session, err := c.chatService.JoinGroupSession(context.Background(), groupID, userID, userRole)
	if err != nil {
		logs.Error("[JoinGroupSession] 加入群聊会话失败：%v", err)
		c.responseError(result.CodeInternalError, "加入群聊会话失败", err.Error())
		return
	}

	c.responseSuccess(map[string]interface{}{
		"group_id":   groupID,
		"user_id":    userID,
		"session_id": session.ID,
		"status":     "joined",
		"joined_at":  time.Now().Unix(),
	})
}

// LeaveGroupSession 离开群聊会话
// @Title 离开群聊会话
// @Description 用户离开群聊会话
// @Param group_id path int true "群聊ID"
// @Success 200 {object} result.Response "离开成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 403 {object} result.Response "权限不足"
// @Failure 500 {object} result.Response "服务器错误"
// @router /groups/:group_id/leave [post]
func (c *SessionController) LeaveGroupSession() {
	// 获取用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "用户未登录", "无法获取用户ID")
		return
	}

	userRole, _ := c.GetCurrentUserRole()

	// 获取群聊ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群聊ID格式错误")
		return
	}

	// 调用服务离开群聊会话
	err = c.chatService.LeaveGroupSession(context.Background(), groupID, userID, userRole)
	if err != nil {
		logs.Error("[LeaveGroupSession] 离开群聊会话失败：%v", err)
		c.responseError(result.CodeInternalError, "离开群聊会话失败", err.Error())
		return
	}

	c.responseSuccess(map[string]interface{}{
		"group_id": groupID,
		"user_id":  userID,
		"status":   "left",
		"left_at":  time.Now().Unix(),
	})
}

// GetGroupSessionDetails 获取群聊会话详情
// @Title 获取群聊会话详情
// @Description 获取群聊会话的详细信息
// @Param group_id path int true "群聊ID"
// @Success 200 {object} result.Response{data=dto.SessionDTO} "会话详情"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 403 {object} result.Response "权限不足"
// @Failure 404 {object} result.Response "会话不存在"
// @Failure 500 {object} result.Response "服务器错误"
// @router /groups/:group_id/session [get]
func (c *SessionController) GetGroupSessionDetails() {
	// 获取用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "用户未登录", "无法获取用户ID")
		return
	}

	userRole, _ := c.GetCurrentUserRole()

	// 获取群聊ID
	groupIDStr := c.Ctx.Input.Param(":group_id")
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "群聊ID格式错误")
		return
	}

	// 获取群聊会话详情
	sessionDTO, err := c.chatService.GetGroupSessionDetails(context.Background(), groupID, userID, userRole)
	if err != nil {
		logs.Error("[GetGroupSessionDetails] 获取群聊会话详情失败：%v", err)
		c.responseError(result.CodeInternalError, "获取群聊会话详情失败", err.Error())
		return
	}

	c.responseSuccess(map[string]interface{}{
		"data": sessionDTO,
	})
}

// GetSession 获取指定会话的详细信息
// @Title 获取会话详情
// @Description 获取指定会话的详细信息
// @Param id path int true "会话ID"
// @Success 200 {object} result.Response{data=models.ChatSession} "会话信息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "会话不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:id [get]
func (c *SessionController) GetSession() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 获取会话信息
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话失败", err.Error())
		return
	}

	// 验证当前用户是否有权限访问该会话
	if session.CreatorID != userID && session.ReceiverID != userID {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}

	// 返回会话信息
	c.responseSuccess(session)
}

// MarkAsRead 标记会话消息为已读
// @Title 标记会话消息为已读
// @Description 将指定会话中的所有未读消息标记为已读
// @Param id path int true "会话ID"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:id/read [put]
func (c *SessionController) MarkAsRead() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 获取会话信息验证权限
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话失败", err.Error())
		return
	}
	
	// 根据会话类型进行权限验证
	if session.Type == models.SessionTypeGroup {
		// 群聊：检查用户是否为群成员
		groupMemberService := &services.GroupMemberService{}
		_, err := groupMemberService.GetMemberByUserID(session.ReceiverID, userID, "user")
		if err != nil {
			c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
			return
		}
	} else {
		// 一对一聊天：检查是否为创建者或接收者
		if session.CreatorID != userID && session.ReceiverID != userID {
			c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
			return
		}
	}

	// 标记会话消息为已读
	err = c.chatService.MarkMessagesAsRead(context.Background(), sessionID, userID)
	if err != nil {
		c.responseError(result.CodeInternalError, "标记消息为已读失败", err.Error())
		return
	}

	// 返回操作结果
	c.responseSuccess(nil)
}

// UpdateSession 更新会话信息
// @Title 更新会话
// @Description 更新指定会话的信息，如状态
// @Param session_id path int true "会话ID"
// @Param body body dto.UpdateSessionRequest true "会话更新信息"
// @Success 200 {object} result.Response{data=dto.SessionDTO} "更新后的会话信息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:session_id [put]
func (c *SessionController) UpdateSession() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":session_id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 获取会话信息验证权限
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话失败", err.Error())
		return
	}

	// 验证当前用户是否有权限访问该会话
	if session.CreatorID != userID && session.ReceiverID != userID {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}

	// 解析请求参数
	var req dto.UpdateSessionRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 更新会话状态
	// 注意：这里需要在services层添加相应的实现
	// 暂时模拟更新成功
	session.Status = req.Status
	
	// 返回更新后的会话信息
	c.responseSuccess(session)
}

// DeleteSession 删除会话
// @Title 删除会话
// @Description 删除指定的会话
// @Param session_id path int true "会话ID"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:session_id [delete]
func (c *SessionController) DeleteSession() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":session_id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 获取会话信息验证权限
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话失败", err.Error())
		return
	}

	// 验证当前用户是否有权限访问该会话
	if session.CreatorID != userID && session.ReceiverID != userID {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}

	// 删除会话操作
	// 注意：这里需要在services层添加相应的实现
	// 暂时模拟删除成功
	
	// 返回操作结果
	c.responseSuccess(map[string]interface{}{
		"message": "删除成功",
	})
}

// JoinSession 加入聊天会话
// @Title 加入会话
// @Description 加入指定的聊天会话，用于前端初始化WebSocket连接
// @Param id path int true "会话ID"
// @Success 200 {object} result.Response "操作结果"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 404 {object} result.Response "会话不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:id/join [post]
func (c *SessionController) JoinSession() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}
	
	// 获取用户角色
	userRole, _ := c.GetCurrentUserRole()

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 检查chatService是否初始化
	if c.chatService == nil {
		logs.Error("[JoinSession] chatService未初始化")
		c.responseError(result.CodeInternalError, "服务器内部错误", "chatService没有正确初始化")
		return
	}
	
	// 获取会话信息验证权限
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		logs.Warning("[JoinSession] 会话 %d 不存在，尝试创建新会话", sessionID)
		
		// 从请求体获取接收者ID和类型，这通常是会话的另一方
		// 尝试从不同位置获取参数（URL查询参数、POST表单、JSON请求体）
		var receiverIDStr string
		var receiverID int64
		var receiverType string
		
		// 记录请求信息以便调试
		logs.Debug("[JoinSession] 请求方法: %s, ContentType: %s", c.Ctx.Input.Method(), c.Ctx.Input.Header("Content-Type"))
		
		// 1. 尝试从JSON请求体获取
		var jsonBody map[string]interface{}
		if err := json.Unmarshal(c.Ctx.Input.RequestBody, &jsonBody); err == nil {
			logs.Debug("[JoinSession] 成功解析JSON请求体: %v", jsonBody)
			if rid, ok := jsonBody["receiver_id"].(string); ok {
				receiverIDStr = rid
			} else if rid, ok := jsonBody["receiver_id"].(float64); ok {
				// JSON中的数字会被解析为float64
				receiverID = int64(rid)
				receiverIDStr = fmt.Sprintf("%d", receiverID)
			}
			
			if rt, ok := jsonBody["receiver_type"].(string); ok {
				receiverType = rt
			}
		}
		
		// 2. 如果从JSON中没有获取到，尝试从POST表单获取
		if receiverIDStr == "" && receiverID == 0 {
			receiverIDStr = c.GetString("receiver_id")
		}
		
		if receiverType == "" {
			receiverType = c.GetString("receiver_type")
		}
		
		// 3. 如果还是没有获取到，尝试从URL查询参数获取
		if receiverIDStr == "" && receiverID == 0 {
			receiverIDStr = c.Ctx.Input.Query("receiver_id")
		}
		
		if receiverType == "" {
			receiverType = c.Ctx.Input.Query("receiver_type")
		}
		
		// 如果还是没有获取到接收者ID，返回错误
		logs.Debug("[JoinSession] 最终获取到的参数: receiverIDStr=%s, receiverID=%d, receiverType=%s", 
			receiverIDStr, receiverID, receiverType)
		
		// 如果还需要解析receiverID
		if receiverID == 0 && receiverIDStr != "" {
			var parseErr error
			receiverID, parseErr = strconv.ParseInt(receiverIDStr, 10, 64)
			if parseErr != nil {
				logs.Error("[JoinSession] 无法解析接收者ID: %v", parseErr)
				c.responseError(result.CodeInvalidParams, "参数错误", "接收者ID格式无效")
				return
			}
		}
		
		if receiverID <= 0 {
			c.responseError(result.CodeInvalidParams, "参数错误", "接收者ID无效")
			return
		}
		
		// 设置默认的接收者类型
		if receiverType == "" {
			receiverType = "user"
		}
		
		// 创建新会话
		// ChatService.CreateSession方法需要分开的参数，而非DTO结构体
		// 参数分别是：上下文、创建者ID、创建者类型、接收者ID、接收者类型
		logs.Info("[JoinSession] 尝试创建新会话，创建者ID: %d, 接收者ID: %d, 接收者类型: %s", userID, receiverID, receiverType)
		newSession, err := c.chatService.CreateSession(context.Background(), userID, "user", receiverID, receiverType)
		if err != nil {
			logs.Error("[JoinSession] 创建会话失败：%v", err)
			c.responseError(result.CodeInternalError, "创建会话失败", err.Error())
			return
		}
		
		logs.Info("[JoinSession] 已为用户 %d 和接收者 %d 创建新会话 %d", userID, receiverID, newSession.ID)
		session = newSession
	}

	// 验证当前用户是否有权限访问该会话
	if session.CreatorID != userID && session.ReceiverID != userID {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}
	
	// 这里可以添加其他加入会话时的逻辑，比如：
	// 1. 标记用户对该会话的在线状态
	// 2. 发送用户加入的通知消息
	// 3. 记录用户加入会话的时间等
	
	// 示例：记录用户上次查看会话的时间（可以用于计算未读消息）
	// 注意：此逻辑应该在service层实现，这里暂时作为注释提供思路
	// err = c.chatService.UpdateSessionLastVisitTime(context.Background(), sessionID, userID)
	// if err != nil {
	// 	c.responseError(result.CodeInternalError, "更新会话访问时间失败", err.Error())
	// 	return
	// }
	
	// 返回当前会话的基本信息，以便前端更新界面
	c.responseSuccess(map[string]interface{}{
		"session_id": session.ID,
		"type": session.Type,
		"creator_id": session.CreatorID,
		"receiver_id": session.ReceiverID,
		"status": "joined",
		"joined_at": time.Now().Unix(),
		"user_id": userID,
		"user_role": userRole,
	})
}

// Options 处理CORS预检请求
// @Title 处理CORS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Router /api/v1/chat/sessions [options]
// @Router /api/v1/chat/sessions/{id} [options]
// @Router /api/v1/chat/sessions/{id}/read [options]
// @Router /api/v1/chat/sessions/{id}/join [options]
// @Success 200 {string} string "OK"
func (c *SessionController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}
