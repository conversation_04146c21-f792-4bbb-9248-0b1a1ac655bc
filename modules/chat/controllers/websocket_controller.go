/**
 * websocket_controller.go
 * WebSocket控制器
 * 
 * 该文件实现了处理WebSocket连接的控制器，管理WebSocket的建立和认证
 */

package controllers

import (
	"net/http"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/gorilla/websocket"

	"o_mall_backend/modules/chat/services"
	"o_mall_backend/utils"
)

// WebSocketController 处理WebSocket连接的控制器
type WebSocketController struct {
	BaseController
	wsManager services.WebSocketManager
}

// Prepare 重写BaseController的Prepare方法，确保每次请求都获取正确的WebSocketManager实例
func (c *WebSocketController) Prepare() {
	// 先调用基类的Prepare方法
	c.BaseController.Prepare()
	
	// 从服务容器获取wsManager实例
	container := services.GetServiceContainer()
	if container != nil {
		c.wsManager = container.GetWebSocketManager()
		if c.wsManager == nil {
			logs.Error("[WebSocketController.Prepare] 从服务容器获取WebSocketManager失败")
		} else {
			logs.Debug("[WebSocketController.Prepare] 成功获取WebSocketManager实例")
		}
	}
}

// 定义WebSocket升级器
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	// 允许所有CORS请求（生产环境中应该限制）
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// NewWebSocketController 创建WebSocket控制器
func NewWebSocketController(wsManager services.WebSocketManager) *WebSocketController {
	return &WebSocketController{
		wsManager: wsManager,
	}
}

// Connect 处理WebSocket连接请求
// @Title 建立WebSocket连接
// @Description 建立客户端和服务器之间的WebSocket连接
// @Param token query string true "JWT令牌"
// @Success 101 {string} string "WebSocket连接已建立"
// @Failure 401 {string} string "未授权"
// @router /ws [get]
func (c *WebSocketController) Connect() {
	// 由于WebSocket请求被中间件放行，这里需要自行处理token验证
	// 尝试从查询参数获取令牌
	token := c.GetString("token")
	if token == "" {
		c.responseError(http.StatusUnauthorized, "未授权", "未提供令牌")
		return
	}
	
	// 验证JWT令牌
	claims, err := utils.ParseToken(token)
	if err != nil {
		c.responseError(http.StatusUnauthorized, "未授权", "令牌无效: " + err.Error())
		return
	}
	
	// 从令牌中获取用户信息
	userID := claims.UserID
	userRole := claims.Role
	
	logs.Info("[WebSocketController] 用户 %d(%s) 正在请求WebSocket连接", userID, userRole)
	
	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Ctx.ResponseWriter, c.Ctx.Request, nil)
	if err != nil {
		logs.Error("[WebSocketController] 升级WebSocket连接失败: %v", err)
		return
	}

	// 创建客户端对象
	client := &services.WebSocketClient{
		UserID:     userID,
		UserType:   userRole,
		Connection: conn,
		Send:       make(chan []byte, 256), // 缓冲通道
		Manager:    c.wsManager,
	}

	// 注册客户端
	c.wsManager.RegisterClient(client)

	// 注意：连接处理已在RegisterClient中启动
	logs.Info("[WebSocketController] 用户 %d(%s) WebSocket连接成功建立", userID, userRole)
}

// 辅助函数：从查询参数获取会话ID
func getSessionIDFromQuery(c *BaseController) (int64, error) {
	sessionIDStr := c.GetString("session_id")
	return strconv.ParseInt(sessionIDStr, 10, 64)
}
