# 管理员聊天功能扩展文档

## 1. 概述

本文档描述了聊天模块对管理员角色的扩展支持，使系统能够支持用户-管理员和管理员-商家之间的聊天功能。

## 2. 扩展内容

### 2.1 新增会话类型

在 `modules/chat/models/chat_session.go` 中新增了两种会话类型：

```go
// 会话类型常量
const (
    SessionTypeUserToUser      = "user_to_user"      // 用户对用户
    SessionTypeUserToMerchant  = "user_to_merchant"  // 用户对商户
    SessionTypeUserToAdmin     = "user_to_admin"     // 用户对管理员 (新增)
    SessionTypeAdminToMerchant = "admin_to_merchant" // 管理员对商户 (新增)
    SessionTypeGroup           = "group"             // 群聊
)
```

### 2.2 智能会话类型判断

在 `modules/chat/services/impl/chat_service_impl.go` 中新增了 `determineSessionType` 方法：

```go
// determineSessionType 根据创建者和接收者类型确定会话类型
func (s *ChatServiceImpl) determineSessionType(creatorType, receiverType string) string {
    // 如果是相同类型的用户，默认为用户对用户
    if creatorType == receiverType {
        return models.SessionTypeUserToUser
    }
    
    // 根据不同的用户类型组合确定会话类型
    switch {
    case (creatorType == "user" && receiverType == "merchant") || (creatorType == "merchant" && receiverType == "user"):
        return models.SessionTypeUserToMerchant
    case (creatorType == "user" && receiverType == "admin") || (creatorType == "admin" && receiverType == "user"):
        return models.SessionTypeUserToAdmin
    case (creatorType == "admin" && receiverType == "merchant") || (creatorType == "merchant" && receiverType == "admin"):
        return models.SessionTypeAdminToMerchant
    default:
        // 默认返回用户对用户类型
        return models.SessionTypeUserToUser
    }
}
```

### 2.3 增强权限验证

在 `modules/chat/controllers/message_controller.go` 中新增了 `hasSessionPermission` 方法：

```go
// hasSessionPermission 检查用户是否有权限访问会话
func (c *MessageController) hasSessionPermission(session *models.ChatSession, userID int64, userRole string) bool {
    // 群聊权限验证
    if session.Type == models.SessionTypeGroup {
        // 检查用户是否为群成员
        groupMemberService := &services.GroupMemberService{}
        _, err := groupMemberService.GetMemberByUserID(session.ReceiverID, userID, userRole)
        return err == nil
    }
    
    // 一对一聊天权限验证
    // 检查是否为会话的参与者（创建者或接收者）
    isCreator := session.CreatorID == userID && session.CreatorType == userRole
    isReceiver := session.ReceiverID == userID && session.ReceiverType == userRole
    
    return isCreator || isReceiver
}
```

## 3. 支持的聊天场景

### 3.1 用户-管理员聊天
- **会话类型**: `user_to_admin`
- **使用场景**: 用户向管理员咨询、投诉、反馈等
- **权限控制**: 用户和管理员都可以发起和参与对话

### 3.2 管理员-商家聊天
- **会话类型**: `admin_to_merchant`
- **使用场景**: 管理员与商家沟通业务、审核、政策等
- **权限控制**: 管理员和商家都可以发起和参与对话

## 4. API 使用示例

### 4.1 创建用户-管理员会话

```javascript
// 用户发起与管理员的对话
const response = await fetch('/api/v1/chat/sessions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${userToken}`
  },
  body: JSON.stringify({
    receiver_id: 1,           // 管理员ID
    receiver_type: 'admin'    // 接收者类型为管理员
  })
});
```

### 4.2 创建管理员-商家会话

```javascript
// 管理员发起与商家的对话
const response = await fetch('/api/v1/chat/sessions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${adminToken}`
  },
  body: JSON.stringify({
    receiver_id: 123,         // 商家ID
    receiver_type: 'merchant' // 接收者类型为商家
  })
});
```

### 4.3 管理员发送消息

```javascript
// 管理员发送文本消息
const response = await fetch(`/api/v1/chat/sessions/${sessionId}/messages/text`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${adminToken}`
  },
  body: JSON.stringify({
    content: '您好，我是客服管理员，有什么可以帮助您的吗？'
  })
});
```

## 5. WebSocket 消息处理

管理员客户端需要处理以下类型的WebSocket消息：

```javascript
// 管理员WebSocket客户端示例
const adminChatClient = new WebSocket(`wss://api.yourdomain.com/api/v1/chat/ws?token=${adminToken}`);

adminChatClient.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'message':
      if (message.event === 'new_message') {
        // 处理新消息
        handleNewMessage(message.data);
      }
      break;
    case 'notification':
      // 处理通知消息
      handleNotification(message.data);
      break;
  }
};
```

## 6. 数据库影响

### 6.1 现有数据兼容性
- 现有的会话数据不受影响
- 新的会话类型会自动应用于新创建的会话

### 6.2 索引优化建议
建议为会话表添加复合索引以提高查询性能：

```sql
-- 为会话类型和参与者添加索引
CREATE INDEX idx_chat_session_type_creator ON chat_session(type, creator_id, creator_type);
CREATE INDEX idx_chat_session_type_receiver ON chat_session(type, receiver_id, receiver_type);
```

## 7. 测试建议

### 7.1 功能测试
1. 测试用户-管理员会话创建
2. 测试管理员-商家会话创建
3. 测试消息发送和接收
4. 测试权限验证

### 7.2 权限测试
1. 验证非参与者无法访问会话
2. 验证不同角色的用户权限
3. 测试WebSocket连接的角色验证

## 8. 部署注意事项

1. **向后兼容**: 此扩展完全向后兼容，不会影响现有功能
2. **渐进部署**: 可以先部署后端扩展，前端可以逐步集成新功能
3. **监控建议**: 建议监控新会话类型的创建和使用情况

## 9. 后续扩展建议

1. **消息模板**: 为管理员提供常用回复模板
2. **自动分配**: 实现用户咨询的自动分配给在线管理员
3. **统计报表**: 添加管理员聊天工作量统计
4. **优先级管理**: 为不同类型的咨询设置优先级

通过以上扩展，聊天模块现在完全支持多角色间的聊天功能，为构建完整的客服系统奠定了基础。
