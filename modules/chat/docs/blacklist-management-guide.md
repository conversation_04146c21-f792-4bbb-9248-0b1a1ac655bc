# 黑名单管理功能集成指南

本文档详细介绍了聊天模块中黑名单管理功能的前端集成方法，包括将用户加入黑名单、从黑名单移除、获取黑名单列表以及检查用户是否被拉黑。

## API 接口

| 方法   | 路径                               | 描述                       |
| ------ | ---------------------------------- | -------------------------- |
| POST   | `/api/v1/chat/blacklist`           | 将用户添加到黑名单         |
| DELETE | `/api/v1/chat/blacklist/:blacklist_id` | 从黑名单中移除用户         |
| GET    | `/api/v1/chat/blacklist`           | 获取当前用户的黑名单列表   |
| GET    | `/api/v1/chat/blacklist/check`     | 检查指定用户是否被拉黑     |

---

## 前端集成示例

以下是使用 JavaScript `fetch` API 与黑名单管理接口交互的示例。请确保在请求头中携带有效的JWT认证令牌。

### 1. 将用户加入黑名单

**接口:** `POST /api/v1/chat/blacklist`

**请求体 (Request Body):**

```json
{
  "blocked_id": 123,          // 被拉黑用户的ID
  "blocked_type": "user",     // 被拉黑用户的类型 (e.g., 'user', 'admin')
  "reason": "恶意骚扰"         // 拉黑原因（可选）
}
```

**JavaScript 示例:**

```javascript
async function addToBlacklist(blockedId, blockedType, reason = '') {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch('/api/v1/chat/blacklist', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({ 
        blocked_id: blockedId,
        blocked_type: blockedType,
        reason: reason
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '添加到黑名单失败');
    }

    console.log('添加到黑名单成功:', result);
    // 在UI上执行相应操作，例如提示用户、更新列表等

  } catch (error) {
    console.error('添加到黑名单时出错:', error);
  }
}

// 使用示例
// addToBlacklist(123, 'user', '发布不当内容');
```

### 2. 从黑名单中移除用户

**接口:** `DELETE /api/v1/chat/blacklist/:blacklist_id`

**URL 参数:**

- `blacklist_id`: 黑名单记录的唯一ID。

**JavaScript 示例:**

```javascript
async function removeFromBlacklist(blacklistId) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch(`/api/v1/chat/blacklist/${blacklistId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '从黑名单移除失败');
    }

    console.log('从黑名单移除成功:', result);
    // 在UI上更新黑名单列表

  } catch (error) {
    console.error('从黑名单移除时出错:', error);
  }
}

// 使用示例
// removeFromBlacklist(5); // 假设要移除的黑名单记录ID为5
```

### 3. 获取黑名单列表

**接口:** `GET /api/v1/chat/blacklist`

**查询参数 (Query Parameters):**

- `page` (可选): 页码，默认为 `1`。
- `page_size` (可选): 每页数量，默认为 `20`。

**JavaScript 示例:**

```javascript
async function getBlacklist(page = 1, pageSize = 20) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch(`/api/v1/chat/blacklist?page=${page}&page_size=${pageSize}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '获取黑名单列表失败');
    }

    console.log('获取黑名单列表成功:', result.data);
    // 将获取到的黑名单列表渲染到UI上
    return result.data;

  } catch (error) {
    console.error('获取黑名单列表时出错:', error);
  }
}

// 使用示例
// getBlacklist().then(blacklistData => {
//   if (blacklistData) {
//     // 更新UI
//   }
// });
```

### 4. 检查用户是否被拉黑

**接口:** `GET /api/v1/chat/blacklist/check`

**查询参数 (Query Parameters):**

- `target_id`: 目标用户的ID。
- `target_type` (可选): 目标用户的类型，如果未提供，则默认为当前用户的类型。

**JavaScript 示例:**

```javascript
async function isUserBlocked(targetId, targetType) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  let url = `/api/v1/chat/blacklist/check?target_id=${targetId}`;
  if (targetType) {
      url += `&target_type=${targetType}`;
  }

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '检查用户黑名单状态失败');
    }

    console.log(`用户 ${targetId} 是否被拉黑:`, result.data.is_blocked);
    // 根据检查结果在UI上执行相应操作，例如禁止发送消息
    return result.data.is_blocked;

  } catch (error) {
    console.error('检查用户黑名单状态时出错:', error);
  }
}

// 使用示例
// isUserBlocked(456, 'user').then(isBlocked => {
//   if (isBlocked) {
//     alert('该用户已被您拉黑');
//   }
// });
```
