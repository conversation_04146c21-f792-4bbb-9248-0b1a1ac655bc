/**
 * 聊天模块前端示例代码
 * 
 * 这个文件提供了如何与聊天模块API交互的前端示例代码
 * 包括WebSocket连接、会话管理和消息收发的完整实现
 */

// 聊天客户端类
class ChatClient {
  constructor(apiBaseUrl, wsBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.wsBaseUrl = wsBaseUrl;
    this.authToken = authToken;
    this.socket = null;
    this.connected = false;
    this.messageHandlers = [];
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectTimeout = 3000;
  }

  // 初始化并连接WebSocket
  async init() {
    this.connectWebSocket();
    return this.fetchSessions();
  }

  // 建立WebSocket连接
  connectWebSocket() {
    const wsUrl = `${this.wsBaseUrl}/api/v1/chat/ws?token=${this.authToken}`;
    
    this.socket = new WebSocket(wsUrl);
    
    this.socket.onopen = () => {
      console.log('WebSocket连接已建立');
      this.connected = true;
      this.reconnectAttempts = 0;
      
      // 发送上线状态
      this.sendStatusUpdate('online');
    };
    
    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('收到WebSocket消息:', message);
        
        // 调用所有注册的消息处理器
        this.messageHandlers.forEach(handler => handler(message));
      } catch (error) {
        console.error('处理WebSocket消息失败:', error);
      }
    };
    
    this.socket.onclose = (event) => {
      this.connected = false;
      console.log('WebSocket连接已关闭, 代码:', event.code, '原因:', event.reason);
      
      // 尝试重新连接
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(`${this.reconnectTimeout / 1000}秒后尝试重新连接...`);
        setTimeout(() => {
          this.reconnectAttempts++;
          this.connectWebSocket();
        }, this.reconnectTimeout);
      } else {
        console.error('达到最大重连次数，停止尝试');
      }
    };
    
    this.socket.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }

  // 注册消息处理器
  onMessage(handler) {
    if (typeof handler === 'function') {
      this.messageHandlers.push(handler);
    }
  }

  // 发送状态更新
  sendStatusUpdate(status) {
    if (!this.connected) return;
    
    const statusMsg = {
      type: 'status',
      status: status,
      timestamp: Date.now()
    };
    
    this.socket.send(JSON.stringify(statusMsg));
  }

  // 获取会话列表
  async fetchSessions() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/chat/sessions`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`获取会话列表失败: ${response.status}`);
      }
      
      const result = await response.json();
      return result.data.sessions;
    } catch (error) {
      console.error('获取会话失败:', error);
      throw error;
    }
  }

  // 获取单个会话详情
  async getSession(sessionId) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/chat/sessions/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`获取会话详情失败: ${response.status}`);
      }
      
      const result = await response.json();
      return result.data.session;
    } catch (error) {
      console.error(`获取会话ID ${sessionId} 失败:`, error);
      throw error;
    }
  }

  // 创建新会话
  async createSession(receiverId, receiverType) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/chat/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({
          receiver_id: receiverId,
          receiver_type: receiverType
        })
      });
      
      if (!response.ok) {
        throw new Error(`创建会话失败: ${response.status}`);
      }
      
      const result = await response.json();
      return result.data.session;
    } catch (error) {
      console.error('创建会话失败:', error);
      throw error;
    }
  }

  // 标记会话为已读
  async markSessionAsRead(sessionId) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/chat/sessions/${sessionId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`标记会话为已读失败: ${response.status}`);
      }
      
      return true;
    } catch (error) {
      console.error(`标记会话 ${sessionId} 为已读失败:`, error);
      throw error;
    }
  }

  // 获取会话消息
  async getMessages(sessionId, page = 1, pageSize = 20, order = 'desc') {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}/api/v1/chat/sessions/${sessionId}/messages?page=${page}&page_size=${pageSize}&order=${order}`,
        {
          headers: {
            'Authorization': `Bearer ${this.authToken}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`获取消息失败: ${response.status}`);
      }

      const result = await response.json();
      return {
        messages: result.data.list,
        total: result.data.total,
        page: result.data.page,
        pageSize: result.data.page_size,
        pageCount: result.data.page_count
      };
    } catch (error) {
      console.error(`获取会话 ${sessionId} 的消息失败:`, error);
      throw error;
    }
  }

  // 发送文本消息
  async sendTextMessage(sessionId, content) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/chat/sessions/${sessionId}/messages/text`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({ content })
      });
      
      if (!response.ok) {
        throw new Error(`发送文本消息失败: ${response.status}`);
      }
      
      const result = await response.json();
      return result.data.message;
    } catch (error) {
      console.error(`发送文本消息到会话 ${sessionId} 失败:`, error);
      throw error;
    }
  }

  // 发送媒体消息
  async sendMediaMessage(sessionId, file, type) {
    // 验证媒体类型
    if (!['image', 'file', 'voice'].includes(type)) {
      throw new Error(`不支持的媒体类型: ${type}`);
    }
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/chat/sessions/${sessionId}/messages/media`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        },
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`发送媒体消息失败: ${response.status}`);
      }
      
      const result = await response.json();
      return result.data.message;
    } catch (error) {
      console.error(`发送媒体消息到会话 ${sessionId} 失败:`, error);
      throw error;
    }
  }

  // 关闭连接
  disconnect() {
    if (this.socket && this.connected) {
      this.sendStatusUpdate('offline');
      this.socket.close();
    }
  }
}

// 使用示例
async function chatExample() {
  // 初始化聊天客户端
  const apiBaseUrl = 'https://api.example.com';
  const wsBaseUrl = 'wss://api.example.com';
  const authToken = 'your-jwt-token';
  
  const chatClient = new ChatClient(apiBaseUrl, wsBaseUrl, authToken);
  
  // 监听消息
  chatClient.onMessage(message => {
    // 处理接收到的消息
    if (message.type === 'message' && message.event === 'new_message') {
      console.log(`收到来自会话 ${message.session_id} 的新消息:`, message.data);
      
      // 在UI中更新消息
      updateChatUI(message.session_id, message.data);
    }
  });
  
  // 初始化并获取会话列表
  const sessions = await chatClient.init();
  console.log('会话列表:', sessions);
  
  // 展示会话列表
  renderSessionsList(sessions);
  
  // 示例：当用户点击会话时
  async function onSessionClick(sessionId) {
    // 获取会话消息
    const { messages, total } = await chatClient.getMessages(sessionId);
    
    // 渲染消息
    renderMessages(messages);
    
    // 标记为已读
    await chatClient.markSessionAsRead(sessionId);
  }
  
  // 示例：当用户发送消息时
  async function onSendMessage(sessionId, text) {
    // 发送文本消息
    const sentMessage = await chatClient.sendTextMessage(sessionId, text);
    console.log('消息已发送:', sentMessage);
    
    // 在UI中立即显示发送的消息
    addMessageToUI(sentMessage);
  }
  
  // 示例：当用户上传图片时
  async function onImageUpload(sessionId, imageFile) {
    // 发送图片消息
    const sentMessage = await chatClient.sendMediaMessage(sessionId, imageFile, 'image');
    console.log('图片消息已发送:', sentMessage);
    
    // 在UI中显示上传的图片
    addImageMessageToUI(sentMessage);
  }
  
  // 注意：这里的UI更新函数仅为示例，实际应用需要实现这些函数
  function renderSessionsList(sessions) { /* 实现会话列表渲染 */ }
  function renderMessages(messages) { /* 实现消息渲染 */ }
  function updateChatUI(sessionId, message) { /* 实现消息UI更新 */ }
  function addMessageToUI(message) { /* 实现添加消息到UI */ }
  function addImageMessageToUI(message) { /* 实现添加图片消息到UI */ }
  
  // 在应用关闭前断开连接
  window.addEventListener('beforeunload', () => {
    chatClient.disconnect();
  });
}

// 在页面加载完成后运行示例
document.addEventListener('DOMContentLoaded', () => {
  // 确认用户已登录后再初始化聊天
  if (isUserLoggedIn()) {
    chatExample();
  }
});

function isUserLoggedIn() {
  // 实现检查用户是否已登录的逻辑
  return localStorage.getItem('authToken') !== null;
}

// 消息分页和排序示例
async function messagesPaginationExample() {
  const chatClient = new ChatClient('http://localhost:8080', 'your-auth-token');
  const sessionId = 1; // 示例会话ID

  try {
    // 获取最新消息（默认倒序）
    console.log('获取最新消息（倒序）:');
    const latestMessages = await chatClient.getMessages(sessionId, 1, 10, 'desc');
    console.log(latestMessages);

    // 获取最早消息（升序）
    console.log('获取最早消息（升序）:');
    const earliestMessages = await chatClient.getMessages(sessionId, 1, 10, 'asc');
    console.log(earliestMessages);

    // 分页获取消息
    console.log('分页获取消息:');
    for (let page = 1; page <= 3; page++) {
      const pageMessages = await chatClient.getMessages(sessionId, page, 5, 'desc');
      console.log(`第${page}页消息:`, pageMessages);
    }

  } catch (error) {
    console.error('获取消息失败:', error);
  }
}
