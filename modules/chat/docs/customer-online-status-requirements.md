# 商家聊天模块 - 客户在线状态需求文档

## 问题描述

当前商家聊天模块中，客户的在线状态一直显示为"离线"，即使客户正在与商家进行实时聊天时也显示为离线状态。这影响了商家对客户状态的准确判断。

## 当前实现分析

### 前端显示逻辑

客户在线状态在以下两个位置显示：

1. **聊天列表中的在线指示器**
```html
<span 
  v-if="session.customer_info?.is_online" 
  class="online-indicator"
></span>
```

2. **聊天详情页的状态显示**
```html
<span class="customer-status">
  <span 
    class="status-dot" 
    :class="{ 'online': currentSession.customer_info?.is_online }"
  ></span>
  {{ currentSession.customer_info?.is_online ? '在线' : '离线' }}
</span>
```

### 数据来源

当前客户在线状态来源于：
- **会话列表API**: `/v1/chat/sessions`
- **数据字段**: `session.customer_info.is_online`

### 问题根因

1. **静态数据**: 会话列表API返回的`is_online`字段是静态的，不会实时更新
2. **缺少实时更新机制**: 没有WebSocket消息或其他机制来实时更新客户在线状态
3. **状态同步缺失**: 客户连接/断开WebSocket时，商家端没有收到相应的状态变更通知

## 解决方案设计

### 1. API接口改进

#### 1.1 会话列表API优化
**接口**: `GET /v1/chat/sessions`

**当前返回格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 13,
        "customer_info": {
          "id": 3,
          "name": "testuser",
          "avatar": "http://example.com/avatar.png",
          "is_online": false  // 当前总是false
        }
      }
    ]
  }
}
```

**需要改进**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 13,
        "customer_info": {
          "id": 3,
          "name": "testuser",
          "avatar": "http://example.com/avatar.png",
          "is_online": true,  // 实时在线状态
          "last_seen": "2025-07-19T21:30:00+08:00",  // 最后在线时间
          "online_status": "active"  // 详细状态: active/idle/offline
        }
      }
    ]
  }
}
```

### 2. WebSocket实时状态推送

#### 2.1 客户上线通知
当客户连接WebSocket时，向所有相关商家推送：

```json
{
  "type": "notification",
  "event": "customer_online",
  "data": {
    "customer_id": 3,
    "customer_name": "testuser",
    "customer_avatar": "http://example.com/avatar.png",
    "session_ids": [13, 15],  // 该客户的所有会话ID
    "timestamp": "2025-07-19T21:30:00+08:00"
  }
}
```

#### 2.2 客户下线通知
当客户断开WebSocket时，向所有相关商家推送：

```json
{
  "type": "notification", 
  "event": "customer_offline",
  "data": {
    "customer_id": 3,
    "customer_name": "testuser",
    "session_ids": [13, 15],
    "last_seen": "2025-07-19T21:35:00+08:00",
    "timestamp": "2025-07-19T21:35:00+08:00"
  }
}
```

#### 2.3 客户状态变更通知
当客户状态发生变化时（如从活跃变为空闲）：

```json
{
  "type": "notification",
  "event": "customer_status_change", 
  "data": {
    "customer_id": 3,
    "old_status": "active",
    "new_status": "idle",
    "session_ids": [13, 15],
    "timestamp": "2025-07-19T21:40:00+08:00"
  }
}
```

### 3. 状态定义

#### 3.1 在线状态枚举
- `active`: 活跃在线（最近5分钟内有活动）
- `idle`: 空闲在线（连接但超过5分钟无活动）
- `offline`: 离线（WebSocket断开连接）

#### 3.2 判断逻辑
- **active**: WebSocket连接 + 最近5分钟内发送过消息或有其他活动
- **idle**: WebSocket连接 + 超过5分钟无活动
- **offline**: WebSocket断开连接超过30秒

### 4. 后端实现要点

#### 4.1 状态管理
1. **Redis缓存**: 使用Redis存储客户在线状态
   - Key: `customer_online:{customer_id}`
   - Value: `{"status": "active", "last_activity": "2025-07-19T21:30:00+08:00", "session_ids": [13, 15]}`
   - TTL: 5分钟（自动过期变为离线）

2. **状态更新时机**:
   - WebSocket连接建立时
   - 收到客户消息时
   - 客户发送心跳时
   - WebSocket连接断开时

#### 4.2 推送机制
1. **会话关联**: 维护客户与商家的会话关系映射
2. **批量推送**: 当客户状态变更时，向所有相关商家推送通知
3. **防重复**: 避免短时间内重复推送相同状态

### 5. 前端适配

#### 5.1 WebSocket消息处理
在`chatWebSocketService.ts`中添加客户状态处理：

```typescript
// 处理客户在线状态通知
private handleCustomerStatusNotification(data: any): void {
  switch (data.event) {
    case 'customer_online':
    case 'customer_offline':
    case 'customer_status_change':
      this.emit('customer_status_update', data)
      break
  }
}
```

#### 5.2 状态更新
在`MerchantChat.vue`中监听状态变更：

```typescript
// 监听客户状态更新
service.on('customer_status_update', (data) => {
  updateCustomerStatus(data)
})

const updateCustomerStatus = (statusData: any) => {
  const { customer_id, session_ids, new_status } = statusData.data
  
  // 更新相关会话的客户状态
  session_ids.forEach(sessionId => {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session && session.customer_info) {
      session.customer_info.is_online = new_status !== 'offline'
      session.customer_info.online_status = new_status
    }
  })
}
```

## 实现优先级

### 高优先级（必须实现）
1. ✅ WebSocket客户上线/下线通知
2. ✅ 会话列表API返回实时在线状态
3. ✅ 前端WebSocket消息处理和状态更新

### 中优先级（建议实现）
1. 🔄 详细状态区分（active/idle/offline）
2. 🔄 最后在线时间显示
3. 🔄 状态变更防抖处理

### 低优先级（可选实现）
1. ⭕ 客户活跃度统计
2. ⭕ 状态历史记录
3. ⭕ 批量状态查询接口

## 测试验证

### 测试场景
1. **客户上线**: 客户打开聊天页面，商家端应显示客户在线
2. **客户下线**: 客户关闭页面或断网，商家端应显示客户离线
3. **状态切换**: 客户在活跃和空闲状态间切换
4. **多会话**: 一个客户与多个商家的会话状态同步
5. **网络异常**: 网络断开重连后状态恢复

### 验收标准
- ✅ 客户实际在线时，商家端显示"在线"状态
- ✅ 客户离线后30秒内，商家端显示"离线"状态
- ✅ 状态变更实时推送，延迟不超过5秒
- ✅ 多个商家同时与同一客户聊天时状态一致

## 技术细节

### WebSocket连接管理
- 客户端连接时记录`customer_id`和`session_ids`的映射关系
- 商家端连接时记录`merchant_id`和可接收通知的会话范围
- 连接断开时清理相关状态和映射关系

### 性能考虑
- 使用Redis集群支持高并发状态查询
- 状态变更消息批量发送，减少WebSocket推送频率
- 客户端状态缓存，避免频繁API调用

### 容错处理
- WebSocket连接异常时的状态回退机制
- Redis故障时的降级策略（从数据库查询）
- 状态不一致时的自动修复机制

## API规范详细说明

### 1. 获取会话列表API
**接口**: `GET /v1/chat/sessions`
**Headers**: `Authorization: Bearer {merchant_token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 13,
        "type": "user_to_merchant",
        "creator_id": 3,
        "creator_type": "user",
        "receiver_id": 1,
        "receiver_type": "merchant",
        "target_name": "testuser",
        "target_avatar": "http://example.com/avatar.png",
        "customer_info": {
          "id": 3,
          "name": "testuser",
          "avatar": "http://example.com/avatar.png",
          "is_online": true,
          "online_status": "active",
          "last_seen": "2025-07-19T21:30:00+08:00"
        },
        "unread_count": 2,
        "last_message": {
          "id": 74,
          "content": "老板在吗",
          "sender_type": "user",
          "sender_name": "testuser",
          "created_at": "2025-07-19T21:09:52+08:00"
        },
        "created_at": "2025-07-19T20:58:48+08:00",
        "updated_at": "2025-07-19T21:09:52+08:00"
      }
    ],
    "page": 1,
    "total": 1
  }
}
```

### 2. WebSocket消息格式规范

#### 2.1 商家连接认证
**发送**: 商家WebSocket连接建立后发送
```json
{
  "type": "auth",
  "token": "merchant_jwt_token",
  "user_type": "merchant",
  "merchant_id": 1
}
```

#### 2.2 客户状态通知消息
**接收**: 服务器推送给商家的状态通知
```json
{
  "type": "notification",
  "event": "customer_online",
  "timestamp": "2025-07-19T21:30:00+08:00",
  "data": {
    "customer_id": 3,
    "customer_name": "testuser",
    "customer_avatar": "http://example.com/avatar.png",
    "online_status": "active",
    "session_ids": [13, 15],
    "affected_sessions": [
      {
        "session_id": 13,
        "merchant_id": 1
      },
      {
        "session_id": 15,
        "merchant_id": 2
      }
    ]
  }
}
```

### 3. 数据库表结构建议

#### 3.1 用户在线状态表
```sql
CREATE TABLE user_online_status (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  user_type ENUM('customer', 'merchant') NOT NULL,
  status ENUM('active', 'idle', 'offline') NOT NULL DEFAULT 'offline',
  last_activity TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  websocket_connection_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY uk_user_type (user_id, user_type),
  INDEX idx_status_activity (status, last_activity),
  INDEX idx_user_id (user_id)
);
```

#### 3.2 会话参与者表
```sql
CREATE TABLE chat_session_participants (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  session_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  user_type ENUM('customer', 'merchant') NOT NULL,
  role ENUM('creator', 'receiver') NOT NULL,
  joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  UNIQUE KEY uk_session_user (session_id, user_id, user_type),
  INDEX idx_session_id (session_id),
  INDEX idx_user_id (user_id)
);
```


## 注意事项

1. **时区处理**: 所有时间戳使用UTC时间，前端根据用户时区显示
2. **并发控制**: 同一客户多设备登录时的状态合并逻辑
3. **隐私保护**: 客户可选择隐藏在线状态的功能
4. **性能监控**: 监控WebSocket连接数和状态推送频率
5. **日志记录**: 记录状态变更日志便于问题排查
