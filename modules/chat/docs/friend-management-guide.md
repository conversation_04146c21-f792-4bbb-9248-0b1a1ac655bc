# 好友关系管理集成指南

本文档旨在为前端开发者提供一个关于如何集成聊天模块中好友关系管理功能的详尽指南。内容涵盖发送和处理好友请求、管理好友列表以及更新好友信息等。

## API 接口

| 方法   | 路径                                       | 描述                       |
| ------ | ------------------------------------------ | -------------------------- |
| POST   | `/api/v1/chat/friends/requests`            | 发送好友请求               |
| PUT    | `/api/v1/chat/friends/requests/:request_id`| 处理好友请求（接受/拒绝）  |
| GET    | `/api/v1/chat/friends`                     | 获取当前用户的好友列表     |
| GET    | `/api/v1/chat/friends/requests`            | 获取好友请求列表           |
| DELETE | `/api/v1/chat/friends/:friend_id`          | 删除好友                   |
| PUT    | `/api/v1/chat/friends/:friend_id`          | 更新好友信息（如备注）     |

---

## 前端集成示例

以下是使用 JavaScript `fetch` API 与好友管理接口交互的示例。请确保在所有请求的 `Authorization` 头中提供有效的JWT令牌。

### 1. 发送好友请求

**接口:** `POST /api/v1/chat/friends/requests`

**请求体 (Request Body):**

```json
{
  "friend_id": 456,         // 对方用户ID
  "friend_type": "user",    // 对方用户类型
  "message": "你好，我是张三，想加你为好友。" // 附加消息
}
```

**JavaScript 示例:**

```javascript
async function sendFriendRequest(friendId, friendType, message) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch('/api/v1/chat/friends/requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({ 
        friend_id: friendId,
        friend_type: friendType,
        message: message
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '发送好友请求失败');
    }

    console.log('好友请求发送成功:', result);
    alert('好友请求已发送！');

  } catch (error) {
    console.error('发送好友请求时出错:', error);
  }
}

// 使用示例
// sendFriendRequest(456, 'user', '你好，我是李四');
```

### 2. 处理好友请求

**接口:** `PUT /api/v1/chat/friends/requests/:request_id`

**URL 参数:**

- `request_id`: 好友请求的唯一ID。

**请求体 (Request Body):**

```json
{
  "action": "accept",  // 处理动作: 'accept' 或 'reject'
  "reason": ""        // 拒绝原因（仅在 action 为 'reject' 时可选）
}
```

**JavaScript 示例:**

```javascript
async function handleFriendRequest(requestId, action, reason = '') {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch(`/api/v1/chat/friends/requests/${requestId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({ action, reason })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '处理好友请求失败');
    }

    console.log('处理好友请求成功:', result);
    // 在UI上更新好友列表或请求列表

  } catch (error) {
    console.error('处理好友请求时出错:', error);
  }
}

// 使用示例
// handleFriendRequest(12, 'accept'); // 接受ID为12的请求
// handleFriendRequest(13, 'reject', '暂时不想加好友'); // 拒绝ID为13的请求
```

### 3. 获取好友列表

**接口:** `GET /api/v1/chat/friends`

**查询参数 (Query Parameters):**

- `page` (可选): 页码，默认为 `1`。
- `page_size` (可选): 每页数量，默认为 `20`。

**JavaScript 示例:**

```javascript
async function getFriendList(page = 1, pageSize = 20) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch(`/api/v1/chat/friends?page=${page}&page_size=${pageSize}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '获取好友列表失败');
    }

    console.log('获取好友列表成功:', result.data);
    // 将好友列表渲染到UI
    return result.data;

  } catch (error) {
    console.error('获取好友列表时出错:', error);
  }
}

// 使用示例
// getFriendList();
```

### 4. 获取好友请求列表

**接口:** `GET /api/v1/chat/friends/requests`

**查询参数 (Query Parameters):**

- `type` (可选): 请求类型，`received` (收到的) 或 `sent` (发送的)，默认为 `received`。
- `page` (可选): 页码，默认为 `1`。
- `page_size` (可选): 每页数量，默认为 `20`。

**JavaScript 示例:**

```javascript
async function getFriendRequestList(type = 'received', page = 1, pageSize = 20) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch(`/api/v1/chat/friends/requests?type=${type}&page=${page}&page_size=${pageSize}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '获取好友请求列表失败');
    }

    console.log(`获取${type === 'received' ? '收到' : '发送'}的好友请求列表成功:`, result.data);
    // 将请求列表渲染到UI
    return result.data;

  } catch (error) {
    console.error('获取好友请求列表时出错:', error);
  }
}

// 使用示例
// getFriendRequestList('received');
// getFriendRequestList('sent');
```

### 5. 删除好友

**接口:** `DELETE /api/v1/chat/friends/:friend_id`

**URL 参数:**

- `friend_id`: 要删除的好友的用户ID。

**JavaScript 示例:**

```javascript
async function deleteFriend(friendId) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  if (!confirm(`确定要删除ID为 ${friendId} 的好友吗？`)) {
    return;
  }
  try {
    const response = await fetch(`/api/v1/chat/friends/${friendId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '删除好友失败');
    }

    console.log('删除好友成功:', result);
    // 从UI中移除该好友

  } catch (error) {
    console.error('删除好友时出错:', error);
  }
}

// 使用示例
// deleteFriend(456);
```

### 6. 更新好友信息

**接口:** `PUT /api/v1/chat/friends/:friend_id`

**URL 参数:**

- `friend_id`: 要更新信息的好友的用户ID。

**请求体 (Request Body):**

```json
{
  "remark": "大学同学-李四",  // 新的备注名
  "status": 1                  // 新的状态（例如：0-正常, 1-特别关注）
}
```

**JavaScript 示例:**

```javascript
async function updateFriendInfo(friendId, remark, status) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  const updateData = {};
  if (remark !== undefined) updateData.remark = remark;
  if (status !== undefined) updateData.status = status;

  try {
    const response = await fetch(`/api/v1/chat/friends/${friendId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '更新好友信息失败');
    }

    console.log('更新好友信息成功:', result);
    // 在UI上更新好友信息

  } catch (error) {
    console.error('更新好友信息时出错:', error);
  }
}

// 使用示例
// updateFriendInfo(456, '最好的朋友-李四');
```
