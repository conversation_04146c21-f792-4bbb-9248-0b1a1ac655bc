# 群组与群成员管理集成指南

本指南为前端开发者提供关于如何集成聊天模块中群组和群成员管理功能的详细说明。内容覆盖群组的创建、查询、更新、解散，以及群成员的邀请、移除、信息更新和权限变更等操作。

## API 接口

### 群组管理

| 方法   | 路径                               | 描述                       |
| ------ | ---------------------------------- | -------------------------- |
| POST   | `/api/v1/chat/groups`              | 创建新群组                 |
| GET    | `/api/v1/chat/groups`              | 获取当前用户加入的群组列表 |
| GET    | `/api/v1/chat/groups/:group_id`    | 获取指定群组的详细信息     |
| PUT    | `/api/v1/chat/groups/:group_id`    | 更新群组信息               |
| POST   | `/api/v1/chat/groups/:group_id/dismiss` | 解散群组（仅群主）         |

### 群成员管理

| 方法   | 路径                                       | 描述                                     |
| ------ | ------------------------------------------ | ---------------------------------------- |
| GET    | `/api/v1/chat/groups/:group_id/members`    | 获取群成员列表                           |
| POST   | `/api/v1/chat/groups/:group_id/members`    | 添加（邀请）新成员到群组                 |
| DELETE | `/api/v1/chat/groups/:group_id/members/:member_id` | 从群组移除成员（或用户主动退群）         |
| PUT    | `/api/v1/chat/groups/:group_id/members/:member_id` | 更新群成员信息（如群昵称）               |
| POST   | `/api/v1/chat/groups/:group_id/transfer`   | 转让群主权限（仅群主）                   |

---

## 前端集成示例

以下是使用 JavaScript `fetch` API 与群组管理接口交互的示例。请确保在所有请求的 `Authorization` 头中提供有效的JWT令牌。

### 1. 创建群组

**接口:** `POST /api/v1/chat/groups`

**请求体 (Request Body):**

```json
{
  "name": "技术交流群",
  "description": "一个用于讨论技术的群组",
  "avatar": "/path/to/avatar.jpg",
  "initial_members": [
    { "user_id": 123, "user_type": "user" },
    { "user_id": 456, "user_type": "user" }
  ]
}
```

**JavaScript 示例:**

```javascript
async function createGroup(name, description, avatar, initialMembers) {
  const authToken = 'YOUR_JWT_TOKEN'; // 替换为真实的JWT令牌
  try {
    const response = await fetch('/api/v1/chat/groups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({ name, description, avatar, initial_members: initialMembers })
    });
    const result = await response.json();
    if (!response.ok) throw new Error(result.message || '创建群组失败');
    console.log('创建群组成功:', result.data);
    return result.data;
  } catch (error) {
    console.error('创建群组时出错:', error);
  }
}
```

### 2. 获取用户加入的群组列表

**接口:** `GET /api/v1/chat/groups`

**JavaScript 示例:**

```javascript
async function getUserGroups(page = 1, pageSize = 20) {
  const authToken = 'YOUR_JWT_TOKEN';
  try {
    const response = await fetch(`/api/v1/chat/groups?page=${page}&page_size=${pageSize}`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    const result = await response.json();
    if (!response.ok) throw new Error(result.message || '获取群组列表失败');
    console.log('获取群组列表成功:', result.data);
    return result.data;
  } catch (error) {
    console.error('获取群组列表时出错:', error);
  }
}
```

### 3. 添加群成员

**接口:** `POST /api/v1/chat/groups/:group_id/members`

**请求体 (Request Body):**

```json
{
  "user_id": 789,
  "user_type": "user",
  "role": "member"
}
```

**JavaScript 示例:**

```javascript
async function addGroupMember(groupId, userId, userType, role = 'member') {
  const authToken = 'YOUR_JWT_TOKEN';
  try {
    const response = await fetch(`/api/v1/chat/groups/${groupId}/members`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({ user_id: userId, user_type: userType, role })
    });
    const result = await response.json();
    if (!response.ok) throw new Error(result.message || '添加群成员失败');
    console.log('添加群成员成功:', result);
    alert('成员已成功邀请！');
  } catch (error) {
    console.error('添加群成员时出错:', error);
  }
}
```

### 4. 移除群成员（或主动退群）

**接口:** `DELETE /api/v1/chat/groups/:group_id/members/:member_id`

**说明:** 如果 `:member_id` 是当前用户的ID，则为主动退群。如果是其他成员ID，则为管理员或群主踢人。

**JavaScript 示例:**

```javascript
async function removeGroupMember(groupId, memberId) {
  const authToken = 'YOUR_JWT_TOKEN';
  if (!confirm(`确定要从群组 ${groupId} 中移除成员 ${memberId} 吗？`)) return;
  try {
    const response = await fetch(`/api/v1/chat/groups/${groupId}/members/${memberId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    const result = await response.json();
    if (!response.ok) throw new Error(result.message || '移除成员失败');
    console.log('移除成员成功:', result);
    // 在UI上更新成员列表
  } catch (error) {
    console.error('移除成员时出错:', error);
  }
}
```

### 5. 转让群主

**接口:** `POST /api/v1/chat/groups/:group_id/transfer`

**查询参数 (Query Parameters):**

- `new_owner_id`: 新群主的用户ID。

**JavaScript 示例:**

```javascript
async function transferGroupOwner(groupId, newOwnerId) {
  const authToken = 'YOUR_JWT_TOKEN';
  try {
    const response = await fetch(`/api/v1/chat/groups/${groupId}/transfer?new_owner_id=${newOwnerId}`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    const result = await response.json();
    if (!response.ok) throw new Error(result.message || '转让群主失败');
    console.log('转让群主成功:', result);
    alert('群主已成功转让！');
  } catch (error) {
    console.error('转让群主时出错:', error);
  }
}
```
