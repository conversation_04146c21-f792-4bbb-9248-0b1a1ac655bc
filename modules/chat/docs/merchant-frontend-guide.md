# 商家前端聊天功能设计指导文档

## 1. 概述

本文档为商家端前端开发提供聊天功能的完整设计指导，包括客服消息接收、用户咨询处理等核心功能的实现方案。基于现有聊天模块API，商家可以实现完整的客服聊天系统。

## 2. 技术架构

### 2.1 核心组件架构
```
商家聊天系统
├── WebSocket连接管理器 (MerchantChatManager)
├── 会话列表组件 (SessionList)
├── 聊天窗口组件 (ChatWindow)
├── 消息输入组件 (MessageInput)
├── 文件上传组件 (FileUpload)
├── 客服工具栏 (CustomerServiceToolbar)
└── 通知管理组件 (NotificationManager)
```

### 2.2 数据流设计
```
用户发起咨询 → 创建会话 → WebSocket推送 → 商家接收通知 → 商家回复 → 用户接收消息
```

## 3. 前端实现指导

### 3.1 商家聊天客户端类

```javascript
/**
 * 商家聊天客户端
 * 专为商家端设计的聊天功能封装
 */
class MerchantChatClient {
  constructor(config) {
    this.apiBaseUrl = config.apiBaseUrl;
    this.wsBaseUrl = config.wsBaseUrl;
    this.merchantToken = config.merchantToken;
    this.merchantId = config.merchantId;
    this.socket = null;
    this.connected = false;
    this.messageHandlers = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectTimeout = 3000;
    
    // 商家特有配置
    this.autoReply = config.autoReply || false;
    this.workingHours = config.workingHours || null;
    this.maxConcurrentChats = config.maxConcurrentChats || 10;
    this.currentActiveSessions = new Set();
  }

  // 初始化商家聊天系统
  async init() {
    try {
      // 1. 建立WebSocket连接
      await this.connectWebSocket();
      
      // 2. 获取待处理的会话列表
      const sessions = await this.fetchPendingSessions();
      
      // 3. 设置自动回复（如果启用）
      if (this.autoReply) {
        this.setupAutoReply();
      }
      
      // 4. 初始化通知系统
      this.initNotificationSystem();
      
      return {
        success: true,
        sessions: sessions,
        message: '商家聊天系统初始化成功'
      };
    } catch (error) {
      console.error('商家聊天系统初始化失败:', error);
      throw error;
    }
  }

  // 建立WebSocket连接
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      const wsUrl = `${this.wsBaseUrl}/api/v1/chat/ws?token=${this.merchantToken}`;
      
      this.socket = new WebSocket(wsUrl);
      
      this.socket.onopen = () => {
        console.log('商家WebSocket连接已建立');
        this.connected = true;
        this.reconnectAttempts = 0;
        
        // 发送商家上线状态
        this.sendMerchantStatus('online');
        resolve();
      };
      
      this.socket.onmessage = (event) => {
        this.handleWebSocketMessage(event);
      };
      
      this.socket.onclose = (event) => {
        this.connected = false;
        console.log('商家WebSocket连接已关闭');
        this.handleReconnection();
      };
      
      this.socket.onerror = (error) => {
        console.error('商家WebSocket错误:', error);
        reject(error);
      };
    });
  }

  // 处理WebSocket消息
  handleWebSocketMessage(event) {
    try {
      const message = JSON.parse(event.data);
      console.log('商家收到WebSocket消息:', message);
      
      switch (message.type) {
        case 'message':
          this.handleNewMessage(message);
          break;
        case 'notification':
          this.handleNotification(message);
          break;
        case 'system':
          this.handleSystemMessage(message);
          break;
        case 'heartbeat':
          this.handleHeartbeat(message);
          break;
        default:
          console.log('未知消息类型:', message.type);
      }
      
      // 调用注册的消息处理器
      const handlers = this.messageHandlers.get(message.type) || [];
      handlers.forEach(handler => handler(message));
      
    } catch (error) {
      console.error('处理WebSocket消息失败:', error);
    }
  }

  // 处理新消息
  handleNewMessage(message) {
    if (message.event === 'new_message') {
      const messageData = message.data;
      
      // 如果是用户发送给商家的消息
      if (messageData.sender_type === 'user') {
        // 添加到当前活跃会话
        this.currentActiveSessions.add(message.session_id);
        
        // 播放提示音
        this.playNotificationSound();
        
        // 显示桌面通知
        this.showDesktopNotification({
          title: '新的客户咨询',
          body: messageData.content || '收到新消息',
          sessionId: message.session_id
        });
        
        // 更新会话列表
        this.updateSessionList(message.session_id);
      }
    }
  }

  // 获取商家的待处理会话
  async fetchPendingSessions() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/chat/sessions`, {
        headers: {
          'Authorization': `Bearer ${this.merchantToken}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`获取会话列表失败: ${response.status}`);
      }
      
      const result = await response.json();
      
      // 过滤出与商家相关的会话
      const merchantSessions = result.data.sessions.filter(session => 
        session.receiver_type === 'merchant' && 
        session.receiver_id === this.merchantId
      );
      
      return merchantSessions;
    } catch (error) {
      console.error('获取商家会话失败:', error);
      throw error;
    }
  }

  // 发送商家状态
  sendMerchantStatus(status) {
    if (!this.connected) return;
    
    const statusMsg = {
      type: 'status',
      status: status,
      user_type: 'merchant',
      timestamp: Date.now()
    };
    
    this.socket.send(JSON.stringify(statusMsg));
  }

  // 播放通知声音
  playNotificationSound() {
    try {
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = 0.5;
      audio.play().catch(e => console.log('播放提示音失败:', e));
    } catch (error) {
      console.log('播放提示音失败:', error);
    }
  }

  // 显示桌面通知
  showDesktopNotification(options) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: '/icons/merchant-chat.png',
        tag: `session-${options.sessionId}`
      });
      
      notification.onclick = () => {
        window.focus();
        // 触发会话选择事件
        this.selectSession(options.sessionId);
        notification.close();
      };
    }
  }

  // 注册消息处理器
  onMessage(type, handler) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type).push(handler);
  }

  // 断开连接
  disconnect() {
    if (this.socket && this.connected) {
      this.sendMerchantStatus('offline');
      this.socket.close();
    }
  }
}
```

### 3.2 使用示例

```javascript
// 初始化商家聊天系统
const merchantChat = new MerchantChatClient({
  apiBaseUrl: 'https://api.yourdomain.com',
  wsBaseUrl: 'wss://api.yourdomain.com',
  merchantToken: 'your-merchant-jwt-token',
  merchantId: 123,
  autoReply: true,
  maxConcurrentChats: 15
});

// 初始化系统
merchantChat.init().then(result => {
  console.log('商家聊天系统启动成功:', result);
  
  // 渲染会话列表
  renderSessionList(result.sessions);
}).catch(error => {
  console.error('启动失败:', error);
});

// 监听新消息
merchantChat.onMessage('message', (message) => {
  if (message.event === 'new_message') {
    // 更新UI显示新消息
    updateChatUI(message);
  }
});
```

## 4. UI组件设计

### 4.1 会话列表组件

```javascript
/**
 * 商家会话列表组件
 * 显示所有客户咨询会话，支持优先级排序
 */
class MerchantSessionList {
  constructor(chatClient, container) {
    this.chatClient = chatClient;
    this.container = container;
    this.sessions = [];
    this.currentSessionId = null;
    this.filterType = 'all'; // all, unread, urgent
    this.sortBy = 'last_message_time';
  }

  // 渲染会话列表
  render() {
    const filteredSessions = this.getFilteredSessions();
    const sortedSessions = this.getSortedSessions(filteredSessions);

    const html = `
      <div class="merchant-session-list">
        <!-- 过滤器 -->
        <div class="session-filters">
          <button class="filter-btn ${this.filterType === 'all' ? 'active' : ''}"
                  data-filter="all">全部咨询 (${this.sessions.length})</button>
          <button class="filter-btn ${this.filterType === 'unread' ? 'active' : ''}"
                  data-filter="unread">未读消息 (${this.getUnreadCount()})</button>
          <button class="filter-btn ${this.filterType === 'urgent' ? 'active' : ''}"
                  data-filter="urgent">紧急咨询 (${this.getUrgentCount()})</button>
        </div>

        <!-- 会话列表 -->
        <div class="sessions-container">
          ${sortedSessions.length > 0
            ? sortedSessions.map(session => this.renderSessionItem(session)).join('')
            : '<div class="no-sessions">暂无咨询会话</div>'
          }
        </div>
      </div>
    `;

    this.container.innerHTML = html;
    this.bindEvents();
  }

  // 渲染单个会话项
  renderSessionItem(session) {
    const isActive = session.id === this.currentSessionId;
    const lastMessage = session.last_message;
    const unreadCount = session.unread_count;
    const customerInfo = session.customer_info || {};

    return `
      <div class="session-item ${isActive ? 'active' : ''} ${unreadCount > 0 ? 'has-unread' : ''}"
           data-session-id="${session.id}">
        <!-- 客户头像 -->
        <div class="customer-avatar">
          <img src="${customerInfo.avatar || '/default-avatar.png'}"
               alt="${customerInfo.name || '客户'}">
          ${customerInfo.is_online ? '<span class="online-indicator"></span>' : ''}
        </div>

        <!-- 会话信息 -->
        <div class="session-info">
          <div class="session-header">
            <span class="customer-name">${customerInfo.name || '客户'}</span>
            <span class="message-time">${this.formatTime(session.updated_at)}</span>
          </div>

          <div class="session-preview">
            <span class="last-message">
              ${this.getMessagePreview(lastMessage)}
            </span>
            ${unreadCount > 0 ? `<span class="unread-badge">${unreadCount}</span>` : ''}
          </div>

          <!-- 客户标签 -->
          <div class="customer-tags">
            ${customerInfo.level ? `<span class="tag level-${customerInfo.level}">VIP${customerInfo.level}</span>` : ''}
            ${customerInfo.is_new_customer ? '<span class="tag new-customer">新客户</span>' : ''}
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="session-actions">
          <button class="action-btn" data-action="quick-reply" title="快速回复">
            <i class="icon-reply"></i>
          </button>
          <button class="action-btn" data-action="mark-read" title="标记已读">
            <i class="icon-check"></i>
          </button>
        </div>
      </div>
    `;
  }

  // 获取消息预览
  getMessagePreview(message) {
    if (!message) return '暂无消息';

    const preview = {
      'text': message.content,
      'image': '[图片]',
      'file': `[文件] ${message.file_name || ''}`,
      'voice': '[语音消息]',
      'video': '[视频消息]'
    };

    return preview[message.type] || '[消息]';
  }

  // 绑定事件
  bindEvents() {
    // 会话点击事件
    this.container.addEventListener('click', (e) => {
      const sessionItem = e.target.closest('.session-item');
      if (sessionItem) {
        const sessionId = parseInt(sessionItem.dataset.sessionId);
        this.selectSession(sessionId);
      }

      // 快捷操作
      const actionBtn = e.target.closest('.action-btn');
      if (actionBtn) {
        e.stopPropagation();
        const action = actionBtn.dataset.action;
        const sessionId = parseInt(actionBtn.closest('.session-item').dataset.sessionId);
        this.handleQuickAction(action, sessionId);
      }

      // 过滤器点击
      const filterBtn = e.target.closest('.filter-btn');
      if (filterBtn) {
        this.setFilter(filterBtn.dataset.filter);
      }
    });
  }

  // 选择会话
  selectSession(sessionId) {
    this.currentSessionId = sessionId;
    this.render();

    // 触发会话选择事件
    this.container.dispatchEvent(new CustomEvent('sessionSelected', {
      detail: { sessionId }
    }));
  }

  // 处理快捷操作
  async handleQuickAction(action, sessionId) {
    switch (action) {
      case 'quick-reply':
        this.showQuickReplyModal(sessionId);
        break;
      case 'mark-read':
        await this.chatClient.markSessionAsRead(sessionId);
        this.updateSessionUnreadCount(sessionId, 0);
        break;
    }
  }
}
```

### 4.2 聊天窗口组件

```javascript
/**
 * 商家聊天窗口组件
 * 处理与客户的实时对话
 */
class MerchantChatWindow {
  constructor(chatClient, container) {
    this.chatClient = chatClient;
    this.container = container;
    this.currentSession = null;
    this.messages = [];
    this.isTyping = false;
    this.quickReplies = [];
    this.customerInfo = null;
    this.messageTemplates = [];
  }

  // 打开会话
  async openSession(sessionId) {
    try {
      // 显示加载状态
      this.showLoading();

      // 获取会话详情
      this.currentSession = await this.chatClient.getSession(sessionId);

      // 获取客户信息
      this.customerInfo = await this.getCustomerInfo(this.currentSession.creator_id);

      // 加载消息历史
      const { messages } = await this.chatClient.getMessages(sessionId, 1, 50);
      this.messages = messages.reverse();

      // 加载快捷回复和模板
      await this.loadQuickReplies();
      await this.loadMessageTemplates();

      // 渲染聊天窗口
      this.render();

      // 标记为已读
      await this.chatClient.markSessionAsRead(sessionId);

      // 滚动到底部
      this.scrollToBottom();

    } catch (error) {
      console.error('打开会话失败:', error);
      this.showError('加载会话失败，请重试');
    }
  }

  // 渲染聊天窗口
  render() {
    if (!this.currentSession) {
      this.container.innerHTML = `
        <div class="no-session">
          <div class="no-session-icon">💬</div>
          <h3>选择一个会话开始聊天</h3>
          <p>从左侧会话列表中选择客户开始对话</p>
        </div>
      `;
      return;
    }

    const html = `
      <div class="merchant-chat-window">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="customer-info">
            <img src="${this.customerInfo?.avatar || '/default-avatar.png'}"
                 alt="客户头像" class="customer-avatar-small">
            <div class="customer-details">
              <h3>${this.customerInfo?.name || '客户'}</h3>
              <span class="customer-status">
                <span class="status-indicator ${this.customerInfo?.is_online ? 'online' : 'offline'}"></span>
                ${this.customerInfo?.is_online ? '在线' : '离线'}
                ${this.customerInfo?.level ? ` · VIP${this.customerInfo.level}` : ''}
              </span>
            </div>
          </div>

          <!-- 客服工具 -->
          <div class="service-tools">
            <button class="tool-btn" data-action="customer-profile" title="客户资料">
              <i class="icon-user"></i>
            </button>
            <button class="tool-btn" data-action="order-history" title="订单历史">
              <i class="icon-orders"></i>
            </button>
            <button class="tool-btn" data-action="transfer-session" title="转接客服">
              <i class="icon-transfer"></i>
            </button>
            <button class="tool-btn" data-action="session-notes" title="会话备注">
              <i class="icon-notes"></i>
            </button>
          </div>
        </div>

        <!-- 消息区域 -->
        <div class="messages-container" id="messages-container">
          ${this.messages.map(msg => this.renderMessage(msg)).join('')}
          <div class="typing-indicator" style="display: none;">
            <span>客户正在输入...</span>
          </div>
        </div>

        <!-- 快捷回复 -->
        <div class="quick-replies" ${this.quickReplies.length === 0 ? 'style="display: none;"' : ''}>
          <div class="quick-replies-header">
            <span>快捷回复</span>
            <button class="toggle-quick-replies">−</button>
          </div>
          <div class="quick-replies-content">
            ${this.quickReplies.map(reply =>
              `<button class="quick-reply-btn" data-content="${reply.content}">
                ${reply.title}
              </button>`
            ).join('')}
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-tools">
            <button class="input-tool" data-action="emoji" title="表情">😊</button>
            <button class="input-tool" data-action="image" title="图片">🖼️</button>
            <button class="input-tool" data-action="file" title="文件">📎</button>
            <button class="input-tool" data-action="template" title="消息模板">📋</button>
            <button class="input-tool" data-action="product" title="商品卡片">🛍️</button>
          </div>

          <div class="input-container">
            <textarea class="message-input"
                      placeholder="输入回复消息..."
                      rows="3"
                      maxlength="1000"></textarea>
            <div class="input-footer">
              <span class="char-count">0/1000</span>
              <button class="send-btn" disabled>发送 (Ctrl+Enter)</button>
            </div>
          </div>
        </div>
      </div>
    `;

    this.container.innerHTML = html;
    this.bindChatEvents();
  }

  // 渲染消息
  renderMessage(message) {
    const isOwn = message.sender_type === 'merchant';
    const time = this.formatMessageTime(message.created_at);
    const senderName = isOwn ? '我' : (this.customerInfo?.name || '客户');

    return `
      <div class="message ${isOwn ? 'own-message' : 'customer-message'}">
        <div class="message-avatar">
          <img src="${this.getMessageAvatar(message)}" alt="头像">
        </div>

        <div class="message-content">
          <div class="message-header">
            <span class="sender-name">${senderName}</span>
            <span class="message-time">${time}</span>
          </div>

          <div class="message-bubble">
            ${this.renderMessageContent(message)}
          </div>

          ${isOwn ? this.renderMessageStatus(message) : ''}
        </div>
      </div>
    `;
  }

  // 渲染消息内容
  renderMessageContent(message) {
    switch (message.type) {
      case 'text':
        return `<div class="text-content">${this.formatTextContent(message.content)}</div>`;

      case 'image':
        return `
          <div class="image-content">
            <img src="${this.getMediaUrl(message.resource_id)}"
                 alt="图片"
                 class="message-image"
                 onclick="previewImage('${this.getMediaUrl(message.resource_id)}')">
          </div>
        `;

      case 'file':
        return `
          <div class="file-content">
            <div class="file-info">
              <i class="file-icon ${this.getFileIcon(message.file_ext)}"></i>
              <div class="file-details">
                <span class="file-name">${message.file_name}</span>
                <span class="file-size">${this.formatFileSize(message.file_size)}</span>
              </div>
            </div>
            <a href="${this.getMediaUrl(message.resource_id)}"
               download="${message.file_name}"
               class="download-btn">下载</a>
          </div>
        `;

      case 'voice':
        return `
          <div class="voice-content">
            <button class="voice-play-btn" data-audio-url="${this.getMediaUrl(message.resource_id)}">
              <i class="icon-play"></i>
            </button>
            <div class="voice-duration">语音消息</div>
          </div>
        `;

      default:
        return `<div class="unknown-content">不支持的消息类型</div>`;
    }
  }

  // 绑定聊天事件
  bindChatEvents() {
    const messageInput = this.container.querySelector('.message-input');
    const sendBtn = this.container.querySelector('.send-btn');
    const charCount = this.container.querySelector('.char-count');

    // 输入框事件
    messageInput.addEventListener('input', (e) => {
      const length = e.target.value.length;
      charCount.textContent = `${length}/1000`;
      sendBtn.disabled = length === 0;

      // 发送正在输入状态
      this.sendTypingStatus(true);
    });

    // 键盘快捷键
    messageInput.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        this.sendMessage(messageInput.value.trim());
      }
    });

    // 发送按钮
    sendBtn.addEventListener('click', () => {
      this.sendMessage(messageInput.value.trim());
    });

    // 快捷回复
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('quick-reply-btn')) {
        const content = e.target.dataset.content;
        messageInput.value = content;
        messageInput.focus();
        sendBtn.disabled = false;
        charCount.textContent = `${content.length}/1000`;
      }

      // 工具按钮
      if (e.target.closest('.input-tool')) {
        const action = e.target.closest('.input-tool').dataset.action;
        this.handleInputTool(action);
      }

      // 服务工具
      if (e.target.closest('.tool-btn')) {
        const action = e.target.closest('.tool-btn').dataset.action;
        this.handleServiceTool(action);
      }
    });
  }

  // 发送消息
  async sendMessage(content, type = 'text') {
    if (!this.currentSession || !content.trim()) return;

    const messageInput = this.container.querySelector('.message-input');
    const sendBtn = this.container.querySelector('.send-btn');

    // 禁用发送按钮
    sendBtn.disabled = true;
    sendBtn.textContent = '发送中...';

    try {
      let sentMessage;

      if (type === 'text') {
        sentMessage = await this.chatClient.sendTextMessage(
          this.currentSession.id,
          content
        );
      } else {
        sentMessage = await this.chatClient.sendMediaMessage(
          this.currentSession.id,
          content,
          type
        );
      }

      // 添加到消息列表
      this.messages.push(sentMessage);

      // 更新UI
      this.appendMessage(sentMessage);
      this.scrollToBottom();

      // 清空输入框
      if (type === 'text') {
        messageInput.value = '';
        this.container.querySelector('.char-count').textContent = '0/1000';
      }

      // 停止输入状态
      this.sendTypingStatus(false);

    } catch (error) {
      console.error('发送消息失败:', error);
      this.showError('发送失败，请重试');
    } finally {
      // 恢复发送按钮
      sendBtn.disabled = false;
      sendBtn.textContent = '发送 (Ctrl+Enter)';
    }
  }

  // 处理输入工具
  handleInputTool(action) {
    switch (action) {
      case 'emoji':
        this.showEmojiPicker();
        break;
      case 'image':
        this.triggerImageUpload();
        break;
      case 'file':
        this.triggerFileUpload();
        break;
      case 'template':
        this.showMessageTemplates();
        break;
      case 'product':
        this.showProductSelector();
        break;
    }
  }

  // 处理服务工具
  handleServiceTool(action) {
    switch (action) {
      case 'customer-profile':
        this.showCustomerProfile();
        break;
      case 'order-history':
        this.showOrderHistory();
        break;
      case 'transfer-session':
        this.showTransferDialog();
        break;
      case 'session-notes':
        this.showSessionNotes();
        break;
    }
  }
}
```

## 5. 样式设计指导

### 5.1 核心样式

```css
/* 商家聊天系统主容器 */
.merchant-chat-system {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 会话列表样式 */
.merchant-session-list {
  width: 320px;
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.session-filters {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  gap: 8px;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid #d0d0d0;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.filter-btn.active {
  background: #1976d2;
  color: white;
  border-color: #1976d2;
}

.session-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.session-item:hover {
  background: #f8f9fa;
}

.session-item.active {
  background: #e3f2fd;
  border-right: 3px solid #1976d2;
}

.session-item.has-unread {
  background: #fff3e0;
}

/* 聊天窗口样式 */
.merchant-chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-avatar-small {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.service-tools {
  display: flex;
  gap: 8px;
}

.tool-btn {
  padding: 8px;
  border: 1px solid #d0d0d0;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.tool-btn:hover {
  background: #f5f5f5;
}

/* 消息区域样式 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
}

.message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 8px;
}

.message.own-message {
  flex-direction: row-reverse;
}

.message-bubble {
  max-width: 60%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.customer-message .message-bubble {
  background: white;
  border: 1px solid #e0e0e0;
}

.own-message .message-bubble {
  background: #1976d2;
  color: white;
}

/* 输入区域样式 */
.input-area {
  border-top: 1px solid #e0e0e0;
  background: white;
}

.input-tools {
  padding: 12px 16px;
  display: flex;
  gap: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.input-tool {
  padding: 6px 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.input-tool:hover {
  background: #f5f5f5;
}

.input-container {
  padding: 16px;
}

.message-input {
  width: 100%;
  border: 1px solid #d0d0d0;
  border-radius: 8px;
  padding: 12px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.send-btn {
  padding: 8px 16px;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.send-btn:not(:disabled):hover {
  background: #1565c0;
}
```

## 6. 高级功能实现

### 6.1 自动回复系统

```javascript
class AutoReplySystem {
  constructor(chatClient) {
    this.chatClient = chatClient;
    this.rules = [];
    this.enabled = false;
  }

  // 设置自动回复规则
  setRules(rules) {
    this.rules = rules;
  }

  // 启用自动回复
  enable() {
    this.enabled = true;
    this.chatClient.onMessage('message', (message) => {
      if (message.event === 'new_message' && message.data.sender_type === 'user') {
        this.processAutoReply(message);
      }
    });
  }

  // 处理自动回复
  async processAutoReply(message) {
    if (!this.enabled) return;

    const content = message.data.content.toLowerCase();

    for (const rule of this.rules) {
      if (this.matchRule(content, rule)) {
        // 延迟发送，模拟人工回复
        setTimeout(async () => {
          await this.chatClient.sendTextMessage(
            message.session_id,
            rule.reply
          );
        }, rule.delay || 1000);
        break;
      }
    }
  }

  // 匹配规则
  matchRule(content, rule) {
    if (rule.type === 'keyword') {
      return rule.keywords.some(keyword => content.includes(keyword));
    }
    if (rule.type === 'regex') {
      return new RegExp(rule.pattern, 'i').test(content);
    }
    return false;
  }
}

// 使用示例
const autoReply = new AutoReplySystem(merchantChat);
autoReply.setRules([
  {
    type: 'keyword',
    keywords: ['你好', '在吗', '客服'],
    reply: '您好！我是客服小助手，很高兴为您服务，请问有什么可以帮助您的吗？',
    delay: 800
  },
  {
    type: 'keyword',
    keywords: ['价格', '多少钱', '费用'],
    reply: '关于价格问题，我来为您详细介绍一下我们的产品...',
    delay: 1200
  }
]);
autoReply.enable();
```

### 6.2 消息模板管理

```javascript
class MessageTemplateManager {
  constructor() {
    this.templates = [];
    this.categories = [];
  }

  // 加载模板
  async loadTemplates() {
    try {
      const response = await fetch('/api/merchant/chat/templates');
      const data = await response.json();
      this.templates = data.templates;
      this.categories = data.categories;
    } catch (error) {
      console.error('加载消息模板失败:', error);
    }
  }

  // 显示模板选择器
  showTemplateSelector(callback) {
    const modal = document.createElement('div');
    modal.className = 'template-modal';
    modal.innerHTML = `
      <div class="template-modal-content">
        <div class="template-header">
          <h3>选择消息模板</h3>
          <button class="close-btn">&times;</button>
        </div>

        <div class="template-categories">
          ${this.categories.map(cat =>
            `<button class="category-btn" data-category="${cat.id}">
              ${cat.name}
            </button>`
          ).join('')}
        </div>

        <div class="template-list">
          ${this.templates.map(template =>
            `<div class="template-item" data-template-id="${template.id}">
              <h4>${template.title}</h4>
              <p>${template.content}</p>
            </div>`
          ).join('')}
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // 绑定事件
    modal.addEventListener('click', (e) => {
      if (e.target.classList.contains('close-btn') || e.target === modal) {
        document.body.removeChild(modal);
      }

      if (e.target.closest('.template-item')) {
        const templateId = e.target.closest('.template-item').dataset.templateId;
        const template = this.templates.find(t => t.id == templateId);
        callback(template.content);
        document.body.removeChild(modal);
      }
    });
  }
}
```

## 7. 性能优化建议

### 7.1 消息虚拟滚动

```javascript
class VirtualMessageList {
  constructor(container, itemHeight = 80) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2;
    this.startIndex = 0;
    this.messages = [];
  }

  // 设置消息数据
  setMessages(messages) {
    this.messages = messages;
    this.render();
  }

  // 渲染可见消息
  render() {
    const endIndex = Math.min(this.startIndex + this.visibleCount, this.messages.length);
    const visibleMessages = this.messages.slice(this.startIndex, endIndex);

    const html = `
      <div class="virtual-spacer" style="height: ${this.startIndex * this.itemHeight}px;"></div>
      ${visibleMessages.map(msg => this.renderMessage(msg)).join('')}
      <div class="virtual-spacer" style="height: ${(this.messages.length - endIndex) * this.itemHeight}px;"></div>
    `;

    this.container.innerHTML = html;
  }

  // 处理滚动
  onScroll(scrollTop) {
    const newStartIndex = Math.floor(scrollTop / this.itemHeight);
    if (newStartIndex !== this.startIndex) {
      this.startIndex = newStartIndex;
      this.render();
    }
  }
}
```

### 7.2 消息缓存策略

```javascript
class MessageCache {
  constructor(maxSize = 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  // 获取会话消息
  getSessionMessages(sessionId) {
    return this.cache.get(`session_${sessionId}`) || [];
  }

  // 缓存会话消息
  setSessionMessages(sessionId, messages) {
    const key = `session_${sessionId}`;
    this.cache.set(key, messages);

    // 清理过期缓存
    if (this.cache.size > this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  // 添加新消息
  addMessage(sessionId, message) {
    const messages = this.getSessionMessages(sessionId);
    messages.push(message);
    this.setSessionMessages(sessionId, messages);
  }
}
```

## 8. 部署和集成指导

### 8.1 环境配置

```javascript
// config/chat.js
const chatConfig = {
  development: {
    apiBaseUrl: 'http://localhost:8080',
    wsBaseUrl: 'ws://localhost:8080',
    enableDebug: true
  },
  production: {
    apiBaseUrl: 'https://api.yourdomain.com',
    wsBaseUrl: 'wss://api.yourdomain.com',
    enableDebug: false
  }
};

export default chatConfig[process.env.NODE_ENV || 'development'];
```

### 8.2 完整集成示例

```html
<!DOCTYPE html>
<html>
<head>
  <title>商家客服系统</title>
  <link rel="stylesheet" href="merchant-chat.css">
</head>
<body>
  <div id="merchant-chat-app">
    <div class="chat-sidebar">
      <div id="session-list"></div>
    </div>
    <div class="chat-main">
      <div id="chat-window"></div>
    </div>
  </div>

  <script src="merchant-chat-client.js"></script>
  <script>
    // 初始化商家聊天系统
    document.addEventListener('DOMContentLoaded', async () => {
      const merchantToken = localStorage.getItem('merchantToken');
      const merchantId = parseInt(localStorage.getItem('merchantId'));

      if (!merchantToken || !merchantId) {
        window.location.href = '/merchant/login';
        return;
      }

      // 创建聊天客户端
      const chatClient = new MerchantChatClient({
        apiBaseUrl: 'https://api.yourdomain.com',
        wsBaseUrl: 'wss://api.yourdomain.com',
        merchantToken: merchantToken,
        merchantId: merchantId,
        autoReply: true,
        maxConcurrentChats: 20
      });

      // 创建UI组件
      const sessionList = new MerchantSessionList(
        chatClient,
        document.getElementById('session-list')
      );

      const chatWindow = new MerchantChatWindow(
        chatClient,
        document.getElementById('chat-window')
      );

      // 初始化系统
      try {
        const result = await chatClient.init();
        sessionList.setSessions(result.sessions);
        sessionList.render();

        console.log('商家聊天系统启动成功');
      } catch (error) {
        console.error('启动失败:', error);
        alert('聊天系统启动失败，请刷新页面重试');
      }

      // 监听会话选择
      document.getElementById('session-list').addEventListener('sessionSelected', (e) => {
        chatWindow.openSession(e.detail.sessionId);
      });

      // 监听新消息
      chatClient.onMessage('message', (message) => {
        if (message.event === 'new_message') {
          sessionList.updateSession(message.session_id);
          if (chatWindow.currentSession?.id === message.session_id) {
            chatWindow.addMessage(message.data);
          }
        }
      });
    });
  </script>
</body>
</html>
```

## 9. 总结

本指导文档提供了完整的商家前端聊天功能实现方案，包括：

1. **核心架构设计** - 模块化的组件设计
2. **WebSocket集成** - 实时消息处理
3. **UI组件实现** - 会话列表和聊天窗口
4. **高级功能** - 自动回复、消息模板等
5. **性能优化** - 虚拟滚动、消息缓存
6. **部署指导** - 完整的集成示例

通过遵循本指导文档，前端开发团队可以快速构建出功能完善、用户体验良好的商家客服聊天系统。
```
```
