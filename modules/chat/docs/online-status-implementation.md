# 客户在线状态管理功能实现文档

## 概述

本文档描述了客户在线状态管理功能的完整实现，包括实时状态管理、WebSocket推送机制和API接口优化。

## 功能特性

### 1. 实时状态管理
- 使用Redis缓存客户在线状态
- 支持三种状态：active（活跃）、idle（空闲）、offline（离线）
- WebSocket连接建立/断开时自动更新状态
- 定期清理过期状态

### 2. WebSocket推送机制
- 客户上线/下线时实时推送通知给相关商家
- 支持状态变更通知（active ↔ idle）
- 推送消息包含客户信息和相关会话列表

### 3. API接口优化
- 会话列表API返回实时的客户在线状态
- 包含详细的在线状态信息和最后在线时间

## 核心组件

### 1. OnlineStatusService（在线状态服务）

**文件位置**: `modules/chat/services/online_status_service.go`

**主要功能**:
- 管理用户在线状态的Redis缓存
- 提供状态查询和更新接口
- 处理会话用户关联

**核心方法**:
```go
// 状态管理
SetUserOnline(ctx, userID, userType, sessionIDs) error
SetUserOffline(ctx, userID, userType) error
UpdateUserActivity(ctx, userID, userType) error

// 状态查询
GetUserOnlineInfo(ctx, userID, userType) (*UserOnlineInfo, error)
IsUserOnline(ctx, userID, userType) (bool, error)
GetUserStatus(ctx, userID, userType) (OnlineStatus, error)

// 批量操作
GetMultipleUserStatus(ctx, users) (map[string]OnlineStatus, error)
```

### 2. WebSocket管理器扩展

**文件位置**: `modules/chat/services/websocket_manager.go`

**新增功能**:
- 集成在线状态服务
- 连接建立时设置用户在线状态
- 连接断开时设置用户离线状态
- 收到消息时更新用户活动时间
- 广播状态变更通知

**关键方法**:
```go
handleUserOnline(client *WebSocketClient)
handleUserOffline(client *WebSocketClient)
broadcastUserStatusChange(userID, userType, event, status, sessionIDs)
```

### 3. 会话列表API优化

**文件位置**: `modules/chat/services/impl/chat_service_impl.go`

**优化内容**:
- 在SessionDTO中添加CustomerInfo字段
- 获取会话列表时查询客户在线状态
- 返回详细的客户在线信息

### 4. 数据传输对象

**文件位置**: `modules/chat/dto/online_status_dto.go`

**新增结构**:
- `CustomerOnlineNotificationData`: 客户上线通知数据
- `CustomerOfflineNotificationData`: 客户下线通知数据
- `CustomerStatusChangeNotificationData`: 状态变更通知数据
- `OnlineStatusNotificationBuilder`: 通知消息构建器

## 状态定义

### 在线状态类型
```go
const (
    StatusActive  OnlineStatus = "active"  // 活跃在线（最近5分钟内有活动）
    StatusIdle    OnlineStatus = "idle"    // 空闲在线（连接但超过5分钟无活动）
    StatusOffline OnlineStatus = "offline" // 离线（WebSocket断开连接）
)
```

### 状态转换规则
- **连接建立** → `active`
- **收到消息** → `active`
- **5分钟无活动** → `idle`
- **连接断开** → `offline`
- **30分钟后清理** → 从缓存中移除

## WebSocket推送消息格式

### 客户上线通知
```json
{
  "type": "notification",
  "event": "customer_online",
  "timestamp": 1640995200,
  "data": {
    "customer_id": 123,
    "customer_name": "张三",
    "customer_avatar": "https://example.com/avatar.jpg",
    "session_ids": [13, 15],
    "online_status": "active",
    "timestamp": "2021-12-31T16:00:00Z"
  }
}
```

### 客户下线通知
```json
{
  "type": "notification",
  "event": "customer_offline",
  "timestamp": 1640995200,
  "data": {
    "customer_id": 123,
    "customer_name": "张三",
    "session_ids": [13, 15],
    "last_seen": "2021-12-31T16:00:00Z",
    "timestamp": "2021-12-31T16:00:00Z"
  }
}
```

### 客户状态变更通知
```json
{
  "type": "notification",
  "event": "customer_status_change",
  "timestamp": 1640995200,
  "data": {
    "customer_id": 123,
    "old_status": "active",
    "new_status": "idle",
    "session_ids": [13, 15],
    "timestamp": "2021-12-31T16:00:00Z"
  }
}
```

## API响应格式

### 会话列表API响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "sessions": [
      {
        "id": 13,
        "type": "private",
        "creator_id": 456,
        "creator_type": "merchant",
        "receiver_id": 123,
        "receiver_type": "user",
        "last_message_id": 789,
        "unread_count": 2,
        "status": 0,
        "created_at": "2021-12-31T16:00:00Z",
        "updated_at": "2021-12-31T16:00:00Z",
        "target_name": "张三",
        "target_avatar": "https://example.com/avatar.jpg",
        "customer_info": {
          "id": 123,
          "name": "张三",
          "avatar": "https://example.com/avatar.jpg",
          "is_online": true,
          "online_status": "active",
          "last_seen": "2021-12-31T16:00:00Z"
        }
      }
    ]
  }
}
```

## Redis数据结构

### 用户在线状态键
- **格式**: `user_online:{userType}:{userID}`
- **类型**: String (JSON)
- **TTL**: 30分钟
- **内容**: UserOnlineInfo JSON数据

### 会话用户列表键
- **格式**: `session_users:{sessionID}`
- **类型**: Set
- **TTL**: 30分钟
- **内容**: 用户键列表 (`{userType}:{userID}`)

## 测试覆盖

### 单元测试
- 在线状态服务的所有方法
- 状态转换逻辑
- Redis缓存操作
- 会话用户管理

### 集成测试
- 完整的用户上线/下线流程
- 多用户状态管理
- 会话关联功能
- 性能基准测试

### 测试运行
```bash
# 运行所有测试
./modules/chat/test_online_status.sh

# 运行特定测试
go test -v ./modules/chat/services -run TestOnlineStatusService
go test -v ./modules/chat/services -run TestOnlineStatusIntegration
```

## 部署注意事项

1. **Redis配置**: 确保Redis服务正常运行且配置正确
2. **内存使用**: 监控Redis内存使用情况，合理设置TTL
3. **并发处理**: 在高并发场景下注意锁的使用
4. **错误处理**: 完善Redis连接失败的降级处理
5. **日志监控**: 关注在线状态相关的日志输出

## 性能优化建议

1. **批量操作**: 使用Redis Pipeline进行批量操作
2. **缓存预热**: 系统启动时预加载热点数据
3. **连接池**: 合理配置Redis连接池大小
4. **监控指标**: 监控在线用户数量和状态变更频率
5. **清理策略**: 定期清理过期的状态数据

## 扩展功能

### 未来可能的扩展
1. **地理位置状态**: 添加用户地理位置信息
2. **设备状态**: 支持多设备在线状态管理
3. **自定义状态**: 支持用户自定义在线状态
4. **状态历史**: 记录用户在线状态历史
5. **统计分析**: 提供在线状态统计和分析功能
