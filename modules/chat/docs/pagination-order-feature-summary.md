# GetMessages API 分页排序功能实现总结

## 功能概述

为 GetMessages API 添加了完整的分页和排序支持，现在支持以下参数：

- `page`: 页码，默认为 1
- `pageSize`: 每页数量，默认为 20，最大为 100  
- `order`: 排序方式，支持 `asc`（升序）和 `desc`（倒序），默认为 `desc`

## 修改的文件

### 1. 数据访问层 (Repository)

#### `modules/chat/repositories/chat_repository.go`
- 更新 `ListMessagesBySession` 接口方法签名，添加 `order string` 参数

#### `modules/chat/repositories/impl/chat_repository_impl.go`
- 实现 `ListMessagesBySession` 方法的排序逻辑
- 根据 `order` 参数构建 `ORDER BY` 子句：
  - `asc`: `ORDER BY created_at ASC`
  - `desc`: `ORDER BY created_at DESC`（默认）

### 2. 业务逻辑层 (Service)

#### `modules/chat/services/chat_service.go`
- 更新 `GetSessionMessages` 接口方法签名，添加 `order string` 参数

#### `modules/chat/services/impl/chat_service_impl.go`
- 更新 `GetSessionMessages` 实现，传递 `order` 参数到数据访问层

### 3. 控制器层 (Controller)

#### `modules/chat/controllers/message_controller.go`
- 在 `GetMessages` 方法中添加 `order` 参数解析
- 添加参数验证逻辑，确保只接受 `asc` 和 `desc` 值
- 更新 API 文档注释，添加 `order` 参数说明
- 同时更新 `GetMessageList` 方法的文档注释

### 4. 前端示例代码

#### `modules/chat/docs/chat-frontend-example.js`
- 更新 `getMessages` 方法，添加 `order` 参数支持
- 修正返回数据结构，使用 `result.data.list` 而不是 `result.data.messages`
- 添加完整的分页信息返回
- 新增 `messagesPaginationExample` 函数，展示如何使用新功能

## 技术实现细节

### 参数验证

```go
// 验证排序参数，默认为desc（倒序）
if order != "asc" && order != "desc" {
    order = "desc"
}
```

### 数据库查询

```go
// 构建排序字段
orderBy := "-created_at" // 默认按创建时间倒序
if order == "asc" {
    orderBy = "created_at"
} else if order == "desc" {
    orderBy = "-created_at"
}

// 分页查询
offset := (page - 1) * pageSize
_, err = qb.OrderBy(orderBy).Limit(pageSize, offset).All(&messages)
```

### API 响应格式

```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "list": [...],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "page_count": 5
  }
}
```

## 使用示例

### 基本用法

```bash
# 获取第一页消息（默认倒序）
GET /api/v1/chat/sessions/1/messages?page=1&page_size=10

# 获取消息按时间升序排列
GET /api/v1/chat/sessions/1/messages?order=asc

# 组合参数
GET /api/v1/chat/sessions/1/messages?page=2&page_size=10&order=asc
```

### JavaScript 调用

```javascript
// 获取最新消息（倒序）
const latestMessages = await chatClient.getMessages(sessionId, 1, 10, 'desc');

// 获取最早消息（升序）  
const earliestMessages = await chatClient.getMessages(sessionId, 1, 10, 'asc');
```

## 边界值处理

1. **页码**: 小于1时自动设为1
2. **页面大小**: 小于1或大于100时自动设为20
3. **排序参数**: 非 `asc` 或 `desc` 时自动设为 `desc`

## 向后兼容性

- 所有现有的API调用都能正常工作
- 新参数都有合理的默认值
- 不会破坏现有的前端代码

## 测试建议

1. 测试基本分页功能
2. 测试排序功能（升序/倒序）
3. 测试参数组合
4. 测试边界值和异常情况
5. 测试权限验证

## 文档更新

- 创建了详细的测试文档 `test_pagination_order.md`
- 更新了前端示例代码
- 添加了使用示例和最佳实践

这个实现提供了完整的分页和排序功能，同时保持了良好的代码结构和向后兼容性。
