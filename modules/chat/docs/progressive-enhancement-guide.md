# 聊天模块渐进增强功能指南

## 1. 概述

本文档介绍了聊天模块的渐进增强功能，包括消息模板管理、自动回复系统和客服转接功能。这些功能可以显著提升客服效率和用户体验。

## 2. 消息模板管理系统

### 2.1 功能特性

- **模板分类管理**: 支持多种模板分类（欢迎、常用、结束语等）
- **角色权限控制**: 不同角色可使用不同的模板
- **变量替换**: 支持动态变量替换
- **使用统计**: 记录模板使用次数和频率
- **排序和搜索**: 支持模板排序和快速搜索

### 2.2 数据模型

#### 模板主表 (chat_template)
```sql
CREATE TABLE chat_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL COMMENT '模板标题',
    content TEXT NOT NULL COMMENT '模板内容',
    category VARCHAR(50) NOT NULL COMMENT '模板分类',
    type VARCHAR(20) NOT NULL COMMENT '模板类型',
    applicable_role VARCHAR(20) NOT NULL COMMENT '适用角色',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    creator_type VARCHAR(20) NOT NULL COMMENT '创建者类型',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    use_count BIGINT DEFAULT 0 COMMENT '使用次数',
    status INT DEFAULT 1 COMMENT '状态',
    tags VARCHAR(255) COMMENT '标签',
    variables TEXT COMMENT '变量定义',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2.3 使用示例

#### 创建模板
```javascript
const template = await fetch('/api/v1/chat/templates', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    title: '欢迎消息',
    content: '您好{{customer_name}}，欢迎咨询我们的服务！',
    category: 'welcome',
    type: 'welcome',
    applicable_role: 'merchant',
    variables: JSON.stringify({
      customer_name: { type: 'string', description: '客户姓名' }
    })
  })
});
```

#### 使用模板
```javascript
// 获取模板列表
const templates = await fetch('/api/v1/chat/templates?role=merchant&category=welcome');

// 使用模板发送消息
const processedContent = await processTemplateVariables(template.content, {
  customer_name: '张先生'
});

await sendMessage(sessionId, processedContent);
```

## 3. 自动回复系统

### 3.1 功能特性

- **多种触发方式**: 关键词匹配、正则表达式、时间触发
- **智能回复**: 支持延迟回复、优先级控制
- **工作时间管理**: 支持工作时间设置和离线消息
- **使用限制**: 支持最大触发次数限制
- **详细日志**: 记录所有自动回复的详细日志

### 3.2 数据模型

#### 自动回复规则表 (chat_auto_reply)
```sql
CREATE TABLE chat_auto_reply (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '规则名称',
    description VARCHAR(255) COMMENT '规则描述',
    type VARCHAR(20) NOT NULL COMMENT '规则类型',
    trigger_condition VARCHAR(20) NOT NULL COMMENT '触发条件',
    keywords TEXT COMMENT '关键词',
    regex_pattern VARCHAR(500) COMMENT '正则表达式',
    reply_content TEXT NOT NULL COMMENT '回复内容',
    reply_delay INT DEFAULT 0 COMMENT '回复延迟',
    priority INT DEFAULT 0 COMMENT '优先级',
    max_trigger_count INT DEFAULT 0 COMMENT '最大触发次数',
    trigger_count BIGINT DEFAULT 0 COMMENT '已触发次数',
    applicable_role VARCHAR(20) NOT NULL COMMENT '适用角色',
    applicable_time VARCHAR(100) COMMENT '适用时间',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    creator_type VARCHAR(20) NOT NULL COMMENT '创建者类型',
    status INT DEFAULT 1 COMMENT '状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3.3 使用示例

#### 创建自动回复规则
```javascript
const rule = await fetch('/api/v1/chat/auto-reply/rules', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${merchantToken}`
  },
  body: JSON.stringify({
    name: '价格咨询自动回复',
    type: 'keyword',
    keywords: JSON.stringify(['价格', '多少钱', '费用', '收费']),
    reply_content: '感谢您的咨询！我们的产品价格会根据具体需求有所不同，请稍等，我来为您详细介绍。',
    reply_delay: 2,
    priority: 1,
    applicable_role: 'merchant'
  })
});
```

#### 配置自动回复
```javascript
const config = await fetch('/api/v1/chat/auto-reply/config', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${merchantToken}`
  },
  body: JSON.stringify({
    enabled: 1,
    working_hours: JSON.stringify({
      start: 9,
      end: 18,
      days: [1, 2, 3, 4, 5] // 周一到周五
    }),
    welcome_message: '您好！欢迎咨询，我是客服小助手。',
    offline_message: '抱歉，当前不在工作时间，请留言，我们会尽快回复您。',
    no_response_delay: 300,
    no_response_message: '如果您还有其他问题，请随时告诉我。',
    max_auto_replies: 5
  })
});
```

## 4. 客服转接功能

### 4.1 功能特性

- **智能分配**: 基于技能、负载和可用性的智能分配
- **队列管理**: 支持多个转接队列和优先级
- **转接规则**: 支持自定义转接规则和条件
- **状态跟踪**: 实时跟踪转接状态和进度
- **统计报表**: 提供转接效率和质量统计

### 4.2 数据模型

#### 转接记录表 (chat_transfer)
```sql
CREATE TABLE chat_transfer (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL COMMENT '会话ID',
    from_user_id BIGINT NOT NULL COMMENT '转出用户ID',
    from_user_type VARCHAR(20) NOT NULL COMMENT '转出用户类型',
    to_user_id BIGINT NOT NULL COMMENT '转入用户ID',
    to_user_type VARCHAR(20) NOT NULL COMMENT '转入用户类型',
    type VARCHAR(20) NOT NULL COMMENT '转接类型',
    reason VARCHAR(50) NOT NULL COMMENT '转接原因',
    reason_text VARCHAR(500) COMMENT '转接原因说明',
    status VARCHAR(20) NOT NULL COMMENT '转接状态',
    priority INT DEFAULT 0 COMMENT '优先级',
    notes TEXT COMMENT '转接备注',
    accepted_at DATETIME COMMENT '接受时间',
    completed_at DATETIME COMMENT '完成时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4.3 使用示例

#### 手动转接
```javascript
const transfer = await fetch('/api/v1/chat/transfer', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${agentToken}`
  },
  body: JSON.stringify({
    session_id: 123,
    to_user_id: 456,
    to_user_type: 'admin',
    type: 'manual',
    reason: 'specialist',
    reason_text: '客户需要技术专家协助解决问题',
    notes: '客户反馈产品功能异常，需要技术支持'
  })
});
```

#### 自动分配
```javascript
const assignment = await fetch('/api/v1/chat/transfer/auto-assign', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${systemToken}`
  },
  body: JSON.stringify({
    session_id: 123,
    queue_id: 1,
    priority: 1,
    required_skills: ['technical_support', 'product_expert']
  })
});
```

## 5. 前端集成指导

### 5.1 模板选择器组件

```javascript
class TemplateSelector {
  constructor(container, chatClient) {
    this.container = container;
    this.chatClient = chatClient;
    this.templates = [];
    this.categories = [];
  }

  async loadTemplates() {
    const response = await fetch('/api/v1/chat/templates?role=merchant');
    const data = await response.json();
    this.templates = data.data.templates;
    this.categories = data.data.categories;
    this.render();
  }

  render() {
    const html = `
      <div class="template-selector">
        <div class="template-search">
          <input type="text" placeholder="搜索模板..." class="search-input">
        </div>
        <div class="template-categories">
          ${this.categories.map(cat => 
            `<button class="category-btn" data-category="${cat.id}">
              ${cat.name}
            </button>`
          ).join('')}
        </div>
        <div class="template-list">
          ${this.templates.map(template => 
            `<div class="template-item" data-template-id="${template.id}">
              <h4>${template.title}</h4>
              <p>${template.content}</p>
              <div class="template-meta">
                <span class="use-count">使用 ${template.use_count} 次</span>
              </div>
            </div>`
          ).join('')}
        </div>
      </div>
    `;
    
    this.container.innerHTML = html;
    this.bindEvents();
  }

  bindEvents() {
    this.container.addEventListener('click', (e) => {
      if (e.target.closest('.template-item')) {
        const templateId = e.target.closest('.template-item').dataset.templateId;
        this.selectTemplate(templateId);
      }
    });
  }

  async selectTemplate(templateId) {
    const template = this.templates.find(t => t.id == templateId);
    if (template) {
      // 处理变量替换
      const content = await this.processVariables(template);
      
      // 插入到输入框
      this.chatClient.insertText(content);
      
      // 记录使用
      await this.recordUsage(templateId);
    }
  }
}
```

### 5.2 自动回复管理界面

```javascript
class AutoReplyManager {
  constructor(container) {
    this.container = container;
    this.rules = [];
  }

  async loadRules() {
    const response = await fetch('/api/v1/chat/auto-reply/rules');
    const data = await response.json();
    this.rules = data.data.rules;
    this.render();
  }

  render() {
    const html = `
      <div class="auto-reply-manager">
        <div class="manager-header">
          <h3>自动回复规则</h3>
          <button class="btn-primary" onclick="this.showCreateDialog()">
            新建规则
          </button>
        </div>
        
        <div class="rules-list">
          ${this.rules.map(rule => this.renderRule(rule)).join('')}
        </div>
      </div>
    `;
    
    this.container.innerHTML = html;
  }

  renderRule(rule) {
    return `
      <div class="rule-item ${rule.status ? 'active' : 'inactive'}">
        <div class="rule-header">
          <h4>${rule.name}</h4>
          <div class="rule-actions">
            <button class="btn-edit" onclick="this.editRule(${rule.id})">编辑</button>
            <button class="btn-toggle" onclick="this.toggleRule(${rule.id})">
              ${rule.status ? '禁用' : '启用'}
            </button>
          </div>
        </div>
        
        <div class="rule-content">
          <p class="rule-description">${rule.description}</p>
          <div class="rule-stats">
            <span>触发次数: ${rule.trigger_count}</span>
            <span>类型: ${rule.type}</span>
            <span>优先级: ${rule.priority}</span>
          </div>
        </div>
      </div>
    `;
  }
}
```

## 6. 部署和配置

### 6.1 数据库迁移

```sql
-- 创建模板相关表
SOURCE modules/chat/migrations/001_create_template_tables.sql;

-- 创建自动回复相关表
SOURCE modules/chat/migrations/002_create_auto_reply_tables.sql;

-- 创建转接相关表
SOURCE modules/chat/migrations/003_create_transfer_tables.sql;
```

### 6.2 配置文件

```yaml
# config/chat_enhancement.yaml
chat:
  template:
    max_templates_per_user: 100
    max_template_length: 2000
    enable_variables: true
    
  auto_reply:
    max_rules_per_user: 50
    max_trigger_count: 1000
    default_delay: 1
    enable_regex: true
    
  transfer:
    max_queue_size: 100
    auto_assign_timeout: 30
    max_concurrent_transfers: 10
```

## 7. 监控和统计

### 7.1 关键指标

- **模板使用率**: 各模板的使用频率和效果
- **自动回复命中率**: 自动回复规则的匹配成功率
- **转接效率**: 转接响应时间和成功率
- **客服工作量**: 各客服的会话数量和处理时间

### 7.2 报表示例

```javascript
// 获取模板使用统计
const templateStats = await fetch('/api/v1/chat/templates/stats?period=7d');

// 获取自动回复效果统计
const autoReplyStats = await fetch('/api/v1/chat/auto-reply/stats?period=7d');

// 获取转接效率统计
const transferStats = await fetch('/api/v1/chat/transfer/stats?period=7d');
```

通过以上渐进增强功能，聊天系统将具备企业级客服系统的核心能力，大幅提升客服效率和用户满意度。
