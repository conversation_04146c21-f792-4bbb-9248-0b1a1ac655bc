# WebSocket集成指南 - 第一部分：基础知识与连接建立

## 概述

WebSocket是一种网络传输协议，提供全双工通信信道，使服务器可以向客户端推送数据而无需客户端发起请求。在O_Mall聊天模块中，WebSocket用于实现以下功能：

1. 实时消息推送
2. 在线状态管理
3. 会话更新通知

本指南将详细介绍如何与O_Mall聊天模块的WebSocket服务进行集成。

## WebSocket连接

### 连接URL

WebSocket连接使用以下URL格式：

```
ws://{服务器地址}/api/v1/chat/ws?token={JWT令牌}
```

在生产环境中，应使用安全的WebSocket连接：

```
wss://{服务器地址}/api/v1/chat/ws?token={JWT令牌}
```

### 认证方式

聊天模块支持两种WebSocket连接认证方式：

#### 1. 查询参数认证

在WebSocket URL中通过查询参数传递JWT令牌：

```
ws://{服务器地址}/api/v1/chat/ws?token={JWT令牌}
```

这是最常用且推荐的方式，特别适用于浏览器环境。

#### 2. 请求头认证

在WebSocket连接请求中添加Authorization请求头：

```
Authorization: Bearer {JWT令牌}
```

注意：某些客户端库可能不支持在WebSocket连接中自定义请求头，请根据您的技术栈选择合适的认证方式。

### 连接状态码

WebSocket连接可能返回以下状态码：

| 状态码 | 描述 |
|-------|------|
| 101 | 连接成功 |
| 401 | 认证失败，令牌无效或过期 |
| 403 | 权限不足，用户无权访问 |
| 429 | 请求过于频繁，触发限流 |

### 连接实例（JavaScript）

```javascript
// 连接建立
function connectWebSocket(token) {
  const wsUrl = `ws://api.example.com/api/v1/chat/ws?token=${token}`;
  const socket = new WebSocket(wsUrl);
  
  // 连接打开事件
  socket.onopen = (event) => {
    console.log('WebSocket连接已建立');
    
    // 发送连接成功消息
    const connectMessage = {
      type: 'system',
      event: 'connected',
      timestamp: Date.now()
    };
    socket.send(JSON.stringify(connectMessage));
  };
  
  // 连接关闭事件
  socket.onclose = (event) => {
    console.log(`WebSocket连接已关闭: 代码=${event.code}, 原因=${event.reason}`);
    
    // 如果是意外关闭，则尝试重新连接
    if (event.code !== 1000) {
      // 实现重连逻辑
      setTimeout(() => connectWebSocket(token), 3000);
    }
  };
  
  // 连接错误事件
  socket.onerror = (error) => {
    console.error('WebSocket错误:', error);
  };
  
  // 接收消息事件
  socket.onmessage = (event) => {
    try {
      const message = JSON.parse(event.data);
      handleWebSocketMessage(message);
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  };
  
  return socket;
}

// 处理接收到的消息
function handleWebSocketMessage(message) {
  switch (message.type) {
    case 'message':
      handleChatMessage(message);
      break;
    case 'system':
      handleSystemMessage(message);
      break;
    case 'heartbeat':
      respondToHeartbeat(message);
      break;
    default:
      console.warn('未知消息类型:', message.type);
  }
}
```

### 连接实例（Go）

```go
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/url"
    "time"

    "github.com/gorilla/websocket"
)

// WebSocketClient 表示WebSocket客户端
type WebSocketClient struct {
    conn      *websocket.Conn
    token     string
    serverURL string
    done      chan struct{}
}

// 消息结构
type Message struct {
    Type      string      `json:"type"`
    Event     string      `json:"event"`
    SessionID int64       `json:"session_id"`
    Data      interface{} `json:"data"`
    Timestamp int64       `json:"timestamp"`
}

// NewWebSocketClient 创建新的WebSocket客户端
func NewWebSocketClient(serverURL, token string) *WebSocketClient {
    return &WebSocketClient{
        serverURL: serverURL,
        token:     token,
        done:      make(chan struct{}),
    }
}

// Connect 连接到WebSocket服务器
func (c *WebSocketClient) Connect() error {
    u, err := url.Parse(c.serverURL)
    if err != nil {
        return err
    }
    
    // 添加token参数
    q := u.Query()
    q.Set("token", c.token)
    u.RawQuery = q.Encode()
    
    // 连接WebSocket
    conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
    if err != nil {
        return err
    }
    c.conn = conn
    
    fmt.Println("WebSocket连接已建立")
    
    // 发送连接消息
    connectMsg := Message{
        Type:      "system",
        Event:     "connected",
        Timestamp: time.Now().Unix(),
    }
    if err := c.Send(connectMsg); err != nil {
        return err
    }
    
    // 启动接收消息的goroutine
    go c.receiveMessages()
    
    return nil
}

// Send 发送消息
func (c *WebSocketClient) Send(msg interface{}) error {
    return c.conn.WriteJSON(msg)
}

// Close 关闭连接
func (c *WebSocketClient) Close() {
    close(c.done)
    if c.conn != nil {
        c.conn.Close()
    }
}

// receiveMessages 接收服务器消息
func (c *WebSocketClient) receiveMessages() {
    for {
        select {
        case <-c.done:
            return
        default:
            var msg Message
            err := c.conn.ReadJSON(&msg)
            if err != nil {
                log.Printf("读取消息错误: %v", err)
                return
            }
            
            // 处理接收到的消息
            c.handleMessage(msg)
        }
    }
}

// handleMessage 处理接收到的消息
func (c *WebSocketClient) handleMessage(msg Message) {
    switch msg.Type {
    case "message":
        fmt.Printf("收到聊天消息: %+v\n", msg)
    case "system":
        fmt.Printf("收到系统消息: %+v\n", msg)
    case "heartbeat":
        // 响应心跳
        c.Send(Message{
            Type:      "heartbeat",
            Event:     "pong",
            Timestamp: time.Now().Unix(),
        })
    default:
        fmt.Printf("收到未知类型消息: %+v\n", msg)
    }
}
```
