# WebSocket集成指南 - 第二部分：消息格式与通信协议

## 消息格式

聊天模块的WebSocket通信采用JSON格式进行数据交换。所有消息都遵循以下基础结构：

```json
{
  "type": "消息类型",
  "event": "事件类型",
  "session_id": 123,
  "data": {
    // 具体消息内容，结构根据消息类型不同而变化
  },
  "timestamp": 1623479123
}
```

### 消息类型 (type)

| 类型 | 描述 |
|-----|------|
| message | 聊天消息，包括文本、图片、文件等 |
| system | 系统消息，如状态更新、会话创建等 |
| notification | 通知消息，如未读消息提醒 |
| heartbeat | 心跳消息，用于保持连接活跃 |
| status | 状态消息，如在线状态变更 |

### 事件类型 (event)

每种消息类型下有不同的事件类型：

#### message 类型事件

| 事件 | 描述 |
|-----|------|
| new_message | 新消息 |
| message_read | 消息已读 |
| message_recall | 消息撤回 |

#### system 类型事件

| 事件 | 描述 |
|-----|------|
| connected | 连接成功 |
| disconnected | 连接断开 |
| session_created | 会话创建 |
| session_closed | 会话关闭 |
| error | 错误消息 |

#### notification 类型事件

| 事件 | 描述 |
|-----|------|
| unread_count | 未读消息计数更新 |
| mention | @提及通知 |

#### heartbeat 类型事件

| 事件 | 描述 |
|-----|------|
| ping | 服务器发送的心跳检测 |
| pong | 客户端响应的心跳确认 |

#### status 类型事件

| 事件 | 描述 |
|-----|------|
| online | 用户上线 |
| offline | 用户离线 |
| typing | 用户正在输入 |

### 具体消息结构示例

#### 1. 新聊天消息 (new_message)

```json
{
  "type": "message",
  "event": "new_message",
  "session_id": 123,
  "data": {
    "id": 456,
    "sender_id": 789,
    "sender_type": "user",
    "content": "你好，请问有什么可以帮助你的？",
    "type": "text",
    "resource_id": "",
    "status": 0,
    "created_at": "2023-06-12T10:30:45Z",
    "sender_name": "客服小王",
    "sender_avatar": "https://example.com/avatars/service1.jpg"
  },
  "timestamp": 1686565845
}
```

#### 2. 图片消息

```json
{
  "type": "message",
  "event": "new_message",
  "session_id": 123,
  "data": {
    "id": 457,
    "sender_id": 101,
    "sender_type": "user",
    "content": "商品图片",
    "type": "image",
    "resource_id": "img_202306120001",
    "status": 0,
    "created_at": "2023-06-12T10:35:22Z",
    "sender_name": "张三",
    "sender_avatar": "https://example.com/avatars/user101.jpg"
  },
  "timestamp": 1686566122
}
```

#### 3. 状态更新 (在线状态)

```json
{
  "type": "status",
  "event": "online",
  "session_id": 123,
  "data": {
    "user_id": 789,
    "user_type": "merchant"
  },
  "timestamp": 1686566300
}
```

#### 4. 心跳消息

服务器发送：
```json
{
  "type": "heartbeat",
  "event": "ping",
  "timestamp": 1686566400
}
```

客户端响应：
```json
{
  "type": "heartbeat",
  "event": "pong",
  "timestamp": 1686566401
}
```

## 通信流程

### 1. 初始连接流程

```
客户端                                      服务器
  |                                          |
  |---- WebSocket连接请求 (带JWT令牌) ------>|
  |                                          |
  |<-------- 连接建立 (状态码101) -----------|
  |                                          |
  |------ {"type":"system",  ---------------->|
  |        "event":"connected"} ------------>|
  |                                          |
  |<------ {"type":"system", ----------------| 
  |         "event":"connected",              |
  |         "data":{"user_id":xxx}} ---------|
  |                                          |
```

### 2. 消息收发流程

```
客户端                                      服务器
  |                                          |
  |------- 发送文本消息(HTTP POST) --------->|
  |                                          |-- 消息存储到数据库
  |<--------- HTTP响应(消息ID等) -----------|  |
  |                                          |  |
  |                                          |---> 查找会话相关的WebSocket连接
  |                                          |
  |<--------- WebSocket消息推送 -------------|
  |  {"type":"message","event":"new_message"}|
  |                                          |
```

### 3. 心跳机制

```
客户端                                      服务器
  |                                          |
  |<------ {"type":"heartbeat", -------------|  每30秒
  |         "event":"ping"} -----------------|
  |                                          |
  |------- {"type":"heartbeat", ------------->|  5秒内
  |         "event":"pong"} ---------------->|
  |                                          |
```

如果服务器在发送ping后5秒内没有收到pong响应，会考虑客户端已断开连接，关闭WebSocket连接。同样，如果客户端超过90秒没有收到任何消息(包括ping)，应主动重新连接。

## 重连机制

为确保可靠通信，客户端应实现重连机制：

1. 检测到连接断开后，以递增的间隔尝试重连
2. 初始重连间隔可设为1-3秒
3. 使用指数退避算法增加重连间隔，直至最大间隔(如60秒)
4. 重连次数达到上限后，可提示用户手动刷新页面

### 指数退避重连示例

```javascript
function reconnect() {
  let reconnectAttempts = 0;
  const maxReconnectAttempts = 10;
  const baseDelay = 1000; // 1秒
  const maxDelay = 60000; // 60秒
  
  function attemptReconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.error('达到最大重连次数，放弃重连');
      showUserMessage('连接已断开，请刷新页面重试');
      return;
    }
    
    reconnectAttempts++;
    
    // 计算延迟：指数退避算法
    const delay = Math.min(baseDelay * Math.pow(1.5, reconnectAttempts - 1), maxDelay);
    console.log(`${reconnectAttempts}次重连尝试将在${delay/1000}秒后进行`);
    
    setTimeout(() => {
      console.log(`尝试第${reconnectAttempts}次重连...`);
      connectWebSocket(authToken)
        .then(socket => {
          console.log('重连成功');
          reconnectAttempts = 0; // 重置计数器
          // 重新初始化处理程序
        })
        .catch(error => {
          console.error('重连失败', error);
          attemptReconnect();
        });
    }, delay);
  }
  
  attemptReconnect();
}
```
