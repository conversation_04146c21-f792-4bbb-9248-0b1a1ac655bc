# WebSocket集成指南 - 第三部分：应用场景与最佳实践

## 常见应用场景

### 场景一：多端同步消息

当用户在多个终端（如Web、iOS、Android）同时登录时，聊天模块确保消息在所有终端同步显示。

**实现方案**：

1. 用户登录时，每个终端建立独立的WebSocket连接
2. 服务器根据用户ID（而非连接ID）管理WebSocket连接池
3. 新消息到达时，服务器向用户的所有活跃连接推送消息

```javascript
// 前端实现示例
function setupMultiDeviceSync() {
  // 消息去重处理
  const processedMsgIds = new Set();
  
  chatClient.onMessage(message => {
    // 检查消息是否已处理（防止多端重复显示）
    if (message.type === 'message' && message.data && message.data.id) {
      if (processedMsgIds.has(message.data.id)) {
        // 忽略已处理的消息
        return;
      }
      // 记录消息ID
      processedMsgIds.add(message.data.id);
      // 设置过期清理（避免集合无限增长）
      setTimeout(() => {
        processedMsgIds.delete(message.data.id);
      }, 60000); // 1分钟后清理
    }
    
    // 处理消息
    handleChatMessage(message);
  });
}
```

### 场景二：消息已读状态同步

当用户在一个终端阅读消息后，其他终端也应同步更新消息状态。

**实现方案**：

1. 用户阅读消息时，前端发送已读确认请求
2. 服务器更新消息状态并通过WebSocket广播状态更新
3. 其他终端收到状态更新后修改本地显示

```javascript
// 标记消息为已读
async function markMessagesAsRead(sessionId) {
  try {
    // API调用
    await api.post(`/api/v1/chat/sessions/${sessionId}/read`);
    
    // 本地状态更新
    updateLocalReadStatus(sessionId);
    
    // 其他终端会通过WebSocket接收状态更新
  } catch (error) {
    console.error('标记已读失败:', error);
  }
}

// 处理接收到的已读状态更新
function handleMessageReadStatus(message) {
  if (message.type === 'message' && message.event === 'message_read') {
    const { session_id, last_read_id } = message.data;
    updateLocalReadStatus(session_id, last_read_id);
  }
}
```

### 场景三：实时在线状态

显示聊天对象的在线状态，提升用户体验。

**实现方案**：

1. 用户连接WebSocket时，服务器广播用户上线状态
2. 定期发送心跳包确认在线状态
3. 用户显式下线或断开连接超时，服务器广播离线状态

```javascript
// 监听在线状态变化
function monitorOnlineStatus() {
  chatClient.onMessage(message => {
    if (message.type === 'status') {
      switch(message.event) {
        case 'online':
          updateUserStatus(message.data.user_id, 'online');
          break;
        case 'offline':
          updateUserStatus(message.data.user_id, 'offline');
          break;
        case 'typing':
          showTypingIndicator(message.data.user_id, message.data.session_id);
          break;
      }
    }
  });
}

// 发送正在输入状态
function sendTypingStatus(sessionId) {
  // 防抖：避免频繁发送
  if (typingTimer) {
    clearTimeout(typingTimer);
  }
  
  // 3秒内只发送一次
  if (!lastTypingSent || Date.now() - lastTypingSent > 3000) {
    chatClient.send({
      type: 'status',
      event: 'typing',
      session_id: sessionId,
      timestamp: Date.now()
    });
    
    lastTypingSent = Date.now();
  }
  
  // 5秒后自动清除输入状态
  typingTimer = setTimeout(() => {
    typingTimer = null;
  }, 5000);
}
```

## 最佳实践

### 1. 安全性建议

#### 1.1 令牌管理

- 使用短期访问令牌（JWT），定期刷新
- WebSocket断开重连时校验令牌有效性，无效则引导用户重新登录
- 敏感操作（如删除消息）通过REST API进行，并添加额外验证

#### 1.2 内容安全

- 客户端发送前对消息内容进行过滤，移除潜在的XSS攻击
- 接收消息时进行安全处理，使用安全的渲染方式
- 对于图片、文件等媒体消息，确保资源URL经过验证

```javascript
// 安全显示消息内容
function renderMessageSafely(message) {
  if (message.type === 'text') {
    const div = document.createElement('div');
    // 使用textContent而非innerHTML防止XSS
    div.textContent = message.content;
    
    // 可选：将链接转为可点击状态（确保安全）
    div.innerHTML = linkifyUrls(div.innerHTML);
    return div;
  }
  // 处理其他消息类型...
}

// 将文本中的URL转为链接
function linkifyUrls(text) {
  // 使用URL正则表达式
  const urlRegex = /https?:\/\/[^\s]+/g;
  return text.replace(urlRegex, url => {
    // 添加noopener noreferrer确保安全
    return `<a href="${encodeURI(url)}" target="_blank" rel="noopener noreferrer">${url}</a>`;
  });
}
```

### 2. 性能优化

#### 2.1 连接管理

- 实现智能重连机制，避免连接风暴
- 连接恢复后，获取离线期间的消息
- 使用单一WebSocket连接，而非为每个会话创建连接

```javascript
// 重连后获取离线消息
async function fetchOfflineMessages() {
  // 记录最后收到的消息时间
  const lastReceivedTime = getLastMessageTimestamp();
  
  try {
    // 获取离线期间的消息
    const response = await api.get('/api/v1/chat/messages/offline', {
      params: { after: lastReceivedTime }
    });
    
    // 处理离线消息
    const offlineMessages = response.data.messages;
    for (const message of offlineMessages) {
      // 添加到相应的会话
      addMessageToSession(message.session_id, message);
    }
  } catch (error) {
    console.error('获取离线消息失败', error);
  }
}
```

#### 2.2 消息处理

- 实现消息分页加载，避免一次加载大量历史消息
- 使用虚拟滚动技术处理长对话
- 延迟加载和处理媒体消息

```javascript
// 实现滚动加载历史消息
function setupScrollLoadHistory(sessionId, messageContainer) {
  let page = 1;
  let loading = false;
  let allLoaded = false;
  const PAGE_SIZE = 20;
  
  messageContainer.addEventListener('scroll', async () => {
    // 检测滚动到顶部
    if (messageContainer.scrollTop <= 50 && !loading && !allLoaded) {
      loading = true;
      
      try {
        // 加载更多历史消息
        const response = await chatClient.getMessages(sessionId, ++page, PAGE_SIZE);
        
        if (response.messages.length > 0) {
          // 获取当前第一条消息的元素，用于后续定位
          const firstMessage = messageContainer.firstChild;
          
          // 添加历史消息到顶部
          for (const message of response.messages.reverse()) {
            prependMessage(messageContainer, message);
          }
          
          // 保持滚动位置
          firstMessage.scrollIntoView();
        }
        
        // 检查是否已加载全部
        if (response.messages.length < PAGE_SIZE) {
          allLoaded = true;
        }
      } catch (error) {
        console.error('加载历史消息失败', error);
      } finally {
        loading = false;
      }
    }
  });
}
```

### 3. 用户体验建议

#### 3.1 消息状态反馈

为消息添加多种状态指示：

- 发送中(sending)：消息已创建但尚未收到服务器确认
- 已发送(sent)：服务器已确认接收
- 已送达(delivered)：消息已送达对方设备
- 已读(read)：对方已阅读消息
- 发送失败(failed)：消息发送失败

```javascript
// 跟踪消息状态
function trackMessageStatus(messageId, sessionId) {
  // 初始状态为"发送中"
  updateMessageStatus(messageId, 'sending');
  
  // 设置超时检测
  const timeout = setTimeout(() => {
    // 检查消息是否仍在"发送中"状态
    if (getMessageStatus(messageId) === 'sending') {
      updateMessageStatus(messageId, 'failed');
      showResendOption(messageId);
    }
  }, 10000); // 10秒超时
  
  // 监听状态更新消息
  const statusListener = message => {
    if (message.type === 'message' && message.data && message.data.id === messageId) {
      // 服务器确认
      clearTimeout(timeout);
      updateMessageStatus(messageId, 'sent');
      
      // 移除监听器
      chatClient.removeMessageListener(statusListener);
    } else if (message.type === 'message' && message.event === 'message_delivered' && 
               message.data && message.data.message_ids && message.data.message_ids.includes(messageId)) {
      // 消息已送达
      updateMessageStatus(messageId, 'delivered');
    } else if (message.type === 'message' && message.event === 'message_read' && 
               message.session_id === sessionId) {
      // 消息已读（会话级别）
      const { last_read_id } = message.data;
      if (last_read_id >= messageId) {
        updateMessageStatus(messageId, 'read');
      }
    }
  };
  
  chatClient.addMessageListener(statusListener);
}

// 重发失败消息
function resendMessage(messageId) {
  const message = getStoredMessage(messageId);
  if (!message) return;
  
  // 删除旧消息显示
  removeMessageElement(messageId);
  
  // 重新发送
  sendMessage(message.session_id, message.content, message.type);
}
```

#### 3.2 离线支持

- 实现本地消息存储，支持离线查看历史消息
- 使用IndexedDB或localStorage缓存会话和消息
- 连接恢复后同步本地与服务器状态

```javascript
// 设置本地存储
const chatStorage = {
  // 存储消息
  async saveMessage(message) {
    const db = await this.getDatabase();
    const tx = db.transaction('messages', 'readwrite');
    await tx.objectStore('messages').put(message);
  },
  
  // 获取会话消息
  async getSessionMessages(sessionId) {
    const db = await this.getDatabase();
    const tx = db.transaction('messages', 'readonly');
    const index = tx.objectStore('messages').index('session_id');
    return await index.getAll(IDBKeyRange.only(sessionId));
  },
  
  // 打开数据库连接
  async getDatabase() {
    if (this.db) return this.db;
    
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('chatDatabase', 1);
      
      request.onerror = event => {
        console.error('数据库打开失败:', event);
        reject(event.target.error);
      };
      
      request.onsuccess = event => {
        this.db = event.target.result;
        resolve(this.db);
      };
      
      request.onupgradeneeded = event => {
        const db = event.target.result;
        
        // 创建消息存储
        const messagesStore = db.createObjectStore('messages', { keyPath: 'id' });
        messagesStore.createIndex('session_id', 'session_id');
        messagesStore.createIndex('created_at', 'created_at');
        
        // 创建会话存储
        const sessionsStore = db.createObjectStore('sessions', { keyPath: 'id' });
      };
    });
  }
};
```

## 故障排查

### 常见问题

1. **连接断开问题**
   - 检查网络连接
   - 验证JWT令牌是否过期
   - 检查服务器日志是否有连接限制或异常

2. **消息丢失问题**
   - 实现消息ID跟踪和确认机制
   - 定期同步本地和服务器消息状态
   - 实现消息重发机制

3. **性能问题**
   - 限制单个会话加载的消息数量
   - 使用分页方式加载历史消息
   - 优化图片和媒体文件的加载方式

### 调试技巧

1. **WebSocket调试**
   - 使用浏览器开发者工具的Network标签监控WebSocket通讯
   - 添加日志记录所有发送和接收的消息
   - 使用专用WebSocket调试工具如WebSocket King

2. **问题定位**
   - 实现详细的客户端日志记录
   - 使用唯一请求ID关联前后端日志
   - 对关键操作添加时间戳，分析性能问题

```javascript
// 增强日志记录
class LoggerService {
  constructor() {
    this.logs = [];
    this.maxLogs = 1000;
  }
  
  log(type, message, data) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type,
      message,
      data: data ? JSON.stringify(data) : undefined
    };
    
    console.log(`[${logEntry.timestamp}] [${type}] ${message}`, data);
    
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }
  }
  
  // 获取日志用于问题报告
  exportLogs() {
    return JSON.stringify(this.logs);
  }
  
  // 上报错误日志
  reportError(error, context) {
    this.log('ERROR', error.message, { stack: error.stack, context });
    // 可以添加远程错误上报
  }
}

const logger = new LoggerService();

// 使用示例
try {
  // 操作
} catch (error) {
  logger.reportError(error, { action: 'sendMessage', sessionId: 123 });
}
```
