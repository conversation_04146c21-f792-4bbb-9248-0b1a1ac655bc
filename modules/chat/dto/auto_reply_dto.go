/**
 * auto_reply_dto.go
 * 自动回复相关的数据传输对象
 *
 * 该文件定义了自动回复系统相关的请求和响应结构体
 */

package dto

import "time"

// CreateAutoReplyRuleRequest 创建自动回复规则请求
type CreateAutoReplyRuleRequest struct {
	Name             string `json:"name" validate:"required,max=100"`        // 规则名称
	Description      string `json:"description" validate:"max=255"`          // 规则描述
	Type             string `json:"type" validate:"required,max=20"`         // 规则类型
	TriggerCondition string `json:"trigger_condition" validate:"required,max=20"` // 触发条件
	Keywords         string `json:"keywords"`                                // 关键词（JSON格式）
	RegexPattern     string `json:"regex_pattern" validate:"max=500"`        // 正则表达式
	ReplyContent     string `json:"reply_content" validate:"required"`       // 回复内容
	ReplyDelay       int    `json:"reply_delay" validate:"min=0,max=300"`    // 回复延迟（秒）
	Priority         int    `json:"priority" validate:"min=0,max=100"`       // 优先级
	MaxTriggerCount  int    `json:"max_trigger_count" validate:"min=0"`      // 最大触发次数
	ApplicableRole   string `json:"applicable_role" validate:"required,max=20"` // 适用角色
	ApplicableTime   string `json:"applicable_time"`                         // 适用时间（JSON格式）
	CreatorID        int64  `json:"creator_id" validate:"required"`          // 创建者ID
	CreatorType      string `json:"creator_type" validate:"required,max=20"` // 创建者类型
}

// UpdateAutoReplyRuleRequest 更新自动回复规则请求
type UpdateAutoReplyRuleRequest struct {
	Name             string `json:"name" validate:"max=100"`          // 规则名称
	Description      string `json:"description" validate:"max=255"`   // 规则描述
	Type             string `json:"type" validate:"max=20"`           // 规则类型
	TriggerCondition string `json:"trigger_condition" validate:"max=20"` // 触发条件
	Keywords         string `json:"keywords"`                         // 关键词（JSON格式）
	RegexPattern     string `json:"regex_pattern" validate:"max=500"` // 正则表达式
	ReplyContent     string `json:"reply_content"`                    // 回复内容
	ReplyDelay       int    `json:"reply_delay" validate:"min=0,max=300"` // 回复延迟（秒）
	Priority         int    `json:"priority" validate:"min=0,max=100"` // 优先级
	MaxTriggerCount  int    `json:"max_trigger_count" validate:"min=0"` // 最大触发次数
	ApplicableRole   string `json:"applicable_role" validate:"max=20"` // 适用角色
	ApplicableTime   string `json:"applicable_time"`                  // 适用时间（JSON格式）
	Status           int    `json:"status"`                           // 状态
}

// ListAutoReplyRulesRequest 获取自动回复规则列表请求
type ListAutoReplyRulesRequest struct {
	Page           int    `json:"page" validate:"min=1"`              // 页码
	PageSize       int    `json:"page_size" validate:"min=1,max=100"` // 每页数量
	Type           string `json:"type"`                               // 类型筛选
	ApplicableRole string `json:"applicable_role"`                    // 角色筛选
	Status         int    `json:"status"`                             // 状态筛选
	CreatorID      int64  `json:"creator_id"`                         // 创建者ID筛选
	Keyword        string `json:"keyword"`                            // 关键词搜索
	SortBy         string `json:"sort_by"`                            // 排序字段
	SortOrder      string `json:"sort_order"`                         // 排序方向
}

// UpdateAutoReplyConfigRequest 更新自动回复配置请求
type UpdateAutoReplyConfigRequest struct {
	Enabled           int    `json:"enabled"`                                  // 是否启用
	WorkingHours      string `json:"working_hours"`                            // 工作时间（JSON格式）
	OfflineMessage    string `json:"offline_message" validate:"max=1000"`      // 离线消息
	WelcomeMessage    string `json:"welcome_message" validate:"max=1000"`      // 欢迎消息
	NoResponseDelay   int    `json:"no_response_delay" validate:"min=0,max=3600"` // 无响应延迟（秒）
	NoResponseMessage string `json:"no_response_message" validate:"max=1000"`  // 无响应消息
	MaxAutoReplies    int    `json:"max_auto_replies" validate:"min=0,max=50"` // 最大自动回复次数
}

// GetAutoReplyLogsRequest 获取自动回复日志请求
type GetAutoReplyLogsRequest struct {
	Page      int    `json:"page" validate:"min=1"`              // 页码
	PageSize  int    `json:"page_size" validate:"min=1,max=100"` // 每页数量
	RuleID    int64  `json:"rule_id"`                            // 规则ID筛选
	SessionID int64  `json:"session_id"`                         // 会话ID筛选
	UserID    int64  `json:"user_id"`                            // 用户ID筛选
	UserType  string `json:"user_type"`                          // 用户类型筛选
	StartTime string `json:"start_time"`                         // 开始时间
	EndTime   string `json:"end_time"`                           // 结束时间
}

// AutoReplyRuleResponse 自动回复规则响应
type AutoReplyRuleResponse struct {
	ID               int64     `json:"id"`                // 规则ID
	Name             string    `json:"name"`              // 规则名称
	Description      string    `json:"description"`       // 规则描述
	Type             string    `json:"type"`              // 规则类型
	TriggerCondition string    `json:"trigger_condition"` // 触发条件
	Keywords         []string  `json:"keywords"`          // 关键词
	RegexPattern     string    `json:"regex_pattern"`     // 正则表达式
	ReplyContent     string    `json:"reply_content"`     // 回复内容
	ReplyDelay       int       `json:"reply_delay"`       // 回复延迟（秒）
	Priority         int       `json:"priority"`          // 优先级
	MaxTriggerCount  int       `json:"max_trigger_count"` // 最大触发次数
	TriggerCount     int64     `json:"trigger_count"`     // 已触发次数
	ApplicableRole   string    `json:"applicable_role"`   // 适用角色
	ApplicableTime   map[string]interface{} `json:"applicable_time"` // 适用时间
	CreatorID        int64     `json:"creator_id"`        // 创建者ID
	CreatorType      string    `json:"creator_type"`      // 创建者类型
	CreatorName      string    `json:"creator_name"`      // 创建者名称
	Status           int       `json:"status"`            // 状态
	CreatedAt        time.Time `json:"created_at"`        // 创建时间
	UpdatedAt        time.Time `json:"updated_at"`        // 更新时间
}

// AutoReplyConfigResponse 自动回复配置响应
type AutoReplyConfigResponse struct {
	ID                int64     `json:"id"`                 // 配置ID
	UserID            int64     `json:"user_id"`            // 用户ID
	UserType          string    `json:"user_type"`          // 用户类型
	Enabled           int       `json:"enabled"`            // 是否启用
	WorkingHours      map[string]interface{} `json:"working_hours"` // 工作时间
	OfflineMessage    string    `json:"offline_message"`    // 离线消息
	WelcomeMessage    string    `json:"welcome_message"`    // 欢迎消息
	NoResponseDelay   int       `json:"no_response_delay"`  // 无响应延迟（秒）
	NoResponseMessage string    `json:"no_response_message"` // 无响应消息
	MaxAutoReplies    int       `json:"max_auto_replies"`   // 最大自动回复次数
	CreatedAt         time.Time `json:"created_at"`         // 创建时间
	UpdatedAt         time.Time `json:"updated_at"`         // 更新时间
}

// AutoReplyLogResponse 自动回复日志响应
type AutoReplyLogResponse struct {
	ID          int64     `json:"id"`           // 日志ID
	RuleID      int64     `json:"rule_id"`      // 规则ID
	RuleName    string    `json:"rule_name"`    // 规则名称
	SessionID   int64     `json:"session_id"`   // 会话ID
	MessageID   int64     `json:"message_id"`   // 触发消息ID
	ReplyID     int64     `json:"reply_id"`     // 回复消息ID
	UserID      int64     `json:"user_id"`      // 用户ID
	UserType    string    `json:"user_type"`    // 用户类型
	UserName    string    `json:"user_name"`    // 用户名称
	TriggerText string    `json:"trigger_text"` // 触发文本
	ReplyText   string    `json:"reply_text"`   // 回复文本
	TriggeredAt time.Time `json:"triggered_at"` // 触发时间
}

// ListAutoReplyRulesResponse 自动回复规则列表响应
type ListAutoReplyRulesResponse struct {
	Rules    []*AutoReplyRuleResponse `json:"rules"`     // 规则列表
	Total    int64                    `json:"total"`     // 总数
	Page     int                      `json:"page"`      // 当前页
	PageSize int                      `json:"page_size"` // 每页数量
}

// ListAutoReplyLogsResponse 自动回复日志列表响应
type ListAutoReplyLogsResponse struct {
	Logs     []*AutoReplyLogResponse `json:"logs"`      // 日志列表
	Total    int64                   `json:"total"`     // 总数
	Page     int                     `json:"page"`      // 当前页
	PageSize int                     `json:"page_size"` // 每页数量
}

// AutoReplyStatsResponse 自动回复统计响应
type AutoReplyStatsResponse struct {
	TotalRules       int64   `json:"total_rules"`       // 总规则数
	ActiveRules      int64   `json:"active_rules"`      // 活跃规则数
	TotalTriggers    int64   `json:"total_triggers"`    // 总触发次数
	TodayTriggers    int64   `json:"today_triggers"`    // 今日触发次数
	SuccessRate      float64 `json:"success_rate"`      // 成功率
	AverageDelay     float64 `json:"average_delay"`     // 平均延迟
	TopRules         []*AutoReplyRuleResponse `json:"top_rules"` // 热门规则
}
