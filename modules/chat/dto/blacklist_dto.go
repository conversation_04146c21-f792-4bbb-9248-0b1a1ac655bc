/**
 * blacklist_dto.go
 * 黑名单数据传输对象
 * 
 * 该文件定义了与黑名单功能相关的数据传输对象，用于API接口传输数据
 */

package dto

import "time"

// BlacklistDTO 黑名单信息DTO
type BlacklistDTO struct {
	ID           int64     `json:"id"`
	UserID       int64     `json:"user_id"`
	UserType     string    `json:"user_type"`
	BlockedID    int64     `json:"blocked_id"`
	BlockedType  string    `json:"blocked_type"`
	Reason       string    `json:"reason"`
	ExpireTime   time.Time `json:"expire_time"`
	CreatedAt    time.Time `json:"created_at"`
	
	// 被屏蔽用户的基本信息（非数据库字段）
	BlockedName  string   `json:"blocked_name,omitempty"`
	BlockedAvatar string  `json:"blocked_avatar,omitempty"`
}

// 请求和响应DTO

// AddBlacklistRequest 添加黑名单请求
type AddBlacklistRequest struct {
	BlockedID    int64     `json:"blocked_id" valid:"Required"`
	BlockedType  string    `json:"blocked_type" valid:"Required"`
	Reason       string    `json:"reason"`
	ExpireDays   int       `json:"expire_days"` // 屏蔽天数，0表示永久
}

// AddToBlacklistDTO 添加用户到黑名单DTO
type AddToBlacklistDTO struct {
	TargetUserID   int64  `json:"target_user_id" valid:"Required"`
	TargetUserType string `json:"target_user_type" valid:"Required"`
	Reason         string `json:"reason"`
	Duration       int    `json:"duration"` // 屏蔽时长（天），0表示永久
}

// BlacklistListResponse 黑名单列表响应
type BlacklistListResponse struct {
	Total int64          `json:"total"`
	List  []BlacklistDTO `json:"list"`
}

// IsBlockedResponse 检查黑名单状态响应
type IsBlockedResponse struct {
	IsBlocked bool `json:"is_blocked"` // 是否在黑名单中
}
