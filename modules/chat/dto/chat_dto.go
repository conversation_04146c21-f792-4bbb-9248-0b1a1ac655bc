/**
 * chat_dto.go
 * 聊天数据传输对象
 *
 * 该文件定义了聊天模块的数据传输对象，用于在不同层之间传递聊天相关数据
 */

package dto

import (
	"strconv"
	"time"
)

// MessageDTO 消息数据传输对象
type MessageDTO struct {
	ID         int64     `json:"id"`                  // 消息ID
	SessionID  int64     `json:"session_id"`          // 会话ID
	SenderID   int64     `json:"sender_id"`           // 发送者ID
	SenderType string    `json:"sender_type"`         // 发送者类型（user/merchant/system）
	Content    string    `json:"content"`             // 消息内容
	Type       string    `json:"type"`                // 消息类型（text/image/file/voice）
	ResourceID string    `json:"resource_id"`         // 资源ID（图片、文件、语音等）
	FileName   string    `json:"file_name,omitempty"` // 文件名称
	FileSize   int64     `json:"file_size,omitempty"` // 文件大小（字节）
	FileType   string    `json:"file_type,omitempty"` // 文件MIME类型
	FileExt    string    `json:"file_ext,omitempty"`  // 文件扩展名
	Status     int       `json:"status"`              // 状态（0:未读, 1:已读）
	CreatedAt  time.Time `json:"created_at"`          // 创建时间

	// 扩展字段（可能从用户或商家表中获取）
	SenderName   string `json:"sender_name,omitempty"`   // 发送者名称
	SenderAvatar string `json:"sender_avatar,omitempty"` // 发送者头像
}

// SessionDTO 会话数据传输对象
type SessionDTO struct {
	ID            int64     `json:"id"`                 // 会话ID
	Type          string    `json:"type"`               // 会话类型
	CreatorID     int64     `json:"creator_id"`         // 创建者ID
	CreatorType   string    `json:"creator_type"`       // 创建者类型
	ReceiverID    int64     `json:"receiver_id"`        // 接收者ID
	ReceiverType  string    `json:"receiver_type"`      // 接收者类型
	GroupID       int64     `json:"group_id,omitempty"` // 群ID（当会话类型为群聊时）
	LastMessageID int64     `json:"last_message_id"`    // 最后一条消息ID
	UnreadCount   int       `json:"unread_count"`       // 未读消息计数
	Status        int       `json:"status"`             // 状态（0:正常, 1:已关闭）
	CreatedAt     time.Time `json:"created_at"`         // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`         // 更新时间

	// 扩展字段
	LastMessage  *MessageDTO `json:"last_message,omitempty"`  // 最后一条消息
	TargetName   string      `json:"target_name,omitempty"`   // 对话目标名称
	TargetAvatar string      `json:"target_avatar,omitempty"` // 对话目标头像

	// 客户在线状态信息（仅当对话目标是客户时有效）
	CustomerInfo *CustomerOnlineInfo `json:"customer_info,omitempty"` // 客户在线状态信息
}

// CustomerOnlineInfo 客户在线状态信息
type CustomerOnlineInfo struct {
	ID           int64   `json:"id"`                  // 客户ID
	Name         string  `json:"name"`                // 客户名称
	Avatar       string  `json:"avatar"`              // 客户头像
	IsOnline     bool    `json:"is_online"`           // 是否在线
	OnlineStatus string  `json:"online_status"`       // 详细在线状态（active/idle/offline）
	LastSeen     *string `json:"last_seen,omitempty"` // 最后在线时间（ISO格式）
}

// WebSocketMessageDTO WebSocket消息传输对象
type WebSocketMessageDTO struct {
	Type      string      `json:"type"`       // 消息类型 (message, notification, heartbeat)
	Event     string      `json:"event"`      // 事件类型 (new_message, message_read, session_update)
	SessionID int64       `json:"session_id"` // 会话ID
	Data      interface{} `json:"data"`       // 消息数据
	Timestamp interface{} `json:"timestamp"`  // 消息时间戳 (支持int64和string格式)
}

// GetTimestamp 获取标准化的时间戳（Unix时间戳）
func (w *WebSocketMessageDTO) GetTimestamp() int64 {
	switch t := w.Timestamp.(type) {
	case int64:
		return t
	case float64:
		return int64(t)
	case string:
		// 尝试解析ISO时间格式
		if parsedTime, err := time.Parse(time.RFC3339, t); err == nil {
			return parsedTime.Unix()
		}
		// 尝试解析Unix时间戳字符串
		if timestamp, err := strconv.ParseInt(t, 10, 64); err == nil {
			return timestamp
		}
		// 解析失败，返回当前时间
		return time.Now().Unix()
	default:
		// 未知格式，返回当前时间
		return time.Now().Unix()
	}
}

// 请求参数DTO

// CreateSessionRequest 创建会话请求
type CreateSessionRequest struct {
	ReceiverID   int64  `json:"receiver_id" valid:"Required"`   // 接收者ID
	ReceiverType string `json:"receiver_type" valid:"Required"` // 接收者类型
}

// SendTextMessageRequest 发送文本消息请求
type SendTextMessageRequest struct {
	Content string `json:"content" valid:"Required"` // 消息内容
}

// SendMediaMessageRequest 发送媒体消息请求
type SendMediaMessageRequest struct {
	Type string `json:"type" valid:"Required"` // 媒体类型 (image, file, voice)
	// 文件会通过multipart/form-data上传，不在此处定义
}

// SendMediaURLRequest 发送媒体URL消息请求
type SendMediaURLRequest struct {
	Content     string `json:"content" valid:"Required"`      // 媒体文件URL
	MessageType string `json:"message_type" valid:"Required"` // 媒体类型 (image, file, voice, video)
	FileName    string `json:"file_name,omitempty"`           // 文件名称
	FileSize    int64  `json:"file_size,omitempty"`           // 文件大小（字节）
	FileType    string `json:"file_type,omitempty"`           // 文件MIME类型
	FileExt     string `json:"file_ext,omitempty"`            // 文件扩展名
}

// SendMessageRequest 统一消息发送请求
type SendMessageRequest struct {
	SessionID int64  `json:"session_id" valid:"Required"` // 会话ID
	Type      string `json:"type" valid:"Required"`       // 消息类型 (text, image, file, voice)
	Content   string `json:"content"`                     // 文本内容 (当type=text时使用)
	// 媒体文件通过multipart/form-data上传，不在此处定义
}

// UpdateSessionRequest 更新会话请求
type UpdateSessionRequest struct {
	Status int `json:"status" valid:"Range(0|1)"` // 状态（0: 正常, 1: 已关闭）
}
