/**
 * friend_dto.go
 * 好友关系数据传输对象
 * 
 * 该文件定义了与好友功能相关的数据传输对象，用于API接口传输数据
 */

package dto

import "time"

// 好友相关DTO

// FriendDTO 好友信息DTO
type FriendDTO struct {
	ID          int64     `json:"id"`
	UserID      int64     `json:"user_id"`
	UserType    string    `json:"user_type"`
	FriendID    int64     `json:"friend_id"`
	FriendType  string    `json:"friend_type"`
	Remark      string    `json:"remark"`
	Status      int       `json:"status"`      // 0正常，1特别关注，2不看他
	SessionID   int64     `json:"session_id"`  // 关联的会话ID
	CreatedAt   time.Time `json:"created_at"`
	
	// 好友的基本信息（非数据库字段）
	FriendName  string    `json:"friend_name,omitempty"`
	FriendAvatar string   `json:"friend_avatar,omitempty"`
}

// FriendRequestDTO 好友请求DTO
type FriendRequestDTO struct {
	ID           int64     `json:"id"`
	SenderID     int64     `json:"sender_id"`
	SenderType   string    `json:"sender_type"`
	ReceiverID   int64     `json:"receiver_id"`
	ReceiverType string    `json:"receiver_type"`
	Message      string    `json:"message"`
	Status       int       `json:"status"`     // 0等待验证，1已接受，2已拒绝
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	
	// 发送者的基本信息（非数据库字段）
	SenderName   string    `json:"sender_name,omitempty"`
	SenderAvatar string    `json:"sender_avatar,omitempty"`
}

// 请求和响应DTO

// AddFriendRequest 添加好友请求
type AddFriendRequest struct {
	FriendID    int64  `json:"friend_id" valid:"Required"`
	FriendType  string `json:"friend_type" valid:"Required"`
	Message     string `json:"message"`
}

// SendFriendRequestDTO 是AddFriendRequest的别名，用于控制器
type SendFriendRequestDTO struct {
	FriendID    int64  `json:"friend_id" valid:"Required"`
	FriendType  string `json:"friend_type" valid:"Required"`
	Message     string `json:"message"`
}

// UpdateFriendRequest 更新好友信息请求
type UpdateFriendRequest struct {
	Remark string `json:"remark"`
	Status int    `json:"status"` // 0正常，1特别关注，2不看他
}

// UpdateFriendDTO 是UpdateFriendRequest的别名，用于控制器
type UpdateFriendDTO struct {
	Remark string `json:"remark"`
	Status int    `json:"status"` // 0正常，1特别关注，2不看他
}

// HandleFriendRequestRequest 处理好友请求的请求
type HandleFriendRequestRequest struct {
	Action string `json:"action" valid:"Required"` // "reject"拒绝，"accept"接受
	Reason string `json:"reason"`                   // 拒绝原因
}

// HandleFriendRequestDTO 是HandleFriendRequestRequest的别名，用于控制器
type HandleFriendRequestDTO struct {
	Action string `json:"action" valid:"Required"` // "reject"拒绝，"accept"接受
	Reason string `json:"reason"`                   // 拒绝原因
}

// FriendListResponse 好友列表响应
type FriendListResponse struct {
	Total int64       `json:"total"`
	List  []FriendDTO `json:"list"`
}

// FriendRequestListResponse 好友请求列表响应
type FriendRequestListResponse struct {
	Total int64             `json:"total"`
	List  []FriendRequestDTO `json:"list"`
}

// UserSearchDTO 用户搜索结果DTO
type UserSearchDTO struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Mobile   string `json:"mobile"`
	UserType string `json:"user_type"` // "user" 或 "merchant"
	IsFriend bool   `json:"is_friend"` // 是否已经是好友
}

// UserSearchRequest 用户搜索请求
type UserSearchRequest struct {
	Keyword  string `json:"keyword" valid:"Required"` // 搜索关键词（用户名或手机号）
	Page     int    `json:"page"`                    // 页码，默认1
	PageSize int    `json:"page_size"`               // 每页数量，默认20
}

// UserSearchResponse 用户搜索响应
type UserSearchResponse struct {
	Total int64           `json:"total"`
	List  []UserSearchDTO `json:"list"`
}
