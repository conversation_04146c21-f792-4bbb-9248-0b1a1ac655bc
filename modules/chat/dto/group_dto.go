/**
 * group_dto.go
 * 群聊数据传输对象
 * 
 * 该文件定义了与群聊功能相关的数据传输对象，用于API接口传输数据
 */

package dto

import "time"

// 群聊相关DTO

// GroupDTO 群聊信息DTO
type GroupDTO struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Avatar      string    `json:"avatar"`
	CreatorID   int64     `json:"creator_id"`
	CreatorType string    `json:"creator_type"`
	MemberCount int       `json:"member_count"`
	Status      int       `json:"status"`
	MaxMembers  int       `json:"max_members"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// GroupMemberDTO 群成员DTO
type GroupMemberDTO struct {
	ID         int64     `json:"id"`
	GroupID    int64     `json:"group_id"`
	UserID     int64     `json:"user_id"`
	UserType   string    `json:"user_type"`
	Nickname   string    `json:"nickname,omitempty"`
	UserName   string    `json:"user_name,omitempty"` // 用户昵称/姓名（非群内昵称）
	Avatar     string    `json:"avatar,omitempty"`    // 用户头像
	Role       int       `json:"role"`                // 0普通成员，1管理员，2群主
	Status     int       `json:"status"`              // 0正常，1禁言
	JoinedAt   time.Time `json:"joined_at"`
	LastReadID int64     `json:"last_read_id"`
}

// 请求和响应DTO

// CreateGroupRequest 创建群聊请求
type CreateGroupRequest struct {
	Name        string  `json:"name" valid:"Required"`
	Description string  `json:"description"`
	Avatar      string  `json:"avatar"`
	InitialMembers []int64 `json:"initial_members"` // 初始成员ID列表（不包括创建者）
	MaxMembers  int     `json:"max_members"`
}

// UpdateGroupRequest 更新群聊请求
type UpdateGroupRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Avatar      string `json:"avatar"`
	MaxMembers  int    `json:"max_members"`
}

// AddGroupMemberRequest 添加群成员请求
type AddGroupMemberRequest struct {
	UserIDs  []int64 `json:"user_ids" valid:"Required"`
	UserType string  `json:"user_type" valid:"Required"`
}

// UpdateGroupMemberRequest 更新群成员请求
type UpdateGroupMemberRequest struct {
	Nickname string `json:"nickname"`
	Role     int    `json:"role"` // 仅群主可设置
	Status   int    `json:"status"` // 管理员及以上可设置
}

// GroupListResponse 群聊列表响应
type GroupListResponse struct {
	Total int64      `json:"total"`
	List  []GroupDTO `json:"list"`
}

// GroupMemberListResponse 群成员列表响应
type GroupMemberListResponse struct {
	Total int64           `json:"total"`
	List  []GroupMemberDTO `json:"list"`
}

// JoinGroupRequest 申请入群请求
type JoinGroupRequest struct {
	Message string `json:"message"` // 入群申请消息
}

// HandleJoinGroupRequest 处理入群申请请求
type HandleJoinGroupRequest struct {
	Action int    `json:"action" valid:"Required"` // 0拒绝，1接受
	Reason string `json:"reason"`                  // 拒绝原因
}
