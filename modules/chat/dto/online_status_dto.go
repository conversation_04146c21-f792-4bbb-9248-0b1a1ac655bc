/**
 * online_status_dto.go
 * 在线状态相关的数据传输对象
 *
 * 该文件定义了客户在线状态推送消息的格式和相关数据结构
 */

package dto

import "time"

// CustomerOnlineNotificationData 客户上线通知数据
type CustomerOnlineNotificationData struct {
	CustomerID     int64   `json:"customer_id"`               // 客户ID
	CustomerName   string  `json:"customer_name"`             // 客户名称
	CustomerAvatar string  `json:"customer_avatar,omitempty"` // 客户头像
	SessionIDs     []int64 `json:"session_ids"`               // 相关会话ID列表
	OnlineStatus   string  `json:"online_status"`             // 在线状态（active/idle）
	Timestamp      string  `json:"timestamp"`                 // 时间戳（ISO格式）
}

// CustomerOfflineNotificationData 客户下线通知数据
type CustomerOfflineNotificationData struct {
	CustomerID   int64   `json:"customer_id"`     // 客户ID
	CustomerName string  `json:"customer_name"`   // 客户名称
	SessionIDs   []int64 `json:"session_ids"`     // 相关会话ID列表
	LastSeen     string  `json:"last_seen"`       // 最后在线时间（ISO格式）
	Timestamp    string  `json:"timestamp"`       // 时间戳（ISO格式）
}

// CustomerStatusChangeNotificationData 客户状态变更通知数据
type CustomerStatusChangeNotificationData struct {
	CustomerID int64   `json:"customer_id"` // 客户ID
	OldStatus  string  `json:"old_status"`  // 旧状态
	NewStatus  string  `json:"new_status"`  // 新状态
	SessionIDs []int64 `json:"session_ids"` // 相关会话ID列表
	Timestamp  string  `json:"timestamp"`   // 时间戳（ISO格式）
}

// OnlineStatusNotificationBuilder 在线状态通知构建器
type OnlineStatusNotificationBuilder struct{}

// NewOnlineStatusNotificationBuilder 创建在线状态通知构建器
func NewOnlineStatusNotificationBuilder() *OnlineStatusNotificationBuilder {
	return &OnlineStatusNotificationBuilder{}
}

// BuildCustomerOnlineNotification 构建客户上线通知
func (b *OnlineStatusNotificationBuilder) BuildCustomerOnlineNotification(
	customerID int64,
	customerName string,
	customerAvatar string,
	sessionIDs []int64,
	onlineStatus string,
) *WebSocketMessageDTO {
	return &WebSocketMessageDTO{
		Type:      "notification",
		Event:     "customer_online",
		Timestamp: time.Now().Unix(),
		Data: &CustomerOnlineNotificationData{
			CustomerID:     customerID,
			CustomerName:   customerName,
			CustomerAvatar: customerAvatar,
			SessionIDs:     sessionIDs,
			OnlineStatus:   onlineStatus,
			Timestamp:      time.Now().Format(time.RFC3339),
		},
	}
}

// BuildCustomerOfflineNotification 构建客户下线通知
func (b *OnlineStatusNotificationBuilder) BuildCustomerOfflineNotification(
	customerID int64,
	customerName string,
	sessionIDs []int64,
	lastSeen time.Time,
) *WebSocketMessageDTO {
	return &WebSocketMessageDTO{
		Type:      "notification",
		Event:     "customer_offline",
		Timestamp: time.Now().Unix(),
		Data: &CustomerOfflineNotificationData{
			CustomerID:   customerID,
			CustomerName: customerName,
			SessionIDs:   sessionIDs,
			LastSeen:     lastSeen.Format(time.RFC3339),
			Timestamp:    time.Now().Format(time.RFC3339),
		},
	}
}

// BuildCustomerStatusChangeNotification 构建客户状态变更通知
func (b *OnlineStatusNotificationBuilder) BuildCustomerStatusChangeNotification(
	customerID int64,
	oldStatus string,
	newStatus string,
	sessionIDs []int64,
) *WebSocketMessageDTO {
	return &WebSocketMessageDTO{
		Type:      "notification",
		Event:     "customer_status_change",
		Timestamp: time.Now().Unix(),
		Data: &CustomerStatusChangeNotificationData{
			CustomerID: customerID,
			OldStatus:  oldStatus,
			NewStatus:  newStatus,
			SessionIDs: sessionIDs,
			Timestamp:  time.Now().Format(time.RFC3339),
		},
	}
}

// OnlineStatusEventType 在线状态事件类型
type OnlineStatusEventType string

const (
	EventCustomerOnline       OnlineStatusEventType = "customer_online"        // 客户上线
	EventCustomerOffline      OnlineStatusEventType = "customer_offline"       // 客户下线
	EventCustomerStatusChange OnlineStatusEventType = "customer_status_change" // 客户状态变更
)

// OnlineStatusLevel 在线状态级别
type OnlineStatusLevel string

const (
	StatusLevelActive  OnlineStatusLevel = "active"  // 活跃
	StatusLevelIdle    OnlineStatusLevel = "idle"    // 空闲
	StatusLevelOffline OnlineStatusLevel = "offline" // 离线
)

// IsValidOnlineStatus 检查是否为有效的在线状态
func IsValidOnlineStatus(status string) bool {
	switch OnlineStatusLevel(status) {
	case StatusLevelActive, StatusLevelIdle, StatusLevelOffline:
		return true
	default:
		return false
	}
}

// GetOnlineStatusDescription 获取在线状态描述
func GetOnlineStatusDescription(status string) string {
	switch OnlineStatusLevel(status) {
	case StatusLevelActive:
		return "活跃在线"
	case StatusLevelIdle:
		return "空闲在线"
	case StatusLevelOffline:
		return "离线"
	default:
		return "未知状态"
	}
}

// OnlineStatusConfig 在线状态配置
type OnlineStatusConfig struct {
	ActiveTimeout  int `json:"active_timeout"`  // 活跃状态超时时间（分钟）
	IdleTimeout    int `json:"idle_timeout"`    // 空闲状态超时时间（分钟）
	OfflineTimeout int `json:"offline_timeout"` // 离线清理超时时间（分钟）
}

// DefaultOnlineStatusConfig 默认在线状态配置
func DefaultOnlineStatusConfig() *OnlineStatusConfig {
	return &OnlineStatusConfig{
		ActiveTimeout:  5,  // 5分钟
		IdleTimeout:    30, // 30分钟
		OfflineTimeout: 60, // 1小时
	}
}
