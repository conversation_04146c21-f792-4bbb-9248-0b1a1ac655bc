/**
 * template_dto.go
 * 模板相关的数据传输对象
 *
 * 该文件定义了模板管理相关的请求和响应结构体
 */

package dto

import "time"

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Title          string `json:"title" validate:"required,max=100"`           // 模板标题
	Content        string `json:"content" validate:"required,max=2000"`        // 模板内容
	Category       string `json:"category" validate:"required,max=50"`         // 模板分类
	Type           string `json:"type" validate:"required,max=20"`             // 模板类型
	ApplicableRole string `json:"applicable_role" validate:"required,max=20"`  // 适用角色
	CreatorID      int64  `json:"creator_id" validate:"required"`              // 创建者ID
	CreatorType    string `json:"creator_type" validate:"required,max=20"`     // 创建者类型
	SortOrder      int    `json:"sort_order"`                                  // 排序顺序
	Tags           string `json:"tags"`                                        // 标签
	Variables      string `json:"variables"`                                   // 变量定义
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	Title          string `json:"title" validate:"max=100"`          // 模板标题
	Content        string `json:"content" validate:"max=2000"`       // 模板内容
	Category       string `json:"category" validate:"max=50"`        // 模板分类
	Type           string `json:"type" validate:"max=20"`            // 模板类型
	ApplicableRole string `json:"applicable_role" validate:"max=20"` // 适用角色
	SortOrder      int    `json:"sort_order"`                        // 排序顺序
	Status         int    `json:"status"`                            // 状态
	Tags           string `json:"tags"`                              // 标签
	Variables      string `json:"variables"`                         // 变量定义
}

// ListTemplatesRequest 获取模板列表请求
type ListTemplatesRequest struct {
	Page           int    `json:"page" validate:"min=1"`             // 页码
	PageSize       int    `json:"page_size" validate:"min=1,max=100"` // 每页数量
	Category       string `json:"category"`                          // 分类筛选
	Type           string `json:"type"`                              // 类型筛选
	ApplicableRole string `json:"applicable_role"`                   // 角色筛选
	Status         int    `json:"status"`                            // 状态筛选
	Keyword        string `json:"keyword"`                           // 关键词搜索
	SortBy         string `json:"sort_by"`                           // 排序字段
	SortOrder      string `json:"sort_order"`                        // 排序方向
}

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name        string `json:"name" validate:"required,max=50"`    // 分类名称
	Description string `json:"description" validate:"max=255"`     // 分类描述
	Icon        string `json:"icon" validate:"max=100"`            // 分类图标
	SortOrder   int    `json:"sort_order"`                         // 排序顺序
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name        string `json:"name" validate:"max=50"`     // 分类名称
	Description string `json:"description" validate:"max=255"` // 分类描述
	Icon        string `json:"icon" validate:"max=100"`    // 分类图标
	SortOrder   int    `json:"sort_order"`                 // 排序顺序
	Status      int    `json:"status"`                     // 状态
}

// TemplateResponse 模板响应
type TemplateResponse struct {
	ID             int64     `json:"id"`              // 模板ID
	Title          string    `json:"title"`           // 模板标题
	Content        string    `json:"content"`         // 模板内容
	Category       string    `json:"category"`        // 模板分类
	CategoryName   string    `json:"category_name"`   // 分类名称
	Type           string    `json:"type"`            // 模板类型
	ApplicableRole string    `json:"applicable_role"` // 适用角色
	CreatorID      int64     `json:"creator_id"`      // 创建者ID
	CreatorType    string    `json:"creator_type"`    // 创建者类型
	CreatorName    string    `json:"creator_name"`    // 创建者名称
	SortOrder      int       `json:"sort_order"`      // 排序顺序
	UseCount       int64     `json:"use_count"`       // 使用次数
	Status         int       `json:"status"`          // 状态
	Tags           []string  `json:"tags"`            // 标签
	Variables      map[string]interface{} `json:"variables"` // 变量定义
	CreatedAt      time.Time `json:"created_at"`      // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`      // 更新时间
}

// CategoryResponse 分类响应
type CategoryResponse struct {
	ID          int64     `json:"id"`          // 分类ID
	Name        string    `json:"name"`        // 分类名称
	Description string    `json:"description"` // 分类描述
	Icon        string    `json:"icon"`        // 分类图标
	SortOrder   int       `json:"sort_order"`  // 排序顺序
	Status      int       `json:"status"`      // 状态
	TemplateCount int     `json:"template_count"` // 模板数量
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
}

// ListTemplatesResponse 模板列表响应
type ListTemplatesResponse struct {
	Templates  []*TemplateResponse  `json:"templates"`  // 模板列表
	Categories []*CategoryResponse  `json:"categories"` // 分类列表
	Total      int64               `json:"total"`      // 总数
	Page       int                 `json:"page"`       // 当前页
	PageSize   int                 `json:"page_size"`  // 每页数量
}

// UseTemplateRequest 使用模板请求
type UseTemplateRequest struct {
	TemplateID int64                  `json:"template_id" validate:"required"` // 模板ID
	SessionID  int64                  `json:"session_id" validate:"required"`  // 会话ID
	Variables  map[string]interface{} `json:"variables"`                       // 变量值
}

// ProcessTemplateResponse 处理模板响应
type ProcessTemplateResponse struct {
	Content   string `json:"content"`    // 处理后的内容
	Variables map[string]interface{} `json:"variables"` // 使用的变量
}
