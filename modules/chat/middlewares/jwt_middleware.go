/**
 * jwt_middleware.go
 * JWT认证中间件
 *
 * 该文件实现了聊天模块的JWT认证中间件，用于验证API请求的认证状态
 */

package middlewares

import (
	"net/http"
	"strings"

	"github.com/beego/beego/v2/server/web/context"

	"o_mall_backend/utils"
)

// JWTMiddleware JWT认证中间件
func JWTMiddleware() func(ctx *context.Context) {
	return func(ctx *context.Context) {
		// 跳过OPTIONS请求（CORS预检请求）
		if ctx.Input.Method() == "OPTIONS" {
			return
		}
		
		// 检查是否是WebSocket握手请求，WebSocket连接会在控制器中单独验证
		if ctx.Input.URL() == "/api/v1/chat/ws" && ctx.Input.Method() == "GET" {
			return
		}

		// 从请求中提取令牌
		authHeader := ctx.Request.Header.Get("Authorization")
		token := ""
		
		// 从授权头中提取令牌
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			token = strings.TrimPrefix(authHeader, "Bearer ")
		}
		
		// 如果授权头中没有，尝试从查询参数中获取
		if token == "" {
			token = ctx.Input.Query("token")
		}
		
		if token == "" {
			// 未找到令牌
			ctx.Output.SetStatus(http.StatusUnauthorized)
			ctx.Output.JSON(map[string]interface{}{
				"code":    http.StatusUnauthorized,
				"message": "未授权",
				"detail":  "未提供令牌",
			}, false, false)
			return
		}
		
		// 验证JWT令牌
		claims, err := utils.ParseToken(token)
		if err != nil {
			// 令牌无效，返回未授权状态
			ctx.Output.SetStatus(http.StatusUnauthorized)
			ctx.Output.JSON(map[string]interface{}{
				"code":    http.StatusUnauthorized,
				"message": "未授权",
				"detail":  err.Error(),
			}, false, false)
			return
		}

		// 将用户信息存储到上下文中，供后续控制器使用
		ctx.Input.SetData("user_id", claims.UserID)
		ctx.Input.SetData("user_name", claims.Username)
		ctx.Input.SetData("user_role", claims.Role)
	}
}
