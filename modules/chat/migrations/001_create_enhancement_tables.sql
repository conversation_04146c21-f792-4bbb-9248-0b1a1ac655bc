-- 聊天模块增强功能数据库迁移脚本
-- 创建消息模板、自动回复和转接相关表

-- 1. 消息模板表
CREATE TABLE IF NOT EXISTS `chat_template` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    `title` VARCHAR(100) NOT NULL COMMENT '模板标题',
    `content` TEXT NOT NULL COMMENT '模板内容',
    `category` VARCHAR(50) NOT NULL COMMENT '模板分类',
    `type` VARCHAR(20) NOT NULL COMMENT '模板类型',
    `applicable_role` VARCHAR(20) NOT NULL COMMENT '适用角色',
    `creator_id` BIGINT NOT NULL COMMENT '创建者ID',
    `creator_type` VARCHAR(20) NOT NULL COMMENT '创建者类型',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `use_count` BIGINT DEFAULT 0 COMMENT '使用次数',
    `status` INT DEFAULT 1 COMMENT '状态',
    `tags` VARCHAR(255) COMMENT '标签',
    `variables` TEXT COMMENT '变量定义',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_category` (`category`),
    INDEX `idx_type` (`type`),
    INDEX `idx_applicable_role` (`applicable_role`),
    INDEX `idx_creator` (`creator_id`, `creator_type`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息模板表';

-- 2. 模板分类表
CREATE TABLE IF NOT EXISTS `chat_template_category` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    `description` VARCHAR(255) COMMENT '分类描述',
    `icon` VARCHAR(100) COMMENT '分类图标',
    `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
    `status` INT DEFAULT 1 COMMENT '状态',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_name` (`name`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天模板分类表';

-- 3. 模板使用记录表
CREATE TABLE IF NOT EXISTS `chat_template_usage` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    `template_id` BIGINT NOT NULL COMMENT '模板ID',
    `user_id` BIGINT NOT NULL COMMENT '使用者ID',
    `user_type` VARCHAR(20) NOT NULL COMMENT '使用者类型',
    `session_id` BIGINT NOT NULL COMMENT '会话ID',
    `message_id` BIGINT NOT NULL COMMENT '消息ID',
    `used_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
    INDEX `idx_template_id` (`template_id`),
    INDEX `idx_user` (`user_id`, `user_type`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_used_at` (`used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板使用记录表';

-- 4. 自动回复规则表
CREATE TABLE IF NOT EXISTS `chat_auto_reply` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    `name` VARCHAR(100) NOT NULL COMMENT '规则名称',
    `description` VARCHAR(255) COMMENT '规则描述',
    `type` VARCHAR(20) NOT NULL COMMENT '规则类型',
    `trigger_condition` VARCHAR(20) NOT NULL COMMENT '触发条件',
    `keywords` TEXT COMMENT '关键词',
    `regex_pattern` VARCHAR(500) COMMENT '正则表达式',
    `reply_content` TEXT NOT NULL COMMENT '回复内容',
    `reply_delay` INT DEFAULT 0 COMMENT '回复延迟',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `max_trigger_count` INT DEFAULT 0 COMMENT '最大触发次数',
    `trigger_count` BIGINT DEFAULT 0 COMMENT '已触发次数',
    `applicable_role` VARCHAR(20) NOT NULL COMMENT '适用角色',
    `applicable_time` VARCHAR(100) COMMENT '适用时间',
    `creator_id` BIGINT NOT NULL COMMENT '创建者ID',
    `creator_type` VARCHAR(20) NOT NULL COMMENT '创建者类型',
    `status` INT DEFAULT 1 COMMENT '状态',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_type` (`type`),
    INDEX `idx_applicable_role` (`applicable_role`),
    INDEX `idx_creator` (`creator_id`, `creator_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动回复规则表';

-- 5. 自动回复日志表
CREATE TABLE IF NOT EXISTS `chat_auto_reply_log` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    `rule_id` BIGINT NOT NULL COMMENT '规则ID',
    `session_id` BIGINT NOT NULL COMMENT '会话ID',
    `message_id` BIGINT NOT NULL COMMENT '触发消息ID',
    `reply_id` BIGINT NOT NULL COMMENT '回复消息ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `user_type` VARCHAR(20) NOT NULL COMMENT '用户类型',
    `trigger_text` VARCHAR(500) NOT NULL COMMENT '触发文本',
    `reply_text` TEXT NOT NULL COMMENT '回复文本',
    `triggered_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',
    INDEX `idx_rule_id` (`rule_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user` (`user_id`, `user_type`),
    INDEX `idx_triggered_at` (`triggered_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动回复日志表';

-- 6. 自动回复配置表
CREATE TABLE IF NOT EXISTS `chat_auto_reply_config` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `user_type` VARCHAR(20) NOT NULL COMMENT '用户类型',
    `enabled` INT DEFAULT 1 COMMENT '是否启用',
    `working_hours` VARCHAR(200) COMMENT '工作时间',
    `offline_message` TEXT COMMENT '离线消息',
    `welcome_message` TEXT COMMENT '欢迎消息',
    `no_response_delay` INT DEFAULT 300 COMMENT '无响应延迟',
    `no_response_message` TEXT COMMENT '无响应消息',
    `max_auto_replies` INT DEFAULT 5 COMMENT '最大自动回复次数',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_user` (`user_id`, `user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动回复配置表';

-- 7. 转接记录表
CREATE TABLE IF NOT EXISTS `chat_transfer` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转接ID',
    `session_id` BIGINT NOT NULL COMMENT '会话ID',
    `from_user_id` BIGINT NOT NULL COMMENT '转出用户ID',
    `from_user_type` VARCHAR(20) NOT NULL COMMENT '转出用户类型',
    `to_user_id` BIGINT NOT NULL COMMENT '转入用户ID',
    `to_user_type` VARCHAR(20) NOT NULL COMMENT '转入用户类型',
    `type` VARCHAR(20) NOT NULL COMMENT '转接类型',
    `reason` VARCHAR(50) NOT NULL COMMENT '转接原因',
    `reason_text` VARCHAR(500) COMMENT '转接原因说明',
    `status` VARCHAR(20) NOT NULL COMMENT '转接状态',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `notes` TEXT COMMENT '转接备注',
    `accepted_at` DATETIME COMMENT '接受时间',
    `completed_at` DATETIME COMMENT '完成时间',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_from_user` (`from_user_id`, `from_user_type`),
    INDEX `idx_to_user` (`to_user_id`, `to_user_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天转接记录表';

-- 8. 转接队列表
CREATE TABLE IF NOT EXISTS `chat_transfer_queue` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '队列ID',
    `name` VARCHAR(100) NOT NULL COMMENT '队列名称',
    `description` VARCHAR(255) COMMENT '队列描述',
    `department` VARCHAR(50) COMMENT '所属部门',
    `max_capacity` INT DEFAULT 10 COMMENT '最大容量',
    `current_load` INT DEFAULT 0 COMMENT '当前负载',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `working_hours` VARCHAR(200) COMMENT '工作时间',
    `skills` TEXT COMMENT '技能标签',
    `status` INT DEFAULT 1 COMMENT '状态',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_status` (`status`),
    INDEX `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转接队列表';

-- 9. 转接客服表
CREATE TABLE IF NOT EXISTS `chat_transfer_agent` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    `queue_id` BIGINT NOT NULL COMMENT '队列ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `user_type` VARCHAR(20) NOT NULL COMMENT '用户类型',
    `max_sessions` INT DEFAULT 5 COMMENT '最大会话数',
    `current_sessions` INT DEFAULT 0 COMMENT '当前会话数',
    `skills` TEXT COMMENT '技能标签',
    `status` VARCHAR(20) NOT NULL COMMENT '状态',
    `last_active_at` DATETIME COMMENT '最后活跃时间',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_queue_id` (`queue_id`),
    INDEX `idx_user` (`user_id`, `user_type`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转接客服表';

-- 10. 转接规则表
CREATE TABLE IF NOT EXISTS `chat_transfer_rule` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
    `name` VARCHAR(100) NOT NULL COMMENT '规则名称',
    `description` VARCHAR(255) COMMENT '规则描述',
    `conditions` TEXT NOT NULL COMMENT '触发条件',
    `actions` TEXT NOT NULL COMMENT '执行动作',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `enabled` INT DEFAULT 1 COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_enabled` (`enabled`),
    INDEX `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转接规则表';

-- 插入默认的模板分类
INSERT IGNORE INTO `chat_template_category` (`name`, `description`, `icon`, `sort_order`) VALUES
('welcome', '欢迎消息', '👋', 1),
('common', '常用回复', '💬', 2),
('closing', '结束语', '👋', 3),
('product', '产品介绍', '🛍️', 4),
('service', '服务说明', '🔧', 5),
('complaint', '投诉处理', '😔', 6),
('refund', '退款相关', '💰', 7),
('shipping', '物流相关', '🚚', 8);

-- 插入默认的消息模板
INSERT IGNORE INTO `chat_template` (`title`, `content`, `category`, `type`, `applicable_role`, `creator_id`, `creator_type`, `sort_order`) VALUES
('通用欢迎消息', '您好！欢迎咨询，我是客服{{agent_name}}，很高兴为您服务！', 'welcome', 'welcome', 'all', 0, 'system', 1),
('价格咨询回复', '感谢您对我们产品的关注！关于价格问题，我来为您详细介绍...', 'common', 'common', 'merchant', 0, 'system', 2),
('订单查询回复', '请提供您的订单号，我来帮您查询订单状态。', 'common', 'common', 'merchant', 0, 'system', 3),
('感谢结束语', '感谢您的咨询，如果还有其他问题，请随时联系我们！', 'closing', 'closing', 'all', 0, 'system', 4);

-- 插入默认的自动回复规则
INSERT IGNORE INTO `chat_auto_reply` (`name`, `description`, `type`, `trigger_condition`, `keywords`, `reply_content`, `reply_delay`, `priority`, `applicable_role`, `creator_id`, `creator_type`) VALUES
('问候自动回复', '用户问候时的自动回复', 'keyword', 'any', '["你好", "您好", "hello", "hi", "在吗"]', '您好！我是智能客服助手，很高兴为您服务。请问有什么可以帮助您的吗？', 1, 10, 'merchant', 0, 'system'),
('价格咨询自动回复', '价格相关咨询的自动回复', 'keyword', 'any', '["价格", "多少钱", "费用", "收费", "报价"]', '感谢您的咨询！关于价格问题，我来为您详细介绍。请稍等片刻...', 2, 8, 'merchant', 0, 'system'),
('订单查询自动回复', '订单查询的自动回复', 'keyword', 'any', '["订单", "查询", "物流", "发货", "快递"]', '请提供您的订单号，我来帮您查询具体情况。', 1, 7, 'merchant', 0, 'system');
