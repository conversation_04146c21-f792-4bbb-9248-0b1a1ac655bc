/**
 * chat_auto_reply.go
 * 聊天自动回复模型
 *
 * 该文件定义了聊天自动回复规则的数据结构
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// 自动回复规则类型常量
const (
	AutoReplyTypeKeyword    = "keyword"    // 关键词匹配
	AutoReplyTypeRegex      = "regex"      // 正则表达式匹配
	AutoReplyTypeTime       = "time"       // 时间触发
	AutoReplyTypeWelcome    = "welcome"    // 欢迎消息
	AutoReplyTypeOffline    = "offline"    // 离线消息
	AutoReplyTypeNoResponse = "no_response" // 无响应触发
)

// 自动回复状态常量
const (
	AutoReplyStatusActive   = 1 // 启用
	AutoReplyStatusInactive = 0 // 禁用
)

// 触发条件常量
const (
	TriggerConditionAny = "any" // 任意条件
	TriggerConditionAll = "all" // 所有条件
)

// ChatAutoReply 自动回复规则模型
type ChatAutoReply struct {
	ID              int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 规则ID
	Name            string    `orm:"size(100);column(name)" json:"name"`                             // 规则名称
	Description     string    `orm:"size(255);null;column(description)" json:"description"`          // 规则描述
	Type            string    `orm:"size(20);column(type)" json:"type"`                              // 规则类型
	TriggerCondition string   `orm:"size(20);column(trigger_condition)" json:"trigger_condition"`   // 触发条件
	Keywords        string    `orm:"type(text);null;column(keywords)" json:"keywords"`               // 关键词（JSON格式）
	RegexPattern    string    `orm:"size(500);null;column(regex_pattern)" json:"regex_pattern"`      // 正则表达式
	ReplyContent    string    `orm:"type(text);column(reply_content)" json:"reply_content"`          // 回复内容
	ReplyDelay      int       `orm:"default(0);column(reply_delay)" json:"reply_delay"`              // 回复延迟（秒）
	Priority        int       `orm:"default(0);column(priority)" json:"priority"`                    // 优先级
	MaxTriggerCount int       `orm:"default(0);column(max_trigger_count)" json:"max_trigger_count"`  // 最大触发次数（0为无限制）
	TriggerCount    int64     `orm:"default(0);column(trigger_count)" json:"trigger_count"`          // 已触发次数
	ApplicableRole  string    `orm:"size(20);column(applicable_role)" json:"applicable_role"`        // 适用角色
	ApplicableTime  string    `orm:"size(100);null;column(applicable_time)" json:"applicable_time"`  // 适用时间（JSON格式）
	CreatorID       int64     `orm:"column(creator_id)" json:"creator_id"`                           // 创建者ID
	CreatorType     string    `orm:"size(20);column(creator_type)" json:"creator_type"`              // 创建者类型
	Status          int       `orm:"default(1);column(status)" json:"status"`                        // 状态
	CreatedAt       time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt       time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (r *ChatAutoReply) TableName() string {
	return "chat_auto_reply"
}

// ChatAutoReplyLog 自动回复日志模型
type ChatAutoReplyLog struct {
	ID         int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 日志ID
	RuleID     int64     `orm:"column(rule_id)" json:"rule_id"`                                 // 规则ID
	SessionID  int64     `orm:"column(session_id)" json:"session_id"`                           // 会话ID
	MessageID  int64     `orm:"column(message_id)" json:"message_id"`                           // 触发消息ID
	ReplyID    int64     `orm:"column(reply_id)" json:"reply_id"`                               // 回复消息ID
	UserID     int64     `orm:"column(user_id)" json:"user_id"`                                 // 用户ID
	UserType   string    `orm:"size(20);column(user_type)" json:"user_type"`                    // 用户类型
	TriggerText string   `orm:"size(500);column(trigger_text)" json:"trigger_text"`             // 触发文本
	ReplyText   string   `orm:"type(text);column(reply_text)" json:"reply_text"`                // 回复文本
	TriggeredAt time.Time `orm:"auto_now_add;type(datetime);column(triggered_at)" json:"triggered_at"` // 触发时间
}

// TableName 设置表名
func (l *ChatAutoReplyLog) TableName() string {
	return "chat_auto_reply_log"
}

// ChatAutoReplyConfig 自动回复配置模型
type ChatAutoReplyConfig struct {
	ID                int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 配置ID
	UserID            int64     `orm:"column(user_id)" json:"user_id"`                                 // 用户ID
	UserType          string    `orm:"size(20);column(user_type)" json:"user_type"`                    // 用户类型
	Enabled           int       `orm:"default(1);column(enabled)" json:"enabled"`                      // 是否启用
	WorkingHours      string    `orm:"size(200);null;column(working_hours)" json:"working_hours"`      // 工作时间（JSON格式）
	OfflineMessage    string    `orm:"type(text);null;column(offline_message)" json:"offline_message"` // 离线消息
	WelcomeMessage    string    `orm:"type(text);null;column(welcome_message)" json:"welcome_message"` // 欢迎消息
	NoResponseDelay   int       `orm:"default(300);column(no_response_delay)" json:"no_response_delay"` // 无响应延迟（秒）
	NoResponseMessage string    `orm:"type(text);null;column(no_response_message)" json:"no_response_message"` // 无响应消息
	MaxAutoReplies    int       `orm:"default(5);column(max_auto_replies)" json:"max_auto_replies"`    // 最大自动回复次数
	CreatedAt         time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt         time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (c *ChatAutoReplyConfig) TableName() string {
	return "chat_auto_reply_config"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatAutoReply))
	orm.RegisterModel(new(ChatAutoReplyLog))
	orm.RegisterModel(new(ChatAutoReplyConfig))
}
