/**
 * chat_blacklist.go
 * 黑名单数据模型
 *
 * 该文件定义了用户黑名单的数据模型，用于管理用户间的屏蔽关系
 */

package models

import (
	"github.com/beego/beego/v2/client/orm"
	"time"
)

// ChatBlacklist 黑名单模型
type ChatBlacklist struct {
	ID           int64     `orm:"pk;auto;column(id)" json:"id"`
	UserID       int64     `orm:"column(user_id)" json:"user_id"`                // 用户ID
	UserType     string    `orm:"size(50);column(user_type)" json:"user_type"`   // 用户类型
	BlockedID    int64     `orm:"column(blocked_id)" json:"blocked_id"`          // 被屏蔽用户ID
	BlockedType  string    `orm:"size(50);column(blocked_type)" json:"blocked_type"` // 被屏蔽用户类型
	Reason       string    `orm:"size(255);null;column(reason)" json:"reason"`   // 屏蔽原因
	ExpireTime   time.Time `orm:"type(datetime);null;column(expire_time)" json:"expire_time"` // 屏蔽到期时间，null表示永久
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"`
	UpdatedAt    time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`
}

// TableName 设置表名
func (cb *ChatBlacklist) TableName() string {
	return "chat_blacklist"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatBlacklist))
}
