/**
 * chat_friend.go
 * 好友关系数据模型
 *
 * 该文件定义了用户好友关系的数据模型，用于管理用户间的好友关系
 */

package models

import (
	"github.com/beego/beego/v2/client/orm"
	"time"
)

// ChatFriend 好友关系模型
type ChatFriend struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id"`
	UserID      int64     `orm:"column(user_id)" json:"user_id"`               // 用户ID
	UserType    string    `orm:"size(50);column(user_type)" json:"user_type"`  // 用户类型
	FriendID    int64     `orm:"column(friend_id)" json:"friend_id"`           // 好友ID
	FriendType  string    `orm:"size(50);column(friend_type)" json:"friend_type"` // 好友类型
	Remark      string    `orm:"size(100);null;column(remark)" json:"remark"`  // 好友备注
	Status      int       `orm:"column(status);default(0)" json:"status"`      // 状态：0正常，1特别关注，2不看他
	SessionID   int64     `orm:"column(session_id)" json:"session_id"`         // 关联的会话ID
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"`
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`
}

// ChatFriendRequest 好友请求模型
type ChatFriendRequest struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id"`
	SenderID    int64     `orm:"column(sender_id)" json:"sender_id"`           // 发送者ID
	SenderType  string    `orm:"size(50);column(sender_type)" json:"sender_type"` // 发送者类型
	ReceiverID  int64     `orm:"column(receiver_id)" json:"receiver_id"`       // 接收者ID
	ReceiverType string   `orm:"size(50);column(receiver_type)" json:"receiver_type"` // 接收者类型
	Message     string    `orm:"size(255);null;column(message)" json:"message"` // 验证消息
	Status      int       `orm:"column(status);default(0)" json:"status"`       // 状态：0等待验证，1已接受，2已拒绝
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"`
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`
}

// TableName 设置表名
func (cf *ChatFriend) TableName() string {
	return "chat_friend"
}

// TableName 设置表名
func (cfr *ChatFriendRequest) TableName() string {
	return "chat_friend_request"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatFriend))
	orm.RegisterModel(new(ChatFriendRequest))
}
