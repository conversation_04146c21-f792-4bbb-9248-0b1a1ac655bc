/**
 * chat_group.go
 * 群聊数据模型
 *
 * 该文件定义了群聊相关的数据模型，包括群聊信息和群成员
 */

package models

import (
	"github.com/beego/beego/v2/client/orm"
	"time"
)

// ChatGroup 群聊模型
type ChatGroup struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id"`
	Name        string    `orm:"size(100);column(name)" json:"name"`              // 群名称
	Description string    `orm:"size(500);null;column(description)" json:"description"` // 群描述
	Avatar      string    `orm:"size(255);null;column(avatar)" json:"avatar"`     // 群头像
	CreatorID   int64     `orm:"column(creator_id)" json:"creator_id"`            // 创建者ID
	CreatorType string    `orm:"size(50);column(creator_type)" json:"creator_type"` // 创建者类型
	SessionID   int64     `orm:"column(session_id)" json:"session_id"`            // 关联的会话ID
	MemberCount int       `orm:"column(member_count);default(1)" json:"member_count"` // 成员数量
	Status      int       `orm:"column(status);default(0)" json:"status"`         // 状态：0正常，1解散
	MaxMembers  int       `orm:"column(max_members);default(200)" json:"max_members"` // 最大成员数
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"`
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`
}

// ChatGroupMember 群成员模型
type ChatGroupMember struct {
	ID         int64     `orm:"pk;auto;column(id)" json:"id"`
	GroupID    int64     `orm:"column(group_id)" json:"group_id"`              // 群ID
	UserID     int64     `orm:"column(user_id)" json:"user_id"`                // 用户ID
	UserType   string    `orm:"size(50);column(user_type)" json:"user_type"`   // 用户类型
	Nickname   string    `orm:"size(100);null;column(nickname)" json:"nickname"` // 群内昵称
	Role       int       `orm:"column(role);default(0)" json:"role"`           // 角色：0普通成员，1管理员，2群主
	Status     int       `orm:"column(status);default(0)" json:"status"`       // 状态：0正常，1禁言
	JoinedAt   time.Time `orm:"auto_now_add;type(datetime);column(joined_at)" json:"joined_at"`
	LastReadID int64     `orm:"column(last_read_id);default(0)" json:"last_read_id"` // 最后阅读的消息ID
	UpdatedAt  time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`
}

// TableName 设置表名
func (cg *ChatGroup) TableName() string {
	return "chat_group"
}

// TableName 设置表名
func (cgm *ChatGroupMember) TableName() string {
	return "chat_group_member"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatGroup))
	orm.RegisterModel(new(ChatGroupMember))
}
