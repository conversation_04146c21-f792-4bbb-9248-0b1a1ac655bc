/**
 * chat_message.go
 * 聊天消息模型
 *
 * 该文件定义了聊天消息的数据结构，用于存储用户之间的聊天记录
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// 消息类型常量
const (
	MessageTypeText  = "text"  // 文本消息
	MessageTypeImage = "image" // 图片消息
	MessageTypeFile  = "file"  // 文件消息
	MessageTypeVoice = "voice" // 语音消息
	MessageTypeVideo = "video" // 视频消息
)

// ChatMessage 聊天消息模型
type ChatMessage struct {
	ID         int64     `orm:"pk;auto" json:"id"`                             // 消息ID
	SessionID  int64     `orm:"column(session_i_d);index" json:"session_id"`   // 会话ID
	SenderID   int64     `orm:"column(sender_i_d);index" json:"sender_id"`     // 发送者ID
	SenderType string    `orm:"size(20)" json:"sender_type"`                   // 发送者类型（user/merchant/system）
	Content    string    `orm:"type(text)" json:"content"`                     // 消息内容
	Type       string    `orm:"size(20);default(text)" json:"type"`            // 消息类型（text/image/file/voice）
	ResourceID string    `orm:"size(255);null" json:"resource_id"`             // 资源ID（图片、文件、语音等）
	FileName   string    `orm:"size(255);null" json:"file_name"`               // 文件名称
	FileSize   int64     `orm:"null" json:"file_size"`                         // 文件大小（字节）
	FileType   string    `orm:"size(100);null" json:"file_type"`               // 文件MIME类型
	FileExt    string    `orm:"size(20);null" json:"file_ext"`                 // 文件扩展名
	Status     int       `orm:"default(0)" json:"status"`                      // 状态（0:未读, 1:已读）
	CreatedAt  time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"` // 创建时间
	UpdatedAt  time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (m *ChatMessage) TableName() string {
	return "chat_message"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatMessage))
}
