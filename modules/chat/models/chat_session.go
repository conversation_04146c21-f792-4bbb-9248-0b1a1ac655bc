/**
 * chat_session.go
 * 聊天会话模型
 *
 * 该文件定义了聊天会话的数据结构，用于管理用户之间的聊天会话
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// 会话类型常量
const (
	SessionTypeUserToUser      = "user_to_user"      // 用户对用户
	SessionTypeUserToMerchant  = "user_to_merchant"  // 用户对商户
	SessionTypeUserToAdmin     = "user_to_admin"     // 用户对管理员
	SessionTypeAdminToMerchant = "admin_to_merchant" // 管理员对商户
	SessionTypeGroup           = "group"             // 群聊
)

// ChatSession 聊天会话模型
type ChatSession struct {
	ID            int64     `orm:"pk;auto;column(i_d)" json:"id"`                              // 会话ID
	Type          string    `orm:"size(20)" json:"type"`                                       // 会话类型
	CreatorID     int64     `orm:"index;column(creator_i_d)" json:"creator_id"`                // 创建者ID
	CreatorType   string    `orm:"size(20)" json:"creator_type"`                               // 创建者类型（user/merchant）
	ReceiverID    int64     `orm:"index;column(receiver_i_d)" json:"receiver_id"`              // 接收者ID
	ReceiverType  string    `orm:"size(20)" json:"receiver_type"`                              // 接收者类型（user/merchant）
	LastMessageID int64     `orm:"default(0);column(last_message_i_d)" json:"last_message_id"` // 最后一条消息ID
	UnreadCount   int       `orm:"default(0)" json:"unread_count"`                             // 未读消息计数
	Status        int       `orm:"default(0)" json:"status"`                                   // 状态（0:正常, 1:已关闭）
	CreatedAt     time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`              // 创建时间
	UpdatedAt     time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`                  // 更新时间
}

// TableName 设置表名
func (s *ChatSession) TableName() string {
	return "chat_session"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatSession))
}
