/**
 * chat_template.go
 * 聊天消息模板模型
 *
 * 该文件定义了聊天消息模板的数据结构，用于存储和管理常用的回复模板
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// 模板类型常量
const (
	TemplateTypeWelcome    = "welcome"    // 欢迎消息
	TemplateTypeCommon     = "common"     // 常用回复
	TemplateTypeClosing    = "closing"    // 结束语
	TemplateTypeProduct    = "product"    // 产品介绍
	TemplateTypeService    = "service"    // 服务说明
	TemplateTypeComplaint  = "complaint"  // 投诉处理
	TemplateTypeRefund     = "refund"     // 退款相关
	TemplateTypeShipping   = "shipping"   // 物流相关
)

// 模板状态常量
const (
	TemplateStatusActive   = 1 // 启用
	TemplateStatusInactive = 0 // 禁用
)

// 模板适用角色常量
const (
	TemplateRoleAll      = "all"      // 所有角色
	TemplateRoleUser     = "user"     // 用户
	TemplateRoleMerchant = "merchant" // 商家
	TemplateRoleAdmin    = "admin"    // 管理员
)

// ChatTemplate 聊天消息模板模型
type ChatTemplate struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 模板ID
	Title       string    `orm:"size(100);column(title)" json:"title"`                           // 模板标题
	Content     string    `orm:"type(text);column(content)" json:"content"`                      // 模板内容
	Category    string    `orm:"size(50);column(category)" json:"category"`                      // 模板分类
	Type        string    `orm:"size(20);column(type)" json:"type"`                              // 模板类型
	ApplicableRole string `orm:"size(20);column(applicable_role)" json:"applicable_role"`       // 适用角色
	CreatorID   int64     `orm:"column(creator_id)" json:"creator_id"`                           // 创建者ID
	CreatorType string    `orm:"size(20);column(creator_type)" json:"creator_type"`              // 创建者类型
	SortOrder   int       `orm:"default(0);column(sort_order)" json:"sort_order"`                // 排序顺序
	UseCount    int64     `orm:"default(0);column(use_count)" json:"use_count"`                  // 使用次数
	Status      int       `orm:"default(1);column(status)" json:"status"`                        // 状态
	Tags        string    `orm:"size(255);null;column(tags)" json:"tags"`                        // 标签（JSON格式）
	Variables   string    `orm:"type(text);null;column(variables)" json:"variables"`             // 变量定义（JSON格式）
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (t *ChatTemplate) TableName() string {
	return "chat_template"
}

// ChatTemplateCategory 模板分类模型
type ChatTemplateCategory struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 分类ID
	Name        string    `orm:"size(50);column(name)" json:"name"`                              // 分类名称
	Description string    `orm:"size(255);null;column(description)" json:"description"`          // 分类描述
	Icon        string    `orm:"size(100);null;column(icon)" json:"icon"`                        // 分类图标
	SortOrder   int       `orm:"default(0);column(sort_order)" json:"sort_order"`                // 排序顺序
	Status      int       `orm:"default(1);column(status)" json:"status"`                        // 状态
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (c *ChatTemplateCategory) TableName() string {
	return "chat_template_category"
}

// ChatTemplateUsage 模板使用记录模型
type ChatTemplateUsage struct {
	ID         int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 记录ID
	TemplateID int64     `orm:"column(template_id)" json:"template_id"`                         // 模板ID
	UserID     int64     `orm:"column(user_id)" json:"user_id"`                                 // 使用者ID
	UserType   string    `orm:"size(20);column(user_type)" json:"user_type"`                    // 使用者类型
	SessionID  int64     `orm:"column(session_id)" json:"session_id"`                           // 会话ID
	MessageID  int64     `orm:"column(message_id)" json:"message_id"`                           // 消息ID
	UsedAt     time.Time `orm:"auto_now_add;type(datetime);column(used_at)" json:"used_at"`     // 使用时间
}

// TableName 设置表名
func (u *ChatTemplateUsage) TableName() string {
	return "chat_template_usage"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatTemplate))
	orm.RegisterModel(new(ChatTemplateCategory))
	orm.RegisterModel(new(ChatTemplateUsage))
}
