/**
 * chat_transfer.go
 * 聊天转接模型
 *
 * 该文件定义了聊天会话转接的数据结构
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// 转接状态常量
const (
	TransferStatusPending   = "pending"   // 待处理
	TransferStatusAccepted  = "accepted"  // 已接受
	TransferStatusRejected  = "rejected"  // 已拒绝
	TransferStatusCompleted = "completed" // 已完成
	TransferStatusCancelled = "cancelled" // 已取消
)

// 转接类型常量
const (
	TransferTypeManual = "manual" // 手动转接
	TransferTypeAuto   = "auto"   // 自动转接
	TransferTypeQueue  = "queue"  // 队列分配
)

// 转接原因常量
const (
	TransferReasonSpecialist = "specialist" // 需要专业人员
	TransferReasonBusy       = "busy"       // 当前客服忙碌
	TransferReasonOffline    = "offline"    // 当前客服离线
	TransferReasonEscalate   = "escalate"   // 问题升级
	TransferReasonLanguage   = "language"   // 语言问题
	TransferReasonOther      = "other"      // 其他原因
)

// ChatTransfer 聊天转接模型
type ChatTransfer struct {
	ID              int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 转接ID
	SessionID       int64     `orm:"column(session_id)" json:"session_id"`                           // 会话ID
	FromUserID      int64     `orm:"column(from_user_id)" json:"from_user_id"`                       // 转出用户ID
	FromUserType    string    `orm:"size(20);column(from_user_type)" json:"from_user_type"`          // 转出用户类型
	ToUserID        int64     `orm:"column(to_user_id)" json:"to_user_id"`                           // 转入用户ID
	ToUserType      string    `orm:"size(20);column(to_user_type)" json:"to_user_type"`              // 转入用户类型
	Type            string    `orm:"size(20);column(type)" json:"type"`                              // 转接类型
	Reason          string    `orm:"size(50);column(reason)" json:"reason"`                          // 转接原因
	ReasonText      string    `orm:"size(500);null;column(reason_text)" json:"reason_text"`          // 转接原因说明
	Status          string    `orm:"size(20);column(status)" json:"status"`                          // 转接状态
	Priority        int       `orm:"default(0);column(priority)" json:"priority"`                    // 优先级
	Notes           string    `orm:"type(text);null;column(notes)" json:"notes"`                     // 转接备注
	AcceptedAt      time.Time `orm:"null;type(datetime);column(accepted_at)" json:"accepted_at"`     // 接受时间
	CompletedAt     time.Time `orm:"null;type(datetime);column(completed_at)" json:"completed_at"`   // 完成时间
	CreatedAt       time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt       time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (t *ChatTransfer) TableName() string {
	return "chat_transfer"
}

// ChatTransferQueue 转接队列模型
type ChatTransferQueue struct {
	ID           int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 队列ID
	Name         string    `orm:"size(100);column(name)" json:"name"`                             // 队列名称
	Description  string    `orm:"size(255);null;column(description)" json:"description"`          // 队列描述
	Department   string    `orm:"size(50);null;column(department)" json:"department"`             // 所属部门
	MaxCapacity  int       `orm:"default(10);column(max_capacity)" json:"max_capacity"`           // 最大容量
	CurrentLoad  int       `orm:"default(0);column(current_load)" json:"current_load"`            // 当前负载
	Priority     int       `orm:"default(0);column(priority)" json:"priority"`                    // 优先级
	WorkingHours string    `orm:"size(200);null;column(working_hours)" json:"working_hours"`      // 工作时间
	Skills       string    `orm:"type(text);null;column(skills)" json:"skills"`                   // 技能标签（JSON格式）
	Status       int       `orm:"default(1);column(status)" json:"status"`                        // 状态
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt    time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (q *ChatTransferQueue) TableName() string {
	return "chat_transfer_queue"
}

// ChatTransferAgent 转接客服模型
type ChatTransferAgent struct {
	ID           int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 记录ID
	QueueID      int64     `orm:"column(queue_id)" json:"queue_id"`                               // 队列ID
	UserID       int64     `orm:"column(user_id)" json:"user_id"`                                 // 用户ID
	UserType     string    `orm:"size(20);column(user_type)" json:"user_type"`                    // 用户类型
	MaxSessions  int       `orm:"default(5);column(max_sessions)" json:"max_sessions"`            // 最大会话数
	CurrentSessions int    `orm:"default(0);column(current_sessions)" json:"current_sessions"`    // 当前会话数
	Skills       string    `orm:"type(text);null;column(skills)" json:"skills"`                   // 技能标签（JSON格式）
	Status       string    `orm:"size(20);column(status)" json:"status"`                          // 状态（online, offline, busy）
	LastActiveAt time.Time `orm:"null;type(datetime);column(last_active_at)" json:"last_active_at"` // 最后活跃时间
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt    time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (a *ChatTransferAgent) TableName() string {
	return "chat_transfer_agent"
}

// ChatTransferRule 转接规则模型
type ChatTransferRule struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id"`                                    // 规则ID
	Name        string    `orm:"size(100);column(name)" json:"name"`                             // 规则名称
	Description string    `orm:"size(255);null;column(description)" json:"description"`          // 规则描述
	Conditions  string    `orm:"type(text);column(conditions)" json:"conditions"`                // 触发条件（JSON格式）
	Actions     string    `orm:"type(text);column(actions)" json:"actions"`                      // 执行动作（JSON格式）
	Priority    int       `orm:"default(0);column(priority)" json:"priority"`                    // 优先级
	Enabled     int       `orm:"default(1);column(enabled)" json:"enabled"`                      // 是否启用
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (r *ChatTransferRule) TableName() string {
	return "chat_transfer_rule"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(ChatTransfer))
	orm.RegisterModel(new(ChatTransferQueue))
	orm.RegisterModel(new(ChatTransferAgent))
	orm.RegisterModel(new(ChatTransferRule))
}
