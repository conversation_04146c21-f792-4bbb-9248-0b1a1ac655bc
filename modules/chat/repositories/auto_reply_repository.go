/**
 * auto_reply_repository.go
 * 自动回复数据访问接口
 *
 * 该文件定义了自动回复相关的数据访问接口
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
)

// AutoReplyRepository 自动回复数据访问接口
type AutoReplyRepository interface {
	// 规则管理
	CreateRule(ctx context.Context, rule *models.ChatAutoReply) (int64, error)
	UpdateRule(ctx context.Context, rule *models.ChatAutoReply) error
	DeleteRule(ctx context.Context, ruleID int64) error
	GetRuleByID(ctx context.Context, ruleID int64) (*models.ChatAutoReply, error)
	ListRules(ctx context.Context, req *dto.ListAutoReplyRulesRequest) ([]*models.ChatAutoReply, int64, error)
	
	// 配置管理
	CreateConfig(ctx context.Context, config *models.ChatAutoReplyConfig) (int64, error)
	UpdateConfig(ctx context.Context, config *models.ChatAutoReplyConfig) error
	GetConfigByUser(ctx context.Context, userID int64, userType string) (*models.ChatAutoReplyConfig, error)
	
	// 日志管理
	CreateLog(ctx context.Context, log *models.ChatAutoReplyLog) error
	GetLogs(ctx context.Context, req *dto.GetAutoReplyLogsRequest) ([]*models.ChatAutoReplyLog, int64, error)
	
	// 查询方法
	GetActiveRulesByUser(ctx context.Context, userID int64, userType string) ([]*models.ChatAutoReply, error)
	GetRulesByType(ctx context.Context, ruleType string, userID int64, userType string) ([]*models.ChatAutoReply, error)
	IncrementTriggerCount(ctx context.Context, ruleID int64) error
	GetRuleStats(ctx context.Context, ruleID int64) (int64, error)
	
	// 统计方法
	GetTotalRulesCount(ctx context.Context, userID int64, userType string) (int64, error)
	GetActiveRulesCount(ctx context.Context, userID int64, userType string) (int64, error)
	GetTotalTriggersCount(ctx context.Context, userID int64, userType string) (int64, error)
	GetTodayTriggersCount(ctx context.Context, userID int64, userType string) (int64, error)
}
