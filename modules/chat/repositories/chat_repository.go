/**
 * chat_repository.go
 * 聊天数据访问层接口
 *
 * 该文件定义了聊天模块的数据访问层接口，用于访问聊天会话和消息数据
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/chat/models"
)

// ChatRepository 聊天数据访问接口
type ChatRepository interface {
	// 会话相关方法
	CreateSession(ctx context.Context, session *models.ChatSession) (int64, error)
	GetSessionByID(ctx context.Context, sessionID int64) (*models.ChatSession, error)
	FindSessionByParticipants(ctx context.Context, creatorID int64, creatorType string, receiverID int64, receiverType string) (*models.ChatSession, error)
	ListSessionsByUser(ctx context.Context, userID int64, userType string, page, pageSize int) ([]*models.ChatSession, int64, error)
	UpdateSessionLastMessage(ctx context.Context, sessionID, lastMessageID int64) error
	UpdateSessionUnreadCount(ctx context.Context, sessionID int64, unreadCount int) error
	ResetSessionUnreadCount(ctx context.Context, sessionID int64) error

	// 消息相关方法
	CreateMessage(ctx context.Context, message *models.ChatMessage) (int64, error)
	GetMessageByID(ctx context.Context, messageID int64) (*models.ChatMessage, error)
	ListMessagesBySession(ctx context.Context, sessionID int64, page, pageSize int, order string) ([]*models.ChatMessage, int64, error)
	MarkMessageAsRead(ctx context.Context, messageID int64) error
	MarkSessionMessagesAsRead(ctx context.Context, sessionID int64, recipientID int64) error
	CountUnreadMessages(ctx context.Context, sessionID int64, recipientID int64) (int, error)
}
