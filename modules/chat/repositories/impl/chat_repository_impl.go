/**
 * chat_repository_impl.go
 * 聊天数据访问层实现
 *
 * 该文件实现了聊天模块的数据访问接口，提供对聊天会话和消息数据的存取功能
 */

package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/chat/models"
	"o_mall_backend/modules/chat/repositories"
)

// ChatRepositoryImpl 聊天数据访问实现类
type ChatRepositoryImpl struct{}

// NewChatRepository 创建聊天数据访问实例
func NewChatRepository() repositories.ChatRepository {
	return &ChatRepositoryImpl{}
}

// CreateSession 创建新的聊天会话
func (r *ChatRepositoryImpl) CreateSession(ctx context.Context, session *models.ChatSession) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(session)
	if err != nil {
		logs.Error("[ChatRepository] 创建会话失败: %v", err)
		return 0, fmt.Errorf("创建会话失败: %v", err)
	}
	return id, nil
}

// GetSessionByID 根据ID获取会话
func (r *ChatRepositoryImpl) GetSessionByID(ctx context.Context, sessionID int64) (*models.ChatSession, error) {
	o := orm.NewOrm()
	session := &models.ChatSession{ID: sessionID}
	err := o.Read(session)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[ChatRepository] 获取会话失败: %v", err)
		return nil, fmt.Errorf("获取会话失败: %v", err)
	}
	return session, nil
}

// FindSessionByParticipants 根据参与者查找会话
func (r *ChatRepositoryImpl) FindSessionByParticipants(
	ctx context.Context, creatorID int64, creatorType string,
	receiverID int64, receiverType string,
) (*models.ChatSession, error) {
	o := orm.NewOrm()
	session := new(models.ChatSession)

	// 查找双方的会话（两种方向都查找）
	// 使用数据库中实际的字段名称
	cond1 := orm.NewCondition()
	cond1 = cond1.And("creator_i_d", creatorID).
		And("creator_type", creatorType).
		And("receiver_i_d", receiverID).
		And("receiver_type", receiverType)

	cond2 := orm.NewCondition()
	cond2 = cond2.And("creator_i_d", receiverID).
		And("creator_type", receiverType).
		And("receiver_i_d", creatorID).
		And("receiver_type", creatorType)

	cond := orm.NewCondition()
	cond = cond.OrCond(cond1).OrCond(cond2)

	err := o.QueryTable(new(models.ChatSession)).
		SetCond(cond).
		Filter("status", 0).
		OrderBy("-updated_at").
		One(session)

	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[ChatRepository] 查询会话失败: %v", err)
		return nil, fmt.Errorf("查询会话失败: %v", err)
	}
	return session, nil
}

// ListSessionsByUser 获取用户的会话列表
func (r *ChatRepositoryImpl) ListSessionsByUser(ctx context.Context, userID int64, userType string, page, pageSize int) ([]*models.ChatSession, int64, error) {
	o := orm.NewOrm()
	var sessions []*models.ChatSession

	// 构建查询条件
	// 1. 用户作为创建者的会话
	cond1 := orm.NewCondition()
	cond1 = cond1.And("creator_i_d", userID).And("creator_type", userType)

	// 2. 用户作为接收者的会话
	cond2 := orm.NewCondition()
	cond2 = cond2.And("receiver_i_d", userID).And("receiver_type", userType)

	// 3. 用户参与的群聊会话
	// 首先查询用户参与的所有群聊ID
	var groupMembers []models.ChatGroupMember
	_, err := o.QueryTable(new(models.ChatGroupMember)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Filter("status", 0).
		All(&groupMembers)
	if err != nil {
		logs.Error("[ChatRepository] 查询用户群聊失败: %v", err)
		return nil, 0, fmt.Errorf("查询用户群聊失败: %v", err)
	}

	// 提取群聊ID
	var groupIDs []int64
	for _, member := range groupMembers {
		groupIDs = append(groupIDs, member.GroupID)
	}

	// 构建群聊会话查询条件
	cond3 := orm.NewCondition()
	if len(groupIDs) > 0 {
		cond3 = cond3.And("type", "group").And("receiver_i_d__in", groupIDs)
	} else {
		// 如果用户没有参与任何群聊，创建一个永远不会匹配的条件
		cond3 = cond3.And("id", -1)
	}

	// 合并所有查询条件
	cond := orm.NewCondition()
	cond = cond.OrCond(cond1).OrCond(cond2).OrCond(cond3)

	// 查询会话总数
	qb := o.QueryTable(new(models.ChatSession)).SetCond(cond).Filter("status", 0)
	total, err := qb.Count()
	if err != nil {
		logs.Error("[ChatRepository] 获取会话总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取会话总数失败: %v", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	_, err = qb.OrderBy("-updated_at").Limit(pageSize, offset).All(&sessions)
	if err != nil {
		logs.Error("[ChatRepository] 分页查询会话失败: %v", err)
		return nil, 0, fmt.Errorf("分页查询会话失败: %v", err)
	}

	return sessions, total, nil
}

// UpdateSessionLastMessage 更新会话最后一条消息
func (r *ChatRepositoryImpl) UpdateSessionLastMessage(ctx context.Context, sessionID, lastMessageID int64) error {
	o := orm.NewOrm()
	session := &models.ChatSession{ID: sessionID}
	if err := o.Read(session); err != nil {
		logs.Error("[ChatRepository] 读取会话失败: %v", err)
		return fmt.Errorf("读取会话失败: %v", err)
	}

	session.LastMessageID = lastMessageID
	_, err := o.Update(session, "LastMessageID", "UpdatedAt")
	if err != nil {
		logs.Error("[ChatRepository] 更新会话最后消息失败: %v", err)
		return fmt.Errorf("更新会话最后消息失败: %v", err)
	}
	return nil
}

// UpdateSessionUnreadCount 更新会话未读消息数
func (r *ChatRepositoryImpl) UpdateSessionUnreadCount(ctx context.Context, sessionID int64, unreadCount int) error {
	o := orm.NewOrm()
	session := &models.ChatSession{ID: sessionID}
	if err := o.Read(session); err != nil {
		logs.Error("[ChatRepository] 读取会话失败: %v", err)
		return fmt.Errorf("读取会话失败: %v", err)
	}

	session.UnreadCount = unreadCount
	_, err := o.Update(session, "UnreadCount", "UpdatedAt")
	if err != nil {
		logs.Error("[ChatRepository] 更新会话未读数失败: %v", err)
		return fmt.Errorf("更新会话未读数失败: %v", err)
	}
	return nil
}

// ResetSessionUnreadCount 重置会话未读计数
func (r *ChatRepositoryImpl) ResetSessionUnreadCount(ctx context.Context, sessionID int64) error {
	o := orm.NewOrm()
	session := &models.ChatSession{ID: sessionID}
	if err := o.Read(session); err != nil {
		logs.Error("[ChatRepository] 读取会话失败: %v", err)
		return fmt.Errorf("读取会话失败: %v", err)
	}

	session.UnreadCount = 0
	_, err := o.Update(session, "UnreadCount", "UpdatedAt")
	if err != nil {
		logs.Error("[ChatRepository] 重置会话未读数失败: %v", err)
		return fmt.Errorf("重置会话未读数失败: %v", err)
	}
	return nil
}

// CreateMessage 创建新消息
func (r *ChatRepositoryImpl) CreateMessage(ctx context.Context, message *models.ChatMessage) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(message)
	if err != nil {
		logs.Error("[ChatRepository] 创建消息失败: %v", err)
		return 0, fmt.Errorf("创建消息失败: %v", err)
	}
	return id, nil
}

// GetMessageByID 根据ID获取消息
func (r *ChatRepositoryImpl) GetMessageByID(ctx context.Context, messageID int64) (*models.ChatMessage, error) {
	o := orm.NewOrm()
	message := &models.ChatMessage{ID: messageID}
	err := o.Read(message)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("[ChatRepository] 获取消息失败: %v", err)
		return nil, fmt.Errorf("获取消息失败: %v", err)
	}
	return message, nil
}

// ListMessagesBySession 获取会话的消息列表
func (r *ChatRepositoryImpl) ListMessagesBySession(ctx context.Context, sessionID int64, page, pageSize int, order string) ([]*models.ChatMessage, int64, error) {
	o := orm.NewOrm()
	var messages []*models.ChatMessage

	// 查询消息总数
	qb := o.QueryTable(new(models.ChatMessage)).Filter("session_i_d", sessionID)
	total, err := qb.Count()
	if err != nil {
		logs.Error("[ChatRepository] 获取消息总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取消息总数失败: %v", err)
	}

	// 构建排序字段
	orderBy := "-created_at" // 默认按创建时间倒序
	if order == "asc" {
		orderBy = "created_at"
	} else if order == "desc" {
		orderBy = "-created_at"
	}

	// 分页查询
	offset := (page - 1) * pageSize
	_, err = qb.OrderBy(orderBy).Limit(pageSize, offset).All(&messages)
	if err != nil {
		logs.Error("[ChatRepository] 分页查询消息失败: %v", err)
		return nil, 0, fmt.Errorf("分页查询消息失败: %v", err)
	}

	return messages, total, nil
}

// MarkMessageAsRead 标记消息为已读
func (r *ChatRepositoryImpl) MarkMessageAsRead(ctx context.Context, messageID int64) error {
	o := orm.NewOrm()
	message := &models.ChatMessage{ID: messageID}
	if err := o.Read(message); err != nil {
		logs.Error("[ChatRepository] 读取消息失败: %v", err)
		return fmt.Errorf("读取消息失败: %v", err)
	}

	message.Status = 1 // 已读状态
	_, err := o.Update(message, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("[ChatRepository] 标记消息为已读失败: %v", err)
		return fmt.Errorf("标记消息为已读失败: %v", err)
	}
	return nil
}

// MarkSessionMessagesAsRead 将会话中的所有消息标记为已读
func (r *ChatRepositoryImpl) MarkSessionMessagesAsRead(ctx context.Context, sessionID int64, recipientID int64) error {
	o := orm.NewOrm()

	// 使用事务处理
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[ChatRepository] 开始事务失败: %v", err)
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 更新接收者为指定用户，且状态为未读的消息
	_, err = tx.QueryTable(new(models.ChatMessage)).
		Filter("session_i_d", sessionID).
		Filter("sender_i_d__ne", recipientID). // 非自己发送的消息
		Filter("status", 0).                   // 未读状态
		Update(orm.Params{
			"status":     1,          // 更新为已读
			"updated_at": time.Now(), // 更新时间
		})

	if err != nil {
		tx.Rollback()
		logs.Error("[ChatRepository] 标记会话消息为已读失败: %v", err)
		return fmt.Errorf("标记会话消息为已读失败: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		logs.Error("[ChatRepository] 提交事务失败: %v", err)
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// CountUnreadMessages 统计会话中的未读消息数量
func (r *ChatRepositoryImpl) CountUnreadMessages(ctx context.Context, sessionID int64, recipientID int64) (int, error) {
	o := orm.NewOrm()

	// 查询接收者为指定用户，且状态为未读的消息数量
	count, err := o.QueryTable(new(models.ChatMessage)).
		Filter("session_i_d", sessionID).
		Filter("sender_i_d__ne", recipientID). // 非自己发送的消息
		Filter("status", 0).                   // 未读状态
		Count()

	if err != nil {
		logs.Error("[ChatRepository] 统计未读消息数量失败: %v", err)
		return 0, fmt.Errorf("统计未读消息数量失败: %v", err)
	}

	return int(count), nil
}
