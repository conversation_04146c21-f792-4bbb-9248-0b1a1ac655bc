/**
 * template_repository.go
 * 模板数据访问接口
 *
 * 该文件定义了模板相关的数据访问接口
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
)

// TemplateRepository 模板数据访问接口
type TemplateRepository interface {
	// 模板管理
	CreateTemplate(ctx context.Context, template *models.ChatTemplate) (int64, error)
	UpdateTemplate(ctx context.Context, template *models.ChatTemplate) error
	DeleteTemplate(ctx context.Context, templateID int64) error
	GetTemplateByID(ctx context.Context, templateID int64) (*models.ChatTemplate, error)
	ListTemplates(ctx context.Context, req *dto.ListTemplatesRequest) ([]*models.ChatTemplate, int64, error)
	
	// 分类管理
	CreateCategory(ctx context.Context, category *models.ChatTemplateCategory) (int64, error)
	UpdateCategory(ctx context.Context, category *models.ChatTemplateCategory) error
	DeleteCategory(ctx context.Context, categoryID int64) error
	GetCategoryByID(ctx context.Context, categoryID int64) (*models.ChatTemplateCategory, error)
	ListCategories(ctx context.Context) ([]*models.ChatTemplateCategory, error)
	
	// 使用记录
	CreateUsage(ctx context.Context, usage *models.ChatTemplateUsage) error
	IncrementUseCount(ctx context.Context, templateID int64) error
	GetUsageStats(ctx context.Context, templateID int64) (int64, error)
	
	// 查询方法
	GetTemplatesByRole(ctx context.Context, userRole string, category string) ([]*models.ChatTemplate, error)
	GetPopularTemplates(ctx context.Context, userRole string, limit int) ([]*models.ChatTemplate, error)
	GetTemplatesByCreator(ctx context.Context, creatorID int64, creatorType string) ([]*models.ChatTemplate, error)
	SearchTemplates(ctx context.Context, keyword string, userRole string) ([]*models.ChatTemplate, error)
}
