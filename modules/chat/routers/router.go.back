/**
 * router.go
 * 聊天模块路由配置
 *
 * 该文件定义了聊天模块的API路由配置，将请求路由到对应的控制器方法
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"
	
	"o_mall_backend/modules/chat/controllers"
	"o_mall_backend/modules/chat/middlewares"
)

// RegisterRoutes 注册聊天模块的路由
func RegisterRoutes(
	webSocketController *controllers.WebSocketController,
	sessionController *controllers.SessionController,
	messageController *controllers.MessageController,
	groupController *controllers.GroupController,
	friendController *controllers.FriendController,
	blacklistController *controllers.BlacklistController,
) {
	// API路由前缀
	ns := web.NewNamespace("/api/v1/chat",
		// 应用JWT中间件
		web.NSBefore(middlewares.JWTMiddleware()),
		
		// WebSocket连接
		web.NSRouter("/ws", webSocketController, "get:HandleConnection"),
		
		// 会话相关路由
		web.NSRouter("/sessions", sessionController, "post:CreateSession"),
		web.NSRouter("/sessions", sessionController, "get:GetSessionList"),
		web.NSRouter("/sessions/:id", sessionController, "get:GetSession"),
		web.NSRouter("/sessions/:id/read", sessionController, "put:MarkAsRead"),
		
		// 消息相关路由
		web.NSRouter("/sessions/:session_id/messages", messageController, "get:GetMessages"),
		web.NSRouter("/sessions/:session_id/messages/text", messageController, "post:SendTextMessage"),
		web.NSRouter("/sessions/:session_id/messages/media", messageController, "post:SendMediaMessage"),
		
		// 群聊相关路由
		web.NSRouter("/groups", groupController, "post:CreateGroup"),
		web.NSRouter("/groups", groupController, "get:GetUserGroups"),
		web.NSRouter("/groups/:group_id", groupController, "get:GetGroupInfo"),
		web.NSRouter("/groups/:group_id", groupController, "put:UpdateGroupInfo"),
		web.NSRouter("/groups/:group_id/dismiss", groupController, "post:DismissGroup"),
		web.NSRouter("/groups/:group_id/transfer", groupController, "post:TransferOwner"),
		web.NSRouter("/groups/:group_id/members", groupController, "get:GetGroupMembers"),
		web.NSRouter("/groups/:group_id/members", groupController, "post:AddGroupMember"),
		web.NSRouter("/groups/:group_id/members/:member_id", groupController, "delete:RemoveGroupMember"),
		web.NSRouter("/groups/:group_id/members/:member_id", groupController, "put:UpdateGroupMember"),
		
		// 好友相关路由
		web.NSRouter("/friends", friendController, "get:GetFriendList"),
		web.NSRouter("/friends/requests", friendController, "post:SendFriendRequest"),
		web.NSRouter("/friends/requests", friendController, "get:GetFriendRequestList"),
		web.NSRouter("/friends/requests/:request_id", friendController, "post:HandleFriendRequest"),
		web.NSRouter("/friends/:friend_id", friendController, "delete:DeleteFriend"),
		web.NSRouter("/friends/:friend_id", friendController, "put:UpdateFriend"),
		
		// 黑名单相关路由
		web.NSRouter("/blacklist", blacklistController, "post:AddToBlacklist"),
		web.NSRouter("/blacklist", blacklistController, "get:GetBlacklist"),
		web.NSRouter("/blacklist/:blocked_id", blacklistController, "delete:RemoveFromBlacklist"),
		web.NSRouter("/blacklist/check/:target_id", blacklistController, "get:IsBlocked"),
	)
	
	// 注册命名空间路由
	web.AddNamespace(ns)
}
