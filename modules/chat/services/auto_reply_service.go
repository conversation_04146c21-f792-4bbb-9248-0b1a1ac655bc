/**
 * auto_reply_service.go
 * 自动回复服务接口
 *
 * 该文件定义了自动回复系统的业务逻辑接口
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
	"o_mall_backend/modules/chat/repositories"
)

// AutoReplyService 自动回复服务接口
type AutoReplyService interface {
	// 规则管理
	CreateRule(ctx context.Context, req *dto.CreateAutoReplyRuleRequest) (*models.ChatAutoReply, error)
	UpdateRule(ctx context.Context, ruleID int64, req *dto.UpdateAutoReplyRuleRequest) (*models.ChatAutoReply, error)
	DeleteRule(ctx context.Context, ruleID int64) error
	GetRule(ctx context.Context, ruleID int64) (*models.ChatAutoReply, error)
	ListRules(ctx context.Context, req *dto.ListAutoReplyRulesRequest) ([]*models.ChatAutoReply, int64, error)

	// 配置管理
	GetConfig(ctx context.Context, userID int64, userType string) (*models.ChatAutoReplyConfig, error)
	UpdateConfig(ctx context.Context, userID int64, userType string, req *dto.UpdateAutoReplyConfigRequest) (*models.ChatAutoReplyConfig, error)

	// 自动回复处理
	ProcessMessage(ctx context.Context, message *models.ChatMessage, session *models.ChatSession) error
	CheckAndReply(ctx context.Context, message *models.ChatMessage, session *models.ChatSession) (*models.ChatMessage, error)

	// 日志查询
	GetReplyLogs(ctx context.Context, req *dto.GetAutoReplyLogsRequest) ([]*models.ChatAutoReplyLog, int64, error)
}

// AutoReplyServiceImpl 自动回复服务实现
type AutoReplyServiceImpl struct {
	autoReplyRepo repositories.AutoReplyRepository
	chatService   ChatService
	wsManager     WebSocketManager
}

// NewAutoReplyService 创建自动回复服务实例
func NewAutoReplyService(autoReplyRepo repositories.AutoReplyRepository, chatService ChatService, wsManager WebSocketManager) AutoReplyService {
	return &AutoReplyServiceImpl{
		autoReplyRepo: autoReplyRepo,
		chatService:   chatService,
		wsManager:     wsManager,
	}
}

// ProcessMessage 处理消息，检查是否需要自动回复
func (s *AutoReplyServiceImpl) ProcessMessage(ctx context.Context, message *models.ChatMessage, session *models.ChatSession) error {
	// 只处理用户发送的消息
	if message.SenderType != "user" {
		return nil
	}

	// 获取接收者的自动回复配置
	var receiverID int64
	var receiverType string

	if session.CreatorID == message.SenderID {
		receiverID = session.ReceiverID
		receiverType = session.ReceiverType
	} else {
		receiverID = session.CreatorID
		receiverType = session.CreatorType
	}

	config, err := s.autoReplyRepo.GetConfigByUser(ctx, receiverID, receiverType)
	if err != nil || config == nil || config.Enabled == 0 {
		return nil // 未启用自动回复
	}

	// 检查工作时间
	if !s.isInWorkingHours(config.WorkingHours) {
		// 发送离线消息
		if config.OfflineMessage != "" {
			return s.sendAutoReply(ctx, session.ID, receiverID, receiverType, config.OfflineMessage, 0, "offline")
		}
		return nil
	}

	// 检查匹配的规则
	replyMessage, err := s.CheckAndReply(ctx, message, session)
	if err != nil {
		logs.Error("[AutoReplyService] 检查自动回复失败: %v", err)
		return err
	}

	if replyMessage != nil {
		logs.Info("[AutoReplyService] 自动回复消息已发送: %d", replyMessage.ID)
	}

	return nil
}

// CheckAndReply 检查并回复
func (s *AutoReplyServiceImpl) CheckAndReply(ctx context.Context, message *models.ChatMessage, session *models.ChatSession) (*models.ChatMessage, error) {
	// 获取适用的规则
	var receiverID int64
	var receiverType string

	if session.CreatorID == message.SenderID {
		receiverID = session.ReceiverID
		receiverType = session.ReceiverType
	} else {
		receiverID = session.CreatorID
		receiverType = session.CreatorType
	}

	rules, err := s.autoReplyRepo.GetActiveRulesByUser(ctx, receiverID, receiverType)
	if err != nil {
		return nil, err
	}

	// 按优先级排序，检查匹配的规则
	for _, rule := range rules {
		if s.matchRule(message.Content, rule) {
			// 检查触发次数限制
			if rule.MaxTriggerCount > 0 && rule.TriggerCount >= int64(rule.MaxTriggerCount) {
				continue
			}

			// 发送自动回复
			replyMessage, err := s.sendAutoReplyWithRule(ctx, session.ID, receiverID, receiverType, rule, message)
			if err != nil {
				logs.Error("[AutoReplyService] 发送自动回复失败: %v", err)
				continue
			}

			// 记录日志
			err = s.logAutoReply(ctx, rule, session.ID, message.ID, replyMessage.ID, message.SenderID, message.SenderType, message.Content, rule.ReplyContent)
			if err != nil {
				logs.Error("[AutoReplyService] 记录自动回复日志失败: %v", err)
			}

			// 更新触发次数
			err = s.autoReplyRepo.IncrementTriggerCount(ctx, rule.ID)
			if err != nil {
				logs.Error("[AutoReplyService] 更新触发次数失败: %v", err)
			}

			return replyMessage, nil
		}
	}

	return nil, nil
}

// matchRule 检查消息是否匹配规则
func (s *AutoReplyServiceImpl) matchRule(content string, rule *models.ChatAutoReply) bool {
	content = strings.ToLower(strings.TrimSpace(content))

	switch rule.Type {
	case models.AutoReplyTypeKeyword:
		return s.matchKeywords(content, rule.Keywords)
	case models.AutoReplyTypeRegex:
		return s.matchRegex(content, rule.RegexPattern)
	case models.AutoReplyTypeWelcome:
		// 欢迎消息通常在会话开始时触发，这里简化处理
		return strings.Contains(content, "你好") || strings.Contains(content, "hello") || strings.Contains(content, "hi")
	default:
		return false
	}
}

// matchKeywords 匹配关键词
func (s *AutoReplyServiceImpl) matchKeywords(content string, keywordsJSON string) bool {
	if keywordsJSON == "" {
		return false
	}

	var keywords []string
	err := json.Unmarshal([]byte(keywordsJSON), &keywords)
	if err != nil {
		logs.Error("[AutoReplyService] 解析关键词失败: %v", err)
		return false
	}

	for _, keyword := range keywords {
		if strings.Contains(content, strings.ToLower(keyword)) {
			return true
		}
	}

	return false
}

// matchRegex 匹配正则表达式
func (s *AutoReplyServiceImpl) matchRegex(content string, pattern string) bool {
	if pattern == "" {
		return false
	}

	regex, err := regexp.Compile(pattern)
	if err != nil {
		logs.Error("[AutoReplyService] 编译正则表达式失败: %v", err)
		return false
	}

	return regex.MatchString(content)
}

// sendAutoReply 发送自动回复
func (s *AutoReplyServiceImpl) sendAutoReply(ctx context.Context, sessionID int64, senderID int64, senderType string, content string, delay int, replyType string) error {
	// 如果有延迟，等待指定时间
	if delay > 0 {
		time.Sleep(time.Duration(delay) * time.Second)
	}

	// 发送消息
	_, err := s.chatService.SendTextMessage(ctx, sessionID, senderID, senderType, content)
	return err
}

// sendAutoReplyWithRule 根据规则发送自动回复
func (s *AutoReplyServiceImpl) sendAutoReplyWithRule(ctx context.Context, sessionID int64, senderID int64, senderType string, rule *models.ChatAutoReply, triggerMessage *models.ChatMessage) (*models.ChatMessage, error) {
	// 如果有延迟，等待指定时间
	if rule.ReplyDelay > 0 {
		time.Sleep(time.Duration(rule.ReplyDelay) * time.Second)
	}

	// 发送消息
	return s.chatService.SendTextMessage(ctx, sessionID, senderID, senderType, rule.ReplyContent)
}

// logAutoReply 记录自动回复日志
func (s *AutoReplyServiceImpl) logAutoReply(ctx context.Context, rule *models.ChatAutoReply, sessionID int64, messageID int64, replyID int64, userID int64, userType string, triggerText string, replyText string) error {
	log := &models.ChatAutoReplyLog{
		RuleID:      rule.ID,
		SessionID:   sessionID,
		MessageID:   messageID,
		ReplyID:     replyID,
		UserID:      userID,
		UserType:    userType,
		TriggerText: triggerText,
		ReplyText:   replyText,
	}

	return s.autoReplyRepo.CreateLog(ctx, log)
}

// isInWorkingHours 检查是否在工作时间内
func (s *AutoReplyServiceImpl) isInWorkingHours(workingHoursJSON string) bool {
	if workingHoursJSON == "" {
		return true // 没有设置工作时间，默认24小时工作
	}

	var workingHours map[string]interface{}
	err := json.Unmarshal([]byte(workingHoursJSON), &workingHours)
	if err != nil {
		logs.Error("[AutoReplyService] 解析工作时间失败: %v", err)
		return true
	}

	// 简化实现：这里可以根据实际需求实现复杂的时间判断逻辑
	now := time.Now()
	currentHour := now.Hour()

	// 假设工作时间格式为 {"start": 9, "end": 18}
	if start, ok := workingHours["start"].(float64); ok {
		if end, ok := workingHours["end"].(float64); ok {
			return currentHour >= int(start) && currentHour < int(end)
		}
	}

	return true
}

// CreateRule 创建自动回复规则
func (s *AutoReplyServiceImpl) CreateRule(ctx context.Context, req *dto.CreateAutoReplyRuleRequest) (*models.ChatAutoReply, error) {
	rule := &models.ChatAutoReply{
		Name:             req.Name,
		Description:      req.Description,
		Type:             req.Type,
		TriggerCondition: req.TriggerCondition,
		Keywords:         req.Keywords,
		RegexPattern:     req.RegexPattern,
		ReplyContent:     req.ReplyContent,
		ReplyDelay:       req.ReplyDelay,
		Priority:         req.Priority,
		MaxTriggerCount:  req.MaxTriggerCount,
		ApplicableRole:   req.ApplicableRole,
		ApplicableTime:   req.ApplicableTime,
		CreatorID:        req.CreatorID,
		CreatorType:      req.CreatorType,
		Status:           models.AutoReplyStatusActive,
	}

	id, err := s.autoReplyRepo.CreateRule(ctx, rule)
	if err != nil {
		return nil, err
	}

	return s.autoReplyRepo.GetRuleByID(ctx, id)
}

// UpdateRule 更新自动回复规则
func (s *AutoReplyServiceImpl) UpdateRule(ctx context.Context, ruleID int64, req *dto.UpdateAutoReplyRuleRequest) (*models.ChatAutoReply, error) {
	rule, err := s.autoReplyRepo.GetRuleByID(ctx, ruleID)
	if err != nil {
		return nil, err
	}
	if rule == nil {
		return nil, fmt.Errorf("规则不存在")
	}

	// 更新字段
	if req.Name != "" {
		rule.Name = req.Name
	}
	if req.Description != "" {
		rule.Description = req.Description
	}
	if req.Type != "" {
		rule.Type = req.Type
	}
	if req.TriggerCondition != "" {
		rule.TriggerCondition = req.TriggerCondition
	}
	if req.Keywords != "" {
		rule.Keywords = req.Keywords
	}
	if req.RegexPattern != "" {
		rule.RegexPattern = req.RegexPattern
	}
	if req.ReplyContent != "" {
		rule.ReplyContent = req.ReplyContent
	}
	if req.ReplyDelay != 0 {
		rule.ReplyDelay = req.ReplyDelay
	}
	if req.Priority != 0 {
		rule.Priority = req.Priority
	}
	if req.MaxTriggerCount != 0 {
		rule.MaxTriggerCount = req.MaxTriggerCount
	}
	if req.ApplicableRole != "" {
		rule.ApplicableRole = req.ApplicableRole
	}
	if req.ApplicableTime != "" {
		rule.ApplicableTime = req.ApplicableTime
	}
	if req.Status != 0 {
		rule.Status = req.Status
	}

	err = s.autoReplyRepo.UpdateRule(ctx, rule)
	if err != nil {
		return nil, err
	}

	return rule, nil
}

// DeleteRule 删除自动回复规则
func (s *AutoReplyServiceImpl) DeleteRule(ctx context.Context, ruleID int64) error {
	return s.autoReplyRepo.DeleteRule(ctx, ruleID)
}

// GetRule 获取自动回复规则
func (s *AutoReplyServiceImpl) GetRule(ctx context.Context, ruleID int64) (*models.ChatAutoReply, error) {
	return s.autoReplyRepo.GetRuleByID(ctx, ruleID)
}

// ListRules 获取自动回复规则列表
func (s *AutoReplyServiceImpl) ListRules(ctx context.Context, req *dto.ListAutoReplyRulesRequest) ([]*models.ChatAutoReply, int64, error) {
	return s.autoReplyRepo.ListRules(ctx, req)
}

// GetConfig 获取自动回复配置
func (s *AutoReplyServiceImpl) GetConfig(ctx context.Context, userID int64, userType string) (*models.ChatAutoReplyConfig, error) {
	return s.autoReplyRepo.GetConfigByUser(ctx, userID, userType)
}

// UpdateConfig 更新自动回复配置
func (s *AutoReplyServiceImpl) UpdateConfig(ctx context.Context, userID int64, userType string, req *dto.UpdateAutoReplyConfigRequest) (*models.ChatAutoReplyConfig, error) {
	config, err := s.autoReplyRepo.GetConfigByUser(ctx, userID, userType)
	if err != nil {
		return nil, err
	}

	if config == nil {
		// 创建新配置
		config = &models.ChatAutoReplyConfig{
			UserID:   userID,
			UserType: userType,
		}
	}

	// 更新字段
	config.Enabled = req.Enabled
	config.WorkingHours = req.WorkingHours
	config.OfflineMessage = req.OfflineMessage
	config.WelcomeMessage = req.WelcomeMessage
	config.NoResponseDelay = req.NoResponseDelay
	config.NoResponseMessage = req.NoResponseMessage
	config.MaxAutoReplies = req.MaxAutoReplies

	if config.ID == 0 {
		// 创建
		_, err = s.autoReplyRepo.CreateConfig(ctx, config)
	} else {
		// 更新
		err = s.autoReplyRepo.UpdateConfig(ctx, config)
	}

	if err != nil {
		return nil, err
	}

	return config, nil
}

// GetReplyLogs 获取自动回复日志
func (s *AutoReplyServiceImpl) GetReplyLogs(ctx context.Context, req *dto.GetAutoReplyLogsRequest) ([]*models.ChatAutoReplyLog, int64, error) {
	return s.autoReplyRepo.GetLogs(ctx, req)
}
