/**
 * blacklist_service.go
 * 黑名单服务
 *
 * 该文件提供黑名单管理的相关功能，包括添加、移除黑名单，检查黑名单关系等
 */

package services

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
)

// BlacklistService 黑名单服务
type BlacklistService struct{}

// NewBlacklistService 创建黑名单服务实例
func NewBlacklistService() *BlacklistService {
	return &BlacklistService{}
}

// AddToBlacklist 添加用户到黑名单
func (s *BlacklistService) AddToBlacklist(userID int64, userType string, req *dto.AddBlacklistRequest) error {
	o := orm.NewOrm()

	// 检查是否已在黑名单中
	isBlocked, err := s.IsBlocked(userID, userType, req.BlockedID, req.BlockedType)
	if err != nil {
		return err
	}

	if isBlocked {
		return errors.New("该用户已在黑名单中")
	}

	// 设置过期时间
	var expireTime time.Time
	if req.ExpireDays > 0 {
		expireTime = time.Now().AddDate(0, 0, req.ExpireDays)
	} else {
		// 设置为零值表示永久
		expireTime = time.Time{}
	}
	
	// 使用DoTx处理事务
	err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		// 添加黑名单记录
		blacklist := &models.ChatBlacklist{
			UserID:      userID,
			UserType:    userType,
			BlockedID:   req.BlockedID,
			BlockedType: req.BlockedType,
			Reason:      req.Reason,
			ExpireTime:  expireTime,
		}

		_, err := txOrm.Insert(blacklist)
		if err != nil {
			return err
		}

		// 如果是好友关系，则删除好友关系
		friendService := NewFriendService()
		isFriend, err := friendService.IsFriend(userID, userType, req.BlockedID, req.BlockedType)
		if err != nil {
			return err
		}

		if isFriend {
			// 这里使用DeleteFriend方法已经重构为内部使用DoTx，不需要显式传递事务
			err = friendService.DeleteFriend(userID, userType, req.BlockedID, req.BlockedType)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// RemoveFromBlacklist 从黑名单中移除用户
func (s *BlacklistService) RemoveFromBlacklist(userID int64, userType string, blockedID int64, blockedType string) error {
	o := orm.NewOrm()

	// 查找并删除黑名单记录
	_, err := o.QueryTable(new(models.ChatBlacklist)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Filter("blocked_id", blockedID).
		Filter("blocked_type", blockedType).
		Delete()

	if err != nil {
		return err
	}

	return nil
}

// GetBlacklist 获取用户的黑名单列表
func (s *BlacklistService) GetBlacklist(userID int64, userType string, page, pageSize int) (*dto.BlacklistListResponse, error) {
	o := orm.NewOrm()

	var blacklists []models.ChatBlacklist
	_, err := o.QueryTable(new(models.ChatBlacklist)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Limit(pageSize, (page-1)*pageSize).
		OrderBy("-id").
		All(&blacklists)

	if err != nil {
		return nil, err
	}

	// 清理过期的黑名单记录
	s.cleanExpiredBlacklist(o, blacklists)

	// 查询总数（重新查询，因为可能清理了过期记录）
	count, err := o.QueryTable(new(models.ChatBlacklist)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Count()
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	var blacklistDTOs []dto.BlacklistDTO
	for _, blacklist := range blacklists {
		// 检查是否已过期
		if !blacklist.ExpireTime.IsZero() && blacklist.ExpireTime.Before(time.Now()) {
			continue // 跳过已过期的记录
		}

		blacklistDTO := dto.BlacklistDTO{
			ID:          blacklist.ID,
			UserID:      blacklist.UserID,
			UserType:    blacklist.UserType,
			BlockedID:   blacklist.BlockedID,
			BlockedType: blacklist.BlockedType,
			Reason:      blacklist.Reason,
			ExpireTime:  blacklist.ExpireTime,
			CreatedAt:   blacklist.CreatedAt,
		}

		// 这里可以添加获取被屏蔽用户基本信息的代码，如姓名、头像等
		// 由于涉及其他模块的用户服务，此处简化处理

		blacklistDTOs = append(blacklistDTOs, blacklistDTO)
	}

	return &dto.BlacklistListResponse{
		Total: count,
		List:  blacklistDTOs,
	}, nil
}

// IsBlocked 检查一个用户是否被另一个用户屏蔽
func (s *BlacklistService) IsBlocked(userID int64, userType string, blockedID int64, blockedType string) (bool, error) {
	o := orm.NewOrm()

	// 查询黑名单记录
	var blacklist models.ChatBlacklist
	err := o.QueryTable(new(models.ChatBlacklist)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Filter("blocked_id", blockedID).
		Filter("blocked_type", blockedType).
		One(&blacklist)

	if err != nil {
		if err == orm.ErrNoRows {
			return false, nil
		}
		return false, err
	}

	// 检查是否已过期
	if !blacklist.ExpireTime.IsZero() && blacklist.ExpireTime.Before(time.Now()) {
		// 过期了，自动删除
		_, err = o.Delete(&blacklist)
		if err != nil {
			return false, err
		}
		return false, nil
	}

	return true, nil
}

// 清理过期的黑名单记录
func (s *BlacklistService) cleanExpiredBlacklist(o orm.Ormer, blacklists []models.ChatBlacklist) {
	for _, blacklist := range blacklists {
		// 如果设置了过期时间并且已过期
		if !blacklist.ExpireTime.IsZero() && blacklist.ExpireTime.Before(time.Now()) {
			_, err := o.Delete(&blacklist)
			if err != nil {
				// 记录错误但不中断处理
				continue
			}
		}
	}
}
