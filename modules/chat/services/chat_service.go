/**
 * chat_service.go
 * 聊天服务接口
 *
 * 该文件定义了聊天模块的业务逻辑接口，提供对聊天会话和消息的管理功能
 */

package services

import (
	"context"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
)

// ChatService 聊天服务接口
type ChatService interface {
	// 会话相关方法
	CreateSession(ctx context.Context, creatorID int64, creatorType string, receiverID int64, receiverType string) (*models.ChatSession, error)
	GetSession(ctx context.Context, sessionID int64) (*models.ChatSession, error)
	FindOrCreateSession(ctx context.Context, creatorID int64, creatorType string, receiverID int64, receiverType string) (*models.ChatSession, error)
	GetUserSessions(ctx context.Context, userID int64, userType string, page, pageSize int) ([]*dto.SessionDTO, int64, error)

	// 群聊会话相关方法
	CreateGroupSession(ctx context.Context, groupID int64, creatorID int64, creatorType string) (*models.ChatSession, error)
	JoinGroupSession(ctx context.Context, groupID int64, userID int64, userType string) (*models.ChatSession, error)
	LeaveGroupSession(ctx context.Context, groupID int64, userID int64, userType string) error
	GetGroupSessionDetails(ctx context.Context, groupID int64, userID int64, userType string) (*dto.SessionDTO, error)

	// 消息相关方法
	SendTextMessage(ctx context.Context, sessionID int64, senderID int64, senderType string, content string) (*models.ChatMessage, error)
	SendMediaMessage(ctx context.Context, sessionID int64, senderID int64, senderType string, mediaType string, resourceID string) (*models.ChatMessage, error)
	SendMediaURLMessage(ctx context.Context, sessionID int64, senderID int64, senderType string, mediaType string, mediaURL string, fileName string, fileSize int64, fileType string, fileExt string) (*models.ChatMessage, error)
	GetMessage(ctx context.Context, messageID int64) (*models.ChatMessage, error)
	GetSessionMessages(ctx context.Context, sessionID int64, page, pageSize int, order string) ([]*dto.MessageDTO, int64, error)
	MarkMessagesAsRead(ctx context.Context, sessionID int64, userID int64) error
}
