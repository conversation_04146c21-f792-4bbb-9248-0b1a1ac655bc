/**
 * friend_service.go
 * 好友关系服务
 *
 * 该文件提供好友关系管理的相关功能，包括添加、删除好友，好友请求处理等
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
	userModels "o_mall_backend/modules/user/models"
	merchantModels "o_mall_backend/modules/merchant/models"
)

// FriendService 好友关系服务
type FriendService struct{}

// NewFriendService 创建好友服务实例
func NewFriendService() *FriendService {
	return &FriendService{}
}

// SendFriendRequest 发送好友请求
func (s *FriendService) SendFriendRequest(senderID int64, senderType string, req *dto.AddFriendRequest) error {
	o := orm.NewOrm()

	// 检查是否已经是好友
	isFriend, err := s.IsFriend(senderID, senderType, req.FriendID, req.FriendType)
	if err != nil {
		return err
	}

	if isFriend {
		return errors.New("已经是好友关系")
	}

	// 检查是否有未处理的好友请求
	count, err := o.QueryTable(new(models.ChatFriendRequest)).
		Filter("sender_id", senderID).
		Filter("sender_type", senderType).
		Filter("receiver_id", req.FriendID).
		Filter("receiver_type", req.FriendType).
		Filter("status", 0).
		Count()

	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("已有等待验证的好友请求")
	}

	// 检查对方是否已拉黑自己
	blacklistService := NewBlacklistService()
	isBlocked, err := blacklistService.IsBlocked(req.FriendID, req.FriendType, senderID, senderType)
	if err != nil {
		return err
	}

	if isBlocked {
		return errors.New("无法添加该用户为好友")
	}

	// 创建好友请求
	friendRequest := &models.ChatFriendRequest{
		SenderID:     senderID,
		SenderType:   senderType,
		ReceiverID:   req.FriendID,
		ReceiverType: req.FriendType,
		Message:      req.Message,
		Status:       0, // 等待验证
	}

	_, err = o.Insert(friendRequest)
	if err != nil {
		return err
	}

	return nil
}

// HandleFriendRequest 处理好友请求
func (s *FriendService) HandleFriendRequest(requestID int64, receiverID int64, receiverType string, req *dto.HandleFriendRequestRequest) error {
	o := orm.NewOrm()

	// 获取好友请求信息
	friendRequest := models.ChatFriendRequest{ID: requestID}
	err := o.Read(&friendRequest)
	if err != nil {
		return err
	}

	// 验证请求是否发给自己
	if friendRequest.ReceiverID != receiverID || friendRequest.ReceiverType != receiverType {
		return errors.New("无权处理该好友请求")
	}

	// 验证请求状态
	if friendRequest.Status != 0 {
		return errors.New("该请求已被处理")
	}

	// 根据操作处理请求
	if req.Action == "accept" { // 接受好友请求
		// TODO: 需要实现或导入NewSessionService
		// 暂时使用模拟会话数据
		sessionID := int64(1)
		
		// 使用DoTx开始事务
		err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
			// 更新请求状态
			friendRequest.Status = 1 // 已接受
			friendRequest.UpdatedAt = time.Now()
			_, err := txOrm.Update(&friendRequest, "Status", "UpdatedAt")
			if err != nil {
				return err
			}

			// 创建一个新的会话
			// 注释掉会话创建代码，使用模拟数据
			// sessionService := NewSessionService()
			// sessionDTO, err := sessionService.CreateSession(receiverID, receiverType, "private", "", "")
			// if err != nil {
			// 	return err
			// }

			// 添加好友关系（双向）
			friend1 := &models.ChatFriend{
				UserID:     receiverID,
				UserType:   receiverType,
				FriendID:   friendRequest.SenderID,
				FriendType: friendRequest.SenderType,
				Status:     0, // 正常
				SessionID:  sessionID,
			}

			_, err = txOrm.Insert(friend1)
			if err != nil {
				return err
			}

			friend2 := &models.ChatFriend{
				UserID:     friendRequest.SenderID,
				UserType:   friendRequest.SenderType,
				FriendID:   receiverID,
				FriendType: receiverType,
				Status:     0, // 正常
				SessionID:  sessionID,
			}

			_, err = txOrm.Insert(friend2)
			if err != nil {
				return err
			}
			
			return nil
		})
		
		if err != nil {
			return err
		}
	} else { // 拒绝好友请求
		friendRequest.Status = 2 // 已拒绝
		friendRequest.UpdatedAt = time.Now()
		_, err = o.Update(&friendRequest, "Status", "UpdatedAt")
		if err != nil {
			return err
		}
	}

	return nil
}

// GetFriendList 获取好友列表
func (s *FriendService) GetFriendList(userID int64, userType string, page, pageSize int) (*dto.FriendListResponse, error) {
	o := orm.NewOrm()

	var friends []models.ChatFriend
	_, err := o.QueryTable(new(models.ChatFriend)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Limit(pageSize, (page-1)*pageSize).
		OrderBy("-status", "id").
		All(&friends)

	if err != nil {
		return nil, err
	}

	// 查询总数
	count, err := o.QueryTable(new(models.ChatFriend)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Count()
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	var friendDTOs []dto.FriendDTO
	for _, friend := range friends {
		friendDTO := dto.FriendDTO{
			ID:          friend.ID,
			UserID:      friend.UserID,
			UserType:    friend.UserType,
			FriendID:    friend.FriendID,
			FriendType:  friend.FriendType,
			Remark:      friend.Remark,
			Status:      friend.Status,
			SessionID:   friend.SessionID,
			CreatedAt:   friend.CreatedAt,
		}

		// 获取好友基本信息（姓名和头像）
		if friend.FriendType == "user" {
			// 直接查询用户表获取基本信息
			var user userModels.User
			err := o.QueryTable(new(userModels.User)).Filter("id", friend.FriendID).One(&user)
			if err == nil {
				friendDTO.FriendName = user.Username
				friendDTO.FriendAvatar = user.Avatar
			} else if err != orm.ErrNoRows {
				logs.Error("获取好友信息失败: %v", err)
			}
		} else if friend.FriendType == "store" {
			// 如果有店铺表，可以类似查询店铺表
			// 这里简化处理，仅模拟填充数据
			friendDTO.FriendName = fmt.Sprintf("商家-%d", friend.FriendID)
			friendDTO.FriendAvatar = "/static/default_store_avatar.png"
		}

		friendDTOs = append(friendDTOs, friendDTO)
	}

	return &dto.FriendListResponse{
		Total: count,
		List:  friendDTOs,
	}, nil
}

// GetFriendRequestList 获取好友请求列表
func (s *FriendService) GetFriendRequestList(userID int64, userType string, status int, page, pageSize int) (*dto.FriendRequestListResponse, error) {
	o := orm.NewOrm()

	var requests []models.ChatFriendRequest
	query := o.QueryTable(new(models.ChatFriendRequest)).
		Filter("receiver_id", userID).
		Filter("receiver_type", userType)
		
	if status >= 0 {
		query = query.Filter("status", status)
	}
	
	_, err := query.Limit(pageSize, (page-1)*pageSize).
		OrderBy("-id").
		All(&requests)

	if err != nil {
		return nil, err
	}

	// 查询总数
	query = o.QueryTable(new(models.ChatFriendRequest)).
		Filter("receiver_id", userID).
		Filter("receiver_type", userType)
		
	if status >= 0 {
		query = query.Filter("status", status)
	}
	
	count, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	var requestDTOs []dto.FriendRequestDTO
	for _, request := range requests {
		requestDTO := dto.FriendRequestDTO{
			ID:           request.ID,
			SenderID:     request.SenderID,
			SenderType:   request.SenderType,
			ReceiverID:   request.ReceiverID,
			ReceiverType: request.ReceiverType,
			Message:      request.Message,
			Status:       request.Status,
			CreatedAt:    request.CreatedAt,
			UpdatedAt:    request.UpdatedAt,
		}

		// 获取发送者基本信息（姓名、头像等）
		logs.Debug("[GetFriendRequestList] 开始获取发送者信息: SenderID=%d, SenderType=%s", request.SenderID, request.SenderType)
		if request.SenderType == "user" {
			// 如果发送者是普通用户
			var user userModels.User
			err := o.QueryTable(new(userModels.User)).Filter("id", request.SenderID).One(&user)
			if err == nil {
				requestDTO.SenderName = user.Nickname
				if requestDTO.SenderName == "" {
					requestDTO.SenderName = user.Username
				}
				requestDTO.SenderAvatar = user.Avatar
				logs.Debug("[GetFriendRequestList] 成功获取用户信息: ID=%d, 名称=%s, 头像=%s", 
					user.ID, requestDTO.SenderName, requestDTO.SenderAvatar)
			} else {
				logs.Error("[GetFriendRequestList] 获取用户信息失败: ID=%d, 错误=%v", request.SenderID, err)
			}
		} else if request.SenderType == "merchant" {
			// 如果发送者是商户用户
			var merchant merchantModels.Merchant
			err := o.QueryTable(new(merchantModels.Merchant)).Filter("id", request.SenderID).One(&merchant)
			if err == nil {
				requestDTO.SenderName = merchant.Name
				if requestDTO.SenderName == "" {
					requestDTO.SenderName = merchant.Username
				}
				requestDTO.SenderAvatar = merchant.Logo
				logs.Debug("[GetFriendRequestList] 成功获取商户信息: ID=%d, 名称=%s, 头像=%s", 
					merchant.ID, requestDTO.SenderName, requestDTO.SenderAvatar)
			} else {
				logs.Error("[GetFriendRequestList] 获取商户信息失败: ID=%d, 错误=%v", request.SenderID, err)
			}
		}

		requestDTOs = append(requestDTOs, requestDTO)
	}

	return &dto.FriendRequestListResponse{
		Total: count,
		List:  requestDTOs,
	}, nil
}

// GetSentFriendRequestList 获取自己发送的好友请求列表
func (s *FriendService) GetSentFriendRequestList(userID int64, userType string, status int, page, pageSize int) (*dto.FriendRequestListResponse, error) {
	o := orm.NewOrm()

	var requests []models.ChatFriendRequest
	query := o.QueryTable(new(models.ChatFriendRequest)).
		Filter("sender_id", userID).
		Filter("sender_type", userType)
		
	if status >= 0 {
		query = query.Filter("status", status)
	}
	
	_, err := query.Limit(pageSize, (page-1)*pageSize).
		OrderBy("-id").
		All(&requests)

	if err != nil {
		return nil, err
	}

	// 查询总数
	query = o.QueryTable(new(models.ChatFriendRequest)).
		Filter("sender_id", userID).
		Filter("sender_type", userType)
		
	if status >= 0 {
		query = query.Filter("status", status)
	}
	
	count, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	var requestDTOs []dto.FriendRequestDTO
	for _, request := range requests {
		requestDTO := dto.FriendRequestDTO{
			ID:           request.ID,
			SenderID:     request.SenderID,
			SenderType:   request.SenderType,
			ReceiverID:   request.ReceiverID,
			ReceiverType: request.ReceiverType,
			Message:      request.Message,
			Status:       request.Status,
			CreatedAt:    request.CreatedAt,
			UpdatedAt:    request.UpdatedAt,
		}

		// 获取发送者（当前用户）的基本信息
		logs.Debug("[GetSentFriendRequestList] 开始获取发送者信息: UserID=%d, UserType=%s", userID, userType)
		if userType == "user" {
			// 如果发送者是普通用户
			var user userModels.User
			err := o.QueryTable(new(userModels.User)).Filter("id", userID).One(&user)
			if err == nil {
				requestDTO.SenderName = user.Nickname
				if requestDTO.SenderName == "" {
					requestDTO.SenderName = user.Username
				}
				requestDTO.SenderAvatar = user.Avatar
				logs.Debug("[GetSentFriendRequestList] 成功获取用户信息: ID=%d, 名称=%s, 头像=%s", 
					user.ID, requestDTO.SenderName, requestDTO.SenderAvatar)
			} else {
				logs.Error("[GetSentFriendRequestList] 获取用户信息失败: ID=%d, 错误=%v", userID, err)
			}
		} else if userType == "merchant" {
			// 如果发送者是商户用户
			var merchant merchantModels.Merchant
			err := o.QueryTable(new(merchantModels.Merchant)).Filter("id", userID).One(&merchant)
			if err == nil {
				requestDTO.SenderName = merchant.Name
				if requestDTO.SenderName == "" {
					requestDTO.SenderName = merchant.Username
				}
				requestDTO.SenderAvatar = merchant.Logo
				logs.Debug("[GetSentFriendRequestList] 成功获取商户信息: ID=%d, 名称=%s, 头像=%s", 
					merchant.ID, requestDTO.SenderName, requestDTO.SenderAvatar)
			} else {
				logs.Error("[GetSentFriendRequestList] 获取商户信息失败: ID=%d, 错误=%v", userID, err)
			}
		}

		requestDTOs = append(requestDTOs, requestDTO)
	}

	return &dto.FriendRequestListResponse{
		Total: count,
		List:  requestDTOs,
	}, nil
}

// DeleteFriend 删除好友
func (s *FriendService) DeleteFriend(userID int64, userType string, friendID int64, friendType string) error {
	o := orm.NewOrm()

	// 使用普通查询先检查好友关系存在与否
	var friend models.ChatFriend
	err := o.QueryTable(new(models.ChatFriend)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Filter("friend_id", friendID).
		Filter("friend_type", friendType).
		One(&friend)

	if err != nil {
		if err == orm.ErrNoRows {
			return errors.New("好友关系不存在")
		}
		return err
	}
	
	// 获取会话ID - 由于会话服务尚未实现，暂时不使用此ID
	// sessionID := friend.SessionID

	// 使用DoTx开始事务处理
	err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		// 删除双向好友关系
		_, err := txOrm.QueryTable(new(models.ChatFriend)).
			Filter("user_id", userID).
			Filter("user_type", userType).
			Filter("friend_id", friendID).
			Filter("friend_type", friendType).
			Delete()
			
		if err != nil {
			return err
		}

		_, err = txOrm.QueryTable(new(models.ChatFriend)).
			Filter("user_id", friendID).
			Filter("user_type", friendType).
			Filter("friend_id", userID).
			Filter("friend_type", userType).
			Delete()
			
		if err != nil {
			return err
		}

		// 检查是否还有其他人使用该会话
		// 注释掉会话计数和关闭代码，因为NewSessionService未定义
		// count, err := txOrm.QueryTable(new(models.ChatFriend)).
		// 	Filter("session_id", sessionID).
		// 	Count()
		// 	
		// if err != nil {
		// 	return err
		// }

		// 如果没有其他人使用该会话，则关闭会话
		// if count == 0 {
		// 	// TODO: 需要实现或导入NewSessionService
		// 	// sessionService := NewSessionService()
		// 	// err = sessionService.CloseSession(sessionID)
		// 	// if err != nil {
		// 	// 	return err
		// 	// }
		// }

		return nil
	})
	
	if err != nil {
		return err
	}

	return nil
}

// UpdateFriend 更新好友信息
func (s *FriendService) UpdateFriend(userID int64, userType string, friendID int64, friendType string, req *dto.UpdateFriendRequest) error {
	o := orm.NewOrm()

	var friend models.ChatFriend
	err := o.QueryTable(new(models.ChatFriend)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Filter("friend_id", friendID).
		Filter("friend_type", friendType).
		One(&friend)

	if err != nil {
		if err == orm.ErrNoRows {
			return errors.New("好友关系不存在")
		}
		return err
	}

	// 更新备注
	if req.Remark != "" {
		friend.Remark = req.Remark
	}

	// 更新状态
	if req.Status >= 0 && req.Status <= 2 {
		friend.Status = req.Status
	}

	friend.UpdatedAt = time.Now()
	_, err = o.Update(&friend, "Remark", "Status", "UpdatedAt")
	if err != nil {
		return err
	}

	return nil
}

// GetFriendByID 通过好友ID获取好友关系
func (s *FriendService) GetFriendByID(userID int64, userType string, friendID int64, friendType string) (*dto.FriendDTO, error) {
	o := orm.NewOrm()

	var friend models.ChatFriend
	err := o.QueryTable(new(models.ChatFriend)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Filter("friend_id", friendID).
		Filter("friend_type", friendType).
		One(&friend)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("好友关系不存在")
		}
		return nil, err
	}

	// 转换为DTO
	friendDTO := &dto.FriendDTO{
		ID:          friend.ID,
		UserID:      friend.UserID,
		UserType:    friend.UserType,
		FriendID:    friend.FriendID,
		FriendType:  friend.FriendType,
		Remark:      friend.Remark,
		Status:      friend.Status,
		SessionID:   friend.SessionID,
		CreatedAt:   friend.CreatedAt,
	}

	// 这里可以添加获取好友基本信息的代码，如姓名、头像等
	// 由于涉及其他模块的用户服务，此处简化处理

	return friendDTO, nil
}

// IsFriend 检查是否是好友关系
func (s *FriendService) IsFriend(userID int64, userType string, friendID int64, friendType string) (bool, error) {
	o := orm.NewOrm()

	count, err := o.QueryTable(new(models.ChatFriend)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Filter("friend_id", friendID).
		Filter("friend_type", friendType).
		Count()

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// SearchUsers 搜索用户（支持用户名和手机号模糊搜索）
func (s *FriendService) SearchUsers(currentUserID int64, currentUserType string, req *dto.UserSearchRequest) (*dto.UserSearchResponse, error) {
	o := orm.NewOrm()

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大页面大小
	}

	offset := (req.Page - 1) * req.PageSize
	var results []dto.UserSearchDTO
	totalCount := int64(0)

	// 搜索普通用户
	var users []userModels.User
	userCond := orm.NewCondition()
	userCond = userCond.And("status", 1). // 只搜索正常状态的用户
		AndCond(orm.NewCondition().Or("username__icontains", req.Keyword).Or("mobile__icontains", req.Keyword))
	userQuery := o.QueryTable(new(userModels.User)).SetCond(userCond)

	userCount, err := userQuery.Count()
	if err != nil {
		return nil, fmt.Errorf("查询用户数量失败: %v", err)
	}

	_, err = userQuery.Limit(req.PageSize, offset).All(&users)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	// 转换用户数据
	for _, user := range users {
		// 检查是否已经是好友
		isFriend, _ := s.IsFriend(currentUserID, currentUserType, user.ID, "user")
		
		userDTO := dto.UserSearchDTO{
			ID:       user.ID,
			Username: user.Username,
			Nickname: user.Nickname,
			Avatar:   user.Avatar,
			Mobile:   user.Mobile,
			UserType: "user",
			IsFriend: isFriend,
		}
		results = append(results, userDTO)
	}

	totalCount += userCount

	// 搜索商户用户
	var merchants []merchantModels.Merchant
	merchantCond := orm.NewCondition()
	merchantCond = merchantCond.And("status", 1). // 只搜索正常状态的商户
		AndCond(orm.NewCondition().Or("username__icontains", req.Keyword).Or("contact_mobile__icontains", req.Keyword))
	merchantQuery := o.QueryTable(new(merchantModels.Merchant)).SetCond(merchantCond)

	merchantCount, err := merchantQuery.Count()
	if err != nil {
		return nil, fmt.Errorf("查询商户数量失败: %v", err)
	}

	// 如果还有剩余的分页空间，继续查询商户
	remainingLimit := req.PageSize - len(results)
	if remainingLimit > 0 {
		merchantOffset := 0
		if offset > int(userCount) {
			merchantOffset = offset - int(userCount)
		}

		_, err = merchantQuery.Limit(remainingLimit, merchantOffset).All(&merchants)
		if err != nil {
			return nil, fmt.Errorf("查询商户失败: %v", err)
		}

		// 转换商户数据
		for _, merchant := range merchants {
			// 检查是否已经是好友
			isFriend, _ := s.IsFriend(currentUserID, currentUserType, merchant.ID, "merchant")
			
			merchantDTO := dto.UserSearchDTO{
				ID:       merchant.ID,
				Username: merchant.Username,
				Nickname: merchant.Name, // 商户使用店铺名称作为昵称
				Avatar:   merchant.Logo, // 商户使用Logo作为头像
				Mobile:   merchant.ContactMobile,
				UserType: "merchant",
				IsFriend: isFriend,
			}
			results = append(results, merchantDTO)
		}
	}

	totalCount += merchantCount

	return &dto.UserSearchResponse{
		Total: totalCount,
		List:  results,
	}, nil
}
