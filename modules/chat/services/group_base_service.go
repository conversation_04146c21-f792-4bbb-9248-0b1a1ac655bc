/**
 * group_base_service.go
 * 群聊基础服务
 *
 * 该文件提供群聊的基本操作服务，包括创建、查询、更新和删除群聊
 */

package services

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
)

// GroupService 群聊服务
type GroupService struct{}

// NewGroupService 创建群聊服务实例
func NewGroupService() *GroupService {
	return &GroupService{}
}

// CreateGroup 创建群聊
func (s *GroupService) CreateGroup(creatorID int64, creatorType string, req *dto.CreateGroupRequest) (*dto.GroupDTO, error) {
	o := orm.NewOrm()
	var group *models.ChatGroup

	err := o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		// 先创建群组以获取群组ID
		group = &models.ChatGroup{
			Name:        req.Name,
			Description: req.Description,
			Avatar:      req.Avatar,
			CreatorID:   creatorID,
			CreatorType: creatorType,
			SessionID:   0, // 先设为0，后面更新
			MemberCount: 1,
			Status:      0, // 正常
			MaxMembers:  req.MaxMembers,
		}
		
		// 如果未指定最大成员数，设置默认值
		if group.MaxMembers <= 0 {
			group.MaxMembers = 200
		}

		groupID, err := txOrm.Insert(group)
		if err != nil {
			return err
		}
		group.ID = groupID

		// 创建群聊会话
		// 注意：这里需要在事务外创建会话，因为ChatService可能有自己的事务管理
		// 为了简化，我们先在这里直接创建会话记录
		session := &models.ChatSession{
			Type:         models.SessionTypeGroup,
			CreatorID:    creatorID,
			CreatorType:  creatorType,
			ReceiverID:   groupID, // 群聊ID作为接收者ID
			ReceiverType: "group",
			Status:       0, // 正常状态
		}

		sessionID, err := txOrm.Insert(session)
		if err != nil {
			return err
		}

		// 更新群组的会话ID
		group.SessionID = sessionID
		_, err = txOrm.Update(group, "SessionID")
		if err != nil {
			return err
		}

		// 添加创建者为群成员（群主）
		member := &models.ChatGroupMember{
			GroupID:  groupID,
			UserID:   creatorID,
			UserType: creatorType,
			Role:     2, // 群主
			Status:   0, // 正常
		}

		_, err = txOrm.Insert(member)
		if err != nil {
			return err
		}

		// 添加初始成员
		if len(req.InitialMembers) > 0 {
			// 这里简化处理，假设初始成员都是相同类型
			// TODO: 需要修改BatchAddMemberInternal方法签名以支持TxOrmer
			// 暂时跳过添加初始成员的功能
			// memberService := NewGroupMemberService()
			// err = memberService.BatchAddMemberInternal(txOrm, groupID, req.InitialMembers, creatorType)
			// if err != nil {
			// 	return err
			// }
		}

		// 更新群成员数量
		// 在实现添加初始成员后再更新计数
		// group.MemberCount = len(req.InitialMembers) + 1
		// _, err = txOrm.Update(group, "MemberCount")
		// if err != nil {
		// 	return err
		// }
		return nil
	})
	if err != nil {
		return nil, err
	}

	return &dto.GroupDTO{
		ID:          group.ID,
		Name:        group.Name,
		Description: group.Description,
		Avatar:      group.Avatar,
		CreatorID:   group.CreatorID,
		CreatorType: group.CreatorType,
		MemberCount: group.MemberCount,
		Status:      group.Status,
		MaxMembers:  group.MaxMembers,
		CreatedAt:   group.CreatedAt,
		UpdatedAt:   group.UpdatedAt,
	}, nil
}

// GetGroupByID 通过ID获取群聊信息
func (s *GroupService) GetGroupByID(groupID int64) (*dto.GroupDTO, error) {
	o := orm.NewOrm()
	group := models.ChatGroup{ID: groupID}
	err := o.Read(&group)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("群聊不存在")
		}
		return nil, err
	}

	return &dto.GroupDTO{
		ID:          group.ID,
		Name:        group.Name,
		Description: group.Description,
		Avatar:      group.Avatar,
		CreatorID:   group.CreatorID,
		CreatorType: group.CreatorType,
		MemberCount: group.MemberCount,
		Status:      group.Status,
		MaxMembers:  group.MaxMembers,
		CreatedAt:   group.CreatedAt,
		UpdatedAt:   group.UpdatedAt,
	}, nil
}

// UpdateGroup 更新群聊信息
func (s *GroupService) UpdateGroup(groupID int64, userID int64, userType string, req *dto.UpdateGroupRequest) (*dto.GroupDTO, error) {
	o := orm.NewOrm()

	// 验证操作权限（只有群主或管理员可以更新群信息）
	memberService := NewGroupMemberService()
	member, err := memberService.GetMemberByUserID(groupID, userID, userType)
	if err != nil {
		return nil, err
	}

	if member.Role < 1 { // 非管理员
		return nil, errors.New("无权更新群聊信息")
	}

	group := models.ChatGroup{ID: groupID}
	err = o.Read(&group)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != "" {
		group.Name = req.Name
	}
	
	group.Description = req.Description
	
	if req.Avatar != "" {
		group.Avatar = req.Avatar
	}
	
	if req.MaxMembers > 0 && member.Role == 2 { // 只有群主可以修改最大成员数
		group.MaxMembers = req.MaxMembers
	}

	group.UpdatedAt = time.Now()
	err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		_, err := txOrm.Update(&group)
		return err
	})
	if err != nil {
		return nil, err
	}

	return &dto.GroupDTO{
		ID:          group.ID,
		Name:        group.Name,
		Description: group.Description,
		Avatar:      group.Avatar,
		CreatorID:   group.CreatorID,
		CreatorType: group.CreatorType,
		MemberCount: group.MemberCount,
		Status:      group.Status,
		MaxMembers:  group.MaxMembers,
		CreatedAt:   group.CreatedAt,
		UpdatedAt:   group.UpdatedAt,
	}, nil
}

// DismissGroup 解散群聊
func (s *GroupService) DismissGroup(groupID int64, userID int64, userType string) error {
	o := orm.NewOrm()
	
	// 验证操作权限（只有群主可以解散群）
	memberService := NewGroupMemberService()
	member, err := memberService.GetMemberByUserID(groupID, userID, userType)
	if err != nil {
		return err
	}

	if member.Role != 2 { // 非群主
		return errors.New("只有群主可以解散群聊")
	}

	group := models.ChatGroup{ID: groupID}
	err = o.Read(&group)
	if err != nil {
		return err
	}

	// 标记群聊为已解散
	group.Status = 1 // 已解散
	group.UpdatedAt = time.Now()
	err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		_, err := txOrm.Update(&group, "Status", "UpdatedAt")
		return err
	})
	if err != nil {
		return err
	}

	// TODO: 需要实现或导入SessionService
	// 暂时跳过关闭关联会话的功能
	// 关闭关联的会话
	// sessionService := NewSessionService()
	// err = sessionService.CloseSession(group.SessionID)
	// if err != nil {
	// 	return err
	// }

	return nil
}

// GetUserGroups 获取用户加入的群聊列表
func (s *GroupService) GetUserGroups(userID int64, userType string, page, pageSize int) (*dto.GroupListResponse, error) {
	o := orm.NewOrm()

	var members []models.ChatGroupMember
	_, err := o.QueryTable(new(models.ChatGroupMember)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Limit(pageSize, (page-1)*pageSize).
		OrderBy("-id").
		All(&members)

	if err != nil {
		return nil, err
	}

	// 查询总数
	count, err := o.QueryTable(new(models.ChatGroupMember)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		Count()
	if err != nil {
		return nil, err
	}

	// 获取群聊详情
	var groupDTOs []dto.GroupDTO
	for _, member := range members {
		group := models.ChatGroup{ID: member.GroupID}
		err = o.Read(&group)
		if err != nil {
			continue // 跳过无法读取的群组
		}

		// 只返回正常状态的群组
		if group.Status == 0 {
			groupDTOs = append(groupDTOs, dto.GroupDTO{
				ID:          group.ID,
				Name:        group.Name,
				Description: group.Description,
				Avatar:      group.Avatar,
				CreatorID:   group.CreatorID,
				CreatorType: group.CreatorType,
				MemberCount: group.MemberCount,
				Status:      group.Status,
				MaxMembers:  group.MaxMembers,
				CreatedAt:   group.CreatedAt,
				UpdatedAt:   group.UpdatedAt,
			})
		}
	}

	return &dto.GroupListResponse{
		Total: count,
		List:  groupDTOs,
	}, nil
}

// GetGroupSession 获取群聊关联的会话ID
func (s *GroupService) GetGroupSession(groupID int64) (int64, error) {
	o := orm.NewOrm()
	group := models.ChatGroup{ID: groupID}
	err := o.Read(&group)
	if err != nil {
		return 0, err
	}
	return group.SessionID, nil
}
