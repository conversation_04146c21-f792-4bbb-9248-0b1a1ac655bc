/**
 * group_member_service.go
 * 群聊成员管理服务
 *
 * 该文件提供群聊成员管理的相关功能，包括添加、移除、更新成员信息等操作
 */

package services

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
)

// GroupMemberService 群聊成员服务
type GroupMemberService struct{}

// NewGroupMemberService 创建群聊成员服务实例
func NewGroupMemberService() *GroupMemberService {
	return &GroupMemberService{}
}

// AddMember 添加群成员
func (s *GroupMemberService) AddMember(groupID int64, operatorID int64, operatorType string, req *dto.AddGroupMemberRequest) error {
	o := orm.NewOrm()
	
	// 验证操作权限
	member, err := s.GetMemberByUserID(groupID, operatorID, operatorType)
	if err != nil {
		return err
	}

	if member.Role < 1 { // 非管理员
		return errors.New("无权添加群成员")
	}

	// 验证群聊状态
	group := models.ChatGroup{ID: groupID}
	err = o.Read(&group)
	if err != nil {
		return err
	}

	if group.Status != 0 {
		return errors.New("群聊状态异常")
	}

	// 验证成员数量上限
	if group.MemberCount >= group.MaxMembers {
		return errors.New("群聊已达到最大成员数量")
	}

	// 使用DoTx处理事务
	err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		// 添加成员
		err := s.BatchAddMemberInternal(txOrm, groupID, req.UserIDs, req.UserType)
		if err != nil {
			return err
		}

		// 更新群成员计数
		group.MemberCount += len(req.UserIDs)
		_, err = txOrm.Update(&group, "MemberCount", "UpdatedAt")
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// BatchAddMemberInternal 批量添加成员（内部方法，供其他服务调用）
func (s *GroupMemberService) BatchAddMemberInternal(o orm.TxOrmer, groupID int64, userIDs []int64, userType string) error {
	// 检查用户是否已经是群成员
	for _, userID := range userIDs {
		count, err := o.QueryTable(new(models.ChatGroupMember)).
			Filter("group_id", groupID).
			Filter("user_id", userID).
			Filter("user_type", userType).
			Count()
		
		if err != nil {
			return err
		}
		
		if count > 0 {
			continue // 已是成员，跳过
		}
		
		// 添加新成员
		member := &models.ChatGroupMember{
			GroupID:  groupID,
			UserID:   userID,
			UserType: userType,
			Role:     0, // 普通成员
			Status:   0, // 正常
		}

		_, err = o.Insert(member)
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveMember 移除群成员
func (s *GroupMemberService) RemoveMember(groupID, operatorID, targetID int64, operatorType, targetType string) error {
	o := orm.NewOrm()
	
	// 验证操作权限
	operatorMember, err := s.GetMemberByUserID(groupID, operatorID, operatorType)
	if err != nil {
		return err
	}

	targetMember, err := s.GetMemberByUserID(groupID, targetID, targetType)
	if err != nil {
		return err
	}

	// 权限检查：
	// 1. 群主可以移除任何人
	// 2. 管理员可以移除普通成员
	// 3. 自己可以退出群聊
	if operatorMember.Role < 1 && operatorID != targetID { // 非管理员且不是自己
		return errors.New("无权移除群成员")
	}

	if operatorMember.Role == 1 && targetMember.Role >= 1 { // 管理员无法移除其他管理员或群主
		return errors.New("无权移除该成员")
	}

	// 群主不能被移除，只能转让群主后退出
	if targetMember.Role == 2 && operatorID != targetID {
		return errors.New("无法移除群主")
	}

	// 如果是群主退出，需先转让群主
	if targetMember.Role == 2 && operatorID == targetID {
		return errors.New("群主需先转让群主身份再退出")
	}

	// 使用DoTx处理事务
	err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		// 删除成员
		_, err := txOrm.Delete(&models.ChatGroupMember{
			GroupID:  groupID,
			UserID:   targetID,
			UserType: targetType,
		})
		
		if err != nil {
			return err
		}

		// 更新群成员计数
		group := models.ChatGroup{ID: groupID}
		err = txOrm.Read(&group)
		if err != nil {
			return err
		}

		group.MemberCount--
		if group.MemberCount < 0 {
			group.MemberCount = 0
		}

		_, err = txOrm.Update(&group, "MemberCount", "UpdatedAt")
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// UpdateMember 更新群成员信息
func (s *GroupMemberService) UpdateMember(groupID, operatorID, targetID int64, operatorType, targetType string, req *dto.UpdateGroupMemberRequest) error {
	o := orm.NewOrm()
	
	// 验证操作权限
	operatorMember, err := s.GetMemberByUserID(groupID, operatorID, operatorType)
	if err != nil {
		return err
	}

	targetMember, err := s.GetMemberByUserID(groupID, targetID, targetType)
	if err != nil {
		return err
	}

	// 获取群聊信息确认创建者
	group := models.ChatGroup{ID: groupID}
	err = o.Read(&group)
	if err != nil {
		return err
	}

	// 修改自己的昵称
	if operatorID == targetID && req.Nickname != "" {
		targetMember.Nickname = req.Nickname
		_, err = o.Update(targetMember, "Nickname", "UpdatedAt")
		if err != nil {
			return err
		}
		return nil
	}

	// 设置角色，只有群主可以设置
	if req.Role >= 0 && req.Role <= 2 {
		// 只有群主可以设置角色
		if operatorMember.Role != 2 {
			return errors.New("只有群主可以设置角色")
		}

		// 不能将其他人设为群主
		if req.Role == 2 && group.CreatorID != targetID {
			return errors.New("不能将他人设为群主")
		}

		targetMember.Role = req.Role
	}

	// 设置状态（禁言或恢复），管理员及以上可以设置
	if req.Status >= 0 && req.Status <= 1 {
		// 验证操作权限
		if operatorMember.Role < 1 { // 非管理员
			return errors.New("无权设置成员状态")
		}

		// 群主和管理员不能被禁言
		if targetMember.Role >= 1 && req.Status == 1 {
			return errors.New("不能禁言群主或管理员")
		}

		targetMember.Status = req.Status
	}

	// 更新成员信息
	_, err = o.Update(targetMember, "Role", "Status", "Nickname", "UpdatedAt")
	if err != nil {
		return err
	}

	return nil
}

// TransferOwner 转让群主
func (s *GroupMemberService) TransferOwner(groupID, currentOwnerID, newOwnerID int64, userType string) error {
	o := orm.NewOrm()
	
	// 验证当前用户是否为群主
	currentOwner, err := s.GetMemberByUserID(groupID, currentOwnerID, userType)
	if err != nil {
		return err
	}

	if currentOwner.Role != 2 {
		return errors.New("只有群主可以转让群主身份")
	}

	// 验证新群主是否为群成员
	newOwner, err := s.GetMemberByUserID(groupID, newOwnerID, userType)
	if err != nil {
		return errors.New("新群主不是群成员")
	}

	// 使用DoTx处理事务
	err = o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		// 更新原群主为管理员
		currentOwner.Role = 1
		_, err := txOrm.Update(currentOwner, "Role", "UpdatedAt")
		if err != nil {
			return err
		}

		// 更新新群主
		newOwner.Role = 2
		_, err = txOrm.Update(newOwner, "Role", "UpdatedAt")
		if err != nil {
			return err
		}

		// 更新群组信息
		group := models.ChatGroup{ID: groupID}
		err = txOrm.Read(&group)
		if err != nil {
			return err
		}

		group.CreatorID = newOwnerID
		group.CreatorType = userType
		_, err = txOrm.Update(&group, "CreatorID", "CreatorType", "UpdatedAt")
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	return nil
}

// GetMembers 获取群成员列表
func (s *GroupMemberService) GetMembers(groupID int64, page, pageSize int) (*dto.GroupMemberListResponse, error) {
	o := orm.NewOrm()

	var members []models.ChatGroupMember
	_, err := o.QueryTable(new(models.ChatGroupMember)).
		Filter("group_id", groupID).
		Limit(pageSize, (page-1)*pageSize).
		OrderBy("-role", "id").
		All(&members)

	if err != nil {
		return nil, err
	}

	// 查询总数
	count, err := o.QueryTable(new(models.ChatGroupMember)).
		Filter("group_id", groupID).
		Count()
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	var memberDTOs []dto.GroupMemberDTO
	for _, member := range members {
		memberDTO := dto.GroupMemberDTO{
			ID:         member.ID,
			GroupID:    member.GroupID,
			UserID:     member.UserID,
			UserType:   member.UserType,
			Nickname:   member.Nickname,
			Role:       member.Role,
			Status:     member.Status,
			JoinedAt:   member.JoinedAt,
			LastReadID: member.LastReadID,
		}

		// 这里可以添加获取用户基本信息的代码，如姓名、头像等
		// 由于涉及其他模块的用户服务，此处简化处理

		memberDTOs = append(memberDTOs, memberDTO)
	}

	return &dto.GroupMemberListResponse{
		Total: count,
		List:  memberDTOs,
	}, nil
}

// GetMemberByUserID 通过用户ID获取群成员信息
func (s *GroupMemberService) GetMemberByUserID(groupID, userID int64, userType string) (*models.ChatGroupMember, error) {
	o := orm.NewOrm()
	
	var member models.ChatGroupMember
	err := o.QueryTable(new(models.ChatGroupMember)).
		Filter("group_id", groupID).
		Filter("user_id", userID).
		Filter("user_type", userType).
		One(&member)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("用户不是该群成员")
		}
		return nil, err
	}

	return &member, nil
}

// UpdateLastReadID 更新用户最后阅读的消息ID
func (s *GroupMemberService) UpdateLastReadID(groupID, userID int64, userType string, messageID int64) error {
	o := orm.NewOrm()
	
	member, err := s.GetMemberByUserID(groupID, userID, userType)
	if err != nil {
		return err
	}

	// 只有更新为更大的消息ID才有效
	if messageID > member.LastReadID {
		member.LastReadID = messageID
		member.UpdatedAt = time.Now()
		_, err = o.Update(member, "LastReadID", "UpdatedAt")
		if err != nil {
			return err
		}
	}

	return nil
}
