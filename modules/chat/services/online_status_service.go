/**
 * online_status_service.go
 * 在线状态管理服务
 *
 * 该文件定义了客户在线状态管理的接口和实现，包括Redis缓存操作、状态更新和查询功能
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"o_mall_backend/utils/redis"

	"github.com/beego/beego/v2/core/logs"
)

// OnlineStatus 在线状态枚举
type OnlineStatus string

const (
	StatusActive  OnlineStatus = "active"  // 活跃在线（最近5分钟内有活动）
	StatusIdle    OnlineStatus = "idle"    // 空闲在线（连接但超过5分钟无活动）
	StatusOffline OnlineStatus = "offline" // 离线（WebSocket断开连接）
)

// UserOnlineInfo 用户在线信息
type UserOnlineInfo struct {
	UserID       int64        `json:"user_id"`       // 用户ID
	UserType     string       `json:"user_type"`     // 用户类型（user/merchant）
	Status       OnlineStatus `json:"status"`        // 在线状态
	LastActivity time.Time    `json:"last_activity"` // 最后活动时间
	SessionIDs   []int64      `json:"session_ids"`   // 相关会话ID列表
	ConnectedAt  time.Time    `json:"connected_at"`  // 连接时间
}

// OnlineStatusService 在线状态管理服务接口
type OnlineStatusService interface {
	// 状态更新
	SetUserOnline(ctx context.Context, userID int64, userType string, sessionIDs []int64) error
	SetUserOffline(ctx context.Context, userID int64, userType string) error
	UpdateUserActivity(ctx context.Context, userID int64, userType string) error

	// 状态查询
	GetUserOnlineInfo(ctx context.Context, userID int64, userType string) (*UserOnlineInfo, error)
	IsUserOnline(ctx context.Context, userID int64, userType string) (bool, error)
	GetUserStatus(ctx context.Context, userID int64, userType string) (OnlineStatus, error)

	// 批量查询
	GetMultipleUserStatus(ctx context.Context, users []struct {
		UserID   int64
		UserType string
	}) (map[string]OnlineStatus, error)

	// 会话相关
	GetOnlineUsersInSession(ctx context.Context, sessionID int64) ([]*UserOnlineInfo, error)
	AddUserToSession(ctx context.Context, userID int64, userType string, sessionID int64) error
	RemoveUserFromSession(ctx context.Context, userID int64, userType string, sessionID int64) error

	// 清理过期状态
	CleanupExpiredStatus(ctx context.Context) error
}

// OnlineStatusServiceImpl 在线状态管理服务实现
type OnlineStatusServiceImpl struct {
	// Redis键前缀
	userOnlineKeyPrefix   string
	sessionUsersKeyPrefix string

	// 状态过期时间配置
	activeTimeout  time.Duration // 活跃状态超时时间（5分钟）
	idleTimeout    time.Duration // 空闲状态超时时间（30分钟）
	offlineTimeout time.Duration // 离线清理超时时间（1小时）
}

// NewOnlineStatusService 创建在线状态管理服务实例
func NewOnlineStatusService() OnlineStatusService {
	return &OnlineStatusServiceImpl{
		userOnlineKeyPrefix:   "user_online:",
		sessionUsersKeyPrefix: "session_users:",
		activeTimeout:         5 * time.Minute,
		idleTimeout:           30 * time.Minute,
		offlineTimeout:        1 * time.Hour,
	}
}

// SetUserOnline 设置用户为在线状态
func (s *OnlineStatusServiceImpl) SetUserOnline(ctx context.Context, userID int64, userType string, sessionIDs []int64) error {
	now := time.Now()

	userInfo := &UserOnlineInfo{
		UserID:       userID,
		UserType:     userType,
		Status:       StatusActive,
		LastActivity: now,
		SessionIDs:   sessionIDs,
		ConnectedAt:  now,
	}

	// 序列化用户信息
	data, err := json.Marshal(userInfo)
	if err != nil {
		logs.Error("[OnlineStatusService] 序列化用户在线信息失败: %v", err)
		return err
	}

	// 保存到Redis
	key := s.getUserOnlineKey(userID, userType)
	err = redis.Set(key, string(data), s.idleTimeout)
	if err != nil {
		logs.Error("[OnlineStatusService] 保存用户在线状态到Redis失败: %v", err)
		return err
	}

	// 将用户添加到相关会话
	for _, sessionID := range sessionIDs {
		err = s.AddUserToSession(ctx, userID, userType, sessionID)
		if err != nil {
			logs.Error("[OnlineStatusService] 添加用户到会话失败: sessionID=%d, error=%v", sessionID, err)
		}
	}

	logs.Info("[OnlineStatusService] 用户 %d(%s) 已设置为在线状态", userID, userType)
	return nil
}

// SetUserOffline 设置用户为离线状态
func (s *OnlineStatusServiceImpl) SetUserOffline(ctx context.Context, userID int64, userType string) error {
	// 获取当前用户信息以获取会话列表
	userInfo, err := s.GetUserOnlineInfo(ctx, userID, userType)
	if err != nil {
		logs.Debug("[OnlineStatusService] 获取用户信息失败，可能用户已离线: %v", err)
	}

	// 从Redis删除用户在线状态
	key := s.getUserOnlineKey(userID, userType)
	_, err = redis.Del(key)
	if err != nil {
		logs.Error("[OnlineStatusService] 从Redis删除用户在线状态失败: %v", err)
		return err
	}

	// 从相关会话中移除用户
	if userInfo != nil {
		for _, sessionID := range userInfo.SessionIDs {
			err = s.RemoveUserFromSession(ctx, userID, userType, sessionID)
			if err != nil {
				logs.Error("[OnlineStatusService] 从会话移除用户失败: sessionID=%d, error=%v", sessionID, err)
			}
		}
	}

	logs.Info("[OnlineStatusService] 用户 %d(%s) 已设置为离线状态", userID, userType)
	return nil
}

// UpdateUserActivity 更新用户活动时间
func (s *OnlineStatusServiceImpl) UpdateUserActivity(ctx context.Context, userID int64, userType string) error {
	userInfo, err := s.GetUserOnlineInfo(ctx, userID, userType)
	if err != nil {
		logs.Debug("[OnlineStatusService] 用户不在线，无法更新活动时间: %v", err)
		return err
	}

	// 更新活动时间和状态
	userInfo.LastActivity = time.Now()
	userInfo.Status = StatusActive

	// 序列化并保存
	data, err := json.Marshal(userInfo)
	if err != nil {
		logs.Error("[OnlineStatusService] 序列化用户信息失败: %v", err)
		return err
	}

	key := s.getUserOnlineKey(userID, userType)
	err = redis.Set(key, string(data), s.idleTimeout)
	if err != nil {
		logs.Error("[OnlineStatusService] 更新用户活动时间失败: %v", err)
		return err
	}

	logs.Debug("[OnlineStatusService] 用户 %d(%s) 活动时间已更新", userID, userType)
	return nil
}

// GetUserOnlineInfo 获取用户在线信息
func (s *OnlineStatusServiceImpl) GetUserOnlineInfo(ctx context.Context, userID int64, userType string) (*UserOnlineInfo, error) {
	key := s.getUserOnlineKey(userID, userType)
	data, err := redis.Get(key)
	if err != nil {
		return nil, fmt.Errorf("用户不在线或获取失败: %v", err)
	}

	var userInfo UserOnlineInfo
	err = json.Unmarshal([]byte(data), &userInfo)
	if err != nil {
		logs.Error("[OnlineStatusService] 反序列化用户在线信息失败: %v", err)
		return nil, err
	}

	// 检查状态是否需要更新（从active变为idle）
	now := time.Now()
	if userInfo.Status == StatusActive && now.Sub(userInfo.LastActivity) > s.activeTimeout {
		userInfo.Status = StatusIdle
		// 更新Redis中的状态
		updatedData, _ := json.Marshal(userInfo)
		redis.Set(key, string(updatedData), s.idleTimeout)
	}

	return &userInfo, nil
}

// IsUserOnline 检查用户是否在线
func (s *OnlineStatusServiceImpl) IsUserOnline(ctx context.Context, userID int64, userType string) (bool, error) {
	_, err := s.GetUserOnlineInfo(ctx, userID, userType)
	return err == nil, nil
}

// GetUserStatus 获取用户状态
func (s *OnlineStatusServiceImpl) GetUserStatus(ctx context.Context, userID int64, userType string) (OnlineStatus, error) {
	userInfo, err := s.GetUserOnlineInfo(ctx, userID, userType)
	if err != nil {
		return StatusOffline, nil // 获取失败认为是离线
	}
	return userInfo.Status, nil
}

// GetMultipleUserStatus 批量获取用户状态
func (s *OnlineStatusServiceImpl) GetMultipleUserStatus(ctx context.Context, users []struct {
	UserID   int64
	UserType string
}) (map[string]OnlineStatus, error) {
	result := make(map[string]OnlineStatus)

	for _, user := range users {
		status, _ := s.GetUserStatus(ctx, user.UserID, user.UserType)
		key := fmt.Sprintf("%s:%d", user.UserType, user.UserID)
		result[key] = status
	}

	return result, nil
}

// AddUserToSession 将用户添加到会话
func (s *OnlineStatusServiceImpl) AddUserToSession(ctx context.Context, userID int64, userType string, sessionID int64) error {
	sessionKey := s.getSessionUsersKey(sessionID)
	userKey := fmt.Sprintf("%s:%d", userType, userID)

	// 使用Redis Set添加用户到会话
	_, err := redis.SAdd(sessionKey, userKey)
	if err != nil {
		logs.Error("[OnlineStatusService] 添加用户到会话失败: %v", err)
		return err
	}

	// 设置会话键的过期时间
	redis.Expire(sessionKey, s.idleTimeout)

	logs.Debug("[OnlineStatusService] 用户 %d(%s) 已添加到会话 %d", userID, userType, sessionID)
	return nil
}

// RemoveUserFromSession 从会话中移除用户
func (s *OnlineStatusServiceImpl) RemoveUserFromSession(ctx context.Context, userID int64, userType string, sessionID int64) error {
	sessionKey := s.getSessionUsersKey(sessionID)
	userKey := fmt.Sprintf("%s:%d", userType, userID)

	// 使用Redis Set移除用户
	_, err := redis.SRem(sessionKey, userKey)
	if err != nil {
		logs.Error("[OnlineStatusService] 从会话移除用户失败: %v", err)
		return err
	}

	logs.Debug("[OnlineStatusService] 用户 %d(%s) 已从会话 %d 移除", userID, userType, sessionID)
	return nil
}

// GetOnlineUsersInSession 获取会话中的在线用户
func (s *OnlineStatusServiceImpl) GetOnlineUsersInSession(ctx context.Context, sessionID int64) ([]*UserOnlineInfo, error) {
	sessionKey := s.getSessionUsersKey(sessionID)

	// 获取会话中的所有用户
	userKeys, err := redis.SMembers(sessionKey)
	if err != nil {
		logs.Error("[OnlineStatusService] 获取会话用户列表失败: %v", err)
		return nil, err
	}

	var onlineUsers []*UserOnlineInfo
	for _, userKey := range userKeys {
		// 解析用户键格式：userType:userID
		var userType string
		var userID int64
		_, err := fmt.Sscanf(userKey, "%[^:]:%d", &userType, &userID)
		if err != nil {
			logs.Error("[OnlineStatusService] 解析用户键失败: %s, error: %v", userKey, err)
			continue
		}

		// 获取用户在线信息
		userInfo, err := s.GetUserOnlineInfo(ctx, userID, userType)
		if err != nil {
			// 用户已离线，从会话中移除
			s.RemoveUserFromSession(ctx, userID, userType, sessionID)
			continue
		}

		onlineUsers = append(onlineUsers, userInfo)
	}

	return onlineUsers, nil
}

// CleanupExpiredStatus 清理过期状态
func (s *OnlineStatusServiceImpl) CleanupExpiredStatus(ctx context.Context) error {
	// 这个方法可以定期调用来清理过期的状态
	// Redis的TTL机制会自动清理过期的键，所以这里主要是日志记录
	logs.Info("[OnlineStatusService] 执行过期状态清理")
	return nil
}

// getUserOnlineKey 生成用户在线状态的Redis键
func (s *OnlineStatusServiceImpl) getUserOnlineKey(userID int64, userType string) string {
	return fmt.Sprintf("%s%s:%d", s.userOnlineKeyPrefix, userType, userID)
}

// getSessionUsersKey 生成会话用户列表的Redis键
func (s *OnlineStatusServiceImpl) getSessionUsersKey(sessionID int64) string {
	return fmt.Sprintf("%s%d", s.sessionUsersKeyPrefix, sessionID)
}
