/**
 * service_container.go
 * 服务容器用于存储全局服务实例
 *
 * 该文件定义了一个服务容器，用于在应用程序生命周期内保存服务实例，
 * 确保控制器可以在每次请求过程中获取相同的服务实例
 */

package services

import (
	"sync"
)

// ServiceContainer 全局服务容器
type ServiceContainer struct {
	chatService       ChatService
	wsManager         WebSocketManager
	friendService     *FriendService
	groupService      *GroupService
	groupMemberService *GroupMemberService
	blacklistService  *BlacklistService
	mutex             sync.RWMutex
}

// 全局服务容器实例
var container *ServiceContainer
var once sync.Once

// GetServiceContainer 获取单例服务容器
func GetServiceContainer() *ServiceContainer {
	once.Do(func() {
		container = &ServiceContainer{}
	})
	return container
}

// SetChatService 设置聊天服务
func (c *ServiceContainer) SetChatService(service ChatService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.chatService = service
}

// GetChatService 获取聊天服务
func (c *ServiceContainer) GetChatService() ChatService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.chatService
}

// SetWebSocketManager 设置WebSocket管理器
func (c *ServiceContainer) SetWebSocketManager(manager WebSocketManager) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.wsManager = manager
}

// GetWebSocketManager 获取WebSocket管理器
func (c *ServiceContainer) GetWebSocketManager() WebSocketManager {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.wsManager
}

// SetFriendService 设置好友服务
func (c *ServiceContainer) SetFriendService(service *FriendService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.friendService = service
}

// GetFriendService 获取好友服务
func (c *ServiceContainer) GetFriendService() *FriendService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.friendService
}

// SetGroupService 设置群组服务
func (c *ServiceContainer) SetGroupService(service *GroupService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.groupService = service
}

// GetGroupService 获取群组服务
func (c *ServiceContainer) GetGroupService() *GroupService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.groupService
}

// SetGroupMemberService 设置群组成员服务
func (c *ServiceContainer) SetGroupMemberService(service *GroupMemberService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.groupMemberService = service
}

// GetGroupMemberService 获取群组成员服务
func (c *ServiceContainer) GetGroupMemberService() *GroupMemberService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.groupMemberService
}

// SetBlacklistService 设置黑名单服务
func (c *ServiceContainer) SetBlacklistService(service *BlacklistService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.blacklistService = service
}

// GetBlacklistService 获取黑名单服务
func (c *ServiceContainer) GetBlacklistService() *BlacklistService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.blacklistService
}
