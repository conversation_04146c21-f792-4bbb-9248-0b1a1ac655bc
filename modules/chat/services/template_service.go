/**
 * template_service.go
 * 聊天模板服务接口
 *
 * 该文件定义了聊天模板管理的业务逻辑接口
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
	"o_mall_backend/modules/chat/repositories"
)

// TemplateService 模板服务接口
type TemplateService interface {
	// 模板管理
	CreateTemplate(ctx context.Context, req *dto.CreateTemplateRequest) (*models.ChatTemplate, error)
	UpdateTemplate(ctx context.Context, templateID int64, req *dto.UpdateTemplateRequest) (*models.ChatTemplate, error)
	DeleteTemplate(ctx context.Context, templateID int64) error
	GetTemplate(ctx context.Context, templateID int64) (*models.ChatTemplate, error)
	ListTemplates(ctx context.Context, req *dto.ListTemplatesRequest) ([]*models.ChatTemplate, int64, error)

	// 模板分类管理
	CreateCategory(ctx context.Context, req *dto.CreateCategoryRequest) (*models.ChatTemplateCategory, error)
	UpdateCategory(ctx context.Context, categoryID int64, req *dto.UpdateCategoryRequest) (*models.ChatTemplateCategory, error)
	DeleteCategory(ctx context.Context, categoryID int64) error
	ListCategories(ctx context.Context) ([]*models.ChatTemplateCategory, error)

	// 模板使用
	UseTemplate(ctx context.Context, templateID int64, userID int64, userType string, sessionID int64, messageID int64) error
	GetTemplatesByRole(ctx context.Context, userRole string, category string) ([]*models.ChatTemplate, error)
	GetPopularTemplates(ctx context.Context, userRole string, limit int) ([]*models.ChatTemplate, error)

	// 模板变量处理
	ProcessTemplateVariables(ctx context.Context, template *models.ChatTemplate, variables map[string]interface{}) (string, error)
	GetTemplateVariables(ctx context.Context, templateID int64) (map[string]interface{}, error)
}

// TemplateServiceImpl 模板服务实现
type TemplateServiceImpl struct {
	templateRepo repositories.TemplateRepository
}

// NewTemplateService 创建模板服务实例
func NewTemplateService(templateRepo repositories.TemplateRepository) TemplateService {
	return &TemplateServiceImpl{
		templateRepo: templateRepo,
	}
}

// CreateTemplate 创建模板
func (s *TemplateServiceImpl) CreateTemplate(ctx context.Context, req *dto.CreateTemplateRequest) (*models.ChatTemplate, error) {
	template := &models.ChatTemplate{
		Title:          req.Title,
		Content:        req.Content,
		Category:       req.Category,
		Type:           req.Type,
		ApplicableRole: req.ApplicableRole,
		CreatorID:      req.CreatorID,
		CreatorType:    req.CreatorType,
		SortOrder:      req.SortOrder,
		Status:         models.TemplateStatusActive,
		Tags:           req.Tags,
		Variables:      req.Variables,
	}

	id, err := s.templateRepo.CreateTemplate(ctx, template)
	if err != nil {
		return nil, err
	}

	return s.templateRepo.GetTemplateByID(ctx, id)
}

// UpdateTemplate 更新模板
func (s *TemplateServiceImpl) UpdateTemplate(ctx context.Context, templateID int64, req *dto.UpdateTemplateRequest) (*models.ChatTemplate, error) {
	template, err := s.templateRepo.GetTemplateByID(ctx, templateID)
	if err != nil {
		return nil, err
	}
	if template == nil {
		return nil, fmt.Errorf("模板不存在")
	}

	// 更新字段
	if req.Title != "" {
		template.Title = req.Title
	}
	if req.Content != "" {
		template.Content = req.Content
	}
	if req.Category != "" {
		template.Category = req.Category
	}
	if req.Type != "" {
		template.Type = req.Type
	}
	if req.ApplicableRole != "" {
		template.ApplicableRole = req.ApplicableRole
	}
	if req.SortOrder != 0 {
		template.SortOrder = req.SortOrder
	}
	if req.Status != 0 {
		template.Status = req.Status
	}
	if req.Tags != "" {
		template.Tags = req.Tags
	}
	if req.Variables != "" {
		template.Variables = req.Variables
	}

	err = s.templateRepo.UpdateTemplate(ctx, template)
	if err != nil {
		return nil, err
	}

	return template, nil
}

// CreateCategory 创建分类
func (s *TemplateServiceImpl) CreateCategory(ctx context.Context, req *dto.CreateCategoryRequest) (*models.ChatTemplateCategory, error) {
	category := &models.ChatTemplateCategory{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
		Status:      1, // 默认启用
	}

	id, err := s.templateRepo.CreateCategory(ctx, category)
	if err != nil {
		return nil, err
	}

	return s.templateRepo.GetCategoryByID(ctx, id)
}

// UpdateCategory 更新分类
func (s *TemplateServiceImpl) UpdateCategory(ctx context.Context, categoryID int64, req *dto.UpdateCategoryRequest) (*models.ChatTemplateCategory, error) {
	category, err := s.templateRepo.GetCategoryByID(ctx, categoryID)
	if err != nil {
		return nil, err
	}
	if category == nil {
		return nil, fmt.Errorf("分类不存在")
	}

	// 更新字段
	if req.Name != "" {
		category.Name = req.Name
	}
	if req.Description != "" {
		category.Description = req.Description
	}
	if req.Icon != "" {
		category.Icon = req.Icon
	}
	if req.SortOrder != 0 {
		category.SortOrder = req.SortOrder
	}
	if req.Status != 0 {
		category.Status = req.Status
	}

	err = s.templateRepo.UpdateCategory(ctx, category)
	if err != nil {
		return nil, err
	}

	return category, nil
}

// DeleteCategory 删除分类
func (s *TemplateServiceImpl) DeleteCategory(ctx context.Context, categoryID int64) error {
	return s.templateRepo.DeleteCategory(ctx, categoryID)
}

// ListCategories 获取分类列表
func (s *TemplateServiceImpl) ListCategories(ctx context.Context) ([]*models.ChatTemplateCategory, error) {
	return s.templateRepo.ListCategories(ctx)
}

// DeleteTemplate 删除模板
func (s *TemplateServiceImpl) DeleteTemplate(ctx context.Context, templateID int64) error {
	return s.templateRepo.DeleteTemplate(ctx, templateID)
}

// GetTemplate 获取模板
func (s *TemplateServiceImpl) GetTemplate(ctx context.Context, templateID int64) (*models.ChatTemplate, error) {
	return s.templateRepo.GetTemplateByID(ctx, templateID)
}

// ListTemplates 获取模板列表
func (s *TemplateServiceImpl) ListTemplates(ctx context.Context, req *dto.ListTemplatesRequest) ([]*models.ChatTemplate, int64, error) {
	return s.templateRepo.ListTemplates(ctx, req)
}

// GetTemplatesByRole 根据角色获取模板
func (s *TemplateServiceImpl) GetTemplatesByRole(ctx context.Context, userRole string, category string) ([]*models.ChatTemplate, error) {
	return s.templateRepo.GetTemplatesByRole(ctx, userRole, category)
}

// GetPopularTemplates 获取热门模板
func (s *TemplateServiceImpl) GetPopularTemplates(ctx context.Context, userRole string, limit int) ([]*models.ChatTemplate, error) {
	return s.templateRepo.GetPopularTemplates(ctx, userRole, limit)
}

// UseTemplate 使用模板
func (s *TemplateServiceImpl) UseTemplate(ctx context.Context, templateID int64, userID int64, userType string, sessionID int64, messageID int64) error {
	// 记录使用记录
	usage := &models.ChatTemplateUsage{
		TemplateID: templateID,
		UserID:     userID,
		UserType:   userType,
		SessionID:  sessionID,
		MessageID:  messageID,
	}

	err := s.templateRepo.CreateUsage(ctx, usage)
	if err != nil {
		return err
	}

	// 更新使用次数
	return s.templateRepo.IncrementUseCount(ctx, templateID)
}

// ProcessTemplateVariables 处理模板变量
func (s *TemplateServiceImpl) ProcessTemplateVariables(ctx context.Context, template *models.ChatTemplate, variables map[string]interface{}) (string, error) {
	content := template.Content

	// 简单的变量替换实现
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		content = strings.ReplaceAll(content, placeholder, fmt.Sprintf("%v", value))
	}

	return content, nil
}

// GetTemplateVariables 获取模板变量
func (s *TemplateServiceImpl) GetTemplateVariables(ctx context.Context, templateID int64) (map[string]interface{}, error) {
	template, err := s.templateRepo.GetTemplateByID(ctx, templateID)
	if err != nil {
		return nil, err
	}
	if template == nil {
		return nil, fmt.Errorf("模板不存在")
	}

	var variables map[string]interface{}
	if template.Variables != "" {
		err = json.Unmarshal([]byte(template.Variables), &variables)
		if err != nil {
			return nil, err
		}
	}

	return variables, nil
}
