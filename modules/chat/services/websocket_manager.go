/**
 * websocket_manager.go
 * WebSocket管理器
 *
 * 该文件实现了WebSocket连接的管理，包括连接的建立、消息的发送和广播、连接的关闭等功能
 */

package services

import (
	"context"
	"encoding/json"
	"strconv"
	"sync"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/gorilla/websocket"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
)

// WebSocketClient 表示一个WebSocket客户端连接
type WebSocketClient struct {
	ID         string           // 客户端唯一标识（通常是用户ID和用户类型的组合）
	UserID     int64            // 用户ID
	UserType   string           // 用户类型
	Connection *websocket.Conn  // WebSocket连接
	Send       chan []byte      // 发送消息的通道
	Manager    WebSocketManager // 关联的WebSocket管理器
	isClosed   bool
	closeMu    sync.Mutex
}

// CloseChannel 安全地关闭Send通道，防止多次关闭
func (c *WebSocketClient) CloseChannel() {
	c.closeMu.Lock()
	defer c.closeMu.Unlock()
	if !c.isClosed {
		close(c.Send)
		c.isClosed = true
	}
}

// WebSocketManager 管理所有WebSocket连接
type WebSocketManager interface {
	// 连接管理
	RegisterClient(client *WebSocketClient)
	UnregisterClient(client *WebSocketClient)
	GetClient(userID int64, userType string) *WebSocketClient
	GetClientsBySessionID(sessionID int64) []*WebSocketClient
	AddToSession(sessionID int64, client *WebSocketClient)

	// 消息发送
	SendToUser(userID int64, userType string, message *dto.WebSocketMessageDTO) error
	BroadcastToSession(sessionID int64, message *dto.WebSocketMessageDTO) error

	// 状态查询
	IsUserOnline(userID int64, userType string) bool
}

// WebSocketManagerImpl 实现了WebSocketManager接口
type WebSocketManagerImpl struct {
	clients    map[string]*WebSocketClient // 客户端连接映射
	clientsMux sync.RWMutex                // 客户端映射的读写锁

	sessionClients    map[int64][]string // 会话ID到客户端ID的映射
	sessionClientsMux sync.RWMutex       // 会话客户端映射的读写锁

	// 在线状态管理服务
	onlineStatusService OnlineStatusService
}

// NewWebSocketManager 创建一个新的WebSocket管理器
func NewWebSocketManager() WebSocketManager {
	manager := &WebSocketManagerImpl{
		clients:             make(map[string]*WebSocketClient),
		sessionClients:      make(map[int64][]string),
		onlineStatusService: NewOnlineStatusService(),
	}

	// 启动心跳检测
	go manager.heartbeatChecker()

	return manager
}

// RegisterClient 注册一个新的客户端连接
func (m *WebSocketManagerImpl) RegisterClient(client *WebSocketClient) {
	clientID := generateClientID(client.UserID, client.UserType)

	// 获取写锁
	m.clientsMux.Lock()

	// 如果之前有相同用户ID的连接，需要关闭旧连接
	if oldClient, exists := m.clients[clientID]; exists {
		logs.Info("用户 %s 的旧连接被新连接替代", clientID)
		// 关闭旧连接的发送通道
		oldClient.CloseChannel()
		// 从客户端映射中删除旧连接
		delete(m.clients, clientID)
	}

	// 注册新连接
	m.clients[clientID] = client
	m.clientsMux.Unlock()

	logs.Info("用户 %s 的WebSocket连接已建立", clientID)

	// 获取用户的会话列表并设置在线状态
	go m.handleUserOnline(client)

	// 自动订阅用户的所有群聊会话
	go m.autoSubscribeUserSessions(client)

	// 启动客户端的读写协程
	go client.readPump()
	go client.writePump()

	// 发送欢迎消息
	welcomeMsg := &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     "connected",
		Timestamp: time.Now().Unix(),
		Data:      map[string]interface{}{"message": "连接成功"},
	}

	msgBytes, _ := json.Marshal(welcomeMsg)
	client.Send <- msgBytes
}

// UnregisterClient 注销客户端连接
func (m *WebSocketManagerImpl) UnregisterClient(client *WebSocketClient) {
	clientID := generateClientID(client.UserID, client.UserType)

	m.clientsMux.Lock()
	if _, exists := m.clients[clientID]; exists {
		// 从客户端映射中删除
		delete(m.clients, clientID)
		// 关闭发送通道
		client.CloseChannel()
		logs.Info("用户 %s 的WebSocket连接已关闭", clientID)
	}
	m.clientsMux.Unlock()

	// 处理用户离线状态
	go m.handleUserOffline(client)

	// 从所有会话的客户端列表中移除
	m.sessionClientsMux.Lock()
	for sessionID, clients := range m.sessionClients {
		newClients := make([]string, 0)
		for _, id := range clients {
			if id != clientID {
				newClients = append(newClients, id)
			}
		}

		if len(newClients) > 0 {
			m.sessionClients[sessionID] = newClients
		} else {
			delete(m.sessionClients, sessionID)
		}
	}
	m.sessionClientsMux.Unlock()
}

// GetClient 获取指定用户的客户端连接
func (m *WebSocketManagerImpl) GetClient(userID int64, userType string) *WebSocketClient {
	clientID := generateClientID(userID, userType)

	m.clientsMux.RLock()
	client, exists := m.clients[clientID]
	m.clientsMux.RUnlock()

	if !exists {
		return nil
	}
	return client
}

// GetClientsBySessionID 获取会话中的所有客户端连接
func (m *WebSocketManagerImpl) GetClientsBySessionID(sessionID int64) []*WebSocketClient {
	m.sessionClientsMux.RLock()
	clientIDs, exists := m.sessionClients[sessionID]
	m.sessionClientsMux.RUnlock()

	if !exists {
		logs.Debug("[GetClientsBySessionID] 会话 %d 中没有注册的客户端", sessionID)
		return []*WebSocketClient{}
	}

	logs.Debug("[GetClientsBySessionID] 会话 %d 中注册的客户端ID: %v", sessionID, clientIDs)

	clients := make([]*WebSocketClient, 0, len(clientIDs))

	m.clientsMux.RLock()
	for _, clientID := range clientIDs {
		if client, exists := m.clients[clientID]; exists {
			clients = append(clients, client)
			logs.Debug("[GetClientsBySessionID] 找到在线客户端: %s", clientID)
		} else {
			logs.Debug("[GetClientsBySessionID] 客户端 %s 已离线，跳过", clientID)
		}
	}
	m.clientsMux.RUnlock()

	logs.Debug("[GetClientsBySessionID] 会话 %d 最终返回 %d 个在线客户端", sessionID, len(clients))
	return clients
}

// SendToUser 发送消息给指定用户
func (m *WebSocketManagerImpl) SendToUser(userID int64, userType string, message *dto.WebSocketMessageDTO) error {
	client := m.GetClient(userID, userType)
	if client == nil {
		return nil // 用户不在线，跳过发送
	}

	// 序列化消息
	msgBytes, err := json.Marshal(message)
	if err != nil {
		logs.Error("序列化WebSocket消息失败: %v", err)
		return err
	}

	// 发送消息
	select {
	case client.Send <- msgBytes:
		// 消息成功投递到通道
	default:
		// 客户端通道已满或已关闭，关闭连接
		m.UnregisterClient(client)
	}

	return nil
}

// BroadcastToSession 广播消息给会话中的所有用户
func (m *WebSocketManagerImpl) BroadcastToSession(sessionID int64, message *dto.WebSocketMessageDTO) error {
	clients := m.GetClientsBySessionID(sessionID)

	logs.Info("[BroadcastToSession] 会话 %d 中找到 %d 个在线客户端", sessionID, len(clients))

	// 序列化消息
	msgBytes, err := json.Marshal(message)
	if err != nil {
		logs.Error("序列化WebSocket消息失败: %v", err)
		return err
	}

	successCount := 0
	for _, client := range clients {
		select {
		case client.Send <- msgBytes:
			// 消息成功投递到通道
			successCount++
			logs.Debug("[BroadcastToSession] 消息成功发送给用户 %d(%s)", client.UserID, client.UserType)
		default:
			// 客户端通道已满或已关闭，关闭连接
			logs.Warn("[BroadcastToSession] 用户 %d(%s) 的通道已满或已关闭，移除连接", client.UserID, client.UserType)
			m.UnregisterClient(client)
		}
	}

	logs.Info("[BroadcastToSession] 会话 %d 消息广播完成，成功发送给 %d/%d 个客户端", sessionID, successCount, len(clients))
	return nil
}

// IsUserOnline 检查用户是否在线
func (m *WebSocketManagerImpl) IsUserOnline(userID int64, userType string) bool {
	return m.GetClient(userID, userType) != nil
}

// AddToSession 将客户端添加到会话
func (m *WebSocketManagerImpl) AddToSession(sessionID int64, client *WebSocketClient) {
	clientID := generateClientID(client.UserID, client.UserType)

	m.sessionClientsMux.Lock()
	defer m.sessionClientsMux.Unlock()

	clients, exists := m.sessionClients[sessionID]
	if !exists {
		clients = make([]string, 0)
		logs.Debug("[AddToSession] 为会话 %d 创建新的客户端列表", sessionID)
	}

	// 检查是否已经在列表中
	alreadyExists := false
	for _, id := range clients {
		if id == clientID {
			alreadyExists = true
			break
		}
	}

	if !alreadyExists {
		clients = append(clients, clientID)
		m.sessionClients[sessionID] = clients
		logs.Info("[AddToSession] 客户端 %s 成功添加到会话 %d，当前会话客户端数: %d", clientID, sessionID, len(clients))
	} else {
		logs.Debug("[AddToSession] 客户端 %s 已存在于会话 %d 中", clientID, sessionID)
	}
}

// heartbeatChecker 定期发送心跳包以保持连接
func (m *WebSocketManagerImpl) heartbeatChecker() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		heartbeatMsg := &dto.WebSocketMessageDTO{
			Type:      "heartbeat",
			Timestamp: time.Now().Unix(),
			Data:      map[string]interface{}{"status": "ok"},
		}

		msgBytes, _ := json.Marshal(heartbeatMsg)

		m.clientsMux.RLock()
		for _, client := range m.clients {
			select {
			case client.Send <- msgBytes:
				// 心跳包成功发送
			default:
				// 客户端通道已满或已关闭，关闭连接
				go m.UnregisterClient(client)
			}
		}
		m.clientsMux.RUnlock()
	}
}

// writePump 将消息从通道写入到WebSocket连接
func (c *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			// 设置写入超时
			c.Connection.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// 通道已关闭，发送关闭消息
				c.Connection.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// 写入消息
			err := c.Connection.WriteMessage(websocket.TextMessage, message)
			if err != nil {
				logs.Error("写入WebSocket消息失败: %v", err)
				return
			}

		case <-ticker.C:
			// 发送ping消息以保持连接
			c.Connection.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Connection.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// readPump 从连接读取消息
func (c *WebSocketClient) readPump() {
	defer func() {
		// readPump是唯一负责清理的地方
		c.Manager.UnregisterClient(c)
		c.Connection.Close()
	}()

	// 设置读取限制和延迟
	c.Connection.SetReadLimit(512 * 1024) // 512KB
	c.Connection.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Connection.SetPongHandler(func(string) error {
		c.Connection.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.Connection.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logs.Error("WebSocket读取错误: %v", err)
			}
			break
		}

		// 处理客户端消息
		logs.Debug("WebSocket收到客户端消息: %s", string(message))

		// 更新用户活动时间（表示用户活跃）
		if manager, ok := c.Manager.(*WebSocketManagerImpl); ok {
			ctx := context.Background()
			err := manager.onlineStatusService.UpdateUserActivity(ctx, c.UserID, c.UserType)
			if err != nil {
				logs.Debug("更新用户活动时间失败: %v", err)
			}
		}

		// 解析消息
		var msg dto.WebSocketMessageDTO
		if err := json.Unmarshal(message, &msg); err != nil {
			logs.Error("WebSocket消息解析失败: %v", err)
			continue
		}

		// 根据消息类型处理
		switch msg.Type {
		case "subscribe":
			// 处理订阅消息
			if msg.Event == "join_session" {
				var sessionID int64

				// 尝试不同方式获取会话ID
				if sid, ok := msg.Data.(map[string]interface{})["session_id"]; ok {
					// 从数据字段获取
					switch v := sid.(type) {
					case float64:
						sessionID = int64(v)
					case int64:
						sessionID = v
					case string:
						sessionID, _ = strconv.ParseInt(v, 10, 64)
					}
				} else if msg.SessionID > 0 {
					// 从消息头部获取
					sessionID = msg.SessionID
				}

				if sessionID > 0 {
					// 添加客户端到会话
					manager, ok := c.Manager.(*WebSocketManagerImpl)
					if ok {
						manager.AddToSession(sessionID, c)
						logs.Info("用户 %d(%s) 已成功订阅会话 %d", c.UserID, c.UserType, sessionID)

						// 发送确认消息
						responseMsg := &dto.WebSocketMessageDTO{
							Type:      "notification",
							Event:     "session_subscribed",
							SessionID: sessionID,
							Timestamp: time.Now().Unix(),
							Data: map[string]interface{}{
								"session_id": sessionID,
								"message":    "成功订阅会话",
							},
						}

						respMsgBytes, _ := json.Marshal(responseMsg)
						c.Send <- respMsgBytes
					}
				} else {
					logs.Error("订阅消息中无法获取会话ID: %v", msg)
				}
			}
		case "heartbeat":
			// 处理心跳响应
			responseMsg := &dto.WebSocketMessageDTO{
				Type:      "heartbeat",
				Timestamp: time.Now().Unix(),
				Data:      map[string]interface{}{"status": "ok"},
			}
			respMsgBytes, _ := json.Marshal(responseMsg)
			c.Send <- respMsgBytes
		default:
			// 日志记录但不处理其他类型的消息
			logs.Info("忽略未知类型的WebSocket消息: %s", string(message))
		}
	}
}

// autoSubscribeUserSessions 自动订阅用户的所有会话（包括一对一和群聊）
func (m *WebSocketManagerImpl) autoSubscribeUserSessions(client *WebSocketClient) {
	o := orm.NewOrm()

	// 1. 获取用户的所有一对一会话
	var sessions []models.ChatSession

	// 构建查询条件：用户作为创建者或接收者的会话
	cond1 := orm.NewCondition()
	cond1 = cond1.And("creator_i_d", client.UserID).And("creator_type", client.UserType)

	cond2 := orm.NewCondition()
	cond2 = cond2.And("receiver_i_d", client.UserID).And("receiver_type", client.UserType)

	// 合并条件
	cond := orm.NewCondition()
	cond = cond.OrCond(cond1).OrCond(cond2)

	// 查询一对一会话
	_, err := o.QueryTable(new(models.ChatSession)).
		SetCond(cond).
		Filter("status", 0).
		Filter("type__in", []string{"user_to_user", "user_to_merchant", "merchant_to_user"}).
		All(&sessions)

	if err != nil {
		logs.Error("[WebSocketManager] 获取用户 %d(%s) 的一对一会话列表失败: %v", client.UserID, client.UserType, err)
	} else {
		logs.Info("[WebSocketManager] 用户 %d(%s) 共有 %d 个一对一会话需要订阅", client.UserID, client.UserType, len(sessions))

		// 订阅所有一对一会话
		for _, session := range sessions {
			m.AddToSession(session.ID, client)
			logs.Info("[WebSocketManager] 用户 %d(%s) 自动订阅一对一会话 %d (类型: %s)", client.UserID, client.UserType, session.ID, session.Type)
		}
	}

	// 2. 获取用户所在的所有群聊会话
	var members []models.ChatGroupMember
	_, err = o.QueryTable(new(models.ChatGroupMember)).
		Filter("user_id", client.UserID).
		Filter("user_type", client.UserType).
		Filter("status", 0). // 只获取正常状态的成员
		All(&members)

	if err != nil {
		logs.Error("[WebSocketManager] 获取用户 %d(%s) 的群聊列表失败: %v", client.UserID, client.UserType, err)
	} else {
		logs.Info("[WebSocketManager] 用户 %d(%s) 共有 %d 个群聊需要订阅", client.UserID, client.UserType, len(members))

		// 为每个群聊获取对应的会话ID并订阅
		for _, member := range members {
			// 获取群聊信息
			var group models.ChatGroup
			err := o.QueryTable(new(models.ChatGroup)).
				Filter("id", member.GroupID).
				Filter("status", 0). // 只获取正常状态的群聊
				One(&group)

			if err != nil {
				logs.Error("[WebSocketManager] 获取群聊 %d 信息失败: %v", member.GroupID, err)
				continue
			}

			// 如果群聊有关联的会话ID，则订阅该会话
			if group.SessionID > 0 {
				m.AddToSession(group.SessionID, client)
				logs.Info("[WebSocketManager] 用户 %d(%s) 自动订阅群聊 %d 的会话 %d", client.UserID, client.UserType, member.GroupID, group.SessionID)
			} else {
				logs.Debug("[WebSocketManager] 群聊 %d 没有关联的会话ID，跳过订阅", member.GroupID)
			}
		}
	}
}

// handleUserOnline 处理用户上线
func (m *WebSocketManagerImpl) handleUserOnline(client *WebSocketClient) {
	ctx := context.Background()

	// 获取用户的会话列表
	sessionIDs := m.getUserSessionIDs(client.UserID, client.UserType)

	// 设置用户在线状态
	err := m.onlineStatusService.SetUserOnline(ctx, client.UserID, client.UserType, sessionIDs)
	if err != nil {
		logs.Error("[WebSocketManager] 设置用户在线状态失败: %v", err)
		return
	}

	// 推送用户上线通知给相关商家
	m.broadcastUserStatusChange(client.UserID, client.UserType, "customer_online", StatusActive, sessionIDs)
}

// handleUserOffline 处理用户离线
func (m *WebSocketManagerImpl) handleUserOffline(client *WebSocketClient) {
	ctx := context.Background()

	// 获取用户信息（包含会话列表）
	userInfo, err := m.onlineStatusService.GetUserOnlineInfo(ctx, client.UserID, client.UserType)
	var sessionIDs []int64
	if err == nil && userInfo != nil {
		sessionIDs = userInfo.SessionIDs
	}

	// 设置用户离线状态
	err = m.onlineStatusService.SetUserOffline(ctx, client.UserID, client.UserType)
	if err != nil {
		logs.Error("[WebSocketManager] 设置用户离线状态失败: %v", err)
	}

	// 推送用户离线通知给相关商家
	m.broadcastUserStatusChange(client.UserID, client.UserType, "customer_offline", StatusOffline, sessionIDs)
}

// getUserSessionIDs 获取用户的会话ID列表
func (m *WebSocketManagerImpl) getUserSessionIDs(userID int64, userType string) []int64 {
	o := orm.NewOrm()
	var sessions []models.ChatSession
	var sessionIDs []int64

	// 构建查询条件：用户作为创建者或接收者的会话
	cond1 := orm.NewCondition()
	cond1 = cond1.And("creator_i_d", userID).And("creator_type", userType)

	cond2 := orm.NewCondition()
	cond2 = cond2.And("receiver_i_d", userID).And("receiver_type", userType)

	// 合并条件
	cond := orm.NewCondition()
	cond = cond.OrCond(cond1).OrCond(cond2)

	// 查询会话
	_, err := o.QueryTable(new(models.ChatSession)).
		SetCond(cond).
		Filter("status", 0).
		All(&sessions)

	if err != nil {
		logs.Error("[WebSocketManager] 获取用户会话列表失败: %v", err)
		return sessionIDs
	}

	for _, session := range sessions {
		sessionIDs = append(sessionIDs, session.ID)
	}

	return sessionIDs
}

// broadcastUserStatusChange 广播用户状态变更通知
func (m *WebSocketManagerImpl) broadcastUserStatusChange(userID int64, userType string, event string, status OnlineStatus, sessionIDs []int64) {
	// 只有客户（user）状态变更才需要通知商家
	if userType != "user" {
		return
	}

	// 获取客户信息
	customerName := "未知用户"
	customerAvatar := ""

	// 这里需要获取用户信息，但为了避免循环依赖，我们先使用基本信息
	// 在实际使用中，可以通过其他方式获取用户信息
	ctx := context.Background()
	if userInfo, err := m.onlineStatusService.GetUserOnlineInfo(ctx, userID, userType); err == nil {
		// 从在线状态信息中获取基本信息（如果有的话）
		// 这里可以扩展UserOnlineInfo结构体来包含用户名和头像
		logs.Debug("[WebSocketManager] 获取到用户在线信息: %+v", userInfo)
	}

	// 构建状态变更消息数据
	messageData := map[string]interface{}{
		"customer_id": userID,
		"session_ids": sessionIDs,
		"timestamp":   time.Now().Format(time.RFC3339),
	}

	// 根据事件类型添加特定字段
	switch event {
	case "customer_online":
		messageData["customer_name"] = customerName
		messageData["customer_avatar"] = customerAvatar
		messageData["online_status"] = string(status)
	case "customer_offline":
		messageData["customer_name"] = customerName
		messageData["last_seen"] = time.Now().Format(time.RFC3339)
	case "customer_status_change":
		// 对于状态变更，需要传入旧状态和新状态
		// 这里暂时只设置新状态，旧状态可以通过参数传入
		messageData["new_status"] = string(status)
		messageData["old_status"] = "unknown" // 需要从调用方传入
	}

	// 构建状态变更消息
	statusMsg := &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     event,
		Timestamp: time.Now().Unix(),
		Data:      messageData,
	}

	// 向每个相关会话的商家推送通知
	for _, sessionID := range sessionIDs {
		// 获取会话信息以确定商家
		session, err := m.getSessionInfo(sessionID)
		if err != nil {
			logs.Error("[WebSocketManager] 获取会话信息失败: sessionID=%d, error=%v", sessionID, err)
			continue
		}

		// 确定要通知的商家
		var merchantID int64
		var merchantType string

		if session.CreatorType == "merchant" && session.CreatorID != userID {
			merchantID = session.CreatorID
			merchantType = "merchant"
		} else if session.ReceiverType == "merchant" && session.ReceiverID != userID {
			merchantID = session.ReceiverID
			merchantType = "merchant"
		}

		if merchantID > 0 {
			// 发送通知给商家
			err := m.SendToUser(merchantID, merchantType, statusMsg)
			if err != nil {
				logs.Error("[WebSocketManager] 发送状态变更通知失败: merchantID=%d, error=%v", merchantID, err)
			} else {
				logs.Info("[WebSocketManager] 已向商家 %d 发送客户 %d 的状态变更通知: %s", merchantID, userID, event)
			}
		}
	}
}

// getSessionInfo 获取会话信息
func (m *WebSocketManagerImpl) getSessionInfo(sessionID int64) (*models.ChatSession, error) {
	o := orm.NewOrm()
	session := &models.ChatSession{ID: sessionID}
	err := o.Read(session)
	if err != nil {
		return nil, err
	}
	return session, nil
}

// generateClientID 生成客户端ID
func generateClientID(userID int64, userType string) string {
	return userType + ":" + strconv.FormatInt(userID, 10)
}
