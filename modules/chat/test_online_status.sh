#!/bin/bash

# 在线状态功能测试脚本
# 该脚本用于测试客户在线状态管理功能

echo "=== 客户在线状态管理功能测试 ==="
echo ""

# 设置测试环境
export GO_ENV=test

# 进入聊天模块目录
cd "$(dirname "$0")"

echo "1. 运行在线状态服务单元测试..."
go test -v ./services -run TestOnlineStatusService
if [ $? -eq 0 ]; then
    echo "✅ 在线状态服务单元测试通过"
else
    echo "❌ 在线状态服务单元测试失败"
    exit 1
fi

echo ""
echo "2. 运行在线状态集成测试..."
go test -v ./services -run TestOnlineStatusIntegration
if [ $? -eq 0 ]; then
    echo "✅ 在线状态集成测试通过"
else
    echo "❌ 在线状态集成测试失败"
    exit 1
fi

echo ""
echo "3. 运行常量和工具函数测试..."
go test -v ./services -run TestGenerateClientID
go test -v ./services -run TestOnlineStatusConstants
if [ $? -eq 0 ]; then
    echo "✅ 常量和工具函数测试通过"
else
    echo "❌ 常量和工具函数测试失败"
    exit 1
fi

echo ""
echo "4. 运行性能测试..."
go test -v ./services -bench=BenchmarkOnlineStatusService -benchmem
if [ $? -eq 0 ]; then
    echo "✅ 性能测试完成"
else
    echo "❌ 性能测试失败"
    exit 1
fi

echo ""
echo "=== 所有测试完成 ==="
echo ""
echo "测试覆盖的功能："
echo "- ✅ 用户上线/下线状态管理"
echo "- ✅ 用户活动时间更新"
echo "- ✅ 会话用户管理"
echo "- ✅ 批量状态查询"
echo "- ✅ Redis缓存操作"
echo "- ✅ 状态过期处理"
echo "- ✅ 客户端ID生成"
echo "- ✅ 在线状态常量"
echo ""
echo "🎉 客户在线状态管理功能测试全部通过！"
