# GetMessages API 分页和排序功能测试

## 功能说明

GetMessages API 现在支持以下分页参数：

- `page`: 页码，默认为 1
- `pageSize`: 每页数量，默认为 20，最大为 100
- `order`: 排序方式，支持 `asc`（升序）和 `desc`（倒序），默认为 `desc`

## API 端点

```
GET /api/v1/chat/sessions/{session_id}/messages
```

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| session_id | int | 是 | - | 会话ID（路径参数） |
| page | int | 否 | 1 | 页码 |
| page_size | int | 否 | 20 | 每页数量（1-100） |
| order | string | 否 | desc | 排序方式：asc（升序）、desc（倒序） |

## 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "session_id": 1,
        "sender_id": 1,
        "sender_type": "user",
        "content": "消息内容",
        "type": "text",
        "resource_id": "",
        "status": 0,
        "created_at": "2025-01-20T10:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "page_count": 5
  }
}
```

## 测试用例

### 1. 基本分页测试

```bash
# 获取第一页消息（默认参数）
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取第二页消息
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?page=2" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 指定每页数量
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 排序测试

```bash
# 按时间升序排列（最早的消息在前）
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?order=asc" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 按时间倒序排列（最新的消息在前，默认）
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?order=desc" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 组合参数测试

```bash
# 获取第2页，每页10条，按时间升序
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?page=2&page_size=10&order=asc" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 边界值测试

```bash
# 测试最大页面大小
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?page_size=100" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 测试超出最大页面大小（应该被限制为20）
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?page_size=200" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 测试无效排序参数（应该使用默认值desc）
curl -X GET "http://localhost:8080/api/v1/chat/sessions/1/messages?order=invalid" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 实现细节

### 1. 控制器层 (MessageController)

- 解析并验证分页参数
- 设置默认值和边界值检查
- 调用服务层方法

### 2. 服务层 (ChatService)

- 验证会话权限
- 调用数据访问层方法
- 转换数据格式

### 3. 数据访问层 (ChatRepository)

- 构建数据库查询
- 实现分页和排序逻辑
- 返回结果和总数

## 数据库查询

排序逻辑：
- `order=asc`: `ORDER BY created_at ASC`
- `order=desc`: `ORDER BY created_at DESC`（默认）

分页逻辑：
- `LIMIT page_size OFFSET (page-1)*page_size`

## 注意事项

1. 页码从1开始计算
2. 页面大小限制在1-100之间
3. 无效的排序参数会使用默认值`desc`
4. 需要有效的JWT token进行身份验证
5. 用户只能访问有权限的会话消息
