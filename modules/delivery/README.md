# 配送模块 (Delivery Module)

配送模块负责管理商城系统的配送区域、配送方式、配送规则、配送时间段以及配送订单等功能。该模块是商城系统中实现商品配送服务的核心组件。

## 模块结构

```
modules/delivery/
├── constants/     - 常量定义（配送状态、操作类型等）
├── controllers/   - 控制器层，处理HTTP请求
├── dto/           - 数据传输对象定义
├── init.go        - 模块初始化文件
├── models/        - 数据模型定义
├── repositories/  - 数据访问层接口及实现
├── routers/       - 路由配置
└── services/      - 业务逻辑层接口及实现
```

## 核心功能

### 1. 配送区域管理

- 支持多级配送区域树结构
- 提供区域的创建、查询、更新、删除功能
- 获取区域树形结构

### 2. 配送方式管理

- 支持多种配送方式（如：普通快递、顺丰速运、EMS等）
- 提供配送方式的创建、查询、更新、删除功能

### 3. 配送规则管理

- 基于配送区域和配送方式的规则设置
- 支持按重量、订单金额等条件设置配送费用
- 提供规则的创建、查询、更新、删除功能

### 4. 配送时间段管理

- 支持设置可配送的时间段
- 提供时间段的创建、查询、更新、删除功能

### 5. 配送订单管理

- 创建配送订单
- 查询配送订单详情
- 更新配送状态
- 配送轨迹追踪

## API接口

### 配送区域接口

- `POST /delivery/areas` - 创建配送区域
- `GET /delivery/areas/:id` - 获取配送区域详情
- `PUT /delivery/areas/:id` - 更新配送区域
- `DELETE /delivery/areas/:id` - 删除配送区域
- `GET /delivery/areas/tree` - 获取配送区域树

### 配送方式接口

- `POST /delivery/methods` - 创建配送方式
- `GET /delivery/methods/:id` - 获取配送方式详情
- `PUT /delivery/methods/:id` - 更新配送方式
- `DELETE /delivery/methods/:id` - 删除配送方式
- `GET /delivery/methods/list` - 获取配送方式列表

### 配送规则接口

- `POST /delivery/rules` - 创建配送规则
- `GET /delivery/rules/:id` - 获取配送规则详情
- `PUT /delivery/rules/:id` - 更新配送规则
- `DELETE /delivery/rules/:id` - 删除配送规则
- `GET /delivery/rules/list` - 获取配送规则列表
- `GET /delivery/rules/calculate` - 计算配送费用

### 配送时间段接口

- `POST /delivery/timeslots` - 创建配送时间段
- `GET /delivery/timeslots/:id` - 获取配送时间段详情
- `PUT /delivery/timeslots/:id` - 更新配送时间段
- `DELETE /delivery/timeslots/:id` - 删除配送时间段
- `GET /delivery/timeslots/list` - 获取配送时间段列表

### 配送订单接口

- `POST /delivery/orders` - 创建配送订单
- `GET /delivery/orders/:id` - 获取配送订单详情
- `PUT /delivery/orders/:id/status` - 更新配送状态
- `GET /delivery/orders/list` - 获取配送订单列表
- `GET /delivery/orders/:id/tracking` - 获取配送轨迹

## 使用示例

### 创建配送区域

```go
// 创建配送区域
req := dto.CreateDeliveryAreaRequest{
    Name:      "上海市",
    Code:      "SH",
    ParentID:  0,
    Level:     1,
    Status:    constants.AreaStatusEnabled,
    SortOrder: 100,
}
areaID, err := deliveryAreaService.CreateDeliveryArea(ctx, req)
```

### 计算配送费用

```go
// 计算配送费用
req := dto.CalculateDeliveryFeeRequest{
    AreaID:     1,
    MethodID:   2,
    TotalWeight: 5.0,
    OrderAmount: 199.0,
}
fee, err := deliveryRuleService.CalculateDeliveryFee(ctx, req)
```

### 创建配送订单

```go
// 创建配送订单
req := dto.CreateDeliveryOrderRequest{
    OrderID:        123456,
    OrderNo:        "O202303150001",
    UserID:         10001,
    MethodID:       2,
    AreaID:         1,
    ReceiverName:   "张三",
    ReceiverPhone:  "13800138000",
    ReceiverAddress: "上海市浦东新区XX路XX号",
    DeliveryFee:    10.0,
}
orderID, err := deliveryOrderService.CreateDeliveryOrder(ctx, req)
```

## 注意事项

1. 配送区域支持多级结构，建议不超过4级
2. 配送规则的优先级：特定区域+特定配送方式 > 特定区域+所有配送方式 > 所有区域+特定配送方式 > 所有区域+所有配送方式
3. 创建配送订单前需要先计算配送费用
4. 配送状态变更会触发相应的订单状态更新 