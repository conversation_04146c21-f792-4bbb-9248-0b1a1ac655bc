/**
 * delivery_constants.go
 * 配送模块常量定义
 *
 * 本文件定义了配送模块中使用的各种常量，包括状态码、类型码等
 */

package constants

// 配送区域状态
const (
	AreaStatusDisabled = 0 // 禁用
	AreaStatusEnabled  = 1 // 启用
)

// 配送方式状态
const (
	MethodStatusDisabled = 0 // 禁用
	MethodStatusEnabled  = 1 // 启用
)

// 配送规则状态
const (
	RuleStatusDisabled = 0 // 禁用
	RuleStatusEnabled  = 1 // 启用
)

// 配送时间段状态
const (
	TimeSlotStatusDisabled = 0 // 禁用
	TimeSlotStatusEnabled  = 1 // 启用
)

// 配送订单状态
const (
	DeliveryStatusPending       = 0 // 待处理
	DeliveryStatusConfirmed     = 1 // 已确认
	DeliveryStatusWaitingRunner = 2 // 等待配送员
	DeliveryStatusAssigned      = 3 // 已分配配送员
	DeliveryStatusDelivering    = 4 // 配送中
	DeliveryStatusDelivered     = 5 // 已送达
	DeliveryStatusCompleted     = 6 // 已完成
	DeliveryStatusFailed        = 7 // 配送失败
	DeliveryStatusRedelivering  = 8 // 重新配送中
	DeliveryStatusCancelled     = 9 // 已取消
)

// 操作人类型
const (
	OperatorTypeSystem   = 0 // 系统
	OperatorTypeShop     = 1 // 店铺
	OperatorTypeRunner   = 2 // 配送员
	OperatorTypeCustomer = 3 // 客户
	OperatorTypeAdmin    = 4 // 管理员
)

// 区域级别
const (
	AreaLevelProvince = 1 // 省/直辖市
	AreaLevelCity     = 2 // 市
	AreaLevelDistrict = 3 // 区/县
)

// 状态描述映射
var (
	// 区域状态描述
	AreaStatusDesc = map[int]string{
		AreaStatusDisabled: "禁用",
		AreaStatusEnabled:  "启用",
	}

	// 配送方式状态描述
	MethodStatusDesc = map[int]string{
		MethodStatusDisabled: "禁用",
		MethodStatusEnabled:  "启用",
	}

	// 配送规则状态描述
	RuleStatusDesc = map[int]string{
		RuleStatusDisabled: "禁用",
		RuleStatusEnabled:  "启用",
	}

	// 配送时间段状态描述
	TimeSlotStatusDesc = map[int]string{
		TimeSlotStatusDisabled: "禁用",
		TimeSlotStatusEnabled:  "启用",
	}

	// 配送订单状态描述
	DeliveryStatusDesc = map[int]string{
		DeliveryStatusPending:       "待处理",
		DeliveryStatusConfirmed:     "已确认",
		DeliveryStatusWaitingRunner: "等待配送员",
		DeliveryStatusAssigned:      "已分配配送员",
		DeliveryStatusDelivering:    "配送中",
		DeliveryStatusDelivered:     "已送达",
		DeliveryStatusCompleted:     "已完成",
		DeliveryStatusFailed:        "配送失败",
		DeliveryStatusRedelivering:  "重新配送中",
		DeliveryStatusCancelled:     "已取消",
	}

	// 操作人类型描述
	OperatorTypeDesc = map[int]string{
		OperatorTypeSystem:   "系统",
		OperatorTypeShop:     "店铺",
		OperatorTypeRunner:   "配送员",
		OperatorTypeCustomer: "客户",
		OperatorTypeAdmin:    "管理员",
	}

	// 区域级别描述
	AreaLevelDesc = map[int]string{
		AreaLevelProvince: "省/直辖市",
		AreaLevelCity:     "市",
		AreaLevelDistrict: "区/县",
	}
)
