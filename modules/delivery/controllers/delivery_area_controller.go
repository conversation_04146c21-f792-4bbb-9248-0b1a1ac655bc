/**
 * delivery_area_controller.go
 * 配送区域控制器
 *
 * 本文件实现了配送区域相关的HTTP接口，包括创建、查询、更新和删除配送区域等功能
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/services"
	"o_mall_backend/utils/common"
)

// DeliveryAreaController 配送区域控制器
type DeliveryAreaController struct {
	controllers.AdminController
	areaService services.DeliveryAreaService
}

// 初始化控制器
func (c *DeliveryAreaController) Prepare() {
	// 调用父类的Prepare方法
	c.BaseController.Prepare()
	// 获取服务实例 - 这里应该使用依赖注入或工厂方法获取服务实例
	// 临时解决方案：直接创建服务实例
	// TODO: 实现正确的服务实例获取机制
	c.areaService = services.NewDeliveryAreaService()
}

// ParseRequest 通用请求参数解析方法
func (c *DeliveryAreaController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// @Title CreateDeliveryArea
// @Description 创建配送区域
// @Param body body dto.DeliveryAreaRequest true "配送区域信息"
// @Success 200 {object} result.Response{data=dto.DeliveryAreaResponse} "创建成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /areas [post]
func (c *DeliveryAreaController) CreateDeliveryArea() {
	// 检查用户权限
	// TODO: 实现正确的权限检查
	// 临时解决方案：跳过权限检查
	/*
		if !c.IsAdmin() {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	*/

	// 解析请求参数
	var req dto.DeliveryAreaRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务创建配送区域
	resp, err := c.areaService.CreateDeliveryArea(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建配送区域失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryArea
// @Description 获取配送区域详情
// @Param id path int true "配送区域ID"
// @Success 200 {object} result.Response{data=dto.DeliveryAreaResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送区域不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /areas/:id [get]
func (c *DeliveryAreaController) GetDeliveryArea() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务获取配送区域详情
	resp, err := c.areaService.GetDeliveryArea(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送区域详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title UpdateDeliveryArea
// @Description 更新配送区域
// @Param id path int true "配送区域ID"
// @Param body body dto.DeliveryAreaRequest true "配送区域信息"
// @Success 200 {object} result.Response{data=dto.DeliveryAreaResponse} "更新成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送区域不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /areas/:id [put]
func (c *DeliveryAreaController) UpdateDeliveryArea() {
	// 检查用户权限
	// TODO: 实现正确的权限检查
	// 临时解决方案：跳过权限检查
	/*
		if !c.IsAdmin() {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	*/

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var req dto.DeliveryAreaRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务更新配送区域
	resp, err := c.areaService.UpdateDeliveryArea(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("更新配送区域失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title DeleteDeliveryArea
// @Description 删除配送区域
// @Param id path int true "配送区域ID"
// @Success 200 {object} result.Response "删除成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /areas/:id [delete]
func (c *DeliveryAreaController) DeleteDeliveryArea() {
	// 检查用户权限
	// TODO: 实现正确的权限检查
	// 临时解决方案：跳过权限检查
	/*
		if !c.IsAdmin() {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	*/

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务删除配送区域
	err = c.areaService.DeleteDeliveryArea(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除配送区域失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// @Title ListDeliveryAreas
// @Description 获取配送区域列表
// @Param parentId query int false "父级区域ID"
// @Param status query int false "状态"
// @Success 200 {object} result.Response{data=[]dto.DeliveryAreaResponse} "获取成功"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /areas [get]
func (c *DeliveryAreaController) ListDeliveryAreas() {
	// 获取查询参数
	parentId, _ := c.GetInt64("parentId", 0)
	status, _ := c.GetInt("status", 0)

	// 调用服务获取配送区域列表
	resp, err := c.areaService.ListDeliveryAreas(c.Ctx.Request.Context(), parentId, status)
	if err != nil {
		logs.Error("获取配送区域列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryAreaTree
// @Description 获取配送区域树
// @Success 200 {object} result.Response{data=[]dto.DeliveryAreaResponse} "获取成功"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /areas/tree [get]
func (c *DeliveryAreaController) GetDeliveryAreaTree() {
	// 调用服务获取配送区域树
	resp, err := c.areaService.GetDeliveryAreaTree(c.Ctx.Request.Context())
	if err != nil {
		logs.Error("获取配送区域树失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}
