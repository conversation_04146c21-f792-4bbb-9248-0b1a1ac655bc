/**
 * delivery_method_controller.go
 * 配送方式控制器
 *
 * 本文件实现了配送方式相关的HTTP接口，包括创建、查询、更新和删除配送方式等功能
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/common/utils"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/services"
	"o_mall_backend/utils/common"
)

// DeliveryMethodController 配送方式控制器
type DeliveryMethodController struct {
	controllers.AdminController
	methodService services.DeliveryMethodService
}

// 初始化控制器
func (c *DeliveryMethodController) Prepare() {
	// 调用父类的Prepare方法
	c.AdminController.Prepare()
	// 获取服务实例
	c.methodService = utils.GetServiceInstance("deliveryMethodService").(services.DeliveryMethodService)
}

// ParseRequest 通用请求参数解析方法
func (c *DeliveryMethodController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// @Title CreateDeliveryMethod
// @Description 创建配送方式
// @Param body body dto.DeliveryMethodRequest true "配送方式信息"
// @Success 200 {object} result.Response{data=dto.DeliveryMethodResponse} "创建成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /methods [post]
func (c *DeliveryMethodController) CreateDeliveryMethod() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req dto.DeliveryMethodRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务创建配送方式
	resp, err := c.methodService.CreateDeliveryMethod(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建配送方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryMethod
// @Description 获取配送方式详情
// @Param id path int true "配送方式ID"
// @Success 200 {object} result.Response{data=dto.DeliveryMethodResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送方式不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /methods/:id [get]
func (c *DeliveryMethodController) GetDeliveryMethod() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务获取配送方式详情
	resp, err := c.methodService.GetDeliveryMethod(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送方式详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title UpdateDeliveryMethod
// @Description 更新配送方式
// @Param id path int true "配送方式ID"
// @Param body body dto.DeliveryMethodRequest true "配送方式信息"
// @Success 200 {object} result.Response{data=dto.DeliveryMethodResponse} "更新成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送方式不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /methods/:id [put]
func (c *DeliveryMethodController) UpdateDeliveryMethod() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var req dto.DeliveryMethodRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务更新配送方式
	resp, err := c.methodService.UpdateDeliveryMethod(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("更新配送方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title DeleteDeliveryMethod
// @Description 删除配送方式
// @Param id path int true "配送方式ID"
// @Success 200 {object} result.Response "删除成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /methods/:id [delete]
func (c *DeliveryMethodController) DeleteDeliveryMethod() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务删除配送方式
	err = c.methodService.DeleteDeliveryMethod(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除配送方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// @Title ListDeliveryMethods
// @Description 获取配送方式列表
// @Param status query int false "状态"
// @Success 200 {object} result.Response{data=[]dto.DeliveryMethodResponse} "获取成功"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /methods [get]
func (c *DeliveryMethodController) ListDeliveryMethods() {
	// 获取查询参数
	status, _ := c.GetInt("status", 0)

	// 调用服务获取配送方式列表
	resp, err := c.methodService.ListDeliveryMethods(c.Ctx.Request.Context(), status)
	if err != nil {
		logs.Error("获取配送方式列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title SetDefaultDeliveryMethod
// @Description 设置默认配送方式
// @Param id path int true "配送方式ID"
// @Success 200 {object} result.Response "设置成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /methods/:id/default [put]
func (c *DeliveryMethodController) SetDefaultDeliveryMethod() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务设置默认配送方式
	err = c.methodService.SetDefaultDeliveryMethod(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("设置默认配送方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
