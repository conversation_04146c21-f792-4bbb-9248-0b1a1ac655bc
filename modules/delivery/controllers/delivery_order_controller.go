/**
 * delivery_order_controller.go
 * 配送订单控制器
 *
 * 本文件实现了配送订单相关的HTTP接口，包括创建、查询、更新状态和物流信息等功能
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/common/utils"
	"o_mall_backend/modules/delivery/constants"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/services"
	"o_mall_backend/utils/common"
)

// DeliveryOrderController 配送订单控制器
type DeliveryOrderController struct {
	controllers.AdminController
	orderService services.DeliveryOrderService
}

// Prepare 初始化控制器
func (c *DeliveryOrderController) Prepare() {
	// 调用父类的Prepare方法
	c.AdminController.Prepare()
	// 获取服务实例
	c.orderService = utils.GetServiceInstance("deliveryOrderService").(services.DeliveryOrderService)
}

// ParseRequest 通用请求参数解析方法
func (c *DeliveryOrderController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// @Title CreateDeliveryOrder
// @Description 创建配送订单
// @Param body body dto.CreateDeliveryOrderRequest true "配送订单信息"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "创建成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders [post]
func (c *DeliveryOrderController) CreateDeliveryOrder() {
	// 解析请求参数
	var req dto.CreateDeliveryOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 如果未指定用户ID，则使用当前登录用户的ID
	if req.UserID <= 0 {
		userId := c.GetCurrentUserID()
		if userId > 0 {
			req.UserID = userId
		} else {
			result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "用户ID不能为空"))
			return
		}
	}

	// 调用服务创建配送订单
	resp, err := c.orderService.CreateDeliveryOrder(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryOrder
// @Description 获取配送订单详情
// @Param id path int true "配送订单ID"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/:id [get]
func (c *DeliveryOrderController) GetDeliveryOrder() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务获取配送订单详情
	resp, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送订单详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() && !c.IsRunner() {
		userId := c.GetCurrentUserID()
		shopId := c.GetShopID()

		// 普通用户只能查看自己的订单
		if !c.IsShop() && userId != resp.UserID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}

		// 店铺只能查看自己店铺的订单
		if c.IsShop() && shopId != resp.ShopID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryOrderByOrderID
// @Description 根据订单ID获取配送订单
// @Param orderId query int true "订单ID"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/by-order-id [get]
func (c *DeliveryOrderController) GetDeliveryOrderByOrderID() {
	// 获取查询参数
	orderId, err := c.GetInt64("orderId")
	if err != nil || orderId <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务根据订单ID获取配送订单
	resp, err := c.orderService.GetDeliveryOrderByOrderID(c.Ctx.Request.Context(), orderId)
	if err != nil {
		logs.Error("根据订单ID获取配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() && !c.IsRunner() {
		userId := c.GetCurrentUserID()
		shopId := c.GetShopID()

		// 普通用户只能查看自己的订单
		if !c.IsShop() && userId != resp.UserID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}

		// 店铺只能查看自己店铺的订单
		if c.IsShop() && shopId != resp.ShopID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryOrderByOrderNo
// @Description 根据订单编号获取配送订单
// @Param orderNo query string true "订单编号"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/by-order-no [get]
func (c *DeliveryOrderController) GetDeliveryOrderByOrderNo() {
	// 获取查询参数
	orderNo := c.GetString("orderNo")
	if orderNo == "" {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "订单编号不能为空"))
		return
	}

	// 调用服务根据订单编号获取配送订单
	resp, err := c.orderService.GetDeliveryOrderByOrderNo(c.Ctx.Request.Context(), orderNo)
	if err != nil {
		logs.Error("根据订单编号获取配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() && !c.IsRunner() {
		userId := c.GetCurrentUserID()
		shopId := c.GetShopID()

		// 普通用户只能查看自己的订单
		if !c.IsShop() && userId != resp.UserID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}

		// 店铺只能查看自己店铺的订单
		if c.IsShop() && shopId != resp.ShopID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title UpdateDeliveryOrderStatus
// @Description 更新配送订单状态
// @Param id path int true "配送订单ID"
// @Param body body dto.UpdateDeliveryOrderStatusRequest true "更新状态请求"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "更新成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/:id/status [put]
func (c *DeliveryOrderController) UpdateDeliveryOrderStatus() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取配送订单详情，检查权限
	order, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送订单详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if order == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 解析请求参数
	var req dto.UpdateDeliveryOrderStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取操作人信息
	operatorID := c.GetCurrentUserID()
	operatorType := constants.OperatorTypeAdmin
	if c.IsShop() {
		operatorType = constants.OperatorTypeShop
	} else if c.IsRunner() {
		operatorType = constants.OperatorTypeRunner
	}

	// 创建UpdateDeliveryStatusRequest对象
	statusReq := &dto.UpdateDeliveryStatusRequest{
		Status:      req.Status,
		Description: req.Remark,
	}

	// 调用服务更新配送订单状态
	err = c.orderService.UpdateDeliveryStatus(c.Ctx.Request.Context(), id, statusReq, operatorID, operatorType)
	if err != nil {
		logs.Error("更新配送订单状态失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取更新后的订单信息
	updatedOrder, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取更新后的配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, updatedOrder)
}

// @Title AssignRunner
// @Description 分配配送员
// @Param id path int true "配送订单ID"
// @Param body body dto.AssignRunnerRequest true "分配配送员请求"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "分配成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/:id/runner [put]
func (c *DeliveryOrderController) AssignRunner() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var req dto.AssignRunnerRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取操作人信息
	operatorID := c.GetCurrentUserID()
	operatorType := constants.OperatorTypeAdmin

	// 调用服务分配配送员
	err = c.orderService.AssignRunner(c.Ctx.Request.Context(), id, req.RunnerID, operatorID, operatorType)
	if err != nil {
		logs.Error("分配配送员失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取更新后的订单信息
	updatedOrder, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取更新后的配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, updatedOrder)
}

// @Title UpdateTrackingInfo
// @Description 更新物流信息
// @Param id path int true "配送订单ID"
// @Param body body dto.UpdateTrackingInfoRequest true "更新物流信息请求"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "更新成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/:id/tracking [put]
func (c *DeliveryOrderController) UpdateTrackingInfo() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取配送订单详情，检查权限
	order, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送订单详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if order == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() && !c.IsRunner() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req dto.UpdateTrackingInfoRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取操作人信息
	operatorID := c.GetCurrentUserID()
	operatorType := constants.OperatorTypeAdmin
	if c.IsRunner() {
		operatorType = constants.OperatorTypeRunner
	}

	// 调用服务更新物流信息
	err = c.orderService.UpdateTrackingInfo(c.Ctx.Request.Context(), id, req.TrackingNo, req.TrackingCompany, operatorID, operatorType)
	if err != nil {
		logs.Error("更新物流信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取更新后的订单信息
	updatedOrder, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取更新后的配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, updatedOrder)
}

// @Title CancelDeliveryOrder
// @Description 取消配送订单
// @Param id path int true "配送订单ID"
// @Param body body dto.CancelDeliveryOrderRequest true "取消配送订单请求"
// @Success 200 {object} result.Response{data=dto.DeliveryOrderResponse} "取消成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/:id/cancel [put]
func (c *DeliveryOrderController) CancelDeliveryOrder() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取配送订单详情，检查权限
	order, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送订单详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if order == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 解析请求参数
	var req dto.CancelDeliveryOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取操作人信息
	operatorID := c.GetCurrentUserID()
	operatorType := constants.OperatorTypeAdmin
	if c.IsShop() {
		operatorType = constants.OperatorTypeShop
	} else if c.IsRunner() {
		operatorType = constants.OperatorTypeRunner
	}

	// 创建CancelDeliveryRequest对象
	cancelReq := &dto.CancelDeliveryRequest{
		Reason: req.Reason,
	}

	// 调用服务取消配送订单
	err = c.orderService.CancelDelivery(c.Ctx.Request.Context(), id, cancelReq, operatorID, operatorType)
	if err != nil {
		logs.Error("取消配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取更新后的订单信息
	updatedOrder, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取更新后的配送订单失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, updatedOrder)
}

// @Title ListDeliveryOrders
// @Description 获取配送订单列表
// @Param status query int false "配送状态"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} result.Response{data=[]dto.DeliveryOrderResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders [get]
func (c *DeliveryOrderController) ListDeliveryOrders() {
	// 获取查询参数
	status, _ := c.GetInt("status", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 根据用户角色获取不同的订单列表
	var orders []*dto.DeliveryOrderResponse
	var total int64
	var err error

	if c.IsAdmin() {
		// 管理员可以查看所有订单
		// 这里使用ListShopDeliveryOrders方法，传入0表示查询所有店铺
		orders, total, err = c.orderService.ListShopDeliveryOrders(c.Ctx.Request.Context(), 0, status, page, pageSize)
	} else if c.IsShop() {
		// 店铺查看自己的订单
		shopID := c.GetShopID()
		orders, total, err = c.orderService.ListShopDeliveryOrders(c.Ctx.Request.Context(), shopID, status, page, pageSize)
	} else if c.IsRunner() {
		// 配送员查看分配给自己的订单
		runnerID := c.GetRunnerID()
		orders, total, err = c.orderService.ListRunnerDeliveryOrders(c.Ctx.Request.Context(), runnerID, status, page, pageSize)
	} else {
		// 普通用户查看自己的订单
		userID := c.GetCurrentUserID()
		orders, total, err = c.orderService.ListUserDeliveryOrders(c.Ctx.Request.Context(), userID, status, page, pageSize)
	}

	if err != nil {
		logs.Error("获取配送订单列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OKWithPagination(c.Ctx, orders, total, page, pageSize)
}

// @Title GetTrackingInfo
// @Description 获取配送物流跟踪信息
// @Param id path int true "配送订单ID"
// @Success 200 {object} result.Response{data=[]dto.DeliveryTrackingResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送订单不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /orders/:id/tracking [get]
func (c *DeliveryOrderController) GetTrackingInfo() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取配送订单详情，检查权限
	order, err := c.orderService.GetDeliveryOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送订单详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if order == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() && !c.IsRunner() {
		userId := c.GetCurrentUserID()
		shopId := c.GetShopID()

		// 普通用户只能查看自己的订单
		if !c.IsShop() && userId != order.UserID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}

		// 店铺只能查看自己店铺的订单
		if c.IsShop() && shopId != order.ShopID {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	}

	// 调用服务获取物流跟踪信息
	trackings, err := c.orderService.ListDeliveryTrackings(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取物流跟踪信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, trackings)
}
