/**
 * delivery_rule_controller.go
 * 配送规则控制器
 *
 * 本文件实现了配送规则相关的HTTP接口，包括创建、查询、更新和删除配送规则等功能
 * 以及计算配送费用的接口
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/common/utils"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/services"
	"o_mall_backend/utils/common"
)

// DeliveryRuleController 配送规则控制器
type DeliveryRuleController struct {
	controllers.AdminController
	ruleService services.DeliveryRuleService
}

// 初始化控制器
func (c *DeliveryRuleController) Prepare() {
	// 调用父类的Prepare方法
	c.AdminController.Prepare()
	// 获取服务实例
	c.ruleService = utils.GetServiceInstance("deliveryRuleService").(services.DeliveryRuleService)
}

// ParseRequest 通用请求参数解析方法
func (c *DeliveryRuleController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// @Title CreateDeliveryRule
// @Description 创建配送规则
// @Param body body dto.DeliveryRuleRequest true "配送规则信息"
// @Success 200 {object} result.Response{data=dto.DeliveryRuleResponse} "创建成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /rules [post]
func (c *DeliveryRuleController) CreateDeliveryRule() {
	// 检查用户权限
	if !c.IsAdmin() && !c.IsShop() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req dto.DeliveryRuleRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 如果是店铺用户，设置店铺ID
	if c.IsShop() && !c.IsAdmin() {
		shopId := c.GetShopID()
		if shopId <= 0 {
			result.HandleError(c.Ctx, result.NewError(result.CodeUnauthorized, "店铺信息异常"))
			return
		}
		req.ShopID = shopId
	}

	// 调用服务创建配送规则
	resp, err := c.ruleService.CreateDeliveryRule(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建配送规则失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryRule
// @Description 获取配送规则详情
// @Param id path int true "配送规则ID"
// @Success 200 {object} result.Response{data=dto.DeliveryRuleResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送规则不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /rules/:id [get]
func (c *DeliveryRuleController) GetDeliveryRule() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务获取配送规则详情
	resp, err := c.ruleService.GetDeliveryRule(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送规则详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() && c.IsShop() {
		shopId := c.GetShopID()
		if shopId != resp.ShopID && resp.ShopID != 0 {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title UpdateDeliveryRule
// @Description 更新配送规则
// @Param id path int true "配送规则ID"
// @Param body body dto.DeliveryRuleRequest true "配送规则信息"
// @Success 200 {object} result.Response{data=dto.DeliveryRuleResponse} "更新成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送规则不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /rules/:id [put]
func (c *DeliveryRuleController) UpdateDeliveryRule() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取配送规则详情，检查权限
	rule, err := c.ruleService.GetDeliveryRule(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送规则详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if rule == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() {
		if c.IsShop() {
			shopId := c.GetShopID()
			if shopId != rule.ShopID && rule.ShopID != 0 {
				result.HandleError(c.Ctx, result.ErrUnauthorized)
				return
			}
		} else {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	}

	// 解析请求参数
	var req dto.DeliveryRuleRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 如果是店铺用户，确保不能修改店铺ID
	if c.IsShop() && !c.IsAdmin() {
		shopId := c.GetShopID()
		if shopId <= 0 {
			result.HandleError(c.Ctx, result.NewError(result.CodeUnauthorized, "店铺信息异常"))
			return
		}
		req.ShopID = shopId
	}

	// 调用服务更新配送规则
	resp, err := c.ruleService.UpdateDeliveryRule(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("更新配送规则失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title DeleteDeliveryRule
// @Description 删除配送规则
// @Param id path int true "配送规则ID"
// @Success 200 {object} result.Response "删除成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送规则不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /rules/:id [delete]
func (c *DeliveryRuleController) DeleteDeliveryRule() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取配送规则详情，检查权限
	rule, err := c.ruleService.GetDeliveryRule(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送规则详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if rule == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 检查权限
	if !c.IsAdmin() {
		if c.IsShop() {
			shopId := c.GetShopID()
			if shopId != rule.ShopID && rule.ShopID != 0 {
				result.HandleError(c.Ctx, result.ErrUnauthorized)
				return
			}
		} else {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
	}

	// 调用服务删除配送规则
	err = c.ruleService.DeleteDeliveryRule(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除配送规则失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// @Title ListDeliveryRules
// @Description 获取配送规则列表
// @Param methodId query int false "配送方式ID"
// @Param areaId query int false "区域ID"
// @Param shopId query int false "店铺ID"
// @Success 200 {object} result.Response{data=[]dto.DeliveryRuleResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /rules [get]
func (c *DeliveryRuleController) ListDeliveryRules() {
	// 获取查询参数
	methodId, _ := c.GetInt64("methodId", 0)
	areaId, _ := c.GetInt64("areaId", 0)
	shopId, _ := c.GetInt64("shopId", 0)

	// 如果是店铺用户，只能查询自己的规则
	if c.IsShop() && !c.IsAdmin() {
		userShopId := c.GetShopID()
		if userShopId > 0 {
			shopId = userShopId
		}
	}

	// 调用服务获取配送规则列表
	resp, err := c.ruleService.ListDeliveryRules(c.Ctx.Request.Context(), methodId, areaId, shopId)
	if err != nil {
		logs.Error("获取配送规则列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title CalculateDeliveryFee
// @Description 计算配送费用
// @Param body body dto.DeliveryFeeCalculateRequest true "计算配送费用请求参数"
// @Success 200 {object} result.Response{data=dto.DeliveryFeeCalculateResponse} "计算成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /rules/calculate-fee [post]
func (c *DeliveryRuleController) CalculateDeliveryFee() {
	// 解析请求参数
	var req dto.DeliveryFeeCalculateRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务计算配送费用
	resp, err := c.ruleService.CalculateDeliveryFee(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("计算配送费用失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}
