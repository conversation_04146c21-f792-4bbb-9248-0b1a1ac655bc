/**
 * delivery_time_slot_controller.go
 * 配送时间段控制器
 *
 * 本文件实现了配送时间段相关的HTTP接口，包括创建、查询、更新和删除配送时间段等功能
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/common/utils"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/services"
	"o_mall_backend/utils/common"
)

// DeliveryTimeSlotController 配送时间段控制器
type DeliveryTimeSlotController struct {
	controllers.AdminController
	timeSlotService services.DeliveryTimeSlotService
}

// 初始化控制器
func (c *DeliveryTimeSlotController) Prepare() {
	// 调用父类的Prepare方法
	c.AdminController.Prepare()
	// 获取服务实例
	c.timeSlotService = utils.GetServiceInstance("deliveryTimeSlotService").(services.DeliveryTimeSlotService)
}

// ParseRequest 通用请求参数解析方法
func (c *DeliveryTimeSlotController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// @Title CreateDeliveryTimeSlot
// @Description 创建配送时间段
// @Param body body dto.DeliveryTimeSlotRequest true "配送时间段信息"
// @Success 200 {object} result.Response{data=dto.DeliveryTimeSlotResponse} "创建成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /time-slots [post]
func (c *DeliveryTimeSlotController) CreateDeliveryTimeSlot() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req dto.DeliveryTimeSlotRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务创建配送时间段
	resp, err := c.timeSlotService.CreateDeliveryTimeSlot(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建配送时间段失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title GetDeliveryTimeSlot
// @Description 获取配送时间段详情
// @Param id path int true "配送时间段ID"
// @Success 200 {object} result.Response{data=dto.DeliveryTimeSlotResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送时间段不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /time-slots/:id [get]
func (c *DeliveryTimeSlotController) GetDeliveryTimeSlot() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务获取配送时间段详情
	resp, err := c.timeSlotService.GetDeliveryTimeSlot(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取配送时间段详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title UpdateDeliveryTimeSlot
// @Description 更新配送时间段
// @Param id path int true "配送时间段ID"
// @Param body body dto.DeliveryTimeSlotRequest true "配送时间段信息"
// @Success 200 {object} result.Response{data=dto.DeliveryTimeSlotResponse} "更新成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 404 {object} result.Response "配送时间段不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /time-slots/:id [put]
func (c *DeliveryTimeSlotController) UpdateDeliveryTimeSlot() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var req dto.DeliveryTimeSlotRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务更新配送时间段
	resp, err := c.timeSlotService.UpdateDeliveryTimeSlot(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("更新配送时间段失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title DeleteDeliveryTimeSlot
// @Description 删除配送时间段
// @Param id path int true "配送时间段ID"
// @Success 200 {object} result.Response "删除成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /time-slots/:id [delete]
func (c *DeliveryTimeSlotController) DeleteDeliveryTimeSlot() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务删除配送时间段
	err = c.timeSlotService.DeleteDeliveryTimeSlot(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除配送时间段失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// @Title ListDeliveryTimeSlots
// @Description 获取配送时间段列表
// @Param methodId query int false "配送方式ID"
// @Param status query int false "状态"
// @Success 200 {object} result.Response{data=[]dto.DeliveryTimeSlotResponse} "获取成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /time-slots [get]
func (c *DeliveryTimeSlotController) ListDeliveryTimeSlots() {
	// 获取查询参数
	methodId, _ := c.GetInt64("methodId", 0)
	status, _ := c.GetInt("status", 0)

	// 调用服务获取配送时间段列表
	resp, err := c.timeSlotService.ListDeliveryTimeSlots(c.Ctx.Request.Context(), methodId, status)
	if err != nil {
		logs.Error("获取配送时间段列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// @Title SetDefaultDeliveryTimeSlot
// @Description 设置默认配送时间段
// @Param methodId query int true "配送方式ID"
// @Param id path int true "配送时间段ID"
// @Success 200 {object} result.Response "设置成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /time-slots/:id/default [put]
func (c *DeliveryTimeSlotController) SetDefaultDeliveryTimeSlot() {
	// 检查用户权限
	if !c.IsAdmin() {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取路径参数和查询参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	methodId, err := c.GetInt64("methodId")
	if err != nil || methodId <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务设置默认配送时间段
	err = c.timeSlotService.SetDefaultDeliveryTimeSlot(c.Ctx.Request.Context(), methodId, id)
	if err != nil {
		logs.Error("设置默认配送时间段失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// @Title CheckTimeSlotAvailable
// @Description 检查配送时间段是否可用
// @Param id path int true "配送时间段ID"
// @Success 200 {object} result.Response{data=bool} "检查成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /time-slots/:id/check [get]
func (c *DeliveryTimeSlotController) CheckTimeSlotAvailable() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := utils.StrToInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务检查配送时间段是否可用
	available, err := c.timeSlotService.CheckTimeSlotAvailable(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("检查配送时间段是否可用失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, available)
}
