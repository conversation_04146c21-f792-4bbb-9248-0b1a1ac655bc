/**
 * delivery_dto.go
 * 配送模块数据传输对象
 *
 * 本文件定义了配送模块相关的请求和响应数据结构，用于前后端数据交互
 */

package dto

import (
	"time"
)

// 配送区域相关DTO

// DeliveryAreaRequest 配送区域请求
type DeliveryAreaRequest struct {
	Name      string `json:"name" valid:"Required"` // 区域名称
	Code      string `json:"code" valid:"Required"` // 区域编码
	ParentID  int64  `json:"parent_id"`             // 父级区域ID
	Level     int    `json:"level"`                 // 层级：1-省/直辖市 2-市 3-区/县
	Status    int    `json:"status"`                // 状态：0-禁用 1-启用
	SortOrder int    `json:"sort_order"`            // 排序
}

// DeliveryAreaResponse 配送区域响应
type DeliveryAreaResponse struct {
	ID         int64                  `json:"id"`                 // 区域ID
	Name       string                 `json:"name"`               // 区域名称
	Code       string                 `json:"code"`               // 区域编码
	ParentID   int64                  `json:"parent_id"`          // 父级区域ID
	Level      int                    `json:"level"`              // 层级：1-省/直辖市 2-市 3-区/县
	Status     int                    `json:"status"`             // 状态：0-禁用 1-启用
	StatusDesc string                 `json:"status_desc"`        // 状态描述
	SortOrder  int                    `json:"sort_order"`         // 排序
	Children   []DeliveryAreaResponse `json:"children,omitempty"` // 子区域列表
	CreateTime time.Time              `json:"create_time"`        // 创建时间
	UpdateTime time.Time              `json:"update_time"`        // 更新时间
}

// 配送方式相关DTO

// DeliveryMethodRequest 配送方式请求
type DeliveryMethodRequest struct {
	Name        string `json:"name" valid:"Required"` // 配送方式名称
	Code        string `json:"code" valid:"Required"` // 配送方式编码
	Icon        string `json:"icon"`                  // 图标
	Description string `json:"description"`           // 描述
	Status      int    `json:"status"`                // 状态：0-禁用 1-启用
	IsDefault   bool   `json:"is_default"`            // 是否默认
	SortOrder   int    `json:"sort_order"`            // 排序
}

// DeliveryMethodResponse 配送方式响应
type DeliveryMethodResponse struct {
	ID          int64     `json:"id"`          // 配送方式ID
	Name        string    `json:"name"`        // 配送方式名称
	Code        string    `json:"code"`        // 配送方式编码
	Icon        string    `json:"icon"`        // 图标
	Description string    `json:"description"` // 描述
	Status      int       `json:"status"`      // 状态：0-禁用 1-启用
	StatusDesc  string    `json:"status_desc"` // 状态描述
	IsDefault   bool      `json:"is_default"`  // 是否默认
	SortOrder   int       `json:"sort_order"`  // 排序
	CreateTime  time.Time `json:"create_time"` // 创建时间
	UpdateTime  time.Time `json:"update_time"` // 更新时间
}

// 配送费用规则相关DTO

// DeliveryRuleRequest 配送费用规则请求
type DeliveryRuleRequest struct {
	MethodID         int64   `json:"method_id" valid:"Required"`    // 关联的配送方式ID
	AreaID           int64   `json:"area_id"`                       // 关联的配送区域ID，0表示全国
	ShopID           int64   `json:"shop_id"`                       // 关联的店铺ID，0表示平台规则
	FirstWeight      float64 `json:"first_weight" valid:"Required"` // 首重(kg)
	FirstFee         float64 `json:"first_fee" valid:"Required"`    // 首重费用
	AdditionalWeight float64 `json:"additional_weight"`             // 续重(kg)
	AdditionalFee    float64 `json:"additional_fee"`                // 续重费用
	MinAmount        float64 `json:"min_amount"`                    // 起送金额
	FreeAmount       float64 `json:"free_amount"`                   // 包邮金额
	MaxDistance      float64 `json:"max_distance"`                  // 最大配送距离(km)
	Status           int     `json:"status"`                        // 状态：0-禁用 1-启用
	StartTime        string  `json:"start_time"`                    // 开始时间 HH:MM
	EndTime          string  `json:"end_time"`                      // 结束时间 HH:MM
}

// DeliveryRuleResponse 配送费用规则响应
type DeliveryRuleResponse struct {
	ID               int64     `json:"id"`                  // 规则ID
	MethodID         int64     `json:"method_id"`           // 关联的配送方式ID
	MethodName       string    `json:"method_name"`         // 配送方式名称
	AreaID           int64     `json:"area_id"`             // 关联的配送区域ID
	AreaName         string    `json:"area_name"`           // 配送区域名称
	ShopID           int64     `json:"shop_id"`             // 关联的店铺ID
	ShopName         string    `json:"shop_name,omitempty"` // 店铺名称
	FirstWeight      float64   `json:"first_weight"`        // 首重(kg)
	FirstFee         float64   `json:"first_fee"`           // 首重费用
	AdditionalWeight float64   `json:"additional_weight"`   // 续重(kg)
	AdditionalFee    float64   `json:"additional_fee"`      // 续重费用
	MinAmount        float64   `json:"min_amount"`          // 起送金额
	FreeAmount       float64   `json:"free_amount"`         // 包邮金额
	MaxDistance      float64   `json:"max_distance"`        // 最大配送距离(km)
	Status           int       `json:"status"`              // 状态：0-禁用 1-启用
	StatusDesc       string    `json:"status_desc"`         // 状态描述
	StartTime        string    `json:"start_time"`          // 开始时间
	EndTime          string    `json:"end_time"`            // 结束时间
	CreateTime       time.Time `json:"create_time"`         // 创建时间
	UpdateTime       time.Time `json:"update_time"`         // 更新时间
}

// 配送时间段相关DTO

// DeliveryTimeSlotRequest 配送时间段请求
type DeliveryTimeSlotRequest struct {
	MethodID      int64   `json:"method_id" valid:"Required"`  // 关联的配送方式ID
	Name          string  `json:"name" valid:"Required"`       // 时间段名称
	StartTime     string  `json:"start_time" valid:"Required"` // 开始时间 HH:MM
	EndTime       string  `json:"end_time" valid:"Required"`   // 结束时间 HH:MM
	MaxOrders     int     `json:"max_orders"`                  // 最大订单数，0表示不限制
	AdditionalFee float64 `json:"additional_fee"`              // 附加费用
	Status        int     `json:"status"`                      // 状态：0-禁用 1-启用
	IsDefault     bool    `json:"is_default"`                  // 是否默认
	SortOrder     int     `json:"sort_order"`                  // 排序
}

// DeliveryTimeSlotResponse 配送时间段响应
type DeliveryTimeSlotResponse struct {
	ID            int64     `json:"id"`             // 时间段ID
	MethodID      int64     `json:"method_id"`      // 关联的配送方式ID
	MethodName    string    `json:"method_name"`    // 配送方式名称
	Name          string    `json:"name"`           // 时间段名称
	StartTime     string    `json:"start_time"`     // 开始时间
	EndTime       string    `json:"end_time"`       // 结束时间
	MaxOrders     int       `json:"max_orders"`     // 最大订单数
	CurrentOrders int       `json:"current_orders"` // 当前订单数
	AdditionalFee float64   `json:"additional_fee"` // 附加费用
	Status        int       `json:"status"`         // 状态：0-禁用 1-启用
	StatusDesc    string    `json:"status_desc"`    // 状态描述
	IsDefault     bool      `json:"is_default"`     // 是否默认
	SortOrder     int       `json:"sort_order"`     // 排序
	CreateTime    time.Time `json:"create_time"`    // 创建时间
	UpdateTime    time.Time `json:"update_time"`    // 更新时间
}

// 配送订单相关DTO

// CreateDeliveryOrderRequest 创建配送订单请求
type CreateDeliveryOrderRequest struct {
	OrderID               int64   `json:"order_id" valid:"Required"`         // 关联的订单ID
	OrderNo               string  `json:"order_no" valid:"Required"`         // 订单编号
	MethodID              int64   `json:"method_id" valid:"Required"`        // 配送方式ID
	ShopID                int64   `json:"shop_id" valid:"Required"`          // 店铺ID
	TimeSlotID            int64   `json:"time_slot_id"`                      // 配送时间段ID
	UserID                int64   `json:"user_id" valid:"Required"`          // 用户ID
	ReceiverName          string  `json:"receiver_name" valid:"Required"`    // 收货人姓名
	ReceiverPhone         string  `json:"receiver_phone" valid:"Required"`   // 收货人电话
	ReceiverAddress       string  `json:"receiver_address" valid:"Required"` // 收货地址
	ReceiverAddressDetail string  `json:"receiver_address_detail"`           // 收货详细地址
	ReceiverLat           float64 `json:"receiver_lat"`                      // 收货地址纬度
	ReceiverLng           float64 `json:"receiver_lng"`                      // 收货地址经度
	Weight                float64 `json:"weight"`                            // 重量(kg)
	Distance              float64 `json:"distance"`                          // 距离(km)
	OrderAmount           float64 `json:"order_amount"`                      // 订单金额
	AreaID                int64   `json:"area_id"`                           // 配送区域ID
	Remark                string  `json:"remark"`                            // 配送备注
}

// DeliveryOrderResponse 配送订单响应
type DeliveryOrderResponse struct {
	ID                    int64                      `json:"id"`                       // 配送ID
	OrderID               int64                      `json:"order_id"`                 // 关联的订单ID
	OrderNo               string                     `json:"order_no"`                 // 订单编号
	MethodID              int64                      `json:"method_id"`                // 配送方式ID
	MethodName            string                     `json:"method_name"`              // 配送方式名称
	ShopID                int64                      `json:"shop_id"`                  // 店铺ID
	ShopName              string                     `json:"shop_name,omitempty"`      // 店铺名称
	UserID                int64                      `json:"user_id"`                  // 用户ID
	RunnerID              int64                      `json:"runner_id"`                // 配送员ID
	RunnerName            string                     `json:"runner_name,omitempty"`    // 配送员名称
	RunnerPhone           string                     `json:"runner_phone,omitempty"`   // 配送员电话
	Status                int                        `json:"status"`                   // 配送状态
	StatusDesc            string                     `json:"status_desc"`              // 状态描述
	DeliveryFee           float64                    `json:"delivery_fee"`             // 配送费用
	TimeSlotID            int64                      `json:"time_slot_id"`             // 配送时间段ID
	TimeSlotName          string                     `json:"time_slot_name,omitempty"` // 配送时间段名称
	TimeSlotFee           float64                    `json:"time_slot_fee"`            // 时间段附加费用
	TotalFee              float64                    `json:"total_fee"`                // 总费用(配送费+时间段费)
	Weight                float64                    `json:"weight"`                   // 重量(kg)
	ExpectedTime          time.Time                  `json:"expected_time"`            // 预计送达时间
	ActualTime            time.Time                  `json:"actual_time"`              // 实际送达时间
	TrackingNo            string                     `json:"tracking_no"`              // 物流单号
	TrackingCompany       string                     `json:"tracking_company"`         // 物流公司
	ReceiverName          string                     `json:"receiver_name"`            // 收货人姓名
	ReceiverPhone         string                     `json:"receiver_phone"`           // 收货人电话
	ReceiverAddress       string                     `json:"receiver_address"`         // 收货地址
	ReceiverAddressDetail string                     `json:"receiver_address_detail"`  // 收货详细地址
	ReceiverLat           float64                    `json:"receiver_lat"`             // 收货地址纬度
	ReceiverLng           float64                    `json:"receiver_lng"`             // 收货地址经度
	Distance              float64                    `json:"distance"`                 // 配送距离(km)
	Remark                string                     `json:"remark"`                   // 配送备注
	CancelReason          string                     `json:"cancel_reason"`            // 取消原因
	Tracking              []DeliveryTrackingResponse `json:"tracking,omitempty"`       // 物流跟踪信息
	CreateTime            time.Time                  `json:"create_time"`              // 创建时间
	UpdateTime            time.Time                  `json:"update_time"`              // 更新时间
}

// UpdateDeliveryStatusRequest 更新配送状态请求
type UpdateDeliveryStatusRequest struct {
	Status      int    `json:"status" valid:"Required"` // 配送状态
	Description string `json:"description"`             // 描述
	Location    string `json:"location"`                // 地点
}

// CancelDeliveryRequest 取消配送请求
type CancelDeliveryRequest struct {
	Reason string `json:"reason" valid:"Required"` // 取消原因
}

// DeliveryFeeCalculateRequest 配送费用计算请求
type DeliveryFeeCalculateRequest struct {
	MethodID    int64   `json:"method_id" valid:"Required"` // 配送方式ID
	ShopID      int64   `json:"shop_id" valid:"Required"`   // 店铺ID
	AreaID      int64   `json:"area_id"`                    // 配送区域ID
	OrderAmount float64 `json:"order_amount"`               // 订单金额
	Weight      float64 `json:"weight"`                     // 重量(kg)
	Distance    float64 `json:"distance"`                   // 距离(km)
	TimeSlotID  int64   `json:"time_slot_id"`               // 配送时间段ID
}

// DeliveryFeeCalculateResponse 配送费用计算响应
type DeliveryFeeCalculateResponse struct {
	DeliveryFee     float64 `json:"delivery_fee"`      // 配送费用
	TimeSlotFee     float64 `json:"time_slot_fee"`     // 时间段附加费
	TotalFee        float64 `json:"total_fee"`         // 总费用
	IsFree          bool    `json:"is_free"`           // 是否免费
	UnderMinAmount  bool    `json:"under_min_amount"`  // 是否低于起送金额
	OverMaxDistance bool    `json:"over_max_distance"` // 是否超过最大配送距离
	MinAmount       float64 `json:"min_amount"`        // 起送金额
	MaxDistance     float64 `json:"max_distance"`      // 最大配送距离
}

// DeliveryTrackingResponse 配送物流跟踪响应
type DeliveryTrackingResponse struct {
	ID           int64     `json:"id"`            // 跟踪ID
	DeliveryID   int64     `json:"delivery_id"`   // 配送订单ID
	OrderNo      string    `json:"order_no"`      // 订单编号
	Status       int       `json:"status"`        // 状态
	StatusDesc   string    `json:"status_desc"`   // 状态描述
	Description  string    `json:"description"`   // 描述
	Location     string    `json:"location"`      // 地点
	OperatorID   int64     `json:"operator_id"`   // 操作人ID
	OperatorType int       `json:"operator_type"` // 操作人类型
	OperatorName string    `json:"operator_name"` // 操作人名称
	CreateTime   time.Time `json:"create_time"`   // 创建时间
}

// UpdateDeliveryOrderStatusRequest 更新配送订单状态请求
type UpdateDeliveryOrderStatusRequest struct {
	Status int    `json:"status" valid:"Required"` // 配送状态
	Remark string `json:"remark"`                  // 备注
}

// AssignRunnerRequest 分配配送员请求
type AssignRunnerRequest struct {
	RunnerID int64  `json:"runner_id" valid:"Required"` // 配送员ID
	ShopID   int64  `json:"shop_id"`                    // 店铺ID
	Remark   string `json:"remark"`                     // 备注
}

// UpdateTrackingInfoRequest 更新物流信息请求
type UpdateTrackingInfoRequest struct {
	TrackingNo      string `json:"tracking_no" valid:"Required"`      // 物流单号
	TrackingCompany string `json:"tracking_company" valid:"Required"` // 物流公司
	TrackingStatus  int64  `json:"tracking_status"`                   // 物流状态
	Remark          string `json:"remark"`                            // 备注
}

// CancelDeliveryOrderRequest 取消配送订单请求
type CancelDeliveryOrderRequest struct {
	Reason string `json:"reason" valid:"Required"` // 取消原因
}
