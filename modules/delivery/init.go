/**
 * 配送模块初始化
 *
 * 本文件负责配送模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保配送模块的功能正常启动。
 */

package delivery

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/models"
	"o_mall_backend/modules/delivery/routers"
)

// Init 初始化配送模块
func Init() {
	logs.Info("初始化配送模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.Init()

	logs.Info("配送模块初始化完成")
}

// 注册ORM模型
func registerModels() {
	logs.Info("注册配送模块ORM模型...")

	// 注册配送相关模型
	orm.RegisterModel(
		new(models.DeliveryArea),
		new(models.DeliveryMethod),
		new(models.DeliveryRule),
		new(models.DeliveryTimeSlot),
		new(models.DeliveryOrder),
		new(models.DeliveryTracking),
	)
}
