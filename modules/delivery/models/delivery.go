/**
 * delivery.go
 * 配送模型定义
 *
 * 本文件定义了配送相关的数据模型，包括配送区域、配送方式、配送费用规则等
 */

package models

import (
	"time"
)

// DeliveryArea 配送区域模型
type DeliveryArea struct {
	ID         int64     `orm:"auto;pk" json:"id" description:"区域ID，主键自增"`                              // 区域ID，主键自增
	Name       string    `orm:"size(100)" json:"name" description:"区域名称"`                               // 区域名称
	Code       string    `orm:"size(20);unique" json:"code" description:"区域编码，唯一标识"`                    // 区域编码，唯一标识
	ParentID   int64     `orm:"default(0)" json:"parent_id" description:"父级区域ID，顶级区域为0"`                // 父级区域ID，顶级区域为0
	Level      int       `orm:"default(1)" json:"level" description:"层级：1-省/直辖市 2-市 3-区/县"`             // 层级：1-省/直辖市 2-市 3-区/县
	Status     int       `orm:"default(1)" json:"status" description:"状态：0-禁用 1-启用"`                    // 状态：0-禁用 1-启用
	SortOrder  int       `orm:"default(0)" json:"sort_order" description:"排序值，数值越小越靠前"`                 // 排序值，数值越小越靠前
	CreateTime time.Time `orm:"auto_now_add;type(datetime)" json:"create_time" description:"创建时间，自动设置"` // 创建时间，自动设置
	UpdateTime time.Time `orm:"auto_now;type(datetime)" json:"update_time" description:"更新时间，自动更新"`     // 更新时间，自动更新
}

// DeliveryMethod 配送方式模型
type DeliveryMethod struct {
	ID          int64     `orm:"auto;pk" json:"id" description:"配送方式ID，主键自增"`                            // 配送方式ID，主键自增
	Name        string    `orm:"size(100)" json:"name" description:"配送方式名称"`                             // 配送方式名称
	Code        string    `orm:"size(20);unique" json:"code" description:"配送方式编码，唯一标识"`                  // 配送方式编码，唯一标识
	Icon        string    `orm:"size(255);null" json:"icon" description:"图标URL"`                         // 图标URL
	Description string    `orm:"size(500);null" json:"description" description:"配送方式描述"`                 // 配送方式描述
	Status      int       `orm:"default(1)" json:"status" description:"状态：0-禁用 1-启用"`                    // 状态：0-禁用 1-启用
	IsDefault   bool      `orm:"default(false)" json:"is_default" description:"是否默认配送方式"`                // 是否默认配送方式
	SortOrder   int       `orm:"default(0)" json:"sort_order" description:"排序值，数值越小越靠前"`                 // 排序值，数值越小越靠前
	CreateTime  time.Time `orm:"auto_now_add;type(datetime)" json:"create_time" description:"创建时间，自动设置"` // 创建时间，自动设置
	UpdateTime  time.Time `orm:"auto_now;type(datetime)" json:"update_time" description:"更新时间，自动更新"`     // 更新时间，自动更新
}

// DeliveryRule 配送费用规则模型
type DeliveryRule struct {
	ID               int64     `orm:"auto;pk" json:"id" description:"规则ID，主键自增"`                                      // 规则ID，主键自增
	MethodID         int64     `orm:"index" json:"method_id" description:"关联的配送方式ID"`                                 // 关联的配送方式ID
	AreaID           int64     `orm:"index" json:"area_id" description:"关联的配送区域ID，0表示全国"`                             // 关联的配送区域ID，0表示全国
	ShopID           int64     `orm:"index;default(0)" json:"shop_id" description:"关联的店铺ID，0表示平台规则"`                  // 关联的店铺ID，0表示平台规则
	FirstWeight      float64   `orm:"digits(10);decimals(2)" json:"first_weight" description:"首重(kg)"`                // 首重(kg)
	FirstFee         float64   `orm:"digits(10);decimals(2)" json:"first_fee" description:"首重费用(元)"`                  // 首重费用(元)
	AdditionalWeight float64   `orm:"digits(10);decimals(2)" json:"additional_weight" description:"续重(kg)"`           // 续重(kg)
	AdditionalFee    float64   `orm:"digits(10);decimals(2)" json:"additional_fee" description:"续重费用(元)"`             // 续重费用(元)
	MinAmount        float64   `orm:"digits(10);decimals(2);default(0)" json:"min_amount" description:"起送金额(元)"`      // 起送金额(元)
	FreeAmount       float64   `orm:"digits(10);decimals(2);default(0)" json:"free_amount" description:"包邮金额(元)"`     // 包邮金额(元)
	MaxDistance      float64   `orm:"digits(10);decimals(2);default(0)" json:"max_distance" description:"最大配送距离(km)"` // 最大配送距离(km)
	Status           int       `orm:"default(1)" json:"status" description:"状态：0-禁用 1-启用"`                            // 状态：0-禁用 1-启用
	StartTime        string    `orm:"size(10);null" json:"start_time" description:"配送开始时间 HH:MM"`                     // 配送开始时间 HH:MM
	EndTime          string    `orm:"size(10);null" json:"end_time" description:"配送结束时间 HH:MM"`                       // 配送结束时间 HH:MM
	CreateTime       time.Time `orm:"auto_now_add;type(datetime)" json:"create_time" description:"创建时间，自动设置"`         // 创建时间，自动设置
	UpdateTime       time.Time `orm:"auto_now;type(datetime)" json:"update_time" description:"更新时间，自动更新"`             // 更新时间，自动更新
}

// DeliveryTimeSlot 配送时间段模型
type DeliveryTimeSlot struct {
	ID            int64     `orm:"auto;pk" json:"id" description:"时间段ID，主键自增"`                                                  // 时间段ID，主键自增
	MethodID      int64     `orm:"index" json:"method_id" description:"关联的配送方式ID"`                                              // 关联的配送方式ID
	Name          string    `orm:"size(100)" json:"name" description:"时间段名称"`                                                   // 时间段名称
	StartTime     string    `orm:"size(8)" json:"start_time" description:"开始时间，格式为HH:MM"`                                       // 开始时间，格式为HH:MM
	EndTime       string    `orm:"size(8)" json:"end_time" description:"结束时间，格式为HH:MM"`                                         // 结束时间，格式为HH:MM
	AdditionalFee float64   `orm:"digits(10);decimals(2);default(0)" json:"additional_fee" description:"附加费用，使用该时间段配送需额外支付的费用"` // 附加费用，使用该时间段配送需额外支付的费用
	MaxOrderCount int       `orm:"default(0)" json:"max_orders" description:"最大订单数量，0表示不限制"`                                    // 最大订单数量，0表示不限制
	CurrentOrders int       `orm:"default(0)" json:"current_orders" description:"当前订单数量"`                                       // 当前订单数量
	Status        int       `orm:"default(1)" json:"status" description:"状态：0-禁用 1-启用"`                                         // 状态：0-禁用 1-启用
	IsDefault     bool      `orm:"default(false)" json:"is_default" description:"是否默认时间段"`                                      // 是否默认时间段
	SortOrder     int       `orm:"default(0)" json:"sort_order" description:"排序值，数值越小越靠前"`                                      // 排序值，数值越小越靠前
	CreateTime    time.Time `orm:"auto_now_add;type(datetime)" json:"create_time" description:"创建时间，自动设置"`                      // 创建时间，自动设置
	UpdateTime    time.Time `orm:"auto_now;type(datetime)" json:"update_time" description:"更新时间，自动更新"`                          // 更新时间，自动更新
}

// DeliveryOrder 配送订单模型
type DeliveryOrder struct {
	ID                    int64     `orm:"auto;pk" json:"id" description:"配送订单ID，主键自增"`                                     // 配送订单ID，主键自增
	OrderID               int64     `orm:"index;unique" json:"order_id" description:"关联的订单ID，唯一索引"`                         // 关联的订单ID，唯一索引
	OrderNo               string    `orm:"size(32);unique" json:"order_no" description:"订单编号，唯一标识"`                         // 订单编号，唯一标识
	MethodID              int64     `orm:"index" json:"method_id" description:"配送方式ID"`                                     // 配送方式ID
	ShopID                int64     `orm:"index" json:"shop_id" description:"店铺ID"`                                         // 店铺ID
	UserID                int64     `orm:"index" json:"user_id" description:"用户ID"`                                         // 用户ID
	RunnerID              int64     `orm:"index;default(0)" json:"runner_id" description:"配送员ID，自营配送时使用"`                   // 配送员ID，自营配送时使用
	Status                int       `orm:"default(0)" json:"status" description:"配送状态：0-待配送 1-配送中 2-已送达 3-已取消"`             // 配送状态：0-待配送 1-配送中 2-已送达 3-已取消
	DeliveryFee           float64   `orm:"digits(10);decimals(2)" json:"delivery_fee" description:"配送费用(元)"`                // 配送费用(元)
	TimeSlotID            int64     `orm:"default(0)" json:"time_slot_id" description:"配送时间段ID"`                            // 配送时间段ID
	TimeSlotFee           float64   `orm:"digits(10);decimals(2);default(0)" json:"time_slot_fee" description:"时间段附加费用(元)"` // 时间段附加费用(元)
	TotalFee              float64   `orm:"digits(10);decimals(2)" json:"total_fee" description:"总费用(元)，包含配送费和时间段附加费"`       // 总费用(元)，包含配送费和时间段附加费
	Weight                float64   `orm:"digits(10);decimals(2);default(0)" json:"weight" description:"重量(kg)"`            // 重量(kg)
	ExpectedTime          time.Time `orm:"null;type(datetime)" json:"expected_time" description:"预计送达时间"`                   // 预计送达时间
	ActualTime            time.Time `orm:"null;type(datetime)" json:"actual_time" description:"实际送达时间"`                     // 实际送达时间
	TrackingNo            string    `orm:"size(50);null" json:"tracking_no" description:"物流单号"`                             // 物流单号
	TrackingCompany       string    `orm:"size(50);null" json:"tracking_company" description:"物流公司名称"`                      // 物流公司名称
	ReceiverName          string    `orm:"size(50)" json:"receiver_name" description:"收货人姓名"`                               // 收货人姓名
	ReceiverPhone         string    `orm:"size(20)" json:"receiver_phone" description:"收货人电话"`                              // 收货人电话
	ReceiverAddress       string    `orm:"size(255)" json:"receiver_address" description:"收货地址"`                            // 收货地址
	ReceiverAddressDetail string    `orm:"size(255)" json:"receiver_address_detail" description:"收货详细地址"`                   // 收货详细地址
	ReceiverLat           float64   `orm:"digits(10);decimals(6)" json:"receiver_lat" description:"收货地址纬度"`                 // 收货地址纬度
	ReceiverLng           float64   `orm:"digits(10);decimals(6)" json:"receiver_lng" description:"收货地址经度"`                 // 收货地址经度
	Distance              float64   `orm:"digits(10);decimals(2);default(0)" json:"distance" description:"配送距离(km)"`        // 配送距离(km)
	Remark                string    `orm:"size(500);null" json:"remark" description:"配送备注"`                                 // 配送备注
	CancelReason          string    `orm:"size(500);null" json:"cancel_reason" description:"取消原因"`                          // 取消原因
	CreateTime            time.Time `orm:"auto_now_add;type(datetime)" json:"create_time" description:"创建时间，自动设置"`          // 创建时间，自动设置
	UpdateTime            time.Time `orm:"auto_now;type(datetime)" json:"update_time" description:"更新时间，自动更新"`              // 更新时间，自动更新
}

// DeliveryTracking 配送物流跟踪模型
type DeliveryTracking struct {
	ID           int64     `orm:"auto;pk" json:"id" description:"跟踪记录ID，主键自增，系统自动生成"`                             // 跟踪记录ID，主键自增
	DeliveryID   int64     `orm:"index" json:"delivery_id" description:"关联的配送订单ID，用于关联配送订单信息"`                    // 关联的配送订单ID
	OrderNo      string    `orm:"size(32)" json:"order_no" description:"订单编号，用于快速查询和关联订单"`                        // 订单编号
	Status       int       `orm:"default(0)" json:"status" description:"物流状态：0-待处理 1-已揽收 2-运输中 3-派送中 4-已签收 5-异常"` // 物流状态
	Location     string    `orm:"size(255);null" json:"location" description:"当前位置，包括城市、区域等具体位置信息"`               // 当前位置
	Description  string    `orm:"size(500)" json:"description" description:"物流描述信息，记录当前状态的详细说明"`                  // 物流描述信息
	OperatorID   int64     `orm:"default(0)" json:"operator_id" description:"操作人ID，记录执行此操作的人员ID"`                 // 操作人ID
	OperatorType int       `orm:"default(0)" json:"operator_type" description:"操作人类型：0-系统自动 1-店铺人员 2-配送员 3-管理员"`  // 操作人类型
	OperatorName string    `orm:"size(50);null" json:"operator_name" description:"操作人名称，记录执行操作人员的姓名"`             // 操作人名称
	CreateTime   time.Time `orm:"auto_now_add;type(datetime)" json:"create_time" description:"创建时间，系统自动生成的时间戳"`   // 创建时间
}
