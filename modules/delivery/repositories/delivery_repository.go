/**
 * delivery_repository.go
 * 配送模块仓库接口定义
 *
 * 本文件定义了配送模块的数据访问接口，包括配送区域、配送方式、配送费用规则等相关操作
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/delivery/models"
)

// DeliveryAreaRepository 配送区域仓库接口
type DeliveryAreaRepository interface {
	// 创建配送区域
	CreateDeliveryArea(ctx context.Context, area *models.DeliveryArea) (int64, error)

	// 获取配送区域详情
	GetDeliveryAreaByID(ctx context.Context, areaID int64) (*models.DeliveryArea, error)

	// 根据编码获取配送区域
	GetDeliveryAreaByCode(ctx context.Context, code string) (*models.DeliveryArea, error)

	// 更新配送区域
	UpdateDeliveryArea(ctx context.Context, area *models.DeliveryArea) error

	// 删除配送区域
	DeleteDeliveryArea(ctx context.Context, areaID int64) error

	// 获取配送区域列表
	ListDeliveryAreas(ctx context.Context, parentID int64, status int) ([]*models.DeliveryArea, error)

	// 获取配送区域树结构
	GetDeliveryAreaTree(ctx context.Context) ([]*models.DeliveryArea, error)
}

// DeliveryMethodRepository 配送方式仓库接口
type DeliveryMethodRepository interface {
	// 创建配送方式
	CreateDeliveryMethod(ctx context.Context, method *models.DeliveryMethod) (int64, error)

	// 获取配送方式详情
	GetDeliveryMethodByID(ctx context.Context, methodID int64) (*models.DeliveryMethod, error)

	// 根据编码获取配送方式
	GetDeliveryMethodByCode(ctx context.Context, code string) (*models.DeliveryMethod, error)

	// 更新配送方式
	UpdateDeliveryMethod(ctx context.Context, method *models.DeliveryMethod) error

	// 删除配送方式
	DeleteDeliveryMethod(ctx context.Context, methodID int64) error

	// 获取配送方式列表
	ListDeliveryMethods(ctx context.Context, status int) ([]*models.DeliveryMethod, error)

	// 设置默认配送方式
	SetDefaultDeliveryMethod(ctx context.Context, methodID int64) error
}

// DeliveryRuleRepository 配送费用规则仓库接口
type DeliveryRuleRepository interface {
	// 创建配送费用规则
	CreateDeliveryRule(ctx context.Context, rule *models.DeliveryRule) (int64, error)

	// 获取配送费用规则详情
	GetDeliveryRuleByID(ctx context.Context, ruleID int64) (*models.DeliveryRule, error)

	// 更新配送费用规则
	UpdateDeliveryRule(ctx context.Context, rule *models.DeliveryRule) error

	// 删除配送费用规则
	DeleteDeliveryRule(ctx context.Context, ruleID int64) error

	// 获取配送费用规则列表
	ListDeliveryRules(ctx context.Context, methodID, areaID, shopID int64) ([]*models.DeliveryRule, error)

	// 获取适用的配送费用规则
	GetApplicableDeliveryRule(ctx context.Context, methodID, areaID, shopID int64) (*models.DeliveryRule, error)
}

// DeliveryTimeSlotRepository 配送时间段仓库接口
type DeliveryTimeSlotRepository interface {
	// 创建配送时间段
	CreateDeliveryTimeSlot(ctx context.Context, timeSlot *models.DeliveryTimeSlot) (int64, error)

	// 获取配送时间段详情
	GetDeliveryTimeSlotByID(ctx context.Context, timeSlotID int64) (*models.DeliveryTimeSlot, error)

	// 更新配送时间段
	UpdateDeliveryTimeSlot(ctx context.Context, timeSlot *models.DeliveryTimeSlot) error

	// 删除配送时间段
	DeleteDeliveryTimeSlot(ctx context.Context, timeSlotID int64) error

	// 获取配送时间段列表
	ListDeliveryTimeSlots(ctx context.Context, methodID int64, status int) ([]*models.DeliveryTimeSlot, error)

	// 设置默认配送时间段
	SetDefaultDeliveryTimeSlot(ctx context.Context, methodID, timeSlotID int64) error

	// 更新配送时间段订单数
	UpdateDeliveryTimeSlotOrderCount(ctx context.Context, timeSlotID int64, increment bool) error

	// 获取配送时间段当前订单数
	GetDeliveryTimeSlotOrderCount(ctx context.Context, timeSlotID int64) (int, error)

	// 增加配送时间段订单数
	IncrementOrderCount(ctx context.Context, timeSlotID int64) error

	// 减少配送时间段订单数
	DecrementOrderCount(ctx context.Context, timeSlotID int64) error
}

// DeliveryOrderRepository 配送订单仓库接口
type DeliveryOrderRepository interface {
	// 创建配送订单
	CreateDeliveryOrder(ctx context.Context, order *models.DeliveryOrder) (int64, error)

	// 获取配送订单详情
	GetDeliveryOrderByID(ctx context.Context, orderID int64) (*models.DeliveryOrder, error)

	// 根据订单ID获取配送订单
	GetDeliveryOrderByOrderID(ctx context.Context, orderID int64) (*models.DeliveryOrder, error)

	// 根据订单编号获取配送订单
	GetDeliveryOrderByOrderNo(ctx context.Context, orderNo string) (*models.DeliveryOrder, error)

	// 更新配送订单
	UpdateDeliveryOrder(ctx context.Context, order *models.DeliveryOrder) error

	// 更新配送订单状态
	UpdateDeliveryOrderStatus(ctx context.Context, deliveryID int64, status int) error

	// 取消配送订单
	CancelDeliveryOrder(ctx context.Context, deliveryID int64, reason string) error

	// 分配配送员
	AssignRunner(ctx context.Context, deliveryID, runnerID int64) error

	// 更新物流信息
	UpdateTrackingInfo(ctx context.Context, deliveryID int64, trackingNo, trackingCompany string) error

	// 获取用户的配送订单列表
	ListUserDeliveryOrders(ctx context.Context, userID int64, status int, page, pageSize int) ([]*models.DeliveryOrder, int64, error)

	// 获取店铺的配送订单列表
	ListShopDeliveryOrders(ctx context.Context, shopID int64, status int, page, pageSize int) ([]*models.DeliveryOrder, int64, error)

	// 获取配送员的配送订单列表
	ListRunnerDeliveryOrders(ctx context.Context, runnerID int64, status int, page, pageSize int) ([]*models.DeliveryOrder, int64, error)

	// 创建配送物流跟踪记录
	CreateDeliveryTracking(ctx context.Context, tracking *models.DeliveryTracking) (int64, error)

	// 获取配送物流跟踪记录列表
	ListDeliveryTrackings(ctx context.Context, deliveryID int64) ([]*models.DeliveryTracking, error)
}
