/**
 * factory.go
 * 配送模块仓库工厂函数
 *
 * 本文件提供了创建配送模块各种仓库实例的工厂函数
 */

package repositories

import (
	"o_mall_backend/modules/delivery/repositories/impl"
)

// NewDeliveryAreaRepository 创建配送区域仓库实例
func NewDeliveryAreaRepository() DeliveryAreaRepository {
	return &impl.DeliveryAreaRepositoryImpl{}
}

// NewDeliveryMethodRepository 创建配送方式仓库实例
func NewDeliveryMethodRepository() DeliveryMethodRepository {
	return &impl.DeliveryMethodRepositoryImpl{}
}

// NewDeliveryRuleRepository 创建配送规则仓库实例
func NewDeliveryRuleRepository() DeliveryRuleRepository {
	return &impl.DeliveryRuleRepositoryImpl{}
}

// NewDeliveryTimeSlotRepository 创建配送时间段仓库实例
func NewDeliveryTimeSlotRepository() DeliveryTimeSlotRepository {
	return &impl.DeliveryTimeSlotRepositoryImpl{}
}

// NewDeliveryOrderRepository 创建配送订单仓库实例
func NewDeliveryOrderRepository() DeliveryOrderRepository {
	return &impl.DeliveryOrderRepositoryImpl{}
}
