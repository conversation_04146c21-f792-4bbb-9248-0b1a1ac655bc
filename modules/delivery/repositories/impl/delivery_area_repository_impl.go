/**
 * delivery_area_repository_impl.go
 * 配送区域仓库实现
 *
 * 本文件实现了配送区域数据的持久化操作，包括配送区域的创建、查询、更新和删除等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/models"
)

// DeliveryAreaRepositoryImpl 配送区域仓库实现
type DeliveryAreaRepositoryImpl struct{}

// CreateDeliveryArea 创建配送区域
func (r *DeliveryAreaRepositoryImpl) CreateDeliveryArea(ctx context.Context, area *models.DeliveryArea) (int64, error) {
	o := orm.NewOrm()

	// 检查编码是否已存在
	count, err := o.QueryTable(new(models.DeliveryArea)).Filter("code", area.Code).Count()
	if err != nil {
		logs.Error("查询配送区域编码是否存在失败: %v", err)
		return 0, fmt.Errorf("创建配送区域失败: %v", err)
	}

	if count > 0 {
		return 0, errors.New("配送区域编码已存在")
	}

	id, err := o.Insert(area)
	if err != nil {
		return 0, fmt.Errorf("创建配送区域失败: %v", err)
	}

	return id, nil
}

// GetDeliveryAreaByID 获取配送区域详情
func (r *DeliveryAreaRepositoryImpl) GetDeliveryAreaByID(ctx context.Context, areaID int64) (*models.DeliveryArea, error) {
	o := orm.NewOrm()
	area := &models.DeliveryArea{ID: areaID}

	err := o.Read(area)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送区域不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送区域失败: %v", err)
	}

	return area, nil
}

// GetDeliveryAreaByCode 根据编码获取配送区域
func (r *DeliveryAreaRepositoryImpl) GetDeliveryAreaByCode(ctx context.Context, code string) (*models.DeliveryArea, error) {
	o := orm.NewOrm()
	area := &models.DeliveryArea{}

	err := o.QueryTable(new(models.DeliveryArea)).Filter("code", code).One(area)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送区域不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送区域失败: %v", err)
	}

	return area, nil
}

// UpdateDeliveryArea 更新配送区域
func (r *DeliveryAreaRepositoryImpl) UpdateDeliveryArea(ctx context.Context, area *models.DeliveryArea) error {
	o := orm.NewOrm()

	// 检查区域是否存在
	existArea := &models.DeliveryArea{ID: area.ID}
	err := o.Read(existArea)
	if err == orm.ErrNoRows {
		return errors.New("配送区域不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送区域失败: %v", err)
	}

	// 检查编码是否已被其他区域使用
	if area.Code != existArea.Code {
		count, err := o.QueryTable(new(models.DeliveryArea)).Filter("code", area.Code).Exclude("id", area.ID).Count()
		if err != nil {
			return fmt.Errorf("查询配送区域编码失败: %v", err)
		}

		if count > 0 {
			return errors.New("配送区域编码已被其他区域使用")
		}
	}

	_, err = o.Update(area)
	if err != nil {
		return fmt.Errorf("更新配送区域失败: %v", err)
	}

	return nil
}

// DeleteDeliveryArea 删除配送区域
func (r *DeliveryAreaRepositoryImpl) DeleteDeliveryArea(ctx context.Context, areaID int64) error {
	o := orm.NewOrm()

	// 检查是否有子区域
	count, err := o.QueryTable(new(models.DeliveryArea)).Filter("parent_id", areaID).Count()
	if err != nil {
		return fmt.Errorf("查询子区域失败: %v", err)
	}

	if count > 0 {
		return errors.New("该区域下存在子区域，无法删除")
	}

	// 检查是否有关联的配送规则
	count, err = o.QueryTable(new(models.DeliveryRule)).Filter("area_id", areaID).Count()
	if err != nil {
		return fmt.Errorf("查询关联配送规则失败: %v", err)
	}

	if count > 0 {
		return errors.New("该区域已关联配送规则，无法删除")
	}

	area := &models.DeliveryArea{ID: areaID}
	_, err = o.Delete(area)
	if err != nil {
		return fmt.Errorf("删除配送区域失败: %v", err)
	}

	return nil
}

// ListDeliveryAreas 获取配送区域列表
func (r *DeliveryAreaRepositoryImpl) ListDeliveryAreas(ctx context.Context, parentID int64, status int) ([]*models.DeliveryArea, error) {
	o := orm.NewOrm()
	var areas []*models.DeliveryArea

	query := o.QueryTable(new(models.DeliveryArea)).Filter("parent_id", parentID).OrderBy("sort_order", "id")

	if status != -1 {
		query = query.Filter("status", status)
	}

	_, err := query.All(&areas)
	if err != nil {
		return nil, fmt.Errorf("获取配送区域列表失败: %v", err)
	}

	return areas, nil
}

// GetDeliveryAreaTree 获取配送区域树结构
func (r *DeliveryAreaRepositoryImpl) GetDeliveryAreaTree(ctx context.Context) ([]*models.DeliveryArea, error) {
	// 获取所有启用的区域
	o := orm.NewOrm()
	var allAreas []*models.DeliveryArea

	_, err := o.QueryTable(new(models.DeliveryArea)).Filter("status", 1).OrderBy("sort_order", "id").All(&allAreas)
	if err != nil {
		return nil, fmt.Errorf("获取配送区域列表失败: %v", err)
	}

	// 构建区域树
	areaMap := make(map[int64][]*models.DeliveryArea)
	for _, area := range allAreas {
		areaMap[area.ParentID] = append(areaMap[area.ParentID], area)
	}

	// 获取顶级区域
	rootAreas := areaMap[0]

	// 递归构建树结构
	var buildAreaTree func(areas []*models.DeliveryArea)
	buildAreaTree = func(areas []*models.DeliveryArea) {
		for _, area := range areas {
			if children, ok := areaMap[area.ID]; ok {
				// 这里无法直接设置Children字段，因为models.DeliveryArea中没有该字段
				// 在实际使用时，需要在服务层处理或修改模型添加Children字段
				buildAreaTree(children)
			}
		}
	}

	buildAreaTree(rootAreas)

	return rootAreas, nil
}

// NewDeliveryAreaRepository 创建配送区域仓库
func NewDeliveryAreaRepository() *DeliveryAreaRepositoryImpl {
	return &DeliveryAreaRepositoryImpl{}
}
