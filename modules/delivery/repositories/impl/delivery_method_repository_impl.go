/**
 * delivery_method_repository_impl.go
 * 配送方式仓库实现
 *
 * 本文件实现了配送方式数据的持久化操作，包括配送方式的创建、查询、更新和删除等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/models"
)

// DeliveryMethodRepositoryImpl 配送方式仓库实现
type DeliveryMethodRepositoryImpl struct{}

// CreateDeliveryMethod 创建配送方式
func (r *DeliveryMethodRepositoryImpl) CreateDeliveryMethod(ctx context.Context, method *models.DeliveryMethod) (int64, error) {
	o := orm.NewOrm()

	// 检查编码是否已存在
	count, err := o.QueryTable(new(models.DeliveryMethod)).Filter("code", method.Code).Count()
	if err != nil {
		logs.Error("查询配送方式编码是否存在失败: %v", err)
		return 0, fmt.Errorf("创建配送方式失败: %v", err)
	}

	if count > 0 {
		return 0, errors.New("配送方式编码已存在")
	}

	// 如果是默认配送方式，需要将其他配送方式设为非默认
	if method.IsDefault {
		_, err = o.QueryTable(new(models.DeliveryMethod)).Update(orm.Params{
			"is_default": false,
		})
		if err != nil {
			return 0, fmt.Errorf("更新其他配送方式为非默认失败: %v", err)
		}
	}

	id, err := o.Insert(method)
	if err != nil {
		return 0, fmt.Errorf("创建配送方式失败: %v", err)
	}

	return id, nil
}

// GetDeliveryMethodByID 获取配送方式详情
func (r *DeliveryMethodRepositoryImpl) GetDeliveryMethodByID(ctx context.Context, methodID int64) (*models.DeliveryMethod, error) {
	o := orm.NewOrm()
	method := &models.DeliveryMethod{ID: methodID}

	err := o.Read(method)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送方式不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送方式失败: %v", err)
	}

	return method, nil
}

// GetDeliveryMethodByCode 根据编码获取配送方式
func (r *DeliveryMethodRepositoryImpl) GetDeliveryMethodByCode(ctx context.Context, code string) (*models.DeliveryMethod, error) {
	o := orm.NewOrm()
	method := &models.DeliveryMethod{}

	err := o.QueryTable(new(models.DeliveryMethod)).Filter("code", code).One(method)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送方式不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送方式失败: %v", err)
	}

	return method, nil
}

// UpdateDeliveryMethod 更新配送方式
func (r *DeliveryMethodRepositoryImpl) UpdateDeliveryMethod(ctx context.Context, method *models.DeliveryMethod) error {
	o := orm.NewOrm()

	// 检查配送方式是否存在
	existMethod := &models.DeliveryMethod{ID: method.ID}
	err := o.Read(existMethod)
	if err == orm.ErrNoRows {
		return errors.New("配送方式不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送方式失败: %v", err)
	}

	// 检查编码是否已被其他配送方式使用
	if method.Code != existMethod.Code {
		count, err := o.QueryTable(new(models.DeliveryMethod)).Filter("code", method.Code).Exclude("id", method.ID).Count()
		if err != nil {
			return fmt.Errorf("查询配送方式编码失败: %v", err)
		}

		if count > 0 {
			return errors.New("配送方式编码已被其他配送方式使用")
		}
	}

	// 如果是默认配送方式，需要将其他配送方式设为非默认
	if method.IsDefault && !existMethod.IsDefault {
		_, err = o.QueryTable(new(models.DeliveryMethod)).Exclude("id", method.ID).Update(orm.Params{
			"is_default": false,
		})
		if err != nil {
			return fmt.Errorf("更新其他配送方式为非默认失败: %v", err)
		}
	}

	_, err = o.Update(method)
	if err != nil {
		return fmt.Errorf("更新配送方式失败: %v", err)
	}

	return nil
}

// DeleteDeliveryMethod 删除配送方式
func (r *DeliveryMethodRepositoryImpl) DeleteDeliveryMethod(ctx context.Context, methodID int64) error {
	o := orm.NewOrm()

	// 检查是否有关联的配送规则
	count, err := o.QueryTable(new(models.DeliveryRule)).Filter("method_id", methodID).Count()
	if err != nil {
		return fmt.Errorf("查询关联配送规则失败: %v", err)
	}

	if count > 0 {
		return errors.New("该配送方式已关联配送规则，无法删除")
	}

	// 检查是否有关联的配送时间段
	count, err = o.QueryTable(new(models.DeliveryTimeSlot)).Filter("method_id", methodID).Count()
	if err != nil {
		return fmt.Errorf("查询关联配送时间段失败: %v", err)
	}

	if count > 0 {
		return errors.New("该配送方式已关联配送时间段，无法删除")
	}

	// 检查是否有关联的配送订单
	count, err = o.QueryTable(new(models.DeliveryOrder)).Filter("method_id", methodID).Count()
	if err != nil {
		return fmt.Errorf("查询关联配送订单失败: %v", err)
	}

	if count > 0 {
		return errors.New("该配送方式已关联配送订单，无法删除")
	}

	// 检查是否为默认配送方式
	method := &models.DeliveryMethod{ID: methodID}
	err = o.Read(method)
	if err != nil {
		return fmt.Errorf("获取配送方式失败: %v", err)
	}

	if method.IsDefault {
		return errors.New("默认配送方式无法删除")
	}

	_, err = o.Delete(method)
	if err != nil {
		return fmt.Errorf("删除配送方式失败: %v", err)
	}

	return nil
}

// ListDeliveryMethods 获取配送方式列表
func (r *DeliveryMethodRepositoryImpl) ListDeliveryMethods(ctx context.Context, status int) ([]*models.DeliveryMethod, error) {
	o := orm.NewOrm()
	var methods []*models.DeliveryMethod

	query := o.QueryTable(new(models.DeliveryMethod)).OrderBy("sort_order", "id")

	if status != -1 {
		query = query.Filter("status", status)
	}

	_, err := query.All(&methods)
	if err != nil {
		return nil, fmt.Errorf("获取配送方式列表失败: %v", err)
	}

	return methods, nil
}

// SetDefaultDeliveryMethod 设置默认配送方式
func (r *DeliveryMethodRepositoryImpl) SetDefaultDeliveryMethod(ctx context.Context, methodID int64) error {
	o := orm.NewOrm()

	// 检查配送方式是否存在
	method := &models.DeliveryMethod{ID: methodID}
	err := o.Read(method)
	if err == orm.ErrNoRows {
		return errors.New("配送方式不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送方式失败: %v", err)
	}

	// 将所有配送方式设为非默认
	_, err = o.QueryTable(new(models.DeliveryMethod)).Update(orm.Params{
		"is_default": false,
	})
	if err != nil {
		return fmt.Errorf("更新配送方式为非默认失败: %v", err)
	}

	// 设置指定配送方式为默认
	method.IsDefault = true
	_, err = o.Update(method, "is_default")
	if err != nil {
		return fmt.Errorf("设置默认配送方式失败: %v", err)
	}

	return nil
}

// NewDeliveryMethodRepository 创建配送方式仓库
func NewDeliveryMethodRepository() *DeliveryMethodRepositoryImpl {
	return &DeliveryMethodRepositoryImpl{}
}
