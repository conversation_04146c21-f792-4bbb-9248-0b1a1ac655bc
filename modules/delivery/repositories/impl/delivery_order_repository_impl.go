/**
 * delivery_order_repository_impl.go
 * 配送订单仓库实现
 *
 * 本文件实现了配送订单数据的持久化操作，包括配送订单的创建、查询、更新和跟踪记录等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/models"
)

// DeliveryOrderRepositoryImpl 配送订单仓库实现
type DeliveryOrderRepositoryImpl struct{}

// CreateDeliveryOrder 创建配送订单
func (r *DeliveryOrderRepositoryImpl) CreateDeliveryOrder(ctx context.Context, order *models.DeliveryOrder) (int64, error) {
	o := orm.NewOrm()

	// 检查订单ID是否已存在配送订单
	count, err := o.QueryTable(new(models.DeliveryOrder)).Filter("order_id", order.OrderID).Count()
	if err != nil {
		logs.Error("查询订单ID是否已存在配送订单失败: %v", err)
		return 0, fmt.Errorf("创建配送订单失败: %v", err)
	}

	if count > 0 {
		return 0, errors.New("该订单已存在配送记录")
	}

	id, err := o.Insert(order)
	if err != nil {
		return 0, fmt.Errorf("创建配送订单失败: %v", err)
	}

	return id, nil
}

// GetDeliveryOrderByID 获取配送订单详情
func (r *DeliveryOrderRepositoryImpl) GetDeliveryOrderByID(ctx context.Context, orderID int64) (*models.DeliveryOrder, error) {
	o := orm.NewOrm()
	order := &models.DeliveryOrder{ID: orderID}

	err := o.Read(order)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送订单不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送订单失败: %v", err)
	}

	return order, nil
}

// GetDeliveryOrderByOrderID 根据订单ID获取配送订单
func (r *DeliveryOrderRepositoryImpl) GetDeliveryOrderByOrderID(ctx context.Context, orderID int64) (*models.DeliveryOrder, error) {
	o := orm.NewOrm()
	order := &models.DeliveryOrder{}

	err := o.QueryTable(new(models.DeliveryOrder)).
		Filter("order_id", orderID).
		One(order)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送订单不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送订单失败: %v", err)
	}

	return order, nil
}

// GetDeliveryOrderByOrderNo 根据订单编号获取配送订单
func (r *DeliveryOrderRepositoryImpl) GetDeliveryOrderByOrderNo(ctx context.Context, orderNo string) (*models.DeliveryOrder, error) {
	o := orm.NewOrm()
	order := &models.DeliveryOrder{}

	err := o.QueryTable(new(models.DeliveryOrder)).
		Filter("order_no", orderNo).
		One(order)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送订单不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送订单失败: %v", err)
	}

	return order, nil
}

// UpdateDeliveryOrder 更新配送订单
func (r *DeliveryOrderRepositoryImpl) UpdateDeliveryOrder(ctx context.Context, order *models.DeliveryOrder) error {
	o := orm.NewOrm()

	// 检查订单是否存在
	existOrder := &models.DeliveryOrder{ID: order.ID}
	err := o.Read(existOrder)
	if err == orm.ErrNoRows {
		return errors.New("配送订单不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送订单失败: %v", err)
	}

	_, err = o.Update(order)
	if err != nil {
		return fmt.Errorf("更新配送订单失败: %v", err)
	}

	return nil
}

// UpdateDeliveryOrderStatus 更新配送订单状态
func (r *DeliveryOrderRepositoryImpl) UpdateDeliveryOrderStatus(ctx context.Context, deliveryID int64, status int) error {
	o := orm.NewOrm()

	// 检查订单是否存在
	order := &models.DeliveryOrder{ID: deliveryID}
	err := o.Read(order)
	if err == orm.ErrNoRows {
		return errors.New("配送订单不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送订单失败: %v", err)
	}

	// 检查状态变更是否合法
	if order.Status == status {
		return nil // 状态未变更，直接返回
	}

	// 更新状态
	order.Status = status
	if status == 2 { // 已送达
		order.ActualTime = time.Now()
	}

	_, err = o.Update(order, "status", "actual_time", "update_time")
	if err != nil {
		return fmt.Errorf("更新配送订单状态失败: %v", err)
	}

	return nil
}

// CancelDeliveryOrder 取消配送订单
func (r *DeliveryOrderRepositoryImpl) CancelDeliveryOrder(ctx context.Context, deliveryID int64, reason string) error {
	o := orm.NewOrm()

	// 检查订单是否存在
	order := &models.DeliveryOrder{ID: deliveryID}
	err := o.Read(order)
	if err == orm.ErrNoRows {
		return errors.New("配送订单不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送订单失败: %v", err)
	}

	// 检查状态是否可取消
	if order.Status == 2 { // 已送达
		return errors.New("已送达的订单不能取消")
	}

	// 更新状态为已取消
	order.Status = 3 // 已取消
	order.CancelReason = reason

	_, err = o.Update(order, "status", "cancel_reason", "update_time")
	if err != nil {
		return fmt.Errorf("取消配送订单失败: %v", err)
	}

	return nil
}

// AssignRunner 分配配送员
func (r *DeliveryOrderRepositoryImpl) AssignRunner(ctx context.Context, deliveryID, runnerID int64) error {
	o := orm.NewOrm()

	// 检查订单是否存在
	order := &models.DeliveryOrder{ID: deliveryID}
	err := o.Read(order)
	if err == orm.ErrNoRows {
		return errors.New("配送订单不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送订单失败: %v", err)
	}

	// 检查状态是否可分配
	if order.Status != 0 { // 待配送
		return errors.New("只有待配送状态的订单可以分配配送员")
	}

	// 更新配送员并将状态改为配送中
	order.RunnerID = runnerID
	order.Status = 1 // 配送中

	_, err = o.Update(order, "runner_id", "status", "update_time")
	if err != nil {
		return fmt.Errorf("分配配送员失败: %v", err)
	}

	return nil
}

// UpdateTrackingInfo 更新物流信息
func (r *DeliveryOrderRepositoryImpl) UpdateTrackingInfo(ctx context.Context, deliveryID int64, trackingNo, trackingCompany string) error {
	o := orm.NewOrm()

	// 检查订单是否存在
	order := &models.DeliveryOrder{ID: deliveryID}
	err := o.Read(order)
	if err == orm.ErrNoRows {
		return errors.New("配送订单不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送订单失败: %v", err)
	}

	// 更新物流信息
	order.TrackingNo = trackingNo
	order.TrackingCompany = trackingCompany

	_, err = o.Update(order, "tracking_no", "tracking_company", "update_time")
	if err != nil {
		return fmt.Errorf("更新物流信息失败: %v", err)
	}

	return nil
}

// ListUserDeliveryOrders 获取用户的配送订单列表
func (r *DeliveryOrderRepositoryImpl) ListUserDeliveryOrders(ctx context.Context, userID int64, status int, page, pageSize int) ([]*models.DeliveryOrder, int64, error) {
	o := orm.NewOrm()
	var orders []*models.DeliveryOrder

	query := o.QueryTable(new(models.DeliveryOrder)).Filter("user_id", userID)

	if status != -1 {
		query = query.Filter("status", status)
	}

	// 获取总数
	count, err := query.Count()
	if err != nil {
		return nil, 0, fmt.Errorf("获取配送订单数量失败: %v", err)
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = query.OrderBy("-create_time").Limit(pageSize, offset).All(&orders)
	} else {
		_, err = query.OrderBy("-create_time").All(&orders)
	}

	if err != nil {
		return nil, 0, fmt.Errorf("获取配送订单列表失败: %v", err)
	}

	return orders, count, nil
}

// ListShopDeliveryOrders 获取店铺的配送订单列表
func (r *DeliveryOrderRepositoryImpl) ListShopDeliveryOrders(ctx context.Context, shopID int64, status int, page, pageSize int) ([]*models.DeliveryOrder, int64, error) {
	o := orm.NewOrm()
	var orders []*models.DeliveryOrder

	query := o.QueryTable(new(models.DeliveryOrder)).Filter("shop_id", shopID)

	if status != -1 {
		query = query.Filter("status", status)
	}

	// 获取总数
	count, err := query.Count()
	if err != nil {
		return nil, 0, fmt.Errorf("获取配送订单数量失败: %v", err)
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = query.OrderBy("-create_time").Limit(pageSize, offset).All(&orders)
	} else {
		_, err = query.OrderBy("-create_time").All(&orders)
	}

	if err != nil {
		return nil, 0, fmt.Errorf("获取配送订单列表失败: %v", err)
	}

	return orders, count, nil
}

// ListRunnerDeliveryOrders 获取配送员的配送订单列表
func (r *DeliveryOrderRepositoryImpl) ListRunnerDeliveryOrders(ctx context.Context, runnerID int64, status int, page, pageSize int) ([]*models.DeliveryOrder, int64, error) {
	o := orm.NewOrm()
	var orders []*models.DeliveryOrder

	query := o.QueryTable(new(models.DeliveryOrder)).Filter("runner_id", runnerID)

	if status != -1 {
		query = query.Filter("status", status)
	}

	// 获取总数
	count, err := query.Count()
	if err != nil {
		return nil, 0, fmt.Errorf("获取配送订单数量失败: %v", err)
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = query.OrderBy("-create_time").Limit(pageSize, offset).All(&orders)
	} else {
		_, err = query.OrderBy("-create_time").All(&orders)
	}

	if err != nil {
		return nil, 0, fmt.Errorf("获取配送订单列表失败: %v", err)
	}

	return orders, count, nil
}

// CreateDeliveryTracking 创建配送物流跟踪记录
func (r *DeliveryOrderRepositoryImpl) CreateDeliveryTracking(ctx context.Context, tracking *models.DeliveryTracking) (int64, error) {
	o := orm.NewOrm()

	// 检查配送订单是否存在
	order := &models.DeliveryOrder{ID: tracking.DeliveryID}
	err := o.Read(order)
	if err == orm.ErrNoRows {
		return 0, errors.New("配送订单不存在")
	}
	if err != nil {
		return 0, fmt.Errorf("获取配送订单失败: %v", err)
	}

	// 如果跟踪记录没有设置订单编号，则使用配送订单的订单编号
	if tracking.OrderNo == "" {
		tracking.OrderNo = order.OrderNo
	}

	id, err := o.Insert(tracking)
	if err != nil {
		return 0, fmt.Errorf("创建配送物流跟踪记录失败: %v", err)
	}

	return id, nil
}

// ListDeliveryTrackings 获取配送物流跟踪记录列表
func (r *DeliveryOrderRepositoryImpl) ListDeliveryTrackings(ctx context.Context, deliveryID int64) ([]*models.DeliveryTracking, error) {
	o := orm.NewOrm()
	var trackings []*models.DeliveryTracking

	_, err := o.QueryTable(new(models.DeliveryTracking)).
		Filter("delivery_id", deliveryID).
		OrderBy("-create_time").
		All(&trackings)

	if err != nil {
		return nil, fmt.Errorf("获取配送物流跟踪记录列表失败: %v", err)
	}

	return trackings, nil
}

// NewDeliveryOrderRepository 创建配送订单仓库
func NewDeliveryOrderRepository() *DeliveryOrderRepositoryImpl {
	return &DeliveryOrderRepositoryImpl{}
}
