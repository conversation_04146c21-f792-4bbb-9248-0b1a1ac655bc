/**
 * delivery_rule_repository_impl.go
 * 配送费用规则仓库实现
 *
 * 本文件实现了配送费用规则数据的持久化操作，包括配送费用规则的创建、查询、更新和删除等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/models"
)

// DeliveryRuleRepositoryImpl 配送费用规则仓库实现
type DeliveryRuleRepositoryImpl struct{}

// CreateDeliveryRule 创建配送费用规则
func (r *DeliveryRuleRepositoryImpl) CreateDeliveryRule(ctx context.Context, rule *models.DeliveryRule) (int64, error) {
	o := orm.NewOrm()

	// 检查是否已存在相同配送方式、区域和店铺的规则
	count, err := o.QueryTable(new(models.DeliveryRule)).
		Filter("method_id", rule.MethodID).
		Filter("area_id", rule.AreaID).
		Filter("shop_id", rule.ShopID).
		Count()
	if err != nil {
		logs.Error("查询配送规则是否存在失败: %v", err)
		return 0, fmt.Errorf("创建配送规则失败: %v", err)
	}

	if count > 0 {
		return 0, errors.New("该区域和配送方式已存在配送规则")
	}

	id, err := o.Insert(rule)
	if err != nil {
		return 0, fmt.Errorf("创建配送规则失败: %v", err)
	}

	return id, nil
}

// GetDeliveryRuleByID 获取配送费用规则详情
func (r *DeliveryRuleRepositoryImpl) GetDeliveryRuleByID(ctx context.Context, ruleID int64) (*models.DeliveryRule, error) {
	o := orm.NewOrm()
	rule := &models.DeliveryRule{ID: ruleID}

	err := o.Read(rule)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送规则不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送规则失败: %v", err)
	}

	return rule, nil
}

// UpdateDeliveryRule 更新配送费用规则
func (r *DeliveryRuleRepositoryImpl) UpdateDeliveryRule(ctx context.Context, rule *models.DeliveryRule) error {
	o := orm.NewOrm()

	// 检查规则是否存在
	existRule := &models.DeliveryRule{ID: rule.ID}
	err := o.Read(existRule)
	if err == orm.ErrNoRows {
		return errors.New("配送规则不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送规则失败: %v", err)
	}

	// 如果修改了区域或配送方式，需要检查是否已存在相同区域和配送方式的规则
	if rule.MethodID != existRule.MethodID || rule.AreaID != existRule.AreaID || rule.ShopID != existRule.ShopID {
		count, err := o.QueryTable(new(models.DeliveryRule)).
			Filter("method_id", rule.MethodID).
			Filter("area_id", rule.AreaID).
			Filter("shop_id", rule.ShopID).
			Exclude("id", rule.ID).
			Count()
		if err != nil {
			return fmt.Errorf("查询配送规则失败: %v", err)
		}

		if count > 0 {
			return errors.New("该区域和配送方式已存在配送规则")
		}
	}

	_, err = o.Update(rule)
	if err != nil {
		return fmt.Errorf("更新配送规则失败: %v", err)
	}

	return nil
}

// DeleteDeliveryRule 删除配送费用规则
func (r *DeliveryRuleRepositoryImpl) DeleteDeliveryRule(ctx context.Context, ruleID int64) error {
	o := orm.NewOrm()

	rule := &models.DeliveryRule{ID: ruleID}
	_, err := o.Delete(rule)
	if err != nil {
		return fmt.Errorf("删除配送规则失败: %v", err)
	}

	return nil
}

// ListDeliveryRules 获取配送费用规则列表
func (r *DeliveryRuleRepositoryImpl) ListDeliveryRules(ctx context.Context, methodID, areaID, shopID int64) ([]*models.DeliveryRule, error) {
	o := orm.NewOrm()
	var rules []*models.DeliveryRule

	query := o.QueryTable(new(models.DeliveryRule))

	if methodID > 0 {
		query = query.Filter("method_id", methodID)
	}

	if areaID > 0 {
		query = query.Filter("area_id", areaID)
	}

	if shopID > 0 {
		query = query.Filter("shop_id", shopID)
	}

	_, err := query.OrderBy("id").All(&rules)
	if err != nil {
		return nil, fmt.Errorf("获取配送规则列表失败: %v", err)
	}

	return rules, nil
}

// GetApplicableDeliveryRule 获取适用的配送费用规则
func (r *DeliveryRuleRepositoryImpl) GetApplicableDeliveryRule(ctx context.Context, methodID, areaID, shopID int64) (*models.DeliveryRule, error) {
	o := orm.NewOrm()

	// 优先查找指定区域、店铺和配送方式的规则
	rule := &models.DeliveryRule{}
	err := o.QueryTable(new(models.DeliveryRule)).
		Filter("method_id", methodID).
		Filter("area_id", areaID).
		Filter("shop_id", shopID).
		Filter("status", 1).
		One(rule)

	if err != nil && err != orm.ErrNoRows {
		return nil, fmt.Errorf("查询配送规则失败: %v", err)
	}

	if err == nil {
		return rule, nil
	}

	// 如果没有找到，查找指定区域、平台级别和配送方式的规则
	err = o.QueryTable(new(models.DeliveryRule)).
		Filter("method_id", methodID).
		Filter("area_id", areaID).
		Filter("shop_id", 0).
		Filter("status", 1).
		One(rule)

	if err != nil && err != orm.ErrNoRows {
		return nil, fmt.Errorf("查询配送规则失败: %v", err)
	}

	if err == nil {
		return rule, nil
	}

	// 如果没有找到，查找全国范围、指定店铺和配送方式的规则
	err = o.QueryTable(new(models.DeliveryRule)).
		Filter("method_id", methodID).
		Filter("area_id", 0).
		Filter("shop_id", shopID).
		Filter("status", 1).
		One(rule)

	if err != nil && err != orm.ErrNoRows {
		return nil, fmt.Errorf("查询配送规则失败: %v", err)
	}

	if err == nil {
		return rule, nil
	}

	// 如果没有找到，查找全国范围、平台级别和配送方式的规则
	err = o.QueryTable(new(models.DeliveryRule)).
		Filter("method_id", methodID).
		Filter("area_id", 0).
		Filter("shop_id", 0).
		Filter("status", 1).
		One(rule)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("未找到适用的配送规则")
		}
		return nil, fmt.Errorf("查询配送规则失败: %v", err)
	}

	return rule, nil
}

// NewDeliveryRuleRepository 创建配送费用规则仓库
func NewDeliveryRuleRepository() *DeliveryRuleRepositoryImpl {
	return &DeliveryRuleRepositoryImpl{}
}
