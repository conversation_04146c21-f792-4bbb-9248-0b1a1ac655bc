/**
 * delivery_time_slot_repository_impl.go
 * 配送时间段仓库实现
 *
 * 本文件实现了配送时间段数据的持久化操作，包括配送时间段的创建、查询、更新和删除等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"

	"github.com/beego/beego/v2/client/orm"

	"o_mall_backend/modules/delivery/models"
)

// DeliveryTimeSlotRepositoryImpl 配送时间段仓库实现
type DeliveryTimeSlotRepositoryImpl struct{}

// CreateDeliveryTimeSlot 创建配送时间段
func (r *DeliveryTimeSlotRepositoryImpl) CreateDeliveryTimeSlot(ctx context.Context, timeSlot *models.DeliveryTimeSlot) (int64, error) {
	o := orm.NewOrm()

	// 检查时间段是否冲突
	count, err := o.QueryTable(new(models.DeliveryTimeSlot)).
		Filter("method_id", timeSlot.MethodID).
		Filter("start_time__lt", timeSlot.EndTime).
		Filter("end_time__gt", timeSlot.StartTime).
		Count()
	if err != nil {
		return 0, fmt.Errorf("查询配送时间段失败: %v", err)
	}

	if count > 0 {
		return 0, errors.New("该时间段与已有时间段冲突")
	}

	// 如果是默认时间段，需要将其他时间段设为非默认
	if timeSlot.IsDefault {
		_, err = o.QueryTable(new(models.DeliveryTimeSlot)).
			Filter("method_id", timeSlot.MethodID).
			Update(orm.Params{
				"is_default": false,
			})
		if err != nil {
			return 0, fmt.Errorf("更新其他时间段为非默认失败: %v", err)
		}
	}

	id, err := o.Insert(timeSlot)
	if err != nil {
		return 0, fmt.Errorf("创建配送时间段失败: %v", err)
	}

	return id, nil
}

// GetDeliveryTimeSlotByID 获取配送时间段详情
func (r *DeliveryTimeSlotRepositoryImpl) GetDeliveryTimeSlotByID(ctx context.Context, timeSlotID int64) (*models.DeliveryTimeSlot, error) {
	o := orm.NewOrm()
	timeSlot := &models.DeliveryTimeSlot{ID: timeSlotID}

	err := o.Read(timeSlot)
	if err == orm.ErrNoRows {
		return nil, errors.New("配送时间段不存在")
	}

	if err != nil {
		return nil, fmt.Errorf("获取配送时间段失败: %v", err)
	}

	return timeSlot, nil
}

// UpdateDeliveryTimeSlot 更新配送时间段
func (r *DeliveryTimeSlotRepositoryImpl) UpdateDeliveryTimeSlot(ctx context.Context, timeSlot *models.DeliveryTimeSlot) error {
	o := orm.NewOrm()

	// 检查时间段是否存在
	existTimeSlot := &models.DeliveryTimeSlot{ID: timeSlot.ID}
	err := o.Read(existTimeSlot)
	if err == orm.ErrNoRows {
		return errors.New("配送时间段不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送时间段失败: %v", err)
	}

	// 检查时间段是否冲突
	if timeSlot.StartTime != existTimeSlot.StartTime || timeSlot.EndTime != existTimeSlot.EndTime || timeSlot.MethodID != existTimeSlot.MethodID {
		count, err := o.QueryTable(new(models.DeliveryTimeSlot)).
			Filter("method_id", timeSlot.MethodID).
			Filter("start_time__lt", timeSlot.EndTime).
			Filter("end_time__gt", timeSlot.StartTime).
			Exclude("id", timeSlot.ID).
			Count()
		if err != nil {
			return fmt.Errorf("查询配送时间段失败: %v", err)
		}

		if count > 0 {
			return errors.New("该时间段与已有时间段冲突")
		}
	}

	// 如果是默认时间段，需要将其他时间段设为非默认
	if timeSlot.IsDefault && !existTimeSlot.IsDefault {
		_, err = o.QueryTable(new(models.DeliveryTimeSlot)).
			Filter("method_id", timeSlot.MethodID).
			Exclude("id", timeSlot.ID).
			Update(orm.Params{
				"is_default": false,
			})
		if err != nil {
			return fmt.Errorf("更新其他时间段为非默认失败: %v", err)
		}
	}

	_, err = o.Update(timeSlot)
	if err != nil {
		return fmt.Errorf("更新配送时间段失败: %v", err)
	}

	return nil
}

// DeleteDeliveryTimeSlot 删除配送时间段
func (r *DeliveryTimeSlotRepositoryImpl) DeleteDeliveryTimeSlot(ctx context.Context, timeSlotID int64) error {
	o := orm.NewOrm()

	// 检查是否有关联的配送订单
	count, err := o.QueryTable(new(models.DeliveryOrder)).Filter("time_slot_id", timeSlotID).Count()
	if err != nil {
		return fmt.Errorf("查询关联配送订单失败: %v", err)
	}

	if count > 0 {
		return errors.New("该时间段已关联配送订单，无法删除")
	}

	// 检查是否为默认时间段
	timeSlot := &models.DeliveryTimeSlot{ID: timeSlotID}
	err = o.Read(timeSlot)
	if err != nil {
		return fmt.Errorf("获取配送时间段失败: %v", err)
	}

	if timeSlot.IsDefault {
		return errors.New("默认时间段无法删除")
	}

	_, err = o.Delete(timeSlot)
	if err != nil {
		return fmt.Errorf("删除配送时间段失败: %v", err)
	}

	return nil
}

// ListDeliveryTimeSlots 获取配送时间段列表
func (r *DeliveryTimeSlotRepositoryImpl) ListDeliveryTimeSlots(ctx context.Context, methodID int64, status int) ([]*models.DeliveryTimeSlot, error) {
	o := orm.NewOrm()
	var timeSlots []*models.DeliveryTimeSlot

	query := o.QueryTable(new(models.DeliveryTimeSlot))

	if methodID > 0 {
		query = query.Filter("method_id", methodID)
	}

	if status != -1 {
		query = query.Filter("status", status)
	}

	_, err := query.OrderBy("start_time", "id").All(&timeSlots)
	if err != nil {
		return nil, fmt.Errorf("获取配送时间段列表失败: %v", err)
	}

	return timeSlots, nil
}

// SetDefaultDeliveryTimeSlot 设置默认配送时间段
func (r *DeliveryTimeSlotRepositoryImpl) SetDefaultDeliveryTimeSlot(ctx context.Context, methodID, timeSlotID int64) error {
	o := orm.NewOrm()

	// 检查时间段是否存在
	timeSlot := &models.DeliveryTimeSlot{ID: timeSlotID}
	err := o.Read(timeSlot)
	if err == orm.ErrNoRows {
		return errors.New("配送时间段不存在")
	}
	if err != nil {
		return fmt.Errorf("获取配送时间段失败: %v", err)
	}

	// 检查时间段是否属于指定的配送方式
	if timeSlot.MethodID != methodID {
		return errors.New("配送时间段不属于指定的配送方式")
	}

	// 将该配送方式的所有时间段设为非默认
	_, err = o.QueryTable(new(models.DeliveryTimeSlot)).
		Filter("method_id", methodID).
		Update(orm.Params{
			"is_default": false,
		})
	if err != nil {
		return fmt.Errorf("更新时间段为非默认失败: %v", err)
	}

	// 设置指定时间段为默认
	timeSlot.IsDefault = true
	_, err = o.Update(timeSlot, "is_default")
	if err != nil {
		return fmt.Errorf("设置默认时间段失败: %v", err)
	}

	return nil
}

// UpdateDeliveryTimeSlotOrderCount 更新配送时间段订单数
func (r *DeliveryTimeSlotRepositoryImpl) UpdateDeliveryTimeSlotOrderCount(ctx context.Context, timeSlotID int64, increment bool) error {
	o := orm.NewOrm()
	timeSlot := models.DeliveryTimeSlot{ID: timeSlotID}

	if err := o.Read(&timeSlot); err != nil {
		return fmt.Errorf("获取配送时间段失败: %v", err)
	}

	// 执行事务
	tx, err := o.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}

	// 更新订单数
	if increment {
		_, err = tx.QueryTable(new(models.DeliveryTimeSlot)).
			Filter("id", timeSlotID).
			Update(orm.Params{
				"current_orders": orm.ColValue(orm.ColAdd, 1),
			})
	} else {
		_, err = tx.QueryTable(new(models.DeliveryTimeSlot)).
			Filter("id", timeSlotID).
			Filter("current_orders__gt", 0). // 确保不会减到负数
			Update(orm.Params{
				"current_orders": orm.ColValue(orm.ColMinus, 1),
			})
	}

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("更新时间段订单数失败: %v", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		tx.Rollback()
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// IncrementOrderCount 增加配送时间段订单数
func (r *DeliveryTimeSlotRepositoryImpl) IncrementOrderCount(ctx context.Context, timeSlotID int64) error {
	return r.UpdateDeliveryTimeSlotOrderCount(ctx, timeSlotID, true)
}

// DecrementOrderCount 减少配送时间段订单数
func (r *DeliveryTimeSlotRepositoryImpl) DecrementOrderCount(ctx context.Context, timeSlotID int64) error {
	return r.UpdateDeliveryTimeSlotOrderCount(ctx, timeSlotID, false)
}

// GetDeliveryTimeSlotOrderCount 获取配送时间段当前订单数
func (r *DeliveryTimeSlotRepositoryImpl) GetDeliveryTimeSlotOrderCount(ctx context.Context, timeSlotID int64) (int, error) {
	// 同上，这里需要一个额外的计数表或使用Redis等来实现
	// 这里简单返回一个查询当前关联该时间段的未完成订单数

	o := orm.NewOrm()
	count, err := o.QueryTable(new(models.DeliveryOrder)).
		Filter("time_slot_id", timeSlotID).
		Filter("status__in", []int{0, 1}).
		Count()
	if err != nil {
		return 0, fmt.Errorf("获取时间段订单数失败: %v", err)
	}

	return int(count), nil
}

// NewDeliveryTimeSlotRepository 创建配送时间段仓库
func NewDeliveryTimeSlotRepository() *DeliveryTimeSlotRepositoryImpl {
	return &DeliveryTimeSlotRepositoryImpl{}
}
