/**
 * router.go
 * 配送模块路由
 *
 * 本文件定义了配送模块的所有API路由，将HTTP请求映射到对应的控制器方法
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/delivery/controllers"
)

// 初始化配送模块路由
func Init() {
	// 配送区域相关路由
	web.Router("/api/v1/delivery/areas", &controllers.DeliveryAreaController{}, "post:CreateDeliveryArea;get:ListDeliveryAreas")
	web.Router("/api/v1/delivery/areas/:id", &controllers.DeliveryAreaController{}, "get:GetDeliveryArea;put:UpdateDeliveryArea;delete:DeleteDeliveryArea")
	web.Router("/api/v1/delivery/areas/tree", &controllers.DeliveryAreaController{}, "get:GetDeliveryAreaTree")

	// 配送方式相关路由
	web.Router("/api/v1/delivery/methods", &controllers.DeliveryMethodController{}, "post:CreateDeliveryMethod;get:ListDeliveryMethods")
	web.Router("/api/v1/delivery/methods/:id", &controllers.DeliveryMethodController{}, "get:GetDeliveryMethod;put:UpdateDeliveryMethod;delete:DeleteDeliveryMethod")
	web.Router("/api/v1/delivery/methods/:id/default", &controllers.DeliveryMethodController{}, "put:SetDefaultDeliveryMethod")

	// 配送规则相关路由
	web.Router("/api/v1/delivery/rules", &controllers.DeliveryRuleController{}, "post:CreateDeliveryRule;get:ListDeliveryRules")
	web.Router("/api/v1/delivery/rules/:id", &controllers.DeliveryRuleController{}, "get:GetDeliveryRule;put:UpdateDeliveryRule;delete:DeleteDeliveryRule")
	web.Router("/api/v1/delivery/rules/calculate-fee", &controllers.DeliveryRuleController{}, "post:CalculateDeliveryFee")

	// 配送时间段相关路由
	web.Router("/api/v1/delivery/time-slots", &controllers.DeliveryTimeSlotController{}, "post:CreateDeliveryTimeSlot;get:ListDeliveryTimeSlots")
	web.Router("/api/v1/delivery/time-slots/:id", &controllers.DeliveryTimeSlotController{}, "get:GetDeliveryTimeSlot;put:UpdateDeliveryTimeSlot;delete:DeleteDeliveryTimeSlot")
	web.Router("/api/v1/delivery/time-slots/:id/default", &controllers.DeliveryTimeSlotController{}, "put:SetDefaultDeliveryTimeSlot")
	web.Router("/api/v1/delivery/time-slots/:id/check", &controllers.DeliveryTimeSlotController{}, "get:CheckTimeSlotAvailable")

	// 配送订单相关路由
	web.Router("/api/v1/delivery/orders", &controllers.DeliveryOrderController{}, "post:CreateDeliveryOrder;get:ListDeliveryOrders")
	web.Router("/api/v1/delivery/orders/:id", &controllers.DeliveryOrderController{}, "get:GetDeliveryOrder")
	web.Router("/api/v1/delivery/orders/by-order-id", &controllers.DeliveryOrderController{}, "get:GetDeliveryOrderByOrderID")
	web.Router("/api/v1/delivery/orders/by-order-no", &controllers.DeliveryOrderController{}, "get:GetDeliveryOrderByOrderNo")
	web.Router("/api/v1/delivery/orders/:id/status", &controllers.DeliveryOrderController{}, "put:UpdateDeliveryOrderStatus")
	web.Router("/api/v1/delivery/orders/:id/assign-runner", &controllers.DeliveryOrderController{}, "put:AssignRunner")
	web.Router("/api/v1/delivery/orders/:id/tracking", &controllers.DeliveryOrderController{}, "put:UpdateTrackingInfo;get:GetTrackingInfo")
	web.Router("/api/v1/delivery/orders/:id/cancel", &controllers.DeliveryOrderController{}, "put:CancelDeliveryOrder")
}
