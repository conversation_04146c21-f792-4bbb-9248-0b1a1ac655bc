/**
 * delivery_area_service_factory.go
 * 配送区域服务工厂
 *
 * 本文件提供了创建配送区域服务实例的工厂函数
 */

package services

import (
	"o_mall_backend/modules/delivery/repositories"
	"o_mall_backend/modules/delivery/services/impl"
)

// NewDeliveryAreaService 创建配送区域服务实例
func NewDeliveryAreaService() DeliveryAreaService {
	// 创建仓储实例
	areaRepo := repositories.NewDeliveryAreaRepository()

	// 创建并返回服务实例
	return impl.NewDeliveryAreaService(areaRepo)
}
