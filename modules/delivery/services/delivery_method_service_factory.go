/**
 * delivery_method_service_factory.go
 * 配送方式服务工厂
 *
 * 本文件提供了创建配送方式服务实例的工厂函数
 */

package services

import (
	"o_mall_backend/modules/delivery/repositories"
	"o_mall_backend/modules/delivery/services/impl"
)

// NewDeliveryMethodService 创建配送方式服务实例
func NewDeliveryMethodService() DeliveryMethodService {
	// 创建仓储实例
	methodRepo := repositories.NewDeliveryMethodRepository()

	// 创建并返回服务实例
	return impl.NewDeliveryMethodService(methodRepo)
}
