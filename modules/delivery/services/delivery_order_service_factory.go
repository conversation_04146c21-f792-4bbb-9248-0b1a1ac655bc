/**
 * delivery_order_service_factory.go
 * 配送订单服务工厂
 *
 * 本文件提供了创建配送订单服务实例的工厂函数
 */

package services

import (
	"o_mall_backend/modules/delivery/repositories"
	"o_mall_backend/modules/delivery/services/impl"
)

// NewDeliveryOrderService 创建配送订单服务实例
func NewDeliveryOrderService() DeliveryOrderService {
	// 创建仓储实例
	orderRepo := repositories.NewDeliveryOrderRepository()
	methodRepo := repositories.NewDeliveryMethodRepository()
	timeSlotRepo := repositories.NewDeliveryTimeSlotRepository()
	ruleRepo := repositories.NewDeliveryRuleRepository()

	// 创建并返回服务实例
	return impl.NewDeliveryOrderService(orderRepo, methodRepo, timeSlotRepo, ruleRepo)
}
