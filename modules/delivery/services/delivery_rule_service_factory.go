/**
 * delivery_rule_service_factory.go
 * 配送费用规则服务工厂
 *
 * 本文件提供了创建配送费用规则服务实例的工厂函数
 */

package services

import (
	"o_mall_backend/modules/delivery/repositories"
	"o_mall_backend/modules/delivery/services/impl"
)

// NewDeliveryRuleService 创建配送费用规则服务实例
func NewDeliveryRuleService() DeliveryRuleService {
	// 创建仓储实例
	ruleRepo := repositories.NewDeliveryRuleRepository()
	methodRepo := repositories.NewDeliveryMethodRepository()
	areaRepo := repositories.NewDeliveryAreaRepository()
	timeSlotRepo := repositories.NewDeliveryTimeSlotRepository()

	// 创建并返回服务实例
	return impl.NewDeliveryRuleService(ruleRepo, methodRepo, areaRepo, timeSlotRepo)
}
