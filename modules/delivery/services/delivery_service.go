/**
 * delivery_service.go
 * 配送模块服务接口定义
 *
 * 本文件定义了配送模块的业务服务接口，包括配送区域、配送方式、配送费用规则等相关业务操作
 */

package services

import (
	"context"

	"o_mall_backend/modules/delivery/dto"
	//"o_mall_backend/modules/delivery/models"
)

// DeliveryAreaService 配送区域服务接口
type DeliveryAreaService interface {
	// 创建配送区域
	CreateDeliveryArea(ctx context.Context, req *dto.DeliveryAreaRequest) (*dto.DeliveryAreaResponse, error) // 创建配送区域

	// 获取配送区域详情
	GetDeliveryArea(ctx context.Context, areaID int64) (*dto.DeliveryAreaResponse, error) // 获取配送区域详情

	// 更新配送区域
	UpdateDeliveryArea(ctx context.Context, areaID int64, req *dto.DeliveryAreaRequest) (*dto.DeliveryAreaResponse, error) // 更新配送区域

	// 删除配送区域
	DeleteDeliveryArea(ctx context.Context, areaID int64) error // 删除配送区域

	// 获取配送区域列表
	ListDeliveryAreas(ctx context.Context, parentID int64, status int) ([]*dto.DeliveryAreaResponse, error) // 获取配送区域列表

	// 获取配送区域树
	GetDeliveryAreaTree(ctx context.Context) ([]*dto.DeliveryAreaResponse, error) // 获取配送区域树
}

// DeliveryMethodService 配送方式服务接口
type DeliveryMethodService interface {
	// 创建配送方式
	CreateDeliveryMethod(ctx context.Context, req *dto.DeliveryMethodRequest) (*dto.DeliveryMethodResponse, error) // 创建配送方式

	// 获取配送方式详情
	GetDeliveryMethod(ctx context.Context, methodID int64) (*dto.DeliveryMethodResponse, error) // 获取配送方式详情

	// 更新配送方式
	UpdateDeliveryMethod(ctx context.Context, methodID int64, req *dto.DeliveryMethodRequest) (*dto.DeliveryMethodResponse, error) // 更新配送方式

	// 删除配送方式
	DeleteDeliveryMethod(ctx context.Context, methodID int64) error // 删除配送方式

	// 获取配送方式列表
	ListDeliveryMethods(ctx context.Context, status int) ([]*dto.DeliveryMethodResponse, error) // 获取配送方式列表

	// 设置默认配送方式
	SetDefaultDeliveryMethod(ctx context.Context, methodID int64) error // 设置默认配送方式
}

// DeliveryRuleService 配送费用规则服务接口
type DeliveryRuleService interface {
	// 创建配送费用规则
	CreateDeliveryRule(ctx context.Context, req *dto.DeliveryRuleRequest) (*dto.DeliveryRuleResponse, error) // 创建配送费用规则

	// 获取配送费用规则详情
	GetDeliveryRule(ctx context.Context, ruleID int64) (*dto.DeliveryRuleResponse, error) // 获取配送费用规则详情

	// 更新配送费用规则
	UpdateDeliveryRule(ctx context.Context, ruleID int64, req *dto.DeliveryRuleRequest) (*dto.DeliveryRuleResponse, error) // 更新配送费用规则

	// 删除配送费用规则
	DeleteDeliveryRule(ctx context.Context, ruleID int64) error // 删除配送费用规则

	// 获取配送费用规则列表
	ListDeliveryRules(ctx context.Context, methodID, areaID, shopID int64) ([]*dto.DeliveryRuleResponse, error) // 获取配送费用规则列表

	// 计算配送费用
	CalculateDeliveryFee(ctx context.Context, req *dto.DeliveryFeeCalculateRequest) (*dto.DeliveryFeeCalculateResponse, error) // 计算配送费用
}

// DeliveryTimeSlotService 配送时间段服务接口
type DeliveryTimeSlotService interface {
	// 创建配送时间段
	CreateDeliveryTimeSlot(ctx context.Context, req *dto.DeliveryTimeSlotRequest) (*dto.DeliveryTimeSlotResponse, error) // 创建配送时间段

	// 获取配送时间段详情
	GetDeliveryTimeSlot(ctx context.Context, timeSlotID int64) (*dto.DeliveryTimeSlotResponse, error) // 获取配送时间段详情

	// 更新配送时间段
	UpdateDeliveryTimeSlot(ctx context.Context, timeSlotID int64, req *dto.DeliveryTimeSlotRequest) (*dto.DeliveryTimeSlotResponse, error) // 更新配送时间段

	// 删除配送时间段
	DeleteDeliveryTimeSlot(ctx context.Context, timeSlotID int64) error // 删除配送时间段

	// 获取配送时间段列表
	ListDeliveryTimeSlots(ctx context.Context, methodID int64, status int) ([]*dto.DeliveryTimeSlotResponse, error) // 获取配送时间段列表

	// 设置默认配送时间段
	SetDefaultDeliveryTimeSlot(ctx context.Context, methodID, timeSlotID int64) error // 设置默认配送时间段

	// 检查配送时间段是否可用
	CheckTimeSlotAvailable(ctx context.Context, timeSlotID int64) (bool, error) // 检查配送时间段是否可用
}

// DeliveryOrderService 配送订单服务接口
type DeliveryOrderService interface {
	// 创建配送订单
	CreateDeliveryOrder(ctx context.Context, req *dto.CreateDeliveryOrderRequest) (*dto.DeliveryOrderResponse, error) // 创建配送订单

	// 获取配送订单详情
	GetDeliveryOrder(ctx context.Context, deliveryID int64) (*dto.DeliveryOrderResponse, error) // 获取配送订单详情

	// 根据订单ID获取配送订单
	GetDeliveryOrderByOrderID(ctx context.Context, orderID int64) (*dto.DeliveryOrderResponse, error) // 根据订单ID获取配送订单

	// 根据订单编号获取配送订单
	GetDeliveryOrderByOrderNo(ctx context.Context, orderNo string) (*dto.DeliveryOrderResponse, error) // 根据订单编号获取配送订单

	// 更新配送订单状态
	UpdateDeliveryStatus(ctx context.Context, deliveryID int64, req *dto.UpdateDeliveryStatusRequest, operatorID int64, operatorType int) error // 更新配送订单状态

	// 取消配送订单
	CancelDelivery(ctx context.Context, deliveryID int64, req *dto.CancelDeliveryRequest, operatorID int64, operatorType int) error // 取消配送订单

	// 分配配送员
	AssignRunner(ctx context.Context, deliveryID, runnerID int64, operatorID int64, operatorType int) error // 分配配送员

	// 更新物流信息
	UpdateTrackingInfo(ctx context.Context, deliveryID int64, trackingNo, trackingCompany string, operatorID int64, operatorType int) error // 更新物流信息

	// 获取用户的配送订单列表
	ListUserDeliveryOrders(ctx context.Context, userID int64, status int, page, pageSize int) ([]*dto.DeliveryOrderResponse, int64, error) // 获取用户的配送订单列表

	// 获取店铺的配送订单列表
	ListShopDeliveryOrders(ctx context.Context, shopID int64, status int, page, pageSize int) ([]*dto.DeliveryOrderResponse, int64, error) // 获取店铺的配送订单列表

	// 获取配送员的配送订单列表
	ListRunnerDeliveryOrders(ctx context.Context, runnerID int64, status int, page, pageSize int) ([]*dto.DeliveryOrderResponse, int64, error) // 获取配送员的配送订单列表

	// 获取配送物流跟踪记录列表
	ListDeliveryTrackings(ctx context.Context, deliveryID int64) ([]*dto.DeliveryTrackingResponse, error) // 获取配送物流跟踪记录列表
}
