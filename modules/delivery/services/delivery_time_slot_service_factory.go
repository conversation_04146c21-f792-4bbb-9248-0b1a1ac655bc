/**
 * delivery_time_slot_service_factory.go
 * 配送时间段服务工厂
 *
 * 本文件提供了创建配送时间段服务实例的工厂函数
 */

package services

import (
	"o_mall_backend/modules/delivery/repositories"
	"o_mall_backend/modules/delivery/services/impl"
)

// NewDeliveryTimeSlotService 创建配送时间段服务实例
func NewDeliveryTimeSlotService() DeliveryTimeSlotService {
	// 创建仓储实例
	timeSlotRepo := repositories.NewDeliveryTimeSlotRepository()
	methodRepo := repositories.NewDeliveryMethodRepository()

	// 创建并返回服务实例
	return impl.NewDeliveryTimeSlotService(timeSlotRepo, methodRepo)
}
