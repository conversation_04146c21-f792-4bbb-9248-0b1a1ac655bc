/**
 * delivery_area_service_impl.go
 * 配送区域服务实现
 *
 * 本文件实现了配送区域相关的业务逻辑
 */

package impl

import (
	"context"
	"errors"
	"fmt"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/constants"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/models"
	"o_mall_backend/modules/delivery/repositories"
)

// DeliveryAreaServiceImpl 配送区域服务实现
type DeliveryAreaServiceImpl struct {
	areaRepo repositories.DeliveryAreaRepository
}

// 将区域模型转换为响应DTO
func (s *DeliveryAreaServiceImpl) convertToAreaResponse(area *models.DeliveryArea) *dto.DeliveryAreaResponse {
	return &dto.DeliveryAreaResponse{
		ID:         area.ID,
		Name:       area.Name,
		Code:       area.Code,
		ParentID:   area.ParentID,
		Level:      area.Level,
		Status:     area.Status,
		StatusDesc: constants.AreaStatusDesc[area.Status],
		SortOrder:  area.SortOrder,
		CreateTime: area.CreateTime,
		UpdateTime: area.UpdateTime,
		Children:   make([]dto.DeliveryAreaResponse, 0), // 初始化为空切片
	}
}

// CreateDeliveryArea 创建配送区域
func (s *DeliveryAreaServiceImpl) CreateDeliveryArea(ctx context.Context, req *dto.DeliveryAreaRequest) (*dto.DeliveryAreaResponse, error) {
	// 校验参数
	if req.Name == "" || req.Code == "" {
		return nil, errors.New("区域名称和编码不能为空")
	}

	// 检查编码是否重复
	existArea, err := s.areaRepo.GetDeliveryAreaByCode(ctx, req.Code)
	if err != nil {
		logs.Error("查询区域编码是否存在失败: %v", err)
		return nil, fmt.Errorf("检查区域编码失败: %v", err)
	}

	if existArea != nil {
		return nil, errors.New("区域编码已存在")
	}

	// 创建区域
	area := &models.DeliveryArea{
		Name:      req.Name,
		Code:      req.Code,
		ParentID:  req.ParentID,
		Level:     req.Level,
		Status:    req.Status,
		SortOrder: req.SortOrder,
	}

	// 如果未指定状态，默认为启用
	if area.Status == 0 {
		area.Status = constants.AreaStatusEnabled
	}

	// 保存到数据库
	id, err := s.areaRepo.CreateDeliveryArea(ctx, area)
	if err != nil {
		logs.Error("创建配送区域失败: %v", err)
		return nil, fmt.Errorf("创建配送区域失败: %v", err)
	}

	// 获取新创建的区域
	newArea, err := s.areaRepo.GetDeliveryAreaByID(ctx, id)
	if err != nil {
		logs.Error("获取新创建的区域失败: %v", err)
		return nil, fmt.Errorf("获取新创建的区域失败: %v", err)
	}

	// 转换为响应DTO
	return s.convertToAreaResponse(newArea), nil
}

// GetDeliveryArea 获取配送区域详情
func (s *DeliveryAreaServiceImpl) GetDeliveryArea(ctx context.Context, areaID int64) (*dto.DeliveryAreaResponse, error) {
	area, err := s.areaRepo.GetDeliveryAreaByID(ctx, areaID)
	if err != nil {
		logs.Error("获取配送区域失败: %v", err)
		return nil, fmt.Errorf("获取配送区域失败: %v", err)
	}

	if area == nil {
		return nil, errors.New("配送区域不存在")
	}

	return s.convertToAreaResponse(area), nil
}

// UpdateDeliveryArea 更新配送区域
func (s *DeliveryAreaServiceImpl) UpdateDeliveryArea(ctx context.Context, areaID int64, req *dto.DeliveryAreaRequest) (*dto.DeliveryAreaResponse, error) {
	// 获取现有区域
	area, err := s.areaRepo.GetDeliveryAreaByID(ctx, areaID)
	if err != nil {
		logs.Error("获取配送区域失败: %v", err)
		return nil, fmt.Errorf("获取配送区域失败: %v", err)
	}

	if area == nil {
		return nil, errors.New("配送区域不存在")
	}

	// 如果更新编码，检查是否重复
	if req.Code != area.Code {
		existArea, err := s.areaRepo.GetDeliveryAreaByCode(ctx, req.Code)
		if err != nil {
			logs.Error("查询区域编码是否存在失败: %v", err)
			return nil, fmt.Errorf("检查区域编码失败: %v", err)
		}

		if existArea != nil && existArea.ID != areaID {
			return nil, errors.New("区域编码已存在")
		}
	}

	// 更新区域信息
	area.Name = req.Name
	area.Code = req.Code
	area.ParentID = req.ParentID
	area.Level = req.Level
	area.Status = req.Status
	area.SortOrder = req.SortOrder

	// 保存更新
	err = s.areaRepo.UpdateDeliveryArea(ctx, area)
	if err != nil {
		logs.Error("更新配送区域失败: %v", err)
		return nil, fmt.Errorf("更新配送区域失败: %v", err)
	}

	return s.convertToAreaResponse(area), nil
}

// DeleteDeliveryArea 删除配送区域
func (s *DeliveryAreaServiceImpl) DeleteDeliveryArea(ctx context.Context, areaID int64) error {
	// 检查是否有子区域
	areas, err := s.areaRepo.ListDeliveryAreas(ctx, areaID, -1)
	if err != nil {
		logs.Error("查询子区域失败: %v", err)
		return fmt.Errorf("查询子区域失败: %v", err)
	}

	if len(areas) > 0 {
		return errors.New("该区域下存在子区域，不能删除")
	}

	// 执行删除
	err = s.areaRepo.DeleteDeliveryArea(ctx, areaID)
	if err != nil {
		logs.Error("删除配送区域失败: %v", err)
		return fmt.Errorf("删除配送区域失败: %v", err)
	}

	return nil
}

// ListDeliveryAreas 获取配送区域列表
func (s *DeliveryAreaServiceImpl) ListDeliveryAreas(ctx context.Context, parentID int64, status int) ([]*dto.DeliveryAreaResponse, error) {
	areas, err := s.areaRepo.ListDeliveryAreas(ctx, parentID, status)
	if err != nil {
		logs.Error("获取配送区域列表失败: %v", err)
		return nil, fmt.Errorf("获取配送区域列表失败: %v", err)
	}

	// 转换为响应DTO
	result := make([]*dto.DeliveryAreaResponse, 0, len(areas))
	for _, area := range areas {
		result = append(result, s.convertToAreaResponse(area))
	}

	return result, nil
}

// GetDeliveryAreaTree 获取配送区域树
func (s *DeliveryAreaServiceImpl) GetDeliveryAreaTree(ctx context.Context) ([]*dto.DeliveryAreaResponse, error) {
	// 获取所有区域
	areas, err := s.areaRepo.ListDeliveryAreas(ctx, -1, constants.AreaStatusEnabled)
	if err != nil {
		logs.Error("获取配送区域列表失败: %v", err)
		return nil, fmt.Errorf("获取配送区域列表失败: %v", err)
	}

	// 构建区域树
	areaMap := make(map[int64][]*dto.DeliveryAreaResponse)
	areaNodeMap := make(map[int64]*dto.DeliveryAreaResponse)

	for _, area := range areas {
		resp := s.convertToAreaResponse(area)
		areaNodeMap[area.ID] = resp
		areaMap[area.ParentID] = append(areaMap[area.ParentID], resp)
	}

	// 递归构建树结构
	var buildAreaTree func(areas []*dto.DeliveryAreaResponse)
	buildAreaTree = func(areas []*dto.DeliveryAreaResponse) {
		for _, area := range areas {
			if children, ok := areaMap[area.ID]; ok {
				// 将指针切片的内容复制到非指针切片中
				childrenSlice := make([]dto.DeliveryAreaResponse, len(children))
				for i, child := range children {
					childrenSlice[i] = *child
				}
				area.Children = childrenSlice
				buildAreaTree(children)
			}
		}
	}

	// 构建树结构
	rootAreas := areaMap[0]
	buildAreaTree(rootAreas)

	// 返回顶级区域列表
	return rootAreas, nil
}

// NewDeliveryAreaService 创建配送区域服务
func NewDeliveryAreaService(areaRepo repositories.DeliveryAreaRepository) *DeliveryAreaServiceImpl {
	return &DeliveryAreaServiceImpl{
		areaRepo: areaRepo,
	}
}
