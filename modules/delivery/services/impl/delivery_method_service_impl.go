/**
 * delivery_method_service_impl.go
 * 配送方式服务实现
 * 
 * 本文件实现了配送方式的业务逻辑，包括配送方式的创建、查询、更新和删除等功能
 */

package impl

import (
	"context"
	"time"
	
	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/delivery/constants"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/models"
	"o_mall_backend/modules/delivery/repositories"
)

// DeliveryMethodServiceImpl 配送方式服务实现
type DeliveryMethodServiceImpl struct {
	methodRepo repositories.DeliveryMethodRepository
}

// 将模型对象转换为响应DTO对象
func (s *DeliveryMethodServiceImpl) convertToMethodResponse(method *models.DeliveryMethod) *dto.DeliveryMethodResponse {
	if method == nil {
		return nil
	}
	
	resp := &dto.DeliveryMethodResponse{
		ID:          method.ID,
		Name:        method.Name,
		Code:        method.Code,
		Icon:        method.Icon,
		Description: method.Description,
		Status:      method.Status,
		StatusDesc:  constants.MethodStatusDesc[method.Status],
		IsDefault:   method.IsDefault,
		SortOrder:   method.SortOrder,
		CreateTime:  method.CreateTime,
		UpdateTime:  method.UpdateTime,
	}
	
	return resp
}

// CreateDeliveryMethod 创建配送方式
func (s *DeliveryMethodServiceImpl) CreateDeliveryMethod(ctx context.Context, req *dto.DeliveryMethodRequest) (*dto.DeliveryMethodResponse, error) {
	// 创建模型对象
	method := &models.DeliveryMethod{
		Name:        req.Name,
		Code:        req.Code,
		Icon:        req.Icon,
		Description: req.Description,
		Status:      req.Status,
		IsDefault:   req.IsDefault,
		SortOrder:   req.SortOrder,
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}
	
	// 如果未指定状态，默认为启用
	if method.Status == 0 {
		method.Status = constants.MethodStatusEnabled
	}
	
	// 调用仓库层创建配送方式
	id, err := s.methodRepo.CreateDeliveryMethod(ctx, method)
	if err != nil {
		logs.Error("创建配送方式失败: %v", err)
		return nil, err
	}
	
	// 获取创建后的配送方式详情
	createdMethod, err := s.methodRepo.GetDeliveryMethodByID(ctx, id)
	if err != nil {
		logs.Error("获取新创建的配送方式失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	return s.convertToMethodResponse(createdMethod), nil
}

// GetDeliveryMethod 获取配送方式详情
func (s *DeliveryMethodServiceImpl) GetDeliveryMethod(ctx context.Context, methodID int64) (*dto.DeliveryMethodResponse, error) {
	// 调用仓库层获取配送方式
	method, err := s.methodRepo.GetDeliveryMethodByID(ctx, methodID)
	if err != nil {
		logs.Error("获取配送方式详情失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	return s.convertToMethodResponse(method), nil
}

// UpdateDeliveryMethod 更新配送方式
func (s *DeliveryMethodServiceImpl) UpdateDeliveryMethod(ctx context.Context, methodID int64, req *dto.DeliveryMethodRequest) (*dto.DeliveryMethodResponse, error) {
	// 先获取现有配送方式
	method, err := s.methodRepo.GetDeliveryMethodByID(ctx, methodID)
	if err != nil {
		logs.Error("获取配送方式失败: %v", err)
		return nil, err
	}
	
	// 更新字段
	method.Name = req.Name
	method.Code = req.Code
	method.Icon = req.Icon
	method.Description = req.Description
	method.Status = req.Status
	method.IsDefault = req.IsDefault
	method.SortOrder = req.SortOrder
	method.UpdateTime = time.Now()
	
	// 调用仓库层更新配送方式
	err = s.methodRepo.UpdateDeliveryMethod(ctx, method)
	if err != nil {
		logs.Error("更新配送方式失败: %v", err)
		return nil, err
	}
	
	// 获取更新后的配送方式详情
	updatedMethod, err := s.methodRepo.GetDeliveryMethodByID(ctx, methodID)
	if err != nil {
		logs.Error("获取更新后的配送方式失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	return s.convertToMethodResponse(updatedMethod), nil
}

// DeleteDeliveryMethod 删除配送方式
func (s *DeliveryMethodServiceImpl) DeleteDeliveryMethod(ctx context.Context, methodID int64) error {
	// 调用仓库层删除配送方式
	err := s.methodRepo.DeleteDeliveryMethod(ctx, methodID)
	if err != nil {
		logs.Error("删除配送方式失败: %v", err)
		return err
	}
	
	return nil
}

// ListDeliveryMethods 获取配送方式列表
func (s *DeliveryMethodServiceImpl) ListDeliveryMethods(ctx context.Context, status int) ([]*dto.DeliveryMethodResponse, error) {
	// 调用仓库层获取配送方式列表
	methods, err := s.methodRepo.ListDeliveryMethods(ctx, status)
	if err != nil {
		logs.Error("获取配送方式列表失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象列表
	result := make([]*dto.DeliveryMethodResponse, len(methods))
	for i, method := range methods {
		result[i] = s.convertToMethodResponse(method)
	}
	
	return result, nil
}

// SetDefaultDeliveryMethod 设置默认配送方式
func (s *DeliveryMethodServiceImpl) SetDefaultDeliveryMethod(ctx context.Context, methodID int64) error {
	// 调用仓库层设置默认配送方式
	err := s.methodRepo.SetDefaultDeliveryMethod(ctx, methodID)
	if err != nil {
		logs.Error("设置默认配送方式失败: %v", err)
		return err
	}
	
	return nil
}

// NewDeliveryMethodService 创建配送方式服务
func NewDeliveryMethodService(methodRepo repositories.DeliveryMethodRepository) *DeliveryMethodServiceImpl {
	return &DeliveryMethodServiceImpl{
		methodRepo: methodRepo,
	}
}
