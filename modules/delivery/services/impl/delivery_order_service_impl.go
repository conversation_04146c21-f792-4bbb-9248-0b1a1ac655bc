/**
 * delivery_order_service_impl.go
 * 配送订单服务实现
 *
 * 本文件实现了配送订单的业务逻辑，包括订单的创建、查询、更新状态和物流跟踪等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/constants"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/models"
	"o_mall_backend/modules/delivery/repositories"
)

// DeliveryOrderServiceImpl 配送订单服务实现
type DeliveryOrderServiceImpl struct {
	orderRepo    repositories.DeliveryOrderRepository
	methodRepo   repositories.DeliveryMethodRepository
	timeSlotRepo repositories.DeliveryTimeSlotRepository
	ruleRepo     repositories.DeliveryRuleRepository
}

// 将配送订单模型转换为响应DTO
func (s *DeliveryOrderServiceImpl) convertToOrderResponse(order *models.DeliveryOrder) *dto.DeliveryOrderResponse {
	resp := &dto.DeliveryOrderResponse{
		ID:                    order.ID,
		OrderID:               order.OrderID,
		OrderNo:               order.OrderNo,
		MethodID:              order.MethodID,
		TimeSlotID:            order.TimeSlotID,
		UserID:                order.UserID,
		ShopID:                order.ShopID,
		RunnerID:              order.RunnerID,
		Status:                order.Status,
		StatusDesc:            constants.DeliveryStatusDesc[order.Status],
		DeliveryFee:           order.DeliveryFee,
		TimeSlotFee:           order.TimeSlotFee,
		TotalFee:              order.TotalFee,
		Weight:                order.Weight,
		Distance:              order.Distance,
		ReceiverName:          order.ReceiverName,
		ReceiverPhone:         order.ReceiverPhone,
		ReceiverAddress:       order.ReceiverAddress,
		ReceiverAddressDetail: order.ReceiverAddressDetail,
		ReceiverLat:           order.ReceiverLat,
		ReceiverLng:           order.ReceiverLng,
		TrackingNo:            order.TrackingNo,
		TrackingCompany:       order.TrackingCompany,
		ExpectedTime:          order.ExpectedTime,
		ActualTime:            order.ActualTime,
		Remark:                order.Remark,
		CancelReason:          order.CancelReason,
		CreateTime:            order.CreateTime,
		UpdateTime:            order.UpdateTime,
		Tracking:              make([]dto.DeliveryTrackingResponse, 0),
	}

	// 获取配送方式名称
	if order.MethodID > 0 {
		method, err := s.methodRepo.GetDeliveryMethodByID(context.Background(), order.MethodID)
		if err == nil && method != nil {
			resp.MethodName = method.Name
		}
	}

	// 获取配送时间段名称
	if order.TimeSlotID > 0 {
		timeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(context.Background(), order.TimeSlotID)
		if err == nil && timeSlot != nil {
			resp.TimeSlotName = timeSlot.Name
		}
	}

	// 获取配送轨迹
	trackings, err := s.orderRepo.ListDeliveryTrackings(context.Background(), order.ID)
	if err == nil && len(trackings) > 0 {
		for _, tracking := range trackings {
			resp.Tracking = append(resp.Tracking, dto.DeliveryTrackingResponse{
				ID:           tracking.ID,
				DeliveryID:   tracking.DeliveryID,
				OrderNo:      tracking.OrderNo,
				Status:       tracking.Status,
				StatusDesc:   constants.DeliveryStatusDesc[tracking.Status],
				Description:  tracking.Description,
				Location:     tracking.Location,
				OperatorID:   tracking.OperatorID,
				OperatorType: tracking.OperatorType,
				OperatorName: tracking.OperatorName,
				CreateTime:   tracking.CreateTime,
			})
		}
	}

	return resp
}

// 计算基本配送费用
func (s *DeliveryOrderServiceImpl) calculateDeliveryFee(rule *models.DeliveryRule, weight float64) float64 {
	var deliveryFee float64

	// 检查是否符合包邮条件 - 这部分在外部处理

	// 计算基本配送费用
	if weight <= rule.FirstWeight {
		// 首重范围内
		deliveryFee = rule.FirstFee
	} else {
		// 超出首重，计算续重费用
		if rule.AdditionalWeight > 0 {
			additionalWeight := weight - rule.FirstWeight
			additionalWeightCount := int(additionalWeight / rule.AdditionalWeight)
			if additionalWeight > float64(additionalWeightCount)*rule.AdditionalWeight {
				additionalWeightCount++
			}
			deliveryFee = rule.FirstFee + float64(additionalWeightCount)*rule.AdditionalFee
		} else {
			// 如果未设置续重，只收取首重费用
			deliveryFee = rule.FirstFee
		}
	}

	return deliveryFee
}

// CreateDeliveryOrder 创建配送订单
func (s *DeliveryOrderServiceImpl) CreateDeliveryOrder(ctx context.Context, req *dto.CreateDeliveryOrderRequest) (*dto.DeliveryOrderResponse, error) {
	// 验证配送方式是否有效
	method, err := s.methodRepo.GetDeliveryMethodByID(ctx, req.MethodID)
	if err != nil || method == nil || method.Status != constants.MethodStatusEnabled {
		logs.Error("配送方式无效: %v", err)
		return nil, errors.New("配送方式无效")
	}

	// 验证配送时间段是否有效（如果指定了时间段）
	var timeSlotFee float64
	if req.TimeSlotID > 0 {
		timeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, req.TimeSlotID)
		if err != nil || timeSlot == nil || timeSlot.Status != constants.TimeSlotStatusEnabled {
			logs.Error("配送时间段无效: %v", err)
			return nil, errors.New("配送时间段无效")
		}

		// 检查时间段是否已满
		if timeSlot.MaxOrderCount > 0 && timeSlot.CurrentOrders >= timeSlot.MaxOrderCount {
			return nil, errors.New("该配送时间段订单已满")
		}

		timeSlotFee = timeSlot.AdditionalFee
	}

	// 计算配送费用
	rule, err := s.ruleRepo.GetApplicableDeliveryRule(ctx, req.MethodID, req.AreaID, req.ShopID)
	if err != nil {
		logs.Error("获取适用的配送规则失败: %v", err)
		return nil, errors.New("无法计算配送费用")
	}

	var deliveryFee float64

	// 检查是否符合包邮条件
	if rule.FreeAmount > 0 && req.OrderAmount >= rule.FreeAmount {
		deliveryFee = 0
	} else {
		// 使用计算函数
		deliveryFee = s.calculateDeliveryFee(rule, req.Weight)
	}

	// 总费用 = 基本配送费 + 时间段附加费
	totalFee := deliveryFee + timeSlotFee

	// 创建配送订单模型对象
	order := &models.DeliveryOrder{
		OrderID:         req.OrderID,
		OrderNo:         req.OrderNo,
		MethodID:        req.MethodID,
		TimeSlotID:      req.TimeSlotID,
		UserID:          req.UserID,
		ShopID:          req.ShopID,
		Status:          constants.DeliveryStatusPending,
		DeliveryFee:     deliveryFee,
		TimeSlotFee:     timeSlotFee,
		TotalFee:        totalFee,
		Weight:          req.Weight,
		Distance:        req.Distance,
		ReceiverName:    req.ReceiverName,
		ReceiverPhone:   req.ReceiverPhone,
		ReceiverAddress: req.ReceiverAddress,
		CreateTime:      time.Now(),
		UpdateTime:      time.Now(),
	}

	// 根据配送时间段设置预计配送时间
	if req.TimeSlotID > 0 {
		timeSlot, _ := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, req.TimeSlotID)
		if timeSlot != nil {
			// 获取当前日期并添加时间段开始时间
			now := time.Now()
			startTimeStr := fmt.Sprintf("%s %s", now.Format("2006-01-02"), timeSlot.StartTime)
			expectedTime, _ := time.Parse("2006-01-02 15:04:05", startTimeStr)
			order.ExpectedTime = expectedTime
		}
	}

	// 调用仓库层创建配送订单
	id, err := s.orderRepo.CreateDeliveryOrder(ctx, order)
	if err != nil {
		logs.Error("创建配送订单失败: %v", err)
		return nil, err
	}

	// 更新时间段订单数量
	if req.TimeSlotID > 0 {
		err = s.timeSlotRepo.IncrementOrderCount(ctx, req.TimeSlotID)
		if err != nil {
			logs.Error("更新时间段订单数量失败: %v", err)
			// 这里不返回错误，不影响主流程
		}
	}

	// 添加初始物流跟踪记录
	tracking := &models.DeliveryTracking{
		DeliveryID:   id,
		Status:       constants.DeliveryStatusPending,
		Description:  "订单已创建，等待商家确认",
		OperatorID:   req.UserID,
		OperatorType: constants.OperatorTypeCustomer,
		CreateTime:   time.Now(),
	}
	_, err = s.orderRepo.CreateDeliveryTracking(ctx, tracking)
	if err != nil {
		logs.Error("创建物流跟踪记录失败: %v", err)
		// 这里不返回错误，不影响主流程
	}

	// 获取创建后的配送订单详情
	createdOrder, err := s.orderRepo.GetDeliveryOrderByID(ctx, id)
	if err != nil {
		logs.Error("获取新创建的配送订单失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToOrderResponse(createdOrder), nil
}

// GetDeliveryOrder 获取配送订单详情
func (s *DeliveryOrderServiceImpl) GetDeliveryOrder(ctx context.Context, deliveryID int64) (*dto.DeliveryOrderResponse, error) {
	// 调用仓库层获取配送订单
	order, err := s.orderRepo.GetDeliveryOrderByID(ctx, deliveryID)
	if err != nil {
		logs.Error("获取配送订单详情失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToOrderResponse(order), nil
}

// GetDeliveryOrderByOrderID 根据订单ID获取配送订单
func (s *DeliveryOrderServiceImpl) GetDeliveryOrderByOrderID(ctx context.Context, orderID int64) (*dto.DeliveryOrderResponse, error) {
	// 调用仓库层获取配送订单
	order, err := s.orderRepo.GetDeliveryOrderByOrderID(ctx, orderID)
	if err != nil {
		logs.Error("根据订单ID获取配送订单失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToOrderResponse(order), nil
}

// GetDeliveryOrderByOrderNo 根据订单编号获取配送订单
func (s *DeliveryOrderServiceImpl) GetDeliveryOrderByOrderNo(ctx context.Context, orderNo string) (*dto.DeliveryOrderResponse, error) {
	// 调用仓库层获取配送订单
	order, err := s.orderRepo.GetDeliveryOrderByOrderNo(ctx, orderNo)
	if err != nil {
		logs.Error("根据订单编号获取配送订单失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToOrderResponse(order), nil
}

// UpdateDeliveryStatus 更新配送订单状态
func (s *DeliveryOrderServiceImpl) UpdateDeliveryStatus(ctx context.Context, deliveryID int64, req *dto.UpdateDeliveryStatusRequest, operatorID int64, operatorType int) error {
	// 获取当前配送订单
	order, err := s.orderRepo.GetDeliveryOrderByID(ctx, deliveryID)
	if err != nil {
		logs.Error("获取配送订单失败: %v", err)
		return err
	}

	// 验证状态变更是否合法
	if !s.isValidStatusTransition(order.Status, req.Status) {
		return errors.New("非法的状态变更")
	}

	// 更新状态
	order.Status = req.Status
	order.UpdateTime = time.Now()

	// 如果是完成状态，设置完成时间
	if req.Status == constants.DeliveryStatusCompleted {
		now := time.Now()
		order.ActualTime = now
	}

	// 保存更新
	err = s.orderRepo.UpdateDeliveryOrder(ctx, order)
	if err != nil {
		logs.Error("更新配送订单状态失败: %v", err)
		return err
	}

	// 添加物流跟踪记录
	tracking := &models.DeliveryTracking{
		DeliveryID:   deliveryID,
		Status:       req.Status,
		Description:  req.Description,
		OperatorID:   operatorID,
		OperatorType: operatorType,
		CreateTime:   time.Now(),
	}
	_, err = s.orderRepo.CreateDeliveryTracking(ctx, tracking)
	if err != nil {
		logs.Error("创建物流跟踪记录失败: %v", err)
		// 这里不返回错误，不影响主流程
	}

	return nil
}

// 验证状态变更是否合法
func (s *DeliveryOrderServiceImpl) isValidStatusTransition(currentStatus, newStatus int) bool {
	switch currentStatus {
	case constants.DeliveryStatusPending:
		// 待处理 -> 已确认、已取消
		return newStatus == constants.DeliveryStatusConfirmed || newStatus == constants.DeliveryStatusCancelled
	case constants.DeliveryStatusConfirmed:
		// 已确认 -> a.分配配送员 b.等待配送员 c.已取消
		return newStatus == constants.DeliveryStatusAssigned ||
			newStatus == constants.DeliveryStatusWaitingRunner ||
			newStatus == constants.DeliveryStatusCancelled
	case constants.DeliveryStatusWaitingRunner:
		// 等待配送员 -> 已分配、已取消
		return newStatus == constants.DeliveryStatusAssigned || newStatus == constants.DeliveryStatusCancelled
	case constants.DeliveryStatusAssigned:
		// 已分配 -> 配送中、已取消
		return newStatus == constants.DeliveryStatusDelivering || newStatus == constants.DeliveryStatusCancelled
	case constants.DeliveryStatusDelivering:
		// 配送中 -> 已送达、配送失败、已取消
		return newStatus == constants.DeliveryStatusDelivered ||
			newStatus == constants.DeliveryStatusFailed ||
			newStatus == constants.DeliveryStatusCancelled
	case constants.DeliveryStatusDelivered:
		// 已送达 -> 已完成、配送失败
		return newStatus == constants.DeliveryStatusCompleted || newStatus == constants.DeliveryStatusFailed
	case constants.DeliveryStatusFailed:
		// 配送失败 -> 重新配送、已取消
		return newStatus == constants.DeliveryStatusRedelivering || newStatus == constants.DeliveryStatusCancelled
	case constants.DeliveryStatusRedelivering:
		// 重新配送 -> 已送达、配送失败、已取消
		return newStatus == constants.DeliveryStatusDelivered ||
			newStatus == constants.DeliveryStatusFailed ||
			newStatus == constants.DeliveryStatusCancelled
	}

	return false
}

// CancelDelivery 取消配送订单
func (s *DeliveryOrderServiceImpl) CancelDelivery(ctx context.Context, deliveryID int64, req *dto.CancelDeliveryRequest, operatorID int64, operatorType int) error {
	// 获取当前配送订单
	order, err := s.orderRepo.GetDeliveryOrderByID(ctx, deliveryID)
	if err != nil {
		logs.Error("获取配送订单失败: %v", err)
		return err
	}

	// 检查是否可以取消
	if order.Status == constants.DeliveryStatusCompleted ||
		order.Status == constants.DeliveryStatusCancelled {
		return errors.New("当前状态无法取消配送")
	}

	// 更新状态
	order.Status = constants.DeliveryStatusCancelled
	order.CancelReason = req.Reason
	order.UpdateTime = time.Now()

	// 保存更新
	err = s.orderRepo.UpdateDeliveryOrder(ctx, order)
	if err != nil {
		logs.Error("取消配送订单失败: %v", err)
		return err
	}

	// 添加物流跟踪记录
	tracking := &models.DeliveryTracking{
		DeliveryID:   deliveryID,
		Status:       constants.DeliveryStatusCancelled,
		Description:  fmt.Sprintf("配送已取消，原因：%s", req.Reason),
		OperatorID:   operatorID,
		OperatorType: operatorType,
		CreateTime:   time.Now(),
	}
	_, err = s.orderRepo.CreateDeliveryTracking(ctx, tracking)
	if err != nil {
		logs.Error("创建物流跟踪记录失败: %v", err)
		// 这里不返回错误，不影响主流程
	}

	// 如果有配送时间段，减少该时间段的订单数量
	if order.TimeSlotID > 0 {
		err = s.timeSlotRepo.DecrementOrderCount(ctx, order.TimeSlotID)
		if err != nil {
			logs.Error("更新时间段订单数量失败: %v", err)
			// 这里不返回错误，不影响主流程
		}
	}

	return nil
}

// AssignRunner 分配配送员
func (s *DeliveryOrderServiceImpl) AssignRunner(ctx context.Context, deliveryID, runnerID int64, operatorID int64, operatorType int) error {
	// 获取当前配送订单
	order, err := s.orderRepo.GetDeliveryOrderByID(ctx, deliveryID)
	if err != nil {
		logs.Error("获取配送订单失败: %v", err)
		return err
	}

	// 检查是否可以分配配送员
	if order.Status != constants.DeliveryStatusConfirmed &&
		order.Status != constants.DeliveryStatusWaitingRunner {
		return errors.New("当前状态无法分配配送员")
	}

	// 更新配送员信息和状态
	order.RunnerID = runnerID
	order.Status = constants.DeliveryStatusAssigned
	order.UpdateTime = time.Now()

	// 保存更新
	err = s.orderRepo.UpdateDeliveryOrder(ctx, order)
	if err != nil {
		logs.Error("分配配送员失败: %v", err)
		return err
	}

	// 添加物流跟踪记录
	tracking := &models.DeliveryTracking{
		DeliveryID:   deliveryID,
		Status:       constants.DeliveryStatusAssigned,
		Description:  "订单已分配配送员",
		OperatorID:   operatorID,
		OperatorType: operatorType,
		CreateTime:   time.Now(),
	}
	_, err = s.orderRepo.CreateDeliveryTracking(ctx, tracking)
	if err != nil {
		logs.Error("创建物流跟踪记录失败: %v", err)
		// 这里不返回错误，不影响主流程
	}

	return nil
}

// UpdateTrackingInfo 更新物流信息
func (s *DeliveryOrderServiceImpl) UpdateTrackingInfo(ctx context.Context, deliveryID int64, trackingNo, trackingCompany string, operatorID int64, operatorType int) error {
	// 获取当前配送订单
	order, err := s.orderRepo.GetDeliveryOrderByID(ctx, deliveryID)
	if err != nil {
		logs.Error("获取配送订单失败: %v", err)
		return err
	}

	// 更新物流信息
	order.TrackingNo = trackingNo
	order.TrackingCompany = trackingCompany
	order.UpdateTime = time.Now()

	// 保存更新
	err = s.orderRepo.UpdateDeliveryOrder(ctx, order)
	if err != nil {
		logs.Error("更新物流信息失败: %v", err)
		return err
	}

	// 添加物流跟踪记录
	description := fmt.Sprintf("更新物流信息：物流公司 %s，物流单号 %s", trackingCompany, trackingNo)
	tracking := &models.DeliveryTracking{
		DeliveryID:   deliveryID,
		Status:       order.Status,
		Description:  description,
		OperatorID:   operatorID,
		OperatorType: operatorType,
		CreateTime:   time.Now(),
	}
	_, err = s.orderRepo.CreateDeliveryTracking(ctx, tracking)
	if err != nil {
		logs.Error("创建物流跟踪记录失败: %v", err)
		// 这里不返回错误，不影响主流程
	}

	return nil
}

// ListUserDeliveryOrders 获取用户的配送订单列表
func (s *DeliveryOrderServiceImpl) ListUserDeliveryOrders(ctx context.Context, userID int64, status int, page, pageSize int) ([]*dto.DeliveryOrderResponse, int64, error) {
	// 调用仓库层获取用户配送订单列表
	orders, total, err := s.orderRepo.ListUserDeliveryOrders(ctx, userID, status, page, pageSize)
	if err != nil {
		logs.Error("获取用户配送订单列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象列表
	result := make([]*dto.DeliveryOrderResponse, 0, len(orders))
	for _, order := range orders {
		resp := s.convertToOrderResponse(order)
		result = append(result, resp)
	}

	return result, total, nil
}

// ListShopDeliveryOrders 获取店铺的配送订单列表
func (s *DeliveryOrderServiceImpl) ListShopDeliveryOrders(ctx context.Context, shopID int64, status int, page, pageSize int) ([]*dto.DeliveryOrderResponse, int64, error) {
	// 调用仓库层获取店铺配送订单列表
	orders, total, err := s.orderRepo.ListShopDeliveryOrders(ctx, shopID, status, page, pageSize)
	if err != nil {
		logs.Error("获取店铺配送订单列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象列表
	result := make([]*dto.DeliveryOrderResponse, 0, len(orders))
	for _, order := range orders {
		resp := s.convertToOrderResponse(order)
		result = append(result, resp)
	}

	return result, total, nil
}

// ListRunnerDeliveryOrders 获取配送员的配送订单列表
func (s *DeliveryOrderServiceImpl) ListRunnerDeliveryOrders(ctx context.Context, runnerID int64, status int, page, pageSize int) ([]*dto.DeliveryOrderResponse, int64, error) {
	// 调用仓库层获取配送员配送订单列表
	orders, total, err := s.orderRepo.ListRunnerDeliveryOrders(ctx, runnerID, status, page, pageSize)
	if err != nil {
		logs.Error("获取配送员配送订单列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象列表
	result := make([]*dto.DeliveryOrderResponse, 0, len(orders))
	for _, order := range orders {
		resp := s.convertToOrderResponse(order)
		result = append(result, resp)
	}

	return result, total, nil
}

// ListDeliveryTrackings 获取配送物流跟踪记录列表
func (s *DeliveryOrderServiceImpl) ListDeliveryTrackings(ctx context.Context, deliveryID int64) ([]*dto.DeliveryTrackingResponse, error) {
	// 调用仓库层获取物流跟踪记录列表
	trackings, err := s.orderRepo.ListDeliveryTrackings(ctx, deliveryID)
	if err != nil {
		logs.Error("获取配送物流跟踪记录列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象列表
	result := make([]*dto.DeliveryTrackingResponse, len(trackings))
	for i, tracking := range trackings {
		result[i] = &dto.DeliveryTrackingResponse{
			ID:           tracking.ID,
			DeliveryID:   tracking.DeliveryID,
			Status:       tracking.Status,
			StatusDesc:   constants.DeliveryStatusDesc[tracking.Status],
			Description:  tracking.Description,
			OperatorID:   tracking.OperatorID,
			OperatorType: tracking.OperatorType,
			CreateTime:   tracking.CreateTime,
		}
	}

	return result, nil
}

// NewDeliveryOrderService 创建配送订单服务
func NewDeliveryOrderService(
	orderRepo repositories.DeliveryOrderRepository,
	methodRepo repositories.DeliveryMethodRepository,
	timeSlotRepo repositories.DeliveryTimeSlotRepository,
	ruleRepo repositories.DeliveryRuleRepository,
) *DeliveryOrderServiceImpl {
	return &DeliveryOrderServiceImpl{
		orderRepo:    orderRepo,
		methodRepo:   methodRepo,
		timeSlotRepo: timeSlotRepo,
		ruleRepo:     ruleRepo,
	}
}
