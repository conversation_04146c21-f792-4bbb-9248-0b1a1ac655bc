/**
 * delivery_rule_service_impl.go
 * 配送费用规则服务实现
 *
 * 本文件实现了配送费用规则的业务逻辑，包括规则的创建、查询、更新和删除等功能
 * 以及计算配送费用的核心功能
 */

package impl

import (
	"context"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/constants"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/models"
	"o_mall_backend/modules/delivery/repositories"
)

// DeliveryRuleServiceImpl 配送费用规则服务实现
type DeliveryRuleServiceImpl struct {
	ruleRepo     repositories.DeliveryRuleRepository
	methodRepo   repositories.DeliveryMethodRepository
	areaRepo     repositories.DeliveryAreaRepository
	timeSlotRepo repositories.DeliveryTimeSlotRepository
}

// 将模型对象转换为响应DTO对象
func (s *DeliveryRuleServiceImpl) convertToRuleResponse(ctx context.Context, rule *models.DeliveryRule) (*dto.DeliveryRuleResponse, error) {
	if rule == nil {
		return nil, nil
	}

	resp := &dto.DeliveryRuleResponse{
		ID:               rule.ID,
		MethodID:         rule.MethodID,
		AreaID:           rule.AreaID,
		ShopID:           rule.ShopID,
		FirstWeight:      rule.FirstWeight,
		FirstFee:         rule.FirstFee,
		AdditionalWeight: rule.AdditionalWeight,
		AdditionalFee:    rule.AdditionalFee,
		MinAmount:        rule.MinAmount,
		FreeAmount:       rule.FreeAmount,
		MaxDistance:      rule.MaxDistance,
		Status:           rule.Status,
		StatusDesc:       constants.RuleStatusDesc[rule.Status],
		StartTime:        rule.StartTime,
		EndTime:          rule.EndTime,
		CreateTime:       rule.CreateTime,
		UpdateTime:       rule.UpdateTime,
	}

	// 获取配送方式名称
	if rule.MethodID > 0 {
		method, err := s.methodRepo.GetDeliveryMethodByID(ctx, rule.MethodID)
		if err == nil && method != nil {
			resp.MethodName = method.Name
		}
	}

	// 获取配送区域名称
	if rule.AreaID > 0 {
		area, err := s.areaRepo.GetDeliveryAreaByID(ctx, rule.AreaID)
		if err == nil && area != nil {
			resp.AreaName = area.Name
		}
	} else {
		resp.AreaName = "全国"
	}

	// 获取店铺名称（这里需要店铺模块的支持，暂时留空）
	if rule.ShopID > 0 {
		// TODO: 获取店铺名称，需要与店铺模块集成
	} else {
		resp.ShopName = "平台"
	}

	return resp, nil
}

// CreateDeliveryRule 创建配送费用规则
func (s *DeliveryRuleServiceImpl) CreateDeliveryRule(ctx context.Context, req *dto.DeliveryRuleRequest) (*dto.DeliveryRuleResponse, error) {
	// 创建模型对象
	rule := &models.DeliveryRule{
		MethodID:         req.MethodID,
		AreaID:           req.AreaID,
		ShopID:           req.ShopID,
		FirstWeight:      req.FirstWeight,
		FirstFee:         req.FirstFee,
		AdditionalWeight: req.AdditionalWeight,
		AdditionalFee:    req.AdditionalFee,
		MinAmount:        req.MinAmount,
		FreeAmount:       req.FreeAmount,
		MaxDistance:      req.MaxDistance,
		Status:           req.Status,
		StartTime:        req.StartTime,
		EndTime:          req.EndTime,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	}

	// 如果未指定状态，默认为启用
	if rule.Status == 0 {
		rule.Status = constants.RuleStatusEnabled
	}

	// 调用仓库层创建配送费用规则
	id, err := s.ruleRepo.CreateDeliveryRule(ctx, rule)
	if err != nil {
		logs.Error("创建配送费用规则失败: %v", err)
		return nil, err
	}

	// 获取创建后的配送费用规则详情
	createdRule, err := s.ruleRepo.GetDeliveryRuleByID(ctx, id)
	if err != nil {
		logs.Error("获取新创建的配送费用规则失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToRuleResponse(ctx, createdRule)
}

// GetDeliveryRule 获取配送费用规则详情
func (s *DeliveryRuleServiceImpl) GetDeliveryRule(ctx context.Context, ruleID int64) (*dto.DeliveryRuleResponse, error) {
	// 调用仓库层获取配送费用规则
	rule, err := s.ruleRepo.GetDeliveryRuleByID(ctx, ruleID)
	if err != nil {
		logs.Error("获取配送费用规则详情失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToRuleResponse(ctx, rule)
}

// UpdateDeliveryRule 更新配送费用规则
func (s *DeliveryRuleServiceImpl) UpdateDeliveryRule(ctx context.Context, ruleID int64, req *dto.DeliveryRuleRequest) (*dto.DeliveryRuleResponse, error) {
	// 先获取现有配送费用规则
	rule, err := s.ruleRepo.GetDeliveryRuleByID(ctx, ruleID)
	if err != nil {
		logs.Error("获取配送费用规则失败: %v", err)
		return nil, err
	}

	// 更新字段
	rule.MethodID = req.MethodID
	rule.AreaID = req.AreaID
	rule.ShopID = req.ShopID
	rule.FirstWeight = req.FirstWeight
	rule.FirstFee = req.FirstFee
	rule.AdditionalWeight = req.AdditionalWeight
	rule.AdditionalFee = req.AdditionalFee
	rule.MinAmount = req.MinAmount
	rule.FreeAmount = req.FreeAmount
	rule.MaxDistance = req.MaxDistance
	rule.Status = req.Status
	rule.StartTime = req.StartTime
	rule.EndTime = req.EndTime
	rule.UpdateTime = time.Now()

	// 调用仓库层更新配送费用规则
	err = s.ruleRepo.UpdateDeliveryRule(ctx, rule)
	if err != nil {
		logs.Error("更新配送费用规则失败: %v", err)
		return nil, err
	}

	// 获取更新后的配送费用规则详情
	updatedRule, err := s.ruleRepo.GetDeliveryRuleByID(ctx, ruleID)
	if err != nil {
		logs.Error("获取更新后的配送费用规则失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToRuleResponse(ctx, updatedRule)
}

// DeleteDeliveryRule 删除配送费用规则
func (s *DeliveryRuleServiceImpl) DeleteDeliveryRule(ctx context.Context, ruleID int64) error {
	// 调用仓库层删除配送费用规则
	err := s.ruleRepo.DeleteDeliveryRule(ctx, ruleID)
	if err != nil {
		logs.Error("删除配送费用规则失败: %v", err)
		return err
	}

	return nil
}

// ListDeliveryRules 获取配送费用规则列表
func (s *DeliveryRuleServiceImpl) ListDeliveryRules(ctx context.Context, methodID, areaID, shopID int64) ([]*dto.DeliveryRuleResponse, error) {
	// 调用仓库层获取配送费用规则列表
	rules, err := s.ruleRepo.ListDeliveryRules(ctx, methodID, areaID, shopID)
	if err != nil {
		logs.Error("获取配送费用规则列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象列表
	result := make([]*dto.DeliveryRuleResponse, 0, len(rules))
	for _, rule := range rules {
		resp, err := s.convertToRuleResponse(ctx, rule)
		if err != nil {
			logs.Error("转换配送规则响应对象失败: %v", err)
			continue
		}
		result = append(result, resp)
	}

	return result, nil
}

// CalculateDeliveryFee 计算配送费用
func (s *DeliveryRuleServiceImpl) CalculateDeliveryFee(ctx context.Context, req *dto.DeliveryFeeCalculateRequest) (*dto.DeliveryFeeCalculateResponse, error) {
	// 获取适用的配送费用规则
	rule, err := s.ruleRepo.GetApplicableDeliveryRule(ctx, req.MethodID, req.AreaID, req.ShopID)
	if err != nil {
		logs.Error("获取适用的配送费用规则失败: %v", err)
		return nil, err
	}

	// 创建响应对象
	resp := &dto.DeliveryFeeCalculateResponse{
		DeliveryFee:     0,
		TimeSlotFee:     0,
		TotalFee:        0,
		IsFree:          false,
		UnderMinAmount:  false,
		OverMaxDistance: false,
		MinAmount:       rule.MinAmount,
		MaxDistance:     rule.MaxDistance,
	}

	// 检查是否低于起送金额
	if rule.MinAmount > 0 && req.OrderAmount < rule.MinAmount {
		resp.UnderMinAmount = true
		// 这里可以选择返回错误或者继续计算费用
		// return nil, errors.New("订单金额低于起送金额")
	}

	// 检查是否超过最大配送距离
	if rule.MaxDistance > 0 && req.Distance > rule.MaxDistance {
		resp.OverMaxDistance = true
		// 这里可以选择返回错误或者继续计算费用
		// return nil, errors.New("配送距离超过最大限制")
	}

	// 检查是否符合包邮条件
	if rule.FreeAmount > 0 && req.OrderAmount >= rule.FreeAmount {
		resp.IsFree = true
		resp.DeliveryFee = 0
	} else {
		// 计算基本配送费用
		if req.Weight <= rule.FirstWeight {
			// 首重范围内
			resp.DeliveryFee = rule.FirstFee
		} else {
			// 超出首重，计算续重费用
			if rule.AdditionalWeight > 0 {
				additionalWeightCount := (req.Weight - rule.FirstWeight) / rule.AdditionalWeight
				// 判断是否有余数，如果有则向上取整（加1）
				if (req.Weight - rule.FirstWeight) > additionalWeightCount*rule.AdditionalWeight {
					additionalWeightCount++
				}
				resp.DeliveryFee = rule.FirstFee + additionalWeightCount*rule.AdditionalFee
			} else {
				// 如果未设置续重，只收取首重费用
				resp.DeliveryFee = rule.FirstFee
			}
		}
	}

	// 获取时间段附加费用
	if req.TimeSlotID > 0 {
		timeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, req.TimeSlotID)
		if err == nil && timeSlot != nil {
			resp.TimeSlotFee = timeSlot.AdditionalFee
		}
	}

	// 计算总费用
	resp.TotalFee = resp.DeliveryFee + resp.TimeSlotFee

	return resp, nil
}

// NewDeliveryRuleService 创建配送费用规则服务
func NewDeliveryRuleService(
	ruleRepo repositories.DeliveryRuleRepository,
	methodRepo repositories.DeliveryMethodRepository,
	areaRepo repositories.DeliveryAreaRepository,
	timeSlotRepo repositories.DeliveryTimeSlotRepository,
) *DeliveryRuleServiceImpl {
	return &DeliveryRuleServiceImpl{
		ruleRepo:     ruleRepo,
		methodRepo:   methodRepo,
		areaRepo:     areaRepo,
		timeSlotRepo: timeSlotRepo,
	}
}
