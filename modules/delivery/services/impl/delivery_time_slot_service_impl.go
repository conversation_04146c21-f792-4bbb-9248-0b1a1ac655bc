/**
 * delivery_time_slot_service_impl.go
 * 配送时间段服务实现
 *
 * 本文件实现了配送时间段的业务逻辑，包括时间段的创建、查询、更新和删除等功能
 */

package impl

import (
	"context"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/delivery/constants"
	"o_mall_backend/modules/delivery/dto"
	"o_mall_backend/modules/delivery/models"
	"o_mall_backend/modules/delivery/repositories"
)

// DeliveryTimeSlotServiceImpl 配送时间段服务实现
type DeliveryTimeSlotServiceImpl struct {
	timeSlotRepo repositories.DeliveryTimeSlotRepository
	methodRepo   repositories.DeliveryMethodRepository
}

// 将模型对象转换为响应DTO对象
func (s *DeliveryTimeSlotServiceImpl) convertToTimeSlotResponse(ctx context.Context, timeSlot *models.DeliveryTimeSlot) (*dto.DeliveryTimeSlotResponse, error) {
	if timeSlot == nil {
		return nil, nil
	}

	resp := &dto.DeliveryTimeSlotResponse{
		ID:            timeSlot.ID,
		MethodID:      timeSlot.MethodID,
		Name:          timeSlot.Name,
		StartTime:     timeSlot.StartTime,
		EndTime:       timeSlot.EndTime,
		Status:        timeSlot.Status,
		StatusDesc:    constants.TimeSlotStatusDesc[timeSlot.Status],
		IsDefault:     timeSlot.IsDefault,
		SortOrder:     timeSlot.SortOrder,
		AdditionalFee: timeSlot.AdditionalFee,
		MaxOrders:     timeSlot.MaxOrderCount,
		CurrentOrders: timeSlot.CurrentOrders,
		CreateTime:    timeSlot.CreateTime,
		UpdateTime:    timeSlot.UpdateTime,
	}

	// 获取配送方式名称
	if timeSlot.MethodID > 0 {
		method, err := s.methodRepo.GetDeliveryMethodByID(ctx, timeSlot.MethodID)
		if err == nil && method != nil {
			resp.MethodName = method.Name
		}
	}

	return resp, nil
}

// CreateDeliveryTimeSlot 创建配送时间段
func (s *DeliveryTimeSlotServiceImpl) CreateDeliveryTimeSlot(ctx context.Context, req *dto.DeliveryTimeSlotRequest) (*dto.DeliveryTimeSlotResponse, error) {
	// 创建模型对象
	timeSlot := &models.DeliveryTimeSlot{
		MethodID:      req.MethodID,
		Name:          req.Name,
		StartTime:     req.StartTime,
		EndTime:       req.EndTime,
		Status:        req.Status,
		IsDefault:     req.IsDefault,
		SortOrder:     req.SortOrder,
		AdditionalFee: req.AdditionalFee,
		MaxOrderCount: req.MaxOrders,
		CurrentOrders: 0, // 新创建的时间段，当前订单数为0
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}

	// 如果未指定状态，默认为启用
	if timeSlot.Status == 0 {
		timeSlot.Status = constants.TimeSlotStatusEnabled
	}

	// 调用仓库层创建配送时间段
	id, err := s.timeSlotRepo.CreateDeliveryTimeSlot(ctx, timeSlot)
	if err != nil {
		logs.Error("创建配送时间段失败: %v", err)
		return nil, err
	}

	// 获取创建后的配送时间段详情
	createdTimeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, id)
	if err != nil {
		logs.Error("获取新创建的配送时间段失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToTimeSlotResponse(ctx, createdTimeSlot)
}

// GetDeliveryTimeSlot 获取配送时间段详情
func (s *DeliveryTimeSlotServiceImpl) GetDeliveryTimeSlot(ctx context.Context, timeSlotID int64) (*dto.DeliveryTimeSlotResponse, error) {
	// 调用仓库层获取配送时间段
	timeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, timeSlotID)
	if err != nil {
		logs.Error("获取配送时间段详情失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToTimeSlotResponse(ctx, timeSlot)
}

// UpdateDeliveryTimeSlot 更新配送时间段
func (s *DeliveryTimeSlotServiceImpl) UpdateDeliveryTimeSlot(ctx context.Context, timeSlotID int64, req *dto.DeliveryTimeSlotRequest) (*dto.DeliveryTimeSlotResponse, error) {
	// 先获取现有配送时间段
	timeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, timeSlotID)
	if err != nil {
		logs.Error("获取配送时间段失败: %v", err)
		return nil, err
	}

	// 更新字段
	timeSlot.MethodID = req.MethodID
	timeSlot.Name = req.Name
	timeSlot.StartTime = req.StartTime
	timeSlot.EndTime = req.EndTime
	timeSlot.Status = req.Status
	timeSlot.IsDefault = req.IsDefault
	timeSlot.SortOrder = req.SortOrder
	timeSlot.AdditionalFee = req.AdditionalFee
	timeSlot.MaxOrderCount = req.MaxOrders
	timeSlot.UpdateTime = time.Now()

	// 调用仓库层更新配送时间段
	err = s.timeSlotRepo.UpdateDeliveryTimeSlot(ctx, timeSlot)
	if err != nil {
		logs.Error("更新配送时间段失败: %v", err)
		return nil, err
	}

	// 获取更新后的配送时间段详情
	updatedTimeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, timeSlotID)
	if err != nil {
		logs.Error("获取更新后的配送时间段失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToTimeSlotResponse(ctx, updatedTimeSlot)
}

// DeleteDeliveryTimeSlot 删除配送时间段
func (s *DeliveryTimeSlotServiceImpl) DeleteDeliveryTimeSlot(ctx context.Context, timeSlotID int64) error {
	// 调用仓库层删除配送时间段
	err := s.timeSlotRepo.DeleteDeliveryTimeSlot(ctx, timeSlotID)
	if err != nil {
		logs.Error("删除配送时间段失败: %v", err)
		return err
	}

	return nil
}

// ListDeliveryTimeSlots 获取配送时间段列表
func (s *DeliveryTimeSlotServiceImpl) ListDeliveryTimeSlots(ctx context.Context, methodID int64, status int) ([]*dto.DeliveryTimeSlotResponse, error) {
	// 调用仓库层获取配送时间段列表
	timeSlots, err := s.timeSlotRepo.ListDeliveryTimeSlots(ctx, methodID, status)
	if err != nil {
		logs.Error("获取配送时间段列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象列表
	result := make([]*dto.DeliveryTimeSlotResponse, 0, len(timeSlots))
	for _, timeSlot := range timeSlots {
		resp, err := s.convertToTimeSlotResponse(ctx, timeSlot)
		if err != nil {
			logs.Error("转换配送时间段响应对象失败: %v", err)
			continue
		}
		result = append(result, resp)
	}

	return result, nil
}

// SetDefaultDeliveryTimeSlot 设置默认配送时间段
func (s *DeliveryTimeSlotServiceImpl) SetDefaultDeliveryTimeSlot(ctx context.Context, methodID, timeSlotID int64) error {
	// 调用仓库层设置默认配送时间段
	err := s.timeSlotRepo.SetDefaultDeliveryTimeSlot(ctx, methodID, timeSlotID)
	if err != nil {
		logs.Error("设置默认配送时间段失败: %v", err)
		return err
	}

	return nil
}

// CheckTimeSlotAvailable 检查配送时间段是否可用
func (s *DeliveryTimeSlotServiceImpl) CheckTimeSlotAvailable(ctx context.Context, timeSlotID int64) (bool, error) {
	// 获取配送时间段
	timeSlot, err := s.timeSlotRepo.GetDeliveryTimeSlotByID(ctx, timeSlotID)
	if err != nil {
		logs.Error("获取配送时间段失败: %v", err)
		return false, err
	}

	// 检查状态
	if timeSlot.Status != constants.TimeSlotStatusEnabled {
		return false, nil
	}

	// 检查当前时间是否在时间段范围内
	now := time.Now()
	currentTimeStr := now.Format("15:04:05")
	if currentTimeStr < timeSlot.StartTime || currentTimeStr > timeSlot.EndTime {
		return false, nil
	}

	// 检查订单数量是否已达到上限
	if timeSlot.MaxOrderCount > 0 && timeSlot.CurrentOrders >= timeSlot.MaxOrderCount {
		return false, nil
	}

	return true, nil
}

// NewDeliveryTimeSlotService 创建配送时间段服务
func NewDeliveryTimeSlotService(
	timeSlotRepo repositories.DeliveryTimeSlotRepository,
	methodRepo repositories.DeliveryMethodRepository,
) *DeliveryTimeSlotServiceImpl {
	return &DeliveryTimeSlotServiceImpl{
		timeSlotRepo: timeSlotRepo,
		methodRepo:   methodRepo,
	}
}
