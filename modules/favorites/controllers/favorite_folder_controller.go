/**
 * 收藏夹控制器
 *
 * 本文件实现了收藏夹相关的HTTP接口，处理前端请求并调用服务层。
 * 提供收藏夹的增删改查等功能。
 */

package controllers

import (
	"strconv"

	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/favorites/dto"
	"o_mall_backend/modules/favorites/services"
	"o_mall_backend/utils/common"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// FavoriteFolderController 收藏夹控制器
type FavoriteFolderController struct {
	web.Controller
	favoriteService services.UserFavoriteService
}

// 初始化控制器
func (c *FavoriteFolderController) Prepare() {
	c.favoriteService = services.NewUserFavoriteService()
}

// ParseRequest 解析请求数据
func (c *FavoriteFolderController) ParseRequest(req interface{}) error {
	logs.Info("[FavoriteFolder] ParseRequest 被调用")
	return common.ParseRequest(c.Ctx, req)
}

// Create 创建收藏夹
// @router /api/v1/user/secured/favorites/folders/create [post]
func (c *FavoriteFolderController) Create() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.CreateFolderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层创建收藏夹
	folder, err := c.favoriteService.CreateFolder(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, folder)
}

// Update 更新收藏夹
// @router /api/v1/user/secured/favorites/folders/update/:id [post]
func (c *FavoriteFolderController) Update() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏夹ID
	folderIDStr := c.Ctx.Input.Param(":id")
	folderID, err := strconv.ParseInt(folderIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的收藏夹ID")
		return
	}

	// 解析请求参数
	var req dto.UpdateFolderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层更新收藏夹
	err = c.favoriteService.UpdateFolder(userID, folderID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "更新成功")
}

// Delete 删除收藏夹
// @router /api/v1/user/secured/favorites/folders/delete/:id [post]
func (c *FavoriteFolderController) Delete() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏夹ID
	folderIDStr := c.Ctx.Input.Param(":id")
	folderID, err := strconv.ParseInt(folderIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的收藏夹ID")
		return
	}

	// 调用服务层删除收藏夹
	err = c.favoriteService.DeleteFolder(userID, folderID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "删除成功")
}

// Detail 获取收藏夹详情
// @router /api/v1/user/secured/favorites/folders/detail/:id [get]
func (c *FavoriteFolderController) Detail() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏夹ID
	folderIDStr := c.Ctx.Input.Param(":id")
	folderID, err := strconv.ParseInt(folderIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的收藏夹ID")
		return
	}

	// 调用服务层获取收藏夹详情
	folder, err := c.favoriteService.GetFolderDetail(userID, folderID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, folder)
}

// List 获取收藏夹列表
// @router /api/v1/user/secured/favorites/folders/list [get]
func (c *FavoriteFolderController) List() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取收藏夹列表
	folders, err := c.favoriteService.ListFolders(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, folders)
}

// Options 处理OPTIONS请求
func (c *FavoriteFolderController) Options() {
	result.OK(c.Ctx, "OK")
}
