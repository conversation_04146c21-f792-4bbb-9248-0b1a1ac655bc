/**
 * 用户收藏控制器
 *
 * 本文件实现了用户收藏相关的HTTP接口，处理前端请求并调用服务层。
 * 提供收藏的增删改查、统计分析等功能。
 */

package controllers

import (
	"strconv"

	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/favorites/dto"
	"o_mall_backend/modules/favorites/services"
	"o_mall_backend/utils/common"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// UserFavoriteController 用户收藏控制器
type UserFavoriteController struct {
	web.Controller
	favoriteService services.UserFavoriteService
}

// 初始化控制器
func (c *UserFavoriteController) Prepare() {
	c.favoriteService = services.NewUserFavoriteService()
}

// ParseRequest 解析请求数据
func (c *UserFavoriteController) ParseRequest(req interface{}) error {
	logs.Info("[Favorites] ParseRequest 被调用")
	return common.ParseRequest(c.Ctx, req)
}

// Add 添加收藏
// @router /api/v1/user/secured/favorites/add [post]
func (c *UserFavoriteController) Add() {
	logs.Info("[Favorites] Add 方法被调用")

	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("[Favorites] 获取用户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("[Favorites] 获取到用户ID: %d", userID)

	// 解析请求参数
	var req dto.AddFavoriteRequest
	logs.Info("[Favorites] 开始解析请求参数")
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("[Favorites] 参数解析失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}
	logs.Info("[Favorites] 请求参数解析成功: %+v", req)
	logs.Info("[Favorites] 收藏类型: %s, 类型: %T", req.Type, req.Type)

	// 调用服务层添加收藏
	logs.Info("[Favorites] 调用服务层添加收藏")
	favorite, err := c.favoriteService.AddFavorite(userID, &req)
	if err != nil {
		logs.Error("[Favorites] 添加收藏失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("[Favorites] 收藏添加成功")
	result.OK(c.Ctx, favorite)
}

// Update 更新收藏
// @router /api/v1/user/secured/favorites/update/:id [post]
func (c *UserFavoriteController) Update() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏ID
	favoriteIDStr := c.Ctx.Input.Param(":id")
	favoriteID, err := strconv.ParseInt(favoriteIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的收藏ID")
		return
	}

	// 解析请求参数
	var req dto.UpdateFavoriteRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层更新收藏
	err = c.favoriteService.UpdateFavorite(userID, favoriteID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "更新成功")
}

// Delete 删除收藏
// @router /api/v1/user/secured/favorites/delete/:id [post]
func (c *UserFavoriteController) Delete() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏ID
	favoriteIDStr := c.Ctx.Input.Param(":id")
	favoriteID, err := strconv.ParseInt(favoriteIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的收藏ID")
		return
	}

	// 调用服务层删除收藏
	err = c.favoriteService.DeleteFavorite(userID, favoriteID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "删除成功")
}

// BatchDelete 批量删除收藏
// @router /api/v1/user/secured/favorites/batch-delete [post]
func (c *UserFavoriteController) BatchDelete() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.BatchDeleteRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层批量删除收藏
	err = c.favoriteService.BatchDeleteFavorites(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "批量删除成功")
}

// Detail 获取收藏详情
// @router /api/v1/user/secured/favorites/detail/:id [get]
func (c *UserFavoriteController) Detail() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏ID
	favoriteIDStr := c.Ctx.Input.Param(":id")
	favoriteID, err := strconv.ParseInt(favoriteIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的收藏ID")
		return
	}

	// 调用服务层获取收藏详情
	favorite, err := c.favoriteService.GetFavoriteDetail(userID, favoriteID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, favorite)
}

// List 获取收藏列表
// @router /api/v1/user/secured/favorites/list [get]
func (c *UserFavoriteController) List() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析查询参数
	var req dto.FavoriteQueryRequest
	req.Type = c.GetString("type")
	req.Tags = c.GetString("tags")
	req.Keyword = c.GetString("keyword")
	req.StartDate = c.GetString("start_date")
	req.EndDate = c.GetString("end_date")
	req.SortBy = c.GetString("sort_by")
	req.SortOrder = c.GetString("sort_order")

	if folderID, err := c.GetInt64("folder_id"); err == nil {
		req.FolderID = folderID
	}

	if isPublic := c.GetString("is_public"); isPublic != "" {
		if isPublic == "true" {
			isPublicBool := true
			req.IsPublic = &isPublicBool
		} else if isPublic == "false" {
			isPublicBool := false
			req.IsPublic = &isPublicBool
		}
	}

	if page, err := c.GetInt("page"); err == nil && page > 0 {
		req.Page = page
	} else {
		req.Page = 1
	}

	if pageSize, err := c.GetInt("page_size"); err == nil && pageSize > 0 {
		req.PageSize = pageSize
	} else {
		req.PageSize = 20
	}

	// 调用服务层获取收藏列表
	response, err := c.favoriteService.ListFavorites(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, response)
}

// Search 搜索收藏
// @router /api/v1/user/secured/favorites/search [get]
func (c *UserFavoriteController) Search() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取搜索参数
	keyword := c.GetString("keyword")
	if keyword == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请输入搜索关键词")
		return
	}

	page, _ := c.GetInt("page")
	if page <= 0 {
		page = 1
	}

	pageSize, _ := c.GetInt("page_size")
	if pageSize <= 0 {
		pageSize = 20
	}

	// 调用服务层搜索收藏
	response, err := c.favoriteService.SearchFavorites(userID, keyword, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, response)
}

// GetByType 根据类型获取收藏
// @router /api/v1/user/secured/favorites/type/:type [get]
func (c *UserFavoriteController) GetByType() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏类型
	favoriteType := c.Ctx.Input.Param(":type")
	if favoriteType == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请指定收藏类型")
		return
	}

	page, _ := c.GetInt("page")
	if page <= 0 {
		page = 1
	}

	pageSize, _ := c.GetInt("page_size")
	if pageSize <= 0 {
		pageSize = 20
	}

	// 调用服务层获取指定类型的收藏
	response, err := c.favoriteService.GetFavoritesByType(userID, favoriteType, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, response)
}

// CheckStatus 检查收藏状态
// @router /api/v1/user/secured/favorites/status [get]
func (c *UserFavoriteController) CheckStatus() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取参数
	favoriteType := c.GetString("type")
	targetID, err := c.GetInt64("target_id")
	if favoriteType == "" || targetID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数错误")
		return
	}

	// 调用服务层检查收藏状态
	status, err := c.favoriteService.CheckFavoriteStatus(userID, favoriteType, targetID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, status)
}

// Statistics 获取收藏统计
// @router /api/v1/user/secured/favorites/statistics [get]
func (c *UserFavoriteController) Statistics() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取统计信息
	statistics, err := c.favoriteService.GetFavoriteStatistics(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, statistics)
}

// Types 获取收藏类型列表
// @router /api/v1/user/secured/favorites/types [get]
func (c *UserFavoriteController) Types() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取类型列表
	types, err := c.favoriteService.GetFavoriteTypes(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, types)
}

// BatchMove 批量移动收藏
// @router /api/v1/user/secured/favorites/batch-move [post]
func (c *UserFavoriteController) BatchMove() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.BatchMoveRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层批量移动收藏
	err = c.favoriteService.BatchMoveFavorites(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "批量移动成功")
}

// Clear 清空收藏
// @router /api/v1/user/secured/favorites/clear [post]
func (c *UserFavoriteController) Clear() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取收藏类型
	favoriteType := c.GetString("type")

	// 调用服务层清空收藏
	err = c.favoriteService.ClearFavorites(userID, favoriteType)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "清空成功")
}

// Options 处理OPTIONS请求
func (c *UserFavoriteController) Options() {
	result.OK(c.Ctx, "OK")
}
