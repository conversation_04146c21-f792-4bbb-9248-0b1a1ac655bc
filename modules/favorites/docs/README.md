# 用户收藏模块

## 模块概述

用户收藏模块用于管理用户在系统中的收藏行为，包括收藏外卖商品、商城商品、商家、分类等。该模块提供完整的收藏管理功能，支持收藏夹分类、标签管理、统计分析等高级功能。

## 功能特性

### 核心功能
- **多类型收藏**：支持外卖商品、商城商品、商家、分类、套餐、优惠券等多种类型的收藏
- **收藏夹管理**：支持创建多个收藏夹，对收藏进行分类管理
- **智能去重**：自动检测重复收藏，避免重复添加相同项目
- **标签系统**：支持为收藏添加标签，便于分类和搜索
- **备注功能**：支持为收藏添加个人备注

### 高级功能
- **收藏夹分类**：创建多个收藏夹，按类别组织收藏
- **排序功能**：支持自定义排序，按时间、名称等排序
- **搜索功能**：支持按名称、标签、备注等搜索收藏
- **批量操作**：支持批量删除、移动收藏
- **公开分享**：支持将收藏夹设为公开，分享给其他用户

### 统计分析
- **基础统计**：总数、今日、本周、本月收藏量统计
- **类型分布**：各类型收藏的数量和占比分析
- **收藏夹统计**：各收藏夹的项目数量统计
- **热门分析**：系统热门收藏项目分析
- **趋势分析**：收藏行为的时间趋势

## 数据模型

### 主表：user_favorite
```sql
CREATE TABLE user_favorite (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type VARCHAR(50) NOT NULL,
    target_id BIGINT NOT NULL,
    target_name VARCHAR(255) NOT NULL,
    target_image VARCHAR(500),
    folder_id BIGINT,
    extra_data TEXT,
    tags VARCHAR(500),
    notes VARCHAR(1000),
    sort_order INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_type_target (user_id, type, target_id)
);
```

### 收藏夹表：favorite_folder
```sql
CREATE TABLE favorite_folder (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    icon VARCHAR(100) DEFAULT 'heart',
    color VARCHAR(20) DEFAULT '#ff6b6b',
    sort_order INT DEFAULT 0,
    is_default BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    item_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 收藏类型

| 类型 | 名称 | 描述 |
|------|------|------|
| takeout_food | 外卖商品 | 用户收藏的外卖商品 |
| mall_product | 商城商品 | 用户收藏的商城商品 |
| merchant | 商家 | 用户收藏的商家 |
| category | 分类 | 用户收藏的分类 |
| combo | 套餐 | 用户收藏的套餐 |
| coupon | 优惠券 | 用户收藏的优惠券 |

## API 接口

### 收藏操作

#### 添加收藏
```
POST /api/v1/user/secured/favorites/add
```

**请求参数：**
```json
{
  "type": "takeout_food",
  "target_id": 12345,
  "target_name": "麻辣香锅",
  "target_image": "https://example.com/image.jpg",
  "folder_id": 1,
  "extra_data": {
    "price": 28.5,
    "merchant_id": 100
  },
  "tags": ["川菜", "辣"],
  "notes": "很好吃的麻辣香锅",
  "is_public": false
}
```

#### 获取收藏列表
```
GET /api/v1/user/secured/favorites/list?type=takeout_food&folder_id=1&page=1&page_size=20
```

#### 搜索收藏
```
GET /api/v1/user/secured/favorites/search?keyword=麻辣&page=1&page_size=20
```

#### 检查收藏状态
```
GET /api/v1/user/secured/favorites/status?type=takeout_food&target_id=12345
```

#### 删除收藏
```
POST /api/v1/user/secured/favorites/delete/123
```

#### 批量删除收藏
```
POST /api/v1/user/secured/favorites/batch-delete
```

**请求参数：**
```json
{
  "ids": [123, 456, 789]
}
```

### 收藏夹操作

#### 创建收藏夹
```
POST /api/v1/user/secured/favorites/folders/create
```

**请求参数：**
```json
{
  "name": "美食收藏",
  "description": "收藏的美食商品",
  "icon": "food",
  "color": "#ffa500",
  "is_public": false
}
```

#### 获取收藏夹列表
```
GET /api/v1/user/secured/favorites/folders/list
```

#### 批量移动收藏
```
POST /api/v1/user/secured/favorites/batch-move
```

**请求参数：**
```json
{
  "ids": [123, 456, 789],
  "folder_id": 2
}
```

### 统计分析

#### 获取统计信息
```
GET /api/v1/user/secured/favorites/statistics
```

**响应示例：**
```json
{
  "total_count": 1250,
  "today_count": 15,
  "week_count": 89,
  "month_count": 324,
  "type_statistics": [
    {
      "type": "takeout_food",
      "type_name": "外卖商品",
      "count": 650,
      "percentage": 52.0
    }
  ],
  "folder_count": 5,
  "public_count": 10,
  "recent_favorites": [...],
  "popular_items": [...]
}
```

#### 获取收藏类型
```
GET /api/v1/user/secured/favorites/types
```

## 使用示例

### 前端集成示例

```javascript
// 收藏服务类
class FavoriteService {
  // 添加收藏
  async addFavorite(type, targetId, targetName, targetImage, options = {}) {
    const data = {
      type,
      target_id: targetId,
      target_name: targetName,
      target_image: targetImage,
      folder_id: options.folderId || 0,
      extra_data: options.extraData || {},
      tags: options.tags || [],
      notes: options.notes || '',
      is_public: options.isPublic || false
    };
    
    return await this.request('/api/v1/user/secured/favorites/add', 'POST', data);
  }
  
  // 检查收藏状态
  async checkFavoriteStatus(type, targetId) {
    const params = { type, target_id: targetId };
    return await this.request('/api/v1/user/secured/favorites/status', 'GET', params);
  }
  
  // 获取收藏列表
  async getFavoriteList(options = {}) {
    const params = {
      type: options.type || '',
      folder_id: options.folderId || 0,
      page: options.page || 1,
      page_size: options.pageSize || 20
    };
    return await this.request('/api/v1/user/secured/favorites/list', 'GET', params);
  }
  
  // 删除收藏
  async deleteFavorite(favoriteId) {
    return await this.request(`/api/v1/user/secured/favorites/delete/${favoriteId}`, 'POST');
  }
}

// 使用示例
const favoriteService = new FavoriteService();

// 用户点击收藏按钮
async function onFavoriteClick(product) {
  try {
    // 检查收藏状态
    const status = await favoriteService.checkFavoriteStatus('takeout_food', product.id);
    
    if (status.data.is_favorited) {
      // 已收藏，执行取消收藏
      await favoriteService.deleteFavorite(status.data.favorite_id);
      showMessage('取消收藏成功');
    } else {
      // 未收藏，执行添加收藏
      await favoriteService.addFavorite(
        'takeout_food',
        product.id,
        product.name,
        product.image,
        {
          extraData: {
            price: product.price,
            merchant_id: product.merchant_id
          },
          tags: ['美食', product.category]
        }
      );
      showMessage('收藏成功');
    }
    
    // 更新UI状态
    updateFavoriteButton(product.id);
  } catch (error) {
    showMessage('操作失败：' + error.message);
  }
}
```

### 后端集成示例

```go
// 在商品详情接口中添加收藏状态
func (c *ProductController) Detail() {
    userID, _ := auth.GetUserIDFromContext(c.Ctx)
    productID, _ := c.GetInt64(":id")
    
    // 获取商品详情
    product, err := c.productService.GetProductDetail(productID)
    if err != nil {
        result.HandleError(c.Ctx, err)
        return
    }
    
    // 如果用户已登录，获取收藏状态
    if userID > 0 {
        favoriteService := services.NewUserFavoriteService()
        status, err := favoriteService.CheckFavoriteStatus(userID, "takeout_food", productID)
        if err == nil {
            product.IsFavorited = status.IsFavorited
            product.FavoriteID = status.FavoriteID
        }
    }
    
    result.OK(c.Ctx, product)
}
```

## 配置说明

模块配置文件位于 `conf/modules/favorites.conf`，主要配置项：

- `favorites.retention_days`：收藏保留天数
- `favorites.cleanup.enabled`：是否启用自动清理
- `favorites.cache.enabled`：是否启用缓存
- `favorites.folder.max_count`：每个用户最大收藏夹数量
- `favorites.security.max_favorites_per_user`：每个用户最大收藏数

## 性能优化

1. **数据库索引**：在 user_id、type、target_id 等字段上建立索引
2. **缓存策略**：对统计数据和热门项目进行缓存
3. **批量操作**：支持批量删除、移动等操作
4. **分页查询**：所有列表查询都支持分页
5. **触发器优化**：使用数据库触发器自动维护收藏夹计数

## 注意事项

1. **隐私保护**：用户可以设置收藏的公开性
2. **数据安全**：收藏只能被用户本人操作
3. **性能考虑**：大量收藏可能影响查询性能，建议定期清理
4. **存储空间**：额外数据和标签会增加存储空间使用

## 扩展功能

1. **推荐系统**：基于收藏行为进行个性化推荐
2. **社交功能**：收藏夹分享、关注其他用户的公开收藏夹
3. **数据分析**：为运营提供用户偏好分析数据
4. **同步功能**：跨设备收藏同步

## 部署说明

1. **数据库迁移**：执行SQL脚本创建相关表
2. **模块注册**：在主程序中注册收藏模块
3. **配置文件**：根据需要调整配置参数
4. **定时任务**：配置收藏清理的定时任务
