# 用户收藏 API 文档

## 接口概览

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 添加收藏 | POST | /api/v1/user/secured/favorites/add | 添加用户收藏 |
| 更新收藏 | POST | /api/v1/user/secured/favorites/update/:id | 更新收藏信息 |
| 删除收藏 | POST | /api/v1/user/secured/favorites/delete/:id | 删除单条收藏 |
| 批量删除 | POST | /api/v1/user/secured/favorites/batch-delete | 批量删除收藏 |
| 获取详情 | GET | /api/v1/user/secured/favorites/detail/:id | 获取收藏详情 |
| 获取列表 | GET | /api/v1/user/secured/favorites/list | 获取收藏列表 |
| 搜索收藏 | GET | /api/v1/user/secured/favorites/search | 搜索收藏 |
| 按类型获取 | GET | /api/v1/user/secured/favorites/type/:type | 按类型获取收藏 |
| 检查状态 | GET | /api/v1/user/secured/favorites/status | 检查收藏状态 |
| 获取统计 | GET | /api/v1/user/secured/favorites/statistics | 获取统计信息 |
| 获取类型 | GET | /api/v1/user/secured/favorites/types | 获取收藏类型 |
| 批量移动 | POST | /api/v1/user/secured/favorites/batch-move | 批量移动收藏 |
| 清空收藏 | POST | /api/v1/user/secured/favorites/clear | 清空收藏 |

## 收藏夹接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建收藏夹 | POST | /api/v1/user/secured/favorites/folders/create | 创建收藏夹 |
| 更新收藏夹 | POST | /api/v1/user/secured/favorites/folders/update/:id | 更新收藏夹 |
| 删除收藏夹 | POST | /api/v1/user/secured/favorites/folders/delete/:id | 删除收藏夹 |
| 收藏夹详情 | GET | /api/v1/user/secured/favorites/folders/detail/:id | 获取收藏夹详情 |
| 收藏夹列表 | GET | /api/v1/user/secured/favorites/folders/list | 获取收藏夹列表 |

## 详细接口说明

### 1. 添加收藏

**接口地址：** `POST /api/v1/user/secured/favorites/add`

**接口描述：** 添加用户收藏，如果已收藏则返回错误。

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "type": "takeout_food",                    // 必填，收藏类型
  "target_id": 12345,                        // 必填，目标对象ID
  "target_name": "麻辣香锅",                  // 必填，目标对象名称
  "target_image": "https://example.com/image.jpg", // 可选，目标对象图片
  "folder_id": 1,                            // 可选，收藏夹ID
  "extra_data": {                            // 可选，额外数据
    "price": 28.5,
    "merchant_id": 100,
    "category_id": 5
  },
  "tags": ["川菜", "辣"],                     // 可选，标签列表
  "notes": "很好吃的麻辣香锅",                // 可选，备注
  "is_public": false                         // 可选，是否公开
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 123,
    "user_id": 1001,
    "type": "takeout_food",
    "type_name": "外卖商品",
    "target_id": 12345,
    "target_name": "麻辣香锅",
    "target_image": "https://example.com/image.jpg",
    "folder_id": 1,
    "folder_name": "默认收藏夹",
    "extra_data": {
      "price": 28.5,
      "merchant_id": 100
    },
    "tags": ["川菜", "辣"],
    "notes": "很好吃的麻辣香锅",
    "sort_order": 0,
    "is_public": false,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 2. 检查收藏状态

**接口地址：** `GET /api/v1/user/secured/favorites/status`

**接口描述：** 检查指定项目的收藏状态。

**请求参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| type | string | 是 | 收藏类型 |
| target_id | int64 | 是 | 目标对象ID |

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "is_favorited": true,
    "favorite_id": 123,
    "folder_id": 1,
    "folder_name": "默认收藏夹",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### 3. 获取收藏列表

**接口地址：** `GET /api/v1/user/secured/favorites/list`

**接口描述：** 获取用户收藏列表，支持多种筛选条件。

**请求参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| type | string | 否 | 收藏类型 |
| folder_id | int64 | 否 | 收藏夹ID |
| tags | string | 否 | 标签，逗号分隔 |
| keyword | string | 否 | 搜索关键词 |
| is_public | bool | 否 | 是否公开 |
| start_date | string | 否 | 开始日期 YYYY-MM-DD |
| end_date | string | 否 | 结束日期 YYYY-MM-DD |
| sort_by | string | 否 | 排序字段 |
| sort_order | string | 否 | 排序方向 |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 150,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 123,
        "user_id": 1001,
        "type": "takeout_food",
        "type_name": "外卖商品",
        "target_id": 12345,
        "target_name": "麻辣香锅",
        "target_image": "https://example.com/image.jpg",
        "folder_id": 1,
        "folder_name": "默认收藏夹",
        "extra_data": {
          "price": 28.5,
          "merchant_id": 100
        },
        "tags": ["川菜", "辣"],
        "notes": "很好吃的麻辣香锅",
        "sort_order": 0,
        "is_public": false,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 4. 获取统计信息

**接口地址：** `GET /api/v1/user/secured/favorites/statistics`

**接口描述：** 获取用户收藏的统计信息。

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_count": 1250,
    "today_count": 15,
    "week_count": 89,
    "month_count": 324,
    "type_statistics": [
      {
        "type": "takeout_food",
        "type_name": "外卖商品",
        "count": 650,
        "percentage": 52.0
      },
      {
        "type": "mall_product",
        "type_name": "商城商品",
        "count": 400,
        "percentage": 32.0
      }
    ],
    "folder_count": 5,
    "public_count": 10,
    "recent_favorites": [
      {
        "id": 123,
        "type": "takeout_food",
        "type_name": "外卖商品",
        "target_name": "麻辣香锅",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "popular_items": [
      {
        "type": "takeout_food",
        "type_name": "外卖商品",
        "target_id": 12345,
        "target_name": "麻辣香锅",
        "target_image": "https://example.com/image.jpg",
        "favorite_count": 15,
        "last_favorite_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 5. 创建收藏夹

**接口地址：** `POST /api/v1/user/secured/favorites/folders/create`

**接口描述：** 创建新的收藏夹。

**请求参数：**
```json
{
  "name": "美食收藏",                         // 必填，收藏夹名称
  "description": "收藏的美食商品",             // 可选，收藏夹描述
  "icon": "food",                            // 可选，收藏夹图标
  "color": "#ffa500",                        // 可选，收藏夹颜色
  "is_public": false                         // 可选，是否公开
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2,
    "user_id": 1001,
    "name": "美食收藏",
    "description": "收藏的美食商品",
    "icon": "food",
    "color": "#ffa500",
    "sort_order": 0,
    "is_default": false,
    "is_public": false,
    "item_count": 0,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 6. 批量移动收藏

**接口地址：** `POST /api/v1/user/secured/favorites/batch-move`

**接口描述：** 批量移动收藏到指定收藏夹。

**请求参数：**
```json
{
  "ids": [123, 456, 789],                    // 必填，要移动的收藏ID列表
  "folder_id": 2                             // 必填，目标收藏夹ID
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "批量移动成功",
  "data": "批量移动成功"
}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如重复收藏） |
| 500 | 服务器内部错误 |

## 收藏类型

| 类型值 | 类型名称 | 描述 |
|--------|----------|------|
| takeout_food | 外卖商品 | 用户收藏的外卖商品 |
| mall_product | 商城商品 | 用户收藏的商城商品 |
| merchant | 商家 | 用户收藏的商家 |
| category | 分类 | 用户收藏的分类 |
| combo | 套餐 | 用户收藏的套餐 |
| coupon | 优惠券 | 用户收藏的优惠券 |

## 使用注意事项

1. **认证要求**：所有接口都需要用户登录，需要在请求头中携带有效的JWT token
2. **权限控制**：用户只能访问和操作自己的收藏
3. **数据隐私**：收藏属于用户隐私数据，需要严格保护
4. **重复检查**：系统会自动检查重复收藏，避免重复添加
5. **收藏夹限制**：每个用户的收藏夹数量有限制
6. **性能考虑**：大量收藏查询可能影响性能，建议使用分页
