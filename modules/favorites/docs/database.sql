-- 用户收藏模块数据库表结构

-- 收藏夹表
CREATE TABLE IF NOT EXISTS `favorite_folder` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '收藏夹名称',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '收藏夹描述',
  `icon` VARCHAR(100) DEFAULT 'heart' COMMENT '收藏夹图标',
  `color` VARCHAR(20) DEFAULT '#ff6b6b' COMMENT '收藏夹颜色',
  `sort_order` INT(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_default` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否默认收藏夹',
  `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
  `item_count` INT(11) NOT NULL DEFAULT 0 COMMENT '收藏项数量',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏夹表';

-- 用户收藏主表
CREATE TABLE IF NOT EXISTS `user_favorite` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `type` VARCHAR(50) NOT NULL COMMENT '收藏类型',
  `target_id` BIGINT(20) NOT NULL COMMENT '目标对象ID',
  `target_name` VARCHAR(255) NOT NULL COMMENT '目标对象名称',
  `target_image` VARCHAR(500) DEFAULT NULL COMMENT '目标对象图片',
  `folder_id` BIGINT(20) DEFAULT NULL COMMENT '收藏夹ID',
  `extra_data` TEXT COMMENT '额外数据，JSON格式',
  `tags` VARCHAR(500) DEFAULT NULL COMMENT '标签，逗号分隔',
  `notes` VARCHAR(1000) DEFAULT NULL COMMENT '备注',
  `sort_order` INT(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_type_target` (`user_id`, `type`, `target_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_target_id` (`target_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_user_favorite_folder` FOREIGN KEY (`folder_id`) REFERENCES `favorite_folder` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏主表';

-- 插入收藏类型配置数据（可选）
INSERT IGNORE INTO `system_config` (`key`, `value`, `description`, `type`, `created_at`, `updated_at`) VALUES
('favorites.retention_days', '365', '收藏保留天数', 'number', NOW(), NOW()),
('favorites.cleanup.enabled', 'true', '是否启用自动清理', 'boolean', NOW(), NOW()),
('favorites.cache.enabled', 'true', '是否启用缓存', 'boolean', NOW(), NOW()),
('favorites.page.default_size', '20', '默认分页大小', 'number', NOW(), NOW()),
('favorites.statistics.enabled', 'true', '是否启用统计功能', 'boolean', NOW(), NOW()),
('favorites.folder.max_count', '50', '每个用户最大收藏夹数量', 'number', NOW(), NOW()),
('favorites.security.max_favorites_per_user', '10000', '每个用户最大收藏数', 'number', NOW(), NOW());

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_user_favorite_user_type` ON `user_favorite` (`user_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_user_favorite_user_folder` ON `user_favorite` (`user_id`, `folder_id`);
CREATE INDEX IF NOT EXISTS `idx_user_favorite_user_created` ON `user_favorite` (`user_id`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_user_favorite_tags` ON `user_favorite` (`tags`);

-- 创建全文索引用于搜索（MySQL 5.7+）
-- ALTER TABLE `user_favorite` ADD FULLTEXT(`target_name`, `notes`, `tags`);

-- 示例数据（开发环境可选）
-- INSERT INTO `favorite_folder` (`user_id`, `name`, `description`, `icon`, `color`, `is_default`) VALUES
-- (1, '默认收藏夹', '系统自动创建的默认收藏夹', 'heart', '#ff6b6b', 1),
-- (1, '美食收藏', '收藏的美食商品', 'food', '#ffa500', 0),
-- (1, '购物清单', '要购买的商品', 'shopping', '#32cd32', 0);

-- INSERT INTO `user_favorite` (`user_id`, `type`, `target_id`, `target_name`, `target_image`, `folder_id`, `tags`, `notes`) VALUES
-- (1, 'takeout_food', 1001, '麻辣香锅', 'https://example.com/food1.jpg', 1, '川菜,辣', '很好吃的麻辣香锅'),
-- (1, 'takeout_food', 1002, '宫保鸡丁', 'https://example.com/food2.jpg', 1, '川菜', '经典川菜'),
-- (1, 'mall_product', 2001, 'iPhone 15', 'https://example.com/phone1.jpg', 2, '手机,苹果', '想要购买的手机'),
-- (2, 'merchant', 3001, '川味小厨', 'https://example.com/merchant1.jpg', NULL, '川菜,餐厅', '不错的川菜餐厅');

-- 创建触发器，自动更新收藏夹项目数量
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS `update_folder_count_insert` 
AFTER INSERT ON `user_favorite`
FOR EACH ROW
BEGIN
    IF NEW.folder_id IS NOT NULL THEN
        UPDATE `favorite_folder` 
        SET `item_count` = `item_count` + 1, `updated_at` = NOW() 
        WHERE `id` = NEW.folder_id;
    END IF;
END$$

CREATE TRIGGER IF NOT EXISTS `update_folder_count_delete` 
AFTER DELETE ON `user_favorite`
FOR EACH ROW
BEGIN
    IF OLD.folder_id IS NOT NULL THEN
        UPDATE `favorite_folder` 
        SET `item_count` = `item_count` - 1, `updated_at` = NOW() 
        WHERE `id` = OLD.folder_id AND `item_count` > 0;
    END IF;
END$$

CREATE TRIGGER IF NOT EXISTS `update_folder_count_update` 
AFTER UPDATE ON `user_favorite`
FOR EACH ROW
BEGIN
    -- 如果收藏夹发生变化
    IF OLD.folder_id != NEW.folder_id THEN
        -- 减少旧收藏夹的计数
        IF OLD.folder_id IS NOT NULL THEN
            UPDATE `favorite_folder` 
            SET `item_count` = `item_count` - 1, `updated_at` = NOW() 
            WHERE `id` = OLD.folder_id AND `item_count` > 0;
        END IF;
        
        -- 增加新收藏夹的计数
        IF NEW.folder_id IS NOT NULL THEN
            UPDATE `favorite_folder` 
            SET `item_count` = `item_count` + 1, `updated_at` = NOW() 
            WHERE `id` = NEW.folder_id;
        END IF;
    END IF;
END$$
DELIMITER ;

-- 创建定时清理任务的存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `CleanupOldFavorites`(IN retention_days INT)
BEGIN
    DECLARE cutoff_date DATETIME;
    SET cutoff_date = DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 删除过期的收藏记录
    DELETE FROM `user_favorite` WHERE `created_at` < cutoff_date;
    
    -- 返回清理结果
    SELECT ROW_COUNT() as deleted_records, cutoff_date as cutoff_date;
END$$
DELIMITER ;

-- 创建获取用户收藏统计的存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `GetUserFavoriteStatistics`(IN user_id BIGINT)
BEGIN
    -- 总数统计
    SELECT 
        COUNT(*) as total_count,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_count,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_count,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_count
    FROM user_favorite 
    WHERE user_id = user_id;
    
    -- 类型统计
    SELECT 
        type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_favorite WHERE user_id = user_id), 2) as percentage
    FROM user_favorite 
    WHERE user_id = user_id
    GROUP BY type
    ORDER BY count DESC;
    
    -- 收藏夹统计
    SELECT 
        f.id,
        f.name,
        f.item_count,
        f.is_default,
        f.is_public
    FROM favorite_folder f
    WHERE f.user_id = user_id
    ORDER BY f.sort_order, f.created_at;
END$$
DELIMITER ;

-- 创建获取热门收藏的存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `GetPopularFavorites`(IN favorite_type VARCHAR(50), IN limit_count INT)
BEGIN
    SET @sql = 'SELECT 
        type,
        target_id,
        target_name,
        target_image,
        COUNT(*) as favorite_count,
        MAX(created_at) as last_favorite_at
    FROM user_favorite 
    WHERE 1=1';
    
    IF favorite_type IS NOT NULL AND favorite_type != '' THEN
        SET @sql = CONCAT(@sql, ' AND type = ''', favorite_type, '''');
    END IF;
    
    SET @sql = CONCAT(@sql, ' GROUP BY type, target_id ORDER BY favorite_count DESC LIMIT ', limit_count);
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

-- 创建视图，方便查询用户收藏统计
CREATE OR REPLACE VIEW `v_user_favorite_stats` AS
SELECT 
    user_id,
    type,
    COUNT(*) as favorite_count,
    MAX(created_at) as last_favorite_at,
    MIN(created_at) as first_favorite_at,
    COUNT(CASE WHEN is_public = 1 THEN 1 END) as public_count
FROM user_favorite
GROUP BY user_id, type;

-- 创建视图，方便查询收藏夹统计
CREATE OR REPLACE VIEW `v_folder_stats` AS
SELECT 
    f.id,
    f.user_id,
    f.name,
    f.item_count,
    f.is_default,
    f.is_public,
    COUNT(uf.id) as actual_count,
    f.created_at,
    f.updated_at
FROM favorite_folder f
LEFT JOIN user_favorite uf ON f.id = uf.folder_id
GROUP BY f.id;

-- 权限设置（根据实际需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON user_favorite TO 'app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON favorite_folder TO 'app_user'@'%';
-- GRANT SELECT ON v_user_favorite_stats TO 'app_user'@'%';
-- GRANT SELECT ON v_folder_stats TO 'app_user'@'%';
-- GRANT EXECUTE ON PROCEDURE CleanupOldFavorites TO 'app_user'@'%';
-- GRANT EXECUTE ON PROCEDURE GetUserFavoriteStatistics TO 'app_user'@'%';
-- GRANT EXECUTE ON PROCEDURE GetPopularFavorites TO 'app_user'@'%';
