# 用户收藏模块部署指南

## 部署步骤

### 1. 数据库准备

执行数据库迁移脚本创建相关表：

```sql
-- 执行 modules/favorites/docs/database.sql 中的SQL语句
source modules/favorites/docs/database.sql
```

### 2. 配置文件

确保 `conf/modules/favorites.conf` 配置文件存在并配置正确：

```ini
# 收藏保留天数
favorites.retention_days = 365

# 收藏清理任务配置
favorites.cleanup.enabled = true
favorites.cleanup.cron = "0 3 * * *"

# 收藏缓存配置
favorites.cache.enabled = true
favorites.cache.ttl = 600

# 收藏夹配置
favorites.folder.max_count = 50
favorites.folder.default_name = "默认收藏夹"

# 收藏安全配置
favorites.security.max_favorites_per_user = 10000
favorites.security.rate_limit = 60
```

### 3. 模块注册

确保在 `routers/router.go` 中已经注册了收藏模块：

```go
import (
    // ... 其他导入
    "o_mall_backend/modules/favorites"
)

func Init() {
    // ... 其他模块初始化
    
    // 初始化收藏模块
    favorites.Init()
    
    // ...
}
```

### 4. API文档同步

在 `main.go` 中的模块列表中添加 "favorites"：

```go
modules := []string{"admin", "system", "merchant", "user", "order", "apidoc", "payment", "ui_config", "delivery", "history", "favorites"}
```

### 5. 编译和启动

```bash
# 编译项目
go build -o o_mall_backend

# 启动服务
./o_mall_backend
```

## 验证部署

### 1. 检查数据库表

确认以下表已创建：
- `user_favorite` - 用户收藏主表
- `favorite_folder` - 收藏夹表

### 2. 测试API接口

```bash
# 测试添加收藏
curl -X POST "http://localhost:8080/api/v1/user/secured/favorites/add" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "takeout_food",
    "target_id": 12345,
    "target_name": "测试商品",
    "target_image": "https://example.com/image.jpg"
  }'

# 测试获取收藏列表
curl -X GET "http://localhost:8080/api/v1/user/secured/favorites/list" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 测试检查收藏状态
curl -X GET "http://localhost:8080/api/v1/user/secured/favorites/status?type=takeout_food&target_id=12345" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 测试获取统计信息
curl -X GET "http://localhost:8080/api/v1/user/secured/favorites/statistics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 检查日志

查看应用日志确认模块正常启动：

```bash
tail -f logs/o_mall.log | grep -i favorite
```

应该看到类似以下的日志：
```
[INFO] 初始化用户收藏模块...
[INFO] 注册用户收藏模型...
[INFO] 用户收藏模型注册完成
[INFO] 用户收藏模块初始化完成
```

## 常见问题

### 1. 数据库连接错误

**问题**: 启动时报数据库连接错误
**解决**: 检查数据库配置和连接参数

### 2. 表不存在错误

**问题**: API调用时报表不存在错误
**解决**: 确认已执行数据库迁移脚本

### 3. 权限错误

**问题**: API调用返回401未授权错误
**解决**: 确认JWT token有效，检查认证中间件配置

### 4. 路由不存在错误

**问题**: API调用返回404错误
**解决**: 确认模块已正确注册，检查路由配置

### 5. 重复收藏错误

**问题**: 添加收藏时提示已收藏
**解决**: 这是正常的业务逻辑，可以先检查收藏状态再决定是否添加

## 性能优化

### 1. 数据库索引

确保已创建必要的索引：

```sql
-- 用户ID和类型的复合索引
CREATE INDEX idx_user_favorite_user_type ON user_favorite (user_id, type);

-- 用户ID和收藏夹ID的复合索引
CREATE INDEX idx_user_favorite_user_folder ON user_favorite (user_id, folder_id);

-- 用户ID和创建时间的复合索引
CREATE INDEX idx_user_favorite_user_created ON user_favorite (user_id, created_at);
```

### 2. 缓存配置

启用Redis缓存以提高查询性能：

```ini
# 在 conf/app.conf 中配置Redis
redis.host = localhost
redis.port = 6379
redis.password = 
redis.db = 0

# 在 conf/modules/favorites.conf 中启用缓存
favorites.cache.enabled = true
favorites.cache.ttl = 600
```

### 3. 定时清理

配置定时任务清理过期数据：

```ini
favorites.cleanup.enabled = true
favorites.cleanup.cron = "0 3 * * *"  # 每天凌晨3点执行
favorites.retention_days = 365        # 保留365天的数据
```

## 监控和维护

### 1. 监控指标

- 收藏总数
- 每日新增收藏数
- API响应时间
- 数据库查询性能
- 收藏夹使用情况

### 2. 日志监控

关键日志关键词：
- "收藏" - 一般操作日志
- "ERROR" - 错误日志
- "WARN" - 警告日志

### 3. 定期维护

- 定期检查数据库表大小
- 监控缓存命中率
- 清理过期数据
- 备份重要数据
- 检查收藏夹计数准确性

## 扩展功能

### 1. 推荐系统

可以基于收藏数据进行：
- 协同过滤推荐
- 基于内容的推荐
- 热门商品推荐

### 2. 社交功能

支持社交功能：
- 公开收藏夹分享
- 关注其他用户的收藏夹
- 收藏夹评论和点赞

### 3. 数据分析

支持数据分析：
- 用户偏好分析
- 商品热度分析
- 收藏行为分析

### 4. 同步功能

支持跨设备同步：
- 收藏数据云同步
- 多设备状态一致性
- 离线收藏支持

## 安全考虑

### 1. 数据保护

- 用户收藏数据加密存储
- 敏感信息脱敏处理
- 定期数据备份

### 2. 访问控制

- 严格的用户权限控制
- API访问频率限制
- 恶意请求检测

### 3. 隐私保护

- 用户可控制收藏公开性
- 数据删除权保障
- 隐私设置灵活配置

这个部署指南提供了完整的部署流程和运维指导，确保用户收藏模块能够稳定运行并提供良好的用户体验。
