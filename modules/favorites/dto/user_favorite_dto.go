/**
 * 用户收藏DTO
 *
 * 本文件定义了用户收藏相关的数据传输对象，用于API接口的请求和响应。
 * 包含收藏的创建、查询、统计等相关的数据结构。
 */

package dto

import (
	"time"
)

// AddFavoriteRequest 添加收藏请求
type AddFavoriteRequest struct {
	Type        string                 `json:"type" valid:"Required" description:"收藏类型"`
	TargetID    int64                  `json:"target_id" valid:"Required" description:"目标对象ID"`
	TargetName  string                 `json:"target_name" valid:"Required;MaxSize(255)" description:"目标对象名称"`
	TargetImage string                 `json:"target_image" valid:"MaxSize(500)" description:"目标对象图片"`
	FolderID    int64                  `json:"folder_id" description:"收藏夹ID"`
	ExtraData   map[string]interface{} `json:"extra_data" description:"额外数据"`
	Tags        []string               `json:"tags" description:"标签列表"`
	Notes       string                 `json:"notes" valid:"MaxSize(1000)" description:"备注"`
	IsPublic    bool                   `json:"is_public" description:"是否公开"`
}

// UpdateFavoriteRequest 更新收藏请求
type UpdateFavoriteRequest struct {
	FolderID  int64    `json:"folder_id" description:"收藏夹ID"`
	Tags      []string `json:"tags" description:"标签列表"`
	Notes     string   `json:"notes" valid:"MaxSize(1000)" description:"备注"`
	SortOrder int      `json:"sort_order" description:"排序顺序"`
	IsPublic  bool     `json:"is_public" description:"是否公开"`
}

// FavoriteQueryRequest 收藏查询请求
type FavoriteQueryRequest struct {
	Type      string `json:"type" description:"收藏类型"`
	FolderID  int64  `json:"folder_id" description:"收藏夹ID"`
	Tags      string `json:"tags" description:"标签，逗号分隔"`
	Keyword   string `json:"keyword" description:"搜索关键词"`
	IsPublic  *bool  `json:"is_public" description:"是否公开"`
	StartDate string `json:"start_date" description:"开始日期 YYYY-MM-DD"`
	EndDate   string `json:"end_date" description:"结束日期 YYYY-MM-DD"`
	SortBy    string `json:"sort_by" description:"排序字段：created_at, updated_at, sort_order"`
	SortOrder string `json:"sort_order" description:"排序方向：asc, desc"`
	Page      int    `json:"page" description:"页码，默认1"`
	PageSize  int    `json:"page_size" description:"每页数量，默认20"`
}

// UserFavoriteDTO 用户收藏DTO
type UserFavoriteDTO struct {
	ID          int64                  `json:"id"`
	UserID      int64                  `json:"user_id"`
	Type        string                 `json:"type"`
	TypeName    string                 `json:"type_name"`
	TargetID    int64                  `json:"target_id"`
	TargetName  string                 `json:"target_name"`
	TargetImage string                 `json:"target_image"`
	FolderID    int64                  `json:"folder_id"`
	FolderName  string                 `json:"folder_name"`
	ExtraData   map[string]interface{} `json:"extra_data"`
	Tags        []string               `json:"tags"`
	Notes       string                 `json:"notes"`
	SortOrder   int                    `json:"sort_order"`
	IsPublic    bool                   `json:"is_public"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// FavoriteListResponse 收藏列表响应
type FavoriteListResponse struct {
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"page_size"`
	List     []UserFavoriteDTO  `json:"list"`
}

// CreateFolderRequest 创建收藏夹请求
type CreateFolderRequest struct {
	Name        string `json:"name" valid:"Required;MaxSize(100)" description:"收藏夹名称"`
	Description string `json:"description" valid:"MaxSize(500)" description:"收藏夹描述"`
	Icon        string `json:"icon" valid:"MaxSize(100)" description:"收藏夹图标"`
	Color       string `json:"color" valid:"MaxSize(20)" description:"收藏夹颜色"`
	IsPublic    bool   `json:"is_public" description:"是否公开"`
}

// UpdateFolderRequest 更新收藏夹请求
type UpdateFolderRequest struct {
	Name        string `json:"name" valid:"MaxSize(100)" description:"收藏夹名称"`
	Description string `json:"description" valid:"MaxSize(500)" description:"收藏夹描述"`
	Icon        string `json:"icon" valid:"MaxSize(100)" description:"收藏夹图标"`
	Color       string `json:"color" valid:"MaxSize(20)" description:"收藏夹颜色"`
	SortOrder   int    `json:"sort_order" description:"排序顺序"`
	IsPublic    bool   `json:"is_public" description:"是否公开"`
}

// FavoriteFolderDTO 收藏夹DTO
type FavoriteFolderDTO struct {
	ID          int64     `json:"id"`
	UserID      int64     `json:"user_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Icon        string    `json:"icon"`
	Color       string    `json:"color"`
	SortOrder   int       `json:"sort_order"`
	IsDefault   bool      `json:"is_default"`
	IsPublic    bool      `json:"is_public"`
	ItemCount   int       `json:"item_count"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FavoriteStatisticsDTO 收藏统计DTO
type FavoriteStatisticsDTO struct {
	TotalCount      int64                    `json:"total_count"`
	TodayCount      int64                    `json:"today_count"`
	WeekCount       int64                    `json:"week_count"`
	MonthCount      int64                    `json:"month_count"`
	TypeStatistics  []TypeStatisticsDTO      `json:"type_statistics"`
	FolderCount     int64                    `json:"folder_count"`
	PublicCount     int64                    `json:"public_count"`
	RecentFavorites []UserFavoriteDTO        `json:"recent_favorites"`
	PopularItems    []PopularFavoriteDTO     `json:"popular_items"`
}

// TypeStatisticsDTO 类型统计DTO
type TypeStatisticsDTO struct {
	Type       string  `json:"type"`
	TypeName   string  `json:"type_name"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}

// PopularFavoriteDTO 热门收藏DTO
type PopularFavoriteDTO struct {
	Type        string    `json:"type"`
	TypeName    string    `json:"type_name"`
	TargetID    int64     `json:"target_id"`
	TargetName  string    `json:"target_name"`
	TargetImage string    `json:"target_image"`
	FavoriteCount int     `json:"favorite_count"`
	LastFavoriteAt time.Time `json:"last_favorite_at"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	IDs []int64 `json:"ids" valid:"Required" description:"要删除的收藏ID列表"`
}

// BatchMoveRequest 批量移动请求
type BatchMoveRequest struct {
	IDs      []int64 `json:"ids" valid:"Required" description:"要移动的收藏ID列表"`
	FolderID int64   `json:"folder_id" description:"目标收藏夹ID"`
}

// FavoriteStatusRequest 收藏状态查询请求
type FavoriteStatusRequest struct {
	Type     string  `json:"type" valid:"Required" description:"收藏类型"`
	TargetID int64   `json:"target_id" valid:"Required" description:"目标对象ID"`
}

// FavoriteStatusResponse 收藏状态响应
type FavoriteStatusResponse struct {
	IsFavorited bool      `json:"is_favorited"`
	FavoriteID  int64     `json:"favorite_id,omitempty"`
	FolderID    int64     `json:"folder_id,omitempty"`
	FolderName  string    `json:"folder_name,omitempty"`
	CreatedAt   time.Time `json:"created_at,omitempty"`
}

// FavoriteTypeDTO 收藏类型DTO
type FavoriteTypeDTO struct {
	Type        string `json:"type"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Count       int64  `json:"count"`
}

// ExportFavoriteRequest 导出收藏请求
type ExportFavoriteRequest struct {
	Type     string `json:"type" description:"收藏类型"`
	FolderID int64  `json:"folder_id" description:"收藏夹ID"`
	Format   string `json:"format" description:"导出格式：csv, excel"`
}

// ImportFavoriteRequest 导入收藏请求
type ImportFavoriteRequest struct {
	FolderID int64  `json:"folder_id" description:"目标收藏夹ID"`
	Format   string `json:"format" description:"导入格式：csv, excel"`
}

// ShareFolderRequest 分享收藏夹请求
type ShareFolderRequest struct {
	FolderID   int64  `json:"folder_id" valid:"Required" description:"收藏夹ID"`
	ShareType  string `json:"share_type" description:"分享类型：link, qr"`
	ExpireTime int64  `json:"expire_time" description:"过期时间戳"`
}

// ShareFolderResponse 分享收藏夹响应
type ShareFolderResponse struct {
	ShareURL   string    `json:"share_url"`
	ShareCode  string    `json:"share_code"`
	QRCode     string    `json:"qr_code,omitempty"`
	ExpireTime time.Time `json:"expire_time"`
}
