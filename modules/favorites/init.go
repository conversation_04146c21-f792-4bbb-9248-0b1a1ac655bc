/**
 * 用户收藏模块初始化
 *
 * 本文件负责初始化用户收藏模块，注册数据库模型并提供模块初始化入口。
 * 该模块处理用户收藏功能，包括外卖商品、商城商品、商家等收藏记录。
 */

package favorites

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/favorites/models"
	"o_mall_backend/modules/favorites/routers"
)

// 初始化模块
func Init() {
	logs.Info("初始化用户收藏模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitFavoritesRouter()

	logs.Info("用户收藏模块初始化完成")
}

// 注册数据库模型
func registerModels() {
	logs.Info("注册用户收藏模型...")

	// 注册用户收藏相关模型
	orm.RegisterModel(
		new(models.UserFavorite),
		new(models.FavoriteFolder),
	)

	logs.Info("用户收藏模型注册完成")
}
