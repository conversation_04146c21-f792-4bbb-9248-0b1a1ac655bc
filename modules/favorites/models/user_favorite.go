/**
 * 用户收藏模型
 *
 * 本文件定义了用户收藏相关的数据模型，用于存储用户收藏的商品、商家等信息。
 * 支持外卖商品、商城商品、商家等多种类型的收藏。
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/core/logs"
)

// FavoriteType 收藏类型常量
const (
	FavoriteTypeTakeoutFood = "takeout_food" // 外卖商品
	FavoriteTypeMallProduct = "mall_product" // 商城商品
	FavoriteTypeMerchant    = "merchant"     // 商家
	FavoriteTypeCategory    = "category"     // 分类
	FavoriteTypeCombo       = "combo"        // 套餐
	FavoriteTypeCoupon      = "coupon"       // 优惠券
)

// UserFavorite 用户收藏主表
type UserFavorite struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`
	UserID      int64     `orm:"column(user_id);index" json:"user_id" description:"用户ID"`
	Type        string    `orm:"column(type);size(50);index" json:"type" description:"收藏类型"`
	TargetID    int64     `orm:"column(target_id);index" json:"target_id" description:"目标对象ID"`
	TargetName  string    `orm:"column(target_name);size(255)" json:"target_name" description:"目标对象名称"`
	TargetImage string    `orm:"column(target_image);size(500)" json:"target_image" description:"目标对象图片"`
	FolderID    int64     `orm:"column(folder_id);null" json:"folder_id" description:"收藏夹ID"`
	ExtraData   string    `orm:"column(extra_data);type(text)" json:"extra_data" description:"额外数据，JSON格式"`
	Tags        string    `orm:"column(tags);size(500)" json:"tags" description:"标签，逗号分隔"`
	Notes       string    `orm:"column(notes);size(1000)" json:"notes" description:"备注"`
	SortOrder   int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序顺序"`
	IsPublic    bool      `orm:"column(is_public);default(false)" json:"is_public" description:"是否公开"`
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`
}

// TableName 设置表名
func (u *UserFavorite) TableName() string {
	return "user_favorite"
}

// FavoriteFolder 收藏夹表
type FavoriteFolder struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`
	UserID      int64     `orm:"column(user_id);index" json:"user_id" description:"用户ID"`
	Name        string    `orm:"column(name);size(100)" json:"name" description:"收藏夹名称"`
	Description string    `orm:"column(description);size(500)" json:"description" description:"收藏夹描述"`
	Icon        string    `orm:"column(icon);size(100)" json:"icon" description:"收藏夹图标"`
	Color       string    `orm:"column(color);size(20)" json:"color" description:"收藏夹颜色"`
	SortOrder   int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序顺序"`
	IsDefault   bool      `orm:"column(is_default);default(false)" json:"is_default" description:"是否默认收藏夹"`
	IsPublic    bool      `orm:"column(is_public);default(false)" json:"is_public" description:"是否公开"`
	ItemCount   int       `orm:"column(item_count);default(0)" json:"item_count" description:"收藏项数量"`
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`
}

// TableName 设置表名
func (f *FavoriteFolder) TableName() string {
	return "favorite_folder"
}

// FavoriteTypeInfo 收藏类型信息
type FavoriteTypeInfo struct {
	Type        string `json:"type"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// GetFavoriteTypes 获取所有收藏类型
func GetFavoriteTypes() []FavoriteTypeInfo {
	return []FavoriteTypeInfo{
		{
			Type:        FavoriteTypeTakeoutFood,
			Name:        "外卖商品",
			Description: "用户收藏的外卖商品",
		},
		{
			Type:        FavoriteTypeMallProduct,
			Name:        "商城商品",
			Description: "用户收藏的商城商品",
		},
		{
			Type:        FavoriteTypeMerchant,
			Name:        "商家",
			Description: "用户收藏的商家",
		},
		{
			Type:        FavoriteTypeCategory,
			Name:        "分类",
			Description: "用户收藏的分类",
		},
		{
			Type:        FavoriteTypeCombo,
			Name:        "套餐",
			Description: "用户收藏的套餐",
		},
		{
			Type:        FavoriteTypeCoupon,
			Name:        "优惠券",
			Description: "用户收藏的优惠券",
		},
	}
}

// IsValidFavoriteType 验证收藏类型是否有效
func IsValidFavoriteType(favoriteType string) bool {
	validTypes := []string{
		FavoriteTypeTakeoutFood,
		FavoriteTypeMallProduct,
		FavoriteTypeMerchant,
		FavoriteTypeCategory,
		FavoriteTypeCombo,
		FavoriteTypeCoupon,
	}

	// 添加调试信息
	logs.Info("[Favorites] 验证收藏类型: '%s'", favoriteType)
	logs.Info("[Favorites] 有效类型列表: %v", validTypes)

	for i, validType := range validTypes {
		logs.Info("[Favorites] 比较 [%d]: '%s' == '%s' ? %t", i, favoriteType, validType, favoriteType == validType)
		if favoriteType == validType {
			logs.Info("[Favorites] 类型验证通过")
			return true
		}
	}

	logs.Error("[Favorites] 类型验证失败，无效类型: '%s'", favoriteType)
	return false
}

// FavoriteStatus 收藏状态
type FavoriteStatus struct {
	IsFavorited bool      `json:"is_favorited"`
	FavoriteID  int64     `json:"favorite_id,omitempty"`
	FolderID    int64     `json:"folder_id,omitempty"`
	CreatedAt   time.Time `json:"created_at,omitempty"`
}

// FavoriteStatistics 收藏统计
type FavoriteStatistics struct {
	TotalCount   int64                   `json:"total_count"`
	TypeCount    map[string]int64        `json:"type_count"`
	FolderCount  int64                   `json:"folder_count"`
	PublicCount  int64                   `json:"public_count"`
	RecentCount  int64                   `json:"recent_count"`
	PopularTypes []FavoriteTypeStatistic `json:"popular_types"`
}

// FavoriteTypeStatistic 收藏类型统计
type FavoriteTypeStatistic struct {
	Type       string  `json:"type"`
	TypeName   string  `json:"type_name"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}
