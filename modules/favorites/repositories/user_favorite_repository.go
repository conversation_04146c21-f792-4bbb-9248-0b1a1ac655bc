/**
 * 用户收藏仓库
 *
 * 本文件实现了用户收藏相关的数据库操作，提供数据持久化和查询功能。
 * 负责user_favorite和favorite_folder表的CRUD操作。
 */

package repositories

import (
	"errors"
	"time"

	"o_mall_backend/modules/favorites/models"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// UserFavoriteRepository 用户收藏仓库接口
type UserFavoriteRepository interface {
	// 收藏基础操作
	CreateFavorite(favorite *models.UserFavorite) (int64, error)
	GetFavoriteByID(id int64) (*models.UserFavorite, error)
	UpdateFavorite(favorite *models.UserFavorite) error
	DeleteFavorite(id int64, userID int64) error
	BatchDeleteFavorites(ids []int64, userID int64) error

	// 收藏查询操作
	GetUserFavorite(userID int64, favoriteType string, targetID int64) (*models.UserFavorite, error)
	ListUserFavorites(userID int64, favoriteType string, folderID int64, offset, limit int) ([]*models.UserFavorite, int64, error)
	SearchUserFavorites(userID int64, keyword string, offset, limit int) ([]*models.UserFavorite, int64, error)
	GetFavoritesByDateRange(userID int64, favoriteType string, startDate, endDate time.Time, offset, limit int) ([]*models.UserFavorite, int64, error)
	GetFavoritesByTags(userID int64, tags []string, offset, limit int) ([]*models.UserFavorite, int64, error)

	// 收藏统计操作
	CountUserFavorites(userID int64, favoriteType string) (int64, error)
	CountUserFavoritesByDateRange(userID int64, favoriteType string, startDate, endDate time.Time) (int64, error)
	GetTypeStatistics(userID int64) (map[string]int64, error)
	GetPopularFavorites(favoriteType string, limit int) ([]*models.UserFavorite, error)

	// 收藏夹操作
	CreateFolder(folder *models.FavoriteFolder) (int64, error)
	GetFolderByID(id int64) (*models.FavoriteFolder, error)
	UpdateFolder(folder *models.FavoriteFolder) error
	DeleteFolder(id int64, userID int64) error
	ListUserFolders(userID int64) ([]*models.FavoriteFolder, error)
	GetDefaultFolder(userID int64) (*models.FavoriteFolder, error)
	UpdateFolderItemCount(folderID int64) error

	// 批量操作
	BatchMoveFavorites(ids []int64, folderID int64, userID int64) error
	BatchUpdateFavorites(ids []int64, updates map[string]interface{}, userID int64) error

	// 清理操作
	ClearUserFavorites(userID int64, favoriteType string) error
	CleanupExpiredFavorites(days int) error
}

// userFavoriteRepository 用户收藏仓库实现
type userFavoriteRepository struct {
	ormer orm.Ormer
}

// NewUserFavoriteRepository 创建用户收藏仓库实例
func NewUserFavoriteRepository() UserFavoriteRepository {
	return &userFavoriteRepository{
		ormer: orm.NewOrm(),
	}
}

// CreateFavorite 创建收藏
func (r *userFavoriteRepository) CreateFavorite(favorite *models.UserFavorite) (int64, error) {
	favorite.CreatedAt = time.Now()
	favorite.UpdatedAt = time.Now()
	return r.ormer.Insert(favorite)
}

// GetFavoriteByID 根据ID获取收藏
func (r *userFavoriteRepository) GetFavoriteByID(id int64) (*models.UserFavorite, error) {
	favorite := &models.UserFavorite{ID: id}
	err := r.ormer.Read(favorite)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("收藏记录不存在")
		}
		return nil, err
	}
	return favorite, nil
}

// UpdateFavorite 更新收藏
func (r *userFavoriteRepository) UpdateFavorite(favorite *models.UserFavorite) error {
	favorite.UpdatedAt = time.Now()
	_, err := r.ormer.Update(favorite)
	return err
}

// DeleteFavorite 删除收藏
func (r *userFavoriteRepository) DeleteFavorite(id int64, userID int64) error {
	favorite := &models.UserFavorite{ID: id, UserID: userID}
	_, err := r.ormer.Delete(favorite, "ID", "UserID")
	return err
}

// BatchDeleteFavorites 批量删除收藏
func (r *userFavoriteRepository) BatchDeleteFavorites(ids []int64, userID int64) error {
	if len(ids) == 0 {
		return nil
	}

	_, err := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("id__in", ids).
		Filter("user_id", userID).
		Delete()
	return err
}

// GetUserFavorite 获取用户特定类型和目标的收藏
func (r *userFavoriteRepository) GetUserFavorite(userID int64, favoriteType string, targetID int64) (*models.UserFavorite, error) {
	favorite := &models.UserFavorite{}
	err := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID).
		Filter("type", favoriteType).
		Filter("target_id", targetID).
		One(favorite)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return favorite, nil
}

// ListUserFavorites 获取用户收藏列表
func (r *userFavoriteRepository) ListUserFavorites(userID int64, favoriteType string, folderID int64, offset, limit int) ([]*models.UserFavorite, int64, error) {
	var favorites []*models.UserFavorite

	qs := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID)

	if favoriteType != "" {
		qs = qs.Filter("type", favoriteType)
	}

	if folderID > 0 {
		qs = qs.Filter("folder_id", folderID)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	_, err = qs.OrderBy("-created_at").
		Offset(offset).
		Limit(limit).
		All(&favorites)

	return favorites, total, err
}

// SearchUserFavorites 搜索用户收藏
func (r *userFavoriteRepository) SearchUserFavorites(userID int64, keyword string, offset, limit int) ([]*models.UserFavorite, int64, error) {
	var favorites []*models.UserFavorite

	qs := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID).
		Filter("target_name__icontains", keyword)

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	_, err = qs.OrderBy("-created_at").
		Offset(offset).
		Limit(limit).
		All(&favorites)

	return favorites, total, err
}

// GetFavoritesByDateRange 根据日期范围获取收藏
func (r *userFavoriteRepository) GetFavoritesByDateRange(userID int64, favoriteType string, startDate, endDate time.Time, offset, limit int) ([]*models.UserFavorite, int64, error) {
	var favorites []*models.UserFavorite

	qs := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID).
		Filter("created_at__gte", startDate).
		Filter("created_at__lte", endDate)

	if favoriteType != "" {
		qs = qs.Filter("type", favoriteType)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	_, err = qs.OrderBy("-created_at").
		Offset(offset).
		Limit(limit).
		All(&favorites)

	return favorites, total, err
}

// GetFavoritesByTags 根据标签获取收藏
func (r *userFavoriteRepository) GetFavoritesByTags(userID int64, tags []string, offset, limit int) ([]*models.UserFavorite, int64, error) {
	var favorites []*models.UserFavorite

	qs := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID)

	// 构建标签查询条件
	for _, tag := range tags {
		qs = qs.Filter("tags__icontains", tag)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	_, err = qs.OrderBy("-created_at").
		Offset(offset).
		Limit(limit).
		All(&favorites)

	return favorites, total, err
}

// CountUserFavorites 统计用户收藏数量
func (r *userFavoriteRepository) CountUserFavorites(userID int64, favoriteType string) (int64, error) {
	qs := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID)

	if favoriteType != "" {
		qs = qs.Filter("type", favoriteType)
	}

	return qs.Count()
}

// CountUserFavoritesByDateRange 根据日期范围统计收藏数量
func (r *userFavoriteRepository) CountUserFavoritesByDateRange(userID int64, favoriteType string, startDate, endDate time.Time) (int64, error) {
	qs := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID).
		Filter("created_at__gte", startDate).
		Filter("created_at__lte", endDate)

	if favoriteType != "" {
		qs = qs.Filter("type", favoriteType)
	}

	return qs.Count()
}

// GetTypeStatistics 获取类型统计
func (r *userFavoriteRepository) GetTypeStatistics(userID int64) (map[string]int64, error) {
	var results []orm.Params
	_, err := r.ormer.Raw("SELECT type, COUNT(*) as count FROM user_favorite WHERE user_id = ? GROUP BY type", userID).Values(&results)
	if err != nil {
		return nil, err
	}

	statistics := make(map[string]int64)
	for _, result := range results {
		favoriteType := result["type"].(string)
		count, _ := result["count"].(int64)
		statistics[favoriteType] = count
	}

	return statistics, nil
}

// GetPopularFavorites 获取热门收藏
func (r *userFavoriteRepository) GetPopularFavorites(favoriteType string, limit int) ([]*models.UserFavorite, error) {
	var favorites []*models.UserFavorite

	sql := `SELECT target_id, target_name, target_image, type, COUNT(*) as favorite_count 
			FROM user_favorite 
			WHERE 1=1`
	params := []interface{}{}

	if favoriteType != "" {
		sql += " AND type = ?"
		params = append(params, favoriteType)
	}

	sql += " GROUP BY target_id, type ORDER BY favorite_count DESC LIMIT ?"
	params = append(params, limit)

	_, err := r.ormer.Raw(sql, params...).QueryRows(&favorites)
	return favorites, err
}

// CreateFolder 创建收藏夹
func (r *userFavoriteRepository) CreateFolder(folder *models.FavoriteFolder) (int64, error) {
	folder.CreatedAt = time.Now()
	folder.UpdatedAt = time.Now()
	return r.ormer.Insert(folder)
}

// GetFolderByID 根据ID获取收藏夹
func (r *userFavoriteRepository) GetFolderByID(id int64) (*models.FavoriteFolder, error) {
	folder := &models.FavoriteFolder{ID: id}
	err := r.ormer.Read(folder)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("收藏夹不存在")
		}
		return nil, err
	}
	return folder, nil
}

// UpdateFolder 更新收藏夹
func (r *userFavoriteRepository) UpdateFolder(folder *models.FavoriteFolder) error {
	folder.UpdatedAt = time.Now()
	_, err := r.ormer.Update(folder)
	return err
}

// DeleteFolder 删除收藏夹
func (r *userFavoriteRepository) DeleteFolder(id int64, userID int64) error {
	folder := &models.FavoriteFolder{ID: id, UserID: userID}
	_, err := r.ormer.Delete(folder, "ID", "UserID")
	return err
}

// ListUserFolders 获取用户收藏夹列表
func (r *userFavoriteRepository) ListUserFolders(userID int64) ([]*models.FavoriteFolder, error) {
	var folders []*models.FavoriteFolder

	_, err := r.ormer.QueryTable(new(models.FavoriteFolder)).
		Filter("user_id", userID).
		OrderBy("sort_order", "-created_at").
		All(&folders)

	return folders, err
}

// GetDefaultFolder 获取默认收藏夹
func (r *userFavoriteRepository) GetDefaultFolder(userID int64) (*models.FavoriteFolder, error) {
	folder := &models.FavoriteFolder{}
	err := r.ormer.QueryTable(new(models.FavoriteFolder)).
		Filter("user_id", userID).
		Filter("is_default", true).
		One(folder)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return folder, nil
}

// UpdateFolderItemCount 更新收藏夹项目数量
func (r *userFavoriteRepository) UpdateFolderItemCount(folderID int64) error {
	count, err := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("folder_id", folderID).
		Count()
	if err != nil {
		return err
	}

	_, err = r.ormer.QueryTable(new(models.FavoriteFolder)).
		Filter("id", folderID).
		Update(orm.Params{"item_count": count, "updated_at": time.Now()})

	return err
}

// BatchMoveFavorites 批量移动收藏
func (r *userFavoriteRepository) BatchMoveFavorites(ids []int64, folderID int64, userID int64) error {
	if len(ids) == 0 {
		return nil
	}

	_, err := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("id__in", ids).
		Filter("user_id", userID).
		Update(orm.Params{"folder_id": folderID, "updated_at": time.Now()})

	return err
}

// BatchUpdateFavorites 批量更新收藏
func (r *userFavoriteRepository) BatchUpdateFavorites(ids []int64, updates map[string]interface{}, userID int64) error {
	if len(ids) == 0 {
		return nil
	}

	updates["updated_at"] = time.Now()
	_, err := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("id__in", ids).
		Filter("user_id", userID).
		Update(updates)

	return err
}

// ClearUserFavorites 清空用户收藏
func (r *userFavoriteRepository) ClearUserFavorites(userID int64, favoriteType string) error {
	qs := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("user_id", userID)

	if favoriteType != "" {
		qs = qs.Filter("type", favoriteType)
	}

	_, err := qs.Delete()
	return err
}

// CleanupExpiredFavorites 清理过期收藏
func (r *userFavoriteRepository) CleanupExpiredFavorites(days int) error {
	cutoffDate := time.Now().AddDate(0, 0, -days)

	_, err := r.ormer.QueryTable(new(models.UserFavorite)).
		Filter("created_at__lt", cutoffDate).
		Delete()

	if err != nil {
		logs.Error("清理过期收藏失败: %v", err)
		return err
	}

	return nil
}
