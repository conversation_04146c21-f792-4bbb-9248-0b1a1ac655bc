/**
 * 用户收藏路由配置
 *
 * 本文件定义了用户收藏模块的路由配置，包括所有相关的API接口路由。
 * 提供收藏的增删改查、统计分析等功能的路由映射。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"
	"o_mall_backend/middlewares"
	"o_mall_backend/modules/favorites/controllers"
)

// InitFavoritesRouter 初始化用户收藏路由
func InitFavoritesRouter() {
	// 创建用户收藏命名空间
	favoritesNS := web.NewNamespace("/api/v1/user/secured/favorites",
		// 需要登录的API
		web.NSBefore(middlewares.JWTFilter),

		// 收藏基础操作
		web.NSRouter("/add", &controllers.UserFavoriteController{}, "post:Add;options:Options"),
		web.NSRouter("/update/:id", &controllers.UserFavoriteController{}, "post:Update;options:Options"),
		web.NSRouter("/delete/:id", &controllers.UserFavoriteController{}, "post:Delete;options:Options"),
		web.NSRouter("/batch-delete", &controllers.UserFavoriteController{}, "post:BatchDelete;options:Options"),
		web.NSRouter("/detail/:id", &controllers.UserFavoriteController{}, "get:Detail;options:Options"),

		// 收藏查询
		web.NSRouter("/list", &controllers.UserFavoriteController{}, "get:List;options:Options"),
		web.NSRouter("/search", &controllers.UserFavoriteController{}, "get:Search;options:Options"),
		web.NSRouter("/type/:type", &controllers.UserFavoriteController{}, "get:GetByType;options:Options"),
		web.NSRouter("/status", &controllers.UserFavoriteController{}, "get:CheckStatus;options:Options"),

		// 统计和分析
		web.NSRouter("/statistics", &controllers.UserFavoriteController{}, "get:Statistics;options:Options"),
		web.NSRouter("/types", &controllers.UserFavoriteController{}, "get:Types;options:Options"),

		// 批量操作
		web.NSRouter("/batch-move", &controllers.UserFavoriteController{}, "post:BatchMove;options:Options"),

		// 清理功能
		web.NSRouter("/clear", &controllers.UserFavoriteController{}, "post:Clear;options:Options"),

		// 收藏夹操作
		web.NSNamespace("/folders",
			web.NSRouter("/create", &controllers.FavoriteFolderController{}, "post:Create;options:Options"),
			web.NSRouter("/update/:id", &controllers.FavoriteFolderController{}, "post:Update;options:Options"),
			web.NSRouter("/delete/:id", &controllers.FavoriteFolderController{}, "post:Delete;options:Options"),
			web.NSRouter("/detail/:id", &controllers.FavoriteFolderController{}, "get:Detail;options:Options"),
			web.NSRouter("/list", &controllers.FavoriteFolderController{}, "get:List;options:Options"),
		),
	)

	// 注册命名空间
	web.AddNamespace(favoritesNS)
}
