/**
 * 用户收藏服务
 *
 * 本文件实现了用户收藏的业务逻辑层，处理收藏的添加、查询、统计等操作。
 * 提供完整的收藏管理功能，支持多种类型的收藏和收藏夹管理。
 */

package services

import (
	"encoding/json"
	"errors"
	"strings"
	"time"

	"o_mall_backend/modules/favorites/dto"
	"o_mall_backend/modules/favorites/models"
	"o_mall_backend/modules/favorites/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// UserFavoriteService 用户收藏服务接口
type UserFavoriteService interface {
	// 收藏操作
	AddFavorite(userID int64, req *dto.AddFavoriteRequest) (*dto.UserFavoriteDTO, error)
	UpdateFavorite(userID int64, favoriteID int64, req *dto.UpdateFavoriteRequest) error
	DeleteFavorite(userID int64, favoriteID int64) error
	BatchDeleteFavorites(userID int64, req *dto.BatchDeleteRequest) error
	GetFavoriteDetail(userID int64, favoriteID int64) (*dto.UserFavoriteDTO, error)

	// 收藏查询
	ListFavorites(userID int64, req *dto.FavoriteQueryRequest) (*dto.FavoriteListResponse, error)
	SearchFavorites(userID int64, keyword string, page, pageSize int) (*dto.FavoriteListResponse, error)
	GetFavoritesByType(userID int64, favoriteType string, page, pageSize int) (*dto.FavoriteListResponse, error)
	CheckFavoriteStatus(userID int64, favoriteType string, targetID int64) (*dto.FavoriteStatusResponse, error)

	// 收藏夹操作
	CreateFolder(userID int64, req *dto.CreateFolderRequest) (*dto.FavoriteFolderDTO, error)
	UpdateFolder(userID int64, folderID int64, req *dto.UpdateFolderRequest) error
	DeleteFolder(userID int64, folderID int64) error
	ListFolders(userID int64) ([]dto.FavoriteFolderDTO, error)
	GetFolderDetail(userID int64, folderID int64) (*dto.FavoriteFolderDTO, error)

	// 批量操作
	BatchMoveFavorites(userID int64, req *dto.BatchMoveRequest) error

	// 统计功能
	GetFavoriteStatistics(userID int64) (*dto.FavoriteStatisticsDTO, error)
	GetFavoriteTypes(userID int64) ([]dto.FavoriteTypeDTO, error)

	// 清理功能
	ClearFavorites(userID int64, favoriteType string) error
	CleanupExpiredFavorites(days int) error
}

// userFavoriteService 用户收藏服务实现
type userFavoriteService struct {
	favoriteRepo repositories.UserFavoriteRepository
}

// NewUserFavoriteService 创建用户收藏服务实例
func NewUserFavoriteService() UserFavoriteService {
	return &userFavoriteService{
		favoriteRepo: repositories.NewUserFavoriteRepository(),
	}
}

// AddFavorite 添加收藏
func (s *userFavoriteService) AddFavorite(userID int64, req *dto.AddFavoriteRequest) (*dto.UserFavoriteDTO, error) {
	logs.Info("[Favorites] AddFavorite 服务被调用，用户ID: %d", userID)
	logs.Info("[Favorites] 请求参数: %+v", req)
	logs.Info("[Favorites] 收藏类型: '%s', 长度: %d", req.Type, len(req.Type))

	// 验证收藏类型
	isValid := models.IsValidFavoriteType(req.Type)
	logs.Info("[Favorites] 类型验证结果: %t", isValid)

	if !isValid {
		logs.Error("[Favorites] 无效的收藏类型: '%s'", req.Type)
		return nil, errors.New("无效的收藏类型")
	}

	// 检查是否已收藏
	existingFavorite, err := s.favoriteRepo.GetUserFavorite(userID, req.Type, req.TargetID)
	if err != nil {
		logs.Error("查询收藏记录失败: %v", err)
		return nil, errors.New("添加收藏失败")
	}

	if existingFavorite != nil {
		return nil, errors.New("已经收藏过该项目")
	}

	// 处理额外数据
	extraDataJSON := ""
	if req.ExtraData != nil {
		data, err := json.Marshal(req.ExtraData)
		if err == nil {
			extraDataJSON = string(data)
		}
	}

	// 处理标签
	tagsStr := ""
	if len(req.Tags) > 0 {
		tagsStr = strings.Join(req.Tags, ",")
	}

	// 如果没有指定收藏夹，使用默认收藏夹
	folderID := req.FolderID
	if folderID == 0 {
		defaultFolder, err := s.getOrCreateDefaultFolder(userID)
		if err != nil {
			logs.Warn("获取默认收藏夹失败: %v", err)
		} else {
			folderID = defaultFolder.ID
		}
	}

	// 创建收藏记录
	favorite := &models.UserFavorite{
		UserID:      userID,
		Type:        req.Type,
		TargetID:    req.TargetID,
		TargetName:  req.TargetName,
		TargetImage: req.TargetImage,
		FolderID:    folderID,
		ExtraData:   extraDataJSON,
		Tags:        tagsStr,
		Notes:       req.Notes,
		IsPublic:    req.IsPublic,
	}

	favoriteID, err := s.favoriteRepo.CreateFavorite(favorite)
	if err != nil {
		logs.Error("创建收藏记录失败: %v", err)
		return nil, errors.New("创建收藏失败")
	}

	// 更新收藏夹项目数量
	if folderID > 0 {
		s.favoriteRepo.UpdateFolderItemCount(folderID)
	}

	// 获取创建的收藏记录
	favorite.ID = favoriteID
	return s.convertToFavoriteDTO(favorite), nil
}

// UpdateFavorite 更新收藏
func (s *userFavoriteService) UpdateFavorite(userID int64, favoriteID int64, req *dto.UpdateFavoriteRequest) error {
	favorite, err := s.favoriteRepo.GetFavoriteByID(favoriteID)
	if err != nil {
		return err
	}

	if favorite.UserID != userID {
		return errors.New("无权限操作此收藏")
	}

	// 更新字段
	oldFolderID := favorite.FolderID
	favorite.FolderID = req.FolderID
	favorite.Notes = req.Notes
	favorite.SortOrder = req.SortOrder
	favorite.IsPublic = req.IsPublic

	// 处理标签
	if len(req.Tags) > 0 {
		favorite.Tags = strings.Join(req.Tags, ",")
	}

	err = s.favoriteRepo.UpdateFavorite(favorite)
	if err != nil {
		return err
	}

	// 更新相关收藏夹的项目数量
	if oldFolderID != req.FolderID {
		if oldFolderID > 0 {
			s.favoriteRepo.UpdateFolderItemCount(oldFolderID)
		}
		if req.FolderID > 0 {
			s.favoriteRepo.UpdateFolderItemCount(req.FolderID)
		}
	}

	return nil
}

// DeleteFavorite 删除收藏
func (s *userFavoriteService) DeleteFavorite(userID int64, favoriteID int64) error {
	favorite, err := s.favoriteRepo.GetFavoriteByID(favoriteID)
	if err != nil {
		return err
	}

	if favorite.UserID != userID {
		return errors.New("无权限操作此收藏")
	}

	err = s.favoriteRepo.DeleteFavorite(favoriteID, userID)
	if err != nil {
		return err
	}

	// 更新收藏夹项目数量
	if favorite.FolderID > 0 {
		s.favoriteRepo.UpdateFolderItemCount(favorite.FolderID)
	}

	return nil
}

// BatchDeleteFavorites 批量删除收藏
func (s *userFavoriteService) BatchDeleteFavorites(userID int64, req *dto.BatchDeleteRequest) error {
	if len(req.IDs) == 0 {
		return errors.New("请选择要删除的收藏")
	}

	// 获取要删除的收藏记录，用于更新收藏夹计数
	folderIDs := make(map[int64]bool)
	for _, id := range req.IDs {
		favorite, err := s.favoriteRepo.GetFavoriteByID(id)
		if err == nil && favorite.UserID == userID && favorite.FolderID > 0 {
			folderIDs[favorite.FolderID] = true
		}
	}

	err := s.favoriteRepo.BatchDeleteFavorites(req.IDs, userID)
	if err != nil {
		return err
	}

	// 更新相关收藏夹的项目数量
	for folderID := range folderIDs {
		s.favoriteRepo.UpdateFolderItemCount(folderID)
	}

	return nil
}

// GetFavoriteDetail 获取收藏详情
func (s *userFavoriteService) GetFavoriteDetail(userID int64, favoriteID int64) (*dto.UserFavoriteDTO, error) {
	favorite, err := s.favoriteRepo.GetFavoriteByID(favoriteID)
	if err != nil {
		return nil, err
	}

	if favorite.UserID != userID {
		return nil, errors.New("无权限查看此收藏")
	}

	return s.convertToFavoriteDTO(favorite), nil
}

// getOrCreateDefaultFolder 获取或创建默认收藏夹
func (s *userFavoriteService) getOrCreateDefaultFolder(userID int64) (*models.FavoriteFolder, error) {
	// 先尝试获取默认收藏夹
	defaultFolder, err := s.favoriteRepo.GetDefaultFolder(userID)
	if err != nil {
		return nil, err
	}

	if defaultFolder != nil {
		return defaultFolder, nil
	}

	// 创建默认收藏夹
	folder := &models.FavoriteFolder{
		UserID:      userID,
		Name:        "默认收藏夹",
		Description: "系统自动创建的默认收藏夹",
		Icon:        "heart",
		Color:       "#ff6b6b",
		IsDefault:   true,
		IsPublic:    false,
	}

	folderID, err := s.favoriteRepo.CreateFolder(folder)
	if err != nil {
		return nil, err
	}

	folder.ID = folderID
	return folder, nil
}

// ListFavorites 获取收藏列表
func (s *userFavoriteService) ListFavorites(userID int64, req *dto.FavoriteQueryRequest) (*dto.FavoriteListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	var favorites []*models.UserFavorite
	var total int64
	var err error

	// 根据查询条件获取数据
	if req.StartDate != "" && req.EndDate != "" {
		startDate, err1 := time.Parse("2006-01-02", req.StartDate)
		endDate, err2 := time.Parse("2006-01-02", req.EndDate)
		if err1 != nil || err2 != nil {
			return nil, errors.New("日期格式错误")
		}
		endDate = endDate.Add(24*time.Hour - time.Second)

		favorites, total, err = s.favoriteRepo.GetFavoritesByDateRange(userID, req.Type, startDate, endDate, offset, req.PageSize)
	} else if req.Tags != "" {
		tags := strings.Split(req.Tags, ",")
		favorites, total, err = s.favoriteRepo.GetFavoritesByTags(userID, tags, offset, req.PageSize)
	} else if req.Keyword != "" {
		favorites, total, err = s.favoriteRepo.SearchUserFavorites(userID, req.Keyword, offset, req.PageSize)
	} else {
		favorites, total, err = s.favoriteRepo.ListUserFavorites(userID, req.Type, req.FolderID, offset, req.PageSize)
	}

	if err != nil {
		logs.Error("查询收藏列表失败: %v", err)
		return nil, errors.New("查询收藏列表失败")
	}

	// 转换为DTO
	favoriteDTOs := make([]dto.UserFavoriteDTO, 0, len(favorites))
	for _, favorite := range favorites {
		favoriteDTOs = append(favoriteDTOs, *s.convertToFavoriteDTO(favorite))
	}

	return &dto.FavoriteListResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     favoriteDTOs,
	}, nil
}

// SearchFavorites 搜索收藏
func (s *userFavoriteService) SearchFavorites(userID int64, keyword string, page, pageSize int) (*dto.FavoriteListResponse, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	favorites, total, err := s.favoriteRepo.SearchUserFavorites(userID, keyword, offset, pageSize)
	if err != nil {
		logs.Error("搜索收藏失败: %v", err)
		return nil, errors.New("搜索收藏失败")
	}

	// 转换为DTO
	favoriteDTOs := make([]dto.UserFavoriteDTO, 0, len(favorites))
	for _, favorite := range favorites {
		favoriteDTOs = append(favoriteDTOs, *s.convertToFavoriteDTO(favorite))
	}

	return &dto.FavoriteListResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		List:     favoriteDTOs,
	}, nil
}

// GetFavoritesByType 根据类型获取收藏
func (s *userFavoriteService) GetFavoritesByType(userID int64, favoriteType string, page, pageSize int) (*dto.FavoriteListResponse, error) {
	if !models.IsValidFavoriteType(favoriteType) {
		return nil, errors.New("无效的收藏类型")
	}

	req := &dto.FavoriteQueryRequest{
		Type:     favoriteType,
		Page:     page,
		PageSize: pageSize,
	}

	return s.ListFavorites(userID, req)
}

// CheckFavoriteStatus 检查收藏状态
func (s *userFavoriteService) CheckFavoriteStatus(userID int64, favoriteType string, targetID int64) (*dto.FavoriteStatusResponse, error) {
	favorite, err := s.favoriteRepo.GetUserFavorite(userID, favoriteType, targetID)
	if err != nil {
		return nil, err
	}

	response := &dto.FavoriteStatusResponse{
		IsFavorited: favorite != nil,
	}

	if favorite != nil {
		response.FavoriteID = favorite.ID
		response.FolderID = favorite.FolderID
		response.CreatedAt = favorite.CreatedAt

		// 获取收藏夹名称
		if favorite.FolderID > 0 {
			folder, err := s.favoriteRepo.GetFolderByID(favorite.FolderID)
			if err == nil {
				response.FolderName = folder.Name
			}
		}
	}

	return response, nil
}

// CreateFolder 创建收藏夹
func (s *userFavoriteService) CreateFolder(userID int64, req *dto.CreateFolderRequest) (*dto.FavoriteFolderDTO, error) {
	folder := &models.FavoriteFolder{
		UserID:      userID,
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		Color:       req.Color,
		IsPublic:    req.IsPublic,
	}

	folderID, err := s.favoriteRepo.CreateFolder(folder)
	if err != nil {
		logs.Error("创建收藏夹失败: %v", err)
		return nil, errors.New("创建收藏夹失败")
	}

	folder.ID = folderID
	return s.convertToFolderDTO(folder), nil
}

// UpdateFolder 更新收藏夹
func (s *userFavoriteService) UpdateFolder(userID int64, folderID int64, req *dto.UpdateFolderRequest) error {
	folder, err := s.favoriteRepo.GetFolderByID(folderID)
	if err != nil {
		return err
	}

	if folder.UserID != userID {
		return errors.New("无权限操作此收藏夹")
	}

	if folder.IsDefault {
		return errors.New("默认收藏夹不能修改")
	}

	// 更新字段
	if req.Name != "" {
		folder.Name = req.Name
	}
	folder.Description = req.Description
	folder.Icon = req.Icon
	folder.Color = req.Color
	folder.SortOrder = req.SortOrder
	folder.IsPublic = req.IsPublic

	return s.favoriteRepo.UpdateFolder(folder)
}

// DeleteFolder 删除收藏夹
func (s *userFavoriteService) DeleteFolder(userID int64, folderID int64) error {
	folder, err := s.favoriteRepo.GetFolderByID(folderID)
	if err != nil {
		return err
	}

	if folder.UserID != userID {
		return errors.New("无权限操作此收藏夹")
	}

	if folder.IsDefault {
		return errors.New("默认收藏夹不能删除")
	}

	// 检查收藏夹是否有收藏项
	count, err := s.favoriteRepo.CountUserFavorites(userID, "")
	if err == nil && count > 0 {
		return errors.New("收藏夹中还有收藏项，请先清空或移动收藏项")
	}

	return s.favoriteRepo.DeleteFolder(folderID, userID)
}

// ListFolders 获取收藏夹列表
func (s *userFavoriteService) ListFolders(userID int64) ([]dto.FavoriteFolderDTO, error) {
	folders, err := s.favoriteRepo.ListUserFolders(userID)
	if err != nil {
		logs.Error("查询收藏夹列表失败: %v", err)
		return nil, errors.New("查询收藏夹列表失败")
	}

	// 转换为DTO
	folderDTOs := make([]dto.FavoriteFolderDTO, 0, len(folders))
	for _, folder := range folders {
		folderDTOs = append(folderDTOs, *s.convertToFolderDTO(folder))
	}

	return folderDTOs, nil
}

// GetFolderDetail 获取收藏夹详情
func (s *userFavoriteService) GetFolderDetail(userID int64, folderID int64) (*dto.FavoriteFolderDTO, error) {
	folder, err := s.favoriteRepo.GetFolderByID(folderID)
	if err != nil {
		return nil, err
	}

	if folder.UserID != userID {
		return nil, errors.New("无权限查看此收藏夹")
	}

	return s.convertToFolderDTO(folder), nil
}

// BatchMoveFavorites 批量移动收藏
func (s *userFavoriteService) BatchMoveFavorites(userID int64, req *dto.BatchMoveRequest) error {
	if len(req.IDs) == 0 {
		return errors.New("请选择要移动的收藏")
	}

	// 验证目标收藏夹
	if req.FolderID > 0 {
		folder, err := s.favoriteRepo.GetFolderByID(req.FolderID)
		if err != nil {
			return errors.New("目标收藏夹不存在")
		}
		if folder.UserID != userID {
			return errors.New("无权限操作目标收藏夹")
		}
	}

	// 获取原收藏夹ID用于更新计数
	oldFolderIDs := make(map[int64]bool)
	for _, id := range req.IDs {
		favorite, err := s.favoriteRepo.GetFavoriteByID(id)
		if err == nil && favorite.UserID == userID && favorite.FolderID > 0 {
			oldFolderIDs[favorite.FolderID] = true
		}
	}

	err := s.favoriteRepo.BatchMoveFavorites(req.IDs, req.FolderID, userID)
	if err != nil {
		return err
	}

	// 更新相关收藏夹的项目数量
	for folderID := range oldFolderIDs {
		s.favoriteRepo.UpdateFolderItemCount(folderID)
	}
	if req.FolderID > 0 {
		s.favoriteRepo.UpdateFolderItemCount(req.FolderID)
	}

	return nil
}

// GetFavoriteStatistics 获取收藏统计
func (s *userFavoriteService) GetFavoriteStatistics(userID int64) (*dto.FavoriteStatisticsDTO, error) {
	// 获取总数
	totalCount, err := s.favoriteRepo.CountUserFavorites(userID, "")
	if err != nil {
		return nil, err
	}

	// 获取今日数量
	today := time.Now()
	startOfDay := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
	endOfDay := startOfDay.Add(24*time.Hour - time.Second)
	todayCount, err := s.favoriteRepo.CountUserFavoritesByDateRange(userID, "", startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	// 获取本周数量
	weekStart := getWeekStart(today)
	weekEnd := weekStart.Add(7*24*time.Hour - time.Second)
	weekCount, err := s.favoriteRepo.CountUserFavoritesByDateRange(userID, "", weekStart, weekEnd)
	if err != nil {
		return nil, err
	}

	// 获取本月数量
	monthStart := time.Date(today.Year(), today.Month(), 1, 0, 0, 0, 0, today.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Second)
	monthCount, err := s.favoriteRepo.CountUserFavoritesByDateRange(userID, "", monthStart, monthEnd)
	if err != nil {
		return nil, err
	}

	// 获取类型统计
	typeStats, err := s.favoriteRepo.GetTypeStatistics(userID)
	if err != nil {
		return nil, err
	}

	typeStatistics := make([]dto.TypeStatisticsDTO, 0)
	favoriteTypes := models.GetFavoriteTypes()
	for _, favoriteType := range favoriteTypes {
		count := typeStats[favoriteType.Type]
		percentage := float64(0)
		if totalCount > 0 {
			percentage = float64(count) / float64(totalCount) * 100
		}

		typeStatistics = append(typeStatistics, dto.TypeStatisticsDTO{
			Type:       favoriteType.Type,
			TypeName:   favoriteType.Name,
			Count:      count,
			Percentage: percentage,
		})
	}

	// 获取收藏夹数量
	folders, err := s.favoriteRepo.ListUserFolders(userID)
	if err != nil {
		return nil, err
	}
	folderCount := int64(len(folders))

	// 获取公开收藏数量
	publicCount := int64(0) // 这里可以添加具体的查询逻辑

	// 获取最近收藏
	recentFavorites, _, err := s.favoriteRepo.ListUserFavorites(userID, "", 0, 0, 10)
	if err != nil {
		return nil, err
	}

	recentFavoriteList := make([]dto.UserFavoriteDTO, 0, len(recentFavorites))
	for _, favorite := range recentFavorites {
		recentFavoriteList = append(recentFavoriteList, *s.convertToFavoriteDTO(favorite))
	}

	// 获取热门收藏
	popularFavorites, err := s.favoriteRepo.GetPopularFavorites("", 10)
	if err != nil {
		return nil, err
	}

	popularItems := make([]dto.PopularFavoriteDTO, 0, len(popularFavorites))
	for _, item := range popularFavorites {
		typeName := s.getTypeNameByType(item.Type)
		popularItems = append(popularItems, dto.PopularFavoriteDTO{
			Type:           item.Type,
			TypeName:       typeName,
			TargetID:       item.TargetID,
			TargetName:     item.TargetName,
			TargetImage:    item.TargetImage,
			FavoriteCount:  1, // 这里需要从查询结果中获取
			LastFavoriteAt: item.CreatedAt,
		})
	}

	return &dto.FavoriteStatisticsDTO{
		TotalCount:      totalCount,
		TodayCount:      todayCount,
		WeekCount:       weekCount,
		MonthCount:      monthCount,
		TypeStatistics:  typeStatistics,
		FolderCount:     folderCount,
		PublicCount:     publicCount,
		RecentFavorites: recentFavoriteList,
		PopularItems:    popularItems,
	}, nil
}

// GetFavoriteTypes 获取收藏类型列表
func (s *userFavoriteService) GetFavoriteTypes(userID int64) ([]dto.FavoriteTypeDTO, error) {
	// 获取类型统计
	typeStats, err := s.favoriteRepo.GetTypeStatistics(userID)
	if err != nil {
		return nil, err
	}

	favoriteTypes := models.GetFavoriteTypes()
	result := make([]dto.FavoriteTypeDTO, 0, len(favoriteTypes))

	for _, favoriteType := range favoriteTypes {
		count := typeStats[favoriteType.Type]
		result = append(result, dto.FavoriteTypeDTO{
			Type:        favoriteType.Type,
			Name:        favoriteType.Name,
			Description: favoriteType.Description,
			Count:       count,
		})
	}

	return result, nil
}

// ClearFavorites 清空收藏
func (s *userFavoriteService) ClearFavorites(userID int64, favoriteType string) error {
	return s.favoriteRepo.ClearUserFavorites(userID, favoriteType)
}

// CleanupExpiredFavorites 清理过期收藏
func (s *userFavoriteService) CleanupExpiredFavorites(days int) error {
	return s.favoriteRepo.CleanupExpiredFavorites(days)
}

// convertToFavoriteDTO 转换为收藏DTO
func (s *userFavoriteService) convertToFavoriteDTO(favorite *models.UserFavorite) *dto.UserFavoriteDTO {
	typeName := s.getTypeNameByType(favorite.Type)

	// 解析额外数据
	var extraData map[string]interface{}
	if favorite.ExtraData != "" {
		json.Unmarshal([]byte(favorite.ExtraData), &extraData)
	}

	// 解析标签
	var tags []string
	if favorite.Tags != "" {
		tags = strings.Split(favorite.Tags, ",")
	}

	// 获取收藏夹名称
	folderName := ""
	if favorite.FolderID > 0 {
		folder, err := s.favoriteRepo.GetFolderByID(favorite.FolderID)
		if err == nil {
			folderName = folder.Name
		}
	}

	return &dto.UserFavoriteDTO{
		ID:          favorite.ID,
		UserID:      favorite.UserID,
		Type:        favorite.Type,
		TypeName:    typeName,
		TargetID:    favorite.TargetID,
		TargetName:  favorite.TargetName,
		TargetImage: favorite.TargetImage,
		FolderID:    favorite.FolderID,
		FolderName:  folderName,
		ExtraData:   extraData,
		Tags:        tags,
		Notes:       favorite.Notes,
		SortOrder:   favorite.SortOrder,
		IsPublic:    favorite.IsPublic,
		CreatedAt:   favorite.CreatedAt,
		UpdatedAt:   favorite.UpdatedAt,
	}
}

// convertToFolderDTO 转换为收藏夹DTO
func (s *userFavoriteService) convertToFolderDTO(folder *models.FavoriteFolder) *dto.FavoriteFolderDTO {
	return &dto.FavoriteFolderDTO{
		ID:          folder.ID,
		UserID:      folder.UserID,
		Name:        folder.Name,
		Description: folder.Description,
		Icon:        folder.Icon,
		Color:       folder.Color,
		SortOrder:   folder.SortOrder,
		IsDefault:   folder.IsDefault,
		IsPublic:    folder.IsPublic,
		ItemCount:   folder.ItemCount,
		CreatedAt:   folder.CreatedAt,
		UpdatedAt:   folder.UpdatedAt,
	}
}

// getTypeNameByType 根据类型获取类型名称
func (s *userFavoriteService) getTypeNameByType(favoriteType string) string {
	favoriteTypes := models.GetFavoriteTypes()
	for _, ft := range favoriteTypes {
		if ft.Type == favoriteType {
			return ft.Name
		}
	}
	return favoriteType
}

// getWeekStart 获取本周开始时间（周一）
func getWeekStart(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日转换为7
	}
	days := weekday - 1 // 距离周一的天数
	return time.Date(t.Year(), t.Month(), t.Day()-days, 0, 0, 0, 0, t.Location())
}
