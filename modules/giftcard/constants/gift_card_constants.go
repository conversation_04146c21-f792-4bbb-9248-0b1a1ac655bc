/**
 * 礼品卡系统常量定义
 *
 * 本文件定义了礼品卡系统相关的常量，包括礼品卡状态、交易类型等。
 * 这些常量被用于系统中的各个模块，以确保数据的一致性和可维护性。
 */

package constants

// 礼品卡状态常量
const (
	GiftCardStatusInactive = "inactive" // 未激活
	GiftCardStatusActive   = "active"   // 已激活
	GiftCardStatusUsed     = "used"     // 已使用完
	GiftCardStatusExpired  = "expired"  // 已过期
)

// 礼品卡类型常量
const (
	GiftCardTypeElectronic = "electronic" // 电子卡
	GiftCardTypePhysical   = "physical"   // 实体卡
)

// 礼品卡交易类型常量
const (
	GiftCardTransactionTypeIssue      = "issue"      // 发行
	GiftCardTransactionTypeActivation = "activation" // 激活
	GiftCardTransactionTypeRecharge   = "recharge"   // 充值
	GiftCardTransactionTypeConsume    = "consume"    // 消费
	GiftCardTransactionTypeRefund     = "refund"     // 退款
	GiftCardTransactionTypeExpire     = "expire"     // 过期
)
