/**
 * gift_card_controller.go
 * 礼品卡控制器
 *
 * 本文件实现了礼品卡相关的API接口，包括礼品卡的创建、激活、使用、查询等功能。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/giftcard/dto"
	"o_mall_backend/modules/giftcard/services"
)

// GiftCardController 礼品卡控制器
type GiftCardController struct {
	controllers.BaseController
	giftCardService services.GiftCardService
}

// NewGiftCardController 创建礼品卡控制器实例
func NewGiftCardController(giftCardService services.GiftCardService) *GiftCardController {
	return &GiftCardController{
		giftCardService: giftCardService,
	}
}

// List 获取礼品卡列表
func (c *GiftCardController) List() {
	// 获取用户ID
	userID := c.GetUserID()
	if userID == 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 构建请求参数
	req := &dto.GetUserGiftCardsRequest{
		UserID: userID,
	}

	// 调用服务获取礼品卡列表
	cards, err := c.giftCardService.GetUserGiftCards(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[List] 获取用户礼品卡列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(cards)
}

// GetByNumber 根据卡号获取礼品卡信息
func (c *GiftCardController) GetByNumber() {
	// 获取卡号
	cardNumber := c.Ctx.Input.Param(":number")
	if cardNumber == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取用户ID
	userID := c.GetUserID()

	// 构建请求参数
	req := &dto.GetGiftCardRequest{
		CardNumber: cardNumber,
		UserID:     userID,
	}

	// 调用服务获取礼品卡信息
	card, err := c.giftCardService.GetGiftCardByNumber(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[GetByNumber] 获取礼品卡信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if card == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(card)
}

// Activate 激活礼品卡
func (c *GiftCardController) Activate() {
	// 解析请求参数
	req := &dto.ActivateGiftCardRequest{}
	if err := c.BindJSON(req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 验证用户ID
	userID := c.GetUserID()
	if userID == 0 || userID != req.UserID {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务激活礼品卡
	res, err := c.giftCardService.ActivateGiftCard(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[Activate] 激活礼品卡失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(res)
}

// Use 使用礼品卡
func (c *GiftCardController) Use() {
	// 解析请求参数
	req := &dto.UseGiftCardRequest{}
	if err := c.BindJSON(req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 验证用户ID
	userID := c.GetUserID()
	if userID == 0 || userID != req.UserID {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务使用礼品卡
	res, err := c.giftCardService.UseGiftCard(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[Use] 使用礼品卡失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(res)
}

// Recharge 充值礼品卡
func (c *GiftCardController) Recharge() {
	// 解析请求参数
	req := &dto.RechargeGiftCardRequest{}
	if err := c.BindJSON(req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 验证用户ID
	userID := c.GetUserID()
	if userID == 0 || userID != req.UserID {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务充值礼品卡
	res, err := c.giftCardService.RechargeGiftCard(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[Recharge] 充值礼品卡失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(res)
}

// ListTransactions 获取礼品卡交易记录
func (c *GiftCardController) ListTransactions() {
	// 获取卡号
	cardNumber := c.GetString("cardNumber")
	if cardNumber == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取用户ID
	userID := c.GetUserID()

	// 获取分页参数
	pageNum, _ := c.GetInt("pageNum", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 构建请求参数
	req := &dto.GetGiftCardTransactionsRequest{
		CardNumber: cardNumber,
		UserID:     userID,
		PageNum:    pageNum,
		PageSize:   pageSize,
	}

	// 调用服务获取交易记录
	transactions, err := c.giftCardService.GetGiftCardTransactions(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[ListTransactions] 获取礼品卡交易记录失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(transactions)
}

// ExchangePointsForGiftCard 积分兑换礼品卡
func (c *GiftCardController) ExchangePointsForGiftCard() {
	// 解析请求参数
	req := &dto.ExchangePointsForGiftCardRequest{}
	if err := c.BindJSON(req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 验证用户ID
	userID := c.GetUserID()
	if userID == 0 || userID != req.UserID {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务兑换礼品卡
	res, err := c.giftCardService.ExchangePointsForGiftCard(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[ExchangePointsForGiftCard] 积分兑换礼品卡失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(res)
}

// 管理员接口

// Create 创建礼品卡（管理员）
func (c *GiftCardController) Create() {
	// 解析请求参数
	req := &dto.CreateGiftCardRequest{}
	if err := c.BindJSON(req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 验证管理员权限（这里简化处理，实际应该使用权限中间件）
	adminID := c.GetUserID()
	if adminID == 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 设置发行人
	req.IssuedBy = adminID

	// 调用服务创建礼品卡
	card, err := c.giftCardService.CreateGiftCard(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[Create] 创建礼品卡失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(card)
}

// BatchCreate 批量创建礼品卡（管理员）
func (c *GiftCardController) BatchCreate() {
	// 解析请求参数
	req := &dto.BatchCreateGiftCardRequest{}
	if err := c.BindJSON(req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 验证管理员权限
	adminID := c.GetUserID()
	if adminID == 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 设置发行人
	req.IssuedBy = adminID

	// 调用服务批量创建礼品卡
	batch, err := c.giftCardService.BatchCreateGiftCards(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[BatchCreate] 批量创建礼品卡失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(batch)
}

// ListBatches 获取礼品卡批次列表（管理员）
func (c *GiftCardController) ListBatches() {
	// 验证管理员权限
	adminID := c.GetUserID()
	if adminID == 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取分页参数
	pageNum, _ := c.GetInt("pageNum", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 调用服务获取批次列表
	batches, err := c.giftCardService.GetGiftCardBatches(c.Ctx.Request.Context(), pageNum, pageSize)
	if err != nil {
		logs.Error("[ListBatches] 获取礼品卡批次列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(batches)
}

// GetBatchByNumber 根据批次号获取批次信息（管理员）
func (c *GiftCardController) GetBatchByNumber() {
	// 验证管理员权限
	adminID := c.GetUserID()
	if adminID == 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取批次号
	batchNumber := c.Ctx.Input.Param(":number")
	if batchNumber == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取批次信息
	batch, err := c.giftCardService.GetGiftCardBatchByNumber(c.Ctx.Request.Context(), batchNumber)
	if err != nil {
		logs.Error("[GetBatchByNumber] 获取礼品卡批次信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if batch == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回成功响应
	c.ResponseSuccess(batch)
}
