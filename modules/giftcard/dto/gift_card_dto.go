/**
 * 礼品卡系统数据传输对象
 *
 * 本文件定义了礼品卡系统相关的数据传输对象，用于接口请求和响应的数据结构。
 * 包括礼品卡查询、激活、充值、使用等相关的请求和响应对象。
 */

package dto

import "time"

// 请求对象

// CreateGiftCardRequest 创建礼品卡请求
type CreateGiftCardRequest struct {
	Amount      float64 `json:"amount" validate:"required,gt=0"`       // 礼品卡面额
	Type        string  `json:"type" validate:"required"`              // 类型(电子卡/实体卡)
	Description string  `json:"description,omitempty"`                 // 描述信息
	ValidDays   int     `json:"validDays" validate:"required,gt=0"`    // 有效期(天)
	IssuedBy    int64   `json:"issuedBy" validate:"required"`          // 发行管理员ID
}

// ActivateGiftCardRequest 激活礼品卡请求
type ActivateGiftCardRequest struct {
	CardNumber string `json:"cardNumber" validate:"required"`    // 礼品卡卡号
	CardSecret string `json:"cardSecret" validate:"required"`    // 礼品卡密码
	UserID     int64  `json:"userId" validate:"required"`        // 用户ID
}

// UseGiftCardRequest 使用礼品卡请求
type UseGiftCardRequest struct {
	CardNumber  string  `json:"cardNumber" validate:"required"`     // 礼品卡卡号
	Amount      float64 `json:"amount" validate:"required,gt=0"`    // 使用金额
	OrderID     string  `json:"orderId" validate:"required"`        // 订单ID
	UserID      int64   `json:"userId" validate:"required"`         // 用户ID
	Description string  `json:"description,omitempty"`              // 描述信息
}

// RechargeGiftCardRequest 充值礼品卡请求
type RechargeGiftCardRequest struct {
	CardNumber  string  `json:"cardNumber" validate:"required"`     // 礼品卡卡号
	Amount      float64 `json:"amount" validate:"required,gt=0"`    // 充值金额
	UserID      int64   `json:"userId" validate:"required"`         // 用户ID
	OperatorID  int64   `json:"operatorId" validate:"required"`     // 操作人ID
	Description string  `json:"description,omitempty"`              // 描述信息
}

// QueryGiftCardRequest 查询礼品卡请求
type QueryGiftCardRequest struct {
	CardNumber string `json:"cardNumber,omitempty"` // 礼品卡卡号
	UserID     int64  `json:"userId,omitempty"`     // 用户ID
}

// BatchCreateGiftCardRequest 批量创建礼品卡请求
type BatchCreateGiftCardRequest struct {
	BatchName   string  `json:"batchName" validate:"required"`      // 批次名称
	CardType    string  `json:"cardType" validate:"required"`       // 卡类型(电子卡/实体卡)
	FaceValue   float64 `json:"faceValue" validate:"required,gt=0"` // 面值
	Count       int     `json:"count" validate:"required,gt=0"`     // 生成数量
	ValidDays   int     `json:"validDays" validate:"required,gt=0"` // 有效期(天)
	IssuedBy    int64   `json:"issuedBy" validate:"required"`       // 发行管理员ID
	Description string  `json:"description,omitempty"`              // 描述信息
}

// ExchangePointsForGiftCardRequest 积分兑换礼品卡请求
type ExchangePointsForGiftCardRequest struct {
	UserID      int64   `json:"userId" validate:"required"`     // 用户ID
	PointsAmount int     `json:"pointsAmount" validate:"required,gt=0"` // 兑换积分数量
	CardAmount  float64 `json:"cardAmount" validate:"required,gt=0"`    // 礼品卡金额
}

// GetGiftCardRequest 获取礼品卡请求
type GetGiftCardRequest struct {
	CardNumber string `json:"cardNumber" validate:"required"` // 礼品卡卡号
	UserID     int64  `json:"userId,omitempty"`              // 用户ID（可选，用于权限检查）
}

// GetUserGiftCardsRequest 获取用户礼品卡请求
type GetUserGiftCardsRequest struct {
	UserID   int64 `json:"userId" validate:"required"` // 用户ID
	PageNum  int   `json:"pageNum,omitempty"`          // 页码
	PageSize int   `json:"pageSize,omitempty"`         // 每页条数
}

// GetGiftCardTransactionsRequest 获取礼品卡交易记录请求
type GetGiftCardTransactionsRequest struct {
	CardNumber string `json:"cardNumber" validate:"required"` // 礼品卡卡号
	UserID     int64  `json:"userId,omitempty"`              // 用户ID（可选，用于权限检查）
	PageNum    int    `json:"pageNum,omitempty"`             // 页码
	PageSize   int    `json:"pageSize,omitempty"`            // 每页条数
}

// GetGiftCardBatchRequest 获取礼品卡批次请求
type GetGiftCardBatchRequest struct {
	BatchNumber string `json:"batchNumber" validate:"required"` // 批次号
}

// 响应对象

// GiftCardResponse 礼品卡信息响应
type GiftCardResponse struct {
	ID          int64     `json:"id"`          // 礼品卡ID
	CardNumber  string    `json:"cardNumber"`  // 礼品卡卡号
	Amount      float64   `json:"amount"`      // 礼品卡面额
	Balance     float64   `json:"balance"`     // 当前余额
	Status      string    `json:"status"`      // 状态
	Type        string    `json:"type"`        // 类型(电子卡/实体卡)
	OwnedBy     int64     `json:"ownedBy"`     // 所属用户ID
	IssuedAt    time.Time `json:"issuedAt"`    // 发行时间
	ActivatedAt time.Time `json:"activatedAt"` // 激活时间
	ExpireAt    time.Time `json:"expireAt"`    // 过期时间
	Description string    `json:"description"` // 描述信息
}

// GiftCardTransactionResponse 礼品卡交易记录响应
type GiftCardTransactionResponse struct {
	ID          int64     `json:"id"`          // 交易ID
	CardNumber  string    `json:"cardNumber"`  // 礼品卡卡号
	UserID      int64     `json:"userId"`      // 用户ID
	Amount      float64   `json:"amount"`      // 交易金额
	Balance     float64   `json:"balance"`     // 交易后余额
	Type        string    `json:"type"`        // 交易类型(充值/消费/退款)
	OrderID     string    `json:"orderId"`     // 关联订单ID(如适用)
	Description string    `json:"description"` // 交易描述
	CreatedAt   time.Time `json:"createdAt"`   // 创建时间
}

// GiftCardOperationResponse 礼品卡操作响应
type GiftCardOperationResponse struct {
	Success      bool    `json:"success"`      // 操作是否成功
	CardNumber   string  `json:"cardNumber"`   // 礼品卡卡号
	CardAmount   float64 `json:"cardAmount"`   // 礼品卡面额
	CurrentBalance float64 `json:"currentBalance"` // 当前余额
	Message      string  `json:"message"`      // 操作信息
}

// GiftCardBatchResponse 礼品卡批次响应
type GiftCardBatchResponse struct {
	BatchNumber string    `json:"batchNumber"` // 批次号
	Name        string    `json:"name"`        // 批次名称
	CardType    string    `json:"cardType"`    // 卡类型
	FaceValue   float64   `json:"faceValue"`   // 面值
	Count       int       `json:"count"`       // 生成数量
	ValidDays   int       `json:"validDays"`   // 有效期(天)
	IssuedAt    time.Time `json:"issuedAt"`    // 发行时间
	Description string    `json:"description"` // 描述信息
}

// GiftCardTransactionListResponse 礼品卡交易记录列表响应
type GiftCardTransactionListResponse struct {
	Total       int                          `json:"total"`       // 总记录数
	CurrentPage int                          `json:"currentPage"` // 当前页码
	PageSize    int                          `json:"pageSize"`    // 每页条数
	Data        []GiftCardTransactionResponse `json:"data"`        // 数据列表
}

// ExchangePointsForGiftCardResponse 积分兑换礼品卡响应
type ExchangePointsForGiftCardResponse struct {
	Success     bool    `json:"success"`     // 操作是否成功
	UserID      int64   `json:"userId"`      // 用户ID
	PointsAmount int     `json:"pointsAmount"` // 兑换积分数量
	CardNumber  string  `json:"cardNumber"`  // 礼品卡卡号
	CardAmount  float64 `json:"cardAmount"`  // 礼品卡金额
	Message     string  `json:"message"`     // 操作信息
}
