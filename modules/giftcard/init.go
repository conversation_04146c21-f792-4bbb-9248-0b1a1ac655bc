/**
 * giftcard模块初始化
 *
 * 本文件负责giftcard模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保giftcard模块的功能正常启动。
 */

package giftcard

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/giftcard/models"
	"o_mall_backend/modules/giftcard/routers"
)

// Init 初始化giftcard模块
func Init() {
	logs.Info("初始化giftcard模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitRouters()

	logs.Info("giftcard模块初始化完成")
}

// registerModels 注册模型
func registerModels() {
	logs.Info("注册giftcard模块ORM模型...")

	// 注册giftcard相关模型
	orm.RegisterModel(
		new(models.GiftCard),
		new(models.GiftCardTransaction),
		new(models.GiftCardBatch),
	)
}
