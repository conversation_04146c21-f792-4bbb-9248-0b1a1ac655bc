/**
 * 礼品卡系统数据库模型
 *
 * 本文件定义了礼品卡系统相关的数据库模型，包括礼品卡、礼品卡交易记录等。
 * 每个模型都包含详细的字段注释，便于理解数据结构和业务含义。
 */

package models

import (
	"time"
)

// GiftCard 礼品卡
type GiftCard struct {
	ID           int64     `orm:"pk;auto" json:"id" description:"礼品卡唯一标识"`                        // 礼品卡ID，主键
	CardNumber   string    `orm:"size(50);unique" json:"cardNumber" description:"礼品卡唯一卡号"`        // 礼品卡卡号，唯一
	CardSecret   string    `orm:"size(100)" json:"-" description:"礼品卡密码，用于激活验证"`                  // 礼品卡密码，不返回给前端
	Amount       float64   `orm:"digits(10);decimals(2)" json:"amount" description:"礼品卡初始面额"`     // 礼品卡面额
	Balance      float64   `orm:"digits(10);decimals(2)" json:"balance" description:"礼品卡当前可用余额"`  // 当前余额
	Status       string    `orm:"size(20);index" json:"status" description:"礼品卡当前状态"`             // 状态(未激活/已激活/已使用/已过期)
	Type         string    `orm:"size(20)" json:"type" description:"礼品卡类型"`                       // 类型(电子卡/实体卡)
	IssuedBy     int64     `orm:"default(0)" json:"issuedBy" description:"发行人员ID"`                // 发行管理员ID
	OwnedBy      int64     `orm:"default(0);index" json:"ownedBy" description:"持卡人用户ID"`          // 所属用户ID，为0表示未绑定用户
	IssuedAt     time.Time `orm:"type(datetime);null" json:"issuedAt" description:"发行时间"`         // 发行时间
	ActivatedAt  time.Time `orm:"type(datetime);null" json:"activatedAt" description:"激活时间"`      // 激活时间
	ExpireAt     time.Time `orm:"type(datetime)" json:"expireAt" description:"过期时间"`              // 过期时间
	Description  string    `orm:"size(255);null" json:"description" description:"礼品卡描述信息"`        // 描述信息
	BatchNumber  string    `orm:"size(50);null;index" json:"batchNumber" description:"批量生成的批次编号"` // 批次号，批量生成时使用
	IsPointsExch bool      `orm:"default(false)" json:"isPointsExch" description:"是否为积分兑换的礼品卡"`   // 是否由积分兑换获得
	PointsUsed   int       `orm:"default(0)" json:"pointsUsed" description:"兑换所使用的积分数量"`          // 兑换使用的积分(如适用)
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime)" json:"-" description:"创建时间"`        // 创建时间
	UpdatedAt    time.Time `orm:"auto_now;type(datetime)" json:"-" description:"最后更新时间"`          // 更新时间
}

// GiftCardTransaction 礼品卡交易记录
type GiftCardTransaction struct {
	ID          int64     `orm:"pk;auto" json:"id" description:"交易记录唯一标识"`                        // 交易ID，主键
	CardID      int64     `orm:"index" json:"cardId" description:"关联的礼品卡ID"`                      // 礼品卡ID
	CardNumber  string    `orm:"size(50);index" json:"cardNumber" description:"礼品卡卡号"`            // 礼品卡卡号
	UserID      int64     `orm:"index" json:"userId" description:"交易用户ID"`                        // 用户ID
	Amount      float64   `orm:"digits(10);decimals(2)" json:"amount" description:"交易发生金额"`       // 交易金额
	Balance     float64   `orm:"digits(10);decimals(2)" json:"balance" description:"交易后卡内余额"`     // 交易后余额
	Type        string    `orm:"size(20)" json:"type" description:"交易类型"`                         // 交易类型(充值/消费/退款)
	OrderID     string    `orm:"size(50);null;index" json:"orderId" description:"关联的订单编号"`        // 关联订单ID(如适用)
	Description string    `orm:"size(255)" json:"description" description:"交易详细描述"`               // 交易描述
	OperatorID  int64     `orm:"default(0)" json:"operatorId" description:"执行操作的操作员ID"`           // 操作人ID
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);index" json:"-" description:"交易创建时间"` // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;type(datetime)" json:"-" description:"记录更新时间"`           // 更新时间
}

// GiftCardBatch 礼品卡批次
type GiftCardBatch struct {
	ID          int64     `orm:"pk;auto" json:"id" description:"批次记录唯一标识"`                       // 批次ID，主键
	BatchNumber string    `orm:"size(50);unique" json:"batchNumber" description:"批次唯一编号"`        // 批次号，唯一
	Name        string    `orm:"size(100)" json:"name" description:"批次名称"`                       // 批次名称
	CardType    string    `orm:"size(20)" json:"cardType" description:"批次内礼品卡类型"`                // 卡类型(电子卡/实体卡)
	FaceValue   float64   `orm:"digits(10);decimals(2)" json:"faceValue" description:"批次内礼品卡面值"` // 面值
	Count       int       `orm:"default(0)" json:"count" description:"批次生成的礼品卡数量"`               // 生成数量
	ValidDays   int       `orm:"default(365)" json:"validDays" description:"礼品卡有效期天数"`           // 有效期(天)
	IssuedBy    int64     `orm:"default(0)" json:"issuedBy" description:"批次发行人员ID"`              // 发行管理员ID
	IssuedAt    time.Time `orm:"type(datetime)" json:"issuedAt" description:"批次发行时间"`            // 发行时间
	Description string    `orm:"size(255);null" json:"description" description:"批次描述信息"`         // 描述信息
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime)" json:"-" description:"创建时间"`        // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;type(datetime)" json:"-" description:"最后更新时间"`          // 更新时间
}

// 移除这里的init函数，因为模型已经在模块的init.go中注册了
// func init() {
// 	// 注册模型到ORM
// 	orm.RegisterModel(new(GiftCard), new(GiftCardTransaction), new(GiftCardBatch))
// }
