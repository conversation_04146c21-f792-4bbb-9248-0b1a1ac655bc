/**
 * 礼品卡模块路由配置
 *
 * 本文件负责配置礼品卡模块的API路由，将请求映射到对应的控制器处理函数。
 * 所有礼品卡相关的路由都在此处注册，保持路由结构清晰。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/giftcard/controllers"
	"o_mall_backend/modules/giftcard/services/impl"
	pointsimpl "o_mall_backend/modules/points/services/impl"
)

// InitRouters 初始化礼品卡模块路由
func InitRouters() {
	// 创建服务实例
	pointsService := pointsimpl.NewPointsService()
	giftCardService := impl.NewGiftCardService(pointsService)

	// 创建控制器实例
	giftCardController := controllers.NewGiftCardController(giftCardService)

	// 礼品卡相关路由
	web.Router("/api/v1/giftcards", giftCardController, "get:List")
	web.Router("/api/v1/giftcards/:number", giftCardController, "get:GetByNumber")

	// 礼品卡激活相关路由
	web.Router("/api/v1/giftcards/activate", giftCardController, "post:Activate")

	// 礼品卡使用相关路由
	web.Router("/api/v1/giftcards/use", giftCardController, "post:Use")

	// 礼品卡充值相关路由
	web.Router("/api/v1/giftcards/recharge", giftCardController, "post:Recharge")

	// 礼品卡交易记录相关路由
	web.Router("/api/v1/giftcards/transactions", giftCardController, "get:ListTransactions")

	// 积分兑换礼品卡相关路由
	web.Router("/api/v1/points/exchange/giftcard", giftCardController, "post:ExchangePointsForGiftCard")

	// 管理员路由
	web.Router("/api/v1/admin/giftcards", giftCardController, "post:Create;get:List")
	web.Router("/api/v1/admin/giftcards/batch", giftCardController, "post:BatchCreate;get:ListBatches")
	web.Router("/api/v1/admin/giftcards/batch/:number", giftCardController, "get:GetBatchByNumber")
}
