/**
 * 礼品卡服务接口定义
 *
 * 本文件定义了礼品卡系统的服务层接口，包括礼品卡创建、激活、使用、查询等功能。
 * 提供了清晰的业务操作接口，便于不同实现的扩展。
 */

package services

import (
	"context"
	
	"o_mall_backend/modules/giftcard/dto"
	"o_mall_backend/modules/giftcard/models"
)

// GiftCardService 礼品卡服务接口
type GiftCardService interface {
	// CreateGiftCard 创建单张礼品卡
	CreateGiftCard(ctx context.Context, req *dto.CreateGiftCardRequest) (*dto.GiftCardResponse, error)
	
	// BatchCreateGiftCards 批量创建礼品卡
	BatchCreateGiftCards(ctx context.Context, req *dto.BatchCreateGiftCardRequest) (*dto.GiftCardBatchResponse, error)
	
	// ActivateGiftCard 激活礼品卡
	ActivateGiftCard(ctx context.Context, req *dto.ActivateGiftCardRequest) (*dto.GiftCardOperationResponse, error)
	
	// UseGiftCard 使用礼品卡
	UseGiftCard(ctx context.Context, req *dto.UseGiftCardRequest) (*dto.GiftCardOperationResponse, error)
	
	// RechargeGiftCard 充值礼品卡
	RechargeGiftCard(ctx context.Context, req *dto.RechargeGiftCardRequest) (*dto.GiftCardOperationResponse, error)
	
	// GetGiftCardByNumber 根据卡号获取礼品卡信息
	GetGiftCardByNumber(ctx context.Context, req *dto.GetGiftCardRequest) (*dto.GiftCardResponse, error)
	
	// GetUserGiftCards 获取用户的礼品卡列表
	GetUserGiftCards(ctx context.Context, req *dto.GetUserGiftCardsRequest) ([]*dto.GiftCardResponse, error)
	
	// GetGiftCardTransactions 获取礼品卡交易记录
	GetGiftCardTransactions(ctx context.Context, req *dto.GetGiftCardTransactionsRequest) (*dto.GiftCardTransactionListResponse, error)
	
	// GetGiftCardBatches 获取礼品卡批次列表
	GetGiftCardBatches(ctx context.Context, page, pageSize int) ([]*dto.GiftCardBatchResponse, error)
	
	// GetGiftCardBatchByNumber 根据批次号获取批次信息
	GetGiftCardBatchByNumber(ctx context.Context, batchNumber string) (*dto.GiftCardBatchResponse, error)
	
	// RefundGiftCard 退款到礼品卡
	RefundGiftCard(ctx context.Context, cardNumber string, amount float64, orderID string, description string) (*dto.GiftCardOperationResponse, error)
	
	// ExchangePointsForGiftCard 积分兑换礼品卡
	ExchangePointsForGiftCard(ctx context.Context, req *dto.ExchangePointsForGiftCardRequest) (*dto.ExchangePointsForGiftCardResponse, error)
	
	// CheckExpiredGiftCards 检查已过期礼品卡
	CheckExpiredGiftCards(ctx context.Context) ([]*models.GiftCard, error)
	
	// ProcessExpiredGiftCards 处理过期礼品卡
	ProcessExpiredGiftCards(ctx context.Context) error
}
