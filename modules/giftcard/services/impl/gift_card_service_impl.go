/**
 * 礼品卡服务实现
 *
 * 本文件实现了礼品卡系统的服务层接口，提供礼品卡创建、激活、使用、查询等功能的具体实现。
 * 包括礼品卡的管理、交易处理和积分兑换等核心业务逻辑。
 */

package impl

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/giftcard/constants"
	"o_mall_backend/modules/giftcard/core"
	"o_mall_backend/modules/giftcard/dto"
	"o_mall_backend/modules/giftcard/models"
	"o_mall_backend/modules/giftcard/services"
	pointsDto "o_mall_backend/modules/points/dto"
	pointsServices "o_mall_backend/modules/points/services"
)

// GiftCardServiceImpl 礼品卡服务实现
type GiftCardServiceImpl struct {
	pointsService pointsServices.PointsService
}

// NewGiftCardService 创建礼品卡服务实例
func NewGiftCardService(pointsService pointsServices.PointsService) services.GiftCardService {
	return &GiftCardServiceImpl{
		pointsService: pointsService,
	}
}

// generateCardNumber 生成礼品卡号
func (s *GiftCardServiceImpl) generateCardNumber(prefix string) (string, error) {
	// 生成16位数字 (格式: XXXX-XXXX-XXXX-XXXX)
	var cardNumber strings.Builder

	if prefix == "" {
		prefix = "GC"
	}

	cardNumber.WriteString(prefix)

	// 添加12个随机数字，分成3组，每组4个数字
	for i := 0; i < 3; i++ {
		cardNumber.WriteString("-")
		for j := 0; j < 4; j++ {
			n, err := rand.Int(rand.Reader, big.NewInt(10))
			if err != nil {
				return "", err
			}
			cardNumber.WriteString(fmt.Sprintf("%d", n))
		}
	}

	return cardNumber.String(), nil
}

// generateCardSecret 生成礼品卡密钥
func (s *GiftCardServiceImpl) generateCardSecret() (string, error) {
	// 生成6位数字密钥
	var cardSecret strings.Builder

	for i := 0; i < 6; i++ {
		n, err := rand.Int(rand.Reader, big.NewInt(10))
		if err != nil {
			return "", err
		}
		cardSecret.WriteString(fmt.Sprintf("%d", n))
	}

	return cardSecret.String(), nil
}

// CreateGiftCard 创建单张礼品卡
func (s *GiftCardServiceImpl) CreateGiftCard(ctx context.Context, req *dto.CreateGiftCardRequest) (*dto.GiftCardResponse, error) {
	if req == nil || req.Amount <= 0 || req.ValidDays <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	o := orm.NewOrm()

	// 生成卡号和密钥
	cardNumber, err := s.generateCardNumber("GC")
	if err != nil {
		logs.Error("生成礼品卡号失败: %v", err)
		return nil, fmt.Errorf("生成礼品卡号失败: %v", err)
	}

	cardSecret, err := s.generateCardSecret()
	if err != nil {
		logs.Error("生成礼品卡密钥失败: %v", err)
		return nil, fmt.Errorf("生成礼品卡密钥失败: %v", err)
	}

	// 创建礼品卡
	now := time.Now()
	expireAt := now.AddDate(0, 0, req.ValidDays)

	giftCard := &models.GiftCard{
		CardNumber:  cardNumber,
		CardSecret:  cardSecret,
		Amount:      req.Amount,
		Balance:     req.Amount,                       // 初始余额等于面额
		Status:      constants.GiftCardStatusInactive, // 初始状态为未激活
		Type:        req.Type,
		Description: req.Description,
		IssuedBy:    req.IssuedBy,
		IssuedAt:    now,
		ExpireAt:    expireAt,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 插入数据库
	id, err := o.Insert(giftCard)
	if err != nil {
		logs.Error("创建礼品卡失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡失败: %v", err)
	}

	giftCard.ID = id

	// 转换为响应对象
	response := &dto.GiftCardResponse{
		ID:          giftCard.ID,
		CardNumber:  giftCard.CardNumber,
		Amount:      giftCard.Amount,
		Balance:     giftCard.Balance,
		Status:      giftCard.Status,
		Type:        giftCard.Type,
		IssuedAt:    giftCard.IssuedAt,
		ExpireAt:    giftCard.ExpireAt,
		Description: giftCard.Description,
	}

	return response, nil
}

// BatchCreateGiftCards 批量创建礼品卡
func (s *GiftCardServiceImpl) BatchCreateGiftCards(ctx context.Context, req *dto.BatchCreateGiftCardRequest) (*dto.GiftCardBatchResponse, error) {
	if req == nil || req.FaceValue <= 0 || req.Count <= 0 || req.ValidDays <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 使用事务管理器
	txManager := core.NewTransactionManager()
	err := txManager.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("批量创建礼品卡失败: %v", err)
	}
	defer func() {
		if err != nil {
			txManager.Rollback()
		}
	}()

	tx := txManager.GetTx().(orm.TxOrmer)

	// 生成批次号
	batchNumber := fmt.Sprintf("BATCH-%s", time.Now().Format("20060102150405"))

	// 创建批次记录
	now := time.Now()
	batch := &models.GiftCardBatch{
		BatchNumber: batchNumber,
		Name:        req.BatchName,
		CardType:    req.CardType,
		FaceValue:   req.FaceValue,
		Count:       req.Count,
		ValidDays:   req.ValidDays,
		IssuedBy:    req.IssuedBy,
		IssuedAt:    now,
		Description: req.Description,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 插入批次记录
	batchID, err := tx.Insert(batch)
	if err != nil {
		logs.Error("创建礼品卡批次失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡批次失败: %v", err)
	}

	batch.ID = batchID

	// 批量创建礼品卡
	expireAt := now.AddDate(0, 0, req.ValidDays)

	for i := 0; i < req.Count; i++ {
		// 生成卡号和密钥
		cardNumber, err := s.generateCardNumber("GC")
		if err != nil {
			logs.Error("生成礼品卡号失败: %v", err)
			return nil, fmt.Errorf("生成礼品卡号失败: %v", err)
		}

		cardSecret, err := s.generateCardSecret()
		if err != nil {
			logs.Error("生成礼品卡密钥失败: %v", err)
			return nil, fmt.Errorf("生成礼品卡密钥失败: %v", err)
		}

		// 创建礼品卡
		giftCard := &models.GiftCard{
			CardNumber:  cardNumber,
			CardSecret:  cardSecret,
			Amount:      req.FaceValue,
			Balance:     req.FaceValue,
			Status:      constants.GiftCardStatusInactive,
			Type:        req.CardType,
			BatchNumber: batchNumber,
			Description: req.Description,
			IssuedBy:    req.IssuedBy,
			IssuedAt:    now,
			ExpireAt:    expireAt,
			CreatedAt:   now,
			UpdatedAt:   now,
		}

		_, err = tx.Insert(giftCard)
		if err != nil {
			logs.Error("批量创建礼品卡失败: %v", err)
			return nil, fmt.Errorf("批量创建礼品卡失败: %v", err)
		}
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 转换为响应对象
	response := &dto.GiftCardBatchResponse{
		BatchNumber: batch.BatchNumber,
		Name:        batch.Name,
		CardType:    batch.CardType,
		FaceValue:   batch.FaceValue,
		Count:       batch.Count,
		ValidDays:   batch.ValidDays,
		IssuedAt:    batch.IssuedAt,
		Description: batch.Description,
	}

	return response, nil
}

// ActivateGiftCard 激活礼品卡
func (s *GiftCardServiceImpl) ActivateGiftCard(ctx context.Context, req *dto.ActivateGiftCardRequest) (*dto.GiftCardOperationResponse, error) {
	if req == nil || req.CardNumber == "" || req.CardSecret == "" || req.UserID <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 使用事务管理器
	txManager := core.NewTransactionManager()
	err := txManager.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("激活礼品卡失败: %v", err)
	}
	defer func() {
		if err != nil {
			txManager.Rollback()
		}
	}()

	tx := txManager.GetTx().(orm.TxOrmer)

	// 查询礼品卡
	giftCard := &models.GiftCard{}
	err = tx.QueryTable(new(models.GiftCard)).Filter("card_number", req.CardNumber).One(giftCard)

	if err == orm.ErrNoRows {
		return nil, errors.New("礼品卡不存在")
	}

	if err != nil {
		logs.Error("查询礼品卡失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡失败: %v", err)
	}

	// 验证密钥
	if giftCard.CardSecret != req.CardSecret {
		return nil, errors.New("礼品卡密钥不正确")
	}

	// 检查状态
	if giftCard.Status != constants.GiftCardStatusInactive {
		return nil, errors.New("礼品卡已激活或已过期")
	}

	// 检查是否过期
	if time.Now().After(giftCard.ExpireAt) {
		return nil, errors.New("礼品卡已过期")
	}

	// 更新礼品卡状态
	giftCard.Status = constants.GiftCardStatusActive
	giftCard.ActivatedAt = time.Now()
	giftCard.OwnedBy = req.UserID
	giftCard.UpdatedAt = time.Now()

	_, err = tx.Update(giftCard)
	if err != nil {
		logs.Error("更新礼品卡状态失败: %v", err)
		return nil, fmt.Errorf("更新礼品卡状态失败: %v", err)
	}

	// 创建激活交易记录
	transaction := &models.GiftCardTransaction{
		CardID:      giftCard.ID,
		CardNumber:  giftCard.CardNumber,
		UserID:      req.UserID,
		Amount:      0, // 激活不涉及金额变动
		Balance:     giftCard.Balance,
		Type:        constants.GiftCardTransactionTypeActivation,
		Description: "礼品卡激活",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err = tx.Insert(transaction)
	if err != nil {
		logs.Error("创建礼品卡交易记录失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡交易记录失败: %v", err)
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 返回响应
	response := &dto.GiftCardOperationResponse{
		Success:        true,
		CardNumber:     giftCard.CardNumber,
		CardAmount:     giftCard.Amount,
		CurrentBalance: giftCard.Balance,
		Message:        "礼品卡激活成功",
	}

	return response, nil
}

// UseGiftCard 使用礼品卡
func (s *GiftCardServiceImpl) UseGiftCard(ctx context.Context, req *dto.UseGiftCardRequest) (*dto.GiftCardOperationResponse, error) {
	if req == nil || req.CardNumber == "" || req.Amount <= 0 || req.UserID <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 使用事务管理器
	txManager := core.NewTransactionManager()
	err := txManager.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("使用礼品卡失败: %v", err)
	}
	defer func() {
		if err != nil {
			txManager.Rollback()
		}
	}()

	tx := txManager.GetTx().(orm.TxOrmer)

	// 查询礼品卡
	giftCard := &models.GiftCard{}
	err = tx.QueryTable(new(models.GiftCard)).Filter("card_number", req.CardNumber).One(giftCard)

	if err == orm.ErrNoRows {
		return nil, errors.New("礼品卡不存在")
	}

	if err != nil {
		logs.Error("查询礼品卡失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡失败: %v", err)
	}

	// 检查状态
	if giftCard.Status != constants.GiftCardStatusActive {
		return nil, errors.New("礼品卡未激活或已过期")
	}

	// 检查所有权
	if giftCard.OwnedBy != req.UserID {
		return nil, errors.New("礼品卡不属于该用户")
	}

	// 检查余额
	if giftCard.Balance < req.Amount {
		return nil, errors.New("礼品卡余额不足")
	}

	// 更新礼品卡余额
	giftCard.Balance -= req.Amount
	giftCard.UpdatedAt = time.Now()

	// 如果余额为0，将状态设置为已用完
	if giftCard.Balance == 0 {
		giftCard.Status = constants.GiftCardStatusUsed
	}

	_, err = tx.Update(giftCard)
	if err != nil {
		logs.Error("更新礼品卡余额失败: %v", err)
		return nil, fmt.Errorf("更新礼品卡余额失败: %v", err)
	}

	// 创建使用交易记录
	transaction := &models.GiftCardTransaction{
		CardID:      giftCard.ID,
		CardNumber:  giftCard.CardNumber,
		UserID:      req.UserID,
		Amount:      -req.Amount, // 消费为负数
		Balance:     giftCard.Balance,
		Type:        constants.GiftCardTransactionTypeConsume,
		OrderID:     req.OrderID,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err = tx.Insert(transaction)
	if err != nil {
		logs.Error("创建礼品卡交易记录失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡交易记录失败: %v", err)
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 返回响应
	response := &dto.GiftCardOperationResponse{
		Success:        true,
		CardNumber:     giftCard.CardNumber,
		CardAmount:     giftCard.Amount,
		CurrentBalance: giftCard.Balance,
		Message:        fmt.Sprintf("礼品卡消费成功，消费金额: %.2f，当前余额: %.2f", req.Amount, giftCard.Balance),
	}

	return response, nil
}

// RechargeGiftCard 充值礼品卡
func (s *GiftCardServiceImpl) RechargeGiftCard(ctx context.Context, req *dto.RechargeGiftCardRequest) (*dto.GiftCardOperationResponse, error) {
	if req == nil || req.CardNumber == "" || req.Amount <= 0 || req.UserID <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 使用事务管理器
	txManager := core.NewTransactionManager()
	err := txManager.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("充值礼品卡失败: %v", err)
	}
	defer func() {
		if err != nil {
			txManager.Rollback()
		}
	}()

	tx := txManager.GetTx().(orm.TxOrmer)

	// 查询礼品卡
	giftCard := &models.GiftCard{}
	err = tx.QueryTable(new(models.GiftCard)).Filter("card_number", req.CardNumber).One(giftCard)

	if err == orm.ErrNoRows {
		return nil, errors.New("礼品卡不存在")
	}

	if err != nil {
		logs.Error("查询礼品卡失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡失败: %v", err)
	}

	// 检查状态
	if giftCard.Status != constants.GiftCardStatusActive && giftCard.Status != constants.GiftCardStatusUsed {
		return nil, errors.New("礼品卡未激活或已过期")
	}

	// 检查所有权
	if giftCard.OwnedBy != req.UserID {
		return nil, errors.New("礼品卡不属于该用户")
	}

	// 更新礼品卡余额
	giftCard.Balance += req.Amount
	giftCard.UpdatedAt = time.Now()

	// 如果状态为已用完，更新为激活状态
	if giftCard.Status == constants.GiftCardStatusUsed {
		giftCard.Status = constants.GiftCardStatusActive
	}

	_, err = tx.Update(giftCard)
	if err != nil {
		logs.Error("更新礼品卡余额失败: %v", err)
		return nil, fmt.Errorf("更新礼品卡余额失败: %v", err)
	}

	// 创建充值交易记录
	transaction := &models.GiftCardTransaction{
		CardID:      giftCard.ID,
		CardNumber:  giftCard.CardNumber,
		UserID:      req.UserID,
		Amount:      req.Amount, // 充值为正数
		Balance:     giftCard.Balance,
		Type:        constants.GiftCardTransactionTypeRecharge,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err = tx.Insert(transaction)
	if err != nil {
		logs.Error("创建礼品卡交易记录失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡交易记录失败: %v", err)
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 返回响应
	response := &dto.GiftCardOperationResponse{
		Success:        true,
		CardNumber:     giftCard.CardNumber,
		CardAmount:     giftCard.Amount,
		CurrentBalance: giftCard.Balance,
		Message:        fmt.Sprintf("礼品卡充值成功，充值金额: %.2f，当前余额: %.2f", req.Amount, giftCard.Balance),
	}

	return response, nil
}

// RefundGiftCard 退款礼品卡
func (s *GiftCardServiceImpl) RefundGiftCard(ctx context.Context, cardNumber string, amount float64, orderID string, description string) (*dto.GiftCardOperationResponse, error) {
	if cardNumber == "" {
		return nil, errors.New("礼品卡卡号不能为空")
	}

	if amount <= 0 {
		return nil, errors.New("退款金额必须大于0")
	}

	// 使用事务管理器
	txManager := core.NewTransactionManager()
	err := txManager.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("退款失败: %v", err)
	}
	defer func() {
		if err != nil {
			txManager.Rollback()
		}
	}()

	tx := txManager.GetTx().(orm.TxOrmer)

	// 查询礼品卡
	giftCard := &models.GiftCard{}
	err = tx.QueryTable(new(models.GiftCard)).Filter("card_number", cardNumber).One(giftCard)

	if err == orm.ErrNoRows {
		return nil, errors.New("礼品卡不存在")
	}

	if err != nil {
		logs.Error("查询礼品卡失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡失败: %v", err)
	}

	// 检查礼品卡状态
	if giftCard.Status != constants.GiftCardStatusActive {
		return nil, errors.New("礼品卡不是激活状态，无法退款")
	}

	// 更新礼品卡余额
	giftCard.Balance = giftCard.Balance - amount
	_, err = tx.Update(giftCard)
	if err != nil {
		logs.Error("更新礼品卡余额失败: %v", err)
		return nil, fmt.Errorf("更新礼品卡余额失败: %v", err)
	}

	// 创建交易记录
	transaction := &models.GiftCardTransaction{
		CardID:      giftCard.ID,
		CardNumber:  cardNumber,
		UserID:      giftCard.OwnedBy,
		Amount:      -amount, // 使用负数表示退款
		Balance:     giftCard.Balance,
		Type:        constants.GiftCardTransactionTypeRefund,
		OrderID:     orderID,
		Description: description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err = tx.Insert(transaction)
	if err != nil {
		logs.Error("创建礼品卡交易记录失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡交易记录失败: %v", err)
	}

	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 构建响应
	response := &dto.GiftCardOperationResponse{
		Success:        true,
		CardNumber:     cardNumber,
		CardAmount:     amount,
		CurrentBalance: giftCard.Balance,
		Message:        "退款成功",
	}

	return response, nil
}

// ExchangePointsForGiftCard 积分兑换礼品卡
func (s *GiftCardServiceImpl) ExchangePointsForGiftCard(ctx context.Context, req *dto.ExchangePointsForGiftCardRequest) (*dto.ExchangePointsForGiftCardResponse, error) {
	if req == nil || req.UserID <= 0 || req.PointsAmount <= 0 || req.CardAmount <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 使用事务管理器
	txManager := core.NewTransactionManager()
	err := txManager.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("开始事务失败: %v", err)
	}
	defer func() {
		if err != nil {
			txManager.Rollback()
		}
	}()

	tx := txManager.GetTx().(orm.TxOrmer)

	// 1. 扣减积分
	deductPointsReq := &pointsDto.DeductPointsRequest{
		UserID:      req.UserID,
		Points:      req.PointsAmount,
		Source:      "exchange",
		Description: "兑换礼品卡",
	}

	// 调用积分服务扣减积分
	_, err = s.pointsService.DeductPoints(ctx, deductPointsReq)
	if err != nil {
		logs.Error("扣减积分失败: %v", err)
		return nil, fmt.Errorf("扣减积分失败: %v", err)
	}

	// 2. 创建礼品卡
	createCardReq := &dto.CreateGiftCardRequest{
		Amount:      req.CardAmount,
		Type:        constants.GiftCardTypeElectronic, // 默认为电子卡
		Description: fmt.Sprintf("积分兑换 (%d积分)", req.PointsAmount),
		ValidDays:   365, // 默认有效期1年
		IssuedBy:    0,   // 系统自动创建
	}

	cardResp, err := s.CreateGiftCard(ctx, createCardReq)
	if err != nil {
		logs.Error("创建礼品卡失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡失败: %v", err)
	}

	// 3. 自动激活礼品卡并绑定到用户
	giftCard := &models.GiftCard{}
	err = tx.QueryTable(new(models.GiftCard)).Filter("card_number", cardResp.CardNumber).One(giftCard)
	if err != nil {
		logs.Error("查询新创建的礼品卡失败: %v", err)
		return nil, fmt.Errorf("查询新创建的礼品卡失败: %v", err)
	}

	// 更新礼品卡状态
	giftCard.Status = constants.GiftCardStatusActive
	giftCard.ActivatedAt = time.Now()
	giftCard.OwnedBy = req.UserID
	giftCard.IsPointsExch = true
	giftCard.PointsUsed = req.PointsAmount
	giftCard.UpdatedAt = time.Now()

	_, err = tx.Update(giftCard)
	if err != nil {
		logs.Error("更新礼品卡状态失败: %v", err)
		return nil, fmt.Errorf("更新礼品卡状态失败: %v", err)
	}

	// 创建激活交易记录
	transaction := &models.GiftCardTransaction{
		CardID:      giftCard.ID,
		CardNumber:  giftCard.CardNumber,
		UserID:      req.UserID,
		Amount:      0, // 激活不涉及金额变动
		Balance:     giftCard.Balance,
		Type:        constants.GiftCardTransactionTypeActivation,
		Description: "积分兑换礼品卡并自动激活",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err = tx.Insert(transaction)
	if err != nil {
		logs.Error("创建礼品卡交易记录失败: %v", err)
		return nil, fmt.Errorf("创建礼品卡交易记录失败: %v", err)
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 返回响应
	response := &dto.ExchangePointsForGiftCardResponse{
		Success:      true,
		UserID:       req.UserID,
		PointsAmount: req.PointsAmount,
		CardNumber:   giftCard.CardNumber,
		CardAmount:   req.CardAmount,
		Message:      fmt.Sprintf("成功用%d积分兑换%.2f元礼品卡", req.PointsAmount, req.CardAmount),
	}

	return response, nil
}

// CheckExpiredGiftCards 检查已过期礼品卡
func (s *GiftCardServiceImpl) CheckExpiredGiftCards(ctx context.Context) ([]*models.GiftCard, error) {
	o := orm.NewOrm()
	var cards []*models.GiftCard

	// 查询已过期但状态仍为active的礼品卡
	now := time.Now()
	_, err := o.QueryTable(new(models.GiftCard)).
		Filter("expire_at__lt", now).
		Filter("status", constants.GiftCardStatusActive).
		All(&cards)

	if err != nil {
		logs.Error("查询过期礼品卡失败: %v", err)
		return nil, fmt.Errorf("查询过期礼品卡失败: %v", err)
	}

	return cards, nil
}

// ProcessExpiredGiftCards 处理过期礼品卡
func (s *GiftCardServiceImpl) ProcessExpiredGiftCards(ctx context.Context) error {
	// 查询过期的礼品卡
	expiredCards, err := s.CheckExpiredGiftCards(ctx)
	if err != nil {
		return err
	}

	// 处理每张过期礼品卡
	for _, card := range expiredCards {
		// 使用事务管理器
		txManager := core.NewTransactionManager()
		err := txManager.Begin()
		if err != nil {
			logs.Error("开始事务失败: %v", err)
			continue
		}

		tx := txManager.GetTx().(orm.TxOrmer)
		now := time.Now()

		// 更新礼品卡状态为过期
		card.Status = constants.GiftCardStatusExpired
		card.UpdatedAt = now

		_, err = tx.Update(card)
		if err != nil {
			txManager.Rollback()
			logs.Error("更新礼品卡状态失败: %v", err)
			continue
		}

		// 创建过期交易记录
		transaction := &models.GiftCardTransaction{
			CardID:      card.ID,
			CardNumber:  card.CardNumber,
			UserID:      card.OwnedBy,
			Amount:      0, // 过期不涉及金额变动
			Balance:     card.Balance,
			Type:        constants.GiftCardTransactionTypeExpire,
			Description: "礼品卡已过期",
			CreatedAt:   now,
			UpdatedAt:   now,
		}

		_, err = tx.Insert(transaction)
		if err != nil {
			txManager.Rollback()
			logs.Error("创建礼品卡交易记录失败: %v", err)
			continue
		}

		// 提交事务
		err = txManager.Commit()
		if err != nil {
			txManager.Rollback()
			logs.Error("提交事务失败: %v", err)
			continue
		}

		logs.Info("礼品卡 %s 已过期处理完成", card.CardNumber)
	}

	return nil
}

// GetGiftCardBatchByNumber 根据批次号获取批次信息
func (s *GiftCardServiceImpl) GetGiftCardBatchByNumber(ctx context.Context, batchNumber string) (*dto.GiftCardBatchResponse, error) {
	if batchNumber == "" {
		return nil, errors.New("批次号不能为空")
	}

	o := orm.NewOrm()
	batch := &models.GiftCardBatch{}

	// 查询批次信息
	err := o.QueryTable(new(models.GiftCardBatch)).Filter("batch_number", batchNumber).One(batch)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, fmt.Errorf("批次不存在: %s", batchNumber)
		}
		logs.Error("查询礼品卡批次失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡批次失败: %v", err)
	}

	// 转换为响应对象
	response := &dto.GiftCardBatchResponse{
		BatchNumber: batch.BatchNumber,
		Name:        batch.Name,
		CardType:    batch.CardType,
		FaceValue:   batch.FaceValue,
		Count:       batch.Count,
		ValidDays:   batch.ValidDays,
		IssuedAt:    batch.IssuedAt,
		Description: batch.Description,
	}

	return response, nil
}

// GetGiftCardBatches 获取礼品卡批次列表
func (s *GiftCardServiceImpl) GetGiftCardBatches(ctx context.Context, page, pageSize int) ([]*dto.GiftCardBatchResponse, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	o := orm.NewOrm()
	var batches []*models.GiftCardBatch

	offset := (page - 1) * pageSize

	_, err := o.QueryTable(new(models.GiftCardBatch)).
		OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&batches)

	if err != nil {
		logs.Error("查询礼品卡批次列表失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡批次失败: %v", err)
	}

	var result []*dto.GiftCardBatchResponse
	for _, batch := range batches {
		// 不需要统计这些变量，移除未使用的变量声明
		result = append(result, &dto.GiftCardBatchResponse{
			BatchNumber: batch.BatchNumber,
			Name:        batch.Name,
			CardType:    batch.CardType,
			FaceValue:   batch.FaceValue,
			Count:       batch.Count,
			ValidDays:   batch.ValidDays,
			IssuedAt:    batch.IssuedAt,
			Description: batch.Description,
		})
	}

	return result, nil
}

// GetGiftCardByNumber 根据卡号获取礼品卡信息
func (s *GiftCardServiceImpl) GetGiftCardByNumber(ctx context.Context, req *dto.GetGiftCardRequest) (*dto.GiftCardResponse, error) {
	if req == nil || req.CardNumber == "" {
		return nil, errors.New("无效的请求参数")
	}

	o := orm.NewOrm()
	giftCard := &models.GiftCard{CardNumber: req.CardNumber}
	err := o.QueryTable(new(models.GiftCard)).Filter("card_number", req.CardNumber).One(giftCard)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没找到返回nil，而不是错误
		}
		logs.Error("获取礼品卡信息失败: %v", err)
		return nil, fmt.Errorf("获取礼品卡信息失败: %v", err)
	}

	// 如果指定了用户ID，则检查权限
	if req.UserID > 0 && giftCard.OwnedBy > 0 && giftCard.OwnedBy != req.UserID {
		// 非管理员情况下，只能查看自己的礼品卡
		return nil, errors.New("没有权限查看该礼品卡")
	}

	// 返回礼品卡信息
	return &dto.GiftCardResponse{
		ID:          giftCard.ID,
		CardNumber:  giftCard.CardNumber,
		Amount:      giftCard.Amount,
		Balance:     giftCard.Balance,
		Status:      giftCard.Status,
		Type:        giftCard.Type,
		OwnedBy:     giftCard.OwnedBy,
		IssuedAt:    giftCard.IssuedAt,
		ActivatedAt: giftCard.ActivatedAt,
		ExpireAt:    giftCard.ExpireAt,
		Description: giftCard.Description,
	}, nil
}

// GetGiftCardTransactions 获取礼品卡交易记录
func (s *GiftCardServiceImpl) GetGiftCardTransactions(ctx context.Context, req *dto.GetGiftCardTransactionsRequest) (*dto.GiftCardTransactionListResponse, error) {
	if req == nil || req.CardNumber == "" {
		return nil, errors.New("无效的请求参数")
	}

	// 设置默认分页参数
	pageNum := req.PageNum
	if pageNum <= 0 {
		pageNum = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	o := orm.NewOrm()

	// 首先验证卡号是否存在
	giftCard := &models.GiftCard{CardNumber: req.CardNumber}
	err := o.QueryTable(new(models.GiftCard)).Filter("card_number", req.CardNumber).One(giftCard)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, fmt.Errorf("礼品卡不存在")
		}
		logs.Error("查询礼品卡信息失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡信息失败: %v", err)
	}

	// 权限检查
	if req.UserID > 0 && giftCard.OwnedBy > 0 && giftCard.OwnedBy != req.UserID {
		// 非管理员情况下，只能查看自己的礼品卡交易记录
		return nil, errors.New("没有权限查看该礼品卡的交易记录")
	}

	// 查询交易记录总数
	var count int64
	count, err = o.QueryTable(new(models.GiftCardTransaction)).
		Filter("card_number", req.CardNumber).
		Count()

	if err != nil {
		logs.Error("查询礼品卡交易记录总数失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡交易记录失败: %v", err)
	}

	// 查询交易记录
	var transactions []*models.GiftCardTransaction
	offset := (pageNum - 1) * pageSize

	_, err = o.QueryTable(new(models.GiftCardTransaction)).
		Filter("card_number", req.CardNumber).
		OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&transactions)

	if err != nil {
		logs.Error("查询礼品卡交易记录失败: %v", err)
		return nil, fmt.Errorf("查询礼品卡交易记录失败: %v", err)
	}

	// 构造响应对象
	var transactionResponses []dto.GiftCardTransactionResponse
	for _, trans := range transactions {
		transactionResponses = append(transactionResponses, dto.GiftCardTransactionResponse{
			ID:          trans.ID,
			CardNumber:  trans.CardNumber,
			UserID:      trans.UserID,
			Amount:      trans.Amount,
			Balance:     trans.Balance,
			Type:        trans.Type,
			OrderID:     trans.OrderID,
			Description: trans.Description,
			CreatedAt:   trans.CreatedAt,
		})
	}

	return &dto.GiftCardTransactionListResponse{
		Total:       int(count),
		CurrentPage: pageNum,
		PageSize:    pageSize,
		Data:        transactionResponses,
	}, nil
}

// GetUserGiftCards 获取用户所拥有的礼品卡列表
func (s *GiftCardServiceImpl) GetUserGiftCards(ctx context.Context, req *dto.GetUserGiftCardsRequest) ([]*dto.GiftCardResponse, error) {
	if req == nil || req.UserID <= 0 {
		return nil, errors.New("无效的用户ID")
	}

	// 设置默认分页参数
	pageNum := req.PageNum
	if pageNum <= 0 {
		pageNum = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	o := orm.NewOrm()

	// 查询礼品卡列表
	var giftCards []*models.GiftCard
	offset := (pageNum - 1) * pageSize

	_, err := o.QueryTable(new(models.GiftCard)).
		Filter("owned_by", req.UserID).
		OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&giftCards)

	if err != nil {
		logs.Error("查询用户礼品卡列表失败: %v", err)
		return nil, fmt.Errorf("查询用户礼品卡失败: %v", err)
	}

	// 构造响应
	var giftCardResponses []*dto.GiftCardResponse
	for _, card := range giftCards {
		giftCardResponses = append(giftCardResponses, &dto.GiftCardResponse{
			ID:          card.ID,
			CardNumber:  card.CardNumber,
			Amount:      card.Amount,
			Balance:     card.Balance,
			Status:      card.Status,
			Type:        card.Type,
			OwnedBy:     card.OwnedBy,
			IssuedAt:    card.IssuedAt,
			ActivatedAt: card.ActivatedAt,
			ExpireAt:    card.ExpireAt,
			Description: card.Description,
		})
	}

	return giftCardResponses, nil
}
