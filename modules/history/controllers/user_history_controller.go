/**
 * 用户历史记录控制器
 *
 * 本文件实现了用户历史记录相关的HTTP接口，处理前端请求并调用服务层。
 * 提供历史记录的增删改查、统计分析等功能。
 */

package controllers

import (
	"strconv"

	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/history/dto"
	"o_mall_backend/modules/history/services"
	"o_mall_backend/utils/common"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// UserHistoryController 用户历史记录控制器
type UserHistoryController struct {
	web.Controller
	historyService services.UserHistoryService
}

// 初始化控制器
func (c *UserHistoryController) Prepare() {
	c.historyService = services.NewUserHistoryService()
}

// ParseRequest 解析请求数据
func (c *UserHistoryController) ParseRequest(req interface{}) error {
	logs.Info("[History] ParseRequest 被调用")
	logs.Info("[History] Content-Type: %s", c.Ctx.Request.Header.Get("Content-Type"))
	logs.Info("[History] Request Body Length: %d", c.Ctx.Request.ContentLength)

	err := common.ParseRequest(c.Ctx, req)
	if err != nil {
		logs.Error("[History] ParseRequest 失败: %v", err)
	} else {
		logs.Info("[History] ParseRequest 成功")
	}
	return err
}

// Add 添加历史记录
// @router /api/v1/user/secured/history/add [post]
func (c *UserHistoryController) Add() {
	logs.Info("[History] Add 方法被调用")

	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("[History] 获取用户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("[History] 获取到用户ID: %d", userID)

	// 解析请求参数
	var req dto.AddHistoryRequest
	logs.Info("[History] 开始解析请求参数")
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("[History] 参数解析失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}
	logs.Info("[History] 请求参数解析成功: %+v", req)

	// 获取客户端IP
	ip := c.Ctx.Input.IP()
	logs.Info("[History] 客户端IP: %s", ip)

	// 调用服务层添加历史记录
	logs.Info("[History] 调用服务层添加历史记录")
	err = c.historyService.AddHistory(userID, &req, ip)
	if err != nil {
		logs.Error("[History] 添加历史记录失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("[History] 历史记录添加成功")
	result.OK(c.Ctx, "添加成功")
}

// Update 更新历史记录
// @router /api/v1/user/history/update/:id [post]
func (c *UserHistoryController) Update() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取历史记录ID
	historyIDStr := c.Ctx.Input.Param(":id")
	historyID, err := strconv.ParseInt(historyIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的历史记录ID")
		return
	}

	// 解析请求参数
	var req dto.UpdateHistoryRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层更新历史记录
	err = c.historyService.UpdateHistory(userID, historyID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "更新成功")
}

// Delete 删除历史记录
// @router /api/v1/user/history/delete/:id [post]
func (c *UserHistoryController) Delete() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取历史记录ID
	historyIDStr := c.Ctx.Input.Param(":id")
	historyID, err := strconv.ParseInt(historyIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的历史记录ID")
		return
	}

	// 调用服务层删除历史记录
	err = c.historyService.DeleteHistory(userID, historyID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "删除成功")
}

// BatchDelete 批量删除历史记录
// @router /api/v1/user/history/batch-delete [post]
func (c *UserHistoryController) BatchDelete() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.BatchDeleteRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层批量删除历史记录
	err = c.historyService.BatchDeleteHistory(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "批量删除成功")
}

// Detail 获取历史记录详情
// @router /api/v1/user/history/detail/:id [get]
func (c *UserHistoryController) Detail() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取历史记录ID
	historyIDStr := c.Ctx.Input.Param(":id")
	historyID, err := strconv.ParseInt(historyIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的历史记录ID")
		return
	}

	// 调用服务层获取历史记录详情
	history, err := c.historyService.GetHistoryDetail(userID, historyID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, history)
}

// List 获取历史记录列表
// @router /api/v1/user/history/list [get]
func (c *UserHistoryController) List() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析查询参数
	var req dto.HistoryQueryRequest
	req.Type = c.GetString("type")
	req.StartDate = c.GetString("start_date")
	req.EndDate = c.GetString("end_date")
	req.Keyword = c.GetString("keyword")

	if page, err := c.GetInt("page"); err == nil && page > 0 {
		req.Page = page
	} else {
		req.Page = 1
	}

	if pageSize, err := c.GetInt("page_size"); err == nil && pageSize > 0 {
		req.PageSize = pageSize
	} else {
		req.PageSize = 20
	}

	// 调用服务层获取历史记录列表
	response, err := c.historyService.ListHistory(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, response)
}

// Search 搜索历史记录
// @router /api/v1/user/history/search [get]
func (c *UserHistoryController) Search() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取搜索参数
	keyword := c.GetString("keyword")
	if keyword == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请输入搜索关键词")
		return
	}

	page, _ := c.GetInt("page")
	if page <= 0 {
		page = 1
	}

	pageSize, _ := c.GetInt("page_size")
	if pageSize <= 0 {
		pageSize = 20
	}

	// 调用服务层搜索历史记录
	response, err := c.historyService.SearchHistory(userID, keyword, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, response)
}

// GetByType 根据类型获取历史记录
// @router /api/v1/user/history/type/:type [get]
func (c *UserHistoryController) GetByType() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取历史记录类型
	historyType := c.Ctx.Input.Param(":type")
	if historyType == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请指定历史记录类型")
		return
	}

	page, _ := c.GetInt("page")
	if page <= 0 {
		page = 1
	}

	pageSize, _ := c.GetInt("page_size")
	if pageSize <= 0 {
		pageSize = 20
	}

	// 调用服务层获取指定类型的历史记录
	response, err := c.historyService.GetHistoryByType(userID, historyType, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, response)
}

// Statistics 获取历史记录统计
// @router /api/v1/user/history/statistics [get]
func (c *UserHistoryController) Statistics() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取统计信息
	statistics, err := c.historyService.GetHistoryStatistics(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, statistics)
}

// Types 获取历史记录类型列表
// @router /api/v1/user/history/types [get]
func (c *UserHistoryController) Types() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取类型列表
	types, err := c.historyService.GetHistoryTypes(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, types)
}

// Analytics 获取历史记录分析
// @router /api/v1/user/history/analytics [get]
func (c *UserHistoryController) Analytics() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取分析周期
	period := c.GetString("period")
	if period == "" {
		period = "week"
	}

	// 调用服务层获取分析数据
	analytics, err := c.historyService.GetHistoryAnalytics(userID, period)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, analytics)
}

// Clear 清空历史记录
// @router /api/v1/user/history/clear [post]
func (c *UserHistoryController) Clear() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.ClearHistoryRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数解析失败")
		return
	}

	// 调用服务层清空历史记录
	err = c.historyService.ClearHistory(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, "清空成功")
}

// Options 处理OPTIONS请求
func (c *UserHistoryController) Options() {
	result.OK(c.Ctx, "OK")
}
