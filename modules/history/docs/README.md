# 用户历史记录模块

## 模块概述

用户历史记录模块用于记录和管理用户在系统中的各种访问行为，包括浏览外卖商品、商城商品、访问商家页面、搜索记录等。该模块提供完整的历史记录管理功能，支持统计分析和数据导出。

## 功能特性

### 核心功能
- **多类型记录**：支持外卖商品、商城商品、商家、分类、搜索、页面等多种类型的历史记录
- **智能去重**：相同类型和目标的记录会自动合并，更新访问次数和最后访问时间
- **详细追踪**：记录用户代理、IP地址、访问平台、来源等详细信息
- **灵活查询**：支持按类型、时间范围、关键词等多种方式查询历史记录

### 统计分析
- **基础统计**：总数、今日、本周、本月访问量统计
- **类型分布**：各类型历史记录的数量和占比分析
- **热门项目**：按访问次数排序的热门内容
- **趋势分析**：历史记录的时间趋势图表
- **活跃分析**：用户活跃时段和活跃日期分析

### 数据管理
- **批量操作**：支持批量删除历史记录
- **定时清理**：自动清理过期的历史记录
- **数据导出**：支持CSV、Excel格式的数据导出
- **隐私保护**：用户可以清空指定类型或时间范围的历史记录

## 数据模型

### 主表：user_history
```sql
CREATE TABLE user_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type VARCHAR(50) NOT NULL,
    target_id BIGINT NOT NULL,
    target_name VARCHAR(255) NOT NULL,
    target_image VARCHAR(500),
    visit_count INT DEFAULT 1,
    last_visit_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_target_id (target_id),
    UNIQUE KEY uk_user_type_target (user_id, type, target_id)
);
```

### 详情表：user_history_detail
```sql
CREATE TABLE user_history_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    history_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    type VARCHAR(50) NOT NULL,
    target_id BIGINT NOT NULL,
    extra_data TEXT,
    user_agent VARCHAR(500),
    ip VARCHAR(50),
    platform VARCHAR(50),
    source VARCHAR(100),
    duration INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_history_id (history_id),
    INDEX idx_user_id (user_id)
);
```

## 历史记录类型

| 类型 | 名称 | 描述 |
|------|------|------|
| takeout_food | 外卖商品 | 用户浏览过的外卖商品 |
| mall_product | 商城商品 | 用户浏览过的商城商品 |
| merchant | 商家 | 用户访问过的商家 |
| category | 分类 | 用户浏览过的分类 |
| search | 搜索记录 | 用户的搜索历史 |
| page | 页面访问 | 用户访问过的页面 |

## API 接口

### 基础操作

#### 添加历史记录
```
POST /api/v1/user/history/add
```

**请求参数：**
```json
{
  "type": "takeout_food",
  "target_id": 12345,
  "target_name": "麻辣香锅",
  "target_image": "https://example.com/image.jpg",
  "extra_data": {
    "price": 28.5,
    "merchant_id": 100
  },
  "user_agent": "Mozilla/5.0...",
  "platform": "web",
  "source": "search",
  "duration": 30
}
```

#### 获取历史记录列表
```
GET /api/v1/user/history/list?type=takeout_food&page=1&page_size=20
```

#### 搜索历史记录
```
GET /api/v1/user/history/search?keyword=麻辣&page=1&page_size=20
```

#### 删除历史记录
```
POST /api/v1/user/history/delete/123
```

#### 批量删除历史记录
```
POST /api/v1/user/history/batch-delete
```

**请求参数：**
```json
{
  "ids": [123, 456, 789]
}
```

### 统计分析

#### 获取统计信息
```
GET /api/v1/user/history/statistics
```

**响应示例：**
```json
{
  "total_count": 1250,
  "today_count": 15,
  "week_count": 89,
  "month_count": 324,
  "type_statistics": [
    {
      "type": "takeout_food",
      "type_name": "外卖商品",
      "count": 650,
      "percentage": 52.0
    }
  ],
  "recent_history": [...],
  "popular_items": [...]
}
```

#### 获取分析数据
```
GET /api/v1/user/history/analytics?period=week
```

#### 获取历史记录类型
```
GET /api/v1/user/history/types
```

### 数据管理

#### 清空历史记录
```
POST /api/v1/user/history/clear
```

**请求参数：**
```json
{
  "type": "takeout_food",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

## 使用示例

### 前端集成示例

```javascript
// 历史记录服务类
class HistoryService {
  // 添加历史记录
  async addHistory(type, targetId, targetName, targetImage, extraData = {}) {
    const data = {
      type,
      target_id: targetId,
      target_name: targetName,
      target_image: targetImage,
      extra_data: extraData,
      user_agent: navigator.userAgent,
      platform: this.getPlatform(),
      source: this.getSource()
    };
    
    return await this.request('/api/v1/user/history/add', 'POST', data);
  }
  
  // 获取历史记录列表
  async getHistoryList(type = '', page = 1, pageSize = 20) {
    const params = { type, page, page_size: pageSize };
    return await this.request('/api/v1/user/history/list', 'GET', params);
  }
  
  // 获取统计信息
  async getStatistics() {
    return await this.request('/api/v1/user/history/statistics', 'GET');
  }
}

// 使用示例
const historyService = new HistoryService();

// 用户浏览商品时记录历史
async function onProductView(product) {
  await historyService.addHistory(
    'takeout_food',
    product.id,
    product.name,
    product.image,
    {
      price: product.price,
      merchant_id: product.merchant_id,
      category_id: product.category_id
    }
  );
}

// 获取用户历史记录
async function loadUserHistory() {
  const response = await historyService.getHistoryList('takeout_food', 1, 20);
  console.log('用户历史记录:', response.data.list);
}
```

### 后端集成示例

```go
// 在商品详情接口中添加历史记录
func (c *ProductController) Detail() {
    // ... 获取商品详情逻辑
    
    // 添加历史记录
    historyService := services.NewUserHistoryService()
    historyReq := &dto.AddHistoryRequest{
        Type:        models.HistoryTypeTakeoutFood,
        TargetID:    product.ID,
        TargetName:  product.Name,
        TargetImage: product.Image,
        ExtraData: map[string]interface{}{
            "price":       product.Price,
            "merchant_id": product.MerchantID,
            "category_id": product.CategoryID,
        },
        Platform: "mobile",
        Source:   "product_list",
    }
    
    go func() {
        err := historyService.AddHistory(userID, historyReq, c.Ctx.Input.IP())
        if err != nil {
            logs.Warn("添加历史记录失败: %v", err)
        }
    }()
    
    // 返回商品详情
    result.OK(c.Ctx, product)
}
```

## 配置说明

模块配置文件位于 `conf/modules/history.conf`，主要配置项：

- `history.retention_days`：历史记录保留天数
- `history.cleanup.enabled`：是否启用自动清理
- `history.cache.enabled`：是否启用缓存
- `history.page.default_size`：默认分页大小
- `history.statistics.enabled`：是否启用统计功能

## 性能优化

1. **数据库索引**：在 user_id、type、target_id 等字段上建立索引
2. **缓存策略**：对统计数据和热门项目进行缓存
3. **异步处理**：历史记录的添加采用异步方式，不影响主业务流程
4. **定时清理**：定期清理过期的历史记录，控制数据量
5. **分页查询**：所有列表查询都支持分页，避免大量数据传输

## 注意事项

1. **隐私保护**：用户可以随时清空自己的历史记录
2. **数据安全**：历史记录只能被记录的用户本人访问
3. **性能考虑**：大量历史记录可能影响查询性能，建议定期清理
4. **存储空间**：详情表会记录每次访问，需要控制存储空间使用

## 扩展功能

1. **推荐系统**：基于历史记录进行个性化推荐
2. **用户画像**：分析用户行为偏好
3. **数据分析**：为运营提供用户行为分析数据
4. **A/B测试**：基于历史记录进行效果评估

## 部署说明

1. **数据库迁移**：执行SQL脚本创建相关表
2. **模块注册**：在主程序中注册历史记录模块
3. **配置文件**：根据需要调整配置参数
4. **定时任务**：配置历史记录清理的定时任务
