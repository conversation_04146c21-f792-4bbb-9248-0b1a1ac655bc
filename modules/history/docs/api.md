# 用户历史记录 API 文档

## 接口概览

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 添加历史记录 | POST | /api/v1/user/history/add | 添加用户访问历史记录 |
| 更新历史记录 | POST | /api/v1/user/history/update/:id | 更新历史记录信息 |
| 删除历史记录 | POST | /api/v1/user/history/delete/:id | 删除单条历史记录 |
| 批量删除 | POST | /api/v1/user/history/batch-delete | 批量删除历史记录 |
| 获取详情 | GET | /api/v1/user/history/detail/:id | 获取历史记录详情 |
| 获取列表 | GET | /api/v1/user/history/list | 获取历史记录列表 |
| 搜索记录 | GET | /api/v1/user/history/search | 搜索历史记录 |
| 按类型获取 | GET | /api/v1/user/history/type/:type | 按类型获取历史记录 |
| 获取统计 | GET | /api/v1/user/history/statistics | 获取统计信息 |
| 获取类型 | GET | /api/v1/user/history/types | 获取历史记录类型 |
| 获取分析 | GET | /api/v1/user/history/analytics | 获取分析数据 |
| 清空记录 | POST | /api/v1/user/history/clear | 清空历史记录 |

## 详细接口说明

### 1. 添加历史记录

**接口地址：** `POST /api/v1/user/history/add`

**接口描述：** 添加用户访问历史记录，如果相同类型和目标的记录已存在，则更新访问次数和最后访问时间。

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
  "type": "takeout_food",                    // 必填，历史记录类型
  "target_id": 12345,                        // 必填，目标对象ID
  "target_name": "麻辣香锅",                  // 必填，目标对象名称
  "target_image": "https://example.com/image.jpg", // 可选，目标对象图片
  "extra_data": {                            // 可选，额外数据
    "price": 28.5,
    "merchant_id": 100,
    "category_id": 5
  },
  "user_agent": "Mozilla/5.0...",            // 可选，用户代理
  "platform": "web",                        // 可选，访问平台
  "source": "search",                        // 可选，访问来源
  "duration": 30                             // 可选，停留时长(秒)
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "添加成功",
  "data": "添加成功"
}
```

### 2. 获取历史记录列表

**接口地址：** `GET /api/v1/user/history/list`

**接口描述：** 获取用户历史记录列表，支持按类型、时间范围、关键词筛选。

**请求参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| type | string | 否 | 历史记录类型 |
| start_date | string | 否 | 开始日期 YYYY-MM-DD |
| end_date | string | 否 | 结束日期 YYYY-MM-DD |
| keyword | string | 否 | 搜索关键词 |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 150,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 123,
        "user_id": 1001,
        "type": "takeout_food",
        "type_name": "外卖商品",
        "target_id": 12345,
        "target_name": "麻辣香锅",
        "target_image": "https://example.com/image.jpg",
        "visit_count": 3,
        "last_visit_at": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-10T09:15:00Z"
      }
    ]
  }
}
```

### 3. 搜索历史记录

**接口地址：** `GET /api/v1/user/history/search`

**接口描述：** 根据关键词搜索历史记录。

**请求参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应格式：** 同获取历史记录列表

### 4. 按类型获取历史记录

**接口地址：** `GET /api/v1/user/history/type/:type`

**接口描述：** 获取指定类型的历史记录。

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| type | string | 是 | 历史记录类型 |

**查询参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应格式：** 同获取历史记录列表

### 5. 获取统计信息

**接口地址：** `GET /api/v1/user/history/statistics`

**接口描述：** 获取用户历史记录的统计信息。

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_count": 1250,
    "today_count": 15,
    "week_count": 89,
    "month_count": 324,
    "type_statistics": [
      {
        "type": "takeout_food",
        "type_name": "外卖商品",
        "count": 650,
        "percentage": 52.0
      },
      {
        "type": "mall_product",
        "type_name": "商城商品",
        "count": 400,
        "percentage": 32.0
      }
    ],
    "recent_history": [
      {
        "id": 123,
        "type": "takeout_food",
        "type_name": "外卖商品",
        "target_name": "麻辣香锅",
        "last_visit_at": "2024-01-15T10:30:00Z"
      }
    ],
    "popular_items": [
      {
        "type": "takeout_food",
        "type_name": "外卖商品",
        "target_id": 12345,
        "target_name": "麻辣香锅",
        "target_image": "https://example.com/image.jpg",
        "visit_count": 15,
        "last_visit_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 6. 获取历史记录类型

**接口地址：** `GET /api/v1/user/history/types`

**接口描述：** 获取所有历史记录类型及其统计信息。

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "type": "takeout_food",
      "name": "外卖商品",
      "description": "用户浏览过的外卖商品",
      "count": 650
    },
    {
      "type": "mall_product",
      "name": "商城商品",
      "description": "用户浏览过的商城商品",
      "count": 400
    }
  ]
}
```

### 7. 获取分析数据

**接口地址：** `GET /api/v1/user/history/analytics`

**接口描述：** 获取历史记录的分析数据，包括趋势图表等。

**请求参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| period | string | 否 | 统计周期：day/week/month，默认week |

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "period": "week",
    "trend_data": [
      {
        "date": "2024-01-10",
        "count": 25
      },
      {
        "date": "2024-01-11",
        "count": 30
      }
    ],
    "type_data": [
      {
        "type": "takeout_food",
        "type_name": "外卖商品",
        "count": 650,
        "percentage": 52.0
      }
    ],
    "peak_hours": [9, 12, 15, 18, 21],
    "active_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
  }
}
```

### 8. 删除历史记录

**接口地址：** `POST /api/v1/user/history/delete/:id`

**接口描述：** 删除指定的历史记录。

**路径参数：**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| id | int64 | 是 | 历史记录ID |

**响应示例：**
```json
{
  "code": 0,
  "message": "删除成功",
  "data": "删除成功"
}
```

### 9. 批量删除历史记录

**接口地址：** `POST /api/v1/user/history/batch-delete`

**接口描述：** 批量删除历史记录。

**请求参数：**
```json
{
  "ids": [123, 456, 789]
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "批量删除成功",
  "data": "批量删除成功"
}
```

### 10. 清空历史记录

**接口地址：** `POST /api/v1/user/history/clear`

**接口描述：** 清空用户的历史记录，支持按类型和时间范围清空。

**请求参数：**
```json
{
  "type": "takeout_food",        // 可选，历史记录类型，为空则清空所有
  "start_date": "2024-01-01",    // 可选，开始日期
  "end_date": "2024-01-31"       // 可选，结束日期
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "清空成功",
  "data": "清空成功"
}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 历史记录类型

| 类型值 | 类型名称 | 描述 |
|--------|----------|------|
| takeout_food | 外卖商品 | 用户浏览过的外卖商品 |
| mall_product | 商城商品 | 用户浏览过的商城商品 |
| merchant | 商家 | 用户访问过的商家 |
| category | 分类 | 用户浏览过的分类 |
| search | 搜索记录 | 用户的搜索历史 |
| page | 页面访问 | 用户访问过的页面 |

## 使用注意事项

1. **认证要求**：所有接口都需要用户登录，需要在请求头中携带有效的JWT token
2. **权限控制**：用户只能访问和操作自己的历史记录
3. **数据隐私**：历史记录属于用户隐私数据，需要严格保护
4. **性能考虑**：大量历史记录查询可能影响性能，建议使用分页
5. **异步处理**：添加历史记录建议使用异步方式，避免影响主业务流程
