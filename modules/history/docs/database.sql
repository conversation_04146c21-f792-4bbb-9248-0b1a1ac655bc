-- 用户历史记录模块数据库表结构

-- 用户历史记录主表
CREATE TABLE IF NOT EXISTS `user_history` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `type` VARCHAR(50) NOT NULL COMMENT '历史记录类型',
  `target_id` BIGINT(20) NOT NULL COMMENT '目标对象ID',
  `target_name` VARCHAR(255) NOT NULL COMMENT '目标对象名称',
  `target_image` VARCHAR(500) DEFAULT NULL COMMENT '目标对象图片',
  `visit_count` INT(11) NOT NULL DEFAULT 1 COMMENT '访问次数',
  `last_visit_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后访问时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_type_target` (`user_id`, `type`, `target_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_target_id` (`target_id`),
  KEY `idx_last_visit_at` (`last_visit_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户历史记录主表';

-- 用户历史记录详情表
CREATE TABLE IF NOT EXISTS `user_history_detail` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `history_id` BIGINT(20) NOT NULL COMMENT '历史记录ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `type` VARCHAR(50) NOT NULL COMMENT '历史记录类型',
  `target_id` BIGINT(20) NOT NULL COMMENT '目标对象ID',
  `extra_data` TEXT COMMENT '额外数据，JSON格式',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `ip` VARCHAR(50) DEFAULT NULL COMMENT '访问IP',
  `platform` VARCHAR(50) DEFAULT NULL COMMENT '访问平台',
  `source` VARCHAR(100) DEFAULT NULL COMMENT '访问来源',
  `duration` INT(11) NOT NULL DEFAULT 0 COMMENT '停留时长(秒)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_history_id` (`history_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_history_detail_history` FOREIGN KEY (`history_id`) REFERENCES `user_history` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户历史记录详情表';

-- 插入历史记录类型配置数据（可选）
INSERT IGNORE INTO `system_config` (`key`, `value`, `description`, `type`, `created_at`, `updated_at`) VALUES
('history.retention_days', '365', '历史记录保留天数', 'number', NOW(), NOW()),
('history.cleanup.enabled', 'true', '是否启用自动清理', 'boolean', NOW(), NOW()),
('history.cache.enabled', 'true', '是否启用缓存', 'boolean', NOW(), NOW()),
('history.page.default_size', '20', '默认分页大小', 'number', NOW(), NOW()),
('history.statistics.enabled', 'true', '是否启用统计功能', 'boolean', NOW(), NOW());

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_user_history_user_type` ON `user_history` (`user_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_user_history_user_last_visit` ON `user_history` (`user_id`, `last_visit_at`);
CREATE INDEX IF NOT EXISTS `idx_user_history_detail_user_type` ON `user_history_detail` (`user_id`, `type`);

-- 示例数据（开发环境可选）
-- INSERT INTO `user_history` (`user_id`, `type`, `target_id`, `target_name`, `target_image`, `visit_count`, `last_visit_at`) VALUES
-- (1, 'takeout_food', 1001, '麻辣香锅', 'https://example.com/food1.jpg', 3, NOW()),
-- (1, 'takeout_food', 1002, '宫保鸡丁', 'https://example.com/food2.jpg', 2, NOW()),
-- (1, 'mall_product', 2001, 'iPhone 15', 'https://example.com/phone1.jpg', 5, NOW()),
-- (2, 'merchant', 3001, '川味小厨', 'https://example.com/merchant1.jpg', 1, NOW());

-- 创建定时清理任务的存储过程（可选）
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `CleanupOldHistory`(IN retention_days INT)
BEGIN
    DECLARE cutoff_date DATETIME;
    SET cutoff_date = DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 删除过期的详情记录
    DELETE FROM `user_history_detail` WHERE `created_at` < cutoff_date;
    
    -- 删除过期的主记录
    DELETE FROM `user_history` WHERE `created_at` < cutoff_date;
    
    -- 返回清理结果
    SELECT ROW_COUNT() as deleted_records, cutoff_date as cutoff_date;
END$$
DELIMITER ;

-- 创建获取用户历史统计的存储过程（可选）
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `GetUserHistoryStatistics`(IN user_id BIGINT)
BEGIN
    -- 总数统计
    SELECT 
        COUNT(*) as total_count,
        COUNT(CASE WHEN DATE(last_visit_at) = CURDATE() THEN 1 END) as today_count,
        COUNT(CASE WHEN last_visit_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_count,
        COUNT(CASE WHEN last_visit_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_count
    FROM user_history 
    WHERE user_id = user_id;
    
    -- 类型统计
    SELECT 
        type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_history WHERE user_id = user_id), 2) as percentage
    FROM user_history 
    WHERE user_id = user_id
    GROUP BY type
    ORDER BY count DESC;
    
    -- 热门项目
    SELECT 
        type,
        target_id,
        target_name,
        target_image,
        visit_count,
        last_visit_at
    FROM user_history 
    WHERE user_id = user_id
    ORDER BY visit_count DESC, last_visit_at DESC
    LIMIT 10;
END$$
DELIMITER ;

-- 创建触发器，自动更新访问次数（可选）
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS `update_visit_count` 
BEFORE UPDATE ON `user_history`
FOR EACH ROW
BEGIN
    IF NEW.last_visit_at > OLD.last_visit_at THEN
        SET NEW.visit_count = OLD.visit_count + 1;
    END IF;
END$$
DELIMITER ;

-- 创建视图，方便查询用户历史记录统计（可选）
CREATE OR REPLACE VIEW `v_user_history_stats` AS
SELECT 
    user_id,
    type,
    COUNT(*) as record_count,
    SUM(visit_count) as total_visits,
    MAX(last_visit_at) as last_visit,
    MIN(created_at) as first_visit
FROM user_history
GROUP BY user_id, type;

-- 权限设置（根据实际需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON user_history TO 'app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON user_history_detail TO 'app_user'@'%';
-- GRANT SELECT ON v_user_history_stats TO 'app_user'@'%';
-- GRANT EXECUTE ON PROCEDURE CleanupOldHistory TO 'app_user'@'%';
-- GRANT EXECUTE ON PROCEDURE GetUserHistoryStatistics TO 'app_user'@'%';
