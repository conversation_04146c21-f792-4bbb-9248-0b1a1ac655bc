# 用户历史记录模块部署指南

## 部署步骤

### 1. 数据库准备

执行数据库迁移脚本创建相关表：

```sql
-- 执行 modules/history/docs/database.sql 中的SQL语句
source modules/history/docs/database.sql
```

### 2. 配置文件

确保 `conf/modules/history.conf` 配置文件存在并配置正确：

```ini
# 历史记录保留天数
history.retention_days = 365

# 历史记录清理任务配置
history.cleanup.enabled = true
history.cleanup.cron = "0 2 * * *"

# 历史记录缓存配置
history.cache.enabled = true
history.cache.ttl = 300

# 历史记录分页配置
history.page.default_size = 20
history.page.max_size = 100
```

### 3. 模块注册

确保在 `routers/router.go` 中已经注册了历史记录模块：

```go
import (
    // ... 其他导入
    "o_mall_backend/modules/history"
)

func Init() {
    // ... 其他模块初始化
    
    // 初始化历史记录模块
    history.Init()
    
    // ...
}
```

### 4. API文档同步

在 `main.go` 中的模块列表中添加 "history"：

```go
modules := []string{"admin", "system", "merchant", "user", "order", "apidoc", "payment", "ui_config", "delivery", "history"}
```

### 5. 编译和启动

```bash
# 编译项目
go build -o o_mall_backend

# 启动服务
./o_mall_backend
```

## 验证部署

### 1. 检查数据库表

确认以下表已创建：
- `user_history` - 用户历史记录主表
- `user_history_detail` - 用户历史记录详情表

### 2. 测试API接口

```bash
# 测试添加历史记录
curl -X POST "http://localhost:8080/api/v1/user/history/add" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "takeout_food",
    "target_id": 12345,
    "target_name": "测试商品",
    "target_image": "https://example.com/image.jpg"
  }'

# 测试获取历史记录列表
curl -X GET "http://localhost:8080/api/v1/user/history/list" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 测试获取统计信息
curl -X GET "http://localhost:8080/api/v1/user/history/statistics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 检查日志

查看应用日志确认模块正常启动：

```bash
tail -f logs/o_mall.log | grep -i history
```

应该看到类似以下的日志：
```
[INFO] 初始化用户历史记录模块...
[INFO] 注册用户历史记录模型...
[INFO] 用户历史记录模型注册完成
[INFO] 用户历史记录模块初始化完成
```

## 常见问题

### 1. 数据库连接错误

**问题**: 启动时报数据库连接错误
**解决**: 检查数据库配置和连接参数

### 2. 表不存在错误

**问题**: API调用时报表不存在错误
**解决**: 确认已执行数据库迁移脚本

### 3. 权限错误

**问题**: API调用返回401未授权错误
**解决**: 确认JWT token有效，检查认证中间件配置

### 4. 路由不存在错误

**问题**: API调用返回404错误
**解决**: 确认模块已正确注册，检查路由配置

## 性能优化

### 1. 数据库索引

确保已创建必要的索引：

```sql
-- 用户ID和类型的复合索引
CREATE INDEX idx_user_history_user_type ON user_history (user_id, type);

-- 用户ID和最后访问时间的复合索引
CREATE INDEX idx_user_history_user_last_visit ON user_history (user_id, last_visit_at);

-- 详情表的用户ID和类型索引
CREATE INDEX idx_user_history_detail_user_type ON user_history_detail (user_id, type);
```

### 2. 缓存配置

启用Redis缓存以提高查询性能：

```ini
# 在 conf/app.conf 中配置Redis
redis.host = localhost
redis.port = 6379
redis.password = 
redis.db = 0

# 在 conf/modules/history.conf 中启用缓存
history.cache.enabled = true
history.cache.ttl = 300
```

### 3. 定时清理

配置定时任务清理过期数据：

```ini
history.cleanup.enabled = true
history.cleanup.cron = "0 2 * * *"  # 每天凌晨2点执行
history.retention_days = 365        # 保留365天的数据
```

## 监控和维护

### 1. 监控指标

- 历史记录总数
- 每日新增记录数
- API响应时间
- 数据库查询性能

### 2. 日志监控

关键日志关键词：
- "历史记录" - 一般操作日志
- "ERROR" - 错误日志
- "WARN" - 警告日志

### 3. 定期维护

- 定期检查数据库表大小
- 监控缓存命中率
- 清理过期数据
- 备份重要数据

## 扩展功能

### 1. 数据分析

可以基于历史记录数据进行：
- 用户行为分析
- 商品热度统计
- 推荐算法优化

### 2. 个性化推荐

利用历史记录实现：
- 基于浏览历史的商品推荐
- 个性化搜索结果
- 用户偏好分析

### 3. 数据导出

支持数据导出功能：
- CSV格式导出
- Excel格式导出
- 数据分析报表

这个部署指南提供了完整的部署流程和常见问题解决方案，确保用户历史记录模块能够正常运行。
