# 用户历史记录模块使用示例

## 后端集成示例

### 1. 在商品详情页面添加历史记录

```go
// 在外卖商品详情控制器中
func (c *TakeoutFoodController) Detail() {
    // 获取用户ID
    userID, err := auth.GetUserIDFromContext(c.Ctx)
    if err != nil {
        result.HandleError(c.Ctx, err)
        return
    }

    // 获取商品ID
    foodID, _ := c.GetInt64(":id")
    
    // 获取商品详情
    food, err := c.foodService.GetFoodDetail(foodID)
    if err != nil {
        result.HandleError(c.Ctx, err)
        return
    }

    // 异步添加历史记录
    go func() {
        historyService := services.NewUserHistoryService()
        historyReq := &dto.AddHistoryRequest{
            Type:        models.HistoryTypeTakeoutFood,
            TargetID:    food.ID,
            TargetName:  food.Name,
            TargetImage: food.Image,
            ExtraData: map[string]interface{}{
                "price":       food.Price,
                "merchant_id": food.MerchantID,
                "category_id": food.CategoryID,
                "rating":      food.Rating,
            },
            Platform: c.GetString("platform", "web"),
            Source:   c.GetString("source", "direct"),
        }
        
        err := historyService.AddHistory(userID, historyReq, c.Ctx.Input.IP())
        if err != nil {
            logs.Warn("添加历史记录失败: %v", err)
        }
    }()

    // 返回商品详情
    result.OK(c.Ctx, food)
}
```

### 2. 在商家页面添加历史记录

```go
// 在商家详情控制器中
func (c *MerchantController) Detail() {
    userID, _ := auth.GetUserIDFromContext(c.Ctx)
    merchantID, _ := c.GetInt64(":id")
    
    merchant, err := c.merchantService.GetMerchantDetail(merchantID)
    if err != nil {
        result.HandleError(c.Ctx, err)
        return
    }

    // 添加商家访问历史记录
    if userID > 0 {
        go func() {
            historyService := services.NewUserHistoryService()
            historyReq := &dto.AddHistoryRequest{
                Type:        models.HistoryTypeMerchant,
                TargetID:    merchant.ID,
                TargetName:  merchant.Name,
                TargetImage: merchant.Logo,
                ExtraData: map[string]interface{}{
                    "category":     merchant.Category,
                    "rating":       merchant.Rating,
                    "delivery_fee": merchant.DeliveryFee,
                    "min_order":    merchant.MinOrder,
                },
                Platform: "mobile",
                Source:   "merchant_list",
            }
            
            historyService.AddHistory(userID, historyReq, c.Ctx.Input.IP())
        }()
    }

    result.OK(c.Ctx, merchant)
}
```

### 3. 搜索记录的添加

```go
// 在搜索控制器中
func (c *SearchController) Search() {
    userID, _ := auth.GetUserIDFromContext(c.Ctx)
    keyword := c.GetString("keyword")
    
    // 执行搜索
    results, err := c.searchService.Search(keyword)
    if err != nil {
        result.HandleError(c.Ctx, err)
        return
    }

    // 添加搜索历史记录
    if userID > 0 && keyword != "" {
        go func() {
            historyService := services.NewUserHistoryService()
            historyReq := &dto.AddHistoryRequest{
                Type:        models.HistoryTypeSearch,
                TargetID:    0, // 搜索记录没有具体的目标ID
                TargetName:  keyword,
                TargetImage: "",
                ExtraData: map[string]interface{}{
                    "result_count": len(results),
                    "search_type":  c.GetString("type", "all"),
                },
                Platform: "web",
                Source:   "search_box",
            }
            
            historyService.AddHistory(userID, historyReq, c.Ctx.Input.IP())
        }()
    }

    result.OK(c.Ctx, results)
}
```

## 前端集成示例

### 1. JavaScript/Vue.js 集成

```javascript
// 历史记录服务类
class HistoryService {
    constructor(apiClient) {
        this.apiClient = apiClient;
    }

    // 添加历史记录
    async addHistory(type, targetId, targetName, targetImage, extraData = {}) {
        try {
            const data = {
                type,
                target_id: targetId,
                target_name: targetName,
                target_image: targetImage,
                extra_data: extraData,
                user_agent: navigator.userAgent,
                platform: this.getPlatform(),
                source: this.getSource(),
                duration: this.getPageDuration()
            };
            
            await this.apiClient.post('/api/v1/user/history/add', data);
        } catch (error) {
            console.warn('添加历史记录失败:', error);
        }
    }

    // 获取历史记录列表
    async getHistoryList(type = '', page = 1, pageSize = 20) {
        const params = { type, page, page_size: pageSize };
        const response = await this.apiClient.get('/api/v1/user/history/list', { params });
        return response.data;
    }

    // 获取统计信息
    async getStatistics() {
        const response = await this.apiClient.get('/api/v1/user/history/statistics');
        return response.data;
    }

    // 搜索历史记录
    async searchHistory(keyword, page = 1, pageSize = 20) {
        const params = { keyword, page, page_size: pageSize };
        const response = await this.apiClient.get('/api/v1/user/history/search', { params });
        return response.data;
    }

    // 删除历史记录
    async deleteHistory(id) {
        await this.apiClient.post(`/api/v1/user/history/delete/${id}`);
    }

    // 批量删除历史记录
    async batchDeleteHistory(ids) {
        await this.apiClient.post('/api/v1/user/history/batch-delete', { ids });
    }

    // 清空历史记录
    async clearHistory(type = '', startDate = '', endDate = '') {
        const data = { type, start_date: startDate, end_date: endDate };
        await this.apiClient.post('/api/v1/user/history/clear', data);
    }

    // 获取平台信息
    getPlatform() {
        const userAgent = navigator.userAgent;
        if (/Android/i.test(userAgent)) return 'android';
        if (/iPhone|iPad|iPod/i.test(userAgent)) return 'ios';
        return 'web';
    }

    // 获取访问来源
    getSource() {
        return document.referrer ? 'referrer' : 'direct';
    }

    // 获取页面停留时长
    getPageDuration() {
        return Math.floor((Date.now() - window.pageStartTime) / 1000);
    }
}

// Vue 组件示例
export default {
    name: 'HistoryPage',
    data() {
        return {
            historyList: [],
            statistics: null,
            loading: false,
            currentType: '',
            page: 1,
            pageSize: 20,
            total: 0
        };
    },
    
    created() {
        this.historyService = new HistoryService(this.$http);
        this.loadHistoryList();
        this.loadStatistics();
    },
    
    methods: {
        // 加载历史记录列表
        async loadHistoryList() {
            this.loading = true;
            try {
                const response = await this.historyService.getHistoryList(
                    this.currentType, 
                    this.page, 
                    this.pageSize
                );
                this.historyList = response.data.list;
                this.total = response.data.total;
            } catch (error) {
                this.$message.error('加载历史记录失败');
            } finally {
                this.loading = false;
            }
        },

        // 加载统计信息
        async loadStatistics() {
            try {
                const response = await this.historyService.getStatistics();
                this.statistics = response.data;
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        },

        // 删除历史记录
        async deleteItem(id) {
            try {
                await this.historyService.deleteHistory(id);
                this.$message.success('删除成功');
                this.loadHistoryList();
                this.loadStatistics();
            } catch (error) {
                this.$message.error('删除失败');
            }
        },

        // 批量删除
        async batchDelete(ids) {
            try {
                await this.historyService.batchDeleteHistory(ids);
                this.$message.success('批量删除成功');
                this.loadHistoryList();
                this.loadStatistics();
            } catch (error) {
                this.$message.error('批量删除失败');
            }
        },

        // 清空历史记录
        async clearAll() {
            this.$confirm('确定要清空所有历史记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    await this.historyService.clearHistory();
                    this.$message.success('清空成功');
                    this.loadHistoryList();
                    this.loadStatistics();
                } catch (error) {
                    this.$message.error('清空失败');
                }
            });
        },

        // 切换历史记录类型
        changeType(type) {
            this.currentType = type;
            this.page = 1;
            this.loadHistoryList();
        },

        // 页码变化
        handlePageChange(page) {
            this.page = page;
            this.loadHistoryList();
        }
    }
};
```

### 2. React Hooks 示例

```javascript
import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';

// 自定义 Hook
export const useHistory = () => {
    const [historyList, setHistoryList] = useState([]);
    const [statistics, setStatistics] = useState(null);
    const [loading, setLoading] = useState(false);

    const historyService = new HistoryService(apiClient);

    // 加载历史记录列表
    const loadHistoryList = useCallback(async (type = '', page = 1, pageSize = 20) => {
        setLoading(true);
        try {
            const response = await historyService.getHistoryList(type, page, pageSize);
            setHistoryList(response.data.list);
            return response.data;
        } catch (error) {
            message.error('加载历史记录失败');
            return null;
        } finally {
            setLoading(false);
        }
    }, [historyService]);

    // 加载统计信息
    const loadStatistics = useCallback(async () => {
        try {
            const response = await historyService.getStatistics();
            setStatistics(response.data);
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }, [historyService]);

    // 删除历史记录
    const deleteHistory = useCallback(async (id) => {
        try {
            await historyService.deleteHistory(id);
            message.success('删除成功');
            return true;
        } catch (error) {
            message.error('删除失败');
            return false;
        }
    }, [historyService]);

    // 添加历史记录
    const addHistory = useCallback(async (type, targetId, targetName, targetImage, extraData) => {
        try {
            await historyService.addHistory(type, targetId, targetName, targetImage, extraData);
        } catch (error) {
            console.warn('添加历史记录失败:', error);
        }
    }, [historyService]);

    useEffect(() => {
        loadHistoryList();
        loadStatistics();
    }, [loadHistoryList, loadStatistics]);

    return {
        historyList,
        statistics,
        loading,
        loadHistoryList,
        loadStatistics,
        deleteHistory,
        addHistory
    };
};

// React 组件示例
const HistoryPage = () => {
    const {
        historyList,
        statistics,
        loading,
        loadHistoryList,
        deleteHistory,
        addHistory
    } = useHistory();

    const [currentType, setCurrentType] = useState('');
    const [page, setPage] = useState(1);

    // 处理类型切换
    const handleTypeChange = (type) => {
        setCurrentType(type);
        setPage(1);
        loadHistoryList(type, 1);
    };

    // 处理删除
    const handleDelete = async (id) => {
        const success = await deleteHistory(id);
        if (success) {
            loadHistoryList(currentType, page);
        }
    };

    return (
        <div className="history-page">
            <div className="statistics">
                {statistics && (
                    <div className="stats-cards">
                        <div className="stat-card">
                            <h3>总记录数</h3>
                            <p>{statistics.total_count}</p>
                        </div>
                        <div className="stat-card">
                            <h3>今日访问</h3>
                            <p>{statistics.today_count}</p>
                        </div>
                        <div className="stat-card">
                            <h3>本周访问</h3>
                            <p>{statistics.week_count}</p>
                        </div>
                    </div>
                )}
            </div>

            <div className="history-list">
                {loading ? (
                    <div>加载中...</div>
                ) : (
                    <div>
                        {historyList.map(item => (
                            <div key={item.id} className="history-item">
                                <img src={item.target_image} alt={item.target_name} />
                                <div className="item-info">
                                    <h4>{item.target_name}</h4>
                                    <p>访问次数: {item.visit_count}</p>
                                    <p>最后访问: {new Date(item.last_visit_at).toLocaleString()}</p>
                                </div>
                                <button onClick={() => handleDelete(item.id)}>删除</button>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default HistoryPage;
```

### 3. 页面访问追踪

```javascript
// 页面访问追踪工具
class PageTracker {
    constructor(historyService) {
        this.historyService = historyService;
        this.startTime = Date.now();
        this.setupTracking();
    }

    setupTracking() {
        // 页面加载时记录
        window.addEventListener('load', () => {
            this.trackPageView();
        });

        // 页面卸载时更新停留时长
        window.addEventListener('beforeunload', () => {
            this.updateDuration();
        });

        // 页面可见性变化时处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.updateDuration();
            } else {
                this.startTime = Date.now();
            }
        });
    }

    // 记录页面访问
    async trackPageView() {
        const pageInfo = this.getPageInfo();
        await this.historyService.addHistory(
            'page',
            0,
            pageInfo.title,
            '',
            {
                url: pageInfo.url,
                referrer: document.referrer,
                screen_resolution: `${screen.width}x${screen.height}`,
                viewport_size: `${window.innerWidth}x${window.innerHeight}`
            }
        );
    }

    // 更新停留时长
    async updateDuration() {
        const duration = Math.floor((Date.now() - this.startTime) / 1000);
        // 这里可以调用更新接口，或者在下次访问时更新
    }

    // 获取页面信息
    getPageInfo() {
        return {
            title: document.title,
            url: window.location.href,
            path: window.location.pathname
        };
    }
}

// 使用示例
const historyService = new HistoryService(apiClient);
const pageTracker = new PageTracker(historyService);
```

这些示例展示了如何在实际项目中集成和使用用户历史记录模块，包括后端服务调用、前端组件开发和页面访问追踪等功能。
