/**
 * 用户历史记录DTO
 *
 * 本文件定义了用户历史记录相关的数据传输对象，用于API接口的请求和响应。
 * 包含历史记录的创建、查询、统计等相关的数据结构。
 */

package dto

import (
	"time"
)

// AddHistoryRequest 添加历史记录请求
type AddHistoryRequest struct {
	Type        string                 `json:"type" valid:"Required" description:"历史记录类型"`
	TargetID    int64                  `json:"target_id" valid:"Required" description:"目标对象ID"`
	TargetName  string                 `json:"target_name" valid:"Required;MaxSize(255)" description:"目标对象名称"`
	TargetImage string                 `json:"target_image" valid:"MaxSize(500)" description:"目标对象图片"`
	ExtraData   map[string]interface{} `json:"extra_data" description:"额外数据"`
	UserAgent   string                 `json:"user_agent" valid:"MaxSize(500)" description:"用户代理"`
	Platform    string                 `json:"platform" valid:"MaxSize(50)" description:"访问平台"`
	Source      string                 `json:"source" valid:"MaxSize(100)" description:"访问来源"`
	Duration    int                    `json:"duration" description:"停留时长(秒)"`
}

// UpdateHistoryRequest 更新历史记录请求
type UpdateHistoryRequest struct {
	Duration int `json:"duration" description:"停留时长(秒)"`
}

// HistoryQueryRequest 历史记录查询请求
type HistoryQueryRequest struct {
	Type      string `json:"type" description:"历史记录类型"`
	StartDate string `json:"start_date" description:"开始日期 YYYY-MM-DD"`
	EndDate   string `json:"end_date" description:"结束日期 YYYY-MM-DD"`
	Page      int    `json:"page" description:"页码，默认1"`
	PageSize  int    `json:"page_size" description:"每页数量，默认20"`
	Keyword   string `json:"keyword" description:"搜索关键词"`
}

// UserHistoryDTO 用户历史记录DTO
type UserHistoryDTO struct {
	ID          int64     `json:"id"`
	UserID      int64     `json:"user_id"`
	Type        string    `json:"type"`
	TypeName    string    `json:"type_name"`
	TargetID    int64     `json:"target_id"`
	TargetName  string    `json:"target_name"`
	TargetImage string    `json:"target_image"`
	VisitCount  int       `json:"visit_count"`
	LastVisitAt time.Time `json:"last_visit_at"`
	CreatedAt   time.Time `json:"created_at"`
}

// UserHistoryDetailDTO 用户历史记录详情DTO
type UserHistoryDetailDTO struct {
	ID        int64                  `json:"id"`
	HistoryID int64                  `json:"history_id"`
	UserID    int64                  `json:"user_id"`
	Type      string                 `json:"type"`
	TargetID  int64                  `json:"target_id"`
	ExtraData map[string]interface{} `json:"extra_data"`
	UserAgent string                 `json:"user_agent"`
	IP        string                 `json:"ip"`
	Platform  string                 `json:"platform"`
	Source    string                 `json:"source"`
	Duration  int                    `json:"duration"`
	CreatedAt time.Time              `json:"created_at"`
}

// HistoryListResponse 历史记录列表响应
type HistoryListResponse struct {
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
	List     []UserHistoryDTO  `json:"list"`
}

// HistoryStatisticsDTO 历史记录统计DTO
type HistoryStatisticsDTO struct {
	TotalCount      int64                    `json:"total_count"`
	TodayCount      int64                    `json:"today_count"`
	WeekCount       int64                    `json:"week_count"`
	MonthCount      int64                    `json:"month_count"`
	TypeStatistics  []TypeStatisticsDTO      `json:"type_statistics"`
	RecentHistory   []UserHistoryDTO         `json:"recent_history"`
	PopularItems    []PopularItemDTO         `json:"popular_items"`
}

// TypeStatisticsDTO 类型统计DTO
type TypeStatisticsDTO struct {
	Type      string `json:"type"`
	TypeName  string `json:"type_name"`
	Count     int64  `json:"count"`
	Percentage float64 `json:"percentage"`
}

// PopularItemDTO 热门项目DTO
type PopularItemDTO struct {
	Type        string `json:"type"`
	TypeName    string `json:"type_name"`
	TargetID    int64  `json:"target_id"`
	TargetName  string `json:"target_name"`
	TargetImage string `json:"target_image"`
	VisitCount  int    `json:"visit_count"`
	LastVisitAt time.Time `json:"last_visit_at"`
}

// HistoryTypeDTO 历史记录类型DTO
type HistoryTypeDTO struct {
	Type        string `json:"type"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Count       int64  `json:"count"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	IDs []int64 `json:"ids" valid:"Required" description:"要删除的历史记录ID列表"`
}

// ClearHistoryRequest 清空历史记录请求
type ClearHistoryRequest struct {
	Type      string `json:"type" description:"历史记录类型，为空则清空所有"`
	StartDate string `json:"start_date" description:"开始日期 YYYY-MM-DD"`
	EndDate   string `json:"end_date" description:"结束日期 YYYY-MM-DD"`
}

// HistoryTrendDTO 历史记录趋势DTO
type HistoryTrendDTO struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// HistoryAnalyticsDTO 历史记录分析DTO
type HistoryAnalyticsDTO struct {
	Period      string            `json:"period"`      // 统计周期：day, week, month
	TrendData   []HistoryTrendDTO `json:"trend_data"`  // 趋势数据
	TypeData    []TypeStatisticsDTO `json:"type_data"` // 类型分布
	PeakHours   []int             `json:"peak_hours"`  // 访问高峰时段
	ActiveDays  []string          `json:"active_days"` // 活跃日期
}

// ExportHistoryRequest 导出历史记录请求
type ExportHistoryRequest struct {
	Type      string `json:"type" description:"历史记录类型"`
	StartDate string `json:"start_date" description:"开始日期 YYYY-MM-DD"`
	EndDate   string `json:"end_date" description:"结束日期 YYYY-MM-DD"`
	Format    string `json:"format" description:"导出格式：csv, excel"`
}
