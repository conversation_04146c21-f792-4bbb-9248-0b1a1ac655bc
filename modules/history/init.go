/**
 * 用户历史记录模块初始化
 *
 * 本文件负责初始化用户历史记录模块，注册数据库模型并提供模块初始化入口。
 * 该模块处理用户访问历史记录，包括外卖商品、商城商品等浏览记录。
 */

package history

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/history/models"
	"o_mall_backend/modules/history/routers"
)

// 初始化模块
func Init() {
	logs.Info("初始化用户历史记录模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitHistoryRouter()

	logs.Info("用户历史记录模块初始化完成")
}

// 注册数据库模型
func registerModels() {
	logs.Info("注册用户历史记录模型...")

	// 注册用户历史记录相关模型
	orm.RegisterModel(
		new(models.UserHistory),
		new(models.UserHistoryDetail),
	)

	logs.Info("用户历史记录模型注册完成")
}
