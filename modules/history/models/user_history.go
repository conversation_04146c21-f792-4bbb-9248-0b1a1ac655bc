/**
 * 用户历史记录模型
 *
 * 本文件定义了用户历史记录相关的数据模型，用于存储用户访问过的商品、页面等信息。
 * 支持外卖商品、商城商品等多种类型的历史记录。
 */

package models

import (
	"time"
)

// HistoryType 历史记录类型常量
const (
	HistoryTypeTakeoutFood   = "takeout_food"   // 外卖商品
	HistoryTypeMallProduct   = "mall_product"   // 商城商品
	HistoryTypeMerchant      = "merchant"       // 商家
	HistoryTypeCategory      = "category"       // 分类
	HistoryTypeSearch        = "search"         // 搜索记录
	HistoryTypePage          = "page"           // 页面访问
)

// UserHistory 用户历史记录主表
type UserHistory struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`
	UserID      int64     `orm:"column(user_id);index" json:"user_id" description:"用户ID"`
	Type        string    `orm:"column(type);size(50);index" json:"type" description:"历史记录类型"`
	TargetID    int64     `orm:"column(target_id);index" json:"target_id" description:"目标对象ID"`
	TargetName  string    `orm:"column(target_name);size(255)" json:"target_name" description:"目标对象名称"`
	TargetImage string    `orm:"column(target_image);size(500)" json:"target_image" description:"目标对象图片"`
	VisitCount  int       `orm:"column(visit_count);default(1)" json:"visit_count" description:"访问次数"`
	LastVisitAt time.Time `orm:"column(last_visit_at);auto_now" json:"last_visit_at" description:"最后访问时间"`
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`
}

// TableName 设置表名
func (u *UserHistory) TableName() string {
	return "user_history"
}

// UserHistoryDetail 用户历史记录详情表
type UserHistoryDetail struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`
	HistoryID   int64     `orm:"column(history_id);index" json:"history_id" description:"历史记录ID"`
	UserID      int64     `orm:"column(user_id);index" json:"user_id" description:"用户ID"`
	Type        string    `orm:"column(type);size(50)" json:"type" description:"历史记录类型"`
	TargetID    int64     `orm:"column(target_id)" json:"target_id" description:"目标对象ID"`
	ExtraData   string    `orm:"column(extra_data);type(text)" json:"extra_data" description:"额外数据，JSON格式"`
	UserAgent   string    `orm:"column(user_agent);size(500)" json:"user_agent" description:"用户代理"`
	IP          string    `orm:"column(ip);size(50)" json:"ip" description:"访问IP"`
	Platform    string    `orm:"column(platform);size(50)" json:"platform" description:"访问平台"`
	Source      string    `orm:"column(source);size(100)" json:"source" description:"访问来源"`
	Duration    int       `orm:"column(duration);default(0)" json:"duration" description:"停留时长(秒)"`
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`
}

// TableName 设置表名
func (u *UserHistoryDetail) TableName() string {
	return "user_history_detail"
}

// HistoryTypeInfo 历史记录类型信息
type HistoryTypeInfo struct {
	Type        string `json:"type"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// GetHistoryTypes 获取所有历史记录类型
func GetHistoryTypes() []HistoryTypeInfo {
	return []HistoryTypeInfo{
		{
			Type:        HistoryTypeTakeoutFood,
			Name:        "外卖商品",
			Description: "用户浏览过的外卖商品",
		},
		{
			Type:        HistoryTypeMallProduct,
			Name:        "商城商品",
			Description: "用户浏览过的商城商品",
		},
		{
			Type:        HistoryTypeMerchant,
			Name:        "商家",
			Description: "用户访问过的商家",
		},
		{
			Type:        HistoryTypeCategory,
			Name:        "分类",
			Description: "用户浏览过的分类",
		},
		{
			Type:        HistoryTypeSearch,
			Name:        "搜索记录",
			Description: "用户的搜索历史",
		},
		{
			Type:        HistoryTypePage,
			Name:        "页面访问",
			Description: "用户访问过的页面",
		},
	}
}

// IsValidHistoryType 验证历史记录类型是否有效
func IsValidHistoryType(historyType string) bool {
	validTypes := []string{
		HistoryTypeTakeoutFood,
		HistoryTypeMallProduct,
		HistoryTypeMerchant,
		HistoryTypeCategory,
		HistoryTypeSearch,
		HistoryTypePage,
	}
	
	for _, validType := range validTypes {
		if historyType == validType {
			return true
		}
	}
	return false
}
