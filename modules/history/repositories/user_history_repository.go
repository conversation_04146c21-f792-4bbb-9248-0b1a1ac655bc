/**
 * 用户历史记录仓库
 *
 * 本文件实现了用户历史记录相关的数据库操作，提供数据持久化和查询功能。
 * 负责user_history和user_history_detail表的CRUD操作。
 */

package repositories

import (
	"errors"
	"time"

	"o_mall_backend/modules/history/models"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// UserHistoryRepository 用户历史记录仓库接口
type UserHistoryRepository interface {
	// 基础CRUD
	CreateHistory(history *models.UserHistory) (int64, error)
	CreateHistoryDetail(detail *models.UserHistoryDetail) (int64, error)
	GetHistoryByID(id int64) (*models.UserHistory, error)
	UpdateHistory(history *models.UserHistory) error
	DeleteHistory(id int64, userID int64) error
	BatchDeleteHistory(ids []int64, userID int64) error

	// 查询操作
	GetUserHistory(userID int64, historyType string, targetID int64) (*models.UserHistory, error)
	ListUserHistory(userID int64, historyType string, offset, limit int) ([]*models.UserHistory, int64, error)
	SearchUserHistory(userID int64, keyword string, offset, limit int) ([]*models.UserHistory, int64, error)
	GetHistoryByDateRange(userID int64, historyType string, startDate, endDate time.Time, offset, limit int) ([]*models.UserHistory, int64, error)

	// 统计操作
	CountUserHistory(userID int64, historyType string) (int64, error)
	CountUserHistoryByDateRange(userID int64, historyType string, startDate, endDate time.Time) (int64, error)
	GetTypeStatistics(userID int64) (map[string]int64, error)
	GetPopularItems(userID int64, historyType string, limit int) ([]*models.UserHistory, error)
	GetHistoryTrend(userID int64, historyType string, days int) (map[string]int64, error)

	// 清理操作
	ClearUserHistory(userID int64, historyType string) error
	ClearUserHistoryByDateRange(userID int64, historyType string, startDate, endDate time.Time) error
	CleanupOldHistory(days int) error
}

// userHistoryRepository 用户历史记录仓库实现
type userHistoryRepository struct {
	ormer orm.Ormer
}

// NewUserHistoryRepository 创建用户历史记录仓库实例
func NewUserHistoryRepository() UserHistoryRepository {
	return &userHistoryRepository{
		ormer: orm.NewOrm(),
	}
}

// CreateHistory 创建历史记录
func (r *userHistoryRepository) CreateHistory(history *models.UserHistory) (int64, error) {
	history.CreatedAt = time.Now()
	history.UpdatedAt = time.Now()
	history.LastVisitAt = time.Now()
	return r.ormer.Insert(history)
}

// CreateHistoryDetail 创建历史记录详情
func (r *userHistoryRepository) CreateHistoryDetail(detail *models.UserHistoryDetail) (int64, error) {
	detail.CreatedAt = time.Now()
	return r.ormer.Insert(detail)
}

// GetHistoryByID 根据ID获取历史记录
func (r *userHistoryRepository) GetHistoryByID(id int64) (*models.UserHistory, error) {
	history := &models.UserHistory{ID: id}
	err := r.ormer.Read(history)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("历史记录不存在")
		}
		return nil, err
	}
	return history, nil
}

// UpdateHistory 更新历史记录
func (r *userHistoryRepository) UpdateHistory(history *models.UserHistory) error {
	history.UpdatedAt = time.Now()
	_, err := r.ormer.Update(history)
	return err
}

// DeleteHistory 删除历史记录
func (r *userHistoryRepository) DeleteHistory(id int64, userID int64) error {
	history := &models.UserHistory{ID: id, UserID: userID}
	_, err := r.ormer.Delete(history, "ID", "UserID")
	if err != nil {
		return err
	}

	// 同时删除详情记录
	_, err = r.ormer.Raw("DELETE FROM user_history_detail WHERE history_id = ? AND user_id = ?", id, userID).Exec()
	return err
}

// BatchDeleteHistory 批量删除历史记录
func (r *userHistoryRepository) BatchDeleteHistory(ids []int64, userID int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 删除主记录
	_, err := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("id__in", ids).
		Filter("user_id", userID).
		Delete()
	if err != nil {
		return err
	}

	// 删除详情记录
	_, err = r.ormer.QueryTable(new(models.UserHistoryDetail)).
		Filter("history_id__in", ids).
		Filter("user_id", userID).
		Delete()
	return err
}

// GetUserHistory 获取用户特定类型和目标的历史记录
func (r *userHistoryRepository) GetUserHistory(userID int64, historyType string, targetID int64) (*models.UserHistory, error) {
	history := &models.UserHistory{}
	err := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID).
		Filter("type", historyType).
		Filter("target_id", targetID).
		One(history)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return history, nil
}

// ListUserHistory 获取用户历史记录列表
func (r *userHistoryRepository) ListUserHistory(userID int64, historyType string, offset, limit int) ([]*models.UserHistory, int64, error) {
	var histories []*models.UserHistory

	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID)

	if historyType != "" {
		qs = qs.Filter("type", historyType)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	_, err = qs.OrderBy("-last_visit_at").
		Offset(offset).
		Limit(limit).
		All(&histories)

	return histories, total, err
}

// SearchUserHistory 搜索用户历史记录
func (r *userHistoryRepository) SearchUserHistory(userID int64, keyword string, offset, limit int) ([]*models.UserHistory, int64, error) {
	var histories []*models.UserHistory

	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID).
		Filter("target_name__icontains", keyword)

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	_, err = qs.OrderBy("-last_visit_at").
		Offset(offset).
		Limit(limit).
		All(&histories)

	return histories, total, err
}

// GetHistoryByDateRange 根据日期范围获取历史记录
func (r *userHistoryRepository) GetHistoryByDateRange(userID int64, historyType string, startDate, endDate time.Time, offset, limit int) ([]*models.UserHistory, int64, error) {
	var histories []*models.UserHistory

	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID).
		Filter("last_visit_at__gte", startDate).
		Filter("last_visit_at__lte", endDate)

	if historyType != "" {
		qs = qs.Filter("type", historyType)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	_, err = qs.OrderBy("-last_visit_at").
		Offset(offset).
		Limit(limit).
		All(&histories)

	return histories, total, err
}

// CountUserHistory 统计用户历史记录数量
func (r *userHistoryRepository) CountUserHistory(userID int64, historyType string) (int64, error) {
	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID)

	if historyType != "" {
		qs = qs.Filter("type", historyType)
	}

	return qs.Count()
}

// CountUserHistoryByDateRange 根据日期范围统计历史记录数量
func (r *userHistoryRepository) CountUserHistoryByDateRange(userID int64, historyType string, startDate, endDate time.Time) (int64, error) {
	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID).
		Filter("last_visit_at__gte", startDate).
		Filter("last_visit_at__lte", endDate)

	if historyType != "" {
		qs = qs.Filter("type", historyType)
	}

	return qs.Count()
}

// GetTypeStatistics 获取类型统计
func (r *userHistoryRepository) GetTypeStatistics(userID int64) (map[string]int64, error) {
	var results []orm.Params
	_, err := r.ormer.Raw("SELECT type, COUNT(*) as count FROM user_history WHERE user_id = ? GROUP BY type", userID).Values(&results)
	if err != nil {
		return nil, err
	}

	statistics := make(map[string]int64)
	for _, result := range results {
		historyType := result["type"].(string)
		count, _ := result["count"].(int64)
		statistics[historyType] = count
	}

	return statistics, nil
}

// GetPopularItems 获取热门项目
func (r *userHistoryRepository) GetPopularItems(userID int64, historyType string, limit int) ([]*models.UserHistory, error) {
	var histories []*models.UserHistory

	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID)

	if historyType != "" {
		qs = qs.Filter("type", historyType)
	}

	_, err := qs.OrderBy("-visit_count", "-last_visit_at").
		Limit(limit).
		All(&histories)

	return histories, err
}

// GetHistoryTrend 获取历史记录趋势
func (r *userHistoryRepository) GetHistoryTrend(userID int64, historyType string, days int) (map[string]int64, error) {
	startDate := time.Now().AddDate(0, 0, -days)

	sql := `SELECT DATE(last_visit_at) as date, COUNT(*) as count 
			FROM user_history 
			WHERE user_id = ? AND last_visit_at >= ?`
	params := []interface{}{userID, startDate}

	if historyType != "" {
		sql += " AND type = ?"
		params = append(params, historyType)
	}

	sql += " GROUP BY DATE(last_visit_at) ORDER BY date"

	var results []orm.Params
	_, err := r.ormer.Raw(sql, params...).Values(&results)
	if err != nil {
		return nil, err
	}

	trend := make(map[string]int64)
	for _, result := range results {
		date := result["date"].(string)
		count, _ := result["count"].(int64)
		trend[date] = count
	}

	return trend, nil
}

// ClearUserHistory 清空用户历史记录
func (r *userHistoryRepository) ClearUserHistory(userID int64, historyType string) error {
	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID)

	if historyType != "" {
		qs = qs.Filter("type", historyType)
	}

	_, err := qs.Delete()
	if err != nil {
		return err
	}

	// 清空详情记录
	detailQs := r.ormer.QueryTable(new(models.UserHistoryDetail)).
		Filter("user_id", userID)

	if historyType != "" {
		detailQs = detailQs.Filter("type", historyType)
	}

	_, err = detailQs.Delete()
	return err
}

// ClearUserHistoryByDateRange 根据日期范围清空历史记录
func (r *userHistoryRepository) ClearUserHistoryByDateRange(userID int64, historyType string, startDate, endDate time.Time) error {
	qs := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("user_id", userID).
		Filter("last_visit_at__gte", startDate).
		Filter("last_visit_at__lte", endDate)

	if historyType != "" {
		qs = qs.Filter("type", historyType)
	}

	_, err := qs.Delete()
	return err
}

// CleanupOldHistory 清理旧的历史记录
func (r *userHistoryRepository) CleanupOldHistory(days int) error {
	cutoffDate := time.Now().AddDate(0, 0, -days)

	// 删除主记录
	_, err := r.ormer.QueryTable(new(models.UserHistory)).
		Filter("created_at__lt", cutoffDate).
		Delete()
	if err != nil {
		logs.Error("清理旧历史记录失败: %v", err)
		return err
	}

	// 删除详情记录
	_, err = r.ormer.QueryTable(new(models.UserHistoryDetail)).
		Filter("created_at__lt", cutoffDate).
		Delete()

	return err
}
