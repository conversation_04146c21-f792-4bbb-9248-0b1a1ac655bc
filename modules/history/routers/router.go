/**
 * 用户历史记录路由配置
 *
 * 本文件定义了用户历史记录模块的路由配置，包括所有相关的API接口路由。
 * 提供历史记录的增删改查、统计分析等功能的路由映射。
 */

package routers

import (
	"o_mall_backend/middlewares"
	"o_mall_backend/modules/history/controllers"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// InitHistoryRouter 初始化用户历史记录路由
func InitHistoryRouter() {
	logs.Info("[History] 初始化历史记录路由")

	// 创建用户历史记录命名空间
	historyNS := web.NewNamespace("/api/v1/user/secured/history",
		// 需要登录的API
		web.NSBefore(middlewares.JWTFilter),

		// 历史记录基础操作
		web.NSRouter("/add", &controllers.UserHistoryController{}, "post:Add;options:Options"),
		web.NSRouter("/update/:id", &controllers.UserHistoryController{}, "post:Update;options:Options"),
		web.NSRouter("/delete/:id", &controllers.UserHistoryController{}, "post:Delete;options:Options"),
		web.NSRouter("/batch-delete", &controllers.UserHistoryController{}, "post:BatchDelete;options:Options"),
		web.NSRouter("/detail/:id", &controllers.UserHistoryController{}, "get:Detail;options:Options"),

		// 历史记录查询
		web.NSRouter("/list", &controllers.UserHistoryController{}, "get:List;options:Options"),
		web.NSRouter("/search", &controllers.UserHistoryController{}, "get:Search;options:Options"),
		web.NSRouter("/type/:type", &controllers.UserHistoryController{}, "get:GetByType;options:Options"),

		// 统计和分析
		web.NSRouter("/statistics", &controllers.UserHistoryController{}, "get:Statistics;options:Options"),
		web.NSRouter("/types", &controllers.UserHistoryController{}, "get:Types;options:Options"),
		web.NSRouter("/analytics", &controllers.UserHistoryController{}, "get:Analytics;options:Options"),

		// 清理功能
		web.NSRouter("/clear", &controllers.UserHistoryController{}, "post:Clear;options:Options"),
	)

	// 注册命名空间
	web.AddNamespace(historyNS)
	logs.Info("[History] 历史记录路由注册完成")
}
