/**
 * 用户历史记录服务
 *
 * 本文件实现了用户历史记录的业务逻辑层，处理历史记录的添加、查询、统计等操作。
 * 提供完整的历史记录管理功能，支持多种类型的历史记录。
 */

package services

import (
	"encoding/json"
	"errors"
	"time"

	"o_mall_backend/modules/history/dto"
	"o_mall_backend/modules/history/models"
	"o_mall_backend/modules/history/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// UserHistoryService 用户历史记录服务接口
type UserHistoryService interface {
	// 历史记录操作
	AddHistory(userID int64, req *dto.AddHistoryRequest, ip string) error
	UpdateHistory(userID int64, historyID int64, req *dto.UpdateHistoryRequest) error
	DeleteHistory(userID int64, historyID int64) error
	BatchDeleteHistory(userID int64, req *dto.BatchDeleteRequest) error
	GetHistoryDetail(userID int64, historyID int64) (*dto.UserHistoryDTO, error)

	// 历史记录查询
	ListHistory(userID int64, req *dto.HistoryQueryRequest) (*dto.HistoryListResponse, error)
	SearchHistory(userID int64, keyword string, page, pageSize int) (*dto.HistoryListResponse, error)
	GetHistoryByType(userID int64, historyType string, page, pageSize int) (*dto.HistoryListResponse, error)

	// 统计功能
	GetHistoryStatistics(userID int64) (*dto.HistoryStatisticsDTO, error)
	GetHistoryTypes(userID int64) ([]dto.HistoryTypeDTO, error)
	GetHistoryAnalytics(userID int64, period string) (*dto.HistoryAnalyticsDTO, error)

	// 清理功能
	ClearHistory(userID int64, req *dto.ClearHistoryRequest) error
	CleanupOldHistory(days int) error
}

// userHistoryService 用户历史记录服务实现
type userHistoryService struct {
	historyRepo repositories.UserHistoryRepository
}

// NewUserHistoryService 创建用户历史记录服务实例
func NewUserHistoryService() UserHistoryService {
	return &userHistoryService{
		historyRepo: repositories.NewUserHistoryRepository(),
	}
}

// AddHistory 添加历史记录
func (s *userHistoryService) AddHistory(userID int64, req *dto.AddHistoryRequest, ip string) error {
	// 验证历史记录类型
	if !models.IsValidHistoryType(req.Type) {
		return errors.New("无效的历史记录类型")
	}

	// 检查是否已存在相同的历史记录
	existingHistory, err := s.historyRepo.GetUserHistory(userID, req.Type, req.TargetID)
	if err != nil {
		logs.Error("查询历史记录失败: %v", err)
		return errors.New("添加历史记录失败")
	}

	if existingHistory != nil {
		// 更新现有记录
		existingHistory.VisitCount++
		existingHistory.LastVisitAt = time.Now()
		existingHistory.TargetName = req.TargetName
		existingHistory.TargetImage = req.TargetImage

		err = s.historyRepo.UpdateHistory(existingHistory)
		if err != nil {
			logs.Error("更新历史记录失败: %v", err)
			return errors.New("更新历史记录失败")
		}

		// 创建详情记录
		err = s.createHistoryDetail(existingHistory.ID, userID, req, ip)
		if err != nil {
			logs.Warn("创建历史记录详情失败: %v", err)
		}
	} else {
		// 创建新记录
		history := &models.UserHistory{
			UserID:      userID,
			Type:        req.Type,
			TargetID:    req.TargetID,
			TargetName:  req.TargetName,
			TargetImage: req.TargetImage,
			VisitCount:  1,
		}

		historyID, err := s.historyRepo.CreateHistory(history)
		if err != nil {
			logs.Error("创建历史记录失败: %v", err)
			return errors.New("创建历史记录失败")
		}

		// 创建详情记录
		err = s.createHistoryDetail(historyID, userID, req, ip)
		if err != nil {
			logs.Warn("创建历史记录详情失败: %v", err)
		}
	}

	return nil
}

// createHistoryDetail 创建历史记录详情
func (s *userHistoryService) createHistoryDetail(historyID, userID int64, req *dto.AddHistoryRequest, ip string) error {
	extraDataJSON := ""
	if req.ExtraData != nil {
		data, err := json.Marshal(req.ExtraData)
		if err == nil {
			extraDataJSON = string(data)
		}
	}

	detail := &models.UserHistoryDetail{
		HistoryID: historyID,
		UserID:    userID,
		Type:      req.Type,
		TargetID:  req.TargetID,
		ExtraData: extraDataJSON,
		UserAgent: req.UserAgent,
		IP:        ip,
		Platform:  req.Platform,
		Source:    req.Source,
		Duration:  req.Duration,
	}

	_, err := s.historyRepo.CreateHistoryDetail(detail)
	return err
}

// UpdateHistory 更新历史记录
func (s *userHistoryService) UpdateHistory(userID int64, historyID int64, req *dto.UpdateHistoryRequest) error {
	history, err := s.historyRepo.GetHistoryByID(historyID)
	if err != nil {
		return err
	}

	if history.UserID != userID {
		return errors.New("无权限操作此历史记录")
	}

	// 目前只支持更新停留时长
	// 可以根据需要扩展更多字段的更新

	return s.historyRepo.UpdateHistory(history)
}

// DeleteHistory 删除历史记录
func (s *userHistoryService) DeleteHistory(userID int64, historyID int64) error {
	return s.historyRepo.DeleteHistory(historyID, userID)
}

// BatchDeleteHistory 批量删除历史记录
func (s *userHistoryService) BatchDeleteHistory(userID int64, req *dto.BatchDeleteRequest) error {
	if len(req.IDs) == 0 {
		return errors.New("请选择要删除的记录")
	}

	return s.historyRepo.BatchDeleteHistory(req.IDs, userID)
}

// GetHistoryDetail 获取历史记录详情
func (s *userHistoryService) GetHistoryDetail(userID int64, historyID int64) (*dto.UserHistoryDTO, error) {
	history, err := s.historyRepo.GetHistoryByID(historyID)
	if err != nil {
		return nil, err
	}

	if history.UserID != userID {
		return nil, errors.New("无权限查看此历史记录")
	}

	return s.convertToHistoryDTO(history), nil
}

// ListHistory 获取历史记录列表
func (s *userHistoryService) ListHistory(userID int64, req *dto.HistoryQueryRequest) (*dto.HistoryListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	var histories []*models.UserHistory
	var total int64
	var err error

	// 根据查询条件获取数据
	if req.StartDate != "" && req.EndDate != "" {
		startDate, err1 := time.Parse("2006-01-02", req.StartDate)
		endDate, err2 := time.Parse("2006-01-02", req.EndDate)
		if err1 != nil || err2 != nil {
			return nil, errors.New("日期格式错误")
		}
		endDate = endDate.Add(24*time.Hour - time.Second) // 包含结束日期的整天

		histories, total, err = s.historyRepo.GetHistoryByDateRange(userID, req.Type, startDate, endDate, offset, req.PageSize)
	} else if req.Keyword != "" {
		histories, total, err = s.historyRepo.SearchUserHistory(userID, req.Keyword, offset, req.PageSize)
	} else {
		histories, total, err = s.historyRepo.ListUserHistory(userID, req.Type, offset, req.PageSize)
	}

	if err != nil {
		logs.Error("查询历史记录失败: %v", err)
		return nil, errors.New("查询历史记录失败")
	}

	// 转换为DTO
	historyDTOs := make([]dto.UserHistoryDTO, 0, len(histories))
	for _, history := range histories {
		historyDTOs = append(historyDTOs, *s.convertToHistoryDTO(history))
	}

	return &dto.HistoryListResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     historyDTOs,
	}, nil
}

// SearchHistory 搜索历史记录
func (s *userHistoryService) SearchHistory(userID int64, keyword string, page, pageSize int) (*dto.HistoryListResponse, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	histories, total, err := s.historyRepo.SearchUserHistory(userID, keyword, offset, pageSize)
	if err != nil {
		logs.Error("搜索历史记录失败: %v", err)
		return nil, errors.New("搜索历史记录失败")
	}

	// 转换为DTO
	historyDTOs := make([]dto.UserHistoryDTO, 0, len(histories))
	for _, history := range histories {
		historyDTOs = append(historyDTOs, *s.convertToHistoryDTO(history))
	}

	return &dto.HistoryListResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		List:     historyDTOs,
	}, nil
}

// GetHistoryByType 根据类型获取历史记录
func (s *userHistoryService) GetHistoryByType(userID int64, historyType string, page, pageSize int) (*dto.HistoryListResponse, error) {
	if !models.IsValidHistoryType(historyType) {
		return nil, errors.New("无效的历史记录类型")
	}

	req := &dto.HistoryQueryRequest{
		Type:     historyType,
		Page:     page,
		PageSize: pageSize,
	}

	return s.ListHistory(userID, req)
}

// GetHistoryStatistics 获取历史记录统计
func (s *userHistoryService) GetHistoryStatistics(userID int64) (*dto.HistoryStatisticsDTO, error) {
	// 获取总数
	totalCount, err := s.historyRepo.CountUserHistory(userID, "")
	if err != nil {
		return nil, err
	}

	// 获取今日数量
	today := time.Now()
	startOfDay := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
	endOfDay := startOfDay.Add(24*time.Hour - time.Second)
	todayCount, err := s.historyRepo.CountUserHistoryByDateRange(userID, "", startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	// 获取本周数量
	weekStart := getWeekStart(today)
	weekEnd := weekStart.Add(7*24*time.Hour - time.Second)
	weekCount, err := s.historyRepo.CountUserHistoryByDateRange(userID, "", weekStart, weekEnd)
	if err != nil {
		return nil, err
	}

	// 获取本月数量
	monthStart := time.Date(today.Year(), today.Month(), 1, 0, 0, 0, 0, today.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Second)
	monthCount, err := s.historyRepo.CountUserHistoryByDateRange(userID, "", monthStart, monthEnd)
	if err != nil {
		return nil, err
	}

	// 获取类型统计
	typeStats, err := s.historyRepo.GetTypeStatistics(userID)
	if err != nil {
		return nil, err
	}

	typeStatistics := make([]dto.TypeStatisticsDTO, 0)
	historyTypes := models.GetHistoryTypes()
	for _, historyType := range historyTypes {
		count := typeStats[historyType.Type]
		percentage := float64(0)
		if totalCount > 0 {
			percentage = float64(count) / float64(totalCount) * 100
		}

		typeStatistics = append(typeStatistics, dto.TypeStatisticsDTO{
			Type:       historyType.Type,
			TypeName:   historyType.Name,
			Count:      count,
			Percentage: percentage,
		})
	}

	// 获取最近历史记录
	recentHistories, _, err := s.historyRepo.ListUserHistory(userID, "", 0, 10)
	if err != nil {
		return nil, err
	}

	recentHistory := make([]dto.UserHistoryDTO, 0, len(recentHistories))
	for _, history := range recentHistories {
		recentHistory = append(recentHistory, *s.convertToHistoryDTO(history))
	}

	// 获取热门项目
	popularItems, err := s.historyRepo.GetPopularItems(userID, "", 10)
	if err != nil {
		return nil, err
	}

	popularItemDTOs := make([]dto.PopularItemDTO, 0, len(popularItems))
	for _, item := range popularItems {
		typeName := s.getTypeNameByType(item.Type)
		popularItemDTOs = append(popularItemDTOs, dto.PopularItemDTO{
			Type:        item.Type,
			TypeName:    typeName,
			TargetID:    item.TargetID,
			TargetName:  item.TargetName,
			TargetImage: item.TargetImage,
			VisitCount:  item.VisitCount,
			LastVisitAt: item.LastVisitAt,
		})
	}

	return &dto.HistoryStatisticsDTO{
		TotalCount:     totalCount,
		TodayCount:     todayCount,
		WeekCount:      weekCount,
		MonthCount:     monthCount,
		TypeStatistics: typeStatistics,
		RecentHistory:  recentHistory,
		PopularItems:   popularItemDTOs,
	}, nil
}

// GetHistoryTypes 获取历史记录类型列表
func (s *userHistoryService) GetHistoryTypes(userID int64) ([]dto.HistoryTypeDTO, error) {
	// 获取类型统计
	typeStats, err := s.historyRepo.GetTypeStatistics(userID)
	if err != nil {
		return nil, err
	}

	historyTypes := models.GetHistoryTypes()
	result := make([]dto.HistoryTypeDTO, 0, len(historyTypes))

	for _, historyType := range historyTypes {
		count := typeStats[historyType.Type]
		result = append(result, dto.HistoryTypeDTO{
			Type:        historyType.Type,
			Name:        historyType.Name,
			Description: historyType.Description,
			Count:       count,
		})
	}

	return result, nil
}

// GetHistoryAnalytics 获取历史记录分析
func (s *userHistoryService) GetHistoryAnalytics(userID int64, period string) (*dto.HistoryAnalyticsDTO, error) {
	days := 7 // 默认7天
	switch period {
	case "day":
		days = 1
	case "week":
		days = 7
	case "month":
		days = 30
	}

	// 获取趋势数据
	trendData, err := s.historyRepo.GetHistoryTrend(userID, "", days)
	if err != nil {
		return nil, err
	}

	trendDTOs := make([]dto.HistoryTrendDTO, 0)
	for date, count := range trendData {
		trendDTOs = append(trendDTOs, dto.HistoryTrendDTO{
			Date:  date,
			Count: count,
		})
	}

	// 获取类型分布
	typeStats, err := s.historyRepo.GetTypeStatistics(userID)
	if err != nil {
		return nil, err
	}

	totalCount, _ := s.historyRepo.CountUserHistory(userID, "")
	typeData := make([]dto.TypeStatisticsDTO, 0)
	historyTypes := models.GetHistoryTypes()
	for _, historyType := range historyTypes {
		count := typeStats[historyType.Type]
		percentage := float64(0)
		if totalCount > 0 {
			percentage = float64(count) / float64(totalCount) * 100
		}

		typeData = append(typeData, dto.TypeStatisticsDTO{
			Type:       historyType.Type,
			TypeName:   historyType.Name,
			Count:      count,
			Percentage: percentage,
		})
	}

	return &dto.HistoryAnalyticsDTO{
		Period:     period,
		TrendData:  trendDTOs,
		TypeData:   typeData,
		PeakHours:  []int{9, 12, 15, 18, 21},                                         // 示例数据，实际应该从数据库计算
		ActiveDays: []string{"Monday", "Tuesday", "Wednesday", "Thursday", "Friday"}, // 示例数据
	}, nil
}

// ClearHistory 清空历史记录
func (s *userHistoryService) ClearHistory(userID int64, req *dto.ClearHistoryRequest) error {
	if req.StartDate != "" && req.EndDate != "" {
		startDate, err1 := time.Parse("2006-01-02", req.StartDate)
		endDate, err2 := time.Parse("2006-01-02", req.EndDate)
		if err1 != nil || err2 != nil {
			return errors.New("日期格式错误")
		}
		endDate = endDate.Add(24*time.Hour - time.Second)

		return s.historyRepo.ClearUserHistoryByDateRange(userID, req.Type, startDate, endDate)
	}

	return s.historyRepo.ClearUserHistory(userID, req.Type)
}

// CleanupOldHistory 清理旧的历史记录
func (s *userHistoryService) CleanupOldHistory(days int) error {
	return s.historyRepo.CleanupOldHistory(days)
}

// convertToHistoryDTO 转换为历史记录DTO
func (s *userHistoryService) convertToHistoryDTO(history *models.UserHistory) *dto.UserHistoryDTO {
	typeName := s.getTypeNameByType(history.Type)

	return &dto.UserHistoryDTO{
		ID:          history.ID,
		UserID:      history.UserID,
		Type:        history.Type,
		TypeName:    typeName,
		TargetID:    history.TargetID,
		TargetName:  history.TargetName,
		TargetImage: history.TargetImage,
		VisitCount:  history.VisitCount,
		LastVisitAt: history.LastVisitAt,
		CreatedAt:   history.CreatedAt,
	}
}

// getTypeNameByType 根据类型获取类型名称
func (s *userHistoryService) getTypeNameByType(historyType string) string {
	historyTypes := models.GetHistoryTypes()
	for _, ht := range historyTypes {
		if ht.Type == historyType {
			return ht.Name
		}
	}
	return historyType
}

// getWeekStart 获取本周开始时间（周一）
func getWeekStart(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日转换为7
	}
	days := weekday - 1 // 距离周一的天数
	return time.Date(t.Year(), t.Month(), t.Day()-days, 0, 0, 0, 0, t.Location())
}
