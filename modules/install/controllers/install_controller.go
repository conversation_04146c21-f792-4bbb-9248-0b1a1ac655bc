/**
 * 安装控制器
 *
 * 该文件实现了系统安装向导的控制器，处理安装过程中的各个步骤，
 * 包括检查安装状态、配置数据库连接、创建管理员账号和完成安装。
 */

package controllers

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/dto"
	"o_mall_backend/modules/admin/models"
	install_dto "o_mall_backend/modules/install/dto"
	"o_mall_backend/utils"
)

// InstallController 安装控制器
type InstallController struct {
	web.Controller
}

// 安装锁文件路径
const installLockFile = "./data/install.lock"

// CheckInstallStatus 检查安装状态
// @Title 检查安装状态
// @Description 检查系统是否已经安装
// @Success 200 {object} dto.Response 成功返回安装状态
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /check [get]
func (c *InstallController) CheckInstallStatus() {
	// 检查安装锁文件是否存在
	installed := isInstalled()

	// 检查数据库连接是否配置
	dbConfigured := isDatabaseConfigured()

	// 返回安装状态
	result := map[string]interface{}{
		"installed":    installed,
		"dbConfigured": dbConfigured,
	}

	c.Data["json"] = dto.NewSuccessResponse(result)
	c.ServeJSON()
}

// ConfigDatabase 配置数据库连接
// @Title 配置数据库连接
// @Description 配置系统数据库连接信息
// @Param	body	body	install_dto.DatabaseConfig	true	"数据库配置信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /database [post]
func (c *InstallController) ConfigDatabase() {
	// 检查是否已安装
	if isInstalled() {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "系统已安装，不能重复安装")
		c.ServeJSON()
		return
	}

	// 解析请求体
	var req install_dto.DatabaseConfig
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 测试数据库连接
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=true&loc=Local",
		req.Username, req.Password, req.Host, req.Port, req.Database)

	// 尝试连接数据库
	err = orm.RegisterDataBase("test", "mysql", dsn)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "数据库连接失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 关闭测试连接
	o := orm.NewOrm()
	_, err = o.Raw(fmt.Sprintf("DROP DATABASE IF EXISTS `%s`", req.Database)).Exec()
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "删除测试数据库失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 更新配置文件
	err = updateDatabaseConfig(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "更新配置文件失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(nil)
	c.ServeJSON()
}

// CreateAdmin 创建管理员账号
// @Title 创建管理员账号
// @Description 创建系统初始管理员账号
// @Param	body	body	install_dto.AdminConfig	true	"管理员账号信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /admin [post]
func (c *InstallController) CreateAdmin() {
	// 检查是否已安装
	if isInstalled() {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "系统已安装，不能重复安装")
		c.ServeJSON()
		return
	}

	// 检查数据库是否已配置
	if !isDatabaseConfigured() {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "请先配置数据库")
		c.ServeJSON()
		return
	}

	// 解析请求体
	var req install_dto.AdminConfig
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "参数验证失败")
		c.ServeJSON()
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, errMsg)
		c.ServeJSON()
		return
	}

	// 初始化数据库连接
	initDatabase()

	// 创建数据表
	err = orm.RunSyncdb("default", false, true)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "创建数据表失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 创建管理员账号
	admin := &models.Admin{
		Username: req.Username,
		Nickname: req.Nickname,
		Email:    req.Email,
		Role:     models.AdminRoleSuper, // 设置为超级管理员
		Status:   models.AdminStatusNormal,
	}

	// 加密密码
	encryptedPassword, err := utils.EncryptPassword(req.Password)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "密码加密失败: "+err.Error())
		c.ServeJSON()
		return
	}
	admin.Password = encryptedPassword

	// 保存管理员账号
	o := orm.NewOrm()
	_, err = o.Insert(admin)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "创建管理员账号失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(nil)
	c.ServeJSON()
}

// CompleteInstall 完成安装
// @Title 完成安装
// @Description 完成系统安装，创建安装锁文件
// @Success 200 {object} dto.Response 成功返回
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /complete [post]
func (c *InstallController) CompleteInstall() {
	// 检查是否已安装
	if isInstalled() {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "系统已安装，不能重复安装")
		c.ServeJSON()
		return
	}

	// 检查数据库是否已配置
	if !isDatabaseConfigured() {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeForbidden, "请先配置数据库")
		c.ServeJSON()
		return
	}

	// 创建安装锁文件
	err := createInstallLock()
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "创建安装锁文件失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(nil)
	c.ServeJSON()
}

// 检查系统是否已安装
func isInstalled() bool {
	_, err := os.Stat(installLockFile)
	return !os.IsNotExist(err)
}

// 检查数据库是否已配置
func isDatabaseConfigured() bool {
	// 检查配置文件中是否有数据库配置
	user, err := web.AppConfig.String("mysqluser")
	if err != nil || user == "" {
		return false
	}

	pass, err := web.AppConfig.String("mysqlpass")
	if err != nil || pass == "" {
		return false
	}

	host, err := web.AppConfig.String("mysqlhost")
	if err != nil || host == "" {
		return false
	}

	return true
}

// 更新数据库配置
func updateDatabaseConfig(config *install_dto.DatabaseConfig) error {
	// 更新配置文件
	err := web.AppConfig.Set("mysqluser", config.Username)
	if err != nil {
		return err
	}

	err = web.AppConfig.Set("mysqlpass", config.Password)
	if err != nil {
		return err
	}

	err = web.AppConfig.Set("mysqlhost", config.Host)
	if err != nil {
		return err
	}

	err = web.AppConfig.Set("mysqlport", config.Port)
	if err != nil {
		return err
	}

	err = web.AppConfig.Set("mysqldb", config.Database)
	if err != nil {
		return err
	}

	// 保存配置到文件
	return web.AppConfig.SaveConfigFile("./conf/app.conf")
}

// 初始化数据库连接
func initDatabase() {
	user, _ := web.AppConfig.String("mysqluser")
	pass, _ := web.AppConfig.String("mysqlpass")
	host, _ := web.AppConfig.String("mysqlhost")
	port, _ := web.AppConfig.String("mysqlport")
	dbName, _ := web.AppConfig.String("mysqldb")

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=true&loc=Local",
		user, pass, host, port, dbName)

	// 注册数据库
	_ = orm.RegisterDriver("mysql", orm.DRMySQL)
	_ = orm.RegisterDataBase("default", "mysql", dsn)
}

// 创建安装锁文件
func createInstallLock() error {
	// 确保目录存在
	dir := filepath.Dir(installLockFile)
	err := os.MkdirAll(dir, 0755)
	if err != nil {
		return err
	}

	// 创建锁文件
	f, err := os.Create(installLockFile)
	if err != nil {
		return err
	}
	defer f.Close()

	// 写入安装时间
	_, err = f.WriteString(fmt.Sprintf("Installed at: %s\n", time.Now().Format(time.RFC3339)))
	if err != nil {
		return err
	}
	return nil
}
