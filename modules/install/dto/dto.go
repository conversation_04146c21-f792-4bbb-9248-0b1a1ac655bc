/**
 * 安装模块数据传输对象
 *
 * 该文件定义了安装过程中使用的数据传输对象，包括数据库配置和管理员配置等。
 * 这些对象用于前端和后端之间的数据交换。
 */

package dto

import (
	"github.com/beego/beego/v2/core/validation"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	// Host 数据库主机地址
	Host string `json:"host" valid:"Required;MaxSize(100)"`

	// Port 数据库端口
	Port string `json:"port" valid:"Required;MaxSize(10)"`

	// Username 数据库用户名
	Username string `json:"username" valid:"Required;MaxSize(50)"`

	// Password 数据库密码
	Password string `json:"password" valid:"Required;MaxSize(100)"`

	// Database 数据库名称
	Database string `json:"database" valid:"Required;MaxSize(50)"`
}

// Valid 验证数据库配置
func (d *DatabaseConfig) Valid(v *validation.Validation) {
	if d.Host == "" {
		v.SetError("host", "数据库主机地址不能为空")
	}

	if d.Port == "" {
		v.SetError("port", "数据库端口不能为空")
	}

	if d.Username == "" {
		v.SetError("username", "数据库用户名不能为空")
	}

	if d.Password == "" {
		v.SetError("password", "数据库密码不能为空")
	}

	if d.Database == "" {
		v.SetError("database", "数据库名称不能为空")
	}
}

// AdminConfig 管理员配置
type AdminConfig struct {
	// Username 管理员用户名
	Username string `json:"username" valid:"Required;MinSize(4);MaxSize(20)"`

	// Password 管理员密码
	Password string `json:"password" valid:"Required;MinSize(6);MaxSize(20)"`

	// Nickname 管理员昵称
	Nickname string `json:"nickname" valid:"Required;MaxSize(50)"`

	// Email 管理员邮箱
	Email string `json:"email" valid:"Required;Email;MaxSize(100)"`
}

// Valid 验证管理员配置
func (a *AdminConfig) Valid(v *validation.Validation) {
	if a.Username == "" {
		v.SetError("username", "管理员用户名不能为空")
	} else if len(a.Username) < 4 {
		v.SetError("username", "管理员用户名长度不能小于4个字符")
	} else if len(a.Username) > 20 {
		v.SetError("username", "管理员用户名长度不能超过20个字符")
	}

	if a.Password == "" {
		v.SetError("password", "管理员密码不能为空")
	} else if len(a.Password) < 6 {
		v.SetError("password", "管理员密码长度不能小于6个字符")
	} else if len(a.Password) > 20 {
		v.SetError("password", "管理员密码长度不能超过20个字符")
	}

	if a.Nickname == "" {
		v.SetError("nickname", "管理员昵称不能为空")
	}

	if a.Email == "" {
		v.SetError("email", "管理员邮箱不能为空")
	}
}
