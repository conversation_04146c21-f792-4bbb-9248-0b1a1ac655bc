/**
 * 安装模块路由注册
 *
 * 该文件实现了安装模块的路由注册，将API接口与控制器方法关联。
 * 路由定义了API的访问路径、HTTP方法以及是否需要认证。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/install/controllers"
)

// InitRouters 初始化安装模块路由
func InitRouters() {
	// 创建安装命名空间
	installNS := web.NewNamespace("/api/v1/install",
		// 安装向导相关路由
		web.NSRouter("/check", &controllers.InstallController{}, "get:CheckInstallStatus"),
		web.NSRouter("/database", &controllers.InstallController{}, "post:ConfigDatabase"),
		web.NSRouter("/admin", &controllers.InstallController{}, "post:CreateAdmin"),
		web.NSRouter("/complete", &controllers.InstallController{}, "post:CompleteInstall"),
	)

	// 注册命名空间
	web.AddNamespace(installNS)
}
