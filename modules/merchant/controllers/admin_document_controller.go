/**
 * 管理员证件管理控制器
 *
 * 该文件实现了管理员对商家证件进行管理的API接口控制器，包括查询、审核等功能。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/merchant/core"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils/common"
)

// AdminDocumentController 管理员证件管理控制器
type AdminDocumentController struct {
	web.Controller
	documentService services.MerchantDocumentService
}

// Prepare 初始化控制器
func (c *AdminDocumentController) Prepare() {
	c.documentService = services.NewMerchantDocumentService()
}

// ParseRequest 解析请求体
func (c *AdminDocumentController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetMerchantDocuments 获取商家证件列表
// @Title 获取商家证件列表
// @Description 管理员获取指定商家的证件列表
// @Param	merchant_id	query	int	true	"商家ID"
// @Param	type	query	int	false	"证件类型，0表示查询所有类型"
// @Success 200 {object} dto.Response 成功返回证件列表
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /documents [get]
func (c *AdminDocumentController) GetMerchantDocuments() {
	// 获取查询参数
	merchantIDStr := c.GetString("merchant_id")
	if merchantIDStr == "" {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "商家ID不能为空"))
		return
	}

	merchantID, err := core.ParseInt64(merchantIDStr)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的商家ID参数"))
		return
	}

	typeStr := c.GetString("type", "0")
	docType, err := core.ParseInt(typeStr)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的证件类型参数"))
		return
	}

	// 调用服务获取证件列表
	var documents []*merchantDto.DocumentResponse
	if docType == 0 {
		// 获取所有类型的证件
		documents, err = c.documentService.GetDocumentsByMerchantID(c.Ctx.Request.Context(), merchantID)
	} else {
		// 获取指定类型的证件
		documents, err = c.documentService.GetDocumentsByType(c.Ctx.Request.Context(), merchantID, docType)
	}

	if err != nil {
		logs.Error("获取商家证件列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, documents)
}

// GetDocument 获取证件详情
// @Title 获取证件详情
// @Description 管理员获取指定ID的证件详情
// @Param	id	path	int	true	"证件ID"
// @Success 200 {object} dto.Response 成功返回证件详情
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 404 {object} dto.Response 证件不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /document/:id [get]
func (c *AdminDocumentController) GetDocument() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的ID参数"))
		return
	}

	// 调用服务获取证件详情
	document, err := c.documentService.GetDocumentByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取证件详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if document == nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeNotFound, "证件不存在"))
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, document)
}

// VerifyDocument 审核证件
// @Title 审核证件
// @Description 管理员审核指定ID的证件
// @Param	id	path	int	true	"证件ID"
// @Param	body	body	merchantDto.VerifyDocumentRequest	true	"审核信息"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权限
// @Failure 404 {object} dto.Response 证件不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /document/:id/verify [post]
func (c *AdminDocumentController) VerifyDocument() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的ID参数"))
		return
	}

	// 解析请求体
	var req merchantDto.VerifyDocumentRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置ID
	req.ID = id

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 获取当前管理员ID
	adminID, err := core.GetCurrentUserID(c.Ctx.Request)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务审核证件
	err = c.documentService.VerifyDocument(c.Ctx.Request.Context(), &req, adminID)
	if err != nil {
		logs.Error("审核证件失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
