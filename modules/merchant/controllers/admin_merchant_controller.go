/**
 * 管理员端商家管理控制器
 *
 * 该文件实现了管理员对商家进行管理的API接口控制器，包括商家审核、查询等功能。
 * 这些接口仅供系统管理员使用，需要管理员权限验证。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils/common"
)

// AdminMerchantController 管理员端商家管理控制器
type AdminMerchantController struct {
	web.Controller
	merchantService services.MerchantService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *AdminMerchantController) Prepare() {
	c.merchantService = services.NewMerchantService()
}

// ParseRequest 解析请求体
func (c *AdminMerchantController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetMerchantByID 获取商家详情
// @Title 获取商家详情
// @Description 管理员获取指定商家的详细信息
// @Param	id	path	int	true	"商家ID"
// @Success 200 {object} result.Response 成功返回商家信息
// @Failure 400 {object} result.Response 参数错误
// @Failure 401 {object} result.Response 未认证
// @Failure 403 {object} result.Response 无权限
// @Failure 500 {object} result.Response 服务器内部错误
// @router /:id [get]
func (c *AdminMerchantController) GetMerchantByID() {
	// 验证管理员权限
	role, ok := c.Ctx.Input.GetData("role").(string)
	if !ok || role != "super" {
		result.HandleError(c.Ctx, result.NewError(result.CodeForbidden, "无权限执行此操作"))
		return
	}

	// 获取商家ID
	id, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的商家ID"))
		return
	}

	// 调用服务层获取商家信息
	merchantInfo, err := c.merchantService.GetMerchantByID(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, merchantInfo)
}

// ListMerchants 获取商家列表
// @Title 获取商家列表
// @Description 管理员分页获取商家列表
// @Param	page	query	int	false	"页码"
// @Param	pageSize	query	int	false	"每页数量"
// @Param	name	query	string	false	"商家名称搜索"
// @Param	status	query	int	false	"状态筛选"
// @Param	level	query	int	false	"等级筛选"
// @Success 200 {object} result.Response 成功返回商家列表和总数
// @Failure 401 {object} result.Response 未认证
// @Failure 403 {object} result.Response 无权限
// @Failure 500 {object} result.Response 服务器内部错误
// @router / [get]
func (c *AdminMerchantController) ListMerchants() {
	// 验证管理员权限
	role, ok := c.Ctx.Input.GetData("role").(string)
	if !ok || role != "super" {
		result.HandleError(c.Ctx, result.NewError(result.CodeForbidden, "无权限执行此操作"))
		return
	}

	// 获取查询参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)
	name := c.GetString("name", "")
	status, _ := c.GetInt("status", -1)
	level, _ := c.GetInt("level", -1)

	// 创建查询请求
	req := &merchantDto.MerchantQueryRequest{
		Page:     page,
		PageSize: pageSize,
		Name:     name,
		Status:   status,
		Level:    level,
	}

	// 调用服务层获取商家列表
	merchants, total, err := c.merchantService.ListMerchants(c.Ctx.Request.Context(), req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"list":  merchants,
		"total": total,
		"page":  page,
		"size":  pageSize,
	}

	// 返回成功响应
	result.OK(c.Ctx, responseData)
}

// AuditMerchant 审核商家
// @Title 审核商家
// @Description 管理员审核商家注册申请
// @Param	body	body	merchantDto.AuditMerchantRequest	true	"审核信息"
// @Success 200 {object} result.Response 成功返回
// @Failure 400 {object} result.Response 参数错误
// @Failure 401 {object} result.Response 未认证
// @Failure 403 {object} result.Response 无权限
// @Failure 500 {object} result.Response 服务器内部错误
// @router /audit [post]
func (c *AdminMerchantController) AuditMerchant() {
	// 验证管理员权限
	role, ok := c.Ctx.Input.GetData("role").(string)
	if !ok || role != "super" {
		result.HandleError(c.Ctx, result.NewError(result.CodeForbidden, "无权限执行此操作"))
		return
	}

	// 解析请求体
	var req merchantDto.AuditMerchantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层审核商家
	err = c.merchantService.AuditMerchant(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// ResetPassword 重置商家密码
// @Title 重置商家密码
// @Description 管理员重置商家密码
// @Param	body	body	merchantDto.ResetPasswordRequest	true	"密码信息"
// @Success 200 {object} result.Response 成功返回
// @Failure 400 {object} result.Response 参数错误
// @Failure 401 {object} result.Response 未认证
// @Failure 403 {object} result.Response 无权限
// @Failure 500 {object} result.Response 服务器内部错误
// @router /password/reset [put]
func (c *AdminMerchantController) ResetPassword() {
	// 验证管理员权限
	role, ok := c.Ctx.Input.GetData("role").(string)
	if !ok || role != "super" {
		result.HandleError(c.Ctx, result.NewError(result.CodeForbidden, "无权限执行此操作"))
		return
	}

	// 解析请求体
	var req merchantDto.ResetPasswordRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层重置密码
	err = c.merchantService.ResetPassword(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateBalance 更新商家余额
// @Title 更新商家余额
// @Description 管理员更新商家账户余额
// @Param	body	body	merchantDto.UpdateBalanceRequest	true	"余额信息"
// @Success 200 {object} result.Response 成功返回
// @Failure 400 {object} result.Response 参数错误
// @Failure 401 {object} result.Response 未认证
// @Failure 403 {object} result.Response 无权限
// @Failure 500 {object} result.Response 服务器内部错误
// @router /balance [put]
func (c *AdminMerchantController) UpdateBalance() {
	// 验证管理员权限
	role, ok := c.Ctx.Input.GetData("role").(string)
	if !ok || role != "super" {
		result.HandleError(c.Ctx, result.NewError(result.CodeForbidden, "无权限执行此操作"))
		return
	}

	// 解析请求体
	var req merchantDto.UpdateBalanceRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层更新余额
	err = c.merchantService.UpdateBalance(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteMerchant 删除商家
// @Title 删除商家
// @Description 管理员删除商家账号
// @Param	id	path	int	true	"商家ID"
// @Success 200 {object} result.Response 成功返回
// @Failure 400 {object} result.Response 参数错误
// @Failure 401 {object} result.Response 未认证
// @Failure 403 {object} result.Response 无权限
// @Failure 500 {object} result.Response 服务器内部错误
// @router /:id [delete]
func (c *AdminMerchantController) DeleteMerchant() {
	// 验证管理员权限
	role, ok := c.Ctx.Input.GetData("role").(string)
	if !ok || role != "super" {
		result.HandleError(c.Ctx, result.NewError(result.CodeForbidden, "无权限执行此操作"))
		return
	}

	// 获取商家ID
	id, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的商家ID"))
		return
	}

	// 调用服务层删除商家
	err = c.merchantService.DeleteMerchant(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
