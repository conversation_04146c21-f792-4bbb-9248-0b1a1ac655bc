/**
 * 商家经营分类控制器
 *
 * 本文件实现了商家经营类型分类的管理功能，包括分类的查询、创建、更新和删除。
 * 控制器负责接收HTTP请求，处理参数验证，调用相应的服务层方法，并返回处理结果。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils/common"
)

// MerchantBusinessCategoryController 商家经营分类控制器
type MerchantBusinessCategoryController struct {
	web.Controller
	categoryService services.MerchantCategoryService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *MerchantBusinessCategoryController) Prepare() {
	c.categoryService = services.NewMerchantCategoryService()
}

// ParseRequest 解析请求体
func (c *MerchantBusinessCategoryController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// ListCategories 获取商家经营分类列表
// @Title 获取商家经营分类列表
// @Description 获取商家经营分类列表，支持分页和条件查询
// @Param	page	    query	int	    false	"页码，默认1"
// @Param	pageSize	query	int	    false	"每页数量，默认10"
// @Param	name	    query	string	false	"分类名称，模糊查询"
// @Param	is_show	    query	bool	false	"是否显示"
// @Success 200 {object} result.Response 成功返回分类列表
// @Failure 401 {object} result.Response 未认证
// @Failure 500 {object} result.Response 服务器内部错误
// @router /business-categories [get]
func (c *MerchantBusinessCategoryController) ListCategories() {
	// 解析查询参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)
	name := c.GetString("name")
	
	var isShow *bool
	if isShowStr := c.GetString("is_show"); isShowStr != "" {
		isShowVal, err := c.GetBool("is_show")
		if err == nil {
			isShow = &isShowVal
		}
	}

	// 构建查询请求
	req := &merchantDto.MerchantCategoryListRequest{
		Page:     page,
		PageSize: pageSize,
		Name:     name,
		IsShow:   isShow,
	}

	// 调用服务层获取分类列表
	resp, err := c.categoryService.ListCategories(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("获取商家经营分类列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// GetAllCategories 获取所有商家经营分类（不分页）
// @Title 获取所有商家经营分类
// @Description 获取所有商家经营分类，可选择只返回显示的分类
// @Param	only_show	query	bool	false	"是否只返回显示的分类，默认false"
// @Success 200 {object} result.Response 成功返回分类列表
// @Failure 401 {object} result.Response 未认证
// @Failure 500 {object} result.Response 服务器内部错误
// @router /business-categories/all [get]
func (c *MerchantBusinessCategoryController) GetAllCategories() {
	// 解析查询参数
	onlyShow, _ := c.GetBool("only_show", false)

	// 调用服务层获取所有分类
	categories, err := c.categoryService.GetAllCategories(c.Ctx.Request.Context(), onlyShow)
	if err != nil {
		logs.Error("获取所有商家经营分类失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, categories)
}

// GetCategoryByID 获取商家经营分类详情
// @Title 获取商家经营分类详情
// @Description 获取指定ID的商家经营分类信息
// @Param	id	path	int	true	"分类ID"
// @Success 200 {object} result.Response 成功返回分类详情
// @Failure 401 {object} result.Response 未认证
// @Failure 404 {object} result.Response 分类不存在
// @Failure 500 {object} result.Response 服务器内部错误
// @router /business-categories/:id [get]
func (c *MerchantBusinessCategoryController) GetCategoryByID() {
	// 获取分类ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的分类ID"))
		return
	}

	// 调用服务层获取分类详情
	category, err := c.categoryService.GetCategoryByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取商家经营分类详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, category)
}

// CreateCategory 创建商家经营分类
// @Title 创建商家经营分类
// @Description 创建商家经营分类
// @Param	body	body	merchantDto.CreateMerchantCategoryRequest	true	"创建分类请求"
// @Success 200 {object} result.Response 成功返回创建的分类ID
// @Failure 401 {object} result.Response 未认证
// @Failure 400 {object} result.Response 参数错误
// @Failure 500 {object} result.Response 服务器内部错误
// @router /business-categories [post]
func (c *MerchantBusinessCategoryController) CreateCategory() {
	// 从请求体解析创建请求
	var req merchantDto.CreateMerchantCategoryRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数验证
	valid := validation.Validation{}
	_, err := valid.Valid(&req)
	if err != nil {
		logs.Error("参数验证错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	if req.Name == "" {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "分类名称不能为空"))
		return
	}

	// 调用服务层创建分类
	id, err := c.categoryService.CreateCategory(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建商家经营分类失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// UpdateCategory 更新商家经营分类
// @Title 更新商家经营分类
// @Description 更新商家经营分类
// @Param	id	path	int	true	"分类ID"
// @Param	body	body	merchantDto.UpdateMerchantCategoryRequest	true	"更新分类请求"
// @Success 200 {object} result.Response 更新成功
// @Failure 401 {object} result.Response 未认证
// @Failure 400 {object} result.Response 参数错误
// @Failure 404 {object} result.Response 分类不存在
// @Failure 500 {object} result.Response 服务器内部错误
// @router /business-categories/:id [put]
func (c *MerchantBusinessCategoryController) UpdateCategory() {
	// 获取分类ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的分类ID"))
		return
	}

	// 从请求体解析更新请求
	var req merchantDto.UpdateMerchantCategoryRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数验证
	valid := validation.Validation{}
	_, err = valid.Valid(&req)
	if err != nil {
		logs.Error("参数验证错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}

	// 调用服务层更新分类
	err = c.categoryService.UpdateCategory(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("更新商家经营分类失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteCategory 删除商家经营分类
// @Title 删除商家经营分类
// @Description 删除商家经营分类
// @Param	id	path	int	true	"分类ID"
// @Success 200 {object} result.Response 删除成功
// @Failure 401 {object} result.Response 未认证
// @Failure 400 {object} result.Response 参数错误
// @Failure 404 {object} result.Response 分类不存在
// @Failure 500 {object} result.Response 服务器内部错误
// @router /business-categories/:id [delete]
func (c *MerchantBusinessCategoryController) DeleteCategory() {
	// 获取分类ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, "无效的分类ID"))
		return
	}

	// 调用服务层删除分类
	err = c.categoryService.DeleteCategory(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除商家经营分类失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
