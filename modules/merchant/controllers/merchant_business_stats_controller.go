/**
 * 商家营业统计控制器
 *
 * 该文件实现了商家营业统计的API接口控制器，处理商家营业时长、次数等统计数据查询。
 */

package controllers

import (
	//"encoding/json"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/dto"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// MerchantBusinessStatsController 商家营业统计控制器
type MerchantBusinessStatsController struct {
	web.Controller
	merchantLogService services.MerchantLogService
}

// Prepare 初始化控制器
func (c *MerchantBusinessStatsController) Prepare() {
	c.merchantLogService = services.NewMerchantLogService()
}

// ParseRequest 通用请求参数解析方法
func (c *MerchantBusinessStatsController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetBusinessStatsRequest 获取商家营业统计请求参数
type GetBusinessStatsRequest struct {
	Period string `json:"period"` // 统计周期，如7d表示7天
}

// GetBusinessStats 获取商家营业统计信息
// @Title 获取商家营业统计
// @Description 获取指定周期内的商家营业统计信息，包括总营业时长、日均营业时长、开店次数等
// @Param period query string true "统计周期，如7d表示7天"
// @Success 200 {object} dto.Response{data=merchantDto.BusinessStatsResponse} 成功返回营业统计信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /business-stats [get]
func (c *MerchantBusinessStatsController) GetBusinessStats() {
	// 解析请求参数
	req := &GetBusinessStatsRequest{
		Period: "7d", // 默认值
	}
	if err := c.ParseRequest(req); err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInvalidParams, "参数解析失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeUnauthorized, "未认证的请求")
		c.ServeJSON()
		return
	}

	// 调用服务获取统计信息
	stats, err := c.merchantLogService.GetBusinessStats(c.Ctx.Request.Context(), merchantID, req.Period)
	if err != nil {
		logs.Error("获取商家营业统计失败: %v", err)
		appErr := utils.ConvertToAppError(err)
		c.Data["json"] = dto.NewErrorResponseWithError(appErr)
		c.ServeJSON()
		return
	}

	// 返回成功响应
	response := &merchantDto.BusinessStatsResponse{
		TotalDuration:         stats.TotalDuration,
		AverageDuration:       stats.AverageDuration,
		TotalOpenTimes:        stats.TotalOpenTimes,
		CompleteOpenTimes:     stats.CompleteOpenTimes,
		IncompleteOpenTimes:   stats.IncompleteOpenTimes,
		AverageOpenTimePerDay: stats.AverageOpenTimePerDay,
		Period:                stats.Period,
		DaysCount:             stats.DaysCount,
		DailyStats:            stats.DailyStats, // 添加 DailyStats 字段
	}

	// 检查 DailyStats 是否为 nil
	if response.DailyStats == nil {
		logs.Warn("Warning: DailyStats is nil")
		// 初始化为空切片而不是 nil
		response.DailyStats = make([]*merchantDto.BusinessDailyStat, 0)
	}

	c.Data["json"] = dto.NewSuccessResponse(response)
	c.ServeJSON()
}
