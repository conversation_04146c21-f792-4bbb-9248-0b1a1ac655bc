/**
 * 商家分类公开API
 *
 * 本文件实现了面向前端的商家分类公开API，提供无需认证的商家分类查询接口。
 */

package controllers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils/common"
)

// MerchantCategoryAPIController 商家分类公开API控制器
type MerchantCategoryAPIController struct {
	web.Controller
}

// ParseRequest 通用请求参数解析方法
func (c *MerchantCategoryAPIController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetMerchantCategories 获取商家分类列表
// @Title 获取商家分类列表
// @Description 无需认证，获取商家分类列表，支持按名称和显示状态筛选
// @Tags 商家分类
// @Accept json
// @Produce json
// @Param name query string false "分类名称"
// @Param is_show query bool false "是否显示"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} dto.Response{data=merchantDto.MerchantCategoryListResponse} "成功"
// @Failure 400 {object} dto.Response "参数错误"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @router /merchant-categories [get]
func (c *MerchantCategoryAPIController) GetMerchantCategories() {
	// 解析查询参数
	var req merchantDto.MerchantCategoryListRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	
	// 如果未指定是否显示，默认只显示已启用的分类
	if req.IsShow == nil {
		isShow := true
		req.IsShow = &isShow
	}

	// 初始化商家分类服务
	categoryService := services.NewMerchantCategoryService()

	// 调用服务层获取分类列表
	resp, err := categoryService.ListCategories(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应，包含分页信息
	result.OKWithPagination(c.Ctx, resp.Items, resp.Total, req.Page, req.PageSize)
}
