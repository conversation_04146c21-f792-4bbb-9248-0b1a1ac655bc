/**
 * 商家分类控制器
 *
 * 本文件实现了商家对商品分类的管理功能，包括分类的查询、创建、更新和删除。
 * 控制器负责接收HTTP请求，处理参数验证，调用相应的服务层方法，并返回处理结果。
 */

package controllers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	productRepositories "o_mall_backend/modules/product/repositories"
	productServices "o_mall_backend/modules/product/services"
	"o_mall_backend/utils/common"
)

// MerchantCategoryController 商家分类控制器
type MerchantCategoryController struct {
	web.Controller
	categoryService productServices.CategoryService
}

// ParseRequest 通用请求参数解析方法
func (c *MerchantCategoryController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *MerchantCategoryController) Prepare() {
	categoryRepo := productRepositories.NewCategoryRepository()
	c.categoryService = productServices.NewCategoryService(categoryRepo)
}

// GetCategories 获取商品分类列表
// @Title 获取商品分类列表
// @Description 获取商品分类列表或树形结构
// @Param	tree	query	bool	false	"是否返回树形结构，默认false"
// @Success 200 {object} result.Response 成功返回分类列表或树形结构
// @Failure 401 {object} result.Response 未认证
// @Failure 500 {object} result.Response 服务器内部错误
// @router /categories [get]
func (c *MerchantCategoryController) GetCategories() {
	// 从上下文获取当前商家ID
	_, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求参数
	isTree, _ := c.GetBool("tree", false)

	var resp interface{}
	var err error

	// 根据参数决定返回列表还是树形结构
	if isTree {
		// 获取分类树
		resp, err = c.categoryService.GetCategoryTree(c.Ctx.Request.Context())
	} else {
		// 获取所有分类列表
		resp, err = c.categoryService.GetAllCategories(c.Ctx.Request.Context())
	}

	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// GetCategoryByID 获取商品分类详情
// @Title 获取商品分类详情
// @Description 获取指定ID的商品分类信息
// @Param	id	path	int	true	"分类ID"
// @Success 200 {object} result.Response 成功返回分类详情
// @Failure 401 {object} result.Response 未认证
// @Failure 404 {object} result.Response 分类不存在
// @Failure 500 {object} result.Response 服务器内部错误
// @router /categories/:id [get]
func (c *MerchantCategoryController) GetCategoryByID() {
	// 从上下文获取当前商家ID
	_, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取分类ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的分类ID")
		return
	}

	// 调用服务层获取分类详情
	category, err := c.categoryService.GetCategory(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, category)
}

// GetChildCategories 获取子分类列表
// @Title 获取子分类列表
// @Description 获取指定父分类ID下的子分类列表
// @Param	parent_id	path	int	true	"父分类ID"
// @Success 200 {object} result.Response 成功返回子分类列表
// @Failure 401 {object} result.Response 未认证
// @Failure 500 {object} result.Response 服务器内部错误
// @router /categories/children/:parent_id [get]
func (c *MerchantCategoryController) GetChildCategories() {
	// 从上下文获取当前商家ID
	_, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取父分类ID
	parentID, err := c.GetInt64(":parent_id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的父分类ID")
		return
	}

	// 调用服务层获取子分类
	categories, err := c.categoryService.GetChildCategories(c.Ctx.Request.Context(), parentID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, categories)
}
