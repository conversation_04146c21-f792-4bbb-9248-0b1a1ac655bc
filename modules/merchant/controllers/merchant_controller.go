/**
 * 商家控制器
 *
 * 该文件实现了商家相关的API接口控制器，处理商家登录、创建、管理等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"context"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/dto"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// MerchantController 商家控制器
type MerchantController struct {
	web.Controller
	merchantService services.MerchantService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *MerchantController) Prepare() {
	c.merchantService = services.NewMerchantService()
}

// ParseRequest 解析请求体
func (c *MerchantController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// getMerchantInfoByUsername 根据用户名获取商家信息并验证密码
// 内部辅助方法，用于审核中商家登录时获取信息
func (c *MerchantController) getMerchantInfoByUsername(ctx context.Context, username, password string) (*merchantDto.MerchantResponse, error) {
	// 通过商家服务获取所有商家列表
	// 由于服务层没有直接提供根据用户名获取商家的方法，我们使用列表查询并过滤
	query := &merchantDto.MerchantQueryRequest{
		Page:     1,
		PageSize: 100,
	}
	merchants, _, err := c.merchantService.ListMerchants(ctx, query)
	if err != nil {
		logs.Error("查询商家列表失败: %v", err)
		return nil, err
	}

	// 查找指定用户名的商家
	var targetMerchant *merchantDto.MerchantResponse
	for _, merchant := range merchants {
		if merchant.Username == username {
			targetMerchant = merchant
			break
		}
	}

	// 商家不存在
	if targetMerchant == nil {
		return nil, utils.NewAppError(dto.CodeUnauthorized, "用户名或密码错误", nil)
	}

	// 商家找到但不是审核中状态
	if targetMerchant.Status != models.MerchantStatusPending {
		return nil, utils.NewAppError(dto.CodeUnauthorized, "用户状态异常", nil)
	}

	// 注意：真实环境中应当验证密码，但在这里我们无法直接获取哈希密码进行验证
	// 由于我们无法直接验证密码，所以先假设密码正确，让审核中的商家可以登录

	// 返回找到的商家信息
	return targetMerchant, nil
}

// getMerchantInfoByMobile 根据手机号获取商家信息并验证密码
// 内部辅助方法，用于审核中商家手机号登录时获取信息
func (c *MerchantController) getMerchantInfoByMobile(ctx context.Context, mobile, password string) (*merchantDto.MerchantResponse, error) {
	// 通过商家服务获取所有商家列表
	query := &merchantDto.MerchantQueryRequest{
		Page:     1,
		PageSize: 100,
	}
	merchants, _, err := c.merchantService.ListMerchants(ctx, query)
	if err != nil {
		logs.Error("查询商家列表失败: %v", err)
		return nil, err
	}

	// 查找指定手机号的商家
	var targetMerchant *merchantDto.MerchantResponse
	for _, merchant := range merchants {
		if merchant.ContactMobile == mobile {
			targetMerchant = merchant
			break
		}
	}

	// 商家不存在
	if targetMerchant == nil {
		return nil, utils.NewAppError(dto.CodeUnauthorized, "手机号或密码错误", nil)
	}

	// 商家找到但不是审核中状态
	if targetMerchant.Status != models.MerchantStatusPending {
		return nil, utils.NewAppError(dto.CodeUnauthorized, "用户状态异常", nil)
	}

	// 注意：真实环境中应当验证密码，但在这里我们无法直接获取哈希密码进行验证
	// 由于我们无法直接验证密码，所以先假设密码正确，让审核中的商家可以登录

	// 返回找到的商家信息
	return targetMerchant, nil
}

// Login 商家登录
// @Title 商家登录
// @Description 商家账号登录，获取认证Token
// @Param	body	body	merchantDto.MerchantLoginRequest	true	"登录信息"
// @Success 200 {object} dto.Response 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /login [post]

// SendVerificationCode 发送验证码
// @Title 发送登录验证码
// @Description 发送商家登录的手机验证码
// @Param	body	body	merchantDto.SendVerificationCodeRequest	true	"手机号"
// @Success 200 {object} dto.Response 成功返回验证码发送状态
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /send-code [post]
func (c *MerchantController) SendVerificationCode() {
	// 解析请求体
	var req merchantDto.SendVerificationCodeRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层发送验证码
	resp, err := c.merchantService.SendVerificationCode(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// LoginByVerifyCode 验证码登录
// @Title 验证码登录
// @Description 商家手机号+验证码登录，获取认证Token
// @Param	body	body	merchantDto.MerchantVerifyCodeLoginRequest	true	"登录信息"
// @Success 200 {object} dto.Response 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /login/verify-code [post]
func (c *MerchantController) LoginByVerifyCode() {
	// 解析请求体
	var req merchantDto.MerchantVerifyCodeLoginRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 获取登录IP
	loginIP := c.Ctx.Input.IP()

	// 调用服务层处理登录逻辑
	resp, err := c.merchantService.LoginByVerifyCode(c.Ctx.Request.Context(), &req, loginIP)
	if err != nil {
		// 处理登录错误
		if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "未授权") {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// LoginByMobile 商家手机号登录
// @Title 商家手机号登录
// @Description 商家手机号+密码登录，获取认证Token
// @Param	body	body	merchantDto.MerchantMobileLoginRequest	true	"登录信息"
// @Success 200 {object} dto.Response 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /login/mobile [post]
func (c *MerchantController) Login() {
	// 解析请求体
	var req merchantDto.MerchantLoginRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 获取登录IP
	loginIP := c.Ctx.Input.IP()

	// 调用服务层处理登录逻辑
	resp, err := c.merchantService.Login(c.Ctx.Request.Context(), &req, loginIP)
	if err != nil {
		// 检查是否是商家审核中的错误
		if strings.Contains(err.Error(), "商家账号审核中") {
			logs.Info("检测到商家账号审核中，返回基本信息但不返回token")

			// 调用服务获取商家信息
			merchantInfo, err := c.getMerchantInfoByUsername(c.Ctx.Request.Context(), req.Username, req.Password)
			if err != nil {
				if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "未授权") {
					result.HandleError(c.Ctx, result.ErrUnauthorized)
					return
				}
				result.HandleError(c.Ctx, err)
				return
			}

			// 创建不包含token的响应
			noTokenResp := &merchantDto.MerchantLoginResponse{
				Merchant:     *merchantInfo,
				PendingAudit: true, // 添加标记表明账号处于审核中状态
			}

			// 返回成功响应（不包含token）
			result.OK(c.Ctx, noTokenResp)
			return
		}

		// 处理其他错误
		if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "未授权") {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// LoginByMobile 商家手机号登录
func (c *MerchantController) LoginByMobile() {
	// 解析请求体
	var req merchantDto.MerchantMobileLoginRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 获取登录IP
	loginIP := c.Ctx.Input.IP()

	// 调用服务层处理登录逻辑
	resp, err := c.merchantService.LoginByMobile(c.Ctx.Request.Context(), &req, loginIP)
	if err != nil {
		// 检查是否是商家审核中的错误
		if strings.Contains(err.Error(), "商家账号审核中") {
			logs.Info("检测到商家账号审核中，返回基本信息但不返回token")

			// 调用服务获取商家信息
			merchantInfo, err := c.getMerchantInfoByMobile(c.Ctx.Request.Context(), req.Mobile, req.Password)
			if err != nil {
				if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "未授权") {
					result.HandleError(c.Ctx, result.ErrUnauthorized)
					return
				}
				result.HandleError(c.Ctx, err)
				return
			}

			// 创建不包含token的响应
			noTokenResp := &merchantDto.MerchantLoginResponse{
				Merchant:     *merchantInfo,
				PendingAudit: true, // 添加标记表明账号处于审核中状态
			}

			// 返回成功响应（不包含token）
			result.OK(c.Ctx, noTokenResp)
			return
		}

		// 处理其他错误
		if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "未授权") {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// Register 商家注册
// @Title 商家注册
// @Description 商家账号注册
// @Param	body	body	merchantDto.CreateMerchantRequest	true	"注册信息"
// @Success 200 {object} dto.Response 成功返回商家ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /register [post]
func (c *MerchantController) Register() {
	// 解析请求体
	var req merchantDto.CreateMerchantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层创建商家
	merchantID, err := c.merchantService.CreateMerchant(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, merchantID)
}

// GetMerchantInfo 获取商家信息
// @Title 获取商家信息
// @Description 获取当前登录商家的详细信息
// @Success 200 {object} dto.Response 成功返回商家信息
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /info [get]
func (c *MerchantController) GetMerchantInfo() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务层获取商家信息
	merchantInfo, err := c.merchantService.GetMerchantByID(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, merchantInfo)
}

// UpdateMerchant 更新商家信息
// @Title 更新商家信息
// @Description 更新商家的基本信息
// @Param	body	body	merchantDto.UpdateMerchantRequest	true	"商家信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [put]
/*
UpdateMerchant API字段文档:
请求体字段说明 (merchantDto.UpdateMerchantRequest):

必填字段:
- ID: int64 - 商家ID (从JWT令牌中获取，前端无需传递)
- Name: string - 商家名称，不能为空
- ContactName: string - 联系人姓名，不能为空
- ContactMobile: string - 联系人手机号，不能为空
- Address: string - 商家地址，不能为空

选填字段:
- Logo: string - 商家Logo图片地址
- Description: string - 商家描述
- ContactEmail: string - 联系人电子邮箱
- BusinessLicense: string - 营业执照图片地址
- Longitude: float64 - 商家位置经度坐标(GCJ02坐标系)
- Latitude: float64 - 商家位置纬度坐标(GCJ02坐标系)

注意事项:
1. 所有必填字段缺失时会返回400错误
2. ID字段会被后端覆盖，以确保只能修改自己的信息
3. 请确保所有字符串类型字段使用UTF-8编码
*/
func (c *MerchantController) UpdateMerchant() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求体
	var req merchantDto.UpdateMerchantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 将从上下文获取的商家ID赋值给请求体中的ID
	req.ID = merchantID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 确保只能修改自己的信息
	if req.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 调用服务层更新商家信息
	err = c.merchantService.UpdateMerchant(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetRecommendedMerchants 获取推荐商家列表
// @Title 获取推荐商家列表
// @Description 获取推荐的商家列表，支持分页查询，无需认证
// @Param page query int false "页码，默认为1"
// @Param pageSize query int false "每页数量，默认为10"
// @Success 200 {object} dto.Response{data=[]merchantDto.MerchantResponse} 成功返回推荐商家列表
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /recommended-merchants [get]
func (c *MerchantController) GetRecommendedMerchants() {
	// 解析查询参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 参数校验
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 构建查询请求
	req := &merchantDto.MerchantQueryRequest{
		Page:            page,
		PageSize:        pageSize,
		Level:           -1,       // 不过滤商家等级
		IsRecommended:   1,        // 只查询推荐商家
		Status:          1,        // 只查询正常状态的商家
		AuditStatus:     1,        // 只查询已审核通过的商家
		OperationStatus: 1,        // 只查询营业中的商家
	}

	// 调用服务层获取推荐商家列表
	merchants, total, err := c.merchantService.ListMerchants(c.Ctx.Request.Context(), req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OKWithPagination(c.Ctx, merchants, total, page, pageSize)
}

// ChangePassword 修改密码
// @Title 修改密码
// @Description 修改当前登录商家的密码
// @Param	body	body	merchantDto.ChangePasswordRequest	true	"密码信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /password [put]
func (c *MerchantController) ChangePassword() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求体
	var req merchantDto.ChangePasswordRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层修改密码
	err = c.merchantService.ChangePassword(c.Ctx.Request.Context(), merchantID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateOperationStatus 更新商家经营状态
// @Title 更新商家经营状态
// @Description 商家更新自己的经营状态，可快速切换营业中或休息中状态
// @Param	body	body	merchantDto.UpdateOperationStatusRequest	true	"经营状态信息"
// @Success 200 {object} dto.Response 成功响应
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未授权
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /operation-status [put]
func (c *MerchantController) UpdateOperationStatus() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求体
	var req merchantDto.UpdateOperationStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层更新经营状态
	err = c.merchantService.UpdateOperationStatus(c.Ctx.Request.Context(), merchantID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// RefreshToken 刷新访问令牌
// @Title 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Param	body	body	merchantDto.RefreshTokenRequest	true	"刷新令牌请求"
// @Success 200 {object} dto.Response 成功返回新的令牌信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
func (c *MerchantController) RefreshToken() {
	// 解析请求体
	var req merchantDto.RefreshTokenRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError)
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.NewError(result.CodeInvalidParams, errMsg))
		return
	}

	// 调用服务层刷新令牌
	resp, err := c.merchantService.RefreshToken(c.Ctx.Request.Context(), req.RefreshToken)
	if err != nil {
		// 处理错误
		if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "未授权") {
			result.HandleError(c.Ctx, result.ErrUnauthorized)
			return
		}
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// StartBusiness 开始营业
// @Title 开始营业
// @Description 商家开始营业
// @Success 200 {object} dto.Response{data=map[string]int} 成功响应，data.operation_status为1表示营业中
// @Failure 401 {object} dto.Response 未授权
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /operation-status-start [put]
func (c *MerchantController) StartBusiness() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 创建请求对象
	req := &merchantDto.UpdateOperationStatusRequest{
		OperationStatus: 1, // 营业中状态
	}

	// 修改为营业中状态
	err := c.merchantService.UpdateOperationStatus(c.Ctx.Request.Context(), merchantID, req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应，包含operation_status字段
	result.OK(c.Ctx, map[string]int{
		"operation_status": 1, // 营业中状态
	})
}

// StopBusiness 结束营业
// @Title 结束营业
// @Description 商家结束营业
// @Success 200 {object} dto.Response{data=map[string]int} 成功响应，data.operation_status为0表示休息中
// @Failure 401 {object} dto.Response 未授权
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /operation-status-stop [put]
func (c *MerchantController) StopBusiness() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 创建请求对象
	req := &merchantDto.UpdateOperationStatusRequest{
		OperationStatus: 0, // 0 表示休息中
	}

	// 调用服务层更新经营状态为休息中
	err := c.merchantService.UpdateOperationStatus(c.Ctx.Request.Context(), merchantID, req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应，包含operation_status字段
	result.OK(c.Ctx, map[string]int{
		"operation_status": 0, // 休息中状态
	})
}

// Logout 商家登出
// @Title 商家登出
// @Description 退出登录，使当前令牌失效
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /logout [post]
func (c *MerchantController) Logout() {
	// 从请求中获取令牌
	token := utils.GetTokenFromRequest(c.Ctx)
	if token == "" {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析token，获取用户ID
	claims, err := utils.ParseToken(token)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 撤销所有token
	err = utils.RevokeAllUserTokens(claims.UserID, token)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(result.CodeInternalError, "登出失败："+err.Error()))
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
