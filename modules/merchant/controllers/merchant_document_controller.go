/**
 * 商家证件和协议控制器
 *
 * 该文件实现了商家证件和协议相关的API接口控制器，处理证件的上传、查询、审核等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 * 使用统一的result包处理API响应，提供一致的接口格式。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/merchant/core"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils/common"
)

// MerchantDocumentController 商家证件和协议控制器
type MerchantDocumentController struct {
	web.Controller
	documentService services.MerchantDocumentService
}

// Prepare 初始化控制器
func (c *MerchantDocumentController) Prepare() {
	c.documentService = services.NewMerchantDocumentService()
}

// ParseRequest 解析请求体
func (c *MerchantDocumentController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// UploadDocument 上传证件
// @Title 上传证件
// @Description 上传商家证件
// @Param	body	body	merchantDto.UploadDocumentRequest	true	"证件信息"
// @Success 200 {object} dto.Response 成功返回证件ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [post]
func (c *MerchantDocumentController) UploadDocument() {
	// 解析请求体
	var req merchantDto.UploadDocumentRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 获取当前登录商家ID
	merchantID, err := core.GetCurrentUserID(c.Ctx.Request)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "获取商家信息失败")
		return
	}
	req.MerchantID = merchantID

	// 调用服务上传证件
	id, err := c.documentService.UploadDocument(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("上传证件失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, map[string]interface{}{
		"id": id,
	})
}

// GetDocumentList 获取证件列表
// @Title 获取证件列表
// @Description 获取当前商家的证件列表
// @Param	type	query	int	false	"证件类型，0表示查询所有类型"
// @Success 200 {object} dto.Response 成功返回证件列表
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [get]
func (c *MerchantDocumentController) GetDocumentList() {
	// 获取当前登录商家ID
	merchantID, err := core.GetCurrentUserID(c.Ctx.Request)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "获取商家信息失败")
		return
	}

	// 获取查询参数
	typeStr := c.GetString("type", "0")
	docType, err := core.ParseInt(typeStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的证件类型参数")
		return
	}

	// 调用服务获取证件列表
	var documents []*merchantDto.DocumentResponse
	if docType == 0 {
		// 获取所有类型的证件
		documents, err = c.documentService.GetDocumentsByMerchantID(c.Ctx.Request.Context(), merchantID)
	} else {
		// 获取指定类型的证件
		documents, err = c.documentService.GetDocumentsByType(c.Ctx.Request.Context(), merchantID, docType)
	}

	if err != nil {
		logs.Error("获取证件列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, documents)
}

// GetDocument 获取证件详情
// @Title 获取证件详情
// @Description 获取指定ID的证件详情
// @Param	id	path	int	true	"证件ID"
// @Success 200 {object} dto.Response 成功返回证件详情
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 404 {object} dto.Response 证件不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [get]
func (c *MerchantDocumentController) GetDocument() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的ID参数")
		return
	}

	// 调用服务获取证件详情
	document, err := c.documentService.GetDocumentByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取证件详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if document == nil {
		result.HandleError(c.Ctx, result.ErrNotFound, "证件不存在")
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, document)
}

// UpdateDocument 更新证件
// @Title 更新证件
// @Description 更新指定ID的证件
// @Param	id	path	int	true	"证件ID"
// @Param	body	body	merchantDto.UpdateDocumentRequest	true	"证件信息"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 404 {object} dto.Response 证件不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [put]
func (c *MerchantDocumentController) UpdateDocument() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的ID参数")
		return
	}

	// 解析请求体
	var req merchantDto.UpdateDocumentRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 设置ID
	req.ID = id

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 调用服务更新证件
	err = c.documentService.UpdateDocument(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("更新证件失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteDocument 删除证件
// @Title 删除证件
// @Description 删除指定ID的证件
// @Param	id	path	int	true	"证件ID"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [delete]
func (c *MerchantDocumentController) DeleteDocument() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的ID参数")
		return
	}

	// 调用服务删除证件
	err = c.documentService.DeleteDocument(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除证件失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetExpiringDocuments 获取即将过期的证件
// @Title 获取即将过期的证件
// @Description 获取当前商家即将过期的证件
// @Success 200 {object} dto.Response 成功返回证件列表
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /expiring [get]
func (c *MerchantDocumentController) GetExpiringDocuments() {
	// 获取当前登录商家ID
	merchantID, err := core.GetCurrentUserID(c.Ctx.Request)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "获取商家信息失败")
		return
	}

	// 调用服务获取即将过期的证件
	documents, err := c.documentService.GetExpiringDocuments(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		logs.Error("获取即将过期的证件失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, documents)
}
