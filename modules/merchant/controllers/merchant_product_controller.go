/**
 * 商家商品控制器
 *
 * 本文件实现了商家对商品的管理功能，包括商品的增删改查、分类管理、规格管理和库存管理。
 * 控制器负责接收HTTP请求，处理参数验证，调用相应的服务层方法，并返回处理结果。
 * 使用统一的result包处理API响应，提供一致的接口格式。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	productDto "o_mall_backend/modules/product/dto"
	productModels "o_mall_backend/modules/product/models"
	productRepositories "o_mall_backend/modules/product/repositories"
	productServices "o_mall_backend/modules/product/services"
	"o_mall_backend/utils/common"
)

// MerchantProductController 商家商品控制器
type MerchantProductController struct {
	web.Controller
	productService       productServices.ProductService
	categoryService      productServices.CategoryService
	specificationService productServices.SpecificationService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *MerchantProductController) Prepare() {
	// 创建仓储层
	categoryRepo := productRepositories.NewCategoryRepository()
	specRepo := productRepositories.NewSpecificationRepository()

	// 初始化服务
	c.productService = productServices.NewProductService()
	c.categoryService = productServices.NewCategoryService(categoryRepo)
	c.specificationService = productServices.NewSpecificationService(specRepo)
}

// ParseRequest 解析请求体
func (c *MerchantProductController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetProducts 获取商家的商品列表
// @Title 获取商家商品列表
// @Description 根据查询条件获取当前登录商家的商品列表
// @Param	page		query	int		false	"页码，默认1"
// @Param	page_size	query	int		false	"每页数量，默认20"
// @Param	keyword		query	string	false	"关键词搜索"
// @Param	category_id	query	int		false	"分类ID"
// @Param	status		query	int		false	"商品状态"
// @Success 200 {object} dto.Response 成功返回商品列表
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /products [get]
func (c *MerchantProductController) GetProducts() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析查询参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)
	keyword := c.GetString("keyword")
	categoryID, _ := c.GetInt64("category_id", 0)
	status, _ := c.GetInt("status", -1)

	// 构造查询请求
	req := &productDto.ProductQueryRequest{
		Page:       page,
		PageSize:   pageSize,
		Keyword:    keyword,
		CategoryID: categoryID,
		Status:     status,
		MerchantID: merchantID, // 只查询当前商家的商品
	}

	// 调用服务层获取商品列表
	resp, err := c.productService.ListProducts(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("获取商品列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// GetProduct 获取商品详情
// @Title 获取商品详情
// @Description 获取指定ID的商品详细信息
// @Param	id	path	int	true	"商品ID"
// @Success 200 {object} dto.Response 成功返回商品详情
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 商品不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /products/:id [get]
func (c *MerchantProductController) GetProduct() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取商品ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的商品ID")
		return
	}

	// 调用服务层获取商品详情
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取商品详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商品是否属于当前商家
	if product.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此商品")
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, product)
}

// CreateProduct 创建商品
// @Title 创建商品
// @Description 创建新的商品
// @Param	body	body	productDto.CreateProductRequest	true	"商品信息"
// @Success 200 {object} dto.Response 成功返回创建的商品详情
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /products [post]
func (c *MerchantProductController) CreateProduct() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体
	var req productDto.CreateProductRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 直接创建一个带有商家ID的商品模型
	product := &productModels.Product{
		MerchantID: merchantID,
	}
	
	// 调用服务层创建商品
	product.Name = req.Name
	product.Brief = req.Brief
	product.Description = req.Description
	product.CategoryID = req.CategoryID
	product.MainImage = req.MainImage
	product.Price = req.Price
	product.OriginalPrice = req.OriginalPrice
	product.CostPrice = req.CostPrice
	product.Stock = req.Stock
	product.Unit = req.Unit
	product.Weight = req.Weight
	product.Keywords = req.Keywords
	product.Tags = req.Tags
	product.Code = req.Code
	product.Barcode = req.Barcode
	product.IsRecommend = req.IsRecommend
	product.IsHot = req.IsHot
	product.IsNew = req.IsNew
	product.Status = req.Status
	product.SaleStartTime = req.SaleStartTime
	product.SaleEndTime = req.SaleEndTime
	product.FreightTemplate = req.FreightTemplate
	product.HasSKU = req.HasSKU
	product.DeliveryType = req.DeliveryType

	// 将商品DTO和模型合并后创建
	productDetail, err := c.productService.CreateProduct(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建商品失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, productDetail)
}

// UpdateProduct 更新商品
// @Title 更新商品
// @Description 更新指定ID的商品信息
// @Param	id		path	int								true	"商品ID"
// @Param	body	body	productDto.UpdateProductRequest	true	"商品信息"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 商品不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /products/:id [put]
func (c *MerchantProductController) UpdateProduct() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取商品ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的商品ID")
		return
	}

	// 首先检查商品是否存在且属于当前商家
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取商品失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商品是否属于当前商家
	if product.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此商品")
		return
	}

	// 解析请求体
	var req productDto.UpdateProductRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 确保更新的商品ID正确
	req.ID = id

	// 调用服务层更新商品
	err = c.productService.UpdateProduct(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("更新商品失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteProduct 删除商品
// @Title 删除商品
// @Description 删除指定ID的商品
// @Param	id	path	int	true	"商品ID"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 商品不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /products/:id [delete]
func (c *MerchantProductController) DeleteProduct() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取商品ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的商品ID")
		return
	}

	// 首先检查商品是否存在且属于当前商家
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取商品失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商品是否属于当前商家
	if product.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此商品")
		return
	}

	// 调用服务层删除商品
	err = c.productService.DeleteProduct(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除商品失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
