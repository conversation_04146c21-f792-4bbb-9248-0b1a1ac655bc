/**
 * 商家结算方式控制器
 *
 * 该文件实现了商家结算方式相关的API接口控制器，处理结算方式的添加、查询、修改等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 * 使用统一的result包处理API响应，提供一致的接口格式。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/merchant/core"
	merchantDto "o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/services"
	"o_mall_backend/utils/common"
)

// MerchantSettlementController 商家结算方式控制器
type MerchantSettlementController struct {
	web.Controller
	settlementService services.MerchantSettlementService
}

// Prepare 初始化控制器
func (c *MerchantSettlementController) Prepare() {
	c.settlementService = services.NewMerchantSettlementService()
}

// ParseRequest 解析请求体
func (c *MerchantSettlementController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// CreateSettlement 创建结算方式
// @Title 创建结算方式
// @Description 创建商家结算方式
// @Param	body	body	merchantDto.CreateSettlementRequest	true	"结算方式信息"
// @Success 200 {object} dto.Response 成功返回结算方式ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [post]
func (c *MerchantSettlementController) CreateSettlement() {
	// 解析请求体
	var req merchantDto.CreateSettlementRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 获取当前登录商家ID
	merchantID, err := core.GetCurrentUserID(c.Ctx.Request)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "获取商家信息失败")
		return
	}
	req.MerchantID = merchantID

	// 调用服务创建结算方式
	id, err := c.settlementService.CreateSettlement(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建结算方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, map[string]interface{}{
		"id": id,
	})
}

// GetSettlementList 获取结算方式列表
// @Title 获取结算方式列表
// @Description 获取当前商家的结算方式列表
// @Success 200 {object} dto.Response 成功返回结算方式列表
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [get]
func (c *MerchantSettlementController) GetSettlementList() {
	// 获取当前登录商家ID
	merchantID, err := core.GetCurrentUserID(c.Ctx.Request)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "获取商家信息失败")
		return
	}

	// 调用服务获取结算方式列表
	settlements, err := c.settlementService.GetSettlementsByMerchantID(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		logs.Error("获取结算方式列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, settlements)
}

// GetSettlement 获取结算方式详情
// @Title 获取结算方式详情
// @Description 获取指定ID的结算方式详情
// @Param	id	path	int	true	"结算方式ID"
// @Success 200 {object} dto.Response 成功返回结算方式详情
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 404 {object} dto.Response 结算方式不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [get]
func (c *MerchantSettlementController) GetSettlement() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的ID参数")
		return
	}

	// 调用服务获取结算方式详情
	settlement, err := c.settlementService.GetSettlementByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取结算方式详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	if settlement == nil {
		result.HandleError(c.Ctx, result.ErrNotFound, "结算方式不存在")
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, settlement)
}

// UpdateSettlement 更新结算方式
// @Title 更新结算方式
// @Description 更新指定ID的结算方式
// @Param	id	path	int	true	"结算方式ID"
// @Param	body	body	merchantDto.UpdateSettlementRequest	true	"结算方式信息"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 404 {object} dto.Response 结算方式不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [put]
func (c *MerchantSettlementController) UpdateSettlement() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的ID参数")
		return
	}

	// 解析请求体
	var req merchantDto.UpdateSettlementRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 设置ID
	req.ID = id

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 调用服务更新结算方式
	err = c.settlementService.UpdateSettlement(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("更新结算方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteSettlement 删除结算方式
// @Title 删除结算方式
// @Description 删除指定ID的结算方式
// @Param	id	path	int	true	"结算方式ID"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [delete]
func (c *MerchantSettlementController) DeleteSettlement() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的ID参数")
		return
	}

	// 调用服务删除结算方式
	err = c.settlementService.DeleteSettlement(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除结算方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// SetDefaultSettlement 设置默认结算方式
// @Title 设置默认结算方式
// @Description 设置指定ID的结算方式为默认
// @Param	id	path	int	true	"结算方式ID"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id/default [put]
func (c *MerchantSettlementController) SetDefaultSettlement() {
	// 获取参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := core.ParseInt64(idStr)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的ID参数")
		return
	}

	// 获取当前登录商家ID
	merchantID, err := core.GetCurrentUserID(c.Ctx.Request)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "获取商家信息失败")
		return
	}

	// 创建请求对象
	req := &merchantDto.SetDefaultSettlementRequest{
		MerchantID:   merchantID,
		SettlementID: id,
	}

	// 调用服务设置默认结算方式
	err = c.settlementService.SetDefaultSettlement(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("设置默认结算方式失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
