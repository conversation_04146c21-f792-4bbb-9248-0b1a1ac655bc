/**
 * 商家商品SKU控制器
 *
 * 本文件实现了商家对商品SKU和库存的管理功能，包括SKU的创建、更新、删除和库存管理。
 * 控制器负责接收HTTP请求，处理参数验证，调用相应的服务层方法，并返回处理结果。
 * 使用统一的result包处理API响应，提供一致的接口格式。
 */

package controllers

import (
	"strconv"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	productDto "o_mall_backend/modules/product/dto"
	productRepositories "o_mall_backend/modules/product/repositories"
	productServices "o_mall_backend/modules/product/services"
	"o_mall_backend/utils/common"
)

// MerchantSkuController 商家SKU控制器
type MerchantSkuController struct {
	web.Controller
	productService  productServices.ProductService
	productSkuRepo  productRepositories.ProductSkuRepository
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *MerchantSkuController) Prepare() {
	c.productService = productServices.NewProductService()
	c.productSkuRepo = productRepositories.NewProductSkuRepository()
}

// ParseRequest 通用请求参数解析方法
func (c *MerchantSkuController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetProductSkus 获取商品的SKU列表
// @Title 获取商品SKU列表
// @Description 获取指定商品ID的SKU列表
// @Param	product_id	path	int	true	"商品ID"
// @Success 200 {object} dto.Response 成功返回SKU列表
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 商品不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /products/:product_id/skus [get]
func (c *MerchantSkuController) GetProductSkus() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取商品ID
	productID, err := c.GetInt64(":product_id")
	if err != nil || productID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的商品ID")
		return
	}

	// 首先检查商品是否存在且属于当前商家
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), productID)
	if err != nil {
		logs.Error("获取商品信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商品是否属于当前商家
	if product.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此商品")
		return
	}

	// 商品详情中已包含SKU信息，直接返回
	skus := product.Skus

	// 返回成功响应
	result.OK(c.Ctx, skus)
}

// UpdateSku 更新SKU信息
// @Title 更新SKU信息
// @Description 更新指定ID的SKU信息
// @Param	sku_id	path	int							true	"SKU ID"
// @Param	body	body	productDto.ProductSkuRequest	true	"SKU信息"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response SKU不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /skus/:sku_id [put]
func (c *MerchantSkuController) UpdateSku() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取SKU ID
	skuID, err := c.GetInt64(":sku_id")
	if err != nil || skuID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的SKU ID")
		return
	}

	// 获取SKU信息
	sku, err := c.productSkuRepo.GetByID(c.Ctx.Request.Context(), skuID)
	if err != nil {
		logs.Error("获取SKU信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取商品信息以验证权限
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), sku.ProductID)
	if err != nil {
		logs.Error("获取商品信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商品是否属于当前商家
	if product.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此SKU")
		return
	}

	// 解析请求体
	var req productDto.ProductSkuRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 更新SKU信息
	sku.Name = req.Name
	sku.Code = req.Code
	sku.BarCode = req.BarCode
	sku.Image = req.Image
	sku.Price = req.Price
	sku.OriginalPrice = req.OriginalPrice
	sku.CostPrice = req.CostPrice
	sku.Stock = req.Stock
	sku.Weight = req.Weight
	sku.Volume = req.Volume
	sku.SpecData = req.SpecData

	// 保存SKU
	err = c.productSkuRepo.Update(c.Ctx.Request.Context(), sku)
	if err != nil {
		logs.Error("更新SKU失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateSkuStock 更新SKU库存
// @Title 更新SKU库存
// @Description 更新指定ID的SKU库存数量
// @Param	sku_id	path	int	true	"SKU ID"
// @Param	stock	body	int	true	"新的库存数量"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response SKU不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /skus/:sku_id/stock [put]
func (c *MerchantSkuController) UpdateSkuStock() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取SKU ID
	skuID, err := c.GetInt64(":sku_id")
	if err != nil || skuID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的SKU ID")
		return
	}

	// 解析请求体中的库存数量
	var stockData struct {
		Stock int `json:"stock" form:"stock" valid:"Required"`
	}
	if err := c.ParseRequest(&stockData); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&stockData)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 库存不能为负数
	if stockData.Stock < 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "库存数量不能为负数")
		return
	}

	// 获取SKU信息
	sku, err := c.productSkuRepo.GetByID(c.Ctx.Request.Context(), skuID)
	if err != nil {
		logs.Error("获取SKU信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取商品信息以验证权限
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), sku.ProductID)
	if err != nil {
		logs.Error("获取商品信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商品是否属于当前商家
	if product.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此SKU")
		return
	}

	// 更新SKU库存
	sku.Stock = stockData.Stock

	// 保存SKU
	err = c.productSkuRepo.Update(c.Ctx.Request.Context(), sku)
	if err != nil {
		logs.Error("更新SKU库存失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// BatchUpdateSkuStock 批量更新SKU库存
// @Title 批量更新SKU库存
// @Description 批量更新多个SKU的库存数量
// @Param	body	body	[]struct{SkuID int64, Stock int}	true	"SKU库存信息列表"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /skus/batch-stock [put]
func (c *MerchantSkuController) BatchUpdateSkuStock() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体中的库存信息列表
	var stockDataList []struct {
		SkuID int64 `json:"sku_id" form:"sku_id" valid:"Required"`
		Stock int   `json:"stock" form:"stock" valid:"Required"`
	}
	if err := c.ParseRequest(&stockDataList); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	if len(stockDataList) == 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "库存信息列表不能为空")
		return
	}

	// 逐个更新SKU库存
	for _, stockData := range stockDataList {
		// 库存不能为负数
		if stockData.Stock < 0 {
			result.HandleError(c.Ctx, result.ErrInvalidParams, "库存数量不能为负数")
			return
		}

		// 获取SKU信息
		sku, err := c.productSkuRepo.GetByID(c.Ctx.Request.Context(), stockData.SkuID)
		if err != nil {
			logs.Error("获取SKU信息失败: %v", err)
			result.HandleError(c.Ctx, err)
			return
		}

		// 获取商品信息以验证权限
		product, err := c.productService.GetProduct(c.Ctx.Request.Context(), sku.ProductID)
		if err != nil {
			logs.Error("获取商品信息失败: %v", err)
			result.HandleError(c.Ctx, err)
			return
		}

		// 验证商品是否属于当前商家
		if product.MerchantID != merchantID {
			result.HandleError(c.Ctx, result.ErrForbidden, "无权操作SKU ID: " + strconv.FormatInt(stockData.SkuID, 10))
			return
		}

		// 更新SKU库存
		sku.Stock = stockData.Stock

		// 保存SKU
		err = c.productSkuRepo.Update(c.Ctx.Request.Context(), sku)
		if err != nil {
			logs.Error("更新SKU库存失败: %v", err)
			result.HandleError(c.Ctx, err)
			return
		}
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// UpdateProductStock 更新商品库存
// @Title 更新商品库存
// @Description 更新指定ID的商品总库存数量
// @Param	product_id	path	int	true	"商品ID"
// @Param	stock		body	int	true	"新的库存数量"
// @Success 200 {object} dto.Response 成功返回空数据
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 商品不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /products/:product_id/stock [put]
func (c *MerchantSkuController) UpdateProductStock() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取商品ID
	productID, err := c.GetInt64(":product_id")
	if err != nil || productID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的商品ID")
		return
	}

	// 解析请求体中的库存数量
	var stockData struct {
		Stock int `json:"stock" form:"stock" valid:"Required"`
	}
	if err := c.ParseRequest(&stockData); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&stockData)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 库存不能为负数
	if stockData.Stock < 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "库存数量不能为负数")
		return
	}

	// 首先检查商品是否存在且属于当前商家
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), productID)
	if err != nil {
		logs.Error("获取商品信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商品是否属于当前商家
	if product.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此商品")
		return
	}

	// 更新商品库存
	// 构造更新请求
	updateReq := &productDto.UpdateProductRequest{
		ID:    productID,
		Stock: stockData.Stock,
	}

	// 调用服务层更新商品
	err = c.productService.UpdateProduct(c.Ctx.Request.Context(), productID, updateReq)
	if err != nil {
		logs.Error("更新商品库存失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
