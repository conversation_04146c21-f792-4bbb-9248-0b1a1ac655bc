/**
 * 商家规格控制器
 *
 * 本文件实现了商家对商品规格的管理功能，包括规格的查询、创建、更新和删除，以及规格值的管理。
 * 控制器负责接收HTTP请求，处理参数验证，调用相应的服务层方法，并返回处理结果。
 * 使用result包统一处理API响应，确保响应格式一致。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	productDto "o_mall_backend/modules/product/dto"
	productRepositories "o_mall_backend/modules/product/repositories"
	productServices "o_mall_backend/modules/product/services"
	"o_mall_backend/utils/common"
)

// MerchantSpecificationController 商家规格控制器
type MerchantSpecificationController struct {
	web.Controller
	specificationService productServices.SpecificationService
}

// ParseRequest 通用请求参数解析方法
func (c *MerchantSpecificationController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *MerchantSpecificationController) Prepare() {
	specRepo := productRepositories.NewSpecificationRepository()
	c.specificationService = productServices.NewSpecificationService(specRepo)
}

// GetSpecifications 获取规格列表
// @Title 获取规格列表
// @Description 获取商家可用的商品规格列表，包括全局规格和商家自定义规格
// @Param	page		query	int		false	"页码，默认1"
// @Param	page_size	query	int		false	"每页数量，默认20"
// @Success 200 {object} dto.Response 成功返回规格列表
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications [get]
func (c *MerchantSpecificationController) GetSpecifications() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 调用服务层获取规格列表
	// 只获取商家自己的规格和全局规格
	specs, total, err := c.specificationService.List(page, pageSize)
	if err != nil {
		logs.Error("获取规格列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 过滤规格，只保留全局规格和商家自己的规格
	var filteredSpecs []*productDto.SpecificationResponse
	for _, spec := range specs {
		// 根据原始代码逻辑，使用spec.ID进行对比，假设非全局规格的ID就是商家ID
		if spec.IsGlobal || (!spec.IsGlobal && spec.ID == merchantID) {
			filteredSpecs = append(filteredSpecs, spec)
		}
	}

	// 构造分页响应
	resp := map[string]interface{}{
		"list":  filteredSpecs,
		"total": total,
		"page":  page,
		"size":  pageSize,
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// GetSpecification 获取规格详情
// @Title 获取规格详情
// @Description 获取指定ID的规格详细信息
// @Param	id	path	int	true	"规格ID"
// @Success 200 {object} dto.Response 成功返回规格详情
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 规格不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications/:id [get]
func (c *MerchantSpecificationController) GetSpecification() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取规格ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格ID")
		return
	}

	// 调用服务层获取规格详情
	spec, err := c.specificationService.Get(id)
	if err != nil {
		logs.Error("获取规格详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查权限：商家只能查看全局规格和自己的规格
	// 根据原始代码逻辑，这里使用spec.ID做对比
	if !spec.IsGlobal && spec.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此规格")
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, spec)
}

// CreateSpecification 创建规格
// @Title 创建规格
// @Description 创建新的商品规格
// @Param	body	body	productDto.CreateSpecificationRequest	true	"规格信息"
// @Success 200 {object} dto.Response 成功返回创建的规格详情
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications [post]
func (c *MerchantSpecificationController) CreateSpecification() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体
	var req productDto.CreateSpecificationRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		logs.Error("参数验证失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 默认为商家私有规格，不允许商家创建全局规格
	req.IsGlobal = false
	// 使用merchantID变量，设置创建者信息或者在日志中记录
	logs.Info("商家 %d 正在创建规格", merchantID)

	// 调用服务层创建规格
	spec, err := c.specificationService.Create(&req)
	if err != nil {
		logs.Error("创建规格失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, spec)
}

// UpdateSpecification 更新规格
// @Title 更新规格
// @Description 更新商品规格信息
// @Param	id	path	int	true	"规格ID"
// @Param	body	body	productDto.UpdateSpecificationRequest	true	"规格更新信息"
// @Success 200 {object} dto.Response 成功返回更新后的规格详情
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 规格不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications/:id [put]
func (c *MerchantSpecificationController) UpdateSpecification() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取规格ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格ID")
		return
	}

	// 获取当前规格信息
	spec, err := c.specificationService.Get(id)
	if err != nil {
		logs.Error("获取规格信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查权限：商家只能修改自己的规格，不能修改全局规格
	if spec.IsGlobal || spec.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此规格")
		return
	}

	// 解析请求体
	var req productDto.UpdateSpecificationRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		logs.Error("参数验证失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParams, errMsg)
		return
	}

	// 设置规格ID
	req.ID = id

	// 不允许商家将规格设置为全局规格
	req.IsGlobal = false

	// 调用服务层更新规格
	err = c.specificationService.Update(&req)
	if err != nil {
		logs.Error("更新规格失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取更新后的规格详情
	updatedSpec, err := c.specificationService.Get(id)
	if err != nil {
		logs.Error("获取更新后的规格详情失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, updatedSpec)
}

// DeleteSpecification 删除规格
// @Title 删除规格
// @Description 删除指定ID的规格
// @Param	id	path	int	true	"规格ID"
// @Success 200 {object} dto.Response 成功返回空对象
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 规格不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications/:id [delete]
func (c *MerchantSpecificationController) DeleteSpecification() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取规格ID
	id, err := c.GetInt64(":id")
	if err != nil || id <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格ID")
		return
	}

	// 获取当前规格信息
	spec, err := c.specificationService.Get(id)
	if err != nil {
		logs.Error("获取规格信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查权限：商家只能删除自己的规格，不能删除全局规格
	// 根据原始代码逻辑，这里使用spec.ID做对比
	if spec.IsGlobal || spec.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此规格")
		return
	}

	// 调用服务层删除规格
	err = c.specificationService.Delete(id)
	if err != nil {
		logs.Error("删除规格失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetSpecificationValues 获取规格值列表
// @Title 获取规格值列表
// @Description 获取指定规格ID的规格值列表
// @Param	spec_id	path	int	true	"规格ID"
// @Success 200 {object} dto.Response 成功返回规格值列表
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 规格不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications/:spec_id/values [get]
func (c *MerchantSpecificationController) GetSpecificationValues() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取规格ID
	specID, err := c.GetInt64(":spec_id")
	if err != nil || specID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格ID")
		return
	}

	// 获取规格信息以检查权限
	spec, err := c.specificationService.Get(specID)
	if err != nil {
		logs.Error("获取规格信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查权限：商家只能查看全局规格和自己的规格的规格值
	// 根据原始代码逻辑，这里使用spec.ID做对比
	if !spec.IsGlobal && spec.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此规格")
		return
	}

	// 调用服务层获取规格值列表
	specValues, err := c.specificationService.ListSpecValues(specID)
	if err != nil {
		logs.Error("获取规格值列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, specValues)
}

// CreateSpecificationValue 创建规格值
// @Title 创建规格值
// @Description 为指定规格创建规格值
// @Param	spec_id	path	int	true	"规格ID"
// @Param	body	body	productDto.CreateSpecificationValueRequest	true	"规格值信息"
// @Success 200 {object} dto.Response 成功返回创建的规格值
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 规格不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications/:spec_id/values [post]
func (c *MerchantSpecificationController) CreateSpecificationValue() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取规格ID
	specID, err := c.GetInt64(":spec_id")
	if err != nil || specID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格ID")
		return
	}

	// 获取规格信息以检查权限
	spec, err := c.specificationService.Get(specID)
	if err != nil {
		logs.Error("获取规格信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查权限：商家只能为自己的规格添加规格值，不能为全局规格添加
	// 根据原始代码逻辑，这里使用spec.ID做对比
	if spec.IsGlobal || spec.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此规格")
		return
	}

	// 解析请求体
	var req productDto.CreateSpecValueRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, err.Error())
		return
	}

	// 设置规格ID
	req.SpecID = specID

	// 调用服务层创建规格值
	specValue, err := c.specificationService.CreateSpecValue(&req)
	if err != nil {
		logs.Error("创建规格值失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, specValue)
}

// DeleteSpecificationValue 删除规格值
// @Title 删除规格值
// @Description 删除指定的规格值
// @Param	spec_id	path	int	true	"规格ID"
// @Param	value_id	path	int	true	"规格值ID"
// @Success 200 {object} dto.Response 成功返回空对象
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 规格不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications/:spec_id/values/:value_id [delete]
func (c *MerchantSpecificationController) DeleteSpecificationValue() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取规格ID和规格值ID
	specID, err := c.GetInt64(":spec_id")
	if err != nil || specID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格ID")
		return
	}

	valueID, err := c.GetInt64(":value_id")
	if err != nil || valueID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格值ID")
		return
	}

	// 获取规格信息以检查权限
	spec, err := c.specificationService.Get(specID)
	if err != nil {
		logs.Error("获取规格信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查权限：商家只能删除自己的规格的规格值，不能删除全局规格的规格值
	// 根据原始代码逻辑，这里使用spec.ID做对比
	if spec.IsGlobal || spec.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此规格")
		return
	}

	// 检查规格值是否存在
	specValue, err := c.specificationService.GetSpecValue(valueID)
	if err != nil {
		logs.Error("获取规格值信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查规格值是否属于该规格
	if specValue.SpecID != specID {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "规格值与规格不匹配")
		return
	}

	// 在日志中记录商家操作，使用merchantID变量
	logs.Info("商家 %d 正在删除规格值 %d", merchantID, valueID)

	// 调用服务层删除规格值
	err = c.specificationService.DeleteSpecValue(valueID)
	if err != nil {
		logs.Error("删除规格值失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
// UpdateSpecificationValue 更新规格值
// @Title 更新规格值
// @Description 更新指定的规格值
// @Param	spec_id	path	int	true	"规格ID"
// @Param	value_id	path	int	true	"规格值ID"
// @Param	body	body	productDto.UpdateSpecValueRequest	true	"规格值更新信息"
// @Success 200 {object} dto.Response 成功返回更新后的规格值
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 403 {object} dto.Response 无权操作
// @Failure 404 {object} dto.Response 规格不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /specifications/:spec_id/values/:value_id [put]
func (c *MerchantSpecificationController) UpdateSpecificationValue() {
	// 从上下文获取当前商家ID
	merchantID, ok := c.Ctx.Input.GetData("merchantID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取规格ID和规格值ID
	specID, err := c.GetInt64(":spec_id")
	if err != nil || specID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格ID")
		return
	}

	valueID, err := c.GetInt64(":value_id")
	if err != nil || valueID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的规格值ID")
		return
	}

	// 获取规格信息以检查权限
	spec, err := c.specificationService.Get(specID)
	if err != nil {
		logs.Error("获取规格信息失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查权限：商家只能修改自己的规格的规格值，不能修改全局规格的规格值
	// 根据原始代码逻辑，这里使用spec.ID做对比
	if spec.IsGlobal || spec.ID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden, "无权操作此规格")
		return
	}

	// 解析请求体
	var req productDto.UpdateSpecValueRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, err.Error())
		return
	}

	// 设置规格值ID和规格ID
	req.ID = valueID
	req.SpecID = specID

	// 调用服务层更新规格值
	err = c.specificationService.UpdateSpecValue(&req)
	if err != nil {
		logs.Error("更新规格值失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取更新后的规格值
	specValue, err := c.specificationService.GetSpecValue(valueID)
	if err != nil {
		logs.Error("获取更新后的规格值失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, specValue)
}

