/*
 * 商家文件上传控制器
 *
 * 该文件处理商家模块的文件上传相关HTTP请求，基于通用上传控制器实现。
 * 主要用于商品图片、营业执照等文件的上传功能。
 */

package controllers

import (
	"github.com/beego/beego/v2/server/web"
	adminServices "o_mall_backend/modules/admin/services"
	commonControllers "o_mall_backend/common/controllers"
)

// UploadFileController 商家文件上传控制器
type UploadFileController struct {
	web.Controller
	uploadService adminServices.UploadFileService
	commonUpload  *commonControllers.CommonUploadController
}

// Prepare 预处理方法
func (c *UploadFileController) Prepare() {
	c.uploadService = adminServices.NewUploadFileService()
	c.commonUpload = commonControllers.NewCommonUploadController("merchant")
	// 设置通用控制器的上下文
	c.commonUpload.Ctx = c.Ctx
}

// Upload 文件上传
// @Title 文件上传
// @Description 上传文件到服务器，支持商品图片等文件类型
// @Param file formData file true "上传的文件"
// @Param file_usage formData string false "文件用途，默认为product"
// @Success 200 {object} adminDto.UploadFileResponse
// @Failure 400 {object} response.ErrorResponse
// @router /upload [post]
func (c *UploadFileController) Upload() {
	// 委托给通用上传控制器处理
	c.commonUpload.Upload()
}
