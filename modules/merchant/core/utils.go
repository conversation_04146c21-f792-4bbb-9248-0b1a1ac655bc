/**
 * 商家模块工具函数
 *
 * 该文件包含商家模块使用的工具函数，为控制器和服务提供支持。
 */

package core

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/dgrijalva/jwt-go"
)

// 错误码常量
const (
	CodeNotFound     = 404 // 资源不存在
	CodeUnauthorized = 401 // 未授权
	CodeForbidden    = 403 // 禁止访问
)

// 从请求中获取当前用户ID
func GetCurrentUserID(r *http.Request) (int64, error) {
	// 从请求上下文中获取JWT Claims
	claims, ok := r.Context().Value("claims").(jwt.MapClaims)
	if !ok {
		logs.Error("获取JWT Claims失败")
		return 0, errors.New("获取用户信息失败")
	}

	// 从Claims中获取用户ID
	userIDStr, ok := claims["id"].(string)
	if !ok {
		logs.Error("Claims中不存在用户ID")
		return 0, errors.New("获取用户ID失败")
	}

	// 转换为int64
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		logs.Error("用户ID转换失败: %v", err)
		return 0, err
	}

	return userID, nil
}

// 解析int64类型参数
func ParseInt64(str string) (int64, error) {
	if str == "" {
		return 0, errors.New("参数不能为空")
	}
	return strconv.ParseInt(str, 10, 64)
}

// 解析int类型参数
func ParseInt(str string) (int, error) {
	if str == "" {
		return 0, errors.New("参数不能为空")
	}
	val, err := strconv.ParseInt(str, 10, 32)
	if err != nil {
		return 0, err
	}
	return int(val), nil
}
