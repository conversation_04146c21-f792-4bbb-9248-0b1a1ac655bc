# 商家模块API使用示例

本文档提供商家模块各API的详细使用示例，帮助前端开发人员和集成测试人员快速理解和使用这些接口。

## 目录

- [1. 商家结算方式管理](#1-商家结算方式管理)
  - [1.1 创建结算方式](#11-创建结算方式)
  - [1.2 获取结算方式列表](#12-获取结算方式列表)
  - [1.3 获取结算方式详情](#13-获取结算方式详情)
  - [1.4 更新结算方式](#14-更新结算方式)
  - [1.5 删除结算方式](#15-删除结算方式)
  - [1.6 设置默认结算方式](#16-设置默认结算方式)
- [2. 商家证件管理](#2-商家证件管理)
  - [2.1 上传证件](#21-上传证件)
  - [2.2 获取证件列表](#22-获取证件列表)
  - [2.3 获取证件详情](#23-获取证件详情)
  - [2.4 更新证件](#24-更新证件)
  - [2.5 删除证件](#25-删除证件)
  - [2.6 获取即将过期证件](#26-获取即将过期证件)
- [3. 管理员证件审核](#3-管理员证件审核)
  - [3.1 获取商家证件列表](#31-获取商家证件列表)
  - [3.2 获取证件详情](#32-获取证件详情)
  - [3.3 审核证件](#33-审核证件)

## 1. 商家结算方式管理

### 1.1 创建结算方式

**请求示例：**

```http
POST /api/v1/merchant/settlements HTTP/1.1
Host: api.example.com
Content-Type: application/json
Authorization: Bearer {TOKEN}

{
  "type": 1,
  "account_name": "张三",
  "account_number": "****************",
  "bank_name": "中国银行",
  "branch_name": "北京朝阳支行",
  "swift_code": "BKCHCNBJ",
  "is_default": true
}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 1001
  }
}
```

### 1.2 获取结算方式列表

**请求示例：**

```http
GET /api/v1/merchant/settlements HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 2,
    "settlements": [
      {
        "id": 1001,
        "type": 1,
        "account_name": "张三",
        "account_number": "****************",
        "bank_name": "中国银行",
        "branch_name": "北京朝阳支行",
        "swift_code": "BKCHCNBJ",
        "is_default": true,
        "created_at": "2025-05-01T10:20:30Z"
      },
      {
        "id": 1002,
        "type": 2,
        "account_name": "张三的微信",
        "account_number": "zhangsan_wx",
        "bank_name": "",
        "branch_name": "",
        "swift_code": "",
        "is_default": false,
        "created_at": "2025-05-02T14:25:10Z"
      }
    ]
  }
}
```

### 1.3 获取结算方式详情

**请求示例：**

```http
GET /api/v1/merchant/settlements/1001 HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 1001,
    "merchant_id": 100,
    "type": 1,
    "account_name": "张三",
    "account_number": "****************",
    "bank_name": "中国银行",
    "branch_name": "北京朝阳支行",
    "swift_code": "BKCHCNBJ",
    "is_default": true,
    "created_at": "2025-05-01T10:20:30Z",
    "updated_at": "2025-05-01T10:20:30Z"
  }
}
```

### 1.4 更新结算方式

**请求示例：**

```http
PUT /api/v1/merchant/settlements/1001 HTTP/1.1
Host: api.example.com
Content-Type: application/json
Authorization: Bearer {TOKEN}

{
  "account_name": "张三(更新)",
  "account_number": "****************",
  "bank_name": "中国工商银行",
  "branch_name": "北京海淀支行",
  "swift_code": "ICBKCNBJ"
}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```

### 1.5 删除结算方式

**请求示例：**

```http
DELETE /api/v1/merchant/settlements/1002 HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```

### 1.6 设置默认结算方式

**请求示例：**

```http
PUT /api/v1/merchant/settlements/1001/default HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```

## 2. 商家证件管理

### 2.1 上传证件

**请求示例：**

```http
POST /api/v1/merchant/documents HTTP/1.1
Host: api.example.com
Content-Type: multipart/form-data
Authorization: Bearer {TOKEN}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="type"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name"

法人身份证正面
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="number"

110101199001011234
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="issue_date"

2020-01-01
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="expire_date"

2030-01-01
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

法人身份证正面照片
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="id_card_front.jpg"
Content-Type: image/jpeg

(文件二进制内容)
------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 2001
  }
}
```

### 2.2 获取证件列表

**请求示例：**

```http
GET /api/v1/merchant/documents?type=1 HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 2,
    "documents": [
      {
        "id": 2001,
        "type": 1,
        "name": "法人身份证正面",
        "number": "110101199001011234",
        "file_url": "https://example.com/storage/documents/id_card_front_123456.jpg",
        "issue_date": "2020-01-01",
        "expire_date": "2030-01-01",
        "status": 1,
        "created_at": "2025-05-10T10:20:30Z"
      },
      {
        "id": 2002,
        "type": 1,
        "name": "法人身份证反面",
        "number": "110101199001011234",
        "file_url": "https://example.com/storage/documents/id_card_back_123456.jpg",
        "issue_date": "2020-01-01",
        "expire_date": "2030-01-01",
        "status": 0,
        "created_at": "2025-05-10T10:25:15Z"
      }
    ]
  }
}
```

### 2.3 获取证件详情

**请求示例：**

```http
GET /api/v1/merchant/documents/2001 HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 2001,
    "merchant_id": 100,
    "type": 1,
    "name": "法人身份证正面",
    "number": "110101199001011234",
    "file_url": "https://example.com/storage/documents/id_card_front_123456.jpg",
    "issue_date": "2020-01-01",
    "expire_date": "2030-01-01",
    "status": 1,
    "remark": "法人身份证正面照片",
    "verify_user_id": 10,
    "verify_time": "2025-05-11T09:30:00Z",
    "verify_remark": "验证通过",
    "created_at": "2025-05-10T10:20:30Z",
    "updated_at": "2025-05-11T09:30:00Z"
  }
}
```

### 2.4 更新证件

**请求示例：**

```http
PUT /api/v1/merchant/documents/2001 HTTP/1.1
Host: api.example.com
Content-Type: multipart/form-data
Authorization: Bearer {TOKEN}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="name"

法人身份证正面(更新)
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="expire_date"

2035-01-01
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

更新后的法人身份证正面照片
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="id_card_front_new.jpg"
Content-Type: image/jpeg

(文件二进制内容)
------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```

### 2.5 删除证件

**请求示例：**

```http
DELETE /api/v1/merchant/documents/2002 HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```

### 2.6 获取即将过期证件

**请求示例：**

```http
GET /api/v1/merchant/documents/expiring HTTP/1.1
Host: api.example.com
Authorization: Bearer {TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": [
    {
      "id": 2001,
      "type": 1,
      "name": "法人身份证正面",
      "expire_date": "2025-06-01",
      "days_remaining": 10,
      "status": 1
    },
    {
      "id": 2003,
      "type": 2,
      "name": "营业执照",
      "expire_date": "2025-06-15",
      "days_remaining": 24,
      "status": 1
    }
  ]
}
```

## 3. 管理员证件审核

### 3.1 获取商家证件列表

**请求示例：**

```http
GET /api/v1/admin/merchant/documents?merchant_id=100&type=0&page=1&size=10 HTTP/1.1
Host: api.example.com
Authorization: Bearer {ADMIN_TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total": 3,
    "page": 1,
    "size": 10,
    "documents": [
      {
        "id": 2001,
        "merchant_id": 100,
        "merchant_name": "测试商家",
        "type": 1,
        "name": "法人身份证正面",
        "number": "110101199001011234",
        "file_url": "https://example.com/storage/documents/id_card_front_123456.jpg",
        "status": 1,
        "created_at": "2025-05-10T10:20:30Z"
      },
      {
        "id": 2002,
        "merchant_id": 100,
        "merchant_name": "测试商家",
        "type": 1,
        "name": "法人身份证反面",
        "number": "110101199001011234",
        "file_url": "https://example.com/storage/documents/id_card_back_123456.jpg",
        "status": 0,
        "created_at": "2025-05-10T10:25:15Z"
      },
      {
        "id": 2003,
        "merchant_id": 100,
        "merchant_name": "测试商家",
        "type": 2,
        "name": "营业执照",
        "number": "91110105MA01ABCDEF",
        "file_url": "https://example.com/storage/documents/business_license_123456.jpg",
        "status": 0,
        "created_at": "2025-05-11T14:30:00Z"
      }
    ]
  }
}
```

### 3.2 获取证件详情

**请求示例：**

```http
GET /api/v1/admin/merchant/documents/2003 HTTP/1.1
Host: api.example.com
Authorization: Bearer {ADMIN_TOKEN}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 2003,
    "merchant_id": 100,
    "merchant_name": "测试商家",
    "type": 2,
    "name": "营业执照",
    "number": "91110105MA01ABCDEF",
    "file_url": "https://example.com/storage/documents/business_license_123456.jpg",
    "issue_date": "2020-03-15",
    "expire_date": "2025-06-15",
    "status": 0,
    "remark": "营业执照扫描件",
    "verify_user_id": null,
    "verify_time": null,
    "verify_remark": null,
    "created_at": "2025-05-11T14:30:00Z",
    "updated_at": "2025-05-11T14:30:00Z"
  }
}
```

### 3.3 审核证件

**请求示例：**

```http
PUT /api/v1/admin/merchant/documents/2003/verify HTTP/1.1
Host: api.example.com
Content-Type: application/json
Authorization: Bearer {ADMIN_TOKEN}

{
  "status": 1,
  "remark": "营业执照审核通过"
}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```

**审核拒绝示例：**

```http
PUT /api/v1/admin/merchant/documents/2002/verify HTTP/1.1
Host: api.example.com
Content-Type: application/json
Authorization: Bearer {ADMIN_TOKEN}

{
  "status": 2,
  "remark": "身份证照片模糊，请重新上传"
}
```

**响应示例：**

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```
