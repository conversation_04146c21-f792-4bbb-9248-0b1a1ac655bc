# 商家模块API接口文档

## 概述

本文档详细描述了商家模块提供的所有API接口，包括接口路径、请求方法、参数说明和响应格式。

## 接口基础信息

- **基础路径**: `/api/v1/merchant`
- **认证方式**: JWT令牌认证（部分接口无需认证）
- **响应格式**: JSON
- **编码**: UTF-8

## 1. 公开接口

### 1.1 商家登录

**接口路径**: `/login`  
**请求方法**: `POST`  
**功能说明**: 商家账号登录，获取认证Token  

**请求参数**:
```json
{
  "username": "商家用户名",
  "password": "登录密码"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "token_info": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_in": 3600,
      "token_type": "Bearer"
    },
    "merchant": {
      "id": 1,
      "name": "示例商家",
      "logo": "http://example.com/logo.png",
      "description": "这是一个示例商家",
      "username": "merchant001",
      "contact_name": "张三",
      "contact_mobile": "***********",
      "contact_email": "<EMAIL>",
      "business_license": "http://example.com/license.png",
      "address": "北京市朝阳区xxx街xxx号",
      "level": 0,
      "balance": 0,
      "audit_status": 1,
      "status": 1,
      "operation_status": 1,
      "business_hours": [
        {
          "weekday": 1,
          "startTime": "08:00",
          "endTime": "20:00"
        }
      ],
      "created_at": "2023-01-01T12:00:00Z"
    }
  }
}
```

### 1.2 手机号登录

**接口路径**: `/login/mobile`  
**请求方法**: `POST`  
**功能说明**: 商家手机号+密码登录，获取认证Token  

**请求参数**:
```json
{
  "mobile": "***********",
  "password": "登录密码"
}
```

**响应格式**: 同上登录接口

### 1.3 发送验证码

**接口路径**: `/send-code`  
**请求方法**: `POST`  
**功能说明**: 发送商家登录的手机验证码  

**请求参数**:
```json
{
  "mobile": "***********"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "expire_time": 5
  }
}
```

### 1.4 验证码登录

**接口路径**: `/login/verify-code`  
**请求方法**: `POST`  
**功能说明**: 商家手机号+验证码登录，获取认证Token  

**请求参数**:
```json
{
  "mobile": "***********",
  "code": "123456"
}
```

**响应格式**: 同上登录接口

### 1.5 商家注册

**接口路径**: `/register`  
**请求方法**: `POST`  
**功能说明**: 商家账号注册  

**请求参数**:
```json
{
  "name": "商家名称",
  "logo": "商家Logo图片URL",
  "description": "商家描述",
  "category_id": 1,
  "username": "登录用户名",
  "password": "登录密码",
  "contact_name": "联系人姓名",
  "contact_mobile": "联系人手机号",
  "contact_email": "联系人邮箱",
  "business_license": "营业执照图片URL",
  "address": "商家地址"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 100
  }
}
```

### 1.6 文件上传

**接口路径**: `/upload`  
**请求方法**: `POST`  
**功能说明**: 上传商家相关文件（图片等）  

**请求参数**: 使用multipart/form-data格式
- `file`: 文件数据

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "url": "http://example.com/uploads/image123.jpg"
  }
}
```

### 1.7 获取商家分类

**接口路径**: `/merchant-categories`  
**请求方法**: `GET`  
**功能说明**: 获取商家分类列表  

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": [
    {
      "id": 1,
      "name": "餐饮",
      "icon": "http://example.com/restaurant.png",
      "sort_order": 1
    },
    {
      "id": 2,
      "name": "服装",
      "icon": "http://example.com/clothing.png",
      "sort_order": 2
    }
  ]
}
```

## 2. 商家认证接口

以下接口需要商家JWT认证，请求头需包含`Authorization: Bearer {token}`

### 2.1 获取商家信息

**接口路径**: `/secured/info`  
**请求方法**: `GET`  
**功能说明**: 获取当前登录商家的详细信息  

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 1,
    "name": "示例商家",
    "logo": "http://example.com/logo.png",
    "description": "这是一个示例商家",
    "username": "merchant001",
    "contact_name": "张三",
    "contact_mobile": "***********",
    "contact_email": "<EMAIL>",
    "business_license": "http://example.com/license.png",
    "address": "北京市朝阳区xxx街xxx号",
    "level": 0,
    "balance": 0,
    "audit_status": 1,
    "status": 1,
    "operation_status": 1,
    "business_hours": [
      {
        "weekday": 1,
        "startTime": "08:00",
        "endTime": "20:00"
      }
    ],
    "created_at": "2023-01-01T12:00:00Z"
  }
}
```

### 2.2 更新商家信息

**接口路径**: `/secured/`  
**请求方法**: `PUT`  
**功能说明**: 更新商家的基本信息  

**请求参数**:
```json
{
  "id": 1,
  "name": "更新后的商家名称",
  "logo": "更新后的Logo URL",
  "description": "更新后的描述",
  "contact_name": "李四",
  "contact_mobile": "***********",
  "contact_email": "<EMAIL>",
  "business_license": "更新后的营业执照URL",
  "address": "更新后的地址"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": null
}
```

### 2.3 修改密码

**接口路径**: `/secured/password`  
**请求方法**: `PUT`  
**功能说明**: 修改当前登录商家的密码  

**请求参数**:
```json
{
  "old_password": "旧密码",
  "new_password": "新密码"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": null
}
```

### 2.4 更新经营状态

**接口路径**: `/secured/operation-status`  
**请求方法**: `PUT`  
**功能说明**: 商家更新自己的经营状态，可快速切换营业中或休息中状态  

**请求参数**:
```json
{
  "operation_status": 1  // 0-休息中，1-营业中
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": null
}
```

### 2.5 开始营业

**接口路径**: `/secured/operation-status-start`  
**请求方法**: `PUT`  
**功能说明**: 商家开始营业，记录开始营业时间  

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "operation_status": 1
  }
}
```

### 2.6 结束营业

**接口路径**: `/secured/operation-status-stop`  
**请求方法**: `PUT`  
**功能说明**: 商家结束营业，记录结束营业时间  

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "operation_status": 0
  }
}
```

### 2.7 获取营业统计信息

**接口路径**: `/secured/business-stats`  
**请求方法**: `GET`  
**功能说明**: 获取指定周期内的商家营业统计信息，包括总营业时长、日均营业时长、开店次数等  

**请求参数**:
- `period`: 统计周期，如7d表示7天（默认7d）

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "total_duration": 56.5,        // 总营业时长（小时）
    "average_duration": 8.07,       // 日均营业时长（小时）
    "total_open_times": 9,          // 总开店次数
    "complete_open_times": 8,       // 完整营业次数（有开始和结束记录）
    "incomplete_open_times": 1,     // 不完整营业次数（只有开始没有结束记录）
    "average_open_time_per_day": 1.29, // 每天平均开店次数
    "period": "7d",                // 统计周期
    "days_count": 7                 // 统计天数
  }
}
```

## 3. 管理员接口

以下接口需要管理员JWT认证，请求头需包含`Authorization: Bearer {admin_token}`

### 3.1 获取商家列表

**接口路径**: `/admin/`  
**请求方法**: `GET`  
**功能说明**: 管理员获取商家列表  

**请求参数**:
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）
- `name`: 商家名称（可选）
- `status`: 商家状态（可选）
- `audit_status`: 审核状态（可选）
- `level`: 商家等级（可选）

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "示例商家1",
        "logo": "http://example.com/logo1.png",
        "description": "这是一个示例商家",
        "username": "merchant001",
        "contact_name": "张三",
        "contact_mobile": "***********",
        "contact_email": "<EMAIL>",
        "business_license": "http://example.com/license1.png",
        "address": "北京市朝阳区xxx街xxx号",
        "level": 0,
        "balance": 0,
        "audit_status": 1,
        "status": 1,
        "operation_status": 1,
        "created_at": "2023-01-01T12:00:00Z"
      },
      // 更多商家...
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20
  }
}
```

### 3.2 获取单个商家

**接口路径**: `/admin/:id`  
**请求方法**: `GET`  
**功能说明**: 管理员获取指定ID的商家详情  

**响应示例**: 同2.1获取商家信息接口

### 3.3 删除商家

**接口路径**: `/admin/:id`  
**请求方法**: `DELETE`  
**功能说明**: 管理员删除指定ID的商家  

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": null
}
```

### 3.4 审核商家

**接口路径**: `/admin/audit`  
**请求方法**: `POST`  
**功能说明**: 管理员审核商家注册申请  

**请求参数**:
```json
{
  "id": 1,
  "audit_status": 1,  // 0-待审核，1-已审核，2-已拒绝
  "reject_reason": "拒绝原因（当audit_status=2时必填）"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": null
}
```

### 3.5 重置密码

**接口路径**: `/admin/password/reset`  
**请求方法**: `PUT`  
**功能说明**: 管理员重置商家密码  

**请求参数**:
```json
{
  "id": 1,
  "new_password": "新密码"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": null
}
```

### 3.6 更新余额

**接口路径**: `/admin/balance`  
**请求方法**: `PUT`  
**功能说明**: 管理员更新商家账户余额  

**请求参数**:
```json
{
  "id": 1,
  "amount": 100.00,  // 正数增加，负数减少
  "remark": "调整原因说明"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": null
}
```

## 4. 错误码说明

| 错误码 | 说明 |
| ----- | ---- |
| 0 | 成功 |
| 400 | 参数错误 |
| 401 | 未认证/认证失败 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
