# 商家模块业务流程文档

## 概述

本文档详细描述了商家模块中的核心业务流程，包括商家注册、审核、登录、信息管理等关键流程的具体步骤和业务规则。

## 1. 商家注册流程

### 流程说明
该流程描述了新商家如何在平台上完成注册申请，从提交信息到等待审核的完整过程。

### 流程步骤

1. **信息填写**
   - 商家填写基本信息(店铺名称、联系方式等)
   - 商家上传必要资质(营业执照等)
   - 商家选择所属分类
   - 商家设置登录账号和密码

2. **信息验证**
   - 系统验证用户名唯一性
   - 系统验证手机号格式及有效性
   - 系统验证邮箱格式
   - 系统验证其他必填字段

3. **提交申请**
   - 系统创建商家记录，状态设为"待审核"
   - 系统记录商家注册日志
   - 系统向管理员发送新商家待审核通知

4. **注册结果**
   - 系统提示商家注册成功，等待审核
   - 商家可使用账号密码登录查看审核状态

### 业务规则

- 同一手机号最多可注册1个商家账号
- 商家名称不能包含敏感词汇
- 密码必须满足安全要求(至少8位，包含字母和数字)
- 新注册商家默认为"普通"等级
- 商家注册后状态为"待审核"，需要管理员审核通过后才能正常使用平台功能

## 2. 商家审核流程

### 流程说明
该流程描述了管理员如何审核新注册的商家申请，确保商家资质符合平台要求。

### 流程步骤

1. **查看待审核商家**
   - 管理员登录后台系统
   - 管理员查看待审核商家列表
   - 管理员选择需要审核的商家

2. **审核商家资质**
   - 管理员查看商家详细信息
   - 管理员核实商家营业执照等资质
   - 管理员评估商家类型与分类是否匹配

3. **审核决策**
   - 管理员做出审核决定(通过/拒绝)
   - 如拒绝，需填写拒绝原因

4. **审核结果处理**
   - 系统更新商家状态(已审核/已拒绝)
   - 系统记录审核操作日志
   - 系统向商家发送审核结果通知

### 业务规则

- 所有新注册商家必须经过审核才能正常使用平台功能
- 审核拒绝必须提供明确的拒绝原因
- 审核通过的商家状态变为"已审核"，可以正常使用平台功能
- 审核拒绝的商家可以修改信息后重新提交审核

## 3. 商家登录流程

### 流程说明
该流程描述了商家如何登录平台，包括多种登录方式和账号状态验证。

### 流程步骤

1. **选择登录方式**
   - 账号密码登录
   - 手机号密码登录
   - 手机号验证码登录

2. **身份验证**
   - 系统验证登录凭证(用户名/密码或手机号/验证码)
   - 系统检查商家账号状态

3. **登录处理**
   - 验证通过，生成JWT令牌对(访问令牌和刷新令牌)
   - 更新商家最后登录时间和IP
   - 返回商家信息和令牌

4. **特殊情况处理**
   - 处理审核中商家登录(仅可查看有限信息)
   - 处理被拒绝商家登录(显示拒绝原因)
   - 处理被禁用商家登录(显示禁用提示)

### 业务规则

- 连续5次密码错误将锁定账号15分钟
- 审核中的商家仅能查看自己的基本信息和审核状态
- 审核被拒绝的商家可以查看拒绝原因并修改信息重新提交
- 被禁用的商家无法登录平台
- 商家登录后令牌有效期为24小时

## 4. 商家信息管理流程

### 流程说明
该流程描述了商家如何管理和更新自己的信息，包括基本信息、密码、营业状态等。

### 流程步骤

1. **查看信息**
   - 商家登录平台
   - 商家查看自己的详细信息

2. **更新基本信息**
   - 商家修改店铺名称、Logo、描述等信息
   - 商家修改联系人信息
   - 商家修改店铺地址等信息
   - 系统验证并保存更新的信息

3. **更新密码**
   - 商家输入原密码
   - 商家输入并确认新密码
   - 系统验证原密码正确性
   - 系统更新密码并记录操作日志

4. **更新营业状态**
   - 商家切换营业状态(营业中/休息中)
   - 商家设置营业时间
   - 系统更新并保存营业状态和时间

### 业务规则

- 商家名称修改需保持唯一性
- 密码修改必须验证原密码
- 密码必须符合安全要求
- 商家信息更新操作会被记录到操作日志中
- 商家可以随时切换营业状态，无需审核

## 5. 商家余额管理流程

### 流程说明
该流程描述了商家账户余额的变动管理，包括充值、扣款、查询等操作。

### 流程步骤

1. **余额查询**
   - 商家查看当前账户余额
   - 商家查看余额变动历史记录

2. **余额充值**
   - 管理员为商家充值
   - 系统记录充值金额和操作说明
   - 系统更新商家账户余额
   - 系统记录操作日志

3. **余额扣款**
   - 管理员从商家账户扣款
   - 系统验证余额是否充足
   - 系统记录扣款金额和操作说明
   - 系统更新商家账户余额
   - 系统记录操作日志

### 业务规则

- 商家余额不能为负数
- 每次余额变动必须有明确的操作说明
- 余额变动操作只能由管理员执行
- 所有余额变动操作都会被详细记录
- 重大金额变动需要多级审批

## 6. 商家经营状态管理流程

### 流程说明
该流程描述了商家如何管理自己的经营状态，包括营业时间设置等。

### 流程步骤

1. **查看当前状态**
   - 商家查看当前经营状态(营业中/休息中)
   - 商家查看当前设置的营业时间

2. **更新经营状态**
   - 商家选择新的经营状态
   - 系统更新状态并记录变更

3. **设置营业时间**
   - 商家设置每周不同日期的营业时间
   - 商家可设置特殊日期的营业时间
   - 系统保存并更新营业时间设置

### 业务规则

- 商家可以随时切换经营状态
- 营业时间必须按照有效的时间格式设置(HH:MM)
- 系统会根据当前时间和设置的营业时间自动判断商家是否在营业时间内
- 商家可以设置特殊节假日的营业时间

## 7. 商家账户安全流程

### 流程说明
该流程描述了商家账户的安全保护措施，包括登录保护、密码重置等。

### 流程步骤

1. **异常登录检测**
   - 系统检测异常IP登录
   - 系统检测异常时间登录
   - 系统发送安全警告

2. **密码重置**
   - 商家申请重置密码
   - 系统发送验证码到绑定手机
   - 商家验证身份
   - 商家设置新密码
   - 系统更新密码并发送通知

3. **账户锁定/解锁**
   - 连续多次密码错误触发账户锁定
   - 管理员处理账户锁定申诉
   - 管理员解锁账户
   - 系统记录锁定/解锁操作

### 业务规则

- 连续5次密码错误将锁定账号15分钟
- 异地登录会触发安全验证
- 密码重置必须验证手机号
- 新密码不能与最近3次使用的密码相同
- 账户解锁必须由管理员操作
