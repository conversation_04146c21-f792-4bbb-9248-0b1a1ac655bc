# 商家模块代码结构文档

## 概述

本文档详细描述了商家模块的代码结构设计，包括目录结构、文件功能、代码组织方式等，帮助开发者快速理解和上手商家模块的开发。

## 1. 目录结构

商家模块采用标准的分层架构，各层职责明确，代码结构如下：

```
modules/merchant/
├── controllers/         # 控制器层，处理HTTP请求
├── core/                # 核心组件和工具
├── dto/                 # 数据传输对象，定义请求和响应结构
├── init.go              # 模块初始化文件
├── models/              # 数据模型层，定义数据结构
├── repositories/        # 数据访问层，处理数据库操作
├── routers/             # 路由配置，注册API路由
└── services/            # 业务服务层，实现业务逻辑
```

## 2. 各部分详细说明

### 2.1 控制器层(controllers)

控制器层负责处理HTTP请求，验证请求参数，调用服务层，返回响应结果。

**主要文件**:
- `merchant_controller.go`: 商家基本操作控制器
- `admin_merchant_controller.go`: 管理员操作商家控制器
- `merchant_category_controller.go`: 商家分类控制器
- `merchant_product_controller.go`: 商家商品控制器
- `merchant_business_category_controller.go`: 商家经营分类控制器
- `merchant_specification_controller.go`: 商品规格控制器
- `merchant_sku_controller.go`: 商品SKU控制器
- `upload_file_controller.go`: 文件上传控制器
- `merchant_category_api_controller.go`: 商家分类API控制器

**职责**:
- 接收并验证HTTP请求参数
- 调用相应的服务层处理业务逻辑
- 根据处理结果构造HTTP响应
- 处理异常和错误情况

### 2.2 核心组件(core)

核心组件包含模块特有的工具函数和通用功能。

**主要文件**:
- `utils.go`: 商家模块特有的工具函数

**职责**:
- 提供模块内部使用的工具函数
- 实现特定的业务算法
- 提供通用功能支持

### 2.3 数据传输对象(dto)

数据传输对象定义了前后端数据交互的结构，包括请求参数和响应结果的格式。

**主要文件**:
- `merchant_dto.go`: 商家相关DTO
- `category_dto.go`: 分类相关DTO
- `business_category_dto.go`: 经营分类相关DTO
- `product_dto.go`: 商品相关DTO

**职责**:
- 定义API请求参数结构
- 定义API响应结果结构
- 提供数据验证规则
- 实现数据转换方法

### 2.4 模块初始化(init.go)

模块初始化文件负责在应用启动时初始化商家模块的各个组件。

**职责**:
- 注册ORM模型
- 初始化路由
- 配置模块依赖
- 加载必要的配置

### 2.5 数据模型层(models)

数据模型层定义了与数据库表对应的结构体，以及相关的数据操作方法。

**主要文件**:
- `merchant.go`: 商家模型
- `merchant_log.go`: 商家日志模型
- `merchant_category.go`: 商家分类模型

**职责**:
- 定义数据库表对应的结构体
- 实现ORM标签和表关系
- 提供模型特有的方法
- 定义常量和枚举值

### 2.6 数据访问层(repositories)

数据访问层负责数据库操作，封装CRUD方法，实现数据持久化。

**主要文件**:
- `merchant_repository.go`: 商家数据访问
- `merchant_log_repository.go`: 商家日志数据访问
- `merchant_category_repository.go`: 商家分类数据访问
- `business_category_repository.go`: 商家经营分类数据访问
- `product_repository.go`: 商品数据访问

**职责**:
- 实现数据库CRUD操作
- 封装复杂的查询逻辑
- 处理事务和并发
- 优化数据库访问性能

### 2.7 路由配置(routers)

路由配置负责注册API路由，将URL路径映射到控制器方法。

**主要文件**:
- `router.go`: 商家模块路由配置

**职责**:
- 定义API路由路径
- 映射URL到控制器方法
- 配置中间件和权限控制
- 分组和命名空间管理

### 2.8 业务服务层(services)

业务服务层实现核心业务逻辑，是整个模块的核心部分。

**主要文件**:
- `merchant_service.go`: 商家服务接口和实现
- `merchant_category_service.go`: 商家分类服务
- `business_category_service.go`: 商家经营分类服务
- `product_service.go`: 商品服务

**职责**:
- 实现业务逻辑和规则
- 协调多个Repository的操作
- 处理业务异常和错误
- 实现事务控制
- 权限和业务规则验证

## 3. 代码规范

商家模块遵循以下代码规范：

### 3.1 命名规范

- **包名**: 使用小写单词，如`controllers`、`services`
- **文件名**: 使用下划线分隔的小写单词，如`merchant_controller.go`
- **结构体名**: 使用CamelCase，如`MerchantController`
- **接口名**: 通常以"er"结尾，如`MerchantRepository`
- **方法名**: 使用CamelCase，如`GetMerchantByID`
- **常量**: 使用CamelCase，如`MerchantStatusApproved`

### 3.2 注释规范

- **文件头注释**: 描述文件的主要功能和作用
- **结构体注释**: 描述结构体的用途和重要字段
- **方法注释**: 描述方法的功能、参数和返回值
- **API注释**: 使用Swagger注释格式

### 3.3 错误处理

- 使用统一的错误处理机制
- 错误信息明确且具有可读性
- 关键操作错误需记录到日志
- 使用自定义的错误类型区分不同错误情况

## 4. 依赖关系

商家模块的主要依赖关系如下：

```
Controllers -> Services -> Repositories -> Models
     |             |
     v             v
    DTOs <-----> Utils
```

## 5. 代码示例

### 5.1 控制器示例

```go
/**
 * 商家控制器
 *
 * 该文件实现了商家相关的API接口控制器，处理商家登录、创建、管理等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
    // 导入包...
)

// MerchantController 商家控制器
type MerchantController struct {
    web.Controller
    merchantService services.MerchantService
}

// Prepare 初始化控制器
func (c *MerchantController) Prepare() {
    c.merchantService = services.NewMerchantService()
}

// Login 商家登录
// @Title 商家登录
// @Description 商家账号登录，获取认证Token
// @Param body body merchantDto.MerchantLoginRequest true "登录信息"
// @Success 200 {object} dto.Response 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /login [post]
func (c *MerchantController) Login() {
    // 实现登录逻辑...
}
```

### 5.2 服务层示例

```go
/**
 * 商家服务实现
 *
 * 该文件实现了商家服务接口，提供商家登录、创建、管理等业务逻辑。
 * 商家服务是电商平台的核心服务之一，负责商家身份认证和信息管理。
 */

package services

import (
    // 导入包...
)

// MerchantService 商家服务接口
type MerchantService interface {
    Login(ctx context.Context, req *dto.MerchantLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error)
    CreateMerchant(ctx context.Context, req *dto.CreateMerchantRequest) (int64, error)
    // 更多方法...
}

// MerchantServiceImpl 商家服务实现
type MerchantServiceImpl struct {
    merchantRepo repositories.MerchantRepository
}

// NewMerchantService 创建商家服务实例
func NewMerchantService() MerchantService {
    return &MerchantServiceImpl{
        merchantRepo: repositories.NewMerchantRepository(),
    }
}

// Login 商家登录
func (s *MerchantServiceImpl) Login(ctx context.Context, req *dto.MerchantLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error) {
    // 实现登录逻辑...
}
```

### 5.3 模型示例

```go
/**
 * 商家模型
 *
 * 该文件定义了商家模型，表示电商平台上的商家实体。
 * 包含商家的基本信息、联系方式、账户状态、营业时间等数据。
 */

package models

import (
    // 导入包...
)

// 商家状态常量
const (
    MerchantStatusPending  = 0 // 待审核
    MerchantStatusApproved = 1 // 已审核
    MerchantStatusRejected = 2 // 已拒绝
    MerchantStatusDisabled = 3 // 已禁用
    MerchantStatusLocked   = 4 // 已锁定
)

// Merchant 商家模型
type Merchant struct {
    ID              int64     `orm:"pk;auto;column(id)" json:"id"`                   // 商家ID
    Name            string    `orm:"size(100);column(name)" json:"name"`             // 商家名称
    // 更多字段...
}

// TableName 指定表名
func (m *Merchant) TableName() string {
    return "merchant"
}

// GetBusinessHours 获取商家营业时间列表
func (m *Merchant) GetBusinessHours() ([]BusinessHour, error) {
    // 实现获取营业时间的逻辑...
}
```

## 6. 开发建议

在开发商家模块时，建议遵循以下原则：

1. **分层清晰**: 保持各层职责明确，避免跨层调用
2. **接口优先**: 优先定义接口，再实现具体类，便于测试和扩展
3. **参数验证**: 在控制器层进行严格的参数验证
4. **统一错误**: 使用统一的错误处理机制和错误码
5. **详细日志**: 记录关键操作和异常情况的详细日志
6. **性能优化**: 注意数据库查询性能和大数据量处理
7. **安全考虑**: 重视数据安全和接口安全
8. **文档同步**: 及时更新API文档和代码注释
