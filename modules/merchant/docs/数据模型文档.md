# 商家模块数据模型文档

## 概述

本文档详细描述了商家模块的数据模型设计，包括数据表结构、字段说明、关系模型等，为开发者提供完整的数据结构参考。

## 1. 商家表(merchant)

### 表描述
存储商家的基本信息、账户信息、状态等核心数据。

### 表结构

| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | int64 | 商家唯一标识 | 主键，自增 |
| name | string(100) | 商家店铺名称 | 非空 |
| logo | string(255) | 商家店铺Logo图片URL | 可空 |
| description | text | 商家店铺详细介绍 | 可空 |
| username | string(50) | 商家登录账号 | 非空，唯一 |
| password | string(255) | 商家登录密码(加密存储) | 非空 |
| contact_name | string(50) | 商家联系人姓名 | 非空 |
| contact_mobile | string(20) | 商家联系人手机号 | 非空 |
| contact_email | string(100) | 商家联系人邮箱 | 非空 |
| business_license | string(255) | 营业执照图片URL | 非空 |
| address | string(255) | 商家店铺详细地址 | 非空 |
| category_id | int64 | 商家分类ID | 非空 |
| level | int | 商家等级(0-普通,1-银牌,2-金牌,3-钻石) | 默认0 |
| balance | float64 | 商家账户余额 | 默认0.00 |
| audit_status | int | 商家审核状态(0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定) | 默认0 |
| status | int | 商家状态(0-禁用，1-正常,2-锁定) | 默认0 |
| operation_status | int | 商家经营状态(0-休息中,1-营业中) | 默认1 |
| reject_reason | text | 商家审核拒绝原因 | 可空 |
| last_login_at | datetime | 最近一次登录时间 | 可空 |
| last_login_ip | string(50) | 最近一次登录IP地址 | 可空 |
| business_hours | text | 商家营业时间(JSON格式) | 可空 |
| created_at | datetime | 创建时间 | 自动设置 |
| updated_at | datetime | 更新时间 | 自动更新 |


### 商家API增加"是否推荐"字段支持
- 商家模型新增`is_recommended`字段（0-否，1-是）
- 创建商家API支持设置`is_recommended`参数
- 更新商家API支持修改`is_recommended`参数
- 查询商家列表API支持按`is_recommended`参数筛选
- 商家详情响应中新增`is_recommended`字段

### 关键索引
- 主键索引: `id`
- 唯一索引: `username`
- 普通索引: `category_id`, `audit_status`, `status`, `operation_status`

### 营业时间结构
`business_hours`字段以JSON格式存储商家的营业时间，具体结构如下：
```json
[
  {
    "weekday": 0,  // 星期几：0-周日，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六
    "startTime": "08:00",  // 开始营业时间，格式：HH:MM
    "endTime": "20:00"  // 结束营业时间，格式：HH:MM
  },
  {
    "weekday": 1,
    "startTime": "08:00",
    "endTime": "20:00"
  }
  // 更多营业日...
]
```

## 2. 商家日志表(merchant_log)

### 表描述
记录商家的重要操作日志，用于审计和问题追踪。

### 表结构

| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | int64 | 日志唯一标识 | 主键，自增 |
| merchant_id | int64 | 商家ID | 非空 |
| operation | string(50) | 操作类型 | 非空 |
| content | text | 操作内容详情 | 可空 |
| operator_id | int64 | 操作者ID | 可空 |
| operator_type | string(20) | 操作者类型(merchant/admin) | 非空 |
| ip | string(50) | 操作IP地址 | 可空 |
| user_agent | string(255) | 用户代理信息 | 可空 |
| created_at | datetime | 创建时间 | 自动设置 |

### 关键索引
- 主键索引: `id`
- 普通索引: `merchant_id`, `operator_id`, `operator_type`

## 3. 商家分类表(merchant_category)

### 表描述
存储商家分类信息，用于对商家进行分类管理。

### 表结构

| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | int64 | 分类唯一标识 | 主键，自增 |
| name | string(50) | 分类名称 | 非空 |
| icon | string(255) | 分类图标URL | 可空 |
| sort_order | int | 排序顺序 | 默认0 |
| status | int | 状态(0-禁用,1-启用) | 默认1 |
| description | string(255) | 分类描述 | 可空 |
| created_at | datetime | 创建时间 | 自动设置 |
| updated_at | datetime | 更新时间 | 自动更新 |

### 关键索引
- 主键索引: `id`
- 普通索引: `status`

## 4. 商家结算方式表(merchant_settlement)

### 表描述
存储商家的结算方式信息，如银行卡、微信、支付宝等。

### 表结构

| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | int64 | 结算方式唐一标识 | 主键，自增 |
| merchant_id | int64 | 商家ID | 非空 |
| type | int | 结算方式类型(1-银行卡,2-微信,3-支付宝) | 非空 |
| account_name | string(100) | 账户名称 | 非空 |
| account_number | string(100) | 账户号码/微信号/支付宝账户 | 非空 |
| bank_name | string(100) | 银行名称(银行卡类型必填) | 可空 |
| branch_name | string(100) | 支行名称 | 可空 |
| swift_code | string(50) | Swift代码(国际转账使用) | 可空 |
| is_default | bool | 是否默认结算方式 | 默认false |
| created_at | datetime | 创建时间 | 自动设置 |
| updated_at | datetime | 更新时间 | 自动更新 |

### 关键索引
- 主键索引: `id`
- 普通索引: `merchant_id`, `type`, `is_default`

## 5. 商家证件表(merchant_document)

### 表描述
存储商家的各类证件和协议文件，如身份证、营业执照、合作协议等。

### 表结构

| 字段名 | 类型 | 描述 | 约束 |
|-------|------|------|------|
| id | int64 | 证件唐一标识 | 主键，自增 |
| merchant_id | int64 | 商家ID | 非空 |
| type | int | 证件类型(1-法人身份证,2-营业执照,3-合作协议) | 非空 |
| name | string(100) | 证件名称 | 非空 |
| number | string(100) | 证件号码 | 可空 |
| file_url | string(255) | 证件文件URL | 非空 |
| issue_date | date | 签发日期 | 可空 |
| expire_date | date | 到期日期 | 可空 |
| status | int | 状态(0-待审核,1-已审核,2-已拒绝) | 默认0 |
| remark | text | 备注信息 | 可空 |
| verify_user_id | int64 | 审核人员ID | 可空 |
| verify_time | datetime | 审核时间 | 可空 |
| verify_remark | text | 审核备注 | 可空 |
| created_at | datetime | 创建时间 | 自动设置 |
| updated_at | datetime | 更新时间 | 自动更新 |

### 关键索引
- 主键索引: `id`
- 普通索引: `merchant_id`, `type`, `status`, `expire_date`

## 6. 数据关系图

```
+-------------------+       +-------------------+
|     Merchant      |       | Merchant Category |
+-------------------+       +-------------------+
| id                |       | id                |
| name              |       | name              |
| ...               |       | icon              |
| category_id       |------>| ...               |
| ...               |       | ...               |
+-------------------+       +-------------------+
        |
        |
        |       +-------------------+
        |------>| Merchant Document |
        |       +-------------------+
        |       | id                |
        |       | merchant_id       |
        |       | type              |
        |       | ...               |
        |       +-------------------+
        |
        |       +-------------------+
        |------>| Merchant Settlement |
        |       +-------------------+
        |       | id                |
        |       | merchant_id       |
        |       | type              |
        |       | ...               |
        |       +-------------------+
        |
        v
+-------------------+
|   Merchant Log    |
+-------------------+
| id                |
| merchant_id       |
| operation         |
| ...               |
+-------------------+
```

## 5. 状态常量定义

### 商家状态(Status)
```go
const (
    MerchantStatusPending  = 0 // 待审核
    MerchantStatusApproved = 1 // 已审核
    MerchantStatusRejected = 2 // 已拒绝
    MerchantStatusDisabled = 3 // 已禁用
    MerchantStatusLocked   = 4 // 已锁定
)
```

### 商家等级(Level)
```go
const (
    MerchantLevelNormal  = 0 // 普通商家
    MerchantLevelSilver  = 1 // 银牌商家
    MerchantLevelGold    = 2 // 金牌商家
    MerchantLevelDiamond = 3 // 钻石商家
)
```

### 商家经营状态(OperationStatus)
```go
const (
    MerchantOperationStatusClosed = 0 // 休息中
    MerchantOperationStatusOpen   = 1 // 营业中
)
```

## 6. 数据验证规则

商家模块中的关键数据验证规则包括：

1. **用户名**：长度5-50个字符，只能包含字母、数字、下划线，必须唯一
2. **密码**：长度8-20个字符，需包含字母和数字
3. **手机号**：符合中国大陆手机号格式
4. **邮箱**：符合标准邮箱格式
5. **商家名称**：长度2-100个字符
6. **余额**：不能为负值

## 7. 事务处理

以下操作需要使用事务保证数据一致性：

1. 商家创建
2. 商家余额变更
3. 商家状态变更
4. 商家信息批量更新
