/**
 * 商家营业统计DTO
 *
 * 该文件定义了商家营业统计相关的数据传输对象，用于服务层与控制层之间的数据交换。
 * 包括营业统计请求及响应数据结构。
 */

package dto

// BusinessStatsRequest 商家营业统计请求
type BusinessStatsRequest struct {
	MerchantID int64  `json:"merchant_id"` // 商家ID
	Period     string `json:"period"`      // 统计周期，如 7d（7天）
}

// BusinessDailyStat 每日营业统计数据
type BusinessDailyStat struct {
	Date         string  `json:"date"`           // 日期，格式：YYYY-MM-DD
	Duration     float64 `json:"duration"`       // 当日营业时长（小时）
	OpenTimes    int     `json:"open_times"`     // 当日开店次数
}

// BusinessStatsResponse 商家营业统计响应
type BusinessStatsResponse struct {
	TotalDuration        float64              `json:"total_duration"`         // 总营业时长（小时）
	AverageDuration      float64              `json:"average_duration"`       // 日均营业时长（小时）
	TotalOpenTimes       int                  `json:"total_open_times"`       // 总开店次数
	CompleteOpenTimes    int                  `json:"complete_open_times"`    // 完整营业次数（有开始和结束记录）
	IncompleteOpenTimes  int                  `json:"incomplete_open_times"`  // 不完整营业次数（只有开始没有结束记录）
	AverageOpenTimePerDay float64             `json:"average_open_time_per_day"` // 每天平均开店次数
	Period               string               `json:"period"`                // 统计周期
	DaysCount            int                  `json:"days_count"`            // 统计天数
	DailyStats           []*BusinessDailyStat `json:"daily_stats"`           // 每日营业统计详情
}
