/**
 * 商家证件和协议DTO
 *
 * 该文件定义了商家证件和协议相关的请求和响应数据结构，用于前后端数据交互。
 * 包括上传、查询、审核证件等操作的数据结构。
 */

package dto

import (
	"time"
)

// DocumentTypeDesc 证件类型描述
var DocumentTypeDesc = map[int]string{
	1: "法人身份证",
	2: "营业执照",
	3: "合作协议",
	4: "食品经营许可证",
	9: "其他证件",
}

// UploadDocumentRequest 上传证件请求
type UploadDocumentRequest struct {
	MerchantID  int64     `json:"merchant_id" valid:"Required" description:"商家ID"`                                 // 商家ID
	Type        int       `json:"type" valid:"Required" description:"证件类型:1-法人身份证,2-营业执照,3-合作协议,4-食品经营许可证,9-其他"` // 证件类型
	Name        string    `json:"name" valid:"Required" description:"证件名称"`                                     // 证件名称
	Number      string    `json:"number" description:"证件编号"`                                                   // 证件编号
	FrontImage  string    `json:"front_image" valid:"Required" description:"证件正面图片URL"`                        // 证件正面图片URL
	BackImage   string    `json:"back_image" description:"证件背面图片URL"`                                           // 证件背面图片URL(可选)
	ValidFrom   time.Time `json:"valid_from" description:"有效期开始日期"`                                              // 有效期开始日期
	ValidTo     time.Time `json:"valid_to" description:"有效期结束日期"`                                               // 有效期结束日期
	IsLongTerm  int       `json:"is_long_term" valid:"Range(0,1)" description:"是否长期有效:0-否,1-是"`                  // 是否长期有效
	Remark      string    `json:"remark" description:"备注"`                                                     // 备注
}

// UpdateDocumentRequest 更新证件请求
type UpdateDocumentRequest struct {
	ID          int64     `json:"id" valid:"Required" description:"证件ID"`                                      // 证件ID
	Type        int       `json:"type" valid:"Required" description:"证件类型:1-法人身份证,2-营业执照,3-合作协议,4-食品经营许可证,9-其他"` // 证件类型
	Name        string    `json:"name" valid:"Required" description:"证件名称"`                                     // 证件名称
	Number      string    `json:"number" description:"证件编号"`                                                   // 证件编号
	FrontImage  string    `json:"front_image" valid:"Required" description:"证件正面图片URL"`                        // 证件正面图片URL
	BackImage   string    `json:"back_image" description:"证件背面图片URL"`                                           // 证件背面图片URL(可选)
	ValidFrom   time.Time `json:"valid_from" description:"有效期开始日期"`                                              // 有效期开始日期
	ValidTo     time.Time `json:"valid_to" description:"有效期结束日期"`                                               // 有效期结束日期
	IsLongTerm  int       `json:"is_long_term" valid:"Range(0,1)" description:"是否长期有效:0-否,1-是"`                  // 是否长期有效
	Remark      string    `json:"remark" description:"备注"`                                                     // 备注
}

// VerifyDocumentRequest 审核证件请求
type VerifyDocumentRequest struct {
	ID          int64  `json:"id" valid:"Required" description:"证件ID"`            // 证件ID
	Status      int    `json:"status" valid:"Required;Range(0,1)" description:"状态:0-无效,1-有效"` // 状态
	VerifyRemark string `json:"verify_remark" description:"审核备注"`                  // 审核备注
}

// DocumentQueryRequest 证件查询请求
type DocumentQueryRequest struct {
	MerchantID int64 `json:"merchant_id" form:"merchant_id" valid:"Required" description:"商家ID"` // 商家ID
	Type       int   `json:"type" form:"type" description:"证件类型，0表示查询所有类型"`                      // 证件类型，0表示查询所有类型
}

// DocumentResponse 证件响应
type DocumentResponse struct {
	ID           int64     `json:"id" description:"证件ID"`                                             // 证件ID
	MerchantID   int64     `json:"merchant_id" description:"商家ID"`                                    // 商家ID
	Type         int       `json:"type" description:"证件类型:1-法人身份证,2-营业执照,3-合作协议,4-食品经营许可证,9-其他"`         // 证件类型
	TypeDesc     string    `json:"type_desc" description:"证件类型描述"`                                    // 证件类型描述
	Name         string    `json:"name" description:"证件名称"`                                           // 证件名称
	Number       string    `json:"number" description:"证件编号"`                                         // 证件编号
	FrontImage   string    `json:"front_image" description:"证件正面图片URL"`                              // 证件正面图片URL
	BackImage    string    `json:"back_image" description:"证件背面图片URL"`                                // 证件背面图片URL(可选)
	ValidFrom    time.Time `json:"valid_from" description:"有效期开始日期"`                                   // 有效期开始日期
	ValidTo      time.Time `json:"valid_to" description:"有效期结束日期"`                                    // 有效期结束日期
	IsLongTerm   int       `json:"is_long_term" description:"是否长期有效:0-否,1-是"`                         // 是否长期有效
	Status       int       `json:"status" description:"状态:0-无效,1-有效"`                                // 状态
	StatusDesc   string    `json:"status_desc" description:"状态描述"`                                   // 状态描述
	UploadedAt   time.Time `json:"uploaded_at" description:"上传时间"`                                   // 上传时间
	VerifiedAt   time.Time `json:"verified_at" description:"审核时间"`                                   // 审核时间
	VerifiedBy   int64     `json:"verified_by" description:"审核人ID"`                                  // 审核人ID
	VerifyRemark string    `json:"verify_remark" description:"审核备注"`                                 // 审核备注
	Remark       string    `json:"remark" description:"备注"`                                          // 备注
	CreatedAt    time.Time `json:"created_at" description:"创建时间"`                                    // 创建时间
	UpdatedAt    time.Time `json:"updated_at" description:"更新时间"`                                    // 更新时间
	IsExpired    bool      `json:"is_expired" description:"是否已过期"`                                   // 是否已过期
	ExpiryDays   int       `json:"expiry_days" description:"距离过期天数，负数表示已过期天数"`                       // 距离过期天数，负数表示已过期天数
}
