/**
 * 商家数据传输对象
 *
 * 该文件定义了商家相关的请求和响应数据结构，用于前后端数据交互。
 * 包括商家登录、注册、查询、更新等操作的数据结构。
 */

package dto

import (
	"time"
)

// MerchantLoginRequest 商家登录请求
type MerchantLoginRequest struct {
	Username string `json:"username" valid:"Required" description:"登录用户名"` // 用户名
	Password string `json:"password" valid:"Required" description:"登录密码"`  // 密码
}

// SendVerificationCodeRequest 发送验证码请求
type SendVerificationCodeRequest struct {
	Mobile string `json:"mobile" valid:"Required;Mobile" description:"手机号"` // 手机号
}

// SendVerificationCodeResponse 发送验证码响应
type SendVerificationCodeResponse struct {
	ExpireTime int `json:"expire_time" description:"验证码有效期（分钟）"` // 验证码有效期（分钟）
}

// MerchantVerifyCodeLoginRequest 商家验证码登录请求
type MerchantVerifyCodeLoginRequest struct {
	Mobile string `json:"mobile" valid:"Required;Mobile" description:"手机号"` // 手机号
	Code   string `json:"code" valid:"Required" description:"验证码"`      // 验证码
}

// MerchantMobileLoginRequest 商家手机号登录请求
type MerchantMobileLoginRequest struct {
	Mobile   string `json:"mobile" valid:"Required;Mobile" description:"登录手机号"` // 手机号
	Password string `json:"password" valid:"Required" description:"登录密码"`  // 密码
}

// MerchantLoginResponse 商家登录响应
type MerchantLoginResponse struct {
	TokenInfo    TokenResponse    `json:"token_info,omitempty" description:"Token信息"` // Token信息
	Merchant     MerchantResponse `json:"merchant" description:"商家信息"`      // 商家信息
	PendingAudit bool            `json:"pending_audit" description:"是否处于审核中状态"` // 是否处于审核中状态
}

// TokenResponse Token响应结构
type TokenResponse struct {
	AccessToken  string `json:"access_token" valid:"Required" description:"访问令牌"`
	RefreshToken string `json:"refresh_token" valid:"Required" description:"刷新令牌"`
	ExpiresIn    int64  `json:"expires_in" valid:"Required" description:"过期时间（秒）"`        // 过期时间（秒）
	TokenType    string `json:"token_type" valid:"Required" description:"令牌类型，固定为Bearer"` // 令牌类型，固定为Bearer
}

// CreateMerchantRequest 创建商家请求
type CreateMerchantRequest struct {
	Name            string  `json:"name" valid:"Required" description:"商家名称"`             // 商家名称
	Logo            string  `json:"logo" description:"商家Logo"`                            // 商家Logo
	Description     string  `json:"description" valid:"Required" description:"商家描述"`      // 商家描述
	CategoryID      int64   `json:"category_id" valid:"Required" description:"商家分类ID"`     // 商家分类ID
	Username        string  `json:"username" valid:"Required" description:"登录用户名"`        // 登录用户名
	Password        string  `json:"password" valid:"Required" description:"登录密码"`         // 登录密码
	ContactName     string  `json:"contact_name" valid:"Required" description:"联系人姓名"`    // 联系人姓名
	ContactMobile   string  `json:"contact_mobile" valid:"Required" description:"联系人手机号"` // 联系人手机号
	ContactEmail    string  `json:"contact_email" valid:"Required" description:"联系人电子邮箱"` // 联系人电子邮箱
	BusinessLicense string  `json:"business_license" valid:"Required" description:"营业执照"` // 营业执照
	Address         string  `json:"address" valid:"Required" description:"商家地址"`          // 商家地址
	Longitude       float64 `json:"longitude" description:"经度"`                            // 经度 GCJ02坐标系
	Latitude        float64 `json:"latitude" description:"纬度"`                             // 纬度 GCJ02坐标系
	IsRecommended   int     `json:"is_recommended" description:"是否推荐:0-否,1-是"`           // 是否推荐
}

// UpdateMerchantRequest 更新商家请求
type UpdateMerchantRequest struct {
	ID              int64   `json:"id" valid:"Required" description:"商家ID"`                 // 商家ID
	Name            string  `json:"name" valid:"Required" description:"商家名称"`             // 商家名称
	Logo            string  `json:"logo" description:"商家Logo"`                            // 商家Logo
	Description     string  `json:"description" description:"商家描述"`                       // 商家描述
	ContactName     string  `json:"contact_name" valid:"Required" description:"联系人姓名"`    // 联系人姓名
	ContactMobile   string  `json:"contact_mobile" valid:"Required" description:"联系人手机号"` // 联系人手机号
	ContactEmail    string  `json:"contact_email" description:"联系人电子邮箱"` // 联系人电子邮箱
	BusinessLicense string  `json:"business_license" description:"营业执照"`                  // 营业执照
	Address         string  `json:"address" valid:"Required" description:"商家地址"`          // 商家地址
	Longitude       float64 `json:"longitude" description:"经度"`                            // 经度 GCJ02坐标系
	Latitude        float64 `json:"latitude" description:"纬度"`                             // 纬度 GCJ02坐标系
	IsRecommended   int     `json:"is_recommended" description:"是否推荐:0-否,1-是"`           // 是否推荐
}

// MerchantResponse 商家响应
type MerchantResponse struct {
	ID              int64     `json:"id" description:"商家ID"`                                           // 商家ID
	Name            string    `json:"name" description:"商家名称"`                                         // 商家名称
	Logo            string    `json:"logo" description:"商家Logo"`                                       // 商家Logo
	Description     string    `json:"description" description:"商家描述"`                                  // 商家描述
	CategoryID      int64     `json:"category_id" description:"商家分类ID"`                                  // 商家分类ID
	Username        string    `json:"username" description:"登录用户名"`                                    // 登录用户名
	ContactName     string    `json:"contact_name" description:"联系人姓名"`                                // 联系人姓名
	ContactMobile   string    `json:"contact_mobile" description:"联系人手机号"`                             // 联系人手机号
	ContactEmail    string    `json:"contact_email" description:"联系人电子邮箱"`                             // 联系人电子邮箱
	BusinessLicense string    `json:"business_license" description:"营业执照"`                             // 营业执照
	Address         string    `json:"address" description:"商家地址"`                                      // 商家地址
	Longitude       float64   `json:"longitude" description:"经度"`                                       // 经度 GCJ02坐标系
	Latitude        float64   `json:"latitude" description:"纬度"`                                        // 纬度 GCJ02坐标系
	Level           int       `json:"level" description:"商家等级 0-普通,1-银牌,2-金牌,3-钻石"`                    // 商家等级
	Balance         float64   `json:"balance" description:"账户余额"`                                      // 账户余额
	AuditStatus     int       `json:"audit_status" description:"商家审核状态 0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 商家审核状态
	Status          int       `json:"status" description:"商家状态 0-禁用,1-正常,2-锁定"`                        // 商家状态
	OperationStatus int       `json:"operation_status" description:"商家经营状态 0-休息中,1-营业中"`                 // 经营状态
	BusinessHours   []BusinessHourResponse `json:"business_hours" description:"商家营业时间"`                            // 营业时间
	RejectReason    string    `json:"reject_reason" description:"拒绝原因"`                                // 拒绝原因
	LastLoginAt     time.Time `json:"last_login_at" description:"最后登录时间"`                              // 最后登录时间
	CreatedAt       time.Time `json:"created_at" description:"创建时间"`                                   // 创建时间
	IsRecommended   int       `json:"is_recommended" description:"是否推荐:0-否,1-是"`                       // 是否推荐
}

// MerchantQueryRequest 商家查询请求
type MerchantQueryRequest struct {
	Page        int    `json:"page" form:"page" description:"页码"`                                                   // 页码
	PageSize    int    `json:"pageSize" form:"pageSize" description:"每页数量"`                                         // 每页数量
	Name        string `json:"name" form:"name" description:"商家名称"`                                                 // 商家名称
	Status      int    `json:"status" form:"status" description:"商家状态 0-禁用,1-正常,2-锁定"`                              // 商家状态
	AuditStatus int    `json:"audit_status" form:"audit_status" description:"商家审核状态 0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 商家审核状态
	Level       int    `json:"level" form:"level" description:"商家等级 0-普通,1-银牌,2-金牌,3-钻石"`                           // 商家等级
	IsRecommended int   `json:"is_recommended" form:"is_recommended" description:"是否推荐 0-否,1-是"`                 // 是否推荐
	OperationStatus int `json:"operation_status" form:"operation_status" description:"商家经营状态 0-休息中,1-营业中"` // 商家经营状态
	
	// 地理位置查询相关参数
	MinLongitude float64 `json:"min_longitude" form:"min_longitude" description:"最小经度范围"` // 最小经度范围
	MaxLongitude float64 `json:"max_longitude" form:"max_longitude" description:"最大经度范围"` // 最大经度范围
	MinLatitude  float64 `json:"min_latitude" form:"min_latitude" description:"最小纬度范围"`   // 最小纬度范围
	MaxLatitude  float64 `json:"max_latitude" form:"max_latitude" description:"最大纬度范围"`   // 最大纬度范围 
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" valid:"Required" description:"原密码"` // 原密码
	NewPassword string `json:"new_password" valid:"Required" description:"新密码"` // 新密码
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	ID          int64  `json:"id" valid:"Required" description:"商家ID"`          // 商家ID
	NewPassword string `json:"new_password" valid:"Required" description:"新密码"` // 新密码
}

// AuditMerchantRequest 审核商家请求
type AuditMerchantRequest struct {
	ID           int64  `json:"id" valid:"Required"`                                                              // 商家ID
	AuditStatus  int    `json:"audit_status" valid:"Required" description:"商家审核状态 0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定"` // 审核状态
	RejectReason string `json:"reject_reason"`                                                                    // 拒绝原因
}

// UpdateBalanceRequest 更新余额请求
type UpdateBalanceRequest struct {
	ID     int64   `json:"id" valid:"Required" description:"商家ID"`                // 商家ID
	Amount float64 `json:"amount" valid:"Required" description:"变动金额（正数增加，负数减少）"` // 变动金额（正数增加，负数减少）
	Remark string  `json:"remark" valid:"Required" description:"变动说明"`            // 变动说明
}

// BusinessHourResponse 商家营业时间响应
type BusinessHourResponse struct {
	Weekday   int    `json:"weekday" description:"星期几：0-周日，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六"`   // 星期几
	StartTime string `json:"startTime" description:"开始营业时间，格式：HH:MM"` // 开始营业时间
	EndTime   string `json:"endTime" description:"结束营业时间，格式：HH:MM"`   // 结束营业时间
}

// UpdateOperationStatusRequest 更新商家经营状态请求
type UpdateOperationStatusRequest struct {
	OperationStatus int `json:"operation_status" valid:"Range(0,1)" description:"经营状态：0-休息中,1-营业中"` // 经营状态
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" valid:"Required"`
}
