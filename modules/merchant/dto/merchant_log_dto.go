/**
 * 商家日志DTO
 *
 * 该文件定义了商家日志相关的数据传输对象，用于服务层与控制层之间的数据交换。
 * 包括日志创建请求、查询请求以及响应数据结构等。
 */

package dto

import (
	"time"
)

// MerchantLogCreateRequest 商家日志创建请求
type MerchantLogCreateRequest struct {
	MerchantID    int64  `json:"merchant_id"`     // 商家ID
	OperatorID    int64  `json:"operator_id"`     // 操作人ID
	OperatorType  int    `json:"operator_type"`   // 操作人类型:1商家,2管理员
	OperationType int    `json:"operation_type"`  // 操作类型
	Content       string `json:"content"`         // 操作内容
	RequestURL    string `json:"request_url"`     // 请求URL
	RequestData   string `json:"request_data"`    // 请求数据（JSON格式）
	IP            string `json:"ip"`              // 操作IP
	UserAgent     string `json:"user_agent"`      // 用户代理
}

// MerchantLogQueryRequest 商家日志查询请求
type MerchantLogQueryRequest struct {
	MerchantID    int64     `json:"merchant_id"`    // 商家ID
	OperatorID    int64     `json:"operator_id"`    // 操作人ID
	OperatorType  int       `json:"operator_type"`  // 操作人类型
	OperationType int       `json:"operation_type"` // 操作类型
	IP            string    `json:"ip"`             // 操作IP
	StartTime     time.Time `json:"start_time"`     // 开始时间
	EndTime       time.Time `json:"end_time"`       // 结束时间
	Page          int       `json:"page"`           // 页码
	PageSize      int       `json:"pageSize"`       // 每页数量
}

// MerchantLogResponse 商家日志响应
type MerchantLogResponse struct {
	ID            int64     `json:"id"`             // 日志ID
	MerchantID    int64     `json:"merchant_id"`    // 商家ID
	OperatorID    int64     `json:"operator_id"`    // 操作人ID
	OperatorType  int       `json:"operator_type"`  // 操作人类型
	OperationName string    `json:"operation_name"` // 操作类型名称
	OperationType int       `json:"operation_type"` // 操作类型
	Content       string    `json:"content"`        // 操作内容
	RequestURL    string    `json:"request_url"`    // 请求URL
	RequestData   string    `json:"request_data"`   // 请求数据（JSON格式）
	IP            string    `json:"ip"`             // 操作IP
	UserAgent     string    `json:"user_agent"`     // 用户代理
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
}

// MerchantLogListResponse 商家日志列表响应
type MerchantLogListResponse struct {
	Total int64                 `json:"total"` // 总数
	List  []*MerchantLogResponse `json:"list"`  // 列表
}
