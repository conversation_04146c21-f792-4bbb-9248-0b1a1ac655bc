/**
 * 商家结算方式DTO
 *
 * 该文件定义了商家结算方式相关的请求和响应数据结构，用于前后端数据交互。
 * 包括创建、查询、更新结算方式等操作的数据结构。
 */

package dto

import (
	"time"
)

// SettlementTypeDesc 结算方式类型描述
var SettlementTypeDesc = map[int]string{
	1: "银行卡",
	2: "微信",
	3: "支付宝",
}

// CreateSettlementRequest 创建结算方式请求
type CreateSettlementRequest struct {
	MerchantID     int64   `json:"merchant_id" valid:"Required" description:"商家ID"`                             // 商家ID
	Type           int     `json:"type" valid:"Required;Range(1,3)" description:"结算方式类型:1-银行卡,2-微信,3-支付宝"`     // 结算方式类型
	AccountName    string  `json:"account_name" valid:"Required" description:"账户名称"`                         // 账户名称
	AccountNumber  string  `json:"account_number" valid:"Required" description:"账号"`                         // 账号
	BankName       string  `json:"bank_name" description:"银行名称,仅银行卡类型需要"`                                      // 银行名称(仅银行卡类型需要)
	BankBranch     string  `json:"bank_branch" description:"开户支行,仅银行卡类型需要"`                                    // 开户支行(仅银行卡类型需要)
	IsDefault      int     `json:"is_default" valid:"Range(0,1)" description:"是否默认:0-否,1-是"`                  // 是否默认
	SettlementRate float64 `json:"settlement_rate" description:"结算费率%" valid:"Range(0,100)"`                 // 结算费率(%)
	Remark         string  `json:"remark" description:"备注"`                                                 // 备注
}

// UpdateSettlementRequest 更新结算方式请求
type UpdateSettlementRequest struct {
	ID             int64   `json:"id" valid:"Required" description:"结算方式ID"`                                 // 结算方式ID
	Type           int     `json:"type" valid:"Required;Range(1,3)" description:"结算方式类型:1-银行卡,2-微信,3-支付宝"`     // 结算方式类型
	AccountName    string  `json:"account_name" valid:"Required" description:"账户名称"`                         // 账户名称
	AccountNumber  string  `json:"account_number" valid:"Required" description:"账号"`                         // 账号
	BankName       string  `json:"bank_name" description:"银行名称,仅银行卡类型需要"`                                      // 银行名称(仅银行卡类型需要)
	BankBranch     string  `json:"bank_branch" description:"开户支行,仅银行卡类型需要"`                                    // 开户支行(仅银行卡类型需要)
	IsDefault      int     `json:"is_default" valid:"Range(0,1)" description:"是否默认:0-否,1-是"`                  // 是否默认
	Status         int     `json:"status" valid:"Range(0,1)" description:"状态:0-禁用,1-启用"`                      // 状态
	SettlementRate float64 `json:"settlement_rate" description:"结算费率%" valid:"Range(0,100)"`                 // 结算费率(%)
	Remark         string  `json:"remark" description:"备注"`                                                 // 备注
}

// SetDefaultSettlementRequest 设置默认结算方式请求
type SetDefaultSettlementRequest struct {
	MerchantID   int64 `json:"merchant_id" valid:"Required" description:"商家ID"`    // 商家ID
	SettlementID int64 `json:"settlement_id" valid:"Required" description:"结算方式ID"` // 结算方式ID
}

// SettlementResponse 结算方式响应
type SettlementResponse struct {
	ID             int64     `json:"id" description:"结算方式ID"`                               // 结算方式ID
	MerchantID     int64     `json:"merchant_id" description:"商家ID"`                        // 商家ID
	Type           int       `json:"type" description:"结算方式类型:1-银行卡,2-微信,3-支付宝"`              // 结算方式类型
	TypeDesc       string    `json:"type_desc" description:"结算方式类型描述"`                      // 结算方式类型描述
	AccountName    string    `json:"account_name" description:"账户名称"`                      // 账户名称
	AccountNumber  string    `json:"account_number" description:"账号"`                      // 账号
	BankName       string    `json:"bank_name" description:"银行名称,仅银行卡类型需要"`                 // 银行名称(仅银行卡类型需要)
	BankBranch     string    `json:"bank_branch" description:"开户支行,仅银行卡类型需要"`               // 开户支行(仅银行卡类型需要)
	Status         int       `json:"status" description:"状态:0-禁用,1-启用"`                     // 状态
	IsDefault      int       `json:"is_default" description:"是否默认:0-否,1-是"`                 // 是否默认
	SettlementRate float64   `json:"settlement_rate" description:"结算费率%"`                  // 结算费率(%)
	Remark         string    `json:"remark" description:"备注"`                             // 备注
	CreatedAt      time.Time `json:"created_at" description:"创建时间"`                       // 创建时间
	UpdatedAt      time.Time `json:"updated_at" description:"更新时间"`                       // 更新时间
}
