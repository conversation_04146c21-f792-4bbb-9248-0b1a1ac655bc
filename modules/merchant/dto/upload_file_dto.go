/**
 * 文件上传数据传输对象
 *
 * 该文件定义了商家模块中文件上传相关的请求和响应数据结构
 */

package dto

import (
	"time"
)

// UploadFileRequest 上传文件请求
type UploadFileRequest struct {
	FileUsage   string `json:"file_usage"` // 文件用途，如商品图片、店铺图片等
	IsAnonymous bool   `json:"is_anonymous"` // 是否匿名上传
}

// UploadFileResponse 上传文件响应
type UploadFileResponse struct {
	ID          int64     `json:"id"`           // 文件ID
	FileName    string    `json:"file_name"`    // 文件名
	FilePath    string    `json:"file_path"`    // 文件路径
	FileURL     string    `json:"file_url"`     // 文件URL
	FileSize    int64     `json:"file_size"`    // 文件大小
	FileType    string    `json:"file_type"`    // 文件类型
	FileExt     string    `json:"file_ext"`     // 文件扩展名
	FileUsage   string    `json:"file_usage"`   // 文件用途
	IsAnonymous bool      `json:"is_anonymous"` // 是否匿名上传
	Storage     string    `json:"storage"`      // 存储方式
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
}

// UploadFileQueryRequest 查询上传文件请求
type UploadFileQueryRequest struct {
	FileUsage   string     `json:"file_usage"`   // 文件用途
	UserType    string     `json:"user_type"`    // 用户类型
	UserID      int64      `json:"user_id"`      // 用户ID
	Username    string     `json:"username"`     // 用户名
	IsAnonymous *bool      `json:"is_anonymous"` // 是否匿名上传
	StartTime   time.Time  `json:"start_time"`   // 开始时间
	EndTime     time.Time  `json:"end_time"`     // 结束时间
	Page        int        `json:"page"`         // 页码
	PageSize    int        `json:"page_size"`    // 每页大小
}

// UploadFileListResponse 上传文件列表响应
type UploadFileListResponse struct {
	Total int64                `json:"total"` // 总数
	List  []*UploadFileResponse `json:"list"`  // 列表
}

// UploadConfigResponse 上传配置响应
type UploadConfigResponse struct {
	AllowAnonymous      bool     `json:"allow_anonymous"`       // 是否允许匿名上传
	MaxFileSize         int64    `json:"max_file_size"`         // 最大文件大小
	AllowedFileTypes    []string `json:"allowed_file_types"`    // 允许的文件类型
	StorageMode         string   `json:"storage_mode"`          // 存储模式
	UploadPath          string   `json:"upload_path"`           // 上传路径
	AllowedUsageTypes   []string `json:"allowed_usage_types"`   // 允许的用途类型
	AnonymousUsageTypes []string `json:"anonymous_usage_types"` // 允许匿名上传的用途类型
}
