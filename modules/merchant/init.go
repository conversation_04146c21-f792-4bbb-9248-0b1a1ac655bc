/**
 * merchant模块初始化
 *
 * 本文件负责merchant模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保merchant模块的功能正常启动。
 */

package merchant

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/routers"
)

// Init 初始化merchant模块
func Init() {
	logs.Info("初始化merchant模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.Init()

	logs.Info("merchant模块初始化完成")
}

// 注册ORM模型
func registerModels() {
	logs.Info("注册merchant模块ORM模型...")

	// 注册merchant相关模型
	orm.RegisterModel(
		new(models.Merchant),
		new(models.MerchantLog),
		new(models.MerchantCategory),
		new(models.MerchantSettlement),
		new(models.MerchantDocument),
	)
}
