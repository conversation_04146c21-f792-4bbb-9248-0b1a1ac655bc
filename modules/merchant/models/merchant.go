/**
 * 商家模型
 *
 * 该文件定义了商家模型，表示电商平台上的商家实体。
 * 包含商家的基本信息、联系方式、账户状态、营业时间等数据。
 */

package models

import (
	"encoding/json"
	"fmt"
	"time"
)

// 商家状态常量
const (
	MerchantStatusPending  = 0 // 待审核
	MerchantStatusApproved = 1 // 已审核
	MerchantStatusRejected = 2 // 已拒绝
	MerchantStatusDisabled = 3 // 已禁用
	MerchantStatusLocked   = 4 // 已锁定
)

// 商家等级常量
const (
	MerchantLevelNormal  = 0 // 普通商家
	MerchantLevelSilver  = 1 // 银牌商家
	MerchantLevelGold    = 2 // 金牌商家
	MerchantLevelDiamond = 3 // 钻石商家
)

// 商家经营状态常量
const (
	MerchantOperationStatusClosed = 0 // 休息中
	MerchantOperationStatusOpen   = 1 // 营业中
)

// BusinessHoursWithStatus 包含营业时间列表和营业状态的结构体
type BusinessHoursWithStatus struct {
	BusinessHours   []BusinessHour `json:"business_hours"`   // 营业时间列表
	OperationStatus int            `json:"operation_status"` // 商家营业状态:0-休息中,1-营业中
}

// BusinessHour 商家营业时间结构体
type BusinessHour struct {
	ID        string `json:"key"`       // 营业时间段的唯一标识符
	Weekday   int    `json:"weekday"`   // 星期几：0-周日，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六
	StartTime string `json:"startTime"` // 开始营业时间，格式：HH:MM
	EndTime   string `json:"endTime"`   // 结束营业时间，格式：HH:MM
}

// Key 生成营业时间段的唯一标识符
// 格式：weekday:startTime:endTime，例如 1:09:00:14:00
func (h BusinessHour) Key() string {
	return fmt.Sprintf("%d:%s:%s", h.Weekday, h.StartTime, h.EndTime)
}

// BusinessHourSettings 商家营业时间设置结构体
type BusinessHourSettings struct {
	Enabled           bool                      `json:"enabled"`             // 是否启用自动开关店
	OpenAheadMinutes  int                       `json:"open_ahead_minutes"`  // 提前开店时间（分钟）
	CloseDelayMinutes int                       `json:"close_delay_minutes"` // 延迟关店时间（分钟）
	Notifications     BusinessHourNotifications `json:"notifications"`       // 通知设置
	LastUpdated       time.Time                 `json:"last_updated"`        // 最后更新时间
}

// BusinessHourSettingsWithStatus 包含营业时间设置和营业状态的结构体
// type BusinessHourSettingsWithStatus struct {
// 	BusinessHourSettings
// 	OperationStatus int `json:"operation_status"` // 商家营业状态:0-休息中,1-营业中
// }

// BusinessHourNotifications 商家营业时间通知设置
type BusinessHourNotifications struct {
	OpenNotification         NotificationSetting `json:"open_notification"`          // 开店通知设置
	CloseNotification        NotificationSetting `json:"close_notification"`         // 关店通知设置
	StatusChangeNotification bool                `json:"status_change_notification"` // 营业状态变更通知
	NotificationMethods      []string            `json:"notification_methods"`       // 通知方式，如["sms", "app", "email"]
}

// NotificationSetting 通知设置
type NotificationSetting struct {
	Enabled      bool `json:"enabled"`       // 是否启用
	AheadMinutes int  `json:"ahead_minutes"` // 提前通知时间（分钟）
}

// BusinessHourStatus 商家营业状态结构体
type BusinessHourStatus struct {
	OperationStatus int       `json:"operation_status"` // 0-休息中, 1-营业中
	AutoRestore     bool      `json:"auto_restore"`     // 是否自动恢复
	RestoreTime     time.Time `json:"restore_time"`     // 恢复时间
	Remarks         string    `json:"remarks"`          // 备注说明
	LastUpdated     time.Time `json:"last_updated"`     // 最后更新时间
}

// 获取默认的营业时间设置
func getDefaultBusinessHourSettings() BusinessHourSettings {
	return BusinessHourSettings{
		Enabled:           true,
		OpenAheadMinutes:  1,
		CloseDelayMinutes: 1,
		Notifications: BusinessHourNotifications{
			OpenNotification: NotificationSetting{
				Enabled:      true,
				AheadMinutes: 30,
			},
			CloseNotification: NotificationSetting{
				Enabled:      true,
				AheadMinutes: 30,
			},
			StatusChangeNotification: true,
			NotificationMethods:      []string{"app"},
		},
		LastUpdated: time.Now(),
	}
}

// Merchant 商家模型
type Merchant struct {
	ID                   int64     `orm:"pk;auto;column(id);description(商家唯一标识)" json:"id"`                                                      // 商家ID
	Name                 string    `orm:"size(100);column(name);description(商家店铺名称)" json:"name"`                                                // 商家名称
	Logo                 string    `orm:"size(255);column(logo);description(商家店铺Logo图片URL)" json:"logo"`                                         // 商家Logo
	Description          string    `orm:"type(text);column(description);description(商家店铺详细介绍)" json:"description"`                               // 商家描述
	Username             string    `orm:"size(50);unique;column(username);description(商家登录账号)" json:"username"`                                  // 登录用户名
	Password             string    `orm:"size(255);column(password);description(商家登录密码)" json:"-"`                                               // 登录密码
	ContactName          string    `orm:"size(50);column(contact_name);description(商家联系人姓名)" json:"contact_name"`                                // 联系人姓名
	ContactMobile        string    `orm:"size(20);column(contact_mobile);description(商家联系人手机号)" json:"contact_mobile"`                           // 联系人手机号
	ContactEmail         string    `orm:"size(100);column(contact_email);description(商家联系人邮箱)" json:"contact_email"`                             // 联系人电子邮箱
	BusinessLicense      string    `orm:"size(255);column(business_license);description(营业执照图片URL)" json:"business_license"`                     // 营业执照
	Address              string    `orm:"size(255);column(address);description(商家店铺详细地址)" json:"address"`                                        // 商家地址
	Longitude            float64   `orm:"column(longitude);digits(10);decimals(7);null" json:"longitude" description:"经度"`                         // 经度 GCJ02坐标系 火星坐标系，WGS84坐标系加密后的坐标系；Google国内地图、高德、QQ地图 使用
	Latitude             float64   `orm:"column(latitude);digits(10);decimals(7);null" json:"latitude" description:"纬度"`                          // 纬度 GCJ02坐标系 火星坐标系，WGS84坐标系加密后的坐标系；Google国内地图、高德、QQ地图 使用
	CategoryID           int64     `orm:"column(category_id);description(商家分类ID)" json:"category_id"`                                            // 商家分类ID
	Level                int       `orm:"default(0);column(level);description(商家等级:0-普通,1-银牌,2-金牌,3-钻石)" json:"level"`                           // 商家等级
	Balance              float64   `orm:"digits(10);decimals(2);column(balance);description(商家账户余额)" json:"balance"`                             // 账户余额
	AuditStatus          int       `orm:"default(0);column(audit_status);description(商家审核状态:0-待审核,1-已审核,2-已拒绝,3-已禁用,4-已锁定)" json:"audit_status"` // 商家审核状态
	Status               int       `orm:"default(0);column(status);description(商家状态:0-禁用，1-正常,2-锁定)" json:"status"`                              // 商家状态
	OperationStatus      int       `orm:"default(1);column(operation_status);description(商家经营状态:0-休息中,1-营业中)" json:"operation_status"`           // 经营状态
	RejectReason         string    `orm:"type(text);column(reject_reason);description(商家审核拒绝原因)" json:"reject_reason"`                           // 拒绝原因
	LastLoginAt          time.Time `orm:"type(datetime);null;column(last_login_at);description(最近一次登录时间)" json:"last_login_at"`                  // 最后登录时间
	LastLoginIP          string    `orm:"size(50);column(last_login_ip);description(最近一次登录IP地址)" json:"last_login_ip"`                           // 最后登录IP
	CreatedAt            time.Time `orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)" json:"created_at"`                    // 创建时间
	UpdatedAt            time.Time `orm:"auto_now;type(datetime);column(updated_at);description(更新时间)" json:"updated_at"`                        // 更新时间
	IsRecommended        int       `orm:"default(0);column(is_recommended);description(是否推荐:0-否,1-是)" json:"is_recommended"`                 // 是否推荐
	BusinessHours        string    `orm:"type(text);null;column(business_hours);description(商家营业时间)" json:"business_hours"`                      // 营业时间
	BusinessHourSettings string    `orm:"type(text);null;column(business_hour_settings);description(商家营业时间设置)" json:"business_hour_settings"`    // 营业时间设置
	OperationStatusData  string    `orm:"type(text);null;column(operation_status_data);description(商家营业状态数据)" json:"operation_status_data"`      // 营业状态数据
}

// TableName 指定表名
func (m *Merchant) TableName() string {
	return "merchant"
}

// GetBusinessHours 获取商家营业时间列表
func (m *Merchant) GetBusinessHours() ([]BusinessHour, error) {
	if m.BusinessHours == "" {
		return []BusinessHour{}, nil
	}

	var hours []BusinessHour
	err := json.Unmarshal([]byte(m.BusinessHours), &hours)
	return hours, err
}

// SetBusinessHours 设置商家营业时间列表
func (m *Merchant) SetBusinessHours(hours []BusinessHour) error {
	if hours == nil {
		m.BusinessHours = ""
		return nil
	}

	data, err := json.Marshal(hours)
	if err != nil {
		return err
	}

	m.BusinessHours = string(data)
	return nil
}

// GetBusinessHourSettings 获取商家营业时间设置
func (m *Merchant) GetBusinessHourSettings() (BusinessHourSettings, error) {
	if m.BusinessHourSettings == "" {
		// 返回默认设置
		return getDefaultBusinessHourSettings(), nil
	}

	var settings BusinessHourSettings
	err := json.Unmarshal([]byte(m.BusinessHourSettings), &settings)
	if err != nil {
		return getDefaultBusinessHourSettings(), err
	}

	return settings, nil
}

// SetBusinessHourSettings 设置商家营业时间设置
func (m *Merchant) SetBusinessHourSettings(settings BusinessHourSettings) error {
	// 更新最后修改时间
	settings.LastUpdated = time.Now()

	data, err := json.Marshal(settings)
	if err != nil {
		return err
	}

	m.BusinessHourSettings = string(data)
	return nil
}

// GetOperationStatusData 获取商家营业状态数据
func (m *Merchant) GetOperationStatusData() (BusinessHourStatus, error) {
	if m.OperationStatusData == "" {
		// 返回默认状态，使用当前的OperationStatus字段
		return BusinessHourStatus{
			OperationStatus: m.OperationStatus,
			AutoRestore:     false,
			LastUpdated:     time.Now(),
		}, nil
	}

	var status BusinessHourStatus
	err := json.Unmarshal([]byte(m.OperationStatusData), &status)
	if err != nil {
		return BusinessHourStatus{
			OperationStatus: m.OperationStatus,
			AutoRestore:     false,
			LastUpdated:     time.Now(),
		}, err
	}

	return status, nil
}

// SetOperationStatusData 设置商家营业状态数据
func (m *Merchant) SetOperationStatusData(status BusinessHourStatus) error {
	// 同时更新OperationStatus字段以保持兼容性
	m.OperationStatus = status.OperationStatus

	// 更新最后修改时间
	status.LastUpdated = time.Now()

	data, err := json.Marshal(status)
	if err != nil {
		return err
	}

	m.OperationStatusData = string(data)
	return nil
}
