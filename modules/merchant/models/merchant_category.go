/**
 * 商家分类模型
 *
 * 该文件定义了商家分类模型，表示商家的经营类型分类。
 * 包含分类名称、图标、描述等信息，用于对商家按经营类型进行分类。
 */

package models

import (
	"time"
)

// MerchantCategory 商家分类模型
type MerchantCategory struct {
	ID          int64     `orm:"pk;auto;column(id);description(分类唯一标识)" json:"id"`                // 分类ID
	Name        string    `orm:"size(100);column(name);description(分类名称)" json:"name"`            // 分类名称
	Description string    `orm:"type(text);column(description);description(分类描述)" json:"description"` // 分类描述
	Icon        string    `orm:"size(255);column(icon);description(分类图标URL)" json:"icon"`        // 分类图标
	SortOrder   int       `orm:"default(0);column(sort_order);description(排序值)" json:"sort_order"` // 排序值，越小越靠前
	IsShow      bool      `orm:"default(true);column(is_show);description(是否显示)" json:"is_show"`  // 是否显示
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)" json:"created_at"` // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);column(updated_at);description(更新时间)" json:"updated_at"`    // 更新时间
}

// TableName 指定表名
func (m *MerchantCategory) TableName() string {
	return "merchant_category"
}
