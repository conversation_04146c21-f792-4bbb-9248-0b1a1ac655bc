/**
 * 商家证件和协议模型
 *
 * 该文件定义了商家证件和协议模型，用于存储商家的各类证件和与平台签署的协议信息。
 * 包括身份证、营业执照、合作协议等多种类型的文档。
 */

package models

import (
	"time"
)

// 证件类型常量
const (
	DocumentTypeIDCard          = 1 // 法人身份证
	DocumentTypeBusinessLicense = 2 // 营业执照
	DocumentTypeAgreement       = 3 // 合作协议
	DocumentTypeFoodLicense     = 4 // 食品经营许可证
	DocumentTypeOther           = 9 // 其他证件
)

// MerchantDocument 商家证件和协议模型
type MerchantDocument struct {
	ID           int64     `orm:"pk;auto;column(id);description(证件ID)" json:"id"`                                   // 证件ID
	MerchantID   int64     `orm:"column(merchant_id);description(商家ID)" json:"merchant_id"`                         // 商家ID
	Type         int       `orm:"column(type);description(证件类型:1-法人身份证,2-营业执照,3-合作协议,4-食品经营许可证,9-其他)" json:"type"`  // 证件类型
	Name         string    `orm:"size(100);column(name);description(证件名称)" json:"name"`                            // 证件名称
	Number       string    `orm:"size(100);column(number);description(证件编号)" json:"number"`                        // 证件编号
	FrontImage   string    `orm:"size(255);column(front_image);description(证件正面图片URL)" json:"front_image"`        // 证件正面图片URL
	BackImage    string    `orm:"size(255);null;column(back_image);description(证件背面图片URL)" json:"back_image"`      // 证件背面图片URL(可选)
	ValidFrom    time.Time `orm:"type(date);null;column(valid_from);description(有效期开始日期)" json:"valid_from"`        // 有效期开始日期
	ValidTo      time.Time `orm:"type(date);null;column(valid_to);description(有效期结束日期)" json:"valid_to"`            // 有效期结束日期
	IsLongTerm   int       `orm:"default(0);column(is_long_term);description(是否长期有效:0-否,1-是)" json:"is_long_term"` // 是否长期有效
	Status       int       `orm:"default(1);column(status);description(状态:0-无效,1-有效)" json:"status"`               // 状态
	UploadedAt   time.Time `orm:"type(datetime);column(uploaded_at);description(上传时间)" json:"uploaded_at"`          // 上传时间
	VerifiedAt   time.Time `orm:"type(datetime);null;column(verified_at);description(审核时间)" json:"verified_at"`      // 审核时间
	VerifiedBy   int64     `orm:"null;column(verified_by);description(审核人ID)" json:"verified_by"`                   // 审核人ID
	VerifyRemark string    `orm:"size(255);null;column(verify_remark);description(审核备注)" json:"verify_remark"`      // 审核备注
	Remark       string    `orm:"size(255);null;column(remark);description(备注)" json:"remark"`                      // 备注
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)" json:"created_at"` // 创建时间
	UpdatedAt    time.Time `orm:"auto_now;type(datetime);column(updated_at);description(更新时间)" json:"updated_at"`     // 更新时间
}

// TableName 指定表名
func (m *MerchantDocument) TableName() string {
	return "merchant_document"
}
