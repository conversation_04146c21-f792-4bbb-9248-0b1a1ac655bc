/**
 * 商家日志模型
 * 
 * 该文件定义了商家日志模型，用于记录商家相关的操作日志。
 * 包含操作类型、操作内容、操作时间等信息。
 */

package models

import (
	"time"
)

// 商家日志操作类型常量
const (
	MerchantLogTypeLogin       = 1 // 登录
	MerchantLogTypeUpdate      = 2 // 更新信息
	MerchantLogTypePassword    = 3 // 修改密码
	MerchantLogTypeAudit       = 4 // 审核
	MerchantLogTypeBalance     = 5 // 余额变更
	MerchantLogTypeStatusChange = 6 // 状态变更
	MerchantLogTypeOpenBusiness = 7 // 开始营业
	MerchantLogTypeCloseBusiness = 8 // 结束营业
)

// MerchantLog 商家日志模型
type MerchantLog struct {
	ID           int64     `orm:"pk;auto;column(id);description(日志唯一标识)" json:"id"`                // 日志ID
	MerchantID   int64     `orm:"column(merchant_id);description(关联的商家ID)" json:"merchant_id"`      // 商家ID
	OperatorID   int64     `orm:"column(operator_id);description(操作人ID)" json:"operator_id"`       // 操作人ID
	OperatorType int       `orm:"column(operator_type);description(操作人类型:1商家,2管理员)" json:"operator_type"` // 操作人类型
	OperationType int      `orm:"column(operation_type);description(操作类型)" json:"operation_type"` // 操作类型
	Content      string    `orm:"type(text);column(content);description(操作内容)" json:"content"`     // 操作内容
	RequestURL   string    `orm:"size(255);column(request_url);description(请求URL)" json:"request_url"`  // 请求URL
	RequestData  string    `orm:"type(text);column(request_data);description(请求数据)" json:"request_data"` // 请求数据（JSON格式）
	IP           string    `orm:"size(50);column(ip);description(操作IP地址)" json:"ip"`              // 操作IP
	UserAgent    string    `orm:"size(255);column(user_agent);description(用户代理)" json:"user_agent"`  // 用户代理
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)" json:"created_at"` // 创建时间
}

// TableName 指定表名
func (l *MerchantLog) TableName() string {
	return "merchant_log"
}