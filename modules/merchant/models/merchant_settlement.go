/**
 * 商家结算方式模型
 *
 * 该文件定义了商家结算方式模型，用于存储商家的结算账户信息。
 * 支持多种结算方式，如银行卡、微信、支付宝等。
 */

package models

import (
	"time"
)

// 结算方式类型常量
const (
	SettlementTypeBankCard = 1 // 银行卡
	SettlementTypeWechat   = 2 // 微信
	SettlementTypeAlipay   = 3 // 支付宝
)

// 结算状态常量
const (
	SettlementStatusDisabled = 0 // 禁用
	SettlementStatusEnabled  = 1 // 启用
)

// MerchantSettlement 商家结算方式模型
type MerchantSettlement struct {
	ID             int64     `orm:"pk;auto;column(id);description(结算方式ID)" json:"id"`                                    // 结算方式ID
	MerchantID     int64     `orm:"column(merchant_id);description(商家ID)" json:"merchant_id"`                             // 商家ID
	Type           int       `orm:"column(type);description(结算方式类型:1-银行卡,2-微信,3-支付宝)" json:"type"`                        // 结算方式类型
	AccountName    string    `orm:"size(100);column(account_name);description(账户名称)" json:"account_name"`                // 账户名称
	AccountNumber  string    `orm:"size(100);column(account_number);description(账号)" json:"account_number"`              // 账号
	BankName       string    `orm:"size(100);column(bank_name);description(银行名称,仅银行卡类型需要)" json:"bank_name"`               // 银行名称(仅银行卡类型需要)
	BankBranch     string    `orm:"size(255);column(bank_branch);description(开户支行,仅银行卡类型需要)" json:"bank_branch"`           // 开户支行(仅银行卡类型需要)
	Status         int       `orm:"default(1);column(status);description(状态:0-禁用,1-启用)" json:"status"`                    // 状态
	IsDefault      int       `orm:"default(0);column(is_default);description(是否默认:0-否,1-是)" json:"is_default"`            // 是否默认
	SettlementRate float64   `orm:"digits(5);decimals(2);column(settlement_rate);description(结算费率%)" json:"settlement_rate"` // 结算费率(%)
	Remark         string    `orm:"size(255);column(remark);description(备注)" json:"remark"`                              // 备注
	CreatedAt      time.Time `orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)" json:"created_at"`   // 创建时间
	UpdatedAt      time.Time `orm:"auto_now;type(datetime);column(updated_at);description(更新时间)" json:"updated_at"`       // 更新时间
}

// TableName 指定表名
func (m *MerchantSettlement) TableName() string {
	return "merchant_settlement"
}
