/**
 * 商家仓库接口定义
 *
 * 该文件定义了商家数据访问层的接口，声明了商家数据的增删改查方法。
 * 通过接口定义与实现分离，便于测试和扩展。
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/merchant/models"
)

// MerchantRepository 商家仓库接口
type MerchantRepository interface {
	// Create 创建商家
	Create(ctx context.Context, merchant *models.Merchant) (int64, error)

	// Update 更新商家信息
	Update(ctx context.Context, merchant *models.Merchant) error

	// Delete 删除商家
	Delete(ctx context.Context, id int64) error

	// GetByID 根据ID获取商家
	GetByID(ctx context.Context, id int64) (*models.Merchant, error)

	// GetByUsername 根据用户名获取商家
	GetByUsername(ctx context.Context, username string) (*models.Merchant, error)

	// GetByMobile 根据手机号获取商家
	GetByMobile(ctx context.Context, mobile string) (*models.Merchant, error)

	// List 获取商家列表
	List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.Merchant, int64, error)

	// UpdatePassword 更新商家密码
	UpdatePassword(ctx context.Context, id int64, password string) error

	// UpdateStatus 更新商家状态
	UpdateStatus(ctx context.Context, id int64, status int, rejectReason string) error

	// UpdateBalance 更新商家余额
	UpdateBalance(ctx context.Context, id int64, amount float64) error

	// UpdateLoginInfo 更新商家登录信息
	UpdateLoginInfo(ctx context.Context, id int64, ip string) error

	// FindByStatus 根据状态查询商家
	FindByStatus(ctx context.Context, status int) ([]*models.Merchant, error)
}
