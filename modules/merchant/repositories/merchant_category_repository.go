/**
 * 商家分类仓库
 *
 * 该文件定义了商家分类仓库接口和实现，提供商家分类的数据库操作功能。
 * 实现了商家分类的增删改查等数据库操作。
 */

package repositories

import (
	"context"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
)

// MerchantCategoryRepository 商家分类仓库接口
type MerchantCategoryRepository interface {
	// Create 创建商家分类
	Create(ctx context.Context, category *models.MerchantCategory) (int64, error)
	
	// Update 更新商家分类
	Update(ctx context.Context, category *models.MerchantCategory) error
	
	// Delete 删除商家分类
	Delete(ctx context.Context, id int64) error
	
	// GetByID 根据ID获取商家分类
	GetByID(ctx context.Context, id int64) (*models.MerchantCategory, error)
	
	// List 获取商家分类列表
	List(ctx context.Context, params *dto.MerchantCategoryQueryParams, page, pageSize int) ([]*models.MerchantCategory, int64, error)

	// GetAll 获取所有商家分类（不分页）
	GetAll(ctx context.Context, onlyShow bool) ([]*models.MerchantCategory, error)
}

// MerchantCategoryRepositoryImpl 商家分类仓库实现
type MerchantCategoryRepositoryImpl struct{}

// NewMerchantCategoryRepository 创建商家分类仓库实例
func NewMerchantCategoryRepository() MerchantCategoryRepository {
	return &MerchantCategoryRepositoryImpl{}
}

// Create 创建商家分类
// 将商家分类信息存入数据库并返回分类ID
func (r *MerchantCategoryRepositoryImpl) Create(ctx context.Context, category *models.MerchantCategory) (int64, error) {
	o := orm.NewOrm()

	// 插入数据
	id, err := o.Insert(category)
	if err != nil {
		logs.Error("创建商家分类失败: %v", err)
		return 0, err
	}

	return id, nil
}

// Update 更新商家分类信息
func (r *MerchantCategoryRepositoryImpl) Update(ctx context.Context, category *models.MerchantCategory) error {
	o := orm.NewOrm()

	// 设置更新时间
	category.UpdatedAt = time.Now()

	// 更新数据，只更新指定字段
	_, err := o.Update(category, "name", "description", "icon", "sort_order", "is_show", "updated_at")
	if err != nil {
		logs.Error("更新商家分类信息失败: %v", err)
		return err
	}

	return nil
}

// Delete 删除商家分类
func (r *MerchantCategoryRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 构建查询条件
	category := &models.MerchantCategory{ID: id}

	// 删除数据
	_, err := o.Delete(category)
	if err != nil {
		logs.Error("删除商家分类失败: %v", err)
		return err
	}

	return nil
}

// GetByID 根据ID获取商家分类
func (r *MerchantCategoryRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.MerchantCategory, error) {
	o := orm.NewOrm()

	// 构建查询条件
	category := &models.MerchantCategory{ID: id}

	// 查询数据
	err := o.Read(category)
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有找到数据
			return nil, nil
		}
		logs.Error("查询商家分类失败: %v", err)
		return nil, err
	}

	return category, nil
}

// List 获取商家分类列表
func (r *MerchantCategoryRepositoryImpl) List(ctx context.Context, params *dto.MerchantCategoryQueryParams, page, pageSize int) ([]*models.MerchantCategory, int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.MerchantCategory))

	// 应用查询条件
	if params.Name != "" {
		qs = qs.Filter("name__icontains", params.Name)
	}

	if params.IsShow != nil {
		qs = qs.Filter("is_show", *params.IsShow)
	}

	// 计算总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计商家分类总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize

	// 查询数据
	var categories []*models.MerchantCategory
	_, err = qs.OrderBy("sort_order", "-created_at").Limit(pageSize, offset).All(&categories)
	if err != nil {
		logs.Error("查询商家分类列表失败: %v", err)
		return nil, 0, err
	}

	return categories, total, nil
}

// GetAll 获取所有商家分类（不分页）
func (r *MerchantCategoryRepositoryImpl) GetAll(ctx context.Context, onlyShow bool) ([]*models.MerchantCategory, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.MerchantCategory))

	if onlyShow {
		qs = qs.Filter("is_show", true)
	}

	// 查询数据
	var categories []*models.MerchantCategory
	_, err := qs.OrderBy("sort_order").All(&categories)
	if err != nil {
		logs.Error("查询所有商家分类失败: %v", err)
		return nil, err
	}

	return categories, nil
}
