/**
 * 商家证件和协议数据存储库
 *
 * 该文件实现了商家证件和协议的数据访问层，提供对商家证件和协议数据的增删改查操作。
 * 包括添加证件、获取证件列表、更新证件状态等功能。
 */

package repositories

import (
	"context"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/models"
)

// MerchantDocumentRepository 商家证件和协议数据存储库接口
type MerchantDocumentRepository interface {
	// Create 创建商家证件
	Create(ctx context.Context, document *models.MerchantDocument) (int64, error)
	
	// GetByID 根据ID获取证件
	GetByID(ctx context.Context, id int64) (*models.MerchantDocument, error)
	
	// GetByMerchantID 获取商家的所有证件
	GetByMerchantID(ctx context.Context, merchantID int64) ([]*models.MerchantDocument, error)
	
	// GetByMerchantIDAndType 获取商家指定类型的证件
	GetByMerchantIDAndType(ctx context.Context, merchantID int64, docType int) ([]*models.MerchantDocument, error)
	
	// Update 更新证件信息
	Update(ctx context.Context, document *models.MerchantDocument) error
	
	// VerifyDocument 审核证件
	VerifyDocument(ctx context.Context, id int64, status int, verifierID int64, remark string) error
	
	// Delete 删除证件
	Delete(ctx context.Context, id int64) error
	
	// CheckDocumentExpiry 检查证件是否过期
	CheckDocumentExpiry(ctx context.Context, merchantID int64) ([]*models.MerchantDocument, error)
}

// MerchantDocumentRepositoryImpl 商家证件和协议数据存储库实现
type MerchantDocumentRepositoryImpl struct{}

// NewMerchantDocumentRepository 创建商家证件和协议数据存储库实例
func NewMerchantDocumentRepository() MerchantDocumentRepository {
	return &MerchantDocumentRepositoryImpl{}
}

// Create 创建商家证件
func (r *MerchantDocumentRepositoryImpl) Create(ctx context.Context, document *models.MerchantDocument) (int64, error) {
	o := orm.NewOrm()
	
	// 设置上传时间
	if document.UploadedAt.IsZero() {
		document.UploadedAt = time.Now()
	}
	
	// 创建新的证件记录
	id, err := o.Insert(document)
	if err != nil {
		logs.Error("创建商家证件失败: %v", err)
		return 0, err
	}
	
	return id, nil
}

// GetByID 根据ID获取证件
func (r *MerchantDocumentRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.MerchantDocument, error) {
	o := orm.NewOrm()
	document := &models.MerchantDocument{ID: id}
	
	err := o.Read(document)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询证件失败: %v", err)
		return nil, err
	}
	
	return document, nil
}

// GetByMerchantID 获取商家的所有证件
func (r *MerchantDocumentRepositoryImpl) GetByMerchantID(ctx context.Context, merchantID int64) ([]*models.MerchantDocument, error) {
	o := orm.NewOrm()
	var documents []*models.MerchantDocument
	
	_, err := o.QueryTable("merchant_document").
		Filter("merchant_id", merchantID).
		OrderBy("type", "uploaded_at").
		All(&documents)
	
	if err != nil {
		logs.Error("查询商家证件列表失败: %v", err)
		return nil, err
	}
	
	return documents, nil
}

// GetByMerchantIDAndType 获取商家指定类型的证件
func (r *MerchantDocumentRepositoryImpl) GetByMerchantIDAndType(ctx context.Context, merchantID int64, docType int) ([]*models.MerchantDocument, error) {
	o := orm.NewOrm()
	var documents []*models.MerchantDocument
	
	_, err := o.QueryTable("merchant_document").
		Filter("merchant_id", merchantID).
		Filter("type", docType).
		OrderBy("uploaded_at").
		All(&documents)
	
	if err != nil {
		logs.Error("查询商家指定类型证件失败: %v", err)
		return nil, err
	}
	
	return documents, nil
}

// Update 更新证件信息
func (r *MerchantDocumentRepositoryImpl) Update(ctx context.Context, document *models.MerchantDocument) error {
	o := orm.NewOrm()
	
	// 更新证件信息
	_, err := o.Update(document)
	if err != nil {
		logs.Error("更新商家证件失败: %v", err)
		return err
	}
	
	return nil
}

// VerifyDocument 审核证件
func (r *MerchantDocumentRepositoryImpl) VerifyDocument(ctx context.Context, id int64, status int, verifierID int64, remark string) error {
	o := orm.NewOrm()
	
	// 先查询证件
	document := &models.MerchantDocument{ID: id}
	if err := o.Read(document); err != nil {
		logs.Error("查询证件失败: %v", err)
		return err
	}
	
	// 更新审核信息
	document.Status = status
	document.VerifiedAt = time.Now()
	document.VerifiedBy = verifierID
	document.VerifyRemark = remark
	
	// 保存更新
	_, err := o.Update(document, "Status", "VerifiedAt", "VerifiedBy", "VerifyRemark")
	if err != nil {
		logs.Error("更新证件审核状态失败: %v", err)
		return err
	}
	
	return nil
}

// Delete 删除证件
func (r *MerchantDocumentRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	
	// 删除证件
	_, err := o.Delete(&models.MerchantDocument{ID: id})
	if err != nil {
		logs.Error("删除商家证件失败: %v", err)
		return err
	}
	
	return nil
}

// CheckDocumentExpiry 检查证件是否过期
func (r *MerchantDocumentRepositoryImpl) CheckDocumentExpiry(ctx context.Context, merchantID int64) ([]*models.MerchantDocument, error) {
	o := orm.NewOrm()
	var documents []*models.MerchantDocument
	
	// 获取当前日期
	today := time.Now()
	
	// 查询即将过期（30天内）或已过期的证件，排除长期有效的证件
	_, err := o.QueryTable("merchant_document").
		Filter("merchant_id", merchantID).
		Filter("is_long_term", 0).
		Filter("status", 1).
		Filter("valid_to__lte", today.AddDate(0, 0, 30)). // 30天内过期
		OrderBy("valid_to").
		All(&documents)
	
	if err != nil {
		logs.Error("查询商家过期证件失败: %v", err)
		return nil, err
	}
	
	return documents, nil
}
