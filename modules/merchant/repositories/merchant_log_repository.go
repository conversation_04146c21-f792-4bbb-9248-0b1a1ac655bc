/**
 * 商家日志仓库
 *
 * 该文件定义了商家日志仓库接口及其实现，用于商家日志数据的持久化和查询操作。
 * 提供了创建、查询等基本的数据访问功能。
 */

package repositories

import (
	"context"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/models"
)

// MerchantLogRepository 商家日志仓库接口
type MerchantLogRepository interface {
	// Create 创建日志
	Create(ctx context.Context, log *models.MerchantLog) (int64, error)
	
	// GetByID 根据ID获取日志
	GetByID(ctx context.Context, id int64) (*models.MerchantLog, error)
	
	// List 查询日志列表
	List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.MerchantLog, int64, error)
	
	// GetLatestByMerchantID 获取商家最近的日志
	GetLatestByMerchantID(ctx context.Context, merchantID int64, limit int) ([]*models.MerchantLog, error)
}

// MerchantLogRepositoryImpl 商家日志仓库实现
type MerchantLogRepositoryImpl struct{}

// NewMerchantLogRepository 创建商家日志仓库
func NewMerchantLogRepository() MerchantLogRepository {
	return &MerchantLogRepositoryImpl{}
}

// Create 创建日志
func (r *MerchantLogRepositoryImpl) Create(ctx context.Context, log *models.MerchantLog) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(log)
	if err != nil {
		logs.Error("创建商家日志失败: %v", err)
		return 0, fmt.Errorf("创建商家日志失败: %v", err)
	}
	return id, nil
}

// GetByID 根据ID获取日志
func (r *MerchantLogRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.MerchantLog, error) {
	o := orm.NewOrm()
	log := &models.MerchantLog{ID: id}
	err := o.Read(log)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("获取商家日志失败: %v", err)
		return nil, fmt.Errorf("获取商家日志失败: %v", err)
	}
	return log, nil
}

// List 查询日志列表
func (r *MerchantLogRepositoryImpl) List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.MerchantLog, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.MerchantLog))
	
	// 应用查询条件
	for key, value := range query {
		if value != nil && value != "" && value != 0 {
			qs = qs.Filter(key, value)
		}
	}
	
	// 统计总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计商家日志数量失败: %v", err)
		return nil, 0, fmt.Errorf("统计商家日志数量失败: %v", err)
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	var logList []*models.MerchantLog
	_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&logList)
	if err != nil {
		logs.Error("查询商家日志列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询商家日志列表失败: %v", err)
	}
	
	return logList, total, nil
}

// GetLatestByMerchantID 获取商家最近的日志
func (r *MerchantLogRepositoryImpl) GetLatestByMerchantID(ctx context.Context, merchantID int64, limit int) ([]*models.MerchantLog, error) {
	o := orm.NewOrm()
	var logList []*models.MerchantLog
	
	_, err := o.QueryTable(new(models.MerchantLog)).
		Filter("merchant_id", merchantID).
		OrderBy("-created_at").
		Limit(limit).
		All(&logList)
	
	if err != nil {
		logs.Error("获取商家最近日志失败: %v", err)
		return nil, fmt.Errorf("获取商家最近日志失败: %v", err)
	}
	
	return logList, nil
}
