/**
 * 商家仓库实现
 *
 * 该文件实现了商家仓库接口，提供商家数据的存取功能。
 * 通过Beego ORM操作数据库，实现商家数据的增删改查。
 */

package repositories

import (
	"context"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/models"
)

// MerchantRepositoryImpl 商家仓库实现
type MerchantRepositoryImpl struct{}

// NewMerchantRepository 创建商家仓库实例
func NewMerchantRepository() MerchantRepository {
	return &MerchantRepositoryImpl{}
}

// Create 创建商家
// 将商家信息存入数据库并返回商家ID
func (r *MerchantRepositoryImpl) Create(ctx context.Context, merchant *models.Merchant) (int64, error) {
	o := orm.NewOrm()

	// 插入数据
	id, err := o.Insert(merchant)
	if err != nil {
		logs.Error("创建商家失败: %v", err)
		return 0, err
	}

	return id, nil
}

// Update 更新商家信息
// 更新商家的基本信息，不包括密码、余额等敏感信息
func (r *MerchantRepositoryImpl) Update(ctx context.Context, merchant *models.Merchant) error {
	o := orm.NewOrm()

	// 设置更新时间
	merchant.UpdatedAt = time.Now()

	// 更新数据，只更新指定字段
	_, err := o.Update(merchant, "name", "logo", "description", "contact_name",
		"contact_mobile", "contact_email", "business_license", "address", "longitude", "latitude", "updated_at", "operation_status", "is_recommended")
	if err != nil {
		logs.Error("更新商家信息失败: %v", err)
		return err
	}

	return nil
}

// Delete 删除商家
// 从数据库中删除商家记录
func (r *MerchantRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 构建查询条件
	merchant := &models.Merchant{ID: id}

	// 删除数据
	_, err := o.Delete(merchant)
	if err != nil {
		logs.Error("删除商家失败: %v", err)
		return err
	}

	return nil
}

// GetByID 根据ID获取商家
// 通过商家ID查询商家详细信息
func (r *MerchantRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.Merchant, error) {
	o := orm.NewOrm()

	// 构建查询条件
	merchant := &models.Merchant{ID: id}

	// 查询数据
	err := o.Read(merchant)
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有找到数据
			return nil, nil
		}
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}

	return merchant, nil
}

// GetByUsername 根据用户名获取商家
// 通过用户名查询商家信息，主要用于登录验证
func (r *MerchantRepositoryImpl) GetByUsername(ctx context.Context, username string) (*models.Merchant, error) {
	o := orm.NewOrm()

	// 构建查询条件
	merchant := &models.Merchant{Username: username}

	// 查询数据
	err := o.Read(merchant, "username")
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有找到数据
			return nil, nil
		}
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}

	return merchant, nil
}

// GetByMobile 根据手机号获取商家
// 通过手机号查询商家信息，用于手机号登录验证
func (r *MerchantRepositoryImpl) GetByMobile(ctx context.Context, mobile string) (*models.Merchant, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.Merchant))

	// 使用 QuerySeter 进行查询，因为我们需要通过联系人手机号查询
	var merchants []*models.Merchant
	_, err := qs.Filter("contact_mobile", mobile).All(&merchants)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}

	// 检查是否找到商家
	if len(merchants) == 0 {
		return nil, nil
	}

	// 返回第一个匹配的商家
	return merchants[0], nil
}

// List 获取商家列表
// 根据查询条件分页获取商家列表
func (r *MerchantRepositoryImpl) List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.Merchant, int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.Merchant))

	// 应用查询条件
	// 首先处理ID查询，这是最高优先级
	if id, ok := query["id"].(int64); ok && id > 0 {
		logs.Info("应用ID查询条件: %d", id)
		qs = qs.Filter("id", id)
	}

	// 如果有关键词查询，可以同时匹配名称、描述等字段
	if keyword, ok := query["keyword"].(string); ok && keyword != "" {
		qs = qs.Filter("name__icontains", keyword)
		// 可以添加其他字段的模糊匹配
		// qs = qs.Filter("description__icontains", keyword)
	}

	// 其他具体字段的查询条件
	if name, ok := query["name"].(string); ok && name != "" {
		qs = qs.Filter("name__icontains", name)
	}

	if status, ok := query["status"].(int); ok && status != -1 {
		qs = qs.Filter("status", status)
	}

	if level, ok := query["level"].(int); ok && level != -1 {
		qs = qs.Filter("level", level)
	}

	if operationStatus, ok := query["operation_status"].(int); ok {
		qs = qs.Filter("operation_status", operationStatus)
	}

	// 筛选是否推荐
	if isRecommended, ok := query["is_recommended"].(int); ok && isRecommended != -1 {
		qs = qs.Filter("is_recommended", isRecommended)
	}

	// 筛选审核状态
	if auditStatus, ok := query["audit_status"].(int); ok && auditStatus != -1 {
		qs = qs.Filter("audit_status", auditStatus)
	}

	// 地理位置相关查询
	// 经度范围查询
	if minLng, ok := query["min_longitude"].(float64); ok {
		qs = qs.Filter("longitude__gte", minLng)
	}
	if maxLng, ok := query["max_longitude"].(float64); ok {
		qs = qs.Filter("longitude__lte", maxLng)
	}

	// 纬度范围查询
	if minLat, ok := query["min_latitude"].(float64); ok {
		qs = qs.Filter("latitude__gte", minLat)
	}
	if maxLat, ok := query["max_latitude"].(float64); ok {
		qs = qs.Filter("latitude__lte", maxLat)
	}

	// 按全局分类筛选商家（需要有该分类的商品）
	if globalCategoryID, ok := query["global_category_id"].(int64); ok && globalCategoryID > 0 {
		logs.Info("应用全局分类筛选条件: global_category_id=%d", globalCategoryID)
		// 使用子查询找到有指定全局分类商品的商家
		var merchantIDs orm.ParamsList
		subQuery := o.QueryTable("takeout_food").Filter("global_category_id", globalCategoryID).Filter("status", 1).Filter("audit_status", 1)
		_, err := subQuery.ValuesFlat(&merchantIDs, "merchant_id")
		if err != nil {
			logs.Error("查询全局分类商品的商家ID失败: %v", err)
			return nil, 0, err
		}

		if len(merchantIDs) > 0 {
			// 转换为 []interface{} 类型
			var merchantIDsInterface []interface{}
			for _, id := range merchantIDs {
				merchantIDsInterface = append(merchantIDsInterface, id)
			}
			qs = qs.Filter("id__in", merchantIDsInterface...)
			logs.Info("找到 %d 个有全局分类 %d 商品的商家", len(merchantIDs), globalCategoryID)
		} else {
			// 如果没有找到任何商家有该分类的商品，返回空结果
			logs.Info("没有找到有全局分类 %d 商品的商家", globalCategoryID)
			return []*models.Merchant{}, 0, nil
		}
	}

	// 计算总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计商家总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize

	// 打印详细的查询条件用于调试
	logs.Info("=== 商家查询调试信息 ===")
	logs.Info("查询条件详情:")
	for key, value := range query {
		logs.Info("  %s = %v", key, value)
	}
	logs.Info("分页参数: page=%d, pageSize=%d, offset=%d", page, pageSize, offset)
	logs.Info("查询条件日志: 排序=创建时间降序, 分页=%d,%d", pageSize, offset)

	// 查询数据
	var merchants []*models.Merchant
	_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&merchants)
	if err != nil {
		logs.Error("查询商家列表失败: %v", err)
		return nil, 0, err
	}

	// 打印查询结果详情
	logs.Info("查询到商家数量: %d", len(merchants))
	for i, m := range merchants {
		logs.Info("商家[%d]: ID=%d, 名称=%s, 状态=%d", i, m.ID, m.Name, m.Status)
	}

	return merchants, total, nil
}

// UpdatePassword 更新商家密码
// 只更新密码字段，用于密码修改和重置
func (r *MerchantRepositoryImpl) UpdatePassword(ctx context.Context, id int64, password string) error {
	o := orm.NewOrm()

	// 更新密码和更新时间
	_, err := o.Update(&models.Merchant{
		ID:        id,
		Password:  password,
		UpdatedAt: time.Now(),
	}, "password", "updated_at")

	if err != nil {
		logs.Error("更新商家密码失败: %v", err)
		return err
	}

	return nil
}

// UpdateStatus 更新商家状态
// 更新商家审核状态和拒绝原因
func (r *MerchantRepositoryImpl) UpdateStatus(ctx context.Context, id int64, status int, rejectReason string) error {
	o := orm.NewOrm()

	// 构建更新对象
	merchant := &models.Merchant{
		ID:           id,
		Status:       status,
		RejectReason: rejectReason,
		UpdatedAt:    time.Now(),
	}

	// 更新状态和拒绝原因
	_, err := o.Update(merchant, "status", "reject_reason", "updated_at")

	if err != nil {
		logs.Error("更新商家状态失败: %v", err)
		return err
	}

	return nil
}

// UpdateBalance 更新商家余额
// 增加或减少商家账户余额
func (r *MerchantRepositoryImpl) UpdateBalance(ctx context.Context, id int64, amount float64) error {
	o := orm.NewOrm()

	// 首先获取当前商家信息
	merchant := &models.Merchant{ID: id}
	err := o.Read(merchant)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return err
	}

	// 计算新余额
	merchant.Balance += amount
	merchant.UpdatedAt = time.Now()

	// 更新余额
	_, err = o.Update(merchant, "balance", "updated_at")
	if err != nil {
		logs.Error("更新商家余额失败: %v", err)
		return err
	}

	return nil
}

// UpdateLoginInfo 更新商家登录信息
// 更新最后登录时间和IP
func (r *MerchantRepositoryImpl) UpdateLoginInfo(ctx context.Context, id int64, ip string) error {
	o := orm.NewOrm()

	// 更新登录信息
	_, err := o.Update(&models.Merchant{
		ID:          id,
		LastLoginAt: time.Now(),
		LastLoginIP: ip,
		UpdatedAt:   time.Now(),
	}, "last_login_at", "last_login_ip", "updated_at")

	if err != nil {
		logs.Error("更新商家登录信息失败: %v", err)
		return err
	}

	return nil
}

// FindByStatus 根据状态查询商家
// 查询指定状态的所有商家
func (r *MerchantRepositoryImpl) FindByStatus(ctx context.Context, status int) ([]*models.Merchant, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.Merchant)).Filter("status", status)

	// 查询数据
	var merchants []*models.Merchant
	_, err := qs.All(&merchants)
	if err != nil {
		logs.Error("根据状态查询商家失败: %v, 状态: %d", err, status)
		return nil, err
	}

	logs.Info("根据状态[%d]查询到%d个商家", status, len(merchants))
	return merchants, nil
}
