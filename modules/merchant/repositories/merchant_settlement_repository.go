/**
 * 商家结算方式数据存储库
 *
 * 该文件实现了商家结算方式的数据访问层，提供对商家结算方式数据的增删改查操作。
 * 包括添加结算方式、获取结算方式列表、设置默认结算方式等功能。
 */

package repositories

import (
	"context"
	"errors"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/models"
)

// MerchantSettlementRepository 商家结算方式数据存储库接口
type MerchantSettlementRepository interface {
	// Create 创建商家结算方式
	Create(ctx context.Context, settlement *models.MerchantSettlement) (int64, error)
	
	// GetByID 根据ID获取结算方式
	GetByID(ctx context.Context, id int64) (*models.MerchantSettlement, error)
	
	// GetByMerchantID 获取商家的所有结算方式
	GetByMerchantID(ctx context.Context, merchantID int64) ([]*models.MerchantSettlement, error)
	
	// GetDefaultByMerchantID 获取商家的默认结算方式
	GetDefaultByMerchantID(ctx context.Context, merchantID int64) (*models.MerchantSettlement, error)
	
	// Update 更新结算方式
	Update(ctx context.Context, settlement *models.MerchantSettlement) error
	
	// Delete 删除结算方式
	Delete(ctx context.Context, id int64) error
	
	// SetDefault 设置默认结算方式
	SetDefault(ctx context.Context, merchantID, settlementID int64) error
}

// MerchantSettlementRepositoryImpl 商家结算方式数据存储库实现
type MerchantSettlementRepositoryImpl struct{}

// NewMerchantSettlementRepository 创建商家结算方式数据存储库实例
func NewMerchantSettlementRepository() MerchantSettlementRepository {
	return &MerchantSettlementRepositoryImpl{}
}

// Create 创建商家结算方式
func (r *MerchantSettlementRepositoryImpl) Create(ctx context.Context, settlement *models.MerchantSettlement) (int64, error) {
	o := orm.NewOrm()
	
	// 如果是默认结算方式，需要将其他结算方式设为非默认
	if settlement.IsDefault == 1 {
		_, err := o.Raw("UPDATE merchant_settlement SET is_default = 0 WHERE merchant_id = ?", settlement.MerchantID).Exec()
		if err != nil {
			logs.Error("重置商家默认结算方式失败: %v", err)
			return 0, err
		}
	}
	
	// 创建新的结算方式
	id, err := o.Insert(settlement)
	if err != nil {
		logs.Error("创建商家结算方式失败: %v", err)
		return 0, err
	}
	
	return id, nil
}

// GetByID 根据ID获取结算方式
func (r *MerchantSettlementRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.MerchantSettlement, error) {
	o := orm.NewOrm()
	settlement := &models.MerchantSettlement{ID: id}
	
	err := o.Read(settlement)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询结算方式失败: %v", err)
		return nil, err
	}
	
	return settlement, nil
}

// GetByMerchantID 获取商家的所有结算方式
func (r *MerchantSettlementRepositoryImpl) GetByMerchantID(ctx context.Context, merchantID int64) ([]*models.MerchantSettlement, error) {
	o := orm.NewOrm()
	var settlements []*models.MerchantSettlement
	
	_, err := o.QueryTable("merchant_settlement").
		Filter("merchant_id", merchantID).
		OrderBy("-is_default", "type", "id").
		All(&settlements)
	
	if err != nil {
		logs.Error("查询商家结算方式列表失败: %v", err)
		return nil, err
	}
	
	return settlements, nil
}

// GetDefaultByMerchantID 获取商家的默认结算方式
func (r *MerchantSettlementRepositoryImpl) GetDefaultByMerchantID(ctx context.Context, merchantID int64) (*models.MerchantSettlement, error) {
	o := orm.NewOrm()
	var settlement models.MerchantSettlement
	
	err := o.QueryTable("merchant_settlement").
		Filter("merchant_id", merchantID).
		Filter("is_default", 1).
		Filter("status", models.SettlementStatusEnabled).
		One(&settlement)
	
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询商家默认结算方式失败: %v", err)
		return nil, err
	}
	
	return &settlement, nil
}

// Update 更新结算方式
func (r *MerchantSettlementRepositoryImpl) Update(ctx context.Context, settlement *models.MerchantSettlement) error {
	o := orm.NewOrm()
	
	// 先查询原始数据
	oldSettlement := &models.MerchantSettlement{ID: settlement.ID}
	if err := o.Read(oldSettlement); err != nil {
		logs.Error("查询原结算方式失败: %v", err)
		return err
	}
	
	// 如果是设置为默认，需要将其他结算方式设为非默认
	if settlement.IsDefault == 1 && oldSettlement.IsDefault == 0 {
		_, err := o.Raw("UPDATE merchant_settlement SET is_default = 0 WHERE merchant_id = ?", settlement.MerchantID).Exec()
		if err != nil {
			logs.Error("重置商家默认结算方式失败: %v", err)
			return err
		}
	}
	
	// 更新结算方式
	_, err := o.Update(settlement)
	if err != nil {
		logs.Error("更新商家结算方式失败: %v", err)
		return err
	}
	
	return nil
}

// Delete 删除结算方式
func (r *MerchantSettlementRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	
	// 先查询是否为默认结算方式
	settlement := &models.MerchantSettlement{ID: id}
	if err := o.Read(settlement); err != nil {
		logs.Error("查询结算方式失败: %v", err)
		return err
	}
	
	// 不允许删除默认结算方式
	if settlement.IsDefault == 1 {
		return errors.New("不能删除默认结算方式，请先设置其他结算方式为默认")
	}
	
	// 删除结算方式
	_, err := o.Delete(settlement)
	if err != nil {
		logs.Error("删除商家结算方式失败: %v", err)
		return err
	}
	
	return nil
}

// SetDefault 设置默认结算方式
func (r *MerchantSettlementRepositoryImpl) SetDefault(ctx context.Context, merchantID, settlementID int64) error {
	o := orm.NewOrm()
	
	// 使用事务执行
	err := o.DoTx(func(ctx context.Context, txOrm orm.TxOrmer) error {
		// 将所有结算方式设为非默认
		_, err := txOrm.Raw("UPDATE merchant_settlement SET is_default = 0 WHERE merchant_id = ?", merchantID).Exec()
		if err != nil {
			logs.Error("重置商家默认结算方式失败: %v", err)
			return err
		}
		
		// 将指定结算方式设为默认
		_, err = txOrm.Raw("UPDATE merchant_settlement SET is_default = 1 WHERE id = ? AND merchant_id = ?", settlementID, merchantID).Exec()
		if err != nil {
			logs.Error("设置默认结算方式失败: %v", err)
			return err
		}
		
		return nil
	})
	
	if err != nil {
		logs.Error("执行事务失败: %v", err)
		return err
	}
	
	return nil
}
