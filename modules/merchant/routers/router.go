/**
 * 商家模块路由注册
 *
 * 该文件负责商家模块的路由注册，将控制器方法映射到URL路径，并设置相应的权限控制。
 * 所有与商家模块相关的API路由都在此处定义。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/merchant/controllers"
)

// Init 初始化商家模块路由
func Init() {
	// 商家端API路由
	ns := web.NewNamespace("/api/v1/merchant",
		// 公开接口，无需认证
		web.NSRouter("/refresh-token", &controllers.MerchantController{}, "post:RefreshToken"),
		web.NSRouter("/login", &controllers.MerchantController{}, "post:Login"),
		web.NSRouter("/login/mobile", &controllers.MerchantController{}, "post:LoginByMobile"),
		web.NSRouter("/login/verify-code", &controllers.MerchantController{}, "post:LoginByVerifyCode"),
		web.NSRouter("/send-code", &controllers.MerchantController{}, "post:SendVerificationCode"),
		web.NSRouter("/register", &controllers.MerchantController{}, "post:Register"),
		web.NSRouter("/upload", &controllers.UploadFileController{}, "post:Upload"),
		web.NSRouter("/merchant-categories", &controllers.MerchantCategoryAPIController{}, "get:GetMerchantCategories"),
		web.NSRouter("/recommended-merchants", &controllers.MerchantController{}, "get:GetRecommendedMerchants"),

		// web.NSNamespace("/public",

		// ),

		// 需要商家认证的接口
		web.NSNamespace("/secured",
			// 添加商家JWT认证中间件
			web.NSBefore(middlewares.JWTMiddleware),
			web.NSBefore(middlewares.MerchantAuthMiddleware),
			// 添加商家日志中间件
			web.NSBefore(middlewares.MerchantLogMiddleware()),

			// 商家信息相关接口
			web.NSRouter("/info", &controllers.MerchantController{}, "get:GetMerchantInfo"),
			web.NSRouter("/", &controllers.MerchantController{}, "put:UpdateMerchant"),
			web.NSRouter("/password", &controllers.MerchantController{}, "put:ChangePassword"),
			web.NSRouter("/operation-status-start", &controllers.MerchantController{}, "put:StartBusiness"),
			web.NSRouter("/operation-status-stop", &controllers.MerchantController{}, "put:StopBusiness"),
			// 商家营业统计相关接口
			web.NSRouter("/business-stats", &controllers.MerchantBusinessStatsController{}, "get:GetBusinessStats"),

			// 商品管理相关接口
			web.NSRouter("/products", &controllers.MerchantProductController{}, "get:GetProducts;post:CreateProduct"),
			web.NSRouter("/products/:id", &controllers.MerchantProductController{}, "get:GetProduct;put:UpdateProduct;delete:DeleteProduct"),

			// 商品分类管理接口
			web.NSRouter("/categories", &controllers.MerchantCategoryController{}, "get:GetCategories"),
			web.NSRouter("/categories/:id", &controllers.MerchantCategoryController{}, "get:GetCategoryByID"),
			web.NSRouter("/categories/children/:parent_id", &controllers.MerchantCategoryController{}, "get:GetChildCategories"),

			// 商家经营分类管理接口
			web.NSRouter("/business-categories", &controllers.MerchantBusinessCategoryController{}, "get:ListCategories;post:CreateCategory"),
			web.NSRouter("/business-categories/all", &controllers.MerchantBusinessCategoryController{}, "get:GetAllCategories"),
			web.NSRouter("/business-categories/:id", &controllers.MerchantBusinessCategoryController{}, "get:GetCategoryByID;put:UpdateCategory;delete:DeleteCategory"),

			// 商品规格管理接口
			web.NSRouter("/specifications", &controllers.MerchantSpecificationController{}, "get:GetSpecifications;post:CreateSpecification"),
			web.NSRouter("/specifications/:id", &controllers.MerchantSpecificationController{}, "get:GetSpecification;put:UpdateSpecification;delete:DeleteSpecification"),
			web.NSRouter("/specifications/:spec_id/values", &controllers.MerchantSpecificationController{}, "get:GetSpecificationValues;post:CreateSpecificationValue"),
			web.NSRouter("/specification-values/:value_id", &controllers.MerchantSpecificationController{}, "put:UpdateSpecificationValue;delete:DeleteSpecificationValue"),

			// 商品SKU和库存管理接口
			web.NSRouter("/products/:product_id/skus", &controllers.MerchantSkuController{}, "get:GetProductSkus"),
			web.NSRouter("/products/:product_id/stock", &controllers.MerchantSkuController{}, "put:UpdateProductStock"),
			web.NSRouter("/skus/:sku_id", &controllers.MerchantSkuController{}, "put:UpdateSku"),
			web.NSRouter("/skus/:sku_id/stock", &controllers.MerchantSkuController{}, "put:UpdateSkuStock"),
			web.NSRouter("/skus/batch-stock", &controllers.MerchantSkuController{}, "put:BatchUpdateSkuStock"),

			// 商家结算方式管理接口
			web.NSRouter("/settlements", &controllers.MerchantSettlementController{}, "get:GetSettlementList;post:CreateSettlement"),
			web.NSRouter("/settlements/:id", &controllers.MerchantSettlementController{}, "get:GetSettlement;put:UpdateSettlement;delete:DeleteSettlement"),
			web.NSRouter("/settlements/:id/default", &controllers.MerchantSettlementController{}, "put:SetDefaultSettlement"),

			// 商家证件和协议管理接口
			web.NSRouter("/documents", &controllers.MerchantDocumentController{}, "get:GetDocumentList;post:UploadDocument"),
			web.NSRouter("/documents/:id", &controllers.MerchantDocumentController{}, "get:GetDocument;put:UpdateDocument;delete:DeleteDocument"),
			web.NSRouter("/documents/expiring", &controllers.MerchantDocumentController{}, "get:GetExpiringDocuments"),
		),

		// 管理员操作商家接口已迁移到 /api/v1/admin/merchants 路径下
	)

	// 注册命名空间
	web.AddNamespace(ns)
}
