/**
 * 商家服务接口定义
 *
 * 该文件定义了商家模块的服务层接口，声明了商家相关的业务方法。
 * 通过接口定义与实现分离，便于单元测试和功能扩展。
 */

package services

import (
	"context"

	"o_mall_backend/modules/merchant/dto"
)

// MerchantService 商家服务接口
type MerchantService interface {
	// 商家认证相关
	Login(ctx context.Context, req *dto.MerchantLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error)

	// LoginByMobile 商家手机号登录
	LoginByMobile(ctx context.Context, req *dto.MerchantMobileLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error)
	
	// SendVerificationCode 发送手机验证码
	SendVerificationCode(ctx context.Context, req *dto.SendVerificationCodeRequest) (*dto.SendVerificationCodeResponse, error)
	
	// LoginByVerifyCode 商家手机验证码登录
	LoginByVerifyCode(ctx context.Context, req *dto.MerchantVerifyCodeLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*dto.TokenResponse, error)
	Logout(ctx context.Context, merchantID int64) error
	CreateMerchant(ctx context.Context, req *dto.CreateMerchantRequest) (int64, error)

	// 商家信息相关
	GetMerchantByID(ctx context.Context, id int64) (*dto.MerchantResponse, error)
	UpdateMerchant(ctx context.Context, req *dto.UpdateMerchantRequest) error
	ChangePassword(ctx context.Context, id int64, req *dto.ChangePasswordRequest) error
	ResetPassword(ctx context.Context, req *dto.ResetPasswordRequest) error
	UpdateOperationStatus(ctx context.Context, merchantID int64, req *dto.UpdateOperationStatusRequest) error
	// UpdateMerchantCoordinates 更新商户经纬度坐标
	UpdateMerchantCoordinates(ctx context.Context, req *dto.UpdateMerchantRequest) error

	// 商家管理相关
	ListMerchants(ctx context.Context, req *dto.MerchantQueryRequest) ([]*dto.MerchantResponse, int64, error)
	AuditMerchant(ctx context.Context, req *dto.AuditMerchantRequest) error
	DeleteMerchant(ctx context.Context, id int64) error

	// 商家财务相关
	UpdateBalance(ctx context.Context, req *dto.UpdateBalanceRequest) error
}

// MerchantLogService 商家日志服务接口
type MerchantLogService interface {
	// CreateLog 创建商家日志
	CreateLog(ctx context.Context, req *dto.MerchantLogCreateRequest) (int64, error)
	
	// GetLogByID 获取日志详情
	GetLogByID(ctx context.Context, id int64) (*dto.MerchantLogResponse, error)
	
	// ListLogs 获取日志列表
	ListLogs(ctx context.Context, req *dto.MerchantLogQueryRequest) ([]*dto.MerchantLogResponse, int64, error)
	
	// GetLatestLogsByMerchantID 获取商家最近的日志
	GetLatestLogsByMerchantID(ctx context.Context, merchantID int64, limit int) ([]*dto.MerchantLogResponse, error)
	
	// GetBusinessStats 获取商家营业统计信息
	GetBusinessStats(ctx context.Context, merchantID int64, period string) (*dto.BusinessStatsResponse, error)
}
