/**
 * 商家分类服务
 *
 * 该文件定义了商家分类服务接口和实现，提供商家分类的业务逻辑处理。
 * 实现了商家分类的创建、更新、删除、查询等业务功能。
 */

package services

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/repositories"
)

// MerchantCategoryService 商家分类服务接口
type MerchantCategoryService interface {
	// CreateCategory 创建商家分类
	CreateCategory(ctx context.Context, req *dto.CreateMerchantCategoryRequest) (int64, error)
	
	// UpdateCategory 更新商家分类
	UpdateCategory(ctx context.Context, id int64, req *dto.UpdateMerchantCategoryRequest) error
	
	// DeleteCategory 删除商家分类
	DeleteCategory(ctx context.Context, id int64) error
	
	// GetCategoryByID 根据ID获取商家分类
	GetCategoryByID(ctx context.Context, id int64) (*dto.MerchantCategoryResponse, error)
	
	// ListCategories 获取商家分类列表
	ListCategories(ctx context.Context, req *dto.MerchantCategoryListRequest) (*dto.MerchantCategoryListResponse, error)

	// GetAllCategories 获取所有商家分类（不分页）
	GetAllCategories(ctx context.Context, onlyShow bool) ([]*dto.MerchantCategoryResponse, error)
}

// MerchantCategoryServiceImpl 商家分类服务实现
type MerchantCategoryServiceImpl struct {
	categoryRepo repositories.MerchantCategoryRepository
}

// NewMerchantCategoryService 创建商家分类服务实例
func NewMerchantCategoryService() MerchantCategoryService {
	return &MerchantCategoryServiceImpl{
		categoryRepo: repositories.NewMerchantCategoryRepository(),
	}
}

// CreateCategory 创建商家分类
func (s *MerchantCategoryServiceImpl) CreateCategory(ctx context.Context, req *dto.CreateMerchantCategoryRequest) (int64, error) {
	// 构建分类模型
	category := &models.MerchantCategory{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
		IsShow:      req.IsShow,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 创建分类
	id, err := s.categoryRepo.Create(ctx, category)
	if err != nil {
		logs.Error("创建商家分类失败: %v", err)
		return 0, fmt.Errorf("创建商家分类失败: %v", err)
	}

	return id, nil
}

// UpdateCategory 更新商家分类
func (s *MerchantCategoryServiceImpl) UpdateCategory(ctx context.Context, id int64, req *dto.UpdateMerchantCategoryRequest) error {
	// 先查询分类是否存在
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商家分类失败: %v", err)
		return fmt.Errorf("获取商家分类失败: %v", err)
	}

	if category == nil {
		return fmt.Errorf("商家分类不存在")
	}

	// 更新分类信息
	if req.Name != "" {
		category.Name = req.Name
	}
	if req.Description != "" || req.Description == "" { // 允许将描述设置为空
		category.Description = req.Description
	}
	if req.Icon != "" {
		category.Icon = req.Icon
	}
	
	category.SortOrder = req.SortOrder
	category.IsShow = req.IsShow
	category.UpdatedAt = time.Now()

	// 更新分类
	err = s.categoryRepo.Update(ctx, category)
	if err != nil {
		logs.Error("更新商家分类失败: %v", err)
		return fmt.Errorf("更新商家分类失败: %v", err)
	}

	return nil
}

// DeleteCategory 删除商家分类
func (s *MerchantCategoryServiceImpl) DeleteCategory(ctx context.Context, id int64) error {
	// 先查询分类是否存在
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商家分类失败: %v", err)
		return fmt.Errorf("获取商家分类失败: %v", err)
	}

	if category == nil {
		return fmt.Errorf("商家分类不存在")
	}

	// 删除分类
	err = s.categoryRepo.Delete(ctx, id)
	if err != nil {
		logs.Error("删除商家分类失败: %v", err)
		return fmt.Errorf("删除商家分类失败: %v", err)
	}

	return nil
}

// GetCategoryByID 根据ID获取商家分类
func (s *MerchantCategoryServiceImpl) GetCategoryByID(ctx context.Context, id int64) (*dto.MerchantCategoryResponse, error) {
	// 查询分类
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商家分类失败: %v", err)
		return nil, fmt.Errorf("获取商家分类失败: %v", err)
	}

	if category == nil {
		return nil, fmt.Errorf("商家分类不存在")
	}

	// 转换为响应DTO
	response := s.convertToResponse(category)
	return response, nil
}

// ListCategories 获取商家分类列表
func (s *MerchantCategoryServiceImpl) ListCategories(ctx context.Context, req *dto.MerchantCategoryListRequest) (*dto.MerchantCategoryListResponse, error) {
	// 构建查询参数
	params := &dto.MerchantCategoryQueryParams{
		Name:   req.Name,
		IsShow: req.IsShow,
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询分类列表
	categories, total, err := s.categoryRepo.List(ctx, params, page, pageSize)
	if err != nil {
		logs.Error("获取商家分类列表失败: %v", err)
		return nil, fmt.Errorf("获取商家分类列表失败: %v", err)
	}

	// 转换为响应DTO
	var items []*dto.MerchantCategoryResponse
	for _, category := range categories {
		items = append(items, s.convertToResponse(category))
	}

	// 构建响应
	response := &dto.MerchantCategoryListResponse{
		Total: total,
		Items: items,
	}

	return response, nil
}

// GetAllCategories 获取所有商家分类（不分页）
func (s *MerchantCategoryServiceImpl) GetAllCategories(ctx context.Context, onlyShow bool) ([]*dto.MerchantCategoryResponse, error) {
	// 查询所有分类
	categories, err := s.categoryRepo.GetAll(ctx, onlyShow)
	if err != nil {
		logs.Error("获取所有商家分类失败: %v", err)
		return nil, fmt.Errorf("获取所有商家分类失败: %v", err)
	}

	// 转换为响应DTO
	var responses []*dto.MerchantCategoryResponse
	for _, category := range categories {
		responses = append(responses, s.convertToResponse(category))
	}

	return responses, nil
}

// convertToResponse 将分类模型转换为响应DTO
func (s *MerchantCategoryServiceImpl) convertToResponse(category *models.MerchantCategory) *dto.MerchantCategoryResponse {
	return &dto.MerchantCategoryResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
		IsShow:      category.IsShow,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}
