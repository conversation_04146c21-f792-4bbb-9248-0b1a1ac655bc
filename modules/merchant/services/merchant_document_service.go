/**
 * 商家证件和协议服务
 *
 * 该文件实现了商家证件和协议的业务逻辑，提供证件的上传、查询、审核等功能。
 * 作为商家管理的重要组成部分，负责处理商家资质和协议相关的业务操作。
 */

package services

import (
	"context"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/repositories"
)

// MerchantDocumentService 商家证件和协议服务接口
type MerchantDocumentService interface {
	// UploadDocument 上传证件
	UploadDocument(ctx context.Context, req *dto.UploadDocumentRequest) (int64, error)
	
	// GetDocumentByID 根据ID获取证件
	GetDocumentByID(ctx context.Context, id int64) (*dto.DocumentResponse, error)
	
	// GetDocumentsByMerchantID 获取商家的所有证件
	GetDocumentsByMerchantID(ctx context.Context, merchantID int64) ([]*dto.DocumentResponse, error)
	
	// GetDocumentsByType 获取商家指定类型的证件
	GetDocumentsByType(ctx context.Context, merchantID int64, docType int) ([]*dto.DocumentResponse, error)
	
	// UpdateDocument 更新证件
	UpdateDocument(ctx context.Context, req *dto.UpdateDocumentRequest) error
	
	// VerifyDocument 审核证件
	VerifyDocument(ctx context.Context, req *dto.VerifyDocumentRequest, verifierID int64) error
	
	// DeleteDocument 删除证件
	DeleteDocument(ctx context.Context, id int64) error
	
	// GetExpiringDocuments 获取即将过期的证件
	GetExpiringDocuments(ctx context.Context, merchantID int64) ([]*dto.DocumentResponse, error)
}

// MerchantDocumentServiceImpl 商家证件和协议服务实现
type MerchantDocumentServiceImpl struct {
	documentRepo repositories.MerchantDocumentRepository
	merchantRepo repositories.MerchantRepository
}

// NewMerchantDocumentService 创建商家证件和协议服务实例
func NewMerchantDocumentService() MerchantDocumentService {
	return &MerchantDocumentServiceImpl{
		documentRepo: repositories.NewMerchantDocumentRepository(),
		merchantRepo: repositories.NewMerchantRepository(),
	}
}

// UploadDocument 上传证件
func (s *MerchantDocumentServiceImpl) UploadDocument(ctx context.Context, req *dto.UploadDocumentRequest) (int64, error) {
	// 检查商家是否存在
	merchant, err := s.merchantRepo.GetByID(ctx, req.MerchantID)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return 0, err
	}
	if merchant == nil {
		return 0, nil
	}
	
	// 创建证件模型
	document := &models.MerchantDocument{
		MerchantID:  req.MerchantID,
		Type:        req.Type,
		Name:        req.Name,
		Number:      req.Number,
		FrontImage:  req.FrontImage,
		BackImage:   req.BackImage,
		ValidFrom:   req.ValidFrom,
		ValidTo:     req.ValidTo,
		IsLongTerm:  req.IsLongTerm,
		Status:      1, // 默认有效
		UploadedAt:  time.Now(),
		Remark:      req.Remark,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	// 如果是长期有效，清空有效期结束日期
	if document.IsLongTerm == 1 {
		document.ValidTo = time.Time{}
	}
	
	// 创建证件
	id, err := s.documentRepo.Create(ctx, document)
	if err != nil {
		logs.Error("创建证件失败: %v", err)
		return 0, err
	}
	
	return id, nil
}

// GetDocumentByID 根据ID获取证件
func (s *MerchantDocumentServiceImpl) GetDocumentByID(ctx context.Context, id int64) (*dto.DocumentResponse, error) {
	// 查询证件
	document, err := s.documentRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询证件失败: %v", err)
		return nil, err
	}
	if document == nil {
		return nil, nil
	}
	
	// 转换为响应对象
	resp := convertToDocumentResponse(document)
	return resp, nil
}

// GetDocumentsByMerchantID 获取商家的所有证件
func (s *MerchantDocumentServiceImpl) GetDocumentsByMerchantID(ctx context.Context, merchantID int64) ([]*dto.DocumentResponse, error) {
	// 查询证件列表
	documents, err := s.documentRepo.GetByMerchantID(ctx, merchantID)
	if err != nil {
		logs.Error("查询商家证件列表失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	respList := make([]*dto.DocumentResponse, 0, len(documents))
	for _, document := range documents {
		resp := convertToDocumentResponse(document)
		respList = append(respList, resp)
	}
	
	return respList, nil
}

// GetDocumentsByType 获取商家指定类型的证件
func (s *MerchantDocumentServiceImpl) GetDocumentsByType(ctx context.Context, merchantID int64, docType int) ([]*dto.DocumentResponse, error) {
	// 查询指定类型的证件列表
	documents, err := s.documentRepo.GetByMerchantIDAndType(ctx, merchantID, docType)
	if err != nil {
		logs.Error("查询商家指定类型证件列表失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	respList := make([]*dto.DocumentResponse, 0, len(documents))
	for _, document := range documents {
		resp := convertToDocumentResponse(document)
		respList = append(respList, resp)
	}
	
	return respList, nil
}

// UpdateDocument 更新证件
func (s *MerchantDocumentServiceImpl) UpdateDocument(ctx context.Context, req *dto.UpdateDocumentRequest) error {
	// 查询证件
	document, err := s.documentRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询证件失败: %v", err)
		return err
	}
	if document == nil {
		return nil
	}
	
	// 更新证件
	document.Type = req.Type
	document.Name = req.Name
	document.Number = req.Number
	document.FrontImage = req.FrontImage
	document.BackImage = req.BackImage
	document.ValidFrom = req.ValidFrom
	document.IsLongTerm = req.IsLongTerm
	document.Remark = req.Remark
	document.UpdatedAt = time.Now()
	
	// 如果是长期有效，清空有效期结束日期
	if document.IsLongTerm == 1 {
		document.ValidTo = time.Time{}
	} else {
		document.ValidTo = req.ValidTo
	}
	
	// 保存更新
	err = s.documentRepo.Update(ctx, document)
	if err != nil {
		logs.Error("更新证件失败: %v", err)
		return err
	}
	
	return nil
}

// VerifyDocument 审核证件
func (s *MerchantDocumentServiceImpl) VerifyDocument(ctx context.Context, req *dto.VerifyDocumentRequest, verifierID int64) error {
	// 审核证件
	err := s.documentRepo.VerifyDocument(ctx, req.ID, req.Status, verifierID, req.VerifyRemark)
	if err != nil {
		logs.Error("审核证件失败: %v", err)
		return err
	}
	
	return nil
}

// DeleteDocument 删除证件
func (s *MerchantDocumentServiceImpl) DeleteDocument(ctx context.Context, id int64) error {
	// 删除证件
	err := s.documentRepo.Delete(ctx, id)
	if err != nil {
		logs.Error("删除证件失败: %v", err)
		return err
	}
	
	return nil
}

// GetExpiringDocuments 获取即将过期的证件
func (s *MerchantDocumentServiceImpl) GetExpiringDocuments(ctx context.Context, merchantID int64) ([]*dto.DocumentResponse, error) {
	// 查询即将过期的证件
	documents, err := s.documentRepo.CheckDocumentExpiry(ctx, merchantID)
	if err != nil {
		logs.Error("查询商家过期证件失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	respList := make([]*dto.DocumentResponse, 0, len(documents))
	for _, document := range documents {
		resp := convertToDocumentResponse(document)
		respList = append(respList, resp)
	}
	
	return respList, nil
}

// convertToDocumentResponse 将证件模型转换为响应对象
func convertToDocumentResponse(document *models.MerchantDocument) *dto.DocumentResponse {
	if document == nil {
		return nil
	}
	
	resp := &dto.DocumentResponse{
		ID:           document.ID,
		MerchantID:   document.MerchantID,
		Type:         document.Type,
		TypeDesc:     dto.DocumentTypeDesc[document.Type],
		Name:         document.Name,
		Number:       document.Number,
		FrontImage:   document.FrontImage,
		BackImage:    document.BackImage,
		ValidFrom:    document.ValidFrom,
		ValidTo:      document.ValidTo,
		IsLongTerm:   document.IsLongTerm,
		Status:       document.Status,
		StatusDesc:   getDocumentStatusDesc(document.Status),
		UploadedAt:   document.UploadedAt,
		VerifiedAt:   document.VerifiedAt,
		VerifiedBy:   document.VerifiedBy,
		VerifyRemark: document.VerifyRemark,
		Remark:       document.Remark,
		CreatedAt:    document.CreatedAt,
		UpdatedAt:    document.UpdatedAt,
	}
	
	// 计算是否过期以及距离过期天数
	now := time.Now()
	if document.IsLongTerm == 1 {
		resp.IsExpired = false
		resp.ExpiryDays = 36500 // 约100年
	} else if !document.ValidTo.IsZero() {
		resp.IsExpired = now.After(document.ValidTo)
		expiryDays := int(document.ValidTo.Sub(now).Hours() / 24)
		resp.ExpiryDays = expiryDays
	}
	
	return resp
}

// getDocumentStatusDesc 获取证件状态描述
func getDocumentStatusDesc(status int) string {
	switch status {
	case 0:
		return "无效"
	case 1:
		return "有效"
	default:
		return "未知状态"
	}
}
