/**
 * 商家位置服务
 *
 * 本文件实现了商家位置相关的服务接口，提供获取商家位置信息、计算商家间距离等功能
 * 主要用于外卖配送和跑腿服务场景
 */

package services

import (
	"context"
	"errors"
	"math"

	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/repositories"
)

// 地球半径（单位：千米）
const earthRadiusKm = 6371.0

// MerchantLocationService 商家位置服务接口
type MerchantLocationService interface {
	// GetMerchantLocation 获取商家位置信息
	GetMerchantLocation(ctx context.Context, merchantID int64) (*MerchantLocationInfo, error)
	
	// CalculateDistance 计算两个位置之间的距离（单位：千米）
	CalculateDistance(lat1, lng1, lat2, lng2 float64) float64
	
	// FindNearbyMerchants 查找指定位置附近的商家
	FindNearbyMerchants(ctx context.Context, latitude, longitude float64, radiusKm float64, limit int) ([]*MerchantLocationInfo, error)
}

// MerchantLocationInfo 商家位置信息
type MerchantLocationInfo struct {
	MerchantID   int64    `json:"merchant_id"`   // 商家ID
	MerchantName string   `json:"merchant_name"` // 商家名称
	Address      string   `json:"address"`       // 地址
	Latitude     float64  `json:"latitude"`      // 纬度
	Longitude    float64  `json:"longitude"`     // 经度
	Distance     float64  `json:"distance"`      // 距离（千米），仅在搜索附近商家时有值
}

// MerchantLocationServiceImpl 商家位置服务实现
type MerchantLocationServiceImpl struct {
	merchantRepo repositories.MerchantRepository
}

// NewMerchantLocationService 创建商家位置服务实例
func NewMerchantLocationService() MerchantLocationService {
	return &MerchantLocationServiceImpl{
		merchantRepo: repositories.NewMerchantRepository(),
	}
}

// GetMerchantLocation 获取商家位置信息
func (s *MerchantLocationServiceImpl) GetMerchantLocation(ctx context.Context, merchantID int64) (*MerchantLocationInfo, error) {
	// 查询商家基本信息
	merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		logs.Error("查询商家信息失败: %v", err)
		return nil, err
	}
	
	if merchant == nil {
		return nil, errors.New("商家不存在")
	}
	
	// 检查商家状态
	if merchant.Status != models.MerchantStatusApproved {
		return nil, errors.New("商家账号状态异常")
	}
	
	// 检查位置信息是否有效
	if merchant.Latitude == 0 && merchant.Longitude == 0 {
		logs.Warning("商家ID=%d 未设置位置信息", merchantID)
		return nil, errors.New("商家未设置位置信息")
	}
	
	// 构建并返回位置信息
	return &MerchantLocationInfo{
		MerchantID:   merchant.ID,
		MerchantName: merchant.Name,
		Address:      merchant.Address,
		Latitude:     merchant.Latitude,
		Longitude:    merchant.Longitude,
	}, nil
}

// CalculateDistance 计算两个位置之间的距离（单位：千米）
// 使用Haversine公式计算球面两点间的距离
func (s *MerchantLocationServiceImpl) CalculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	// 将角度转换为弧度
	lat1Rad := lat1 * math.Pi / 180.0
	lng1Rad := lng1 * math.Pi / 180.0
	lat2Rad := lat2 * math.Pi / 180.0
	lng2Rad := lng2 * math.Pi / 180.0
	
	// Haversine公式
	dLat := lat2Rad - lat1Rad
	dLng := lng2Rad - lng1Rad
	a := math.Pow(math.Sin(dLat/2), 2) + math.Cos(lat1Rad)*math.Cos(lat2Rad)*math.Pow(math.Sin(dLng/2), 2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadiusKm * c
	
	// 保留两位小数
	return math.Round(distance*100) / 100
}

// FindNearbyMerchants 查找指定位置附近的商家
func (s *MerchantLocationServiceImpl) FindNearbyMerchants(ctx context.Context, latitude, longitude float64, radiusKm float64, limit int) ([]*MerchantLocationInfo, error) {
	// 获取所有正常状态的商家
	merchants, err := s.merchantRepo.FindByStatus(ctx, models.MerchantStatusApproved)
	if err != nil {
		logs.Error("查询商家列表失败: %v", err)
		return nil, err
	}
	
	if len(merchants) == 0 {
		return []*MerchantLocationInfo{}, nil
	}
	
	// 计算距离并筛选满足条件的商家
	var result []*MerchantLocationInfo
	for _, merchant := range merchants {
		// 跳过位置信息不完整的商家
		if merchant.Latitude == 0 && merchant.Longitude == 0 {
			continue
		}
		
		// 计算距离
		distance := s.CalculateDistance(latitude, longitude, merchant.Latitude, merchant.Longitude)
		
		// 如果在指定半径内，加入结果集
		if distance <= radiusKm {
			result = append(result, &MerchantLocationInfo{
				MerchantID:   merchant.ID,
				MerchantName: merchant.Name,
				Address:      merchant.Address,
				Latitude:     merchant.Latitude,
				Longitude:    merchant.Longitude,
				Distance:     distance,
			})
			
			// 如果结果数量达到限制，提前返回
			if limit > 0 && len(result) >= limit {
				break
			}
		}
	}
	
	return result, nil
}
