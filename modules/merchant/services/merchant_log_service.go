/**
 * 商家日志服务实现
 *
 * 该文件实现了商家日志服务接口，提供日志记录和查询功能。
 * 商家日志是系统安全审计的重要组成部分，记录商家及管理员对商家的各项操作。
 */

package services

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/repositories"
)

// MerchantLogServiceImpl 商家日志服务实现
type MerchantLogServiceImpl struct {
	merchantLogRepo repositories.MerchantLogRepository
}

// NewMerchantLogService 创建商家日志服务实例
func NewMerchantLogService() MerchantLogService {
	return &MerchantLogServiceImpl{
		merchantLogRepo: repositories.NewMerchantLogRepository(),
	}
}

// CreateLog 创建商家日志
func (s *MerchantLogServiceImpl) CreateLog(ctx context.Context, req *dto.MerchantLogCreateRequest) (int64, error) {
	// 创建日志对象
	merchantLog := &models.MerchantLog{
		MerchantID:    req.MerchantID,
		OperatorID:    req.OperatorID,
		OperatorType:  req.OperatorType,
		OperationType: req.OperationType,
		Content:       req.Content,
		RequestURL:    req.RequestURL,
		RequestData:   req.RequestData,
		IP:            req.IP,
		UserAgent:     req.UserAgent,
		CreatedAt:     time.Now(),
	}

	// 调用仓库创建日志
	id, err := s.merchantLogRepo.Create(ctx, merchantLog)
	if err != nil {
		logs.Error("创建商家日志失败: %v", err)
		return 0, fmt.Errorf("创建日志失败: %v", err)
	}

	return id, nil
}

// GetLogByID 获取日志详情
func (s *MerchantLogServiceImpl) GetLogByID(ctx context.Context, id int64) (*dto.MerchantLogResponse, error) {
	// 调用仓库获取日志
	merchantLog, err := s.merchantLogRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商家日志失败: %v", err)
		return nil, fmt.Errorf("获取日志失败: %v", err)
	}

	if merchantLog == nil {
		return nil, fmt.Errorf("日志不存在")
	}

	// 转换为响应对象
	return convertToMerchantLogResponse(merchantLog), nil
}

// ListLogs 获取日志列表
func (s *MerchantLogServiceImpl) ListLogs(ctx context.Context, req *dto.MerchantLogQueryRequest) ([]*dto.MerchantLogResponse, int64, error) {
	// 构建查询条件
	query := make(map[string]interface{})
	if req.MerchantID > 0 {
		query["merchant_id"] = req.MerchantID
	}
	if req.OperatorID > 0 {
		query["operator_id"] = req.OperatorID
	}
	if req.OperatorType > 0 {
		query["operator_type"] = req.OperatorType
	}
	if req.OperationType > 0 {
		query["operation_type"] = req.OperationType
	}
	if req.IP != "" {
		query["ip"] = req.IP
	}

	// 时间范围查询
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		query["created_at__gte"] = req.StartTime
		query["created_at__lte"] = req.EndTime
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询数据
	logs, total, err := s.merchantLogRepo.List(ctx, query, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日志列表失败: %v", err)
	}

	// 转换为响应对象
	var responses []*dto.MerchantLogResponse
	for _, merchantLog := range logs {
		responses = append(responses, convertToMerchantLogResponse(merchantLog))
	}

	return responses, total, nil
}

// GetLatestLogsByMerchantID 获取商家最近的日志
func (s *MerchantLogServiceImpl) GetLatestLogsByMerchantID(ctx context.Context, merchantID int64, limit int) ([]*dto.MerchantLogResponse, error) {
	if limit <= 0 {
		limit = 10
	}

	// 调用仓库查询
	logs, err := s.merchantLogRepo.GetLatestByMerchantID(ctx, merchantID, limit)
	if err != nil {
		return nil, fmt.Errorf("获取最近日志失败: %v", err)
	}

	// 转换为响应对象
	var responses []*dto.MerchantLogResponse
	for _, merchantLog := range logs {
		responses = append(responses, convertToMerchantLogResponse(merchantLog))
	}

	return responses, nil
}

// GetBusinessStats 获取商家营业统计信息
func (s *MerchantLogServiceImpl) GetBusinessStats(ctx context.Context, merchantID int64, period string) (*dto.BusinessStatsResponse, error) {
	// 解析统计周期
	daysCount := 0
	if len(period) >= 2 && period[len(period)-1] == 'd' {
		// 解析天数
		var days int
		_, err := fmt.Sscanf(period[:len(period)-1], "%d", &days)
		if err != nil || days <= 0 {
			return nil, fmt.Errorf("无效的统计周期格式: %s", period)
		}
		daysCount = days
	} else {
		return nil, fmt.Errorf("无效的统计周期格式: %s，应为Nd格式，如7d表示7天", period)
	}

	// 计算查询时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -daysCount)

	// 获取所有营业相关日志（开始营业和结束营业）
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.MerchantLog)).
		Filter("merchant_id", merchantID).
		Filter("created_at__gte", startTime).
		Filter("created_at__lte", endTime).
		Filter("operation_type__in", []int{models.MerchantLogTypeOpenBusiness, models.MerchantLogTypeCloseBusiness}).
		OrderBy("created_at")

	var businessLogs []*models.MerchantLog
	_, err := qs.All(&businessLogs)
	if err != nil {
		logs.Error("查询商家营业日志失败: %v", err)
		return nil, fmt.Errorf("查询商家营业日志失败: %v", err)
	}

	// 初始化统计结果
	result := &dto.BusinessStatsResponse{
		Period:     period,
		DaysCount:  daysCount,
		DailyStats: make([]*dto.BusinessDailyStat, 0), // 确保初始化为空切片而不是nil
	}

	// 如果没有日志记录，直接返回初始统计结果
	if len(businessLogs) == 0 {
		return result, nil
	}

	// 初始化每日统计的map
	dailyStatsMap := make(map[string]*dto.BusinessDailyStat)

	// 初始化日期范围
	for d := 0; d < daysCount; d++ {
		date := startTime.AddDate(0, 0, d).Format("2006-01-02")
		dailyStatsMap[date] = &dto.BusinessDailyStat{
			Date:      date,
			Duration:  0,
			OpenTimes: 0,
		}
	}

	// 调试日志：打印初始化后的日期范围
	logs.Info("初始化日期范围: %v", dailyStatsMap)

	// 统计营业信息
	var currentOpenTime *time.Time // 当前开始时间指针

	// 遍历日志，提取营业时间
	for _, log := range businessLogs {
		// 获取日志日期
		logDate := log.CreatedAt.Format("2006-01-02")

		// 获取或创建当天的统计
		dailyStat, exists := dailyStatsMap[logDate]
		if !exists {
			// 如果日期不在统计范围内，跳过
			continue
		}

		if log.OperationType == models.MerchantLogTypeOpenBusiness {
			// 开始营业
			currentOpenTime = &log.CreatedAt
			result.TotalOpenTimes++
			dailyStat.OpenTimes++
		} else if log.OperationType == models.MerchantLogTypeCloseBusiness && currentOpenTime != nil {
			// 结束营业
			// 计算此次营业时长（小时）
			duration := log.CreatedAt.Sub(*currentOpenTime).Hours()

			// 更新总营业时长
			result.TotalDuration += duration
			result.CompleteOpenTimes++

			// 更新当天的营业时长
			dailyStat.Duration += duration

			// 重置当前开始时间
			currentOpenTime = nil
		}
	}

	// 统计未完成的营业记录（有开始无结束）
	result.IncompleteOpenTimes = result.TotalOpenTimes - result.CompleteOpenTimes

	// 计算日均营业时长和平均开店次数
	if daysCount > 0 {
		result.AverageDuration = result.TotalDuration / float64(daysCount)
		result.AverageOpenTimePerDay = float64(result.TotalOpenTimes) / float64(daysCount)
	}

	// 确保 DailyStats 被正确初始化
	if result.DailyStats == nil {
		result.DailyStats = make([]*dto.BusinessDailyStat, 0, len(dailyStatsMap))
	}

	// 清空现有的 DailyStats
	result.DailyStats = result.DailyStats[:0]

	// 将每日统计数据添加到结果中
	for _, stat := range dailyStatsMap {
		// 创建一个新的指针指向原始数据
		statCopy := *stat
		result.DailyStats = append(result.DailyStats, &statCopy)
	}

	// 调试日志：打印转换前的数据
	// for i, stat := range result.DailyStats {
	// 	logs.Info("转换前的每日统计数据[%d]: %+v", i, stat)
	// }

	// 按日期排序
	sort.Slice(result.DailyStats, func(i, j int) bool {
		return result.DailyStats[i].Date < result.DailyStats[j].Date
	})

	// 调试日志：打印排序后的数据
	// for i, stat := range result.DailyStats {
	// 	logs.Info("排序后的每日统计数据[%d]: %+v", i, stat)
	// }

	// 确保 DailyStats 不是 nil
	if result.DailyStats == nil {
		logs.Warn("Warning: DailyStats is still nil after processing, initializing empty slice")
		result.DailyStats = make([]*dto.BusinessDailyStat, 0)
	}

	return result, nil
}

// convertToMerchantLogResponse 将日志模型转换为响应对象
func convertToMerchantLogResponse(log *models.MerchantLog) *dto.MerchantLogResponse {
	if log == nil {
		return nil
	}

	// 获取操作类型名称
	operationName := getOperationTypeName(log.OperationType)

	// 返回响应对象
	return &dto.MerchantLogResponse{
		ID:            log.ID,
		MerchantID:    log.MerchantID,
		OperatorID:    log.OperatorID,
		OperatorType:  log.OperatorType,
		OperationType: log.OperationType,
		OperationName: operationName,
		Content:       log.Content,
		RequestURL:    log.RequestURL,
		RequestData:   log.RequestData,
		IP:            log.IP,
		UserAgent:     log.UserAgent,
		CreatedAt:     log.CreatedAt,
	}
}

// getOperationTypeName 获取操作类型名称
func getOperationTypeName(operationType int) string {
	switch operationType {
	case models.MerchantLogTypeLogin:
		return "登录"
	case models.MerchantLogTypeUpdate:
		return "更新信息"
	case models.MerchantLogTypePassword:
		return "修改密码"
	case models.MerchantLogTypeAudit:
		return "审核"
	case models.MerchantLogTypeBalance:
		return "余额变更"
	case models.MerchantLogTypeStatusChange:
		return "状态变更"
	case models.MerchantLogTypeOpenBusiness:
		return "开始营业"
	case models.MerchantLogTypeCloseBusiness:
		return "结束营业"
	default:
		return "未知操作"
	}
}
