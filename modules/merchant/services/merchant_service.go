/**
 * 商家服务实现
 *
 * 该文件实现了商家服务接口，提供商家登录、创建、管理等业务逻辑。
 * 商家服务是电商平台的核心服务之一，负责商家身份认证和信息管理。
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/repositories"
	"o_mall_backend/utils"
	"o_mall_backend/utils/redis"
	"o_mall_backend/utils/sms"
)

// MerchantServiceImpl 商家服务实现
type MerchantServiceImpl struct {
	merchantRepo repositories.MerchantRepository
}

// NewMerchantService 创建商家服务实例
func NewMerchantService() MerchantService {
	return &MerchantServiceImpl{
		merchantRepo: repositories.NewMerchantRepository(),
	}
}

// Login 商家登录
// 处理商家登录流程，包括身份验证和JWT令牌生成
func (s *MerchantServiceImpl) Login(ctx context.Context, req *dto.MerchantLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error) {
	// 查询商家
	merchant, err := s.merchantRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}
	if merchant == nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 检查商家状态
	if merchant.Status != models.MerchantStatusApproved {
		if merchant.Status == models.MerchantStatusPending {
			return nil, errors.New("商家账号审核中，请耐心等待")
		} else if merchant.Status == models.MerchantStatusRejected {
			return nil, errors.New("商家账号审核被拒绝，原因: " + merchant.RejectReason)
		} else {
			return nil, errors.New("商家账号已被禁用")
		}
	}

	// 验证密码
	if !utils.ValidatePassword(req.Password, merchant.Password) {
		return nil, errors.New("用户名或密码错误")
	}

	// 更新登录信息
	if err := s.merchantRepo.UpdateLoginInfo(ctx, merchant.ID, loginIP); err != nil {
		logs.Error("更新登录信息失败: %v", err)
		// 登录信息更新失败不影响登录流程，继续执行
	}

	// 生成JWT令牌对
	tokenPair, err := utils.GenerateTokenPair(merchant.ID, merchant.Username, "merchant")
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 存储refresh token
	err = utils.StoreRefreshToken(merchant.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储refresh token失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	merchantResp := convertToMerchantResponse(merchant)
	loginResp := &dto.MerchantLoginResponse{
		TokenInfo: dto.TokenResponse{
			AccessToken:  tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresIn:    tokenPair.ExpiresIn,
			TokenType:    "Bearer",
		},
		Merchant: *merchantResp,
	}

	return loginResp, nil
}

// LoginByMobile 商家手机号登录
// 处理商家手机号登录流程，包括身份验证和JWT令牌生成
func (s *MerchantServiceImpl) LoginByMobile(ctx context.Context, req *dto.MerchantMobileLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error) {
	// 查询商家
	merchant, err := s.merchantRepo.GetByMobile(ctx, req.Mobile)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}
	if merchant == nil {
		return nil, errors.New("手机号或密码错误")
	}

	// 检查商家状态
	if merchant.Status != models.MerchantStatusApproved {
		if merchant.Status == models.MerchantStatusPending {
			return nil, errors.New("商家账号审核中，请耐心等待")
		} else if merchant.Status == models.MerchantStatusRejected {
			return nil, errors.New("商家账号审核被拒绝，原因: " + merchant.RejectReason)
		} else {
			return nil, errors.New("商家账号已被禁用")
		}
	}

	// 验证密码
	if !utils.ValidatePassword(req.Password, merchant.Password) {
		return nil, errors.New("手机号或密码错误")
	}

	// 更新登录信息
	if err := s.merchantRepo.UpdateLoginInfo(ctx, merchant.ID, loginIP); err != nil {
		logs.Error("更新登录信息失败: %v", err)
		// 登录信息更新失败不影响登录流程，继续执行
	}

	// 生成JWT令牌对
	tokenPair, err := utils.GenerateTokenPair(merchant.ID, merchant.Username, "merchant")
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 存储refresh token
	err = utils.StoreRefreshToken(merchant.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储refresh token失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	merchantResp := convertToMerchantResponse(merchant)
	loginResp := &dto.MerchantLoginResponse{
		TokenInfo: dto.TokenResponse{
			AccessToken:  tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresIn:    tokenPair.ExpiresIn,
			TokenType:    "Bearer",
		},
		Merchant: *merchantResp,
	}

	return loginResp, nil
}

// CreateMerchant 创建商家
// 创建新的商家账号，包括参数验证和密码加密
func (s *MerchantServiceImpl) CreateMerchant(ctx context.Context, req *dto.CreateMerchantRequest) (int64, error) {
	// 检查用户名是否已存在
	existMerchant, err := s.merchantRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		logs.Error("查询用户名是否存在失败: %v", err)
		return 0, err
	}
	if existMerchant != nil {
		return 0, errors.New("用户名已存在")
	}

	// 加密密码
	hashedPassword, err := utils.EncryptPassword(req.Password)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return 0, err
	}

	// 创建商家对象
	merchant := &models.Merchant{
		Name:            req.Name,
		Logo:            req.Logo,
		Description:     req.Description,
		CategoryID:      req.CategoryID, // 添加商家分类ID
		Username:        req.Username,
		Password:        hashedPassword,
		ContactName:     req.ContactName,
		ContactMobile:   req.ContactMobile,
		ContactEmail:    req.ContactEmail,
		BusinessLicense: req.BusinessLicense,
		Address:         req.Address,
		Longitude:       req.Longitude, // 添加经度坐标
		Latitude:        req.Latitude,  // 添加纬度坐标
		Level:           models.MerchantLevelNormal,
		Balance:         0.00,
		IsRecommended:   req.IsRecommended, // 添加是否推荐字段
		Status:          models.MerchantStatusPending, // 默认待审核状态
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 保存商家信息
	merchantID, err := s.merchantRepo.Create(ctx, merchant)
	if err != nil {
		logs.Error("创建商家失败: %v", err)
		return 0, err
	}

	return merchantID, nil
}

// GetMerchantByID 获取商家信息
// 根据商家ID获取商家详细信息
func (s *MerchantServiceImpl) GetMerchantByID(ctx context.Context, id int64) (*dto.MerchantResponse, error) {
	// 查询商家
	merchant, err := s.merchantRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}
	if merchant == nil {
		return nil, errors.New("商家不存在")
	}

	// 转换为响应对象
	return convertToMerchantResponse(merchant), nil
}

// UpdateMerchant 更新商家信息
// 更新商家的基本信息
func (s *MerchantServiceImpl) UpdateMerchant(ctx context.Context, req *dto.UpdateMerchantRequest) error {
	// 查询商家
	merchant, err := s.merchantRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 更新商家信息
	merchant.Name = req.Name
	merchant.Logo = req.Logo
	merchant.Description = req.Description
	merchant.ContactName = req.ContactName
	merchant.ContactMobile = req.ContactMobile
	merchant.ContactEmail = req.ContactEmail

	if req.BusinessLicense != "" {
		merchant.BusinessLicense = req.BusinessLicense
	}
	merchant.Address = req.Address
	merchant.Longitude = req.Longitude // 更新经度
	merchant.Latitude = req.Latitude   // 更新纬度
	merchant.IsRecommended = req.IsRecommended // 更新是否推荐

	// 保存更新
	if err := s.merchantRepo.Update(ctx, merchant); err != nil {
		logs.Error("更新商家信息失败: %v", err)
		return err
	}

	return nil
}

// ChangePassword 修改密码
// 修改商家密码，需要验证原密码
func (s *MerchantServiceImpl) ChangePassword(ctx context.Context, id int64, req *dto.ChangePasswordRequest) error {
	// 查询商家
	merchant, err := s.merchantRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 验证旧密码
	if !utils.ValidatePassword(req.OldPassword, merchant.Password) {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := utils.EncryptPassword(req.NewPassword)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return err
	}

	// 更新密码
	if err := s.merchantRepo.UpdatePassword(ctx, id, hashedPassword); err != nil {
		logs.Error("更新密码失败: %v", err)
		return err
	}

	return nil
}

// ResetPassword 重置密码
// 管理员重置商家密码，无需验证原密码
func (s *MerchantServiceImpl) ResetPassword(ctx context.Context, req *dto.ResetPasswordRequest) error {
	// 查询商家
	merchant, err := s.merchantRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 加密新密码
	hashedPassword, err := utils.EncryptPassword(req.NewPassword)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return err
	}

	// 更新密码
	if err := s.merchantRepo.UpdatePassword(ctx, req.ID, hashedPassword); err != nil {
		logs.Error("重置密码失败: %v", err)
		return err
	}

	return nil
}

// ListMerchants 获取商家列表
// 根据查询条件分页获取商家列表
func (s *MerchantServiceImpl) ListMerchants(ctx context.Context, req *dto.MerchantQueryRequest) ([]*dto.MerchantResponse, int64, error) {
	// 构建查询条件
	query := make(map[string]interface{})
	if req.Name != "" {
		query["name"] = req.Name
	}
	if req.Status != -1 {
		query["status"] = req.Status
	}
	if req.Level != -1 {
		query["level"] = req.Level
	}
	if req.IsRecommended != -1 {
		query["is_recommended"] = req.IsRecommended
	}
	if req.AuditStatus != -1 {
		query["audit_status"] = req.AuditStatus
	}
	if req.OperationStatus != -1 {
		query["operation_status"] = req.OperationStatus
	}

	// 处理经纬度查询参数
	if req.MinLongitude != 0 {
		query["min_longitude"] = req.MinLongitude
	}
	if req.MaxLongitude != 0 {
		query["max_longitude"] = req.MaxLongitude
	}
	if req.MinLatitude != 0 {
		query["min_latitude"] = req.MinLatitude
	}
	if req.MaxLatitude != 0 {
		query["max_latitude"] = req.MaxLatitude
	}

	// 设置默认分页参数
	page := req.Page
	if page < 1 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize < 1 {
		pageSize = 10
	}

	// 查询商家列表
	merchants, total, err := s.merchantRepo.List(ctx, query, page, pageSize)
	if err != nil {
		logs.Error("查询商家列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	merchantResponses := make([]*dto.MerchantResponse, 0, len(merchants))
	for _, merchant := range merchants {
		merchantResponses = append(merchantResponses, convertToMerchantResponse(merchant))
	}

	return merchantResponses, total, nil
}

// AuditMerchant 审核商家
// 审核商家注册申请
func (s *MerchantServiceImpl) AuditMerchant(ctx context.Context, req *dto.AuditMerchantRequest) error {
	// 查询商家
	merchant, err := s.merchantRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 验证状态是否合法
	if req.AuditStatus != models.MerchantStatusApproved && req.AuditStatus != models.MerchantStatusRejected {
		return errors.New("无效的审核状态")
	}

	// 如果是拒绝状态，必须有拒绝原因
	if req.AuditStatus == models.MerchantStatusRejected && req.RejectReason == "" {
		return errors.New("请提供拒绝原因")
	}

	// 更新状态
	if err := s.merchantRepo.UpdateStatus(ctx, req.ID, req.AuditStatus, req.RejectReason); err != nil {
		logs.Error("审核商家失败: %v", err)
		return err
	}

	return nil
}

// DeleteMerchant 删除商家
// 从系统中删除商家账号
func (s *MerchantServiceImpl) DeleteMerchant(ctx context.Context, id int64) error {
	// 查询商家
	merchant, err := s.merchantRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 删除商家
	if err := s.merchantRepo.Delete(ctx, id); err != nil {
		logs.Error("删除商家失败: %v", err)
		return err
	}

	return nil
}

// UpdateBalance 更新余额
// 增加或减少商家账户余额
func (s *MerchantServiceImpl) UpdateBalance(ctx context.Context, req *dto.UpdateBalanceRequest) error {
	// 查询商家
	merchant, err := s.merchantRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 检查余额变动是否合法（如果是减少余额，确保余额足够）
	if req.Amount < 0 && merchant.Balance < -req.Amount {
		return errors.New("商家余额不足")
	}

	// 更新余额
	if err := s.merchantRepo.UpdateBalance(ctx, req.ID, req.Amount); err != nil {
		logs.Error("更新商家余额失败: %v", err)
		return err
	}

	// TODO: 记录余额变动日志

	return nil
}

// RefreshToken 刷新访问令牌
// 使用有效的刷新令牌生成新的访问令牌和刷新令牌
func (s *MerchantServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*dto.TokenResponse, error) {
	// 验证刷新令牌
	claims, err := utils.ValidateRefreshToken(refreshToken)
	if err != nil {
		logs.Error("刷新令牌验证失败: %v", err)
		return nil, errors.New("无效的刷新令牌")
	}

	// 检查刷新令牌是否存在于存储中
	valid, err := utils.CheckRefreshToken(claims.UserID, refreshToken)
	if err != nil || !valid {
		logs.Error("刷新令牌不存在或已失效: %v", err)
		return nil, errors.New("刷新令牌已失效")
	}

	// 查询商家信息
	merchant, err := s.merchantRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}
	if merchant == nil {
		return nil, errors.New("商家不存在")
	}

	// 检查商家状态
	if merchant.Status != models.MerchantStatusApproved {
		return nil, errors.New("商家账号已被禁用或未通过审核")
	}

	// 生成新的令牌对
	tokenPair, err := utils.GenerateTokenPair(merchant.ID, merchant.Username, "merchant")
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 删除旧的刷新令牌
	if err := utils.DeleteRefreshToken(merchant.ID); err != nil {
		logs.Error("删除旧刷新令牌失败: %v", err)
		// 继续执行，不影响主流程
	}

	// 存储新的刷新令牌
	err = utils.StoreRefreshToken(merchant.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储新刷新令牌失败: %v", err)
		return nil, err
	}

	return &dto.TokenResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
		TokenType:    "Bearer",
	}, nil
}

// Logout 商家登出
// 使当前商家的刷新令牌失效
func (s *MerchantServiceImpl) Logout(ctx context.Context, merchantID int64) error {
	// 删除该商家的所有刷新令牌
	err := utils.DeleteAllRefreshTokens(merchantID)
	if err != nil {
		logs.Error("删除商家刷新令牌失败: %v", err)
		return err
	}
	return nil
}

// SendVerificationCode 发送手机验证码
// 为商家登录发送手机验证码，并将验证码保存到Redis
func (s *MerchantServiceImpl) SendVerificationCode(ctx context.Context, req *dto.SendVerificationCodeRequest) (*dto.SendVerificationCodeResponse, error) {
	// 查询商家手机号是否存在
	// 首先验证手机号是否已经注册
	merchant, err := s.merchantRepo.GetByMobile(ctx, req.Mobile)
	if err != nil {
		logs.Error("查询手机号对应商家失败: %v", err)
		return nil, err
	}

	if merchant == nil {
		return nil, errors.New("该手机号未注册，请先注册商家账号")
	}

	// 生成随机验证码
	code := sms.GenerateVerificationCode(6) // 生成6位验证码

	// 获取验证码有效期（分钟）
	expireTime, _ := web.AppConfig.Int("sms::sms_code_expire_time")
	if expireTime <= 0 {
		expireTime = 15 // 默认15分钟
	}

	// 将验证码保存到Redis
	redisKey := fmt.Sprintf("sms:login:merchant:%s", req.Mobile)
	// 设置验证码和过期时间
	err = redis.Set(redisKey, code, time.Duration(expireTime)*time.Minute)
	if err != nil {
		logs.Error("保存验证码到Redis失败: %v", err)
		return nil, errors.New("验证码生成失败，请稍后再试")
	}

	// 发送验证码短信
	err = sms.SendVerificationCode(req.Mobile, code, sms.UsageLogin)
	if err != nil {
		logs.Error("发送验证码短信失败: %v", err)
		// 如果发送失败，删除Redis中的验证码
		_, _ = redis.Del(redisKey)
		return nil, errors.New("验证码发送失败，请稍后再试")
	}

	// 返回响应
	resp := &dto.SendVerificationCodeResponse{
		ExpireTime: expireTime,
	}

	logs.Info("商家登录验证码已发送到手机: %s", req.Mobile)
	return resp, nil
}

// LoginByVerifyCode 商家验证码登录
// 使用手机号和验证码登录商家账号
func (s *MerchantServiceImpl) LoginByVerifyCode(ctx context.Context, req *dto.MerchantVerifyCodeLoginRequest, loginIP string) (*dto.MerchantLoginResponse, error) {
	// 验证手机验证码
	redisKey := fmt.Sprintf("sms:login:merchant:%s", req.Mobile)
	storeCode, err := redis.Get(redisKey)
	if err != nil {
		logs.Error("获取验证码失败: %v", err)
		return nil, errors.New("验证码已过期或不存在，请重新获取")
	}

	// 验证码比对
	if storeCode != req.Code {
		return nil, errors.New("验证码错误")
	}

	// 验证码正确，删除Redis中的验证码，防止重复使用
	_, _ = redis.Del(redisKey)

	// 查询商家
	merchant, err := s.merchantRepo.GetByMobile(ctx, req.Mobile)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return nil, err
	}
	if merchant == nil {
		return nil, errors.New("手机号未注册")
	}

	// 检查商家状态
	if merchant.Status != models.MerchantStatusApproved {
		if merchant.Status == models.MerchantStatusPending {
			return nil, errors.New("商家账号审核中，请耐心等待")
		} else if merchant.Status == models.MerchantStatusRejected {
			return nil, errors.New("商家账号审核被拒绝，原因: " + merchant.RejectReason)
		} else {
			return nil, errors.New("商家账号已被禁用")
		}
	}

	// 更新登录信息
	if err := s.merchantRepo.UpdateLoginInfo(ctx, merchant.ID, loginIP); err != nil {
		logs.Error("更新登录信息失败: %v", err)
		// 登录信息更新失败不影响登录流程，继续执行
	}

	// 生成JWT令牌对
	tokenPair, err := utils.GenerateTokenPair(merchant.ID, merchant.Username, "merchant")
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 存储refresh token
	err = utils.StoreRefreshToken(merchant.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储refresh token失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	merchantResp := convertToMerchantResponse(merchant)
	loginResp := &dto.MerchantLoginResponse{
		TokenInfo: dto.TokenResponse{
			AccessToken:  tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresIn:    tokenPair.ExpiresIn,
			TokenType:    "Bearer",
		},
		Merchant: *merchantResp,
	}

	logs.Info("商家验证码登录成功, 商家ID: %d, 手机号: %s", merchant.ID, req.Mobile)
	return loginResp, nil
}

// UpdateOperationStatus 更新商家经营状态
// 允许商家快速切换不同的经营状态（营业中或休息中）
func (s *MerchantServiceImpl) UpdateOperationStatus(ctx context.Context, merchantID int64, req *dto.UpdateOperationStatusRequest) error {
	// 检查商家是否存在
	merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		logs.Error("[UpdateOperationStatus] 查询商家失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 检查商家账户状态
	if merchant.Status != 1 { // 1表示正常状态
		return errors.New("商家账户状态异常，无法更新经营状态")
	}

	// 验证请求状态值是否有效
	if req.OperationStatus != models.MerchantOperationStatusClosed && req.OperationStatus != models.MerchantOperationStatusOpen {
		return errors.New("无效的经营状态值")
	}

	// 更新经营状态
	merchant.OperationStatus = req.OperationStatus

	// 保存到数据库
	if err := s.merchantRepo.Update(ctx, merchant); err != nil {
		logs.Error("[UpdateOperationStatus] 更新商家经营状态失败: %v", err)
		return err
	}

	// 记录日志（可选）
	logs.Info("[UpdateOperationStatus] 商家ID=%d 更新经营状态为 %d", merchantID, req.OperationStatus)

	return nil
}

// convertToMerchantResponse 将商家模型转换为响应对象
// 内部辅助方法，用于转换数据格式
func convertToMerchantResponse(merchant *models.Merchant) *dto.MerchantResponse {
	if merchant == nil {
		return nil
	}

	// 解析营业时间数据
	var businessHoursResp []dto.BusinessHourResponse
	businessHours, err := merchant.GetBusinessHours()
	if err == nil && len(businessHours) > 0 {
		// 将模型中的BusinessHour转为DTO中的BusinessHourResponse
		for _, hour := range businessHours {
			businessHoursResp = append(businessHoursResp, dto.BusinessHourResponse{
				Weekday:   hour.Weekday,
				StartTime: hour.StartTime,
				EndTime:   hour.EndTime,
			})
		}
	}

	return &dto.MerchantResponse{
		ID:              merchant.ID,
		Name:            merchant.Name,
		Logo:            merchant.Logo,
		Description:     merchant.Description,
		CategoryID:      merchant.CategoryID, // 添加商家分类ID
		Username:        merchant.Username,
		ContactName:     merchant.ContactName,
		ContactMobile:   merchant.ContactMobile,
		ContactEmail:    merchant.ContactEmail,
		BusinessLicense: merchant.BusinessLicense,
		Address:         merchant.Address,
		Longitude:       merchant.Longitude, // 增加经度
		Latitude:        merchant.Latitude,  // 增加纬度
		Level:           merchant.Level,
		Balance:         merchant.Balance,
		AuditStatus:     merchant.AuditStatus,
		Status:          merchant.Status,
		OperationStatus: merchant.OperationStatus, // 增加经营状态
		BusinessHours:   businessHoursResp,        // 增加营业时间
		RejectReason:    merchant.RejectReason,
		LastLoginAt:     merchant.LastLoginAt,
		CreatedAt:       merchant.CreatedAt,
		IsRecommended:   merchant.IsRecommended,   // 增加是否推荐字段
	}
}
