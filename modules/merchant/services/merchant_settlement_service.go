/**
 * 商家结算方式服务
 *
 * 该文件实现了商家结算方式的业务逻辑，提供结算方式的添加、查询、更新等功能。
 * 作为商家管理的重要组成部分，负责处理商家结算账户相关的业务操作。
 */

package services

import (
	"context"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/dto"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/merchant/repositories"
)

// MerchantSettlementService 商家结算方式服务接口
type MerchantSettlementService interface {
	// CreateSettlement 创建结算方式
	CreateSettlement(ctx context.Context, req *dto.CreateSettlementRequest) (int64, error)
	
	// GetSettlementByID 根据ID获取结算方式
	GetSettlementByID(ctx context.Context, id int64) (*dto.SettlementResponse, error)
	
	// GetSettlementsByMerchantID 获取商家的所有结算方式
	GetSettlementsByMerchantID(ctx context.Context, merchantID int64) ([]*dto.SettlementResponse, error)
	
	// GetDefaultSettlement 获取商家的默认结算方式
	GetDefaultSettlement(ctx context.Context, merchantID int64) (*dto.SettlementResponse, error)
	
	// UpdateSettlement 更新结算方式
	UpdateSettlement(ctx context.Context, req *dto.UpdateSettlementRequest) error
	
	// DeleteSettlement 删除结算方式
	DeleteSettlement(ctx context.Context, id int64) error
	
	// SetDefaultSettlement 设置默认结算方式
	SetDefaultSettlement(ctx context.Context, req *dto.SetDefaultSettlementRequest) error
}

// MerchantSettlementServiceImpl 商家结算方式服务实现
type MerchantSettlementServiceImpl struct {
	settlementRepo repositories.MerchantSettlementRepository
	merchantRepo   repositories.MerchantRepository
}

// NewMerchantSettlementService 创建商家结算方式服务实例
func NewMerchantSettlementService() MerchantSettlementService {
	return &MerchantSettlementServiceImpl{
		settlementRepo: repositories.NewMerchantSettlementRepository(),
		merchantRepo:   repositories.NewMerchantRepository(),
	}
}

// CreateSettlement 创建结算方式
func (s *MerchantSettlementServiceImpl) CreateSettlement(ctx context.Context, req *dto.CreateSettlementRequest) (int64, error) {
	// 检查商家是否存在
	merchant, err := s.merchantRepo.GetByID(ctx, req.MerchantID)
	if err != nil {
		logs.Error("查询商家失败: %v", err)
		return 0, err
	}
	if merchant == nil {
		return 0, nil
	}
	
	// 创建结算方式模型
	settlement := &models.MerchantSettlement{
		MerchantID:     req.MerchantID,
		Type:           req.Type,
		AccountName:    req.AccountName,
		AccountNumber:  req.AccountNumber,
		BankName:       req.BankName,
		BankBranch:     req.BankBranch,
		Status:         models.SettlementStatusEnabled,
		IsDefault:      req.IsDefault,
		SettlementRate: req.SettlementRate,
		Remark:         req.Remark,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	
	// 创建结算方式
	id, err := s.settlementRepo.Create(ctx, settlement)
	if err != nil {
		logs.Error("创建结算方式失败: %v", err)
		return 0, err
	}
	
	return id, nil
}

// GetSettlementByID 根据ID获取结算方式
func (s *MerchantSettlementServiceImpl) GetSettlementByID(ctx context.Context, id int64) (*dto.SettlementResponse, error) {
	// 查询结算方式
	settlement, err := s.settlementRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询结算方式失败: %v", err)
		return nil, err
	}
	if settlement == nil {
		return nil, nil
	}
	
	// 转换为响应对象
	resp := convertToSettlementResponse(settlement)
	return resp, nil
}

// GetSettlementsByMerchantID 获取商家的所有结算方式
func (s *MerchantSettlementServiceImpl) GetSettlementsByMerchantID(ctx context.Context, merchantID int64) ([]*dto.SettlementResponse, error) {
	// 查询结算方式列表
	settlements, err := s.settlementRepo.GetByMerchantID(ctx, merchantID)
	if err != nil {
		logs.Error("查询商家结算方式列表失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	respList := make([]*dto.SettlementResponse, 0, len(settlements))
	for _, settlement := range settlements {
		resp := convertToSettlementResponse(settlement)
		respList = append(respList, resp)
	}
	
	return respList, nil
}

// GetDefaultSettlement 获取商家的默认结算方式
func (s *MerchantSettlementServiceImpl) GetDefaultSettlement(ctx context.Context, merchantID int64) (*dto.SettlementResponse, error) {
	// 查询默认结算方式
	settlement, err := s.settlementRepo.GetDefaultByMerchantID(ctx, merchantID)
	if err != nil {
		logs.Error("查询商家默认结算方式失败: %v", err)
		return nil, err
	}
	if settlement == nil {
		return nil, nil
	}
	
	// 转换为响应对象
	resp := convertToSettlementResponse(settlement)
	return resp, nil
}

// UpdateSettlement 更新结算方式
func (s *MerchantSettlementServiceImpl) UpdateSettlement(ctx context.Context, req *dto.UpdateSettlementRequest) error {
	// 查询结算方式
	settlement, err := s.settlementRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询结算方式失败: %v", err)
		return err
	}
	if settlement == nil {
		return nil
	}
	
	// 更新结算方式
	settlement.Type = req.Type
	settlement.AccountName = req.AccountName
	settlement.AccountNumber = req.AccountNumber
	settlement.BankName = req.BankName
	settlement.BankBranch = req.BankBranch
	settlement.Status = req.Status
	settlement.IsDefault = req.IsDefault
	settlement.SettlementRate = req.SettlementRate
	settlement.Remark = req.Remark
	settlement.UpdatedAt = time.Now()
	
	// 保存更新
	err = s.settlementRepo.Update(ctx, settlement)
	if err != nil {
		logs.Error("更新结算方式失败: %v", err)
		return err
	}
	
	return nil
}

// DeleteSettlement 删除结算方式
func (s *MerchantSettlementServiceImpl) DeleteSettlement(ctx context.Context, id int64) error {
	// 删除结算方式
	err := s.settlementRepo.Delete(ctx, id)
	if err != nil {
		logs.Error("删除结算方式失败: %v", err)
		return err
	}
	
	return nil
}

// SetDefaultSettlement 设置默认结算方式
func (s *MerchantSettlementServiceImpl) SetDefaultSettlement(ctx context.Context, req *dto.SetDefaultSettlementRequest) error {
	// 设置默认结算方式
	err := s.settlementRepo.SetDefault(ctx, req.MerchantID, req.SettlementID)
	if err != nil {
		logs.Error("设置默认结算方式失败: %v", err)
		return err
	}
	
	return nil
}

// convertToSettlementResponse 将结算方式模型转换为响应对象
func convertToSettlementResponse(settlement *models.MerchantSettlement) *dto.SettlementResponse {
	if settlement == nil {
		return nil
	}
	
	resp := &dto.SettlementResponse{
		ID:             settlement.ID,
		MerchantID:     settlement.MerchantID,
		Type:           settlement.Type,
		TypeDesc:       dto.SettlementTypeDesc[settlement.Type],
		AccountName:    settlement.AccountName,
		AccountNumber:  settlement.AccountNumber,
		BankName:       settlement.BankName,
		BankBranch:     settlement.BankBranch,
		Status:         settlement.Status,
		IsDefault:      settlement.IsDefault,
		SettlementRate: settlement.SettlementRate,
		Remark:         settlement.Remark,
		CreatedAt:      settlement.CreatedAt,
		UpdatedAt:      settlement.UpdatedAt,
	}
	
	return resp
}
