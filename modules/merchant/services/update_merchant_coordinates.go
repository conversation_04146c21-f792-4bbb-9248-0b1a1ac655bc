/**
 * 更新商户经纬度坐标服务实现
 *
 * 该文件实现了商户经纬度坐标更新的业务逻辑处理。
 */

package services

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/dto"
)

// UpdateMerchantCoordinates 更新商户经纬度坐标
// 专门用于更新商户的经纬度坐标信息
func (s *MerchantServiceImpl) UpdateMerchantCoordinates(ctx context.Context, req *dto.UpdateMerchantRequest) error {
	// 检查商户是否存在
	merchant, err := s.merchantRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("[UpdateMerchantCoordinates] 查询商户失败: %v", err)
		return err
	}
	if merchant == nil {
		return errors.New("商户不存在")
	}

	// 仅更新经纬度信息
	merchant.Longitude = req.Longitude
	merchant.Latitude = req.Latitude
	merchant.UpdatedAt = time.Now()

	// 只更新经纬度字段
	err = s.merchantRepo.Update(ctx, merchant)
	if err != nil {
		logs.Error("[UpdateMerchantCoordinates] 更新商户经纬度坐标失败: %v", err)
		return err
	}

	logs.Info("[UpdateMerchantCoordinates] 商户ID=%d 更新经纬度坐标成功，经度: %f, 纬度: %f", 
		req.ID, req.Longitude, req.Latitude)
	return nil
}
