# 订单模块 (Order Module)

## 模块概述

订单模块是O_Mall多商家电商平台的核心组件之一，负责处理订单的完整生命周期管理，包括订单创建、支付、发货、收货、取消以及退款等流程。本模块提供了一套完整的订单管理解决方案，支持多种支付方式和配送方式，实现了订单从创建到完成的全过程跟踪和管理。

## 模块架构

订单模块采用了清晰的分层架构，主要包含以下组件：

```
modules/order/
├── controllers/       # 控制器层：处理HTTP请求
│   ├── order_controller.go        # 订单基本操作控制器
│   └── order_payment_controller.go # 订单支付相关控制器
├── services/          # 服务层：实现业务逻辑
│   ├── interface.go              # 服务接口定义
│   ├── order_service.go          # 订单服务实现
│   └── impl/                     # 服务实现目录
│       └── order_payment_service_impl.go # 支付服务实现
├── repositories/      # 仓库层：数据访问
│   └── order_repository.go       # 订单数据访问实现
├── models/            # 数据模型层
│   └── order.go                  # 订单相关数据模型
├── dto/               # 数据传输对象
│   ├── order_dto.go              # 订单DTO定义
│   └── payment_dto.go            # 支付相关DTO定义
├── constants/         # 常量定义
│   └── order_constants.go        # 订单相关常量
├── routers/           # 路由配置
│   └── router.go                 # 订单模块路由
└── init.go            # 模块初始化入口
```

## 主要功能

### 基本订单功能

1. **订单创建与管理**
   - 创建订单：支持多商品、多SKU、多配送方式
   - 订单详情查询：获取订单完整信息
   - 订单列表查询：支持多种筛选条件和分页
   - 订单状态管理：支持订单状态全流程追踪
   - 订单取消：用户可取消未支付订单

2. **订单配送管理**
   - 订单发货：商家发货操作
   - 确认收货：用户确认收到商品
   - 物流信息跟踪：记录和显示物流状态

3. **订单完成与评价**
   - 订单完成：订单流程结束
   - 订单评价入口：关联订单评价系统

4. **订单统计与日志**
   - 订单统计信息：提供订单总量、金额等统计数据
   - 订单操作日志：记录订单状态变更历史

### 支付相关功能

1. **多种支付方式**
   - 支持微信支付、支付宝、余额支付等多种支付方式
   - 统一的支付接口和回调处理

2. **支付流程管理**
   - 发起支付：生成支付参数
   - 支付状态查询：查询订单支付结果
   - 支付回调处理：处理支付平台异步通知

3. **退款管理**
   - 发起退款：对已支付订单进行退款
   - 退款状态查询：查询退款处理进度
   - 退款回调处理：处理退款结果通知

## 数据模型

### 订单主体 (Order)

订单主体模型存储订单的核心信息，包括订单状态、金额、支付信息、配送信息等。

主要字段：
- `ID`：订单ID
- `OrderNo`：订单编号
- `UserID`：用户ID
- `Status`：订单状态（待付款、待发货、待收货、已完成、已取消等）
- `PayStatus`：支付状态
- `TotalAmount`：订单总金额
- `PayAmount`：实际支付金额
- `FreightAmount`：运费
- `DiscountAmount`：优惠金额
- `DeliveryType`：配送方式
- `OrderType`：订单类型
- 其他时间信息：创建时间、支付时间、发货时间、收货时间等

### 订单项 (OrderItem)

订单项模型存储订单中的商品明细信息。

主要字段：
- `ID`：订单项ID
- `OrderID`：关联的订单ID
- `ProductID`：商品ID
- `SkuID`：SKU ID
- `Quantity`：购买数量
- `Price`：实际单价
- `SubtotalAmount`：小计金额
- `DiscountAmount`：优惠金额
- `RefundStatus`：退款状态

### 订单地址 (OrderAddress)

订单地址模型存储订单的收货地址信息。

主要字段：
- `ID`：地址ID
- `OrderID`：关联的订单ID
- `ReceiverName`：收货人姓名
- `ReceiverPhone`：收货人电话
- `Province`：省份
- `City`：城市
- `District`：区/县
- `Detail`：详细地址

### 订单日志 (OrderLog)

订单日志模型记录订单状态变更历史。

主要字段：
- `ID`：日志ID
- `OrderID`：关联的订单ID
- `Action`：操作类型
- `Status`：操作后的订单状态
- `Content`：日志内容
- `CreatedAt`：创建时间

### 订单支付 (OrderPayment)

订单支付模型记录订单的支付信息。

主要字段：
- `ID`：支付ID
- `OrderID`：关联的订单ID
- `PaymentNo`：支付流水号
- `PaymentMethod`：支付方式
- `PaymentAmount`：支付金额
- `PaymentStatus`：支付状态
- `TransactionID`：交易ID
- `PaymentTime`：支付时间

## API接口

### 订单基本操作

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/orders/create` | POST | 创建订单 |
| `/api/v1/orders/:id` | GET | 获取订单详情 |
| `/api/v1/orders/list` | POST | 获取订单列表 |
| `/api/v1/orders/:id/cancel` | POST | 取消订单 |
| `/api/v1/orders/:id/ship` | POST | 订单发货 |
| `/api/v1/orders/:id/receive` | POST | 确认收货 |
| `/api/v1/orders/:id/complete` | POST | 完成订单 |
| `/api/v1/orders/:id` | DELETE | 删除订单 |
| `/api/v1/orders/statistics` | GET | 获取订单统计信息 |
| `/api/v1/orders/:id/logs` | GET | 获取订单日志 |

### 订单支付相关

| 接口路径 | 方法 | 功能描述 |
|---------|------|--------|
| `/api/v1/orders/:id/pay` | POST | 支付订单 |
| `/api/orders/:order_id/payment` | POST | 发起支付 |
| `/api/orders/:order_id/payment/status` | GET | 查询支付状态 |
| `/api/payment/callback/:method/:order_no` | POST | 处理支付回调 |
| `/api/orders/:order_id/refund` | POST | 发起退款 |
| `/api/refund/callback/:method/:order_no` | POST | 处理退款回调 |

## 支付流程

### 发起支付

1. 用户选择商品下单，系统生成订单
2. 用户选择支付方式，调用支付接口
3. 系统生成支付参数，前端根据参数调起支付
4. 用户完成支付操作

### 支付结果处理

1. 支付平台异步通知支付结果
2. 系统接收回调并验证签名
3. 系统更新订单支付状态
4. 触发后续业务流程（发货准备等）

### 退款处理

1. 用户申请退款
2. 系统验证退款条件
3. 调用支付平台退款接口
4. 接收退款结果回调
5. 更新订单退款状态

## 常量定义

订单模块定义了多种状态常量，用于标识订单在不同阶段的状态：

### 订单状态
- `OrderStatusPending = 10`：待付款
- `OrderStatusPaid = 20`：已付款
- `OrderStatusShipped = 30`：已发货
- `OrderStatusDelivered = 40`：已收货
- `OrderStatusCompleted = 50`：已完成
- `OrderStatusCancelled = 60`：已取消
- `OrderStatusRefunding = 70`：退款中
- `OrderStatusRefunded = 80`：已退款

### 支付状态
- `PayStatusUnpaid = 0`：未支付
- `PayStatusPaid = 1`：已支付
- `PayStatusRefunding = 2`：退款中
- `PayStatusRefunded = 3`：已退款
- `PayStatusFailed = 4`：支付失败

### 配送方式
- `DeliveryTypeExpress = 1`：快递配送
- `DeliveryTypeSelf = 2`：自提
- `DeliveryTypeLocal = 3`：同城配送

## 使用示例

### 创建订单

```go
// 构建创建订单请求
createOrderReq := &dto.CreateOrderRequest{
    UserID: 10001,
    Items: []*dto.OrderItemRequest{
        {
            ProductID: 1001,
            SkuID:     2001,
            Quantity:  2,
            Price:     99.99,
        },
    },
    Address: &dto.OrderAddressRequest{
        ReceiverName:  "张三",
        ReceiverPhone: "***********",
        Province:      "广东省",
        City:          "深圳市",
        District:      "南山区",
        Detail:        "科技园南路88号",
    },
    PayMethod:    1, // 1:微信支付
    DeliveryType: 1, // 1:快递配送
    Source:       1, // 1:PC端
}

// 调用订单服务创建订单
orderService := services.NewOrderService()
orderResp, err := orderService.CreateOrder(context.Background(), createOrderReq)
if err != nil {
    // 处理错误
    return
}

// 获取订单信息
fmt.Printf("订单创建成功，订单号：%s，订单金额：%.2f\n", 
           orderResp.OrderNo, orderResp.TotalAmount)
```

### 支付订单

```go
// 构建支付请求
payReq := &dto.PayOrderRequest{
    PayMethod: 1, // 1:微信支付
}

// 调用订单服务支付订单
err := orderService.PayOrder(context.Background(), orderId, payReq)
if err != nil {
    // 处理错误
    return
}
```

## 注意事项

1. 订单创建后状态为"待付款"，需要在规定时间内完成支付，否则系统会自动取消订单
2. 订单支付成功后，状态会更新为"待发货"，等待商家发货
3. 订单发货后，用户需要在规定时间内确认收货，超时系统会自动确认收货
4. 订单支付和退款需要与支付模块集成，确保数据一致性
5. 订单状态变更应该记录详细的操作日志，便于问题排查和数据分析

## 依赖模块

订单模块依赖以下其他模块：

1. 用户模块：获取用户信息和地址
2. 商品模块：获取商品信息和价格
3. 支付模块：处理订单支付和退款
4. 配送模块：处理订单物流和配送

## 安全考虑

1. 订单支付相关的API需要进行严格的权限控制和参数验证
2. 支付回调接口需要验证签名，防止伪造回调
3. 订单金额计算需要在服务端进行，不信任前端传入的金额数据
4. 敏感操作需要记录详细的操作日志 