/**
 * 订单相关常量定义
 *
 * 本文件定义了订单模块中的各种常量，包括订单状态、支付状态、订单来源等。
 * 这些常量用于标识订单在不同阶段的状态和类型。
 */

package constants

// 订单状态常量
const (
	// OrderStatusPending 待付款状态
	OrderStatusPending = 10
	// OrderStatusPaid 已付款状态
	OrderStatusPaid = 20
	// OrderStatusShipped 处理中，待发货状态
	OrderStatusShipped = 30
	// OrderStatusDelivered 配送中，已发货状态
	OrderStatusDelivered = 40
	// OrderStatusCompleted 已完成，已收货状态
	OrderStatusCompleted = 50
	// OrderStatusCancelled 已取消状态
	OrderStatusCancelled = 60
	// OrderStatusRefunding 退款中状态
	OrderStatusRefunding = 70
	// OrderStatusRefunded 已退款状态
	OrderStatusRefunded = 80
)

// OrderStatusMap 订单状态映射表
var OrderStatusMap = map[int]string{
	OrderStatusPending:   "待付款",
	OrderStatusPaid:      "已付款",
	OrderStatusShipped:   "处理中，待发货",
	OrderStatusDelivered: "配送中，已发货",
	OrderStatusCompleted: "已完成，已收货",
	OrderStatusCancelled: "已取消",
	OrderStatusRefunding: "退款中",
	OrderStatusRefunded:  "已退款",
}

// 支付状态常量
const (
	// PayStatusUnpaid 未支付
	PayStatusUnpaid = 0
	// PayStatusPaid 已支付
	PayStatusPaid = 1
	// PayStatusRefunding 退款中
	PayStatusRefunding = 2
	// PayStatusRefunded 已退款
	PayStatusRefunded = 3
	// PayStatusFailed 支付失败
	PayStatusFailed = 4
)

// PayStatusMap 支付状态映射表
var PayStatusMap = map[int]string{
	PayStatusUnpaid:    "未支付",
	PayStatusPaid:      "已支付",
	PayStatusRefunding: "退款中",
	PayStatusRefunded:  "已退款",
	PayStatusFailed:    "支付失败",
}

// 订单来源常量
const (
	// OrderSourcePc 电脑端
	OrderSourcePc = 1
	// OrderSourceApp 移动应用
	OrderSourceApp = 2
	// OrderSourceWechat 微信小程序
	OrderSourceWechat = 3
	// OrderSourceOther 其他渠道
	OrderSourceOther = 4
)

// OrderSourceMap 订单来源映射表
var OrderSourceMap = map[int]string{
	OrderSourcePc:     "电脑端",
	OrderSourceApp:    "移动应用",
	OrderSourceWechat: "微信小程序",
	OrderSourceOther:  "其他渠道",
}

// 配送方式常量
const (
	// DeliveryTypeExpress 快递配送
	DeliveryTypeExpress = 1
	// DeliveryTypeSelf 自提
	DeliveryTypeSelf = 2
	// DeliveryTypeLocal 同城配送
	DeliveryTypeLocal = 3
)

// DeliveryTypeMap 配送方式映射表
var DeliveryTypeMap = map[int]string{
	DeliveryTypeExpress: "快递配送",
	DeliveryTypeSelf:    "自提",
	DeliveryTypeLocal:   "同城配送",
}

// 评价状态常量
const (
	// CommentStatusNone 未评价
	CommentStatusNone = 0
	// CommentStatusDone 已评价
	CommentStatusDone = 1
)

// 发票类型常量
const (
	// InvoiceTypeNone 不开发票
	InvoiceTypeNone = 0
	// InvoiceTypePersonal 个人发票
	InvoiceTypePersonal = 1
	// InvoiceTypeCompany 公司发票
	InvoiceTypeCompany = 2
)

// InvoiceTypeMap 发票类型映射表
var InvoiceTypeMap = map[int]string{
	InvoiceTypeNone:     "不开发票",
	InvoiceTypePersonal: "个人发票",
	InvoiceTypeCompany:  "公司发票",
}

// 订单类型常量
const (
	// OrderTypeNormal 普通订单
	OrderTypeNormal = 0
	// OrderTypeGroup 团购订单
	OrderTypeGroup = 1
	// OrderTypePromotion 促销订单
	OrderTypePromotion = 2
)

// OrderTypeMap 订单类型映射表
var OrderTypeMap = map[int]string{
	OrderTypeNormal:    "普通订单",
	OrderTypeGroup:     "团购订单",
	OrderTypePromotion: "促销订单",
}
