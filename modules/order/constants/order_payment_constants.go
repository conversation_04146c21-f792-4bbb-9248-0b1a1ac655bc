/**
 * order_payment_constants.go
 * 订单支付相关常量
 *
 * 本文件定义了订单支付相关的常量，主要包括支付方式相关常量。
 * 支付状态常量已在order_constants.go中定义，避免重复定义。
 */

package constants

// 支付方式
const (
	PayMethodWechat  = 1 // 微信支付
	PayMethodAlipay  = 2 // 支付宝
	PayMethodBalance = 3 // 余额支付
)

// 支付方式映射
var PayMethodMap = map[int]string{
	PayMethodWechat:  "微信支付",
	PayMethodAlipay:  "支付宝",
	PayMethodBalance: "余额支付",
}

// 支付处理中状态（order_constants.go中未定义的状态）
const (
	PayStatusProcessing = 5 // 支付处理中
	PayStatusCancelled  = 6 // 已取消
)
