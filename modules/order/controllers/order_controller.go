/**
 * 订单控制器
 *
 * 本文件实现了订单模块的HTTP控制器，处理与订单相关的所有HTTP请求。
 * 包括订单的创建、查询、支付、取消等API接口实现。
 */

package controllers

import (
	"sort"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/order/dto"
	"o_mall_backend/modules/order/services"
	takeoutServices "o_mall_backend/modules/takeout/services"
	takeoutDto "o_mall_backend/modules/takeout/dto"
	"o_mall_backend/utils/common"
)

// OrderController 订单控制器
type OrderController struct {
	web.Controller
	orderService        services.OrderService
	takeoutOrderService takeoutServices.TakeoutOrderService
}

// Prepare 初始化控制器
func (c *OrderController) Prepare() {
	c.orderService = services.NewOrderService()
	c.takeoutOrderService = takeoutServices.NewTakeoutOrderService()
}

// ParseRequest 通用请求参数解析方法
func (c *OrderController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// CreateOrder 创建订单
// @Title 创建订单
// @Description 创建一个新的订单
// @Param body body dto.CreateOrderRequest true "订单创建请求参数"
// @Success 200 {object} dto.OrderResponse "订单创建成功返回订单信息"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /create [post]
func (c *OrderController) CreateOrder() {
	logs.Info("接收创建订单请求")

	// 解析请求参数
	var req dto.CreateOrderRequest
	err := c.ParseRequest(&req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 这里模拟从JWT Token获取用户ID，实际应该从认证中间件获取
	// userID, err := c.GetSession("user_id").(int64)
	// TODO: 改为从JWT Token获取用户ID
	userID, _ := c.GetInt64("user_id", 0)
	if userID > 0 {
		req.UserID = userID
	}

	// 调用服务创建订单
	resp, err := c.orderService.CreateOrder(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建订单失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, resp)
}

// GetOrder 获取订单详情
// @Title 获取订单详情
// @Description 根据订单ID获取订单详情
// @Param id path int true "订单ID"
// @Success 200 {object} dto.OrderResponse "订单详情"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "订单不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id [get]
func (c *OrderController) GetOrder() {
	logs.Info("接收获取订单详情请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取订单详情
	resp, err := c.orderService.GetOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取订单详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if resp == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, resp)
}

// ListOrders 获取订单列表
// @Title 获取订单列表
// @Description 根据查询条件获取订单列表，支持外卖订单查询
// @Param body body dto.OrderQueryRequest true "订单查询条件"
// @Success 200 {object} dto.OrderListResponse "订单列表"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /list [post]
func (c *OrderController) ListOrders() {
	logs.Info("接收获取订单列表请求")

	// 解析请求参数
	var req dto.OrderQueryRequest
	err := c.ParseRequest(&req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 从JWT Token获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取用户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 设置查询条件中的用户ID
	req.UserID = userID

	// 如果明确指定只查询外卖订单
	if req.Takeout {
		// 调用外卖订单服务获取外卖订单列表
		takeoutResp, err := c.takeoutOrderService.ListOrdersByUserID(req.UserID, 0, req.Page, req.PageSize)
		if err != nil {
			logs.Error("获取外卖订单列表失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInternal)
			return
		}

		// 转换外卖订单数据格式为通用订单格式
		orderItems := make([]*dto.OrderResponse, 0, len(takeoutResp.List))
		for _, takeoutOrder := range takeoutResp.List {
			orderResp := c.convertTakeoutOrderToOrderResponse(takeoutOrder)
			orderItems = append(orderItems, orderResp)
		}

		result.OKWithPagination(c.Ctx, orderItems, int64(takeoutResp.Total), req.Page, req.PageSize)
		return
	}

	// 默认查询所有订单（普通订单 + 外卖订单）
	// 1. 先获取普通订单
	resp, err := c.orderService.ListOrders(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("获取普通订单列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 2. 获取外卖订单（使用较大的页面大小来获取足够的数据进行合并）
	takeoutResp, err := c.takeoutOrderService.ListOrdersByUserID(req.UserID, 0, 1, 100)
	if err != nil {
		logs.Error("获取外卖订单列表失败: %v", err)
		// 如果外卖订单获取失败，仍然返回普通订单
		result.OKWithPagination(c.Ctx, resp.Items, resp.Total, req.Page, req.PageSize)
		return
	}

	// 3. 转换外卖订单为通用格式
	takeoutOrderItems := make([]*dto.OrderResponse, 0, len(takeoutResp.List))
	for _, takeoutOrder := range takeoutResp.List {
		orderResp := c.convertTakeoutOrderToOrderResponse(takeoutOrder)
		takeoutOrderItems = append(takeoutOrderItems, orderResp)
	}

	// 4. 合并两种订单
	allOrders := make([]*dto.OrderResponse, 0, len(resp.Items)+len(takeoutOrderItems))
	allOrders = append(allOrders, resp.Items...)
	allOrders = append(allOrders, takeoutOrderItems...)

	// 5. 按创建时间倒序排序
	sort.Slice(allOrders, func(i, j int) bool {
		return allOrders[i].CreatedAt.After(allOrders[j].CreatedAt)
	})

	// 6. 应用分页
	totalCount := int64(len(allOrders))
	startIndex := (req.Page - 1) * req.PageSize
	endIndex := startIndex + req.PageSize

	if startIndex >= len(allOrders) {
		// 如果起始索引超出范围，返回空列表
		result.OKWithPagination(c.Ctx, []*dto.OrderResponse{}, totalCount, req.Page, req.PageSize)
		return
	}

	if endIndex > len(allOrders) {
		endIndex = len(allOrders)
	}

	pagedOrders := allOrders[startIndex:endIndex]
	result.OKWithPagination(c.Ctx, pagedOrders, totalCount, req.Page, req.PageSize)
}

// convertTakeoutOrderToOrderResponse 将外卖订单转换为通用订单格式
func (c *OrderController) convertTakeoutOrderToOrderResponse(takeoutOrder *takeoutDto.TakeoutOrderDTO) *dto.OrderResponse {
	// 转换订单项
	orderItems := make([]*dto.OrderItemResponse, 0, len(takeoutOrder.Items))
	for _, item := range takeoutOrder.Items {
		orderItem := &dto.OrderItemResponse{
			ID:              item.ID,
			OrderID:         takeoutOrder.OrderID,
			OrderNo:         takeoutOrder.OrderNo,
			ProductID:       item.ProductID,
			ProductName:     item.ProductName,
			ProductImage:    item.Image,
			SkuID:           0, // 外卖订单可能没有SKU概念
			SkuAttributes:   item.SpecText,
			Quantity:        item.Quantity,
			Price:           item.Price,
			OriginalPrice:   item.Price,
			SubtotalAmount:  item.Amount,
			CreatedAt:       takeoutOrder.CreateTime,
			VariantSnapshot: "",
		}
		orderItems = append(orderItems, orderItem)
	}

	// 转换地址信息
	var address *dto.OrderAddressResponse
	if takeoutOrder.DeliveryInfo != nil {
		address = &dto.OrderAddressResponse{
			ReceiverName:  takeoutOrder.DeliveryInfo.ReceiverName,
			ReceiverPhone: takeoutOrder.DeliveryInfo.ReceiverPhone,
			Province:      "", // 外卖订单可能没有省份信息
			City:          "", // 外卖订单可能没有城市信息
			District:      "", // 外卖订单可能没有区县信息
			Detail:        takeoutOrder.DeliveryInfo.DeliveryAddress,
		}
	}

	// 转换支付信息
	var payment *dto.OrderPaymentResponse
	payment = &dto.OrderPaymentResponse{
		OrderID:           takeoutOrder.OrderID,
		OrderNo:           takeoutOrder.OrderNo,
		UserID:            takeoutOrder.UserID,
		PaymentNo:         "", // 外卖订单可能没有支付单号
		PaymentMethod:     0, // 需要转换支付方式
		PaymentMethodText: takeoutOrder.PaymentMethod,
		PaymentAmount:     takeoutOrder.TotalAmount,
		PaymentStatus:     takeoutOrder.PayStatus,
		PaymentStatusText: "", // 外卖订单可能没有支付状态文本
		PaymentTime:       takeoutOrder.PayTime,
		TransactionID:     "", // 外卖订单可能没有交易ID
		CreatedAt:         takeoutOrder.CreateTime,
	}

	// 构造通用订单响应
	orderResp := &dto.OrderResponse{
		ID:               takeoutOrder.OrderID,
		OrderNo:          takeoutOrder.OrderNo,
		UserID:           takeoutOrder.UserID,
		UserName:         "", // 外卖订单可能没有用户名
		OrderType:        1, // 外卖订单类型
		OrderTypeText:    "外卖订单",
		Status:           takeoutOrder.OrderStatus,
		StatusText:       "", // 外卖订单可能没有状态文本
		PayStatus:        takeoutOrder.PayStatus,
		PayStatusText:    "", // 外卖订单可能没有支付状态文本
		PayTime:          takeoutOrder.PayTime,
		PayMethod:        0, // 需要转换支付方式
		PayMethodText:    takeoutOrder.PaymentMethod,
		PayAmount:        takeoutOrder.TotalAmount,
		TotalAmount:      takeoutOrder.TotalAmount,
		FreightAmount:    takeoutOrder.DeliveryFee,
		DiscountAmount:   takeoutOrder.DiscountAmount,
		Remark:           takeoutOrder.Remark,
		Source:           0, // 外卖订单来源
		SourceText:       "外卖订单",
		DeliveryType:     0, // 外卖配送类型
		DeliveryTypeText: "外卖配送",
		DeliveryTime:     takeoutOrder.DeliveryTime,
		ReceiveTime:      takeoutOrder.CompleteTime,
		CancelReason:     takeoutOrder.CancelReason,
		CancelTime:       takeoutOrder.CancelTime,
		RefundTime:       takeoutOrder.RefundTime,
		RefundAmount:     takeoutOrder.RefundAmount,
		Items:            orderItems,
		Address:          address,
		Payment:          payment,
		CreatedAt:        takeoutOrder.CreateTime,
		UpdatedAt:        takeoutOrder.CreateTime, // 外卖订单可能没有UpdatedAt字段
	}

	return orderResp
}

// CancelOrder 取消订单
// @Title 取消订单
// @Description 取消指定ID的订单
// @Param id path int true "订单ID"
// @Param body body dto.CancelOrderRequest true "取消订单请求参数"
// @Success 200 {object} utils.Response "取消订单成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id/cancel [post]
func (c *OrderController) CancelOrder() {
	logs.Info("接收取消订单请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var req dto.CancelOrderRequest
	err = c.ParseRequest(&req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务取消订单
	err = c.orderService.CancelOrder(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("取消订单失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// PayOrder 支付订单
// @Title 支付订单
// @Description 支付指定ID的订单
// @Param id path int true "订单ID"
// @Param body body dto.PayOrderRequest true "支付订单请求参数"
// @Success 200 {object} utils.Response "支付订单成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id/pay [post]
func (c *OrderController) PayOrder() {
	logs.Info("接收支付订单请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var req dto.PayOrderRequest
	err = c.ParseRequest(&req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务支付订单
	err = c.orderService.PayOrder(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("发起支付失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// ShipOrder 订单发货
// @Title 订单发货
// @Description 处理指定ID的订单发货
// @Param id path int true "订单ID"
// @Param body body dto.ShipOrderRequest true "订单发货请求参数"
// @Success 200 {object} utils.Response "订单发货成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id/ship [post]
func (c *OrderController) ShipOrder() {
	logs.Info("接收订单发货请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var req dto.ShipOrderRequest
	err = c.ParseRequest(&req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务处理订单发货
	err = c.orderService.ShipOrder(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("订单发货失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// ConfirmReceive 确认收货
// @Title 确认收货
// @Description 确认指定ID的订单收货
// @Param id path int true "订单ID"
// @Success 200 {object} utils.Response "确认收货成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id/receive [post]
func (c *OrderController) ConfirmReceive() {
	logs.Info("接收确认收货请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务确认收货
	err = c.orderService.ConfirmReceive(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("确认收货失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// CompleteOrder 完成订单
// @Title 完成订单
// @Description 完成指定ID的订单
// @Param id path int true "订单ID"
// @Success 200 {object} utils.Response "完成订单成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id/complete [post]
func (c *OrderController) CompleteOrder() {
	logs.Info("接收完成订单请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务完成订单
	err = c.orderService.CompleteOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("完成订单失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeleteOrder 删除订单
// @Title 删除订单
// @Description 删除指定ID的订单
// @Param id path int true "订单ID"
// @Success 200 {object} utils.Response "删除订单成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id [delete]
func (c *OrderController) DeleteOrder() {
	logs.Info("接收删除订单请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务删除订单
	err = c.orderService.DeleteOrder(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除订单失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// GetOrderStatistics 获取订单统计信息
// @Title 获取订单统计信息
// @Description 获取订单统计信息
// @Success 200 {object} dto.OrderStatisticsResponse "订单统计信息"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /statistics [get]
func (c *OrderController) GetOrderStatistics() {
	logs.Info("接收获取订单统计信息请求")

	// 调用服务获取订单统计信息
	stats, err := c.orderService.GetOrderStatistics(c.Ctx.Request.Context())
	if err != nil {
		logs.Error("获取订单统计信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, stats)
}

// GetOrderLogs 获取订单日志
// @Title 获取订单日志
// @Description 获取指定订单ID的订单日志
// @Param id path int true "订单ID"
// @Success 200 {array} dto.OrderLogResponse "订单日志列表"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @router /:id/logs [get]
func (c *OrderController) GetOrderLogs() {
	logs.Info("接收获取订单日志请求")

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取订单日志
	orderLogs, err := c.orderService.GetOrderLogs(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取订单日志失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, orderLogs)
}
