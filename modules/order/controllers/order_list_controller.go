/**
 * 订单列表控制器
 *
 * 本文件定义了轻量级订单列表查询的控制器。
 * 专门用于订单列表页面，只返回必要信息，提高API响应速度。
 */

package controllers

import (
	"fmt"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/order/dto"
	"o_mall_backend/modules/order/services"
)

// OrderListController 订单列表控制器
type OrderListController struct {
	web.Controller
	orderListService services.OrderListService
	orderService     services.OrderService
}

// Prepare 初始化
func (c *OrderListController) Prepare() {
	c.orderListService = services.NewOrderListService()
	c.orderService = services.NewOrderService()
}

// ListOrders 获取订单列表（轻量级）
// @Title 获取订单列表
// @Description 获取用户订单列表，只返回必要信息
// @Param order_no query string false "订单号"
// @Param status query int false "订单状态"
// @Param pay_status query int false "支付状态"
// @Param order_type query int false "订单类型"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} dto.OrderListResponse
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /list [get]
func (c *OrderListController) ListOrders() {
	logs.Info("接收到轻量级订单列表查询请求")

	// 解析请求参数
	req := &dto.OrderListRequest{}

	// 从JWT上下文中获取用户ID
	userIDInterface := c.Ctx.Input.GetData("userID")
	if userIDInterface == nil {
		logs.Error("无法从上下文获取用户ID")
		result.HandleError(c.Ctx, result.ErrUnauthorized, "用户未认证")
		return
	}
	userID, ok := userIDInterface.(int64)
	if !ok {
		logs.Error("用户ID类型转换失败")
		result.HandleError(c.Ctx, result.ErrInternal, "用户ID类型错误")
		return
	}
	req.UserID = userID

	// 订单号（可选）
	req.OrderNo = c.GetString("order_no")

	// 订单状态（可选）
	if statusStr := c.GetString("status"); statusStr != "" {
		status, err := strconv.Atoi(statusStr)
		if err != nil {
			logs.Error("订单状态格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "订单状态格式错误")
			return
		}
		req.Status = status
	}

	// 支付状态（可选）
	if payStatusStr := c.GetString("pay_status"); payStatusStr != "" {
		payStatus, err := strconv.Atoi(payStatusStr)
		if err != nil {
			logs.Error("支付状态格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "支付状态格式错误")
			return
		}
		req.PayStatus = payStatus
	}

	// 订单类型（可选）
	if orderTypeStr := c.GetString("order_type"); orderTypeStr != "" {
		orderType, err := strconv.Atoi(orderTypeStr)
		if err != nil {
			logs.Error("订单类型格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "订单类型格式错误")
			return
		}
		req.OrderType = orderType
	}

	// 开始时间（可选）
	if startTimeStr := c.GetString("start_time"); startTimeStr != "" {
		startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr)
		if err != nil {
			logs.Error("开始时间格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "开始时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
			return
		}
		req.StartTime = startTime
	}

	// 结束时间（可选）
	if endTimeStr := c.GetString("end_time"); endTimeStr != "" {
		endTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr)
		if err != nil {
			logs.Error("结束时间格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "结束时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")
			return
		}
		req.EndTime = endTime
	}

	// 分页参数（可选）
	if pageStr := c.GetString("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			logs.Error("页码格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "页码格式错误")
			return
		}
		req.Page = page
	} else {
		req.Page = 1
	}

	if pageSizeStr := c.GetString("page_size"); pageSizeStr != "" {
		pageSize, err := strconv.Atoi(pageSizeStr)
		if err != nil || pageSize < 1 {
			logs.Error("每页数量格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "每页数量格式错误")
			return
		}
		req.PageSize = pageSize
	} else {
		req.PageSize = 10
	}

	// 调用服务层
	logs.Info("开始调用订单列表服务...")
	response, err := c.orderListService.GetOrderList(req)
	if err != nil {
		logs.Error("查询订单列表失败: %v, 详细错误信息: %#v", err, err)
		// 将详细错误信息返回给前端（仅在开发环境）
		errorMsg := fmt.Sprintf("查询订单列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, errorMsg)
		return
	}

	logs.Info("成功查询订单列表，用户ID: %d，总数: %d", userID, response.Total)
	result.OK(c.Ctx, response)
}

// GetOrderStatusCount 获取订单状态统计
// @Title 获取订单状态统计
// @Description 获取用户各状态订单数量统计
// @Success 200 {object} dto.OrderStatusCountResponse
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /status-count [get]
func (c *OrderListController) GetOrderStatusCount() {
	logs.Info("接收到订单状态统计查询请求")

	// 从JWT上下文中获取用户ID
	userIDInterface := c.Ctx.Input.GetData("userID")
	if userIDInterface == nil {
		logs.Error("无法从上下文获取用户ID")
		result.HandleError(c.Ctx, result.ErrUnauthorized, "用户未认证")
		return
	}
	userID, ok := userIDInterface.(int64)
	if !ok {
		logs.Error("用户ID类型转换失败")
		result.HandleError(c.Ctx, result.ErrInternal, "用户ID类型错误")
		return
	}

	// 调用服务层
	response, err := c.orderListService.GetOrderStatusCount(userID)
	if err != nil {
		logs.Error("查询订单状态统计失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "查询订单状态统计失败")
		return
	}

	logs.Info("成功查询订单状态统计，用户ID: %d", userID)
	result.OK(c.Ctx, response)
}

// ListOrdersHighPerformance 高性能订单列表查询
// @Title 高性能订单列表查询
// @Description 高性能订单列表查询，专门优化响应速度
// @Param status query int false "订单状态"
// @Param order_type query int false "订单类型"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} dto.HighPerformanceOrderListResponse
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /high-performance [get]
func (c *OrderListController) ListOrdersHighPerformance() {
	logs.Info("接收到高性能订单列表查询请求")

	// 解析请求参数
	req := &dto.HighPerformanceOrderListRequest{}

	// 从JWT上下文中获取用户ID
	userIDInterface := c.Ctx.Input.GetData("userID")
	if userIDInterface == nil {
		logs.Error("无法从上下文获取用户ID")
		result.HandleError(c.Ctx, result.ErrUnauthorized, "用户未认证")
		return
	}
	userID, ok := userIDInterface.(int64)
	if !ok {
		logs.Error("用户ID类型转换失败")
		result.HandleError(c.Ctx, result.ErrInternal, "用户ID类型错误")
		return
	}
	req.UserID = userID

	// 订单状态（可选）
	if statusStr := c.GetString("status"); statusStr != "" {
		status, err := strconv.Atoi(statusStr)
		if err != nil {
			logs.Error("订单状态格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "订单状态格式错误")
			return
		}
		req.Status = status
	}

	// 订单类型（可选）
	if orderTypeStr := c.GetString("order_type"); orderTypeStr != "" {
		orderType, err := strconv.Atoi(orderTypeStr)
		if err != nil {
			logs.Error("订单类型格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "订单类型格式错误")
			return
		}
		req.OrderType = orderType
	}

	// 分页参数（可选）
	if pageStr := c.GetString("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			logs.Error("页码格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "页码格式错误")
			return
		}
		req.Page = page
		logs.Debug("接收到页码参数: %s -> %d", pageStr, page)
	} else {
		req.Page = 1
		logs.Debug("使用默认页码: 1")
	}

	if pageSizeStr := c.GetString("page_size"); pageSizeStr != "" {
		pageSize, err := strconv.Atoi(pageSizeStr)
		if err != nil || pageSize < 1 {
			logs.Error("每页数量格式错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "每页数量格式错误")
			return
		}
		req.PageSize = pageSize
		logs.Debug("接收到每页数量参数: %s -> %d", pageSizeStr, pageSize)
	} else {
		req.PageSize = 10
		logs.Debug("使用默认每页数量: 10")
	}

	logs.Debug("最终分页参数: Page=%d, PageSize=%d", req.Page, req.PageSize)

	// 调用高性能服务层
	logs.Info("开始调用高性能订单列表服务...")
	response, err := c.orderService.ListOrdersHighPerformance(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("高性能查询订单列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "查询订单列表失败")
		return
	}

	logs.Info("成功查询高性能订单列表，用户ID: %d，总数: %d", userID, response.Total)
	result.OKWithPagination(c.Ctx, response.Orders, response.Total, response.Page, response.PageSize)
}
