/**
 * order_payment_controller.go
 * 订单支付控制器
 *
 * 该控制器负责处理订单支付相关的HTTP请求，包括发起支付、查询支付状态、处理支付回调等
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/auth"
	"o_mall_backend/common/response"

	//"o_mall_backend/modules/order/dto"
	"o_mall_backend/modules/order/services/impl"
)

// OrderPaymentController 订单支付控制器
type OrderPaymentController struct {
	web.Controller
	paymentService impl.OrderPaymentService
}

// Prepare 准备工作
func (c *OrderPaymentController) Prepare() {
	c.paymentService = impl.NewOrderPaymentService()
}

// InitiatePayment 发起支付
// @Title 发起支付
// @Description 为指定订单发起支付
// @Param Authorization header string true "用户授权令牌"
// @Param order_id path int true "订单ID"
// @Param pay_method formData int true "支付方式：1-微信支付，2-支付宝，3-余额支付"
// @Success 200 {object} response.ApiResponse{data=dto.PaymentResponse} "返回支付参数"
// @Failure 400 {object} response.ApiResponse "请求参数错误"
// @Failure 401 {object} response.ApiResponse "未授权"
// @Failure 500 {object} response.ApiResponse "服务器内部错误"
// @Router /api/orders/{order_id}/payment [post]
func (c *OrderPaymentController) InitiatePayment() {
	// 检查用户授权
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取用户ID失败: %v", err)
		response.ErrorUnauthorized(c.Ctx)
		return
	}

	// 获取订单ID
	orderIDStr := c.Ctx.Input.Param(":order_id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		response.ErrorBadRequest(c.Ctx, "订单ID格式错误")
		return
	}

	// 获取支付方式
	payMethodStr := c.GetString("pay_method")
	payMethod, err := strconv.Atoi(payMethodStr)
	if err != nil {
		logs.Error("解析支付方式失败: %v", err)
		response.ErrorBadRequest(c.Ctx, "支付方式格式错误")
		return
	}

	// 发起支付
	resp, err := c.paymentService.InitiatePayment(c.Ctx.Request.Context(), orderID, payMethod, userID)
	if err != nil {
		logs.Error("发起支付失败: %v", err)
		response.ErrorInternal(c.Ctx, err.Error())
		return
	}

	response.Success(c.Ctx, resp)
}

// QueryPaymentStatus 查询支付状态
// @Title 查询支付状态
// @Description 查询指定订单的支付状态
// @Param Authorization header string true "用户授权令牌"
// @Param order_id path int true "订单ID"
// @Success 200 {object} response.ApiResponse{data=dto.PaymentStatusResponse} "返回支付状态"
// @Failure 400 {object} response.ApiResponse "请求参数错误"
// @Failure 401 {object} response.ApiResponse "未授权"
// @Failure 500 {object} response.ApiResponse "服务器内部错误"
// @Router /api/orders/{order_id}/payment/status [get]
func (c *OrderPaymentController) QueryPaymentStatus() {
	// 检查用户授权
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取用户ID失败: %v", err)
		response.ErrorUnauthorized(c.Ctx)
		return
	}
	logs.Info("用户ID: %v", userID)

	// 获取订单ID
	orderIDStr := c.Ctx.Input.Param(":order_id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		response.ErrorBadRequest(c.Ctx, "订单ID格式错误")
		return
	}

	// 查询支付状态
	resp, err := c.paymentService.QueryPaymentStatus(c.Ctx.Request.Context(), orderID)
	if err != nil {
		logs.Error("查询支付状态失败: %v", err)
		response.ErrorInternal(c.Ctx, err.Error())
		return
	}

	response.Success(c.Ctx, resp)
}

// HandlePaymentCallback 处理支付回调
// @Title 处理支付回调
// @Description 处理支付平台的支付结果回调
// @Param method path string true "支付方式：wechat-微信支付，alipay-支付宝，balance-余额支付"
// @Param order_no path string true "订单编号"
// @Success 200 {string} string "success"
// @Failure 400 {string} string "参数错误"
// @Failure 500 {string} string "处理失败"
// @Router /api/payment/callback/{method}/{order_no} [post]
func (c *OrderPaymentController) HandlePaymentCallback() {
	// 获取支付方式
	method := c.Ctx.Input.Param(":method")

	// 获取订单编号
	orderNo := c.Ctx.Input.Param(":order_no")

	// 解析不同支付方式的回调数据
	var paymentNo, transactionID string
	var amount float64
	var payMethod int
	var rawData string

	// 获取原始请求数据
	rawData = string(c.Ctx.Input.RequestBody)

	// 根据支付方式解析回调数据
	switch method {
	case "wechat":
		// 解析微信支付回调数据
		payMethod = 1 // 微信支付
		var wxCallback struct {
			OutTradeNo    string `json:"out_trade_no"`
			TransactionId string `json:"transaction_id"`
			Amount        struct {
				Total int `json:"total"`
			} `json:"amount"`
			// 其他微信支付回调参数...
		}

		if err := json.Unmarshal(c.Ctx.Input.RequestBody, &wxCallback); err != nil {
			logs.Error("解析微信支付回调数据失败: %v", err)
			c.Ctx.Output.SetStatus(400)
			c.Ctx.Output.Body([]byte("参数错误"))
			return
		}

		paymentNo = wxCallback.OutTradeNo
		transactionID = wxCallback.TransactionId
		amount = float64(wxCallback.Amount.Total) / 100.0 // 微信支付金额单位为分

	case "alipay":
		// 解析支付宝回调数据
		payMethod = 2 // 支付宝

		// 支付宝回调通常是表单数据
		c.Ctx.Request.ParseForm()
		formData := c.Ctx.Request.Form

		// 获取关键数据
		paymentNo = formData.Get("out_trade_no")
		transactionID = formData.Get("trade_no")
		amountStr := formData.Get("total_amount")
		amount, _ = strconv.ParseFloat(amountStr, 64)

		// 构建原始数据
		rawDataBytes, _ := json.Marshal(formData)
		rawData = string(rawDataBytes)

	case "balance":
		// 解析余额支付回调数据
		payMethod = 3 // 余额支付
		var balanceCallback struct {
			OutTradeNo    string  `json:"out_trade_no"`
			TransactionId string  `json:"transaction_id"`
			Amount        float64 `json:"amount"`
			// 其他余额支付回调参数...
		}

		if err := json.Unmarshal(c.Ctx.Input.RequestBody, &balanceCallback); err != nil {
			logs.Error("解析余额支付回调数据失败: %v", err)
			c.Ctx.Output.SetStatus(400)
			c.Ctx.Output.Body([]byte("参数错误"))
			return
		}

		paymentNo = balanceCallback.OutTradeNo
		transactionID = balanceCallback.TransactionId
		amount = balanceCallback.Amount

	default:
		logs.Error("不支持的支付方式: %s", method)
		c.Ctx.Output.SetStatus(400)
		c.Ctx.Output.Body([]byte("不支持的支付方式"))
		return
	}

	// 处理支付回调
	err := c.paymentService.HandlePaymentCallback(c.Ctx.Request.Context(), orderNo, paymentNo, transactionID, amount, payMethod, rawData)
	if err != nil {
		logs.Error("处理支付回调失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("处理失败"))
		return
	}

	// 返回成功
	c.Ctx.Output.SetStatus(200)
	c.Ctx.Output.Body([]byte("success"))
}

// InitiateRefund 发起退款
// @Title 发起退款
// @Description 为指定订单发起退款
// @Param Authorization header string true "用户授权令牌"
// @Param order_id path int true "订单ID"
// @Param amount formData number true "退款金额"
// @Param reason formData string true "退款原因"
// @Success 200 {object} response.ApiResponse{data=dto.RefundResponse} "返回退款信息"
// @Failure 400 {object} response.ApiResponse "请求参数错误"
// @Failure 401 {object} response.ApiResponse "未授权"
// @Failure 500 {object} response.ApiResponse "服务器内部错误"
// @Router /api/orders/{order_id}/refund [post]
func (c *OrderPaymentController) InitiateRefund() {
	// 检查用户授权
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取用户ID失败: %v", err)
		response.ErrorUnauthorized(c.Ctx)
		return
	}

	// 获取订单ID
	orderIDStr := c.Ctx.Input.Param(":order_id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		logs.Error("解析订单ID失败: %v", err)
		response.ErrorBadRequest(c.Ctx, "订单ID格式错误")
		return
	}

	// 获取退款金额
	amountStr := c.GetString("amount")
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		logs.Error("解析退款金额失败: %v", err)
		response.ErrorBadRequest(c.Ctx, "退款金额格式错误")
		return
	}

	// 获取退款原因
	reason := c.GetString("reason")
	if reason == "" {
		reason = "用户申请退款"
	}

	// 发起退款
	resp, err := c.paymentService.InitiateRefund(c.Ctx.Request.Context(), orderID, amount, reason, userID)
	if err != nil {
		logs.Error("发起退款失败: %v", err)
		response.ErrorInternal(c.Ctx, err.Error())
		return
	}

	response.Success(c.Ctx, resp)
}

// HandleRefundCallback 处理退款回调
// @Title 处理退款回调
// @Description 处理支付平台的退款结果回调
// @Param method path string true "支付方式：wechat-微信支付，alipay-支付宝，balance-余额支付"
// @Param order_no path string true "订单编号"
// @Success 200 {string} string "success"
// @Failure 400 {string} string "参数错误"
// @Failure 500 {string} string "处理失败"
// @Router /api/refund/callback/{method}/{order_no} [post]
func (c *OrderPaymentController) HandleRefundCallback() {
	// 获取支付方式
	method := c.Ctx.Input.Param(":method")

	// 获取订单编号
	orderNo := c.Ctx.Input.Param(":order_no")

	// 解析不同支付方式的回调数据
	var refundNo, externalRefundNo string
	var amount float64
	var rawData string

	// 获取原始请求数据
	rawData = string(c.Ctx.Input.RequestBody)

	// 根据支付方式解析回调数据
	switch method {
	case "wechat":
		// 解析微信支付退款回调数据
		var wxCallback struct {
			OutRefundNo string `json:"out_refund_no"`
			RefundID    string `json:"refund_id"`
			Amount      struct {
				Refund int `json:"refund"`
			} `json:"amount"`
			// 其他微信支付退款回调参数...
		}

		if err := json.Unmarshal(c.Ctx.Input.RequestBody, &wxCallback); err != nil {
			logs.Error("解析微信支付退款回调数据失败: %v", err)
			c.Ctx.Output.SetStatus(400)
			c.Ctx.Output.Body([]byte("参数错误"))
			return
		}

		refundNo = wxCallback.OutRefundNo
		externalRefundNo = wxCallback.RefundID
		amount = float64(wxCallback.Amount.Refund) / 100.0 // 微信支付金额单位为分

	case "alipay":
		// 解析支付宝退款回调数据

		// 支付宝回调通常是表单数据
		c.Ctx.Request.ParseForm()
		formData := c.Ctx.Request.Form

		// 获取关键数据
		refundNo = formData.Get("out_request_no")
		externalRefundNo = formData.Get("trade_no")
		amountStr := formData.Get("refund_amount")
		amount, _ = strconv.ParseFloat(amountStr, 64)

		// 构建原始数据
		rawDataBytes, _ := json.Marshal(formData)
		rawData = string(rawDataBytes)

	case "balance":
		// 解析余额支付退款回调数据
		var balanceCallback struct {
			OutRefundNo string  `json:"out_refund_no"`
			RefundID    string  `json:"refund_id"`
			Amount      float64 `json:"amount"`
			// 其他余额支付退款回调参数...
		}

		if err := json.Unmarshal(c.Ctx.Input.RequestBody, &balanceCallback); err != nil {
			logs.Error("解析余额支付退款回调数据失败: %v", err)
			c.Ctx.Output.SetStatus(400)
			c.Ctx.Output.Body([]byte("参数错误"))
			return
		}

		refundNo = balanceCallback.OutRefundNo
		externalRefundNo = balanceCallback.RefundID
		amount = balanceCallback.Amount

	default:
		logs.Error("不支持的支付方式: %s", method)
		c.Ctx.Output.SetStatus(400)
		c.Ctx.Output.Body([]byte("不支持的支付方式"))
		return
	}

	// 处理退款回调
	err := c.paymentService.HandleRefundCallback(c.Ctx.Request.Context(), orderNo, refundNo, externalRefundNo, amount, rawData)
	if err != nil {
		logs.Error("处理退款回调失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("处理失败"))
		return
	}

	// 返回成功
	c.Ctx.Output.SetStatus(200)
	c.Ctx.Output.Body([]byte("success"))
}
