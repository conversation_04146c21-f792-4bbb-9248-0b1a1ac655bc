/**
 * 性能监控控制器
 *
 * 本文件实现了性能监控相关的API接口，提供性能统计数据查询、
 * 慢查询分析等功能，便于运维人员监控系统性能。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/middlewares"
)

// PerformanceController 性能监控控制器
type PerformanceController struct {
	web.Controller
}

// GetPerformanceStats 获取性能统计数据
// @Title 获取性能统计数据
// @Description 获取API性能统计数据，包括响应时间、慢查询等信息
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} utils.Response
// @router /stats [get]
func (c *PerformanceController) GetPerformanceStats() {
	logs.Info("获取性能统计数据")

	// 获取统计数据
	stats := middlewares.GetPerformanceStats()

	// 计算汇总信息
	summary := make(map[string]interface{})
	totalRequests := int64(0)
	totalSlowRequests := int64(0)
	totalAverageTime := float64(0)
	endpointCount := 0

	for _, stat := range stats {
		totalRequests += stat.TotalRequests
		totalSlowRequests += stat.SlowRequests
		totalAverageTime += stat.AverageTime
		endpointCount++
	}

	if endpointCount > 0 {
		summary["total_requests"] = totalRequests
		summary["total_slow_requests"] = totalSlowRequests
		summary["slow_request_rate"] = float64(totalSlowRequests) / float64(totalRequests) * 100
		summary["overall_average_time"] = totalAverageTime / float64(endpointCount)
		summary["monitored_endpoints"] = endpointCount
	} else {
		summary["total_requests"] = 0
		summary["total_slow_requests"] = 0
		summary["slow_request_rate"] = 0
		summary["overall_average_time"] = 0
		summary["monitored_endpoints"] = 0
	}

	response := map[string]interface{}{
		"summary":   summary,
		"endpoints": stats,
	}

	logs.Info("成功获取性能统计数据，监控接口数: %d", endpointCount)
	result.OK(c.Ctx, response)
}

// GetSlowQueries 获取慢查询列表
// @Title 获取慢查询列表
// @Description 获取响应时间超过阈值的慢查询接口列表
// @Param threshold query int false "慢查询阈值(ms)，默认500"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} utils.Response
// @router /slow-queries [get]
func (c *PerformanceController) GetSlowQueries() {
	logs.Info("获取慢查询列表")

	// 获取阈值参数
	threshold := 500 // 默认500ms
	if thresholdStr := c.GetString("threshold"); thresholdStr != "" {
		if t, err := c.GetInt("threshold"); err == nil && t > 0 {
			threshold = t
		}
	}

	// 获取统计数据
	stats := middlewares.GetPerformanceStats()

	// 筛选慢查询
	slowQueries := make([]map[string]interface{}, 0)
	for endpoint, stat := range stats {
		if stat.SlowRequests > 0 {
			slowRate := float64(stat.SlowRequests) / float64(stat.TotalRequests) * 100
			slowQuery := map[string]interface{}{
				"endpoint":         endpoint,
				"total_requests":   stat.TotalRequests,
				"slow_requests":    stat.SlowRequests,
				"slow_rate":        slowRate,
				"average_time":     stat.AverageTime,
				"max_time":         stat.MaxTime,
				"last_update_time": stat.LastUpdateTime,
			}
			slowQueries = append(slowQueries, slowQuery)
		}
	}

	response := map[string]interface{}{
		"threshold":    threshold,
		"slow_queries": slowQueries,
		"total_count":  len(slowQueries),
	}

	logs.Info("成功获取慢查询列表，阈值: %dms，慢查询接口数: %d", threshold, len(slowQueries))
	result.OK(c.Ctx, response)
}

// ResetStats 重置性能统计数据
// @Title 重置性能统计数据
// @Description 清空所有性能统计数据，重新开始统计
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /reset [post]
func (c *PerformanceController) ResetStats() {
	logs.Info("重置性能统计数据")

	// 重置统计数据
	middlewares.ResetPerformanceStats()

	logs.Info("性能统计数据重置成功")
	result.OK(c.Ctx, map[string]interface{}{
		"message": "性能统计数据已重置",
	})
}

// LogStats 输出性能统计报告到日志
// @Title 输出性能统计报告
// @Description 将当前性能统计数据输出到日志文件
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /log-report [post]
func (c *PerformanceController) LogStats() {
	logs.Info("输出性能统计报告到日志")

	// 输出统计报告
	middlewares.LogPerformanceStats()

	logs.Info("性能统计报告已输出到日志")
	result.OK(c.Ctx, map[string]interface{}{
		"message": "性能统计报告已输出到日志",
	})
}

// GetOrderListPerformance 获取订单列表专项性能数据
// @Title 获取订单列表性能数据
// @Description 获取订单列表相关接口的性能统计数据
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} utils.Response
// @router /order-list [get]
func (c *PerformanceController) GetOrderListPerformance() {
	logs.Info("获取订单列表性能数据")

	// 获取统计数据
	allStats := middlewares.GetPerformanceStats()

	// 筛选订单列表相关接口
	orderListStats := make(map[string]interface{})
	orderListPaths := []string{
		"GET /api/v1/orders/list",
		"GET /api/v1/orders/simple/list",
		"GET /api/v1/orders/simple/high-performance",
		"GET /api/v1/orders/simple/status-count",
	}

	for _, path := range orderListPaths {
		if stat, exists := allStats[path]; exists {
			orderListStats[path] = stat
		}
	}

	// 计算订单列表接口汇总
	summary := map[string]interface{}{
		"monitored_apis": len(orderListStats),
		"total_requests": int64(0),
		"slow_requests":  int64(0),
	}

	totalRequests := int64(0)
	slowRequests := int64(0)
	for _, statInterface := range orderListStats {
		if stat, ok := statInterface.(*middlewares.PerformanceStats); ok {
			totalRequests += stat.TotalRequests
			slowRequests += stat.SlowRequests
		}
	}

	summary["total_requests"] = totalRequests
	summary["slow_requests"] = slowRequests
	if totalRequests > 0 {
		summary["slow_rate"] = float64(slowRequests) / float64(totalRequests) * 100
	} else {
		summary["slow_rate"] = 0
	}

	response := map[string]interface{}{
		"summary": summary,
		"apis":    orderListStats,
	}

	logs.Info("成功获取订单列表性能数据，监控API数: %d", len(orderListStats))
	result.OK(c.Ctx, response)
}
