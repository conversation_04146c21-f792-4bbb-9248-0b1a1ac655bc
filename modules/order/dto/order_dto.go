/**
 * 订单DTO定义
 *
 * 本文件定义了订单模块中用于数据传输的对象，包括请求对象和响应对象。
 * 这些DTO用于控制器层与服务层之间的数据交换，规范了数据的传输格式。
 */

package dto

import (
	"time"
)

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	UserID        int64                `json:"user_id" valid:"Required"`            // 用户ID
	MerchantID    int64                `json:"merchant_id"`                         // 商家ID（用于区分不同商家的订单）
	Items         []*OrderItemRequest  `json:"items" valid:"Required"`              // 订单项
	Address       *OrderAddressRequest `json:"address" valid:"Required"`            // 收货地址
	Remark        string               `json:"remark" valid:"MaxSize(500)"`         // 订单备注
	CouponIDs     []int64              `json:"coupon_ids"`                          // 优惠券ID列表
	UseIntegral   bool                 `json:"use_integral"`                        // 是否使用积分
	DeliveryType  int                  `json:"delivery_type" valid:"Range(1,3)"`    // 配送方式
	PayMethod     int                  `json:"pay_method" valid:"Required"`         // 支付方式
	Source        int                  `json:"source" valid:"Range(1,4)"`           // 订单来源
	OrderType     int                  `json:"order_type" valid:"Range(0,2)"`       // 订单类型
	InvoiceType   int                  `json:"invoice_type" valid:"Range(0,2)"`     // 发票类型
	InvoiceTitle  string               `json:"invoice_title" valid:"MaxSize(100)"`  // 发票抬头
	InvoiceTaxNo  string               `json:"invoice_tax_no" valid:"MaxSize(50)"`  // 发票税号
	PromotionInfo string               `json:"promotion_info" valid:"MaxSize(500)"` // 促销信息
}

// OrderItemRequest 订单项请求
type OrderItemRequest struct {
	ProductID       int64   `json:"product_id" valid:"Required"`      // 商品ID
	ProductName     string  `json:"product_name"`                     // 商品名称
	ProductImage    string  `json:"product_image"`                    // 商品图片URL
	SkuID           int64   `json:"sku_id" valid:"Required"`          // SKU ID
	Quantity        int     `json:"quantity" valid:"Required;Min(1)"` // 购买数量
	Price           float64 `json:"price" valid:"Required"`           // 单价
	ItemType        string  `json:"item_type"`                        // 项目类型（普通商品或外卖商品）
	MerchantID      int64   `json:"merchant_id"`                      // 商家ID（外卖特有）
	MerchantName    string  `json:"merchant_name"`                    // 商家名称（外卖特有）
	PackagingFee    float64 `json:"packaging_fee"`                    // 包装费（外卖特有）
	Remark          string  `json:"remark" valid:"MaxSize(500)"`      // 备注
	ProductSnapshot string  `json:"product_snapshot"`                 // 商品快照JSON
	VariantSnapshot string  `json:"variant_snapshot"`                 // 规格快照JSON
	ComboSelections string  `json:"combo_selections"`                 // 套餐选择JSON（外卖特有）
}

// OrderAddressRequest 订单地址请求
type OrderAddressRequest struct {
	ReceiverName  string `json:"receiver_name" valid:"Required;MaxSize(50)"`  // 收货人姓名
	ReceiverPhone string `json:"receiver_phone" valid:"Required;MaxSize(20)"` // 收货人电话
	Province      string `json:"province" valid:"Required;MaxSize(20)"`       // 省份
	City          string `json:"city" valid:"Required;MaxSize(20)"`           // 城市
	District      string `json:"district" valid:"Required;MaxSize(20)"`       // 区/县
	Detail        string `json:"detail" valid:"Required;MaxSize(200)"`        // 详细地址
	PostCode      string `json:"post_code" valid:"MaxSize(10)"`               // 邮政编码
}

// UpdateOrderRequest 更新订单请求
type UpdateOrderRequest struct {
	Status         int    `json:"status" valid:"Range(10,80)"`         // 订单状态
	Remark         string `json:"remark" valid:"MaxSize(500)"`         // 订单备注
	TrackingNo     string `json:"tracking_no" valid:"MaxSize(50)"`     // 物流单号
	ExpressCompany string `json:"express_company" valid:"MaxSize(50)"` // 快递公司
	CancelReason   string `json:"cancel_reason" valid:"MaxSize(255)"`  // 取消原因
}

// PayOrderRequest 支付订单请求
type PayOrderRequest struct {
	PayMethod     int     `json:"pay_method" valid:"Required"`              // 支付方式
	PaymentNo     string  `json:"payment_no" valid:"Required;MaxSize(100)"` // 支付流水号
	PaymentAmount float64 `json:"payment_amount" valid:"Required"`          // 支付金额
	TransactionID string  `json:"transaction_id" valid:"MaxSize(100)"`      // 交易ID
	ProviderReply string  `json:"provider_reply" valid:"MaxSize(1000)"`     // 支付提供商返回信息
}

// CancelOrderRequest 取消订单请求
type CancelOrderRequest struct {
	CancelReason string `json:"reason" valid:"Required;MaxSize(255)"` // 取消原因
}

// ShipOrderRequest 发货请求
type ShipOrderRequest struct {
	TrackingNo     string `json:"tracking_no" valid:"Required;MaxSize(50)"`     // 物流单号
	ExpressCompany string `json:"express_company" valid:"Required;MaxSize(50)"` // 快递公司
}

// OrderQueryRequest 订单查询请求
type OrderQueryRequest struct {
	UserID    int64     `json:"user_id"`                          // 用户ID
	OrderNo   string    `json:"order_no" valid:"MaxSize(32)"`     // 订单编号
	Status    int       `json:"status"`                           // 订单状态
	PayStatus int       `json:"pay_status"`                       // 支付状态
	OrderType int       `json:"order_type"`                       // 订单类型
	Takeout   bool      `json:"takeout"`                          // 是否查询外卖订单
	StartTime time.Time `json:"start_time"`                       // 开始时间
	EndTime   time.Time `json:"end_time"`                         // 结束时间
	Page      int       `json:"page" valid:"Min(1)"`              // 页码
	PageSize  int       `json:"pageSize" valid:"Min(1);Max(100)"` // 每页数量
}

// OrderResponse 订单响应
type OrderResponse struct {
	ID               int64                 `json:"id"`                 // 订单ID
	OrderNo          string                `json:"order_no"`           // 订单编号
	UserID           int64                 `json:"user_id"`            // 用户ID
	UserName         string                `json:"user_name"`          // 用户名称
	Status           int                   `json:"status"`             // 订单状态
	StatusText       string                `json:"status_text"`        // 订单状态文本
	PayStatus        int                   `json:"pay_status"`         // 支付状态
	PayStatusText    string                `json:"pay_status_text"`    // 支付状态文本
	PayTime          *time.Time            `json:"pay_time"`           // 支付时间
	PayMethod        int                   `json:"pay_method"`         // 支付方式
	PayMethodText    string                `json:"pay_method_text"`    // 支付方式文本
	PayAmount        float64               `json:"pay_amount"`         // 支付金额
	TotalAmount      float64               `json:"total_amount"`       // 订单总金额
	FreightAmount    float64               `json:"freight_amount"`     // 运费
	DiscountAmount   float64               `json:"discount_amount"`    // 优惠金额
	CouponAmount     float64               `json:"coupon_amount"`      // 优惠券抵扣金额
	IntegralAmount   float64               `json:"integral_amount"`    // 积分抵扣金额
	PromotionAmount  float64               `json:"promotion_amount"`   // 促销优惠金额
	Remark           string                `json:"remark"`             // 订单备注
	Source           int                   `json:"source"`             // 订单来源
	SourceText       string                `json:"source_text"`        // 订单来源文本
	DeliveryType     int                   `json:"delivery_type"`      // 配送方式
	DeliveryTypeText string                `json:"delivery_type_text"` // 配送方式文本
	DeliveryTime     *time.Time            `json:"delivery_time"`      // 发货时间
	ReceiveTime      *time.Time            `json:"receive_time"`       // 收货时间
	CommentStatus    int                   `json:"comment_status"`     // 评价状态
	CommentTime      *time.Time            `json:"comment_time"`       // 评价时间
	InvoiceType      int                   `json:"invoice_type"`       // 发票类型
	InvoiceTypeText  string                `json:"invoice_type_text"`  // 发票类型文本
	InvoiceTitle     string                `json:"invoice_title"`      // 发票抬头
	InvoiceTaxNo     string                `json:"invoice_tax_no"`     // 发票税号
	CancelReason     string                `json:"cancel_reason"`      // 取消原因
	CancelTime       *time.Time            `json:"cancel_time"`        // 取消时间
	RefundTime       *time.Time            `json:"refund_time"`        // 退款时间
	RefundAmount     float64               `json:"refund_amount"`      // 退款金额
	OrderType        int                   `json:"order_type"`         // 订单类型
	OrderTypeText    string                `json:"order_type_text"`    // 订单类型文本
	TrackingNo       string                `json:"tracking_no"`        // 物流单号
	ExpressCompany   string                `json:"express_company"`    // 快递公司
	PromotionInfo    string                `json:"promotion_info"`     // 促销信息
	Items            []*OrderItemResponse  `json:"items"`              // 订单项
	Address          *OrderAddressResponse `json:"address"`            // 收货地址
	Payment          *OrderPaymentResponse `json:"payment"`            // 支付信息
	CreatedAt        time.Time             `json:"created_at"`         // 创建时间
	UpdatedAt        time.Time             `json:"updated_at"`         // 更新时间
}

// OrderItemResponse 订单项响应
type OrderItemResponse struct {
	ID             int64     `json:"id"`              // 订单项ID
	OrderID        int64     `json:"order_id"`        // 订单ID
	OrderNo        string    `json:"order_no"`        // 订单编号
	ProductID      int64     `json:"product_id"`      // 商品ID
	ProductName    string    `json:"product_name"`    // 商品名称
	ProductImage   string    `json:"product_image"`   // 商品图片
	SkuID          int64     `json:"sku_id"`          // SKU ID
	SkuCode        string    `json:"sku_code"`        // SKU编码
	SkuAttributes  string    `json:"sku_attributes"`  // SKU属性
	Quantity       int       `json:"quantity"`        // 购买数量
	Price          float64   `json:"price"`           // 实际单价
	OriginalPrice  float64   `json:"original_price"`  // 原始单价
	SubtotalAmount float64   `json:"subtotal_amount"` // 小计金额
	DiscountAmount float64   `json:"discount_amount"` // 优惠金额
	CommentStatus  int       `json:"comment_status"`  // 评价状态
	RefundStatus   int       `json:"refund_status"`   // 退款状态
	RefundQuantity int       `json:"refund_quantity"` // 退款数量
	RefundAmount   float64   `json:"refund_amount"`   // 退款金额
	CreatedAt      time.Time `json:"created_at"`      // 创建时间
	VariantSnapshot string    `json:"variant_snapshot"` // 规格快照
}

// OrderAddressResponse 订单地址响应
type OrderAddressResponse struct {
	ID            int64     `json:"id"`             // 地址ID
	OrderID       int64     `json:"order_id"`       // 订单ID
	OrderNo       string    `json:"order_no"`       // 订单编号
	UserID        int64     `json:"user_id"`        // 用户ID
	ReceiverName  string    `json:"receiver_name"`  // 收货人姓名
	ReceiverPhone string    `json:"receiver_phone"` // 收货人电话
	Province      string    `json:"province"`       // 省份
	City          string    `json:"city"`           // 城市
	District      string    `json:"district"`       // 区/县
	Detail        string    `json:"detail"`         // 详细地址
	PostCode      string    `json:"post_code"`      // 邮政编码
	IsDefault     bool      `json:"is_default"`     // 是否默认
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
}

// OrderPaymentResponse 订单支付响应
type OrderPaymentResponse struct {
	ID                int64      `json:"id"`                  // 支付ID
	OrderID           int64      `json:"order_id"`            // 订单ID
	OrderNo           string     `json:"order_no"`            // 订单编号
	UserID            int64      `json:"user_id"`             // 用户ID
	PaymentNo         string     `json:"payment_no"`          // 支付流水号
	PaymentMethod     int        `json:"payment_method"`      // 支付方式
	PaymentMethodText string     `json:"payment_method_text"` // 支付方式文本
	PaymentAmount     float64    `json:"payment_amount"`      // 支付金额
	PaymentStatus     int        `json:"payment_status"`      // 支付状态
	PaymentStatusText string     `json:"payment_status_text"` // 支付状态文本
	PaymentTime       *time.Time `json:"payment_time"`        // 支付时间
	TransactionID     string     `json:"transaction_id"`      // 交易ID
	CreatedAt         time.Time  `json:"created_at"`          // 创建时间
}

// OrderLogResponse 订单日志响应
type OrderLogResponse struct {
	ID         int64     `json:"id"`          // 日志ID
	OrderID    int64     `json:"order_id"`    // 订单ID
	OrderNo    string    `json:"order_no"`    // 订单编号
	UserID     int64     `json:"user_id"`     // 用户ID
	AdminID    int64     `json:"admin_id"`    // 管理员ID
	Action     string    `json:"action"`      // 操作类型
	Status     int       `json:"status"`      // 订单状态
	StatusText string    `json:"status_text"` // 订单状态文本
	Content    string    `json:"content"`     // 日志内容
	IP         string    `json:"ip"`          // 操作IP
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
}

// OrderListResponse 订单列表响应
type OrderListResponse struct {
	Total    int64            `json:"total"`    // 总数
	Page     int              `json:"page"`     // 页码
	PageSize int              `json:"pageSize"` // 每页数量
	Items    []*OrderResponse `json:"items"`    // 订单列表
}

// OrderStatisticsResponse 订单统计响应
type OrderStatisticsResponse struct {
	TotalOrders     int64   `json:"total_orders"`     // 订单总数
	TotalAmount     float64 `json:"total_amount"`     // 总金额
	PendingOrders   int64   `json:"pending_orders"`   // 待付款订单数
	PaidOrders      int64   `json:"paid_orders"`      // 已付款订单数
	ShippedOrders   int64   `json:"shipped_orders"`   // 已发货订单数
	CompletedOrders int64   `json:"completed_orders"` // 已完成订单数
	CancelledOrders int64   `json:"cancelled_orders"` // 已取消订单数
	RefundingOrders int64   `json:"refunding_orders"` // 退款中订单数
	RefundedOrders  int64   `json:"refunded_orders"`  // 已退款订单数
	TodayOrders     int64   `json:"today_orders"`     // 今日订单数
	TodayAmount     float64 `json:"today_amount"`     // 今日金额
	YesterdayOrders int64   `json:"yesterday_orders"` // 昨日订单数
	YesterdayAmount float64 `json:"yesterday_amount"` // 昨日金额
	WeekOrders      int64   `json:"week_orders"`      // 本周订单数
	WeekAmount      float64 `json:"week_amount"`      // 本周金额
	MonthOrders     int64   `json:"month_orders"`     // 本月订单数
	MonthAmount     float64 `json:"month_amount"`     // 本月金额
}
