/**
 * 订单列表DTO
 *
 * 本文件定义了轻量级订单列表查询的请求和响应数据传输对象。
 * 专门用于订单列表页面，只包含必要的基础信息，提高查询和传输效率。
 */

package dto

import "time"

// OrderListRequest 订单列表查询请求
type OrderListRequest struct {
	UserID    int64     `json:"user_id"`    // 用户ID（由系统设置）
	OrderNo   string    `json:"order_no"`   // 订单号（可选）
	Status    int       `json:"status"`     // 订单状态（0-全部，1-待付款，2-待发货，3-待收货，4-已完成，5-已取消）
	PayStatus int       `json:"pay_status"` // 支付状态（0-全部，1-未支付，2-已支付，3-已退款）
	OrderType int       `json:"order_type"` // 订单类型（0-全部，1-外卖订单，2-普通订单）
	StartTime time.Time `json:"start_time"` // 开始时间
	EndTime   time.Time `json:"end_time"`   // 结束时间
	Page      int       `json:"page"`       // 页码
	PageSize  int       `json:"page_size"`  // 每页数量
}

// SimpleOrderListResponse 轻量级订单列表响应
type SimpleOrderListResponse struct {
	Orders     []*OrderListItem `json:"orders"`      // 订单列表
	Total      int64            `json:"total"`       // 总数量
	Page       int              `json:"page"`        // 当前页码
	PageSize   int              `json:"page_size"`   // 每页数量
	TotalPages int              `json:"total_pages"` // 总页数
}

// OrderListItem 订单列表项（轻量级）
type OrderListItem struct {
	ID            int64                `json:"id"`              // 订单ID
	OrderNo       string               `json:"order_no"`        // 订单号
	Status        int                  `json:"status"`          // 订单状态
	StatusText    string               `json:"status_text"`     // 订单状态文本
	PayStatus     int                  `json:"pay_status"`      // 支付状态
	PayStatusText string               `json:"pay_status_text"` // 支付状态文本
	OrderType     int                  `json:"order_type"`      // 订单类型（1-外卖，2-普通）
	OrderTypeText string               `json:"order_type_text"` // 订单类型文本
	TotalAmount   float64              `json:"total_amount"`    // 订单总金额
	CreateTime    time.Time            `json:"create_time"`     // 创建时间
	Items         []*OrderListItemInfo `json:"items"`           // 订单项列表
}

// OrderListItemInfo 订单项信息（轻量级）
type OrderListItemInfo struct {
	ID              int64   `json:"id"`               // 订单项ID
	ProductID       int64   `json:"product_id"`       // 商品ID
	ProductName     string  `json:"product_name"`     // 商品名称
	ProductImage    string  `json:"product_image"`    // 商品图片
	Price           float64 `json:"price"`            // 单价
	Quantity        int     `json:"quantity"`         // 数量
	SubtotalAmount  float64 `json:"subtotal_amount"`  // 小计金额
	SkuAttributes   string  `json:"sku_attributes"`   // SKU属性（直接返回文本）
	VariantSnapshot string  `json:"variant_snapshot"` // 规格快照（原始JSON，由前端解析）
	ProductSnapshot string  `json:"product_snapshot"` // 商品快照（原始JSON，由前端解析）
	ComboSelections string  `json:"combo_selections"` // 套餐选择（原始JSON，由前端解析）
}

// OrderStatusCountResponse 订单状态统计响应
type OrderStatusCountResponse struct {
	All       int64 `json:"all"`       // 全部订单数量
	Pending   int64 `json:"pending"`   // 待付款数量
	Paid      int64 `json:"paid"`      // 待发货数量
	Shipped   int64 `json:"shipped"`   // 待收货数量
	Completed int64 `json:"completed"` // 已完成数量
	Cancelled int64 `json:"cancelled"` // 已取消数量
}

// HighPerformanceOrderListRequest 高性能订单列表查询请求
type HighPerformanceOrderListRequest struct {
	UserID    int64 `json:"user_id"`    // 用户ID（由系统设置）
	Status    int   `json:"status"`     // 订单状态（0-全部）
	OrderType int   `json:"order_type"` // 订单类型（0-全部，1-外卖订单）
	Page      int   `json:"page"`       // 页码
	PageSize  int   `json:"page_size"`  // 每页数量
}

// HighPerformanceOrderListResponse 高性能订单列表响应
type HighPerformanceOrderListResponse struct {
	Orders   []*HighPerformanceOrderItem `json:"orders"`    // 订单列表
	Total    int64                       `json:"total"`     // 总数量
	Page     int                         `json:"page"`      // 当前页码
	PageSize int                         `json:"page_size"` // 每页数量
}

// HighPerformanceOrderItem 高性能订单列表项（最轻量级）
type HighPerformanceOrderItem struct {
	ID            int64     `json:"id"`              // 订单ID
	OrderNo       string    `json:"order_no"`        // 订单号
	Status        int       `json:"status"`          // 订单状态
	StatusText    string    `json:"status_text"`     // 订单状态文本
	PayStatus     int       `json:"pay_status"`      // 支付状态
	PayStatusText string    `json:"pay_status_text"` // 支付状态文本
	OrderType     int       `json:"order_type"`      // 订单类型
	OrderTypeText string    `json:"order_type_text"` // 订单类型文本
	TotalAmount   float64   `json:"total_amount"`    // 订单总金额
	CreateTime    time.Time `json:"create_time"`     // 创建时间
	// 订单项摘要信息（避免JOIN查询）
	FirstProductName  string `json:"first_product_name"`  // 第一个商品名称
	FirstProductImage string `json:"first_product_image"` // 第一个商品图片
	ItemCount         int    `json:"item_count"`          // 订单项总数
	// 显示文本：如"商品名称等N件商品"
	ItemSummary string `json:"item_summary"` // 订单项摘要
}
