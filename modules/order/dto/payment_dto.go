/**
 * payment_dto.go
 * 支付相关数据传输对象
 * 
 * 本文件定义了订单支付和退款相关的数据传输对象，用于前后端数据交换
 */

package dto

import (
	"time"
)

// PaymentResponse 支付响应
type PaymentResponse struct {
	OrderID      int64             `json:"order_id"`      // 订单ID
	OrderNo      string            `json:"order_no"`      // 订单编号
	PaymentID    int64             `json:"payment_id"`    // 支付ID
	PaymentNo    string            `json:"payment_no"`    // 支付流水号
	Amount       float64           `json:"amount"`        // 支付金额
	PayMethod    int               `json:"pay_method"`    // 支付方式
	PaymentURL   string            `json:"payment_url"`   // 支付页面URL
	QrCodeURL    string            `json:"qr_code_url"`   // 二维码URL(如有)
	AppPayParams map[string]string `json:"app_pay_params"` // APP支付参数
	WebPayParams map[string]string `json:"web_pay_params"` // Web支付参数
	ExpireTime   *time.Time        `json:"expire_time"`   // 过期时间
}

// PaymentStatusResponse 支付状态响应
type PaymentStatusResponse struct {
	OrderID       int64      `json:"order_id"`        // 订单ID
	OrderNo       string     `json:"order_no"`        // 订单编号
	PaymentNo     string     `json:"payment_no"`      // 支付流水号
	Status        int        `json:"status"`          // 支付状态
	StatusDesc    string     `json:"status_desc"`     // 状态描述
	PaymentMethod int        `json:"payment_method"`  // 支付方式
	Amount        float64    `json:"amount"`          // 订单金额
	PaidAmount    float64    `json:"paid_amount"`     // 实付金额
	TransactionID string     `json:"transaction_id"`  // 交易ID
	PaidTime      time.Time  `json:"paid_time"`       // 支付时间
	ExpireTime    *time.Time `json:"expire_time"`     // 过期时间
}

// RefundResponse 退款响应
type RefundResponse struct {
	OrderID        int64  `json:"order_id"`         // 订单ID
	OrderNo        string `json:"order_no"`         // 订单编号
	RefundID       int64  `json:"refund_id"`        // 退款ID
	RefundNo       string `json:"refund_no"`        // 退款单号
	Amount         float64 `json:"amount"`          // 退款金额
	Status         int    `json:"status"`           // 退款状态
	StatusDesc     string `json:"status_desc"`      // 状态描述
	ApprovalNeeded bool   `json:"approval_needed"`  // 是否需要审批
}

// RefundStatusResponse 退款状态响应
type RefundStatusResponse struct {
	OrderID       int64      `json:"order_id"`        // 订单ID
	OrderNo       string     `json:"order_no"`        // 订单编号
	RefundID      int64      `json:"refund_id"`       // 退款ID
	RefundNo      string     `json:"refund_no"`       // 退款单号
	Amount        float64    `json:"amount"`          // 退款金额
	Status        int        `json:"status"`          // 退款状态
	StatusDesc    string     `json:"status_desc"`     // 状态描述
	RefundTime    *time.Time `json:"refund_time"`     // 退款时间
	ExternalRefundNo string  `json:"external_refund_no"` // 外部退款单号
}
