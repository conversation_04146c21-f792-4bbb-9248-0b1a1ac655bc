/**
 * 订单模块初始化
 *
 * 本文件负责订单模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保订单模块的功能正常启动。
 */

package order

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/order/models"
	"o_mall_backend/modules/order/routers"
)

// Init 初始化订单模块
func Init() {
	logs.Info("初始化订单模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitRouters()

	logs.Info("订单模块初始化完成")
}

// 注册ORM模型
func registerModels() {
	logs.Info("注册订单模块ORM模型...")

	// 注册订单相关模型
	orm.RegisterModel(
		new(models.Order),
		new(models.OrderItem),
		new(models.OrderAddress),
		new(models.OrderLog),
		new(models.OrderPayment),
	)
}
