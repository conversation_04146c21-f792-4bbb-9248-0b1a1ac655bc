/**
 * 订单模型定义
 *
 * 本文件定义了订单相关的数据模型，包括订单主体、订单项目、收货地址等。
 * 这些模型用于在数据库中存储订单相关的信息。
 */

package models

import (
	"encoding/json"
	"time"
	//"github.com/beego/beego/v2/client/orm"
)

// 订单项类型常量定义
const (
	ItemTypeProduct = "product" // 普通商品类型
	ItemTypeTakeout = "takeout" // 外卖商品类型
)

// 订单状态常量定义
const (
	OrderStatusPending    = 10 // 待支付/待付款
	OrderStatusPaid       = 20 // 已支付/待发货
	OrderStatusProcessing = 30 // 处理中/待收货
	OrderStatusDelivering = 40 // 配送中
	OrderStatusDelivered  = 45 // 已送达
	OrderStatusCompleted  = 50 // 已完成
	OrderStatusCancelled  = 60 // 已取消
	OrderStatusRefunding  = 70 // 退款中
	OrderStatusRefunded   = 80 // 已退款
)

// Order 订单模型
type Order struct {
	ID              int64     `orm:"auto;pk" json:"id" description:"订单ID，系统自动生成的唯一标识"`                                                         // 订单ID
	OrderNo         string    `orm:"size(32);unique" json:"order_no" description:"订单编号，业务层面的唯一标识"`                                             // 订单编号
	UserID          int64     `orm:"index" json:"user_id" description:"下单用户的ID，关联用户表"`                                                         // 用户ID
	MerchantID      int64     `orm:"index" json:"merchant_id" description:"商家ID，用于区分不同商家的订单"`                                                  // 商家ID
	Status          int       `orm:"default(10)" json:"status" description:"订单状态：10待付款/待支付，20已支付/待发货，30处理中/待收货，40配送中，50已完成，60已取消，70退款中，80已退款"` // 订单状态
	PayStatus       int       `orm:"default(0)" json:"pay_status" description:"支付状态：0未支付，1支付中，2已支付，3支付失败"`                                     // 支付状态
	PayTime         time.Time `orm:"null;type(datetime)" json:"pay_time" description:"支付完成时间，支付成功后更新"`                                         // 支付时间
	PayMethod       int       `orm:"default(0)" json:"pay_method" description:"支付方式：0未选择，1微信，2支付宝，3银行卡"`                                       // 支付方式
	PayAmount       float64   `orm:"digits(10);decimals(2)" json:"pay_amount" description:"实际支付金额，可能与订单总金额不同"`                                 // 支付金额
	TotalAmount     float64   `orm:"digits(10);decimals(2)" json:"total_amount" description:"订单总金额，包含商品金额和运费"`                                 // 订单总金额
	FreightAmount   float64   `orm:"digits(10);decimals(2)" json:"freight_amount" description:"运费金额，根据配送方式和地区计算"`                              // 运费
	DiscountAmount  float64   `orm:"digits(10);decimals(2)" json:"discount_amount" description:"优惠总金额，包含所有优惠方式"`                               // 优惠金额
	CouponAmount    float64   `orm:"digits(10);decimals(2)" json:"coupon_amount" description:"优惠券抵扣的金额"`                                       // 优惠券抵扣金额
	IntegralAmount  float64   `orm:"digits(10);decimals(2)" json:"integral_amount" description:"积分抵扣的金额"`                                      // 积分抵扣金额
	PromotionAmount float64   `orm:"digits(10);decimals(2)" json:"promotion_amount" description:"促销活动优惠的金额"`                                   // 促销优惠金额
	Remark          string    `orm:"size(500)" json:"remark" description:"订单备注信息，用户下单时填写"`                                                     // 订单备注
	Source          int       `orm:"default(1)" json:"source" description:"订单来源：1PC端，2移动端，3小程序，4手工录入"`                                         // 订单来源
	DeliveryType    int       `orm:"default(1)" json:"delivery_type" description:"配送方式：1普通快递，2同城配送，3上门自提"`                                     // 配送方式
	DeliveryTime    time.Time `orm:"null;type(datetime)" json:"delivery_time" description:"实际发货时间"`                                            // 发货时间
	ReceiveTime     time.Time `orm:"null;type(datetime)" json:"receive_time" description:"实际收货时间"`                                             // 收货时间
	CommentStatus   int       `orm:"default(0)" json:"comment_status" description:"评价状态：0未评价，1已评价，2已追评"`                                       // 评价状态
	CommentTime     time.Time `orm:"null;type(datetime)" json:"comment_time" description:"评价提交时间"`                                             // 评价时间
	InvoiceType     int       `orm:"default(0)" json:"invoice_type" description:"发票类型：0不开发票，1普通发票，2电子发票，3增值税发票"`                               // 发票类型
	InvoiceTitle    string    `orm:"size(100)" json:"invoice_title" description:"发票抬头，公司或个人名称"`                                                // 发票抬头
	InvoiceTaxNo    string    `orm:"size(50)" json:"invoice_tax_no" description:"发票税号，纳税人识别号"`                                                 // 发票税号
	CancelReason    string    `orm:"size(255)" json:"cancel_reason" description:"订单取消原因，可由用户或系统取消"`                                            // 取消原因
	CancelTime      time.Time `orm:"null;type(datetime)" json:"cancel_time" description:"订单取消时间"`                                              // 取消时间
	RefundTime      time.Time `orm:"null;type(datetime)" json:"refund_time" description:"退款完成时间"`                                              // 退款时间
	RefundAmount    float64   `orm:"digits(10);decimals(2)" json:"refund_amount" description:"退款总金额，可能包含多次退款"`                                 // 退款金额
	OrderType       int       `orm:"default(0)" json:"order_type" description:"订单类型：0普通订单，1秒杀订单，2团购订单"`                                        // 订单类型
	TrackingNo      string    `orm:"size(50)" json:"tracking_no" description:"物流运单号，由快递公司提供"`                                                  // 物流单号
	ExpressCompany  string    `orm:"size(50)" json:"express_company" description:"快递公司名称"`                                                     // 快递公司
	PromotionInfo   string    `orm:"size(500)" json:"promotion_info" description:"促销活动信息，包含活动名称和优惠详情"`                                         // 促销信息
	CouponIDs       string    `orm:"size(200)" json:"coupon_ids" description:"使用的优惠券ID列表，多个ID用逗号分隔"`                                           // 优惠券ID列表
	DeletedAt       time.Time `orm:"null;type(datetime)" json:"deleted_at" description:"软删除时间标记"`                                              // 删除时间
	CreatedAt       time.Time `orm:"auto_now_add;type(datetime)" json:"created_at" description:"订单创建时间，系统自动生成"`                                // 创建时间
	UpdatedAt       time.Time `orm:"auto_now;type(datetime)" json:"updated_at" description:"订单更新时间，系统自动更新"`                                    // 更新时间
}

// OrderItem 订单项模型
type OrderItem struct {
	ID             int64   `orm:"auto;pk" json:"id" description:"订单项ID，系统自动生成的唯一标识"`                                        // 订单项ID
	OrderID        int64   `orm:"index" json:"order_id" description:"关联的订单ID，用于关联订单信息"`                                     // 订单ID
	OrderNo        string  `orm:"size(32);index" json:"order_no" description:"订单编号，用于快速查询和关联订单"`                            // 订单编号
	ItemType       string  `orm:"size(20);default(product)" json:"item_type" description:"订单项类型：product-普通商品，takeout-外卖商品"` // 订单项类型
	ProductID      int64   `orm:"index" json:"product_id" description:"商品ID，关联商品基本信息"`                                      // 商品ID
	ProductName    string  `orm:"size(100)" json:"product_name" description:"商品名称，冗余存储以提高查询效率"`                             // 商品名称
	ProductImage   string  `orm:"size(255)" json:"product_image" description:"商品主图URL，用于展示"`                                // 商品图片
	SkuID          int64   `orm:"index" json:"sku_id" description:"商品SKU ID，关联具体规格信息"`                                      // SKU ID
	SkuCode        string  `orm:"size(50)" json:"sku_code" description:"SKU编码，用于库存管理"`                                      // SKU编码
	SkuAttributes  string  `orm:"size(500)" json:"sku_attributes" description:"SKU规格属性JSON字符串，记录颜色、尺寸等信息"`                  // SKU属性
	Quantity       int     `orm:"default(1)" json:"quantity" description:"购买数量，默认为1"`                                       // 购买数量
	Price          float64 `orm:"digits(10);decimals(2)" json:"price" description:"实际销售单价，可能经过优惠"`                          // 实际单价
	OriginalPrice  float64 `orm:"digits(10);decimals(2)" json:"original_price" description:"原始标价，未经过优惠"`                    // 原始单价
	SubtotalAmount float64 `orm:"digits(10);decimals(2)" json:"subtotal_amount" description:"小计金额，等于实际单价乘以数量"`              // 小计金额
	DiscountAmount float64 `orm:"digits(10);decimals(2)" json:"discount_amount" description:"优惠金额，包括各种优惠方式"`                // 优惠金额
	CommentStatus  int     `orm:"default(0)" json:"comment_status" description:"评价状态：0未评价，1已评价"`                            // 评价状态
	RefundStatus   int     `orm:"default(0)" json:"refund_status" description:"退款状态：0未退款，1退款中，2已退款"`                        // 退款状态
	RefundQuantity int     `orm:"default(0)" json:"refund_quantity" description:"退款商品数量，部分退款时使用"`                           // 退款数量
	RefundAmount   float64 `orm:"digits(10);decimals(2)" json:"refund_amount" description:"退款金额，可能小于等于实际支付金额"`              // 退款金额
	// 外卖特有字段
	MerchantID      int64   `orm:"default(0)" json:"merchant_id" description:"商家ID"`                                  // 商家ID
	MerchantName    string  `orm:"size(100)" json:"merchant_name" description:"商家名称，仅外卖订单项有效"`                        // 商家名称
	PackagingFee    float64 `orm:"digits(10);decimals(2);default(0)" json:"packaging_fee" description:"包装费，仅外卖订单项有效"` // 包装费
	ComboSelections string  `orm:"type(text)" json:"combo_selections" description:"套餐选择JSON字符串，记录套餐加料等信息"`            // 套餐选择
	Remark          string  `orm:"size(500)" json:"remark" description:"订单项备注，如口味、偏好等"`                               // 订单项备注
	// 商品冻结帧（快照）
	ProductSnapshot string    `orm:"type(text)" json:"product_snapshot" description:"商品快照JSON字符串，记录下单时商品状态"`   // 商品快照
	VariantSnapshot string    `orm:"type(text)" json:"variant_snapshot" description:"SKU快照JSON字符串，记录下单时SKU状态"` // SKU快照
	CreatedAt       time.Time `orm:"auto_now_add;type(datetime)" json:"created_at" description:"创建时间，系统自动生成"`  // 创建时间
	UpdatedAt       time.Time `orm:"auto_now;type(datetime)" json:"updated_at" description:"更新时间，系统自动更新"`      // 更新时间
}

// OrderAddress 订单收货地址模型
type OrderAddress struct {
	ID            int64     `orm:"auto;pk" json:"id" description:"地址ID，主键自增"`                               // 地址ID
	OrderID       int64     `orm:"unique;index" json:"order_id" description:"关联的订单ID，一个订单只能有一个地址"`          // 订单ID
	OrderNo       string    `orm:"size(32)" json:"order_no" description:"订单编号，用于关联查询"`                      // 订单编号
	UserID        int64     `orm:"index" json:"user_id" description:"收货地址所属用户ID"`                           // 用户ID
	ReceiverName  string    `orm:"size(50)" json:"receiver_name" description:"收货人真实姓名"`                     // 收货人姓名
	ReceiverPhone string    `orm:"size(20)" json:"receiver_phone" description:"收货人联系电话"`                    // 收货人电话
	Province      string    `orm:"size(20)" json:"province" description:"省份名称"`                             // 省份
	City          string    `orm:"size(20)" json:"city" description:"城市名称"`                                 // 城市
	District      string    `orm:"size(20)" json:"district" description:"区/县名称"`                            // 区/县
	Detail        string    `orm:"size(200)" json:"detail" description:"详细地址信息，如街道门牌号"`                     // 详细地址
	PostCode      string    `orm:"size(10)" json:"post_code" description:"邮政编码，选填"`                         // 邮政编码
	IsDefault     bool      `orm:"default(false)" json:"is_default" description:"是否为默认收货地址"`                // 是否默认
	CreatedAt     time.Time `orm:"auto_now_add;type(datetime)" json:"created_at" description:"创建时间，系统自动生成"` // 创建时间
	UpdatedAt     time.Time `orm:"auto_now;type(datetime)" json:"updated_at" description:"更新时间，系统自动更新"`     // 更新时间
}

// OrderLog 订单日志模型
type OrderLog struct {
	ID        int64     `orm:"auto;pk" json:"id" description:"日志ID，系统自动生成"`                        // 日志ID
	OrderID   int64     `orm:"index" json:"order_id" description:"关联的订单ID"`                        // 订单ID
	OrderNo   string    `orm:"size(32)" json:"order_no" description:"订单编号，便于查询"`                   // 订单编号
	UserID    int64     `orm:"default(0)" json:"user_id" description:"操作用户ID，0表示系统操作"`             // 用户ID
	AdminID   int64     `orm:"default(0)" json:"admin_id" description:"操作管理员ID，0表示用户操作"`           // 管理员ID
	Action    string    `orm:"size(50)" json:"action" description:"操作类型，如创建、支付、发货等"`               // 操作类型
	Status    int       `orm:"default(0)" json:"status" description:"操作后的订单状态"`                    // 订单状态
	Content   string    `orm:"size(500)" json:"content" description:"操作详细说明"`                      // 日志内容
	IP        string    `orm:"size(50)" json:"ip" description:"操作者IP地址"`                           // 操作IP
	UserAgent string    `orm:"size(500)" json:"user_agent" description:"操作者浏览器信息"`                 // 用户代理
	CreatedAt time.Time `orm:"auto_now_add;type(datetime)" json:"created_at" description:"日志记录时间"` // 创建时间
}

// OrderPayment 订单支付信息模型
type OrderPayment struct {
	ID            int64     `orm:"auto;pk" json:"id" description:"支付记录ID，主键自增"`                               // 支付ID
	OrderID       int64     `orm:"unique;index" json:"order_id" description:"关联的订单ID，一个订单只能有一条支付记录"`          // 订单ID
	OrderNo       string    `orm:"size(32)" json:"order_no" description:"订单编号，用于关联查询"`                        // 订单编号
	UserID        int64     `orm:"index" json:"user_id" description:"支付用户ID"`                                 // 用户ID
	PaymentNo     string    `orm:"size(100)" json:"payment_no" description:"支付系统生成的流水号"`                      // 支付流水号
	PaymentMethod int       `orm:"default(0)" json:"payment_method" description:"支付方式：0未选择，1微信，2支付宝，3银行卡"`    // 支付方式
	PaymentAmount float64   `orm:"digits(10);decimals(2)" json:"payment_amount" description:"实际支付金额"`         // 支付金额
	PaymentStatus int       `orm:"default(0)" json:"payment_status" description:"支付状态：0未支付，1支付中，2支付成功，3支付失败"` // 支付状态
	PaymentTime   time.Time `orm:"null;type(datetime)" json:"payment_time" description:"支付完成时间"`              // 支付时间
	TransactionID string    `orm:"size(100)" json:"transaction_id" description:"支付平台交易号"`                     // 交易ID
	ProviderReply string    `orm:"size(1000)" json:"provider_reply" description:"支付平台返回的原始数据"`                // 支付提供商返回信息
	CreatedAt     time.Time `orm:"auto_now_add;type(datetime)" json:"created_at" description:"创建时间，系统自动生成"`   // 创建时间
	UpdatedAt     time.Time `orm:"auto_now;type(datetime)" json:"updated_at" description:"更新时间，系统自动更新"`       // 更新时间
}

// ProductSnapshot 商品快照结构，用于保存下单时的商品状态
type ProductSnapshot struct {
	ID            int64                  `json:"id"`             // 商品ID
	Name          string                 `json:"name"`           // 商品名称
	Brief         string                 `json:"brief"`          // 商品简介
	Image         string                 `json:"image"`          // 商品图片
	Price         float64                `json:"price"`          // 商品价格
	OriginalPrice float64                `json:"original_price"` // 原始价格
	Attributes    map[string]interface{} `json:"attributes"`     // 商品属性
	Categories    []string               `json:"categories"`     // 商品分类
	Tags          []string               `json:"tags"`           // 商品标签
}

// VariantSnapshot SKU快照结构，用于保存下单时的SKU状态
type VariantSnapshot struct {
	ID            int64             `json:"id"`             // SKU ID
	Code          string            `json:"code"`           // SKU编码
	Name          string            `json:"name"`           // SKU名称
	Price         float64           `json:"price"`          // SKU价格
	OriginalPrice float64           `json:"original_price"` // 原始价格
	Attributes    map[string]string `json:"attributes"`     // SKU属性
	Stock         int               `json:"stock"`          // 库存数量
}

// TakeoutSnapshot 外卖商品快照结构，用于保存下单时的外卖商品状态
type TakeoutSnapshot struct {
	ID              int64    `json:"id"`               // 外卖商品ID
	Name            string   `json:"name"`             // 外卖商品名称
	Brief           string   `json:"brief"`            // 外卖商品简介
	Image           string   `json:"image"`            // 外卖商品图片
	Price           float64  `json:"price"`            // 外卖商品价格
	OriginalPrice   float64  `json:"original_price"`   // 原始价格
	PreparationTime int      `json:"preparation_time"` // 备餐时间
	PackagingFee    float64  `json:"packaging_fee"`    // 包装费
	IsCombination   bool     `json:"is_combination"`   // 是否为套餐组合
	IsSpicy         bool     `json:"is_spicy"`         // 是否为辣味食品
	MerchantID      int64    `json:"merchant_id"`      // 商家ID
	MerchantName    string   `json:"merchant_name"`    // 商家名称
	Tags            []string `json:"tags"`             // 标签
}

// TakeoutVariantSnapshot 外卖规格快照结构，用于保存下单时的外卖规格状态
type TakeoutVariantSnapshot struct {
	ID            int64             `json:"id"`             // 规格ID
	Name          string            `json:"name"`           // 规格名称
	Price         float64           `json:"price"`          // 规格价格
	OriginalPrice float64           `json:"original_price"` // 原始价格
	Attributes    map[string]string `json:"attributes"`     // 规格属性
}

// TakeoutComboSelectionSnapshot 外卖套餐选择快照结构，保存下单时的套餐选择信息
type TakeoutComboSelectionSnapshot struct {
	GroupID         int64  `json:"group_id"`   // 套餐组ID
	GroupName       string `json:"group_name"` // 套餐组名称
	SelectedOptions []struct {
		OptionID   int64   `json:"option_id"`   // 选项ID
		OptionName string  `json:"option_name"` // 选项名称
		ExtraPrice float64 `json:"extra_price"` // 额外加价
		Quantity   int     `json:"quantity"`    // 选择数量
	} `json:"selected_options"` // 已选择的选项
}

// GetProductSnapshot 获取商品快照数据
func (item *OrderItem) GetProductSnapshot() (*ProductSnapshot, error) {
	if item.ProductSnapshot == "" {
		return nil, nil
	}

	var snapshot ProductSnapshot
	err := json.Unmarshal([]byte(item.ProductSnapshot), &snapshot)
	return &snapshot, err
}

// GetVariantSnapshot 获取SKU快照数据
func (item *OrderItem) GetVariantSnapshot() (*VariantSnapshot, error) {
	if item.VariantSnapshot == "" {
		return nil, nil
	}

	var snapshot VariantSnapshot
	err := json.Unmarshal([]byte(item.VariantSnapshot), &snapshot)
	return &snapshot, err
}

// GetTakeoutSnapshot 获取外卖商品快照数据
func (item *OrderItem) GetTakeoutSnapshot() (*TakeoutSnapshot, error) {
	if item.ItemType != ItemTypeTakeout || item.ProductSnapshot == "" {
		return nil, nil
	}

	var snapshot TakeoutSnapshot
	err := json.Unmarshal([]byte(item.ProductSnapshot), &snapshot)
	return &snapshot, err
}

// GetTakeoutVariantSnapshot 获取外卖规格快照数据
func (item *OrderItem) GetTakeoutVariantSnapshot() (*TakeoutVariantSnapshot, error) {
	if item.ItemType != ItemTypeTakeout || item.VariantSnapshot == "" {
		return nil, nil
	}

	var snapshot TakeoutVariantSnapshot
	err := json.Unmarshal([]byte(item.VariantSnapshot), &snapshot)
	return &snapshot, err
}

// GetComboSelections 获取套餐选择数据
func (item *OrderItem) GetComboSelections() ([]TakeoutComboSelectionSnapshot, error) {
	if item.ComboSelections == "" {
		return nil, nil
	}

	var selections []TakeoutComboSelectionSnapshot
	err := json.Unmarshal([]byte(item.ComboSelections), &selections)
	return selections, err
}

// TableIndex 设置订单表索引
func (o *Order) TableIndex() [][]string {
	return [][]string{
		// 用户订单按时间排序的核心索引
		{"UserID", "CreatedAt"},
		// 用户订单按状态筛选的索引
		{"UserID", "Status", "CreatedAt"},
		// 用户订单按类型筛选的索引
		{"UserID", "OrderType", "CreatedAt"},
		// 用户订单按状态和类型筛选的复合索引
		{"UserID", "Status", "OrderType", "CreatedAt"},
		// 商家订单查询索引
		{"MerchantID", "Status", "CreatedAt"},
		// 支付状态查询索引
		{"PayStatus", "CreatedAt"},
		// 订单号唯一索引（已在字段定义中设置unique）
		// 删除状态索引，用于软删除查询优化
		{"DeletedAt"},
	}
}

// TableIndex 设置订单项表索引
func (oi *OrderItem) TableIndex() [][]string {
	return [][]string{
		// 订单项批量查询优化索引
		{"OrderID", "ID"},
		// 商品相关查询索引
		{"ProductID", "CreatedAt"},
		// SKU相关查询索引
		{"SkuID", "CreatedAt"},
		// 商家订单项查询索引（外卖场景）
		{"MerchantID", "CreatedAt"},
		// 订单项类型索引
		{"ItemType", "OrderID"},
	}
}

// TableIndex 设置订单地址表索引
func (oa *OrderAddress) TableIndex() [][]string {
	return [][]string{
		// 订单地址批量查询优化索引
		{"OrderID"},
		// 用户地址查询索引
		{"UserID", "CreatedAt"},
	}
}

// TableIndex 设置订单日志表索引
func (ol *OrderLog) TableIndex() [][]string {
	return [][]string{
		// 订单日志查询索引
		{"OrderID", "CreatedAt"},
		// 用户操作日志索引
		{"UserID", "CreatedAt"},
		// 管理员操作日志索引
		{"AdminID", "CreatedAt"},
		// 操作类型索引
		{"Action", "CreatedAt"},
	}
}

// TableIndex 设置订单支付表索引
func (op *OrderPayment) TableIndex() [][]string {
	return [][]string{
		// 订单支付批量查询优化索引
		{"OrderID"},
		// 用户支付记录索引
		{"UserID", "CreatedAt"},
		// 支付方式统计索引
		{"PaymentMethod", "PaymentStatus", "CreatedAt"},
		// 支付状态查询索引
		{"PaymentStatus", "CreatedAt"},
		// 支付流水号索引
		{"PaymentNo"},
		// 交易ID索引
		{"TransactionID"},
	}
}

// 移除这里的init函数和模型注册代码，因为这些模型已经在modules/order/init.go中注册了
