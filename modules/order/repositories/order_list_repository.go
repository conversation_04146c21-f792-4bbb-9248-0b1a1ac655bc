/**
 * 订单列表仓库接口
 *
 * 本文件定义了轻量级订单列表查询的仓库接口。
 * 专门用于订单列表页面，提供高性能的数据库查询功能。
 */

package repositories

import (
	"time"

	"o_mall_backend/modules/order/dto"
)

// OrderListItem 订单列表项（数据库模型）
type OrderListItem struct {
	ID          int64                    `json:"id"`           // 订单ID
	OrderNo     string                   `json:"order_no"`     // 订单号
	Status      int                      `json:"status"`       // 订单状态
	PayStatus   int                      `json:"pay_status"`   // 支付状态
	OrderType   int                      `json:"order_type"`   // 订单类型
	TotalAmount float64                  `json:"total_amount"` // 订单总金额
	CreateTime  time.Time                `json:"create_time"`  // 创建时间
	Items       []*OrderListItemDetail   `json:"items"`        // 订单项列表
}

// OrderListItemDetail 订单项详情（数据库模型）
type OrderListItemDetail struct {
	ID               int64   `json:"id"`                // 订单项ID
	ProductID        int64   `json:"product_id"`        // 商品ID
	ProductName      string  `json:"product_name"`      // 商品名称
	ProductImage     string  `json:"product_image"`     // 商品图片
	Price            float64 `json:"price"`             // 单价
	Quantity         int     `json:"quantity"`          // 数量
	SubtotalAmount   float64 `json:"subtotal_amount"`   // 小计金额
	SkuAttributes    string  `json:"sku_attributes"`    // SKU属性
	VariantSnapshot  string  `json:"variant_snapshot"`  // 规格快照
	ProductSnapshot  string  `json:"product_snapshot"`  // 商品快照
	ComboSelections  string  `json:"combo_selections"`  // 套餐选择
}

// OrderListRepository 订单列表仓库接口
type OrderListRepository interface {
	// GetOrderList 获取订单列表（轻量级）
	GetOrderList(req *dto.OrderListRequest) ([]*OrderListItem, int64, error)

	// GetOrderStatusCount 获取各状态订单数量统计
	GetOrderStatusCount(userID int64) (*dto.OrderStatusCountResponse, error)
}

// NewOrderListRepository 创建订单列表仓库实例
func NewOrderListRepository() OrderListRepository {
	return &orderListRepository{}
}