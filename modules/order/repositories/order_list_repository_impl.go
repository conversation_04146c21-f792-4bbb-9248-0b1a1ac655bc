/**
 * 订单列表仓库实现
 *
 * 本文件实现了轻量级订单列表查询的数据库操作。
 * 使用优化的SQL查询，避免复杂的关联查询和JSON解析，提高性能。
 */

package repositories

import (
	"fmt"
	"strings"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/order/dto"
)

// orderListRepository 订单列表仓库实现
type orderListRepository struct{}

// GetOrderList 获取订单列表（轻量级）
func (r *orderListRepository) GetOrderList(req *dto.OrderListRequest) ([]*OrderListItem, int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	whereClauses := []string{"o.user_i_d = ?"}
	args := []interface{}{req.UserID}

	// 订单号查询
	if req.OrderNo != "" {
		whereClauses = append(whereClauses, "o.order_no LIKE ?")
		args = append(args, "%"+req.OrderNo+"%")
	}

	// 订单状态查询
	if req.Status > 0 {
		whereClauses = append(whereClauses, "o.status = ?")
		args = append(args, req.Status)
	}

	// 支付状态查询
	if req.PayStatus > 0 {
		whereClauses = append(whereClauses, "o.pay_status = ?")
		args = append(args, req.PayStatus)
	}

	// 订单类型查询
	if req.OrderType > 0 {
		whereClauses = append(whereClauses, "o.order_type = ?")
		args = append(args, req.OrderType)
	}

	// 时间范围查询
	if !req.StartTime.IsZero() {
		whereClauses = append(whereClauses, "o.create_time >= ?")
		args = append(args, req.StartTime)
	}
	if !req.EndTime.IsZero() {
		whereClauses = append(whereClauses, "o.create_time <= ?")
		args = append(args, req.EndTime)
	}

	whereClause := strings.Join(whereClauses, " AND ")

	// 查询总数
	countSQL := fmt.Sprintf(""+
		"SELECT COUNT(*) "+
		"FROM `order` o "+
		"WHERE %s", whereClause)

	var total int64
	err := o.Raw(countSQL, args...).QueryRow(&total)
	if err != nil {
		logs.Error("查询订单总数失败: %v", err)
		return nil, 0, err
	}

	// 查询订单列表
	offset := (req.Page - 1) * req.PageSize
	orderSQL := fmt.Sprintf(""+
		"SELECT "+
		"o.id, "+
		"o.order_no, "+
		"o.status, "+
		"o.pay_status, "+
		"o.order_type, "+
		"o.total_amount, "+
		"o.create_time "+
		"FROM `order` o "+
		"WHERE %s "+
		"ORDER BY o.create_time DESC "+
		"LIMIT ? OFFSET ?", whereClause)

	orderArgs := append(args, req.PageSize, offset)

	var orders []*OrderListItem
	_, err = o.Raw(orderSQL, orderArgs...).QueryRows(&orders)
	if err != nil {
		logs.Error("查询订单列表失败: %v", err)
		return nil, 0, err
	}

	// 如果没有订单，直接返回
	if len(orders) == 0 {
		return orders, total, nil
	}

	// 获取订单ID列表
	orderIDs := make([]interface{}, len(orders))
	orderMap := make(map[int64]*OrderListItem)
	for i, order := range orders {
		orderIDs[i] = order.ID
		orderMap[order.ID] = order
		order.Items = make([]*OrderListItemDetail, 0)
	}

	// 批量查询订单项（使用优化的查询，避免JSON解析）
	placeholders := strings.Repeat("?,", len(orderIDs))
	placeholders = placeholders[:len(placeholders)-1] // 移除最后一个逗号

	itemSQL := fmt.Sprintf(""+
		"SELECT "+
		"oi.id, "+
		"oi.order_i_d, "+
		"oi.product_i_d, "+
		"oi.product_name, "+
		"oi.product_image, "+
		"oi.price, "+
		"oi.quantity, "+
		"oi.subtotal_amount, "+
		"COALESCE(oi.sku_attributes, '') as sku_attributes, "+
		"COALESCE(oi.variant_snapshot, '') as variant_snapshot, "+
		"COALESCE(oi.product_snapshot, '') as product_snapshot, "+
		"COALESCE(oi.combo_selections, '') as combo_selections "+
		"FROM `order_item` oi "+
		"WHERE oi.order_i_d IN (%s) "+
		"ORDER BY oi.order_i_d, oi.i_d", placeholders)

	type OrderItemRow struct {
		ID              int64   `orm:"column(i_d)"`
		OrderID         int64   `orm:"column(order_i_d)"`
		ProductID       int64   `orm:"column(product_i_d)"`
		ProductName     string  `orm:"column(product_name)"`
		ProductImage    string  `orm:"column(product_image)"`
		Price           float64 `orm:"column(price)"`
		Quantity        int     `orm:"column(quantity)"`
		SubtotalAmount  float64 `orm:"column(subtotal_amount)"`
		SkuAttributes   string  `orm:"column(sku_attributes)"`
		VariantSnapshot string  `orm:"column(variant_snapshot)"`
		ProductSnapshot string  `orm:"column(product_snapshot)"`
		ComboSelections string  `orm:"column(combo_selections)"`
	}

	var itemRows []*OrderItemRow
	_, err = o.Raw(itemSQL, orderIDs...).QueryRows(&itemRows)
	if err != nil {
		logs.Error("查询订单项失败: %v", err)
		return nil, 0, err
	}

	// 将订单项分组到对应的订单中
	for _, itemRow := range itemRows {
		if order, exists := orderMap[itemRow.OrderID]; exists {
			item := &OrderListItemDetail{
				ID:              itemRow.ID,
				ProductID:       itemRow.ProductID,
				ProductName:     itemRow.ProductName,
				ProductImage:    itemRow.ProductImage,
				Price:           itemRow.Price,
				Quantity:        itemRow.Quantity,
				SubtotalAmount:  itemRow.SubtotalAmount,
				SkuAttributes:   itemRow.SkuAttributes,
				VariantSnapshot: itemRow.VariantSnapshot,
				ProductSnapshot: itemRow.ProductSnapshot,
				ComboSelections: itemRow.ComboSelections,
			}
			order.Items = append(order.Items, item)
		}
	}

	logs.Info("成功查询订单列表，订单数: %d，订单项数: %d", len(orders), len(itemRows))
	return orders, total, nil
}

// GetOrderStatusCount 获取各状态订单数量统计
func (r *orderListRepository) GetOrderStatusCount(userID int64) (*dto.OrderStatusCountResponse, error) {
	o := orm.NewOrm()

	// 查询各状态订单数量 - 使用正确的状态值
	countSQL := "" +
		"SELECT " +
		"COUNT(*) as all_count, " +
		"SUM(CASE WHEN status = 10 THEN 1 ELSE 0 END) as pending_count, " +
		"SUM(CASE WHEN status = 20 THEN 1 ELSE 0 END) as paid_count, " +
		"SUM(CASE WHEN status = 30 THEN 1 ELSE 0 END) as shipped_count, " +
		"SUM(CASE WHEN status = 50 THEN 1 ELSE 0 END) as completed_count, " +
		"SUM(CASE WHEN status = 60 THEN 1 ELSE 0 END) as cancelled_count " +
		"FROM `order` " +
		"WHERE user_i_d = ?"

	type CountResult struct {
		All       int64 `orm:"column(all_count)"`
		Pending   int64 `orm:"column(pending_count)"`
		Paid      int64 `orm:"column(paid_count)"`
		Shipped   int64 `orm:"column(shipped_count)"`
		Completed int64 `orm:"column(completed_count)"`
		Cancelled int64 `orm:"column(cancelled_count)"`
	}

	var result CountResult
	err := o.Raw(countSQL, userID).QueryRow(&result)
	if err != nil {
		logs.Error("查询订单状态统计失败: %v", err)
		return nil, err
	}

	response := &dto.OrderStatusCountResponse{
		All:       result.All,
		Pending:   result.Pending,
		Paid:      result.Paid,
		Shipped:   result.Shipped,
		Completed: result.Completed,
		Cancelled: result.Cancelled,
	}

	logs.Info("成功查询订单状态统计，用户ID: %d", userID)
	return response, nil
}
