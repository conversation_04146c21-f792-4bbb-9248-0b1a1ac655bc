/**
 * 订单仓库实现
 *
 * 本文件实现了订单仓库接口，提供对订单数据的基本操作。
 * 主要负责订单数据的存储、查询、更新和删除等数据库操作。
 */

package repositories

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/order/dto"
	"o_mall_backend/modules/order/models"
)

// OrderRepository 订单仓库接口
type OrderRepository interface {
	// CreateOrder 创建订单
	CreateOrder(ctx context.Context, order *models.Order) (int64, error)

	// GetOrderByID 根据ID获取订单
	GetOrderByID(ctx context.Context, id int64) (*models.Order, error)

	// GetOrderByOrderNo 根据订单号获取订单
	GetOrderByOrderNo(ctx context.Context, orderNo string) (*models.Order, error)

	// UpdateOrder 更新订单
	UpdateOrder(ctx context.Context, order *models.Order) error

	// DeleteOrder 删除订单
	DeleteOrder(ctx context.Context, id int64) error

	// ListOrders 查询订单列表
	ListOrders(ctx context.Context, req *dto.OrderQueryRequest) ([]*models.Order, int64, error)

	// UpdateOrderStatus 更新订单状态
	UpdateOrderStatus(ctx context.Context, id int64, status int) error

	// UpdatePayStatus 更新支付状态
	UpdatePayStatus(ctx context.Context, id int64, payStatus int, payTime time.Time) error

	// GetOrderStatistics 获取订单统计
	GetOrderStatistics(ctx context.Context) (*dto.OrderStatisticsResponse, error)

	// AddOrderItem 添加订单项
	AddOrderItem(ctx context.Context, item *models.OrderItem) (int64, error)

	// GetOrderItems 获取订单项列表
	GetOrderItems(ctx context.Context, orderID int64) ([]*models.OrderItem, error)

	// AddOrderAddress 添加订单地址
	AddOrderAddress(ctx context.Context, address *models.OrderAddress) (int64, error)

	// GetOrderAddress 获取订单地址
	GetOrderAddress(ctx context.Context, orderID int64) (*models.OrderAddress, error)

	// AddOrderLog 添加订单日志
	AddOrderLog(ctx context.Context, log *models.OrderLog) (int64, error)

	// GetOrderLogs 获取订单日志
	GetOrderLogs(ctx context.Context, orderID int64) ([]*models.OrderLog, error)

	// AddOrderPayment 添加订单支付信息
	AddOrderPayment(ctx context.Context, payment *models.OrderPayment) (int64, error)

	// GetOrderPayment 获取订单支付信息
	GetOrderPayment(ctx context.Context, orderID int64) (*models.OrderPayment, error)

	// UpdateOrderPayment 更新订单支付信息
	UpdateOrderPayment(ctx context.Context, payment *models.OrderPayment) error

	// GetOrderItemsByOrderIDs 批量获取多个订单的订单项
	GetOrderItemsByOrderIDs(ctx context.Context, orderIDs []int64) ([]*models.OrderItem, error)

	// GetOrderAddressesByOrderIDs 批量获取多个订单的地址信息
	GetOrderAddressesByOrderIDs(ctx context.Context, orderIDs []int64) ([]*models.OrderAddress, error)

	// GetOrderPaymentsByOrderIDs 批量获取多个订单的支付信息
	GetOrderPaymentsByOrderIDs(ctx context.Context, orderIDs []int64) ([]*models.OrderPayment, error)

	// ListOrdersHighPerformance 高性能订单列表查询（轻量级）
	ListOrdersHighPerformance(ctx context.Context, req *dto.HighPerformanceOrderListRequest) ([]*dto.HighPerformanceOrderItem, int64, error)
}

// OrderRepositoryImpl 订单仓库实现
type OrderRepositoryImpl struct {
	ormer orm.Ormer
}

// NewOrderRepository 创建订单仓库
func NewOrderRepository() OrderRepository {
	return &OrderRepositoryImpl{
		ormer: orm.NewOrm(),
	}
}

// CreateOrder 创建订单
func (r *OrderRepositoryImpl) CreateOrder(ctx context.Context, order *models.Order) (int64, error) {
	logs.Info("创建订单: %+v", order)
	return r.ormer.Insert(order)
}

// GetOrderByID 根据ID获取订单
func (r *OrderRepositoryImpl) GetOrderByID(ctx context.Context, id int64) (*models.Order, error) {
	// logs.Info("根据ID获取订单: %d", id)
	order := &models.Order{ID: id}
	err := r.ormer.Read(order)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	return order, err
}

// GetOrderByOrderNo 根据订单号获取订单
func (r *OrderRepositoryImpl) GetOrderByOrderNo(ctx context.Context, orderNo string) (*models.Order, error) {
	logs.Info("根据订单号获取订单: %s", orderNo)
	order := &models.Order{OrderNo: orderNo}
	err := r.ormer.Read(order, "OrderNo")
	if err == orm.ErrNoRows {
		return nil, nil
	}
	return order, err
}

// UpdateOrder 更新订单
func (r *OrderRepositoryImpl) UpdateOrder(ctx context.Context, order *models.Order) error {
	logs.Info("更新订单: %+v", order)
	_, err := r.ormer.Update(order)
	return err
}

// DeleteOrder 删除订单
func (r *OrderRepositoryImpl) DeleteOrder(ctx context.Context, id int64) error {
	logs.Info("删除订单: %d", id)
	// 软删除，更新DeletedAt字段
	order := &models.Order{ID: id, DeletedAt: time.Now()}
	_, err := r.ormer.Update(order, "DeletedAt")
	return err
}

// ListOrders 查询订单列表
func (r *OrderRepositoryImpl) ListOrders(ctx context.Context, req *dto.OrderQueryRequest) ([]*models.Order, int64, error) {
	logs.Info("查询订单列表: %+v", req)

	qs := r.ormer.QueryTable(new(models.Order)).Filter("DeletedAt__isnull", true)

	// 添加查询条件
	if req.UserID > 0 {
		qs = qs.Filter("UserID", req.UserID)
	}

	if req.OrderNo != "" {
		qs = qs.Filter("OrderNo", req.OrderNo)
	}

	if req.Status > 0 {
		qs = qs.Filter("Status", req.Status)
	}

	if req.PayStatus > 0 {
		qs = qs.Filter("PayStatus", req.PayStatus)
	}

	if req.OrderType >= 0 {
		qs = qs.Filter("OrderType", req.OrderType)
	}

	if !req.StartTime.IsZero() {
		qs = qs.Filter("CreatedAt__gte", req.StartTime)
	}

	if !req.EndTime.IsZero() {
		qs = qs.Filter("CreatedAt__lte", req.EndTime)
	}

	// 获取总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("查询订单总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	if req.Page <= 0 {
		req.Page = 1
	}

	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize

	var orders []*models.Order
	_, err = qs.OrderBy("-CreatedAt").Limit(req.PageSize, offset).All(&orders)
	if err != nil {
		logs.Error("查询订单列表失败: %v", err)
		return nil, 0, err
	}

	return orders, count, nil
}

// UpdateOrderStatus 更新订单状态
func (r *OrderRepositoryImpl) UpdateOrderStatus(ctx context.Context, id int64, status int) error {
	logs.Info("更新订单状态: id=%d, status=%d", id, status)

	order := &models.Order{ID: id}
	if err := r.ormer.Read(order); err != nil {
		if err == orm.ErrNoRows {
			return errors.New("订单不存在")
		}
		return err
	}

	order.Status = status
	order.UpdatedAt = time.Now()

	// 根据状态更新相应的时间字段
	switch status {
	case 20: // 已付款
		order.PayTime = time.Now()
	case 30: // 已发货
		order.DeliveryTime = time.Now()
	case 40: // 已收货
		order.ReceiveTime = time.Now()
	case 60: // 已取消
		order.CancelTime = time.Now()
	case 80: // 已退款
		order.RefundTime = time.Now()
	}

	_, err := r.ormer.Update(order, "Status", "UpdatedAt", "PayTime", "DeliveryTime", "ReceiveTime", "CancelTime", "RefundTime")
	return err
}

// UpdatePayStatus 更新支付状态
func (r *OrderRepositoryImpl) UpdatePayStatus(ctx context.Context, id int64, payStatus int, payTime time.Time) error {
	logs.Info("更新支付状态: id=%d, payStatus=%d", id, payStatus)

	order := &models.Order{ID: id}
	if err := r.ormer.Read(order); err != nil {
		if err == orm.ErrNoRows {
			return errors.New("订单不存在")
		}
		return err
	}

	order.PayStatus = payStatus
	if !payTime.IsZero() {
		order.PayTime = payTime
	}
	order.UpdatedAt = time.Now()

	// 如果是已支付，同时更新订单状态为已付款
	if payStatus == 1 {
		order.Status = 20 // 已付款
	}

	_, err := r.ormer.Update(order, "PayStatus", "PayTime", "Status", "UpdatedAt")
	return err
}

// GetOrderStatistics 获取订单统计
func (r *OrderRepositoryImpl) GetOrderStatistics(ctx context.Context) (*dto.OrderStatisticsResponse, error) {
	logs.Info("获取订单统计")

	stats := &dto.OrderStatisticsResponse{}

	// 查询订单总数和总金额
	var totalAmount float64
	err := r.ormer.Raw("SELECT COUNT(*) as total, IFNULL(SUM(total_amount), 0) as amount FROM order WHERE deleted_at IS NULL").QueryRow(&stats.TotalOrders, &totalAmount)
	if err != nil {
		logs.Error("查询订单总数和总金额失败: %v", err)
		return nil, err
	}
	stats.TotalAmount = totalAmount

	// 查询各状态订单数量
	var pendingOrders, paidOrders, shippedOrders, completedOrders, cancelledOrders, refundingOrders, refundedOrders int64
	err = r.ormer.Raw("SELECT COUNT(*) FROM order WHERE status = ? AND deleted_at IS NULL", 10).QueryRow(&pendingOrders)
	if err != nil {
		logs.Error("查询待付款订单数失败: %v", err)
		return nil, err
	}
	stats.PendingOrders = pendingOrders

	err = r.ormer.Raw("SELECT COUNT(*) FROM order WHERE status = ? AND deleted_at IS NULL", 20).QueryRow(&paidOrders)
	if err != nil {
		logs.Error("查询已付款订单数失败: %v", err)
		return nil, err
	}
	stats.PaidOrders = paidOrders

	err = r.ormer.Raw("SELECT COUNT(*) FROM order WHERE status = ? AND deleted_at IS NULL", 30).QueryRow(&shippedOrders)
	if err != nil {
		logs.Error("查询已发货订单数失败: %v", err)
		return nil, err
	}
	stats.ShippedOrders = shippedOrders

	err = r.ormer.Raw("SELECT COUNT(*) FROM order WHERE status = ? AND deleted_at IS NULL", 50).QueryRow(&completedOrders)
	if err != nil {
		logs.Error("查询已完成订单数失败: %v", err)
		return nil, err
	}
	stats.CompletedOrders = completedOrders

	err = r.ormer.Raw("SELECT COUNT(*) FROM order WHERE status = ? AND deleted_at IS NULL", 60).QueryRow(&cancelledOrders)
	if err != nil {
		logs.Error("查询已取消订单数失败: %v", err)
		return nil, err
	}
	stats.CancelledOrders = cancelledOrders

	err = r.ormer.Raw("SELECT COUNT(*) FROM order WHERE status = ? AND deleted_at IS NULL", 70).QueryRow(&refundingOrders)
	if err != nil {
		logs.Error("查询退款中订单数失败: %v", err)
		return nil, err
	}
	stats.RefundingOrders = refundingOrders

	err = r.ormer.Raw("SELECT COUNT(*) FROM order WHERE status = ? AND deleted_at IS NULL", 80).QueryRow(&refundedOrders)
	if err != nil {
		logs.Error("查询已退款订单数失败: %v", err)
		return nil, err
	}
	stats.RefundedOrders = refundedOrders

	// 查询今日订单数和金额
	today := time.Now().Format("2006-01-02")
	var todayAmount float64
	err = r.ormer.Raw("SELECT COUNT(*) as count, IFNULL(SUM(total_amount), 0) as amount FROM order WHERE DATE(created_at) = ? AND deleted_at IS NULL", today).QueryRow(&stats.TodayOrders, &todayAmount)
	if err != nil {
		logs.Error("查询今日订单数和金额失败: %v", err)
		return nil, err
	}
	stats.TodayAmount = todayAmount

	// 查询昨日订单数和金额
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	var yesterdayAmount float64
	err = r.ormer.Raw("SELECT COUNT(*) as count, IFNULL(SUM(total_amount), 0) as amount FROM order WHERE DATE(created_at) = ? AND deleted_at IS NULL", yesterday).QueryRow(&stats.YesterdayOrders, &yesterdayAmount)
	if err != nil {
		logs.Error("查询昨日订单数和金额失败: %v", err)
		return nil, err
	}
	stats.YesterdayAmount = yesterdayAmount

	// 查询本周订单数和金额
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday())).Format("2006-01-02")
	var weekAmount float64
	err = r.ormer.Raw("SELECT COUNT(*) as count, IFNULL(SUM(total_amount), 0) as amount FROM order WHERE DATE(created_at) >= ? AND deleted_at IS NULL", weekStart).QueryRow(&stats.WeekOrders, &weekAmount)
	if err != nil {
		logs.Error("查询本周订单数和金额失败: %v", err)
		return nil, err
	}
	stats.WeekAmount = weekAmount

	// 查询本月订单数和金额
	monthStart := time.Date(time.Now().Year(), time.Now().Month(), 1, 0, 0, 0, 0, time.Now().Location()).Format("2006-01-02")
	var monthAmount float64
	err = r.ormer.Raw("SELECT COUNT(*) as count, IFNULL(SUM(total_amount), 0) as amount FROM order WHERE DATE(created_at) >= ? AND deleted_at IS NULL", monthStart).QueryRow(&stats.MonthOrders, &monthAmount)
	if err != nil {
		logs.Error("查询本月订单数和金额失败: %v", err)
		return nil, err
	}
	stats.MonthAmount = monthAmount

	return stats, nil
}

// AddOrderItem 添加订单项
func (r *OrderRepositoryImpl) AddOrderItem(ctx context.Context, item *models.OrderItem) (int64, error) {
	logs.Info("添加订单项: %+v", item)
	return r.ormer.Insert(item)
}

// GetOrderItems 获取订单项列表
func (r *OrderRepositoryImpl) GetOrderItems(ctx context.Context, orderID int64) ([]*models.OrderItem, error) {
	// logs.Info("获取订单项列表: orderID=%d", orderID)

	var items []*models.OrderItem
	_, err := r.ormer.QueryTable(new(models.OrderItem)).Filter("OrderID", orderID).OrderBy("ID").All(&items)
	if err != nil {
		logs.Error("获取订单项列表失败: %v", err)
		return nil, err
	}

	return items, nil
}

// AddOrderAddress 添加订单地址
func (r *OrderRepositoryImpl) AddOrderAddress(ctx context.Context, address *models.OrderAddress) (int64, error) {
	logs.Info("添加订单地址: %+v", address)
	return r.ormer.Insert(address)
}

// GetOrderAddress 获取订单地址
func (r *OrderRepositoryImpl) GetOrderAddress(ctx context.Context, orderID int64) (*models.OrderAddress, error) {
	// logs.Info("获取订单地址: orderID=%d", orderID)

	address := &models.OrderAddress{OrderID: orderID}
	err := r.ormer.Read(address, "OrderID")
	if err == orm.ErrNoRows {
		return nil, nil
	}
	return address, err
}

// AddOrderLog 添加订单日志
func (r *OrderRepositoryImpl) AddOrderLog(ctx context.Context, log *models.OrderLog) (int64, error) {
	logs.Info("添加订单日志: %+v", log)
	return r.ormer.Insert(log)
}

// GetOrderLogs 获取订单日志
func (r *OrderRepositoryImpl) GetOrderLogs(ctx context.Context, orderID int64) ([]*models.OrderLog, error) {
	logs.Info("获取订单日志: orderID=%d", orderID)

	var orderLogs []*models.OrderLog
	_, err := r.ormer.QueryTable(new(models.OrderLog)).Filter("OrderID", orderID).OrderBy("-CreatedAt").All(&orderLogs)
	if err != nil {
		logs.Error("获取订单日志失败: %v", err)
		return nil, err
	}

	return orderLogs, nil
}

// AddOrderPayment 添加订单支付信息
func (r *OrderRepositoryImpl) AddOrderPayment(ctx context.Context, payment *models.OrderPayment) (int64, error) {
	logs.Info("添加订单支付信息: %+v", payment)
	return r.ormer.Insert(payment)
}

// GetOrderPayment 获取订单支付信息
func (r *OrderRepositoryImpl) GetOrderPayment(ctx context.Context, orderID int64) (*models.OrderPayment, error) {
	// logs.Info("获取订单支付信息: orderID=%d", orderID)

	payment := &models.OrderPayment{OrderID: orderID}
	err := r.ormer.Read(payment, "OrderID")
	if err == orm.ErrNoRows {
		return nil, nil
	}
	return payment, err
}

// UpdateOrderPayment 更新订单支付信息
func (r *OrderRepositoryImpl) UpdateOrderPayment(ctx context.Context, payment *models.OrderPayment) error {
	logs.Info("更新订单支付信息: %+v", payment)
	_, err := r.ormer.Update(payment)
	return err
}

// GetOrderItemsByOrderIDs 批量获取多个订单的订单项
func (r *OrderRepositoryImpl) GetOrderItemsByOrderIDs(ctx context.Context, orderIDs []int64) ([]*models.OrderItem, error) {
	logs.Info("批量获取订单项列表: orderIDs=%v", orderIDs)
	if len(orderIDs) == 0 {
		return []*models.OrderItem{}, nil
	}

	// 使用 IN 查询获取所有订单项
	var items []*models.OrderItem
	_, err := r.ormer.QueryTable(new(models.OrderItem)).Filter("OrderID__in", orderIDs).All(&items)
	if err != nil {
		logs.Error("批量查询订单项失败: %v", err)
		return nil, err
	}
	return items, nil
}

// GetOrderAddressesByOrderIDs 批量获取多个订单的地址信息
func (r *OrderRepositoryImpl) GetOrderAddressesByOrderIDs(ctx context.Context, orderIDs []int64) ([]*models.OrderAddress, error) {
	logs.Info("批量获取订单地址: orderIDs=%v", orderIDs)
	if len(orderIDs) == 0 {
		return []*models.OrderAddress{}, nil
	}

	// 查询所有订单地址
	var addresses []*models.OrderAddress
	_, err := r.ormer.QueryTable(new(models.OrderAddress)).Filter("OrderID__in", orderIDs).All(&addresses)
	if err != nil {
		logs.Error("批量查询订单地址失败: %v", err)
		return nil, err
	}
	return addresses, nil
}

// GetOrderPaymentsByOrderIDs 批量获取多个订单的支付信息
func (r *OrderRepositoryImpl) GetOrderPaymentsByOrderIDs(ctx context.Context, orderIDs []int64) ([]*models.OrderPayment, error) {
	logs.Info("批量获取订单支付信息: orderIDs=%v", orderIDs)
	if len(orderIDs) == 0 {
		return []*models.OrderPayment{}, nil
	}

	// 查询所有订单支付信息
	var payments []*models.OrderPayment
	_, err := r.ormer.QueryTable(new(models.OrderPayment)).Filter("OrderID__in", orderIDs).All(&payments)
	if err != nil {
		logs.Error("批量查询订单支付信息失败: %v", err)
		return nil, err
	}
	return payments, nil
}

// ListOrdersHighPerformance 高性能订单列表查询（轻量级）
func (r *OrderRepositoryImpl) ListOrdersHighPerformance(ctx context.Context, req *dto.HighPerformanceOrderListRequest) ([]*dto.HighPerformanceOrderItem, int64, error) {
	logs.Info("高性能查询订单列表: %+v", req)

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 50 {
		req.PageSize = 50 // 限制最大页面大小
	}

	// 构建WHERE条件
	whereConditions := []string{"o.deleted_at IS NULL"}
	args := []interface{}{}

	// 用户ID条件
	if req.UserID > 0 {
		whereConditions = append(whereConditions, "o.user_i_d = ?")
		args = append(args, req.UserID)
	}

	// 订单状态条件
	if req.Status > 0 {
		whereConditions = append(whereConditions, "o.status = ?")
		args = append(args, req.Status)
	}

	// 订单类型条件
	if req.OrderType > 0 {
		whereConditions = append(whereConditions, "o.order_type = ?")
		args = append(args, req.OrderType)
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM `order` o WHERE %s", whereClause)
	var total int64
	err := r.ormer.Raw(countSQL, args...).QueryRow(&total)
	if err != nil {
		logs.Error("查询订单总数失败: %v", err)
		return nil, 0, err
	}

	// 如果没有数据，直接返回
	if total == 0 {
		return []*dto.HighPerformanceOrderItem{}, 0, nil
	}

	// 计算分页
	offset := (req.Page - 1) * req.PageSize
	logs.Debug("分页计算: Page=%d, PageSize=%d, Offset=%d", req.Page, req.PageSize, offset)

	// 高性能查询SQL - 使用子查询获取订单项摘要信息，避免JOIN
	orderSQL := fmt.Sprintf(`
		SELECT
			o.i_d,
			o.order_no,
			o.status,
			o.pay_status,
			o.order_type,
			o.total_amount,
			o.created_at,
			(SELECT oi.product_name FROM order_item oi WHERE oi.order_i_d = o.i_d ORDER BY oi.i_d LIMIT 1) as first_product_name,
			(SELECT oi.product_image FROM order_item oi WHERE oi.order_i_d = o.i_d ORDER BY oi.i_d LIMIT 1) as first_product_image,
			(SELECT COUNT(*) FROM order_item oi WHERE oi.order_i_d = o.i_d) as item_count
		FROM `+"`order`"+` o
		WHERE %s
		ORDER BY o.created_at DESC
		LIMIT ? OFFSET ?`, whereClause)

	queryArgs := append(args, req.PageSize, offset)
	logs.Debug("执行SQL: %s", orderSQL)
	logs.Debug("SQL参数: %+v", queryArgs)

	// 执行查询
	var rawResults []orm.Params
	_, err = r.ormer.Raw(orderSQL, queryArgs...).Values(&rawResults)
	if err != nil {
		logs.Error("查询订单列表失败: %v", err)
		return nil, 0, err
	}

	// 转换结果
	orders := make([]*dto.HighPerformanceOrderItem, 0, len(rawResults))
	for i, row := range rawResults {
		order := &dto.HighPerformanceOrderItem{}

		// 添加调试信息
		logs.Debug("处理第%d行数据: %+v", i+1, row)

		// 基础字段转换 - 修复字段映射和类型转换
		if id, ok := row["i_d"]; ok {
			switch v := id.(type) {
			case int64:
				order.ID = v
			case int:
				order.ID = int64(v)
			case string:
				if idVal, err := strconv.ParseInt(v, 10, 64); err == nil {
					order.ID = idVal
				}
			}
			logs.Debug("ID字段: %v -> %d", id, order.ID)
		}

		if orderNo, ok := row["order_no"].(string); ok {
			order.OrderNo = orderNo
		}

		if status, ok := row["status"]; ok {
			switch v := status.(type) {
			case int64:
				order.Status = int(v)
			case int:
				order.Status = v
			case string:
				if statusVal, err := strconv.Atoi(v); err == nil {
					order.Status = statusVal
				}
			}
			order.StatusText = r.getOrderStatusText(order.Status)
			logs.Debug("Status字段: %v -> %d (%s)", status, order.Status, order.StatusText)
		}

		if payStatus, ok := row["pay_status"]; ok {
			switch v := payStatus.(type) {
			case int64:
				order.PayStatus = int(v)
			case int:
				order.PayStatus = v
			case string:
				if payStatusVal, err := strconv.Atoi(v); err == nil {
					order.PayStatus = payStatusVal
				}
			}
			order.PayStatusText = r.getPayStatusText(order.PayStatus)
			logs.Debug("PayStatus字段: %v -> %d (%s)", payStatus, order.PayStatus, order.PayStatusText)
		}

		if orderType, ok := row["order_type"]; ok {
			switch v := orderType.(type) {
			case int64:
				order.OrderType = int(v)
			case int:
				order.OrderType = v
			case string:
				if orderTypeVal, err := strconv.Atoi(v); err == nil {
					order.OrderType = orderTypeVal
				}
			}
			order.OrderTypeText = r.getOrderTypeText(order.OrderType)
			logs.Debug("OrderType字段: %v -> %d (%s)", orderType, order.OrderType, order.OrderTypeText)
		}

		if totalAmount, ok := row["total_amount"]; ok {
			switch v := totalAmount.(type) {
			case float64:
				order.TotalAmount = v
			case string:
				if amount, err := strconv.ParseFloat(v, 64); err == nil {
					order.TotalAmount = amount
				}
			}
			logs.Debug("TotalAmount字段: %v -> %f", totalAmount, order.TotalAmount)
		}

		if createdAt, ok := row["created_at"]; ok {
			logs.Debug("CreatedAt字段类型: %T, 值: %v", createdAt, createdAt)
			switch v := createdAt.(type) {
			case time.Time:
				order.CreateTime = v
				logs.Debug("直接使用time.Time类型: %s", v.Format("2006-01-02 15:04:05"))
			case string:
				logs.Debug("尝试解析字符串时间: %s", v)
				// 直接解析时间字符串，不进行复杂转换
				if t, err := time.Parse(time.RFC3339, v); err == nil {
					order.CreateTime = t
					logs.Debug("RFC3339解析成功: %s", t.Format("2006-01-02 15:04:05"))
				} else {
					// 如果RFC3339解析失败，尝试MySQL格式
					if t, err := time.Parse("2006-01-02 15:04:05", v); err == nil {
						order.CreateTime = t
						logs.Debug("MySQL格式解析成功: %s", t.Format("2006-01-02 15:04:05"))
					} else {
						logs.Debug("时间解析失败: %v", err)
					}
				}
			default:
				logs.Debug("未知的时间类型: %T", v)
			}
			logs.Debug("最终CreateTime: %s", order.CreateTime.Format("2006-01-02 15:04:05"))
		}

		// 订单项摘要信息
		if firstName, ok := row["first_product_name"].(string); ok {
			order.FirstProductName = firstName
		}
		if firstImage, ok := row["first_product_image"].(string); ok {
			order.FirstProductImage = firstImage
		}
		if itemCount, ok := row["item_count"]; ok {
			switch v := itemCount.(type) {
			case int64:
				order.ItemCount = int(v)
			case int:
				order.ItemCount = v
			case string:
				if countVal, err := strconv.Atoi(v); err == nil {
					order.ItemCount = countVal
				}
			}
			// 构建订单项摘要
			if order.ItemCount > 1 {
				order.ItemSummary = fmt.Sprintf("%s等%d件商品", order.FirstProductName, order.ItemCount)
			} else {
				order.ItemSummary = order.FirstProductName
			}
			logs.Debug("ItemCount字段: %v -> %d", itemCount, order.ItemCount)
		}

		logs.Debug("转换后的订单数据: %+v", order)
		orders = append(orders, order)
	}

	return orders, total, nil
}

// getOrderStatusText 获取订单状态文本
func (r *OrderRepositoryImpl) getOrderStatusText(status int) string {
	statusMap := map[int]string{
		10: "待付款",
		20: "已付款",
		30: "处理中",
		40: "配送中",
		45: "已送达",
		50: "已完成",
		60: "已取消",
		70: "退款中",
		80: "已退款",
	}
	if text, ok := statusMap[status]; ok {
		return text
	}
	return "未知状态"
}

// getPayStatusText 获取支付状态文本
func (r *OrderRepositoryImpl) getPayStatusText(payStatus int) string {
	statusMap := map[int]string{
		0: "未支付",
		1: "支付中",
		2: "已支付",
		3: "支付失败",
	}
	if text, ok := statusMap[payStatus]; ok {
		return text
	}
	return "未知状态"
}

// getOrderTypeText 获取订单类型文本
func (r *OrderRepositoryImpl) getOrderTypeText(orderType int) string {
	typeMap := map[int]string{
		0: "普通订单",
		1: "外卖订单",
		2: "跑腿订单",
	}
	if text, ok := typeMap[orderType]; ok {
		return text
	}
	return "未知类型"
}
