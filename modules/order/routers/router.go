/**
 * 订单模块路由配置
 *
 * 本文件定义了订单模块的路由配置，将HTTP请求映射到对应的控制器方法上。
 * 包括订单创建、查询、支付、取消等API路由定义。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/order/controllers"
)

// InitRouters 初始化订单模块路由
func InitRouters() {
	// 订单管理路由
	orderNs := web.NewNamespace("/api/v1/orders",
		// 添加JWT认证中间件
		web.NSBefore(middlewares.JWTFilter),
		// 添加用户操作日志中间件
		web.NSBefore(middlewares.UserLogMiddleware()),
		// 添加性能监控中间件
		web.NSBefore(middlewares.OrderListPerformanceMiddleware()),

		// 创建订单
		web.NSRouter("/create", &controllers.OrderController{}, "post:CreateOrder"),

		// 获取订单详情
		web.NSRouter("/:id", &controllers.OrderController{}, "get:GetOrder"),

		// 获取订单列表
		web.NSRouter("/list", &controllers.OrderController{}, "post:ListOrders"),

		// 轻量级订单列表（高性能）
		web.NSRouter("/simple/list", &controllers.OrderListController{}, "get:ListOrders"),
		web.NSRouter("/simple/status-count", &controllers.OrderListController{}, "get:GetOrderStatusCount"),
		// 超高性能订单列表（最优化）
		web.NSRouter("/simple/high-performance", &controllers.OrderListController{}, "get:ListOrdersHighPerformance"),

		// 性能监控接口
		web.NSRouter("/performance/stats", &controllers.PerformanceController{}, "get:GetPerformanceStats"),
		web.NSRouter("/performance/slow-queries", &controllers.PerformanceController{}, "get:GetSlowQueries"),
		web.NSRouter("/performance/reset", &controllers.PerformanceController{}, "post:ResetStats"),
		web.NSRouter("/performance/log-report", &controllers.PerformanceController{}, "post:LogStats"),
		web.NSRouter("/performance/order-list", &controllers.PerformanceController{}, "get:GetOrderListPerformance"),

		// 取消订单
		web.NSRouter("/:id/cancel", &controllers.OrderController{}, "post:CancelOrder"),

		// 支付订单
		web.NSRouter("/:id/pay", &controllers.OrderController{}, "post:PayOrder"),

		// 发货
		web.NSRouter("/:id/ship", &controllers.OrderController{}, "post:ShipOrder"),

		// 确认收货
		web.NSRouter("/:id/receive", &controllers.OrderController{}, "post:ConfirmReceive"),

		// 完成订单
		web.NSRouter("/:id/complete", &controllers.OrderController{}, "post:CompleteOrder"),

		// 删除订单
		web.NSRouter("/:id", &controllers.OrderController{}, "delete:DeleteOrder"),

		// 获取订单统计信息
		web.NSRouter("/statistics", &controllers.OrderController{}, "get:GetOrderStatistics"),

		// 获取订单日志
		web.NSRouter("/:id/logs", &controllers.OrderController{}, "get:GetOrderLogs"),

		// ===== 订单支付相关路由 =====
		// 发起支付
		web.NSRouter("/:order_id/payment", &controllers.OrderPaymentController{}, "post:InitiatePayment"),

		// 查询支付状态
		web.NSRouter("/:order_id/payment/status", &controllers.OrderPaymentController{}, "get:QueryPaymentStatus"),

		// 发起退款
		web.NSRouter("/:order_id/refund", &controllers.OrderPaymentController{}, "post:InitiateRefund"),
	)

	// 注册命名空间
	web.AddNamespace(orderNs)

	// 支付回调路由（不需要认证）
	web.Router("/api/payment/callback/:method/:order_no", &controllers.OrderPaymentController{}, "post:HandlePaymentCallback")
	web.Router("/api/refund/callback/:method/:order_no", &controllers.OrderPaymentController{}, "post:HandleRefundCallback")
}
