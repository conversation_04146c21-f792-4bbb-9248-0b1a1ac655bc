/**
 * order_payment_service_impl.go
 * 订单支付服务实现
 *
 * 该服务负责集成订单系统和支付系统，处理订单支付、退款等操作
 */

package impl

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/order/constants"
	"o_mall_backend/modules/order/dto"
	"o_mall_backend/modules/order/models"
	"o_mall_backend/modules/order/repositories"
	paymentDto "o_mall_backend/modules/payment/dto"
	paymentModels "o_mall_backend/modules/payment/models"
	paymentServices "o_mall_backend/modules/payment/services"
)

// OrderPaymentService 订单支付服务接口
type OrderPaymentService interface {
	// InitiatePayment 发起支付
	InitiatePayment(ctx context.Context, orderID int64, payMethod int, userID int64) (*dto.PaymentResponse, error)

	// HandlePaymentCallback 处理支付回调
	HandlePaymentCallback(ctx context.Context, orderNo string, paymentNo string, transactionID string, amount float64, payMethod int, rawData string) error

	// QueryPaymentStatus 查询支付状态
	QueryPaymentStatus(ctx context.Context, orderID int64) (*dto.PaymentStatusResponse, error)

	// InitiateRefund 发起退款
	InitiateRefund(ctx context.Context, orderID int64, refundAmount float64, reason string, userID int64) (*dto.RefundResponse, error)

	// HandleRefundCallback 处理退款回调
	HandleRefundCallback(ctx context.Context, orderNo string, refundNo string, externalRefundNo string, amount float64, rawData string) error
}

// OrderPaymentServiceImpl 订单支付服务实现
type OrderPaymentServiceImpl struct {
	orderRepo      repositories.OrderRepository
	paymentService paymentServices.PaymentService
	refundService  paymentServices.RefundService
}

// NewOrderPaymentService 创建订单支付服务实例
func NewOrderPaymentService() OrderPaymentService {
	return &OrderPaymentServiceImpl{
		orderRepo:      repositories.NewOrderRepository(),
		paymentService: paymentServices.NewPaymentService(),
		refundService:  paymentServices.NewRefundService(),
	}
}

// InitiatePayment 发起支付
func (s *OrderPaymentServiceImpl) InitiatePayment(ctx context.Context, orderID int64, payMethod int, userID int64) (*dto.PaymentResponse, error) {
	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, orderID)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return nil, err
	}

	if order == nil {
		return nil, errors.New("订单不存在")
	}

	// 检查订单是否属于当前用户
	if order.UserID != userID {
		return nil, errors.New("无权操作此订单")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusPending {
		return nil, errors.New("订单状态不允许支付")
	}

	// 检查订单支付状态
	if order.PayStatus == constants.PayStatusPaid {
		return nil, errors.New("订单已支付")
	}

	// 转换支付方式
	var paymentMethod paymentModels.PaymentMethod
	switch payMethod {
	case constants.PayMethodWechat:
		paymentMethod = paymentModels.PaymentMethodWechat
	case constants.PayMethodAlipay:
		paymentMethod = paymentModels.PaymentMethodAlipay
	case constants.PayMethodBalance:
		paymentMethod = paymentModels.PaymentMethodBalance
	default:
		return nil, errors.New("不支持的支付方式")
	}

	// 创建支付记录
	createPaymentReq := &paymentDto.PaymentCreateRequest{
		OrderID:    orderID,
		UserID:     userID,
		Amount:     order.PayAmount,
		Method:     paymentMethod,
		ClientIP:   "",    // 客户端IP，实际应从请求中获取
		DeviceInfo: "Web", // 设备信息
		ReturnURL:  fmt.Sprintf("/pages/order/payment-result?order_id=%d&status=success", orderID),
		Remark:     fmt.Sprintf("订单支付-%s", order.OrderNo),
	}

	// 调用支付服务创建支付
	paymentResp, err := s.paymentService.CreatePayment(createPaymentReq)
	if err != nil {
		logs.Error("创建支付失败: %v", err)
		return nil, err
	}

	// 保存支付信息到订单支付记录
	payment := &models.OrderPayment{
		OrderID:       orderID,
		OrderNo:       order.OrderNo,
		UserID:        userID,
		PaymentNo:     paymentResp.TransactionNo, // 使用TransactionNo作为PaymentNo
		PaymentMethod: payMethod,
		PaymentAmount: order.PayAmount,
		PaymentStatus: constants.PayStatusProcessing, // 支付处理中
		TransactionID: "",
	}

	// 检查是否已有支付记录
	existPayment, err := s.orderRepo.GetOrderPayment(ctx, orderID)
	if err != nil {
		logs.Error("查询订单支付信息失败: %v", err)
		return nil, err
	}

	if existPayment == nil {
		// 添加支付记录
		_, err = s.orderRepo.AddOrderPayment(ctx, payment)
		if err != nil {
			logs.Error("创建订单支付记录失败: %v", err)
			return nil, err
		}
	} else {
		// 更新支付记录
		payment.ID = existPayment.ID
		err = s.orderRepo.UpdateOrderPayment(ctx, payment)
		if err != nil {
			logs.Error("更新订单支付记录失败: %v", err)
			return nil, err
		}
	}

	// 构建返回的Map参数
	var appPayParams map[string]string
	var webPayParams map[string]string

	// 将JSON字符串转为Map
	if paymentResp.AppPayParams != "" {
		if err := json.Unmarshal([]byte(paymentResp.AppPayParams), &appPayParams); err != nil {
			appPayParams = make(map[string]string)
		}
	} else {
		appPayParams = make(map[string]string)
	}

	if paymentResp.WebPayParams != "" {
		if err := json.Unmarshal([]byte(paymentResp.WebPayParams), &webPayParams); err != nil {
			webPayParams = make(map[string]string)
		}
	} else {
		webPayParams = make(map[string]string)
	}

	// 创建过期时间
	var expireTime *time.Time
	if paymentResp.ExpireTime > 0 {
		t := time.Now().Add(time.Duration(paymentResp.ExpireTime) * time.Second)
		expireTime = &t
	}

	// 构建响应
	response := &dto.PaymentResponse{
		OrderID:      orderID,
		OrderNo:      order.OrderNo,
		PaymentID:    paymentResp.PaymentID,
		PaymentNo:    paymentResp.TransactionNo, // 使用TransactionNo作为PaymentNo
		Amount:       order.PayAmount,
		PayMethod:    payMethod,
		PaymentURL:   paymentResp.PaymentURL,
		QrCodeURL:    paymentResp.QrCodeURL,
		AppPayParams: appPayParams,
		WebPayParams: webPayParams,
		ExpireTime:   expireTime,
	}

	return response, nil
}

// HandlePaymentCallback 处理支付回调
func (s *OrderPaymentServiceImpl) HandlePaymentCallback(ctx context.Context, orderNo string, paymentNo string, transactionID string, amount float64, payMethod int, rawData string) error {
	// 查询订单
	order, err := s.orderRepo.GetOrderByOrderNo(ctx, orderNo)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 检查订单状态
	if order.PayStatus == constants.PayStatusPaid {
		logs.Info("订单已支付，忽略重复回调: %s", orderNo)
		return nil
	}

	// 更新订单状态
	payTime := time.Now()
	err = s.orderRepo.UpdatePayStatus(ctx, order.ID, constants.PayStatusPaid, payTime)
	if err != nil {
		logs.Error("更新订单支付状态失败: %v", err)
		return err
	}

	// 订单状态变更为已支付
	err = s.orderRepo.UpdateOrderStatus(ctx, order.ID, constants.OrderStatusPaid)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return err
	}

	// 更新订单支付信息
	payment, err := s.orderRepo.GetOrderPayment(ctx, order.ID)
	if err != nil {
		logs.Error("查询订单支付信息失败: %v", err)
		return err
	}

	if payment != nil {
		payment.PaymentStatus = constants.PayStatusPaid
		payment.PaymentTime = payTime
		payment.TransactionID = transactionID
		payment.ProviderReply = rawData

		err = s.orderRepo.UpdateOrderPayment(ctx, payment)
		if err != nil {
			logs.Error("更新订单支付信息失败: %v", err)
			return err
		}
	}

	// 记录订单日志
	orderLog := &models.OrderLog{
		OrderID: order.ID,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "payment",
		Status:  constants.OrderStatusPaid,
		Content: fmt.Sprintf("订单支付成功，支付方式：%s，支付金额：%.2f", constants.PayMethodMap[payMethod], amount),
	}

	_, err = s.orderRepo.AddOrderLog(ctx, orderLog)
	if err != nil {
		logs.Error("创建订单日志失败: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}

// QueryPaymentStatus 查询支付状态
func (s *OrderPaymentServiceImpl) QueryPaymentStatus(ctx context.Context, orderID int64) (*dto.PaymentStatusResponse, error) {
	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, orderID)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return nil, err
	}

	if order == nil {
		return nil, errors.New("订单不存在")
	}

	// 查询订单支付信息
	payment, err := s.orderRepo.GetOrderPayment(ctx, orderID)
	if err != nil {
		logs.Error("查询订单支付信息失败: %v", err)
		return nil, err
	}

	if payment == nil {
		return nil, errors.New("订单支付信息不存在")
	}

	// 查询支付状态
	queryReq := &paymentDto.PaymentQueryRequest{
		PaymentID:     payment.ID,
		TransactionNo: payment.TransactionID,
		OrderID:       orderID,
	}

	queryResp, err := s.paymentService.QueryPayment(queryReq)
	if err != nil {
		logs.Error("查询支付状态失败: %v", err)
		return nil, err
	}

	// 如果支付状态发生变化，更新订单状态
	if queryResp.Status == paymentModels.PaymentStatusSuccess && order.PayStatus != constants.PayStatusPaid {
		// 更新订单支付状态
		payTime := time.Now()
		if queryResp.PaymentTime != nil {
			payTime = *queryResp.PaymentTime
		}

		err = s.orderRepo.UpdatePayStatus(ctx, orderID, constants.PayStatusPaid, payTime)
		if err != nil {
			logs.Error("更新订单支付状态失败: %v", err)
			return nil, err
		}

		// 订单状态变更为已支付
		err = s.orderRepo.UpdateOrderStatus(ctx, orderID, constants.OrderStatusPaid)
		if err != nil {
			logs.Error("更新订单状态失败: %v", err)
			return nil, err
		}

		// 更新订单支付信息
		payment.PaymentStatus = constants.PayStatusPaid
		payment.PaymentTime = payTime
		payment.TransactionID = queryResp.TransactionNo

		err = s.orderRepo.UpdateOrderPayment(ctx, payment)
		if err != nil {
			logs.Error("更新订单支付信息失败: %v", err)
			return nil, err
		}

		// 记录订单日志
		orderLog := &models.OrderLog{
			OrderID: orderID,
			OrderNo: order.OrderNo,
			UserID:  order.UserID,
			Action:  "payment_query",
			Status:  constants.OrderStatusPaid,
			Content: fmt.Sprintf("查询支付状态显示已支付，金额：%.2f", order.PayAmount),
		}

		_, err = s.orderRepo.AddOrderLog(ctx, orderLog)
		if err != nil {
			logs.Error("创建订单日志失败: %v", err)
			// 不影响主流程，继续执行
		}
	}

	// 构建响应
	statusDesc := "未知状态"
	switch payment.PaymentStatus {
	case constants.PayStatusUnpaid:
		statusDesc = "未支付"
	case constants.PayStatusPaid:
		statusDesc = "已支付"
	case constants.PayStatusProcessing:
		statusDesc = "支付处理中"
	case constants.PayStatusFailed:
		statusDesc = "支付失败"
	case constants.PayStatusRefunding:
		statusDesc = "退款中"
	case constants.PayStatusRefunded:
		statusDesc = "已退款"
	}

	response := &dto.PaymentStatusResponse{
		OrderID:       orderID,
		OrderNo:       order.OrderNo,
		PaymentNo:     payment.PaymentNo,
		Status:        payment.PaymentStatus,
		StatusDesc:    statusDesc,
		PaymentMethod: payment.PaymentMethod,
		Amount:        order.TotalAmount,
		PaidAmount:    payment.PaymentAmount,
		TransactionID: payment.TransactionID,
	}

	if payment.PaymentTime.IsZero() {
		response.PaidTime = time.Time{}
	} else {
		response.PaidTime = payment.PaymentTime
	}

	return response, nil
}

// InitiateRefund 发起退款
func (s *OrderPaymentServiceImpl) InitiateRefund(ctx context.Context, orderID int64, refundAmount float64, reason string, userID int64) (*dto.RefundResponse, error) {
	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, orderID)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return nil, err
	}

	if order == nil {
		return nil, errors.New("订单不存在")
	}

	// 检查订单是否属于当前用户
	if order.UserID != userID {
		return nil, errors.New("无权操作此订单")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusPaid && order.Status != constants.OrderStatusShipped {
		return nil, errors.New("当前订单状态不允许退款")
	}

	// 检查订单支付状态
	if order.PayStatus != constants.PayStatusPaid {
		return nil, errors.New("订单未支付，无法退款")
	}

	// 检查退款金额
	if refundAmount <= 0 || refundAmount > order.PayAmount {
		return nil, fmt.Errorf("退款金额无效，应在0-%.2f之间", order.PayAmount)
	}

	// 获取订单支付信息
	payment, err := s.orderRepo.GetOrderPayment(ctx, orderID)
	if err != nil {
		logs.Error("查询订单支付信息失败: %v", err)
		return nil, err
	}

	if payment == nil {
		return nil, errors.New("订单支付信息不存在")
	}

	// 创建退款请求
	refundReq := &paymentDto.RefundCreateRequest{
		PaymentID:    payment.ID,
		OrderID:      orderID,
		UserID:       userID,
		Amount:       refundAmount,
		Reason:       reason,
		OperatorID:   0,     // 用户操作，不需要管理员ID
		NeedApproval: false, // 默认不需要审批
	}

	// 调用支付服务发起退款
	refundResp, err := s.refundService.CreateRefund(refundReq)
	if err != nil {
		logs.Error("创建退款失败: %v", err)
		return nil, err
	}

	// 更新订单状态为退款中
	err = s.orderRepo.UpdateOrderStatus(ctx, orderID, constants.OrderStatusRefunding)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		// 继续执行，不影响退款流程
	}

	// 更新订单支付状态
	err = s.orderRepo.UpdatePayStatus(ctx, orderID, constants.PayStatusRefunding, time.Time{})
	if err != nil {
		logs.Error("更新订单支付状态失败: %v", err)
		// 继续执行，不影响退款流程
	}

	// 记录订单日志
	orderLog := &models.OrderLog{
		OrderID: orderID,
		OrderNo: order.OrderNo,
		UserID:  userID,
		Action:  "refund",
		Status:  constants.OrderStatusRefunding,
		Content: fmt.Sprintf("发起退款: 退款金额%.2f, 退款原因: %s", refundAmount, reason),
	}

	_, err = s.orderRepo.AddOrderLog(ctx, orderLog)
	if err != nil {
		logs.Error("创建订单日志失败: %v", err)
		// 不影响主流程，继续执行
	}

	// 构建响应
	response := &dto.RefundResponse{
		OrderID:    orderID,
		OrderNo:    order.OrderNo,
		RefundID:   refundResp.RefundID,
		RefundNo:   refundResp.RefundNo,
		Amount:     refundAmount,
		Status:     refundResp.Status,
		StatusDesc: "退款处理中", // 根据状态生成描述
	}

	return response, nil
}

// HandleRefundCallback 处理退款回调
func (s *OrderPaymentServiceImpl) HandleRefundCallback(ctx context.Context, orderNo string, refundNo string, externalRefundNo string, amount float64, rawData string) error {
	// 查询订单
	order, err := s.orderRepo.GetOrderByOrderNo(ctx, orderNo)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 检查订单状态
	if order.Status != constants.OrderStatusRefunding {
		// 如果订单已经是退款完成状态，忽略重复回调
		if order.Status == constants.OrderStatusRefunded {
			logs.Info("订单已退款，忽略重复回调: %s", orderNo)
			return nil
		}

		logs.Warn("订单状态不是退款中: %d", order.Status)
		// 继续处理，不中断流程
	}

	// 更新订单状态为已退款
	err = s.orderRepo.UpdateOrderStatus(ctx, order.ID, constants.OrderStatusRefunded)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return err
	}

	// 更新订单支付状态
	refundTime := time.Now()
	err = s.orderRepo.UpdatePayStatus(ctx, order.ID, constants.PayStatusRefunded, refundTime)
	if err != nil {
		logs.Error("更新订单支付状态失败: %v", err)
		return err
	}

	// 更新订单退款金额
	order.RefundAmount = amount
	order.RefundTime = refundTime
	err = s.orderRepo.UpdateOrder(ctx, order)
	if err != nil {
		logs.Error("更新订单退款金额失败: %v", err)
		// 继续执行，不影响退款处理
	}

	// 记录订单日志
	orderLog := &models.OrderLog{
		OrderID: order.ID,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "refund_callback",
		Status:  constants.OrderStatusRefunded,
		Content: fmt.Sprintf("退款成功: 退款单号%s, 外部退款单号%s, 退款金额%.2f", refundNo, externalRefundNo, amount),
	}

	_, err = s.orderRepo.AddOrderLog(ctx, orderLog)
	if err != nil {
		logs.Error("创建订单日志失败: %v", err)
		// 不影响主流程，继续执行
	}

	return nil
}
