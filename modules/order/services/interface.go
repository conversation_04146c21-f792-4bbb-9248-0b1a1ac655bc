/**
 * 订单服务接口定义
 *
 * 本文件定义了订单模块的服务层接口，提供订单业务逻辑的处理方法。
 * 包括订单的创建、查询、支付、取消等功能接口定义。
 */

package services

import (
	"context"

	"o_mall_backend/modules/order/dto"
)

// OrderService 订单服务接口
type OrderService interface {
	// 创建订单
	CreateOrder(ctx context.Context, req *dto.CreateOrderRequest) (*dto.OrderResponse, error)

	// 获取订单详情
	GetOrder(ctx context.Context, id int64) (*dto.OrderResponse, error)

	// 获取订单列表
	ListOrders(ctx context.Context, req *dto.OrderQueryRequest) (*dto.OrderListResponse, error)

	// 取消订单
	CancelOrder(ctx context.Context, id int64, req *dto.CancelOrderRequest) error

	// 支付订单
	PayOrder(ctx context.Context, id int64, req *dto.PayOrderRequest) error

	// 发货
	ShipOrder(ctx context.Context, id int64, req *dto.ShipOrderRequest) error

	// 确认收货
	ConfirmReceive(ctx context.Context, id int64) error

	// 完成订单
	CompleteOrder(ctx context.Context, id int64) error

	// 删除订单
	DeleteOrder(ctx context.Context, id int64) error

	// 获取订单统计信息
	GetOrderStatistics(ctx context.Context) (*dto.OrderStatisticsResponse, error)

	// 获取订单日志
	GetOrderLogs(ctx context.Context, orderID int64) ([]*dto.OrderLogResponse, error)

	// 生成订单号
	GenerateOrderNo(ctx context.Context) (string, error)

	// 高性能订单列表查询
	ListOrdersHighPerformance(ctx context.Context, req *dto.HighPerformanceOrderListRequest) (*dto.HighPerformanceOrderListResponse, error)
}
