/**
 * 订单缓存服务
 *
 * 本文件实现了订单相关的缓存逻辑，提供高性能的订单数据缓存和失效机制。
 * 主要用于订单列表查询的性能优化。
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/order/dto"
	"o_mall_backend/utils/redis"
)

// OrderCacheService 订单缓存服务接口
type OrderCacheService interface {
	// 获取缓存的订单列表
	GetOrderListCache(ctx context.Context, req *dto.HighPerformanceOrderListRequest) (*dto.HighPerformanceOrderListResponse, error)

	// 设置订单列表缓存
	SetOrderListCache(ctx context.Context, req *dto.HighPerformanceOrderListRequest, resp *dto.HighPerformanceOrderListResponse) error

	// 清除用户订单列表缓存
	ClearUserOrderListCache(ctx context.Context, userID int64) error

	// 清除订单相关缓存
	ClearOrderCache(ctx context.Context, orderID int64) error
}

// OrderCacheServiceImpl 订单缓存服务实现
type OrderCacheServiceImpl struct {
	// 使用项目的Redis工具包，不需要存储客户端实例
}

// NewOrderCacheService 创建订单缓存服务
func NewOrderCacheService() OrderCacheService {
	return &OrderCacheServiceImpl{}
}

// 缓存键常量
const (
	// 订单列表缓存键模板
	OrderListCacheKeyTemplate = "order:list:user:%d:status:%d:type:%d:page:%d:size:%d"
	// 订单详情缓存键模板
	OrderDetailCacheKeyTemplate = "order:detail:%d"
	// 用户订单列表缓存键前缀
	UserOrderListCachePrefix = "order:list:user:%d:*"
	// 缓存过期时间
	OrderListCacheTTL   = 5 * time.Minute
	OrderDetailCacheTTL = 10 * time.Minute
)

// GetOrderListCache 获取缓存的订单列表
func (s *OrderCacheServiceImpl) GetOrderListCache(ctx context.Context, req *dto.HighPerformanceOrderListRequest) (*dto.HighPerformanceOrderListResponse, error) {
	// 构建缓存键
	cacheKey := fmt.Sprintf(OrderListCacheKeyTemplate, req.UserID, req.Status, req.OrderType, req.Page, req.PageSize)

	// 从Redis获取缓存
	cached, err := redis.Get(cacheKey)
	if err != nil {
		// 缓存未命中或获取失败
		logs.Debug("订单列表缓存未命中: %s, 错误: %v", cacheKey, err)
		return nil, nil
	}

	if cached == "" {
		// 缓存为空
		logs.Debug("订单列表缓存为空: %s", cacheKey)
		return nil, nil
	}

	// 反序列化缓存数据
	var resp dto.HighPerformanceOrderListResponse
	err = json.Unmarshal([]byte(cached), &resp)
	if err != nil {
		logs.Error("反序列化订单列表缓存失败: %v", err)
		// 删除损坏的缓存
		redis.Del(cacheKey)
		return nil, err
	}

	logs.Debug("订单列表缓存命中: %s", cacheKey)
	return &resp, nil
}

// SetOrderListCache 设置订单列表缓存
func (s *OrderCacheServiceImpl) SetOrderListCache(ctx context.Context, req *dto.HighPerformanceOrderListRequest, resp *dto.HighPerformanceOrderListResponse) error {
	// 构建缓存键
	cacheKey := fmt.Sprintf(OrderListCacheKeyTemplate, req.UserID, req.Status, req.OrderType, req.Page, req.PageSize)

	// 序列化数据
	data, err := json.Marshal(resp)
	if err != nil {
		logs.Error("序列化订单列表缓存失败: %v", err)
		return err
	}

	// 设置缓存
	err = redis.Set(cacheKey, string(data), OrderListCacheTTL)
	if err != nil {
		logs.Error("设置订单列表缓存失败: %v", err)
		return err
	}

	logs.Debug("设置订单列表缓存成功: %s", cacheKey)
	return nil
}

// ClearUserOrderListCache 清除用户订单列表缓存
func (s *OrderCacheServiceImpl) ClearUserOrderListCache(ctx context.Context, userID int64) error {
	// 由于项目的Redis工具包没有Keys方法，我们删除常用的缓存键
	// 这是一个简化的实现，删除用户最常访问的缓存键

	commonCacheKeys := []string{
		// 全部订单，不同分页
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 0, 0, 1, 10),
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 0, 0, 1, 20),
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 0, 0, 2, 10),
		// 不同状态的订单
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 10, 0, 1, 10), // 待付款
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 20, 0, 1, 10), // 已付款
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 30, 0, 1, 10), // 处理中
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 40, 0, 1, 10), // 配送中
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 50, 0, 1, 10), // 已完成
		// 外卖订单
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 0, 1, 1, 10),
		fmt.Sprintf(OrderListCacheKeyTemplate, userID, 0, 1, 1, 20),
	}

	deletedCount := 0
	for _, key := range commonCacheKeys {
		_, err := redis.Del(key)
		if err == nil {
			deletedCount++
		}
	}

	logs.Info("清除用户订单列表缓存完成，用户ID: %d，尝试清除键数量: %d", userID, deletedCount)
	return nil
}

// ClearOrderCache 清除订单相关缓存
func (s *OrderCacheServiceImpl) ClearOrderCache(ctx context.Context, orderID int64) error {
	// 这里可以根据订单ID查询用户ID，然后清除相关缓存
	// 为了简化，暂时只记录日志
	logs.Info("清除订单相关缓存，订单ID: %d", orderID)

	// 清除订单详情缓存
	detailCacheKey := fmt.Sprintf(OrderDetailCacheKeyTemplate, orderID)
	_, err := redis.Del(detailCacheKey)
	if err != nil {
		logs.Error("删除订单详情缓存失败: %v", err)
		return err
	}

	return nil
}

// GetCacheStats 获取缓存统计信息（用于监控）
func (s *OrderCacheServiceImpl) GetCacheStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})

	// 由于项目的Redis工具包功能有限，这里只返回基本信息
	stats["cache_service"] = "order_cache_service"
	stats["cache_enabled"] = true
	stats["cache_ttl_seconds"] = int64(OrderListCacheTTL.Seconds())

	return stats
}
