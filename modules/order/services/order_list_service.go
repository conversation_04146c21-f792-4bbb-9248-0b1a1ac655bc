/**
 * 订单列表服务接口
 *
 * 本文件定义了轻量级订单列表查询的服务接口。
 * 专门用于订单列表页面，提供高性能的基础信息查询功能。
 */

package services

import "o_mall_backend/modules/order/dto"

// OrderListService 订单列表服务接口
type OrderListService interface {
	// GetOrderList 获取订单列表（轻量级）
	GetOrderList(req *dto.OrderListRequest) (*dto.SimpleOrderListResponse, error)

	// GetOrderStatusCount 获取各状态订单数量统计
	GetOrderStatusCount(userID int64) (*dto.OrderStatusCountResponse, error)
}

// NewOrderListService 创建订单列表服务实例
func NewOrderListService() OrderListService {
	return NewOrderListServiceImpl()
}