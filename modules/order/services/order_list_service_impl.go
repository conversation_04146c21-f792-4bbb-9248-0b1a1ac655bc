/**
 * 订单列表服务实现
 *
 * 本文件实现了轻量级订单列表查询的服务逻辑。
 * 专门用于订单列表页面，避免复杂的JSON解析和不必要的关联查询，提高性能。
 */

package services

import (
	"math"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/order/dto"
	"o_mall_backend/modules/order/repositories"
)

// orderListService 订单列表服务实现
type orderListService struct {
	orderListRepo repositories.OrderListRepository
}

// 初始化服务，确保仓库实例被创建
func NewOrderListServiceImpl() *orderListService {
	return &orderListService{
		orderListRepo: repositories.NewOrderListRepository(),
	}
}

// GetOrderList 获取订单列表（轻量级）
func (s *orderListService) GetOrderList(req *dto.OrderListRequest) (*dto.SimpleOrderListResponse, error) {
	logs.Info("查询轻量级订单列表: %+v", req)

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大页面大小
	}

	// 查询订单列表
	orders, total, err := s.orderListRepo.GetOrderList(req)
	if err != nil {
		logs.Error("查询订单列表失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	orderItems := make([]*dto.OrderListItem, 0, len(orders))
	for _, order := range orders {
		orderItem := &dto.OrderListItem{
			ID:            order.ID,
			OrderNo:       order.OrderNo,
			Status:        order.Status,
			StatusText:    s.getOrderStatusText(order.Status),
			PayStatus:     order.PayStatus,
			PayStatusText: s.getPayStatusText(order.PayStatus),
			OrderType:     order.OrderType,
			OrderTypeText: s.getOrderTypeText(order.OrderType),
			TotalAmount:   order.TotalAmount,
			CreateTime:    order.CreateTime,
			Items:         make([]*dto.OrderListItemInfo, 0),
		}

		// 添加订单项信息
		for _, item := range order.Items {
			orderItem.Items = append(orderItem.Items, &dto.OrderListItemInfo{
				ID:               item.ID,
				ProductID:        item.ProductID,
				ProductName:      item.ProductName,
				ProductImage:     item.ProductImage,
				Price:            item.Price,
				Quantity:         item.Quantity,
				SubtotalAmount:   item.SubtotalAmount,
				SkuAttributes:    item.SkuAttributes,
				VariantSnapshot:  item.VariantSnapshot,
				ProductSnapshot:  item.ProductSnapshot,
				ComboSelections:  item.ComboSelections,
			})
		}

		orderItems = append(orderItems, orderItem)
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	response := &dto.SimpleOrderListResponse{
		Orders:     orderItems,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	logs.Info("成功查询订单列表，总数: %d，当前页: %d，页面大小: %d", total, req.Page, req.PageSize)
	return response, nil
}

// GetOrderStatusCount 获取各状态订单数量统计
func (s *orderListService) GetOrderStatusCount(userID int64) (*dto.OrderStatusCountResponse, error) {
	logs.Info("查询用户订单状态统计，用户ID: %d", userID)

	// 查询状态统计
	counts, err := s.orderListRepo.GetOrderStatusCount(userID)
	if err != nil {
		logs.Error("查询订单状态统计失败: %v", err)
		return nil, err
	}

	logs.Info("成功查询订单状态统计")
	return counts, nil
}

// getOrderStatusText 获取订单状态文本
func (s *orderListService) getOrderStatusText(status int) string {
	switch status {
	case 1:
		return "待付款"
	case 2:
		return "待发货"
	case 3:
		return "待收货"
	case 4:
		return "已完成"
	case 5:
		return "已取消"
	default:
		return "未知状态"
	}
}

// getPayStatusText 获取支付状态文本
func (s *orderListService) getPayStatusText(payStatus int) string {
	switch payStatus {
	case 1:
		return "未支付"
	case 2:
		return "已支付"
	case 3:
		return "已退款"
	default:
		return "未知状态"
	}
}

// getOrderTypeText 获取订单类型文本
func (s *orderListService) getOrderTypeText(orderType int) string {
	switch orderType {
	case 1:
		return "外卖订单"
	case 2:
		return "普通订单"
	default:
		return "未知类型"
	}
}