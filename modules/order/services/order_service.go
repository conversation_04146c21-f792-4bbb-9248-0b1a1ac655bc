/**
 * 订单服务实现
 *
 * 本文件实现了订单服务接口，提供完整的订单业务逻辑处理功能。
 * 包括订单的创建、查询、支付、发货、收货、取消等业务操作。
 */

package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/order/constants"
	"o_mall_backend/modules/order/dto"
	"o_mall_backend/modules/order/models"
	"o_mall_backend/modules/order/repositories"
)

// OrderServiceImpl 订单服务实现
type OrderServiceImpl struct {
	orderRepo    repositories.OrderRepository
	cacheService OrderCacheService
}

// NewOrderService 创建订单服务
func NewOrderService() OrderService {
	return &OrderServiceImpl{
		orderRepo:    repositories.NewOrderRepository(),
		cacheService: NewOrderCacheService(),
	}
}

// CreateOrder 创建订单
func (s *OrderServiceImpl) CreateOrder(ctx context.Context, req *dto.CreateOrderRequest) (*dto.OrderResponse, error) {
	logs.Info("创建订单: %+v", req)

	if req == nil {
		return nil, errors.New("请求参数不能为空")
	}

	if req.UserID <= 0 {
		return nil, errors.New("用户ID不能为空")
	}

	if len(req.Items) == 0 {
		return nil, errors.New("订单项不能为空")
	}

	if req.Address == nil {
		return nil, errors.New("收货地址不能为空")
	}

	// 生成订单号
	orderNo, err := s.GenerateOrderNo(ctx)
	if err != nil {
		logs.Error("生成订单号失败: %v", err)
		return nil, err
	}

	// 计算订单金额
	totalAmount := 0.0
	discountAmount := 0.0
	// TODO: 调用商品服务获取商品信息和价格
	for _, item := range req.Items {
		totalAmount += float64(item.Quantity) * item.Price
	}

	// 创建订单
	order := &models.Order{
		OrderNo:        orderNo,
		UserID:         req.UserID,
		MerchantID:     req.MerchantID,               // 设置商家ID
		Status:         constants.OrderStatusPending, // 待付款
		PayStatus:      constants.PayStatusUnpaid,    // 未支付
		PayMethod:      req.PayMethod,
		TotalAmount:    totalAmount,
		DiscountAmount: discountAmount,
		PayAmount:      totalAmount - discountAmount,
		Remark:         req.Remark,
		Source:         req.Source,
		DeliveryType:   req.DeliveryType,
		OrderType:      req.OrderType,
		InvoiceType:    req.InvoiceType,
		InvoiceTitle:   req.InvoiceTitle,
		InvoiceTaxNo:   req.InvoiceTaxNo,
		PromotionInfo:  req.PromotionInfo,
	}

	// 如果有优惠券ID，转换为字符串
	if len(req.CouponIDs) > 0 {
		couponIDs := ""
		for i, id := range req.CouponIDs {
			if i > 0 {
				couponIDs += ","
			}
			couponIDs += strconv.FormatInt(id, 10)
		}
		order.CouponIDs = couponIDs
	}

	// 保存订单
	orderID, err := s.orderRepo.CreateOrder(ctx, order)
	if err != nil {
		logs.Error("保存订单失败: %v", err)
		return nil, err
	}
	order.ID = orderID

	// 创建订单项
	for _, itemReq := range req.Items {
		// 添加调试信息：检查接收到的OrderItemRequest
		logs.Info("[调试] CreateOrder接收 - ProductID: %d, ProductName: '%s', ProductImage: '%s'",
			itemReq.ProductID, itemReq.ProductName, itemReq.ProductImage)

		item := &models.OrderItem{
			OrderID:        orderID,
			OrderNo:        orderNo,
			ItemType:       itemReq.ItemType, // 使用传入的项目类型
			ProductID:      itemReq.ProductID,
			ProductName:    itemReq.ProductName,  // 直接使用传入的商品名称
			ProductImage:   itemReq.ProductImage, // 直接使用传入的商品图片
			SkuID:          itemReq.SkuID,
			Quantity:       itemReq.Quantity,
			Price:          itemReq.Price,
			OriginalPrice:  itemReq.Price,
			SubtotalAmount: float64(itemReq.Quantity) * itemReq.Price,
			// 外卖特有字段
			MerchantID:   itemReq.MerchantID,
			MerchantName: itemReq.MerchantName,
			PackagingFee: itemReq.PackagingFee,
			Remark:       itemReq.Remark,
			// 快照字段
			ProductSnapshot: itemReq.ProductSnapshot,
			VariantSnapshot: itemReq.VariantSnapshot,
			ComboSelections: itemReq.ComboSelections,
		}

		// 如果直接传入的字段为空，则尝试从商品快照中提取
		if (item.ProductName == "" || item.ProductImage == "") && itemReq.ProductSnapshot != "" {
			var snapshot map[string]interface{}
			if err := json.Unmarshal([]byte(itemReq.ProductSnapshot), &snapshot); err == nil {
				if item.ProductName == "" {
					if name, ok := snapshot["name"].(string); ok {
						item.ProductName = name
					}
				}
				if item.ProductImage == "" {
					if image, ok := snapshot["image"].(string); ok {
						item.ProductImage = image
					}
				}
			}
		}

		// 从规格快照中提取规格信息
		if itemReq.VariantSnapshot != "" {
			var variantData map[string]interface{}
			if err := json.Unmarshal([]byte(itemReq.VariantSnapshot), &variantData); err == nil {
				// 构建规格文本
				specText := ""

				// 获取规格名称
				if name, ok := variantData["name"].(string); ok && name != "标准规格" {
					specText = name
				}

				// 获取规格属性
				if attributes, ok := variantData["attributes"].(string); ok && attributes != "" {
					if specText != "" {
						specText += ", " + attributes
					} else {
						specText = attributes
					}
				}

				// 设置规格文本
				if specText != "" {
					item.SkuAttributes = specText
					logs.Info("[调试] 设置规格文本 - OrderItem: %d, SpecText: '%s'", item.ProductID, specText)
				}
			} else {
				logs.Warn("解析规格快照失败: %v", err)
			}
		}

		// 添加调试信息：检查最终要保存的OrderItem
		logs.Info("[调试] 保存订单项 - ProductID: %d, ProductName: '%s', ProductImage: '%s'",
			item.ProductID, item.ProductName, item.ProductImage)

		_, err = s.orderRepo.AddOrderItem(ctx, item)
		if err != nil {
			logs.Error("保存订单项失败: %v", err)
			return nil, err
		}
	}

	// 保存订单地址
	address := &models.OrderAddress{
		OrderID:       orderID,
		OrderNo:       orderNo,
		UserID:        req.UserID,
		ReceiverName:  req.Address.ReceiverName,
		ReceiverPhone: req.Address.ReceiverPhone,
		Province:      req.Address.Province,
		City:          req.Address.City,
		District:      req.Address.District,
		Detail:        req.Address.Detail,
		PostCode:      req.Address.PostCode,
	}

	_, err = s.orderRepo.AddOrderAddress(ctx, address)
	if err != nil {
		logs.Error("保存订单地址失败: %v", err)
		return nil, err
	}

	// 添加订单日志
	log := &models.OrderLog{
		OrderID: orderID,
		OrderNo: orderNo,
		UserID:  req.UserID,
		Action:  "create_order",
		Status:  constants.OrderStatusPending,
		Content: "创建订单",
	}

	_, err = s.orderRepo.AddOrderLog(ctx, log)
	if err != nil {
		logs.Error("保存订单日志失败: %v", err)
		// 不影响订单创建
	}

	// 查询完整订单信息
	return s.GetOrder(ctx, orderID)
}

// GetOrder 获取订单详情
func (s *OrderServiceImpl) GetOrder(ctx context.Context, id int64) (*dto.OrderResponse, error) {
	// logs.Info("获取订单详情: %d", id)

	order, err := s.orderRepo.GetOrderByID(ctx, id)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return nil, err
	}

	if order == nil {
		return nil, errors.New("订单不存在")
	}

	// 获取订单项
	items, err := s.orderRepo.GetOrderItems(ctx, id)
	if err != nil {
		logs.Error("查询订单项失败: %v", err)
		return nil, err
	}

	// 获取订单地址
	address, err := s.orderRepo.GetOrderAddress(ctx, id)
	if err != nil {
		logs.Error("查询订单地址失败: %v", err)
		return nil, err
	}

	// 获取支付信息
	payment, err := s.orderRepo.GetOrderPayment(ctx, id)
	if err != nil {
		logs.Error("查询订单支付信息失败: %v", err)
		// 不影响订单查询
	}

	// 构建订单响应
	resp := &dto.OrderResponse{
		ID:               order.ID,
		OrderNo:          order.OrderNo,
		UserID:           order.UserID,
		UserName:         "", // TODO: 从用户服务获取
		Status:           order.Status,
		StatusText:       constants.OrderStatusMap[order.Status],
		PayStatus:        order.PayStatus,
		PayStatusText:    constants.PayStatusMap[order.PayStatus],
		PayAmount:        order.PayAmount,
		TotalAmount:      order.TotalAmount,
		FreightAmount:    order.FreightAmount,
		DiscountAmount:   order.DiscountAmount,
		CouponAmount:     order.CouponAmount,
		IntegralAmount:   order.IntegralAmount,
		PromotionAmount:  order.PromotionAmount,
		Remark:           order.Remark,
		Source:           order.Source,
		SourceText:       constants.OrderSourceMap[order.Source],
		DeliveryType:     order.DeliveryType,
		DeliveryTypeText: constants.DeliveryTypeMap[order.DeliveryType],
		CommentStatus:    order.CommentStatus,
		InvoiceType:      order.InvoiceType,
		InvoiceTypeText:  constants.InvoiceTypeMap[order.InvoiceType],
		InvoiceTitle:     order.InvoiceTitle,
		InvoiceTaxNo:     order.InvoiceTaxNo,
		CancelReason:     order.CancelReason,
		OrderType:        order.OrderType,
		OrderTypeText:    constants.OrderTypeMap[order.OrderType],
		TrackingNo:       order.TrackingNo,
		ExpressCompany:   order.ExpressCompany,
		PromotionInfo:    order.PromotionInfo,
		CreatedAt:        order.CreatedAt,
		UpdatedAt:        order.UpdatedAt,
		Items:            make([]*dto.OrderItemResponse, 0, len(items)),
	}

	// 处理时间
	if !order.PayTime.IsZero() {
		resp.PayTime = &order.PayTime
	}

	if !order.DeliveryTime.IsZero() {
		resp.DeliveryTime = &order.DeliveryTime
	}

	if !order.ReceiveTime.IsZero() {
		resp.ReceiveTime = &order.ReceiveTime
	}

	if !order.CommentTime.IsZero() {
		resp.CommentTime = &order.CommentTime
	}

	if !order.CancelTime.IsZero() {
		resp.CancelTime = &order.CancelTime
	}

	if !order.RefundTime.IsZero() {
		resp.RefundTime = &order.RefundTime
	}

	// 处理订单项
	for _, item := range items {
		itemResp := &dto.OrderItemResponse{
			ID:              item.ID,
			OrderID:         item.OrderID,
			OrderNo:         item.OrderNo,
			ProductID:       item.ProductID,
			ProductName:     item.ProductName,
			ProductImage:    item.ProductImage,
			SkuID:           item.SkuID,
			SkuCode:         item.SkuCode,
			SkuAttributes:   item.SkuAttributes,
			Quantity:        item.Quantity,
			Price:           item.Price,
			OriginalPrice:   item.OriginalPrice,
			SubtotalAmount:  item.SubtotalAmount,
			DiscountAmount:  item.DiscountAmount,
			CommentStatus:   item.CommentStatus,
			RefundStatus:    item.RefundStatus,
			RefundQuantity:  item.RefundQuantity,
			RefundAmount:    item.RefundAmount,
			CreatedAt:       item.CreatedAt,
			VariantSnapshot: item.VariantSnapshot, // 添加规格快照字段赋值
		}
		resp.Items = append(resp.Items, itemResp)
	}

	// 处理地址信息
	if address != nil {
		resp.Address = &dto.OrderAddressResponse{
			ID:            address.ID,
			OrderID:       address.OrderID,
			OrderNo:       address.OrderNo,
			UserID:        address.UserID,
			ReceiverName:  address.ReceiverName,
			ReceiverPhone: address.ReceiverPhone,
			Province:      address.Province,
			City:          address.City,
			District:      address.District,
			Detail:        address.Detail,
			PostCode:      address.PostCode,
			IsDefault:     address.IsDefault,
			CreatedAt:     address.CreatedAt,
		}
	}

	// 处理支付信息
	if payment != nil {
		paymentResp := &dto.OrderPaymentResponse{
			ID:            payment.ID,
			OrderID:       payment.OrderID,
			OrderNo:       payment.OrderNo,
			UserID:        payment.UserID,
			PaymentNo:     payment.PaymentNo,
			PaymentMethod: payment.PaymentMethod,
			PaymentAmount: payment.PaymentAmount,
			PaymentStatus: payment.PaymentStatus,
			TransactionID: payment.TransactionID,
			CreatedAt:     payment.CreatedAt,
		}

		if !payment.PaymentTime.IsZero() {
			paymentResp.PaymentTime = &payment.PaymentTime
		}

		resp.Payment = paymentResp
	}

	return resp, nil
}

// ListOrders 获取订单列表
func (s *OrderServiceImpl) ListOrders(ctx context.Context, req *dto.OrderQueryRequest) (*dto.OrderListResponse, error) {
	logs.Info("查询订单列表: %+v", req)

	if req == nil {
		req = &dto.OrderQueryRequest{
			Page:     1,
			PageSize: 10,
		}
	}

	if req.Page <= 0 {
		req.Page = 1
	}

	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 查询订单列表
	orders, total, err := s.orderRepo.ListOrders(ctx, req)
	if err != nil {
		logs.Error("查询订单列表失败: %v", err)
		return nil, err
	}

	resp := &dto.OrderListResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Items:    make([]*dto.OrderResponse, 0, len(orders)),
	}

	if len(orders) == 0 {
		// 没有订单数据，直接返回空列表
		return resp, nil
	}

	// 收集所有订单ID
	orderIDs := make([]int64, len(orders))
	orderMap := make(map[int64]*models.Order, len(orders))
	for i, order := range orders {
		orderIDs[i] = order.ID
		orderMap[order.ID] = order
	}

	// 批量查询所有订单项
	allItems, err := s.orderRepo.GetOrderItemsByOrderIDs(ctx, orderIDs)
	if err != nil {
		logs.Error("批量查询订单项失败: %v", err)
		return nil, err
	}

	// 按订单ID组织订单项
	itemsMap := make(map[int64][]*models.OrderItem)
	for _, item := range allItems {
		itemsMap[item.OrderID] = append(itemsMap[item.OrderID], item)
	}

	// 批量查询所有订单地址
	allAddresses, err := s.orderRepo.GetOrderAddressesByOrderIDs(ctx, orderIDs)
	if err != nil {
		logs.Error("批量查询订单地址失败: %v", err)
		// 不影响主流程，继续执行
	}

	// 按订单ID组织地址信息
	addressMap := make(map[int64]*models.OrderAddress)
	for _, addr := range allAddresses {
		addressMap[addr.OrderID] = addr
	}

	// 批量查询所有支付信息
	allPayments, err := s.orderRepo.GetOrderPaymentsByOrderIDs(ctx, orderIDs)
	if err != nil {
		logs.Error("批量查询订单支付信息失败: %v", err)
		// 不影响主流程，继续执行
	}

	// 按订单ID组织支付信息
	paymentMap := make(map[int64]*models.OrderPayment)
	for _, payment := range allPayments {
		paymentMap[payment.OrderID] = payment
	}

	// 构造每个订单的响应对象
	for _, orderID := range orderIDs {
		order := orderMap[orderID]
		if order == nil {
			continue
		}

		// 获取支付方式文本
		payMethodText := ""
		switch order.PayMethod {
		case 1:
			payMethodText = "支付宝"
		case 2:
			payMethodText = "微信支付"
		case 3:
			payMethodText = "银行卡"
		case 4:
			payMethodText = "余额支付"
		default:
			payMethodText = "未知"
		}

		// 构造订单响应对象
		orderResp := &dto.OrderResponse{
			ID:             order.ID,
			OrderNo:        order.OrderNo,
			UserID:         order.UserID,
			OrderType:      order.OrderType,
			Status:         order.Status,
			PayStatus:      order.PayStatus,
			TotalAmount:    order.TotalAmount,
			PayAmount:      order.PayAmount,
			FreightAmount:  order.FreightAmount,
			DiscountAmount: order.DiscountAmount,
			Remark:         order.Remark,
			PayMethodText:  payMethodText, // 使用文本值而非整数
			CreatedAt:      order.CreatedAt,
			Items:          make([]*dto.OrderItemResponse, 0),
		}

		// 设置订单时间 - 将time.Time转为*time.Time
		if !order.PayTime.IsZero() {
			tmp := order.PayTime
			orderResp.PayTime = &tmp
		}
		if !order.DeliveryTime.IsZero() {
			tmp := order.DeliveryTime
			orderResp.DeliveryTime = &tmp
		}
		if !order.ReceiveTime.IsZero() {
			tmp := order.ReceiveTime
			orderResp.ReceiveTime = &tmp
		}
		// 订单模型中没有CompleteTime字段，移除此部分
		if !order.CancelTime.IsZero() {
			tmp := order.CancelTime
			orderResp.CancelTime = &tmp
		}

		// 添加订单项
		orderItems := itemsMap[orderID]
		for _, orderItem := range orderItems {
			itemResp := &dto.OrderItemResponse{
				ID:              orderItem.ID,
				OrderID:         orderItem.OrderID,
				OrderNo:         orderItem.OrderNo,
				ProductID:       orderItem.ProductID,
				ProductName:     orderItem.ProductName,
				ProductImage:    orderItem.ProductImage,
				SkuID:           orderItem.SkuID,
				SkuCode:         orderItem.SkuCode,
				SkuAttributes:   orderItem.SkuAttributes,
				Quantity:        orderItem.Quantity,
				Price:           orderItem.Price,
				OriginalPrice:   orderItem.OriginalPrice,
				SubtotalAmount:  orderItem.SubtotalAmount,
				DiscountAmount:  orderItem.DiscountAmount,
				CommentStatus:   orderItem.CommentStatus,
				RefundStatus:    orderItem.RefundStatus,
				RefundQuantity:  orderItem.RefundQuantity,
				RefundAmount:    orderItem.RefundAmount,
				CreatedAt:       orderItem.CreatedAt,
				VariantSnapshot: orderItem.VariantSnapshot, // 添加规格快照字段
			}
			orderResp.Items = append(orderResp.Items, itemResp)
		}

		// 地址信息和支付信息已经在前面通过批量查询方式添加，此处不需要重复添加

		// 地址信息和支付信息已在前面通过批量查询方式添加完毕

		resp.Items = append(resp.Items, orderResp)
	}

	return resp, nil
}

// CancelOrder 取消订单
func (s *OrderServiceImpl) CancelOrder(ctx context.Context, id int64, req *dto.CancelOrderRequest) error {
	logs.Info("取消订单: id=%d, reason=%s", id, req.CancelReason)

	if req == nil || req.CancelReason == "" {
		return errors.New("取消原因不能为空")
	}

	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, id)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 只有待付款的订单可以取消
	if order.Status != constants.OrderStatusPending {
		return errors.New("当前订单状态不能取消")
	}

	// 更新订单信息
	order.Status = constants.OrderStatusCancelled
	order.CancelReason = req.CancelReason
	order.CancelTime = time.Now()
	order.UpdatedAt = time.Now()

	err = s.orderRepo.UpdateOrder(ctx, order)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return err
	}

	// 添加订单日志
	log := &models.OrderLog{
		OrderID: id,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "cancel_order",
		Status:  constants.OrderStatusCancelled,
		Content: "取消订单: " + req.CancelReason,
	}

	_, err = s.orderRepo.AddOrderLog(ctx, log)
	if err != nil {
		logs.Error("保存订单日志失败: %v", err)
		// 不影响订单取消
	}

	// TODO: 释放库存

	// 清除用户订单列表缓存
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.ClearUserOrderListCache(cacheCtx, order.UserID); err != nil {
			logs.Error("清除用户订单列表缓存失败: %v", err)
		}
	}()

	return nil
}

// PayOrder 支付订单
func (s *OrderServiceImpl) PayOrder(ctx context.Context, id int64, req *dto.PayOrderRequest) error {
	logs.Info("支付订单: id=%d, req=%+v", id, req)

	if req == nil {
		return errors.New("请求参数不能为空")
	}

	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, id)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 只有待付款的订单可以支付
	if order.Status != constants.OrderStatusPending {
		return errors.New("当前订单状态不能支付")
	}

	// 检查支付金额
	if req.PaymentAmount < order.PayAmount {
		return errors.New("支付金额不足")
	}

	payTime := time.Now()

	// 更新订单支付状态
	err = s.orderRepo.UpdatePayStatus(ctx, id, constants.PayStatusPaid, payTime)
	if err != nil {
		logs.Error("更新订单支付状态失败: %v", err)
		return err
	}

	// 保存支付信息
	payment := &models.OrderPayment{
		OrderID:       id,
		OrderNo:       order.OrderNo,
		UserID:        order.UserID,
		PaymentNo:     req.PaymentNo,
		PaymentMethod: req.PayMethod,
		PaymentAmount: req.PaymentAmount,
		PaymentStatus: constants.PayStatusPaid,
		PaymentTime:   payTime,
		TransactionID: req.TransactionID,
		ProviderReply: req.ProviderReply,
	}

	// 检查是否已有支付记录
	existPayment, err := s.orderRepo.GetOrderPayment(ctx, id)
	if err != nil {
		logs.Error("查询订单支付信息失败: %v", err)
		return err
	}

	if existPayment == nil {
		// 添加支付记录
		_, err = s.orderRepo.AddOrderPayment(ctx, payment)
		if err != nil {
			logs.Error("保存订单支付信息失败: %v", err)
			return err
		}
	} else {
		// 更新支付记录
		payment.ID = existPayment.ID
		err = s.orderRepo.UpdateOrderPayment(ctx, payment)
		if err != nil {
			logs.Error("更新订单支付信息失败: %v", err)
			return err
		}
	}

	// 添加订单日志
	log := &models.OrderLog{
		OrderID: id,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "pay_order",
		Status:  constants.OrderStatusPaid,
		Content: fmt.Sprintf("支付订单: 支付金额%.2f元", req.PaymentAmount),
	}

	_, err = s.orderRepo.AddOrderLog(ctx, log)
	if err != nil {
		logs.Error("保存订单日志失败: %v", err)
		// 不影响订单支付
	}

	// 清除用户订单列表缓存
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.ClearUserOrderListCache(cacheCtx, order.UserID); err != nil {
			logs.Error("清除用户订单列表缓存失败: %v", err)
		}
	}()

	return nil
}

// ShipOrder 发货
func (s *OrderServiceImpl) ShipOrder(ctx context.Context, id int64, req *dto.ShipOrderRequest) error {
	logs.Info("订单发货: id=%d, req=%+v", id, req)

	if req == nil {
		return errors.New("请求参数不能为空")
	}

	if req.TrackingNo == "" {
		return errors.New("物流单号不能为空")
	}

	if req.ExpressCompany == "" {
		return errors.New("快递公司不能为空")
	}

	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, id)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 只有已付款的订单可以发货
	if order.Status != constants.OrderStatusPaid {
		return errors.New("当前订单状态不能发货")
	}

	// 更新订单信息
	order.Status = constants.OrderStatusShipped
	order.TrackingNo = req.TrackingNo
	order.ExpressCompany = req.ExpressCompany
	order.DeliveryTime = time.Now()
	order.UpdatedAt = time.Now()

	err = s.orderRepo.UpdateOrder(ctx, order)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return err
	}

	// 添加订单日志
	log := &models.OrderLog{
		OrderID: id,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "ship_order",
		Status:  constants.OrderStatusShipped,
		Content: fmt.Sprintf("订单发货: 快递公司=%s, 物流单号=%s", req.ExpressCompany, req.TrackingNo),
	}

	_, err = s.orderRepo.AddOrderLog(ctx, log)
	if err != nil {
		logs.Error("保存订单日志失败: %v", err)
		// 不影响订单发货
	}

	// 清除用户订单列表缓存
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.ClearUserOrderListCache(cacheCtx, order.UserID); err != nil {
			logs.Error("清除用户订单列表缓存失败: %v", err)
		}
	}()

	return nil
}

// ConfirmReceive 确认收货
func (s *OrderServiceImpl) ConfirmReceive(ctx context.Context, id int64) error {
	logs.Info("确认收货: id=%d", id)

	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, id)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 只有已发货的订单可以确认收货
	if order.Status != constants.OrderStatusShipped {
		return errors.New("当前订单状态不能确认收货")
	}

	// 更新订单信息
	order.Status = constants.OrderStatusDelivered
	order.ReceiveTime = time.Now()
	order.UpdatedAt = time.Now()

	err = s.orderRepo.UpdateOrder(ctx, order)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return err
	}

	// 添加订单日志
	log := &models.OrderLog{
		OrderID: id,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "confirm_receive",
		Status:  constants.OrderStatusDelivered,
		Content: "确认收货",
	}

	_, err = s.orderRepo.AddOrderLog(ctx, log)
	if err != nil {
		logs.Error("保存订单日志失败: %v", err)
		// 不影响确认收货
	}

	// 清除用户订单列表缓存
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.ClearUserOrderListCache(cacheCtx, order.UserID); err != nil {
			logs.Error("清除用户订单列表缓存失败: %v", err)
		}
	}()

	return nil
}

// CompleteOrder 完成订单
func (s *OrderServiceImpl) CompleteOrder(ctx context.Context, id int64) error {
	logs.Info("完成订单: id=%d", id)

	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, id)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 只有已收货的订单可以完成
	if order.Status != constants.OrderStatusDelivered {
		return errors.New("当前订单状态不能完成")
	}

	// 更新订单信息
	order.Status = constants.OrderStatusCompleted
	order.UpdatedAt = time.Now()

	err = s.orderRepo.UpdateOrder(ctx, order)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return err
	}

	// 添加订单日志
	log := &models.OrderLog{
		OrderID: id,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "complete_order",
		Status:  constants.OrderStatusCompleted,
		Content: "完成订单",
	}

	_, err = s.orderRepo.AddOrderLog(ctx, log)
	if err != nil {
		logs.Error("保存订单日志失败: %v", err)
		// 不影响订单完成
	}

	// 清除用户订单列表缓存
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.ClearUserOrderListCache(cacheCtx, order.UserID); err != nil {
			logs.Error("清除用户订单列表缓存失败: %v", err)
		}
	}()

	return nil
}

// DeleteOrder 删除订单
func (s *OrderServiceImpl) DeleteOrder(ctx context.Context, id int64) error {
	logs.Info("删除订单: id=%d", id)

	// 查询订单
	order, err := s.orderRepo.GetOrderByID(ctx, id)
	if err != nil {
		logs.Error("查询订单失败: %v", err)
		return err
	}

	if order == nil {
		return errors.New("订单不存在")
	}

	// 已完成或已取消的订单才能删除
	if order.Status != constants.OrderStatusCompleted && order.Status != constants.OrderStatusCancelled {
		return errors.New("当前订单状态不能删除")
	}

	// 执行删除
	err = s.orderRepo.DeleteOrder(ctx, id)
	if err != nil {
		logs.Error("删除订单失败: %v", err)
		return err
	}

	// 添加订单日志
	log := &models.OrderLog{
		OrderID: id,
		OrderNo: order.OrderNo,
		UserID:  order.UserID,
		Action:  "delete_order",
		Status:  order.Status,
		Content: "删除订单",
	}

	_, err = s.orderRepo.AddOrderLog(ctx, log)
	if err != nil {
		logs.Error("保存订单日志失败: %v", err)
		// 不影响订单删除
	}

	return nil
}

// GetOrderStatistics 获取订单统计信息
func (s *OrderServiceImpl) GetOrderStatistics(ctx context.Context) (*dto.OrderStatisticsResponse, error) {
	logs.Info("获取订单统计信息")
	return s.orderRepo.GetOrderStatistics(ctx)
}

// GetOrderLogs 获取订单日志
func (s *OrderServiceImpl) GetOrderLogs(ctx context.Context, orderID int64) ([]*dto.OrderLogResponse, error) {
	logs.Info("获取订单日志: orderID=%d", orderID)

	orderLogs, err := s.orderRepo.GetOrderLogs(ctx, orderID)
	if err != nil {
		logs.Error("获取订单日志失败: %v", err)
		return nil, err
	}

	respLogs := make([]*dto.OrderLogResponse, 0, len(orderLogs))
	for _, log := range orderLogs {
		respLog := &dto.OrderLogResponse{
			ID:         log.ID,
			OrderID:    log.OrderID,
			OrderNo:    log.OrderNo,
			UserID:     log.UserID,
			AdminID:    log.AdminID,
			Action:     log.Action,
			Status:     log.Status,
			StatusText: constants.OrderStatusMap[log.Status],
			Content:    log.Content,
			IP:         log.IP,
			CreatedAt:  log.CreatedAt,
		}
		respLogs = append(respLogs, respLog)
	}

	return respLogs, nil
}

// GenerateOrderNo 生成订单号
func (s *OrderServiceImpl) GenerateOrderNo(ctx context.Context) (string, error) {
	// 格式：年月日时分秒+4位随机数
	now := time.Now()
	randNum := rand.Intn(10000)
	orderNo := now.Format("20060102150405") + fmt.Sprintf("%04d", randNum)
	return orderNo, nil
}

// ListOrdersHighPerformance 高性能订单列表查询（带缓存）
func (s *OrderServiceImpl) ListOrdersHighPerformance(ctx context.Context, req *dto.HighPerformanceOrderListRequest) (*dto.HighPerformanceOrderListResponse, error) {
	logs.Info("高性能查询订单列表（带缓存）: %+v", req)

	if req == nil {
		return nil, errors.New("请求参数不能为空")
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 1. 尝试从缓存获取
	cachedResp, err := s.cacheService.GetOrderListCache(ctx, req)
	if err != nil {
		logs.Warn("获取订单列表缓存失败，继续查询数据库: %v", err)
	} else if cachedResp != nil {
		logs.Info("订单列表缓存命中，用户ID: %d", req.UserID)
		return cachedResp, nil
	}

	// 2. 缓存未命中，查询数据库
	logs.Info("订单列表缓存未命中，查询数据库")
	orders, total, err := s.orderRepo.ListOrdersHighPerformance(ctx, req)
	if err != nil {
		logs.Error("高性能查询订单列表失败: %v", err)
		return nil, err
	}

	// 3. 构建响应
	resp := &dto.HighPerformanceOrderListResponse{
		Orders:   orders,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	// 4. 异步设置缓存（避免影响响应时间）
	go func() {
		cacheCtx := context.Background()
		if err := s.cacheService.SetOrderListCache(cacheCtx, req, resp); err != nil {
			logs.Error("设置订单列表缓存失败: %v", err)
		}
	}()

	logs.Info("高性能查询订单列表成功，总数: %d，当前页: %d", total, req.Page)
	return resp, nil
}
