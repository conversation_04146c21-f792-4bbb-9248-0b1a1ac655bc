/**
 * transaction.go
 * 事务管理器
 *
 * 本文件定义了事务管理器接口和实现，用于处理数据库事务
 */

package common

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// Transaction 事务接口
type Transaction interface {
	// Begin 开始事务
	Begin() error

	// Commit 提交事务
	Commit() error

	// Rollback 回滚事务
	Rollback() error

	// GetTx 获取事务对象
	GetTx() interface{}
}

// TransactionManager Beego ORM事务管理器
type TransactionManager struct {
	ormer orm.Ormer
	tx    orm.TxOrmer
}

var _ Transaction = (*TransactionManager)(nil)

// NewTransactionManager 创建事务管理器
func NewTransactionManager() Transaction {
	return &TransactionManager{
		ormer: orm.NewOrm(),
	}
}

// Begin 开始事务
func (t *TransactionManager) Begin() error {
	tx, err := t.ormer.Begin()
	if err != nil {
		logs.Error("[TransactionManager] 开启事务失败: %v", err)
		return err
	}
	t.tx = tx
	return nil
}

// Commit 提交事务
func (t *TransactionManager) Commit() error {
	if t.tx == nil {
		return nil
	}
	err := t.tx.Commit()
	if err != nil {
		logs.Error("[TransactionManager] 提交事务失败: %v", err)
		return err
	}
	t.tx = nil
	return nil
}

// Rollback 回滚事务
func (t *TransactionManager) Rollback() error {
	if t.tx == nil {
		return nil
	}
	err := t.tx.Rollback()
	if err != nil {
		logs.Error("[TransactionManager] 回滚事务失败: %v", err)
		return err
	}
	t.tx = nil
	return nil
}

// GetTx 获取事务对象
func (t *TransactionManager) GetTx() interface{} {
	if t.tx != nil {
		return t.tx
	}
	return t.ormer
}