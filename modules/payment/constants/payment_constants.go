/**
 * payment_constants.go
 * 支付系统常量定义
 * 定义支付相关的常量，如支付渠道、错误码等
 */

package constants

// 支付相关错误码
const (
	// 通用错误码
	PaymentErrCodeSuccess        = 0    // 成功
	PaymentErrCodeParamInvalid   = 1001 // 参数无效
	PaymentErrCodeSystemError    = 1002 // 系统错误
	PaymentErrCodeOrderNotExists = 1003 // 订单不存在
	PaymentErrCodeUserNotMatch   = 1004 // 用户不匹配
	PaymentErrCodeAmountInvalid  = 1005 // 金额无效
	PaymentErrCodePaymentExists  = 1006 // 支付记录已存在

	// 支付相关错误码
	PaymentErrCodeCreateFailed   = 2001 // 创建支付失败
	PaymentErrCodePaymentExpired = 2002 // 支付已过期
	PaymentErrCodeQueryFailed    = 2003 // 查询支付状态失败
	PaymentErrCodeCancelFailed   = 2004 // 取消支付失败
	PaymentErrCodeInvalidStatus  = 2005 // 状态不正确，无法执行操作

	// 退款相关错误码
	PaymentErrCodeRefundFailed      = 3001 // 创建退款失败
	PaymentErrCodeRefundAmountLimit = 3002 // 退款金额超过限制
	PaymentErrCodeRefundTimeout     = 3003 // 退款超时
	PaymentErrCodeRefundRejected    = 3004 // 退款被拒绝
	PaymentErrCodeRefundCompleted   = 3005 // 已经完成退款

	// 支付账户相关错误码
	PaymentErrCodeAccountNotExists = 4001 // 支付账户不存在
	PaymentErrCodeAccountDisabled  = 4002 // 支付账户已禁用
	PaymentErrCodeConfigInvalid    = 4003 // 配置无效
)

// 支付回调相关常量
const (
	// 回调动作类型
	PaymentActionCreate     = "create"      // 创建支付
	PaymentActionNotify     = "notify"      // 支付通知
	PaymentActionPay        = "pay"         // 支付操作
	PaymentActionQuery      = "query"       // 查询支付
	PaymentActionCancel     = "cancel"      // 取消支付
	PaymentActionRefund     = "refund"      // 退款
	PaymentActionRNotify    = "rnotify"     // 退款通知
	PaymentActionRQuery     = "rquery"      // 查询退款
	PaymentActionOrderUpdate = "order_update" // 订单状态更新

	// 退款动作类型
	RefundActionCreate  = "create"  // 创建退款
	RefundActionNotify  = "notify"  // 退款通知
	RefundActionQuery   = "query"   // 查询退款
	RefundActionApprove = "approve" // 审批退款
	RefundActionReject  = "reject"  // 拒绝退款

	// 支付交易流水号前缀
	PaymentNoPrefix = "PAY" // 支付交易号前缀
	RefundNoPrefix  = "REF" // 退款交易号前缀

	// 支付过期时间（分钟）
	PaymentExpireMinutes = 30
)

// 支付渠道具体配置键
const (
	// 支付宝相关配置键
	AlipayAppID      = "appid"       // 应用ID
	AlipayPrivateKey = "private_key" // 私钥
	AlipayPublicKey  = "public_key"  // 公钥
	AlipaySignType   = "sign_type"   // 签名类型
	AlipayReturnURL  = "return_url"  // 前端回跳URL
	AlipayNotifyURL  = "notify_url"  // 后端通知URL
	AlipayGatewayURL = "gateway_url" // 网关URL

	// 微信支付相关配置键
	WechatAppID     = "appid"      // 应用ID
	WechatMchID     = "mch_id"     // 商户号
	WechatKey       = "key"        // API密钥
	WechatCertFile  = "cert_file"  // 证书文件
	WechatKeyFile   = "key_file"   // 证书密钥文件
	WechatNotifyURL = "notify_url" // 通知URL
	WechatTradeType = "trade_type" // 交易类型

	// 银行卡支付相关配置键
	BankMerchantID = "merchant_id" // 商户ID
	BankTerminalID = "terminal_id" // 终端ID
	BankAccessKey  = "access_key"  // 访问密钥
	BankSecretKey  = "secret_key"  // 密钥
	BankGatewayURL = "gateway_url" // 网关URL
)

// 支付状态相关常量
const (
	// 审批状态
	ApprovalStatusNoNeed   = 0 // 无需审批
	ApprovalStatusPending  = 1 // 待审批
	ApprovalStatusApproved = 2 // 已通过
	ApprovalStatusRejected = 3 // 已拒绝

	// 对账状态
	AccountCheckStatusInit     = 0 // 未对账
	AccountCheckStatusChecking = 1 // 对账中
	AccountCheckStatusMatched  = 2 // 已平账
	AccountCheckStatusDiff     = 3 // 有差异
)
