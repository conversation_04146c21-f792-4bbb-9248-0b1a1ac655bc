/**
 * payment_dto.go
 * 支付系统数据传输对象
 * 用于服务层与控制器层之间的数据交换
 */

package dto

import (
	"time"

	"o_mall_backend/modules/payment/models"
)

// PaymentCreateRequest 创建支付请求
type PaymentCreateRequest struct {
	OrderID    int64                `json:"order_id" valid:"Required"`  // 订单ID
	UserID     int64                `json:"user_id" valid:"Required"`   // 用户ID
	Amount     float64              `json:"amount" valid:"Required"`    // 支付金额
	Method     models.PaymentMethod `json:"method" valid:"Required"`    // 支付方式
	ClientIP   string               `json:"client_ip" valid:"Required"` // 客户端IP
	DeviceInfo string               `json:"device_info"`                // 设备信息
	ReturnURL  string               `json:"return_url"`                 // 支付完成后前端跳转URL
	Remark     string               `json:"remark"`                     // 备注
}

// PaymentCreateResponse 创建支付响应
type PaymentCreateResponse struct {
	PaymentID     int64  `json:"payment_id"`     // 支付记录ID
	TransactionNo string `json:"transaction_no"` // 交易流水号
	PaymentURL    string `json:"payment_url"`    // 支付URL（跳转到支付页面或返回支付二维码链接）
	QrCodeURL     string `json:"qr_code_url"`    // 二维码URL（如果是扫码支付）
	AppPayParams  string `json:"app_pay_params"` // APP支付参数（JSON字符串，APP端拉起支付用）
	WebPayParams  string `json:"web_pay_params"` // Web支付参数（JSON字符串，Web端拉起支付用）
	ExpireTime    int64  `json:"expire_time"`    // 支付过期时间戳（秒）
}

// PaymentQueryRequest 支付查询请求
type PaymentQueryRequest struct {
	PaymentID     int64  `json:"payment_id"`     // 支付记录ID
	TransactionNo string `json:"transaction_no"` // 交易流水号
	OrderID       int64  `json:"order_id"`       // 订单ID
}

// PaymentQueryResponse 支付查询响应
type PaymentQueryResponse struct {
	PaymentID     int64                `json:"payment_id"`     // 支付记录ID
	TransactionNo string               `json:"transaction_no"` // 交易流水号
	OrderID       int64                `json:"order_id"`       // 订单ID
	UserID        int64                `json:"user_id"`        // 用户ID
	Amount        float64              `json:"amount"`         // 支付金额
	Method        models.PaymentMethod `json:"method"`         // 支付方式
	Status        models.PaymentStatus `json:"status"`         // 支付状态
	PaymentTime   *time.Time           `json:"payment_time"`   // 支付时间
	ExpireTime    *time.Time           `json:"expire_time"`    // 过期时间
}

// PaymentCallbackRequest 支付回调请求
type PaymentCallbackRequest struct {
	Method        models.PaymentMethod `json:"method"`         // 支付方式
	RawData       string               `json:"raw_data"`       // 原始回调数据
	TransactionNo string               `json:"transaction_no"` // 平台交易号
	ExternalNo    string               `json:"external_no"`    // 外部交易号
}

// PaymentCancelRequest 取消支付请求
type PaymentCancelRequest struct {
	PaymentID     int64  `json:"payment_id" valid:"Required"`     // 支付记录ID
	TransactionNo string `json:"transaction_no" valid:"Required"` // 交易流水号
	UserID        int64  `json:"user_id" valid:"Required"`        // 用户ID（验证权限）
	Reason        string `json:"reason"`                          // 取消原因
}

// RefundCreateRequest 创建退款请求
type RefundCreateRequest struct {
	PaymentID    int64   `json:"payment_id" valid:"Required"` // 支付记录ID
	OrderID      int64   `json:"order_id" valid:"Required"`   // 订单ID
	UserID       int64   `json:"user_id" valid:"Required"`    // 用户ID
	Amount       float64 `json:"amount" valid:"Required"`     // 退款金额
	Reason       string  `json:"reason" valid:"Required"`     // 退款原因
	OperatorID   int64   `json:"operator_id"`                 // 操作人ID（管理员操作时设置）
	NeedApproval bool    `json:"need_approval"`               // 是否需要审批
}

// RefundCreateResponse 创建退款响应
type RefundCreateResponse struct {
	RefundID int64  `json:"refund_id"` // 退款记录ID
	RefundNo string `json:"refund_no"` // 退款流水号
	Status   int    `json:"status"`    // 状态：0-处理中，1-成功，2-失败，3-待审批
	Message  string `json:"message"`   // 消息
}

// RefundQueryRequest 退款查询请求
type RefundQueryRequest struct {
	RefundID  int64  `json:"refund_id"`  // 退款记录ID
	RefundNo  string `json:"refund_no"`  // 退款流水号
	OrderID   int64  `json:"order_id"`   // 订单ID
	PaymentID int64  `json:"payment_id"` // 支付ID
}

// RefundQueryResponse 退款查询响应
type RefundQueryResponse struct {
	RefundID       int64               `json:"refund_id"`       // 退款记录ID
	RefundNo       string              `json:"refund_no"`       // 退款流水号
	PaymentID      int64               `json:"payment_id"`      // 支付记录ID
	OrderID        int64               `json:"order_id"`        // 订单ID
	UserID         int64               `json:"user_id"`         // 用户ID
	Amount         float64             `json:"amount"`          // 退款金额
	Status         models.RefundStatus `json:"status"`          // 退款状态
	Reason         string              `json:"reason"`          // 退款原因
	RefundTime     *time.Time          `json:"refund_time"`     // 退款时间
	ApprovalStatus int                 `json:"approval_status"` // 审批状态
}

// PaymentMethodInfo 支付方式信息
type PaymentMethodInfo struct {
	Method      models.PaymentMethod `json:"method"`      // 支付方式
	Name        string               `json:"name"`        // 支付方式名称
	Icon        string               `json:"icon"`        // 图标URL
	Description string               `json:"description"` // 描述
	Extra       map[string]string    `json:"extra"`       // 额外信息
	IsEnabled   bool                 `json:"is_enabled"`  // 是否启用
}

// PaymentListRequest 支付列表查询请求
type PaymentListRequest struct {
	UserID    int64                `json:"user_id"`    // 用户ID
	OrderID   int64                `json:"order_id"`   // 订单ID
	Status    models.PaymentStatus `json:"status"`     // 支付状态
	Method    models.PaymentMethod `json:"method"`     // 支付方式
	StartTime *time.Time           `json:"start_time"` // 开始时间
	EndTime   *time.Time           `json:"end_time"`   // 结束时间
	Page      int                  `json:"page"`       // 页码
	PageSize  int                  `json:"pageSize"`   // 每页大小
}

// PaymentBriefInfo 支付简要信息
type PaymentBriefInfo struct {
	ID            int64                `json:"id"`             // 支付ID
	TransactionNo string               `json:"transaction_no"` // 交易流水号
	OrderID       int64                `json:"order_id"`       // 订单ID
	Amount        float64              `json:"amount"`         // 金额
	Method        models.PaymentMethod `json:"method"`         // 支付方式
	MethodName    string               `json:"method_name"`    // 支付方式名称
	Status        models.PaymentStatus `json:"status"`         // 状态
	StatusName    string               `json:"status_name"`    // 状态名称
	CreatedAt     time.Time            `json:"created_at"`     // 创建时间
	PaymentTime   *time.Time           `json:"payment_time"`   // 支付时间
}

// PagedPaymentList 分页支付列表
type PagedPaymentList struct {
	Total    int64              `json:"total"`    // 总数
	Page     int                `json:"page"`     // 当前页
	PageSize int                `json:"pageSize"` // 每页大小
	List     []PaymentBriefInfo `json:"list"`     // 支付列表
}

// RefundListRequest 退款列表查询请求
type RefundListRequest struct {
	UserID    int64               `json:"user_id"`    // 用户ID
	OrderID   int64               `json:"order_id"`   // 订单ID
	PaymentID int64               `json:"payment_id"` // 支付ID
	Status    models.RefundStatus `json:"status"`     // 退款状态
	StartTime *time.Time          `json:"start_time"` // 开始时间
	EndTime   *time.Time          `json:"end_time"`   // 结束时间
	Page      int                 `json:"page"`       // 页码
	PageSize  int                 `json:"pageSize"`   // 每页大小
}

// RefundBriefInfo 退款简要信息
type RefundBriefInfo struct {
	ID         int64               `json:"id"`          // 退款ID
	RefundNo   string              `json:"refund_no"`   // 退款流水号
	PaymentID  int64               `json:"payment_id"`  // 支付ID
	OrderID    int64               `json:"order_id"`    // 订单ID
	Amount     float64             `json:"amount"`      // 金额
	Status     models.RefundStatus `json:"status"`      // 状态
	StatusName string              `json:"status_name"` // 状态名称
	Reason     string              `json:"reason"`      // 退款原因
	CreatedAt  time.Time           `json:"created_at"`  // 创建时间
	RefundTime *time.Time          `json:"refund_time"` // 退款时间
}

// PagedRefundList 分页退款列表
type PagedRefundList struct {
	Total    int64             `json:"total"`    // 总数
	Page     int               `json:"page"`     // 当前页
	PageSize int               `json:"pageSize"` // 每页大小
	List     []RefundBriefInfo `json:"list"`     // 退款列表
}
