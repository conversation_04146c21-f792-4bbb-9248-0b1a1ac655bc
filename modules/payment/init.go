/**
 * payment模块初始化
 *
 * 本文件负责payment模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保payment模块的功能正常启动。
 */

package payment

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/routers"
)

// Init 初始化payment模块
func Init() {
	logs.Info("初始化payment模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitRouters()

	logs.Info("payment模块初始化完成")
}

// registerModels 注册模型
func registerModels() {
	logs.Info("注册payment模块ORM模型...")

	// 注册payment相关模型
	orm.RegisterModel(
		new(models.Payment),
		new(models.PaymentAccount),
		new(models.Refund),
		new(models.PaymentLog),
		new(models.PaymentBill),
	)
}
