/**
 * payment_service.go
 * 支付服务接口定义
 * 定义支付相关的业务逻辑接口
 */

package interfaces

import (
	"o_mall_backend/modules/payment/dto"
	"o_mall_backend/modules/payment/models"
)

// PaymentService 支付服务接口
type PaymentService interface {
	// 创建支付
	CreatePayment(req *dto.PaymentCreateRequest) (*dto.PaymentCreateResponse, error)

	// 查询支付状态
	QueryPayment(req *dto.PaymentQueryRequest) (*dto.PaymentQueryResponse, error)

	// 处理支付回调
	HandlePaymentCallback(req *dto.PaymentCallbackRequest) (bool, error)

	// 取消支付
	CancelPayment(req *dto.PaymentCancelRequest) (bool, error)

	// 获取用户支付列表
	GetUserPayments(req *dto.PaymentListRequest) (*dto.PagedPaymentList, error)

	// 获取支付详情
	GetPaymentDetail(paymentID int64, userID int64) (*models.Payment, error)

	// 获取可用的支付方式
	GetAvailablePaymentMethods() ([]dto.PaymentMethodInfo, error)

	// 获取支付日志
	GetPaymentLogs(paymentID int64, operatorID int64) ([]*models.PaymentLog, error)
}
