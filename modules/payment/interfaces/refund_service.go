/**
 * refund_service.go
 * 退款服务接口定义
 * 定义退款相关的业务逻辑接口
 */

package interfaces

import (
	"o_mall_backend/modules/payment/dto"
	"o_mall_backend/modules/payment/models"
)

// RefundService 退款服务接口
type RefundService interface {
	// 创建退款
	CreateRefund(req *dto.RefundCreateRequest) (*dto.RefundCreateResponse, error)

	// 查询退款状态
	QueryRefund(req *dto.RefundQueryRequest) (*dto.RefundQueryResponse, error)

	// 处理退款回调
	HandleRefundCallback(refundNo string, externalNo string, rawData string) (bool, error)

	// 获取用户退款列表
	GetUserRefunds(req *dto.RefundListRequest) (*dto.PagedRefundList, error)

	// 获取退款详情
	GetRefundDetail(refundID int64, userID int64) (*models.Refund, error)

	// 审批退款
	ApproveRefund(refundID int64, approverID int64, approved bool, remark string) error
	
	// 获取待审批的退款列表
	GetPendingApprovalRefunds(page, pageSize int) ([]*models.Refund, int64, error)
	
	// 还原订单优惠券状态
	RestoreCouponsForRefund(orderID int64) error
}
