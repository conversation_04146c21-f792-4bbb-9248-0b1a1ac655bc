/**
 * payment_model.go
 * 支付系统数据模型定义
 * 包含支付记录、支付方式、支付状态等基础结构
 */

package models

import (
	"time"
)

// PaymentMethod 支付方式枚举
type PaymentMethod int

const (
	// PaymentMethodUnknown 未知支付方式
	PaymentMethodUnknown PaymentMethod = iota
	// PaymentMethodWechat 微信支付
	PaymentMethodWechat
	// PaymentMethodAlipay 支付宝
	PaymentMethodAlipay
	// PaymentMethodCreditCard 信用卡
	PaymentMethodCreditCard
	// PaymentMethodBankTransfer 银行转账
	PaymentMethodBankTransfer
	// PaymentMethodBalance 余额支付
	PaymentMethodBalance
	// PaymentMethodCombination 组合支付
	PaymentMethodCombination
)

// String 将支付方式枚举转为字符串
func (p PaymentMethod) String() string {
	switch p {
	case PaymentMethodWechat:
		return "微信支付"
	case PaymentMethodAlipay:
		return "支付宝"
	case PaymentMethodCreditCard:
		return "信用卡"
	case PaymentMethodBankTransfer:
		return "银行转账"
	case PaymentMethodBalance:
		return "余额支付"
	case PaymentMethodCombination:
		return "组合支付"
	default:
		return "未知支付方式"
	}
}

// PaymentStatus 支付状态枚举
type PaymentStatus int

const (
	// PaymentStatusPending 待支付
	PaymentStatusPending PaymentStatus = iota
	// PaymentStatusProcessing 处理中
	PaymentStatusProcessing
	// PaymentStatusSuccess 支付成功
	PaymentStatusSuccess
	// PaymentStatusFailed 支付失败
	PaymentStatusFailed
	// PaymentStatusCancelled 已取消
	PaymentStatusCancelled
	// PaymentStatusRefunding 退款中
	PaymentStatusRefunding
	// PaymentStatusRefunded 已退款
	PaymentStatusRefunded
	// PaymentStatusPartialRefunded 部分退款
	PaymentStatusPartialRefunded
)

// String 将支付状态枚举转为字符串
func (p PaymentStatus) String() string {
	switch p {
	case PaymentStatusPending:
		return "待支付"
	case PaymentStatusProcessing:
		return "处理中"
	case PaymentStatusSuccess:
		return "支付成功"
	case PaymentStatusFailed:
		return "支付失败"
	case PaymentStatusCancelled:
		return "已取消"
	case PaymentStatusRefunding:
		return "退款中"
	case PaymentStatusRefunded:
		return "已退款"
	case PaymentStatusPartialRefunded:
		return "部分退款"
	default:
		return "未知状态"
	}
}

// Payment 支付记录
type Payment struct {
	ID              int64         `orm:"pk;auto;description(支付记录ID)" json:"id"`                          // 支付记录ID
	OrderID         int64         `orm:"index;description(关联订单ID)" json:"order_id"`                      // 关联订单ID
	UserID          int64         `orm:"index;description(支付用户ID)" json:"user_id"`                       // 支付用户ID
	Amount          float64       `orm:"digits(12);decimals(2);description(支付金额)" json:"amount"`         // 支付金额
	Method          PaymentMethod `orm:"index;description(支付方式)" json:"method"`                          // 支付方式
	Status          PaymentStatus `orm:"index;description(支付状态)" json:"status"`                          // 支付状态
	TransactionNo   string        `orm:"size(64);index;description(交易流水号)" json:"transaction_no"`        // 交易流水号
	ExternalTradeNo string        `orm:"size(64);index;description(外部支付平台交易号)" json:"external_trade_no"` // 外部支付平台交易号
	PaymentTime     *time.Time    `orm:"null;type(datetime);description(支付完成时间)" json:"payment_time"`    // 支付完成时间
	ExpireTime      *time.Time    `orm:"null;type(datetime);description(支付过期时间)" json:"expire_time"`     // 支付过期时间
	CallbackData    string        `orm:"type(text);null;description(支付回调原始数据)" json:"callback_data"`     // 支付回调原始数据
	ClientIP        string        `orm:"size(40);description(客户端IP)" json:"client_ip"`                   // 客户端IP
	DeviceInfo      string        `orm:"size(255);null;description(设备信息)" json:"device_info"`            // 设备信息
	Remark          string        `orm:"size(255);null;description(备注)" json:"remark"`                   // 备注
	CreatedAt       time.Time     `orm:"auto_now_add;type(datetime);description(创建时间)" json:"created_at"`
	UpdatedAt       time.Time     `orm:"auto_now;type(datetime);description(更新时间)" json:"updated_at"`
}

// Refund 退款记录
type Refund struct {
	ID             int64        `orm:"pk;auto;description(退款记录ID)" json:"id"`                      // 退款记录ID
	PaymentID      int64        `orm:"index;description(关联支付记录ID)" json:"payment_id"`              // 关联支付记录ID
	OrderID        int64        `orm:"index;description(关联订单ID)" json:"order_id"`                  // 关联订单ID
	UserID         int64        `orm:"index;description(用户ID)" json:"user_id"`                     // 用户ID
	Amount         float64      `orm:"digits(12);decimals(2);description(退款金额)" json:"amount"`     // 退款金额
	Status         RefundStatus `orm:"index;description(退款状态)" json:"status"`                      // 退款状态
	RefundNo       string       `orm:"size(64);index;description(退款流水号)" json:"refund_no"`         // 退款流水号
	ExternalNo     string       `orm:"size(64);index;description(外部退款号)" json:"external_no"`       // 外部退款号
	Reason         string       `orm:"size(255);description(退款原因)" json:"reason"`                  // 退款原因
	RefundTime     *time.Time   `orm:"null;type(datetime);description(退款完成时间)" json:"refund_time"` // 退款完成时间
	CallbackData   string       `orm:"type(text);null;description(退款回调原始数据)" json:"callback_data"` // 退款回调原始数据
	OperatorID     int64        `orm:"default(0);description(操作人ID)" json:"operator_id"`           // 操作人ID（0表示用户自己操作）
	ApprovalStatus int          `orm:"default(0);description(审批状态)" json:"approval_status"`        // 审批状态：0-无需审批，1-待审批，2-已通过，3-已拒绝
	ApproverID     int64        `orm:"default(0);description(审批人ID)" json:"approver_id"`           // 审批人ID
	ApprovalTime   *time.Time   `orm:"null;type(datetime);description(审批时间)" json:"approval_time"` // 审批时间
	ApprovalRemark string       `orm:"size(255);null;description(审批备注)" json:"approval_remark"`    // 审批备注
	CreatedAt      time.Time    `orm:"auto_now_add;type(datetime);description(创建时间)" json:"created_at"`
	UpdatedAt      time.Time    `orm:"auto_now;type(datetime);description(更新时间)" json:"updated_at"`
}

// PaymentAccount 支付账户（平台账户）
type PaymentAccount struct {
	ID          int64         `orm:"pk;auto;description(账户ID)" json:"id"`                          // 账户ID
	Name        string        `orm:"size(64);description(账户名称)" json:"name"`                       // 账户名称
	Method      PaymentMethod `orm:"index;description(支付方式)" json:"method"`                        // 支付方式
	AppID       string        `orm:"size(64);description(应用ID)" json:"app_id"`                     // 应用ID
	MerchantID  string        `orm:"size(64);description(商户ID)" json:"merchant_id"`                // 商户ID
	SecretKey   string        `orm:"size(255);description(密钥)" json:"secret_key"`                  // 密钥（存储时加密）
	PublicKey   string        `orm:"type(text);null;description(公钥)" json:"public_key"`            // 公钥
	PrivateKey  string        `orm:"type(text);null;description(私钥)" json:"private_key"`           // 私钥
	GatewayURL  string        `orm:"size(255);description(网关URL)" json:"gateway_url"`              // 网关URL
	NotifyURL   string        `orm:"size(255);description(回调通知URL)" json:"notify_url"`             // 回调通知URL
	Config      string        `orm:"type(text);null;description(配置JSON)" json:"config"`            // 配置JSON
	IsDefault   bool          `orm:"default(false);description(是否默认)" json:"is_default"`           // 是否默认
	Status      int           `orm:"default(1);description(状态)" json:"status"`                     // 状态：0-禁用，1-启用
	Description string        `orm:"size(255);null;description(描述)" json:"description"`            // 描述
	LastUseTime *time.Time    `orm:"null;type(datetime);description(最后使用时间)" json:"last_use_time"` // 最后使用时间
	CreatedAt   time.Time     `orm:"auto_now_add;type(datetime);description(创建时间)" json:"created_at"`
	UpdatedAt   time.Time     `orm:"auto_now;type(datetime);description(更新时间)" json:"updated_at"`
}

// PaymentLog 支付日志
type PaymentLog struct {
	ID           int64     `orm:"pk;auto;description(日志ID)" json:"id"`                    // 日志ID
	PaymentID    int64     `orm:"index;description(关联支付ID)" json:"payment_id"`            // 关联支付ID
	RefundID     int64     `orm:"default(0);description(关联退款ID)" json:"refund_id"`        // 关联退款ID（如果有）
	Action       string    `orm:"size(32);description(动作类型)" json:"action"`               // 动作类型：create, notify, query, refund等
	RequestData  string    `orm:"type(text);null;description(请求数据)" json:"request_data"`  // 请求数据
	ResponseData string    `orm:"type(text);null;description(响应数据)" json:"response_data"` // 响应数据
	Success      bool      `orm:"default(false);description(是否成功)" json:"success"`        // 是否成功
	ErrorMsg     string    `orm:"size(255);null;description(错误信息)" json:"error_msg"`      // 错误信息
	IP           string    `orm:"size(40);description(操作IP)" json:"ip"`                   // 操作IP
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);description(创建时间)" json:"created_at"`
}

// PaymentBill 支付账单（对账用）
type PaymentBill struct {
	ID                 int64         `orm:"pk;auto;description(账单ID)" json:"id"`                           // 账单ID
	BillDate           time.Time     `orm:"type(date);index;description(账单日期)" json:"bill_date"`           // 账单日期
	Method             PaymentMethod `orm:"index;description(支付方式)" json:"method"`                         // 支付方式
	TotalAmount        float64       `orm:"digits(12);decimals(2);description(总金额)" json:"total_amount"`   // 总金额
	RefundAmount       float64       `orm:"digits(12);decimals(2);description(退款金额)" json:"refund_amount"` // 退款金额
	FeeAmount          float64       `orm:"digits(12);decimals(2);description(手续费金额)" json:"fee_amount"`   // 手续费金额
	SettleAmount       float64       `orm:"digits(12);decimals(2);description(结算金额)" json:"settle_amount"` // 结算金额
	TransCount         int           `orm:"default(0);description(交易笔数)" json:"trans_count"`               // 交易笔数
	RefundCount        int           `orm:"default(0);description(退款笔数)" json:"refund_count"`              // 退款笔数
	AccountCheckStatus int           `orm:"default(0);description(对账状态)" json:"account_check_status"`      // 对账状态：0-未对账，1-对账中，2-已平账，3-有差异
	BillData           string        `orm:"type(text);null;description(账单原始数据)" json:"bill_data"`          // 账单原始数据
	BillFilePath       string        `orm:"size(255);null;description(账单文件路径)" json:"bill_file_path"`      // 账单文件路径
	Remark             string        `orm:"size(255);null;description(备注)" json:"remark"`                  // 备注
	CreatedAt          time.Time     `orm:"auto_now_add;type(datetime);description(创建时间)" json:"created_at"`
	UpdatedAt          time.Time     `orm:"auto_now;type(datetime);description(更新时间)" json:"updated_at"`
}
