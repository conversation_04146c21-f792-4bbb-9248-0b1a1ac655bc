/**
 * refund_status.go
 * 退款状态定义
 */

package models

// RefundStatus 退款状态枚举
type RefundStatus int

const (
	// RefundStatusPending 待退款
	RefundStatusPending RefundStatus = iota
	// RefundStatusProcessing 退款处理中
	RefundStatusProcessing
	// RefundStatusSuccess 退款成功
	RefundStatusSuccess
	// RefundStatusFailed 退款失败
	RefundStatusFailed
	// RefundStatusClosed 已关闭
	RefundStatusClosed
	// RefundStatusWaitingApproval 等待审批
	RefundStatusWaitingApproval
	// RefundStatusRejected 已拒绝
	RefundStatusRejected
)

// String 将退款状态枚举转为字符串
func (r RefundStatus) String() string {
	switch r {
	case RefundStatusPending:
		return "待退款"
	case RefundStatusProcessing:
		return "退款处理中"
	case RefundStatusSuccess:
		return "退款成功"
	case RefundStatusFailed:
		return "退款失败"
	case RefundStatusClosed:
		return "已关闭"
	case RefundStatusWaitingApproval:
		return "等待审批"
	case RefundStatusRejected:
		return "已拒绝"
	default:
		return "未知状态"
	}
}
