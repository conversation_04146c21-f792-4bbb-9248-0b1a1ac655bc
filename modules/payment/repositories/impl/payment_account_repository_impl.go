/**
 * payment_account_repository_impl.go
 * 支付账户仓储层实现
 * 负责平台支付账户配置的数据库操作
 */

package impl

import (
	"errors"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/common"
	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
)

// PaymentAccountRepositoryImpl 支付账户仓储实现
type PaymentAccountRepositoryImpl struct{}

// NewPaymentAccountRepository 创建支付账户仓储
func NewPaymentAccountRepository() repositories.PaymentAccountRepository {
	return &PaymentAccountRepositoryImpl{}
}

// CreatePaymentAccount 创建支付账户
func (r *PaymentAccountRepositoryImpl) CreatePaymentAccount(account *models.PaymentAccount) (int64, error) {
	if account == nil {
		return 0, errors.New("账户信息不能为空")
	}

	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return 0, err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 插入支付账户
	id, err := tx.Insert(account)
	if err != nil {
		txManager.Rollback()
		logs.Error("插入支付账户失败: %v", err)
		return 0, err
	}

	// 如果设置为默认账户，需要将同类型的其他账户设置为非默认
	if account.IsDefault {
		_, err = tx.QueryTable(new(models.PaymentAccount)).
			Filter("method", account.Method).
			Filter("id__ne", id).
			Update(orm.Params{"is_default": false})
		if err != nil {
			txManager.Rollback()
			logs.Error("更新其他账户默认状态失败: %v", err)
			return 0, err
		}
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		txManager.Rollback()
		logs.Error("提交事务失败: %v", err)
		return 0, err
	}

	return id, nil
}

// GetPaymentAccountByID 获取支付账户
func (r *PaymentAccountRepositoryImpl) GetPaymentAccountByID(id int64) (*models.PaymentAccount, error) {
	if id <= 0 {
		return nil, errors.New("无效的账户ID")
	}

	o := orm.NewOrm()
	account := &models.PaymentAccount{ID: id}

	err := o.Read(account)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("支付账户不存在")
		}
		logs.Error("查询支付账户失败: %v", err)
		return nil, err
	}

	return account, nil
}

// UpdatePaymentAccount 更新支付账户
func (r *PaymentAccountRepositoryImpl) UpdatePaymentAccount(account *models.PaymentAccount) error {
	if account == nil || account.ID <= 0 {
		return errors.New("无效的账户信息")
	}

	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 更新支付账户
	_, err = tx.Update(account)
	if err != nil {
		txManager.Rollback()
		logs.Error("更新支付账户失败: %v", err)
		return err
	}

	// 如果设置为默认账户，需要将同类型的其他账户设置为非默认
	if account.IsDefault {
		_, err = tx.QueryTable(new(models.PaymentAccount)).
			Filter("method", account.Method).
			Filter("id__ne", account.ID).
			Update(orm.Params{"is_default": false})
		if err != nil {
			txManager.Rollback()
			logs.Error("更新其他账户默认状态失败: %v", err)
			return err
		}
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		txManager.Rollback()
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// DeletePaymentAccount 删除支付账户
func (r *PaymentAccountRepositoryImpl) DeletePaymentAccount(id int64) error {
	if id <= 0 {
		return errors.New("无效的账户ID")
	}

	o := orm.NewOrm()
	account := &models.PaymentAccount{ID: id}

	// 先查询账户是否存在
	err := o.Read(account)
	if err != nil {
		if err == orm.ErrNoRows {
			return errors.New("支付账户不存在")
		}
		logs.Error("查询支付账户失败: %v", err)
		return err
	}

	// 删除账户
	_, err = o.Delete(account)
	if err != nil {
		logs.Error("删除支付账户失败: %v", err)
		return err
	}

	return nil
}

// GetAllPaymentAccounts 获取所有支付账户
func (r *PaymentAccountRepositoryImpl) GetAllPaymentAccounts() ([]*models.PaymentAccount, error) {
	o := orm.NewOrm()
	var accounts []*models.PaymentAccount

	_, err := o.QueryTable(new(models.PaymentAccount)).OrderBy("method", "-is_default").All(&accounts)
	if err != nil {
		logs.Error("查询所有支付账户失败: %v", err)
		return nil, err
	}

	return accounts, nil
}

// GetPaymentAccountsByMethod 获取指定支付方式的账户
func (r *PaymentAccountRepositoryImpl) GetPaymentAccountsByMethod(method models.PaymentMethod) ([]*models.PaymentAccount, error) {
	o := orm.NewOrm()
	var accounts []*models.PaymentAccount

	_, err := o.QueryTable(new(models.PaymentAccount)).
		Filter("method", method).
		Filter("status", 1). // 只查询启用状态的账户
		OrderBy("-is_default").
		All(&accounts)

	if err != nil {
		logs.Error("查询支付方式账户失败: %v", err)
		return nil, err
	}

	return accounts, nil
}

// GetDefaultPaymentAccount 获取默认支付账户
func (r *PaymentAccountRepositoryImpl) GetDefaultPaymentAccount(method models.PaymentMethod) (*models.PaymentAccount, error) {
	o := orm.NewOrm()
	account := &models.PaymentAccount{}

	err := o.QueryTable(new(models.PaymentAccount)).
		Filter("method", method).
		Filter("is_default", true).
		Filter("status", 1). // 只查询启用状态的账户
		One(account)

	if err != nil {
		if err == orm.ErrNoRows {
			// 如果没有默认账户，尝试获取第一个启用的账户
			err = o.QueryTable(new(models.PaymentAccount)).
				Filter("method", method).
				Filter("status", 1).
				One(account)

			if err != nil {
				if err == orm.ErrNoRows {
					return nil, errors.New("没有可用的支付账户")
				}
				logs.Error("查询支付账户失败: %v", err)
				return nil, err
			}
		} else {
			logs.Error("查询默认支付账户失败: %v", err)
			return nil, err
		}
	}

	return account, nil
}

// SetDefaultPaymentAccount 设置默认支付账户
func (r *PaymentAccountRepositoryImpl) SetDefaultPaymentAccount(id int64, method models.PaymentMethod) error {
	if id <= 0 {
		return errors.New("无效的账户ID")
	}

	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 将同类型的所有账户设为非默认
	_, err = tx.QueryTable(new(models.PaymentAccount)).
		Filter("method", method).
		Update(orm.Params{"is_default": false})

	if err != nil {
		txManager.Rollback()
		logs.Error("更新账户为非默认失败: %v", err)
		return err
	}

	// 将指定账户设为默认
	_, err = tx.QueryTable(new(models.PaymentAccount)).
		Filter("id", id).
		Update(orm.Params{"is_default": true})

	if err != nil {
		txManager.Rollback()
		logs.Error("设置默认账户失败: %v", err)
		return err
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		txManager.Rollback()
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}