/**
 * payment_repository_impl.go
 * 支付记录仓储层实现
 * 负责支付记录的数据库操作
 */

package impl

import (
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/common"
	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
)

// PaymentRepositoryImpl 支付仓储实现
type PaymentRepositoryImpl struct{}

// NewPaymentRepository 创建支付仓储
func NewPaymentRepository() repositories.PaymentRepository {
	return &PaymentRepositoryImpl{}
}

// CreatePayment 创建支付记录
func (r *PaymentRepositoryImpl) CreatePayment(payment *models.Payment) (int64, error) {
	logs.Info("[支付仓储层] 开始创建支付记录 - 用户ID: %d, 订单ID: %d, 支付方式: %s, 金额: %.2f",
		payment.UserID, payment.OrderID, payment.Method.String(), payment.Amount)

	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[支付仓储层] 开始事务失败: %v", err)
		return 0, err
	}
	logs.Info("[支付仓储层] 数据库事务开启成功")

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logs.Error("[支付仓储层] 创建支付记录过程中出现异常: %v", r)
		}
	}()

	// 插入支付记录
	logs.Info("[支付仓储层] 准备插入支付记录 - 交易流水号: %s, 状态: %s",
		payment.TransactionNo, payment.Status.String())
	id, err := tx.Insert(payment)
	if err != nil {
		tx.Rollback()
		logs.Error("[支付仓储层] 插入支付记录失败: %v", err)
		return 0, err
	}
	logs.Info("[支付仓储层] 支付记录插入成功 - 支付ID: %d", id)

	// 提交事务
	logs.Info("[支付仓储层] 准备提交事务 - 支付ID: %d", id)
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[支付仓储层] 提交事务失败: %v, 支付ID: %d", err, id)
		return 0, err
	}
	logs.Info("[支付仓储层] 事务提交成功，支付记录创建完成 - 支付ID: %d, 订单ID: %d", id, payment.OrderID)

	return id, nil
}

// GetPaymentByID 通过ID获取支付记录
func (r *PaymentRepositoryImpl) GetPaymentByID(id int64) (*models.Payment, error) {
	if id <= 0 {
		return nil, errors.New("无效的支付ID")
	}

	o := orm.NewOrm()
	payment := &models.Payment{ID: id}

	err := o.Read(payment)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("支付记录不存在")
		}
		logs.Error("查询支付记录失败: %v", err)
		return nil, err
	}

	return payment, nil
}

// GetPaymentByTransactionNo 通过交易号获取支付记录
func (r *PaymentRepositoryImpl) GetPaymentByTransactionNo(transactionNo string) (*models.Payment, error) {
	if transactionNo == "" {
		return nil, errors.New("交易号不能为空")
	}

	o := orm.NewOrm()
	payment := &models.Payment{}

	err := o.QueryTable(new(models.Payment)).Filter("transaction_no", transactionNo).One(payment)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("支付记录不存在")
		}
		logs.Error("查询支付记录失败: %v", err)
		return nil, err
	}

	return payment, nil
}

// GetPaymentByOrderID 通过订单ID获取支付记录（优先返回最新的成功支付记录）
func (r *PaymentRepositoryImpl) GetPaymentByOrderID(orderID int64) (*models.Payment, error) {
	if orderID <= 0 {
		return nil, errors.New("无效的订单ID")
	}

	o := orm.NewOrm()
	payment := &models.Payment{}

	// 首先尝试获取最新的成功支付记录
	err := o.QueryTable(new(models.Payment)).Filter("order_id", orderID).Filter("status", models.PaymentStatusSuccess).OrderBy("-created_at").One(payment)
	if err == nil {
		logs.Info("[支付仓储层] 找到成功支付记录 - 订单ID: %d, 支付ID: %d, 状态: %s", orderID, payment.ID, payment.Status.String())
		return payment, nil
	}

	// 如果没有成功的支付记录，则获取最新的支付记录
	err = o.QueryTable(new(models.Payment)).Filter("order_id", orderID).OrderBy("-created_at").One(payment)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("支付记录不存在")
		}
		logs.Error("查询支付记录失败: %v", err)
		return nil, err
	}

	logs.Info("[支付仓储层] 找到支付记录 - 订单ID: %d, 支付ID: %d, 状态: %s", orderID, payment.ID, payment.Status.String())
	return payment, nil
}

// UpdatePayment 更新支付记录
func (r *PaymentRepositoryImpl) UpdatePayment(payment *models.Payment) error {
	if payment.ID <= 0 {
		return errors.New("无效的支付ID")
	}

	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 更新支付记录
	_, err = tx.Update(payment)
	if err != nil {
		txManager.Rollback()
		logs.Error("更新支付记录失败: %v", err)
		return err
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		txManager.Rollback()
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// GetUserPayments 获取用户的支付记录列表
func (r *PaymentRepositoryImpl) GetUserPayments(userID int64, status models.PaymentStatus, page, pageSize int) ([]*models.Payment, int64, error) {
	if userID <= 0 {
		return nil, 0, errors.New("无效的用户ID")
	}

	if page <= 0 {
		page = 1
	}

	if pageSize <= 0 {
		pageSize = 10
	}

	o := orm.NewOrm()
	qs := o.QueryTable(new(models.Payment)).Filter("user_id", userID)

	// 如果指定了状态，则按状态筛选
	if status > 0 {
		qs = qs.Filter("status", status)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("获取支付记录总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	var payments []*models.Payment
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&payments)
	if err != nil {
		logs.Error("查询支付记录列表失败: %v", err)
		return nil, 0, err
	}

	return payments, total, nil
}

// CreatePaymentLog 创建支付日志
func (r *PaymentRepositoryImpl) CreatePaymentLog(log *models.PaymentLog) (int64, error) {
	o := orm.NewOrm()

	// 插入支付日志
	id, err := o.Insert(log)
	if err != nil {
		logs.Error("插入支付日志失败: %v", err)
		return 0, err
	}

	return id, nil
}

// GetPaymentLogs 获取支付记录的所有日志
func (r *PaymentRepositoryImpl) GetPaymentLogs(paymentID int64) ([]*models.PaymentLog, error) {
	if paymentID <= 0 {
		return nil, errors.New("无效的支付ID")
	}

	o := orm.NewOrm()
	var logs []*models.PaymentLog

	_, err := o.QueryTable(new(models.PaymentLog)).Filter("payment_id", paymentID).OrderBy("-created_at").All(&logs)
	if err != nil {
		return nil, err
	}

	return logs, nil
}

// UpdatePaymentStatus 更新支付状态
func (r *PaymentRepositoryImpl) UpdatePaymentStatus(paymentID int64, status models.PaymentStatus, externalTradeNo string) error {
	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 使用事务更新支付状态
	err = r.UpdatePaymentStatusTx(tx, paymentID, status, externalTradeNo)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// UpdatePaymentStatusTx 在事务中更新支付状态
func (r *PaymentRepositoryImpl) UpdatePaymentStatusTx(o orm.TxOrmer, paymentID int64, status models.PaymentStatus, externalTradeNo string) error {
	// 获取支付记录
	payment := &models.Payment{ID: paymentID}
	err := o.Read(payment)
	if err != nil {
		logs.Error("获取支付记录失败: %v, 支付ID: %d", err, paymentID)
		return err
	}

	// 更新状态
	payment.Status = status
	payment.UpdatedAt = time.Now()

	// 如果有外部交易号则更新
	if externalTradeNo != "" {
		payment.ExternalTradeNo = externalTradeNo
	}

	// 如果是成功支付状态，记录支付时间
	if status == models.PaymentStatusSuccess {
		now := time.Now()
		payment.PaymentTime = &now
	}

	// 更新支付记录
	_, err = o.Update(payment, "Status", "UpdatedAt", "ExternalTradeNo", "PaymentTime")
	if err != nil {
		logs.Error("更新支付状态失败: %v, 支付ID: %d", err, paymentID)
		return err
	}

	return nil
}

// GetExpiredPayments 获取过期的支付记录
func (r *PaymentRepositoryImpl) GetExpiredPayments() ([]*models.Payment, error) {
	o := orm.NewOrm()
	var payments []*models.Payment

	// 查询状态为待支付且过期时间小于当前时间的支付记录
	_, err := o.QueryTable(new(models.Payment)).
		Filter("status", models.PaymentStatusPending).
		Filter("expire_time__lt", time.Now()).
		All(&payments)

	if err != nil {
		logs.Error("查询过期支付记录失败: %v", err)
		return nil, err
	}

	return payments, nil
}
