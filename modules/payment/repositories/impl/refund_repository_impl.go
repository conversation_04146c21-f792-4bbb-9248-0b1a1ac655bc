/**
 * refund_repository_impl.go
 * 退款记录仓储层实现
 * 负责退款记录的数据库操作
 */

package impl

import (
	"errors"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/common"
	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
)

// RefundRepositoryImpl 退款仓储实现
type RefundRepositoryImpl struct{}

// NewRefundRepository 创建退款仓储
func NewRefundRepository() repositories.RefundRepository {
	return &RefundRepositoryImpl{}
}

// CreateRefund 创建退款记录
func (r *RefundRepositoryImpl) CreateRefund(refund *models.Refund) (int64, error) {
	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return 0, err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 插入退款记录
	id, err := tx.Insert(refund)
	if err != nil {
		txManager.Rollback()
		logs.Error("插入退款记录失败: %v", err)
		return 0, err
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		txManager.Rollback()
		logs.Error("提交事务失败: %v", err)
		return 0, err
	}

	return id, nil
}

// GetRefundByID 通过ID获取退款记录
func (r *RefundRepositoryImpl) GetRefundByID(id int64) (*models.Refund, error) {
	if id <= 0 {
		return nil, errors.New("无效的退款ID")
	}

	o := orm.NewOrm()
	refund := &models.Refund{ID: id}

	err := o.Read(refund)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("退款记录不存在")
		}
		logs.Error("查询退款记录失败: %v", err)
		return nil, err
	}

	return refund, nil
}

// GetRefundByRefundNo 通过退款流水号获取退款记录
func (r *RefundRepositoryImpl) GetRefundByRefundNo(refundNo string) (*models.Refund, error) {
	if refundNo == "" {
		return nil, errors.New("退款流水号不能为空")
	}

	o := orm.NewOrm()
	refund := &models.Refund{}

	err := o.QueryTable(new(models.Refund)).Filter("refund_no", refundNo).One(refund)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("退款记录不存在")
		}
		logs.Error("查询退款记录失败: %v", err)
		return nil, err
	}

	return refund, nil
}

// GetRefundsByPaymentID 获取支付记录的所有退款
func (r *RefundRepositoryImpl) GetRefundsByPaymentID(paymentID int64) ([]*models.Refund, error) {
	if paymentID <= 0 {
		return nil, errors.New("无效的支付ID")
	}

	o := orm.NewOrm()
	var refunds []*models.Refund

	_, err := o.QueryTable(new(models.Refund)).Filter("payment_id", paymentID).OrderBy("-created_at").All(&refunds)
	if err != nil {
		logs.Error("查询支付退款记录失败: %v", err)
		return nil, err
	}

	return refunds, nil
}

// GetRefundsByOrderID 获取订单的所有退款
func (r *RefundRepositoryImpl) GetRefundsByOrderID(orderID int64) ([]*models.Refund, error) {
	if orderID <= 0 {
		return nil, errors.New("无效的订单ID")
	}

	o := orm.NewOrm()
	var refunds []*models.Refund

	_, err := o.QueryTable(new(models.Refund)).Filter("order_id", orderID).OrderBy("-created_at").All(&refunds)
	if err != nil {
		logs.Error("查询订单退款记录失败: %v", err)
		return nil, err
	}

	return refunds, nil
}

// UpdateRefund 更新退款记录
func (r *RefundRepositoryImpl) UpdateRefund(refund *models.Refund) error {
	if refund.ID <= 0 {
		return errors.New("无效的退款ID")
	}

	o := orm.NewOrm()

	// 更新退款记录
	_, err := o.Update(refund)
	if err != nil {
		logs.Error("更新退款记录失败: %v", err)
		return err
	}

	return nil
}

// GetUserRefunds 获取用户退款记录
func (r *RefundRepositoryImpl) GetUserRefunds(userID int64, status models.RefundStatus, page, pageSize int) ([]*models.Refund, int64, error) {
	if userID <= 0 {
		return nil, 0, errors.New("无效的用户ID")
	}

	if page <= 0 {
		page = 1
	}

	if pageSize <= 0 {
		pageSize = 10
	}

	o := orm.NewOrm()
	qs := o.QueryTable(new(models.Refund)).Filter("user_id", userID)

	// 如果指定了状态，则按状态筛选
	if status > 0 {
		qs = qs.Filter("status", status)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("获取退款记录总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	var refunds []*models.Refund
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&refunds)
	if err != nil {
		logs.Error("查询退款记录列表失败: %v", err)
		return nil, 0, err
	}

	return refunds, total, nil
}

// GetPendingApprovalRefunds 获取待审批的退款记录
func (r *RefundRepositoryImpl) GetPendingApprovalRefunds(page, pageSize int) ([]*models.Refund, int64, error) {
	if page <= 0 {
		page = 1
	}

	if pageSize <= 0 {
		pageSize = 10
	}

	o := orm.NewOrm()
	qs := o.QueryTable(new(models.Refund)).Filter("approval_status", 1) // 待审批状态

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("获取待审批退款记录总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	var refunds []*models.Refund
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&refunds)
	if err != nil {
		logs.Error("查询待审批退款记录列表失败: %v", err)
		return nil, 0, err
	}

	return refunds, total, nil
}

// GetRefundsByStatus 获取指定状态的退款记录
func (r *RefundRepositoryImpl) GetRefundsByStatus(status models.RefundStatus, page, pageSize int) ([]*models.Refund, int64, error) {
	if page <= 0 {
		page = 1
	}

	if pageSize <= 0 {
		pageSize = 10
	}

	o := orm.NewOrm()
	qs := o.QueryTable(new(models.Refund)).Filter("status", status)

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("查询指定状态退款记录总数失败: %v", err)
		return nil, 0, err
	}

	var refunds []*models.Refund
	_, err = o.QueryTable(new(models.Refund)).
		Filter("status", status).
		OrderBy("-created_at").
		Limit(pageSize, (page-1)*pageSize).
		All(&refunds)

	if err != nil {
		logs.Error("查询指定状态退款记录失败: %v", err)
		return nil, 0, err
	}

	return refunds, total, nil
}
