/**
 * payment_account_repository.go
 * 支付账户仓储层实现
 * 负责平台支付账户配置的数据库操作
 */

package repositories

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/common"
	"o_mall_backend/modules/payment/models"
)

// PaymentAccountRepository 支付账户仓储接口
type PaymentAccountRepository interface {
	// 创建支付账户
	CreatePaymentAccount(account *models.PaymentAccount) (int64, error)
	// 获取支付账户
	GetPaymentAccountByID(id int64) (*models.PaymentAccount, error)
	// 更新支付账户
	UpdatePaymentAccount(account *models.PaymentAccount) error
	// 删除支付账户
	DeletePaymentAccount(id int64) error
	// 获取所有支付账户
	GetAllPaymentAccounts() ([]*models.PaymentAccount, error)
	// 获取指定支付方式的账户
	GetPaymentAccountsByMethod(method models.PaymentMethod) ([]*models.PaymentAccount, error)
	// 获取默认支付账户
	GetDefaultPaymentAccount(method models.PaymentMethod) (*models.PaymentAccount, error)
	// 设置默认支付账户
	SetDefaultPaymentAccount(id int64, method models.PaymentMethod) error
}

// PaymentAccountRepositoryImpl 支付账户仓储实现
type PaymentAccountRepositoryImpl struct{}

// NewPaymentAccountRepository 创建支付账户仓储
func NewPaymentAccountRepository() PaymentAccountRepository {
	return &PaymentAccountRepositoryImpl{}
}

// CreatePaymentAccount 创建支付账户
func (r *PaymentAccountRepositoryImpl) CreatePaymentAccount(account *models.PaymentAccount) (int64, error) {
	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败：%v", err)
		return 0, err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 如果设置为默认账户，先将同类型的其他账户设为非默认
	if account.IsDefault {
		_, err = tx.QueryTable(new(models.PaymentAccount)).
			Filter("method", account.Method).
			Update(orm.Params{"is_default": false})
		if err != nil {
			txManager.Rollback()
			logs.Error("更新其他账户为非默认失败：%v", err)
			return 0, err
		}
	}

	// 插入新账户
	id, err := tx.Insert(account)
	if err != nil {
		txManager.Rollback()
		logs.Error("创建支付账户失败：%v", err)
		return 0, err
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败：%v", err)
		return 0, err
	}

	return id, nil
}

// GetPaymentAccountByID 获取支付账户
func (r *PaymentAccountRepositoryImpl) GetPaymentAccountByID(id int64) (*models.PaymentAccount, error) {
	o := orm.NewOrm()
	account := &models.PaymentAccount{ID: id}
	err := o.Read(account)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("查询支付账户失败：%v", err)
		return nil, err
	}
	return account, nil
}

// UpdatePaymentAccount 更新支付账户
func (r *PaymentAccountRepositoryImpl) UpdatePaymentAccount(account *models.PaymentAccount) error {
	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败：%v", err)
		return err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 如果设置为默认账户，先将同类型的其他账户设为非默认
	if account.IsDefault {
		_, err = tx.QueryTable(new(models.PaymentAccount)).
			Filter("method", account.Method).
			Filter("id__ne", account.ID).
			Update(orm.Params{"is_default": false})
		if err != nil {
			txManager.Rollback()
			logs.Error("更新其他账户为非默认失败：%v", err)
			return err
		}
	}

	// 更新账户
	_, err = tx.Update(account)
	if err != nil {
		txManager.Rollback()
		logs.Error("更新支付账户失败：%v", err)
		return err
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败：%v", err)
		return err
	}

	return nil
}

// DeletePaymentAccount 删除支付账户
func (r *PaymentAccountRepositoryImpl) DeletePaymentAccount(id int64) error {
	o := orm.NewOrm()
	account := &models.PaymentAccount{ID: id}

	// 先读取账户信息
	err := o.Read(account)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil
		}
		logs.Error("读取支付账户失败：%v", err)
		return err
	}

	// 删除账户
	_, err = o.Delete(account)
	if err != nil {
		logs.Error("删除支付账户失败：%v", err)
		return err
	}

	// 如果删除的是默认账户，尝试设置同类型的另一个账户为默认
	if account.IsDefault {
		var accounts []*models.PaymentAccount
		_, err = o.QueryTable(new(models.PaymentAccount)).
			Filter("method", account.Method).
			OrderBy("id").
			Limit(1).
			All(&accounts)

		if err == nil && len(accounts) > 0 {
			accounts[0].IsDefault = true
			_, err = o.Update(accounts[0], "is_default")
			if err != nil {
				logs.Error("设置新的默认账户失败：%v", err)
				// 不返回错误，因为主要操作已成功
			}
		}
	}

	return nil
}

// GetAllPaymentAccounts 获取所有支付账户
func (r *PaymentAccountRepositoryImpl) GetAllPaymentAccounts() ([]*models.PaymentAccount, error) {
	o := orm.NewOrm()
	var accounts []*models.PaymentAccount

	_, err := o.QueryTable(new(models.PaymentAccount)).OrderBy("method", "-is_default", "id").All(&accounts)
	if err != nil {
		logs.Error("获取所有支付账户失败：%v", err)
		return nil, err
	}

	return accounts, nil
}

// GetPaymentAccountsByMethod 获取指定支付方式的账户
func (r *PaymentAccountRepositoryImpl) GetPaymentAccountsByMethod(method models.PaymentMethod) ([]*models.PaymentAccount, error) {
	o := orm.NewOrm()
	var accounts []*models.PaymentAccount

	_, err := o.QueryTable(new(models.PaymentAccount)).
		Filter("method", method).
		Filter("status", 1). // 只返回启用状态的账户
		OrderBy("-is_default", "id").
		All(&accounts)

	if err != nil {
		logs.Error("获取指定支付方式账户失败：%v", err)
		return nil, err
	}

	return accounts, nil
}

// GetDefaultPaymentAccount 获取默认支付账户
func (r *PaymentAccountRepositoryImpl) GetDefaultPaymentAccount(method models.PaymentMethod) (*models.PaymentAccount, error) {
	o := orm.NewOrm()
	account := &models.PaymentAccount{}

	err := o.QueryTable(new(models.PaymentAccount)).
		Filter("method", method).
		Filter("is_default", true).
		Filter("status", 1). // 只返回启用状态的账户
		One(account)

	if err != nil {
		if err == orm.ErrNoRows {
			// 如果没有默认账户，尝试获取此方式的第一个启用账户
			err = o.QueryTable(new(models.PaymentAccount)).
				Filter("method", method).
				Filter("status", 1).
				OrderBy("id").
				One(account)

			if err != nil {
				if err == orm.ErrNoRows {
					return nil, nil
				}
				logs.Error("获取支付方式的第一个账户失败：%v", err)
				return nil, err
			}
		} else {
			logs.Error("获取默认支付账户失败：%v", err)
			return nil, err
		}
	}

	return account, nil
}

// SetDefaultPaymentAccount 设置默认支付账户
func (r *PaymentAccountRepositoryImpl) SetDefaultPaymentAccount(id int64, method models.PaymentMethod) error {
	// 创建事务管理器
	txManager := common.NewTransactionManager()

	// 开启事务
	err := txManager.Begin()
	if err != nil {
		logs.Error("开启事务失败：%v", err)
		return err
	}

	// 获取事务对象
	tx := txManager.GetTx().(orm.TxOrmer)

	// 将同类型的所有账户设为非默认
	_, err = tx.QueryTable(new(models.PaymentAccount)).
		Filter("method", method).
		Update(orm.Params{"is_default": false})

	if err != nil {
		txManager.Rollback()
		logs.Error("更新账户为非默认失败：%v", err)
		return err
	}

	// 将指定账户设为默认
	_, err = tx.QueryTable(new(models.PaymentAccount)).
		Filter("id", id).
		Update(orm.Params{"is_default": true})

	if err != nil {
		txManager.Rollback()
		logs.Error("设置默认账户失败：%v", err)
		return err
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		logs.Error("提交事务失败：%v", err)
		return err
	}

	return nil
}
