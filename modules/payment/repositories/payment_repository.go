/**
 * payment_repository.go
 * 支付记录仓储层实现
 * 负责支付记录的数据库操作
 */

package repositories

import (
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/models"
)

// PaymentRepository 支付仓储接口
type PaymentRepository interface {
	// 创建支付记录
	CreatePayment(payment *models.Payment) (int64, error)
	// 通过ID获取支付记录
	GetPaymentByID(id int64) (*models.Payment, error)
	// 通过交易号获取支付记录
	GetPaymentByTransactionNo(transactionNo string) (*models.Payment, error)
	// 通过订单ID获取支付记录
	GetPaymentByOrderID(orderID int64) (*models.Payment, error)
	// 更新支付记录
	UpdatePayment(payment *models.Payment) error
	// 更新支付状态
	UpdatePaymentStatus(paymentID int64, status models.PaymentStatus, externalTradeNo string) error
	// 在事务中更新支付状态
	UpdatePaymentStatusTx(o orm.TxOrmer, paymentID int64, status models.PaymentStatus, externalTradeNo string) error
	// 获取用户的支付记录列表
	GetUserPayments(userID int64, status models.PaymentStatus, page, pageSize int) ([]*models.Payment, int64, error)
	// 创建支付日志
	CreatePaymentLog(log *models.PaymentLog) (int64, error)
	// 获取支付记录的所有日志
	GetPaymentLogs(paymentID int64) ([]*models.PaymentLog, error)
	// 获取过期的支付记录
	GetExpiredPayments() ([]*models.Payment, error)
}

// PaymentRepositoryImpl 支付仓储实现
type PaymentRepositoryImpl struct{}

// NewPaymentRepository 创建支付仓储
func NewPaymentRepository() PaymentRepository {
	return &PaymentRepositoryImpl{}
}

// CreatePayment 创建支付记录
func (r *PaymentRepositoryImpl) CreatePayment(payment *models.Payment) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(payment)
	if err != nil {
		logs.Error("创建支付记录失败：%v", err)
		return 0, err
	}
	return id, nil
}

// GetPaymentByID 通过ID获取支付记录
func (r *PaymentRepositoryImpl) GetPaymentByID(id int64) (*models.Payment, error) {
	o := orm.NewOrm()
	payment := &models.Payment{ID: id}
	err := o.Read(payment)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("查询支付记录失败：%v", err)
		return nil, err
	}
	return payment, nil
}

// GetPaymentByTransactionNo 通过交易号获取支付记录
func (r *PaymentRepositoryImpl) GetPaymentByTransactionNo(transactionNo string) (*models.Payment, error) {
	o := orm.NewOrm()
	payment := &models.Payment{}
	err := o.QueryTable(new(models.Payment)).Filter("transaction_no", transactionNo).One(payment)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("通过交易号查询支付记录失败：%v", err)
		return nil, err
	}
	return payment, nil
}

// GetPaymentByOrderID 通过订单ID获取支付记录
func (r *PaymentRepositoryImpl) GetPaymentByOrderID(orderID int64) (*models.Payment, error) {
	o := orm.NewOrm()
	payment := &models.Payment{}
	err := o.QueryTable(new(models.Payment)).Filter("OrderID", orderID).One(payment)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("通过订单ID查询支付记录失败：%v", err)
		return nil, err
	}
	return payment, nil
}

// UpdatePayment 更新支付记录
func (r *PaymentRepositoryImpl) UpdatePayment(payment *models.Payment) error {
	o := orm.NewOrm()
	_, err := o.Update(payment)
	if err != nil {
		logs.Error("更新支付记录失败：%v", err)
		return err
	}
	return nil
}

// GetUserPayments 获取用户的支付记录列表
func (r *PaymentRepositoryImpl) GetUserPayments(userID int64, status models.PaymentStatus, page, pageSize int) ([]*models.Payment, int64, error) {
	o := orm.NewOrm()
	var payments []*models.Payment

	query := o.QueryTable(new(models.Payment)).Filter("user_id", userID)

	// 如果状态不为零值，则添加状态过滤
	if status != 0 {
		query = query.Filter("status", status)
	}

	// 计算总数
	count, err := query.Count()
	if err != nil {
		logs.Error("统计用户支付记录数量失败：%v", err)
		return nil, 0, err
	}

	// 没有记录，直接返回空列表
	if count == 0 {
		return []*models.Payment{}, 0, nil
	}

	// 分页查询
	offset := (page - 1) * pageSize
	_, err = query.OrderBy("-created_at").Limit(pageSize, offset).All(&payments)
	if err != nil {
		logs.Error("查询用户支付记录列表失败：%v", err)
		return nil, 0, err
	}

	return payments, count, nil
}

// CreatePaymentLog 创建支付日志
func (r *PaymentRepositoryImpl) CreatePaymentLog(log *models.PaymentLog) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(log)
	if err != nil {
		logs.Error("创建支付日志失败：%v", err)
		return 0, err
	}
	return id, nil
}

// GetPaymentLogs 获取支付记录的所有日志
func (r *PaymentRepositoryImpl) GetPaymentLogs(paymentID int64) ([]*models.PaymentLog, error) {
	o := orm.NewOrm()
	var paymentLogs []*models.PaymentLog

	_, err := o.QueryTable(new(models.PaymentLog)).Filter("payment_id", paymentID).OrderBy("created_at").All(&paymentLogs)
	if err != nil {
		logs.Error("查询支付日志失败：%v", err)
		return nil, err
	}

	return paymentLogs, nil
}

// GetExpiredPayments 获取过期的支付记录
func (r *PaymentRepositoryImpl) GetExpiredPayments() ([]*models.Payment, error) {
	o := orm.NewOrm()
	var payments []*models.Payment

	// 获取状态为待支付且过期时间已过的支付记录
	now := time.Now()
	_, err := o.QueryTable(new(models.Payment)).
		Filter("status", models.PaymentStatusPending).
		Filter("expire_time__lt", now).
		All(&payments)

	if err != nil {
		logs.Error("查询过期支付记录失败：%v", err)
		return nil, err
	}

	return payments, nil
}

// UpdatePaymentStatus 更新支付状态
func (r *PaymentRepositoryImpl) UpdatePaymentStatus(paymentID int64, status models.PaymentStatus, externalTradeNo string) error {
	o := orm.NewOrm()

	// 先获取支付记录
	payment, err := r.GetPaymentByID(paymentID)
	if err != nil {
		logs.Error("获取支付记录失败：%v", err)
		return err
	}

	if payment == nil {
		logs.Error("支付记录不存在，ID: %d", paymentID)
		return errors.New("支付记录不存在")
	}

	// 更新状态
	payment.Status = status
	if status == models.PaymentStatusSuccess {
		now := time.Now()
		payment.PaymentTime = &now
	}

	// 更新外部交易号
	if externalTradeNo != "" {
		payment.ExternalTradeNo = externalTradeNo
	}

	// 保存更新
	_, err = o.Update(payment, "Status", "PaymentTime", "ExternalTradeNo", "UpdatedAt")
	if err != nil {
		logs.Error("更新支付状态失败：%v", err)
		return err
	}

	return nil
}

// UpdatePaymentStatusTx 在事务中更新支付状态
func (r *PaymentRepositoryImpl) UpdatePaymentStatusTx(o orm.TxOrmer, paymentID int64, status models.PaymentStatus, externalTradeNo string) error {
	// 先获取支付记录
	payment := &models.Payment{ID: paymentID}
	err := o.Read(payment)
	if err != nil {
		if err == orm.ErrNoRows {
			return errors.New("支付记录不存在")
		}
		return err
	}

	// 更新状态
	payment.Status = status
	if status == models.PaymentStatusSuccess {
		now := time.Now()
		payment.PaymentTime = &now
	}

	// 更新外部交易号
	if externalTradeNo != "" {
		payment.ExternalTradeNo = externalTradeNo
	}

	// 保存更新
	_, err = o.Update(payment, "Status", "PaymentTime", "ExternalTradeNo", "UpdatedAt")
	if err != nil {
		return err
	}

	return nil
}
