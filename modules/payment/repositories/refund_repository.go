/**
 * refund_repository.go
 * 退款记录仓储层实现
 * 负责退款记录的数据库操作
 */

package repositories

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/models"
)

// RefundRepository 退款仓储接口
type RefundRepository interface {
	// 创建退款记录
	CreateRefund(refund *models.Refund) (int64, error)
	// 通过ID获取退款记录
	GetRefundByID(id int64) (*models.Refund, error)
	// 通过退款流水号获取退款记录
	GetRefundByRefundNo(refundNo string) (*models.Refund, error)
	// 获取支付记录的所有退款
	GetRefundsByPaymentID(paymentID int64) ([]*models.Refund, error)
	// 获取订单的所有退款
	GetRefundsByOrderID(orderID int64) ([]*models.Refund, error)
	// 更新退款记录
	UpdateRefund(refund *models.Refund) error
	// 获取用户的退款记录列表
	GetUserRefunds(userID int64, status models.RefundStatus, page, pageSize int) ([]*models.Refund, int64, error)
	// 获取待审批的退款记录
	GetPendingApprovalRefunds(page, pageSize int) ([]*models.Refund, int64, error)
	// 获取指定状态的退款记录
	GetRefundsByStatus(status models.RefundStatus, page, pageSize int) ([]*models.Refund, int64, error)
}

// RefundRepositoryImpl 退款仓储实现
type RefundRepositoryImpl struct{}

// NewRefundRepository 创建退款仓储
func NewRefundRepository() RefundRepository {
	return &RefundRepositoryImpl{}
}

// CreateRefund 创建退款记录
func (r *RefundRepositoryImpl) CreateRefund(refund *models.Refund) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(refund)
	if err != nil {
		logs.Error("创建退款记录失败：%v", err)
		return 0, err
	}
	return id, nil
}

// GetRefundByID 通过ID获取退款记录
func (r *RefundRepositoryImpl) GetRefundByID(id int64) (*models.Refund, error) {
	o := orm.NewOrm()
	refund := &models.Refund{ID: id}
	err := o.Read(refund)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("查询退款记录失败：%v", err)
		return nil, err
	}
	return refund, nil
}

// GetRefundByRefundNo 通过退款流水号获取退款记录
func (r *RefundRepositoryImpl) GetRefundByRefundNo(refundNo string) (*models.Refund, error) {
	o := orm.NewOrm()
	refund := &models.Refund{}
	err := o.QueryTable(new(models.Refund)).Filter("refund_no", refundNo).One(refund)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("通过退款流水号查询退款记录失败：%v", err)
		return nil, err
	}
	return refund, nil
}

// GetRefundsByPaymentID 获取支付记录的所有退款
func (r *RefundRepositoryImpl) GetRefundsByPaymentID(paymentID int64) ([]*models.Refund, error) {
	o := orm.NewOrm()
	var refunds []*models.Refund

	_, err := o.QueryTable(new(models.Refund)).Filter("payment_id", paymentID).OrderBy("-created_at").All(&refunds)
	if err != nil {
		logs.Error("获取支付记录的退款列表失败：%v", err)
		return nil, err
	}

	return refunds, nil
}

// GetRefundsByOrderID 获取订单的所有退款
func (r *RefundRepositoryImpl) GetRefundsByOrderID(orderID int64) ([]*models.Refund, error) {
	o := orm.NewOrm()
	var refunds []*models.Refund

	_, err := o.QueryTable(new(models.Refund)).Filter("order_id", orderID).OrderBy("-created_at").All(&refunds)
	if err != nil {
		logs.Error("获取订单的退款列表失败：%v", err)
		return nil, err
	}

	return refunds, nil
}

// UpdateRefund 更新退款记录
func (r *RefundRepositoryImpl) UpdateRefund(refund *models.Refund) error {
	o := orm.NewOrm()
	_, err := o.Update(refund)
	if err != nil {
		logs.Error("更新退款记录失败：%v", err)
		return err
	}
	return nil
}

// GetUserRefunds 获取用户的退款记录列表
func (r *RefundRepositoryImpl) GetUserRefunds(userID int64, status models.RefundStatus, page, pageSize int) ([]*models.Refund, int64, error) {
	o := orm.NewOrm()
	var refunds []*models.Refund

	query := o.QueryTable(new(models.Refund)).Filter("user_id", userID)

	// 如果状态不为零值，则添加状态过滤
	if status != 0 {
		query = query.Filter("status", status)
	}

	// 计算总数
	count, err := query.Count()
	if err != nil {
		logs.Error("统计用户退款记录数量失败：%v", err)
		return nil, 0, err
	}

	// 没有记录，直接返回空列表
	if count == 0 {
		return []*models.Refund{}, 0, nil
	}

	// 分页查询
	offset := (page - 1) * pageSize
	_, err = query.OrderBy("-created_at").Limit(pageSize, offset).All(&refunds)
	if err != nil {
		logs.Error("查询用户退款记录列表失败：%v", err)
		return nil, 0, err
	}

	return refunds, count, nil
}

// GetPendingApprovalRefunds 获取待审批的退款记录
func (r *RefundRepositoryImpl) GetPendingApprovalRefunds(page, pageSize int) ([]*models.Refund, int64, error) {
	o := orm.NewOrm()
	var refunds []*models.Refund

	query := o.QueryTable(new(models.Refund)).Filter("approval_status", 1) // 待审批状态

	// 计算总数
	count, err := query.Count()
	if err != nil {
		logs.Error("统计待审批退款记录数量失败：%v", err)
		return nil, 0, err
	}

	// 没有记录，直接返回空列表
	if count == 0 {
		return []*models.Refund{}, 0, nil
	}

	// 分页查询
	offset := (page - 1) * pageSize
	_, err = query.OrderBy("created_at").Limit(pageSize, offset).All(&refunds)
	if err != nil {
		logs.Error("查询待审批退款记录列表失败：%v", err)
		return nil, 0, err
	}

	return refunds, count, nil
}

// GetRefundsByStatus 获取指定状态的退款记录
func (r *RefundRepositoryImpl) GetRefundsByStatus(status models.RefundStatus, page, pageSize int) ([]*models.Refund, int64, error) {
	o := orm.NewOrm()
	var refunds []*models.Refund

	_, err := o.QueryTable(new(models.Refund)).
		Filter("status", status).
		OrderBy("created_at").
		Limit(pageSize, (page-1)*pageSize).
		All(&refunds)

	if err != nil {
		logs.Error("获取指定状态退款记录失败：%v", err)
		return nil, 0, err
	}

	// 计算总数
	count, err := o.QueryTable(new(models.Refund)).Filter("status", status).Count()
	if err != nil {
		logs.Error("统计指定状态退款记录数量失败：%v", err)
		return nil, 0, err
	}

	return refunds, count, nil
}
