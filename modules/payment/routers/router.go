/**
 * payment模块路由
 *
 * 本文件负责注册payment模块的所有路由，将请求映射到对应的控制器处理函数。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/payment/controllers"
)

// InitRouters 初始化支付模块路由
func InitRouters() {
	logs.Info("初始化支付模块路由...")

	// 支付相关路由
	web.Router("/api/v1/payments/create", &controllers.PaymentController{}, "post:CreatePayment")
	web.Router("/api/v1/payments/query/:transaction_no", &controllers.PaymentController{}, "get:QueryPayment")
	web.Router("/api/v1/payments/close/:transaction_no", &controllers.PaymentController{}, "post:ClosePayment")

	// 支付回调路由
	web.Router("/api/payment/callback/:method/:order_no", &controllers.PaymentController{}, "post:PaymentCallback")

	// 退款相关路由
	web.Router("/api/v1/refunds/create", &controllers.PaymentController{}, "post:CreateRefund")
	web.Router("/api/v1/refunds/query/:refund_no", &controllers.PaymentController{}, "get:QueryRefund")

	// 退款回调路由
	web.Router("/api/refund/callback/:method/:order_no", &controllers.PaymentController{}, "post:RefundCallback")

	logs.Info("支付模块路由初始化完成")
}