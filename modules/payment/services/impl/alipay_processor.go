/**
 * alipay_processor.go
 * 支付宝支付处理器
 * 实现支付宝相关的支付、查询、退款等功能
 */

package impl

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
)

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID           string // 应用ID
	PrivateKey      string // 应用私钥
	AlipayPublicKey string // 支付宝公钥
	SignType        string // 签名类型
	Gateway         string // 网关地址
	NotifyURL       string // 回调通知URL
	ReturnURL       string // 前端跳转URL
	Charset         string // 字符集
	Format          string // 格式
	Version         string // 版本
}

// AlipayProcessor 支付宝处理器
type AlipayProcessor struct {
	accountRepo repositories.PaymentAccountRepository
}

// NewAlipayProcessor 创建支付宝处理器
func NewAlipayProcessor(accountRepo repositories.PaymentAccountRepository) *AlipayProcessor {
	return &AlipayProcessor{
		accountRepo: accountRepo,
	}
}

// GeneratePaymentParams 生成支付参数
func (p *AlipayProcessor) GeneratePaymentParams(payment *models.Payment, returnURL string) (*PaymentParams, error) {
	// 获取支付宝账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodAlipay)
	if err != nil {
		return nil, err
	}
	if account == nil {
		return nil, errors.New("未配置支付宝账户")
	}

	// 解析配置
	config := &AlipayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return nil, fmt.Errorf("解析支付宝配置失败: %v", err)
	}

	// 确定支付产品类型
	productCode := "FAST_INSTANT_TRADE_PAY" // 默认为电脑网站支付
	method := "alipay.trade.page.pay"

	// 根据设备信息选择不同的产品类型
	if strings.Contains(strings.ToLower(payment.DeviceInfo), "android") ||
		strings.Contains(strings.ToLower(payment.DeviceInfo), "ios") {
		productCode = "QUICK_MSECURITY_PAY" // APP支付
		method = "alipay.trade.app.pay"
	} else if strings.Contains(strings.ToLower(payment.DeviceInfo), "mobile") ||
		strings.Contains(strings.ToLower(payment.DeviceInfo), "wap") {
		productCode = "QUICK_WAP_WAY" // 手机网站支付
		method = "alipay.trade.wap.pay"
	}

	// 构建业务参数
	bizContent := map[string]interface{}{
		"out_trade_no":    payment.TransactionNo,
		"total_amount":    fmt.Sprintf("%.2f", payment.Amount),
		"subject":         fmt.Sprintf("订单支付-%s", payment.TransactionNo),
		"product_code":    productCode,
		"timeout_express": "30m", // 订单超时时间
	}

	// 转换为JSON
	bizContentJSON, err := json.Marshal(bizContent)
	if err != nil {
		return nil, fmt.Errorf("序列化业务参数失败: %v", err)
	}

	// 构建公共参数
	params := make(map[string]string)
	params["app_id"] = config.AppID
	params["method"] = method
	params["charset"] = config.Charset
	params["sign_type"] = config.SignType
	params["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	params["version"] = config.Version
	params["notify_url"] = config.NotifyURL
	params["biz_content"] = string(bizContentJSON)

	// 如果是网页支付，添加return_url
	if method == "alipay.trade.page.pay" || method == "alipay.trade.wap.pay" {
		if returnURL != "" {
			params["return_url"] = returnURL
		} else {
			params["return_url"] = config.ReturnURL
		}
	}

	// 计算签名
	sign, err := p.sign(params, config.PrivateKey, config.SignType)
	if err != nil {
		return nil, fmt.Errorf("计算签名失败: %v", err)
	}
	params["sign"] = sign

	// 构建请求字符串
	var buf strings.Builder
	buf.WriteString(config.Gateway)
	if !strings.Contains(config.Gateway, "?") {
		buf.WriteString("?")
	} else {
		buf.WriteString("&")
	}

	// 按照参数名ASCII码从小到大排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 拼接请求参数
	for i, k := range keys {
		if i > 0 {
			buf.WriteString("&")
		}
		buf.WriteString(k)
		buf.WriteString("=")
		buf.WriteString(url.QueryEscape(params[k]))
	}

	payURL := buf.String()

	// 根据支付类型返回对应的参数
	result := &PaymentParams{
		AppPayParams: make(map[string]string),
		WebPayParams: make(map[string]string),
	}

	switch method {
	case "alipay.trade.page.pay", "alipay.trade.wap.pay":
		// 电脑网站支付或手机网站支付
		result.PaymentURL = payURL
	case "alipay.trade.app.pay":
		// APP支付，返回给APP的请求字符串
		for k, v := range params {
			result.AppPayParams[k] = v
		}
	}

	return result, nil
}

// QueryPaymentStatus 查询支付状态
func (p *AlipayProcessor) QueryPaymentStatus(payment *models.Payment) (models.PaymentStatus, string, error) {
	// 获取支付宝账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodAlipay)
	if err != nil {
		return payment.Status, "", err
	}
	if account == nil {
		return payment.Status, "", errors.New("未配置支付宝账户")
	}

	// 解析配置
	config := &AlipayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return payment.Status, "", fmt.Errorf("解析支付宝配置失败: %v", err)
	}

	// 构建业务参数
	bizContent := map[string]string{
		"out_trade_no": payment.TransactionNo,
	}

	// 转换为JSON
	bizContentJSON, err := json.Marshal(bizContent)
	if err != nil {
		return payment.Status, "", fmt.Errorf("序列化业务参数失败: %v", err)
	}

	// 构建公共参数
	params := make(map[string]string)
	params["app_id"] = config.AppID
	params["method"] = "alipay.trade.query"
	params["charset"] = config.Charset
	params["sign_type"] = config.SignType
	params["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	params["version"] = config.Version
	params["biz_content"] = string(bizContentJSON)

	// 计算签名
	sign, err := p.sign(params, config.PrivateKey, config.SignType)
	if err != nil {
		return payment.Status, "", fmt.Errorf("计算签名失败: %v", err)
	}
	params["sign"] = sign

	// 发起HTTP请求查询订单状态
	// 实际项目中应该发起HTTP请求
	// 这里模拟返回结果
	respMap := p.mockQueryResponse(payment.TransactionNo)

	// 验证响应签名
	// ...

	// 解析交易状态
	// 支付宝交易状态：WAIT_BUYER_PAY（交易创建，等待买家付款）、TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、
	//                TRADE_SUCCESS（交易支付成功）、TRADE_FINISHED（交易结束，不可退款）
	tradeStatus := respMap["trade_status"]
	tradeNo := respMap["trade_no"] // 支付宝交易号

	switch tradeStatus {
	case "TRADE_SUCCESS", "TRADE_FINISHED":
		return models.PaymentStatusSuccess, tradeNo, nil
	case "WAIT_BUYER_PAY":
		return models.PaymentStatusPending, "", nil
	case "TRADE_CLOSED":
		return models.PaymentStatusCancelled, "", nil
	default:
		return payment.Status, "", nil
	}
}

// ClosePayment 关闭支付
func (p *AlipayProcessor) ClosePayment(payment *models.Payment) (bool, error) {
	// 获取支付宝账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodAlipay)
	if err != nil {
		return false, err
	}
	if account == nil {
		return false, errors.New("未配置支付宝账户")
	}

	// 解析配置
	config := &AlipayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return false, fmt.Errorf("解析支付宝配置失败: %v", err)
	}

	// 构建业务参数
	bizContent := map[string]string{
		"out_trade_no": payment.TransactionNo,
	}

	// 转换为JSON
	bizContentJSON, err := json.Marshal(bizContent)
	if err != nil {
		return false, fmt.Errorf("序列化业务参数失败: %v", err)
	}

	// 构建公共参数
	params := make(map[string]string)
	params["app_id"] = config.AppID
	params["method"] = "alipay.trade.close"
	params["charset"] = config.Charset
	params["sign_type"] = config.SignType
	params["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	params["version"] = config.Version
	params["biz_content"] = string(bizContentJSON)

	// 计算签名
	sign, err := p.sign(params, config.PrivateKey, config.SignType)
	if err != nil {
		return false, fmt.Errorf("计算签名失败: %v", err)
	}
	params["sign"] = sign

	// 发起HTTP请求关闭订单
	// 实际项目中应该发起HTTP请求
	// 这里模拟返回结果
	respMap := p.mockCloseResponse(payment.TransactionNo)

	// 验证响应签名
	// ...

	// 解析结果
	code := respMap["code"]
	subCode := respMap["sub_code"]

	// 返回结果
	return code == "10000" && subCode == "", nil
}

// VerifyCallback 验证回调
func (p *AlipayProcessor) VerifyCallback(rawData string, payment *models.Payment) (bool, float64, error) {
	// 获取支付宝账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodAlipay)
	if err != nil {
		return false, 0, err
	}
	if account == nil {
		return false, 0, errors.New("未配置支付宝账户")
	}

	// 解析配置
	config := &AlipayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return false, 0, fmt.Errorf("解析支付宝配置失败: %v", err)
	}

	// 解析回调参数
	callbackParams, err := url.ParseQuery(rawData)
	if err != nil {
		return false, 0, fmt.Errorf("解析回调参数失败: %v", err)
	}

	// 转换为Map
	params := make(map[string]string)
	for k, v := range callbackParams {
		if len(v) > 0 {
			params[k] = v[0]
		}
	}

	// 获取签名和签名类型，用于验证签名
	// 实际项目中应该使用这些值进行签名验证
	_, _ = params["sign"], params["sign_type"]

	// 验证签名
	// 注意：这里的签名验证在实际项目中应该实现
	// 为简化示例，这里省略了签名验证步骤

	// 验证订单号
	outTradeNo := params["out_trade_no"]
	if outTradeNo != payment.TransactionNo {
		return false, 0, errors.New("订单号不匹配")
	}

	// 验证交易状态
	tradeStatus := params["trade_status"]
	if tradeStatus != "TRADE_SUCCESS" && tradeStatus != "TRADE_FINISHED" {
		return false, 0, fmt.Errorf("交易状态不正确: %s", tradeStatus)
	}

	// 获取支付金额
	totalAmount := params["total_amount"]
	amount, err := strconv.ParseFloat(totalAmount, 64)
	if err != nil {
		return false, 0, fmt.Errorf("解析支付金额失败: %v", err)
	}

	// 获取支付宝交易号
	tradeNo := params["trade_no"]
	if tradeNo == "" {
		return false, 0, errors.New("支付宝交易号为空")
	}

	return true, amount, nil
}

// RequestRefund 申请退款
func (p *AlipayProcessor) RequestRefund(refund *models.Refund, payment *models.Payment) (string, error) {
	// 获取支付宝账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodAlipay)
	if err != nil {
		return "", err
	}
	if account == nil {
		return "", errors.New("未配置支付宝账户")
	}

	// 解析配置
	config := &AlipayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return "", fmt.Errorf("解析支付宝配置失败: %v", err)
	}

	// 构建业务参数
	bizContent := map[string]interface{}{
		"out_trade_no":   payment.TransactionNo,
		"refund_amount":  fmt.Sprintf("%.2f", refund.Amount),
		"out_request_no": refund.RefundNo,
		"refund_reason":  refund.Reason,
	}

	// 转换为JSON
	bizContentJSON, err := json.Marshal(bizContent)
	if err != nil {
		return "", fmt.Errorf("序列化业务参数失败: %v", err)
	}

	// 构建公共参数
	params := make(map[string]string)
	params["app_id"] = config.AppID
	params["method"] = "alipay.trade.refund"
	params["charset"] = config.Charset
	params["sign_type"] = config.SignType
	params["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	params["version"] = config.Version
	params["biz_content"] = string(bizContentJSON)

	// 计算签名
	sign, err := p.sign(params, config.PrivateKey, config.SignType)
	if err != nil {
		return "", fmt.Errorf("计算签名失败: %v", err)
	}
	params["sign"] = sign

	// 发起HTTP请求申请退款
	// 实际项目中应该发起HTTP请求
	// 这里模拟返回结果
	respMap := p.mockRefundResponse(payment.TransactionNo, refund.RefundNo)

	// 验证响应签名
	// ...

	// 解析结果
	code := respMap["code"]
	subCode := respMap["sub_code"]

	if code != "10000" || subCode != "" {
		return "", fmt.Errorf("退款申请失败: %s - %s", subCode, respMap["sub_msg"])
	}

	// 返回支付宝退款单号
	return respMap["trade_no"], nil
}

// QueryRefundStatus 查询退款状态
func (p *AlipayProcessor) QueryRefundStatus(refund *models.Refund) (models.RefundStatus, error) {
	// 获取支付宝账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodAlipay)
	if err != nil {
		return models.RefundStatus(int(refund.Status)), err
	}
	if account == nil {
		return models.RefundStatus(int(refund.Status)), errors.New("未配置支付宝账户")
	}

	// 解析配置
	config := &AlipayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("解析支付宝配置失败: %v", err)
	}

	// 获取关联的支付记录
	// 在实际项目中，应该通过refund.PaymentID获取支付记录
	// 这里简化处理，假设已经获取到了支付记录
	paymentTransactionNo := fmt.Sprintf("P%s", refund.RefundNo) // 模拟生成交易号

	// 构建业务参数
	bizContent := map[string]string{
		"out_trade_no":   paymentTransactionNo,
		"out_request_no": refund.RefundNo,
	}

	// 转换为JSON
	bizContentJSON, err := json.Marshal(bizContent)
	if err != nil {
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("序列化业务参数失败: %v", err)
	}

	// 构建公共参数
	params := make(map[string]string)
	params["app_id"] = config.AppID
	params["method"] = "alipay.trade.fastpay.refund.query"
	params["charset"] = config.Charset
	params["sign_type"] = config.SignType
	params["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	params["version"] = config.Version
	params["biz_content"] = string(bizContentJSON)

	// 计算签名
	sign, err := p.sign(params, config.PrivateKey, config.SignType)
	if err != nil {
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("计算签名失败: %v", err)
	}
	params["sign"] = sign

	// 发起HTTP请求查询退款状态
	// 实际项目中应该发起HTTP请求
	// 这里模拟返回结果
	respMap := p.mockRefundQueryResponse(paymentTransactionNo, refund.RefundNo)

	// 验证响应签名
	// 注意：这里的签名验证在实际项目中应该实现
	// 为简化示例，这里省略了签名验证步骤

	// 解析结果
	code := respMap["code"]
	subCode := respMap["sub_code"]
	refundAmount := respMap["refund_amount"]

	if code != "10000" || subCode != "" {
		// 查询失败
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("退款查询失败: %s - %s", subCode, respMap["sub_msg"])
	}

	// 返回状态
	if refundAmount != "" {
		// 退款成功
		return models.RefundStatusSuccess, nil
	} else {
		// 退款处理中或失败
		return models.RefundStatusProcessing, nil
	}
}

// 以下为工具方法

// sign 签名
func (p *AlipayProcessor) sign(params map[string]string, privateKey, signType string) (string, error) {
	// 在实际项目中，这里应该使用RSA或RSA2进行签名
	// 这里只是简化示例
	_ = signType // 使用变量避免未使用警告
	return "signed_data", nil
}

// mockQueryResponse 模拟查询结果
func (p *AlipayProcessor) mockQueryResponse(outTradeNo string) map[string]string {
	// 模拟响应结果
	return map[string]string{
		"code":             "10000",
		"msg":              "Success",
		"trade_no":         "2020010122001429090512345678",
		"out_trade_no":     outTradeNo,
		"buyer_logon_id":   "159****5620",
		"trade_status":     "TRADE_SUCCESS",
		"total_amount":     "88.88",
		"receipt_amount":   "88.88",
		"buyer_pay_amount": "88.88",
		"point_amount":     "0.00",
		"invoice_amount":   "88.88",
		"send_pay_date":    "2020-01-01 12:00:00",
		"store_id":         "NJ_001",
		"terminal_id":      "NJ_T_001",
		"store_name":       "证大五道口店",
		"buyer_user_id":    "2088102177846880",
		"sign":             "ERITJKEIJKJHKKKKKKKHJEREEEEEEEEEEE",
	}
}

// mockCloseResponse 模拟关闭订单结果
func (p *AlipayProcessor) mockCloseResponse(outTradeNo string) map[string]string {
	// 模拟响应结果
	return map[string]string{
		"code":         "10000",
		"msg":          "Success",
		"trade_no":     "2020010122001429090512345678",
		"out_trade_no": outTradeNo,
		"sign":         "ERITJKEIJKJHKKKKKKKHJEREEEEEEEEEEE",
	}
}

// mockRefundResponse 模拟退款结果
func (p *AlipayProcessor) mockRefundResponse(outTradeNo, outRequestNo string) map[string]string {
	// 模拟响应结果
	return map[string]string{
		"code":           "10000",
		"msg":            "Success",
		"trade_no":       "2020010122001429090512345678",
		"out_trade_no":   outTradeNo,
		"buyer_logon_id": "159****5620",
		"fund_change":    "Y",
		"refund_fee":     "88.88",
		"refund_time":    "2020-01-01 12:00:00",
		"store_name":     "证大五道口店",
		"buyer_user_id":  "2088102177846880",
		"sign":           "ERITJKEIJKJHKKKKKKKHJEREEEEEEEEEEE",
	}
}

// mockRefundQueryResponse 模拟退款查询结果
func (p *AlipayProcessor) mockRefundQueryResponse(outTradeNo, outRequestNo string) map[string]string {
	// 模拟响应结果
	return map[string]string{
		"code":           "10000",
		"msg":            "Success",
		"trade_no":       "2020010122001429090512345678",
		"out_trade_no":   outTradeNo,
		"out_request_no": outRequestNo,
		"refund_reason":  "正常退款",
		"total_amount":   "88.88",
		"refund_amount":  "88.88",
		"sign":           "ERITJKEIJKJHKKKKKKKHJEREEEEEEEEEEE",
	}
}
