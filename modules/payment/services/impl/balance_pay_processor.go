/**
 * balance_pay_processor.go
 * 余额支付处理器
 * 实现用户余额支付相关功能
 */

package impl

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/payment/models"
	userRepo "o_mall_backend/modules/user/repositories"
	userModels "o_mall_backend/modules/user/models"
)

// BalancePayProcessor 余额支付处理器
type BalancePayProcessor struct {
	userAccountRepo userRepo.UserAccountRepository
}

// NewBalancePayProcessor 创建余额支付处理器
func NewBalancePayProcessor() *BalancePayProcessor {
	return &BalancePayProcessor{
		userAccountRepo: userRepo.NewUserAccountRepository(),
	}
}

// GeneratePaymentParams 生成支付参数
func (p *BalancePayProcessor) GeneratePaymentParams(payment *models.Payment, returnURL string) (*PaymentParams, error) {
	// 余额支付不需要跳转到第三方平台，直接返回支付参数
	return &PaymentParams{
		PaymentURL:   returnURL,               // 支付完成后跳转地址
		QrCodeURL:    "",                      // 余额支付不需要二维码
		AppPayParams: make(map[string]string), // 余额支付不需要APP参数
		WebPayParams: map[string]string{ // 余额支付需要的参数
			"payment_id":     fmt.Sprintf("%d", payment.ID),
			"transaction_no": payment.TransactionNo,
			"amount":         fmt.Sprintf("%.2f", payment.Amount),
		},
	}, nil
}

// QueryPaymentStatus 查询支付状态
func (p *BalancePayProcessor) QueryPaymentStatus(payment *models.Payment) (models.PaymentStatus, string, error) {
	// 余额支付的状态由系统内部维护，直接返回当前状态
	return payment.Status, payment.ExternalTradeNo, nil
}

// ClosePayment 关闭支付
func (p *BalancePayProcessor) ClosePayment(payment *models.Payment) (bool, error) {
	// 余额支付由系统内部处理，可以直接关闭
	return true, nil
}

// VerifyCallback 验证回调
func (p *BalancePayProcessor) VerifyCallback(rawData string, payment *models.Payment) (bool, float64, error) {
	// 解析回调数据
	// 余额支付回调数据格式: user_id=123&amount=88.88&password_hash=xxx

	// 模拟解析数据
	params := make(map[string]string)
	for _, pair := range strings.Split(rawData, "&") {
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) == 2 {
			params[parts[0]] = parts[1]
		}
	}

	// 验证用户ID
	userIDStr, exists := params["user_id"]
	if !exists {
		return false, 0, errors.New("缺少用户ID参数")
	}

	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		return false, 0, fmt.Errorf("用户ID格式错误: %v", err)
	}

	if userID != payment.UserID {
		return false, 0, errors.New("用户ID不匹配")
	}

	// 验证金额
	amountStr, exists := params["amount"]
	if !exists {
		return false, 0, errors.New("缺少金额参数")
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		return false, 0, fmt.Errorf("金额格式错误: %v", err)
	}

	// 验证密码哈希（实际项目中应该更安全地验证）
	_, exists = params["password_hash"]
	if !exists {
		return false, 0, errors.New("缺少密码验证参数")
	}

	// TODO: 在实际项目中，应该验证密码哈希的正确性

	// 这里应当扣减用户余额
	success, err := p.deductUserBalance(userID, amount)
	if err != nil {
		return false, 0, fmt.Errorf("扣减用户余额失败: %v", err)
	}

	if !success {
		return false, 0, errors.New("用户余额不足")
	}

	return true, amount, nil
}

// RequestRefund 申请退款
func (p *BalancePayProcessor) RequestRefund(refund *models.Refund, payment *models.Payment) (string, error) {
	// 余额支付退款，实际上是向用户余额中增加相应金额
	success, err := p.addUserBalance(payment.UserID, refund.Amount)
	if err != nil {
		return "", fmt.Errorf("增加用户余额失败: %v", err)
	}

	if !success {
		return "", errors.New("退款失败")
	}

	// 生成内部退款编号
	refundID := fmt.Sprintf("BAL%s%d", time.Now().Format("**************"), refund.ID)

	return refundID, nil
}

// QueryRefundStatus 查询退款状态
func (p *BalancePayProcessor) QueryRefundStatus(refund *models.Refund) (models.RefundStatus, error) {
	// 余额支付的退款状态由系统内部维护，直接返回当前状态
	// 需要将PaymentStatus类型转换为RefundStatus类型
	return models.RefundStatus(int(refund.Status)), nil
}

// 以下为内部辅助方法

// deductUserBalance 扣减用户余额
func (p *BalancePayProcessor) deductUserBalance(userID int64, amount float64) (bool, error) {
	logs.Info("[余额支付处理器] 开始扣减用户余额 - 用户ID: %d, 金额: %.2f", userID, amount)
	
	// 先查询用户账户，检查余额是否足够
	userAccount, err := p.userAccountRepo.GetUserAccountByUserID(userID)
	if err != nil {
		logs.Error("[余额支付处理器] 获取用户账户失败: %v, 用户ID: %d", err, userID)
		return false, err
	}
	
	if userAccount == nil {
		logs.Error("[余额支付处理器] 用户账户不存在 - 用户ID: %d", userID)
		return false, errors.New("用户账户不存在")
	}
	
	if userAccount.Balance < amount {
		logs.Error("[余额支付处理器] 用户余额不足 - 用户ID: %d, 当前余额: %.2f, 需要金额: %.2f", 
			userID, userAccount.Balance, amount)
		return false, nil
	}
	
	// 开启事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[余额支付处理器] 开启事务失败: %v", err)
		return false, err
	}
	
	// 使用用户账户仓储服务扣减用户余额
	account, err := p.userAccountRepo.UpdateUserBalanceTx(tx, userID, amount, true)
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 扣减用户余额失败: %v", err)
		return false, err
	}
	
	// 记录交易
	transaction := &userModels.UserAccountTransaction{
		UserID:        userID,
		AccountID:     account.ID,
		TransactionNo: fmt.Sprintf("PAY%s%d", time.Now().Format("**************"), userID),
		Amount:        amount,
		BeforeBalance: account.Balance + amount, // 交易前余额
		AfterBalance:  account.Balance,         // 交易后余额
		Type:          userModels.TransactionTypePayment,
		Operation:     userModels.OperationTypeDecrease,
		Status:        1, // 成功
		Description:   "余额支付",
	}
	
	_, err = p.userAccountRepo.CreateTransactionTx(tx, transaction)
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 创建交易记录失败: %v", err)
		return false, err
	}
	
	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 提交事务失败: %v", err)
		return false, err
	}
	
	logs.Info("[余额支付处理器] 用户余额扣减成功 - 用户ID: %d, 金额: %.2f, 当前余额: %.2f", 
		userID, amount, account.Balance)
	return true, nil
}

// addUserBalance 增加用户余额
func (p *BalancePayProcessor) addUserBalance(userID int64, amount float64) (bool, error) {
	logs.Info("[余额支付处理器] 开始增加用户余额 - 用户ID: %d, 金额: %.2f", userID, amount)
	
	// 开启事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[余额支付处理器] 开启事务失败: %v", err)
		return false, err
	}
	
	// 使用用户账户仓储服务增加用户余额
	account, err := p.userAccountRepo.UpdateUserBalanceTx(tx, userID, amount, false)
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 增加用户余额失败: %v", err)
		return false, err
	}
	
	// 记录交易
	transaction := &userModels.UserAccountTransaction{
		UserID:        userID,
		AccountID:     account.ID,
		TransactionNo: fmt.Sprintf("RF%s%d", time.Now().Format("**************"), userID),
		Amount:        amount,
		BeforeBalance: account.Balance - amount, // 交易前余额
		AfterBalance:  account.Balance,         // 交易后余额
		Type:          userModels.TransactionTypeRefund,
		Operation:     userModels.OperationTypeIncrease,
		Status:        1, // 成功
		Description:   "订单退款",
	}
	
	_, err = p.userAccountRepo.CreateTransactionTx(tx, transaction)
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 创建交易记录失败: %v", err)
		return false, err
	}
	
	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 提交事务失败: %v", err)
		return false, err
	}
	
	logs.Info("[余额支付处理器] 用户余额增加成功 - 用户ID: %d, 金额: %.2f, 当前余额: %.2f", 
		userID, amount, account.Balance)
	return true, nil
}
