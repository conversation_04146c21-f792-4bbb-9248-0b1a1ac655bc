/**
 * balance_payment_processor.go
 * 余额支付处理器实现
 * 处理用户余额支付相关的业务逻辑
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	orderRepo "o_mall_backend/modules/order/repositories"
	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
	userModels "o_mall_backend/modules/user/models"
	userRepo "o_mall_backend/modules/user/repositories"
)

// BalancePaymentProcessor 余额支付处理器
type BalancePaymentProcessor struct {
	paymentRepo        repositories.PaymentRepository
	paymentAccountRepo repositories.PaymentAccountRepository
	userAccountRepo    userRepo.UserAccountRepository
}

// NewBalancePaymentProcessor 创建余额支付处理器
func NewBalancePaymentProcessor() *BalancePaymentProcessor {
	return &BalancePaymentProcessor{
		paymentRepo:        repositories.NewPaymentRepository(),
		paymentAccountRepo: repositories.NewPaymentAccountRepository(),
		userAccountRepo:    userRepo.NewUserAccountRepository(),
	}
}

// GeneratePaymentParams 生成支付参数
func (p *BalancePaymentProcessor) GeneratePaymentParams(payment *models.Payment, returnURL string) (*PaymentParams, error) {
	// 1. 检查用户余额是否充足
	userAccount, err := p.userAccountRepo.GetUserAccountByUserID(payment.UserID)
	if err != nil {
		logs.Error("获取用户账户失败: %v, 用户ID: %d", err, payment.UserID)
		return nil, fmt.Errorf("获取账户信息失败: %v", err)
	}

	if userAccount == nil {
		logs.Error("用户账户不存在: 用户ID: %d", payment.UserID)
		return nil, errors.New("用户账户不存在")
	}

	if userAccount.Balance < payment.Amount {
		logs.Warn("用户余额不足: 用户ID: %d, 余额: %.2f, 支付金额: %.2f",
			payment.UserID, userAccount.Balance, payment.Amount)
		return nil, errors.New("余额不足")
	}

	// 2. 对于余额支付，直接返回支付参数（不需要跳转到第三方）
	params := &PaymentParams{
		PaymentURL: "", // 余额支付不需要跳转
		QrCodeURL:  "", // 余额支付不需要二维码
		AppPayParams: map[string]string{
			"payment_id":     fmt.Sprintf("%d", payment.ID),
			"transaction_no": payment.TransactionNo,
			"amount":         fmt.Sprintf("%.2f", payment.Amount),
			"method":         "balance",
		},
		WebPayParams: map[string]string{
			"payment_id":     fmt.Sprintf("%d", payment.ID),
			"transaction_no": payment.TransactionNo,
			"amount":         fmt.Sprintf("%.2f", payment.Amount),
			"method":         "balance",
			"return_url":     returnURL,
		},
	}

	return params, nil
}

// QueryPaymentStatus 查询支付状态
func (p *BalancePaymentProcessor) QueryPaymentStatus(payment *models.Payment) (models.PaymentStatus, string, error) {
	// 查询数据库中的支付记录
	dbPayment, err := p.paymentRepo.GetPaymentByID(payment.ID)
	if err != nil {
		logs.Error("查询支付记录失败: %v, 支付ID: %d", err, payment.ID)
		return models.PaymentStatusFailed, "", err
	}

	if dbPayment == nil {
		logs.Error("支付记录不存在: 支付ID: %d", payment.ID)
		return models.PaymentStatusFailed, "", errors.New("支付记录不存在")
	}

	return dbPayment.Status, dbPayment.ExternalTradeNo, nil
}

// ClosePayment 关闭支付
func (p *BalancePaymentProcessor) ClosePayment(payment *models.Payment) (bool, error) {
	// 余额支付关闭操作，不需要调用外部API
	// 只需更新支付状态为已取消
	err := p.paymentRepo.UpdatePaymentStatus(payment.ID, models.PaymentStatusCancelled, "")
	if err != nil {
		logs.Error("关闭余额支付失败: %v, 支付ID: %d", err, payment.ID)
		return false, err
	}
	return true, nil
}

// VerifyCallback 验证回调
func (p *BalancePaymentProcessor) VerifyCallback(rawData string, payment *models.Payment) (bool, float64, error) {
	// 余额支付没有第三方回调，这个方法不会被调用
	// 但需要实现接口方法
	return false, 0, errors.New("余额支付不支持回调验证")
}

// ProcessBalancePayment 处理余额支付（直接扣减用户余额）
// 这是余额支付特有的方法，不属于接口方法
func (p *BalancePaymentProcessor) ProcessBalancePayment(payment *models.Payment) (bool, error) {
	logs.Info("[余额支付处理器] 开始处理余额支付 - 支付ID: %d, 用户ID: %d, 订单ID: %d, 金额: %.2f",
		payment.ID, payment.UserID, payment.OrderID, payment.Amount)

	// 1. 再次检查用户余额是否充足
	logs.Info("[余额支付处理器] 步骤1: 检查用户余额 - 用户ID: %d", payment.UserID)
	userAccount, err := p.userAccountRepo.GetUserAccountByUserID(payment.UserID)
	if err != nil {
		logs.Error("[余额支付处理器] 获取用户账户失败: %v, 用户ID: %d", err, payment.UserID)
		return false, err
	}

	if userAccount == nil {
		logs.Error("[余额支付处理器] 用户账户不存在 - 用户ID: %d", payment.UserID)
		return false, errors.New("用户账户不存在")
	}

	logs.Info("[余额支付处理器] 用户账户信息 - 用户ID: %d, 当前余额: %.2f, 需要金额: %.2f",
		payment.UserID, userAccount.Balance, payment.Amount)

	if userAccount.Balance < payment.Amount {
		logs.Error("[余额支付处理器] 用户余额不足 - 用户ID: %d, 当前余额: %.2f, 需要金额: %.2f",
			payment.UserID, userAccount.Balance, payment.Amount)
		return false, errors.New("余额不足")
	}

	logs.Info("[余额支付处理器] 用户余额充足，可以进行支付 - 用户ID: %d", payment.UserID)

	// 2. 开启事务处理
	logs.Info("[余额支付处理器] 步骤2: 开启数据库事务 - 支付ID: %d", payment.ID)
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[余额支付处理器] 开始事务失败: %v, 支付ID: %d", err, payment.ID)
		return false, err
	}
	logs.Info("[余额支付处理器] 数据库事务开启成功 - 支付ID: %d", payment.ID)

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logs.Error("[余额支付处理器] 余额支付处理过程中出现异常: %v, 支付ID: %d", r, payment.ID)
		}
	}()

	// 3. 扣减用户余额
	logs.Info("[余额支付处理器] 步骤3: 扣减用户余额 - 用户ID: %d, 扣减金额: %.2f", payment.UserID, payment.Amount)
	updatedAccount, err := p.userAccountRepo.UpdateUserBalanceTx(tx, payment.UserID, payment.Amount, true) // true表示减少余额
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 更新用户余额失败: %v, 用户ID: %d, 支付ID: %d", err, payment.UserID, payment.ID)
		return false, err
	}

	newBalance := updatedAccount.Balance
	logs.Info("[余额支付处理器] 用户余额扣减成功 - 用户ID: %d, 扣减前: %.2f, 扣减后: %.2f",
		payment.UserID, userAccount.Balance, newBalance)

	// 4. 创建账户交易记录
	logs.Info("[余额支付处理器] 步骤4: 创建账户交易记录 - 用户ID: %d, 支付ID: %d", payment.UserID, payment.ID)

	// 为账户交易记录生成唯一的交易号
	transNo := fmt.Sprintf("BALTRANS%s%d", time.Now().Format("**************"), payment.UserID)
	rand.Seed(time.Now().UnixNano())
	transNo = fmt.Sprintf("%s%04d", transNo, rand.Intn(10000))
	logs.Info("[余额支付处理器] 生成账户交易号: %s", transNo)

	accountTrans := &userModels.UserAccountTransaction{
		UserID:        payment.UserID,
		Type:          userModels.TransactionTypePayment,
		Amount:        payment.Amount,
		BeforeBalance: userAccount.Balance,
		AfterBalance:  newBalance,
		RelatedID:     payment.ID,
		RelatedType:   "payment",
		Remark:        fmt.Sprintf("订单支付: %d", payment.OrderID),
		Operation:     userModels.OperationTypeDecrease,
		TransactionNo: transNo, // 设置交易号
	}
	logs.Info("[余额支付处理器] 账户交易记录对象创建完成 - 用户ID: %d, 交易类型: %s, 金额: %.2f, 操作: %s, 交易号: %s",
		accountTrans.UserID, accountTrans.Type, accountTrans.Amount, accountTrans.Operation, accountTrans.TransactionNo)

	transactionID, err := p.userAccountRepo.CreateTransactionTx(tx, accountTrans)
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 创建账户交易记录失败: %v, 用户ID: %d, 支付ID: %d", err, payment.UserID, payment.ID)
		return false, err
	}
	logs.Info("[余额支付处理器] 账户交易记录创建成功 - 交易记录ID: %d, 用户ID: %d", transactionID, payment.UserID)

	// 5. 更新支付记录状态
	logs.Info("[余额支付处理器] 步骤5: 更新支付记录状态 - 支付ID: %d", payment.ID)
	paymentTime := time.Now()
	payment.Status = models.PaymentStatusSuccess
	payment.PaymentTime = &paymentTime
	payment.ExternalTradeNo = payment.TransactionNo // 余额支付使用内部交易号作为外部交易号
	logs.Info("[余额支付处理器] 支付记录状态更新准备 - 支付ID: %d, 新状态: %s, 支付时间: %v, 外部交易号: %s",
		payment.ID, payment.Status.String(), paymentTime, payment.ExternalTradeNo)

	err = p.paymentRepo.UpdatePaymentStatusTx(tx, payment.ID, models.PaymentStatusSuccess, payment.ExternalTradeNo)
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 更新支付记录状态失败: %v, 支付ID: %d", err, payment.ID)
		return false, err
	}
	logs.Info("[余额支付处理器] 支付记录状态更新成功 - 支付ID: %d, 状态: %s", payment.ID, models.PaymentStatusSuccess.String())

	// 6. 提交事务
	logs.Info("[余额支付处理器] 步骤6: 提交数据库事务 - 支付ID: %d", payment.ID)
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[余额支付处理器] 提交事务失败: %v, 支付ID: %d", err, payment.ID)
		return false, err
	}
	logs.Info("[余额支付处理器] 数据库事务提交成功 - 支付ID: %d", payment.ID)

	// 7. 更新订单支付状态（新增步骤）
	logs.Info("[余额支付处理器] 步骤7: 更新订单支付状态 - 订单ID: %d", payment.OrderID)
	
	// 直接创建订单仓库实例
	orderRepository := orderRepo.NewOrderRepository()
	
	// 调用订单仓库更新订单支付状态
	// 支付状态设置为已支付(1)，并更新支付时间为当前时间
	err = orderRepository.UpdatePayStatus(context.Background(), payment.OrderID, 1, time.Now())
	
	if err != nil {
		logs.Error("[余额支付处理器] 更新订单支付状态失败: %v, 订单ID: %d", err, payment.OrderID)
		// 不影响主流程返回，因为支付本身已经成功
		logs.Warn("[余额支付处理器] 支付已成功处理，但订单状态更新失败，需要手动处理 - 订单ID: %d", payment.OrderID)
	} else {
		logs.Info("[余额支付处理器] 订单支付状态更新成功 - 订单ID: %d", payment.OrderID)
	}

	logs.Info("[余额支付处理器] 余额支付处理完成 - 支付ID: %d, 用户ID: %d, 订单ID: %d, 金额: %.2f, 最终状态: %s",
		payment.ID, payment.UserID, payment.OrderID, payment.Amount, payment.Status.String())
	return true, nil
}

// RequestRefund 申请退款
func (p *BalancePaymentProcessor) RequestRefund(refund *models.Refund, payment *models.Payment) (string, error) {
	// 余额支付的退款处理就是直接退回用户余额
	// 开启事务处理
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return "", err
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logs.Error("余额退款处理过程中出现异常: %v", r)
		}
	}()

	// 获取用户当前余额
	userAccount, err := p.userAccountRepo.GetUserAccountByUserID(payment.UserID)
	if err != nil {
		tx.Rollback()
		logs.Error("获取用户账户失败: %v, 用户ID: %d", err, payment.UserID)
		return "", err
	}

	if userAccount == nil {
		tx.Rollback()
		return "", errors.New("用户账户不存在")
	}

	// 增加用户余额
	updatedAccount, err := p.userAccountRepo.UpdateUserBalanceTx(tx, payment.UserID, refund.Amount, false) // false表示增加余额
	if err != nil {
		tx.Rollback()
		logs.Error("更新用户余额失败: %v, 用户ID: %d", err, payment.UserID)
		return "", err
	}

	newBalance := updatedAccount.Balance

	// 创建账户交易记录
	accountTrans := &userModels.UserAccountTransaction{
		UserID:        payment.UserID,
		Type:          userModels.TransactionTypeRefund,
		Amount:        refund.Amount,
		BeforeBalance: userAccount.Balance,
		AfterBalance:  newBalance,
		RelatedID:     refund.ID,
		RelatedType:   "refund",
		Remark:        fmt.Sprintf("订单退款: %d", payment.OrderID),
		Operation:     userModels.OperationTypeIncrease,
	}

	_, err = p.userAccountRepo.CreateTransactionTx(tx, accountTrans)
	if err != nil {
		tx.Rollback()
		logs.Error("创建账户交易记录失败: %v, 用户ID: %d", err, payment.UserID)
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v", err)
		return "", err
	}

	logs.Info("余额退款处理成功: 退款ID: %d, 支付ID: %d, 用户ID: %d, 金额: %.2f",
		refund.ID, payment.ID, payment.UserID, refund.Amount)

	// 返回退款处理号（这里直接使用退款的流水号）
	return refund.RefundNo, nil
}

// QueryRefundStatus 查询退款状态
func (p *BalancePaymentProcessor) QueryRefundStatus(refund *models.Refund) (models.RefundStatus, error) {
	// 余额支付退款是实时完成的，总是返回成功
	return models.RefundStatusSuccess, nil
}
