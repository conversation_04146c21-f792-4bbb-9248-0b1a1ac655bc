/**
 * payment_processor.go
 * 支付处理器接口
 * 定义不同支付方式的处理接口
 */

package impl

import (
	"o_mall_backend/modules/payment/models"
)

// PaymentParams 支付参数
type PaymentParams struct {
	PaymentURL   string            // 支付跳转URL
	QrCodeURL    string            // 二维码URL
	AppPayParams map[string]string // APP支付参数
	WebPayParams map[string]string // 网页支付参数
}

// PaymentProcessor 支付处理器接口
type PaymentProcessor interface {
	// 生成支付参数
	GeneratePaymentParams(payment *models.Payment, returnURL string) (*PaymentParams, error)

	// 查询支付状态
	QueryPaymentStatus(payment *models.Payment) (models.PaymentStatus, string, error)

	// 关闭支付
	ClosePayment(payment *models.Payment) (bool, error)

	// 验证回调
	VerifyCallback(rawData string, payment *models.Payment) (bool, float64, error)

	// 申请退款
	RequestRefund(refund *models.Refund, payment *models.Payment) (string, error)

	// 查询退款状态
	QueryRefundStatus(refund *models.Refund) (models.RefundStatus, error)
}
