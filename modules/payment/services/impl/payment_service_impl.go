/**
 * payment_service_impl.go
 * 支付服务实现
 * 实现支付相关的核心业务逻辑
 */

package impl

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/payment/constants"
	"o_mall_backend/modules/payment/dto"
	"o_mall_backend/modules/payment/interfaces"
	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
)

// PaymentServiceImpl 支付服务实现
type PaymentServiceImpl struct {
	paymentRepo        repositories.PaymentRepository
	paymentAccountRepo repositories.PaymentAccountRepository
	orderPaymentCallback interfaces.OrderPaymentCallback // 订单支付回调处理器
}

// NewPaymentService 创建支付服务
func NewPaymentService() interfaces.PaymentService {
	return &PaymentServiceImpl{
		paymentRepo:        repositories.NewPaymentRepository(),
		paymentAccountRepo: repositories.NewPaymentAccountRepository(),
		orderPaymentCallback: nil, // 初始化为空，需要外部设置
	}
}

// SetOrderPaymentCallback 设置订单支付回调处理器
func (s *PaymentServiceImpl) SetOrderPaymentCallback(callback interfaces.OrderPaymentCallback) {
	s.orderPaymentCallback = callback
}

// getLatestPaymentByOrderID 获取订单的最新支付记录（优先返回成功记录，其次是最新记录）
func (s *PaymentServiceImpl) getLatestPaymentByOrderID(orderID int64) (*models.Payment, error) {
	return s.paymentRepo.GetPaymentByOrderID(orderID)
}

// CreatePayment 创建支付
func (s *PaymentServiceImpl) CreatePayment(req *dto.PaymentCreateRequest) (*dto.PaymentCreateResponse, error) {
	logs.Info("[支付创建流程] 开始创建支付 - 订单ID: %d, 用户ID: %d, 金额: %.2f, 支付方式: %d", req.OrderID, req.UserID, req.Amount, req.Method)

	// 1. 检查是否已存在该订单的支付记录（未支付或已支付）
	logs.Info("[支付创建流程] 步骤1: 检查订单是否已存在支付记录 - 订单ID: %d", req.OrderID)
	// 获取所有支付记录以便处理失败记录的重用
	existPayment, err := s.getLatestPaymentByOrderID(req.OrderID)
	if err != nil {
		logs.Error("[支付创建流程] 查询订单支付记录失败: %v, 订单ID: %d", err, req.OrderID)
		return nil, err
	}

	// 如果存在未过期的待支付记录
	if existPayment != nil && existPayment.Status == models.PaymentStatusPending {
		logs.Info("[支付创建流程] 发现待支付记录 - 支付ID: %d, 状态: %s", existPayment.ID, existPayment.Status.String())
		now := time.Now()
		
		// 判断记录是否过期
		if existPayment.ExpireTime != nil && now.Before(*existPayment.ExpireTime) {
			// 如果是余额支付方式，并且未过期的待支付记录也是余额支付方式，则直接处理而不是返回
			if req.Method == models.PaymentMethodBalance && existPayment.Method == models.PaymentMethodBalance {
				logs.Info("[支付创建流程] 发现未过期的余额支付记录，将直接处理支付 - 支付ID: %d", existPayment.ID)
				
				// 获取余额支付处理器
				logs.Info("[支付创建流程] 创建余额支付处理器")
				balanceProcessor := NewBalancePaymentProcessor()
				
				// 直接处理余额支付
				logs.Info("[支付创建流程] 调用余额支付处理器处理支付 - 支付ID: %d", existPayment.ID)
				success, err := balanceProcessor.ProcessBalancePayment(existPayment)
				if err != nil {
					logs.Error("[支付创建流程] 余额支付处理失败: %v, 支付ID: %d", err, existPayment.ID)
					// 更新支付记录状态为失败
					logs.Info("[支付创建流程] 更新支付记录状态为失败 - 支付ID: %d", existPayment.ID)
					s.paymentRepo.UpdatePaymentStatus(existPayment.ID, models.PaymentStatusFailed, "")
					// 记录日志
					s.createPaymentLog(existPayment.ID, 0, constants.PaymentActionPay,
						"余额支付处理失败", "", false, err.Error())
					return nil, err
				}
				
				if !success {
					logs.Error("[支付创建流程] 余额支付处理未成功, 支付ID: %d", existPayment.ID)
					return nil, errors.New("余额支付处理未成功")
				}
				
				logs.Info("[支付创建流程] 余额支付处理成功 - 支付ID: %d", existPayment.ID)
				// 记录余额支付成功日志
				s.createPaymentLog(existPayment.ID, 0, constants.PaymentActionPay,
					"余额支付成功", "", true, "")
					
				// 构造余额支付成功的响应
				logs.Info("[支付创建流程] 构造余额支付成功响应 - 支付ID: %d, 交易号: %s", existPayment.ID, existPayment.TransactionNo)
				resp := &dto.PaymentCreateResponse{
					PaymentID:     existPayment.ID,
					TransactionNo: existPayment.TransactionNo,
					PaymentURL:    "", // 余额支付不需要跳转
					QrCodeURL:     "", // 余额支付不需要二维码
					AppPayParams:  "{\"status\":\"success\",\"message\":\"余额支付成功\"}",
					WebPayParams:  "{\"status\":\"success\",\"message\":\"余额支付成功\"}",
					ExpireTime:    0, // 已完成支付，不需要过期时间
				}
				
				return resp, nil
			} else {
				// 非余额支付方式，仍然按照原来的逻辑处理
				logs.Info("[支付创建流程] 待支付记录未过期，直接返回 - 支付ID: %d, 过期时间: %v", existPayment.ID, existPayment.ExpireTime)
				return &dto.PaymentCreateResponse{
					PaymentID:     existPayment.ID,
					TransactionNo: existPayment.TransactionNo,
					ExpireTime:    existPayment.ExpireTime.Unix(),
				}, nil
			}
		} else {
			logs.Info("[支付创建流程] 待支付记录已过期，继续创建新支付 - 支付ID: %d, 过期时间: %v", existPayment.ID, existPayment.ExpireTime)
		}
	}

	// 如果存在已支付的记录，返回错误
	if existPayment != nil && (existPayment.Status == models.PaymentStatusSuccess ||
		existPayment.Status == models.PaymentStatusProcessing) {
		logs.Error("[支付创建流程] 订单已存在支付记录 - 支付ID: %d, 状态: %s", existPayment.ID, existPayment.Status.String())
		return nil, errors.New("该订单已存在支付记录")
	}

	// 2. 准备支付记录（重用失败记录或创建新记录）
	var payment *models.Payment
	var isReuse bool = false
	
	// 如果存在失败或取消的支付记录，重用该记录
	if existPayment != nil && (existPayment.Status == models.PaymentStatusFailed || existPayment.Status == models.PaymentStatusCancelled) {
		logs.Info("[支付创建流程] 发现失败/取消的支付记录，将重用该记录 - 支付ID: %d, 原状态: %s", existPayment.ID, existPayment.Status.String())
		payment = existPayment
		isReuse = true
		
		// 更新支付记录信息
		payment.Amount = req.Amount
		payment.Method = req.Method
		payment.Status = models.PaymentStatusPending
		payment.ClientIP = req.ClientIP
		payment.DeviceInfo = req.DeviceInfo
		payment.Remark = req.Remark
		payment.PaymentTime = nil // 清空之前的支付时间
		payment.ExternalTradeNo = "" // 清空外部交易号
		payment.CallbackData = "" // 清空回调数据
		
		// 生成新的交易流水号
		logs.Info("[支付创建流程] 为重用记录生成新交易流水号 - 订单ID: %d, 用户ID: %d", req.OrderID, req.UserID)
		payment.TransactionNo = s.generateTransactionNo(req.OrderID, req.UserID)
		
		// 计算新的过期时间
		logs.Info("[支付创建流程] 为重用记录计算新过期时间 - 过期分钟数: %d", constants.PaymentExpireMinutes)
		expireTime := time.Now().Add(time.Minute * constants.PaymentExpireMinutes)
		payment.ExpireTime = &expireTime
		
		logs.Info("[支付创建流程] 重用支付记录更新完成 - 支付ID: %d, 新交易号: %s, 新状态: %s", payment.ID, payment.TransactionNo, payment.Status.String())
	} else {
		// 创建新的支付记录
		if existPayment == nil {
			logs.Info("[支付创建流程] 订单无现有支付记录，创建新支付 - 订单ID: %d", req.OrderID)
		} else {
			logs.Info("[支付创建流程] 现有支付记录状态: %s，创建新支付 - 支付ID: %d", existPayment.Status.String(), existPayment.ID)
		}
		
		// 生成交易流水号
		logs.Info("[支付创建流程] 生成交易流水号 - 订单ID: %d, 用户ID: %d", req.OrderID, req.UserID)
		transactionNo := s.generateTransactionNo(req.OrderID, req.UserID)
		logs.Info("[支付创建流程] 生成交易流水号成功: %s", transactionNo)

		// 计算过期时间
		logs.Info("[支付创建流程] 计算过期时间 - 过期分钟数: %d", constants.PaymentExpireMinutes)
		expireTime := time.Now().Add(time.Minute * constants.PaymentExpireMinutes)
		logs.Info("[支付创建流程] 计算过期时间成功: %v", expireTime)

		// 创建支付记录对象
		payment = &models.Payment{
			OrderID:       req.OrderID,
			UserID:        req.UserID,
			Amount:        req.Amount,
			Method:        req.Method,
			Status:        models.PaymentStatusPending,
			TransactionNo: transactionNo,
			ExpireTime:    &expireTime,
			ClientIP:      req.ClientIP,
			DeviceInfo:    req.DeviceInfo,
			Remark:        req.Remark,
		}
		logs.Info("[支付创建流程] 支付记录对象创建完成 - 交易号: %s, 金额: %.2f, 支付方式: %d, 状态: %s",
			payment.TransactionNo, payment.Amount, payment.Method, payment.Status.String())
	}
	
	// 3. 保存或更新支付记录
	if isReuse {
		logs.Info("[支付创建流程] 更新重用的支付记录 - 支付ID: %d", payment.ID)
		err = s.paymentRepo.UpdatePayment(payment)
		if err != nil {
			logs.Error("[支付创建流程] 更新支付记录失败: %v", err)
			return nil, err
		}
		logs.Info("[支付创建流程] 支付记录更新成功 - 支付ID: %d", payment.ID)
	} else {
		logs.Info("[支付创建流程] 创建新支付记录")
		paymentID, err := s.paymentRepo.CreatePayment(payment)
		if err != nil {
			logs.Error("[支付创建流程] 创建支付记录失败: %v", err)
			return nil, err
		}
		payment.ID = paymentID
		logs.Info("[支付创建流程] 支付记录创建成功 - 支付ID: %d", paymentID)
	}

	// 4. 记录日志
	if isReuse {
		s.createPaymentLog(payment.ID, 0, constants.PaymentActionCreate,
			fmt.Sprintf("重用支付记录，订单: %d, 金额: %.2f, 原状态: %s", req.OrderID, req.Amount, existPayment.Status.String()), "", true, "")
	} else {
		s.createPaymentLog(payment.ID, 0, constants.PaymentActionCreate,
			fmt.Sprintf("创建支付订单: %d, 金额: %.2f", req.OrderID, req.Amount), "", true, "")
	}

	// 5. 判断是否为余额支付，如果是则直接处理
	logs.Info("[支付创建流程] 步骤5: 检查支付方式 - 支付方式: %d (%s)", payment.Method, payment.Method.String())
	if payment.Method == models.PaymentMethodBalance {
		logs.Info("[支付创建流程] 检测到余额支付，开始处理余额支付流程 - 支付ID: %d, 用户ID: %d, 金额: %.2f", payment.ID, req.UserID, req.Amount)

		// 获取余额支付处理器
		logs.Info("[支付创建流程] 创建余额支付处理器")
		balanceProcessor := NewBalancePaymentProcessor()

		// 直接处理余额支付
		logs.Info("[支付创建流程] 调用余额支付处理器处理支付 - 支付ID: %d", payment.ID)
		success, err := balanceProcessor.ProcessBalancePayment(payment)
		if err != nil {
			logs.Error("[支付创建流程] 余额支付处理失败: %v, 支付ID: %d", err, payment.ID)
			// 更新支付记录状态为失败
			logs.Info("[支付创建流程] 更新支付记录状态为失败 - 支付ID: %d", payment.ID)
			s.paymentRepo.UpdatePaymentStatus(payment.ID, models.PaymentStatusFailed, "")
			// 记录日志
			s.createPaymentLog(payment.ID, 0, constants.PaymentActionPay,
				"余额支付处理失败", "", false, err.Error())
			return nil, err
		}

		if !success {
			logs.Error("[支付创建流程] 余额支付处理未成功, 支付ID: %d", payment.ID)
			return nil, errors.New("余额支付处理未成功")
		}

		logs.Info("[支付创建流程] 余额支付处理成功 - 支付ID: %d", payment.ID)
		// 记录余额支付成功日志
		s.createPaymentLog(payment.ID, 0, constants.PaymentActionPay,
			"余额支付成功", "", true, "")

		// 触发订单支付回调处理器（新增）
		if s.orderPaymentCallback != nil {
			logs.Info("[支付创建流程] 触发订单支付回调处理器 - 支付ID: %d, 订单ID: %d", payment.ID, payment.OrderID)
			err := s.orderPaymentCallback.HandlePaymentCallback(context.Background(), "", payment.TransactionNo,
				payment.ExternalTradeNo, payment.Amount, int(payment.Method), "")
			if err != nil {
				logs.Error("[支付创建流程] 订单支付回调处理失败: %v, 支付ID: %d, 订单ID: %d", err, payment.ID, payment.OrderID)
				// 不影响支付结果返回，但记录错误日志
				s.createPaymentLog(payment.ID, 0, constants.PaymentActionOrderUpdate,
					fmt.Sprintf("订单状态更新失败: %v", err), "", false, err.Error())
			} else {
				logs.Info("[支付创建流程] 订单支付回调处理成功 - 支付ID: %d, 订单ID: %d", payment.ID, payment.OrderID)
				s.createPaymentLog(payment.ID, 0, constants.PaymentActionOrderUpdate,
					"订单状态更新成功", "", true, "")
			}
		} else {
			logs.Warn("[支付创建流程] 订单支付回调处理器未设置，无法更新订单状态 - 支付ID: %d, 订单ID: %d", payment.ID, payment.OrderID)
		}

		// 构造余额支付成功的响应
		logs.Info("[支付创建流程] 构造余额支付成功响应 - 支付ID: %d, 交易号: %s", payment.ID, payment.TransactionNo)
		resp := &dto.PaymentCreateResponse{
			PaymentID:     payment.ID,
			TransactionNo: payment.TransactionNo,
			PaymentURL:    "", // 余额支付不需要跳转
			QrCodeURL:     "", // 余额支付不需要二维码
			AppPayParams:  "{\"status\":\"success\",\"message\":\"余额支付成功\"}",
			WebPayParams:  "{\"status\":\"success\",\"message\":\"余额支付成功\"}",
			ExpireTime:    0, // 已完成支付，不需要过期时间
		}

		return resp, nil
	}

	// 6. 生成第三方支付参数
	paymentMethod := s.getPaymentProcessorByMethod(payment.Method)
	if paymentMethod == nil {
		logs.Error("不支持的支付方式: %v", payment.Method)
		return nil, errors.New("不支持的支付方式")
	}

	payParams, err := paymentMethod.GeneratePaymentParams(payment, req.ReturnURL)
	if err != nil {
		logs.Error("生成支付参数失败: %v", err)
		// 更新支付状态为失败
		payment.Status = models.PaymentStatusFailed
		s.paymentRepo.UpdatePayment(payment)
		return nil, err
	}

	// 7. 构建响应
	appParamsStr, _ := json.Marshal(payParams.AppPayParams) // 将map转为JSON字符串
	webParamsStr, _ := json.Marshal(payParams.WebPayParams) // 将map转为JSON字符串

	response := &dto.PaymentCreateResponse{
		PaymentID:     payment.ID,
		TransactionNo: payment.TransactionNo,
		PaymentURL:    payParams.PaymentURL,
		QrCodeURL:     payParams.QrCodeURL,
		AppPayParams:  string(appParamsStr), // 使用JSON字符串
		WebPayParams:  string(webParamsStr), // 使用JSON字符串
		ExpireTime:    payment.ExpireTime.Unix(),
	}

	logs.Info("[支付创建流程] 余额支付流程完成，返回响应 - 支付ID: %d", payment.ID)
	return response, nil
}

// QueryPayment 查询支付状态
func (s *PaymentServiceImpl) QueryPayment(req *dto.PaymentQueryRequest) (*dto.PaymentQueryResponse, error) {
	var payment *models.Payment
	var err error

	// 根据提供的参数查询支付记录
	if req.PaymentID > 0 {
		payment, err = s.paymentRepo.GetPaymentByID(req.PaymentID)
	} else if req.TransactionNo != "" {
		payment, err = s.paymentRepo.GetPaymentByTransactionNo(req.TransactionNo)
	} else if req.OrderID > 0 {
		payment, err = s.paymentRepo.GetPaymentByOrderID(req.OrderID)
	} else {
		return nil, errors.New("必须提供支付ID、交易号或订单ID")
	}

	if err != nil {
		logs.Error("查询支付记录失败: %v", err)
		return nil, err
	}

	if payment == nil {
		return nil, errors.New("支付记录不存在")
	}

	// 如果支付状态是待支付或处理中，主动查询一次支付平台
	if payment.Status == models.PaymentStatusPending || payment.Status == models.PaymentStatusProcessing {
		paymentMethod := s.getPaymentProcessorByMethod(payment.Method)
		if paymentMethod != nil {
			// 查询支付平台
			status, externalTradeNo, err := paymentMethod.QueryPaymentStatus(payment)
			if err == nil {
				// 更新支付状态
				if status != payment.Status {
					oldStatus := payment.Status
					payment.Status = status
					if status == models.PaymentStatusSuccess {
						now := time.Now()
						payment.PaymentTime = &now
						payment.ExternalTradeNo = externalTradeNo
					}

					// 更新支付记录
					err = s.paymentRepo.UpdatePayment(payment)
					if err != nil {
						logs.Error("更新支付状态失败: %v", err)
					} else {
						// 记录日志
						s.createPaymentLog(payment.ID, 0, constants.PaymentActionQuery,
							fmt.Sprintf("支付状态变更: 从 %s 变为 %s", oldStatus.String(), status.String()),
							"", true, "")
					}
				}
			} else {
				logs.Error("查询支付平台失败: %v", err)
			}
		}
	}

	// 构建响应
	response := &dto.PaymentQueryResponse{
		PaymentID:     payment.ID,
		TransactionNo: payment.TransactionNo,
		OrderID:       payment.OrderID,
		UserID:        payment.UserID,
		Amount:        payment.Amount,
		Method:        payment.Method,
		Status:        payment.Status,
		PaymentTime:   payment.PaymentTime,
		ExpireTime:    payment.ExpireTime,
	}

	return response, nil
}

// HandlePaymentCallback 处理支付回调
func (s *PaymentServiceImpl) HandlePaymentCallback(req *dto.PaymentCallbackRequest) (bool, error) {
	// 1. 查询支付记录
	payment, err := s.paymentRepo.GetPaymentByTransactionNo(req.TransactionNo)
	if err != nil {
		logs.Error("查询支付记录失败: %v", err)
		return false, err
	}

	if payment == nil {
		logs.Error("支付记录不存在: %s", req.TransactionNo)
		return false, errors.New("支付记录不存在")
	}

	// 2. 验证支付方式是否一致
	if payment.Method != req.Method {
		logs.Error("支付方式不匹配: %v != %v", payment.Method, req.Method)
		return false, errors.New("支付方式不匹配")
	}

	// 3. 获取支付处理器
	paymentMethod := s.getPaymentProcessorByMethod(payment.Method)
	if paymentMethod == nil {
		logs.Error("不支持的支付方式: %v", payment.Method)
		return false, errors.New("不支持的支付方式")
	}

	// 4. 验证回调数据
	success, amount, err := paymentMethod.VerifyCallback(req.RawData, payment)
	if err != nil {
		logs.Error("验证回调数据失败: %v", err)
		return false, err
	}

	// 5. 记录回调原始数据
	payment.CallbackData = req.RawData

	// 6. 判断支付状态
	if success {
		// 验证金额是否一致（允许小数点后两位的误差）
		if !equalFloat(amount, payment.Amount, 0.01) {
			logs.Error("支付金额不匹配: %.2f != %.2f", amount, payment.Amount)
			s.createPaymentLog(payment.ID, 0, constants.PaymentActionNotify,
				fmt.Sprintf("回调金额不匹配: %.2f != %.2f", amount, payment.Amount),
				req.RawData, false, "金额不匹配")
			return false, errors.New("支付金额不匹配")
		}

		// 设置支付成功
		oldStatus := payment.Status
		payment.Status = models.PaymentStatusSuccess
		now := time.Now()
		payment.PaymentTime = &now
		payment.ExternalTradeNo = req.ExternalNo

		// 更新支付记录
		err = s.paymentRepo.UpdatePayment(payment)
		if err != nil {
			logs.Error("更新支付状态失败: %v", err)
			return false, err
		}

		// 记录日志
		s.createPaymentLog(payment.ID, 0, constants.PaymentActionNotify,
			fmt.Sprintf("支付回调处理成功, 状态从 %s 变为 %s", oldStatus.String(), payment.Status.String()),
			req.RawData, true, "")

		// 触发订单状态更新等业务逻辑
		if s.orderPaymentCallback != nil {
			err := s.orderPaymentCallback.HandlePaymentCallback(context.Background(), "", payment.TransactionNo, 
				payment.ExternalTradeNo, payment.Amount, int(payment.Method), req.RawData)
			if err != nil {
				logs.Error("触发订单状态更新失败: %v, 支付ID: %d, 订单ID: %d", err, payment.ID, payment.OrderID)
				// 不影响支付结果返回，但记录错误日志
				s.createPaymentLog(payment.ID, 0, constants.PaymentActionOrderUpdate,
					fmt.Sprintf("订单状态更新失败: %v", err), "", false, err.Error())
			} else {
				logs.Info("订单状态更新成功 - 支付ID: %d, 订单ID: %d", payment.ID, payment.OrderID)
				s.createPaymentLog(payment.ID, 0, constants.PaymentActionOrderUpdate,
					"订单状态更新成功", "", true, "")
			}
		} else {
			logs.Warn("订单支付回调处理器未设置，无法更新订单状态 - 支付ID: %d, 订单ID: %d", payment.ID, payment.OrderID)
		}

		return true, nil
	} else {
		// 支付失败
		s.createPaymentLog(payment.ID, 0, constants.PaymentActionNotify,
			"支付回调处理失败", req.RawData, false, "回调验证失败")

		return false, errors.New("支付失败")
	}
}

// CancelPayment 取消支付
func (s *PaymentServiceImpl) CancelPayment(req *dto.PaymentCancelRequest) (bool, error) {
	// 1. 查询支付记录
	var payment *models.Payment
	var err error

	if req.PaymentID > 0 {
		payment, err = s.paymentRepo.GetPaymentByID(req.PaymentID)
	} else if req.TransactionNo != "" {
		payment, err = s.paymentRepo.GetPaymentByTransactionNo(req.TransactionNo)
	} else {
		return false, errors.New("必须提供支付ID或交易号")
	}

	if err != nil {
		logs.Error("查询支付记录失败: %v", err)
		return false, err
	}

	if payment == nil {
		return false, errors.New("支付记录不存在")
	}

	// 2. 验证用户权限
	if payment.UserID != req.UserID {
		logs.Error("用户无权操作此支付记录: %d != %d", payment.UserID, req.UserID)
		return false, errors.New("无权操作")
	}

	// 3. 验证支付状态是否可取消
	if payment.Status != models.PaymentStatusPending {
		logs.Error("支付状态不可取消: %s", payment.Status.String())
		return false, errors.New("当前支付状态不可取消")
	}

	// 4. 获取支付处理器
	paymentMethod := s.getPaymentProcessorByMethod(payment.Method)
	if paymentMethod == nil {
		logs.Error("不支持的支付方式: %v", payment.Method)
		return false, errors.New("不支持的支付方式")
	}

	// 5. 调用支付平台关闭接口
	success, err := paymentMethod.ClosePayment(payment)
	if err != nil {
		logs.Error("调用支付平台关闭接口失败: %v", err)
		// 记录尝试取消的日志
		s.createPaymentLog(payment.ID, 0, constants.PaymentActionCancel,
			fmt.Sprintf("尝试取消支付失败: %v", err), "", false, err.Error())
	} else if success {
		// 记录成功关闭的日志
		s.createPaymentLog(payment.ID, 0, constants.PaymentActionCancel,
			"成功关闭支付", "", true, "")
	}

	// 6. 无论支付平台关闭是否成功，都更新本地状态为已取消
	oldStatus := payment.Status
	payment.Status = models.PaymentStatusCancelled
	payment.Remark = payment.Remark + " | 取消原因: " + req.Reason

	// 7. 更新支付记录
	err = s.paymentRepo.UpdatePayment(payment)
	if err != nil {
		logs.Error("更新支付状态失败: %v", err)
		return false, err
	}

	// 8. 记录日志
	s.createPaymentLog(payment.ID, 0, constants.PaymentActionCancel,
		fmt.Sprintf("支付已取消, 状态从 %s 变为 %s, 原因: %s",
			oldStatus.String(), payment.Status.String(), req.Reason),
		"", true, "")

	return true, nil
}

// GetUserPayments 获取用户支付列表
func (s *PaymentServiceImpl) GetUserPayments(req *dto.PaymentListRequest) (*dto.PagedPaymentList, error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 调用仓储层查询数据
	payments, total, err := s.paymentRepo.GetUserPayments(req.UserID, req.Status, req.Page, req.PageSize)
	if err != nil {
		logs.Error("查询用户支付列表失败: %v", err)
		return nil, err
	}

	// 构造返回数据
	result := &dto.PagedPaymentList{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     make([]dto.PaymentBriefInfo, 0, len(payments)),
	}

	// 转换数据
	for _, p := range payments {
		result.List = append(result.List, dto.PaymentBriefInfo{
			ID:            p.ID,
			TransactionNo: p.TransactionNo,
			OrderID:       p.OrderID,
			Amount:        p.Amount,
			Method:        p.Method,
			MethodName:    p.Method.String(),
			Status:        p.Status,
			StatusName:    p.Status.String(),
			CreatedAt:     p.CreatedAt,
			PaymentTime:   p.PaymentTime,
		})
	}

	return result, nil
}

// GetPaymentDetail 获取支付详情
func (s *PaymentServiceImpl) GetPaymentDetail(paymentID int64, userID int64) (*models.Payment, error) {
	// 查询支付记录
	payment, err := s.paymentRepo.GetPaymentByID(paymentID)
	if err != nil {
		logs.Error("查询支付记录失败: %v", err)
		return nil, err
	}

	if payment == nil {
		return nil, errors.New("支付记录不存在")
	}

	// 验证用户权限
	if payment.UserID != userID {
		logs.Error("用户无权查看此支付记录: %d != %d", payment.UserID, userID)
		return nil, errors.New("无权查看")
	}

	return payment, nil
}

// GetAvailablePaymentMethods 获取可用的支付方式
func (s *PaymentServiceImpl) GetAvailablePaymentMethods() ([]dto.PaymentMethodInfo, error) {
	// 查询支付账户配置
	accounts, err := s.paymentAccountRepo.GetAllPaymentAccounts()
	if err != nil {
		logs.Error("获取支付账户配置失败: %v", err)
		return nil, err
	}

	// 统计支持的支付方式
	methodMap := make(map[models.PaymentMethod]bool)
	for _, account := range accounts {
		if account.Status == 1 { // 已启用
			methodMap[account.Method] = true
		}
	}

	// 构造返回值
	methods := make([]dto.PaymentMethodInfo, 0)

	// 获取支付方式配置
	payMethodsStr := web.AppConfig.DefaultString("payment_methods", "")
	if payMethodsStr == "" {
		// 如果配置为空，添加默认支持的支付方式
		for method := range methodMap {
			methods = append(methods, dto.PaymentMethodInfo{
				Method:      method,
				Name:        method.String(),
				Icon:        s.getPaymentMethodIcon(method),
				Description: s.getPaymentMethodDesc(method),
				Extra:       make(map[string]string),
				IsEnabled:   true,
			})
		}
	} else {
		// TODO: 解析配置，支持更灵活的支付方式配置
	}

	return methods, nil
}

// GetPaymentLogs 获取支付日志
func (s *PaymentServiceImpl) GetPaymentLogs(paymentID int64, operatorID int64) ([]*models.PaymentLog, error) {
	// 根据支付ID查询支付记录
	payment, err := s.paymentRepo.GetPaymentByID(paymentID)
	if err != nil {
		logs.Error("查询支付记录失败: %v", err)
		return nil, err
	}

	if payment == nil {
		return nil, errors.New("支付记录不存在")
	}

	// TODO: 验证操作员权限

	// 查询日志
	paymentLogs, err := s.paymentRepo.GetPaymentLogs(paymentID)
	if err != nil {
		logs.Error("查询支付日志失败: %v", err)
		return nil, err
	}

	return paymentLogs, nil
}

// 以下是辅助方法

// generateTransactionNo 生成交易流水号
func (s *PaymentServiceImpl) generateTransactionNo(orderID, userID int64) string {
	now := time.Now()
	// 格式：前缀 + 年月日时分秒 + 随机数 + 用户ID后4位
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	userIDStr := fmt.Sprintf("%04d", userID%10000)
	randomStr := fmt.Sprintf("%04d", r.Intn(10000))

	return fmt.Sprintf("%s%s%s%s",
		constants.PaymentNoPrefix,
		now.Format("20060102150405"),
		randomStr,
		userIDStr)
}

// createPaymentLog 创建支付日志
func (s *PaymentServiceImpl) createPaymentLog(paymentID, refundID int64, action, msg, responseData string, success bool, errorMsg string) {
	log := &models.PaymentLog{
		PaymentID:    paymentID,
		RefundID:     refundID,
		Action:       action,
		RequestData:  msg,
		ResponseData: responseData,
		Success:      success,
		ErrorMsg:     errorMsg,
		IP:           "",
	}

	_, err := s.paymentRepo.CreatePaymentLog(log)
	if err != nil {
		logs.Error("创建支付日志失败: %v", err)
	}
}

// getPaymentProcessorByMethod 根据支付方式获取处理器
func (s *PaymentServiceImpl) getPaymentProcessorByMethod(method models.PaymentMethod) PaymentProcessor {
	switch method {
	case models.PaymentMethodWechat:
		return NewWechatPayProcessor(s.paymentAccountRepo)
	case models.PaymentMethodAlipay:
		return NewAlipayProcessor(s.paymentAccountRepo)
	case models.PaymentMethodBalance:
		return NewBalancePayProcessor()
	default:
		return nil
	}
}

// equalFloat 比较两个浮点数是否相等（允许一定误差）
func equalFloat(a, b, epsilon float64) bool {
	diff := a - b
	if diff < 0 {
		diff = -diff
	}
	return diff < epsilon
}

// getPaymentMethodIcon 获取支付方式图标
func (s *PaymentServiceImpl) getPaymentMethodIcon(method models.PaymentMethod) string {
	// 在实际应用中，这些图标URL应该从配置中读取
	switch method {
	case models.PaymentMethodWechat:
		return "/static/img/payment/wechat.png"
	case models.PaymentMethodAlipay:
		return "/static/img/payment/alipay.png"
	case models.PaymentMethodCreditCard:
		return "/static/img/payment/credit_card.png"
	case models.PaymentMethodBankTransfer:
		return "/static/img/payment/bank.png"
	case models.PaymentMethodBalance:
		return "/static/img/payment/balance.png"
	default:
		return "/static/img/payment/default.png"
	}
}

// getPaymentMethodDesc 获取支付方式描述
func (s *PaymentServiceImpl) getPaymentMethodDesc(method models.PaymentMethod) string {
	switch method {
	case models.PaymentMethodWechat:
		return "微信支付，方便快捷"
	case models.PaymentMethodAlipay:
		return "支付宝支付，安全可靠"
	case models.PaymentMethodCreditCard:
		return "信用卡支付，无需手续费"
	case models.PaymentMethodBankTransfer:
		return "银行转账，支持多家银行"
	case models.PaymentMethodBalance:
		return "余额支付，实时到账"
	default:
		return "其他支付方式"
	}
}
