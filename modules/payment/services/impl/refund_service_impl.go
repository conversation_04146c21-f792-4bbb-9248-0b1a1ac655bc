/**
 * refund_service_impl.go
 * 退款服务实现
 * 实现退款相关的业务逻辑
 */

package impl

import (
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/client/orm"

	"o_mall_backend/modules/payment/constants"
	"o_mall_backend/modules/payment/dto"
	"o_mall_backend/modules/payment/interfaces"
	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
)

// RefundServiceImpl 退款服务实现
type RefundServiceImpl struct {
	refundRepo      repositories.RefundRepository
	paymentRepo     repositories.PaymentRepository
	paymentAcctRepo repositories.PaymentAccountRepository
}

// NewRefundService 创建退款服务
func NewRefundService() interfaces.RefundService {
	return &RefundServiceImpl{
		refundRepo:      repositories.NewRefundRepository(),
		paymentRepo:     repositories.NewPaymentRepository(),
		paymentAcctRepo: repositories.NewPaymentAccountRepository(),
	}
}

// CreateRefund 创建退款
func (s *RefundServiceImpl) CreateRefund(req *dto.RefundCreateRequest) (*dto.RefundCreateResponse, error) {
	// 检查参数
	if req.PaymentID <= 0 || req.OrderID <= 0 || req.UserID <= 0 || req.Amount <= 0 {
		return nil, errors.New("参数无效")
	}

	// 查询支付记录
	payment, err := s.paymentRepo.GetPaymentByID(req.PaymentID)
	if err != nil {
		logs.Error("查询支付记录失败: %v", err)
		return nil, err
	}

	if payment == nil {
		return nil, errors.New("支付记录不存在")
	}

	// 检查支付状态
	logs.Info("退款检查支付状态 - PaymentID: %d, 当前状态: %d (%s), 期望状态: %d (%s)", 
		req.PaymentID, int(payment.Status), payment.Status.String(), 
		int(models.PaymentStatusSuccess), models.PaymentStatusSuccess.String())
	
	if payment.Status != models.PaymentStatusSuccess {
		logs.Error("支付状态检查失败 - PaymentID: %d, 当前状态: %d (%s), 不是成功状态", 
			req.PaymentID, int(payment.Status), payment.Status.String())
		return nil, errors.New("支付状态不正确，无法退款")
	}

	// 检查订单ID是否匹配
	if payment.OrderID != req.OrderID {
		return nil, errors.New("订单ID不匹配")
	}

	// 检查用户ID是否匹配（除非是管理员操作）
	if payment.UserID != req.UserID && req.OperatorID <= 0 {
		return nil, errors.New("无权操作此退款")
	}

	// 检查退款金额是否超过支付金额
	if req.Amount > payment.Amount {
		return nil, errors.New("退款金额不能超过支付金额")
	}

	// 生成退款流水号
	refundNo := s.generateRefundNo(req.PaymentID, req.UserID)

	// 创建退款记录
	refund := &models.Refund{
		PaymentID:      req.PaymentID,
		OrderID:        req.OrderID,
		UserID:         req.UserID,
		Amount:         req.Amount,
		Status:         models.RefundStatusProcessing, // 初始状态为处理中
		RefundNo:       refundNo,
		Reason:         req.Reason,
		OperatorID:     req.OperatorID,
		ApprovalStatus: constants.ApprovalStatusNoNeed, // 默认无需审批
	}

	// 大额退款可能需要审批
	if req.NeedApproval || req.Amount >= 1000 {
		refund.ApprovalStatus = constants.ApprovalStatusPending
		refund.Status = models.RefundStatusProcessing
	}

	// 保存退款记录
	refundID, err := s.refundRepo.CreateRefund(refund)
	if err != nil {
		logs.Error("创建退款记录失败: %v", err)
		return nil, err
	}

	refund.ID = refundID

	// 创建退款日志
	s.createRefundLog(refundID, "create", fmt.Sprintf("创建退款申请: 金额%.2f, 原因: %s", req.Amount, req.Reason), "", true, "")

	// 如果需要审批，则返回等待审批状态
	if refund.ApprovalStatus == constants.ApprovalStatusPending {
		return &dto.RefundCreateResponse{
			RefundID: refundID,
			RefundNo: refundNo,
			Status:   int(models.PaymentStatusProcessing),
			Message:  "退款申请已提交，等待审批",
		}, nil
	}

	// 异步处理退款
	go s.processRefund(refund)

	// 返回结果
	return &dto.RefundCreateResponse{
		RefundID: refundID,
		RefundNo: refundNo,
		Status:   int(models.PaymentStatusProcessing),
		Message:  "退款申请已提交，正在处理",
	}, nil
}

// QueryRefund 查询退款
func (s *RefundServiceImpl) QueryRefund(req *dto.RefundQueryRequest) (*dto.RefundQueryResponse, error) {
	var refund *models.Refund
	var err error

	// 根据退款ID或退款单号查询退款记录
	if req.RefundID > 0 {
		refund, err = s.refundRepo.GetRefundByID(req.RefundID)
	} else if req.RefundNo != "" {
		refund, err = s.refundRepo.GetRefundByRefundNo(req.RefundNo)
	} else {
		return nil, errors.New("必须提供退款ID或退款单号")
	}

	if err != nil {
		logs.Error("查询退款记录失败: %v", err)
		return nil, err
	}

	if refund == nil {
		return nil, errors.New("退款记录不存在")
	}

	// 验证用户权限 - 这里需要从上下文或请求中获取用户ID和操作员ID
	// 由于DTO中没有UserID和OperatorID字段，我们应该从上下文中获取这些信息
	userID := int64(0)     // 从上下文获取当前用户ID
	operatorID := int64(0) // 从上下文获取当前操作员ID

	if userID > 0 && refund.UserID != userID && operatorID == 0 {
		return nil, errors.New("无权查看此退款记录")
	}

	// 如果退款状态是处理中，主动查询一次退款平台
	if refund.Status == models.RefundStatusProcessing {
		// 查询支付记录
		payment, err := s.paymentRepo.GetPaymentByID(refund.PaymentID)
		if err != nil {
			logs.Error("查询支付记录失败: %v", err)
		} else if payment != nil {
			// 获取支付处理器
			paymentMethod := s.getPaymentProcessorByMethod(payment.Method)
			if paymentMethod != nil {
				// 查询退款状态
				status, err := paymentMethod.QueryRefundStatus(refund)
				if err == nil && status != refund.Status {
					// 更新退款状态
					oldStatus := refund.Status
					refund.Status = status

					if status == models.RefundStatusSuccess {
						now := time.Now()
						refund.RefundTime = &now
					}

					// 更新退款记录
					err = s.refundRepo.UpdateRefund(refund)
					if err != nil {
						logs.Error("更新退款状态失败: %v", err)
					} else {
						// 记录日志
						s.createRefundLog(refund.ID, constants.RefundActionQuery,
							fmt.Sprintf("退款状态变更: 从 %s 变为 %s", oldStatus.String(), status.String()),
							"", true, "")
					}
				} else if err != nil {
					logs.Error("查询退款平台失败: %v", err)
				}
			}
		}
	}

	// 构建响应
	response := &dto.RefundQueryResponse{
		RefundID:       refund.ID,
		RefundNo:       refund.RefundNo,
		PaymentID:      refund.PaymentID,
		OrderID:        refund.OrderID,
		UserID:         refund.UserID,
		Amount:         refund.Amount,
		Reason:         refund.Reason,
		Status:         refund.Status,
		RefundTime:     refund.RefundTime,
		ApprovalStatus: refund.ApprovalStatus,
	}

	return response, nil
}

// HandleRefundCallback 处理退款回调
func (s *RefundServiceImpl) HandleRefundCallback(refundNo string, externalNo string, rawData string) (bool, error) {
	// 1. 查询退款记录
	refund, err := s.refundRepo.GetRefundByRefundNo(refundNo)
	if err != nil {
		logs.Error("查询退款记录失败: %v", err)
		return false, err
	}

	if refund == nil {
		logs.Error("退款记录不存在: %s", refundNo)
		return false, errors.New("退款记录不存在")
	}

	// 2. 查询支付记录
	payment, err := s.paymentRepo.GetPaymentByID(refund.PaymentID)
	if err != nil {
		logs.Error("查询支付记录失败: %v", err)
		return false, err
	}

	if payment == nil {
		logs.Error("支付记录不存在: %d", refund.PaymentID)
		return false, errors.New("支付记录不存在")
	}

	// 3. 获取支付处理器
	paymentMethod := s.getPaymentProcessorByMethod(payment.Method)
	if paymentMethod == nil {
		logs.Error("不支持的支付方式: %v", payment.Method)
		return false, errors.New("不支持的支付方式")
	}

	// 4. 更新退款状态
	oldStatus := refund.Status
	refund.Status = models.RefundStatusSuccess
	now := time.Now()
	refund.RefundTime = &now
	refund.ExternalNo = externalNo
	refund.CallbackData = rawData

	// 5. 更新退款记录
	err = s.refundRepo.UpdateRefund(refund)
	if err != nil {
		logs.Error("更新退款记录失败: %v", err)
		return false, err
	}

	// 6. 记录日志
	s.createRefundLog(refund.ID, constants.RefundActionNotify,
		fmt.Sprintf("退款回调处理成功, 状态从 %s 变为 %s", oldStatus.String(), refund.Status.String()),
		rawData, true, "")

	// 7. 还原优惠券状态
	s.restoreCouponsForRefund(refund.OrderID)

	// 8. 通知订单系统更新状态
	// TODO: 调用订单系统API更新订单退款状态

	return true, nil
}

// GetUserRefunds 获取用户退款列表
func (s *RefundServiceImpl) GetUserRefunds(req *dto.RefundListRequest) (*dto.PagedRefundList, error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 查询退款记录
	refunds, total, err := s.refundRepo.GetUserRefunds(req.UserID, req.Status, req.Page, req.PageSize)
	if err != nil {
		logs.Error("查询用户退款列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	result := &dto.PagedRefundList{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     make([]dto.RefundBriefInfo, 0, len(refunds)),
	}

	// 转换数据
	for _, r := range refunds {
		result.List = append(result.List, dto.RefundBriefInfo{
			ID:         r.ID,
			RefundNo:   r.RefundNo,
			PaymentID:  r.PaymentID,
			OrderID:    r.OrderID,
			Amount:     r.Amount,
			Status:     r.Status,
			StatusName: r.Status.String(),
			Reason:     r.Reason,
			CreatedAt:  r.CreatedAt,
			RefundTime: r.RefundTime,
		})
	}

	return result, nil
}

// GetRefundDetail 获取退款详情
func (s *RefundServiceImpl) GetRefundDetail(refundID int64, userID int64) (*models.Refund, error) {
	// 查询退款记录
	refund, err := s.refundRepo.GetRefundByID(refundID)
	if err != nil {
		logs.Error("查询退款记录失败: %v", err)
		return nil, err
	}

	if refund == nil {
		return nil, errors.New("退款记录不存在")
	}

	// 验证用户权限
	if refund.UserID != userID {
		return nil, errors.New("无权查看此退款记录")
	}

	return refund, nil
}

// ApproveRefund 审批退款
func (s *RefundServiceImpl) ApproveRefund(refundID int64, approverID int64, approved bool, remark string) error {
	// 1. 查询退款记录
	refund, err := s.refundRepo.GetRefundByID(refundID)
	if err != nil {
		logs.Error("查询退款记录失败: %v", err)
		return err
	}

	if refund == nil {
		return errors.New("退款记录不存在")
	}

	// 2. 检查退款状态
	if refund.Status != models.RefundStatusWaitingApproval {
		return errors.New("该退款记录状态不允许审批")
	}

	// 3. 更新审批信息
	now := time.Now()
	refund.ApproverID = approverID
	refund.ApprovalTime = &now
	refund.ApprovalRemark = remark

	if approved {
		// 审批通过
		refund.ApprovalStatus = constants.ApprovalStatusApproved
		refund.Status = models.RefundStatusProcessing
	} else {
		// 审批拒绝
		refund.ApprovalStatus = constants.ApprovalStatusRejected
		refund.Status = models.RefundStatusRejected
	}

	// 4. 更新退款记录
	err = s.refundRepo.UpdateRefund(refund)
	if err != nil {
		logs.Error("更新退款记录失败: %v", err)
		return err
	}

	// 5. 记录日志
	action := constants.RefundActionApprove
	var msg string
	if approved {
		msg = fmt.Sprintf("退款审批通过，审批人ID: %d, 备注: %s", approverID, remark)
	} else {
		msg = fmt.Sprintf("退款审批拒绝，审批人ID: %d, 备注: %s", approverID, remark)
	}
	s.createRefundLog(refund.ID, action, msg, "", true, "")

	// 6. 如果审批通过，处理退款
	if approved {
		// 异步处理退款
		go s.processRefund(refund)
	}

	return nil
}

// GetPendingApprovalRefunds 获取待审批的退款列表
func (s *RefundServiceImpl) GetPendingApprovalRefunds(page, pageSize int) ([]*models.Refund, int64, error) {
	// 参数校验
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 查询待审批退款记录
	return s.refundRepo.GetRefundsByStatus(models.RefundStatusWaitingApproval, page, pageSize)
}

// 以下是辅助方法

// generateRefundNo 生成退款单号
func (s *RefundServiceImpl) generateRefundNo(paymentID, userID int64) string {
	now := time.Now()
	// 格式：前缀 + 年月日时分秒 + 随机数 + 用户ID后4位
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	userIDStr := fmt.Sprintf("%04d", userID%10000)
	randomStr := fmt.Sprintf("%04d", r.Intn(10000))

	return fmt.Sprintf("%s%s%s%s",
		constants.RefundNoPrefix,
		now.Format("20060102150405"),
		randomStr,
		userIDStr)
}

// createRefundLog 创建退款日志
func (s *RefundServiceImpl) createRefundLog(refundID int64, action, msg, responseData string, success bool, errorMsg string) {
	log := &models.PaymentLog{
		RefundID:     refundID,
		Action:       action,
		RequestData:  msg,
		ResponseData: responseData,
		Success:      success,
		ErrorMsg:     errorMsg,
		IP:           "",
	}

	_, err := s.paymentRepo.CreatePaymentLog(log)
	if err != nil {
		logs.Error("创建退款日志失败: %v", err)
	}
}

// processRefund 处理退款
func (s *RefundServiceImpl) processRefund(refund *models.Refund) {
	// 查询支付记录
	payment, err := s.paymentRepo.GetPaymentByID(refund.PaymentID)
	if err != nil {
		logs.Error("查询支付记录失败: %v", err)

		// 更新退款状态为失败
		refund.Status = models.RefundStatusFailed
		// 记录失败原因到备注字段
		refund.ApprovalRemark = "查询支付记录失败: " + err.Error()
		s.refundRepo.UpdateRefund(refund)

		// 记录日志
		s.createRefundLog(refund.ID, constants.PaymentActionRefund,
			"处理退款失败: 查询支付记录失败", "", false, err.Error())
		return
	}

	if payment == nil {
		logs.Error("支付记录不存在: %d", refund.PaymentID)

		// 更新退款状态为失败
		refund.Status = models.RefundStatusFailed
		refund.ApprovalRemark = "支付记录不存在"
		s.refundRepo.UpdateRefund(refund)

		// 记录日志
		s.createRefundLog(refund.ID, constants.PaymentActionRefund,
			"处理退款失败: 支付记录不存在", "", false, "支付记录不存在")
		return
	}

	// 获取支付处理器
	paymentMethod := s.getPaymentProcessorByMethod(payment.Method)
	if paymentMethod == nil {
		logs.Error("不支持的支付方式: %v", payment.Method)

		// 更新退款状态为失败
		refund.Status = models.RefundStatusFailed
		refund.ApprovalRemark = "不支持的支付方式"
		s.refundRepo.UpdateRefund(refund)

		// 记录日志
		s.createRefundLog(refund.ID, constants.PaymentActionRefund,
			fmt.Sprintf("处理退款失败: 不支持的支付方式 %s", payment.Method.String()),
			"", false, "不支持的支付方式")
		return
	}

	// 申请退款
	externalRefundNo, err := paymentMethod.RequestRefund(refund, payment)
	if err != nil {
		logs.Error("申请退款失败: %v", err)

		// 更新退款状态为失败
		refund.Status = models.RefundStatusFailed
		refund.ApprovalRemark = fmt.Sprintf("申请退款失败: %v", err)
		s.refundRepo.UpdateRefund(refund)

		// 记录日志
		s.createRefundLog(refund.ID, constants.PaymentActionRefund,
			"申请退款失败", "", false, err.Error())
		return
	}

	// 更新退款记录
	refund.Status = models.RefundStatusProcessing
	refund.ExternalNo = externalRefundNo
	err = s.refundRepo.UpdateRefund(refund)
	if err != nil {
		logs.Error("更新退款记录失败: %v", err)
	}

	// 记录日志
	s.createRefundLog(refund.ID, constants.PaymentActionRefund,
		fmt.Sprintf("申请退款成功，退款单号: %s", refund.RefundNo),
		"", true, "")

	// 对于余额支付等同步退款方式，立即查询一次退款状态
	if payment.Method == models.PaymentMethodBalance {
		status, err := paymentMethod.QueryRefundStatus(refund)
		if err == nil && status == models.RefundStatusSuccess {
			// 更新退款状态
			refund.Status = status
			now := time.Now()
			refund.RefundTime = &now

			// 更新退款记录
			err = s.refundRepo.UpdateRefund(refund)
			if err != nil {
				logs.Error("更新退款状态失败: %v", err)
			} else {
				// 记录日志
				s.createRefundLog(refund.ID, constants.RefundActionQuery,
					"退款成功", "", true, "")

				// 还原优惠券状态
				s.restoreCouponsForRefund(refund.OrderID)

				// 通知订单系统
				// TODO: 调用订单系统API更新订单退款状态
			}
		}
	}
}

// getPaymentProcessorByMethod 根据支付方式获取处理器
func (s *RefundServiceImpl) getPaymentProcessorByMethod(method models.PaymentMethod) PaymentProcessor {
	switch method {
	case models.PaymentMethodWechat:
		return NewWechatPayProcessor(s.paymentAcctRepo)
	case models.PaymentMethodAlipay:
		return NewAlipayProcessor(s.paymentAcctRepo)
	case models.PaymentMethodBalance:
		return NewBalancePayProcessor()
	default:
		return nil
	}
}

// restoreCouponsForRefund 还原订单使用的优惠券状态
// RestoreCouponsForRefund 还原订单优惠券状态（公开方法）
func (s *RefundServiceImpl) RestoreCouponsForRefund(orderID int64) error {
	logs.Info("[优惠券还原] 公开接口被调用 - 订单ID: %d", orderID)
	s.restoreCouponsForRefund(orderID)
	logs.Info("[优惠券还原] 公开接口调用完成 - 订单ID: %d", orderID)
	return nil
}

// restoreCouponsForRefund 还原订单优惠券状态（私有方法）
func (s *RefundServiceImpl) restoreCouponsForRefund(orderID int64) {
	logs.Info("开始还原订单 %d 的优惠券状态", orderID)
	
	// 查询订单信息
	order, err := s.getOrderByID(orderID)
	if err != nil {
		logs.Error("查询订单信息失败: %v", err)
		return
	}
	
	if order == nil {
		logs.Error("订单不存在: %d", orderID)
		return
	}
	
	// 检查订单是否使用了优惠券
	if order.CouponIDs == "" {
		logs.Info("订单 %d 未使用优惠券，无需还原", orderID)
		return
	}
	
	// 解析优惠券ID列表
	couponIDStrs := strings.Split(order.CouponIDs, ",")
	if len(couponIDStrs) == 0 {
		logs.Info("订单 %d 优惠券ID列表为空，无需还原", orderID)
		return
	}
	
	// 还原每个优惠券的状态
	for _, couponIDStr := range couponIDStrs {
		couponIDStr = strings.TrimSpace(couponIDStr)
		if couponIDStr == "" {
			continue
		}
		
		couponID, err := strconv.ParseInt(couponIDStr, 10, 64)
		if err != nil {
			logs.Error("解析优惠券ID失败: %s, error: %v", couponIDStr, err)
			continue
		}
		
		// 还原优惠券状态
		err = s.restoreSingleCoupon(order.UserID, couponID, orderID)
		if err != nil {
			logs.Error("还原优惠券 %d 状态失败: %v", couponID, err)
		} else {
			logs.Info("成功还原优惠券 %d 状态", couponID)
		}
	}
	
	logs.Info("完成订单 %d 的优惠券状态还原", orderID)
}

// restoreSingleCoupon 还原单个优惠券状态
func (s *RefundServiceImpl) restoreSingleCoupon(userID, couponID, orderID int64) error {
	// 查询用户优惠券记录
	userCoupon, err := s.getUserCouponByUserIDAndCouponIDAndOrderID(userID, couponID, orderID)
	if err != nil {
		return fmt.Errorf("查询用户优惠券记录失败: %v", err)
	}
	
	if userCoupon == nil {
		return fmt.Errorf("未找到用户 %d 的优惠券 %d 使用记录", userID, couponID)
	}
	
	// 检查优惠券状态
	if userCoupon.Status != 2 { // 2表示已使用状态
		logs.Warn("优惠券 %d 状态不是已使用状态，当前状态: %d", couponID, userCoupon.Status)
		return nil
	}
	
	// 还原优惠券状态为未使用
	err = s.updateUserCouponStatus(userCoupon.ID, 1, 0) // 1表示未使用状态，0表示清空订单ID
	if err != nil {
		return fmt.Errorf("更新优惠券状态失败: %v", err)
	}
	
	return nil
}

// getOrderByID 根据订单ID查询订单信息
func (s *RefundServiceImpl) getOrderByID(orderID int64) (*OrderInfo, error) {
	// 这里需要调用订单服务获取订单信息
	// 由于跨模块调用，这里使用数据库直接查询
	o := orm.NewOrm()
	var order OrderInfo
	err := o.QueryTable("order").Filter("id", orderID).One(&order)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &order, nil
}

// getUserCouponByUserIDAndCouponIDAndOrderID 查询用户优惠券使用记录
func (s *RefundServiceImpl) getUserCouponByUserIDAndCouponIDAndOrderID(userID, couponID, orderID int64) (*UserCouponInfo, error) {
	// 这里需要调用外卖模块的优惠券服务
	// 由于跨模块调用，这里使用数据库直接查询
	o := orm.NewOrm()
	var userCoupon UserCouponInfo
	err := o.QueryTable("takeout_user_coupon").Filter("user_id", userID).Filter("coupon_id", couponID).Filter("order_id", orderID).One(&userCoupon)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &userCoupon, nil
}

// updateUserCouponStatus 更新用户优惠券状态
func (s *RefundServiceImpl) updateUserCouponStatus(userCouponID int64, status int, orderID int64) error {
	// 这里需要调用外卖模块的优惠券服务
	// 由于跨模块调用，这里使用数据库直接更新
	o := orm.NewOrm()
	params := orm.Params{
		"status": status,
	}
	
	// 如果是还原为未使用状态，清空使用时间和订单ID
	if status == 1 {
		params["used_time"] = nil
		params["order_id"] = nil
	} else if orderID > 0 {
		params["order_id"] = orderID
		params["used_time"] = time.Now()
	}
	
	_, err := o.QueryTable("takeout_user_coupon").Filter("id", userCouponID).Update(params)
	return err
}

// OrderInfo 订单信息结构体
type OrderInfo struct {
	ID        int64  `orm:"column(id)"`
	UserID    int64  `orm:"column(user_id)"`
	CouponIDs string `orm:"column(coupon_ids)"`
}

// UserCouponInfo 用户优惠券信息结构体
type UserCouponInfo struct {
	ID       int64 `orm:"column(id)"`
	UserID   int64 `orm:"column(user_id)"`
	CouponID int64 `orm:"column(coupon_id)"`
	Status   int   `orm:"column(status)"`
	OrderID  int64 `orm:"column(order_id)"`
}
