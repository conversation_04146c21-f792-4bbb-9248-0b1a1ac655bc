/**
 * wechat_pay_processor.go
 * 微信支付处理器
 * 实现微信支付相关的支付、查询、退款等功能
 */

package impl

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"o_mall_backend/modules/payment/models"
	"o_mall_backend/modules/payment/repositories"
)

// WechatPayConfig 微信支付配置
type WechatPayConfig struct {
	AppID      string // 微信AppID
	MchID      string // 商户号
	SecretKey  string // API密钥
	NotifyURL  string // 回调通知URL
	TradeType  string // 交易类型
	SignType   string // 签名类型
	ServerIP   string // 服务器IP
	CertPath   string // 证书路径
	KeyPath    string // 密钥路径
	RootCAPath string // 根证书路径
}

// WechatPayProcessor 微信支付处理器
type WechatPayProcessor struct {
	accountRepo repositories.PaymentAccountRepository
}

// NewWechatPayProcessor 创建微信支付处理器
func NewWechatPayProcessor(accountRepo repositories.PaymentAccountRepository) *WechatPayProcessor {
	return &WechatPayProcessor{
		accountRepo: accountRepo,
	}
}

// GeneratePaymentParams 生成支付参数
func (p *WechatPayProcessor) GeneratePaymentParams(payment *models.Payment, returnURL string) (*PaymentParams, error) {
	// 获取微信支付账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodWechat)
	if err != nil {
		return nil, err
	}
	if account == nil {
		return nil, errors.New("未配置微信支付账户")
	}

	// 解析配置
	config := &WechatPayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return nil, fmt.Errorf("解析微信支付配置失败: %v", err)
	}

	// 根据客户端信息选择适当的交易类型
	tradeType := "NATIVE" // 默认使用扫码支付
	if strings.Contains(strings.ToLower(payment.DeviceInfo), "android") ||
		strings.Contains(strings.ToLower(payment.DeviceInfo), "ios") {
		tradeType = "APP"
	} else if strings.Contains(strings.ToLower(payment.DeviceInfo), "wechat") {
		tradeType = "JSAPI"
	} else if strings.Contains(strings.ToLower(payment.DeviceInfo), "h5") ||
		strings.Contains(strings.ToLower(payment.DeviceInfo), "mobile") {
		tradeType = "MWEB"
	}

	// 构建请求参数
	params := map[string]string{
		"appid":            config.AppID,
		"mch_id":           config.MchID,
		"nonce_str":        p.generateNonceStr(),
		"body":             fmt.Sprintf("订单支付-%s", payment.TransactionNo),
		"out_trade_no":     payment.TransactionNo,
		"total_fee":        fmt.Sprintf("%d", int64(payment.Amount*100)), // 转换为分
		"spbill_create_ip": payment.ClientIP,
		"notify_url":       config.NotifyURL,
		"trade_type":       tradeType,
		"time_expire":      payment.ExpireTime.Format("20060102150405"),
	}

	// 如果是JSAPI类型，需要提供openid
	if tradeType == "JSAPI" {
		// 从DeviceInfo中获取openid，实际中可能需要另外的方法获取
		openid := p.getOpenIDFromDeviceInfo(payment.DeviceInfo)
		if openid == "" {
			return nil, errors.New("公众号支付需要提供openid")
		}
		params["openid"] = openid
	}

	// H5支付需要场景信息
	if tradeType == "MWEB" {
		sceneInfo := map[string]interface{}{
			"h5_info": map[string]string{
				"type":     "Wap",
				"wap_url":  returnURL,
				"wap_name": "O_Mall购物",
			},
		}
		sceneInfoJson, _ := json.Marshal(sceneInfo)
		params["scene_info"] = string(sceneInfoJson)
	}

	// 计算签名
	params["sign"] = p.calculateSign(params, config.SecretKey)

	// 构建XML请求
	xmlRequest := p.mapToXml(params)

	// 调用微信统一下单接口
	// 注意：这里应该是实际的HTTP请求调用，这里仅作示例
	xmlResponse := p.callWechatAPI("https://api.mch.weixin.qq.com/pay/unifiedorder", xmlRequest)

	// 解析返回结果
	respMap, err := p.xmlToMap(xmlResponse)
	if err != nil {
		return nil, fmt.Errorf("解析微信返回结果失败: %v", err)
	}

	// 验证返回结果
	if respMap["return_code"] != "SUCCESS" {
		return nil, fmt.Errorf("微信支付下单失败: %s", respMap["return_msg"])
	}

	if respMap["result_code"] != "SUCCESS" {
		return nil, fmt.Errorf("微信支付下单失败: %s - %s", respMap["err_code"], respMap["err_code_des"])
	}

	// 根据交易类型返回对应的支付参数
	result := &PaymentParams{
		AppPayParams: make(map[string]string),
		WebPayParams: make(map[string]string),
	}

	switch tradeType {
	case "NATIVE":
		// 扫码支付，返回二维码URL
		result.QrCodeURL = respMap["code_url"]

	case "APP":
		// APP支付，返回APP调起支付所需参数
		timestamp := fmt.Sprintf("%d", time.Now().Unix())
		appPayParams := map[string]string{
			"appid":     config.AppID,
			"partnerid": config.MchID,
			"prepayid":  respMap["prepay_id"],
			"package":   "Sign=WXPay",
			"noncestr":  p.generateNonceStr(),
			"timestamp": timestamp,
		}
		appPayParams["sign"] = p.calculateSign(appPayParams, config.SecretKey)
		result.AppPayParams = appPayParams

	case "JSAPI":
		// 公众号支付，返回JS调起支付所需参数
		timestamp := fmt.Sprintf("%d", time.Now().Unix())
		jsPayParams := map[string]string{
			"appId":     config.AppID,
			"timeStamp": timestamp,
			"nonceStr":  p.generateNonceStr(),
			"package":   "prepay_id=" + respMap["prepay_id"],
			"signType":  "MD5",
		}
		jsPayParams["paySign"] = p.calculateSign(jsPayParams, config.SecretKey)
		result.WebPayParams = jsPayParams

	case "MWEB":
		// H5支付，返回支付跳转链接
		result.PaymentURL = respMap["mweb_url"]
		if returnURL != "" {
			result.PaymentURL += "&redirect_url=" + returnURL
		}
	}

	return result, nil
}

// QueryPaymentStatus 查询支付状态
func (p *WechatPayProcessor) QueryPaymentStatus(payment *models.Payment) (models.PaymentStatus, string, error) {
	// 获取微信支付账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodWechat)
	if err != nil {
		return payment.Status, "", err
	}
	if account == nil {
		return payment.Status, "", errors.New("未配置微信支付账户")
	}

	// 解析配置
	config := &WechatPayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return payment.Status, "", fmt.Errorf("解析微信支付配置失败: %v", err)
	}

	// 构建查询参数
	params := map[string]string{
		"appid":        config.AppID,
		"mch_id":       config.MchID,
		"out_trade_no": payment.TransactionNo,
		"nonce_str":    p.generateNonceStr(),
	}

	// 计算签名
	params["sign"] = p.calculateSign(params, config.SecretKey)

	// 构建XML请求
	xmlRequest := p.mapToXml(params)

	// 调用微信查询接口
	xmlResponse := p.callWechatAPI("https://api.mch.weixin.qq.com/pay/orderquery", xmlRequest)

	// 解析返回结果
	respMap, err := p.xmlToMap(xmlResponse)
	if err != nil {
		return payment.Status, "", fmt.Errorf("解析微信返回结果失败: %v", err)
	}

	// 验证返回结果
	if respMap["return_code"] != "SUCCESS" {
		return payment.Status, "", fmt.Errorf("微信支付查询失败: %s", respMap["return_msg"])
	}

	if respMap["result_code"] != "SUCCESS" {
		return payment.Status, "", fmt.Errorf("微信支付查询失败: %s - %s", respMap["err_code"], respMap["err_code_des"])
	}

	// 解析交易状态
	tradeState := respMap["trade_state"]
	transactionId := respMap["transaction_id"]

	switch tradeState {
	case "SUCCESS": // 支付成功
		return models.PaymentStatusSuccess, transactionId, nil
	case "REFUND": // 转入退款
		return models.PaymentStatusRefunded, transactionId, nil
	case "NOTPAY": // 未支付
		return models.PaymentStatusPending, "", nil
	case "CLOSED": // 已关闭
		return models.PaymentStatusCancelled, "", nil
	case "USERPAYING": // 用户支付中
		return models.PaymentStatusProcessing, "", nil
	case "PAYERROR": // 支付失败
		return models.PaymentStatusFailed, "", nil
	default:
		return payment.Status, "", nil
	}
}

// ClosePayment 关闭支付
func (p *WechatPayProcessor) ClosePayment(payment *models.Payment) (bool, error) {
	// 获取微信支付账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodWechat)
	if err != nil {
		return false, err
	}
	if account == nil {
		return false, errors.New("未配置微信支付账户")
	}

	// 解析配置
	config := &WechatPayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return false, fmt.Errorf("解析微信支付配置失败: %v", err)
	}

	// 构建关闭订单参数
	params := map[string]string{
		"appid":        config.AppID,
		"mch_id":       config.MchID,
		"out_trade_no": payment.TransactionNo,
		"nonce_str":    p.generateNonceStr(),
	}

	// 计算签名
	params["sign"] = p.calculateSign(params, config.SecretKey)

	// 构建XML请求
	xmlRequest := p.mapToXml(params)

	// 调用微信关闭订单接口
	xmlResponse := p.callWechatAPI("https://api.mch.weixin.qq.com/pay/closeorder", xmlRequest)

	// 解析返回结果
	respMap, err := p.xmlToMap(xmlResponse)
	if err != nil {
		return false, fmt.Errorf("解析微信返回结果失败: %v", err)
	}

	// 验证返回结果
	if respMap["return_code"] != "SUCCESS" {
		return false, fmt.Errorf("微信支付关闭订单失败: %s", respMap["return_msg"])
	}

	if respMap["result_code"] != "SUCCESS" {
		if respMap["err_code"] == "ORDERPAID" {
			// 订单已支付，无法关闭
			return false, errors.New("订单已支付，无法关闭")
		}
		return false, fmt.Errorf("微信支付关闭订单失败: %s - %s", respMap["err_code"], respMap["err_code_des"])
	}

	return true, nil
}

// VerifyCallback 验证回调
func (p *WechatPayProcessor) VerifyCallback(rawData string, payment *models.Payment) (bool, float64, error) {
	// 解析XML数据
	notifyMap, err := p.xmlToMap(rawData)
	if err != nil {
		return false, 0, fmt.Errorf("解析微信回调数据失败: %v", err)
	}

	// 验证基本字段
	if notifyMap["return_code"] != "SUCCESS" {
		return false, 0, fmt.Errorf("微信支付回调返回失败: %s", notifyMap["return_msg"])
	}

	if notifyMap["result_code"] != "SUCCESS" {
		return false, 0, fmt.Errorf("微信支付回调业务结果失败: %s - %s", notifyMap["err_code"], notifyMap["err_code_des"])
	}

	// 获取微信支付账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodWechat)
	if err != nil {
		return false, 0, err
	}
	if account == nil {
		return false, 0, errors.New("未配置微信支付账户")
	}

	// 解析配置
	config := &WechatPayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return false, 0, fmt.Errorf("解析微信支付配置失败: %v", err)
	}

	// 验证签名
	sign := notifyMap["sign"]
	delete(notifyMap, "sign")

	calculatedSign := p.calculateSign(notifyMap, config.SecretKey)
	if sign != calculatedSign {
		return false, 0, errors.New("微信支付回调签名验证失败")
	}

	// 验证商户号、应用ID
	if notifyMap["mch_id"] != config.MchID {
		return false, 0, errors.New("微信支付回调商户号不匹配")
	}

	if notifyMap["appid"] != config.AppID {
		return false, 0, errors.New("微信支付回调应用ID不匹配")
	}

	// 验证订单号
	if notifyMap["out_trade_no"] != payment.TransactionNo {
		return false, 0, errors.New("微信支付回调订单号不匹配")
	}

	// 获取支付金额（单位：分）
	totalFeeStr := notifyMap["total_fee"]
	totalFee, err := strconv.ParseInt(totalFeeStr, 10, 64)
	if err != nil {
		return false, 0, fmt.Errorf("解析微信支付金额失败: %v", err)
	}

	// 转换为元
	amount := float64(totalFee) / 100.0

	return true, amount, nil
}

// RequestRefund 申请退款
func (p *WechatPayProcessor) RequestRefund(refund *models.Refund, payment *models.Payment) (string, error) {
	// 获取微信支付账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodWechat)
	if err != nil {
		return "", err
	}
	if account == nil {
		return "", errors.New("未配置微信支付账户")
	}

	// 解析配置
	config := &WechatPayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return "", fmt.Errorf("解析微信支付配置失败: %v", err)
	}

	// 构建退款参数
	params := map[string]string{
		"appid":         config.AppID,
		"mch_id":        config.MchID,
		"nonce_str":     p.generateNonceStr(),
		"out_trade_no":  payment.TransactionNo,
		"out_refund_no": refund.RefundNo,
		"total_fee":     fmt.Sprintf("%d", int64(payment.Amount*100)), // 转换为分
		"refund_fee":    fmt.Sprintf("%d", int64(refund.Amount*100)),  // 转换为分
		"refund_desc":   refund.Reason,
		"notify_url":    config.NotifyURL + "/refund", // 退款通知URL
	}

	// 计算签名
	params["sign"] = p.calculateSign(params, config.SecretKey)

	// 构建XML请求
	xmlRequest := p.mapToXml(params)

	// 调用微信退款接口（需要证书）
	// 注意：这里需要使用带证书的HTTPS请求
	xmlResponse := p.callWechatAPIWithCert("https://api.mch.weixin.qq.com/secapi/pay/refund",
		xmlRequest, config.CertPath, config.KeyPath)

	// 解析返回结果
	respMap, err := p.xmlToMap(xmlResponse)
	if err != nil {
		return "", fmt.Errorf("解析微信返回结果失败: %v", err)
	}

	// 验证返回结果
	if respMap["return_code"] != "SUCCESS" {
		return "", fmt.Errorf("微信退款请求失败: %s", respMap["return_msg"])
	}

	if respMap["result_code"] != "SUCCESS" {
		return "", fmt.Errorf("微信退款失败: %s - %s", respMap["err_code"], respMap["err_code_des"])
	}

	// 返回微信退款单号
	return respMap["refund_id"], nil
}

// QueryRefundStatus 查询退款状态
func (p *WechatPayProcessor) QueryRefundStatus(refund *models.Refund) (models.RefundStatus, error) {
	// 获取微信支付账户
	account, err := p.accountRepo.GetDefaultPaymentAccount(models.PaymentMethodWechat)
	if err != nil {
		return models.RefundStatus(int(refund.Status)), err
	}
	if account == nil {
		return models.RefundStatus(int(refund.Status)), errors.New("未配置微信支付账户")
	}

	// 解析配置
	config := &WechatPayConfig{}
	err = json.Unmarshal([]byte(account.Config), config)
	if err != nil {
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("解析微信支付配置失败: %v", err)
	}

	// 构建查询参数
	params := map[string]string{
		"appid":         config.AppID,
		"mch_id":        config.MchID,
		"nonce_str":     p.generateNonceStr(),
		"out_refund_no": refund.RefundNo,
	}

	// 计算签名
	params["sign"] = p.calculateSign(params, config.SecretKey)

	// 构建XML请求
	xmlRequest := p.mapToXml(params)

	// 调用微信退款查询接口
	xmlResponse := p.callWechatAPI("https://api.mch.weixin.qq.com/pay/refundquery", xmlRequest)

	// 解析返回结果
	respMap, err := p.xmlToMap(xmlResponse)
	if err != nil {
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("解析微信返回结果失败: %v", err)
	}

	// 验证返回结果
	if respMap["return_code"] != "SUCCESS" {
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("微信退款查询失败: %s", respMap["return_msg"])
	}

	if respMap["result_code"] != "SUCCESS" {
		return models.RefundStatus(int(refund.Status)), fmt.Errorf("微信退款查询失败: %s - %s", respMap["err_code"], respMap["err_code_des"])
	}

	// 解析退款状态
	// 微信退款状态：SUCCESS-退款成功，REFUNDCLOSE-退款关闭，PROCESSING-退款处理中，CHANGE-退款异常
	refundStatus := respMap["refund_status_0"]

	switch refundStatus {
	case "SUCCESS":
		return models.RefundStatusSuccess, nil
	case "REFUNDCLOSE":
		return models.RefundStatusFailed, nil
	case "PROCESSING":
		return models.RefundStatusProcessing, nil
	case "CHANGE":
		return models.RefundStatusFailed, errors.New("退款异常，请联系客服")
	default:
		return models.RefundStatus(int(refund.Status)), nil
	}
}

// 以下为工具方法

// generateNonceStr 生成随机字符串
func (p *WechatPayProcessor) generateNonceStr() string {
	chars := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := ""
	for i := 0; i < 32; i++ {
		index := time.Now().UnixNano() % int64(len(chars))
		result += string(chars[index])
		time.Sleep(time.Nanosecond)
	}
	return result
}

// calculateSign 计算签名
func (p *WechatPayProcessor) calculateSign(params map[string]string, key string) string {
	// 按照参数名ASCII码从小到大排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 拼接签名字符串
	var buf strings.Builder
	for _, k := range keys {
		if params[k] == "" {
			continue
		}
		buf.WriteString(k)
		buf.WriteString("=")
		buf.WriteString(params[k])
		buf.WriteString("&")
	}
	buf.WriteString("key=")
	buf.WriteString(key)

	// MD5签名
	hash := md5.New()
	hash.Write([]byte(buf.String()))
	return strings.ToUpper(hex.EncodeToString(hash.Sum(nil)))
}

// mapToXml 将Map转换为XML
func (p *WechatPayProcessor) mapToXml(params map[string]string) string {
	var buf strings.Builder
	buf.WriteString("<xml>")
	for k, v := range params {
		buf.WriteString("<")
		buf.WriteString(k)
		buf.WriteString(">")
		buf.WriteString("<![CDATA[")
		buf.WriteString(v)
		buf.WriteString("]]>")
		buf.WriteString("</")
		buf.WriteString(k)
		buf.WriteString(">")
	}
	buf.WriteString("</xml>")
	return buf.String()
}

// xmlToMap 将XML转换为Map
// 注意：这里是简化版本，实际应使用XML解析库
func (p *WechatPayProcessor) xmlToMap(xmlStr string) (map[string]string, error) {
	result := make(map[string]string)

	// 简化的XML解析，仅作示例，实际应使用XML解析库
	// 实际项目中应该使用encoding/xml包进行解析

	// 模拟返回结果
	result["return_code"] = "SUCCESS"
	result["return_msg"] = "OK"
	result["result_code"] = "SUCCESS"
	result["mch_id"] = "1230000109"
	result["appid"] = "wx12345678"
	result["nonce_str"] = "abcdefghijklmn"
	result["sign"] = "ABCDEFGHIJKLMN"
	result["trade_state"] = "SUCCESS"
	result["out_trade_no"] = "202302030001"
	result["transaction_id"] = "4200001234567890"
	result["total_fee"] = "100"
	result["fee_type"] = "CNY"
	result["trade_state_desc"] = "支付成功"

	return result, nil
}

// callWechatAPI 调用微信API
func (p *WechatPayProcessor) callWechatAPI(url, xmlData string) string {
	// 在实际项目中，这里应该发起HTTP POST请求
	// 返回模拟的XML响应
	return `<xml>
<return_code><![CDATA[SUCCESS]]></return_code>
<return_msg><![CDATA[OK]]></return_msg>
<appid><![CDATA[wx12345678]]></appid>
<mch_id><![CDATA[1230000109]]></mch_id>
<nonce_str><![CDATA[abcdefghijklmn]]></nonce_str>
<sign><![CDATA[ABCDEFGHIJKLMN]]></sign>
<result_code><![CDATA[SUCCESS]]></result_code>
<prepay_id><![CDATA[wx201809181347481234567890]]></prepay_id>
<trade_type><![CDATA[NATIVE]]></trade_type>
<code_url><![CDATA[weixin://wxpay/bizpayurl?pr=ABCDEFG]]></code_url>
</xml>`
}

// callWechatAPIWithCert 调用需要证书的微信API
func (p *WechatPayProcessor) callWechatAPIWithCert(url, xmlData, certPath, keyPath string) string {
	// 在实际项目中，这里应该发起带证书的HTTPS POST请求
	// 返回模拟的XML响应
	return `<xml>
<return_code><![CDATA[SUCCESS]]></return_code>
<return_msg><![CDATA[OK]]></return_msg>
<appid><![CDATA[wx12345678]]></appid>
<mch_id><![CDATA[1230000109]]></mch_id>
<nonce_str><![CDATA[abcdefghijklmn]]></nonce_str>
<sign><![CDATA[ABCDEFGHIJKLMN]]></sign>
<result_code><![CDATA[SUCCESS]]></result_code>
<out_trade_no><![CDATA[202302030001]]></out_trade_no>
<out_refund_no><![CDATA[RF202302030001]]></out_refund_no>
<refund_id><![CDATA[50000009012023020300123456789]]></refund_id>
<refund_fee><![CDATA[100]]></refund_fee>
<total_fee><![CDATA[100]]></total_fee>
</xml>`
}

// getOpenIDFromDeviceInfo 从设备信息中提取OpenID
func (p *WechatPayProcessor) getOpenIDFromDeviceInfo(deviceInfo string) string {
	// 实际项目中，应该从用户会话中获取openid
	// 这里只是示例实现
	if strings.Contains(deviceInfo, "openid=") {
		parts := strings.Split(deviceInfo, "openid=")
		if len(parts) > 1 {
			return strings.Split(parts[1], "&")[0]
		}
	}
	return ""
}
