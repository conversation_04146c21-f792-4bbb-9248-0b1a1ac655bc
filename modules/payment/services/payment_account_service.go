/**
 * payment_account_service.go
 * 支付账户服务接口定义
 * 定义支付账户相关的业务逻辑接口
 */

package services

import (
	"o_mall_backend/modules/payment/models"
)

// PaymentAccountService 支付账户服务接口
type PaymentAccountService interface {
	// 创建支付账户
	CreatePaymentAccount(account *models.PaymentAccount) (int64, error)

	// 更新支付账户
	UpdatePaymentAccount(account *models.PaymentAccount) error

	// 删除支付账户
	DeletePaymentAccount(id int64) error

	// 获取支付账户详情
	GetPaymentAccount(id int64) (*models.PaymentAccount, error)

	// 获取所有支付账户
	GetAllPaymentAccounts() ([]*models.PaymentAccount, error)

	// 获取指定支付方式的账户列表
	GetPaymentAccountsByMethod(method models.PaymentMethod) ([]*models.PaymentAccount, error)

	// 获取默认支付账户
	GetDefaultPaymentAccount(method models.PaymentMethod) (*models.PaymentAccount, error)

	// 设置默认支付账户
	SetDefaultPaymentAccount(id int64, method models.PaymentMethod) error

	// 测试支付账户连接
	TestPaymentAccountConnection(id int64) (bool, string, error)
}
