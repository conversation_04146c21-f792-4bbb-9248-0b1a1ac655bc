# 权限系统模块

## 1. 系统概述

权限系统模块是O-Mall电商平台的核心组件之一，提供了完整的RBAC（基于角色的访问控制）权限管理功能。该模块支持权限定义、角色管理、权限分配和权限验证等功能，为系统提供了灵活而安全的访问控制机制。

### 1.1 主要功能

- **权限管理**：创建、查询、更新和删除权限
- **角色管理**：创建、查询、更新和删除角色
- **权限分配**：为角色分配权限，为用户分配角色
- **权限验证**：验证用户是否拥有特定权限

### 1.2 技术架构

权限系统采用分层架构设计：

- **控制层（Controllers）**：处理HTTP请求，参数验证和响应返回
- **服务层（Services）**：实现业务逻辑，处理权限和角色的核心功能
- **数据层（Models）**：定义数据结构和数据库交互
- **DTO层（Data Transfer Objects）**：定义数据传输对象，用于请求和响应

## 2. 核心模型

### 2.1 权限模型（Permission）

权限是系统中最基本的访问控制单元，定义了用户可以执行的操作。

```go
type Permission struct {
    ID          int64            // 权限ID
    ParentID    int64            // 父权限ID
    Name        string           // 权限名称
    Code        string           // 权限编码
    Type        PermissionType   // 权限类型
    Level       PermissionLevel  // 权限级别
    Path        string           // 权限路径
    Component   string           // 前端组件
    Icon        string           // 图标
    Sort        int              // 排序
    Status      PermissionStatus // 状态
    Description string           // 描述
    CreatedAt   time.Time        // 创建时间
    UpdatedAt   time.Time        // 更新时间
}
```

#### 权限类型（PermissionType）

- `PermissionTypeMenu = 1`：菜单权限
- `PermissionTypeButton = 2`：按钮权限
- `PermissionTypeAPI = 3`：API权限

#### 权限级别（PermissionLevel）

- `PermissionLevelSystem = 1`：系统级权限
- `PermissionLevelModule = 2`：模块级权限
- `PermissionLevelFeature = 3`：功能级权限

#### 权限状态（PermissionStatus）

- `PermissionStatusDisabled = 0`：禁用
- `PermissionStatusEnabled = 1`：启用

### 2.2 角色模型（Role）

角色是权限的集合，用户通过被分配角色来获取权限。

```go
type Role struct {
    ID          int64            // 角色ID
    Name        string           // 角色名称
    Code        string           // 角色编码
    Status      PermissionStatus // 状态
    Description string           // 描述
    CreatedAt   time.Time        // 创建时间
    UpdatedAt   time.Time        // 更新时间
}
```

### 2.3 关联模型

#### 角色权限关联（RolePermission）

```go
type RolePermission struct {
    ID           int64     // 关联ID
    RoleID       int64     // 角色ID
    PermissionID int64     // 权限ID
    CreatedAt    time.Time // 创建时间
}
```

#### 用户角色关联（UserRole）

```go
type UserRole struct {
    ID        int64     // 关联ID
    UserID    int64     // 用户ID
    UserType  string    // 用户类型（admin/merchant/user）
    RoleID    int64     // 角色ID
    CreatedAt time.Time // 创建时间
}
```

## 3. API接口

### 3.1 权限管理接口

| 接口 | 方法 | 路径 | 描述 |
| --- | --- | --- | --- |
| 获取权限列表 | GET | /api/v1/admin/secured/permissions | 分页获取权限列表 |
| 获取权限详情 | GET | /api/v1/admin/secured/permissions/:id | 获取指定权限详情 |
| 创建权限 | POST | /api/v1/admin/secured/permissions | 创建新权限 |
| 更新权限 | PUT | /api/v1/admin/secured/permissions/:id | 更新指定权限 |
| 删除权限 | DELETE | /api/v1/admin/secured/permissions/:id | 删除指定权限 |

### 3.2 角色管理接口

| 接口 | 方法 | 路径 | 描述 |
| --- | --- | --- | --- |
| 获取角色列表 | GET | /api/v1/admin/secured/roles | 分页获取角色列表 |
| 获取角色详情 | GET | /api/v1/admin/secured/roles/:id | 获取指定角色详情 |
| 创建角色 | POST | /api/v1/admin/secured/roles | 创建新角色 |
| 更新角色 | PUT | /api/v1/admin/secured/roles/:id | 更新指定角色 |
| 删除角色 | DELETE | /api/v1/admin/secured/roles/:id | 删除指定角色 |

### 3.3 权限分配接口

| 接口 | 方法 | 路径 | 描述 |
| --- | --- | --- | --- |
| 分配角色 | POST | /api/v1/admin/secured/roles/assign | 为用户分配角色 |
| 分配权限 | PUT | /api/v1/admin/secured/roles/:id/permissions | 为角色分配权限 |

### 3.4 用户权限接口

| 接口 | 方法 | 路径 | 描述 |
| --- | --- | --- | --- |
| 获取用户权限 | GET | /api/v1/admin/secured/users/:user_id/permissions | 获取指定用户的权限列表 |
| 获取用户角色 | GET | /api/v1/admin/secured/users/:user_id/roles | 获取指定用户的角色列表 |
| 获取当前用户权限 | GET | /api/v1/user/permissions | 获取当前登录用户的权限列表 |
| 获取当前用户角色 | GET | /api/v1/user/roles | 获取当前登录用户的角色列表 |

## 4. 使用示例

### 4.1 创建权限

```go
// 创建权限请求
req := &dto.CreatePermissionRequest{
    ParentID:    0,
    Name:        "商品管理",
    Code:        "product:manage",
    Type:        int(models.PermissionTypeMenu),
    Level:       int(models.PermissionLevelModule),
    Path:        "/products",
    Component:   "ProductList",
    Icon:        "shopping-cart",
    Sort:        1,
    Description: "商品管理模块",
}

// 调用服务创建权限
permissionService := services.NewPermissionService()
err := permissionService.CreatePermission(context.Background(), req)
```

### 4.2 创建角色并分配权限

```go
// 创建角色
roleReq := &dto.CreateRoleRequest{
    Name:        "商品管理员",
    Code:        "product_manager",
    Description: "负责商品管理的角色",
}

permissionService := services.NewPermissionService()
err := permissionService.CreateRole(context.Background(), roleReq)

// 为角色分配权限
permReq := &dto.AssignPermissionRequest{
    RoleID:        1, // 角色ID
    PermissionIDs: []int64{1, 2, 3}, // 权限ID列表
}

err = permissionService.AssignPermissions(context.Background(), permReq)
```

### 4.3 为用户分配角色

```go
// 为用户分配角色
req := &dto.AssignRoleRequest{
    UserID:   1,
    UserType: "admin", // 用户类型：admin/merchant/user
    RoleID:   1,
}

permissionService := services.NewPermissionService()
err := permissionService.AssignRole(context.Background(), req)
```

### 4.4 获取用户权限

```go
// 获取用户权限列表
permissionService := services.NewPermissionService()
permissions, err := permissionService.GetUserPermissions(context.Background(), 1, "admin")

// 检查用户是否有特定权限
hasPermission := false
permCode := "product:create"
for _, perm := range permissions {
    if perm.Code == permCode {
        hasPermission = true
        break
    }
}
```

## 5. 集成指南

### 5.1 模块初始化

权限模块在应用启动时通过`Init()`函数进行初始化：

```go
// 在main.go中调用
permission.Init()
```

初始化过程包括：
- 注册ORM模型
- 初始化路由

### 5.2 中间件集成

权限验证可以通过中间件实现：

```go
// 权限验证中间件示例
func PermissionMiddleware(permCode string) web.FilterFunc {
    return func(ctx *context.Context) {
        // 获取当前用户ID和类型
        userID := ctx.Input.GetData("userID").(int64)
        userType := ctx.Input.GetData("userType").(string)
        
        // 获取用户权限
        permissionService := services.NewPermissionService()
        permissions, err := permissionService.GetUserPermissions(ctx.Request.Context(), userID, userType)
        if err != nil {
            // 处理错误
            ctx.ResponseWriter.WriteHeader(500)
            ctx.WriteString("获取权限失败")
            return
        }
        
        // 检查是否有权限
        hasPermission := false
        for _, perm := range permissions {
            if perm.Code == permCode {
                hasPermission = true
                break
            }
        }
        
        if !hasPermission {
            // 无权限访问
            ctx.ResponseWriter.WriteHeader(403)
            ctx.WriteString("无权限访问")
            return
        }
        
        // 有权限，继续处理
        ctx.Next()
    }
}
```

### 5.3 权限检查工具函数

```go
// 检查用户是否有特定权限
func HasPermission(ctx context.Context, userID int64, userType string, permCode string) (bool, error) {
    permissionService := services.NewPermissionService()
    permissions, err := permissionService.GetUserPermissions(ctx, userID, userType)
    if err != nil {
        return false, err
    }
    
    for _, perm := range permissions {
        if perm.Code == permCode {
            return true, nil
        }
    }
    
    return false, nil
}
```

## 6. 最佳实践

1. **权限命名规范**：使用`模块:操作`的格式命名权限，如`product:create`、`order:view`等。

2. **权限分层**：按照系统级、模块级、功能级进行权限分层，便于管理和分配。

3. **最小权限原则**：为用户分配完成工作所需的最小权限集合。

4. **角色设计**：根据业务职责设计角色，避免角色过多或过少。

5. **权限缓存**：对频繁使用的权限信息进行缓存，提高系统性能。

6. **权限审计**：记录权限变更和权限使用日志，便于安全审计。