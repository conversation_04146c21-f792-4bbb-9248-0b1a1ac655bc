/**
 * permission模块初始化
 *
 * 本文件负责permission模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保permission模块的功能正常启动。
 */

package permission

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/permission/models"
	"o_mall_backend/modules/permission/routers"
)

// Init 初始化permission模块
func Init() {
	logs.Info("初始化permission模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitPermissionRouter()

	logs.Info("permission模块初始化完成")
}

// registerModels 注册模型
func registerModels() {
	logs.Info("注册permission模块ORM模型...")

	// 注册permission相关模型
	orm.RegisterModel(
		new(models.Permission),
		new(models.Role),
		new(models.RolePermission),
		new(models.UserRole),
	)
}
