/**
 * 权限模型
 *
 * 该文件定义了权限相关的数据结构和常量。
 * 包括权限类型、权限级别、权限状态等。
 */

package models

import (
	"time"
)

// PermissionType 权限类型
type PermissionType int

const (
	// PermissionTypeMenu 菜单权限
	PermissionTypeMenu PermissionType = 1
	// PermissionTypeButton 按钮权限
	PermissionTypeButton PermissionType = 2
	// PermissionTypeAPI API权限
	PermissionTypeAPI PermissionType = 3
)

// PermissionLevel 权限级别
type PermissionLevel int

const (
	// PermissionLevelSystem 系统级权限
	PermissionLevelSystem PermissionLevel = 1
	// PermissionLevelModule 模块级权限
	PermissionLevelModule PermissionLevel = 2
	// PermissionLevelFeature 功能级权限
	PermissionLevelFeature PermissionLevel = 3
)

// PermissionStatus 权限状态
type PermissionStatus int

const (
	// PermissionStatusDisabled 禁用
	PermissionStatusDisabled PermissionStatus = 0
	// PermissionStatusEnabled 启用
	PermissionStatusEnabled PermissionStatus = 1
)

// Permission 权限模型
type Permission struct {
	ID          int64            `orm:"column(id);pk;auto" json:"id"`                                 // 权限ID
	ParentID    int64            `orm:"column(parent_id)" json:"parent_id"`                           // 父权限ID
	Name        string           `orm:"column(name);size(50)" json:"name"`                            // 权限名称
	Code        string           `orm:"column(code);size(50)" json:"code"`                            // 权限编码
	Type        PermissionType   `orm:"column(type)" json:"type" description:"权限类型 1-菜单，2-按钮，3-API"`  // 权限类型
	Level       PermissionLevel  `orm:"column(level)" json:"level" description:"权限级别 1-系统，2-模块，3-功能"` // 权限级别
	Path        string           `orm:"column(path);size(200)" json:"path"`                           // 权限路径
	Component   string           `orm:"column(component);size(200)" json:"component"`                 // 前端组件
	Icon        string           `orm:"column(icon);size(50)" json:"icon"`                            // 图标
	Sort        int              `orm:"column(sort)" json:"sort"`                                     // 排序
	Status      PermissionStatus `orm:"column(status)" json:"status" description:"状态 0-禁用，1-启用"`      // 状态
	Description string           `orm:"column(description);size(200)" json:"description"`             // 描述
	CreatedAt   time.Time        `orm:"column(created_at);auto_now_add" json:"created_at"`            // 创建时间
	UpdatedAt   time.Time        `orm:"column(updated_at);auto_now" json:"updated_at"`                // 更新时间
}

// TableName 指定表名
func (p *Permission) TableName() string {
	return "permissions"
}

// Role 角色模型
type Role struct {
	ID          int64            `orm:"column(id);pk;auto" json:"id"`                      // 角色ID
	Name        string           `orm:"column(name);size(50)" json:"name"`                 // 角色名称
	Code        string           `orm:"column(code);size(50)" json:"code"`                 // 角色编码
	Status      PermissionStatus `orm:"column(status)" json:"status"`                      // 状态
	Description string           `orm:"column(description);size(200)" json:"description"`  // 描述
	CreatedAt   time.Time        `orm:"column(created_at);auto_now_add" json:"created_at"` // 创建时间
	UpdatedAt   time.Time        `orm:"column(updated_at);auto_now" json:"updated_at"`     // 更新时间
}

// TableName 指定表名
func (r *Role) TableName() string {
	return "roles"
}

// RolePermission 角色权限关联模型
type RolePermission struct {
	ID           int64     `orm:"column(id);pk;auto" json:"id"`                      // 关联ID
	RoleID       int64     `orm:"column(role_id)" json:"role_id"`                    // 角色ID
	PermissionID int64     `orm:"column(permission_id)" json:"permission_id"`        // 权限ID
	CreatedAt    time.Time `orm:"column(created_at);auto_now_add" json:"created_at"` // 创建时间
}

// TableName 指定表名
func (rp *RolePermission) TableName() string {
	return "role_permissions"
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID        int64     `orm:"column(id);pk;auto" json:"id"`                      // 关联ID
	UserID    int64     `orm:"column(user_id)" json:"user_id"`                    // 用户ID
	UserType  string    `orm:"column(user_type);size(20)" json:"user_type"`       // 用户类型：admin-管理员，merchant-商户，user-用户
	RoleID    int64     `orm:"column(role_id)" json:"role_id"`                    // 角色ID
	CreatedAt time.Time `orm:"column(created_at);auto_now_add" json:"created_at"` // 创建时间
}

// TableName 指定表名
func (ur *UserRole) TableName() string {
	return "user_roles"
}
