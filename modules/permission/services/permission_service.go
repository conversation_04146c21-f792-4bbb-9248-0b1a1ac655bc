/**
 * 权限服务实现
 *
 * 该文件实现了权限相关的服务接口。
 * 包括权限管理、角色管理、权限分配等功能的具体实现。
 */

package services

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/permission/dto"
	"o_mall_backend/modules/permission/models"
)

// PermissionServiceImpl 权限服务实现
type PermissionServiceImpl struct {
	o orm.Ormer
}

// NewPermissionService 创建权限服务实例
func NewPermissionService() PermissionService {
	return &PermissionServiceImpl{
		o: orm.NewOrm(),
	}
}

// ListPermissions 获取权限列表
func (s *PermissionServiceImpl) ListPermissions(ctx context.Context, req *dto.PermissionQueryRequest) ([]*dto.PermissionResponse, int64, error) {
	// 构建查询条件
	cond := orm.NewCondition()
	if req.Name != "" {
		cond = cond.And("name__icontains", req.Name)
	}
	if req.Code != "" {
		cond = cond.And("code__icontains", req.Code)
	}
	if req.Type != 0 {
		cond = cond.And("type", req.Type)
	}
	if req.Level != 0 {
		cond = cond.And("level", req.Level)
	}
	if req.Status != 0 {
		cond = cond.And("status", req.Status)
	}

	// 查询总数
	var permissions []*models.Permission
	total, err := s.o.QueryTable(new(models.Permission)).SetCond(cond).Count()
	if err != nil {
		logs.Error("[ListPermissions] 查询权限总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	_, err = s.o.QueryTable(new(models.Permission)).SetCond(cond).
		Offset((req.Page-1)*req.PageSize).
		Limit(req.PageSize).
		OrderBy("sort", "id").
		All(&permissions)
	if err != nil {
		logs.Error("[ListPermissions] 查询权限列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	var result []*dto.PermissionResponse
	for _, permission := range permissions {
		result = append(result, s.convertToPermissionResponse(permission))
	}

	return result, total, nil
}

// GetPermissionByID 获取权限详情
func (s *PermissionServiceImpl) GetPermissionByID(ctx context.Context, id int64) (*dto.PermissionResponse, error) {
	permission := &models.Permission{ID: id}
	err := s.o.Read(permission)
	if err == orm.ErrNoRows {
		return nil, fmt.Errorf("权限不存在")
	}
	if err != nil {
		logs.Error("[GetPermissionByID] 查询权限失败: %v", err)
		return nil, err
	}

	return s.convertToPermissionResponse(permission), nil
}

// CreatePermission 创建权限
func (s *PermissionServiceImpl) CreatePermission(ctx context.Context, req *dto.CreatePermissionRequest) error {
	permission := &models.Permission{
		ParentID:    req.ParentID,
		Name:        req.Name,
		Code:        req.Code,
		Type:        models.PermissionType(req.Type),
		Level:       models.PermissionLevel(req.Level),
		Path:        req.Path,
		Component:   req.Component,
		Icon:        req.Icon,
		Sort:        req.Sort,
		Status:      models.PermissionStatusEnabled,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err := s.o.Insert(permission)
	if err != nil {
		logs.Error("[CreatePermission] 创建权限失败: %v", err)
		return err
	}

	return nil
}

// UpdatePermission 更新权限
func (s *PermissionServiceImpl) UpdatePermission(ctx context.Context, req *dto.UpdatePermissionRequest) error {
	permission := &models.Permission{ID: req.ID}
	err := s.o.Read(permission)
	if err == orm.ErrNoRows {
		return fmt.Errorf("权限不存在")
	}
	if err != nil {
		logs.Error("[UpdatePermission] 查询权限失败: %v", err)
		return err
	}

	// 更新权限信息
	permission.ParentID = req.ParentID
	permission.Name = req.Name
	permission.Code = req.Code
	permission.Type = models.PermissionType(req.Type)
	permission.Level = models.PermissionLevel(req.Level)
	permission.Path = req.Path
	permission.Component = req.Component
	permission.Icon = req.Icon
	permission.Sort = req.Sort
	permission.Status = models.PermissionStatus(req.Status)
	permission.Description = req.Description
	permission.UpdatedAt = time.Now()

	_, err = s.o.Update(permission)
	if err != nil {
		logs.Error("[UpdatePermission] 更新权限失败: %v", err)
		return err
	}

	return nil
}

// DeletePermission 删除权限
func (s *PermissionServiceImpl) DeletePermission(ctx context.Context, id int64) error {
	permission := &models.Permission{ID: id}
	err := s.o.Read(permission)
	if err == orm.ErrNoRows {
		return fmt.Errorf("权限不存在")
	}
	if err != nil {
		logs.Error("[DeletePermission] 查询权限失败: %v", err)
		return err
	}

	// 检查是否有子权限
	var count int64
	count, err = s.o.QueryTable(new(models.Permission)).Filter("parent_id", id).Count()
	if err != nil {
		logs.Error("[DeletePermission] 查询子权限数量失败: %v", err)
		return err
	}
	if count > 0 {
		return fmt.Errorf("存在子权限，无法删除")
	}

	// 删除权限
	_, err = s.o.Delete(permission)
	if err != nil {
		logs.Error("[DeletePermission] 删除权限失败: %v", err)
		return err
	}

	return nil
}

// ListRoles 获取角色列表
func (s *PermissionServiceImpl) ListRoles(ctx context.Context, req *dto.RoleQueryRequest) ([]*dto.RoleResponse, int64, error) {
	// 构建查询条件
	cond := orm.NewCondition()
	if req.Name != "" {
		cond = cond.And("name__icontains", req.Name)
	}
	if req.Code != "" {
		cond = cond.And("code__icontains", req.Code)
	}
	if req.Status != 0 {
		cond = cond.And("status", req.Status)
	}

	// 查询总数
	var roles []*models.Role
	total, err := s.o.QueryTable(new(models.Role)).SetCond(cond).Count()
	if err != nil {
		logs.Error("[ListRoles] 查询角色总数失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	_, err = s.o.QueryTable(new(models.Role)).SetCond(cond).
		Offset((req.Page - 1) * req.PageSize).
		Limit(req.PageSize).
		OrderBy("id").
		All(&roles)
	if err != nil {
		logs.Error("[ListRoles] 查询角色列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	var result []*dto.RoleResponse
	for _, role := range roles {
		result = append(result, s.convertToRoleResponse(role))
	}

	return result, total, nil
}

// GetRoleByID 获取角色详情
func (s *PermissionServiceImpl) GetRoleByID(ctx context.Context, id int64) (*dto.RoleResponse, error) {
	role := &models.Role{ID: id}
	err := s.o.Read(role)
	if err == orm.ErrNoRows {
		return nil, fmt.Errorf("角色不存在")
	}
	if err != nil {
		logs.Error("[GetRoleByID] 查询角色失败: %v", err)
		return nil, err
	}

	return s.convertToRoleResponse(role), nil
}

// CreateRole 创建角色
func (s *PermissionServiceImpl) CreateRole(ctx context.Context, req *dto.CreateRoleRequest) error {
	role := &models.Role{
		Name:        req.Name,
		Code:        req.Code,
		Status:      models.PermissionStatusEnabled,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err := s.o.Insert(role)
	if err != nil {
		logs.Error("[CreateRole] 创建角色失败: %v", err)
		return err
	}

	return nil
}

// UpdateRole 更新角色
func (s *PermissionServiceImpl) UpdateRole(ctx context.Context, req *dto.UpdateRoleRequest) error {
	role := &models.Role{ID: req.ID}
	err := s.o.Read(role)
	if err == orm.ErrNoRows {
		return fmt.Errorf("角色不存在")
	}
	if err != nil {
		logs.Error("[UpdateRole] 查询角色失败: %v", err)
		return err
	}

	// 更新角色信息
	role.Name = req.Name
	role.Code = req.Code
	role.Status = models.PermissionStatus(req.Status)
	role.Description = req.Description
	role.UpdatedAt = time.Now()

	_, err = s.o.Update(role)
	if err != nil {
		logs.Error("[UpdateRole] 更新角色失败: %v", err)
		return err
	}

	return nil
}

// DeleteRole 删除角色
func (s *PermissionServiceImpl) DeleteRole(ctx context.Context, id int64) error {
	role := &models.Role{ID: id}
	err := s.o.Read(role)
	if err == orm.ErrNoRows {
		return fmt.Errorf("角色不存在")
	}
	if err != nil {
		logs.Error("[DeleteRole] 查询角色失败: %v", err)
		return err
	}

	// 检查是否有用户使用该角色
	var count int64
	count, err = s.o.QueryTable(new(models.UserRole)).Filter("role_id", id).Count()
	if err != nil {
		logs.Error("[DeleteRole] 查询用户角色关联数量失败: %v", err)
		return err
	}
	if count > 0 {
		return fmt.Errorf("存在用户使用该角色，无法删除")
	}

	// 删除角色
	_, err = s.o.Delete(role)
	if err != nil {
		logs.Error("[DeleteRole] 删除角色失败: %v", err)
		return err
	}

	return nil
}

// AssignRole 分配角色
func (s *PermissionServiceImpl) AssignRole(ctx context.Context, req *dto.AssignRoleRequest) error {
	// 检查角色是否存在
	role := &models.Role{ID: req.RoleID}
	err := s.o.Read(role)
	if err == orm.ErrNoRows {
		return fmt.Errorf("角色不存在")
	}
	if err != nil {
		logs.Error("[AssignRole] 查询角色失败: %v", err)
		return err
	}

	// 删除原有角色
	_, err = s.o.QueryTable(new(models.UserRole)).
		Filter("user_id", req.UserID).
		Filter("user_type", req.UserType).
		Delete()
	if err != nil {
		logs.Error("[AssignRole] 删除原有角色失败: %v", err)
		return err
	}

	// 分配新角色
	userRole := &models.UserRole{
		UserID:    req.UserID,
		UserType:  req.UserType,
		RoleID:    req.RoleID,
		CreatedAt: time.Now(),
	}

	_, err = s.o.Insert(userRole)
	if err != nil {
		logs.Error("[AssignRole] 分配角色失败: %v", err)
		return err
	}

	return nil
}

// AssignPermissions 分配权限
func (s *PermissionServiceImpl) AssignPermissions(ctx context.Context, req *dto.AssignPermissionRequest) error {
	// 检查角色是否存在
	role := &models.Role{ID: req.RoleID}
	err := s.o.Read(role)
	if err == orm.ErrNoRows {
		return fmt.Errorf("角色不存在")
	}
	if err != nil {
		logs.Error("[AssignPermissions] 查询角色失败: %v", err)
		return err
	}

	// 删除原有权限
	_, err = s.o.QueryTable(new(models.RolePermission)).
		Filter("role_id", req.RoleID).
		Delete()
	if err != nil {
		logs.Error("[AssignPermissions] 删除原有权限失败: %v", err)
		return err
	}

	// 分配新权限
	for _, permissionID := range req.PermissionIDs {
		rolePermission := &models.RolePermission{
			RoleID:       req.RoleID,
			PermissionID: permissionID,
			CreatedAt:    time.Now(),
		}

		_, err = s.o.Insert(rolePermission)
		if err != nil {
			logs.Error("[AssignPermissions] 分配权限失败: %v", err)
			return err
		}
	}

	return nil
}

// GetUserPermissions 获取用户权限列表
func (s *PermissionServiceImpl) GetUserPermissions(ctx context.Context, userID int64, userType string) ([]*dto.PermissionResponse, error) {
	// 获取用户角色
	var userRoles []*models.UserRole
	_, err := s.o.QueryTable(new(models.UserRole)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		All(&userRoles)
	if err != nil {
		logs.Error("[GetUserPermissions] 查询用户角色失败: %v", err)
		return nil, err
	}

	if len(userRoles) == 0 {
		return nil, nil
	}

	// 获取角色ID列表
	var roleIDs []int64
	for _, userRole := range userRoles {
		roleIDs = append(roleIDs, userRole.RoleID)
	}

	// 获取角色权限
	var rolePermissions []*models.RolePermission
	_, err = s.o.QueryTable(new(models.RolePermission)).
		Filter("role_id__in", roleIDs).
		All(&rolePermissions)
	if err != nil {
		logs.Error("[GetUserPermissions] 查询角色权限失败: %v", err)
		return nil, err
	}

	if len(rolePermissions) == 0 {
		return nil, nil
	}

	// 获取权限ID列表
	var permissionIDs []int64
	for _, rolePermission := range rolePermissions {
		permissionIDs = append(permissionIDs, rolePermission.PermissionID)
	}

	// 获取权限列表
	var permissions []*models.Permission
	_, err = s.o.QueryTable(new(models.Permission)).
		Filter("id__in", permissionIDs).
		Filter("status", models.PermissionStatusEnabled).
		OrderBy("sort", "id").
		All(&permissions)
	if err != nil {
		logs.Error("[GetUserPermissions] 查询权限列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	var result []*dto.PermissionResponse
	for _, permission := range permissions {
		result = append(result, s.convertToPermissionResponse(permission))
	}

	return result, nil
}

// GetUserRoles 获取用户角色列表
func (s *PermissionServiceImpl) GetUserRoles(ctx context.Context, userID int64, userType string) ([]*dto.RoleResponse, error) {
	// 获取用户角色
	var userRoles []*models.UserRole
	_, err := s.o.QueryTable(new(models.UserRole)).
		Filter("user_id", userID).
		Filter("user_type", userType).
		All(&userRoles)
	if err != nil {
		logs.Error("[GetUserRoles] 查询用户角色失败: %v", err)
		return nil, err
	}

	if len(userRoles) == 0 {
		return nil, nil
	}

	// 获取角色ID列表
	var roleIDs []int64
	for _, userRole := range userRoles {
		roleIDs = append(roleIDs, userRole.RoleID)
	}

	// 获取角色列表
	var roles []*models.Role
	_, err = s.o.QueryTable(new(models.Role)).
		Filter("id__in", roleIDs).
		Filter("status", models.PermissionStatusEnabled).
		OrderBy("id").
		All(&roles)
	if err != nil {
		logs.Error("[GetUserRoles] 查询角色列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	var result []*dto.RoleResponse
	for _, role := range roles {
		result = append(result, s.convertToRoleResponse(role))
	}

	return result, nil
}

// convertToPermissionResponse 将权限模型转换为响应对象
func (s *PermissionServiceImpl) convertToPermissionResponse(permission *models.Permission) *dto.PermissionResponse {
	return &dto.PermissionResponse{
		ID:          permission.ID,
		ParentID:    permission.ParentID,
		Name:        permission.Name,
		Code:        permission.Code,
		Type:        int(permission.Type),
		Level:       int(permission.Level),
		Path:        permission.Path,
		Component:   permission.Component,
		Icon:        permission.Icon,
		Sort:        permission.Sort,
		Status:      int(permission.Status),
		Description: permission.Description,
		CreatedAt:   permission.CreatedAt,
		UpdatedAt:   permission.UpdatedAt,
	}
}

// convertToRoleResponse 将角色模型转换为响应对象
func (s *PermissionServiceImpl) convertToRoleResponse(role *models.Role) *dto.RoleResponse {
	return &dto.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		Code:        role.Code,
		Status:      int(role.Status),
		Description: role.Description,
		CreatedAt:   role.CreatedAt,
		UpdatedAt:   role.UpdatedAt,
	}
}
