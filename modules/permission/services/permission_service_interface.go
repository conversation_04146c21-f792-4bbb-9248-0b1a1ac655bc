/**
 * 权限服务接口
 *
 * 该文件定义了权限相关的服务接口。
 * 包括权限管理、角色管理、权限分配等功能。
 */

package services

import (
	"context"

	"o_mall_backend/modules/permission/dto"
)

// PermissionService 权限服务接口
type PermissionService interface {
	// ListPermissions 获取权限列表
	ListPermissions(ctx context.Context, req *dto.PermissionQueryRequest) ([]*dto.PermissionResponse, int64, error)

	// GetPermissionByID 获取权限详情
	GetPermissionByID(ctx context.Context, id int64) (*dto.PermissionResponse, error)

	// CreatePermission 创建权限
	CreatePermission(ctx context.Context, req *dto.CreatePermissionRequest) error

	// UpdatePermission 更新权限
	UpdatePermission(ctx context.Context, req *dto.UpdatePermissionRequest) error

	// DeletePermission 删除权限
	DeletePermission(ctx context.Context, id int64) error

	// ListRoles 获取角色列表
	ListRoles(ctx context.Context, req *dto.RoleQueryRequest) ([]*dto.RoleResponse, int64, error)

	// GetRoleByID 获取角色详情
	GetRoleByID(ctx context.Context, id int64) (*dto.RoleResponse, error)

	// CreateRole 创建角色
	CreateRole(ctx context.Context, req *dto.CreateRoleRequest) error

	// UpdateRole 更新角色
	UpdateRole(ctx context.Context, req *dto.UpdateRoleRequest) error

	// DeleteRole 删除角色
	DeleteRole(ctx context.Context, id int64) error

	// AssignRole 分配角色
	AssignRole(ctx context.Context, req *dto.AssignRoleRequest) error

	// AssignPermissions 分配权限
	AssignPermissions(ctx context.Context, req *dto.AssignPermissionRequest) error

	// GetUserPermissions 获取用户权限列表
	GetUserPermissions(ctx context.Context, userID int64, userType string) ([]*dto.PermissionResponse, error)

	// GetUserRoles 获取用户角色列表
	GetUserRoles(ctx context.Context, userID int64, userType string) ([]*dto.RoleResponse, error)
}
