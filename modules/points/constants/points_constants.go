/**
 * 积分系统常量定义
 *
 * 本文件定义了积分系统中使用的常量，包括积分来源类型、积分操作类型等。
 * 便于系统中统一使用积分相关的常量值，提高代码可维护性。
 */

package constants

// 积分来源类型
const (
	// PointsSourceRegister 注册赠送积分
	PointsSourceRegister = "register"
	
	// PointsSourcePurchase 购物消费获得积分
	PointsSourcePurchase = "purchase"
	
	// PointsSourceReview 评价获得积分
	PointsSourceReview = "review"
	
	// PointsSourcePromotion 促销活动获得积分
	PointsSourcePromotion = "promotion"
	
	// PointsSourceAdmin 管理员调整积分
	PointsSourceAdmin = "admin"
	
	// PointsSourceExchange 积分兑换礼品卡
	PointsSourceExchange = "exchange"
)

// 积分操作类型
const (
	// PointsOperationAdd 增加积分
	PointsOperationAdd = "add"
	
	// PointsOperationDeduct 扣减积分
	PointsOperationDeduct = "deduct"
)

// 积分规则类型
const (
	// PointsRuleRegister 注册赠送积分规则
	PointsRuleRegister = "register"
	
	// PointsRulePurchase 购物获取积分规则
	PointsRulePurchase = "purchase"
	
	// PointsRuleReview 评价获取积分规则
	PointsRuleReview = "review"
)

// 积分兑换状态
const (
	// PointsExchangePending 兑换待处理
	PointsExchangePending = "pending"
	
	// PointsExchangeCompleted 兑换已完成
	PointsExchangeCompleted = "completed"
	
	// PointsExchangeFailed 兑换失败
	PointsExchangeFailed = "failed"
	
	// PointsExchangeCancelled 兑换已取消
	PointsExchangeCancelled = "cancelled"
)
