/**
 * 积分账户控制器
 *
 * 本文件实现了积分账户相关的API接口控制器，处理积分账户查询等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/points/services"
	"o_mall_backend/modules/points/services/impl"
)

// PointsAccountController 积分账户控制器
type PointsAccountController struct {
	web.Controller
	pointsService services.PointsService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *PointsAccountController) Prepare() {
	c.pointsService = impl.NewPointsService()
}

// GetAccount 获取用户积分账户信息
// @Title 获取用户积分账户信息
// @Description 获取当前登录用户的积分账户信息
// @Param userId query int true "用户ID"
// @Success 200 {object} dto.Response 成功返回积分账户信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /account [get]
func (c *PointsAccountController) GetAccount() {
	// 获取用户ID参数
	userIDStr := c.GetString("userId")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil || userID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取积分账户信息
	account, err := c.pointsService.GetPointsAccount(c.Ctx.Request.Context(), userID)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, account)
}
