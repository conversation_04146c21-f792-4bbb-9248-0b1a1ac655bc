/**
 * 积分操作控制器
 *
 * 本文件实现了积分操作相关的API接口控制器，处理积分增加、扣减等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"encoding/json"

	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/points/dto"
	"o_mall_backend/modules/points/services"
	"o_mall_backend/modules/points/services/impl"
)

// PointsOperationController 积分操作控制器
type PointsOperationController struct {
	web.Controller
	pointsService services.PointsService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *PointsOperationController) Prepare() {
	c.pointsService = impl.NewPointsService()
}

// AddPoints 增加积分
// @Title 增加积分
// @Description 为用户增加积分
// @Param body body dto.AddPointsRequest true "增加积分请求参数"
// @Success 200 {object} dto.Response 成功返回积分操作结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /add [post]
func (c *PointsOperationController) AddPoints() {
	// 解析请求参数
	var req dto.AddPointsRequest
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil || req.UserID <= 0 || req.Points <= 0 || req.Source == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务增加积分
	response, err := c.pointsService.AddPoints(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, response)
}

// DeductPoints 扣减积分
// @Title 扣减积分
// @Description 从用户账户扣减积分
// @Param body body dto.DeductPointsRequest true "扣减积分请求参数"
// @Success 200 {object} dto.Response 成功返回积分操作结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /deduct [post]
func (c *PointsOperationController) DeductPoints() {
	// 解析请求参数
	var req dto.DeductPointsRequest
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil || req.UserID <= 0 || req.Points <= 0 || req.Source == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务扣减积分
	response, err := c.pointsService.DeductPoints(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, response)
}
