/**
 * 积分规则控制器
 *
 * 本文件实现了积分规则相关的API接口控制器，处理积分规则的查询、保存、更新和删除等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/points/models"
	"o_mall_backend/modules/points/services"
	"o_mall_backend/modules/points/services/impl"
)

// PointsRuleController 积分规则控制器
type PointsRuleController struct {
	web.Controller
	pointsService services.PointsService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *PointsRuleController) Prepare() {
	c.pointsService = impl.NewPointsService()
}

// ListRules 获取积分规则列表
// @Title 获取积分规则列表
// @Description 获取所有积分规则
// @Success 200 {object} dto.Response 成功返回积分规则列表
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /rules [get]
func (c *PointsRuleController) ListRules() {
	// 调用服务获取积分规则列表
	rules, err := c.pointsService.GetPointsRules(c.Ctx.Request.Context())
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, rules)
}

// GetRule 获取指定类型的积分规则
// @Title 获取指定类型的积分规则
// @Description 根据规则类型获取积分规则
// @Param type path string true "规则类型"
// @Success 200 {object} dto.Response 成功返回积分规则
// @Failure 400 {object} dto.Response 参数错误
// @Failure 404 {object} dto.Response 规则不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /rules/:type [get]
func (c *PointsRuleController) GetRule() {
	// 获取规则类型参数
	ruleType := c.Ctx.Input.Param(":type")
	if ruleType == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取指定类型的积分规则
	rule, err := c.pointsService.GetPointsRule(c.Ctx.Request.Context(), ruleType)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 如果规则不存在
	if rule == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 返回结果
	result.OK(c.Ctx, rule)
}

// SaveRule 保存积分规则
// @Title 保存积分规则
// @Description 保存新的积分规则
// @Param body body models.PointsRule true "积分规则信息"
// @Success 200 {object} dto.Response 成功返回操作结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /rules [post]
func (c *PointsRuleController) SaveRule() {
	// 解析请求参数
	var rule models.PointsRule
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &rule)
	if err != nil || rule.RuleType == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务保存积分规则
	err = c.pointsService.SavePointsRule(c.Ctx.Request.Context(), &rule)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, map[string]interface{}{
		"success": true,
		"message": "积分规则保存成功",
	})
}

// UpdateRule 更新积分规则
// @Title 更新积分规则
// @Description 更新指定ID的积分规则
// @Param id path int true "规则ID"
// @Param body body models.PointsRule true "积分规则信息"
// @Success 200 {object} dto.Response 成功返回操作结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /rules/:id [put]
func (c *PointsRuleController) UpdateRule() {
	// 获取规则ID参数
	ruleIDStr := c.Ctx.Input.Param(":id")
	ruleID, err := strconv.ParseInt(ruleIDStr, 10, 64)
	if err != nil || ruleID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var rule models.PointsRule
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &rule)
	if err != nil || rule.RuleType == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 设置规则ID
	rule.ID = ruleID

	// 调用服务更新积分规则
	err = c.pointsService.SavePointsRule(c.Ctx.Request.Context(), &rule)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, map[string]interface{}{
		"success": true,
		"message": "积分规则更新成功",
	})
}

// DeleteRule 删除积分规则
// @Title 删除积分规则
// @Description 删除指定ID的积分规则
// @Param id path int true "规则ID"
// @Success 200 {object} dto.Response 成功返回操作结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /rules/:id [delete]
func (c *PointsRuleController) DeleteRule() {
	// 获取规则ID参数
	ruleIDStr := c.Ctx.Input.Param(":id")
	ruleID, err := strconv.ParseInt(ruleIDStr, 10, 64)
	if err != nil || ruleID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务删除积分规则
	err = c.pointsService.DeletePointsRule(c.Ctx.Request.Context(), ruleID)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, map[string]interface{}{
		"success": true,
		"message": "积分规则删除成功",
	})
}
