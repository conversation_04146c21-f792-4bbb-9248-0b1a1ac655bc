/**
 * 积分交易记录控制器
 *
 * 本文件实现了积分交易记录相关的API接口控制器，处理积分交易记录查询等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/points/dto"
	"o_mall_backend/modules/points/services"
	"o_mall_backend/modules/points/services/impl"
)

// PointsTransactionController 积分交易记录控制器
type PointsTransactionController struct {
	web.Controller
	pointsService services.PointsService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *PointsTransactionController) Prepare() {
	c.pointsService = impl.NewPointsService()
}

// List 获取积分交易记录列表
// @Title 获取积分交易记录列表
// @Description 获取用户的积分交易记录列表
// @Param userId query int true "用户ID"
// @Param page query int false "页码"
// @Param pageSize query int false "每页条数"
// @Param startTime query string false "开始时间"
// @Param endTime query string false "结束时间"
// @Param source query string false "积分来源"
// @Param type query string false "交易类型"
// @Success 200 {object} dto.Response 成功返回积分交易记录列表
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /transactions [get]
func (c *PointsTransactionController) List() {
	// 获取查询参数
	//userIDStr := c.GetString("userId")
	userID, err := c.GetInt64("userId")
	if err != nil || userID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 构建查询请求
	req := &dto.PointsTransactionQueryRequest{
		UserID:    userID,
		StartTime: c.GetString("startTime"),
		EndTime:   c.GetString("endTime"),
		Source:    c.GetString("source"),
		Type:      c.GetString("type"),
	}

	// 分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}
	req.Page = page
	req.PageSize = pageSize

	// 调用服务获取积分交易记录
	response, err := c.pointsService.GetPointsTransactions(c.Ctx.Request.Context(), req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, response)
}
