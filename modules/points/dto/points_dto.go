/**
 * 积分系统数据传输对象
 *
 * 本文件定义了积分系统相关的数据传输对象，用于接口请求和响应的数据结构。
 * 包括积分查询、积分操作、积分兑换等相关的请求和响应对象。
 */

package dto

import "time"

// 请求对象

// QueryPointsRequest 查询积分请求
type QueryPointsRequest struct {
	UserID int64 `json:"userId" validate:"required"` // 用户ID
}

// AddPointsRequest 添加积分请求
type AddPointsRequest struct {
	UserID      int64  `json:"userId" validate:"required"`      // 用户ID
	Points      int    `json:"points" validate:"required,gt=0"` // 积分数量
	Source      string `json:"source" validate:"required"`      // 积分来源
	Description string `json:"description"`                     // 描述
	OrderID     string `json:"orderId,omitempty"`              // 订单ID(如有)
	OperatorID  int64  `json:"operatorId,omitempty"`           // 操作人ID(如有)
}

// DeductPointsRequest 扣减积分请求
type DeductPointsRequest struct {
	UserID      int64  `json:"userId" validate:"required"`      // 用户ID
	Points      int    `json:"points" validate:"required,gt=0"` // 积分数量
	Source      string `json:"source" validate:"required"`      // 积分来源
	Description string `json:"description"`                     // 描述
	OrderID     string `json:"orderId,omitempty"`              // 订单ID(如有)
	OperatorID  int64  `json:"operatorId,omitempty"`           // 操作人ID(如有)
}

// PointsExchangeRequest 积分兑换请求
type PointsExchangeRequest struct {
	UserID       int64  `json:"userId" validate:"required"`      // 用户ID
	PointsAmount int    `json:"pointsAmount" validate:"required,gt=0"` // 积分数量
	ExchangeType string `json:"exchangeType" validate:"required"`      // 兑换类型
	ExchangeID   int64  `json:"exchangeId,omitempty"`                 // 兑换物品ID
	Remark       string `json:"remark,omitempty"`                     // 备注
}

// PointsTransactionQueryRequest 积分交易记录查询请求
type PointsTransactionQueryRequest struct {
	UserID    int64  `json:"userId" validate:"required"`     // 用户ID
	StartTime string `json:"startTime,omitempty"`           // 开始时间
	EndTime   string `json:"endTime,omitempty"`             // 结束时间
	Source    string `json:"source,omitempty"`              // 积分来源
	Type      string `json:"type,omitempty"`                // 交易类型
	Page      int    `json:"page,omitempty" default:"1"`    // 页码
	PageSize  int    `json:"pageSize,omitempty" default:"10"` // 每页条数
}

// 响应对象

// PointsAccountResponse 积分账户响应
type PointsAccountResponse struct {
	UserID       int64     `json:"userId"`       // 用户ID
	Balance      int       `json:"balance"`      // 当前积分余额
	TotalEarned  int       `json:"totalEarned"`  // 历史总获得积分
	TotalUsed    int       `json:"totalUsed"`    // 历史总使用积分
	LastActivity time.Time `json:"lastActivity"` // 最近一次积分活动时间
	ExpirePoints int       `json:"expirePoints"` // 即将过期的积分数量
	ExpireTime   time.Time `json:"expireTime"`   // 积分过期时间
}

// PointsOperationResponse 积分操作响应
type PointsOperationResponse struct {
	Success      bool   `json:"success"`      // 操作是否成功
	UserID       int64  `json:"userId"`       // 用户ID
	CurrentPoint int    `json:"currentPoint"` // 当前积分余额
	ChangePoint  int    `json:"changePoint"`  // 变动积分
	Message      string `json:"message"`      // 操作信息
}

// PointsTransactionResponse 积分交易记录响应
type PointsTransactionResponse struct {
	ID          int64     `json:"id"`          // 交易记录ID
	UserID      int64     `json:"userId"`      // 用户ID
	Amount      int       `json:"amount"`      // 交易积分数量
	Balance     int       `json:"balance"`     // 交易后余额
	Type        string    `json:"type"`        // 交易类型(add/deduct)
	Source      string    `json:"source"`      // 积分来源
	Description string    `json:"description"` // 交易描述
	OrderID     string    `json:"orderId"`     // 关联订单ID(如适用)
	CreatedAt   time.Time `json:"createdAt"`   // 创建时间
}

// PointsExchangeResponse 积分兑换响应
type PointsExchangeResponse struct {
	ExchangeID   int64     `json:"exchangeId"`   // 兑换ID
	UserID       int64     `json:"userId"`       // 用户ID
	PointsAmount int       `json:"pointsAmount"` // 兑换的积分数量
	ExchangeType string    `json:"exchangeType"` // 兑换类型
	Status       string    `json:"status"`       // 兑换状态
	CreatedAt    time.Time `json:"createdAt"`    // 创建时间
}

// PointsTransactionListResponse 积分交易记录列表响应
type PointsTransactionListResponse struct {
	Total       int                        `json:"total"`       // 总记录数
	CurrentPage int                        `json:"currentPage"` // 当前页码
	PageSize    int                        `json:"pageSize"`    // 每页条数
	Data        []PointsTransactionResponse `json:"data"`        // 数据列表
}
