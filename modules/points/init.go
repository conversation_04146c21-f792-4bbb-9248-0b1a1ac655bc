/**
 * points模块初始化
 *
 * 本文件负责points模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保points模块的功能正常启动。
 */

package points

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/points/models"
	"o_mall_backend/modules/points/routers"
)

// Init 初始化points模块
func Init() {
	logs.Info("初始化points模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitRouters()

	logs.Info("points模块初始化完成")
}

// registerModels 注册模型
func registerModels() {
	logs.Info("注册points模块ORM模型...")

	// 注册points相关模型
	orm.RegisterModel(
		new(models.PointsAccount),
		new(models.PointsTransaction),
		new(models.PointsRule),
		new(models.PointsExchange),
	)
}
