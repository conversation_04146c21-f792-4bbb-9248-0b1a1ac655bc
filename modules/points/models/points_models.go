/**
 * 积分系统数据库模型
 *
 * 本文件定义了积分系统相关的数据库模型，包括用户积分账户、积分记录、积分规则等。
 * 每个模型都包含详细的字段注释，便于理解数据结构和业务含义。
 */

package models

import (
	"time"
)

// PointsAccount 用户积分账户
type PointsAccount struct {
	ID           int64     `orm:"pk;auto;description(积分账户ID，系统自动生成的主键)" json:"id"`            // 积分账户ID，主键
	UserID       int64     `orm:"index;description(关联的用户ID，外键关联用户表)" json:"userId"`           // 用户ID，外键关联用户表
	Balance      int       `orm:"default(0);description(用户当前可用积分余额)" json:"balance"`          // 当前积分余额
	TotalEarned  int       `orm:"default(0);description(用户历史累计获得的总积分)" json:"totalEarned"`    // 历史总获得积分
	TotalUsed    int       `orm:"default(0);description(用户历史累计使用的总积分)" json:"totalUsed"`      // 历史总使用积分
	LastActivity time.Time `orm:"auto_now;description(最近一次积分变动的时间)" json:"lastActivity"`      // 最近一次积分活动时间
	ExpirePoints int       `orm:"default(0);description(即将在有效期内过期的积分数量)" json:"expirePoints"` // 即将过期的积分数量
	ExpireTime   time.Time `orm:"null;description(当前积分的过期时间)" json:"expireTime"`              // 积分过期时间
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);description(记录创建时间)" json:"-"`   // 创建时间
	UpdatedAt    time.Time `orm:"auto_now;type(datetime);description(记录最后更新时间)" json:"-"`     // 更新时间
}

// PointsTransaction 积分交易记录
type PointsTransaction struct {
	ID          int64     `orm:"pk;auto;description(积分交易记录的唯一标识，自增主键)" json:"id"`                                // 交易记录ID，主键
	AccountID   int64     `orm:"index;description(关联的积分账户ID)" json:"accountId"`                                  // 积分账户ID，关联积分账户表
	UserID      int64     `orm:"index;description(交易所属用户ID，冗余字段方便查询)" json:"userId"`                             // 用户ID，冗余字段方便查询
	Amount      int       `orm:"default(0);description(本次交易的积分数量，正数表示增加，负数表示减少)" json:"amount"`                  // 交易积分数量
	Balance     int       `orm:"default(0);description(交易完成后的账户积分余额)" json:"balance"`                            // 交易后余额
	Type        string    `orm:"size(20);index;description(交易类型：add增加/deduct扣减)" json:"type"`                    // 交易类型(add/deduct)
	Source      string    `orm:"size(50);index;description(积分来源：purchase购物/register注册/review评价等)" json:"source"` // 积分来源(purchase/register/review等)
	Description string    `orm:"size(255);description(交易的详细描述信息)" json:"description"`                            // 交易描述
	OrderID     string    `orm:"size(50);null;description(关联的订单编号，购物获得积分时使用)" json:"orderId"`                    // 关联订单ID(如适用)
	Remark      string    `orm:"size(255);null;description(交易的补充说明信息)" json:"remark"`                            // 备注信息
	OperatorID  int64     `orm:"null;description(执行该操作的管理员ID，系统自动操作时为空)" json:"operatorId"`                      // 操作人ID(管理员操作时)
	ExpireTime  time.Time `orm:"null;description(本次交易积分的过期时间)" json:"expireTime"`                                // 积分过期时间(如适用)
	CreatedAt   time.Time `orm:"auto_now_add;type(datetime);index;description(交易记录的创建时间)" json:"-"`              // 创建时间
	UpdatedAt   time.Time `orm:"auto_now;type(datetime);description(交易记录的最后更新时间)" json:"-"`                      // 更新时间
}

// PointsRule 积分规则配置
type PointsRule struct {
	ID             int64     `orm:"pk;auto;description(积分规则的唯一标识，自增主键)" json:"id"`                                     // 规则ID，主键
	RuleType       string    `orm:"size(50);unique;description(规则类型：register注册/purchase购物/review评价等)" json:"ruleType"` // 规则类型(注册/购物/评价等)
	PointsValue    int       `orm:"default(0);description(积分值：获取积分数量或兑换比例)" json:"pointsValue"`                        // 积分值或兑换比例
	Description    string    `orm:"size(255);description(规则的详细描述信息)" json:"description"`                               // 规则描述
	IsEnabled      bool      `orm:"default(true);description(规则是否启用：true启用/false禁用)" json:"isEnabled"`                 // 是否启用
	ValidStartTime time.Time `orm:"type(datetime);null;description(规则生效的开始时间)" json:"validStartTime"`                  // 有效开始时间
	ValidEndTime   time.Time `orm:"type(datetime);null;description(规则生效的结束时间)" json:"validEndTime"`                    // 有效结束时间
	ExpiryDays     int       `orm:"default(365);description(通过该规则获得的积分的有效期天数)" json:"expiryDays"`                      // 积分有效期(天)
	CreatedAt      time.Time `orm:"auto_now_add;type(datetime);description(规则创建时间)" json:"-"`                          // 创建时间
	UpdatedAt      time.Time `orm:"auto_now;type(datetime);description(规则最后更新时间)" json:"-"`                            // 更新时间
}

// PointsExchange 积分兑换记录
type PointsExchange struct {
	ID           int64     `orm:"pk;auto;description(积分兑换记录ID，系统自动生成的主键)" json:"id"`                 // 兑换ID，主键
	UserID       int64     `orm:"index;description(关联的用户ID，用于标识兑换记录所属用户)" json:"userId"`             // 用户ID
	PointsAmount int       `orm:"default(0);description(本次兑换使用的积分数量)" json:"pointsAmount"`           // 兑换的积分数量
	ExchangeType string    `orm:"size(50);description(兑换商品的类型，如礼品卡、商品、服务等)" json:"exchangeType"`     // 兑换类型(礼品卡/商品/服务等)
	ExchangeID   int64     `orm:"null;description(兑换商品的ID，关联到具体的兑换物品)" json:"exchangeId"`            // 兑换物品ID
	Status       string    `orm:"size(20);index;description(兑换记录的当前状态)" json:"status"`               // 兑换状态
	Remark       string    `orm:"size(255);null;description(兑换交易的补充说明信息)" json:"remark"`             // 备注信息
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);index;description(兑换记录的创建时间)" json:"-"` // 创建时间
	UpdatedAt    time.Time `orm:"auto_now;type(datetime);description(兑换记录的最后更新时间)" json:"-"`         // 更新时间
}

// 移除这里的init函数，因为模型已经在模块的init.go中注册
