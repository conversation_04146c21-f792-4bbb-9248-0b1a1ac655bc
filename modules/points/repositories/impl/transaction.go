/**
 * transaction.go
 * 事务管理器实现
 *
 * 本文件实现了事务管理器接口，用于处理积分模块的数据库事务
 */

package impl

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/points/core"
)

// TransactionManager Beego ORM事务管理器
type TransactionManager struct {
	ormer orm.Ormer
	tx    orm.TxOrmer
}

var _ core.Transaction = (*TransactionManager)(nil)

// NewTransactionManager 创建事务管理器
func NewTransactionManager() core.Transaction {
	return &TransactionManager{
		ormer: orm.NewOrm(),
	}
}

// Begin 开始事务
func (t *TransactionManager) Begin() error {
	tx, err := t.ormer.Begin()
	if err != nil {
		logs.Error("[TransactionManager] 开启事务失败: %v", err)
		return err
	}
	t.tx = tx
	return nil
}

// Commit 提交事务
func (t *TransactionManager) Commit() error {
	if t.tx == nil {
		return nil
	}
	err := t.tx.Commit()
	if err != nil {
		logs.Error("[TransactionManager] 提交事务失败: %v", err)
		return err
	}
	t.tx = nil
	return nil
}

// Rollback 回滚事务
func (t *TransactionManager) Rollback() error {
	if t.tx == nil {
		return nil
	}
	err := t.tx.Rollback()
	if err != nil {
		logs.Error("[TransactionManager] 回滚事务失败: %v", err)
		return err
	}
	t.tx = nil
	return nil
}

// GetTx 获取事务对象
func (t *TransactionManager) GetTx() interface{} {
	if t.tx != nil {
		return t.tx
	}
	return t.ormer
}
