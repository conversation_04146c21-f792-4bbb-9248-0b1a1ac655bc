/**
 * 积分模块路由配置
 *
 * 本文件负责配置积分模块的API路由，将请求映射到对应的控制器处理函数。
 * 所有积分相关的路由都在此处注册，保持路由结构清晰。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/points/controllers"
)

// InitRouters 初始化积分模块路由
func InitRouters() {
	// 积分账户相关路由
	web.Router("/api/v1/points/account", &controllers.PointsAccountController{}, "get:GetAccount")

	// 积分交易记录相关路由
	web.Router("/api/v1/points/transactions", &controllers.PointsTransactionController{}, "get:List")

	// 积分操作相关路由
	web.Router("/api/v1/points/add", &controllers.PointsOperationController{}, "post:AddPoints")
	web.Router("/api/v1/points/deduct", &controllers.PointsOperationController{}, "post:DeductPoints")

	// 积分兑换相关路由
	web.Router("/api/v1/points/exchange", &controllers.PointsExchangeController{}, "post:Exchange")
	web.Router("/api/v1/points/exchange/records", &controllers.PointsExchangeController{}, "get:ListExchangeRecords")

	// 积分规则相关路由
	web.Router("/api/v1/points/rules", &controllers.PointsRuleController{}, "get:ListRules")
	web.Router("/api/v1/points/rules/:type", &controllers.PointsRuleController{}, "get:GetRule")

	// 管理员路由
	web.Router("/api/v1/admin/points/rules", &controllers.PointsRuleController{}, "post:SaveRule;get:ListRules")
	web.Router("/api/v1/admin/points/rules/:id", &controllers.PointsRuleController{}, "put:UpdateRule;delete:DeleteRule")
}
