/**
 * 积分服务实现
 *
 * 本文件实现了积分系统的服务层接口，提供积分账户管理、积分操作、积分规则管理等功能的具体实现。
 * 包括积分的增加、扣减、查询、兑换等核心业务逻辑。
 */

package impl

import (
	context "context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/points/constants"
	"o_mall_backend/modules/points/dto"
	"o_mall_backend/modules/points/models"
	"o_mall_backend/modules/points/repositories/impl"
	"o_mall_backend/modules/points/services"
)

// PointsServiceImpl 积分服务实现
type PointsServiceImpl struct{}

// NewPointsService 创建积分服务实例
func NewPointsService() services.PointsService {
	return &PointsServiceImpl{}
}

// GetPointsAccount 获取用户积分账户信息
func (s *PointsServiceImpl) GetPointsAccount(ctx context.Context, userID int64) (*dto.PointsAccountResponse, error) {
	if userID <= 0 {
		return nil, errors.New("无效的用户ID")
	}

	o := orm.NewOrm()
	account := &models.PointsAccount{UserID: userID}
	err := o.QueryTable(new(models.PointsAccount)).Filter("user_id", userID).One(account)

	// 如果账户不存在，则创建新账户
	if err == orm.ErrNoRows {
		account.UserID = userID
		account.Balance = 0
		account.TotalEarned = 0
		account.TotalUsed = 0
		account.LastActivity = time.Now()
		account.ExpirePoints = 0
		account.ExpireTime = time.Now().AddDate(1, 0, 0) // 默认积分有效期为1年

		_, err = o.Insert(account)
		if err != nil {
			logs.Error("创建积分账户失败: %v", err)
			return nil, fmt.Errorf("创建积分账户失败: %v", err)
		}
	} else if err != nil {
		logs.Error("查询积分账户失败: %v", err)
		return nil, fmt.Errorf("查询积分账户失败: %v", err)
	}

	// 转换为响应对象
	response := &dto.PointsAccountResponse{
		UserID:       account.UserID,
		Balance:      account.Balance,
		TotalEarned:  account.TotalEarned,
		TotalUsed:    account.TotalUsed,
		LastActivity: account.LastActivity,
		ExpirePoints: account.ExpirePoints,
		ExpireTime:   account.ExpireTime,
	}

	return response, nil
}

// AddPoints 增加积分
func (s *PointsServiceImpl) AddPoints(ctx context.Context, req *dto.AddPointsRequest) (*dto.PointsOperationResponse, error) {
	if req == nil || req.UserID <= 0 || req.Points <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 创建事务管理器
	tx := impl.NewTransactionManager()
	err := tx.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("开始事务失败: %v", err)
	}

	// 获取事务对象
	o := tx.GetTx().(orm.Ormer)

	// 查询或创建积分账户
	account := &models.PointsAccount{UserID: req.UserID}
	err = o.QueryTable(new(models.PointsAccount)).Filter("user_id", req.UserID).One(account)

	if err == orm.ErrNoRows {
		// 创建新账户
		account.UserID = req.UserID
		account.Balance = 0
		account.TotalEarned = 0
		account.TotalUsed = 0
		account.LastActivity = time.Now()
		account.ExpirePoints = 0

		// 获取积分有效期规则
		var expiryDays int = 365 // 默认一年
		rule, _ := s.GetPointsRule(ctx, req.Source)
		if rule != nil && rule.ExpiryDays > 0 {
			expiryDays = rule.ExpiryDays
		}

		account.ExpireTime = time.Now().AddDate(0, 0, expiryDays)

		_, err = o.Insert(account)
		if err != nil {
			tx.Rollback()
			logs.Error("创建积分账户失败: %v", err)
			return nil, fmt.Errorf("创建积分账户失败: %v", err)
		}
	} else if err != nil {
		tx.Rollback()
		logs.Error("查询积分账户失败: %v", err)
		return nil, fmt.Errorf("查询积分账户失败: %v", err)
	}

	// 更新积分账户
	account.Balance += req.Points
	account.TotalEarned += req.Points
	account.LastActivity = time.Now()

	_, err = o.Update(account)
	if err != nil {
		tx.Rollback()
		logs.Error("更新积分账户失败: %v", err)
		return nil, fmt.Errorf("更新积分账户失败: %v", err)
	}

	// 创建积分交易记录
	transaction := &models.PointsTransaction{
		AccountID:   account.ID,
		UserID:      req.UserID,
		Amount:      req.Points,
		Balance:     account.Balance,
		Type:        constants.PointsOperationAdd,
		Source:      req.Source,
		Description: req.Description,
		OrderID:     req.OrderID,
		OperatorID:  req.OperatorID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 设置过期时间
	var expiryDays int = 365 // 默认一年
	rule, _ := s.GetPointsRule(ctx, req.Source)
	if rule != nil && rule.ExpiryDays > 0 {
		expiryDays = rule.ExpiryDays
	}
	transaction.ExpireTime = time.Now().AddDate(0, 0, expiryDays)

	_, err = o.Insert(transaction)
	if err != nil {
		tx.Rollback()
		logs.Error("创建积分交易记录失败: %v", err)
		return nil, fmt.Errorf("创建积分交易记录失败: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 返回响应
	response := &dto.PointsOperationResponse{
		Success:      true,
		UserID:       req.UserID,
		CurrentPoint: account.Balance,
		ChangePoint:  req.Points,
		Message:      fmt.Sprintf("已成功增加 %d 积分", req.Points),
	}

	return response, nil
}

// DeductPoints 扣减积分
func (s *PointsServiceImpl) DeductPoints(ctx context.Context, req *dto.DeductPointsRequest) (*dto.PointsOperationResponse, error) {
	if req == nil || req.UserID <= 0 || req.Points <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 创建事务管理器
	tx := impl.NewTransactionManager()
	err := tx.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, fmt.Errorf("开始事务失败: %v", err)
	}

	// 获取事务对象
	o := tx.GetTx().(orm.Ormer)

	// 查询积分账户
	account := &models.PointsAccount{}
	err = o.QueryTable(new(models.PointsAccount)).Filter("user_id", req.UserID).One(account)

	if err == orm.ErrNoRows {
		tx.Rollback()
		return nil, errors.New("积分账户不存在")
	} else if err != nil {
		tx.Rollback()
		logs.Error("查询积分账户失败: %v", err)
		return nil, fmt.Errorf("查询积分账户失败: %v", err)
	}

	// 检查积分余额是否足够
	if account.Balance < req.Points {
		tx.Rollback()
		return nil, errors.New("积分余额不足")
	}

	// 更新积分账户
	account.Balance -= req.Points
	account.TotalUsed += req.Points
	account.LastActivity = time.Now()

	_, err = o.Update(account)
	if err != nil {
		tx.Rollback()
		logs.Error("更新积分账户失败: %v", err)
		return nil, fmt.Errorf("更新积分账户失败: %v", err)
	}

	// 创建积分交易记录
	transaction := &models.PointsTransaction{
		AccountID:   account.ID,
		UserID:      req.UserID,
		Amount:      req.Points,
		Balance:     account.Balance,
		Type:        constants.PointsOperationDeduct,
		Source:      req.Source,
		Description: req.Description,
		OrderID:     req.OrderID,
		OperatorID:  req.OperatorID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	_, err = o.Insert(transaction)
	if err != nil {
		tx.Rollback()
		logs.Error("创建积分交易记录失败: %v", err)
		return nil, fmt.Errorf("创建积分交易记录失败: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v", err)
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 返回响应
	response := &dto.PointsOperationResponse{
		Success:      true,
		UserID:       req.UserID,
		CurrentPoint: account.Balance,
		ChangePoint:  -req.Points,
		Message:      fmt.Sprintf("已成功扣减 %d 积分", req.Points),
	}

	return response, nil
}

// GetPointsTransactions 获取积分交易记录
func (s *PointsServiceImpl) GetPointsTransactions(ctx context.Context, req *dto.PointsTransactionQueryRequest) (*dto.PointsTransactionListResponse, error) {
	if req == nil || req.UserID <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	o := orm.NewOrm()
	qb, _ := orm.NewQueryBuilder("mysql")

	// 构建查询条件
	qb.Select("*").From("points_transaction").Where("user_id = ?")

	params := []interface{}{req.UserID}

	if req.Source != "" {
		qb.And("source = ?")
		params = append(params, req.Source)
	}

	if req.Type != "" {
		qb.And("type = ?")
		params = append(params, req.Type)
	}

	if req.StartTime != "" {
		qb.And("created_at >= ?")
		params = append(params, req.StartTime)
	}

	if req.EndTime != "" {
		qb.And("created_at <= ?")
		params = append(params, req.EndTime)
	}

	// 计算总数
	countSQL := qb.String()
	var count int64
	err := o.Raw(fmt.Sprintf("SELECT COUNT(*) FROM (%s) as t", countSQL), params...).QueryRow(&count)
	if err != nil {
		logs.Error("查询积分交易记录总数失败: %v", err)
		return nil, fmt.Errorf("查询积分交易记录总数失败: %v", err)
	}

	// 分页查询
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	qb.OrderBy("created_at DESC").Limit(pageSize).Offset(offset)

	// 执行查询
	sql := qb.String()
	var transactions []*models.PointsTransaction
	_, err = o.Raw(sql, params...).QueryRows(&transactions)
	if err != nil {
		logs.Error("查询积分交易记录失败: %v", err)
		return nil, fmt.Errorf("查询积分交易记录失败: %v", err)
	}

	// 转换为响应对象
	var transactionResponses []dto.PointsTransactionResponse
	for _, transaction := range transactions {
		transactionResponse := dto.PointsTransactionResponse{
			ID:          transaction.ID,
			UserID:      transaction.UserID,
			Amount:      transaction.Amount,
			Balance:     transaction.Balance,
			Type:        transaction.Type,
			Source:      transaction.Source,
			Description: transaction.Description,
			OrderID:     transaction.OrderID,
			CreatedAt:   transaction.CreatedAt,
		}
		transactionResponses = append(transactionResponses, transactionResponse)
	}

	// 返回响应
	response := &dto.PointsTransactionListResponse{
		Total:       int(count),
		CurrentPage: page,
		PageSize:    pageSize,
		Data:        transactionResponses,
	}

	return response, nil
}

// ExchangePoints 积分兑换
func (s *PointsServiceImpl) ExchangePoints(ctx context.Context, req *dto.PointsExchangeRequest) (*dto.PointsExchangeResponse, error) {
	if req == nil || req.UserID <= 0 || req.PointsAmount <= 0 || req.ExchangeType == "" {
		return nil, errors.New("无效的请求参数")
	}

	// 先扣减积分
	deductReq := &dto.DeductPointsRequest{
		UserID:      req.UserID,
		Points:      req.PointsAmount,
		Source:      constants.PointsSourceExchange,
		Description: fmt.Sprintf("积分兑换%s", req.ExchangeType),
	}

	_, err := s.DeductPoints(ctx, deductReq)
	if err != nil {
		logs.Error("扣减积分失败: %v", err)
		return nil, fmt.Errorf("扣减积分失败: %v", err)
	}

	o := orm.NewOrm()

	// 创建积分兑换记录
	exchange := &models.PointsExchange{
		UserID:       req.UserID,
		PointsAmount: req.PointsAmount,
		ExchangeType: req.ExchangeType,
		ExchangeID:   req.ExchangeID,
		Status:       constants.PointsExchangePending,
		Remark:       req.Remark,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 插入记录
	id, err := o.Insert(exchange)
	if err != nil {
		// 如果创建兑换记录失败，需要回滚积分
		addReq := &dto.AddPointsRequest{
			UserID:      req.UserID,
			Points:      req.PointsAmount,
			Source:      constants.PointsSourceExchange,
			Description: "兑换失败，退回积分",
		}
		s.AddPoints(ctx, addReq)

		logs.Error("创建积分兑换记录失败: %v", err)
		return nil, fmt.Errorf("创建积分兑换记录失败: %v", err)
	}

	// 返回响应
	response := &dto.PointsExchangeResponse{
		ExchangeID:   id,
		UserID:       req.UserID,
		PointsAmount: req.PointsAmount,
		ExchangeType: req.ExchangeType,
		Status:       constants.PointsExchangePending,
		CreatedAt:    exchange.CreatedAt,
	}

	return response, nil
}

// GetPointsRules 获取积分规则
func (s *PointsServiceImpl) GetPointsRules(ctx context.Context) ([]*models.PointsRule, error) {
	o := orm.NewOrm()
	var rules []*models.PointsRule

	_, err := o.QueryTable(new(models.PointsRule)).All(&rules)
	if err != nil {
		logs.Error("查询积分规则失败: %v", err)
		return nil, fmt.Errorf("查询积分规则失败: %v", err)
	}

	return rules, nil
}

// GetPointsRule 获取指定类型的积分规则
func (s *PointsServiceImpl) GetPointsRule(ctx context.Context, ruleType string) (*models.PointsRule, error) {
	if ruleType == "" {
		return nil, errors.New("规则类型不能为空")
	}

	o := orm.NewOrm()
	rule := &models.PointsRule{RuleType: ruleType}

	err := o.QueryTable(new(models.PointsRule)).Filter("rule_type", ruleType).One(rule)
	if err == orm.ErrNoRows {
		return nil, nil
	}

	if err != nil {
		logs.Error("查询积分规则失败: %v", err)
		return nil, fmt.Errorf("查询积分规则失败: %v", err)
	}

	return rule, nil
}

// SavePointsRule 保存积分规则
func (s *PointsServiceImpl) SavePointsRule(ctx context.Context, rule *models.PointsRule) error {
	if rule == nil || rule.RuleType == "" {
		return errors.New("无效的规则参数")
	}

	o := orm.NewOrm()

	// 检查规则是否存在
	existingRule := &models.PointsRule{RuleType: rule.RuleType}
	err := o.QueryTable(new(models.PointsRule)).Filter("rule_type", rule.RuleType).One(existingRule)

	if err == orm.ErrNoRows {
		// 创建新规则
		rule.CreatedAt = time.Now()
		rule.UpdatedAt = time.Now()
		_, err = o.Insert(rule)
	} else if err != nil {
		logs.Error("查询积分规则失败: %v", err)
		return fmt.Errorf("查询积分规则失败: %v", err)
	} else {
		// 更新现有规则
		existingRule.PointsValue = rule.PointsValue
		existingRule.Description = rule.Description
		existingRule.IsEnabled = rule.IsEnabled
		existingRule.ValidStartTime = rule.ValidStartTime
		existingRule.ValidEndTime = rule.ValidEndTime
		existingRule.ExpiryDays = rule.ExpiryDays
		existingRule.UpdatedAt = time.Now()

		_, err = o.Update(existingRule)
	}

	if err != nil {
		logs.Error("保存积分规则失败: %v", err)
		return fmt.Errorf("保存积分规则失败: %v", err)
	}

	return nil
}

// DeletePointsRule 删除积分规则
func (s *PointsServiceImpl) DeletePointsRule(ctx context.Context, ruleID int64) error {
	if ruleID <= 0 {
		return errors.New("无效的规则ID")
	}

	o := orm.NewOrm()

	// 检查规则是否存在
	rule := &models.PointsRule{ID: ruleID}
	err := o.Read(rule)

	if err == orm.ErrNoRows {
		return errors.New("规则不存在")
	}

	if err != nil {
		logs.Error("查询积分规则失败: %v", err)
		return fmt.Errorf("查询积分规则失败: %v", err)
	}

	// 删除规则
	_, err = o.Delete(rule)
	if err != nil {
		logs.Error("删除积分规则失败: %v", err)
		return fmt.Errorf("删除积分规则失败: %v", err)
	}

	return nil
}

// CalculatePurchasePoints 计算订单可获得的积分
func (s *PointsServiceImpl) CalculatePurchasePoints(ctx context.Context, orderAmount float64) (int, error) {
	if orderAmount <= 0 {
		return 0, nil
	}

	// 获取购物积分规则
	rule, err := s.GetPointsRule(ctx, constants.PointsRulePurchase)
	if err != nil {
		logs.Error("获取积分规则失败: %v", err)
		return 0, fmt.Errorf("获取积分规则失败: %v", err)
	}

	if rule == nil || !rule.IsEnabled {
		return 0, nil
	}

	// 检查规则是否在有效期内
	now := time.Now()
	if !rule.ValidStartTime.IsZero() && now.Before(rule.ValidStartTime) {
		return 0, nil
	}

	if !rule.ValidEndTime.IsZero() && now.After(rule.ValidEndTime) {
		return 0, nil
	}

	// 计算积分 (默认每10元1积分)
	pointsValue := rule.PointsValue
	if pointsValue <= 0 {
		pointsValue = 1
	}

	points := int(orderAmount * float64(pointsValue) / 10)

	return points, nil
}

// ProcessOrderPoints 处理订单积分（下单获取积分）
func (s *PointsServiceImpl) ProcessOrderPoints(ctx context.Context, userID int64, orderID string, orderAmount float64) (*dto.PointsOperationResponse, error) {
	if userID <= 0 || orderID == "" || orderAmount <= 0 {
		return nil, errors.New("无效的请求参数")
	}

	// 计算积分
	points, err := s.CalculatePurchasePoints(ctx, orderAmount)
	if err != nil {
		logs.Error("计算订单积分失败: %v", err)
		return nil, fmt.Errorf("计算订单积分失败: %v", err)
	}

	if points <= 0 {
		// 无积分可获得
		return &dto.PointsOperationResponse{
			Success:      true,
			UserID:       userID,
			CurrentPoint: 0,
			ChangePoint:  0,
			Message:      "当前订单无积分可获得",
		}, nil
	}

	// 添加积分
	addReq := &dto.AddPointsRequest{
		UserID:      userID,
		Points:      points,
		Source:      constants.PointsSourcePurchase,
		Description: fmt.Sprintf("订单 %s 购物获得积分", orderID),
		OrderID:     orderID,
	}

	return s.AddPoints(ctx, addReq)
}

// CheckExpiringPoints 检查即将过期的积分
func (s *PointsServiceImpl) CheckExpiringPoints(ctx context.Context, daysThreshold int) ([]*models.PointsAccount, error) {
	if daysThreshold <= 0 {
		daysThreshold = 30 // 默认30天
	}

	o := orm.NewOrm()
	var accounts []*models.PointsAccount

	// 计算阈值日期
	thresholdDate := time.Now().AddDate(0, 0, daysThreshold)

	_, err := o.QueryTable(new(models.PointsAccount)).
		Filter("expire_time__lte", thresholdDate).
		Filter("expire_points__gt", 0).
		All(&accounts)

	if err != nil {
		logs.Error("查询即将过期积分失败: %v", err)
		return nil, fmt.Errorf("查询即将过期积分失败: %v", err)
	}

	return accounts, nil
}

// ProcessExpiringPoints 处理过期积分
func (s *PointsServiceImpl) ProcessExpiringPoints(ctx context.Context) error {
	o := orm.NewOrm()
	now := time.Now()

	// 查询已过期但未处理的积分账户
	var accounts []*models.PointsAccount
	_, err := o.QueryTable(new(models.PointsAccount)).
		Filter("expire_time__lt", now).
		Filter("expire_points__gt", 0).
		All(&accounts)

	if err != nil {
		logs.Error("查询过期积分账户失败: %v", err)
		return fmt.Errorf("查询过期积分账户失败: %v", err)
	}

	// 处理每个账户的过期积分
	for _, account := range accounts {
		// 创建事务管理器
		txManager := impl.NewTransactionManager()
		err := txManager.Begin()
		if err != nil {
			logs.Error("开始事务失败: %v", err)
			continue
		}

		// 获取事务对象
		tx := txManager.GetTx().(orm.TxOrmer)

		// 更新账户积分
		if account.Balance < account.ExpirePoints {
			account.ExpirePoints = account.Balance
		}

		account.Balance -= account.ExpirePoints
		account.LastActivity = now
		account.ExpirePoints = 0
		account.ExpireTime = now.AddDate(1, 0, 0) // 重置过期时间为一年后

		_, err = tx.Update(account)
		if err != nil {
			txManager.Rollback()
			logs.Error("更新账户积分失败: %v", err)
			continue
		}

		// 创建积分过期记录
		transaction := &models.PointsTransaction{
			AccountID:   account.ID,
			UserID:      account.UserID,
			Amount:      account.ExpirePoints,
			Balance:     account.Balance,
			Type:        constants.PointsOperationDeduct,
			Source:      "expire",
			Description: "积分过期",
			CreatedAt:   now,
			UpdatedAt:   now,
		}

		_, err = tx.Insert(transaction)
		if err != nil {
			txManager.Rollback()
			logs.Error("创建积分过期记录失败: %v", err)
			continue
		}

		err = txManager.Commit()
		if err != nil {
			txManager.Rollback()
			logs.Error("提交事务失败: %v", err)
		}
	}

	return nil
}
