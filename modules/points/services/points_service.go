/**
 * 积分服务接口定义
 *
 * 本文件定义了积分系统的服务层接口，包括积分账户管理、积分操作、积分规则管理等功能。
 * 提供了清晰的业务操作接口，便于不同实现的扩展。
 */

package services

import (
	"context"
	
	"o_mall_backend/modules/points/dto"
	"o_mall_backend/modules/points/models"
)

// PointsService 积分服务接口
type PointsService interface {
	// GetPointsAccount 获取用户积分账户信息
	GetPointsAccount(ctx context.Context, userID int64) (*dto.PointsAccountResponse, error)
	
	// AddPoints 增加积分
	AddPoints(ctx context.Context, req *dto.AddPointsRequest) (*dto.PointsOperationResponse, error)
	
	// DeductPoints 扣减积分
	DeductPoints(ctx context.Context, req *dto.DeductPointsRequest) (*dto.PointsOperationResponse, error)
	
	// GetPointsTransactions 获取积分交易记录
	GetPointsTransactions(ctx context.Context, req *dto.PointsTransactionQueryRequest) (*dto.PointsTransactionListResponse, error)
	
	// ExchangePoints 积分兑换
	ExchangePoints(ctx context.Context, req *dto.PointsExchangeRequest) (*dto.PointsExchangeResponse, error)
	
	// GetPointsRules 获取积分规则
	GetPointsRules(ctx context.Context) ([]*models.PointsRule, error)
	
	// GetPointsRule 获取指定类型的积分规则
	GetPointsRule(ctx context.Context, ruleType string) (*models.PointsRule, error)
	
	// SavePointsRule 保存积分规则
	SavePointsRule(ctx context.Context, rule *models.PointsRule) error
	
	// DeletePointsRule 删除积分规则
	DeletePointsRule(ctx context.Context, ruleID int64) error
	
	// CalculatePurchasePoints 计算订单可获得的积分
	CalculatePurchasePoints(ctx context.Context, orderAmount float64) (int, error)
	
	// ProcessOrderPoints 处理订单积分（下单获取积分）
	ProcessOrderPoints(ctx context.Context, userID int64, orderID string, orderAmount float64) (*dto.PointsOperationResponse, error)
	
	// CheckExpiringPoints 检查即将过期的积分
	CheckExpiringPoints(ctx context.Context, daysThreshold int) ([]*models.PointsAccount, error)
	
	// ProcessExpiringPoints 处理过期积分
	ProcessExpiringPoints(ctx context.Context) error
}
