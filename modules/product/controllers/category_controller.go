/**
 * 商品分类控制器
 *
 * 本文件提供商品分类管理的API接口，包括查询分类列表、获取分类树形结构、创建分类、
 * 更新分类、删除分类等功能。
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/product/dto"
	"o_mall_backend/modules/product/services"
	"o_mall_backend/common/result"
)

// CategoryController 商品分类控制器
type CategoryController struct {
	web.Controller
	categoryService services.CategoryService
}

// Prepare 初始化控制器
func (c *CategoryController) Prepare() {
	// 初始化服务
	c.categoryService = services.GetCategoryService()
}

// GetTree 获取分类树形结构
// @Title 获取分类树形结构
// @Description 获取完整的商品分类树形结构，前端无需登录即可访问
// @Success 200 {object} response.Response
// @router /tree [get]
func (c *CategoryController) GetTree() {
	// 调用服务获取分类树
	tree, err := c.categoryService.GetCategoryTree(c.Ctx.Request.Context())
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, tree)
}

// List 获取分类列表
// @Title 获取分类列表
// @Description 获取所有分类的列表，前端无需登录即可访问
// @Success 200 {object} response.Response
// @router / [get]
func (c *CategoryController) List() {
	// 调用服务获取所有分类
	categories, err := c.categoryService.GetAllCategories(c.Ctx.Request.Context())
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, categories)
}

// Get 获取单个分类详情
// @Title 获取分类详情
// @Description 根据ID获取分类详情，前端无需登录即可访问
// @Param id path string true "分类ID"
// @Success 200 {object} response.Response
// @router /:id [get]
func (c *CategoryController) Get() {
	// 获取分类ID参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务获取分类详情
	category, err := c.categoryService.GetCategory(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, category)
}

// Create 创建分类
// @Title 创建分类
// @Description 创建新商品分类，需要管理员权限
// @Param body body dto.CreateCategoryRequest true "分类信息"
// @Success 200 {object} response.Response
// @router / [post]
func (c *CategoryController) Create() {
	// 解析请求参数
	var req dto.CreateCategoryRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务创建分类
	category, err := c.categoryService.CreateCategory(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, category)
}

// Update 更新分类
// @Title 更新分类
// @Description 更新现有商品分类，需要管理员权限
// @Param id path string true "分类ID"
// @Param body body dto.UpdateCategoryRequest true "分类信息"
// @Success 200 {object} response.Response
// @router /:id [put]
func (c *CategoryController) Update() {
	// 获取分类ID参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var req dto.UpdateCategoryRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务更新分类
	err = c.categoryService.UpdateCategory(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// Delete 删除分类
// @Title 删除分类
// @Description 删除商品分类，需要管理员权限
// @Param id path string true "分类ID"
// @Success 200 {object} response.Response
// @router /:id [delete]
func (c *CategoryController) Delete() {
	// 获取分类ID参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务删除分类
	err = c.categoryService.DeleteCategory(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// SetShow 设置分类显示状态
// @Title 设置分类显示状态
// @Description 设置分类是否在前端显示，需要管理员权限
// @Param id path string true "分类ID"
// @Param show body string true "显示状态"
// @Success 200 {object} response.Response
// @router /:id/show [put]
func (c *CategoryController) SetShow() {
	// 获取分类ID参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	type ShowRequest struct {
		IsShow bool `json:"is_show"`
	}
	var req ShowRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务设置显示状态
	err = c.categoryService.SetCategoryShow(c.Ctx.Request.Context(), id, req.IsShow)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetChildren 获取子分类
// @Title 获取子分类
// @Description 获取指定父分类下的所有子分类，前端无需登录即可访问
// @Param parent_id query int false "父分类ID，默认为0表示顶级分类"
// @Success 200 {object} response.Response
// @router /children [get]
func (c *CategoryController) GetChildren() {
	// 获取父分类ID参数
	parentIDStr := c.Ctx.Input.Query("parent_id")
	parentID, err := strconv.ParseInt(parentIDStr, 10, 64)
	if err != nil || parentIDStr == "" {
		parentID = 0 // 默认获取一级分类
	}

	// 调用服务获取子分类
	children, err := c.categoryService.GetChildCategories(c.Ctx.Request.Context(), parentID)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, children)
}
