/**
 * u5546u54c1u8bc4u4ef7u63a7u5236u5668
 *
 * u672cu6587u4ef6u63d0u4f9bu5546u54c1u8bc4u4ef7u7ba1u7406u7684APIu63a5u53e3uff0cu5305u62ecu521bu5efau8bc4u4ef7u3001u83b7u53d6u8bc4u4ef7u5217u8868u3001u70b9u8d5eu8bc4u4ef7u7b49u529fu80fdu3002
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/product/dto"
	"o_mall_backend/modules/product/services"
	"o_mall_backend/common/result"
)

// CommentController u5546u54c1u8bc4u4ef7u63a7u5236u5668
type CommentController struct {
	web.Controller
	commentService services.CommentService
}

// Prepare u521du59cbu5316u63a7u5236u5668
func (c *CommentController) Prepare() {
	// 初始化服务
	c.commentService = services.GetCommentService()
}

// Create u521bu5efau5546u54c1u8bc4u4ef7
// @Title u521bu5efau5546u54c1u8bc4u4ef7
// @Description u7528u6237u521bu5efau5546u54c1u8bc4u4ef7uff0cu9700u8981u7528u6237u767bu5f55
// @Param body body dto.CreateCommentRequest true "u8bc4u4ef7u4fe1u606f"
// @Success 200 {object} response.Response
// @router / [post]
func (c *CommentController) Create() {
	// u89e3u6790u8bf7u6c42u53c2u6570
	var req dto.CreateCommentRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// u83b7u53d6u5f53u524du7528u6237ID
	userID := c.Ctx.Input.GetData("user_id").(int64)
	req.UserID = userID

	// u8c03u7528u670du52a1u521bu5efau8bc4u4ef7
	comment, err := c.commentService.CreateComment(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// u8fd4u56deu6210u529fu54cdu5e94
	result.OK(c.Ctx, comment)
}

// GetProductComments u83b7u53d6u5546u54c1u8bc4u4ef7u5217u8868
// @Title u83b7u53d6u5546u54c1u8bc4u4ef7u5217u8868
// @Description u83b7u53d6u6307u5b9au5546u54c1u7684u8bc4u4ef7u5217u8868uff0cu5305u62ecu7528u6237u53efu89c1u7684u5404u7c7bu8bc4u4ef7uff0cu524du7aefu65e0u9700u767bu5f55u5373u53efu8bbfu95ee
// @Param product_id query string true "u5546u54c1ID"
// @Param page query int false "u9875u7801"
// @Param page_size query int false "u6bcfu9875u8bb0u5f55u6570"
// @Success 200 {object} response.Response
// @router /product [get]
func (c *CommentController) GetProductComments() {
	// u83b7u53d6u5546u54c1IDu53c2u6570
	productIDStr := c.Ctx.Input.Query("product_id")
	productID, err := strconv.ParseInt(productIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// u83b7u53d6u5206u9875u53c2u6570
	page, _ := strconv.Atoi(c.Ctx.Input.Query("page"))
	pageSize, _ := strconv.Atoi(c.Ctx.Input.Query("page_size"))

	// u8bbeu7f6eu9ed8u8ba4u503c
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// u7ec4u88c5u8bf7u6c42u53c2u6570
	req := &dto.CommentListRequest{
		Page:     page,
		PageSize: pageSize,
	}

	// u8c03u7528u670du52a1u83b7u53d6u5546u54c1u8bc4u4ef7
	comments, err := c.commentService.GetProductComments(c.Ctx.Request.Context(), productID, req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// u8fd4u56deu6210u529fu54cdu5e94
	result.OK(c.Ctx, comments)
}

// GetUserComments u83b7u53d6u7528u6237u8bc4u4ef7u5217u8868
// @Title u83b7u53d6u7528u6237u8bc4u4ef7u5217u8868
// @Description u83b7u53d6u5f53u524du767bu5f55u7528u6237u7684u8bc4u4ef7u5217u8868uff0cu9700u8981u7528u6237u767bu5f55
// @Param page query int false "u9875u7801"
// @Param page_size query int false "u6bcfu9875u8bb0u5f55u6570"
// @Success 200 {object} response.Response
// @router /user [get]
func (c *CommentController) GetUserComments() {
	// u83b7u53d6u7528u6237ID
	userID := c.Ctx.Input.GetData("user_id").(int64)

	// u83b7u53d6u5206u9875u53c2u6570
	page, _ := strconv.Atoi(c.Ctx.Input.Query("page"))
	pageSize, _ := strconv.Atoi(c.Ctx.Input.Query("page_size"))

	// u8bbeu7f6eu9ed8u8ba4u503c
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// u7ec4u88c5u8bf7u6c42u53c2u6570
	req := &dto.CommentListRequest{
		Page:     page,
		PageSize: pageSize,
	}

	// u8c03u7528u670du52a1u83b7u53d6u7528u6237u8bc4u4ef7
	comments, err := c.commentService.GetUserComments(c.Ctx.Request.Context(), userID, req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// u8fd4u56deu6210u529fu54cdu5e94
	result.OK(c.Ctx, comments)
}

// Get u83b7u53d6u8bc4u4ef7u8be6u60c5
// @Title u83b7u53d6u8bc4u4ef7u8be6u60c5
// @Description u6839u636eIDu83b7u53d6u8bc4u4ef7u8be6u60c5uff0cu524du7aefu65e0u9700u767bu5f55u5373u53efu8bbfu95ee
// @Param id path string true "u8bc4u4ef7ID"
// @Success 200 {object} response.Response
// @router /:id [get]
func (c *CommentController) Get() {
	// u83b7u53d6u8bc4u4ef7IDu53c2u6570
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// u8c03u7528u670du52a1u83b7u53d6u8bc4u4ef7u8be6u60c5
	comment, err := c.commentService.GetComment(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// u8fd4u56deu6210u529fu54cdu5e94
	result.OK(c.Ctx, comment)
}

// LikeComment u70b9u8d5eu8bc4u4ef7
// @Title u70b9u8d5eu8bc4u4ef7
// @Description u7528u6237u5bf9u8bc4u4ef7u8fdbu884cu70b9u8d5euff0cu9700u8981u7528u6237u767bu5f55
// @Param id path string true "u8bc4u4ef7ID"
// @Success 200 {object} response.Response
// @router /:id/like [post]
func (c *CommentController) LikeComment() {
	// u83b7u53d6u8bc4u4ef7IDu53c2u6570
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// u8c03u7528u670du52a1u8fdbu884cu70b9u8d5e
	err = c.commentService.LikeComment(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// u8fd4u56deu6210u529fu54cdu5e94
	result.OK(c.Ctx, nil)
}

// UnlikeComment u53d6u6d88u70b9u8d5e
// @Title u53d6u6d88u70b9u8d5e
// @Description u7528u6237u53d6u6d88u5bf9u8bc4u4ef7u7684u70b9u8d5euff0cu9700u8981u7528u6237u767bu5f55
// @Param id path string true "u8bc4u4ef7ID"
// @Success 200 {object} response.Response
// @router /:id/unlike [post]
func (c *CommentController) UnlikeComment() {
	// u83b7u53d6u8bc4u4ef7IDu53c2u6570
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// u8c03u7528u670du52a1u53d6u6d88u70b9u8d5e
	err = c.commentService.UnlikeComment(c.Ctx.Request.Context(), id)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// u8fd4u56deu6210u529fu54cdu5e94
	result.OK(c.Ctx, nil)
}

// GetCommentStatistics u83b7u53d6u8bc4u4ef7u7edfu8ba1u4fe1u606f
// @Title u83b7u53d6u8bc4u4ef7u7edfu8ba1u4fe1u606f
// @Description u83b7u53d6u6307u5b9au5546u54c1u7684u8bc4u4ef7u7edfu8ba1u4fe1u606fuff0cu5305u62ecu597du8bc4u7387u3001u8bc4u5206u5206u5e03u7b49uff0cu524du7aefu65e0u9700u767bu5f55u5373u53efu8bbfu95ee
// @Param product_id query string true "u5546u54c1ID"
// @Success 200 {object} response.Response
// @router /statistics [get]
func (c *CommentController) GetCommentStatistics() {
	// u83b7u53d6u5546u54c1IDu53c2u6570
	productIDStr := c.Ctx.Input.Query("product_id")
	productID, err := strconv.ParseInt(productIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// u8c03u7528u670du52a1u83b7u53d6u7edfu8ba1u4fe1u606f
	stats, err := c.commentService.GetCommentStatistics(c.Ctx.Request.Context(), productID)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// u8fd4u56deu6210u529fu54cdu5e94
	result.OK(c.Ctx, stats)
}
