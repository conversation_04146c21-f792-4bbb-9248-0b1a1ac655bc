/**
 * product_controller.go
 * 商品控制器
 *
 * 本文件实现了商品相关的API接口
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/product/dto"
	"o_mall_backend/modules/product/services"
)

// ProductController 商品控制器
type ProductController struct {
	web.Controller
	productService services.ProductService
}

// Prepare 初始化
func (c *ProductController) Prepare() {
	// 初始化服务
	c.productService = services.NewProductService()
}

// List 获取商品列表
func (c *ProductController) List() {
	// 解析查询参数
	var req dto.ProductQueryRequest
	req.Page, _ = strconv.Atoi(c.GetString("page", "1"))
	req.PageSize, _ = strconv.Atoi(c.GetString("pageSize", "10"))
	req.CategoryID, _ = strconv.ParseInt(c.GetString("category_id", "0"), 10, 64)
	req.Keyword = c.GetString("keyword", "")
	req.SortBy = c.GetString("sort_by", "")
	req.SortOrder = c.GetString("sort_order", "")
	req.MinPrice, _ = strconv.ParseFloat(c.GetString("min_price", "0"), 64)
	req.MaxPrice, _ = strconv.ParseFloat(c.GetString("max_price", "0"), 64)
	req.MerchantID, _ = strconv.ParseInt(c.GetString("merchant_id", "0"), 10, 64)
	req.Status, _ = strconv.Atoi(c.GetString("status", "-1"))

	// 调用服务
	response, err := c.productService.ListProducts(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("获取商品列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, response)
}

// Get 获取商品详情
func (c *ProductController) Get() {
	// 解析ID参数
	idStr := c.GetString(":id")
	if idStr == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	product, err := c.productService.GetProduct(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("获取商品详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if product == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 增加浏览次数
	go c.productService.IncrProductViewCount(c.Ctx.Request.Context(), id)

	// 返回结果
	result.OK(c.Ctx, product)
}

// Create 创建商品
func (c *ProductController) Create() {
	// 解析请求参数
	var req dto.CreateProductRequest
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取商家ID
	merchantID, ok := c.Ctx.Input.GetData("user_id").(int64)
	if !ok || merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}
	// 注意: CreateProductRequest没有MerchantID字段

	// 调用服务
	product, err := c.productService.CreateProduct(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("创建商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, product)
}

// Update 更新商品
func (c *ProductController) Update() {
	// 获取商品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var req dto.UpdateProductRequest
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	err = c.productService.UpdateProduct(c.Ctx.Request.Context(), id, &req)
	if err != nil {
		logs.Error("更新商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// Delete 删除商品
func (c *ProductController) Delete() {
	// 获取商品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	err = c.productService.DeleteProduct(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("删除商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// BatchDelete 批量删除商品
func (c *ProductController) BatchDelete() {
	// 解析请求参数
	var req struct {
		IDs []int64 `json:"ids"`
	}
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	err = c.productService.BatchDeleteProducts(c.Ctx.Request.Context(), req.IDs)
	if err != nil {
		logs.Error("批量删除商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// UpdateStatus 更新商品状态
func (c *ProductController) UpdateStatus() {
	// 获取商品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var req struct {
		Status       int    `json:"status"`
		RejectReason string `json:"reject_reason"`
	}
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	err = c.productService.UpdateProductStatus(c.Ctx.Request.Context(), id, req.Status, req.RejectReason)
	if err != nil {
		logs.Error("更新商品状态失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, nil)
}

// GetRecommend 获取推荐商品
func (c *ProductController) GetRecommend() {
	// 获取限制数量
	limit, _ := strconv.Atoi(c.GetString("limit", "10"))

	// 调用服务
	products, err := c.productService.GetRecommendProducts(c.Ctx.Request.Context(), limit)
	if err != nil {
		logs.Error("获取推荐商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, products)
}

// GetHot 获取热销商品
func (c *ProductController) GetHot() {
	// 获取限制数量
	limit, _ := strconv.Atoi(c.GetString("limit", "10"))

	// 调用服务
	products, err := c.productService.GetHotProducts(c.Ctx.Request.Context(), limit)
	if err != nil {
		logs.Error("获取热销商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, products)
}

// GetNew 获取新品商品
func (c *ProductController) GetNew() {
	// 获取限制数量
	limit, _ := strconv.Atoi(c.GetString("limit", "10"))

	// 调用服务
	products, err := c.productService.GetNewProducts(c.Ctx.Request.Context(), limit)
	if err != nil {
		logs.Error("获取新品商品失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, products)
}

// CheckStock 检查库存
func (c *ProductController) CheckStock() {
	// 获取商品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析请求参数
	var req struct {
		SkuID    int64 `json:"sku_id"`
		Quantity int   `json:"quantity"`
	}
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	enough, err := c.productService.CheckStock(c.Ctx.Request.Context(), id, req.SkuID, req.Quantity)
	if err != nil {
		logs.Error("检查库存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回结果
	result.OK(c.Ctx, map[string]bool{"enough": enough})
}
