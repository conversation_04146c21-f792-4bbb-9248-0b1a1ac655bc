/**
 * 商品分类DTO定义
 *
 * 本文件定义了商品分类相关的数据传输对象，用于服务层与控制器层之间的数据传输。
 * 包括分类的创建、查询、更新等相关DTO。
 */

package dto

import (
	"time"
)

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name        string `json:"name" valid:"Required;MaxSize(100)"`
	Description string `json:"description" valid:"MaxSize(500)"`
	ParentID    int64  `json:"parent_id"`
	Icon        string `json:"icon" valid:"MaxSize(255)"`
	SortOrder   int    `json:"sort_order"`
	IsShow      bool   `json:"is_show"`
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name        string `json:"name" valid:"MaxSize(100)"`
	Description string `json:"description" valid:"MaxSize(500)"`
	ParentID    int64  `json:"parent_id"`
	Icon        string `json:"icon" valid:"MaxSize(255)"`
	SortOrder   int    `json:"sort_order"`
	IsShow      bool   `json:"is_show"`
}

// CategoryResponse 分类响应
type CategoryResponse struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	ParentID    int64     `json:"parent_id"`
	Icon        string    `json:"icon"`
	SortOrder   int       `json:"sort_order"`
	IsShow      bool      `json:"is_show"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CategoryTreeResponse 分类树响应
type CategoryTreeResponse struct {
	ID          int64                   `json:"id"`
	Name        string                  `json:"name"`
	Description string                  `json:"description"`
	ParentID    int64                   `json:"parent_id"`
	Icon        string                  `json:"icon"`
	SortOrder   int                     `json:"sort_order"`
	IsShow      bool                    `json:"is_show"`
	CreatedAt   time.Time               `json:"created_at"`
	UpdatedAt   time.Time               `json:"updated_at"`
	Children    []*CategoryTreeResponse `json:"children"`
}

// CategoryListResponse 分类列表响应
type CategoryListResponse struct {
	Total int64               `json:"total"`
	Items []*CategoryResponse `json:"items"`
}
