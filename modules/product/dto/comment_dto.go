/**
 * 商品评论DTO定义
 *
 * 本文件定义了商品评论相关的数据传输对象，用于服务层与控制器层之间的数据传输。
 * 包括评论的创建、查询、更新、审核等相关DTO。
 */

package dto

import (
	"time"
)

// CreateCommentRequest 创建评论请求
type CreateCommentRequest struct {
	ProductID      int64    `json:"product_id" valid:"Required"`
	UserID         int64    `json:"user_id" valid:"Required"`
	UserName       string   `json:"user_name" valid:"Required;MaxSize(100)"`
	UserAvatar     string   `json:"user_avatar"`
	Content        string   `json:"content" valid:"Required;MaxSize(1000)"`
	Rating         int      `json:"rating" valid:"Required;Range(1,5)"`
	OrderID        int64    `json:"order_id"`
	IsAnonymous    bool     `json:"is_anonymous"`
	Images         []string `json:"images"`
	HasProductSku  bool     `json:"has_product_sku"`
	ProductSkuInfo string   `json:"product_sku_info"`
}

// UpdateCommentRequest 更新评论请求
type UpdateCommentRequest struct {
	Content     string `json:"content" valid:"MaxSize(1000)"`
	Rating      int    `json:"rating" valid:"Range(1,5)"`
	IsAnonymous bool   `json:"is_anonymous"`
}

// CommentListRequest 评论列表查询请求
type CommentListRequest struct {
	Page     int `json:"page" valid:"Min(1)"`
	PageSize int `json:"pageSize" valid:"Min(1);Max(100)"`
	Status   int `json:"status" valid:"Range(-1,2)"`
}

// AuditCommentRequest 审核评论请求
type AuditCommentRequest struct {
	Status int    `json:"status" valid:"Required;Range(0,2)"`
	Reason string `json:"reason" valid:"MaxSize(200)"`
}

// BatchAuditCommentRequest 批量审核评论请求
type BatchAuditCommentRequest struct {
	IDs    []int64 `json:"ids" valid:"Required"`
	Status int     `json:"status" valid:"Required;Range(0,2)"`
	Reason string  `json:"reason" valid:"MaxSize(200)"`
}

// ReplyCommentRequest 回复评论请求
type ReplyCommentRequest struct {
	Content   string `json:"content" valid:"Required;MaxSize(500)"`
	AdminName string `json:"admin_name" valid:"Required;MaxSize(50)"`
}

// AddCommentImageRequest 添加评论图片请求
type AddCommentImageRequest struct {
	CommentID int64  `json:"comment_id" valid:"Required"`
	ImageURL  string `json:"image_url" valid:"Required;MaxSize(255)"`
}

// CommentResponse 评论响应
type CommentResponse struct {
	ID             int64     `json:"id"`
	ProductID      int64     `json:"product_id"`
	UserID         int64     `json:"user_id"`
	UserName       string    `json:"user_name"`
	UserAvatar     string    `json:"user_avatar"`
	Content        string    `json:"content"`
	Rating         int       `json:"rating"`
	OrderID        int64     `json:"order_id"`
	Status         int       `json:"status"`
	StatusText     string    `json:"status_text"`
	IsAnonymous    bool      `json:"is_anonymous"`
	Images         []string  `json:"images"`
	ReplyContent   string    `json:"reply_content"`
	ReplyAdmin     string    `json:"reply_admin"`
	ReplyTime      time.Time `json:"reply_time"`
	IsReplied      bool      `json:"is_replied"`
	HasProductSku  bool      `json:"has_product_sku"`
	ProductSkuInfo string    `json:"product_sku_info"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// CommentListResponse 评论列表响应
type CommentListResponse struct {
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"pageSize"`
	Items    []*CommentResponse `json:"items"`
}

// CommentStatisticsResponse 评论统计响应
type CommentStatisticsResponse struct {
	TotalCount  int64   `json:"total_count"`
	GoodCount   int64   `json:"good_count"`
	NormalCount int64   `json:"normal_count"`
	BadCount    int64   `json:"bad_count"`
	GoodRate    float64 `json:"good_rate"`
}
