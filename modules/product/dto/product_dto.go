/**
 * 商品数据传输对象
 *
 * 本文件定义了商品相关的数据传输对象(DTO)，用于前后端数据交互。
 * 包括商品创建、更新、查询等接口的请求和响应数据结构。
 */

package dto

import (
	"time"
)

// 商品列表查询请求
type ProductQueryRequest struct {
	Page        int     `json:"page" form:"page"`                 // 页码，默认1
	PageSize    int     `json:"pageSize" form:"pageSize"`         // 每页数量，默认10
	Name        string  `json:"name" form:"name"`                 // 商品名称关键词
	Keyword     string  `json:"keyword" form:"keyword"`           // 商品搜索关键词，与Name字段功能类似
	CategoryID  int64   `json:"category_id" form:"category_id"`   // 分类ID
	MerchantID  int64   `json:"merchant_id" form:"merchant_id"`   // 商家ID
	Status      int     `json:"status" form:"status"`             // 商品状态
	PriceMin    float64 `json:"price_min" form:"price_min"`       // 最低价格
	PriceMax    float64 `json:"price_max" form:"price_max"`       // 最高价格
	MinPrice    float64 `json:"min_price" form:"min_price"`       // 最低价格，与PriceMin字段功能相同
	MaxPrice    float64 `json:"max_price" form:"max_price"`       // 最高价格，与PriceMax字段功能相同
	IsRecommend bool    `json:"is_recommend" form:"is_recommend"` // 是否推荐商品
	IsHot       bool    `json:"is_hot" form:"is_hot"`             // 是否热销商品
	IsNew       bool    `json:"is_new" form:"is_new"`             // 是否新品
	SortField   string  `json:"sort_field" form:"sort_field"`     // 排序字段
	SortBy      string  `json:"sort_by" form:"sort_by"`           // 排序字段，与SortField字段功能相同
	SortOrder   string  `json:"sort_order" form:"sort_order"`     // 排序方式：asc升序，desc降序
}

// 商品创建请求
type CreateProductRequest struct {
	Name            string               `json:"name" form:"name" valid:"Required"`               // 商品名称
	Brief           string               `json:"brief" form:"brief" valid:"Required"`             // 商品简介
	Description     string               `json:"description" form:"description"`                  // 商品详细描述
	CategoryID      int64                `json:"category_id" form:"category_id" valid:"Required"` // 商品分类ID
	MainImage       string               `json:"main_image" form:"main_image" valid:"Required"`   // 商品主图
	Price           float64              `json:"price" form:"price" valid:"Required"`             // 商品价格
	OriginalPrice   float64              `json:"original_price" form:"original_price"`            // 商品原价
	CostPrice       float64              `json:"cost_price" form:"cost_price"`                    // 商品成本价
	Stock           int                  `json:"stock" form:"stock" valid:"Required"`             // 商品库存
	Unit            string               `json:"unit" form:"unit"`                                // 商品单位
	Weight          float64              `json:"weight" form:"weight"`                            // 商品重量
	Keywords        string               `json:"keywords" form:"keywords"`                        // 搜索关键词
	Tags            string               `json:"tags" form:"tags"`                                // 商品标签
	Code            string               `json:"code" form:"code"`                                // 商品编码
	Barcode         string               `json:"barcode" form:"barcode"`                          // 商品条形码
	IsRecommend     bool                 `json:"is_recommend" form:"is_recommend"`                // 是否推荐
	IsHot           bool                 `json:"is_hot" form:"is_hot"`                            // 是否热销
	IsNew           bool                 `json:"is_new" form:"is_new"`                            // 是否新品
	Status          int                  `json:"status" form:"status"`                            // 商品状态
	SaleStartTime   time.Time            `json:"sale_start_time" form:"sale_start_time"`          // 上架开始时间
	SaleEndTime     time.Time            `json:"sale_end_time" form:"sale_end_time"`              // 上架结束时间
	FreightTemplate int64                `json:"freight_template" form:"freight_template"`        // 运费模板ID
	HasSKU          bool                 `json:"has_sku" form:"has_sku"`                          // 是否有SKU
	DeliveryType    int                  `json:"delivery_type" form:"delivery_type"`              // 配送方式
	Images          []string             `json:"images" form:"images"`                            // 商品图片列表
	DetailImages    []string             `json:"detail_images" form:"detail_images"`              // 商品详情图片列表
	Specifications  []ProductSpecRequest `json:"specifications" form:"specifications"`            // 商品规格
	Skus            []ProductSkuRequest  `json:"skus" form:"skus"`                                // 商品SKU列表
}

// 商品规格请求
type ProductSpecRequest struct {
	SpecificationID int64   `json:"specification_id" form:"specification_id"` // 规格ID
	IsRequired      bool    `json:"is_required" form:"is_required"`           // 是否必选
	Values          []int64 `json:"values" form:"values"`                     // 规格值ID列表
}

// 商品SKU请求
type ProductSkuRequest struct {
	Name          string  `json:"name" form:"name"`                     // SKU名称
	Code          string  `json:"code" form:"code"`                     // SKU编码
	BarCode       string  `json:"bar_code" form:"bar_code"`             // SKU条形码
	Image         string  `json:"image" form:"image"`                   // SKU图片
	Price         float64 `json:"price" form:"price"`                   // SKU价格
	OriginalPrice float64 `json:"original_price" form:"original_price"` // SKU原价
	CostPrice     float64 `json:"cost_price" form:"cost_price"`         // SKU成本价
	Stock         int     `json:"stock" form:"stock"`                   // SKU库存
	Weight        float64 `json:"weight" form:"weight"`                 // SKU重量
	Volume        float64 `json:"volume" form:"volume"`                 // SKU体积
	SpecData      string  `json:"spec_data" form:"spec_data"`           // SKU规格数据，格式为：{"颜色":"红色","尺寸":"XL"}
}

// 商品更新请求
type UpdateProductRequest struct {
	ID              int64                `json:"id" form:"id" valid:"Required"`            // 商品ID
	Name            string               `json:"name" form:"name"`                         // 商品名称
	Brief           string               `json:"brief" form:"brief"`                       // 商品简介
	Description     string               `json:"description" form:"description"`           // 商品详细描述
	CategoryID      int64                `json:"category_id" form:"category_id"`           // 商品分类ID
	MainImage       string               `json:"main_image" form:"main_image"`             // 商品主图
	Price           float64              `json:"price" form:"price"`                       // 商品价格
	OriginalPrice   float64              `json:"original_price" form:"original_price"`     // 商品原价
	CostPrice       float64              `json:"cost_price" form:"cost_price"`             // 商品成本价
	Stock           int                  `json:"stock" form:"stock"`                       // 商品库存
	Unit            string               `json:"unit" form:"unit"`                         // 商品单位
	Weight          float64              `json:"weight" form:"weight"`                     // 商品重量
	Keywords        string               `json:"keywords" form:"keywords"`                 // 搜索关键词
	Tags            string               `json:"tags" form:"tags"`                         // 商品标签
	Code            string               `json:"code" form:"code"`                         // 商品编码
	Barcode         string               `json:"barcode" form:"barcode"`                   // 商品条形码
	IsRecommend     bool                 `json:"is_recommend" form:"is_recommend"`         // 是否推荐
	IsHot           bool                 `json:"is_hot" form:"is_hot"`                     // 是否热销
	IsNew           bool                 `json:"is_new" form:"is_new"`                     // 是否新品
	Status          int                  `json:"status" form:"status"`                     // 商品状态
	SaleStartTime   time.Time            `json:"sale_start_time" form:"sale_start_time"`   // 上架开始时间
	SaleEndTime     time.Time            `json:"sale_end_time" form:"sale_end_time"`       // 上架结束时间
	FreightTemplate int64                `json:"freight_template" form:"freight_template"` // 运费模板ID
	HasSKU          bool                 `json:"has_sku" form:"has_sku"`                   // 是否有SKU
	DeliveryType    int                  `json:"delivery_type" form:"delivery_type"`       // 配送方式
	Images          []string             `json:"images" form:"images"`                     // 商品图片列表
	Specifications  []ProductSpecRequest `json:"specifications" form:"specifications"`     // 商品规格
	Skus            []ProductSkuRequest  `json:"skus" form:"skus"`                         // 商品SKU列表
}

// ProductResponse 商品基本信息响应
type ProductResponse struct {
	ID            int64     `json:"id"`             // 商品ID
	MerchantID    int64     `json:"merchant_id"`    // 所属商家ID
	MerchantName  string    `json:"merchant_name"`  // 商家名称
	CategoryID    int64     `json:"category_id"`    // 商品分类ID
	CategoryName  string    `json:"category_name"`  // 分类名称
	Name          string    `json:"name"`           // 商品名称
	Brief         string    `json:"brief"`          // 商品简介
	MainImage     string    `json:"main_image"`     // 商品主图URL
	Price         float64   `json:"price"`          // 商品价格
	OriginalPrice float64   `json:"original_price"` // 商品原价
	CostPrice     float64   `json:"cost_price"`     // 商品成本价
	Stock         int       `json:"stock"`          // 商品库存数量
	SoldNum       int       `json:"sold_num"`       // 已售数量
	Unit          string    `json:"unit"`           // 商品单位
	Weight        float64   `json:"weight"`         // 商品重量
	Keywords      string    `json:"keywords"`       // 搜索关键词
	Tags          string    `json:"tags"`           // 商品标签
	Code          string    `json:"code"`           // 商品编码
	Barcode       string    `json:"barcode"`        // 商品条形码
	IsRecommend   bool      `json:"is_recommend"`   // 是否推荐商品
	IsHot         bool      `json:"is_hot"`         // 是否热销商品
	IsNew         bool      `json:"is_new"`         // 是否新品
	Status        int       `json:"status"`         // 商品状态
	StatusText    string    `json:"status_text"`    // 状态文本说明
	ViewCount     int       `json:"view_count"`     // 浏览次数
	CommentCount  int       `json:"comment_count"`  // 评论数量
	FavoriteCount int       `json:"favorite_count"` // 收藏数量
	HasSKU        bool      `json:"has_sku"`        // 是否有SKU
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`     // 更新时间
}

// 商品详情响应
type ProductDetailResponse struct {
	ID              int64                 `json:"id"`               // 商品ID
	MerchantID      int64                 `json:"merchant_id"`      // 所属商家ID
	MerchantName    string                `json:"merchant_name"`    // 商家名称
	CategoryID      int64                 `json:"category_id"`      // 商品分类ID
	CategoryName    string                `json:"category_name"`    // 分类名称
	Name            string                `json:"name"`             // 商品名称
	Brief           string                `json:"brief"`            // 商品简介
	Description     string                `json:"description"`      // 商品详细描述
	MainImage       string                `json:"main_image"`       // 商品主图URL
	Price           float64               `json:"price"`            // 商品价格
	OriginalPrice   float64               `json:"original_price"`   // 商品原价
	CostPrice       float64               `json:"cost_price"`       // 商品成本价
	Stock           int                   `json:"stock"`            // 商品库存数量
	SoldNum         int                   `json:"sold_num"`         // 已售数量
	Unit            string                `json:"unit"`             // 商品单位
	Weight          float64               `json:"weight"`           // 商品重量
	Keywords        string                `json:"keywords"`         // 搜索关键词
	Tags            string                `json:"tags"`             // 商品标签
	Code            string                `json:"code"`             // 商品编码
	Barcode         string                `json:"barcode"`          // 商品条形码
	IsRecommend     bool                  `json:"is_recommend"`     // 是否推荐商品
	IsHot           bool                  `json:"is_hot"`           // 是否热销商品
	IsNew           bool                  `json:"is_new"`           // 是否新品
	Status          int                   `json:"status"`           // 商品状态
	StatusText      string                `json:"status_text"`      // 状态文本说明
	SaleStartTime   time.Time             `json:"sale_start_time"`  // 上架开始时间
	SaleEndTime     time.Time             `json:"sale_end_time"`    // 上架结束时间
	ViewCount       int                   `json:"view_count"`       // 浏览次数
	CommentCount    int                   `json:"comment_count"`    // 评论数量
	FavoriteCount   int                   `json:"favorite_count"`   // 收藏数量
	FreightTemplate int64                 `json:"freight_template"` // 运费模板ID
	HasSKU          bool                  `json:"has_sku"`          // 是否有SKU
	DeliveryType    int                   `json:"delivery_type"`    // 配送方式
	CreatedAt       time.Time             `json:"created_at"`       // 创建时间
	UpdatedAt       time.Time             `json:"updated_at"`       // 更新时间
	Images          []string              `json:"images"`           // 商品图片列表
	DetailImages    []string              `json:"detail_images"`    // 商品详情图片列表
	Specifications  []ProductSpecResponse `json:"specifications"`   // 商品规格
	Skus            []ProductSkuResponse  `json:"skus"`             // 商品SKU列表
	ProductResponse ProductResponse       `json:"product_response"` // 基本商品信息
}

// 商品图片响应
type ProductImageResponse struct {
	ID       int64  `json:"id"`        // 图片ID
	ImageURL string `json:"image_url"` // 图片URL
	Sort     int    `json:"sort"`      // 排序值
	Type     int    `json:"type"`      // 图片类型
	Width    int    `json:"width"`     // 图片宽度
	Height   int    `json:"height"`    // 图片高度
}

// 商品规格响应
type ProductSpecResponse struct {
	SpecificationID int64                      `json:"specification_id"` // 规格ID
	Name            string                     `json:"name"`             // 规格名称
	IsRequired      bool                       `json:"is_required"`      // 是否必选
	Values          []ProductSpecValueResponse `json:"values"`           // 规格值列表
}

// 商品规格值响应
type ProductSpecValueResponse struct {
	ID    int64  `json:"id"`    // 规格值ID
	Value string `json:"value"` // 规格值
	Image string `json:"image"` // 规格值图片
	Color string `json:"color"` // 颜色值
}

// 商品SKU响应
type ProductSkuResponse struct {
	ID            int64   `json:"id"`             // SKU ID
	Name          string  `json:"name"`           // SKU名称
	Code          string  `json:"code"`           // SKU编码
	BarCode       string  `json:"bar_code"`       // SKU条形码
	Image         string  `json:"image"`          // SKU图片
	Price         float64 `json:"price"`          // SKU价格
	OriginalPrice float64 `json:"original_price"` // SKU原价
	CostPrice     float64 `json:"cost_price"`     // SKU成本价
	Stock         int     `json:"stock"`          // SKU库存
	SoldNum       int     `json:"sold_num"`       // SKU已售数量
	Weight        float64 `json:"weight"`         // SKU重量
	Volume        float64 `json:"volume"`         // SKU体积
	SpecData      string  `json:"spec_data"`      // SKU规格数据
	SpecTexts     string  `json:"spec_texts"`     // SKU规格文本，如"颜色:红色,尺寸:XL"
}

// 商品列表响应
type ProductListResponse struct {
	ID            int64     `json:"id"`             // 商品ID
	MerchantID    int64     `json:"merchant_id"`    // 所属商家ID
	MerchantName  string    `json:"merchant_name"`  // 商家名称
	CategoryID    int64     `json:"category_id"`    // 商品分类ID
	CategoryName  string    `json:"category_name"`  // 分类名称
	Name          string    `json:"name"`           // 商品名称
	Brief         string    `json:"brief"`          // 商品简介
	MainImage     string    `json:"main_image"`     // 商品主图URL
	Price         float64   `json:"price"`          // 商品价格
	OriginalPrice float64   `json:"original_price"` // 商品原价
	Stock         int       `json:"stock"`          // 商品库存数量
	SoldNum       int       `json:"sold_num"`       // 已售数量
	IsRecommend   bool      `json:"is_recommend"`   // 是否推荐商品
	IsHot         bool      `json:"is_hot"`         // 是否热销商品
	IsNew         bool      `json:"is_new"`         // 是否新品
	Status        int       `json:"status"`         // 商品状态
	StatusText    string    `json:"status_text"`    // 状态文本说明
	ViewCount     int       `json:"view_count"`     // 浏览次数
	CommentCount  int       `json:"comment_count"`  // 评论数量
	FavoriteCount int       `json:"favorite_count"` // 收藏数量
	HasSKU        bool      `json:"has_sku"`        // 是否有SKU
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
}

// 商品状态更新请求
type UpdateProductStatusRequest struct {
	ID     int64  `json:"id" form:"id" valid:"Required"`         // 商品ID
	Status int    `json:"status" form:"status" valid:"Required"` // 商品状态
	Reason string `json:"reason" form:"reason"`                  // 拒绝原因，当状态为拒绝时必填
}

// 商品评论查询请求
type ProductCommentQueryRequest struct {
	Page        int   `json:"page" form:"page"`                 // 页码
	PageSize    int   `json:"pageSize" form:"pageSize"`         // 每页数量
	ProductID   int64 `json:"product_id" form:"product_id"`     // 商品ID
	Rating      int   `json:"rating" form:"rating"`             // 评分筛选
	RatingLevel int   `json:"rating_level" form:"rating_level"` // 评分等级：1差评，2中评，3好评
	HasImage    bool  `json:"has_image" form:"has_image"`       // 是否有图
}

// 商品评论响应
type ProductCommentResponse struct {
	ID           int64                    `json:"id"`            // 评论ID
	ProductID    int64                    `json:"product_id"`    // 商品ID
	ProductName  string                   `json:"product_name"`  // 商品名称
	ProductImage string                   `json:"product_image"` // 商品图片
	UserID       int64                    `json:"user_id"`       // 用户ID
	UserName     string                   `json:"user_name"`     // 用户名称
	UserAvatar   string                   `json:"user_avatar"`   // 用户头像
	Content      string                   `json:"content"`       // 评论内容
	Images       []string                 `json:"images"`        // 评论图片
	Rating       int                      `json:"rating"`        // 商品评分
	RatingLevel  int                      `json:"rating_level"`  // 评分等级
	IsAnonymous  bool                     `json:"is_anonymous"`  // 是否匿名评论
	SpecInfo     string                   `json:"spec_info"`     // 规格信息
	LikeCount    int                      `json:"like_count"`    // 点赞数量
	Type         int                      `json:"type"`          // 评论类型
	CreatedAt    time.Time                `json:"created_at"`    // 创建时间
	Replies      []ProductCommentResponse `json:"replies"`       // 回复列表
}

// 创建商品评论请求
type CreateProductCommentRequest struct {
	ProductID   int64    `json:"product_id" form:"product_id" valid:"Required"` // 商品ID
	OrderID     int64    `json:"order_id" form:"order_id" valid:"Required"`     // 订单ID
	Content     string   `json:"content" form:"content" valid:"Required"`       // 评论内容
	Images      []string `json:"images" form:"images"`                          // 评论图片
	Rating      int      `json:"rating" form:"rating"`                          // 商品评分
	IsAnonymous bool     `json:"is_anonymous" form:"is_anonymous"`              // 是否匿名评论
	SpecInfo    string   `json:"spec_info" form:"spec_info"`                    // 规格信息
	TagInfo     string   `json:"tag_info" form:"tag_info"`                      // 评价标签
}

// 创建规格请求
type CreateSpecificationRequest struct {
	Name        string `json:"name" form:"name" valid:"Required"` // 规格名称
	Description string `json:"description" form:"description"`    // 规格描述
	CategoryID  int64  `json:"category_id" form:"category_id"`    // 分类ID
	SortOrder   int    `json:"sort_order" form:"sort_order"`      // 排序值
	IsGlobal    bool   `json:"is_global" form:"is_global"`        // 是否全局规格
}

// 更新规格请求
type UpdateSpecificationRequest struct {
	ID          int64  `json:"id" form:"id" valid:"Required"`  // 规格ID
	Name        string `json:"name" form:"name"`               // 规格名称
	Description string `json:"description" form:"description"` // 规格描述
	CategoryID  int64  `json:"category_id" form:"category_id"` // 分类ID
	SortOrder   int    `json:"sort_order" form:"sort_order"`   // 排序值
	IsGlobal    bool   `json:"is_global" form:"is_global"`     // 是否全局规格
	Status      int    `json:"status" form:"status"`           // 状态
}

// 规格响应
type SpecificationResponse struct {
	ID          int64     `json:"id"`          // 规格ID
	Name        string    `json:"name"`        // 规格名称
	Description string    `json:"description"` // 规格描述
	CategoryID  int64     `json:"category_id"` // 分类ID
	SortOrder   int       `json:"sort_order"`  // 排序值
	IsGlobal    bool      `json:"is_global"`   // 是否全局规格
	Status      int       `json:"status"`      // 状态
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
}

// 创建规格值请求
type CreateSpecValueRequest struct {
	SpecID    int64  `json:"spec_id" form:"spec_id" valid:"Required"` // 规格ID
	Value     string `json:"value" form:"value" valid:"Required"`     // 规格值内容
	SortOrder int    `json:"sort_order" form:"sort_order"`            // 排序值
}

// 更新规格值请求
type UpdateSpecValueRequest struct {
	ID        int64  `json:"id" form:"id" valid:"Required"` // 规格值ID
	SpecID    int64  `json:"spec_id" form:"spec_id"`        // 规格ID
	Value     string `json:"value" form:"value"`            // 规格值内容
	SortOrder int    `json:"sort_order" form:"sort_order"`  // 排序值
}

// 规格值响应
type SpecValueResponse struct {
	ID        int64     `json:"id"`         // 规格值ID
	SpecID    int64     `json:"spec_id"`    // 规格ID
	Value     string    `json:"value"`      // 规格值内容
	SortOrder int       `json:"sort_order"` // 排序值
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}
