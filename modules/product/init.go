/**
 * init.go
 * 商品模块初始化
 *
 * 本文件负责初始化商品模块，包括注册路由和初始化数据库等
 */

package product

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/models"
	"o_mall_backend/modules/product/routes"
)

// Init 初始化商品模块
func Init() {
	logs.Info("正在初始化商品模块...")

	// 注册模型
	registerModels()

	// 注册路由
	routes.InitRoutes()

	logs.Info("商品模块初始化完成")
}

// registerModels 注册商品相关模型
func registerModels() {
	logs.Info("注册商品数据模型")

	// 注册商品相关模型
	orm.RegisterModel(new(models.Product))
	orm.RegisterModel(new(models.ProductSku))
	orm.RegisterModel(new(models.ProductImage))
	orm.RegisterModel(new(models.Category))
	orm.RegisterModel(new(models.Specification))
	orm.RegisterModel(new(models.ProductComment))
}
