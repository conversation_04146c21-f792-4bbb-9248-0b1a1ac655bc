/**
 * 商品分类模型
 *
 * 本文件定义了商品分类相关的数据模型，支持多级分类结构。
 * 分类是电商平台商品管理的重要部分，用于对商品进行分类展示和管理。
 */

package models

import (
	"time"
)

// Category 商品分类模型
type Category struct {
	ID          int64       `orm:"pk;auto;column(id)" json:"id" description:"分类ID，系统自动生成的唯一标识"`                             // 分类ID，主键
	ParentID    int64       `orm:"column(parent_id);default(0)" json:"parent_id" description:"父分类ID，用于构建分类层级关系，顶级分类为0"`     // 父分类ID，顶级分类为0
	Name        string      `orm:"column(name);size(50)" json:"name" description:"分类名称，最多50个字符"`                            // 分类名称
	Icon        string      `orm:"column(icon);size(255)" json:"icon" description:"分类图标的URL地址，用于前端展示"`                      // 分类图标URL
	Image       string      `orm:"column(image);size(255)" json:"image" description:"分类的展示图片URL地址"`                         // 分类图片URL
	Level       int         `orm:"column(level);default(1)" json:"level" description:"分类的层级深度，1代表一级分类，2代表二级分类，3代表三级分类"`     // 分类层级：1一级，2二级，3三级
	Sort        int         `orm:"column(sort);default(0)" json:"sort" description:"分类的排序权重，数值越小排序越靠前"`                     // 排序值，数值越小越靠前
	IsShow      bool        `orm:"column(is_show);default(true)" json:"is_show" description:"分类是否在前端显示，true显示，false隐藏"`     // 是否显示
	Keywords    string      `orm:"column(keywords);size(255)" json:"keywords" description:"分类的SEO关键词，多个关键词用逗号分隔"`           // 关键词，用于SEO
	Description string      `orm:"column(description);size(500)" json:"description" description:"分类的详细描述信息，最多500个字符"`       // 分类描述
	Path        string      `orm:"column(path);size(100)" json:"path" description:"分类的层级路径，格式：1-2-3表示一级分类ID-二级分类ID-三级分类ID"` // 分类路径，格式：1-2-3 表示一级-二级-三级
	CreatedAt   time.Time   `orm:"column(created_at);auto_now_add" json:"created_at" description:"记录创建时间，系统自动生成"`           // 创建时间
	UpdatedAt   time.Time   `orm:"column(updated_at);auto_now" json:"updated_at" description:"记录最后更新时间，系统自动维护"`             // 更新时间
	Children    []*Category `orm:"-" json:"children" description:"子分类列表"`                                                   // 子分类列表，非数据库字段
}

// TableName 设置数据库表名
func (c *Category) TableName() string {
	return "product_category"
}
