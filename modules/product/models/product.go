/**
 * 商品模型
 * 
 * 本文件定义了商品相关的数据模型，包括商品基本信息、价格、库存等字段。
 * 商品是电商平台的核心数据实体，与商家、分类、订单等多个模块相关联。
 */

package models

import (
	"time"
)

// 商品状态常量
const (
	ProductStatusDraft     = 0 // 草稿状态
	ProductStatusPending   = 1 // 待审核状态
	ProductStatusOnSale    = 2 // 上架销售中
	ProductStatusOffSale   = 3 // 已下架
	ProductStatusRejected  = 4 // 审核拒绝
)

// Product 商品模型
type Product struct {
	ID              int64     `orm:"pk;auto;column(id)" json:"id" description:"商品的唯一标识，系统自动生成的主键"`                        // 商品ID，主键
	MerchantID      int64     `orm:"column(merchant_id)" json:"merchant_id" description:"商品所属的商家ID，关联商家表"`              // 所属商家ID
	CategoryID      int64     `orm:"column(category_id)" json:"category_id" description:"商品所属的分类ID，关联分类表"`              // 商品分类ID
	Name            string    `orm:"column(name);size(100)" json:"name" description:"商品的名称，最多100个字符"`                  // 商品名称
	Brief           string    `orm:"column(brief);size(200)" json:"brief" description:"商品的简短介绍，最多200个字符"`                // 商品简介
	Description     string    `orm:"column(description);type(text)" json:"description" description:"商品的详细描述信息，支持富文本"`   // 商品详细描述
	MainImage       string    `orm:"column(main_image);size(255)" json:"main_image" description:"商品主图的URL地址，用于列表展示"`      // 商品主图URL
	Price           float64   `orm:"column(price);digits(10);decimals(2)" json:"price" description:"商品当前售价，精确到2位小数"`   // 商品价格
	OriginalPrice   float64   `orm:"column(original_price);digits(10);decimals(2)" json:"original_price" description:"商品原始标价，用于显示折扣"` // 商品原价
	CostPrice       float64   `orm:"column(cost_price);digits(10);decimals(2)" json:"cost_price" description:"商品成本价格，用于计算利润"`         // 商品成本价
	Stock           int       `orm:"column(stock);default(0)" json:"stock" description:"商品当前库存数量，默认为0"`               // 商品库存数量
	SoldNum         int       `orm:"column(sold_num);default(0)" json:"sold_num" description:"商品累计销售数量，默认为0"`         // 已售数量
	Unit            string    `orm:"column(unit);size(20)" json:"unit" description:"商品计量单位，如：件、个、箱等"`                   // 商品单位（如：件、个、箱）
	Weight          float64   `orm:"column(weight);digits(10);decimals(2)" json:"weight" description:"商品重量，单位为克"` // 商品重量（单位：克）
	Keywords        string    `orm:"column(keywords);size(255)" json:"keywords" description:"商品搜索关键词，多个关键词用逗号分隔"`          // 搜索关键词
	Tags            string    `orm:"column(tags);size(255)" json:"tags" description:"商品标签，多个标签用逗号分隔"`                  // 商品标签，多个标签用逗号分隔
	Code            string    `orm:"column(code);size(50)" json:"code" description:"商品唯一编码，用于商品管理"`                   // 商品编码
	Barcode         string    `orm:"column(barcode);size(50)" json:"barcode" description:"商品条形码，用于扫码识别"`             // 商品条形码
	IsRecommend     bool      `orm:"column(is_recommend);default(false)" json:"is_recommend" description:"是否为推荐商品，用于首页推荐"` // 是否推荐商品
	IsHot           bool      `orm:"column(is_hot);default(false)" json:"is_hot" description:"是否为热销商品，用于热销榜单"`         // 是否热销商品
	IsNew           bool      `orm:"column(is_new);default(false)" json:"is_new" description:"是否为新品，用于新品推荐"`         // 是否新品
	Status          int       `orm:"column(status);default(0)" json:"status" description:"商品状态：0草稿，1待审核，2上架，3下架，4拒绝"`             // 商品状态：0草稿，1待审核，2上架，3下架，4拒绝
	RejectReason    string    `orm:"column(reject_reason);size(255)" json:"reject_reason" description:"商品审核拒绝的原因说明"` // 拒绝原因
	SaleStartTime   time.Time `orm:"column(sale_start_time);null" json:"sale_start_time" description:"商品上架开始时间，可以为空"` // 上架开始时间
	SaleEndTime     time.Time `orm:"column(sale_end_time);null" json:"sale_end_time" description:"商品上架结束时间，可以为空"`     // 上架结束时间
	ViewCount       int       `orm:"column(view_count);default(0)" json:"view_count" description:"商品被浏览的次数统计"`     // 浏览次数
	CommentCount    int       `orm:"column(comment_count);default(0)" json:"comment_count" description:"商品评论的总数统计"` // 评论数量
	FavoriteCount   int       `orm:"column(favorite_count);default(0)" json:"favorite_count" description:"商品被收藏的次数统计"` // 收藏数量
	FreightTemplate int64     `orm:"column(freight_template)" json:"freight_template" description:"商品关联的运费模板ID"`    // 运费模板ID
	HasSKU          bool      `orm:"column(has_sku);default(false)" json:"has_sku" description:"商品是否有规格选项，用于SKU管理"`       // 是否有SKU
	DeliveryType    int       `orm:"column(delivery_type);default(0)" json:"delivery_type" description:"配送方式：0无需配送，1物流配送"` // 配送方式：0无需配送，1物流配送
	CreatedAt       time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"记录创建时间，系统自动生成"`   // 创建时间
	UpdatedAt       time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"记录最后更新时间，系统自动维护"`       // 更新时间
}

// TableName 设置数据库表名
func (p *Product) TableName() string {
	return "product"
}
