/**
 * 商品评论模型
 *
 * 本文件定义了商品评论相关的数据模型，用于管理用户对商品的评价信息。
 * 评论是电商平台的重要功能，帮助用户了解商品的真实使用体验。
 */

package models

import (
	"time"
)

// 评论类型常量
const (
	CommentTypeNormal = 0 // 普通评论
	CommentTypeAppend = 1 // 追加评论
	CommentTypeReply  = 2 // 商家回复
)

// 评分等级常量
const (
	RatingBad    = 1 // 差评 (1-2星)
	RatingNormal = 2 // 中评 (3星)
	RatingGood   = 3 // 好评 (4-5星)
)

// 评论状态常量
const (
	CommentStatusPending  = 0 // 待审核
	CommentStatusApproved = 1 // 已通过
	CommentStatusRejected = 2 // 已拒绝
)

// ProductComment 商品评论模型
type ProductComment struct {
	ID             int64     `orm:"pk;auto;column(id);description(评论的唯一标识，系统自动生成)" json:"id"`
	ProductID      int64     `orm:"column(product_id);index;description(关联的商品ID)" json:"product_id"`
	OrderID        int64     `orm:"column(order_id);description(关联的订单ID)" json:"order_id"`
	UserID         int64     `orm:"column(user_id);description(发表评论的用户ID)" json:"user_id"`
	UserName       string    `orm:"column(user_name);size(50);description(发表评论的用户名称)" json:"user_name"`
	UserAvatar     string    `orm:"column(user_avatar);size(255);description(发表评论的用户头像)" json:"user_avatar"`
	MerchantID     int64     `orm:"column(merchant_id);description(商品所属商家ID)" json:"merchant_id"`
	ParentID       int64     `orm:"column(parent_id);default(0);description(父评论ID，用于关联追加评论或商家回复)" json:"parent_id"`
	Type           int       `orm:"column(type);default(0);description(评论类型：0普通评论，1追加评论，2商家回复)" json:"type"`
	Content        string    `orm:"column(content);type(text);description(评论的文字内容)" json:"content"`
	Images         string    `orm:"column(images);type(text);description(评论图片，多张图片URL以JSON数组格式存储)" json:"images"`
	Rating         int       `orm:"column(rating);default(5);description(商品评分，范围1-5星)" json:"rating"`
	RatingLevel    int       `orm:"column(rating_level);default(3);description(评分等级：1差评，2中评，3好评)" json:"rating_level"`
	IsAnonymous    bool      `orm:"column(is_anonymous);default(false);description(是否匿名发表评论)" json:"is_anonymous"`
	SpecInfo       string    `orm:"column(spec_info);size(255);description(购买商品的规格信息，如：颜色:红色,尺寸:XL)" json:"spec_info"`
	TagInfo        string    `orm:"column(tag_info);size(255);description(评价标签信息，如：质量好,服务好)" json:"tag_info"`
	LikeCount      int       `orm:"column(like_count);default(0);description(评论获得的点赞数量)" json:"like_count"`
	Status         int       `orm:"column(status);default(1);description(评论状态：0隐藏，1显示)" json:"status"`
	CreatedAt      time.Time `orm:"column(created_at);auto_now_add;description(评论创建时间，系统自动生成)" json:"created_at"`
	UpdatedAt      time.Time `orm:"column(updated_at);auto_now;description(评论更新时间，系统自动维护)" json:"updated_at"`
	IsReplied      bool      `orm:"column(is_replied);default(false);description(是否已回复)" json:"is_replied"`
	ReplyContent   string    `orm:"column(reply_content);type(text);null;description(商家回复内容)" json:"reply_content"`
	ReplyAdmin     string    `orm:"column(reply_admin);size(50);null;description(回复管理员名称)" json:"reply_admin"`
	ReplyTime      time.Time `orm:"column(reply_time);null;type(datetime);description(回复时间)" json:"reply_time"`
	HasProductSku  bool      `orm:"column(has_product_sku);default(false);description(是否包含商品规格)" json:"has_product_sku"`
	ProductSkuInfo string    `orm:"column(product_sku_info);size(500);description(商品规格详细信息)" json:"product_sku_info"`
}

// TableName 设置数据库表名
func (p *ProductComment) TableName() string {
	return "product_comment"
}

// CommentImage 评论图片模型
type CommentImage struct {
	ID        int64     `orm:"pk;auto;column(id)" json:"id"`
	CommentID int64     `orm:"column(comment_id);index" json:"comment_id"`
	ImageURL  string    `orm:"column(image_url);size(255)" json:"image_url"`
	SortOrder int       `orm:"column(sort_order);default(0)" json:"sort_order"`
	CreatedAt time.Time `orm:"column(created_at);auto_now_add" json:"created_at"`
	UpdatedAt time.Time `orm:"column(updated_at);auto_now" json:"updated_at"`
}

func (c *CommentImage) TableName() string {
	return "comment_image"
}
