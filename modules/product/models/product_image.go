/**
 * 商品图片模型
 *
 * 本文件定义了商品图片的数据模型，用于管理商品的多图片展示。
 * 除了商品主图外，一个商品可以有多张图片用于详细展示不同角度和细节。
 */

package models

import (
	"time"
)

// 商品图片类型常量
const (
	ProductImageTypeNormal    = 1 // 普通商品图片
	ProductImageTypeDetail    = 2 // 商品详情图片
	ProductImageTypeBanner    = 3 // 商品轮播图片
	ProductImageTypeThumbnail = 4 // 商品缩略图
)

// ProductImage 商品图片模型
type ProductImage struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ProductID int64     `gorm:"column:product_id;index:idx_product_id" json:"product_id"` // 商品ID
	ImageURL  string    `gorm:"column:image_url;size:255" json:"image_url"`               // 图片URL
	SortOrder int       `gorm:"column:sort_order;default:0" json:"sort_order"`            // 排序顺序
	ImageType int       `gorm:"column:image_type;default:1" json:"image_type"`            // 图片类型：1-普通图片，2-详情图片
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`       // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`       // 更新时间
}

// TableName 设置表名
func (ProductImage) TableName() string {
	return "product_images"
}
