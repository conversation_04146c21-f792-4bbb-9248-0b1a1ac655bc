/**
 * 商品SKU模型
 * 
 * 本文件定义了商品SKU（Stock Keeping Unit，库存管理单元）相关的数据模型。
 * SKU代表具有特定属性组合的商品，如颜色、尺寸等不同规格的商品。
 */

package models

import (
	"time"
)

// ProductSku 商品SKU模型
type ProductSku struct {
	ID            int64     `orm:"pk;auto;column(id)" json:"id" description:"SKU的唯一标识，系统自动生成"`                      // SKU ID，主键
	ProductID     int64     `orm:"column(product_id);index" json:"product_id" description:"关联的商品ID，用于标识SKU所属商品"`        // 所属商品ID
	Name          string    `orm:"column(name);size(200)" json:"name" description:"SKU的名称，通常由规格属性组合而成"`                // SKU名称，通常为属性组合名
	Code          string    `orm:"column(code);size(50)" json:"code" description:"SKU的唯一编码，用于内部管理"`                 // SKU编码
	BarCode       string    `orm:"column(bar_code);size(50)" json:"bar_code" description:"SKU的条形码，用于扫码识别"`         // SKU条形码
	Image         string    `orm:"column(image);size(255)" json:"image" description:"SKU的展示图片URL地址"`              // SKU图片URL
	Price         float64   `orm:"column(price);digits(10);decimals(2)" json:"price" description:"SKU的实际销售价格"` // SKU销售价格
	OriginalPrice float64   `orm:"column(original_price);digits(10);decimals(2)" json:"original_price" description:"SKU的原始价格，用于显示折扣"` // SKU原价
	CostPrice     float64   `orm:"column(cost_price);digits(10);decimals(2)" json:"cost_price" description:"SKU的成本价格，用于计算利润"`         // SKU成本价
	Stock         int       `orm:"column(stock);default(0)" json:"stock" description:"SKU的当前库存数量"`             // SKU库存数量
	SoldNum       int       `orm:"column(sold_num);default(0)" json:"sold_num" description:"SKU的累计销售数量"`       // SKU已售数量
	Weight        float64   `orm:"column(weight);digits(10);decimals(2)" json:"weight" description:"SKU的重量，单位为克"` // SKU重量（单位：克）
	Volume        float64   `orm:"column(volume);digits(10);decimals(3)" json:"volume" description:"SKU的体积，单位为立方厘米"` // SKU体积（单位：立方厘米）
	SpecData      string    `orm:"column(spec_data);type(text)" json:"spec_data" description:"SKU的规格数据，JSON格式存储规格组合信息"`     // SKU规格数据，JSON格式存储
	CreatedAt     time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"记录创建时间，系统自动生成"` // 创建时间
	UpdatedAt     time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"记录最后更新时间，系统自动维护"`     // 更新时间
}

// TableName 设置数据库表名
func (p *ProductSku) TableName() string {
	return "product_sku"
}
