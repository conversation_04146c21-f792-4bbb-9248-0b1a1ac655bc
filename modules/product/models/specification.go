/**
 * 商品规格模型
 *
 * 本文件定义了商品规格和规格值的数据模型，用于管理商品的多规格属性。
 * 规格是商品的属性类别，如颜色、尺寸等，规格值是具体的属性值，如红色、XL等。
 */

package models

import (
	"time"
)

// Specification 商品规格模型
type Specification struct {
	ID          int64     `orm:"pk;auto;column(id);description(规格的唯一标识，系统自动生成)" json:"id"`                                       // 规格ID，主键
	Name        string    `orm:"column(name);size(50);description(规格的名称，如颜色、尺寸等)" json:"name"`                                   // 规格名称，如：颜色、尺寸
	Description string    `orm:"column(description);size(255);description(规格的详细描述信息)" json:"description"`                        // 规格描述
	Sort        int       `orm:"column(sort);default(0);description(规格的排序权重，数值越小排序越靠前)" json:"sort"`                             // 排序值，数值越小越靠前
	IsGlobal    bool      `orm:"column(is_global);default(true);description(是否为全局规格，true表示所有商家可用，false表示商家私有)" json:"is_global"` // 是否全局规格，否则为商家私有
	MerchantID  int64     `orm:"column(merchant_id);default(0);description(规格所属的商家ID，全局规格为0)" json:"merchant_id"`                // 商家ID，全局规格为0
	Status      int       `orm:"column(status);default(1);description(规格状态：0表示禁用，1表示启用)" json:"status"`                          // 状态：0禁用，1启用
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add;description(记录创建时间，系统自动生成)" json:"created_at"`                   // 创建时间
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now;description(记录最后更新时间，系统自动维护)" json:"updated_at"`                     // 更新时间
}

// TableName 设置数据库表名
func (s *Specification) TableName() string {
	return "product_specification"
}

// SpecificationValue 规格值模型
type SpecificationValue struct {
	ID        int64     `orm:"pk;auto;column(id);description(规格值唯一ID)" json:"id"`
	SpecID    int64     `orm:"column(spec_id);index;description(所属规格ID)" json:"spec_id"`
	Value     string    `orm:"column(value);size(100);description(规格值内容)" json:"value"`
	SortOrder int       `orm:"column(sort_order);default(0);description(排序值，越小越靠前)" json:"sort_order"`
	CreatedAt time.Time `orm:"column(created_at);auto_now_add;description(记录创建时间，系统自动生成)" json:"created_at"` // 创建时间
	UpdatedAt time.Time `orm:"column(updated_at);auto_now;description(记录最后更新时间，系统自动维护)" json:"updated_at"`   // 更新时间
}

// TableName 设置数据库表名
func (s *SpecificationValue) TableName() string {
	return "product_specification_value"
}

// ProductSpecification 商品规格关联模型
type ProductSpecification struct {
	ID          int64     `orm:"pk;auto;column(id);description(关联记录ID)" json:"id"`
	ProductID   int64     `orm:"column(product_id);index;description(商品ID)" json:"product_id"`
	SpecID      int64     `orm:"column(spec_id);index;description(规格ID)" json:"spec_id"`
	SpecValueID int64     `orm:"column(spec_value_id);index;description(规格值ID)" json:"spec_value_id"`
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add;description(记录创建时间，系统自动生成)" json:"created_at"`
}

// TableName 设置数据库表名
func (s *ProductSpecification) TableName() string {
	return "product_product_specification"
}

// ProductSpecificationValue 商品规格值关联模型
type ProductSpecificationValue struct {
	ID          int64     `orm:"pk;auto;column(id);description(关联记录ID)" json:"id"`
	ProductID   int64     `orm:"column(product_id);index;description(商品ID)" json:"product_id"`
	SpecValueID int64     `orm:"column(spec_value_id);index;description(规格值ID)" json:"spec_value_id"`
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add;description(记录创建时间，系统自动生成)" json:"created_at"`
}

// TableName 设置数据库表名
func (s *ProductSpecificationValue) TableName() string {
	return "product_specification_value_link"
}
