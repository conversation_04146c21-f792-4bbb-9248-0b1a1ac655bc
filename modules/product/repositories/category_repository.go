/**
 * 商品分类仓库实现
 *
 * 本文件实现了商品分类仓库接口，提供对商品分类数据的访问和操作实现。
 * 使用Beego ORM作为数据库访问层，实现对分类表的增删改查等操作。
 */

package repositories

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/models"
)

// CategoryRepositoryImpl 商品分类仓库实现
type CategoryRepositoryImpl struct{}

// NewCategoryRepository 创建分类仓库实例
func NewCategoryRepository() CategoryRepository {
	return &CategoryRepositoryImpl{}
}

// Create 创建分类
// 保存商品分类信息到数据库
func (r *CategoryRepositoryImpl) Create(ctx context.Context, category *models.Category) (int64, error) {
	o := orm.NewOrm()

	// 检查分类名称是否已存在（同一父分类下不允许重名）
	count, err := o.QueryTable(new(models.Category)).
		Filter("name", category.Name).
		Filter("parent_id", category.ParentID).
		Count()
	if err != nil {
		logs.Error("检查分类名称是否存在失败: %v", err)
		return 0, err
	}
	if count > 0 {
		return 0, errors.New("同一父分类下已存在相同名称的分类")
	}

	// 如果父分类ID > 0，则需要检查父分类是否存在
	if category.ParentID > 0 {
		parentExists := o.QueryTable(new(models.Category)).
			Filter("id", category.ParentID).
			Exist()
		if !parentExists {
			return 0, errors.New("父分类不存在")
		}
	}

	// 设置创建时间和更新时间
	now := time.Now()
	category.CreatedAt = now
	category.UpdatedAt = now

	// 插入分类数据
	id, err := o.Insert(category)
	if err != nil {
		logs.Error("创建分类失败: %v", err)
		return 0, err
	}

	return id, nil
}

// GetByID 获取分类信息
// 根据ID查询分类详细信息
func (r *CategoryRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.Category, error) {
	o := orm.NewOrm()
	category := &models.Category{ID: id}

	err := o.Read(category)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询分类失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}

	return category, nil
}

// Update 更新分类信息
// 更新分类的基本信息
func (r *CategoryRepositoryImpl) Update(ctx context.Context, category *models.Category) error {
	o := orm.NewOrm()

	// 检查分类是否存在
	exists, err := r.Exists(ctx, category.ID)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("分类不存在")
	}

	// 检查分类名称是否已被其他分类使用（同一父分类下不允许重名）
	count, err := o.QueryTable(new(models.Category)).
		Filter("name", category.Name).
		Filter("parent_id", category.ParentID).
		Exclude("id", category.ID).
		Count()
	if err != nil {
		logs.Error("检查分类名称是否重复失败: %v", err)
		return err
	}
	if count > 0 {
		return errors.New("同一父分类下已存在相同名称的分类")
	}

	// 检查父分类是否存在
	if category.ParentID > 0 {
		parentExists := o.QueryTable(new(models.Category)).
			Filter("id", category.ParentID).
			Exist()
		if !parentExists {
			return errors.New("父分类不存在")
		}
	}

	// 禁止将分类的父分类设置为自己或其子分类（防止循环引用）
	if category.ParentID == category.ID {
		return errors.New("不能将分类的父分类设置为自己")
	}

	// 检查是否将分类的父分类设置为其子分类
	if category.ParentID > 0 {
		// 获取所有子分类ID
		childIDs, err := r.getAllChildIDs(ctx, category.ID)
		if err != nil {
			logs.Error("获取子分类失败: %v", err)
			return err
		}

		// 检查父分类ID是否在子分类列表中
		for _, childID := range childIDs {
			if childID == category.ParentID {
				return errors.New("不能将分类的父分类设置为其子分类")
			}
		}
	}

	// 设置更新时间
	category.UpdatedAt = time.Now()

	// 更新分类数据
	_, err = o.Update(category)
	if err != nil {
		logs.Error("更新分类失败, ID=%d, 错误: %v", category.ID, err)
		return err
	}

	return nil
}

// Delete 删除分类
// 从数据库中删除指定ID的分类
func (r *CategoryRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 检查分类是否存在
	exists, err := r.Exists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("分类不存在")
	}

	// 检查分类下是否有子分类
	childCount, err := o.QueryTable(new(models.Category)).
		Filter("parent_id", id).
		Count()
	if err != nil {
		logs.Error("检查子分类失败: %v", err)
		return err
	}
	if childCount > 0 {
		return errors.New("分类下还有子分类，无法删除")
	}

	// 检查分类下是否有商品
	productCount, err := o.QueryTable(new(models.Product)).
		Filter("category_id", id).
		Count()
	if err != nil {
		logs.Error("检查分类商品失败: %v", err)
		return err
	}
	if productCount > 0 {
		return errors.New("分类下还有商品，无法删除")
	}

	// 删除分类
	_, err = o.Delete(&models.Category{ID: id})
	if err != nil {
		logs.Error("删除分类失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// GetAll 获取所有分类
// 返回所有分类记录
func (r *CategoryRepositoryImpl) GetAll(ctx context.Context) ([]*models.Category, error) {
	o := orm.NewOrm()

	var categories []*models.Category
	_, err := o.QueryTable(new(models.Category)).
		OrderBy("sort_order", "id").
		All(&categories)
	if err != nil {
		logs.Error("获取所有分类失败: %v", err)
		return nil, err
	}

	return categories, nil
}

// GetTree 获取分类树
// 返回层级结构的分类树
func (r *CategoryRepositoryImpl) GetTree(ctx context.Context) ([]*models.Category, error) {
	// 获取所有分类
	categories, err := r.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	// 构建分类树
	// 这里的实现返回的是根分类，而每个分类的Children字段会填充其子分类
	return r.buildCategoryTree(categories, 0), nil
}

// GetChildren 获取子分类列表
// 返回指定父分类下的所有子分类
func (r *CategoryRepositoryImpl) GetChildren(ctx context.Context, parentID int64) ([]*models.Category, error) {
	o := orm.NewOrm()

	var children []*models.Category
	_, err := o.QueryTable(new(models.Category)).
		Filter("parent_id", parentID).
		OrderBy("sort_order", "id").
		All(&children)
	if err != nil {
		logs.Error("获取子分类失败, 父分类ID=%d, 错误: %v", parentID, err)
		return nil, err
	}

	return children, nil
}

// Exists 分类是否存在
// 检查指定ID的分类是否存在
func (r *CategoryRepositoryImpl) Exists(ctx context.Context, id int64) (bool, error) {
	o := orm.NewOrm()
	exist := o.QueryTable(new(models.Category)).Filter("id", id).Exist()
	return exist, nil
}

// SetShow 设置分类显示状态
// 修改分类的显示状态
func (r *CategoryRepositoryImpl) SetShow(ctx context.Context, id int64, isShow bool) error {
	o := orm.NewOrm()

	// 检查分类是否存在
	exists, err := r.Exists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("分类不存在")
	}

	// 更新显示状态
	_, err = o.Update(&models.Category{
		ID:        id,
		IsShow:    isShow,
		UpdatedAt: time.Now(),
	}, "IsShow", "UpdatedAt")
	if err != nil {
		logs.Error("更新分类显示状态失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// buildCategoryTree 构建分类树
// 内部辅助方法，根据父分类ID构建分类树结构
func (r *CategoryRepositoryImpl) buildCategoryTree(categories []*models.Category, parentID int64) []*models.Category {
	var result []*models.Category

	for _, category := range categories {
		if category.ParentID == parentID {
			// 查找此分类的子分类
			category.Children = r.buildCategoryTree(categories, category.ID)
			result = append(result, category)
		}
	}

	return result
}

// getAllChildIDs 获取所有子分类ID
// 内部辅助方法，递归获取分类下的所有子分类ID
func (r *CategoryRepositoryImpl) getAllChildIDs(ctx context.Context, categoryID int64) ([]int64, error) {
	o := orm.NewOrm()

	// 获取直接子分类
	var children []*models.Category
	_, err := o.QueryTable(new(models.Category)).
		Filter("parent_id", categoryID).
		All(&children, "id")
	if err != nil {
		logs.Error("获取子分类失败, 父分类ID=%d, 错误: %v", categoryID, err)
		return nil, err
	}

	// 提取子分类ID
	var childIDs []int64
	for _, child := range children {
		childIDs = append(childIDs, child.ID)

		// 递归获取子分类的子分类
		subChildIDs, err := r.getAllChildIDs(ctx, child.ID)
		if err != nil {
			return nil, err
		}

		childIDs = append(childIDs, subChildIDs...)
	}

	return childIDs, nil
}
