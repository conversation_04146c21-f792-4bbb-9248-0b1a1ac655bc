/**
 * 商品评论仓库实现
 *
 * 本文件实现了商品评论仓库接口，提供对商品评论数据的访问和操作实现。
 * 使用Beego ORM作为数据库访问层，实现对评论表的增删改查等操作。
 */

package repositories

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/models"
)

// CommentRepositoryImpl 商品评论仓库实现
type CommentRepositoryImpl struct{}

// NewCommentRepository 创建评论仓库实例
func NewCommentRepository() ProductCommentRepository {
	return &CommentRepositoryImpl{}
}

// Create 创建评论
// 保存商品评论信息到数据库
func (r *CommentRepositoryImpl) Create(ctx context.Context, comment *models.ProductComment) (int64, error) {
	o := orm.NewOrm()

	// 检查商品是否存在
	productExists := o.QueryTable(new(models.Product)).
		Filter("id", comment.ProductID).
		Exist()
	if !productExists {
		return 0, errors.New("商品不存在")
	}

	// 设置创建时间
	now := time.Now()
	comment.CreatedAt = now
	comment.UpdatedAt = now

	// 插入评论数据
	id, err := o.Insert(comment)
	if err != nil {
		logs.Error("创建评论失败: %v", err)
		return 0, err
	}

	return id, nil
}

// GetByID 获取评论信息
// 根据ID查询评论详细信息
func (r *CommentRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.ProductComment, error) {
	o := orm.NewOrm()
	comment := &models.ProductComment{ID: id}

	err := o.Read(comment)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询评论失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}

	return comment, nil
}

// Update 更新评论信息
// 主要用于管理员审核评论或回复评论
func (r *CommentRepositoryImpl) Update(ctx context.Context, comment *models.ProductComment) error {
	o := orm.NewOrm()

	existingComment := &models.ProductComment{ID: comment.ID}
	err := o.Read(existingComment)
	if err == orm.ErrNoRows {
		return errors.New("评论不存在")
	}

	comment.UpdatedAt = time.Now()

	// 移除不必要的类型转换
	_, err = o.Update(comment, "reply_content", "reply_admin", "reply_time", "is_replied")
	if err != nil {
		logs.Error("更新评论失败, ID=%d, 错误: %v", comment.ID, err)
		return err
	}

	return nil
}

// Delete 删除评论
// 从数据库中删除指定ID的评论
func (r *CommentRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 检查评论是否存在
	comment := &models.ProductComment{ID: id}
	err := o.Read(comment)
	if err == orm.ErrNoRows {
		return errors.New("评论不存在")
	}
	if err != nil {
		logs.Error("查询评论失败, ID=%d, 错误: %v", id, err)
		return err
	}

	// 删除评论
	_, err = o.Delete(comment)
	if err != nil {
		logs.Error("删除评论失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// GetByProductID 获取商品评论列表
// 返回指定商品的所有评论，支持分页查询
func (r *CommentRepositoryImpl) GetByProductID(ctx context.Context, productID int64, page, pageSize int, status int) ([]*models.ProductComment, int64, error) {
	o := orm.NewOrm()

	// 检查商品是否存在
	productExists := o.QueryTable(new(models.Product)).
		Filter("id", productID).
		Exist()
	if !productExists {
		return nil, 0, errors.New("商品不存在")
	}

	// 构建查询条件
	qs := o.QueryTable(new(models.ProductComment)).
		Filter("product_id", productID)

	// 根据状态过滤
	if status >= 0 {
		qs = qs.Filter("status", status)
	}

	// 计算总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("计算评论总数失败: %v", err)
		return nil, 0, err
	}

	// 查询评论数据
	var comments []*models.ProductComment

	// 处理分页
	offset := (page - 1) * pageSize
	_, err = qs.OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&comments)
	if err != nil {
		logs.Error("查询商品评论列表失败: %v", err)
		return nil, 0, err
	}

	return comments, count, nil
}

// GetAll 获取所有评论
// 返回所有评论，支持分页和状态过滤
func (r *CommentRepositoryImpl) GetAll(ctx context.Context, page, pageSize int, status int) ([]*models.ProductComment, int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.ProductComment))

	// 根据状态过滤
	if status >= 0 {
		qs = qs.Filter("status", status)
	}

	// 计算总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("计算评论总数失败: %v", err)
		return nil, 0, err
	}

	// 查询评论数据
	var comments []*models.ProductComment

	// 处理分页
	offset := (page - 1) * pageSize
	_, err = qs.OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&comments)
	if err != nil {
		logs.Error("查询所有评论列表失败: %v", err)
		return nil, 0, err
	}

	return comments, count, nil
}

// UpdateLikeCount 更新评论点赞数
// 增加或减少评论的点赞数量
func (r *CommentRepositoryImpl) UpdateLikeCount(ctx context.Context, id int64, delta int) error {
	o := orm.NewOrm()

	// 检查评论是否存在
	comment := &models.ProductComment{ID: id}
	err := o.Read(comment)
	if err == orm.ErrNoRows {
		return errors.New("评论不存在")
	}
	if err != nil {
		logs.Error("查询评论失败, ID=%d, 错误: %v", id, err)
		return err
	}

	// 更新点赞数
	_, err = o.QueryTable(new(models.ProductComment)).
		Filter("id", id).
		Update(orm.Params{
			"like_count": orm.ColValue(orm.ColAdd, delta),
			"updated_at": time.Now(),
		})
	if err != nil {
		logs.Error("更新评论点赞数失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// UpdateStatus 更新评论状态
// 更新评论的状态，如审核状态等
func (r *CommentRepositoryImpl) UpdateStatus(ctx context.Context, id int64, status int) error {
	o := orm.NewOrm()

	// 检查评论是否存在
	comment := &models.ProductComment{ID: id}
	err := o.Read(comment)
	if err == orm.ErrNoRows {
		return errors.New("评论不存在")
	}
	if err != nil {
		logs.Error("查询评论失败, ID=%d, 错误: %v", id, err)
		return err
	}

	// 更新状态
	_, err = o.QueryTable(new(models.ProductComment)).
		Filter("id", id).
		Update(orm.Params{
			"status":     status,
			"updated_at": time.Now(),
		})
	if err != nil {
		logs.Error("更新评论状态失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// BatchUpdateStatus 批量更新评论状态
// 批量修改多个评论的审核状态
func (r *CommentRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []int64, status int) error {
	if len(ids) == 0 {
		return nil
	}

	o := orm.NewOrm()

	// 开启事务
	txOrm, err := o.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 批量更新评论状态
	now := time.Now()
	for _, id := range ids {
		comment := &models.ProductComment{ID: id}
		err := txOrm.Read(comment)
		if err != nil {
			// 回滚事务
			err = txOrm.Rollback()
			if err != nil {
				logs.Error("回滚事务失败: %v", err)
			}
			logs.Error("查询评论失败, ID=%d, 错误: %v", id, err)
			return err
		}

		comment.Status = status
		comment.UpdatedAt = now

		_, err = txOrm.Update(comment, "Status", "UpdatedAt")
		if err != nil {
			// 回滚事务
			err = txOrm.Rollback()
			if err != nil {
				logs.Error("回滚事务失败: %v", err)
			}
			logs.Error("更新评论状态失败, ID=%d, 错误: %v", id, err)
			return err
		}
	}

	// 提交事务
	err = txOrm.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// AddReply 添加评论回复
// 添加商品评论的回复信息
func (r *CommentRepositoryImpl) AddReply(ctx context.Context, id int64, replyContent string, replyAdmin string) error {
	o := orm.NewOrm()

	// 检查评论是否存在
	comment := &models.ProductComment{ID: id}
	err := o.Read(comment)
	if err == orm.ErrNoRows {
		return errors.New("评论不存在")
	}
	if err != nil {
		logs.Error("查询评论失败, ID=%d, 错误: %v", id, err)
		return err
	}

	// 更新评论回复信息
	comment.ReplyContent = replyContent
	comment.ReplyAdmin = replyAdmin
	comment.ReplyTime = time.Now()
	comment.IsReplied = true
	comment.UpdatedAt = time.Now()

	_, err = o.Update(comment, "ReplyContent", "ReplyAdmin", "ReplyTime", "IsReplied", "UpdatedAt")
	if err != nil {
		logs.Error("添加评论回复失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// GetCountByStatus 获取不同状态的评论数量
// 返回各状态评论的数量统计
func (r *CommentRepositoryImpl) GetCountByStatus(ctx context.Context) (map[int]int64, error) {
	o := orm.NewOrm()

	// 查询各状态评论数量
	var counts []orm.Params
	_, err := o.Raw("SELECT status, COUNT(*) as count FROM product_comment GROUP BY status").Values(&counts)
	if err != nil {
		logs.Error("统计评论数量失败: %v", err)
		return nil, err
	}

	// 构建结果映射
	result := make(map[int]int64)
	for _, c := range counts {
		status, _ := c["status"].(int)
		count, _ := c["count"].(int64)
		result[status] = count
	}

	return result, nil
}

// GetCommentImages 获取评论图片
// 返回特定评论的所有图片
func (r *CommentRepositoryImpl) GetCommentImages(ctx context.Context, commentID int64) ([]*models.CommentImage, error) {
	o := orm.NewOrm()

	// 检查评论是否存在
	commentExists := o.QueryTable(new(models.ProductComment)).
		Filter("id", commentID).
		Exist()
	if !commentExists {
		return nil, errors.New("评论不存在")
	}

	var images []*models.CommentImage
	_, err := o.QueryTable(new(models.CommentImage)).
		Filter("comment_id", commentID).
		OrderBy("sort_order").
		All(&images)
	if err != nil {
		logs.Error("获取评论图片失败: %v", err)
		return nil, err
	}

	return images, nil
}

// AddCommentImage 添加评论图片
// 保存评论图片信息到数据库
func (r *CommentRepositoryImpl) AddCommentImage(ctx context.Context, image *models.CommentImage) (int64, error) {
	o := orm.NewOrm()

	// 检查评论是否存在
	commentExists := o.QueryTable(new(models.ProductComment)).
		Filter("id", image.CommentID).
		Exist()
	if !commentExists {
		return 0, errors.New("评论不存在")
	}

	// 设置创建时间
	now := time.Now()
	image.CreatedAt = now

	// 插入图片数据
	id, err := o.Insert(image)
	if err != nil {
		logs.Error("添加评论图片失败: %v", err)
		return 0, err
	}

	return id, nil
}

// BatchAddCommentImages 批量添加评论图片
// 批量保存评论图片信息到数据库
func (r *CommentRepositoryImpl) BatchAddCommentImages(ctx context.Context, images []*models.CommentImage) error {
	if len(images) == 0 {
		return nil
	}

	o := orm.NewOrm()

	// 检查评论是否存在
	commentID := images[0].CommentID
	commentExists := o.QueryTable(new(models.ProductComment)).
		Filter("id", commentID).
		Exist()
	if !commentExists {
		return errors.New("评论不存在")
	}

	// 开启事务
	txOrm, err := o.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 批量插入图片数据
	now := time.Now()
	for _, image := range images {
		// 设置创建时间
		image.CreatedAt = now

		// 插入图片数据
		_, err := txOrm.Insert(image)
		if err != nil {
			// 回滚事务
			err = txOrm.Rollback()
			if err != nil {
				logs.Error("回滚事务失败: %v", err)
			}
			logs.Error("批量添加评论图片失败: %v", err)
			return err
		}
	}

	// 提交事务
	err = txOrm.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// DeleteCommentImage 删除评论图片
// 从数据库中删除指定ID的评论图片
func (r *CommentRepositoryImpl) DeleteCommentImage(ctx context.Context, imageID int64) error {
	o := orm.NewOrm()

	// 检查图片是否存在
	image := &models.CommentImage{ID: imageID}
	err := o.Read(image)
	if err == orm.ErrNoRows {
		return errors.New("评论图片不存在")
	}
	if err != nil {
		logs.Error("查询评论图片失败, ID=%d, 错误: %v", imageID, err)
		return err
	}

	// 删除图片
	_, err = o.Delete(image)
	if err != nil {
		logs.Error("删除评论图片失败, ID=%d, 错误: %v", imageID, err)
		return err
	}

	return nil
}

// ListByParentID 获取父评论下的所有回复
// 返回指定父评论ID下的所有回复评论
func (r *CommentRepositoryImpl) ListByParentID(ctx context.Context, parentID int64) ([]*models.ProductComment, error) {
	o := orm.NewOrm()

	// 检查父评论是否存在
	parentExists := o.QueryTable(new(models.ProductComment)).
		Filter("id", parentID).
		Exist()
	if !parentExists {
		return nil, errors.New("父评论不存在")
	}

	// 查询回复评论
	var comments []*models.ProductComment
	_, err := o.QueryTable(new(models.ProductComment)).
		Filter("parent_id", parentID).
		OrderBy("created_at").
		All(&comments)
	if err != nil {
		logs.Error("查询回复评论列表失败: %v", err)
		return nil, err
	}

	return comments, nil
}

// ListByProductID 获取商品评论列表
// 根据商品ID查询评论列表，支持分页和条件查询
func (r *CommentRepositoryImpl) ListByProductID(ctx context.Context, productID int64, query map[string]interface{}, page, pageSize int) ([]*models.ProductComment, int64, error) {
	o := orm.NewOrm()

	// 检查商品是否存在
	productExists := o.QueryTable(new(models.Product)).
		Filter("id", productID).
		Exist()
	if !productExists {
		return nil, 0, errors.New("商品不存在")
	}

	// 构建查询条件
	qs := o.QueryTable(new(models.ProductComment)).
		Filter("product_id", productID)

	// 应用查询条件
	if query != nil {
		// 根据状态过滤
		if status, ok := query["status"]; ok {
			qs = qs.Filter("status", status)
		}

		// 根据评分过滤
		if rating, ok := query["rating"]; ok {
			qs = qs.Filter("rating", rating)
		}

		// 根据是否有图片过滤
		if hasImage, ok := query["has_image"]; ok && hasImage.(bool) {
			qs = qs.Filter("image_urls__isnull", false)
		}

		// 根据是否已回复过滤
		if isReplied, ok := query["is_replied"]; ok {
			qs = qs.Filter("is_replied", isReplied)
		}
	}

	// 计算总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("计算评论总数失败: %v", err)
		return nil, 0, err
	}

	// 查询评论数据
	var comments []*models.ProductComment

	// 处理分页
	offset := (page - 1) * pageSize
	_, err = qs.OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&comments)
	if err != nil {
		logs.Error("查询商品评论列表失败: %v", err)
		return nil, 0, err
	}

	return comments, count, nil
}

// ListByUserID 获取用户评论列表
// 根据用户ID查询评论列表，支持分页和条件查询
func (r *CommentRepositoryImpl) ListByUserID(ctx context.Context, userID int64, query map[string]interface{}, page, pageSize int) ([]*models.ProductComment, int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.ProductComment)).
		Filter("user_id", userID)

	// 应用查询条件
	if query != nil {
		// 根据状态过滤
		if status, ok := query["status"]; ok {
			qs = qs.Filter("status", status)
		}

		// 根据评分过滤
		if rating, ok := query["rating"]; ok {
			qs = qs.Filter("rating", rating)
		}

		// 根据商品ID过滤
		if productID, ok := query["product_id"]; ok {
			qs = qs.Filter("product_id", productID)
		}

		// 根据是否已回复过滤
		if isReplied, ok := query["is_replied"]; ok {
			qs = qs.Filter("is_replied", isReplied)
		}
	}

	// 计算总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("计算用户评论总数失败: %v", err)
		return nil, 0, err
	}

	// 查询评论数据
	var comments []*models.ProductComment

	// 处理分页
	offset := (page - 1) * pageSize
	_, err = qs.OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&comments)
	if err != nil {
		logs.Error("查询用户评论列表失败: %v", err)
		return nil, 0, err
	}

	return comments, count, nil
}

// CountByProductID 获取指定商品的评论总数
func (r *CommentRepositoryImpl) CountByProductID(ctx context.Context, productID int64) (int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.ProductComment)).
		Filter("product_id", productID)

	// 计算总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("计算评论总数失败: %v", err)
		return 0, err
	}

	return count, nil
}

// CountByProductIDAndRatingLevel 获取指定商品指定评分等级的评论数量
func (r *CommentRepositoryImpl) CountByProductIDAndRatingLevel(ctx context.Context, productID int64, rating int) (int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.ProductComment)).
		Filter("product_id", productID).
		Filter("rating", rating)

	// 计算总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("计算评论数量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetByUserID 获取用户的评论列表
func (r *CommentRepositoryImpl) GetByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*models.ProductComment, int64, error) {
	o := orm.NewOrm()

	// 构建查询条件
	qs := o.QueryTable(new(models.ProductComment)).
		Filter("user_id", userID)

	// 计算总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("计算用户评论总数失败: %v", err)
		return nil, 0, err
	}

	// 查询评论数据
	var comments []*models.ProductComment

	// 处理分页
	offset := (page - 1) * pageSize
	_, err = qs.OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&comments)
	if err != nil {
		logs.Error("查询用户评论列表失败: %v", err)
		return nil, 0, err
	}

	return comments, count, nil
}
