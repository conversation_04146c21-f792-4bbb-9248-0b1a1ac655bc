/**
 * 商品仓库接口定义
 *
 * 本文件定义了商品模块的仓库层接口，提供对商品数据的访问和操作方法。
 * 包括商品、SKU、分类、规格等多个仓库接口的定义。
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/product/models"
)

// ProductRepository 商品仓库接口
type ProductRepository interface {
	// 创建商品
	Create(ctx context.Context, product *models.Product) (int64, error)

	// 获取商品信息
	GetByID(ctx context.Context, id int64) (*models.Product, error)

	// 更新商品信息
	Update(ctx context.Context, product *models.Product) error

	// 删除商品
	Delete(ctx context.Context, id int64) error

	// 批量删除商品
	BatchDelete(ctx context.Context, ids []int64) error

	// 更新商品状态
	UpdateStatus(ctx context.Context, id int64, status int, rejectReason string) error

	// 查询商品列表
	List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.Product, int64, error)

	// 获取商家的商品数量
	GetCountByMerchant(ctx context.Context, merchantID int64) (int64, error)

	// 获取分类下的商品数量
	GetCountByCategory(ctx context.Context, categoryID int64) (int64, error)

	// 增加商品浏览次数
	IncrViewCount(ctx context.Context, id int64) error

	// 增加商品评论数量
	IncrCommentCount(ctx context.Context, id int64) error

	// 增加商品收藏数量
	IncrFavoriteCount(ctx context.Context, id int64, delta int) error

	// 减少商品库存
	DecrStock(ctx context.Context, id int64, quantity int) error

	// 增加商品销售量
	IncrSoldNum(ctx context.Context, id int64, quantity int) error

	// 检查商品是否存在
	Exists(ctx context.Context, id int64) (bool, error)

	// 获取推荐商品列表
	GetRecommend(ctx context.Context, limit int) ([]*models.Product, error)

	// 获取热销商品列表
	GetHot(ctx context.Context, limit int) ([]*models.Product, error)

	// 获取新品商品列表
	GetNew(ctx context.Context, limit int) ([]*models.Product, error)
}

// ProductSkuRepository 商品SKU仓库接口
type ProductSkuRepository interface {
	// 创建商品SKU
	Create(ctx context.Context, sku *models.ProductSku) (int64, error)

	// 批量创建商品SKU
	BatchCreate(ctx context.Context, skus []*models.ProductSku) error

	// 获取SKU信息
	GetByID(ctx context.Context, id int64) (*models.ProductSku, error)

	// 更新SKU信息
	Update(ctx context.Context, sku *models.ProductSku) error

	// 删除SKU
	Delete(ctx context.Context, id int64) error

	// 批量删除SKU
	BatchDeleteByProductID(ctx context.Context, productID int64) error

	// 获取商品的所有SKU
	ListByProductID(ctx context.Context, productID int64) ([]*models.ProductSku, error)

	// 减少SKU库存
	DecrStock(ctx context.Context, id int64, quantity int) error

	// 增加SKU销售量
	IncrSoldNum(ctx context.Context, id int64, quantity int) error

	// 检查SKU库存是否足够
	CheckStock(ctx context.Context, id int64, quantity int) (bool, error)
}

// CategoryRepository 商品分类仓库接口
type CategoryRepository interface {
	// 创建分类
	Create(ctx context.Context, category *models.Category) (int64, error)

	// 获取分类信息
	GetByID(ctx context.Context, id int64) (*models.Category, error)

	// 更新分类信息
	Update(ctx context.Context, category *models.Category) error

	// 删除分类
	Delete(ctx context.Context, id int64) error

	// 获取所有分类
	GetAll(ctx context.Context) ([]*models.Category, error)

	// 获取分类树
	GetTree(ctx context.Context) ([]*models.Category, error)

	// 获取子分类列表
	GetChildren(ctx context.Context, parentID int64) ([]*models.Category, error)

	// 分类是否存在
	Exists(ctx context.Context, id int64) (bool, error)

	// 设置分类显示状态
	SetShow(ctx context.Context, id int64, isShow bool) error
}

// SpecificationRepository 规格仓库接口
type SpecificationRepository interface {
	// 创建规格
	Create(ctx context.Context, spec *models.Specification) (int64, error)

	// 获取规格信息
	GetByID(ctx context.Context, id int64) (*models.Specification, error)

	// 更新规格信息
	Update(ctx context.Context, spec *models.Specification) error

	// 删除规格
	Delete(ctx context.Context, id int64) error

	// 获取所有规格
	GetAll(ctx context.Context, query map[string]interface{}) ([]*models.Specification, error)

	// 创建规格值
	CreateValue(ctx context.Context, value *models.SpecificationValue) (int64, error)

	// 批量创建规格值
	BatchCreateValues(ctx context.Context, values []*models.SpecificationValue) error

	// 获取规格值
	GetValueByID(ctx context.Context, id int64) (*models.SpecificationValue, error)

	// 更新规格值
	UpdateValue(ctx context.Context, value *models.SpecificationValue) error

	// 删除规格值
	DeleteValue(ctx context.Context, id int64) error

	// 获取规格的所有值
	GetValuesBySpecID(ctx context.Context, specID int64) ([]*models.SpecificationValue, error)

	// 关联商品规格
	LinkProductSpec(ctx context.Context, link *models.ProductSpecification) (int64, error)

	// 删除商品规格关联
	UnlinkProductSpec(ctx context.Context, productID int64, specID int64) error

	// 删除商品的所有规格关联
	UnlinkAllProductSpec(ctx context.Context, productID int64) error

	// 获取商品关联的所有规格
	GetSpecsByProductID(ctx context.Context, productID int64) ([]*models.ProductSpecification, error)

	// 关联商品规格值
	LinkProductSpecValue(ctx context.Context, link *models.ProductSpecificationValue) (int64, error)

	// 批量关联商品规格值
	BatchLinkProductSpecValue(ctx context.Context, links []*models.ProductSpecificationValue) error

	// 删除商品的规格值关联
	UnlinkProductSpecValue(ctx context.Context, productID int64, specID int64) error

	// 删除商品的所有规格值关联
	UnlinkAllProductSpecValue(ctx context.Context, productID int64) error

	// 获取商品关联的所有规格值
	GetSpecValuesByProductID(ctx context.Context, productID int64) ([]*models.ProductSpecificationValue, error)
}

// ProductImageRepository 商品图片仓库接口
type ProductImageRepository interface {
	// 创建商品图片
	Create(ctx context.Context, image *models.ProductImage) (int64, error)

	// 批量创建商品图片
	BatchCreate(ctx context.Context, images []*models.ProductImage) error

	// 获取图片信息
	GetByID(ctx context.Context, id int64) (*models.ProductImage, error)

	// 更新图片信息
	Update(ctx context.Context, image *models.ProductImage) error

	// 删除图片
	Delete(ctx context.Context, id int64) error

	// 批量删除图片
	BatchDelete(ctx context.Context, ids []int64) error

	// 删除商品的所有图片
	DeleteByProductID(ctx context.Context, productID int64) error

	// 获取商品的所有图片
	ListByProductID(ctx context.Context, productID int64, imageType int) ([]*models.ProductImage, error)
}

// ProductCommentRepository 商品评论仓库接口
type ProductCommentRepository interface {
	// 创建商品评论
	Create(ctx context.Context, comment *models.ProductComment) (int64, error)

	// 获取评论信息
	GetByID(ctx context.Context, id int64) (*models.ProductComment, error)

	// 更新评论信息
	Update(ctx context.Context, comment *models.ProductComment) error

	// 删除评论
	Delete(ctx context.Context, id int64) error

	// 获取商品的所有评论
	ListByProductID(ctx context.Context, productID int64, query map[string]interface{}, page, pageSize int) ([]*models.ProductComment, int64, error)

	// 获取用户的所有评论
	ListByUserID(ctx context.Context, userID int64, query map[string]interface{}, page, pageSize int) ([]*models.ProductComment, int64, error)

	// 获取父评论下的所有回复
	ListByParentID(ctx context.Context, parentID int64) ([]*models.ProductComment, error)

	// 更新评论点赞数
	UpdateLikeCount(ctx context.Context, id int64, delta int) error

	// 更新评论状态
	UpdateStatus(ctx context.Context, id int64, status int) error

	// 批量更新评论状态
	BatchUpdateStatus(ctx context.Context, ids []int64, status int) error

	// 添加评论回复
	AddReply(ctx context.Context, id int64, replyContent string, replyAdmin string) error

	// 获取不同状态的评论数量
	GetCountByStatus(ctx context.Context) (map[int]int64, error)

	// 获取评论图片
	GetCommentImages(ctx context.Context, commentID int64) ([]*models.CommentImage, error)

	// 添加评论图片
	AddCommentImage(ctx context.Context, image *models.CommentImage) (int64, error)

	// 批量添加评论图片
	BatchAddCommentImages(ctx context.Context, images []*models.CommentImage) error

	// 删除评论图片
	DeleteCommentImage(ctx context.Context, imageID int64) error

	// 获取商品评论列表
	GetByProductID(ctx context.Context, productID int64, page, pageSize int, status int) ([]*models.ProductComment, int64, error)

	// 获取所有评论
	GetAll(ctx context.Context, page, pageSize int, status int) ([]*models.ProductComment, int64, error)

	// 获取指定商品的评论总数
	CountByProductID(ctx context.Context, productID int64) (int64, error)

	// 获取指定商品指定评分等级的评论数量
	CountByProductIDAndRatingLevel(ctx context.Context, productID int64, rating int) (int64, error)

	// 获取用户的评论列表
	GetByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*models.ProductComment, int64, error)
}

// CommentRepository = ProductCommentRepository 类型别名
type CommentRepository = ProductCommentRepository
