/**
 * 商品图片仓库实现
 *
 * 本文件实现了商品图片仓库接口，提供对商品图片数据的访问和操作方法。
 */

package repositories

import (
	"context"
	"errors"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/models"
)

// ProductImageRepositoryImpl 商品图片仓库实现
type ProductImageRepositoryImpl struct {
}

// NewProductImageRepository 创建商品图片仓库实例
func NewProductImageRepository() ProductImageRepository {
	return &ProductImageRepositoryImpl{}
}

// Create 创建商品图片
func (r *ProductImageRepositoryImpl) Create(ctx context.Context, image *models.ProductImage) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(image)
	if err != nil {
		logs.Error("创建商品图片失败: %v", err)
		return 0, err
	}
	return id, nil
}

// BatchCreate 批量创建商品图片
func (r *ProductImageRepositoryImpl) BatchCreate(ctx context.Context, images []*models.ProductImage) error {
	if len(images) == 0 {
		return nil
	}

	o := orm.NewOrm()
	_, err := o.InsertMulti(len(images), images)
	if err != nil {
		logs.Error("批量创建商品图片失败: %v", err)
		return err
	}
	return nil
}

// GetByID 获取图片信息
func (r *ProductImageRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.ProductImage, error) {
	o := orm.NewOrm()
	image := &models.ProductImage{ID: id}
	err := o.Read(image)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("获取商品图片信息失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}
	return image, nil
}

// Update 更新图片信息
func (r *ProductImageRepositoryImpl) Update(ctx context.Context, image *models.ProductImage) error {
	o := orm.NewOrm()
	_, err := o.Update(image)
	if err != nil {
		logs.Error("更新商品图片信息失败: %v", err)
		return err
	}
	return nil
}

// Delete 删除图片
func (r *ProductImageRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	_, err := o.Delete(&models.ProductImage{ID: id})
	if err != nil {
		logs.Error("删除商品图片失败, ID=%d, 错误: %v", id, err)
		return err
	}
	return nil
}

// BatchDelete 批量删除图片
func (r *ProductImageRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	o := orm.NewOrm()
	qs := o.QueryTable(new(models.ProductImage))

	// 由于ORM不支持直接使用IN查询删除，需要分批删除
	for _, id := range ids {
		_, err := qs.Filter("id", id).Delete()
		if err != nil {
			logs.Error("批量删除商品图片失败, ID=%d, 错误: %v", id, err)
			return err
		}
	}
	return nil
}

// DeleteByProductID 删除商品的所有图片
func (r *ProductImageRepositoryImpl) DeleteByProductID(ctx context.Context, productID int64) error {
	if productID <= 0 {
		return errors.New("无效的商品ID")
	}

	o := orm.NewOrm()
	_, err := o.QueryTable(new(models.ProductImage)).Filter("product_id", productID).Delete()
	if err != nil {
		logs.Error("删除商品图片失败, ProductID=%d, 错误: %v", productID, err)
		return err
	}
	return nil
}

// ListByProductID 获取商品的所有图片
func (r *ProductImageRepositoryImpl) ListByProductID(ctx context.Context, productID int64, imageType int) ([]*models.ProductImage, error) {
	o := orm.NewOrm()
	var images []*models.ProductImage
	qs := o.QueryTable(new(models.ProductImage)).Filter("product_id", productID)

	if imageType > 0 {
		qs = qs.Filter("image_type", imageType)
	}

	_, err := qs.OrderBy("sort_order").All(&images)
	if err != nil {
		logs.Error("获取商品图片列表失败, ProductID=%d, 错误: %v", productID, err)
		return nil, err
	}
	return images, nil
}
