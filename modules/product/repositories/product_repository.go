/**
 * 商品仓库实现
 *
 * 本文件实现了商品仓库接口，提供对商品数据的访问和操作实现。
 * 使用Beego ORM作为数据库访问层，实现对商品表的增删改查等操作。
 */

package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/models"
)

// ProductRepositoryImpl 商品仓库实现
type ProductRepositoryImpl struct{}

// NewProductRepository 创建商品仓库实例
func NewProductRepository() ProductRepository {
	return &ProductRepositoryImpl{}
}

// Create 创建商品
// 保存商品基本信息到数据库
func (r *ProductRepositoryImpl) Create(ctx context.Context, product *models.Product) (int64, error) {
	o := orm.NewOrm()

	// 检查商品名称是否已存在
	count, err := o.QueryTable(new(models.Product)).
		Filter("name", product.Name).
		Count()
	if err != nil {
		logs.Error("查询商品名称是否存在失败: %v", err)
		return 0, err
	}
	if count > 0 {
		return 0, errors.New("商品名称已存在")
	}

	// 设置创建时间和更新时间
	now := time.Now()
	product.CreatedAt = now
	product.UpdatedAt = now

	// 插入商品数据
	id, err := o.Insert(product)
	if err != nil {
		logs.Error("创建商品失败: %v", err)
		return 0, err
	}

	return id, nil
}

// GetByID 获取商品信息
// 根据ID查询商品详细信息
func (r *ProductRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.Product, error) {
	o := orm.NewOrm()
	product := &models.Product{ID: id}

	// 查询商品
	err := o.Read(product)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询商品失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}

	return product, nil
}

// Update 更新商品信息
// 更新商品的基本信息
func (r *ProductRepositoryImpl) Update(ctx context.Context, product *models.Product) error {
	o := orm.NewOrm()

	// 检查商品是否存在
	exists, err := r.Exists(ctx, product.ID)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("商品不存在")
	}

	// 检查商品名称是否已被其他商品使用
	count, err := o.QueryTable(new(models.Product)).
		Filter("name", product.Name).
		Exclude("id", product.ID).
		Count()
	if err != nil {
		logs.Error("检查商品名称是否重复失败: %v", err)
		return err
	}
	if count > 0 {
		return errors.New("商品名称已被其他商品使用")
	}

	// 设置更新时间
	product.UpdatedAt = time.Now()

	// 更新商品数据
	_, err = o.Update(product)
	if err != nil {
		logs.Error("更新商品失败, ID=%d, 错误: %v", product.ID, err)
		return err
	}

	return nil
}

// Delete 删除商品
// 从数据库中删除指定ID的商品
func (r *ProductRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 检查商品是否存在
	exists, err := r.Exists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("商品不存在")
	}

	// 删除商品
	_, err = o.Delete(&models.Product{ID: id})
	if err != nil {
		logs.Error("删除商品失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// BatchDelete 批量删除商品
// 删除多个商品记录
func (r *ProductRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	o := orm.NewOrm()

	// 构建IN查询条件
	_, err := o.QueryTable(new(models.Product)).
		Filter("id__in", ids).
		Delete()
	if err != nil {
		logs.Error("批量删除商品失败, IDs=%v, 错误: %v", ids, err)
		return err
	}

	return nil
}

// UpdateStatus 更新商品状态
// 修改商品状态，如上架、下架等
func (r *ProductRepositoryImpl) UpdateStatus(ctx context.Context, id int64, status int, rejectReason string) error {
	o := orm.NewOrm()

	// 检查商品是否存在
	exists, err := r.Exists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("商品不存在")
	}

	// 验证状态值是否合法
	if status < models.ProductStatusDraft || status > models.ProductStatusRejected {
		return errors.New("无效的商品状态")
	}

	// 更新状态字段
	product := &models.Product{ID: id}
	err = o.Read(product)
	if err != nil {
		logs.Error("读取商品信息失败, ID=%d, 错误: %v", id, err)
		return err
	}

	product.Status = status
	if status == models.ProductStatusRejected && rejectReason != "" {
		product.RejectReason = rejectReason
	}
	product.UpdatedAt = time.Now()

	_, err = o.Update(product, "Status", "RejectReason", "UpdatedAt")
	if err != nil {
		logs.Error("更新商品状态失败, ID=%d, 状态=%d, 错误: %v", id, status, err)
		return err
	}

	return nil
}

// List 查询商品列表
// 根据查询条件分页获取商品列表
func (r *ProductRepositoryImpl) List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.Product, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.Product))

	// 构建查询条件
	for key, value := range query {
		switch key {
		case "name":
			qs = qs.Filter("name__icontains", value)
		case "category_id":
			qs = qs.Filter("category_id", value)
		case "merchant_id":
			qs = qs.Filter("merchant_id", value)
		case "status":
			qs = qs.Filter("status", value)
		case "is_recommend":
			qs = qs.Filter("is_recommend", value)
		case "is_hot":
			qs = qs.Filter("is_hot", value)
		case "is_new":
			qs = qs.Filter("is_new", value)
		case "price_min":
			qs = qs.Filter("price__gte", value)
		case "price_max":
			qs = qs.Filter("price__lte", value)
		}
	}

	// 获取总记录数
	total, err := qs.Count()
	if err != nil {
		logs.Error("获取商品总数失败: %v", err)
		return nil, 0, err
	}

	// 如果没有记录，直接返回空列表
	if total == 0 {
		return make([]*models.Product, 0), 0, nil
	}

	// 计算分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 排序方式
	sortField := "-created_at" // 默认按创建时间降序
	if sortBy, ok := query["sort_field"].(string); ok && sortBy != "" {
		sortOrder := ""
		if order, ok := query["sort_order"].(string); ok && order == "desc" {
			sortOrder = "-"
		}
		sortField = sortOrder + sortBy
	}

	// 查询分页数据
	var products []*models.Product
	_, err = qs.OrderBy(sortField).Limit(pageSize, offset).All(&products)
	if err != nil {
		logs.Error("查询商品列表失败: %v", err)
		return nil, 0, err
	}

	return products, total, nil
}

// GetCountByMerchant 获取商家的商品数量
// 统计特定商家的商品数量
func (r *ProductRepositoryImpl) GetCountByMerchant(ctx context.Context, merchantID int64) (int64, error) {
	o := orm.NewOrm()
	count, err := o.QueryTable(new(models.Product)).
		Filter("merchant_id", merchantID).
		Count()
	if err != nil {
		logs.Error("获取商家商品数量失败, 商家ID=%d, 错误: %v", merchantID, err)
		return 0, err
	}

	return count, nil
}

// GetCountByCategory 获取分类下的商品数量
// 统计特定分类的商品数量
func (r *ProductRepositoryImpl) GetCountByCategory(ctx context.Context, categoryID int64) (int64, error) {
	o := orm.NewOrm()
	count, err := o.QueryTable(new(models.Product)).
		Filter("category_id", categoryID).
		Count()
	if err != nil {
		logs.Error("获取分类商品数量失败, 分类ID=%d, 错误: %v", categoryID, err)
		return 0, err
	}

	return count, nil
}

// IncrViewCount 增加商品浏览次数
// 每次浏览商品时递增浏览计数
func (r *ProductRepositoryImpl) IncrViewCount(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 直接更新浏览次数，不需要先查询
	_, err := o.Raw("UPDATE product SET view_count = view_count + 1 WHERE id = ?", id).Exec()
	if err != nil {
		logs.Error("增加商品浏览次数失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// IncrCommentCount 增加商品评论数量
// 发表评论时递增评论计数
func (r *ProductRepositoryImpl) IncrCommentCount(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 直接更新评论数量，不需要先查询
	_, err := o.Raw("UPDATE product SET comment_count = comment_count + 1 WHERE id = ?", id).Exec()
	if err != nil {
		logs.Error("增加商品评论数量失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// IncrFavoriteCount 增加商品收藏数量
// 收藏/取消收藏商品时更新收藏计数
func (r *ProductRepositoryImpl) IncrFavoriteCount(ctx context.Context, id int64, delta int) error {
	o := orm.NewOrm()

	// 根据delta增加或减少收藏数
	var sql string
	if delta > 0 {
		sql = "UPDATE product SET favorite_count = favorite_count + ? WHERE id = ?"
	} else {
		sql = "UPDATE product SET favorite_count = GREATEST(0, favorite_count + ?) WHERE id = ?"
	}

	_, err := o.Raw(sql, delta, id).Exec()
	if err != nil {
		logs.Error("更新商品收藏数量失败, ID=%d, delta=%d, 错误: %v", id, delta, err)
		return err
	}

	return nil
}

// DecrStock 减少商品库存
// 下单时减少商品库存
func (r *ProductRepositoryImpl) DecrStock(ctx context.Context, id int64, quantity int) error {
	if quantity <= 0 {
		return errors.New("减少的库存数量必须大于0")
	}

	o := orm.NewOrm()

	// 首先检查库存是否足够
	product := &models.Product{ID: id}
	err := o.Read(product)
	if err != nil {
		logs.Error("查询商品失败, ID=%d, 错误: %v", id, err)
		return err
	}

	if product.Stock < quantity {
		return errors.New(fmt.Sprintf("商品库存不足，当前库存: %d, 需要: %d", product.Stock, quantity))
	}

	// 更新库存
	_, err = o.Raw("UPDATE product SET stock = stock - ? WHERE id = ? AND stock >= ?",
		quantity, id, quantity).Exec()
	if err != nil {
		logs.Error("减少商品库存失败, ID=%d, 数量=%d, 错误: %v", id, quantity, err)
		return err
	}

	return nil
}

// IncrSoldNum 增加商品销售量
// 订单完成时增加商品销售量
func (r *ProductRepositoryImpl) IncrSoldNum(ctx context.Context, id int64, quantity int) error {
	if quantity <= 0 {
		return errors.New("增加的销售数量必须大于0")
	}

	o := orm.NewOrm()

	// 更新销售量
	_, err := o.Raw("UPDATE product SET sold_num = sold_num + ? WHERE id = ?",
		quantity, id).Exec()
	if err != nil {
		logs.Error("增加商品销售量失败, ID=%d, 数量=%d, 错误: %v", id, quantity, err)
		return err
	}

	return nil
}

// Exists 检查商品是否存在
// 验证商品ID是否有效
func (r *ProductRepositoryImpl) Exists(ctx context.Context, id int64) (bool, error) {
	o := orm.NewOrm()
	exist := o.QueryTable(new(models.Product)).Filter("id", id).Exist()
	return exist, nil
}

// GetRecommend 获取推荐商品
// 查询推荐商品列表，按创建时间倒序排列
func (r *ProductRepositoryImpl) GetRecommend(ctx context.Context, limit int) ([]*models.Product, error) {
	o := orm.NewOrm()

	// 最多返回limit条记录
	if limit <= 0 {
		limit = 10 // 默认取10条
	}

	var products []*models.Product
	_, err := o.QueryTable(new(models.Product)).
		Filter("is_recommend", true).
		Filter("status", models.ProductStatusOnSale). // 只获取在售状态的商品
		OrderBy("-created_at").
		Limit(limit).
		All(&products)

	if err != nil {
		logs.Error("获取推荐商品失败: %v", err)
		return nil, err
	}

	return products, nil
}

// GetHot 获取热销商品
// 查询热销商品列表，按销量倒序排列
func (r *ProductRepositoryImpl) GetHot(ctx context.Context, limit int) ([]*models.Product, error) {
	o := orm.NewOrm()

	// 最多返回limit条记录
	if limit <= 0 {
		limit = 10 // 默认取10条
	}

	var products []*models.Product
	_, err := o.QueryTable(new(models.Product)).
		Filter("is_hot", true).
		Filter("status", models.ProductStatusOnSale). // 只获取在售状态的商品
		OrderBy("-sold_num", "-created_at").
		Limit(limit).
		All(&products)

	if err != nil {
		logs.Error("获取热销商品失败: %v", err)
		return nil, err
	}

	return products, nil
}

// GetNew 获取新品商品
// 查询新品商品列表，按创建时间倒序排列
func (r *ProductRepositoryImpl) GetNew(ctx context.Context, limit int) ([]*models.Product, error) {
	o := orm.NewOrm()

	// 最多返回limit条记录
	if limit <= 0 {
		limit = 10 // 默认取10条
	}

	var products []*models.Product
	_, err := o.QueryTable(new(models.Product)).
		Filter("is_new", true).
		Filter("status", models.ProductStatusOnSale). // 只获取在售状态的商品
		OrderBy("-created_at").
		Limit(limit).
		All(&products)

	if err != nil {
		logs.Error("获取新品商品失败: %v", err)
		return nil, err
	}

	return products, nil
}
