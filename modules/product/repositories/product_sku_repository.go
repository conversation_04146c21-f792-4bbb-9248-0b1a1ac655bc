/**
 * 商品SKU仓库实现
 *
 * 本文件实现了商品SKU仓库接口，提供对商品SKU数据的访问和操作方法。
 */

package repositories

import (
	"context"
	"errors"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/models"
)

// ProductSkuRepositoryImpl 商品SKU仓库实现
type ProductSkuRepositoryImpl struct {
}

// NewProductSkuRepository 创建商品SKU仓库实例
func NewProductSkuRepository() ProductSkuRepository {
	return &ProductSkuRepositoryImpl{}
}

// Create 创建商品SKU
func (r *ProductSkuRepositoryImpl) Create(ctx context.Context, sku *models.ProductSku) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(sku)
	if err != nil {
		logs.Error("创建商品SKU失败: %v", err)
		return 0, err
	}
	return id, nil
}

// BatchCreate 批量创建商品SKU
func (r *ProductSkuRepositoryImpl) BatchCreate(ctx context.Context, skus []*models.ProductSku) error {
	if len(skus) == 0 {
		return nil
	}

	o := orm.NewOrm()
	_, err := o.InsertMulti(len(skus), skus)
	if err != nil {
		logs.Error("批量创建商品SKU失败: %v", err)
		return err
	}
	return nil
}

// GetByID 获取SKU信息
func (r *ProductSkuRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.ProductSku, error) {
	o := orm.NewOrm()
	sku := &models.ProductSku{ID: id}
	err := o.Read(sku)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("获取商品SKU信息失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}
	return sku, nil
}

// Update 更新SKU信息
func (r *ProductSkuRepositoryImpl) Update(ctx context.Context, sku *models.ProductSku) error {
	o := orm.NewOrm()
	_, err := o.Update(sku)
	if err != nil {
		logs.Error("更新商品SKU信息失败: %v", err)
		return err
	}
	return nil
}

// Delete 删除SKU
func (r *ProductSkuRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	_, err := o.Delete(&models.ProductSku{ID: id})
	if err != nil {
		logs.Error("删除商品SKU失败, ID=%d, 错误: %v", id, err)
		return err
	}
	return nil
}

// BatchDeleteByProductID 批量删除SKU
func (r *ProductSkuRepositoryImpl) BatchDeleteByProductID(ctx context.Context, productID int64) error {
	if productID <= 0 {
		return errors.New("无效的商品ID")
	}

	o := orm.NewOrm()
	_, err := o.QueryTable(new(models.ProductSku)).Filter("product_id", productID).Delete()
	if err != nil {
		logs.Error("批量删除商品SKU失败, ProductID=%d, 错误: %v", productID, err)
		return err
	}
	return nil
}

// ListByProductID 获取商品的所有SKU
func (r *ProductSkuRepositoryImpl) ListByProductID(ctx context.Context, productID int64) ([]*models.ProductSku, error) {
	o := orm.NewOrm()
	var skus []*models.ProductSku
	_, err := o.QueryTable(new(models.ProductSku)).Filter("product_id", productID).OrderBy("id").All(&skus)
	if err != nil {
		logs.Error("获取商品SKU列表失败, ProductID=%d, 错误: %v", productID, err)
		return nil, err
	}
	return skus, nil
}

// DecrStock 减少SKU库存
func (r *ProductSkuRepositoryImpl) DecrStock(ctx context.Context, id int64, quantity int) error {
	if quantity <= 0 {
		return errors.New("减少的库存数量必须大于0")
	}

	o := orm.NewOrm()
	res, err := o.Raw("UPDATE product_sku SET stock = stock - ? WHERE id = ? AND stock >= ?", quantity, id, quantity).Exec()
	if err != nil {
		logs.Error("减少SKU库存失败, ID=%d, 数量=%d, 错误: %v", id, quantity, err)
		return err
	}

	affected, _ := res.RowsAffected()
	if affected == 0 {
		return errors.New("库存不足")
	}
	return nil
}

// IncrSoldNum 增加SKU销售量
func (r *ProductSkuRepositoryImpl) IncrSoldNum(ctx context.Context, id int64, quantity int) error {
	if quantity <= 0 {
		return errors.New("增加的销售数量必须大于0")
	}

	o := orm.NewOrm()
	_, err := o.Raw("UPDATE product_sku SET sold_num = sold_num + ? WHERE id = ?", quantity, id).Exec()
	if err != nil {
		logs.Error("增加SKU销售量失败, ID=%d, 数量=%d, 错误: %v", id, quantity, err)
		return err
	}
	return nil
}

// CheckStock 检查SKU库存是否足够
func (r *ProductSkuRepositoryImpl) CheckStock(ctx context.Context, id int64, quantity int) (bool, error) {
	o := orm.NewOrm()
	var stock int
	err := o.Raw("SELECT stock FROM product_sku WHERE id = ?", id).QueryRow(&stock)
	if err != nil {
		logs.Error("检查SKU库存失败, ID=%d, 错误: %v", id, err)
		return false, err
	}
	return stock >= quantity, nil
}
