/**
 * 商品规格仓库实现
 *
 * 本文件实现了商品规格仓库接口，提供对商品规格和规格值数据的访问和操作实现。
 * 使用Beego ORM作为数据库访问层，实现对规格表的增删改查等操作。
 */

package repositories

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/models"
)

// SpecificationRepositoryImpl 商品规格仓库实现
type SpecificationRepositoryImpl struct{}

// NewSpecificationRepository 创建规格仓库实例
func NewSpecificationRepository() SpecificationRepository {
	return &SpecificationRepositoryImpl{}
}

// Create 创建规格
// 保存商品规格信息到数据库
func (r *SpecificationRepositoryImpl) Create(ctx context.Context, spec *models.Specification) (int64, error) {
	o := orm.NewOrm()

	// 检查规格名称是否已存在
	count, err := o.QueryTable(new(models.Specification)).
		Filter("name", spec.Name).
		Count()
	if err != nil {
		logs.Error("检查规格名称是否存在失败: %v", err)
		return 0, err
	}
	if count > 0 {
		return 0, errors.New("规格名称已存在")
	}

	// 设置创建时间和更新时间
	now := time.Now()
	spec.CreatedAt = now
	spec.UpdatedAt = now

	// 插入规格数据
	id, err := o.Insert(spec)
	if err != nil {
		logs.Error("创建规格失败: %v", err)
		return 0, err
	}

	return id, nil
}

// GetByID 获取规格信息
// 根据ID查询规格详细信息
func (r *SpecificationRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.Specification, error) {
	o := orm.NewOrm()
	spec := &models.Specification{ID: id}

	err := o.Read(spec)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询规格失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}

	return spec, nil
}

// Update 更新规格信息
// 更新规格的基本信息
func (r *SpecificationRepositoryImpl) Update(ctx context.Context, spec *models.Specification) error {
	o := orm.NewOrm()

	// 检查规格是否存在
	existingSpec := &models.Specification{ID: spec.ID}
	err := o.Read(existingSpec)
	if err == orm.ErrNoRows {
		return errors.New("规格不存在")
	}
	if err != nil {
		logs.Error("查询规格失败, ID=%d, 错误: %v", spec.ID, err)
		return err
	}

	// 检查规格名称是否已被其他规格使用
	count, err := o.QueryTable(new(models.Specification)).
		Filter("name", spec.Name).
		Exclude("id", spec.ID).
		Count()
	if err != nil {
		logs.Error("检查规格名称是否重复失败: %v", err)
		return err
	}
	if count > 0 {
		return errors.New("规格名称已被其他规格使用")
	}

	// 设置更新时间
	spec.UpdatedAt = time.Now()

	// 更新规格数据
	_, err = o.Update(spec)
	if err != nil {
		logs.Error("更新规格失败, ID=%d, 错误: %v", spec.ID, err)
		return err
	}

	return nil
}

// Delete 删除规格
// 从数据库中删除指定ID的规格
func (r *SpecificationRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 检查规格是否存在
	spec := &models.Specification{ID: id}
	err := o.Read(spec)
	if err == orm.ErrNoRows {
		return errors.New("规格不存在")
	}
	if err != nil {
		logs.Error("查询规格失败, ID=%d, 错误: %v", id, err)
		return err
	}

	// 检查规格是否已关联商品
	count, err := o.QueryTable(new(models.ProductSpecification)).
		Filter("spec_id", id).
		Count()
	if err != nil {
		logs.Error("检查规格商品关联失败: %v", err)
		return err
	}
	if count > 0 {
		return errors.New("规格已关联商品，无法删除")
	}

	// 检查规格是否有规格值
	valueCount, err := o.QueryTable(new(models.SpecificationValue)).
		Filter("spec_id", id).
		Count()
	if err != nil {
		logs.Error("检查规格值失败: %v", err)
		return err
	}
	if valueCount > 0 {
		return errors.New("规格下还有规格值，无法删除")
	}

	// 删除规格
	_, err = o.Delete(spec)
	if err != nil {
		logs.Error("删除规格失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// GetAll 获取所有规格
// 返回所有规格记录
func (r *SpecificationRepositoryImpl) GetAll(ctx context.Context, query map[string]interface{}) ([]*models.Specification, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.Specification))

	// 根据查询条件过滤
	if categoryID, ok := query["category_id"].(int64); ok && categoryID > 0 {
		qs = qs.Filter("category_id", categoryID)
	}

	var specifications []*models.Specification
	_, err := qs.OrderBy("sort_order", "id").All(&specifications)
	if err != nil {
		logs.Error("获取所有规格失败: %v", err)
		return nil, err
	}

	return specifications, nil
}

// CreateValue 创建规格值
// 保存规格值信息到数据库
func (r *SpecificationRepositoryImpl) CreateValue(ctx context.Context, value *models.SpecificationValue) (int64, error) {
	o := orm.NewOrm()

	// 检查规格是否存在
	specExists := o.QueryTable(new(models.Specification)).
		Filter("id", value.SpecID).
		Exist()
	if !specExists {
		return 0, errors.New("规格不存在")
	}

	// 检查规格值名称是否已存在于同一规格下
	count, err := o.QueryTable(new(models.SpecificationValue)).
		Filter("spec_id", value.SpecID).
		Filter("value", value.Value).
		Count()
	if err != nil {
		logs.Error("检查规格值是否存在失败: %v", err)
		return 0, err
	}
	if count > 0 {
		return 0, errors.New("同一规格下已存在相同的规格值")
	}

	// 设置创建时间和更新时间
	now := time.Now()
	value.CreatedAt = now
	value.UpdatedAt = now

	// 插入规格值数据
	id, err := o.Insert(value)
	if err != nil {
		logs.Error("创建规格值失败: %v", err)
		return 0, err
	}

	return id, nil
}

// BatchCreateValues 批量创建规格值
// 批量保存多个规格值到数据库
func (r *SpecificationRepositoryImpl) BatchCreateValues(ctx context.Context, values []*models.SpecificationValue) error {
	if len(values) == 0 {
		return nil
	}

	o := orm.NewOrm()

	// 开启事务
	txOrm, err := o.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 批量插入规格值数据
	now := time.Now()
	for _, value := range values {
		// 设置创建时间和更新时间
		value.CreatedAt = now
		value.UpdatedAt = now

		// 插入规格值
		_, err := txOrm.Insert(value)
		if err != nil {
			// 回滚事务
			rollbackErr := txOrm.Rollback()
			if rollbackErr != nil {
				logs.Error("回滚事务失败: %v", rollbackErr)
			}
			logs.Error("批量创建规格值失败: %v", err)
			return err
		}
	}

	// 提交事务
	err = txOrm.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// GetValueByID 获取规格值
// 根据ID查询规格值详细信息
func (r *SpecificationRepositoryImpl) GetValueByID(ctx context.Context, id int64) (*models.SpecificationValue, error) {
	o := orm.NewOrm()
	value := &models.SpecificationValue{ID: id}

	err := o.Read(value)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("查询规格值失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}

	return value, nil
}

// UpdateValue 更新规格值
// 更新规格值的基本信息
func (r *SpecificationRepositoryImpl) UpdateValue(ctx context.Context, value *models.SpecificationValue) error {
	o := orm.NewOrm()

	// 检查规格值是否存在
	existingValue := &models.SpecificationValue{ID: value.ID}
	err := o.Read(existingValue)
	if err == orm.ErrNoRows {
		return errors.New("规格值不存在")
	}
	if err != nil {
		logs.Error("查询规格值失败, ID=%d, 错误: %v", value.ID, err)
		return err
	}

	// 检查规格值内容是否已被同一规格下的其他规格值使用
	count, err := o.QueryTable(new(models.SpecificationValue)).
		Filter("spec_id", value.SpecID).
		Filter("value", value.Value).
		Exclude("id", value.ID).
		Count()
	if err != nil {
		logs.Error("检查规格值是否重复失败: %v", err)
		return err
	}
	if count > 0 {
		return errors.New("同一规格下已存在相同的规格值")
	}

	// 设置更新时间
	value.UpdatedAt = time.Now()

	// 更新规格值数据
	_, err = o.Update(value)
	if err != nil {
		logs.Error("更新规格值失败, ID=%d, 错误: %v", value.ID, err)
		return err
	}

	return nil
}

// DeleteValue 删除规格值
// 从数据库中删除指定ID的规格值
func (r *SpecificationRepositoryImpl) DeleteValue(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 检查规格值是否存在
	value := &models.SpecificationValue{ID: id}
	err := o.Read(value)
	if err == orm.ErrNoRows {
		return errors.New("规格值不存在")
	}
	if err != nil {
		logs.Error("查询规格值失败, ID=%d, 错误: %v", id, err)
		return err
	}

	// 检查规格值是否已关联商品
	count, err := o.QueryTable(new(models.ProductSpecificationValue)).
		Filter("spec_value_id", id).
		Count()
	if err != nil {
		logs.Error("检查规格值商品关联失败: %v", err)
		return err
	}
	if count > 0 {
		return errors.New("规格值已关联商品，无法删除")
	}

	// 删除规格值
	_, err = o.Delete(value)
	if err != nil {
		logs.Error("删除规格值失败, ID=%d, 错误: %v", id, err)
		return err
	}

	return nil
}

// GetValuesBySpecID 获取规格的所有值
// 返回指定规格下的所有规格值
func (r *SpecificationRepositoryImpl) GetValuesBySpecID(ctx context.Context, specID int64) ([]*models.SpecificationValue, error) {
	o := orm.NewOrm()

	// 检查规格是否存在
	specExists := o.QueryTable(new(models.Specification)).
		Filter("id", specID).
		Exist()
	if !specExists {
		return nil, errors.New("规格不存在")
	}

	var values []*models.SpecificationValue
	_, err := o.QueryTable(new(models.SpecificationValue)).
		Filter("spec_id", specID).
		OrderBy("sort_order", "id").
		All(&values)
	if err != nil {
		logs.Error("获取规格值失败, 规格ID=%d, 错误: %v", specID, err)
		return nil, err
	}

	return values, nil
}

// LinkProductSpec 关联商品规格
// 创建商品与规格的关联关系
func (r *SpecificationRepositoryImpl) LinkProductSpec(ctx context.Context, link *models.ProductSpecification) (int64, error) {
	o := orm.NewOrm()

	// 检查商品是否存在
	productExists := o.QueryTable(new(models.Product)).
		Filter("id", link.ProductID).
		Exist()
	if !productExists {
		return 0, errors.New("商品不存在")
	}

	// 检查规格是否存在
	specExists := o.QueryTable(new(models.Specification)).
		Filter("id", link.SpecID).
		Exist()
	if !specExists {
		return 0, errors.New("规格不存在")
	}

	// 检查关联是否已存在
	exist := o.QueryTable(new(models.ProductSpecification)).
		Filter("product_id", link.ProductID).
		Filter("spec_id", link.SpecID).
		Exist()
	if exist {
		return 0, errors.New("商品规格关联已存在")
	}

	// 设置创建时间
	link.CreatedAt = time.Now()

	// 插入关联数据
	id, err := o.Insert(link)
	if err != nil {
		logs.Error("创建商品规格关联失败: %v", err)
		return 0, err
	}

	return id, nil
}

// UnlinkProductSpec 删除商品规格关联
// 删除商品与特定规格的关联关系
func (r *SpecificationRepositoryImpl) UnlinkProductSpec(ctx context.Context, productID int64, specID int64) error {
	o := orm.NewOrm()

	// 删除关联
	_, err := o.QueryTable(new(models.ProductSpecification)).
		Filter("product_id", productID).
		Filter("spec_id", specID).
		Delete()
	if err != nil {
		logs.Error("删除商品规格关联失败: %v", err)
		return err
	}

	return nil
}

// UnlinkAllProductSpec 删除商品的所有规格关联
// 删除商品与所有规格的关联关系
func (r *SpecificationRepositoryImpl) UnlinkAllProductSpec(ctx context.Context, productID int64) error {
	o := orm.NewOrm()

	// 删除商品的所有规格关联
	_, err := o.QueryTable(new(models.ProductSpecification)).
		Filter("product_id", productID).
		Delete()
	if err != nil {
		logs.Error("删除商品所有规格关联失败: %v", err)
		return err
	}

	return nil
}

// GetSpecsByProductID 获取商品关联的所有规格
// 返回特定商品关联的所有规格信息
func (r *SpecificationRepositoryImpl) GetSpecsByProductID(ctx context.Context, productID int64) ([]*models.ProductSpecification, error) {
	o := orm.NewOrm()

	// 检查商品是否存在
	productExists := o.QueryTable(new(models.Product)).
		Filter("id", productID).
		Exist()
	if !productExists {
		return nil, errors.New("商品不存在")
	}

	var links []*models.ProductSpecification
	_, err := o.QueryTable(new(models.ProductSpecification)).
		Filter("product_id", productID).
		All(&links)
	if err != nil {
		logs.Error("获取商品规格关联失败: %v", err)
		return nil, err
	}

	return links, nil
}

// LinkProductSpecValue 关联商品规格值
// 创建商品与规格值的关联关系
func (r *SpecificationRepositoryImpl) LinkProductSpecValue(ctx context.Context, link *models.ProductSpecificationValue) (int64, error) {
	o := orm.NewOrm()

	// 检查商品是否存在
	productExists := o.QueryTable(new(models.Product)).
		Filter("id", link.ProductID).
		Exist()
	if !productExists {
		return 0, errors.New("商品不存在")
	}

	// 检查规格值是否存在
	valueExists := o.QueryTable(new(models.SpecificationValue)).
		Filter("id", link.SpecValueID).
		Exist()
	if !valueExists {
		return 0, errors.New("规格值不存在")
	}

	// 检查关联是否已存在
	exist := o.QueryTable(new(models.ProductSpecificationValue)).
		Filter("product_id", link.ProductID).
		Filter("spec_value_id", link.SpecValueID).
		Exist()
	if exist {
		return 0, errors.New("商品规格值关联已存在")
	}

	// 设置创建时间
	link.CreatedAt = time.Now()

	// 插入关联数据
	id, err := o.Insert(link)
	if err != nil {
		logs.Error("创建商品规格值关联失败: %v", err)
		return 0, err
	}

	return id, nil
}

// BatchLinkProductSpecValue 批量关联商品规格值
// 批量创建商品与多个规格值的关联关系
func (r *SpecificationRepositoryImpl) BatchLinkProductSpecValue(ctx context.Context, links []*models.ProductSpecificationValue) error {
	if len(links) == 0 {
		return nil
	}

	o := orm.NewOrm()

	// 开启事务
	txOrm, err := o.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 批量插入关联数据
	now := time.Now()
	for _, link := range links {
		// 设置创建时间
		link.CreatedAt = now

		// 插入关联数据
		_, err := txOrm.Insert(link)
		if err != nil {
			// 回滚事务
			rollbackErr := txOrm.Rollback()
			if rollbackErr != nil {
				logs.Error("回滚事务失败: %v", rollbackErr)
			}
			logs.Error("批量创建商品规格值关联失败: %v", err)
			return err
		}
	}

	// 提交事务
	err = txOrm.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// UnlinkProductSpecValue 删除商品的规格值关联
// 删除商品与特定规格的所有规格值关联
func (r *SpecificationRepositoryImpl) UnlinkProductSpecValue(ctx context.Context, productID int64, specID int64) error {
	o := orm.NewOrm()

	// 获取规格下的所有规格值
	var specValues []*models.SpecificationValue
	_, err := o.QueryTable(new(models.SpecificationValue)).
		Filter("spec_id", specID).
		All(&specValues, "id")
	if err != nil {
		logs.Error("获取规格值失败: %v", err)
		return err
	}

	if len(specValues) == 0 {
		return nil
	}

	// 提取规格值ID
	var specValueIDs []interface{}
	for _, value := range specValues {
		specValueIDs = append(specValueIDs, value.ID)
	}

	// 删除关联
	_, err = o.QueryTable(new(models.ProductSpecificationValue)).
		Filter("product_id", productID).
		Filter("spec_value_id__in", specValueIDs).
		Delete()
	if err != nil {
		logs.Error("删除商品规格值关联失败: %v", err)
		return err
	}

	return nil
}

// UnlinkAllProductSpecValue 删除商品的所有规格值关联
// 删除商品与所有规格值的关联关系
func (r *SpecificationRepositoryImpl) UnlinkAllProductSpecValue(ctx context.Context, productID int64) error {
	o := orm.NewOrm()

	// 删除商品的所有规格值关联
	_, err := o.QueryTable(new(models.ProductSpecificationValue)).
		Filter("product_id", productID).
		Delete()
	if err != nil {
		logs.Error("删除商品所有规格值关联失败: %v", err)
		return err
	}

	return nil
}

// GetSpecValuesByProductID 获取商品关联的所有规格值
// 返回特定商品关联的所有规格值信息
func (r *SpecificationRepositoryImpl) GetSpecValuesByProductID(ctx context.Context, productID int64) ([]*models.ProductSpecificationValue, error) {
	o := orm.NewOrm()

	// 检查商品是否存在
	productExists := o.QueryTable(new(models.Product)).
		Filter("id", productID).
		Exist()
	if !productExists {
		return nil, errors.New("商品不存在")
	}

	var links []*models.ProductSpecificationValue
	_, err := o.QueryTable(new(models.ProductSpecificationValue)).
		Filter("product_id", productID).
		All(&links)
	if err != nil {
		logs.Error("获取商品规格值关联失败: %v", err)
		return nil, err
	}

	return links, nil
}
