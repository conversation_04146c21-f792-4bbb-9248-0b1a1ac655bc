/**
 * routes.go
 * 商品模块路由
 *
 * 本文件负责注册商品模块的所有路由，包括商品、分类、评价等相关API接口
 */

package routes

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/product/controllers"
)

// InitRoutes 初始化商品模块所有路由
func InitRoutes() {
	// 初始化商品路由
	InitProductRoutes()
	
	// 初始化分类路由
	InitCategoryRoutes()
	
	// 初始化评价路由
	InitCommentRoutes()
}

// InitProductRoutes 初始化商品路由
func InitProductRoutes() {
	// 商品命名空间
	ns := web.NewNamespace("/api/v1",
		// 商品相关路由
		web.NSNamespace("/products",
			// 获取商品列表 - 公开接口
			web.NSRouter("", &controllers.ProductController{}, "get:List"),
			// 获取商品详情 - 公开接口
			web.NSRouter("/:id", &controllers.ProductController{}, "get:Get"),
			// 创建商品 - 需要商家认证
			web.NSBefore(middlewares.JWTMiddleware, middlewares.MerchantAuthMiddleware),
			web.NSRouter("", &controllers.ProductController{}, "post:Create"),
			// 更新商品 - 需要商家认证
			web.NSRouter("/:id", &controllers.ProductController{}, "put:Update"),
			// 删除商品 - 需要商家认证
			web.NSRouter("/:id", &controllers.ProductController{}, "delete:Delete"),
			// 批量删除商品 - 需要商家认证
			web.NSRouter("/batch/delete", &controllers.ProductController{}, "post:BatchDelete"),
			// 更新商品状态 - 需要商家认证
			web.NSRouter("/:id/status", &controllers.ProductController{}, "put:UpdateStatus"),
			// 检查库存 - 公开接口
			web.NSRouter("/:id/stock", &controllers.ProductController{}, "post:CheckStock"),
			// 获取推荐商品 - 公开接口
			web.NSRouter("/recommend", &controllers.ProductController{}, "get:GetRecommend"),
			// 获取热销商品 - 公开接口
			web.NSRouter("/hot", &controllers.ProductController{}, "get:GetHot"),
			// 获取新品商品 - 公开接口
			web.NSRouter("/new", &controllers.ProductController{}, "get:GetNew"),
		),
	)

	// 注册命名空间
	web.AddNamespace(ns)
}

// InitCategoryRoutes 初始化分类路由
func InitCategoryRoutes() {
	// 分类命名空间
	ns := web.NewNamespace("/api/v1",
		// 分类相关路由
		web.NSNamespace("/categories",
			// 获取分类树形结构 - 公开接口
			web.NSRouter("/tree", &controllers.CategoryController{}, "get:GetTree"),
			// 获取分类列表 - 公开接口
			web.NSRouter("", &controllers.CategoryController{}, "get:List"),
			// 获取分类详情 - 公开接口
			web.NSRouter("/:id", &controllers.CategoryController{}, "get:Get"),
			// 获取子分类 - 公开接口
			web.NSRouter("/children", &controllers.CategoryController{}, "get:GetChildren"),
			
			// 以下接口需要管理员权限
			web.NSBefore(middlewares.JWTMiddleware, middlewares.AdminAuthMiddleware),
			// 创建分类 - 需要管理员权限
			web.NSRouter("", &controllers.CategoryController{}, "post:Create"),
			// 更新分类 - 需要管理员权限
			web.NSRouter("/:id", &controllers.CategoryController{}, "put:Update"),
			// 删除分类 - 需要管理员权限
			web.NSRouter("/:id", &controllers.CategoryController{}, "delete:Delete"),
			// 设置分类显示状态 - 需要管理员权限
			web.NSRouter("/:id/show", &controllers.CategoryController{}, "put:SetShow"),
		),
	)

	// 注册命名空间
	web.AddNamespace(ns)
}

// InitCommentRoutes 初始化评价路由
func InitCommentRoutes() {
	// 评价命名空间
	ns := web.NewNamespace("/api/v1",
		// 评价相关路由
		web.NSNamespace("/comments",
			// 获取商品评价列表 - 公开接口
			web.NSRouter("/product", &controllers.CommentController{}, "get:GetProductComments"),
			// 获取评价详情 - 公开接口
			web.NSRouter("/:id", &controllers.CommentController{}, "get:Get"),
			// 获取评价统计信息 - 公开接口
			web.NSRouter("/statistics", &controllers.CommentController{}, "get:GetCommentStatistics"),
			
			// 以下接口需要用户登录权限
			web.NSBefore(middlewares.JWTMiddleware),
			// 创建评价 - 需要用户登录
			web.NSRouter("", &controllers.CommentController{}, "post:Create"),
			// 获取用户评价列表 - 需要用户登录
			web.NSRouter("/user", &controllers.CommentController{}, "get:GetUserComments"),
			// 点赞评价 - 需要用户登录
			web.NSRouter("/:id/like", &controllers.CommentController{}, "post:LikeComment"),
			// 取消点赞 - 需要用户登录
			web.NSRouter("/:id/unlike", &controllers.CommentController{}, "post:UnlikeComment"),
		),
	)

	// 注册命名空间
	web.AddNamespace(ns)
}
