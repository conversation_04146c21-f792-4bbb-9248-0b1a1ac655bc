/**
 * 商品分类服务实现
 *
 * 本文件实现了商品分类服务接口，提供分类的业务逻辑处理实现。
 * 包括创建、修改、查询、删除分类以及获取分类树等功能。
 */

package services

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/dto"
	"o_mall_backend/modules/product/models"
	"o_mall_backend/modules/product/repositories"
)

// CategoryServiceImpl 商品分类服务实现
type CategoryServiceImpl struct {
	categoryRepo repositories.CategoryRepository
}

// NewCategoryService 创建分类服务实例
func NewCategoryService(categoryRepo repositories.CategoryRepository) CategoryService {
	return &CategoryServiceImpl{
		categoryRepo: categoryRepo,
	}
}

// CreateCategory 创建分类
// 处理商品分类创建的业务逻辑
func (s *CategoryServiceImpl) CreateCategory(ctx context.Context, req *dto.CreateCategoryRequest) (*dto.CategoryResponse, error) {
	// 检查父分类是否存在
	if req.ParentID > 0 {
		exists, err := s.categoryRepo.Exists(ctx, req.ParentID)
		if err != nil {
			logs.Error("检查父分类是否存在失败: %v", err)
			return nil, err
		}
		if !exists {
			return nil, errors.New("父分类不存在")
		}
	}

	// 创建分类模型
	category := &models.Category{
		Name:        req.Name,
		Description: req.Description,
		ParentID:    req.ParentID,
		Icon:        req.Icon,
		Sort:        req.SortOrder,
		IsShow:      req.IsShow,
	}

	// 保存分类信息
	id, err := s.categoryRepo.Create(ctx, category)
	if err != nil {
		logs.Error("创建分类失败: %v", err)
		return nil, err
	}

	// 查询创建好的分类信息
	category, err = s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取新创建的分类失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	return s.convertToCategoryResponse(category), nil
}

// GetCategory 获取分类详情
// 根据ID获取分类详细信息
func (s *CategoryServiceImpl) GetCategory(ctx context.Context, id int64) (*dto.CategoryResponse, error) {
	// 获取分类信息
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取分类失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}
	if category == nil {
		return nil, errors.New("分类不存在")
	}

	// 转换为响应对象
	return s.convertToCategoryResponse(category), nil
}

// UpdateCategory 更新分类信息
// 修改分类的基本信息
func (s *CategoryServiceImpl) UpdateCategory(ctx context.Context, id int64, req *dto.UpdateCategoryRequest) error {
	// 获取分类信息
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取分类失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if category == nil {
		return errors.New("分类不存在")
	}

	// 检查父分类是否存在
	if req.ParentID > 0 && req.ParentID != category.ParentID {
		exists, err := s.categoryRepo.Exists(ctx, req.ParentID)
		if err != nil {
			logs.Error("检查父分类是否存在失败: %v", err)
			return err
		}
		if !exists {
			return errors.New("父分类不存在")
		}
		category.ParentID = req.ParentID
	}

	// 更新分类信息
	if req.Name != "" {
		category.Name = req.Name
	}
	if req.Description != "" {
		category.Description = req.Description
	}
	if req.Icon != "" {
		category.Icon = req.Icon
	}
	category.Sort = req.SortOrder
	category.IsShow = req.IsShow
	category.UpdatedAt = time.Now()

	// 保存分类信息
	err = s.categoryRepo.Update(ctx, category)
	if err != nil {
		logs.Error("更新分类失败: %v", err)
		return err
	}

	return nil
}

// DeleteCategory 删除分类
// 删除指定ID的分类
func (s *CategoryServiceImpl) DeleteCategory(ctx context.Context, id int64) error {
	// 检查分类是否存在
	category, err := s.categoryRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取分类失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if category == nil {
		return errors.New("分类不存在")
	}

	// 删除分类
	err = s.categoryRepo.Delete(ctx, id)
	if err != nil {
		logs.Error("删除分类失败: %v", err)
		return err
	}

	return nil
}

// GetAllCategories 获取所有分类
// 返回所有分类列表
func (s *CategoryServiceImpl) GetAllCategories(ctx context.Context) ([]*dto.CategoryResponse, error) {
	// 获取所有分类
	categories, err := s.categoryRepo.GetAll(ctx)
	if err != nil {
		logs.Error("获取所有分类失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	var result []*dto.CategoryResponse
	for _, category := range categories {
		result = append(result, s.convertToCategoryResponse(category))
	}

	return result, nil
}

// GetCategoryTree 获取分类树
// 返回分类的树形结构
func (s *CategoryServiceImpl) GetCategoryTree(ctx context.Context) ([]*dto.CategoryTreeResponse, error) {
	// 获取分类树
	categoryTree, err := s.categoryRepo.GetTree(ctx)
	if err != nil {
		logs.Error("获取分类树失败: %v", err)
		return nil, err
	}

	// 转换为树形响应对象
	var result []*dto.CategoryTreeResponse
	for _, category := range categoryTree {
		result = append(result, s.convertToCategoryTreeResponse(category))
	}

	return result, nil
}

// GetChildCategories 获取子分类列表
// 返回指定父分类下的所有子分类
func (s *CategoryServiceImpl) GetChildCategories(ctx context.Context, parentID int64) ([]*dto.CategoryResponse, error) {
	// 如果父分类ID > 0，则检查父分类是否存在
	if parentID > 0 {
		exists, err := s.categoryRepo.Exists(ctx, parentID)
		if err != nil {
			logs.Error("检查父分类是否存在失败: %v", err)
			return nil, err
		}
		if !exists {
			return nil, errors.New("父分类不存在")
		}
	}

	// 获取子分类
	children, err := s.categoryRepo.GetChildren(ctx, parentID)
	if err != nil {
		logs.Error("获取子分类失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	var result []*dto.CategoryResponse
	for _, child := range children {
		result = append(result, s.convertToCategoryResponse(child))
	}

	return result, nil
}

// SetCategoryShow 设置分类显示状态
// 修改分类的显示/隐藏状态
func (s *CategoryServiceImpl) SetCategoryShow(ctx context.Context, id int64, isShow bool) error {
	// 检查分类是否存在
	exists, err := s.categoryRepo.Exists(ctx, id)
	if err != nil {
		logs.Error("检查分类是否存在失败: %v", err)
		return err
	}
	if !exists {
		return errors.New("分类不存在")
	}

	// 更新分类显示状态
	err = s.categoryRepo.SetShow(ctx, id, isShow)
	if err != nil {
		logs.Error("设置分类显示状态失败: %v", err)
		return err
	}

	return nil
}

// convertToCategoryResponse 将分类模型转换为响应DTO
// 内部辅助方法，转换数据格式
func (s *CategoryServiceImpl) convertToCategoryResponse(category *models.Category) *dto.CategoryResponse {
	if category == nil {
		return nil
	}

	return &dto.CategoryResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		ParentID:    category.ParentID,
		Icon:        category.Icon,
		SortOrder:   category.Sort,
		IsShow:      category.IsShow,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}

// convertToCategoryTreeResponse 将分类模型转换为树形响应DTO
// 内部辅助方法，转换树形数据格式
func (s *CategoryServiceImpl) convertToCategoryTreeResponse(category *models.Category) *dto.CategoryTreeResponse {
	if category == nil {
		return nil
	}

	response := &dto.CategoryTreeResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		ParentID:    category.ParentID,
		Icon:        category.Icon,
		SortOrder:   category.Sort,
		IsShow:      category.IsShow,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
		Children:    make([]*dto.CategoryTreeResponse, 0),
	}

	// 递归转换子分类
	for _, child := range category.Children {
		response.Children = append(response.Children, s.convertToCategoryTreeResponse(child))
	}

	return response
}
