/**
 * 商品评论服务实现
 *
 * 本文件实现了商品评论服务接口，提供评论的业务逻辑处理实现。
 * 包括创建、修改、查询、删除评论以及评论审核等功能。
 */

package services

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/dto"
	"o_mall_backend/modules/product/models"
	"o_mall_backend/modules/product/repositories"
)

// CommentServiceImpl 商品评论服务实现
type CommentServiceImpl struct {
	commentRepo repositories.ProductCommentRepository
	productRepo repositories.ProductRepository
}

// NewCommentService 创建评论服务实例
func NewCommentService(
	commentRepo repositories.ProductCommentRepository,
	productRepo repositories.ProductRepository,
) CommentService {
	return &CommentServiceImpl{
		commentRepo: commentRepo,
		productRepo: productRepo,
	}
}

// CreateComment 创建评论
func (s *CommentServiceImpl) CreateComment(ctx context.Context, req *dto.CreateCommentRequest) (*dto.CommentResponse, error) {
	// 创建评论对象
	comment := &models.ProductComment{
		ProductID:      req.ProductID,
		UserID:         req.UserID,
		UserName:       req.UserName,
		UserAvatar:     req.UserAvatar,
		Content:        req.Content,
		Rating:         req.Rating,
		OrderID:        req.OrderID,
		Status:         models.CommentStatusPending, // 默认待审核状态
		IsAnonymous:    req.IsAnonymous,
		HasProductSku:  req.HasProductSku,
		ProductSkuInfo: req.ProductSkuInfo,
	}

	// 保存评论
	id, err := s.commentRepo.Create(ctx, comment)
	if err != nil {
		return nil, err
	}

	// 保存评论图片（如果有）
	if len(req.Images) > 0 {
		var commentImages []*models.CommentImage
		for i, imageURL := range req.Images {
			commentImage := &models.CommentImage{
				CommentID: id,
				ImageURL:  imageURL,
				SortOrder: i,
			}
			commentImages = append(commentImages, commentImage)
		}

		err = s.commentRepo.BatchAddCommentImages(ctx, commentImages)
		if err != nil {
			// 这里不返回错误，继续执行
			logs.Error("保存评论图片失败: %v", err)
		}
	}

	// 查询创建好的评论信息
	comment, err = s.commentRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取新创建的评论失败: %v", err)
		return nil, err
	}

	// 获取评论图片
	images, err := s.commentRepo.GetCommentImages(ctx, id)
	if err != nil {
		logs.Error("获取评论图片失败: %v", err)
		// 这里不返回错误，继续执行
	}

	// 转换为响应对象
	return s.convertToCommentResponse(comment, images), nil
}

// GetComment 获取评论详情
func (s *CommentServiceImpl) GetComment(ctx context.Context, id int64) (*dto.CommentResponse, error) {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if comment == nil {
		return nil, errors.New("评论不存在")
	}

	// 获取评论图片
	images, err := s.commentRepo.GetCommentImages(ctx, comment.ID)
	if err != nil {
		// 这里不返回错误，继续执行
		logs.Error("获取评论图片失败: %v", err)
	}

	// 转换为响应对象
	return s.convertToCommentResponse(comment, images), nil
}

// UpdateComment 更新评论
func (s *CommentServiceImpl) UpdateComment(ctx context.Context, id int64, req *dto.UpdateCommentRequest) error {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 更新评论信息
	if req.Content != "" {
		comment.Content = req.Content
	}
	if req.Rating > 0 {
		comment.Rating = req.Rating
	}
	comment.IsAnonymous = req.IsAnonymous
	comment.UpdatedAt = time.Now()

	// 保存评论信息
	err = s.commentRepo.Update(ctx, comment)
	if err != nil {
		return err
	}

	return nil
}

// DeleteComment 删除评论
// 删除指定ID的评论
func (s *CommentServiceImpl) DeleteComment(ctx context.Context, id int64) error {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 删除评论
	err = s.commentRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

// GetProductComments 获取商品评论列表
// 返回指定商品的所有评论，支持分页查询
func (s *CommentServiceImpl) GetProductComments(ctx context.Context, productID int64, req *dto.CommentListRequest) (*dto.CommentListResponse, error) {
	// 获取评论列表 - 正确的返回值顺序
	comments, total, err := s.commentRepo.GetByProductID(ctx, productID, req.Page, req.PageSize, req.Status)
	if err != nil {
		return nil, err
	}

	// 转换为响应对象
	var items []*dto.CommentResponse
	for _, comment := range comments {
		// 获取评论图片
		images, err := s.commentRepo.GetCommentImages(ctx, comment.ID)
		if err != nil {
			// 这里不返回错误，继续执行
			logs.Error("获取评论图片失败: %v", err)
		}

		items = append(items, s.convertToCommentResponse(comment, images))
	}

	return &dto.CommentListResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Items:    items,
	}, nil
}

// GetAllComments 获取所有评论
// 返回所有评论列表，支持分页和状态过滤
func (s *CommentServiceImpl) GetAllComments(ctx context.Context, req *dto.CommentListRequest) (*dto.CommentListResponse, error) {
	// 设置默认参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 获取所有评论列表 - 正确的返回值顺序
	comments, total, err := s.commentRepo.GetAll(ctx, page, pageSize, req.Status)
	if err != nil {
		logs.Error("获取所有评论列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	var items []*dto.CommentResponse
	for _, comment := range comments {
		// 获取评论图片
		images, err := s.commentRepo.GetCommentImages(ctx, comment.ID)
		if err != nil {
			logs.Error("获取评论图片失败: %v", err)
			// 这里不返回错误，继续执行
		}

		items = append(items, s.convertToCommentResponse(comment, images))
	}

	// 构建响应结果
	result := &dto.CommentListResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Items:    items,
	}

	return result, nil
}

// AuditComment 审核评论
// 审核商品评论，设置评论的状态
func (s *CommentServiceImpl) AuditComment(ctx context.Context, id int64, status int, reason string) error {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取评论失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 更新评论状态
	comment.Status = status
	comment.ReplyContent = reason
	comment.UpdatedAt = time.Now()

	// 保存评论信息
	err = s.commentRepo.Update(ctx, comment)
	if err != nil {
		logs.Error("更新评论状态失败: %v", err)
		return err
	}

	return nil
}

// BatchAuditComments 批量审核评论
// 批量审核商品评论，设置评论的状态
func (s *CommentServiceImpl) BatchAuditComments(ctx context.Context, ids []int64, status int, reason string) error {
	// 批量更新评论状态
	for _, id := range ids {
		err := s.AuditComment(ctx, id, status, reason)
		if err != nil {
			logs.Error("批量审核评论失败, ID=%d, 错误: %v", id, err)
			return err
		}
	}
	return nil
}

// ReplyComment 回复评论
// 商家或管理员回复评论
func (s *CommentServiceImpl) ReplyComment(ctx context.Context, id int64, content string, adminName string) error {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 更新评论回复信息
	comment.ReplyContent = content
	comment.ReplyAdmin = adminName
	comment.ReplyTime = time.Now()
	comment.IsReplied = true

	// 保存更新
	err = s.commentRepo.Update(ctx, comment)
	if err != nil {
		return err
	}

	return nil
}

// 定义评论等级常量
const (
	RatingGood   = 5
	RatingNormal = 3
	RatingBad    = 1
)

// GetCommentStatistics 获取评论统计信息
// 获取指定商品的评论统计信息
func (s *CommentServiceImpl) GetCommentStatistics(ctx context.Context, productID int64) (*dto.CommentStatisticsResponse, error) {
	// 获取总评论数
	total, err := s.commentRepo.CountByProductID(ctx, productID)
	if err != nil {
		logs.Error("获取评论总数失败: %v", err)
		return nil, err
	}

	// 获取好评数量
	goodCount, err := s.commentRepo.CountByProductIDAndRatingLevel(ctx, productID, RatingGood)
	if err != nil {
		logs.Error("获取好评数量失败: %v", err)
		return nil, err
	}

	// 获取中评数量
	normalCount, err := s.commentRepo.CountByProductIDAndRatingLevel(ctx, productID, RatingNormal)
	if err != nil {
		logs.Error("获取中评数量失败: %v", err)
		return nil, err
	}

	// 获取差评数量
	badCount, err := s.commentRepo.CountByProductIDAndRatingLevel(ctx, productID, RatingBad)
	if err != nil {
		logs.Error("获取差评数量失败: %v", err)
		return nil, err
	}

	// 计算好评率
	goodRate := float64(0)
	if total > 0 {
		goodRate = float64(goodCount) / float64(total) * 100
	}

	// 构建响应对象
	result := &dto.CommentStatisticsResponse{
		TotalCount:  total,
		GoodCount:   goodCount,
		NormalCount: normalCount,
		BadCount:    badCount,
		GoodRate:    goodRate,
	}

	return result, nil
}

// AddCommentImage 添加评论图片
// 为已有评论添加图片
func (s *CommentServiceImpl) AddCommentImage(ctx context.Context, req *dto.AddCommentImageRequest) error {
	// 检查评论是否存在
	comment, err := s.commentRepo.GetByID(ctx, req.CommentID)
	if err != nil {
		logs.Error("获取评论失败, ID=%d, 错误: %v", req.CommentID, err)
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 获取当前评论图片数量，用于确定排序顺序
	images, err := s.commentRepo.GetCommentImages(ctx, req.CommentID)
	if err != nil {
		logs.Error("获取评论图片失败: %v", err)
		return err
	}

	// 创建评论图片
	image := &models.CommentImage{
		CommentID: req.CommentID,
		ImageURL:  req.ImageURL,
		SortOrder: len(images),
	}

	_, err = s.commentRepo.AddCommentImage(ctx, image)
	if err != nil {
		logs.Error("添加评论图片失败: %v", err)
		return err
	}

	return nil
}

// DeleteCommentImage 删除评论图片
// 删除评论的图片
func (s *CommentServiceImpl) DeleteCommentImage(ctx context.Context, imageID int64) error {
	// 删除评论图片
	err := s.commentRepo.DeleteCommentImage(ctx, imageID)
	if err != nil {
		logs.Error("删除评论图片失败: %v", err)
		return err
	}

	return nil
}

// convertToCommentResponse 将评论模型转换为响应DTO
// 内部辅助方法，转换数据格式
func (s *CommentServiceImpl) convertToCommentResponse(comment *models.ProductComment, images []*models.CommentImage) *dto.CommentResponse {
	if comment == nil {
		return nil
	}

	// 转换评论图片
	var imageURLs []string
	for _, image := range images {
		imageURLs = append(imageURLs, image.ImageURL)
	}

	// 匿名处理
	userName := comment.UserName
	if comment.IsAnonymous {
		userName = s.anonymizeUsername(userName)
	}

	return &dto.CommentResponse{
		ID:             comment.ID,
		ProductID:      comment.ProductID,
		UserID:         comment.UserID,
		UserName:       userName,
		UserAvatar:     comment.UserAvatar,
		Content:        comment.Content,
		Rating:         comment.Rating,
		OrderID:        comment.OrderID,
		Status:         comment.Status,
		StatusText:     s.getStatusText(comment.Status),
		IsAnonymous:    comment.IsAnonymous,
		Images:         imageURLs,
		ReplyContent:   comment.ReplyContent,
		ReplyAdmin:     comment.ReplyAdmin,
		ReplyTime:      comment.ReplyTime,
		IsReplied:      comment.IsReplied,
		HasProductSku:  comment.HasProductSku,
		ProductSkuInfo: comment.ProductSkuInfo,
		CreatedAt:      comment.CreatedAt,
		UpdatedAt:      comment.UpdatedAt,
	}
}

// anonymizeUsername 匿名化用户名
// 将用户名转换为匿名形式，保留首尾字符，中间用星号代替
func (s *CommentServiceImpl) anonymizeUsername(name string) string {
	if name == "" {
		return "匿名用户"
	}

	runes := []rune(name)
	length := len(runes)

	if length <= 1 {
		return string(runes[0]) + "***"
	} else if length == 2 {
		return string(runes[0]) + "***" + string(runes[1])
	} else {
		return string(runes[0]) + "***" + string(runes[length-1])
	}
}

// getStatusText 获取状态文本
// 将状态码转换为对应的文本描述
func (s *CommentServiceImpl) getStatusText(status int) string {
	switch status {
	case models.CommentStatusPending:
		return "待审核"
	case models.CommentStatusApproved:
		return "已通过"
	case models.CommentStatusRejected:
		return "已拒绝"
	default:
		return "未知状态"
	}
}

// GetUserComments 获取用户评论列表
// 获取指定用户的评论列表
func (s *CommentServiceImpl) GetUserComments(ctx context.Context, userID int64, req *dto.CommentListRequest) (*dto.CommentListResponse, error) {
	comments, total, err := s.commentRepo.GetByUserID(ctx, userID, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 转换为 DTO
	var items []*dto.CommentResponse
	for _, comment := range comments {
		items = append(items, s.convertToCommentResponse(comment, nil))
	}

	return &dto.CommentListResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Items:    items,
	}, nil
}

// LikeComment 点赞评论
// 为指定评论增加点赞数
func (s *CommentServiceImpl) LikeComment(ctx context.Context, id int64) error {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取评论失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 增加点赞数
	comment.LikeCount++
	comment.UpdatedAt = time.Now()

	// 保存评论信息
	err = s.commentRepo.Update(ctx, comment)
	if err != nil {
		logs.Error("更新评论点赞数失败: %v", err)
		return err
	}

	return nil
}

// UnlikeComment 取消点赞评论
// 为指定评论减少点赞数
func (s *CommentServiceImpl) UnlikeComment(ctx context.Context, id int64) error {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取评论失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 减少点赞数，但不能小于0
	if comment.LikeCount > 0 {
		comment.LikeCount--
		comment.UpdatedAt = time.Now()

		// 保存评论信息
		err = s.commentRepo.Update(ctx, comment)
		if err != nil {
			logs.Error("更新评论点赞数失败: %v", err)
			return err
		}
	}

	return nil
}

// AddReply 添加评论回复
func (s *CommentServiceImpl) AddReply(ctx context.Context, id int64, replyContent string, replyAdmin string) error {
	// 获取评论信息
	comment, err := s.commentRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if comment == nil {
		return errors.New("评论不存在")
	}

	// 更新评论回复信息
	comment.ReplyContent = replyContent
	comment.ReplyAdmin = replyAdmin
	comment.ReplyTime = time.Now()
	comment.IsReplied = true

	// 保存更新
	err = s.commentRepo.Update(ctx, comment)
	if err != nil {
		return err
	}

	return nil
}

// MockGetByProductID 模拟 CommentRepository 的 GetByProductID 方法
// 仅用于说明 GetByProductID 方法的返回类型，实际使用时应当删除此方法
func MockGetByProductID(ctx context.Context, productID int64, page, pageSize, status int) ([]*models.ProductComment, int64, error) {
	// 此方法仅用于示例，实际应返回正确的商品评论数据
	var comments []*models.ProductComment
	var total int64 = 0
	return comments, total, nil
}
