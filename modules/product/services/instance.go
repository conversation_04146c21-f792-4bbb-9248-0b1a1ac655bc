/**
 * instance.go
 * 服务实例初始化
 *
 * 本文件提供了商品模块各个服务的实例化方法
 */

package services

import (
	"o_mall_backend/modules/product/repositories"
)

// GetCategoryService 获取分类服务实例
func GetCategoryService() CategoryService {
	// 创建分类仓库实例
	categoryRepo := repositories.NewCategoryRepository()
	return &CategoryServiceImpl{
		categoryRepo: categoryRepo,
	}
}

// GetCommentService 获取评论服务实例
func GetCommentService() CommentService {
	// 创建评论仓库和产品仓库实例
	commentRepo := repositories.NewCommentRepository()
	productRepo := repositories.NewProductRepository()
	return &CommentServiceImpl{
		commentRepo: commentRepo,
		productRepo: productRepo,
	}
}
