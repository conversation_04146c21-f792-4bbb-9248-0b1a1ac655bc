/**
 * 商品服务接口
 *
 * 本文件定义了商品相关的服务接口，包括商品的创建、查询、更新、删除等操作。
 */

package services

import (
	"context"
	"o_mall_backend/modules/product/dto"
)

// ProductService 商品服务接口
type ProductService interface {
	Create(req *dto.CreateProductRequest) (*dto.ProductDetailResponse, error)
	Get(id int64) (*dto.ProductDetailResponse, error)
	Update(req *dto.UpdateProductRequest) error
	UpdateStatus(req *dto.UpdateProductStatusRequest) error
	Delete(id int64) error
	List(req *dto.ProductQueryRequest) ([]*dto.ProductListResponse, int64, error)
	GetProductComments(req *dto.ProductCommentQueryRequest) ([]*dto.ProductCommentResponse, int64, error)
	CreateProductComment(req *dto.CreateProductCommentRequest) (*dto.ProductCommentResponse, error)

	// 以下方法与现有方法功能相似，但接口签名不同，为兼容控制器而添加
	ListProducts(ctx context.Context, req *dto.ProductQueryRequest) (*dto.ProductListResponseWrapper, error)
	GetProduct(ctx context.Context, id int64) (*dto.ProductDetailResponse, error)

	// 以下是控制器需要的额外方法
	IncrProductViewCount(ctx context.Context, id int64) error
	CreateProduct(ctx context.Context, req *dto.CreateProductRequest) (*dto.ProductDetailResponse, error)
	UpdateProduct(ctx context.Context, id int64, req *dto.UpdateProductRequest) error
	DeleteProduct(ctx context.Context, id int64) error
	BatchDeleteProducts(ctx context.Context, ids []int64) error
	UpdateProductStatus(ctx context.Context, id int64, status int, rejectReason string) error
	GetRecommendProducts(ctx context.Context, limit int) ([]*dto.ProductListResponse, error)
	GetHotProducts(ctx context.Context, limit int) ([]*dto.ProductListResponse, error)
	GetNewProducts(ctx context.Context, limit int) ([]*dto.ProductListResponse, error)
	CheckStock(ctx context.Context, productID int64, skuID int64, quantity int) (bool, error)
}

// CategoryService 商品分类服务接口
type CategoryService interface {
	CreateCategory(ctx context.Context, req *dto.CreateCategoryRequest) (*dto.CategoryResponse, error)
	GetCategory(ctx context.Context, id int64) (*dto.CategoryResponse, error)
	UpdateCategory(ctx context.Context, id int64, req *dto.UpdateCategoryRequest) error
	DeleteCategory(ctx context.Context, id int64) error
	GetAllCategories(ctx context.Context) ([]*dto.CategoryResponse, error)
	GetCategoryTree(ctx context.Context) ([]*dto.CategoryTreeResponse, error)
	GetChildCategories(ctx context.Context, parentID int64) ([]*dto.CategoryResponse, error)
	SetCategoryShow(ctx context.Context, id int64, isShow bool) error
}

// SpecificationService 规格服务接口
type SpecificationService interface {
	Create(req *dto.CreateSpecificationRequest) (*dto.SpecificationResponse, error)
	Get(id int64) (*dto.SpecificationResponse, error)
	Update(req *dto.UpdateSpecificationRequest) error
	Delete(id int64) error
	List(page, pageSize int) ([]*dto.SpecificationResponse, int64, error)
	CreateSpecValue(req *dto.CreateSpecValueRequest) (*dto.SpecValueResponse, error)
	GetSpecValue(id int64) (*dto.SpecValueResponse, error)
	UpdateSpecValue(req *dto.UpdateSpecValueRequest) error
	DeleteSpecValue(id int64) error
	ListSpecValues(specID int64) ([]*dto.SpecValueResponse, error)
	GetProductSpecs(productID int64) ([]dto.ProductSpecResponse, error)
}

// CommentService 商品评论服务接口
type CommentService interface {
	CreateComment(ctx context.Context, req *dto.CreateCommentRequest) (*dto.CommentResponse, error)
	GetComment(ctx context.Context, id int64) (*dto.CommentResponse, error)
	UpdateComment(ctx context.Context, id int64, req *dto.UpdateCommentRequest) error
	DeleteComment(ctx context.Context, id int64) error
	GetProductComments(ctx context.Context, productID int64, req *dto.CommentListRequest) (*dto.CommentListResponse, error)
	GetAllComments(ctx context.Context, req *dto.CommentListRequest) (*dto.CommentListResponse, error)
	AuditComment(ctx context.Context, id int64, status int, reason string) error
	BatchAuditComments(ctx context.Context, ids []int64, status int, reason string) error
	ReplyComment(ctx context.Context, id int64, content string, adminName string) error
	LikeComment(ctx context.Context, id int64) error
	UnlikeComment(ctx context.Context, id int64) error
	GetUserComments(ctx context.Context, userID int64, req *dto.CommentListRequest) (*dto.CommentListResponse, error)
	GetCommentStatistics(ctx context.Context, productID int64) (*dto.CommentStatisticsResponse, error)
}
