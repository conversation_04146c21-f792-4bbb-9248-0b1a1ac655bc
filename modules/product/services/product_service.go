/**
 * 商品服务实现
 *
 * 本文件实现了商品服务接口，提供商品业务逻辑的处理实现。
 * 包括商品的创建、修改、查询等相关业务功能。
 */

package services

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/product/dto"
	"o_mall_backend/modules/product/models"
	"o_mall_backend/modules/product/repositories"
)

// ProductServiceImpl 商品服务实现
type ProductServiceImpl struct {
	productRepo      repositories.ProductRepository
	productSkuRepo   repositories.ProductSkuRepository
	categoryRepo     repositories.CategoryRepository
	specRepo         repositories.SpecificationRepository
	productImageRepo repositories.ProductImageRepository
}

// NewProductService 创建商品服务实例
func NewProductService() ProductService {
	return &ProductServiceImpl{
		productRepo:      repositories.NewProductRepository(),
		productSkuRepo:   repositories.NewProductSkuRepository(),
		categoryRepo:     repositories.NewCategoryRepository(),
		specRepo:         repositories.NewSpecificationRepository(),
		productImageRepo: repositories.NewProductImageRepository(),
	}
}

// Create 创建商品
// 处理商品创建的业务逻辑，包括保存商品基本信息、规格、图片等
func (s *ProductServiceImpl) Create(req *dto.CreateProductRequest) (*dto.ProductDetailResponse, error) {
	ctx := context.Background()

	// 检查分类是否存在
	categoryExists, err := s.categoryRepo.Exists(ctx, req.CategoryID)
	if err != nil {
		logs.Error("检查分类是否存在失败: %v", err)
		return nil, err
	}
	if !categoryExists {
		return nil, errors.New("商品分类不存在")
	}

	// 创建商品模型
	product := &models.Product{
		Name:          req.Name,
		Brief:         req.Brief,
		Description:   req.Description,
		CategoryID:    req.CategoryID,
		MainImage:     req.MainImage,
		Price:         req.Price,
		OriginalPrice: req.OriginalPrice,
		CostPrice:     req.CostPrice,
		Stock:         req.Stock,
		Unit:          req.Unit,
		Weight:        req.Weight,
		IsRecommend:   req.IsRecommend,
		IsHot:         req.IsHot,
		IsNew:         req.IsNew,
		Status:        models.ProductStatusDraft, // 默认为草稿状态
	}

	// 保存商品信息
	productID, err := s.productRepo.Create(ctx, product)
	if err != nil {
		logs.Error("创建商品失败: %v", err)
		return nil, err
	}

	// 保存商品图片
	if len(req.Images) > 0 {
		var productImages []*models.ProductImage
		for i, imageURL := range req.Images {
			productImage := &models.ProductImage{
				ProductID: productID,
				ImageURL:  imageURL,
				SortOrder: i,
				ImageType: models.ProductImageTypeNormal, // 普通图片
				CreatedAt: time.Now(),
			}
			productImages = append(productImages, productImage)
		}

		// 批量保存商品图片
		err = s.productImageRepo.BatchCreate(ctx, productImages)
		if err != nil {
			logs.Error("保存商品图片失败: %v", err)
			// 这里不回滚商品创建，继续处理
		}
	}

	// 处理商品详情图
	if len(req.DetailImages) > 0 {
		var detailImages []*models.ProductImage
		for i, imageURL := range req.DetailImages {
			detailImage := &models.ProductImage{
				ProductID: productID,
				ImageURL:  imageURL,
				SortOrder: i,
				ImageType: models.ProductImageTypeDetail, // 详情图片
				CreatedAt: time.Now(),
			}
			detailImages = append(detailImages, detailImage)
		}

		// 批量保存商品详情图
		err = s.productImageRepo.BatchCreate(ctx, detailImages)
		if err != nil {
			logs.Error("保存商品详情图失败: %v", err)
			// 这里不回滚商品创建，继续处理
		}
	}

	// 如果有SKU信息，保存SKU信息
	if len(req.Skus) > 0 {
		var productSkus []*models.ProductSku
		for _, skuReq := range req.Skus {
			sku := &models.ProductSku{
				ProductID:     productID,
				Code:          skuReq.Code,
				SpecData:      skuReq.SpecData,
				Price:         skuReq.Price,
				OriginalPrice: skuReq.OriginalPrice,
				CostPrice:     skuReq.CostPrice,
				Stock:         skuReq.Stock,
				CreatedAt:     time.Now(),
			}
			productSkus = append(productSkus, sku)
		}

		// 批量保存商品SKU
		err = s.productSkuRepo.BatchCreate(ctx, productSkus)
		if err != nil {
			logs.Error("保存商品SKU失败: %v", err)
			// 这里不回滚商品创建，继续处理
		}
	}

	// 获取创建的商品详情
	createdProduct, err := s.productRepo.GetByID(ctx, productID)
	if err != nil {
		logs.Error("获取创建的商品失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	productResponse := s.convertToProductResponse(createdProduct)
	response := &dto.ProductDetailResponse{
		ProductResponse: *productResponse,
		Description:     createdProduct.Description,
		Images:          make([]string, 0),
		DetailImages:    make([]string, 0),
		Skus:            make([]dto.ProductSkuResponse, 0),
		Specifications:  make([]dto.ProductSpecResponse, 0),
	}

	return response, nil
}

// GetProduct 获取商品详情
// 查询商品的详细信息，包括基本信息、规格、图片等
func (s *ProductServiceImpl) GetProduct(ctx context.Context, id int64) (*dto.ProductDetailResponse, error) {
	// 获取商品基本信息
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}
	if product == nil {
		return nil, errors.New("商品不存在")
	}

	// 增加商品浏览次数
	// 这里不需要等待浏览次数更新完成，可以异步处理
	go func() {
		ctx := context.Background()
		_ = s.productRepo.IncrViewCount(ctx, id)
	}()

	// 获取商品图片
	images, err := s.productImageRepo.ListByProductID(ctx, id, models.ProductImageTypeNormal)
	if err != nil {
		logs.Error("获取商品图片失败, 商品ID=%d, 错误: %v", id, err)
		// 不影响主流程，继续执行
	}

	// 获取商品详情图
	detailImages, err := s.productImageRepo.ListByProductID(ctx, id, models.ProductImageTypeDetail)
	if err != nil {
		logs.Error("获取商品详情图失败, 商品ID=%d, 错误: %v", id, err)
		// 不影响主流程，继续执行
	}

	// 获取商品SKU
	skus, err := s.productSkuRepo.ListByProductID(ctx, id)
	if err != nil {
		logs.Error("获取商品SKU失败, 商品ID=%d, 错误: %v", id, err)
		// 不影响主流程，继续执行
	}

	// 获取商品规格信息
	// 由于这里没有用到specs变量，注释掉以避免未使用变量警告
	// specs, err := s.specRepo.GetSpecsByProductID(ctx, id)
	// if err != nil {
	// 	logs.Error("获取商品规格失败, 商品ID=%d, 错误: %v", id, err)
	// 	// 不影响主流程，继续执行
	// }

	// 转换为响应对象
	response := &dto.ProductDetailResponse{
		ProductResponse: *s.convertToProductResponse(product),
		Description:     product.Description,
		Images:          make([]string, 0),
		DetailImages:    make([]string, 0),
		Skus:            make([]dto.ProductSkuResponse, 0),
		Specifications:  make([]dto.ProductSpecResponse, 0),
	}

	// 处理图片
	for _, img := range images {
		response.Images = append(response.Images, img.ImageURL)
	}

	// 处理详情图
	for _, img := range detailImages {
		response.DetailImages = append(response.DetailImages, img.ImageURL)
	}

	// 处理SKU
	for _, sku := range skus {
		skuResp := dto.ProductSkuResponse{
			ID: sku.ID,
			// 将 sku 模型字段映射到 DTO 字段
			// 注意： 根据 DTO 定义，调整字段名称
			Code:          sku.Code,
			Price:         sku.Price,
			OriginalPrice: sku.OriginalPrice,
			CostPrice:     sku.CostPrice,
			Stock:         sku.Stock,
			SpecData:      sku.SpecData,
		}
		response.Skus = append(response.Skus, skuResp)
	}

	// 处理商品规格信息
	// 这里需要进一步处理规格和规格值的关系，暂时简化处理

	return response, nil
}

// UpdateProduct 更新商品信息
// 修改商品的基本信息、规格、图片等
func (s *ProductServiceImpl) UpdateProduct(ctx context.Context, id int64, req *dto.UpdateProductRequest) error {
	// 获取商品信息
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if product == nil {
		return errors.New("商品不存在")
	}

	// 检查分类是否存在
	if req.CategoryID > 0 && req.CategoryID != product.CategoryID {
		categoryExists, err := s.categoryRepo.Exists(ctx, req.CategoryID)
		if err != nil {
			logs.Error("检查分类是否存在失败: %v", err)
			return err
		}
		if !categoryExists {
			return errors.New("商品分类不存在")
		}
		product.CategoryID = req.CategoryID
	}

	// 更新商品基本信息
	if req.Name != "" {
		product.Name = req.Name
	}
	if req.Brief != "" {
		product.Brief = req.Brief
	}
	if req.Description != "" {
		product.Description = req.Description
	}
	if req.Price > 0 {
		product.Price = req.Price
	}
	if req.OriginalPrice > 0 {
		product.OriginalPrice = req.OriginalPrice
	}
	if req.CostPrice > 0 {
		product.CostPrice = req.CostPrice
	}
	if req.Stock >= 0 {
		product.Stock = req.Stock
	}
	if req.Unit != "" {
		product.Unit = req.Unit
	}
	if req.Weight > 0 {
		product.Weight = req.Weight
	}

	// 更新推荐、热门、新品状态
	product.IsRecommend = req.IsRecommend
	product.IsHot = req.IsHot
	product.IsNew = req.IsNew

	// 保存商品信息
	err = s.productRepo.Update(ctx, product)
	if err != nil {
		logs.Error("更新商品失败: %v", err)
		return err
	}

	// 如果有更新图片，则先删除旧图片，再添加新图片
	if len(req.Images) > 0 {
		// 删除旧图片
		err = s.productImageRepo.DeleteByProductID(ctx, id)
		if err != nil {
			logs.Error("删除商品图片失败: %v", err)
			// 不影响主流程，继续执行
		}

		// 添加新图片
		var productImages []*models.ProductImage
		for i, imageURL := range req.Images {
			productImage := &models.ProductImage{
				ProductID: id,
				ImageURL:  imageURL,
				SortOrder: i,
				ImageType: models.ProductImageTypeNormal,
				CreatedAt: time.Now(),
			}
			productImages = append(productImages, productImage)
		}

		// 批量保存商品图片
		err = s.productImageRepo.BatchCreate(ctx, productImages)
		if err != nil {
			logs.Error("保存商品图片失败: %v", err)
			// 不影响主流程，继续执行
		}
	}

	// 检查是否有并处理详情图更新
	// 注意：UpdateProductRequest 可能没有 DetailImages 字段，这里注释掉相关逻辑

	// 如果有更新SKU，则先删除旧SKU，再添加新SKU
	if len(req.Skus) > 0 {
		// 删除旧SKU
		err = s.productSkuRepo.BatchDeleteByProductID(ctx, id)
		if err != nil {
			logs.Error("删除商品SKU失败: %v", err)
			// 不影响主流程，继续执行
		}

		// 添加新SKU
		var productSkus []*models.ProductSku
		for _, skuReq := range req.Skus {
			sku := &models.ProductSku{
				ProductID:     id,
				Code:          skuReq.Code,
				SpecData:      skuReq.SpecData,
				Price:         skuReq.Price,
				OriginalPrice: skuReq.OriginalPrice,
				CostPrice:     skuReq.CostPrice,
				Stock:         skuReq.Stock,
				CreatedAt:     time.Now(),
			}
			productSkus = append(productSkus, sku)
		}

		// 批量保存商品SKU
		err = s.productSkuRepo.BatchCreate(ctx, productSkus)
		if err != nil {
			logs.Error("保存商品SKU失败: %v", err)
			// 不影响主流程，继续执行
		}
	}

	return nil
}

// DeleteProduct 删除商品
// 删除商品及其关联信息
func (s *ProductServiceImpl) DeleteProduct(ctx context.Context, id int64) error {
	// 检查商品是否存在
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if product == nil {
		return errors.New("商品不存在")
	}

	// 删除商品图片
	err = s.productImageRepo.DeleteByProductID(ctx, id)
	if err != nil {
		logs.Error("删除商品图片失败: %v", err)
		// 不影响主流程，继续执行
	}

	// 删除商品SKU
	err = s.productSkuRepo.BatchDeleteByProductID(ctx, id)
	if err != nil {
		logs.Error("删除商品SKU失败: %v", err)
		// 不影响主流程，继续执行
	}

	// 删除商品规格关联
	err = s.specRepo.UnlinkAllProductSpec(ctx, id)
	if err != nil {
		logs.Error("删除商品规格关联失败: %v", err)
		// 不影响主流程，继续执行
	}

	// 删除商品规格值关联
	err = s.specRepo.UnlinkAllProductSpecValue(ctx, id)
	if err != nil {
		logs.Error("删除商品规格值关联失败: %v", err)
		// 不影响主流程，继续执行
	}

	// 删除商品
	err = s.productRepo.Delete(ctx, id)
	if err != nil {
		logs.Error("删除商品失败: %v", err)
		return err
	}

	return nil
}

// BatchDeleteProducts 批量删除商品
// 批量删除多个商品
func (s *ProductServiceImpl) BatchDeleteProducts(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 批量删除商品关联的数据
	for _, id := range ids {
		// 删除商品图片
		_ = s.productImageRepo.DeleteByProductID(ctx, id)

		// 删除商品SKU
		_ = s.productSkuRepo.BatchDeleteByProductID(ctx, id)

		// 删除商品规格关联
		_ = s.specRepo.UnlinkAllProductSpec(ctx, id)

		// 删除商品规格值关联
		_ = s.specRepo.UnlinkAllProductSpecValue(ctx, id)
	}

	// 批量删除商品
	err := s.productRepo.BatchDelete(ctx, ids)
	if err != nil {
		logs.Error("批量删除商品失败: %v", err)
		return err
	}

	return nil
}

// UpdateProductStatus 更新商品状态
// 修改商品状态，如上架、下架等
func (s *ProductServiceImpl) UpdateProductStatus(ctx context.Context, id int64, status int, rejectReason string) error {
	// 检查商品是否存在
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", id, err)
		return err
	}
	if product == nil {
		return errors.New("商品不存在")
	}

	// 验证状态值
	if status < models.ProductStatusDraft || status > models.ProductStatusRejected {
		return errors.New("无效的商品状态")
	}

	// 更新商品状态
	err = s.productRepo.UpdateStatus(ctx, id, status, rejectReason)
	if err != nil {
		logs.Error("更新商品状态失败: %v", err)
		return err
	}

	return nil
}

// CreateProductComment 创建商品评论
// 为商品添加评论
func (s *ProductServiceImpl) CreateProductComment(req *dto.CreateProductCommentRequest) (*dto.ProductCommentResponse, error) {
	ctx := context.Background()

	// 检查商品是否存在
	product, err := s.productRepo.GetByID(ctx, req.ProductID)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", req.ProductID, err)
		return nil, err
	}
	if product == nil {
		return nil, errors.New("商品不存在")
	}

	// 此处应创建评论并返回评论响应
	// 由于我们没有完整的评论服务实现，这里简单返回模拟数据
	return &dto.ProductCommentResponse{
		ID:          1,
		ProductID:   req.ProductID,
		ProductName: product.Name,
		UserID:      1,
		Content:     req.Content,
		Rating:      req.Rating,
		CreatedAt:   time.Now(),
	}, nil
}

// Delete 删除商品
// 实现 ProductService 接口的 Delete 方法
func (s *ProductServiceImpl) Delete(id int64) error {
	ctx := context.Background()
	return s.DeleteProduct(ctx, id)
}

// Get 获取商品详情
// 实现 ProductService 接口的 Get 方法
func (s *ProductServiceImpl) Get(id int64) (*dto.ProductDetailResponse, error) {
	ctx := context.Background()
	// 避免递归调用，不能直接调用GetProduct
	// 获取商品基本信息
	product, err := s.productRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", id, err)
		return nil, err
	}
	if product == nil {
		return nil, errors.New("商品不存在")
	}

	// 构建详情响应对象
	productResponse := s.convertToProductResponse(product)
	response := &dto.ProductDetailResponse{
		ProductResponse: *productResponse,
		Description:     product.Description,
		// 其他字段需要从数据库查询或通过其他方式获取
	}

	return response, nil
}

// GetProductComments 获取商品评论列表
// 实现 ProductService 接口的 GetProductComments 方法
func (s *ProductServiceImpl) GetProductComments(req *dto.ProductCommentQueryRequest) ([]*dto.ProductCommentResponse, int64, error) {
	ctx := context.Background()

	// 检查商品是否存在
	product, err := s.productRepo.GetByID(ctx, req.ProductID)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", req.ProductID, err)
		return nil, 0, err
	}
	if product == nil {
		return nil, 0, errors.New("商品不存在")
	}

	// 初始化评论仓库
	commentRepo := repositories.NewCommentRepository()

	// 构建查询条件
	status := models.CommentStatusApproved
	if req.Rating > 0 {
		// 如果指定了评分，则按评分筛选
		// 这里需要根据实际需求实现
	}

	// 获取评论列表，只获取已审核通过的评论
	// 正确的返回值顺序是：评论列表、总数量、错误
	commentList, totalCount, err := commentRepo.GetByProductID(ctx, req.ProductID, req.Page, req.PageSize, status)
	if err != nil {
		logs.Error("获取商品评论失败: %v", err)
		return nil, 0, err
	}

	// 构建响应
	result := make([]*dto.ProductCommentResponse, 0, len(commentList))
	for _, comment := range commentList {
		// 获取评论图片
		images, _ := commentRepo.GetCommentImages(ctx, comment.ID)
		imageUrls := make([]string, 0, len(images))
		for _, img := range images {
			imageUrls = append(imageUrls, img.ImageURL)
		}

		// 构建评论响应
		commentResp := &dto.ProductCommentResponse{
			ID:           comment.ID,
			ProductID:    comment.ProductID,
			ProductName:  product.Name,
			ProductImage: product.MainImage,
			UserID:       comment.UserID,
			UserName:     comment.UserName,
			UserAvatar:   comment.UserAvatar,
			Content:      comment.Content,
			Rating:       comment.Rating,
			IsAnonymous:  comment.IsAnonymous,
			SpecInfo:     comment.ProductSkuInfo,
			CreatedAt:    comment.CreatedAt,
			Images:       imageUrls,
		}

		result = append(result, commentResp)
	}

	return result, totalCount, nil
}

// List 获取商品列表
// 实现 ProductService 接口的 List 方法
func (s *ProductServiceImpl) List(req *dto.ProductQueryRequest) ([]*dto.ProductListResponse, int64, error) {
	ctx := context.Background()

	// 调用 ListProducts 方法获取商品列表
	response, err := s.ListProducts(ctx, req)
	if err != nil {
		return nil, 0, err
	}

	// 将每个商品转换为 ProductListResponse
	result := make([]*dto.ProductListResponse, 0, len(response.List))
	for _, item := range response.List {
		productResponse := &dto.ProductListResponse{
			ID:            item.ID,
			Name:          item.Name,
			Brief:         item.Brief,
			CategoryID:    item.CategoryID,
			CategoryName:  item.CategoryName,
			MerchantID:    item.MerchantID,
			MerchantName:  item.MerchantName,
			MainImage:     item.MainImage,
			Price:         item.Price,
			OriginalPrice: item.OriginalPrice,
			Stock:         item.Stock,
			SoldNum:       item.SoldNum,
			IsRecommend:   item.IsRecommend,
			IsHot:         item.IsHot,
			IsNew:         item.IsNew,
			Status:        item.Status,
			StatusText:    item.StatusText,
			ViewCount:     item.ViewCount,
			CommentCount:  item.CommentCount,
			FavoriteCount: item.FavoriteCount,
			HasSKU:        item.HasSKU,
			CreatedAt:     item.CreatedAt,
		}
		result = append(result, productResponse)
	}

	return result, response.Total, nil
}

// Update 更新商品信息
// 实现 ProductService 接口的 Update 方法
func (s *ProductServiceImpl) Update(req *dto.UpdateProductRequest) error {
	ctx := context.Background()
	return s.UpdateProduct(ctx, req.ID, req)
}

// UpdateStatus 更新商品状态
// 实现 ProductService 接口的 UpdateStatus 方法
func (s *ProductServiceImpl) UpdateStatus(req *dto.UpdateProductStatusRequest) error {
	ctx := context.Background()
	return s.UpdateProductStatus(ctx, req.ID, req.Status, req.Reason)
}

// ListProducts 获取商品列表
// 实现 ProductService 接口的 ListProducts 方法
// 此方法作为适配器，将原有的 List 方法包装为控制器所需的接口形式
func (s *ProductServiceImpl) ListProducts(ctx context.Context, req *dto.ProductQueryRequest) (*dto.ProductListResponseWrapper, error) {
	// 调用原有的List方法
	products, total, err := s.List(req)
	if err != nil {
		return nil, err
	}

	// 转换为控制器期望的返回类型
	response := &dto.ProductListResponseWrapper{
		Total: total,
		List:  make([]*dto.ProductResponse, 0, len(products)),
		Page:  req.Page,
		Size:  req.PageSize,
	}

	// 将ProductListResponse转换为ProductResponse
	for _, product := range products {
		productResp := &dto.ProductResponse{
			ID:            product.ID,
			Name:          product.Name,
			Brief:         product.Brief,
			CategoryID:    product.CategoryID,
			CategoryName:  product.CategoryName,
			MerchantID:    product.MerchantID,
			MerchantName:  product.MerchantName,
			MainImage:     product.MainImage,
			Price:         product.Price,
			OriginalPrice: product.OriginalPrice,
			Stock:         product.Stock,
			SoldNum:       product.SoldNum,
			IsRecommend:   product.IsRecommend,
			IsHot:         product.IsHot,
			IsNew:         product.IsNew,
			Status:        product.Status,
			StatusText:    product.StatusText,
			ViewCount:     product.ViewCount,
			CommentCount:  product.CommentCount,
			FavoriteCount: product.FavoriteCount,
			HasSKU:        product.HasSKU,
			CreatedAt:     product.CreatedAt,
		}
		response.List = append(response.List, productResp)
	}

	return response, nil
}

// IncrProductViewCount 增加商品浏览次数
// 实现 ProductService 接口的 IncrProductViewCount 方法
func (s *ProductServiceImpl) IncrProductViewCount(ctx context.Context, id int64) error {
	return s.productRepo.IncrViewCount(ctx, id)
}

// CreateProduct 创建商品
// 实现 ProductService 接口的 CreateProduct 方法
func (s *ProductServiceImpl) CreateProduct(ctx context.Context, req *dto.CreateProductRequest) (*dto.ProductDetailResponse, error) {
	// 直接调用现有的Create方法
	return s.Create(req)
}

// GetRecommendProducts 获取推荐商品
func (s *ProductServiceImpl) GetRecommendProducts(ctx context.Context, limit int) ([]*dto.ProductListResponse, error) {
	// 调用仓库查询推荐商品
	products, err := s.productRepo.GetRecommend(ctx, limit)
	if err != nil {
		logs.Error("获取推荐商品失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	result := make([]*dto.ProductListResponse, 0, len(products))
	for _, product := range products {
		resp := &dto.ProductListResponse{
			ID:            product.ID,
			Name:          product.Name,
			Brief:         product.Brief,
			CategoryID:    product.CategoryID,
			MainImage:     product.MainImage,
			Price:         product.Price,
			OriginalPrice: product.OriginalPrice,
			Stock:         product.Stock,
			IsRecommend:   product.IsRecommend,
			IsHot:         product.IsHot,
			IsNew:         product.IsNew,
			Status:        product.Status,
			CreatedAt:     product.CreatedAt,
		}
		result = append(result, resp)
	}

	return result, nil
}

// GetHotProducts 获取热销商品
func (s *ProductServiceImpl) GetHotProducts(ctx context.Context, limit int) ([]*dto.ProductListResponse, error) {
	// 调用仓库查询热销商品
	products, err := s.productRepo.GetHot(ctx, limit)
	if err != nil {
		logs.Error("获取热销商品失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	result := make([]*dto.ProductListResponse, 0, len(products))
	for _, product := range products {
		resp := &dto.ProductListResponse{
			ID:            product.ID,
			Name:          product.Name,
			Brief:         product.Brief,
			CategoryID:    product.CategoryID,
			MainImage:     product.MainImage,
			Price:         product.Price,
			OriginalPrice: product.OriginalPrice,
			Stock:         product.Stock,
			IsRecommend:   product.IsRecommend,
			IsHot:         product.IsHot,
			IsNew:         product.IsNew,
			Status:        product.Status,
			CreatedAt:     product.CreatedAt,
		}
		result = append(result, resp)
	}

	return result, nil
}

// GetNewProducts 获取新品商品
func (s *ProductServiceImpl) GetNewProducts(ctx context.Context, limit int) ([]*dto.ProductListResponse, error) {
	// 调用仓库查询新品商品
	products, err := s.productRepo.GetNew(ctx, limit)
	if err != nil {
		logs.Error("获取新品商品失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	result := make([]*dto.ProductListResponse, 0, len(products))
	for _, product := range products {
		resp := &dto.ProductListResponse{
			ID:            product.ID,
			Name:          product.Name,
			Brief:         product.Brief,
			CategoryID:    product.CategoryID,
			MainImage:     product.MainImage,
			Price:         product.Price,
			OriginalPrice: product.OriginalPrice,
			Stock:         product.Stock,
			IsRecommend:   product.IsRecommend,
			IsHot:         product.IsHot,
			IsNew:         product.IsNew,
			Status:        product.Status,
			CreatedAt:     product.CreatedAt,
		}
		result = append(result, resp)
	}

	return result, nil
}

// CheckStock 检查库存
func (s *ProductServiceImpl) CheckStock(ctx context.Context, productID int64, skuID int64, quantity int) (bool, error) {
	// 检查商品是否存在
	product, err := s.productRepo.GetByID(ctx, productID)
	if err != nil {
		logs.Error("获取商品失败, ID=%d, 错误: %v", productID, err)
		return false, err
	}
	if product == nil {
		return false, errors.New("商品不存在")
	}

	// 如果有指定SKU，则检查SKU库存
	if skuID > 0 {
		sku, err := s.productSkuRepo.GetByID(ctx, skuID)
		if err != nil {
			logs.Error("获取SKU失败, ID=%d, 错误: %v", skuID, err)
			return false, err
		}
		if sku == nil {
			return false, errors.New("商品规格不存在")
		}

		// 检查SKU库存是否足够
		return sku.Stock >= quantity, nil
	}

	// 否则检查商品总库存
	return product.Stock >= quantity, nil
}

// convertToProductResponse 将商品模型转换为响应DTO
// 内部辅助方法，转换数据格式
func (s *ProductServiceImpl) convertToProductResponse(product *models.Product) *dto.ProductResponse {
	if product == nil {
		return nil
	}

	return &dto.ProductResponse{
		ID:            product.ID,
		Name:          product.Name,
		Brief:         product.Brief,
		CategoryID:    product.CategoryID,
		MerchantID:    product.MerchantID,
		Price:         product.Price,
		OriginalPrice: product.OriginalPrice,
		Unit:          product.Unit,
		Stock:         product.Stock,
		SoldNum:       product.SoldNum,
		ViewCount:     product.ViewCount,
		CommentCount:  product.CommentCount,
		FavoriteCount: product.FavoriteCount,
		IsRecommend:   product.IsRecommend,
		IsHot:         product.IsHot,
		IsNew:         product.IsNew,
		Status:        product.Status,
		CreatedAt:     product.CreatedAt,
		UpdatedAt:     product.UpdatedAt,
	}
}
