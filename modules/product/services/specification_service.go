/**
 * 商品规格服务
 *
 * 本文件实现了商品规格相关的业务逻辑，包括规格的创建、查询、更新、删除等操作。
 * 规格是商品的属性类别，如颜色、尺寸等，规格值是具体的属性值，如红色、XL等。
 */

package services

import (
	"context"
	"o_mall_backend/modules/product/dto"
	"o_mall_backend/modules/product/models"
	"o_mall_backend/modules/product/repositories"
)

// specificationServiceImpl 规格服务实现
type specificationServiceImpl struct {
	specRepo repositories.SpecificationRepository
}

// NewSpecificationService 创建规格服务
// 构造器函数，用于创建规格服务实例
// 参数:
//   - specRepo: 规格仓库接口，提供数据访问能力
//
// 返回:
//   - SpecificationService: 规格服务接口实例
func NewSpecificationService(specRepo repositories.SpecificationRepository) SpecificationService {
	return &specificationServiceImpl{
		specRepo: specRepo,
	}
}

// Create 创建商品规格
// 根据请求创建新的商品规格信息
// 参数:
//   - req: 创建规格请求，包含规格名称、描述、排序值等信息
//
// 返回:
//   - *dto.SpecificationResponse: 创建成功的规格响应
//   - error: 创建过程中发生的错误，如数据库操作失败
func (s *specificationServiceImpl) Create(req *dto.CreateSpecificationRequest) (*dto.SpecificationResponse, error) {
	spec := &models.Specification{
		Name:        req.Name,
		Description: req.Description,
		Sort:        req.SortOrder,
		IsGlobal:    req.IsGlobal,
	}

	_, err := s.specRepo.Create(context.Background(), spec)
	if err != nil {
		return nil, err
	}

	resp := s.convertToSpecificationResponse(spec)
	return resp, nil
}

// Get 获取规格详情
// 根据规格ID获取规格详细信息
// 参数:
//   - id: 规格ID
//
// 返回:
//   - *dto.SpecificationResponse: 规格详情响应
//   - error: 获取过程中发生的错误，如规格不存在
func (s *specificationServiceImpl) Get(id int64) (*dto.SpecificationResponse, error) {
	spec, err := s.specRepo.GetByID(context.Background(), id)
	if err != nil {
		return nil, err
	}

	resp := s.convertToSpecificationResponse(spec)
	return resp, nil
}

// Update 更新规格信息
// 根据请求更新现有规格的信息
// 参数:
//   - req: 更新规格请求，包含需要更新的规格字段
//
// 返回:
//   - error: 更新过程中发生的错误，如规格不存在或数据验证失败
func (s *specificationServiceImpl) Update(req *dto.UpdateSpecificationRequest) error {
	spec, err := s.specRepo.GetByID(context.Background(), req.ID)
	if err != nil {
		return err
	}

	spec.Name = req.Name
	spec.Description = req.Description
	spec.Sort = req.SortOrder
	spec.IsGlobal = req.IsGlobal
	spec.Status = req.Status

	return s.specRepo.Update(context.Background(), spec)
}

// Delete 删除规格
// 根据规格ID删除规格
// 参数:
//   - id: 需要删除的规格ID
//
// 返回:
//   - error: 删除过程中发生的错误，如规格不存在
func (s *specificationServiceImpl) Delete(id int64) error {
	return s.specRepo.Delete(context.Background(), id)
}

// List 获取规格列表
// 分页获取所有规格信息
// 参数:
//   - page: 页码，从1开始
//   - pageSize: 每页条数
//
// 返回:
//   - []*dto.SpecificationResponse: 规格列表
//   - int64: 总条数
//   - error: 查询过程中发生的错误
func (s *specificationServiceImpl) List(page, pageSize int) ([]*dto.SpecificationResponse, int64, error) {
	query := map[string]interface{}{}
	specs, err := s.specRepo.GetAll(context.Background(), query)
	if err != nil {
		return nil, 0, err
	}

	total := int64(len(specs))

	respList := make([]*dto.SpecificationResponse, len(specs))
	for i, spec := range specs {
		respList[i] = s.convertToSpecificationResponse(spec)
	}

	return respList, total, nil
}

// CreateSpecValue 创建规格值
// 为指定规格创建新的规格值
// 参数:
//   - req: 创建规格值请求，包含规格ID和规格值内容
//
// 返回:
//   - *dto.SpecValueResponse: 创建成功的规格值响应
//   - error: 创建过程中发生的错误，如规格不存在
func (s *specificationServiceImpl) CreateSpecValue(req *dto.CreateSpecValueRequest) (*dto.SpecValueResponse, error) {
	specValue := &models.SpecificationValue{
		SpecID:    req.SpecID,
		Value:     req.Value,
		SortOrder: req.SortOrder,
	}

	_, err := s.specRepo.CreateValue(context.Background(), specValue)
	if err != nil {
		return nil, err
	}

	resp := s.convertToSpecValueResponse(specValue)
	return resp, nil
}

// GetSpecValue 获取规格值详情
// 根据规格值ID获取规格值详细信息
// 参数:
//   - id: 规格值ID
//
// 返回:
//   - *dto.SpecValueResponse: 规格值详情响应
//   - error: 获取过程中发生的错误，如规格值不存在
func (s *specificationServiceImpl) GetSpecValue(id int64) (*dto.SpecValueResponse, error) {
	specValue, err := s.specRepo.GetValueByID(context.Background(), id)
	if err != nil {
		return nil, err
	}

	resp := s.convertToSpecValueResponse(specValue)
	return resp, nil
}

// UpdateSpecValue 更新规格值
// 根据请求更新现有规格值的信息
// 参数:
//   - req: 更新规格值请求，包含需要更新的规格值字段
//
// 返回:
//   - error: 更新过程中发生的错误，如规格值不存在
func (s *specificationServiceImpl) UpdateSpecValue(req *dto.UpdateSpecValueRequest) error {
	specValue, err := s.specRepo.GetValueByID(context.Background(), req.ID)
	if err != nil {
		return err
	}

	specValue.SpecID = req.SpecID
	specValue.Value = req.Value
	specValue.SortOrder = req.SortOrder

	return s.specRepo.UpdateValue(context.Background(), specValue)
}

// DeleteSpecValue 删除规格值
// 根据规格值ID删除规格值
// 参数:
//   - id: 需要删除的规格值ID
//
// 返回:
//   - error: 删除过程中发生的错误，如规格值不存在
func (s *specificationServiceImpl) DeleteSpecValue(id int64) error {
	return s.specRepo.DeleteValue(context.Background(), id)
}

// ListSpecValues 获取规格值列表
// 获取指定规格的所有规格值
// 参数:
//   - specID: 规格ID
//
// 返回:
//   - []*dto.SpecValueResponse: 规格值列表
//   - error: 查询过程中发生的错误，如规格不存在
func (s *specificationServiceImpl) ListSpecValues(specID int64) ([]*dto.SpecValueResponse, error) {
	specValues, err := s.specRepo.GetValuesBySpecID(context.Background(), specID)
	if err != nil {
		return nil, err
	}

	respList := make([]*dto.SpecValueResponse, len(specValues))
	for i, specValue := range specValues {
		respList[i] = s.convertToSpecValueResponse(specValue)
	}

	return respList, nil
}

// GetProductSpecs 获取商品规格
// 获取指定商品的所有规格和规格值信息
// 参数:
//   - productID: 商品ID
//
// 返回:
//   - []dto.ProductSpecResponse: 商品规格列表，包含规格名称和规格值
//   - error: 查询过程中发生的错误，如商品不存在
func (s *specificationServiceImpl) GetProductSpecs(productID int64) ([]dto.ProductSpecResponse, error) {
	specs, err := s.specRepo.GetSpecsByProductID(context.Background(), productID)
	if err != nil {
		return nil, err
	}

	respList := make([]dto.ProductSpecResponse, len(specs))
	for i, spec := range specs {
		specValueList, err := s.specRepo.GetSpecValuesByProductID(context.Background(), spec.ID)
		if err != nil {
			return nil, err
		}

		specValueRespList := make([]dto.ProductSpecValueResponse, len(specValueList))
		for j, value := range specValueList {
			specValue, err := s.specRepo.GetValueByID(context.Background(), value.SpecValueID)
			var valueText string
			if err == nil && specValue != nil {
				valueText = specValue.Value
			} else {
				valueText = "未知"
			}

			specValueRespList[j] = dto.ProductSpecValueResponse{
				ID:    value.ID,
				Value: valueText,
			}
		}

		specName := ""
		originalSpec, err := s.specRepo.GetByID(context.Background(), spec.SpecID)
		if err == nil && originalSpec != nil {
			specName = originalSpec.Name
		}

		respList[i] = dto.ProductSpecResponse{
			SpecificationID: spec.SpecID,
			Name:            specName,
			IsRequired:      true,
			Values:          specValueRespList,
		}
	}

	return respList, nil
}

// convertToSpecificationResponse 转换为规格响应
// 内部辅助方法，将规格模型转换为规格响应DTO
// 参数:
//   - spec: 规格模型
//
// 返回:
//   - *dto.SpecificationResponse: 规格响应DTO，如果输入为nil则返回nil
func (s *specificationServiceImpl) convertToSpecificationResponse(spec *models.Specification) *dto.SpecificationResponse {
	if spec == nil {
		return nil
	}

	return &dto.SpecificationResponse{
		ID:          spec.ID,
		Name:        spec.Name,
		Description: spec.Description,
		SortOrder:   spec.Sort,
		IsGlobal:    spec.IsGlobal,
		Status:      spec.Status,
		CreatedAt:   spec.CreatedAt,
		UpdatedAt:   spec.UpdatedAt,
	}
}

// convertToSpecValueResponse 转换为规格值响应
// 内部辅助方法，将规格值模型转换为规格值响应DTO
// 参数:
//   - specValue: 规格值模型
//
// 返回:
//   - *dto.SpecValueResponse: 规格值响应DTO，如果输入为nil则返回nil
func (s *specificationServiceImpl) convertToSpecValueResponse(specValue *models.SpecificationValue) *dto.SpecValueResponse {
	if specValue == nil {
		return nil
	}

	return &dto.SpecValueResponse{
		ID:        specValue.ID,
		SpecID:    specValue.SpecID,
		Value:     specValue.Value,
		SortOrder: specValue.SortOrder,
		CreatedAt: specValue.CreatedAt,
		UpdatedAt: specValue.UpdatedAt,
	}
}
