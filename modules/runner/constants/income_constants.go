/**
 * income_constants.go
 * 跑腿员收入相关常量定义
 *
 * 本文件定义了跑腿员收入相关的常量，包括收入类型、状态等
 */

package constants

// 收入类型常量
const (
	IncomeTypeDelivery = 1 // 配送收入
	IncomeTypeBonus    = 2 // 奖励收入
	IncomeTypeWithdraw = 3 // 提现
	IncomeTypeRefund   = 4 // 退款
	IncomeTypeTip      = 5 // 小费
)

// 收入状态常量
const (
	IncomeStatusPending  = 0 // 待入账
	IncomeStatusSuccess  = 1 // 已入账
	IncomeStatusFailed   = 2 // 入账失败
	IncomeStatusRefunded = 3 // 已退款
)

// 提现状态常量
const (
	WithdrawalStatusPending    = 0 // 待处理
	WithdrawalStatusProcessing = 1 // 处理中
	WithdrawalStatusSuccess    = 2 // 成功
	WithdrawalStatusFailed     = 3 // 失败
	WithdrawalStatusCanceled   = 4 // 已取消
)

// 提现方式常量
const (
	WithdrawalMethodWeChat = 1 // 微信提现
	WithdrawalMethodAliPay = 2 // 支付宝提现
	WithdrawalMethodBank   = 3 // 银行卡提现
)
