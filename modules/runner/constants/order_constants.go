/**
 * order_constants.go
 * 跑腿订单常量定义
 *
 * 本文件定义了跑腿订单相关的常量，包括订单状态、支付状态、配送状态等
 */

package constants

// 订单状态常量
const (
	OrderStatusWaitingPay = 10 // 待支付（初始状态）
	OrderStatusPaid       = 20 // 已支付，待接单
	OrderStatusAccepted   = 30 // 已接单，待取货
	OrderStatusPickedUp   = 40 // 已取货，配送中
	OrderStatusCompleted  = 50 // 已完成
	OrderStatusCanceled   = 60 // 已取消
)

// 配送状态常量
const (
	DeliveryStatusWaiting    = 0  // 待配送（初始状态）
	DeliveryStatusPending    = 10 // 待接单
	DeliveryStatusAccepted   = 20 // 已接单
	DeliveryStatusPicking    = 30 // 取餐中
	DeliveryStatusPickedUp   = 40 // 已取餐
	DeliveryStatusDelivering = 50 // 配送中
	DeliveryStatusCompleted  = 60 // 已送达
	DeliveryStatusCancelled  = 70 // 已取消
)

// 支付状态常量
const (
	PayStatusUnpaid = 0 // 未支付
	PayStatusPaid   = 1 // 已支付
	PayStatusRefund = 2 // 已退款
)

// 支付方式常量
const (
	PayMethodWeChat  = 1 // 微信支付
	PayMethodAliPay  = 2 // 支付宝
	PayMethodBalance = 3 // 余额支付
)

// 用户类型常量
const (
	UserTypeUser   = 1 // 普通用户
	UserTypeRunner = 2 // 跑腿员
)

// 订单类型常量
const (
	OrderTypeNormal  = 1 // 普通订单
	OrderTypeUrgent  = 2 // 加急订单
	OrderTypeSpecial = 3 // 特殊订单
)
