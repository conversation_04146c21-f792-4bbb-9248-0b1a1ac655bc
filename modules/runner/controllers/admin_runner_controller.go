/*
 * admin_runner_controller.go
 * 管理员跑腿员管理控制器
 *
 * 本文件实现了管理员对跑腿员的管理功能，包括查看、审核、暂停、删除、黑名单等操作
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/runner/dto"
	"o_mall_backend/modules/runner/services"
	"o_mall_backend/modules/runner/services/impl"
	"o_mall_backend/utils/common"
)

// AdminRunnerController 管理员跑腿员管理控制器
type AdminRunnerController struct {
	web.Controller
	runnerService services.RunnerService
}

// ParseRequest 通用请求参数解析方法
func (c *AdminRunnerController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// Prepare 初始化
func (c *AdminRunnerController) Prepare() {
	c.runnerService = impl.NewRunnerService()
}

// ListRunners 获取跑腿员列表
// @Title ListRunners
// @Description 管理员获取跑腿员列表
// @Param   page     query    int     false   "页码，默认1"
// @Param   pageSize query    int     false   "每页数量，默认10"
// @Param   status   query    int     false   "状态筛选：-1全部（包含待审核申请和已审核跑腿员）0待审核1审核通过2审核拒绝3暂停服务4黑名单，默认0（待审核）"
// @Param   keyword  query    string  false   "关键词搜索（姓名、手机号）"
// @Success 200 {object} result.Response{data=dto.AdminRunnerListResponse}
// @Failure 400 {object} result.Response
// @router /list [get]
func (c *AdminRunnerController) ListRunners() {
	// 解析查询参数
	page, _ := strconv.Atoi(c.GetString("page", "1"))
	pageSize, _ := strconv.Atoi(c.GetString("pageSize", "10"))
	status, _ := strconv.Atoi(c.GetString("status", "-1"))
	keyword := c.GetString("keyword")

	// 构建请求参数
	req := &dto.AdminRunnerListRequest{
		Page:     page,
		PageSize: pageSize,
		Status:   status,
		Keyword:  keyword,
	}

	// 调用服务
	resp, err := c.runnerService.AdminListRunners(c.Ctx.Request.Context(), req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetRunnerDetail 获取跑腿员详细信息
// @Title GetRunnerDetail
// @Description 管理员获取跑腿员详细信息
// @Param   id   path    int  true   "跑腿员ID"
// @Success 200 {object} result.Response{data=dto.AdminRunnerDetailResponse}
// @Failure 400 {object} result.Response
// @router /:id/detail [get]
func (c *AdminRunnerController) GetRunnerDetail() {
	// 获取跑腿员ID
	idStr := c.Ctx.Input.Param(":id")
	runnerID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的跑腿员ID"))
		return
	}

	// 调用服务
	resp, err := c.runnerService.AdminGetRunnerDetail(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// AuditRunner 审核跑腿员申请
// @Title AuditRunner
// @Description 管理员审核跑腿员申请
// @Param   id   path    int                           true   "跑腿员ID"
// @Param   body body    dto.AdminAuditRunnerRequest   true   "审核信息"
// @Success 200 {object} result.Response
// @Failure 400 {object} result.Response
// @router /:id/audit [put]
func (c *AdminRunnerController) AuditRunner() {
	// 获取跑腿员ID
	idStr := c.Ctx.Input.Param(":id")
	runnerID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的跑腿员ID"))
		return
	}

	// 解析请求
	var req dto.AdminAuditRunnerRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 获取管理员ID - 从JWT中间件设置的userID获取
	adminID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 调用服务
	err = c.runnerService.AdminAuditRunner(c.Ctx.Request.Context(), runnerID, &req, adminID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// UpdateRunnerStatus 更新跑腿员状态
// @Title UpdateRunnerStatus
// @Description 管理员更新跑腿员状态（暂停、恢复、拉黑等）
// @Param   id   path    int                               true   "跑腿员ID"
// @Param   body body    dto.AdminUpdateRunnerStatusRequest true   "状态更新信息"
// @Success 200 {object} result.Response
// @Failure 400 {object} result.Response
// @router /:id/status [put]
func (c *AdminRunnerController) UpdateRunnerStatus() {
	// 获取跑腿员ID
	idStr := c.Ctx.Input.Param(":id")
	runnerID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的跑腿员ID"))
		return
	}

	// 解析请求
	var req dto.AdminUpdateRunnerStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 获取管理员ID - 从JWT中间件设置的userID获取
	adminID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 调用服务
	err = c.runnerService.AdminUpdateRunnerStatus(c.Ctx.Request.Context(), runnerID, &req, adminID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// DeleteRunner 删除跑腿员
// @Title DeleteRunner
// @Description 管理员删除跑腿员（软删除）
// @Param   id   path    int  true   "跑腿员ID"
// @Success 200 {object} result.Response
// @Failure 400 {object} result.Response
// @router /:id [delete]
func (c *AdminRunnerController) DeleteRunner() {
	// 获取跑腿员ID
	idStr := c.Ctx.Input.Param(":id")
	runnerID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的跑腿员ID"))
		return
	}

	// 获取管理员ID - 从JWT中间件设置的userID获取
	adminID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 调用服务
	err = c.runnerService.AdminDeleteRunner(c.Ctx.Request.Context(), runnerID, adminID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// GetRunnerStatistics 获取跑腿员统计信息
// @Title GetRunnerStatistics
// @Description 管理员获取跑腿员统计信息
// @Success 200 {object} result.Response{data=dto.AdminRunnerStatisticsResponse}
// @Failure 400 {object} result.Response
// @router /statistics [get]
func (c *AdminRunnerController) GetRunnerStatistics() {
	// 调用服务
	resp, err := c.runnerService.AdminGetRunnerStatistics(c.Ctx.Request.Context())
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// UpdateRunnerRemark 更新跑腿员备注
// @Title UpdateRunnerRemark
// @Description 管理员更新跑腿员备注信息
// @Param   id   path    int                               true   "跑腿员ID"
// @Param   body body    dto.AdminUpdateRunnerRemarkRequest true   "备注信息"
// @Success 200 {object} result.Response
// @Failure 400 {object} result.Response
// @router /:id/remark [put]
func (c *AdminRunnerController) UpdateRunnerRemark() {
	// 获取跑腿员ID
	idStr := c.Ctx.Input.Param(":id")
	runnerID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的跑腿员ID"))
		return
	}

	// 解析请求
	var req dto.AdminUpdateRunnerRemarkRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 获取管理员ID - 从JWT中间件设置的userID获取
	adminID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 调用服务
	err = c.runnerService.AdminUpdateRunnerRemark(c.Ctx.Request.Context(), runnerID, &req, adminID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// GetRunnerOrders 获取跑腿员订单列表
// @Title GetRunnerOrders
// @Description 管理员获取指定跑腿员的订单列表
// @Param   id       path     int     true    "跑腿员ID"
// @Param   page     query    int     false   "页码，默认1"
// @Param   pageSize query    int     false   "每页数量，默认10"
// @Param   status   query    int     false   "订单状态筛选"
// @Success 200 {object} result.Response{data=dto.RunnerOrderListResponse}
// @Failure 400 {object} result.Response
// @router /:id/orders [get]
func (c *AdminRunnerController) GetRunnerOrders() {
	// 获取跑腿员ID
	idStr := c.Ctx.Input.Param(":id")
	runnerID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的跑腿员ID"))
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.GetString("page", "1"))
	pageSize, _ := strconv.Atoi(c.GetString("pageSize", "10"))
	status, _ := strconv.Atoi(c.GetString("status", "-1"))

	// 构建请求参数
	req := &dto.RunnerOrderListRequest{
		Page:     page,
		PageSize: pageSize,
		Status:   status,
	}

	// 调用服务
	resp, err := c.runnerService.ListRunnerOrders(c.Ctx.Request.Context(), req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}