/**
 * runner_controller.go
 * 跑腿员控制器
 *
 * 本文件实现了跑腿员相关的API接口，包括跑腿员管理、订单处理等
 */

package controllers

import (
	"strconv"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/runner/dto"
	runnerFactory "o_mall_backend/modules/runner/factory"
	"o_mall_backend/modules/runner/services"
	"o_mall_backend/modules/runner/services/impl"
	"o_mall_backend/utils/common"
)

// RunnerController 跑腿员控制器
type RunnerController struct {
	controllers.BaseController
	runnerService services.RunnerService
}

// ParseRequest 通用请求参数解析方法
func (c *RunnerController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// Prepare 初始化
func (c *RunnerController) Prepare() {
	c.BaseController.Prepare()
	c.runnerService = impl.NewRunnerService()
}

// Register 注册跑腿员
// @Title Register
// @Description 注册跑腿员
// @Param   body     body    dto.RunnerRegisterRequest  true   "跑腿员注册信息"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /register [post]
func (c *RunnerController) Register() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 解析请求
	var req dto.RunnerRegisterRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.RegisterRunner(c.Ctx.Request.Context(), &req, userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, map[string]string{"message": "申请提交成功，请等待审核"})
}

// GetRunnerInfo 获取跑腿员信息
// @Title GetRunnerInfo
// @Description 获取跑腿员信息
// @Success 200 {object} controllers.Response{data=dto.RunnerResponse}
// @Failure 400 {object} controllers.Response
// @router /info [get]
func (c *RunnerController) GetRunnerInfo() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetRunnerInfo(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetApplyStatus 获取申请状态
// @Title GetApplyStatus
// @Description 获取申请状态
// @Success 200 {object} controllers.Response{data=dto.ApplyStatusResponse}
// @Failure 400 {object} controllers.Response
// @router /apply-status [get]
func (c *RunnerController) GetApplyStatus() {
	// 检查用户认证
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetApplyStatus(c.Ctx.Request.Context(), userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetRunnerDetail 获取跑腿员详细信息
// @Title GetRunnerDetail
// @Description 获取跑腿员详细信息
// @Param   id     path    int  true   "跑腿员ID"
// @Success 200 {object} controllers.Response{data=dto.RunnerDetailResponse}
// @Failure 400 {object} controllers.Response
// @router /:id/detail [get]
func (c *RunnerController) GetRunnerDetail() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	idStr := c.Ctx.Input.Param(":id")
	runnerID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || runnerID <= 0 {
		c.ResponseError("无效的跑腿员ID")
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetRunnerDetail(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// UpdateStatus 更新跑腿员状态
// @Title UpdateStatus
// @Description 更新跑腿员审核状态
// @Param   body     body    dto.UpdateRunnerStatusRequest  true   "状态更新信息"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /audit-status [put]
func (c *RunnerController) UpdateStatus() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.UpdateRunnerStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.UpdateRunnerStatus(c.Ctx.Request.Context(), runnerID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// UpdateWorkingStatus 更新跑腿员工作状态
// @Title UpdateWorkingStatus
// @Description 更新跑腿员工作状态（0-休息中 1-接单中 2-配送中）
// @Param   body     body    dto.UpdateWorkingStatusRequest  true   "工作状态更新信息"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /status [put]
func (c *RunnerController) UpdateWorkingStatus() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.UpdateWorkingStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.UpdateWorkingStatus(c.Ctx.Request.Context(), runnerID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// UpdateOnlineStatus 更新跑腿员在线状态
// @Title UpdateOnlineStatus
// @Description 更新跑腿员在线状态
// @Param   body     body    dto.UpdateOnlineStatusRequest  true   "在线状态更新信息"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /online-status [put]
func (c *RunnerController) UpdateOnlineStatus() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.UpdateOnlineStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.UpdateOnlineStatus(c.Ctx.Request.Context(), runnerID, req.IsOnline)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// UpdateLocation 更新跑腿员位置
// @Title UpdateLocation
// @Description 更新跑腿员位置
// @Param   body     body    dto.UpdateRunnerLocationRequest  true   "位置更新信息"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /location [put]
func (c *RunnerController) UpdateLocation() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.UpdateRunnerLocationRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.UpdateRunnerLocation(c.Ctx.Request.Context(), runnerID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// GetNearbyRunners 获取附近跑腿员
// @Title GetNearbyRunners
// @Description 获取附近跑腿员
// @Param   body     body    dto.NearbyRunnerRequest  true   "查询条件"
// @Success 200 {object} controllers.Response{data=[]dto.NearbyRunnerResponse}
// @Failure 400 {object} controllers.Response
// @router /nearby [post]
func (c *RunnerController) GetNearbyRunners() {
	// 解析请求
	var req dto.NearbyRunnerRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetNearbyRunners(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetRunnerIncome 获取跑腿员收入
// @Title GetRunnerIncome
// @Description 获取跑腿员收入
// @Success 200 {object} controllers.Response{data=dto.RunnerIncomeResponse}
// @Failure 400 {object} controllers.Response
// @router /income [get]
func (c *RunnerController) GetRunnerIncome() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetRunnerIncome(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// ListIncomeLogs 获取跑腿员收入记录
// @Title ListIncomeLogs
// @Description 获取跑腿员收入记录
// @Param   page     query    int  false   "页码"
// @Param   pageSize     query    int  false   "每页数量"
// @Success 200 {object} controllers.Response{data=[]dto.RunnerIncomeLogResponse}
// @Failure 400 {object} controllers.Response
// @router /income/logs [get]
func (c *RunnerController) ListIncomeLogs() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 调用服务
	logs, total, err := c.runnerService.ListRunnerIncomeLogs(c.Ctx.Request.Context(), page, pageSize, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OKWithPagination(c.Ctx, logs, total, page, pageSize)
}

// ListWithdrawals 获取提现记录
// @Title ListWithdrawals
// @Description 获取提现记录
// @Param   page     query    int  false   "页码"
// @Param   pageSize     query    int  false   "每页数量"
// @Success 200 {object} controllers.Response{data=[]dto.WithdrawalResponse}
// @Failure 400 {object} controllers.Response
// @router /withdrawals [get]
func (c *RunnerController) ListWithdrawals() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 调用服务
	withdrawals, total, err := c.runnerService.ListWithdrawals(c.Ctx.Request.Context(), page, pageSize, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OKWithPagination(c.Ctx, withdrawals, total, page, pageSize)
}

// ApplyWithdrawal 申请提现
// @Title ApplyWithdrawal
// @Description 申请提现
// @Param   body     body    dto.WithdrawalRequest  true   "提现信息"
// @Success 200 {object} controllers.Response{data=dto.WithdrawalResponse}
// @Failure 400 {object} controllers.Response
// @router /withdrawal [post]
func (c *RunnerController) ApplyWithdrawal() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.WithdrawalRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	resp, err := c.runnerService.ApplyWithdrawal(c.Ctx.Request.Context(), &req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetStatistics 获取跑腿员统计信息
// @Title GetStatistics
// @Description 获取跑腿员统计信息
// @Success 200 {object} controllers.Response{data=dto.RunnerStatisticsResponse}
// @Failure 400 {object} controllers.Response
// @router /statistics [get]
func (c *RunnerController) GetStatistics() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetRunnerStatistics(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetBalance 获取账户余额信息
// @Title GetBalance
// @Description 获取跑腿员账户余额信息，包括可提现余额、冻结金额和提现中金额
// @Success 200 {object} controllers.Response{data=dto.RunnerBalanceResponse}
// @Failure 400 {object} controllers.Response
// @router /balance [get]
func (c *RunnerController) GetBalance() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务获取账户余额信息
	resp, err := c.runnerService.GetRunnerBalance(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetIncomeStats 获取跑腿员收入统计数据
// @Title GetIncomeStats
// @Description 获取跑腿员收入统计数据，包括今日、本周、本月和累计收入
// @Tags 跑腿员收入管理
// @Param Authorization header string true "JWT认证令牌"
// @Success 200 {object} controllers.Response{data=dto.RunnerIncomeStatsResponse} "获取成功"
// @Failure 400 {object} controllers.Response "请求错误"
// @Failure 401 {object} controllers.Response "未认证"
// @Failure 403 {object} controllers.Response "无权限"
// @Failure 500 {object} controllers.Response "服务器错误"
// @router /income-stats [get]
func (c *RunnerController) GetIncomeStats() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务获取收入统计数据
	resp, err := c.runnerService.GetIncomeStats(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetTodayStats 获取跑腿员今日统计信息
// @Title GetTodayStats
// @Description 获取跑腿员今日统计信息，包括完成订单数、工作时长、配送距离、平均评分等
// @Success 200 {object} controllers.Response{data=dto.RunnerTodayStatsResponse}
// @Failure 400 {object} controllers.Response
// @router /today-stats [get]
func (c *RunnerController) GetTodayStats() {
	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetRunnerTodayStats(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// CalculateDeliveryFee 计算配送费
// @Title CalculateDeliveryFee
// @Description 计算配送费
// @Param   body     body    dto.DeliveryFeeCalculateRequest  true   "计算条件"
// @Success 200 {object} controllers.Response{data=dto.DeliveryFeeCalculateResponse}
// @Failure 400 {object} controllers.Response
// @router /delivery-fee/calculate [post]
func (c *RunnerController) CalculateDeliveryFee() {
	// 解析请求
	var req dto.DeliveryFeeCalculateRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	resp, err := c.runnerService.CalculateDeliveryFee(c.Ctx.Request.Context(), &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetProfile 获取跑腿员个人信息
// @Title GetProfile
// @Description 获取跑腿员个人信息
// @Success 200 {object} controllers.Response{data=dto.RunnerDetailResponse}
// @Failure 400 {object} controllers.Response
// @Failure 403 {object} controllers.Response{data=string} "您不是跑腿员"
// @router /profile [get]
func (c *RunnerController) GetProfile() {
	// 检查用户认证
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok || userID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务获取跑腿员详细信息
	resp, err := c.runnerService.GetRunnerDetail(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetRunnerID 获取跑腿员ID
func (c *RunnerController) GetRunnerID() int64 {
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok || userID <= 0 {
		return 0
	}

	// 先尝试从上下文缓存中获取跑腿员ID
	runnerID, ok := c.Ctx.Input.GetData("runner_id").(int64)
	if ok && runnerID > 0 {
		return runnerID
	}

	// 如果缓存中没有，则通过repository查询数据库
	runnerRepo := runnerFactory.NewRunnerRepository()
	runner, err := runnerRepo.GetRunnerByUserID(c.Ctx.Request.Context(), userID)
	if err != nil {
		return 0
	}

	// 检查跑腿员状态是否正常（1表示正常状态）
	if runner.Status == 1 {
		// 将跑腿员ID缓存到上下文中，避免重复查询
		c.Ctx.Input.SetData("runner_id", runner.ID)
		return runner.ID
	}

	return 0
}

// GetPaymentAccounts 获取跑腿员支付账户信息
// @Title GetPaymentAccounts
// @Description 获取跑腿员支付账户信息
// @Success 200 {object} controllers.Response{data=[]dto.PaymentAccountsResponse}
// @Failure 400 {object} controllers.Response
// @Failure 403 {object} controllers.Response{data=string} "您不是跑腿员"
// @router /payment-accounts [get]
func (c *RunnerController) GetPaymentAccounts() {
	// 检查用户认证
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok || userID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务获取支付账户信息
	resp, err := c.runnerService.GetPaymentAccounts(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetNotificationSettings 获取跑腿员通知设置
// @Title GetNotificationSettings
// @Description 获取跑腿员通知设置
// @Success 200 {object} controllers.Response{data=dto.NotificationSettingsResponse}
// @Failure 400 {object} controllers.Response
// @Failure 403 {object} controllers.Response{data=string} "您不是跑腿员"
// @router /notification-settings [get]
func (c *RunnerController) GetNotificationSettings() {
	// 检查用户认证
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok || userID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务获取通知设置
	resp, err := c.runnerService.GetNotificationSettings(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetServiceSettings 获取跑腿员服务设置
// @Title GetServiceSettings
// @Description 获取跑腿员服务设置
// @Success 200 {object} controllers.Response{data=dto.ServiceSettingsResponse}
// @Failure 400 {object} controllers.Response
// @Failure 403 {object} controllers.Response{data=string} "您不是跑腿员"
// @router /service-settings [get]
func (c *RunnerController) GetServiceSettings() {
	// 检查用户认证
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok || userID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 调用服务获取服务设置
	resp, err := c.runnerService.GetServiceSettings(c.Ctx.Request.Context(), runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}
