/**
 * runner_order_controller.go
 * 跑腿订单控制器
 *
 * 本文件实现了跑腿订单相关的API接口，包括订单创建、查询、状态更新等
 */

package controllers

import (
	"strconv"
	"time"

	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/runner/dto"
	runnerFactory "o_mall_backend/modules/runner/factory"
	"o_mall_backend/modules/runner/services"
	"o_mall_backend/modules/runner/services/impl"
	"o_mall_backend/utils/common"
)

// RunnerOrderController 跑腿订单控制器
type RunnerOrderController struct {
	controllers.BaseController
	runnerService services.RunnerService
}

// ParseRequest 通用请求参数解析方法
func (c *RunnerOrderController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// Prepare 初始化
func (c *RunnerOrderController) Prepare() {
	c.BaseController.Prepare()
	c.runnerService = impl.NewRunnerService()
}

// CreateOrder 创建跑腿订单
// @Title CreateOrder
// @Description 创建跑腿订单
// @Param   body     body    dto.CreateRunnerOrderRequest  true   "订单信息"
// @Success 200 {object} controllers.Response{data=dto.RunnerOrderResponse}
// @Failure 400 {object} controllers.Response
// @router / [post]
func (c *RunnerOrderController) CreateOrder() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 解析请求
	var req dto.CreateRunnerOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	resp, err := c.runnerService.CreateRunnerOrder(c.Ctx.Request.Context(), &req, userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// GetOrderDetail 获取跑腿订单详情
// @Title GetOrderDetail
// @Description 获取跑腿订单详情
// @Param   id     path    int  true   "订单ID"
// @Success 200 {object} controllers.Response{data=dto.RunnerOrderResponse}
// @Failure 400 {object} controllers.Response
// @router /:id [get]
func (c *RunnerOrderController) GetOrderDetail() {
	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	orderID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || orderID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的订单ID"))
		return
	}

	// 调用服务
	resp, err := c.runnerService.GetRunnerOrder(c.Ctx.Request.Context(), orderID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// ListUserOrders 获取用户跑腿订单列表
// @Title ListUserOrders
// @Description 获取用户跑腿订单列表
// @Param   status     query    int  false   "订单状态"
// @Param   page     query    int  false   "页码"
// @Param   pageSize     query    int  false   "每页数量"
// @Success 200 {object} controllers.Response{data=dto.RunnerOrderListResponse}
// @Failure 400 {object} controllers.Response
// @router /user/list [get]
func (c *RunnerOrderController) ListUserOrders() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取请求参数
	status, _ := c.GetInt("status", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 构建请求
	req := &dto.RunnerOrderListRequest{
		Status:   status,
		Page:     page,
		PageSize: pageSize,
	}

	// 调用服务
	resp, err := c.runnerService.ListUserRunnerOrders(c.Ctx.Request.Context(), req, userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// ListRunnerOrders 获取跑腿员订单列表
// @Title ListRunnerOrders
// @Description 获取跑腿员订单列表
// @Param   status     query    int  false   "订单状态"
// @Param   page     query    int  false   "页码"
// @Param   pageSize     query    int  false   "每页数量"
// @Success 200 {object} controllers.Response{data=dto.RunnerOrderListResponse}
// @Failure 400 {object} controllers.Response
// @router /runner/list [get]
func (c *RunnerOrderController) ListRunnerOrders() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 获取请求参数
	status, _ := c.GetInt("status", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 构建请求
	req := &dto.RunnerOrderListRequest{
		Status:   status,
		Page:     page,
		PageSize: pageSize,
	}

	// 调用服务
	resp, err := c.runnerService.ListRunnerOrders(c.Ctx.Request.Context(), req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回数据
	result.OK(c.Ctx, resp)
}

// AcceptOrder 接单
// @Title AcceptOrder
// @Description 接单
// @Param   body     body    dto.AcceptOrderRequest  true   "接单请求"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /accept [post]
func (c *RunnerOrderController) AcceptOrder() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.AcceptOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.AcceptOrder(c.Ctx.Request.Context(), &req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// CancelOrder 取消订单
// @Title CancelOrder
// @Description 取消订单
// @Param   body     body    dto.CancelOrderRequest  true   "取消请求"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /cancel [post]
func (c *RunnerOrderController) CancelOrder() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 解析请求
	var req dto.CancelOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 判断用户类型
	runnerID := c.GetRunnerID()
	userType := 1 // 默认为普通用户

	if runnerID > 0 {
		// 跑腿员取消订单
		userType = 2

		// 调用服务
		err := c.runnerService.CancelOrder(c.Ctx.Request.Context(), &req, runnerID, userType)
		if err != nil {
			result.HandleError(c.Ctx, err)
			return
		}
	} else {
		// 普通用户取消订单

		// 调用服务
		err := c.runnerService.CancelOrder(c.Ctx.Request.Context(), &req, userID, userType)
		if err != nil {
			result.HandleError(c.Ctx, err)
			return
		}
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// PickupOrder 取货
// @Title PickupOrder
// @Description 取货
// @Param   body     body    dto.PickupOrderRequest  true   "取货请求"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /pickup [post]
func (c *RunnerOrderController) PickupOrder() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.PickupOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.PickupOrder(c.Ctx.Request.Context(), &req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// StartDelivery 开始配送
// @Title StartDelivery
// @Description 骑手开始配送
// @Param   body     body    dto.StartDeliveryRequest  true   "开始配送请求"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /start-delivery [post]
func (c *RunnerOrderController) StartDelivery() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.StartDeliveryRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.StartDelivery(c.Ctx.Request.Context(), &req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// CompleteOrder 完成订单
// @Title CompleteOrder
// @Description 完成订单
// @Param   body     body    dto.CompleteOrderRequest  true   "完成订单请求"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /complete [post]
func (c *RunnerOrderController) CompleteOrder() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.CompleteOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.CompleteOrder(c.Ctx.Request.Context(), &req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// UserRateOrder 用户评价订单
// @Title UserRateOrder
// @Description 用户评价订单
// @Param   body     body    dto.RateOrderRequest  true   "评价请求"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /user/rate [post]
func (c *RunnerOrderController) UserRateOrder() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 解析请求
	var req dto.RateOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.UserRateOrder(c.Ctx.Request.Context(), &req, userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// RunnerRateOrder 跑腿员评价订单
// @Title RunnerRateOrder
// @Description 跑腿员评价订单
// @Param   body     body    dto.RateOrderRequest  true   "评价请求"
// @Success 200 {object} controllers.Response
// @Failure 400 {object} controllers.Response
// @router /runner/rate [post]
func (c *RunnerOrderController) RunnerRateOrder() {
	// 检查用户认证
	if _, ok := c.Ctx.Input.GetData("userID").(int64); !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 获取跑腿员ID
	runnerID := c.GetRunnerID()
	if runnerID <= 0 {
		result.HandleError(c.Ctx, result.ErrForbidden.WithDetails("您不是跑腿员"))
		return
	}

	// 解析请求
	var req dto.RateOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 调用服务
	err := c.runnerService.RunnerRateOrder(c.Ctx.Request.Context(), &req, runnerID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, nil)
}

// GetStats 获取跑腿订单统计信息
// @Title GetStats
// @Description 获取跑腿订单统计信息，包括订单状态分布、完成率等
// @Success 200 {object} controllers.Response{data=dto.OrderStatsResponse}
// @Failure 400 {object} controllers.Response
// @router /stats [get]
func (c *RunnerOrderController) GetStats() {
	// 检查用户认证
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("未认证的请求"))
		return
	}

	// 创建初始统计响应
	response := &dto.OrderStatsResponse{
		Total:              0,
		Pending:            0,
		Accepted:           0,
		PickedUp:           0,
		InDelivery:         0,
		Completed:          0,
		Cancelled:          0,
		TodayOrders:        0,
		TodayCompleted:     0,
		TodayCancelled:     0,
		WeekOrders:         0,
		MonthOrders:        0,
		CompletionRate:     0.0,
		CancellationRate:   0.0,
		AverageDeliveryTime: 0,
	}

	// 获取仓储层实例
	repoFactory := runnerFactory.NewRepositoryFactory()
	orderRepo := repoFactory.RunnerOrderRepository()

	// 根据用户类型查询相关统计数据
	// 检查是否是跑腿员
	runnerID := c.GetRunnerID()
	if runnerID > 0 {
		// 跑腿员统计数据 - 使用GetRunnerStatistics方法
		stats, err := orderRepo.GetRunnerStatistics(c.Ctx.Request.Context(), runnerID)
		if err != nil {
			result.HandleError(c.Ctx, result.ErrInternalError.WithDetails(err.Error()))
			return
		}

		// 填充统计数据
		if totalOrders, ok := stats["total_orders"].(int); ok {
			response.Total = totalOrders
		}
		if pendingOrders, ok := stats["pending_orders"].(int); ok {
			response.Pending = pendingOrders
		}
		if acceptedOrders, ok := stats["accepted_orders"].(int); ok {
			response.Accepted = acceptedOrders
		}
		if pickedUpOrders, ok := stats["picked_up_orders"].(int); ok {
			response.PickedUp = pickedUpOrders
		}
		if inDeliveryOrders, ok := stats["in_delivery_orders"].(int); ok {
			response.InDelivery = inDeliveryOrders
		}
		if completedOrders, ok := stats["completed_orders"].(int); ok {
			response.Completed = completedOrders
		}
		if cancelledOrders, ok := stats["cancelled_orders"].(int); ok {
			response.Cancelled = cancelledOrders
		}
		if todayOrders, ok := stats["today_orders"].(int); ok {
			response.TodayOrders = todayOrders
		}
		if todayCompleted, ok := stats["today_completed"].(int); ok {
			response.TodayCompleted = todayCompleted
		}
		if todayCancelled, ok := stats["today_cancelled"].(int); ok {
			response.TodayCancelled = todayCancelled
		}
		if weekOrders, ok := stats["week_orders"].(int); ok {
			response.WeekOrders = weekOrders
		}
		if monthOrders, ok := stats["month_orders"].(int); ok {
			response.MonthOrders = monthOrders
		}
		if avgDeliveryTime, ok := stats["average_delivery_time"].(int); ok {
			response.AverageDeliveryTime = avgDeliveryTime
		}

		// 计算比率
		if response.Total > 0 {
			response.CompletionRate = float64(response.Completed) / float64(response.Total) * 100
			response.CancellationRate = float64(response.Cancelled) / float64(response.Total) * 100
		}

		// 获取平均评分
		scoreAvg, err := orderRepo.GetRunnerAverageScore(c.Ctx.Request.Context(), runnerID)
		if err == nil && scoreAvg > 0 {
			// 如果需要，可以将评分添加到响应中
			// response.AverageScore = scoreAvg
		}
	} else {
		// 普通用户统计数据 - 需要查询该用户相关的订单
		orders, _, err := orderRepo.ListUserRunnerOrders(c.Ctx.Request.Context(), userID, 0, 1, 1000) // 获取所有状态的订单，最多1000条
		if err != nil {
			result.HandleError(c.Ctx, result.ErrInternalError.WithDetails(err.Error()))
			return
		}

		// 手动统计各种状态的订单数量
		response.Total = len(orders)
		for _, order := range orders {
			// 根据订单状态进行统计
			switch order.Status {
			case 0: // 待接单
				response.Pending++
			case 1: // 已接单
				response.Accepted++
			case 2: // 已取货
				response.PickedUp++
			case 3: // 配送中
				response.InDelivery++
			case 4: // 已完成
				response.Completed++
			case 5: // 已取消
				response.Cancelled++
			}

			// 统计今日、本周、本月订单
			now := time.Now()
			orderTime := order.CreateTime
			if orderTime.Year() == now.Year() && orderTime.Month() == now.Month() && orderTime.Day() == now.Day() {
				response.TodayOrders++
				if order.Status == 4 { // 完成
					response.TodayCompleted++
				}
				if order.Status == 5 { // 取消
					response.TodayCancelled++
				}
			}

			// 判断是否为本周订单
			year, week := now.ISOWeek()
			orderYear, orderWeek := orderTime.ISOWeek()
			if orderYear == year && orderWeek == week {
				response.WeekOrders++
			}

			// 判断是否为本月订单
			if orderTime.Year() == now.Year() && orderTime.Month() == now.Month() {
				response.MonthOrders++
			}
		}

		// 计算比率
		if response.Total > 0 {
			response.CompletionRate = float64(response.Completed) / float64(response.Total) * 100
			response.CancellationRate = float64(response.Cancelled) / float64(response.Total) * 100
		}
	}

	// 返回数据
	result.OK(c.Ctx, response)
}

// GetRunnerID 获取跑腿员ID
func (c *RunnerOrderController) GetRunnerID() int64 {
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok || userID <= 0 {
		return 0
	}

	// 先尝试从上下文缓存中获取跑腿员ID
	runnerID, ok := c.Ctx.Input.GetData("runner_id").(int64)
	if ok && runnerID > 0 {
		return runnerID
	}

	// 如果缓存中没有，则通过repository查询数据库
	runnerRepo := runnerFactory.NewRunnerRepository()
	runner, err := runnerRepo.GetRunnerByUserID(c.Ctx.Request.Context(), userID)
	if err != nil {
		return 0
	}

	// 检查跑腿员状态是否正常（1表示正常状态）
	if runner.Status == 1 {
		// 将跑腿员ID缓存到上下文中，避免重复查询
		c.Ctx.Input.SetData("runner_id", runner.ID)
		return runner.ID
	}

	return 0
}
