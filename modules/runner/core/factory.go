/**
 * factory.go
 * 仓库工厂实现
 *
 * 本文件实现了仓库工厂接口，负责创建和管理仓库实例
 */

package core

import (
	"sync"
)

var (
	factory     RepositoryFactory
	factoryOnce sync.Once
)

// DefaultRepositoryFactory 默认仓库工厂实现
type DefaultRepositoryFactory struct {
	runnerRepo      RunnerRepository
	runnerOrderRepo RunnerOrderRepository
	incomeRepo      RunnerIncomeRepository
	miscRepo        RunnerMiscRepository
}

// InitFactory 初始化工厂
func InitFactory(f RepositoryFactory) {
	factoryOnce.Do(func() {
		factory = f
	})
}

// GetFactory 获取工厂实例
func GetFactory() RepositoryFactory {
	return factory
}

// NewDefaultFactory 创建默认工厂实例
func NewDefaultFactory(runnerRepo RunnerRepository, runnerOrderRepo RunnerOrderRepository, incomeRepo RunnerIncomeRepository, miscRepo RunnerMiscRepository) RepositoryFactory {
	return &DefaultRepositoryFactory{
		runnerRepo:      runnerRepo,
		runnerOrderRepo: runnerOrderRepo,
		incomeRepo:      incomeRepo,
		miscRepo:        miscRepo,
	}
}

// RunnerRepository 获取跑腿员仓库
func (f *DefaultRepositoryFactory) RunnerRepository() RunnerRepository {
	return f.runnerRepo
}

// RunnerOrderRepository 获取跑腿订单仓库
func (f *DefaultRepositoryFactory) RunnerOrderRepository() RunnerOrderRepository {
	return f.runnerOrderRepo
}

// RunnerIncomeRepository 获取跑腿员收入仓库
func (f *DefaultRepositoryFactory) RunnerIncomeRepository() RunnerIncomeRepository {
	return f.incomeRepo
}

// RunnerMiscRepository 获取跑腿员杂项仓库
func (f *DefaultRepositoryFactory) RunnerMiscRepository() RunnerMiscRepository {
	return f.miscRepo
}
