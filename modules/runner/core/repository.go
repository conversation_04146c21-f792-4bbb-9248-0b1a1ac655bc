/**
 * repository.go
 * 仓库接口定义
 *
 * 本文件定义了跑腿员模块的核心仓库接口
 */

package core

import (
	"context"
	"time"

	"o_mall_backend/modules/runner/models"
)

// RunnerRepository 跑腿员仓库接口
type RunnerRepository interface {
	CreateRunner(ctx context.Context, runner *models.Runner) error
	GetRunnerByID(ctx context.Context, runnerID int64) (*models.Runner, error)
	GetRunnerByUserID(ctx context.Context, userID int64) (*models.Runner, error)
	UpdateRunner(ctx context.Context, runner *models.Runner) error
	UpdateRunnerStatus(ctx context.Context, runnerID int64, status int) error
	UpdateRunnerWorkingStatus(ctx context.Context, runnerID int64, status int) error
	UpdateRunnerLocation(ctx context.Context, runnerID int64, latitude, longitude float64) error
	ListRunners(ctx context.Context, page, pageSize int, status int) ([]*models.Runner, int64, error)
	ListNearbyRunners(ctx context.Context, latitude, longitude float64, radius float64) ([]*models.Runner, error)
}

// RunnerOrderRepository 跑腿订单仓库接口
type RunnerOrderRepository interface {
	CreateRunnerOrder(ctx context.Context, order *models.RunnerOrder) (int64, error)
	GetRunnerOrderByID(ctx context.Context, orderID int64) (*models.RunnerOrder, error)
	GetRunnerOrderByOrderNo(ctx context.Context, orderNo string) (*models.RunnerOrder, error)
	UpdateRunnerOrder(ctx context.Context, order *models.RunnerOrder) error
	UpdateRunnerOrderStatus(ctx context.Context, orderID int64, status int) error
	AcceptRunnerOrder(ctx context.Context, orderID, runnerID int64) error
	CancelRunnerOrder(ctx context.Context, orderID int64, cancelReason string, cancelUserType int) error
	PickupRunnerOrder(ctx context.Context, orderID int64) error
	CompleteRunnerOrder(ctx context.Context, orderID int64) error
	ListUserRunnerOrders(ctx context.Context, userID int64, status, page, pageSize int) ([]*models.RunnerOrder, int64, error)
	ListRunnerOrders(ctx context.Context, runnerID int64, status, page, pageSize int) ([]*models.RunnerOrder, int64, error)
	GetRunnerStatistics(ctx context.Context, runnerID int64) (map[string]interface{}, error)
	GetRunnerAverageScore(ctx context.Context, runnerID int64) (float64, error)
}

// RunnerIncomeRepository 跑腿员收入仓库接口
type RunnerIncomeRepository interface {
	CreateRunnerIncomeLog(ctx context.Context, incomeLog *models.RunnerIncomeLog) (int64, error)
	GetRunnerIncome(ctx context.Context, runnerID int64) (float64, error)
	ListRunnerIncomeLogs(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerIncomeLog, int64, error)
	GetRunnerIncomeStatistics(ctx context.Context, runnerID int64, startTime, endTime time.Time) (map[string]interface{}, error)
	UpdateRunnerIncomeStatus(ctx context.Context, logID int64, status int) error
	GetRunnerTotalIncome(ctx context.Context, runnerID int64) (float64, error)
	GetRunnerWithdrawingAmount(ctx context.Context, runnerID int64) (float64, error)
	GetRunnerIncomeByTimeRange(ctx context.Context, runnerID int64, startTime, endTime time.Time) (float64, error)
	GetRunnerIncomeLogs(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerIncomeLog, int64, error)
	// 收入统计相关
	GetRunnerDailyIncome(ctx context.Context, runnerID int64) (float64, error)       // 获取今日收入
	GetRunnerWeeklyIncome(ctx context.Context, runnerID int64) (float64, error)      // 获取本周收入
	GetRunnerMonthlyIncome(ctx context.Context, runnerID int64) (float64, error)     // 获取本月收入
	CreateRunnerWithdrawal(ctx context.Context, withdrawal *models.RunnerWithdrawal) error
	DeleteRunnerWithdrawal(ctx context.Context, withdrawalID int64) error
	GetRunnerWithdrawals(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerWithdrawal, int64, error)
}

// RunnerMiscRepository 跑腿员杂项仓库接口
type RunnerMiscRepository interface {
	CreateRunnerApply(ctx context.Context, apply *models.RunnerApply) error
	UpdateRunnerApply(ctx context.Context, apply *models.RunnerApply) error
	GetRunnerApplyByID(ctx context.Context, applyID int64) (*models.RunnerApply, error)
	GetRunnerApplyByUserID(ctx context.Context, userID int64) (*models.RunnerApply, error)
	CreateRunnerLocation(ctx context.Context, location *models.RunnerLocation) error
	UpdateRunnerWallet(ctx context.Context, runnerID int64, amount float64, isAdd bool) error
}

// RepositoryFactory 仓库工厂接口
type RepositoryFactory interface {
	RunnerRepository() RunnerRepository
	RunnerOrderRepository() RunnerOrderRepository
	RunnerIncomeRepository() RunnerIncomeRepository
	RunnerMiscRepository() RunnerMiscRepository
}
