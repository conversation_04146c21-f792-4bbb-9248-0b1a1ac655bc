# 跑腿模块 (modules/runner) 文档索引

## 模块核心文档

1. [**跑腿订单生命周期与流程**](./runner_order_lifecycle.md) - 详细描述跑腿订单的完整生命周期、状态定义与业务流程
   - 订单状态常量定义
   - 生命周期流程详解
   - 核心操作流程（接单、取货、配送、完成、取消等）
   - 订单数据模型
   - 配送费计算规则
   - 收入与提现流程

2. [**跑腿订单状态流转图**](./runner_order_flow.md) - 可视化展示订单各状态的流转关系
   - 订单状态流转图（Mermaid图表）
   - 跑腿员工作状态流转
   - 收入状态流转
   - 提现状态流转

## 其他参考文档

3. [数据库设计](./database_design.md) - 跑腿模块相关表结构设计

4. [外卖模块集成指南](./runner_takeout_integration.md) - 跑腿模块与外卖模块的集成方案

5. [跑腿模块分析](./runner_module_analysis.md) - 跑腿模块整体架构分析

6. [跑腿员管理API](./admin_runner_management_api.md) - 管理后台跑腿员相关API

7. [跑腿API示例](./runner_api_examples.md) - 跑腿相关API调用示例

8. [前端开发指南](./frontend_development_guide.md) - 跑腿相关前端开发指南

## 目录结构

跑腿模块的主要目录结构：

```
modules/runner/
├── constants/         - 常量定义
├── controllers/       - 控制器层，处理HTTP请求
├── core/              - 核心功能模块
├── dto/               - 数据传输对象
├── factory/           - 工厂类，用于创建对象实例
├── models/            - 数据模型
├── repositories/      - 数据访问层
│   └── impl/          - 数据访问层实现
├── routers/           - 路由定义
└── services/          - 业务服务层
    └── impl/          - 业务服务实现
```

## 核心流程概览

1. **订单状态流转**：
   - 待支付(10) → 已支付(20) → 已接单待取货(30) → 已取货配送中(40) → 已完成(50)
   - 在各阶段都可能转为已取消(60)状态

2. **跑腿员工作状态**：
   - 休息中(0) → 接单中(1) → 配送中(2) → 接单中(1)

3. **收入流程**：
   - 待入账(0) → 已入账(1)/入账失败(2)

4. **提现流程**：
   - 待处理(0) → 处理中(1) → 成功(2)/失败(3)
