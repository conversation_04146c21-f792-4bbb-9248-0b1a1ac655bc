# 管理员跑腿员管理API文档

## 概述

本文档描述了管理员对跑腿员进行管理的API接口，包括查看、审核、暂停、删除、黑名单等操作。

## 基础信息

- **基础URL**: `/api/v1/admin/secured`
- **认证方式**: JWT Token (管理员权限)
- **Content-Type**: `application/json`

## API接口列表

### 1. 获取跑腿员列表

**接口地址**: `GET /runners`

**功能描述**: 获取跑腿员列表，支持分页、状态筛选和关键词搜索

**重要说明**: 
- 当status=0（待审核）时，返回的是跑腿员申请记录，用于管理员审核
- 当status为其他值时，返回的是已审核通过的跑腿员记录
- 这样设计是为了区分申请阶段和正式跑腿员阶段的数据

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认20 |
| status | int | 否 | 状态筛选：-1全部（包含待审核申请和已审核跑腿员），0待审核，1审核通过，2审核拒绝，3暂停服务，4黑名单，默认0（待审核） |
| keyword | string | 否 | 关键词搜索（姓名、手机号） |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "user_id": 123,
        "real_name": "张三",
        "mobile": "13800138000",
        "status": 1,
        "status_desc": "审核通过",
        "current_location": "北京市朝阳区",
        "is_online": true,
        "working_status": 1,
        "working_status_desc": "空闲中",
        "score": 4.8,
        "order_count": 150,
        "success_count": 145,
        "cancel_count": 5,
        "balance": 1250.50,
        "service_radius": 5.0,
        "join_time": "2024-01-15T10:30:00Z",
        "last_online_time": "2024-01-20T15:45:00Z",
        "remark": "表现良好"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```

### 2. 获取跑腿员详细信息

**接口地址**: `GET /runners/{id}`

**功能描述**: 获取指定跑腿员的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 跑腿员ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "user_id": 123,
    "real_name": "张三",
    "id_card_number": "110101199001011234",
    "id_card_front_pic": "https://example.com/id_front.jpg",
    "id_card_back_pic": "https://example.com/id_back.jpg",
    "face_pic": "https://example.com/face.jpg",
    "mobile": "13800138000",
    "status": 1,
    "status_desc": "审核通过",
    "current_location": "北京市朝阳区",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "is_online": true,
    "working_status": 1,
    "working_status_desc": "空闲中",
    "score": 4.8,
    "order_count": 150,
    "success_count": 145,
    "cancel_count": 5,
    "balance": 1250.50,
    "wallet": 1250.50,
    "area_codes": "110105,110106",
    "service_radius": 5.0,
    "remark": "表现良好",
    "frontend_remark": "优秀跑腿员",
    "join_time": "2024-01-15T10:30:00Z",
    "last_login_time": "2024-01-20T14:30:00Z",
    "last_online_time": "2024-01-20T15:45:00Z",
    "create_time": "2024-01-15T10:30:00Z",
    "update_time": "2024-01-20T15:45:00Z"
  }
}
```

### 3. 审核跑腿员

**接口地址**: `PUT /runners/{id}/audit`

**功能描述**: 审核跑腿员申请

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 跑腿员ID |

**请求参数**:
```json
{
  "status": 1,
  "reject_reason": "身份证信息不清晰",
  "remark": "需要重新上传身份证照片"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | int | 是 | 审核状态：1审核通过，2审核拒绝 |
| reject_reason | string | 否 | 拒绝原因（审核拒绝时必填） |
| remark | string | 否 | 管理员备注 |

**响应示例**:
```json
{
  "code": 200,
  "message": "审核成功",
  "data": null
}
```

### 4. 更新跑腿员状态

**接口地址**: `PUT /runners/{id}/status`

**功能描述**: 更新跑腿员状态（暂停、恢复、拉黑等）

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 跑腿员ID |

**请求参数**:
```json
{
  "status": 3,
  "reason": "违规操作",
  "remark": "暂停服务30天"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | int | 是 | 状态：1审核通过，3暂停服务，4黑名单 |
| reason | string | 否 | 操作原因 |
| remark | string | 否 | 管理员备注 |

**响应示例**:
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

### 5. 删除跑腿员

**接口地址**: `DELETE /runners/{id}`

**功能描述**: 软删除跑腿员

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 跑腿员ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 6. 获取跑腿员统计信息

**接口地址**: `GET /runners/statistics`

**功能描述**: 获取跑腿员统计信息

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_runners": 1000,
    "pending_audit": 50,
    "approved_runners": 800,
    "rejected_runners": 100,
    "suspended_runners": 30,
    "blacklist_runners": 20,
    "online_runners": 200,
    "working_runners": 150,
    "today_new_runners": 10,
    "this_week_new_runners": 50,
    "this_month_new_runners": 200
  }
}
```

### 7. 更新跑腿员备注

**接口地址**: `PUT /runners/{id}/remark`

**功能描述**: 更新跑腿员备注信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 跑腿员ID |

**请求参数**:
```json
{
  "remark": "表现优秀，可重点关注",
  "frontend_remark": "五星跑腿员"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| remark | string | 否 | 管理员备注 |
| frontend_remark | string | 否 | 前端显示备注 |

**响应示例**:
```json
{
  "code": 200,
  "message": "备注更新成功",
  "data": null
}
```

### 8. 获取跑腿员订单列表

**接口地址**: `GET /runners/{id}/orders`

**功能描述**: 获取指定跑腿员的订单列表

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 跑腿员ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认20 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1001,
        "order_type": 1,
        "user_id": 456,
        "runner_id": 1,
        "status": 6,
        "status_desc": "已完成",
        "payment_status": 2,
        "payment_status_desc": "已支付",
        "total_amount": 25.50,
        "delivery_fee": 8.00,
        "tip_amount": 2.00,
        "pickup_address": "北京市朝阳区xxx路xxx号",
        "delivery_address": "北京市朝阳区yyy路yyy号",
        "goods_description": "奶茶2杯",
        "create_time": "2024-01-20T10:00:00Z",
        "complete_time": "2024-01-20T10:45:00Z"
      }
    ],
    "total": 150,
    "page": 1,
    "pageSize": 20,
    "totalPages": 8
  }
}
```

## 状态码说明

### 跑腿员状态 (status)

| 状态码 | 状态描述 | 说明 |
|--------|----------|------|
| 0 | 待审核 | 刚注册，等待管理员审核 |
| 1 | 审核通过 | 可以正常接单 |
| 2 | 审核拒绝 | 注册信息不符合要求 |
| 3 | 暂停服务 | 临时暂停，可恢复 |
| 4 | 黑名单 | 永久禁用 |

### 工作状态 (working_status)

| 状态码 | 状态描述 | 说明 |
|--------|----------|------|
| 0 | 离线 | 未在线 |
| 1 | 空闲中 | 在线但未接单 |
| 2 | 工作中 | 正在执行订单 |
| 3 | 休息中 | 在线但暂停接单 |

### 订单状态 (order_status)

| 状态码 | 状态描述 | 说明 |
|--------|----------|------|
| 1 | 待接单 | 等待跑腿员接单 |
| 2 | 已接单 | 跑腿员已接单 |
| 3 | 取货中 | 跑腿员正在取货 |
| 4 | 配送中 | 跑腿员正在配送 |
| 5 | 已取消 | 订单已取消 |
| 6 | 已完成 | 订单已完成 |

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 参数错误 | 请求参数格式错误或缺少必填参数 |
| 401 | 未授权 | 未登录或token无效 |
| 403 | 权限不足 | 非管理员用户 |
| 404 | 资源不存在 | 跑腿员不存在 |
| 500 | 服务器内部错误 | 系统异常 |

## 注意事项

1. 所有接口都需要管理员权限，请确保在请求头中包含有效的JWT Token
2. 时间格式统一使用ISO 8601格式（RFC3339）
3. 金额字段使用浮点数，单位为元，保留2位小数
4. 分页参数page从1开始
5. 审核拒绝时必须填写拒绝原因
6. 删除操作为软删除，不会真正删除数据
7. 状态更新操作会记录操作日志