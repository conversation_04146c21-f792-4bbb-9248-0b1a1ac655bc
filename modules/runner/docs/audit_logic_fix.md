# 跑腿员审核逻辑修复文档

## 问题描述

在原有的 `AdminAuditRunner` 方法中存在以下严重缺陷：

1. **状态更新不完整**：审核通过时只创建新的跑腿员记录，没有检查用户是否已经是跑腿员
2. **重复创建风险**：可能导致同一用户创建多个跑腿员记录
3. **状态不一致**：已存在的跑腿员状态没有被正确更新为审核通过（Status=1）
4. **审核拒绝处理缺失**：审核拒绝时没有更新现有跑腿员的状态
5. **参数验证不足**：缺少对审核状态和拒绝原因的验证

## 修复方案

### 1. 增强参数验证

```go
// 参数验证
if req.Status != 1 && req.Status != 2 {
    return errors.New("无效的审核状态，只能是1（通过）或2（拒绝）")
}
if req.Status == 2 && req.RejectReason == "" {
    return errors.New("审核拒绝时必须提供拒绝原因")
}
```

### 2. 完善审核通过逻辑

- **检查现有跑腿员**：使用 `GetRunnerByUserID` 检查用户是否已经是跑腿员
- **更新现有记录**：如果跑腿员已存在，更新其状态为审核通过（Status=1）
- **创建新记录**：如果跑腿员不存在，创建新的跑腿员记录
- **信息同步**：更新跑腿员的个人信息、证件信息等

### 3. 添加审核拒绝处理

- **状态更新**：审核拒绝时，如果跑腿员已存在，将其状态设置为审核拒绝（Status=2）
- **日志记录**：记录审核拒绝的操作日志

### 4. 改进日志记录

- 添加更详细的操作日志
- 区分创建新记录和更新现有记录的情况
- 记录管理员ID以便审计

## 修复后的流程

### 审核通过流程（Status=1）

1. 验证参数有效性
2. 获取并验证申请记录
3. 更新申请记录状态
4. 检查用户是否已是跑腿员
   - **如果已存在**：更新现有跑腿员状态为审核通过，同步最新信息
   - **如果不存在**：创建新的跑腿员记录
5. 记录操作日志

### 审核拒绝流程（Status=2）

1. 验证参数有效性（必须提供拒绝原因）
2. 获取并验证申请记录
3. 更新申请记录状态和拒绝原因
4. 检查用户是否已是跑腿员
   - **如果已存在**：更新现有跑腿员状态为审核拒绝
   - **如果不存在**：仅记录日志
5. 记录操作日志

## 影响范围

### 修改的文件

- `modules/runner/services/impl/runner_service_impl.go`
  - `AdminAuditRunner` 方法完全重构

### 相关接口

- `PUT /api/v1/admin/secured/runners/:id/audit`

### 数据库表

- `runner_apply`：申请记录表
- `runner`：跑腿员信息表

## 测试建议

### 测试场景

1. **新用户审核通过**：验证能正确创建跑腿员记录
2. **现有跑腿员重新审核通过**：验证能正确更新状态为1
3. **审核拒绝**：验证拒绝原因必填，状态正确更新
4. **重复审核**：验证不能重复审核同一申请
5. **参数验证**：验证无效状态值和缺失拒绝原因的处理

### 验证要点

1. 跑腿员状态正确更新为1（审核通过）
2. 跑腿员能正常使用相关功能（不再出现403错误）
3. 不会创建重复的跑腿员记录
4. 审核拒绝时状态正确设置为2
5. 日志记录完整准确

## 注意事项

1. **向后兼容**：修改保持了原有接口的兼容性
2. **事务处理**：建议后续添加数据库事务确保数据一致性
3. **性能考虑**：增加了一次数据库查询，但对性能影响微乎其微
4. **错误处理**：增强了错误信息的准确性和可读性

## 后续优化建议

1. **添加事务支持**：确保申请记录和跑腿员记录的更新原子性
2. **添加审核历史记录**：记录每次审核操作的详细信息
3. **通知机制**：审核结果通知用户
4. **批量审核**：支持批量审核功能