# 跑腿员管理数据库设计文档

## 概述

本文档描述了跑腿员管理系统的数据库表结构设计，包括跑腿员信息、订单管理、收入记录等相关表的详细设计。

## 数据库表结构

### 1. 跑腿员表 (runners)

**表名**: `runners`

**功能**: 存储跑腿员的基本信息和状态

**字段说明**:

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 跑腿员ID，主键 |
| user_id | BIGINT | - | NOT NULL | - | 关联的用户ID，外键 |
| real_name | VARCHAR | 50 | NOT NULL | - | 真实姓名 |
| id_card_number | VARCHAR | 18 | NOT NULL | - | 身份证号码 |
| id_card_front_pic | VARCHAR | 255 | NULL | - | 身份证正面照片URL |
| id_card_back_pic | VARCHAR | 255 | NULL | - | 身份证背面照片URL |
| face_pic | VARCHAR | 255 | NULL | - | 人脸照片URL |
| mobile | VARCHAR | 11 | NOT NULL | - | 手机号码 |
| status | TINYINT | - | NOT NULL | 0 | 状态：0待审核 1审核通过 2审核拒绝 3暂停服务 4黑名单 |
| current_location | VARCHAR | 255 | NULL | - | 当前位置描述 |
| latitude | DECIMAL | 10,7 | NULL | - | 纬度 |
| longitude | DECIMAL | 10,7 | NULL | - | 经度 |
| is_online | TINYINT | - | NOT NULL | 0 | 是否在线：0离线 1在线 |
| working_status | TINYINT | - | NOT NULL | 0 | 工作状态：0离线 1空闲中 2工作中 3休息中 |
| score | DECIMAL | 3,2 | NOT NULL | 5.00 | 评分(1-5分) |
| order_count | INT | - | NOT NULL | 0 | 总订单数 |
| success_count | INT | - | NOT NULL | 0 | 成功订单数 |
| cancel_count | INT | - | NOT NULL | 0 | 取消订单数 |
| balance | DECIMAL | 10,2 | NOT NULL | 0.00 | 账户余额 |
| wallet | DECIMAL | 10,2 | NOT NULL | 0.00 | 钱包余额 |
| area_codes | VARCHAR | 255 | NULL | - | 服务区域编码，逗号分隔 |
| service_radius | DECIMAL | 5,2 | NOT NULL | 5.00 | 服务半径(km) |
| remark | TEXT | - | NULL | - | 管理员备注 |
| frontend_remark | VARCHAR | 255 | NULL | - | 前端显示备注 |
| join_time | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 加入时间 |
| last_login_time | TIMESTAMP | - | NULL | - | 最后登录时间 |
| last_online_time | TIMESTAMP | - | NULL | - | 最后在线时间 |
| is_deleted | TINYINT | - | NOT NULL | 0 | 是否删除：0未删除 1已删除 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引
UNIQUE KEY uk_user_id (user_id)
UNIQUE KEY uk_id_card (id_card_number)
UNIQUE KEY uk_mobile (mobile)

-- 普通索引
KEY idx_status (status)
KEY idx_is_online (is_online)
KEY idx_working_status (working_status)
KEY idx_location (latitude, longitude)
KEY idx_join_time (join_time)
KEY idx_is_deleted (is_deleted)
```

### 2. 跑腿员申请表 (runner_applies)

**表名**: `runner_applies`

**功能**: 存储跑腿员申请记录

**字段说明**:

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 申请ID，主键 |
| user_id | BIGINT | - | NOT NULL | - | 申请用户ID |
| real_name | VARCHAR | 50 | NOT NULL | - | 真实姓名 |
| id_card_number | VARCHAR | 18 | NOT NULL | - | 身份证号码 |
| id_card_front_pic | VARCHAR | 255 | NULL | - | 身份证正面照 |
| id_card_back_pic | VARCHAR | 255 | NULL | - | 身份证背面照 |
| face_pic | VARCHAR | 255 | NULL | - | 人脸照片 |
| mobile | VARCHAR | 11 | NOT NULL | - | 手机号码 |
| status | TINYINT | - | NOT NULL | 0 | 申请状态：0待审核 1审核通过 2审核拒绝 |
| reject_reason | VARCHAR | 255 | NULL | - | 拒绝原因 |
| audit_admin_id | BIGINT | - | NULL | - | 审核管理员ID |
| audit_time | TIMESTAMP | - | NULL | - | 审核时间 |
| remark | TEXT | - | NULL | - | 备注 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
```sql
PRIMARY KEY (id)
KEY idx_user_id (user_id)
KEY idx_status (status)
KEY idx_audit_admin (audit_admin_id)
KEY idx_created_at (created_at)
```

### 3. 跑腿订单表 (runner_orders)

**表名**: `runner_orders`

**功能**: 存储跑腿订单信息

**字段说明**:

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 订单ID，主键 |
| order_no | VARCHAR | 32 | NOT NULL | - | 订单号 |
| order_type | TINYINT | - | NOT NULL | 1 | 订单类型：1代购 2代送 3其他 |
| user_id | BIGINT | - | NOT NULL | - | 下单用户ID |
| runner_id | BIGINT | - | NULL | - | 接单跑腿员ID |
| status | TINYINT | - | NOT NULL | 10 | 订单状态：10待支付 20已支付 30已接单 40已取货 50已完成 60已取消 |
| payment_status | TINYINT | - | NOT NULL | 1 | 支付状态：1待支付 2已支付 3已退款 |
| total_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 订单总金额 |
| goods_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 商品金额 |
| delivery_fee | DECIMAL | 10,2 | NOT NULL | 0.00 | 配送费 |
| tip_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 小费金额 |
| pickup_address | VARCHAR | 255 | NOT NULL | - | 取货地址 |
| pickup_latitude | DECIMAL | 10,7 | NULL | - | 取货地址纬度 |
| pickup_longitude | DECIMAL | 10,7 | NULL | - | 取货地址经度 |
| pickup_contact | VARCHAR | 50 | NULL | - | 取货联系人 |
| pickup_mobile | VARCHAR | 11 | NULL | - | 取货联系电话 |
| delivery_address | VARCHAR | 255 | NOT NULL | - | 配送地址 |
| delivery_latitude | DECIMAL | 10,7 | NULL | - | 配送地址纬度 |
| delivery_longitude | DECIMAL | 10,7 | NULL | - | 配送地址经度 |
| delivery_contact | VARCHAR | 50 | NULL | - | 配送联系人 |
| delivery_mobile | VARCHAR | 11 | NULL | - | 配送联系电话 |
| goods_description | TEXT | - | NULL | - | 商品描述 |
| special_requirements | TEXT | - | NULL | - | 特殊要求 |
| distance | DECIMAL | 8,2 | NULL | - | 配送距离(km) |
| estimate_time | INT | - | NULL | - | 预计配送时间(分钟) |
| accept_time | TIMESTAMP | - | NULL | - | 接单时间 |
| pickup_time | TIMESTAMP | - | NULL | - | 取货时间 |
| delivery_time | TIMESTAMP | - | NULL | - | 配送时间 |
| complete_time | TIMESTAMP | - | NULL | - | 完成时间 |
| cancel_time | TIMESTAMP | - | NULL | - | 取消时间 |
| cancel_reason | VARCHAR | 255 | NULL | - | 取消原因 |
| cancel_by | TINYINT | - | NULL | - | 取消方：1用户 2跑腿员 3系统 |
| user_rating | TINYINT | - | NULL | - | 用户评分(1-5) |
| user_comment | TEXT | - | NULL | - | 用户评价 |
| runner_rating | TINYINT | - | NULL | - | 跑腿员评分(1-5) |
| runner_comment | TEXT | - | NULL | - | 跑腿员评价 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
```sql
PRIMARY KEY (id)
UNIQUE KEY uk_order_no (order_no)
KEY idx_user_id (user_id)
KEY idx_runner_id (runner_id)
KEY idx_status (status)
KEY idx_payment_status (payment_status)
KEY idx_order_type (order_type)
KEY idx_created_at (created_at)
KEY idx_pickup_location (pickup_latitude, pickup_longitude)
KEY idx_delivery_location (delivery_latitude, delivery_longitude)
```

### 4. 跑腿员位置记录表 (runner_locations)

**表名**: `runner_locations`

**功能**: 记录跑腿员位置变化历史

**字段说明**:

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 记录ID，主键 |
| runner_id | BIGINT | - | NOT NULL | - | 跑腿员ID |
| latitude | DECIMAL | 10,7 | NOT NULL | - | 纬度 |
| longitude | DECIMAL | 10,7 | NOT NULL | - | 经度 |
| address | VARCHAR | 255 | NULL | - | 地址描述 |
| accuracy | DECIMAL | 8,2 | NULL | - | 定位精度(米) |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 记录时间 |

**索引设计**:
```sql
PRIMARY KEY (id)
KEY idx_runner_id (runner_id)
KEY idx_location (latitude, longitude)
KEY idx_created_at (created_at)
```

### 5. 跑腿员收入记录表 (runner_income_logs)

**表名**: `runner_income_logs`

**功能**: 记录跑腿员收入明细

**字段说明**:

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 记录ID，主键 |
| runner_id | BIGINT | - | NOT NULL | - | 跑腿员ID |
| order_id | BIGINT | - | NULL | - | 关联订单ID |
| type | TINYINT | - | NOT NULL | 1 | 收入类型：1订单收入 2小费收入 3奖励收入 4其他收入 |
| amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 收入金额 |
| description | VARCHAR | 255 | NULL | - | 收入描述 |
| balance_before | DECIMAL | 10,2 | NOT NULL | 0.00 | 操作前余额 |
| balance_after | DECIMAL | 10,2 | NOT NULL | 0.00 | 操作后余额 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计**:
```sql
PRIMARY KEY (id)
KEY idx_runner_id (runner_id)
KEY idx_order_id (order_id)
KEY idx_type (type)
KEY idx_created_at (created_at)
```

### 6. 跑腿员提现记录表 (runner_withdrawals)

**表名**: `runner_withdrawals`

**功能**: 记录跑腿员提现申请和处理记录

**字段说明**:

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 提现ID，主键 |
| runner_id | BIGINT | - | NOT NULL | - | 跑腿员ID |
| amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 提现金额 |
| fee | DECIMAL | 10,2 | NOT NULL | 0.00 | 手续费 |
| actual_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 实际到账金额 |
| bank_name | VARCHAR | 50 | NULL | - | 银行名称 |
| bank_account | VARCHAR | 30 | NULL | - | 银行账号 |
| account_name | VARCHAR | 50 | NULL | - | 账户姓名 |
| status | TINYINT | - | NOT NULL | 1 | 状态：1申请中 2处理中 3已完成 4已拒绝 |
| reject_reason | VARCHAR | 255 | NULL | - | 拒绝原因 |
| process_admin_id | BIGINT | - | NULL | - | 处理管理员ID |
| process_time | TIMESTAMP | - | NULL | - | 处理时间 |
| remark | TEXT | - | NULL | - | 备注 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 申请时间 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
```sql
PRIMARY KEY (id)
KEY idx_runner_id (runner_id)
KEY idx_status (status)
KEY idx_process_admin (process_admin_id)
KEY idx_created_at (created_at)
```

### 7. 跑腿员操作日志表 (runner_operation_logs)

**表名**: `runner_operation_logs`

**功能**: 记录管理员对跑腿员的操作日志

**字段说明**:

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 日志ID，主键 |
| runner_id | BIGINT | - | NOT NULL | - | 跑腿员ID |
| admin_id | BIGINT | - | NOT NULL | - | 操作管理员ID |
| operation_type | VARCHAR | 50 | NOT NULL | - | 操作类型：audit,status_change,remark_update,delete等 |
| operation_desc | VARCHAR | 255 | NOT NULL | - | 操作描述 |
| before_data | JSON | - | NULL | - | 操作前数据 |
| after_data | JSON | - | NULL | - | 操作后数据 |
| ip_address | VARCHAR | 45 | NULL | - | 操作IP地址 |
| user_agent | VARCHAR | 255 | NULL | - | 用户代理 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 操作时间 |

**索引设计**:
```sql
PRIMARY KEY (id)
KEY idx_runner_id (runner_id)
KEY idx_admin_id (admin_id)
KEY idx_operation_type (operation_type)
KEY idx_created_at (created_at)
```

## 数据库创建脚本

### 1. 跑腿员表

```sql
CREATE TABLE `runners` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '跑腿员ID',
  `user_id` bigint NOT NULL COMMENT '关联的用户ID',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card_number` varchar(18) NOT NULL COMMENT '身份证号码',
  `id_card_front_pic` varchar(255) DEFAULT NULL COMMENT '身份证正面照片URL',
  `id_card_back_pic` varchar(255) DEFAULT NULL COMMENT '身份证背面照片URL',
  `face_pic` varchar(255) DEFAULT NULL COMMENT '人脸照片URL',
  `mobile` varchar(11) NOT NULL COMMENT '手机号码',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0待审核 1审核通过 2审核拒绝 3暂停服务 4黑名单',
  `current_location` varchar(255) DEFAULT NULL COMMENT '当前位置描述',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `is_online` tinyint NOT NULL DEFAULT '0' COMMENT '是否在线：0离线 1在线',
  `working_status` tinyint NOT NULL DEFAULT '0' COMMENT '工作状态：0离线 1空闲中 2工作中 3休息中',
  `score` decimal(3,2) NOT NULL DEFAULT '5.00' COMMENT '评分(1-5分)',
  `order_count` int NOT NULL DEFAULT '0' COMMENT '总订单数',
  `success_count` int NOT NULL DEFAULT '0' COMMENT '成功订单数',
  `cancel_count` int NOT NULL DEFAULT '0' COMMENT '取消订单数',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `wallet` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '钱包余额',
  `area_codes` varchar(255) DEFAULT NULL COMMENT '服务区域编码，逗号分隔',
  `service_radius` decimal(5,2) NOT NULL DEFAULT '5.00' COMMENT '服务半径(km)',
  `remark` text COMMENT '管理员备注',
  `frontend_remark` varchar(255) DEFAULT NULL COMMENT '前端显示备注',
  `join_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_online_time` timestamp NULL DEFAULT NULL COMMENT '最后在线时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0未删除 1已删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  UNIQUE KEY `uk_id_card` (`id_card_number`),
  UNIQUE KEY `uk_mobile` (`mobile`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`),
  KEY `idx_working_status` (`working_status`),
  KEY `idx_location` (`latitude`,`longitude`),
  KEY `idx_join_time` (`join_time`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跑腿员表';
```

### 2. 跑腿员申请表

```sql
CREATE TABLE `runner_applies` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` bigint NOT NULL COMMENT '申请用户ID',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card_number` varchar(18) NOT NULL COMMENT '身份证号码',
  `id_card_front_pic` varchar(255) DEFAULT NULL COMMENT '身份证正面照',
  `id_card_back_pic` varchar(255) DEFAULT NULL COMMENT '身份证背面照',
  `face_pic` varchar(255) DEFAULT NULL COMMENT '人脸照片',
  `mobile` varchar(11) NOT NULL COMMENT '手机号码',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '申请状态：0待审核 1审核通过 2审核拒绝',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `audit_admin_id` bigint DEFAULT NULL COMMENT '审核管理员ID',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `remark` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_audit_admin` (`audit_admin_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跑腿员申请表';
```

### 3. 跑腿员操作日志表

```sql
CREATE TABLE `runner_operation_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `runner_id` bigint NOT NULL COMMENT '跑腿员ID',
  `admin_id` bigint NOT NULL COMMENT '操作管理员ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_desc` varchar(255) NOT NULL COMMENT '操作描述',
  `before_data` json DEFAULT NULL COMMENT '操作前数据',
  `after_data` json DEFAULT NULL COMMENT '操作后数据',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '操作IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_runner_id` (`runner_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跑腿员操作日志表';
```

## 数据字典

### 状态枚举值

#### 跑腿员状态 (runners.status)

| 值 | 描述 | 说明 |
|----|------|------|
| 0 | 待审核 | 刚注册，等待管理员审核 |
| 1 | 审核通过 | 可以正常接单 |
| 2 | 审核拒绝 | 注册信息不符合要求 |
| 3 | 暂停服务 | 临时暂停，可恢复 |
| 4 | 黑名单 | 永久禁用 |

#### 工作状态 (runners.working_status)

| 值 | 描述 | 说明 |
|----|------|------|
| 0 | 离线 | 未在线 |
| 1 | 空闲中 | 在线但未接单 |
| 2 | 工作中 | 正在执行订单 |
| 3 | 休息中 | 在线但暂停接单 |

#### 订单状态 (runner_orders.status)

| 值 | 描述 | 说明 |
|----|------|------|
| 1 | 待接单 | 等待跑腿员接单 |
| 2 | 已接单 | 跑腿员已接单 |
| 3 | 取货中 | 跑腿员正在取货 |
| 4 | 配送中 | 跑腿员正在配送 |
| 5 | 已取消 | 订单已取消 |
| 6 | 已完成 | 订单已完成 |

#### 支付状态 (runner_orders.payment_status)

| 值 | 描述 | 说明 |
|----|------|------|
| 1 | 待支付 | 等待用户支付 |
| 2 | 已支付 | 用户已支付 |
| 3 | 已退款 | 已退款给用户 |

#### 收入类型 (runner_income_logs.type)

| 值 | 描述 | 说明 |
|----|------|------|
| 1 | 订单收入 | 完成订单获得的收入 |
| 2 | 小费收入 | 用户给的小费 |
| 3 | 奖励收入 | 平台奖励 |
| 4 | 其他收入 | 其他类型收入 |

#### 提现状态 (runner_withdrawals.status)

| 值 | 描述 | 说明 |
|----|------|------|
| 1 | 申请中 | 刚提交申请 |
| 2 | 处理中 | 管理员正在处理 |
| 3 | 已完成 | 提现成功 |
| 4 | 已拒绝 | 提现被拒绝 |

## 性能优化建议

### 1. 索引优化

- **位置查询优化**: 对经纬度字段建立复合索引，支持附近跑腿员查询
- **状态查询优化**: 对状态字段建立索引，支持快速筛选
- **时间范围查询**: 对时间字段建立索引，支持时间范围查询

### 2. 分表策略

对于数据量较大的表，建议采用分表策略：

```sql
-- 按月分表示例
CREATE TABLE `runner_orders_202401` LIKE `runner_orders`;
CREATE TABLE `runner_orders_202402` LIKE `runner_orders`;
-- ...

-- 按跑腿员ID分表示例
CREATE TABLE `runner_income_logs_0` LIKE `runner_income_logs`;
CREATE TABLE `runner_income_logs_1` LIKE `runner_income_logs`;
-- ...
```

### 3. 读写分离

- **主库**: 处理写操作（增删改）
- **从库**: 处理读操作（查询）
- **缓存**: 热点数据缓存到Redis

### 4. 数据归档

定期归档历史数据：

```sql
-- 归档6个月前的订单数据
INSERT INTO runner_orders_archive 
SELECT * FROM runner_orders 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);

DELETE FROM runner_orders 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

## 数据安全

### 1. 敏感数据加密

```sql
-- 身份证号码加密存储
UPDATE runners SET id_card_number = AES_ENCRYPT(id_card_number, 'encryption_key');

-- 手机号码脱敏显示
SELECT 
  id,
  real_name,
  CONCAT(LEFT(mobile, 3), '****', RIGHT(mobile, 4)) AS mobile_masked
FROM runners;
```

### 2. 数据备份

```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d)
mysqldump -u root -p database_name > backup_${DATE}.sql

# 压缩备份文件
gzip backup_${DATE}.sql

# 上传到云存储
aws s3 cp backup_${DATE}.sql.gz s3://backup-bucket/
```

### 3. 访问控制

```sql
-- 创建只读用户
CREATE USER 'readonly'@'%' IDENTIFIED BY 'password';
GRANT SELECT ON database_name.* TO 'readonly'@'%';

-- 创建应用用户
CREATE USER 'app_user'@'%' IDENTIFIED BY 'password';
GRANT SELECT, INSERT, UPDATE ON database_name.* TO 'app_user'@'%';
```

## 监控指标

### 1. 业务指标

- 跑腿员注册数量
- 审核通过率
- 在线跑腿员数量
- 订单完成率
- 平均响应时间

### 2. 技术指标

- 数据库连接数
- 查询响应时间
- 慢查询数量
- 磁盘使用率
- 内存使用率

### 3. 监控SQL

```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看表大小
SELECT 
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'database_name'
ORDER BY (data_length + index_length) DESC;
```

## 总结

本数据库设计文档提供了跑腿员管理系统的完整数据库方案，包括：

1. **完整的表结构设计**: 涵盖跑腿员信息、订单管理、收入记录等核心功能
2. **合理的索引设计**: 优化查询性能，支持各种业务场景
3. **详细的数据字典**: 明确各字段含义和枚举值
4. **性能优化建议**: 分表、索引、缓存等优化策略
5. **数据安全方案**: 加密、备份、访问控制等安全措施
6. **监控指标体系**: 业务和技术指标的监控方案

该设计方案可以支撑大规模的跑腿员管理业务，具有良好的扩展性和维护性。