# 跑腿员管理前端开发指导文档

## 概述

本文档为前端开发人员提供跑腿员管理功能的开发指导，包括页面设计、组件开发、API调用等详细说明。

## 页面结构设计

### 1. 跑腿员管理主页面

**路由**: `/admin/runners`

**页面功能**:
- 跑腿员列表展示
- 搜索和筛选功能
- 批量操作
- 统计信息展示

**页面布局**:
```
┌─────────────────────────────────────────────────────────────┐
│ 跑腿员管理                                                    │
├─────────────────────────────────────────────────────────────┤
│ [统计卡片区域]                                                │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐              │
│ │总跑腿员  │ │待审核   │ │在线人数  │ │工作中   │              │
│ │  1000   │ │   50    │ │  200    │ │  150    │              │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘              │
├─────────────────────────────────────────────────────────────┤
│ [搜索筛选区域]                                                │
│ 关键词: [_________] 状态: [全部▼] [搜索] [重置]               │
├─────────────────────────────────────────────────────────────┤
│ [操作按钮区域]                                                │
│ [批量审核] [批量暂停] [导出数据]                              │
├─────────────────────────────────────────────────────────────┤
│ [数据表格区域]                                                │
│ ┌───┬────────┬──────────┬────────┬────────┬────────┬────────┐ │
│ │选择│跑腿员信息│联系方式   │状态     │评分     │订单统计 │操作     │ │
│ ├───┼────────┼──────────┼────────┼────────┼────────┼────────┤ │
│ │☐ │张三      │138****0000│审核通过 │4.8★    │150/145 │[详情]  │ │
│ │☐ │李四      │139****0000│待审核   │-       │0/0     │[审核]  │ │
│ └───┴────────┴──────────┴────────┴────────┴────────┴────────┘ │
├─────────────────────────────────────────────────────────────┤
│ [分页区域]                                                    │
│                                    共100条 [1][2][3]...[5] │
└─────────────────────────────────────────────────────────────┘
```

### 2. 跑腿员详情页面

**路由**: `/admin/runners/:id`

**页面功能**:
- 跑腿员基本信息展示
- 身份认证信息查看
- 订单历史记录
- 收入统计
- 操作记录

**页面布局**:
```
┌─────────────────────────────────────────────────────────────┐
│ 跑腿员详情 - 张三                                [返回列表]    │
├─────────────────────────────────────────────────────────────┤
│ [基本信息卡片]                    [状态操作卡片]              │
│ ┌─────────────────────────┐    ┌─────────────────────────┐   │
│ │姓名: 张三                │    │当前状态: 审核通过        │   │
│ │手机: 138****0000        │    │[暂停服务] [拉入黑名单]   │   │
│ │身份证: 110101****1234   │    │[编辑备注]               │   │
│ │加入时间: 2024-01-15     │    │                         │   │
│ └─────────────────────────┘    └─────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│ [身份认证信息]                                                │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │身份证正面    │ │身份证背面    │ │人脸照片      │             │
│ │[查看大图]    │ │[查看大图]    │ │[查看大图]    │             │
│ └─────────────┘ └─────────────┘ └─────────────┘             │
├─────────────────────────────────────────────────────────────┤
│ [统计信息]                                                    │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐              │
│ │总订单    │ │成功订单  │ │取消订单  │ │评分      │              │
│ │  150    │ │  145    │ │   5     │ │ 4.8★   │              │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘              │
├─────────────────────────────────────────────────────────────┤
│ [Tab切换区域]                                                 │
│ [订单记录] [收入记录] [操作日志]                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │[对应Tab内容区域]                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件开发

### 1. 跑腿员列表组件 (RunnerList)

**组件路径**: `src/components/admin/runner/RunnerList.vue`

**主要功能**:
- 数据表格展示
- 搜索筛选
- 分页处理
- 批量操作

**关键代码结构**:
```vue
<template>
  <div class="runner-list">
    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <StatisticCard 
        v-for="stat in statistics" 
        :key="stat.key"
        :title="stat.title"
        :value="stat.value"
        :icon="stat.icon"
      />
    </div>
    
    <!-- 搜索筛选 -->
    <div class="search-filter">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="姓名、手机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" clearable>
            <el-option label="全部" :value="-1" />
            <el-option label="待审核" :value="0" />
            <el-option label="审核通过" :value="1" />
            <el-option label="审核拒绝" :value="2" />
            <el-option label="暂停服务" :value="3" />
            <el-option label="黑名单" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 数据表格 -->
    <el-table 
      :data="runnerList" 
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="跑腿员信息" min-width="150">
        <template #default="{ row }">
          <div class="runner-info">
            <div class="name">{{ row.real_name }}</div>
            <div class="id">ID: {{ row.id }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" prop="mobile" />
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ row.status_desc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="评分" width="80">
        <template #default="{ row }">
          <el-rate 
            v-model="row.score" 
            disabled 
            show-score 
            text-color="#ff9900"
          />
        </template>
      </el-table-column>
      <el-table-column label="订单统计" width="100">
        <template #default="{ row }">
          <div>{{ row.success_count }}/{{ row.order_count }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="viewDetail(row.id)">详情</el-button>
          <el-button 
            v-if="row.status === 0" 
            size="small" 
            type="primary"
            @click="auditRunner(row)"
          >
            审核
          </el-button>
          <el-dropdown @command="handleCommand">
            <el-button size="small">更多<i class="el-icon-arrow-down"></i></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{action: 'suspend', row}">暂停</el-dropdown-item>
                <el-dropdown-item :command="{action: 'blacklist', row}">拉黑</el-dropdown-item>
                <el-dropdown-item :command="{action: 'remark', row}">备注</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { runnerApi } from '@/api/admin/runner'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const runnerList = ref([])
const selectedRunners = ref([])
const statistics = ref([])

const searchForm = reactive({
  keyword: '',
  status: -1
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 方法
const fetchRunnerList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await runnerApi.getRunnerList(params)
    runnerList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取跑腿员列表失败')
  } finally {
    loading.value = false
  }
}

const fetchStatistics = async () => {
  try {
    const response = await runnerApi.getStatistics()
    statistics.value = [
      { key: 'total', title: '总跑腿员', value: response.data.total_runners, icon: 'user' },
      { key: 'pending', title: '待审核', value: response.data.pending_audit, icon: 'clock' },
      { key: 'online', title: '在线人数', value: response.data.online_runners, icon: 'online' },
      { key: 'working', title: '工作中', value: response.data.working_runners, icon: 'work' }
    ]
  } catch (error) {
    ElMessage.error('获取统计信息失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchRunnerList()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = -1
  pagination.page = 1
  fetchRunnerList()
}

const viewDetail = (id) => {
  router.push(`/admin/runners/${id}`)
}

const auditRunner = (runner) => {
  // 打开审核对话框
  // 实现审核逻辑
}

const getStatusType = (status) => {
  const statusMap = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info',
    4: 'danger'
  }
  return statusMap[status] || 'info'
}

// 生命周期
onMounted(() => {
  fetchRunnerList()
  fetchStatistics()
})
</script>
```

### 2. 跑腿员详情组件 (RunnerDetail)

**组件路径**: `src/components/admin/runner/RunnerDetail.vue`

**主要功能**:
- 详细信息展示
- 状态操作
- Tab切换
- 图片预览

### 3. 审核对话框组件 (AuditDialog)

**组件路径**: `src/components/admin/runner/AuditDialog.vue`

**主要功能**:
- 审核表单
- 图片查看
- 审核结果提交

## API调用封装

### API服务文件

**文件路径**: `src/api/admin/runner.js`

```javascript
import request from '@/utils/request'

// 跑腿员管理API
export const runnerApi = {
  // 获取跑腿员列表
  getRunnerList(params) {
    return request({
      url: '/admin/secured/runners',
      method: 'get',
      params
    })
  },

  // 获取跑腿员详情
  getRunnerDetail(id) {
    return request({
      url: `/admin/secured/runners/${id}`,
      method: 'get'
    })
  },

  // 审核跑腿员
  auditRunner(id, data) {
    return request({
      url: `/admin/secured/runners/${id}/audit`,
      method: 'put',
      data
    })
  },

  // 更新跑腿员状态
  updateRunnerStatus(id, data) {
    return request({
      url: `/admin/secured/runners/${id}/status`,
      method: 'put',
      data
    })
  },

  // 删除跑腿员
  deleteRunner(id) {
    return request({
      url: `/admin/secured/runners/${id}`,
      method: 'delete'
    })
  },

  // 获取统计信息
  getStatistics() {
    return request({
      url: '/admin/secured/runners/statistics',
      method: 'get'
    })
  },

  // 更新备注
  updateRemark(id, data) {
    return request({
      url: `/admin/secured/runners/${id}/remark`,
      method: 'put',
      data
    })
  },

  // 获取跑腿员订单
  getRunnerOrders(id, params) {
    return request({
      url: `/admin/secured/runners/${id}/orders`,
      method: 'get',
      params
    })
  }
}
```

## 路由配置

**文件路径**: `src/router/modules/admin.js`

```javascript
export default {
  path: '/admin',
  component: () => import('@/layouts/AdminLayout.vue'),
  meta: { requiresAuth: true, role: 'admin' },
  children: [
    {
      path: 'runners',
      name: 'RunnerManagement',
      component: () => import('@/views/admin/runner/RunnerList.vue'),
      meta: {
        title: '跑腿员管理',
        icon: 'user',
        breadcrumb: ['管理后台', '跑腿员管理']
      }
    },
    {
      path: 'runners/:id',
      name: 'RunnerDetail',
      component: () => import('@/views/admin/runner/RunnerDetail.vue'),
      meta: {
        title: '跑腿员详情',
        hidden: true,
        breadcrumb: ['管理后台', '跑腿员管理', '跑腿员详情']
      }
    }
  ]
}
```

## 状态管理 (Pinia)

**文件路径**: `src/stores/admin/runner.js`

```javascript
import { defineStore } from 'pinia'
import { runnerApi } from '@/api/admin/runner'

export const useRunnerStore = defineStore('adminRunner', {
  state: () => ({
    runnerList: [],
    currentRunner: null,
    statistics: {},
    loading: false
  }),

  getters: {
    // 获取不同状态的跑腿员数量
    getRunnerCountByStatus: (state) => (status) => {
      return state.runnerList.filter(runner => runner.status === status).length
    },

    // 获取在线跑腿员
    getOnlineRunners: (state) => {
      return state.runnerList.filter(runner => runner.is_online)
    }
  },

  actions: {
    // 获取跑腿员列表
    async fetchRunnerList(params) {
      this.loading = true
      try {
        const response = await runnerApi.getRunnerList(params)
        this.runnerList = response.data.list
        return response.data
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取统计信息
    async fetchStatistics() {
      try {
        const response = await runnerApi.getStatistics()
        this.statistics = response.data
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 审核跑腿员
    async auditRunner(id, data) {
      try {
        await runnerApi.auditRunner(id, data)
        // 更新本地状态
        const runner = this.runnerList.find(r => r.id === id)
        if (runner) {
          runner.status = data.status
          runner.status_desc = data.status === 1 ? '审核通过' : '审核拒绝'
        }
      } catch (error) {
        throw error
      }
    }
  }
})
```

## 样式设计规范

### 1. 色彩规范

```scss
// 状态颜色
$status-colors: (
  pending: #E6A23C,    // 待审核 - 橙色
  approved: #67C23A,   // 审核通过 - 绿色
  rejected: #F56C6C,   // 审核拒绝 - 红色
  suspended: #909399,  // 暂停服务 - 灰色
  blacklist: #F56C6C   // 黑名单 - 红色
);

// 主题色
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;
```

### 2. 组件样式

```scss
// 跑腿员列表样式
.runner-list {
  .statistics-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .search-filter {
    background: #fff;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .runner-info {
    .name {
      font-weight: 500;
      color: #303133;
    }
    .id {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
}

// 统计卡片样式
.statistic-card {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-2px);
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }

    .title {
      font-size: 14px;
      color: #606266;
    }
  }

  .card-value {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
}
```

## 权限控制

### 1. 路由权限

```javascript
// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      next('/login')
      return
    }
    
    if (to.meta.role && !userStore.hasRole(to.meta.role)) {
      next('/403')
      return
    }
  }
  
  next()
})
```

### 2. 按钮权限

```vue
<template>
  <el-button 
    v-if="hasPermission('runner:audit')"
    @click="auditRunner"
  >
    审核
  </el-button>
</template>

<script setup>
import { usePermission } from '@/composables/usePermission'

const { hasPermission } = usePermission()
</script>
```

## 错误处理

### 1. API错误处理

```javascript
// request拦截器
request.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    const { response } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          // 跳转到登录页
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(response.data.message || '请求失败')
      }
    } else {
      ElMessage.error('网络错误')
    }
    
    return Promise.reject(error)
  }
)
```

### 2. 组件错误边界

```vue
<template>
  <div class="error-boundary">
    <slot v-if="!hasError" />
    <div v-else class="error-content">
      <el-result
        icon="error"
        title="页面出错了"
        sub-title="请刷新页面重试"
      >
        <template #extra>
          <el-button type="primary" @click="retry">重试</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'

const hasError = ref(false)

onErrorCaptured((error) => {
  console.error('组件错误:', error)
  hasError.value = true
  return false
})

const retry = () => {
  hasError.value = false
  location.reload()
}
</script>
```

## 性能优化建议

### 1. 列表虚拟滚动

对于大量数据的列表，建议使用虚拟滚动：

```vue
<template>
  <el-table-v2
    :columns="columns"
    :data="runnerList"
    :width="800"
    :height="600"
    fixed
  />
</template>
```

### 2. 图片懒加载

```vue
<template>
  <el-image
    :src="runner.face_pic"
    lazy
    :preview-src-list="[runner.face_pic]"
    fit="cover"
  />
</template>
```

### 3. 防抖搜索

```javascript
import { debounce } from 'lodash-es'

const debouncedSearch = debounce(() => {
  fetchRunnerList()
}, 300)

watch(() => searchForm.keyword, () => {
  debouncedSearch()
})
```

## 测试指导

### 1. 单元测试

```javascript
// RunnerList.test.js
import { mount } from '@vue/test-utils'
import RunnerList from '@/components/admin/runner/RunnerList.vue'

describe('RunnerList', () => {
  it('should render runner list correctly', () => {
    const wrapper = mount(RunnerList, {
      props: {
        runners: mockRunners
      }
    })
    
    expect(wrapper.find('.runner-list').exists()).toBe(true)
    expect(wrapper.findAll('.runner-item')).toHaveLength(mockRunners.length)
  })
  
  it('should handle search correctly', async () => {
    const wrapper = mount(RunnerList)
    
    await wrapper.find('input[placeholder="姓名、手机号"]').setValue('张三')
    await wrapper.find('.search-btn').trigger('click')
    
    expect(wrapper.emitted('search')).toBeTruthy()
  })
})
```

### 2. E2E测试

```javascript
// runner-management.e2e.js
describe('Runner Management', () => {
  it('should display runner list', () => {
    cy.visit('/admin/runners')
    cy.get('.runner-list').should('be.visible')
    cy.get('.runner-item').should('have.length.greaterThan', 0)
  })
  
  it('should audit runner successfully', () => {
    cy.visit('/admin/runners')
    cy.get('[data-testid="audit-btn"]').first().click()
    cy.get('.audit-dialog').should('be.visible')
    cy.get('input[name="status"]').select('审核通过')
    cy.get('.confirm-btn').click()
    cy.get('.success-message').should('contain', '审核成功')
  })
})
```

## 部署注意事项

1. **环境变量配置**:
   ```bash
   # .env.production
   VITE_API_BASE_URL=https://api.yourdomain.com
   VITE_APP_TITLE=跑腿员管理系统
   ```

2. **构建优化**:
   ```javascript
   // vite.config.js
   export default defineConfig({
     build: {
       rollupOptions: {
         output: {
           manualChunks: {
             'element-plus': ['element-plus'],
             'vue-vendor': ['vue', 'vue-router', 'pinia']
           }
         }
       }
     }
   })
   ```

3. **CDN配置**:
   ```javascript
   // 使用CDN加速静态资源
   const cdnConfig = {
     css: ['https://cdn.jsdelivr.net/npm/element-plus/dist/index.css'],
     js: ['https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js']
   }
   ```

## 总结

本文档提供了跑腿员管理功能的完整前端开发指导，包括：

1. **页面设计**: 详细的页面布局和交互设计
2. **组件开发**: 核心组件的实现方案
3. **API集成**: 完整的API调用封装
4. **状态管理**: Pinia状态管理方案
5. **样式规范**: 统一的样式设计规范
6. **权限控制**: 完善的权限控制机制
7. **错误处理**: 全面的错误处理方案
8. **性能优化**: 实用的性能优化建议
9. **测试指导**: 单元测试和E2E测试方案
10. **部署配置**: 生产环境部署注意事项

开发人员可以根据本文档快速搭建跑腿员管理功能，确保代码质量和用户体验。