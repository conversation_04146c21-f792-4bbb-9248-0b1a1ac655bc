# 跑腿员状态更新修复 - 前端适配指南

## 概述

本文档说明了跑腿员状态更新功能的修复内容，以及前端需要进行的相应调整。

## 问题背景

在之前的实现中，跑腿员状态更新存在字段混淆问题：
- 原有的 `/api/v1/runner/status` 接口更新的是 `Status` 字段（审核状态）
- 但跑腿员实际需要更新的是 `WorkingStatus` 字段（工作状态）
- 导致跑腿员身份验证失败，因为 `GetRunnerID()` 方法依赖 `Status == 1`（审核通过）来验证跑腿员身份

## 修复内容

### 1. 后端修复

#### 1.1 保持原有API功能
- **文件**: `modules/runner/services/impl/runner_service_impl.go`
- **修改**: `UpdateRunnerStatus` 方法恢复为更新 `Status` 字段（审核状态）
- **原因**: 保持跑腿员身份验证逻辑正常工作

#### 1.2 新增工作状态更新API
- **新增文件**: `modules/runner/dto/working_status_dto.go`
- **新增DTO**: `UpdateWorkingStatusRequest` 专门用于工作状态更新
- **新增服务方法**: `UpdateWorkingStatus` 专门更新 `WorkingStatus` 字段
- **新增API**: `PUT /api/v1/runner/working-status` 专门用于更新工作状态

#### 1.3 字段含义明确
- **Status字段**（审核状态）:
  - `0`: 待审核
  - `1`: 审核通过
  - `2`: 审核拒绝
  - `3`: 暂停服务
  - `4`: 黑名单
- **WorkingStatus字段**（工作状态）:
  - `0`: 休息中
  - `1`: 接单中
  - `2`: 配送中

## 前端适配指南

### 1. 状态字段理解

请确保前端正确理解两个不同的状态字段：

```javascript
// 跑腿员模型中的两个状态字段
const runner = {
  // 审核状态（管理员设置）
  status: 1, // 0-待审核 1-审核通过 2-审核拒绝 3-暂停服务 4-黑名单
  status_desc: "审核通过",
  
  // 工作状态（跑腿员自己设置）
  working_status: 1, // 0-休息中 1-接单中 2-配送中
  working_status_desc: "接单中"
}
```

### 2. 跑腿员端状态更新

#### 2.1 使用新的工作状态API

跑腿员端应该使用新的工作状态更新API：

```vue
<template>
  <div class="status-switcher">
    <el-radio-group v-model="workingStatus" @change="updateWorkingStatus">
      <el-radio-button :label="0">休息中</el-radio-button>
      <el-radio-button :label="1">接单中</el-radio-button>
      <el-radio-button :label="2">配送中</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { runnerApi } from '@/api/runner'

const workingStatus = ref(0)

const updateWorkingStatus = async (status) => {
  try {
    await runnerApi.updateWorkingStatus({
      working_status: status, // 0-休息中 1-接单中 2-配送中
      reason: '状态切换'
    })
    ElMessage.success('工作状态更新成功')
  } catch (error) {
    ElMessage.error('工作状态更新失败')
  }
}
</script>
```

#### 2.2 API调用更新

更新API调用，使用新的工作状态接口：

```javascript
// src/api/runner.js
export const runnerApi = {
  // 更新审核状态（管理员使用）
  updateStatus(data) {
    return request({
      url: '/api/v1/runner/status',
      method: 'put',
      data: {
        status: data.status, // 0-待审核 1-审核通过 2-审核拒绝 3-暂停服务 4-黑名单
        reason: data.reason || ''
      }
    })
  },
  
  // 更新工作状态（跑腿员使用）
  updateWorkingStatus(data) {
    return request({
      url: '/api/v1/runner/working-status',
      method: 'put',
      data: {
        working_status: data.working_status, // 0-休息中 1-接单中 2-配送中
        reason: data.reason || ''
      }
    })
  }
}
```

### 3. 管理员端显示

#### 3.1 列表页面

确保管理员列表页面正确显示两种状态：

```vue
<template>
  <el-table-column label="审核状态" width="100">
    <template #default="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ row.status_desc }}
      </el-tag>
    </template>
  </el-table-column>
  
  <el-table-column label="工作状态" width="100">
    <template #default="{ row }">
      <el-tag :type="getWorkingStatusType(row.working_status)">
        {{ row.working_status_desc }}
      </el-tag>
    </template>
  </el-table-column>
</template>

<script setup>
const getStatusType = (status) => {
  const statusMap = {
    0: 'warning', // 待审核
    1: 'success', // 审核通过
    2: 'danger',  // 审核拒绝
    3: 'info',    // 暂停服务
    4: 'danger'   // 黑名单
  }
  return statusMap[status] || 'info'
}

const getWorkingStatusType = (workingStatus) => {
  const statusMap = {
    0: 'info',    // 休息中
    1: 'success', // 接单中
    2: 'warning'  // 配送中
  }
  return statusMap[workingStatus] || 'info'
}
</script>
```

#### 3.2 详情页面

在跑腿员详情页面中，确保正确显示和区分两种状态：

```vue
<template>
  <div class="runner-detail">
    <el-card title="状态信息">
      <el-descriptions :column="2">
        <el-descriptions-item label="审核状态">
          <el-tag :type="getStatusType(runner.status)">
            {{ runner.status_desc }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="工作状态">
          <el-tag :type="getWorkingStatusType(runner.working_status)">
            {{ runner.working_status_desc }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="在线状态">
          <el-tag :type="runner.is_online ? 'success' : 'danger'">
            {{ runner.is_online ? '在线' : '离线' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>
```

### 4. 状态筛选功能

如果管理员端有按工作状态筛选的功能，需要确保使用正确的字段：

```vue
<template>
  <el-form-item label="工作状态">
    <el-select v-model="searchForm.working_status" clearable>
      <el-option label="全部" :value="-1" />
      <el-option label="休息中" :value="0" />
      <el-option label="接单中" :value="1" />
      <el-option label="配送中" :value="2" />
    </el-select>
  </el-form-item>
</template>
```

### 5. 数据统计

更新统计相关的代码，确保正确统计工作状态：

```javascript
// 统计不同工作状态的跑腿员数量
const getWorkingStatusStats = (runners) => {
  return {
    resting: runners.filter(r => r.working_status === 0).length,
    available: runners.filter(r => r.working_status === 1).length,
    delivering: runners.filter(r => r.working_status === 2).length
  }
}
```

## 测试建议

### 1. 功能测试

1. **跑腿员端测试**:
   - 登录跑腿员账号
   - 切换工作状态（休息中 → 接单中 → 配送中）
   - 验证状态是否正确保存

2. **管理员端测试**:
   - 查看跑腿员列表，确认工作状态正确显示
   - 查看跑腿员详情，确认状态信息完整
   - 测试按工作状态筛选功能

### 2. 数据验证

在浏览器开发者工具中验证API响应：

```javascript
// 跑腿员状态更新请求
PUT /api/v1/runner/status
{
  "status": 1,  // 工作状态：1-接单中
  "reason": "开始接单"
}

// 管理员获取跑腿员列表响应
GET /admin/secured/runners
{
  "data": {
    "list": [
      {
        "id": 1,
        "status": 1,              // 审核状态
        "status_desc": "审核通过",
        "working_status": 1,      // 工作状态
        "working_status_desc": "接单中",
        "is_online": true
      }
    ]
  }
}
```

## 注意事项

1. **向后兼容**: 修复后的API保持了向后兼容性，现有的前端代码无需大幅修改

2. **状态区分**: 请务必区分 `status`（审核状态）和 `working_status`（工作状态）两个字段

3. **用户体验**: 建议在跑腿员端提供清晰的状态说明，帮助用户理解不同状态的含义

4. **错误处理**: 添加适当的错误处理，当状态更新失败时给用户明确的提示

## 联系方式

如有疑问，请联系后端开发团队或查看相关API文档。