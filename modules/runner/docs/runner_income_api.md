# 跑腿员收入记录API文档

## API概述

**接口路径**：`/income-list`  
**请求方法**：GET  
**接口描述**：获取跑腿员的收入记录列表  

## 接口鉴权

该接口需要跑腿员身份认证，通过JWT令牌验证用户身份和权限。

## 请求参数

| 参数名 | 类型 | 是否必须 | 默认值 | 说明 |
| ----- | --- | ------ | ----- | --- |
| page | int | 否 | 1 | 页码 |
| pageSize | int | 否 | 10 | 每页数量 |

## 返回字段说明

返回数据为分页列表，包含多个RunnerIncomeLogResponse对象。每个对象包含以下字段：

| 字段名 | 类型 | 说明 |
| ----- | --- | --- |
| id | int64 | 记录ID |
| order_id | int64 | 订单ID |
| order_no | string | 订单编号 |
| amount | float64 | 金额 |
| type | int | 收入类型 |
| type_desc | string | 收入类型描述 |
| status | int | 结算状态 |
| status_desc | string | 结算状态描述 |
| description | string | 描述 |
| create_time | time.Time | 创建时间 |

## Type字段详细说明

Type字段表示收入的类型，可以有以下取值：

| 值 | 描述 | 说明 |
| -- | --- | --- |
| 0 | 配送费 | 跑腿员完成订单获得的基础配送费用 |
| 1 | 小费 | 用户额外支付给跑腿员的小费 |
| 2 | 奖励 | 系统发放的奖励金额，如活动奖励、达标奖励等 |
| 3 | 退款 | 订单取消或异常情况下的退款 |

## Status字段详细说明

Status字段表示收入的结算状态，可以有以下取值：

| 值 | 描述 | 说明 |
| -- | --- | --- |
| 0 | 未结算 | 收入已记录但尚未结算到账户余额 |
| 1 | 已结算 | 收入已结算并已计入账户可用余额 |
| 2 | 已退款 | 该笔收入已被退款（如订单取消等情况） |

## 响应示例

### 成功响应

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "order_id": 10086,
        "order_no": "RO202507071234567890",
        "amount": 15.5,
        "type": 0,
        "type_desc": "配送费",
        "status": 1,
        "status_desc": "已结算",
        "description": "订单#RO202507071234567890配送费",
        "create_time": "2025-07-07T10:30:00+08:00"
      },
      {
        "id": 2,
        "order_id": 10086,
        "order_no": "RO202507071234567890",
        "amount": 5.0,
        "type": 1,
        "type_desc": "小费",
        "status": 1,
        "status_desc": "已结算",
        "description": "订单#RO202507071234567890小费",
        "create_time": "2025-07-07T10:30:00+08:00"
      }
    ],
    "total": 2,
    "page": 1,
    "page_size": 10
  }
}
```

### 错误响应

#### 未认证的请求

```json
{
  "code": 401,
  "msg": "未认证的请求",
  "data": null
}
```

#### 非跑腿员身份访问

```json
{
  "code": 403,
  "msg": "您不是跑腿员",
  "data": null
}
```

## 业务流程说明

1. 接口首先验证用户身份认证信息
2. 检查用户是否具有跑腿员身份
3. 获取分页参数
4. 调用runnerService.ListRunnerIncomeLogs服务获取收入记录
5. 返回格式化后的收入记录列表及分页信息

## 注意事项

- 该接口只返回当前认证的跑腿员自己的收入记录
- 记录按时间倒序排列，最新的记录排在最前面
- Type和Status字段均有对应的描述字段(type_desc, status_desc)，便于前端直接显示
