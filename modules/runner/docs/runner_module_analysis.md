# 跑腿组件分析文档

## 1. 简介

跑腿组件是O_Mall后端系统的重要部分，用于管理跑腿员和跑腿订单业务，为用户提供商品配送服务。该组件实现了跑腿员注册、审核、接单、配送以及收入管理等完整功能。

## 2. 组件结构

跑腿组件采用标准的分层架构设计，包含以下主要部分：

```
/modules/runner/
├── constants/        # 常量定义
├── controllers/      # 控制器层，处理HTTP请求
├── core/             # 核心业务逻辑
├── dto/              # 数据传输对象
├── factory/          # 工厂类
├── models/           # 数据模型定义
├── repositories/     # 数据访问层
├── routers/          # 路由配置
└── services/         # 服务层
    └── impl/         # 服务实现
```

## 3. 核心功能模块

### 3.1 跑腿员管理

跑腿员管理模块负责跑腿员的全生命周期管理，包括：

- **注册与审核**：用户可以申请成为跑腿员，需要提交个人信息、身份证件照片等资料，经过后台审核后方可成为正式跑腿员
- **状态管理**：跑腿员可以设置工作状态（休息中、接单中、配送中）以及在线/离线状态
- **位置更新**：跑腿员可以实时更新自己的位置信息，用于接单和配送管理
- **收入管理**：跑腿员可以查看收入统计、收入明细，并申请提现

### 3.2 订单管理

订单管理模块处理跑腿订单的全流程，主要功能包括：

- **订单创建**：用户可以创建跑腿订单，指定取货地点、配送地点和其他订单要求
- **订单匹配**：系统可以查询附近跑腿员，并支持自动/手动分配订单
- **订单状态流转**：订单状态从创建、接单、取货到完成的全流程管理
- **订单评价**：用户和跑腿员可以互相评价，形成评分体系

### 3.3 配送费计算

提供配送费用的智能计算功能，基于距离、重量、时间等因素动态计算配送费用。

## 4. 数据模型

跑腿组件包含以下核心数据模型：

### 4.1 Runner（跑腿员）

跑腿员模型存储跑腿员的基本信息、认证信息、状态信息和统计数据等：

- 基本信息：ID、用户ID、真实姓名、联系方式等
- 认证信息：身份证号码、身份证照片、人脸照片等
- 状态信息：在线状态、工作状态、当前位置等
- 统计数据：评分、订单数量、成功率、账户余额等

### 4.2 RunnerOrder（跑腿订单）

跑腿订单模型记录订单的全周期信息：

- 基本信息：订单ID、用户ID、跑腿员ID等
- 配送信息：取货地点、配送地点、配送距离等
- 状态信息：订单状态、支付状态等
- 时间信息：创建时间、接单时间、取货时间、完成时间等
- 费用信息：配送费用、小费等

### 4.3 RunnerIncomeLog（收入记录）

记录跑腿员的每笔收入明细：

- 收入ID、跑腿员ID、关联订单ID
- 收入金额、收入类型、收入状态
- 创建时间、结算时间等

### 4.4 RunnerWithdrawal（提现记录）

记录跑腿员的提现申请和处理情况：

- 提现ID、跑腿员ID
- 提现金额、提现状态、收款账号
- 申请时间、处理时间等

## 5. API接口

跑腿组件提供了两组主要的API接口：

### 5.1 跑腿员接口（/api/v1/runner）

- **POST /register**：注册跑腿员
- **GET /info**：获取跑腿员信息
- **GET /:id/detail**：获取跑腿员详情
- **PUT /status**：更新跑腿员状态
- **PUT /location**：更新跑腿员位置
- **POST /nearby**：获取附近跑腿员
- **GET /income**：获取跑腿员收入
- **GET /income/logs**：获取收入记录
- **POST /withdrawal**：申请提现
- **GET /withdrawals**：获取提现记录
- **GET /statistics**：获取统计信息
- **POST /delivery-fee/calculate**：计算配送费

### 5.2 跑腿订单接口（/api/v1/runner-order）

- **POST /**：创建跑腿订单
- **GET /:id**：获取订单详情
- **GET /user/list**：获取用户订单列表
- **GET /runner/list**：获取跑腿员订单列表
- **POST /accept**：接单
- **POST /cancel**：取消订单
- **POST /pickup**：取货
- **POST /complete**：完成订单
- **POST /user/rate**：用户评价
- **POST /runner/rate**：跑腿员评价

## 6. 业务流程

### 6.1 跑腿员注册流程

1. 用户提交跑腿员注册申请，包括个人信息和认证材料
2. 系统创建跑腿员申请记录，状态为
### 6.1 跑腿员注册流程

1. 用户提交跑腿员注册申请，包括个人信息和认证材料
2. 系统创建跑腿员申请记录，状态为"待审核"
3. 管理员审核申请材料，通过或拒绝申请
4. 若审核通过，系统创建跑腿员账号，设置初始状态
5. 跑腿员可开始接单工作

### 6.2 订单处理流程

1. 用户创建跑腿订单，指定取货和配送信息
2. 系统计算配送费用，创建订单记录
3. 系统匹配附近可用跑腿员，或由跑腿员主动接单
4. 跑腿员接单后，前往取货地点取货
5. 取货完成后，跑腿员确认取货，前往配送地点
6. 配送完成后，跑腿员确认完成订单
7. 用户确认收货，对跑腿员服务进行评价
8. 系统结算订单，向跑腿员账户添加收入

### 6.3 收入提现流程

1. 跑腿员查看账户余额和收入明细
2. 跑腿员提交提现申请，指定提现金额和收款账号
3. 系统创建提现记录，状态为"处理中"
4. 财务人员审核并处理提现申请
5. 完成转账后，更新提现状态为"已完成"

## 7. 技术实现

### 7.1 位置服务

- 使用高德地图或百度地图API进行地理编码和距离计算
- 采用Redis GEO类型存储和查询跑腿员实时位置信息
- 基于地理围栏技术优化配送区域划分

### 7.2 订单匹配算法

- 基于位置、评分、接单率等多维度权重计算最优跑腿员
- 支持智能调度和手动指派两种分配模式
- 高峰期动态调整配送费及匹配策略

### 7.3 安全性考虑

- 跑腿员身份信息加密存储，敏感信息脱敏显示
- 位置信息实时更新但有权限控制，保护用户和跑腿员隐私
- 支付和提现采用多重验证机制

## 8. 扩展与优化方向

### 8.1 功能扩展

- 增加智能路线规划，优化配送路径
- 提供实时配送轨迹查看功能
- 增加配送保险和意外赔付机制
- 支持特殊物品（如鲜花、蛋糕等）的定制化配送

### 8.2 性能优化

- 订单分配系统优化，减少匹配时延
- 位置服务使用异步更新，降低系统负载
- 历史订单数据归档，优化查询性能

### 8.3 用户体验

- 提供更精准的配送时间预估
- 增强订单状态实时通知机制
- 优化跑腿员接单和配送体验

## 9. 总结

跑腿组件作为O_Mall平台的重要功能模块，通过完善的跑腿员管理、订单处理和收入管理体系，为用户提供了高效、可靠的配送服务。该组件采用分层架构设计，具有良好的扩展性和维护性，可以支持业务的持续发展和功能迭代。
