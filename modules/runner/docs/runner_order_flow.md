# 跑腿订单状态流转图

## 订单状态流转

```mermaid
stateDiagram-v2
    [*] --> OrderStatusWaitingPay: 创建订单
    
    OrderStatusWaitingPay --> OrderStatusPaid: 用户支付
    OrderStatusWaitingPay --> OrderStatusCanceled: 用户取消
    
    OrderStatusPaid --> OrderStatusAccepted: 跑腿员接单
    OrderStatusPaid --> OrderStatusCanceled: 用户/系统取消
    
    OrderStatusAccepted --> OrderStatusPickedUp: 跑腿员取货
    OrderStatusAccepted --> OrderStatusCanceled: 用户/跑腿员取消
    
    OrderStatusPickedUp --> OrderStatusCompleted: 跑腿员完成配送
    OrderStatusPickedUp --> OrderStatusCanceled: 用户/跑腿员取消
    
    OrderStatusCompleted --> [*]: 订单完成
    OrderStatusCanceled --> [*]: 订单结束
    
    state OrderStatusWaitingPay {
        [*] --> WaitPay
        WaitPay: 状态码: 10
        WaitPay: 描述: 待支付
    }
    
    state OrderStatusPaid {
        [*] --> Paid
        Paid: 状态码: 20
        Paid: 描述: 已支付，待接单
    }
    
    state OrderStatusAccepted {
        [*] --> Accepted
        Accepted: 状态码: 30
        Accepted: 描述: 已接单，待取货
    }
    
    state OrderStatusPickedUp {
        [*] --> PickedUp
        PickedUp: 状态码: 40
        PickedUp: 描述: 已取货，配送中
    }
    
    state OrderStatusCompleted {
        [*] --> Completed
        Completed: 状态码: 50
        Completed: 描述: 已完成
    }
    
    state OrderStatusCanceled {
        [*] --> Canceled
        Canceled: 状态码: 60
        Canceled: 描述: 已取消
    }
```

## 跑腿员工作状态流转

```mermaid
stateDiagram-v2
    [*] --> Rest: 初始状态
    
    Rest --> Receiving: 上线接单
    Receiving --> Delivering: 接单成功
    Delivering --> Receiving: 订单完成
    Receiving --> Rest: 下线休息
    Delivering --> Rest: 特殊情况下线
    
    state Rest {
        [*] --> RestState
        RestState: 状态码: 0
        RestState: 描述: 休息中
    }
    
    state Receiving {
        [*] --> ReceivingState
        ReceivingState: 状态码: 1
        ReceivingState: 描述: 接单中
    }
    
    state Delivering {
        [*] --> DeliveringState
        DeliveringState: 状态码: 2
        DeliveringState: 描述: 配送中
    }
```

## 收入状态流转

```mermaid
stateDiagram-v2
    [*] --> IncomePending: 订单完成创建收入
    
    IncomePending --> IncomeSuccess: 系统审核通过
    IncomePending --> IncomeFailed: 审核不通过
    
    IncomeSuccess --> [*]: 收入入账
    IncomeFailed --> [*]: 收入处理结束
    
    state IncomePending {
        [*] --> Pending
        Pending: 状态码: 0
        Pending: 描述: 待入账
    }
    
    state IncomeSuccess {
        [*] --> Success
        Success: 状态码: 1
        Success: 描述: 已入账
    }
    
    state IncomeFailed {
        [*] --> Failed
        Failed: 状态码: 2
        Failed: 描述: 入账失败
    }
```

## 提现状态流转

```mermaid
stateDiagram-v2
    [*] --> WithdrawalPending: 申请提现
    
    WithdrawalPending --> WithdrawalProcessing: 开始处理
    WithdrawalProcessing --> WithdrawalSuccess: 提现成功
    WithdrawalProcessing --> WithdrawalFailed: 提现失败
    
    WithdrawalSuccess --> [*]: 提现完成
    WithdrawalFailed --> [*]: 提现失败结束
    
    state WithdrawalPending {
        [*] --> Pending
        Pending: 状态码: 0
        Pending: 描述: 待处理
    }
    
    state WithdrawalProcessing {
        [*] --> Processing
        Processing: 状态码: 1
        Processing: 描述: 处理中
    }
    
    state WithdrawalSuccess {
        [*] --> Success
        Success: 状态码: 2
        Success: 描述: 成功
    }
    
    state WithdrawalFailed {
        [*] --> Failed
        Failed: 状态码: 3
        Failed: 描述: 失败
    }
```
