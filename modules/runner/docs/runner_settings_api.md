# 跑腿员个人资料与设置API文档

本文档详细介绍跑腿员个人资料和各类设置的相关API接口。这些接口允许跑腿员获取和管理自己的个人资料、支付账户、通知设置和服务设置信息。

## 目录

- [个人资料API](#个人资料api)
- [支付账户API](#支付账户api)
- [通知设置API](#通知设置api)
- [服务设置API](#服务设置api)

---

## 个人资料API

### API概述

**接口路径**：`/profile`  
**请求方法**：GET  
**接口描述**：获取跑腿员的个人资料信息  

### 接口鉴权

该接口需要跑腿员身份认证，通过JWT令牌验证用户身份和权限。

### 请求参数

无需额外参数，系统自动从JWT令牌中识别用户身份。

### 返回字段说明

| 字段名 | 类型 | 说明 |
| ----- | --- | --- |
| id | int64 | 跑腿员ID |
| user_id | int64 | 用户ID |
| real_name | string | 真实姓名 |
| mobile | string | 手机号码 |
| avatar | string | 头像URL |
| id_card_number | string | 身份证号码 |
| status | int | 状态 |
| status_desc | string | 状态描述 |
| working_status | int | 工作状态 |
| working_status_desc | string | 工作状态描述 |
| is_online | bool | 是否在线 |
| create_time | string | 创建时间 |
| last_login_time | string | 最后登录时间 |
| score | float64 | 评分 |
| order_count | int64 | 完成订单数 |
| cancel_count | int64 | 取消订单数 |
| service_areas | []string | 服务区域 |

### Status字段说明

| 值 | 描述 | 说明 |
| -- | --- | --- |
| 1 | 正常 | 跑腿员可以正常接单工作 |
| 2 | 暂停 | 跑腿员账号被暂时停用 |
| 3 | 黑名单 | 跑腿员账号因违规被永久停用 |

### WorkingStatus字段说明

| 值 | 描述 | 说明 |
| -- | --- | --- |
| 0 | 空闲 | 跑腿员当前无订单，可以接单 |
| 1 | 繁忙 | 跑腿员当前有订单，但未达到接单上限 |
| 2 | 配送中 | 跑腿员当前正在配送订单 |

### 请求示例

```
GET /api/v1/runner/secured/profile HTTP/1.1
Host: api.example.com
Authorization: Bearer {jwt_token}
```

### 响应示例

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 123456,
    "user_id": 789012,
    "real_name": "张三",
    "mobile": "13800138000",
    "avatar": "https://example.com/avatars/runner123.jpg",
    "id_card_number": "3****************X",
    "status": 1,
    "status_desc": "正常",
    "working_status": 0,
    "working_status_desc": "空闲",
    "is_online": true,
    "create_time": "2025-01-01 12:00:00",
    "last_login_time": "2025-07-07 10:30:15",
    "score": 4.8,
    "order_count": 328,
    "cancel_count": 5,
    "service_areas": ["朝阳区", "海淀区"]
  }
}
```

---

## 支付账户API

### API概述

**接口路径**：`/payment-accounts`  
**请求方法**：GET  
**接口描述**：获取跑腿员的支付账户信息  

### 接口鉴权

该接口需要跑腿员身份认证，通过JWT令牌验证用户身份和权限。

### 请求参数

无需额外参数，系统自动从JWT令牌中识别用户身份。

### 返回字段说明

返回数据为数组，包含多个支付账户信息。每个账户包含以下字段：

| 字段名 | 类型 | 说明 |
| ----- | --- | --- |
| id | int64 | 支付账户ID |
| runner_id | int64 | 跑腿员ID |
| account_type | int | 账户类型 |
| account_name | string | 账户名称 |
| account_number | string | 账户号码 |
| bank_name | string | 银行名称（银行卡类型时有效） |
| is_default | bool | 是否默认账户 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

### AccountType字段说明

| 值 | 描述 | 说明 |
| -- | --- | --- |
| 1 | 微信 | 微信支付账户 |
| 2 | 支付宝 | 支付宝账户 |
| 3 | 银行卡 | 银行卡账户 |

### 请求示例

```
GET /api/v1/runner/secured/payment-accounts HTTP/1.1
Host: api.example.com
Authorization: Bearer {jwt_token}
```

### 响应示例

```json
{
  "code": 0,
  "message": "成功",
  "data": [
    {
      "id": 1,
      "runner_id": 123456,
      "account_type": 1,
      "account_name": "张三",
      "account_number": "test_account_123",
      "bank_name": "",
      "is_default": true,
      "created_at": "2025-01-01 12:00:00",
      "updated_at": "2025-07-01 18:30:25"
    },
    {
      "id": 2,
      "runner_id": 123456,
      "account_type": 3,
      "account_name": "张三",
      "account_number": "6222************1234",
      "bank_name": "招商银行",
      "is_default": false,
      "created_at": "2025-02-15 09:20:00",
      "updated_at": "2025-02-15 09:20:00"
    }
  ]
}
```

---

## 通知设置API

### API概述

**接口路径**：`/notification-settings`  
**请求方法**：GET  
**接口描述**：获取跑腿员的通知设置  

### 接口鉴权

该接口需要跑腿员身份认证，通过JWT令牌验证用户身份和权限。

### 请求参数

无需额外参数，系统自动从JWT令牌中识别用户身份。

### 返回字段说明

| 字段名 | 类型 | 说明 |
| ----- | --- | --- |
| runner_id | int64 | 跑腿员ID |
| order_notification | bool | 是否接收订单通知 |
| system_notification | bool | 是否接收系统通知 |
| marketing_notification | bool | 是否接收营销通知 |
| payment_notification | bool | 是否接收支付通知 |
| sound | bool | 是否开启声音通知 |
| vibration | bool | 是否开启振动通知 |
| updated_at | string | 更新时间 |

### 请求示例

```
GET /api/v1/runner/secured/notification-settings HTTP/1.1
Host: api.example.com
Authorization: Bearer {jwt_token}
```

### 响应示例

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "runner_id": 123456,
    "order_notification": true,
    "system_notification": true,
    "marketing_notification": false,
    "payment_notification": true,
    "sound": true,
    "vibration": true,
    "updated_at": "2025-06-15 14:30:25"
  }
}
```

---

## 服务设置API

### API概述

**接口路径**：`/service-settings`  
**请求方法**：GET  
**接口描述**：获取跑腿员的服务设置  

### 接口鉴权

该接口需要跑腿员身份认证，通过JWT令牌验证用户身份和权限。

### 请求参数

无需额外参数，系统自动从JWT令牌中识别用户身份。

### 返回字段说明

| 字段名 | 类型 | 说明 |
| ----- | --- | --- |
| runner_id | int64 | 跑腿员ID |
| auto_accept_order | bool | 是否自动接单 |
| max_order_distance | float64 | 最大接单距离（公里） |
| order_types | []int | 可接订单类型 |
| working_hours_start | string | 工作时间开始（格式：HH:MM） |
| working_hours_end | string | 工作时间结束（格式：HH:MM） |
| rest_days | []int | 休息日（1-周一，2-周二...7-周日） |
| max_simultaneous_order | int | 最大同时接单数 |
| updated_at | string | 更新时间 |

### OrderTypes字段说明

| 值 | 描述 | 说明 |
| -- | --- | --- |
| 1 | 外卖 | 餐饮外卖订单 |
| 2 | 快递 | 快递代取订单 |
| 3 | 买药 | 药品购买配送订单 |
| 4 | 代购 | 日常用品代购订单 |

### 请求示例

```
GET /api/v1/runner/secured/service-settings HTTP/1.1
Host: api.example.com
Authorization: Bearer {jwt_token}
```

### 响应示例

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "runner_id": 123456,
    "auto_accept_order": false,
    "max_order_distance": 5.0,
    "order_types": [1, 2, 3, 4],
    "working_hours_start": "08:00",
    "working_hours_end": "20:00",
    "rest_days": [7],
    "max_simultaneous_order": 2,
    "updated_at": "2025-06-20 09:15:30"
  }
}
```
