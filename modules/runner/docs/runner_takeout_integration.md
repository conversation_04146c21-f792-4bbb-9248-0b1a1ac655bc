# 跑腿员与外卖模块集成技术文档

> 本文档详细说明跑腿员模块与外卖模块的集成方案，包括技术架构、接口设计、数据流转和实现细节。

## 目录

- [1. 集成架构概述](#1-集成架构概述)
  - [1.1 整体架构](#11-整体架构)
  - [1.2 模块关系](#12-模块关系)
  - [1.3 数据流转](#13-数据流转)
- [2. 核心集成功能](#2-核心集成功能)
  - [2.1 跑腿员状态同步](#21-跑腿员状态同步)
  - [2.2 订单自动分配](#22-订单自动分配)
  - [2.3 配送状态追踪](#23-配送状态追踪)
- [3. 接口设计](#3-接口设计)
  - [3.1 跑腿员查询接口](#31-跑腿员查询接口)
  - [3.2 订单分配接口](#32-订单分配接口)
  - [3.3 状态同步接口](#33-状态同步接口)
- [4. 数据库设计](#4-数据库设计)
  - [4.1 关联表设计](#41-关联表设计)
  - [4.2 索引优化](#42-索引优化)
  - [4.3 数据一致性](#43-数据一致性)
- [5. 事件驱动机制](#5-事件驱动机制)
  - [5.1 事件定义](#51-事件定义)
  - [5.2 事件处理](#52-事件处理)
  - [5.3 异常处理](#53-异常处理)
- [6. 性能优化](#6-性能优化)
  - [6.1 缓存策略](#61-缓存策略)
  - [6.2 查询优化](#62-查询优化)
  - [6.3 负载均衡](#63-负载均衡)

## 1. 集成架构概述

### 1.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户端APP     │    │   商家端APP     │    │  跑腿员APP      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API Gateway                             │
└─────────────────────────────────────────────────────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   外卖模块      │◄──►│   跑腿员模块    │◄──►│   订单模块      │
│  (Takeout)      │    │   (Runner)      │    │   (Order)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                        共享数据库                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ 外卖订单表  │  │ 跑腿员表    │  │ 跑腿订单表  │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 模块关系

#### 外卖模块 (Takeout Module)
- **职责**：管理商家、商品、外卖订单
- **与跑腿员模块的交互**：
  - 查询可用跑腿员
  - 创建配送任务
  - 接收配送状态更新
  - 计算配送费用

#### 跑腿员模块 (Runner Module)
- **职责**：管理跑腿员、跑腿订单、配送流程
- **与外卖模块的交互**：
  - 提供跑腿员查询服务
  - 接收配送任务
  - 推送配送状态
  - 提供配送费计算

### 1.3 数据流转

```
用户下单 → 外卖订单创建 → 查询可用跑腿员 → 创建跑腿订单 → 跑腿员接单 → 配送状态同步 → 订单完成
```

## 2. 核心集成功能

### 2.1 跑腿员状态同步

#### 状态映射关系

```go
// 跑腿员工作状态
const (
    RunnerStatusResting    = 0  // 休息中
    RunnerStatusAvailable  = 1  // 接单中
    RunnerStatusDelivering = 2  // 配送中
)

// 外卖订单配送状态
const (
    DeliveryStatusPending    = 0  // 待配送
    DeliveryStatusAssigned   = 1  // 已分配
    DeliveryStatusPickedUp   = 2  // 已取货
    DeliveryStatusDelivering = 3  // 配送中
    DeliveryStatusDelivered  = 4  // 已送达
    DeliveryStatusCanceled   = 5  // 已取消
)
```

#### 状态同步机制

```go
// RunnerStatusSyncService 跑腿员状态同步服务
type RunnerStatusSyncService struct {
    runnerRepo   repositories.RunnerRepository
    takeoutRepo  repositories.TakeoutOrderRepository
    eventBus     *events.EventBus
}

// SyncRunnerStatus 同步跑腿员状态到外卖订单
func (s *RunnerStatusSyncService) SyncRunnerStatus(runnerID int64, status int) error {
    // 1. 获取跑腿员当前配送的外卖订单
    orders, err := s.takeoutRepo.GetActiveOrdersByRunner(runnerID)
    if err != nil {
        return err
    }

    // 2. 根据跑腿员状态更新外卖订单状态
    for _, order := range orders {
        deliveryStatus := s.mapRunnerStatusToDeliveryStatus(status)
        err = s.takeoutRepo.UpdateDeliveryStatus(order.ID, deliveryStatus)
        if err != nil {
            return err
        }

        // 3. 发送状态变更事件
        event := &events.DeliveryStatusChangedEvent{
            OrderID:        order.ID,
            RunnerID:       runnerID,
            DeliveryStatus: deliveryStatus,
            Timestamp:      time.Now(),
        }
        s.eventBus.Publish("delivery.status.changed", event)
    }

    return nil
}
```

### 2.2 订单自动分配

#### 分配算法

```go
// OrderAssignmentService 订单分配服务
type OrderAssignmentService struct {
    runnerService    services.RunnerService
    distanceService  services.DistanceService
    scoreService     services.ScoreService
}

// AssignmentCriteria 分配条件
type AssignmentCriteria struct {
    MaxDistance    float64  `json:"max_distance"`     // 最大距离(公里)
    MinScore       float64  `json:"min_score"`        // 最低评分
    Priority       string   `json:"priority"`         // 优先级策略
    MaxCurrentOrders int    `json:"max_current_orders"` // 最大同时配送订单数
}

// AutoAssignOrder 自动分配订单
func (s *OrderAssignmentService) AutoAssignOrder(takeoutOrderID int64, criteria AssignmentCriteria) (*dto.RunnerAssignmentResult, error) {
    // 1. 获取外卖订单信息
    takeoutOrder, err := s.takeoutRepo.GetByID(takeoutOrderID)
    if err != nil {
        return nil, err
    }

    // 2. 查询附近可用跑腿员
    nearbyRequest := &dto.NearbyRunnerRequest{
        Latitude:         takeoutOrder.MerchantLat,
        Longitude:        takeoutOrder.MerchantLng,
        Radius:           criteria.MaxDistance,
        WorkingStatus:    RunnerStatusAvailable,
        MinScore:         criteria.MinScore,
        MaxCurrentOrders: criteria.MaxCurrentOrders,
        Limit:            20,
    }

    runners, err := s.runnerService.GetNearbyRunners(nearbyRequest)
    if err != nil {
        return nil, err
    }

    if len(runners) == 0 {
        return &dto.RunnerAssignmentResult{
            Success: false,
            Message: "暂无可用跑腿员",
        }, nil
    }

    // 3. 根据策略排序跑腿员
    sortedRunners := s.sortRunnersByPriority(runners, criteria.Priority, takeoutOrder)

    // 4. 尝试分配给最优跑腿员
    for _, runner := range sortedRunners {
        success, err := s.tryAssignToRunner(takeoutOrderID, runner.ID)
        if err != nil {
            continue // 尝试下一个跑腿员
        }
        if success {
            return &dto.RunnerAssignmentResult{
                Success:    true,
                RunnerID:   runner.ID,
                RunnerName: runner.RealName,
                Message:    "分配成功",
            }, nil
        }
    }

    return &dto.RunnerAssignmentResult{
        Success: false,
        Message: "分配失败，所有跑腿员都无法接单",
    }, nil
}

// sortRunnersByPriority 根据优先级策略排序跑腿员
func (s *OrderAssignmentService) sortRunnersByPriority(runners []dto.NearbyRunnerResponse, priority string, order *models.TakeoutOrder) []dto.NearbyRunnerResponse {
    switch priority {
    case "distance":
        // 按距离排序
        sort.Slice(runners, func(i, j int) bool {
            return runners[i].Distance < runners[j].Distance
        })
    case "score":
        // 按评分排序
        sort.Slice(runners, func(i, j int) bool {
            return runners[i].Score > runners[j].Score
        })
    case "speed":
        // 按接单速度排序（历史平均接单时间）
        sort.Slice(runners, func(i, j int) bool {
            return runners[i].AvgAcceptTime < runners[j].AvgAcceptTime
        })
    case "comprehensive":
        // 综合评分排序
        sort.Slice(runners, func(i, j int) bool {
            scoreI := s.calculateComprehensiveScore(runners[i], order)
            scoreJ := s.calculateComprehensiveScore(runners[j], order)
            return scoreI > scoreJ
        })
    }
    return runners
}

// calculateComprehensiveScore 计算综合评分
func (s *OrderAssignmentService) calculateComprehensiveScore(runner dto.NearbyRunnerResponse, order *models.TakeoutOrder) float64 {
    // 距离权重 40%
    distanceScore := math.Max(0, 1.0-runner.Distance/10.0) * 0.4
    
    // 评分权重 30%
    ratingScore := (runner.Score / 5.0) * 0.3
    
    // 接单速度权重 20%
    speedScore := math.Max(0, 1.0-runner.AvgAcceptTime/300.0) * 0.2
    
    // 当前订单数权重 10%（订单数越少越好）
    orderScore := math.Max(0, 1.0-float64(runner.CurrentOrders)/5.0) * 0.1
    
    return distanceScore + ratingScore + speedScore + orderScore
}
```

### 2.3 配送状态追踪

#### 状态追踪服务

```go
// DeliveryTrackingService 配送追踪服务
type DeliveryTrackingService struct {
    runnerOrderRepo repositories.RunnerOrderRepository
    takeoutOrderRepo repositories.TakeoutOrderRepository
    locationService services.LocationService
    notificationService services.NotificationService
}

// TrackingInfo 追踪信息
type TrackingInfo struct {
    OrderID           int64     `json:"order_id"`
    RunnerID          int64     `json:"runner_id"`
    RunnerName        string    `json:"runner_name"`
    RunnerPhone       string    `json:"runner_phone"`
    CurrentLocation   *Location `json:"current_location"`
    Status            int       `json:"status"`
    StatusDesc        string    `json:"status_desc"`
    EstimatedArrival  time.Time `json:"estimated_arrival"`
    Timeline          []TrackingEvent `json:"timeline"`
}

// TrackingEvent 追踪事件
type TrackingEvent struct {
    EventType   string    `json:"event_type"`
    Description string    `json:"description"`
    Location    *Location `json:"location,omitempty"`
    Timestamp   time.Time `json:"timestamp"`
}

// GetDeliveryTracking 获取配送追踪信息
func (s *DeliveryTrackingService) GetDeliveryTracking(takeoutOrderID int64) (*TrackingInfo, error) {
    // 1. 获取外卖订单
    takeoutOrder, err := s.takeoutOrderRepo.GetByID(takeoutOrderID)
    if err != nil {
        return nil, err
    }

    // 2. 获取关联的跑腿订单
    runnerOrder, err := s.runnerOrderRepo.GetByTakeoutOrderID(takeoutOrderID)
    if err != nil {
        return nil, err
    }

    // 3. 获取跑腿员信息
    runner, err := s.runnerRepo.GetByID(runnerOrder.RunnerID)
    if err != nil {
        return nil, err
    }

    // 4. 获取当前位置
    currentLocation, err := s.locationService.GetRunnerCurrentLocation(runnerOrder.RunnerID)
    if err != nil {
        currentLocation = nil // 位置信息可选
    }

    // 5. 计算预计到达时间
    estimatedArrival := s.calculateEstimatedArrival(runnerOrder, currentLocation)

    // 6. 构建时间线
    timeline := s.buildTimeline(runnerOrder)

    return &TrackingInfo{
        OrderID:          takeoutOrderID,
        RunnerID:         runnerOrder.RunnerID,
        RunnerName:       runner.RealName,
        RunnerPhone:      runner.Mobile,
        CurrentLocation:  currentLocation,
        Status:           runnerOrder.Status,
        StatusDesc:       s.getStatusDescription(runnerOrder.Status),
        EstimatedArrival: estimatedArrival,
        Timeline:         timeline,
    }, nil
}
```

## 3. 接口设计

### 3.1 跑腿员查询接口

#### 查询附近跑腿员

```go
// GET /api/v1/runner/nearby
// 供外卖模块调用，查询附近可用跑腿员

type NearbyRunnerRequest struct {
    Latitude         float64 `json:"latitude" binding:"required"`
    Longitude        float64 `json:"longitude" binding:"required"`
    Radius           float64 `json:"radius" binding:"required,min=0.1,max=50"`
    WorkingStatus    int     `json:"working_status,omitempty"`
    MinScore         float64 `json:"min_score,omitempty"`
    MaxCurrentOrders int     `json:"max_current_orders,omitempty"`
    Limit            int     `json:"limit,omitempty"`
}

type NearbyRunnerResponse struct {
    ID               int64   `json:"id"`
    RealName         string  `json:"real_name"`
    Mobile           string  `json:"mobile"`
    Distance         float64 `json:"distance"`
    Score            float64 `json:"score"`
    OrderCount       int     `json:"order_count"`
    WorkingStatus    int     `json:"working_status"`
    ServiceRadius    float64 `json:"service_radius"`
    CurrentOrders    int     `json:"current_orders"`
    AvgAcceptTime    float64 `json:"avg_accept_time"`
    LastActiveTime   time.Time `json:"last_active_time"`
}
```

#### 批量查询跑腿员状态

```go
// POST /api/v1/runner/batch-status
// 批量查询跑腿员状态，用于外卖模块批量检查

type BatchStatusRequest struct {
    RunnerIDs []int64 `json:"runner_ids" binding:"required,min=1,max=100"`
}

type BatchStatusResponse struct {
    Runners []RunnerStatusInfo `json:"runners"`
}

type RunnerStatusInfo struct {
    ID            int64     `json:"id"`
    WorkingStatus int       `json:"working_status"`
    OnlineStatus  int       `json:"online_status"`
    CurrentOrders int       `json:"current_orders"`
    LastActiveTime time.Time `json:"last_active_time"`
    Available     bool      `json:"available"`
}
```

### 3.2 订单分配接口

#### 创建跑腿订单

```go
// POST /api/v1/runner-order/create-from-takeout
// 从外卖订单创建跑腿订单

type CreateFromTakeoutRequest struct {
    TakeoutOrderID    int64   `json:"takeout_order_id" binding:"required"`
    PickupAddress     string  `json:"pickup_address" binding:"required"`
    PickupLat         float64 `json:"pickup_lat" binding:"required"`
    PickupLng         float64 `json:"pickup_lng" binding:"required"`
    PickupContact     string  `json:"pickup_contact" binding:"required"`
    PickupPhone       string  `json:"pickup_phone" binding:"required"`
    DeliveryAddress   string  `json:"delivery_address" binding:"required"`
    DeliveryLat       float64 `json:"delivery_lat" binding:"required"`
    DeliveryLng       float64 `json:"delivery_lng" binding:"required"`
    DeliveryContact   string  `json:"delivery_contact" binding:"required"`
    DeliveryPhone     string  `json:"delivery_phone" binding:"required"`
    GoodsDescription  string  `json:"goods_description"`
    GoodsWeight       float64 `json:"goods_weight"`
    DeliveryFee       float64 `json:"delivery_fee" binding:"required,min=0"`
    EstimatedPrepTime int     `json:"estimated_prep_time"` // 预计备餐时间(分钟)
    SpecialRequirements string `json:"special_requirements,omitempty"`
    AutoAssign        bool    `json:"auto_assign"`
    AssignmentCriteria *AssignmentCriteria `json:"assignment_criteria,omitempty"`
}

type CreateFromTakeoutResponse struct {
    RunnerOrderID   int64  `json:"runner_order_id"`
    RunnerOrderNo   string `json:"runner_order_no"`
    Status          int    `json:"status"`
    AssignmentResult *RunnerAssignmentResult `json:"assignment_result,omitempty"`
}
```

#### 手动分配跑腿员

```go
// POST /api/v1/runner-order/{order_id}/assign
// 手动分配跑腿员

type ManualAssignRequest struct {
    RunnerID int64  `json:"runner_id" binding:"required"`
    Reason   string `json:"reason,omitempty"`
}

type ManualAssignResponse struct {
    Success      bool      `json:"success"`
    Message      string    `json:"message"`
    AssignedTime time.Time `json:"assigned_time,omitempty"`
}
```

### 3.3 状态同步接口

#### 订单状态变更通知

```go
// POST /api/v1/integration/takeout/order-status-changed
// 跑腿员模块向外卖模块推送订单状态变更

type OrderStatusChangedNotification struct {
    TakeoutOrderID   int64     `json:"takeout_order_id"`
    RunnerOrderID    int64     `json:"runner_order_id"`
    RunnerID         int64     `json:"runner_id"`
    OldStatus        int       `json:"old_status"`
    NewStatus        int       `json:"new_status"`
    StatusDesc       string    `json:"status_desc"`
    Location         *Location `json:"location,omitempty"`
    EstimatedArrival *time.Time `json:"estimated_arrival,omitempty"`
    Timestamp        time.Time `json:"timestamp"`
    Remark           string    `json:"remark,omitempty"`
}
```

#### 跑腿员位置更新通知

```go
// POST /api/v1/integration/takeout/runner-location-updated
// 跑腿员位置更新通知

type RunnerLocationUpdatedNotification struct {
    RunnerID         int64     `json:"runner_id"`
    TakeoutOrderIDs  []int64   `json:"takeout_order_ids"`
    Location         Location  `json:"location"`
    Timestamp        time.Time `json:"timestamp"`
}

type Location struct {
    Latitude  float64 `json:"latitude"`
    Longitude float64 `json:"longitude"`
    Address   string  `json:"address,omitempty"`
}
```

## 4. 数据库设计

### 4.1 关联表设计

#### 外卖订单与跑腿订单关联表

```sql
CREATE TABLE `takeout_runner_order_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `takeout_order_id` bigint(20) NOT NULL COMMENT '外卖订单ID',
  `runner_order_id` bigint(20) NOT NULL COMMENT '跑腿订单ID',
  `runner_id` bigint(20) NOT NULL COMMENT '跑腿员ID',
  `assignment_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分配类型：1-自动分配 2-手动分配',
  `assignment_time` datetime NOT NULL COMMENT '分配时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '关联状态：1-有效 2-已取消',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_takeout_order` (`takeout_order_id`),
  KEY `idx_runner_order` (`runner_order_id`),
  KEY `idx_runner_id` (`runner_id`),
  KEY `idx_assignment_time` (`assignment_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外卖订单与跑腿订单关联表';
```

#### 配送状态同步记录表

```sql
CREATE TABLE `delivery_status_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `takeout_order_id` bigint(20) NOT NULL COMMENT '外卖订单ID',
  `runner_order_id` bigint(20) NOT NULL COMMENT '跑腿订单ID',
  `runner_id` bigint(20) NOT NULL COMMENT '跑腿员ID',
  `old_status` tinyint(4) NOT NULL COMMENT '原状态',
  `new_status` tinyint(4) NOT NULL COMMENT '新状态',
  `sync_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '同步类型：1-状态变更 2-位置更新',
  `sync_data` json DEFAULT NULL COMMENT '同步数据',
  `sync_result` tinyint(4) NOT NULL DEFAULT '0' COMMENT '同步结果：0-待同步 1-成功 2-失败',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_takeout_order` (`takeout_order_id`),
  KEY `idx_runner_order` (`runner_order_id`),
  KEY `idx_sync_result` (`sync_result`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配送状态同步记录表';
```

### 4.2 索引优化

#### 跑腿员位置查询索引

```sql
-- 为跑腿员表添加地理位置索引
ALTER TABLE `runners` ADD SPATIAL INDEX `idx_location` (`location_point`);

-- 为跑腿员表添加复合索引
ALTER TABLE `runners` ADD INDEX `idx_status_score` (`working_status`, `online_status`, `score`);
ALTER TABLE `runners` ADD INDEX `idx_area_status` (`area_codes`, `working_status`, `online_status`);
```

#### 订单查询索引

```sql
-- 跑腿订单表索引
ALTER TABLE `runner_orders` ADD INDEX `idx_runner_status` (`runner_id`, `status`);
ALTER TABLE `runner_orders` ADD INDEX `idx_takeout_order` (`takeout_order_id`);
ALTER TABLE `runner_orders` ADD INDEX `idx_status_time` (`status`, `created_at`);

-- 外卖订单表索引
ALTER TABLE `takeout_orders` ADD INDEX `idx_delivery_status` (`delivery_status`, `created_at`);
ALTER TABLE `takeout_orders` ADD INDEX `idx_merchant_status` (`merchant_id`, `status`);
```

### 4.3 数据一致性

#### 分布式事务处理

```go
// TransactionManager 事务管理器
type TransactionManager struct {
    db *gorm.DB
}

// CreateRunnerOrderWithTakeoutOrder 创建跑腿订单并关联外卖订单
func (tm *TransactionManager) CreateRunnerOrderWithTakeoutOrder(req *CreateFromTakeoutRequest) error {
    return tm.db.Transaction(func(tx *gorm.DB) error {
        // 1. 创建跑腿订单
        runnerOrder := &models.RunnerOrder{
            TakeoutOrderID:    req.TakeoutOrderID,
            PickupAddress:     req.PickupAddress,
            PickupLat:         req.PickupLat,
            PickupLng:         req.PickupLng,
            DeliveryAddress:   req.DeliveryAddress,
            DeliveryLat:       req.DeliveryLat,
            DeliveryLng:       req.DeliveryLng,
            DeliveryFee:       req.DeliveryFee,
            Status:            0, // 待接单
        }
        
        if err := tx.Create(runnerOrder).Error; err != nil {
            return err
        }

        // 2. 创建关联记录
        relation := &models.TakeoutRunnerOrderRelation{
            TakeoutOrderID:   req.TakeoutOrderID,
            RunnerOrderID:    runnerOrder.ID,
            AssignmentType:   1, // 自动分配
            AssignmentTime:   time.Now(),
            Status:           1, // 有效
        }
        
        if err := tx.Create(relation).Error; err != nil {
            return err
        }

        // 3. 更新外卖订单状态
        if err := tx.Model(&models.TakeoutOrder{}).Where("id = ?", req.TakeoutOrderID).Update("delivery_status", 1).Error; err != nil {
            return err
        }

        return nil
    })
}
```

## 5. 事件驱动机制

### 5.1 事件定义

```go
// 事件类型常量
const (
    EventTypeRunnerStatusChanged    = "runner.status.changed"
    EventTypeRunnerLocationUpdated  = "runner.location.updated"
    EventTypeOrderAssigned          = "order.assigned"
    EventTypeOrderAccepted          = "order.accepted"
    EventTypeOrderPickedUp          = "order.picked_up"
    EventTypeOrderDelivering        = "order.delivering"
    EventTypeOrderDelivered         = "order.delivered"
    EventTypeOrderCanceled          = "order.canceled"
)

// BaseEvent 基础事件
type BaseEvent struct {
    EventID   string                 `json:"event_id"`
    EventType string                 `json:"event_type"`
    Timestamp time.Time              `json:"timestamp"`
    Source    string                 `json:"source"`
    Data      map[string]interface{} `json:"data"`
}

// RunnerStatusChangedEvent 跑腿员状态变更事件
type RunnerStatusChangedEvent struct {
    BaseEvent
    RunnerID      int64 `json:"runner_id"`
    OldStatus     int   `json:"old_status"`
    NewStatus     int   `json:"new_status"`
    TakeoutOrders []int64 `json:"takeout_orders"`
}

// OrderStatusChangedEvent 订单状态变更事件
type OrderStatusChangedEvent struct {
    BaseEvent
    TakeoutOrderID int64     `json:"takeout_order_id"`
    RunnerOrderID  int64     `json:"runner_order_id"`
    RunnerID       int64     `json:"runner_id"`
    OldStatus      int       `json:"old_status"`
    NewStatus      int       `json:"new_status"`
    Location       *Location `json:"location,omitempty"`
}
```

### 5.2 事件处理

```go
// EventBus 事件总线
type EventBus struct {
    handlers map[string][]EventHandler
    mutex    sync.RWMutex
}

// EventHandler 事件处理器接口
type EventHandler interface {
    Handle(event BaseEvent) error
}

// TakeoutOrderStatusSyncHandler 外卖订单状态同步处理器
type TakeoutOrderStatusSyncHandler struct {
    takeoutService services.TakeoutService
    logger         *logrus.Logger
}

// Handle 处理订单状态变更事件
func (h *TakeoutOrderStatusSyncHandler) Handle(event BaseEvent) error {
    if event.EventType != EventTypeOrderStatusChanged {
        return nil
    }

    var orderEvent OrderStatusChangedEvent
    if err := mapstructure.Decode(event.Data, &orderEvent); err != nil {
        h.logger.WithError(err).Error("Failed to decode order status changed event")
        return err
    }

    // 同步状态到外卖订单
    deliveryStatus := h.mapRunnerStatusToDeliveryStatus(orderEvent.NewStatus)
    err := h.takeoutService.UpdateDeliveryStatus(orderEvent.TakeoutOrderID, deliveryStatus)
    if err != nil {
        h.logger.WithError(err).WithFields(logrus.Fields{
            "takeout_order_id": orderEvent.TakeoutOrderID,
            "runner_order_id":  orderEvent.RunnerOrderID,
            "new_status":       orderEvent.NewStatus,
        }).Error("Failed to sync delivery status to takeout order")
        return err
    }

    h.logger.WithFields(logrus.Fields{
        "takeout_order_id": orderEvent.TakeoutOrderID,
        "runner_order_id":  orderEvent.RunnerOrderID,
        "delivery_status":  deliveryStatus,
    }).Info("Successfully synced delivery status to takeout order")

    return nil
}

// 注册事件处理器
func (bus *EventBus) Subscribe(eventType string, handler EventHandler) {
    bus.mutex.Lock()
    defer bus.mutex.Unlock()
    
    if bus.handlers == nil {
        bus.handlers = make(map[string][]EventHandler)
    }
    
    bus.handlers[eventType] = append(bus.handlers[eventType], handler)
}

// 发布事件
func (bus *EventBus) Publish(eventType string, data interface{}) {
    event := BaseEvent{
        EventID:   uuid.New().String(),
        EventType: eventType,
        Timestamp: time.Now(),
        Source:    "runner-module",
        Data:      data,
    }
    
    bus.mutex.RLock()
    handlers := bus.handlers[eventType]
    bus.mutex.RUnlock()
    
    for _, handler := range handlers {
        go func(h EventHandler) {
            if err := h.Handle(event); err != nil {
                // 记录错误日志，可以考虑重试机制
                logrus.WithError(err).WithField("event_type", eventType).Error("Event handler failed")
            }
        }(handler)
    }
}
```

### 5.3 异常处理

```go
// RetryableEventHandler 可重试的事件处理器
type RetryableEventHandler struct {
    handler    EventHandler
    maxRetries int
    retryDelay time.Duration
    logger     *logrus.Logger
}

// Handle 处理事件，支持重试
func (h *RetryableEventHandler) Handle(event BaseEvent) error {
    var lastErr error
    
    for i := 0; i <= h.maxRetries; i++ {
        err := h.handler.Handle(event)
        if err == nil {
            if i > 0 {
                h.logger.WithFields(logrus.Fields{
                    "event_id":     event.EventID,
                    "event_type":   event.EventType,
                    "retry_count":  i,
                }).Info("Event handled successfully after retry")
            }
            return nil
        }
        
        lastErr = err
        
        if i < h.maxRetries {
            h.logger.WithError(err).WithFields(logrus.Fields{
                "event_id":    event.EventID,
                "event_type":  event.EventType,
                "retry_count": i + 1,
                "max_retries": h.maxRetries,
            }).Warn("Event handler failed, retrying...")
            
            time.Sleep(h.retryDelay * time.Duration(i+1)) // 指数退避
        }
    }
    
    h.logger.WithError(lastErr).WithFields(logrus.Fields{
        "event_id":    event.EventID,
        "event_type":  event.EventType,
        "max_retries": h.maxRetries,
    }).Error("Event handler failed after all retries")
    
    return lastErr
}
```

## 6. 性能优化

### 6.1 缓存策略

#### Redis缓存设计

```go
// CacheService 缓存服务
type CacheService struct {
    redis  *redis.Client
    logger *logrus.Logger
}

// 缓存键定义
const (
    CacheKeyRunnerStatus     = "runner:status:%d"           // 跑腿员状态
    CacheKeyRunnerLocation   = "runner:location:%d"         // 跑腿员位置
    CacheKeyNearbyRunners    = "nearby:runners:%s"          // 附近跑腿员
    CacheKeyOrderAssignment  = "order:assignment:%d"        // 订单分配结果
    CacheKeyDeliveryTracking = "delivery:tracking:%d"       // 配送追踪
)

// CacheRunnerStatus 缓存跑腿员状态
func (c *CacheService) CacheRunnerStatus(runnerID int64, status *dto.RunnerStatusInfo) error {
    key := fmt.Sprintf(CacheKeyRunnerStatus, runnerID)
    data, err := json.Marshal(status)
    if err != nil {
        return err
    }
    
    return c.redis.Set(context.Background(), key, data, 5*time.Minute).Err()
}

// GetRunnerStatus 获取缓存的跑腿员状态
func (c *CacheService) GetRunnerStatus(runnerID int64) (*dto.RunnerStatusInfo, error) {
    key := fmt.Sprintf(CacheKeyRunnerStatus, runnerID)
    data, err := c.redis.Get(context.Background(), key).Result()
    if err != nil {
        if err == redis.Nil {
            return nil, nil // 缓存未命中
        }
        return nil, err
    }
    
    var status dto.RunnerStatusInfo
    err = json.Unmarshal([]byte(data), &status)
    return &status, err
}

// CacheNearbyRunners 缓存附近跑腿员查询结果
func (c *CacheService) CacheNearbyRunners(lat, lng, radius float64, runners []dto.NearbyRunnerResponse) error {
    key := fmt.Sprintf(CacheKeyNearbyRunners, fmt.Sprintf("%.6f:%.6f:%.1f", lat, lng, radius))
    data, err := json.Marshal(runners)
    if err != nil {
        return err
    }
    
    return c.redis.Set(context.Background(), key, data, 2*time.Minute).Err()
}
```

#### 缓存更新策略

```go
// CacheUpdateService 缓存更新服务
type CacheUpdateService struct {
    cache  *CacheService
    logger *logrus.Logger
}

// OnRunnerStatusChanged 跑腿员状态变更时更新缓存
func (s *CacheUpdateService) OnRunnerStatusChanged(runnerID int64, newStatus int) {
    // 1. 删除跑腿员状态缓存
    key := fmt.Sprintf(CacheKeyRunnerStatus, runnerID)
    s.cache.redis.Del(context.Background(), key)
    
    // 2. 删除相关的附近跑腿员缓存
    pattern := fmt.Sprintf("%s*", "nearby:runners:")
    keys, err := s.cache.redis.Keys(context.Background(), pattern).Result()
    if err == nil && len(keys) > 0 {
        s.cache.redis.Del(context.Background(), keys...)
    }
    
    s.logger.WithFields(logrus.Fields{
        "runner_id":  runnerID,
        "new_status": newStatus,
    }).Debug("Updated cache after runner status changed")
}

// OnRunnerLocationChanged 跑腿员位置变更时更新缓存
func (s *CacheUpdateService) OnRunnerLocationChanged(runnerID int64, location *Location) {
    // 1. 更新跑腿员位置缓存
    key := fmt.Sprintf(CacheKeyRunnerLocation, runnerID)
    data, _ := json.Marshal(location)
    s.cache.redis.Set(context.Background(), key, data, 10*time.Minute)
    
    // 2. 删除附近跑腿员缓存（位置变更可能影响查询结果）
    pattern := fmt.Sprintf("%s*", "nearby:runners:")
    keys, err := s.cache.redis.Keys(context.Background(), pattern).Result()
    if err == nil && len(keys) > 0 {
        s.cache.redis.Del(context.Background(), keys...)
    }
}
```

### 6.2 查询优化

#### 地理位置查询优化

```go
// GeoQueryService 地理位置查询服务
type GeoQueryService struct {
    db     *gorm.DB
    redis  *redis.Client
    logger *logrus.Logger
}

// GetNearbyRunnersOptimized 优化的附近跑腿员查询
func (s *GeoQueryService) GetNearbyRunnersOptimized(req *dto.NearbyRunnerRequest) ([]dto.NearbyRunnerResponse, error) {
    // 1. 尝试从缓存获取
    cacheKey := s.buildCacheKey(req)
    if cached, err := s.getFromCache(cacheKey); err == nil && cached != nil {
        return cached, nil
    }
    
    // 2. 使用空间索引查询
    query := s.db.Table("runners r").
        Select(`r.id, r.real_name, r.mobile, r.score, r.order_count, 
                r.working_status, r.service_radius, r.current_orders,
                ST_Distance_Sphere(r.location_point, ST_GeomFromText(?, 4326)) / 1000 as distance`,
            fmt.Sprintf("POINT(%f %f)", req.Longitude, req.Latitude)).
        Where("r.online_status = 1").
        Where("ST_Distance_Sphere(r.location_point, ST_GeomFromText(?, 4326)) / 1000 <= ?",
            fmt.Sprintf("POINT(%f %f)", req.Longitude, req.Latitude), req.Radius)
    
    // 3. 添加筛选条件
    if req.WorkingStatus > 0 {
        query = query.Where("r.working_status = ?", req.WorkingStatus)
    }
    if req.MinScore > 0 {
        query = query.Where("r.score >= ?", req.MinScore)
    }
    if req.MaxCurrentOrders > 0 {
        query = query.Where("r.current_orders <= ?", req.MaxCurrentOrders)
    }
    
    // 4. 排序和限制
    query = query.Order("distance ASC")
    if req.Limit > 0 {
        query = query.Limit(req.Limit)
    }
    
    var results []dto.NearbyRunnerResponse
    err := query.Scan(&results).Error
    if err != nil {
        return nil, err
    }
    
    // 5. 缓存结果
    s.cacheResults(cacheKey, results)
    
    return results, nil
}

// buildCacheKey 构建缓存键
func (s *GeoQueryService) buildCacheKey(req *dto.NearbyRunnerRequest) string {
    hash := md5.Sum([]byte(fmt.Sprintf("%f:%f:%f:%d:%f:%d:%d", 
        req.Latitude, req.Longitude, req.Radius, 
        req.WorkingStatus, req.MinScore, req.MaxCurrentOrders, req.Limit)))
    return fmt.Sprintf("nearby:runners:%x", hash)
}
```

#### 批量查询优化

```go
// BatchQueryService 批量查询服务
type BatchQueryService struct {
    db    *gorm.DB
    cache *CacheService
}

// GetRunnerStatusBatch 批量获取跑腿员状态
func (s *BatchQueryService) GetRunnerStatusBatch(runnerIDs []int64) (map[int64]*dto.RunnerStatusInfo, error) {
    result := make(map[int64]*dto.RunnerStatusInfo)
    var uncachedIDs []int64
    
    // 1. 尝试从缓存批量获取
    for _, id := range runnerIDs {
        if status, err := s.cache.GetRunnerStatus(id); err == nil && status != nil {
            result[id] = status
        } else {
            uncachedIDs = append(uncachedIDs, id)
        }
    }
    
    // 2. 查询未缓存的数据
    if len(uncachedIDs) > 0 {
        var runners []models.Runner
        err := s.db.Where("id IN ?", uncachedIDs).Find(&runners).Error
        if err != nil {
            return nil, err
        }
        
        // 3. 转换并缓存结果
        for _, runner := range runners {
            status := &dto.RunnerStatusInfo{
                ID:             runner.ID,
                WorkingStatus:  runner.WorkingStatus,
                OnlineStatus:   runner.OnlineStatus,
                CurrentOrders:  runner.CurrentOrders,
                LastActiveTime: runner.LastActiveTime,
                Available:      runner.WorkingStatus == 1 && runner.OnlineStatus == 1,
            }
            result[runner.ID] = status
            
            // 异步缓存
            go s.cache.CacheRunnerStatus(runner.ID, status)
        }
    }
    
    return result, nil
}
```

### 6.3 负载均衡

#### 跑腿员负载均衡

```go
// LoadBalancer 负载均衡器
type LoadBalancer struct {
    runnerRepo repositories.RunnerRepository
    logger     *logrus.Logger
}

// BalanceOrderAssignment 平衡订单分配
func (lb *LoadBalancer) BalanceOrderAssignment(candidates []dto.NearbyRunnerResponse, order *models.TakeoutOrder) []dto.NearbyRunnerResponse {
    // 1. 计算每个跑腿员的负载分数
    for i := range candidates {
        candidates[i].LoadScore = lb.calculateLoadScore(&candidates[i], order)
    }
    
    // 2. 按负载分数排序（分数越低越优先）
    sort.Slice(candidates, func(i, j int) bool {
        return candidates[i].LoadScore < candidates[j].LoadScore
    })
    
    return candidates
}

// calculateLoadScore 计算负载分数
func (lb *LoadBalancer) calculateLoadScore(runner *dto.NearbyRunnerResponse, order *models.TakeoutOrder) float64 {
    // 基础分数：当前订单数 * 权重
    baseScore := float64(runner.CurrentOrders) * 10.0
    
    // 距离分数：距离越远分数越高
    distanceScore := runner.Distance * 2.0
    
    // 评分分数：评分越低分数越高
    ratingScore := (5.0 - runner.Score) * 5.0
    
    // 历史接单速度分数
    speedScore := runner.AvgAcceptTime / 60.0 // 转换为分钟
    
    // 综合负载分数
    totalScore := baseScore + distanceScore + ratingScore + speedScore
    
    return totalScore
}

// GetOptimalRunner 获取最优跑腿员
func (lb *LoadBalancer) GetOptimalRunner(candidates []dto.NearbyRunnerResponse, order *models.TakeoutOrder) *dto.NearbyRunnerResponse {
    if len(candidates) == 0 {
        return nil
    }
    
    balanced := lb.BalanceOrderAssignment(candidates, order)
    return &balanced[0]
}
```

#### 系统负载监控

```go
// SystemLoadMonitor 系统负载监控
type SystemLoadMonitor struct {
    runnerRepo repositories.RunnerRepository
    orderRepo  repositories.RunnerOrderRepository
    redis      *redis.Client
    logger     *logrus.Logger
}

// LoadMetrics 负载指标
type LoadMetrics struct {
    TotalRunners      int     `json:"total_runners"`
    OnlineRunners     int     `json:"online_runners"`
    AvailableRunners  int     `json:"available_runners"`
    ActiveOrders      int     `json:"active_orders"`
    AvgOrdersPerRunner float64 `json:"avg_orders_per_runner"`
    SystemLoadLevel   string  `json:"system_load_level"`
}

// GetSystemLoadMetrics 获取系统负载指标
func (m *SystemLoadMonitor) GetSystemLoadMetrics() (*LoadMetrics, error) {
    // 1. 统计跑腿员数量
    var totalRunners, onlineRunners, availableRunners int64
    
    m.runnerRepo.Count(&totalRunners)
    m.runnerRepo.CountByCondition(map[string]interface{}{"online_status": 1}, &onlineRunners)
    m.runnerRepo.CountByCondition(map[string]interface{}{
        "online_status":  1,
        "working_status": 1,
    }, &availableRunners)
    
    // 2. 统计活跃订单数
    var activeOrders int64
    m.orderRepo.CountByCondition(map[string]interface{}{
        "status": []int{0, 1, 2, 3}, // 待接单、已接单、取货中、配送中
    }, &activeOrders)
    
    // 3. 计算平均订单数
    var avgOrdersPerRunner float64
    if onlineRunners > 0 {
        avgOrdersPerRunner = float64(activeOrders) / float64(onlineRunners)
    }
    
    // 4. 判断系统负载等级
    loadLevel := m.determineLoadLevel(availableRunners, activeOrders, avgOrdersPerRunner)
    
    metrics := &LoadMetrics{
        TotalRunners:       int(totalRunners),
        OnlineRunners:      int(onlineRunners),
        AvailableRunners:   int(availableRunners),
        ActiveOrders:       int(activeOrders),
        AvgOrdersPerRunner: avgOrdersPerRunner,
        SystemLoadLevel:    loadLevel,
    }
    
    // 5. 缓存指标
    m.cacheMetrics(metrics)
    
    return metrics, nil
}

// determineLoadLevel 判断负载等级
func (m *SystemLoadMonitor) determineLoadLevel(availableRunners, activeOrders int64, avgOrders float64) string {
    if availableRunners == 0 {
        return "CRITICAL" // 无可用跑腿员
    }
    
    if avgOrders >= 3.0 {
        return "HIGH" // 高负载
    } else if avgOrders >= 2.0 {
        return "MEDIUM" // 中等负载
    } else {
        return "LOW" // 低负载
    }
}
```

## 总结

本文档详细描述了跑腿员模块与外卖模块的集成方案，包括：

1. **架构设计**：采用事件驱动的微服务架构，确保模块间的松耦合
2. **接口设计**：提供完整的API接口，支持跑腿员查询、订单分配、状态同步等功能
3. **数据设计**：设计了关联表和索引，确保数据一致性和查询性能
4. **事件机制**：实现了完整的事件驱动机制，支持异步处理和重试
5. **性能优化**：通过缓存、查询优化和负载均衡等手段提升系统性能

该集成方案能够有效支撑外卖平台的配送业务，为用户提供高效、可靠的配送服务。