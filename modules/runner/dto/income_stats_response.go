/**
 * income_stats_response.go
 * 跑腿员收入统计响应DTO
 *
 * 本文件定义了跑腿员收入统计的响应数据结构
 * 用于向前端返回跑腿员不同时间段（今日、本周、本月、累计）的收入统计数据
 * 访问路径：/v1/runner/secured/income-stats
 * 请求方式：GET
 * 需要认证：是
 */

package dto

// RunnerIncomeStatsResponse 跑腿员收入统计响应
type RunnerIncomeStatsResponse struct {
	Today float64 `json:"today"`  // 今日收入(元)
	Week  float64 `json:"week"`   // 本周收入(元)
	Month float64 `json:"month"`  // 本月收入(元)
	Total float64 `json:"total"`  // 累计收入(元)
}
