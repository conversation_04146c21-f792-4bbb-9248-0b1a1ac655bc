/**
 * order_stats_dto.go
 * 跑腿订单统计数据传输对象
 *
 * 本文件定义了跑腿订单统计相关的数据结构
 * 用于向前端返回订单统计数据
 * 访问路径：/api/v1/runner-order/stats
 * 请求方式：GET
 * 需要认证：是
 */

package dto

// OrderStatsResponse 跑腿订单统计响应
type OrderStatsResponse struct {
	Total           int `json:"total"`            // 总订单数
	Pending         int `json:"pending"`          // 待接单数量
	Accepted        int `json:"accepted"`         // 已接单数量
	PickedUp        int `json:"picked_up"`        // 已取货数量
	InDelivery      int `json:"in_delivery"`      // 配送中数量
	Completed       int `json:"completed"`        // 已完成数量
	Cancelled       int `json:"cancelled"`        // 已取消数量
	TodayOrders     int `json:"today_orders"`     // 今日订单数
	TodayCompleted  int `json:"today_completed"`  // 今日完成订单数
	TodayCancelled  int `json:"today_cancelled"`  // 今日取消订单数
	WeekOrders      int `json:"week_orders"`      // 本周订单数
	MonthOrders     int `json:"month_orders"`     // 本月订单数
	CompletionRate  float64 `json:"completion_rate"`  // 完成率
	CancellationRate float64 `json:"cancellation_rate"` // 取消率
	AverageDeliveryTime int `json:"average_delivery_time"` // 平均配送时间(分钟)
}
