/**
 * order_stats_repo.go
 * 跑腿订单统计数据传输对象（仓储层）
 *
 * 本文件定义了跑腿订单统计相关的仓储层数据结构
 * 用于订单统计查询逻辑
 */

package dto

// OrderStatsRepo 跑腿订单统计仓储数据
type OrderStatsRepo struct {
	TotalOrders          int  // 总订单数
	PendingOrders        int  // 待接单数量
	AcceptedOrders       int  // 已接单数量
	PickedUpOrders       int  // 已取货数量
	InDeliveryOrders     int  // 配送中数量
	CompletedOrders      int  // 已完成数量
	CancelledOrders      int  // 已取消数量
	TodayOrders          int  // 今日订单数
	TodayCompletedOrders int  // 今日完成订单数
	TodayCancelledOrders int  // 今日取消订单数
	WeekOrders           int  // 本周订单数
	MonthOrders          int  // 本月订单数
}
