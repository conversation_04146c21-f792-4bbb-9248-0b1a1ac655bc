/**
 * runner_dto.go
 * 跑腿员数据传输对象
 *
 * 本文件定义了跑腿员模块的数据传输对象，用于前后端数据交换
 */

package dto

import (
	"time"
)

// RunnerRegisterRequest 跑腿员注册请求
type RunnerRegisterRequest struct {
	RealName       string  `json:"real_name" valid:"Required"`         // 真实姓名
	IDCardNumber   string  `json:"id_card_number" valid:"Required"`    // 身份证号码
	IDCardFrontPic string  `json:"id_card_front_pic" valid:"Required"` // 身份证正面照
	IDCardBackPic  string  `json:"id_card_back_pic" valid:"Required"`  // 身份证背面照
	FacePic        string  `json:"face_pic" valid:"Required"`          // 人脸照片
	Mobile         string  `json:"mobile" valid:"Required;Mobile"`     // 手机号码
	AreaCodes      string  `json:"area_codes"`                         // 服务区域编码，逗号分隔
	ServiceRadius  float64 `json:"service_radius"`                     // 服务半径(km)
}

// RunnerApplyStatusResponse 跑腿员申请状态响应
type RunnerApplyStatusResponse struct {
	ID           int64     `json:"id"`            // 申请ID
	UserID       int64     `json:"user_id"`       // 用户ID
	RealName     string    `json:"real_name"`     // 真实姓名
	Mobile       string    `json:"mobile"`        // 手机号码
	Status       int       `json:"status"`        // 申请状态：0-待审核 1-审核通过 2-审核拒绝
	StatusDesc   string    `json:"status_desc"`   // 状态描述
	RejectReason string    `json:"reject_reason"` // 拒绝原因
	CreateTime   time.Time `json:"create_time"`   // 申请时间
	AuditTime    time.Time `json:"audit_time"`    // 审核时间
}

// RunnerLoginRequest 跑腿员登录请求
type RunnerLoginRequest struct {
	Username string `json:"username" valid:"Required"` // 用户名
	Password string `json:"password" valid:"Required"` // 密码
}

// RunnerResponse 跑腿员基本信息响应
type RunnerResponse struct {
	ID                int64   `json:"id"`                  // 跑腿员ID
	UserID            int64   `json:"user_id"`             // 关联的用户ID
	RealName          string  `json:"real_name"`           // 真实姓名
	Mobile            string  `json:"mobile"`              // 手机号码
	Status            int     `json:"status"`              // 状态
	StatusDesc        string  `json:"status_desc"`         // 状态描述
	CurrentLocation   string  `json:"current_location"`    // 当前位置
	IsOnline          bool    `json:"is_online"`           // 是否在线
	WorkingStatus     int     `json:"working_status"`      // 工作状态
	WorkingStatusDesc string  `json:"working_status_desc"` // 工作状态描述
	Score             float64 `json:"score"`               // 评分
	OrderCount        int     `json:"order_count"`         // 订单数量
	SuccessCount      int     `json:"success_count"`       // 成功订单数
	CancelCount       int     `json:"cancel_count"`        // 取消订单数
	Wallet            float64 `json:"wallet"`              // 钱包余额
	ServiceRadius     float64 `json:"service_radius"`      // 服务半径(km)
	JoinDays          int     `json:"join_days"`           // 加入天数
}

// RunnerDetailResponse 跑腿员详细信息响应
type RunnerDetailResponse struct {
	RunnerResponse
	IDCardNumber   string    `json:"id_card_number"`    // 身份证号码
	IDCardFrontPic string    `json:"id_card_front_pic"` // 身份证正面照
	IDCardBackPic  string    `json:"id_card_back_pic"`  // 身份证背面照
	FacePic        string    `json:"face_pic"`          // 人脸照片
	Latitude       float64   `json:"latitude"`          // 纬度
	Longitude      float64   `json:"longitude"`         // 经度
	AreaCodes      string    `json:"area_codes"`        // 服务区域编码
	JoinTime       time.Time `json:"join_time"`         // 加入时间
	LastLoginTime  time.Time `json:"last_login_time"`   // 最后登录时间
	LastOnlineTime time.Time `json:"last_online_time"`  // 最后在线时间
	Remark         string    `json:"remark"`            // 备注
	FrontendRemark string    `json:"frontend_remark"`   // 前端显示备注
}

// UpdateRunnerStatusRequest 更新跑腿员状态请求
type UpdateRunnerStatusRequest struct {
	Status int    `json:"status" description:"状态"` // 状态
	Reason string `json:"reason" description:"原因"` // 原因
}

// UpdateRunnerLocationRequest 更新跑腿员位置请求
type UpdateRunnerLocationRequest struct {
	Latitude  float64 `json:"latitude" valid:"Required"`  // 纬度
	Longitude float64 `json:"longitude" valid:"Required"` // 经度
	Address   string  `json:"address"`                    // 地址描述
}

// UpdateOnlineStatusRequest 更新跑腿员在线状态请求
type UpdateOnlineStatusRequest struct {
	IsOnline bool `json:"is_online"` // 是否在线
}

// CreateRunnerOrderRequest 创建跑腿订单请求
type CreateRunnerOrderRequest struct {
	OrderType             int     `json:"order_type" valid:"Required"`            // 订单类型
	OrderNo               string  `json:"order_no"`                               // 订单号，与主订单关联用
	DeliveryFee           float64 `json:"delivery_fee"`                           // 配送费用
	ServiceFee            float64 `json:"service_fee"`                            // 服务费用
	TipAmount             float64 `json:"tip_amount"`                             // 小费金额
	Distance              float64 `json:"distance"`                               // 配送距离(km)
	EstimateTime          int     `json:"estimate_time"`                          // 预计配送时间(分钟)
	PickupAddress         string  `json:"pickup_address" valid:"Required"`        // 取货地址
	PickupAddressDetail   string  `json:"pickup_address_detail"`                  // 取货详细地址
	PickupLat             float64 `json:"pickup_lat" valid:"Required"`            // 取货地址纬度
	PickupLng             float64 `json:"pickup_lng" valid:"Required"`            // 取货地址经度
	PickupContact         string  `json:"pickup_contact" valid:"Required"`        // 取货联系人
	PickupPhone           string  `json:"pickup_phone" valid:"Required;Mobile"`   // 取货联系电话
	DeliveryAddress       string  `json:"delivery_address" valid:"Required"`      // 送货地址
	DeliveryAddressDetail string  `json:"delivery_address_detail"`                // 送货详细地址
	DeliveryLat           float64 `json:"delivery_lat" valid:"Required"`          // 送货地址纬度
	DeliveryLng           float64 `json:"delivery_lng" valid:"Required"`          // 送货地址经度
	DeliveryContact       string  `json:"delivery_contact" valid:"Required"`      // 送货联系人
	DeliveryPhone         string  `json:"delivery_phone" valid:"Required;Mobile"` // 送货联系电话
	Goods                 string  `json:"goods" valid:"Required"`                 // 商品描述
	GoodsWeight           float64 `json:"goods_weight"`                           // 商品重量(kg)
	GoodsValue            float64 `json:"goods_value"`                            // 商品价值
	Remark                string  `json:"remark"`                                 // 备注
	PayMethod             int     `json:"pay_method" valid:"Required"`            // 支付方式
}

// RunnerOrderResponse 跑腿订单响应
type RunnerOrderResponse struct {
	ID                    int64     `json:"id"`                      // 订单ID
	OrderNo               string    `json:"order_no"`                // 订单编号
	OrderType             int       `json:"order_type"`              // 订单类型
	OrderTypeDesc         string    `json:"order_type_desc"`         // 订单类型描述
	UserID                int64     `json:"user_id"`                 // 用户ID
	RunnerID              int64     `json:"runner_id"`               // 跑腿员ID
	RunnerName            string    `json:"runner_name"`             // 跑腿员姓名
	RunnerMobile          string    `json:"runner_mobile"`           // 跑腿员手机号
	Status                int       `json:"status"`                  // 订单状态
	StatusDesc            string    `json:"status_desc"`             // 订单状态描述
	PayStatus             int       `json:"pay_status"`              // 支付状态
	PayStatusDesc         string    `json:"pay_status_desc"`         // 支付状态描述
	PayMethod             int       `json:"pay_method"`              // 支付方式
	PayMethodDesc         string    `json:"pay_method_desc"`         // 支付方式描述
	TotalAmount           float64   `json:"total_amount"`            // 订单总金额
	DeliveryFee           float64   `json:"delivery_fee"`            // 配送费用
	ServiceFee            float64   `json:"service_fee"`             // 服务费用
	TipAmount             float64   `json:"tip_amount"`              // 小费金额
	Distance              float64   `json:"distance"`                // 配送距离(km)
	EstimateTime          int       `json:"estimate_time"`           // 预计配送时间(分钟)
	PickupAddress         string    `json:"pickup_address"`          // 取货地址
	PickupAddressDetail   string    `json:"pickup_address_detail"`   // 取货详细地址
	PickupLatitude        float64   `json:"pickup_latitude"`         // 取货地址纬度
	PickupLongitude       float64   `json:"pickup_longitude"`        // 取货地址经度
	PickupContact         string    `json:"pickup_contact"`          // 取货联系人
	PickupPhone           string    `json:"pickup_phone"`            // 取货联系电话
	DeliveryAddress       string    `json:"delivery_address"`        // 送货地址
	DeliveryAddressDetail string    `json:"delivery_address_detail"` // 送货详细地址
	DeliveryLatitude      float64   `json:"delivery_latitude"`       // 送货地址纬度
	DeliveryLongitude     float64   `json:"delivery_longitude"`      // 送货地址经度
	DeliveryContact       string    `json:"delivery_contact"`        // 送货联系人
	DeliveryPhone         string    `json:"delivery_phone"`          // 送货联系电话
	Goods                 string    `json:"goods"`                   // 商品描述
	GoodsWeight           float64   `json:"goods_weight"`            // 商品重量(kg)
	GoodsValue            float64   `json:"goods_value"`             // 商品价值
	Remark                string    `json:"remark"`                  // 备注
	AcceptTime            time.Time `json:"accept_time"`             // 接单时间
	PickupTime            time.Time `json:"pickup_time"`             // 取货时间
	DeliveryTime          time.Time `json:"delivery_time"`           // 送达时间
	CancelTime            time.Time `json:"cancel_time"`             // 取消时间
	CancelReason          string    `json:"cancel_reason"`           // 取消原因
	CancelUserType        int       `json:"cancel_user_type"`        // 取消用户类型
	ScoreByUser           int       `json:"score_by_user"`           // 用户评分
	ScoreByRunner         int       `json:"score_by_runner"`         // 跑腿员评分
	CommentByUser         string    `json:"comment_by_user"`         // 用户评价
	CommentByRunner       string    `json:"comment_by_runner"`       // 跑腿员评价
	CreateTime            time.Time `json:"create_time"`             // 创建时间
}

// RunnerOrderListRequest 跑腿订单列表请求
type RunnerOrderListRequest struct {
	Status   int `json:"status"`   // 订单状态
	Page     int `json:"page"`     // 页码
	PageSize int `json:"pageSize"` // 每页数量
}

// RunnerOrderListResponse 跑腿订单列表响应
type RunnerOrderListResponse struct {
	Total int64                 `json:"total"` // 总数
	List  []RunnerOrderResponse `json:"list"`  // 列表数据
}

// AcceptOrderRequest 接单请求
type AcceptOrderRequest struct {
	OrderID int64 `json:"order_id" valid:"Required"` // 订单ID
}

// CancelOrderRequest 取消订单请求
type CancelOrderRequest struct {
	OrderID int64  `json:"order_id" valid:"Required"` // 订单ID
	Reason  string `json:"reason" valid:"Required"`   // 取消原因
}

// PickupOrderRequest 取货请求
type PickupOrderRequest struct {
	OrderID int64 `json:"order_id" valid:"Required"` // 订单ID
}

// CompleteOrderRequest 完成订单请求
type CompleteOrderRequest struct {
	OrderID int64 `json:"order_id" valid:"Required"` // 订单ID
}

// StartDeliveryRequest 开始配送请求
type StartDeliveryRequest struct {
	OrderID int64 `json:"order_id" valid:"Required"` // 订单ID
}

// RateOrderRequest 评价订单请求
type RateOrderRequest struct {
	OrderID int64  `json:"order_id" valid:"Required"` // 订单ID
	Score   int    `json:"score" valid:"Required"`    // 评分
	Comment string `json:"comment"`                   // 评价内容
}

// RunnerIncomeResponse 跑腿员收入响应
type RunnerIncomeResponse struct {
	TotalIncome       float64 `json:"total_income"`       // 总收入
	AvailableBalance  float64 `json:"available_balance"`  // 可用余额
	WithdrawingAmount float64 `json:"withdrawing_amount"` // 提现中金额
	TodayIncome       float64 `json:"today_income"`       // 今日收入
	YesterdayIncome   float64 `json:"yesterday_income"`   // 昨日收入
	MonthIncome       float64 `json:"month_income"`       // 本月收入
	TotalOrderCount   int     `json:"total_order_count"`  // 总单数
}

// RunnerIncomeLogResponse 跑腿员收入记录响应
type RunnerIncomeLogResponse struct {
	ID          int64     `json:"id"`          // 记录ID
	OrderID     int64     `json:"order_id"`    // 订单ID
	OrderNo     string    `json:"order_no"`    // 订单编号
	Amount      float64   `json:"amount"`      // 金额
	Type        int       `json:"type"`        // 类型
	TypeDesc    string    `json:"type_desc"`   // 类型描述
	Status      int       `json:"status"`      // 状态
	StatusDesc  string    `json:"status_desc"` // 状态描述
	Description string    `json:"description"` // 描述
	CreateTime  time.Time `json:"create_time"` // 创建时间
}

// WithdrawalRequest 提现请求
type WithdrawalRequest struct {
	Amount           float64 `json:"amount" valid:"Required"`            // 提现金额
	WithdrawalMethod int     `json:"withdrawal_method" valid:"Required"` // 提现方式
	AccountName      string  `json:"account_name" valid:"Required"`      // 账户名称
	AccountNo        string  `json:"account_no" valid:"Required"`        // 账号
	BankName         string  `json:"bank_name"`                          // 银行名称
}

// WithdrawalResponse 提现响应
type WithdrawalResponse struct {
	ID                   int64     `json:"id"`                     // 记录ID
	WithdrawalNo         string    `json:"withdrawal_no"`          // 提现编号
	Amount               float64   `json:"amount"`                 // 提现金额
	Status               int       `json:"status"`                 // 状态
	StatusDesc           string    `json:"status_desc"`            // 状态描述
	WithdrawalMethod     int       `json:"withdrawal_method"`      // 提现方式
	WithdrawalMethodDesc string    `json:"withdrawal_method_desc"` // 提现方式描述
	AccountName          string    `json:"account_name"`           // 账户名称
	AccountNo            string    `json:"account_no"`             // 账号
	BankName             string    `json:"bank_name"`              // 银行名称
	Remark               string    `json:"remark"`                 // 备注
	CreateTime           time.Time `json:"create_time"`            // 创建时间
	HandleTime           time.Time `json:"handle_time"`            // 处理时间
}

// RunnerStatisticsResponse 跑腿员统计响应
type RunnerStatisticsResponse struct {
	TotalOrderCount     int     `json:"total_order_count"`     // 总订单数
	CompletedOrderCount int     `json:"completed_order_count"` // 已完成订单数
	CanceledOrderCount  int     `json:"canceled_order_count"`  // 已取消订单数
	CompletionRate      float64 `json:"completion_rate"`       // 完成率
	AverageDeliveryTime int     `json:"average_delivery_time"` // 平均配送时间(分钟)
	TotalIncome         float64 `json:"total_income"`          // 总收入
	TodayOrderCount     int     `json:"today_order_count"`     // 今日订单数
	TodayIncome         float64 `json:"today_income"`          // 今日收入
	WeekOrderCount      int     `json:"week_order_count"`      // 本周订单数
	WeekIncome          float64 `json:"week_income"`           // 本周收入
	MonthOrderCount     int     `json:"month_order_count"`     // 本月订单数
	MonthIncome         float64 `json:"month_income"`          // 本月收入
}

// RunnerTodayStatsResponse 跑腿员今日统计响应
type RunnerTodayStatsResponse struct {
	CompletedOrderCount int     `json:"completed_order_count"` // 今日完成订单数
	WorkingHours        float64 `json:"working_hours"`        // 今日工作时长(小时)
	DeliveryDistance    float64 `json:"delivery_distance"`    // 今日配送距离(公里)
	AverageRating       float64 `json:"average_rating"`       // 今日平均评分
	TotalIncome         float64 `json:"total_income"`         // 今日总收入
	OnlineTime          float64 `json:"online_time"`          // 今日在线时长(小时)
	OrderAcceptRate     float64 `json:"order_accept_rate"`    // 今日接单率
	DeliverySuccessRate float64 `json:"delivery_success_rate"` // 今日配送成功率
}

// NearbyRunnerRequest 附近跑腿员请求
type NearbyRunnerRequest struct {
	Latitude  float64 `json:"latitude" valid:"Required"`  // 纬度
	Longitude float64 `json:"longitude" valid:"Required"` // 经度
	Radius    float64 `json:"radius"`                     // 半径(km)
	Limit     int     `json:"limit"`                      // 限制数量
}

// NearbyRunnerResponse 附近跑腿员响应
type NearbyRunnerResponse struct {
	ID            int64   `json:"id"`             // 跑腿员ID
	RealName      string  `json:"real_name"`      // 姓名
	Distance      float64 `json:"distance"`       // 距离(km)
	Score         float64 `json:"score"`          // 评分
	OrderCount    int     `json:"order_count"`    // 订单数
	WorkingStatus int     `json:"working_status"` // 工作状态
}

// DeliveryFeeCalculateRequest 配送费计算请求
type DeliveryFeeCalculateRequest struct {
	PickupLat   float64 `json:"pickup_lat" valid:"Required"`   // 取货地址纬度
	PickupLng   float64 `json:"pickup_lng" valid:"Required"`   // 取货地址经度
	DeliveryLat float64 `json:"delivery_lat" valid:"Required"` // 送货地址纬度
	DeliveryLng float64 `json:"delivery_lng" valid:"Required"` // 送货地址经度
	GoodsWeight float64 `json:"goods_weight"`                  // 商品重量(kg)
	OrderType   int     `json:"order_type" valid:"Required"`   // 订单类型
}

// DeliveryFeeCalculateResponse 配送费计算响应
type DeliveryFeeCalculateResponse struct {
	Distance         float64 `json:"distance"`           // 距离(km)
	BaseDeliveryFee  float64 `json:"base_delivery_fee"`  // 基础配送费
	WeightFee        float64 `json:"weight_fee"`         // 重量费用
	TotalDeliveryFee float64 `json:"total_delivery_fee"` // 总配送费
	ServiceFee       float64 `json:"service_fee"`        // 服务费
	EstimateTime     int     `json:"estimate_time"`      // 预计配送时间(分钟)
}

// AdminRunnerListRequest 管理员获取跑腿员列表请求
type AdminRunnerListRequest struct {
	Page     int    `json:"page"`     // 页码
	PageSize int    `json:"pageSize"` // 每页数量
	Status   int    `json:"status"`   // 状态筛选：-1全部 0待审核 1审核通过 2审核拒绝 3暂停服务 4黑名单
	Keyword  string `json:"keyword"`  // 关键词搜索（姓名、手机号）
}

// AdminRunnerListResponse 管理员跑腿员列表响应
type AdminRunnerListResponse struct {
	List       []AdminRunnerItem `json:"list"`        // 跑腿员列表
	Total      int64             `json:"total"`       // 总数
	Page       int               `json:"page"`        // 当前页码
	PageSize   int               `json:"pageSize"`    // 每页数量
	TotalPages int               `json:"totalPages"`  // 总页数
}

// AdminRunnerItem 管理员跑腿员列表项
type AdminRunnerItem struct {
	ID                int64     `json:"id"`                  // 跑腿员ID
	UserID            int64     `json:"user_id"`             // 关联的用户ID
	RealName          string    `json:"real_name"`           // 真实姓名
	Mobile            string    `json:"mobile"`              // 手机号码
	Status            int       `json:"status"`              // 状态
	StatusDesc        string    `json:"status_desc"`         // 状态描述
	CurrentLocation   string    `json:"current_location"`    // 当前位置
	IsOnline          bool      `json:"is_online"`           // 是否在线
	WorkingStatus     int       `json:"working_status"`      // 工作状态
	WorkingStatusDesc string    `json:"working_status_desc"` // 工作状态描述
	Score             float64   `json:"score"`               // 评分
	OrderCount        int       `json:"order_count"`         // 订单数量
	SuccessCount      int       `json:"success_count"`       // 成功订单数
	CancelCount       int       `json:"cancel_count"`        // 取消订单数
	Balance           float64   `json:"balance"`             // 账户余额
	ServiceRadius     float64   `json:"service_radius"`      // 服务半径(km)
	JoinTime          time.Time `json:"join_time"`           // 加入时间
	LastOnlineTime    time.Time `json:"last_online_time"`    // 最后在线时间
	Remark            string    `json:"remark"`              // 管理员备注
}

// AdminRunnerDetailResponse 管理员跑腿员详细信息响应
type AdminRunnerDetailResponse struct {
	ID                int64     `json:"id"`                  // 跑腿员ID
	UserID            int64     `json:"user_id"`             // 关联的用户ID
	RealName          string    `json:"real_name"`           // 真实姓名
	IDCardNumber      string    `json:"id_card_number"`      // 身份证号码
	IDCardFrontPic    string    `json:"id_card_front_pic"`   // 身份证正面照
	IDCardBackPic     string    `json:"id_card_back_pic"`    // 身份证背面照
	FacePic           string    `json:"face_pic"`            // 人脸照片
	Mobile            string    `json:"mobile"`              // 手机号码
	Status            int       `json:"status"`              // 状态
	StatusDesc        string    `json:"status_desc"`         // 状态描述
	CurrentLocation   string    `json:"current_location"`    // 当前位置
	Latitude          float64   `json:"latitude"`            // 纬度
	Longitude         float64   `json:"longitude"`           // 经度
	IsOnline          bool      `json:"is_online"`           // 是否在线
	WorkingStatus     int       `json:"working_status"`      // 工作状态
	WorkingStatusDesc string    `json:"working_status_desc"` // 工作状态描述
	Score             float64   `json:"score"`               // 评分
	OrderCount        int       `json:"order_count"`         // 订单数量
	SuccessCount      int       `json:"success_count"`       // 成功订单数
	CancelCount       int       `json:"cancel_count"`        // 取消订单数
	Balance           float64   `json:"balance"`             // 账户余额
	Wallet            float64   `json:"wallet"`              // 钱包余额
	AreaCodes         string    `json:"area_codes"`          // 服务区域编码
	ServiceRadius     float64   `json:"service_radius"`      // 服务半径(km)
	Remark            string    `json:"remark"`              // 管理员备注
	FrontendRemark    string    `json:"frontend_remark"`     // 前端显示备注
	JoinTime          time.Time `json:"join_time"`           // 加入时间
	LastLoginTime     time.Time `json:"last_login_time"`     // 最后登录时间
	LastOnlineTime    time.Time `json:"last_online_time"`    // 最后在线时间
	CreateTime        time.Time `json:"create_time"`         // 创建时间
	UpdateTime        time.Time `json:"update_time"`         // 更新时间
}

// AdminAuditRunnerRequest 管理员审核跑腿员请求
type AdminAuditRunnerRequest struct {
	Status       int    `json:"status" valid:"Required"`       // 审核状态：1审核通过 2审核拒绝
	RejectReason string `json:"reject_reason"`                // 拒绝原因（审核拒绝时必填）
	Remark       string `json:"remark"`                       // 管理员备注
}

// AdminUpdateRunnerStatusRequest 管理员更新跑腿员状态请求
type AdminUpdateRunnerStatusRequest struct {
	Status int    `json:"status" valid:"Required"` // 状态：1审核通过 3暂停服务 4黑名单
	Reason string `json:"reason"`                 // 操作原因
	Remark string `json:"remark"`                 // 管理员备注
}

// AdminUpdateRunnerRemarkRequest 管理员更新跑腿员备注请求
type AdminUpdateRunnerRemarkRequest struct {
	Remark         string `json:"remark"`          // 管理员备注
	FrontendRemark string `json:"frontend_remark"` // 前端显示备注
}

// RunnerBalanceResponse 骑手账户余额信息响应
type RunnerBalanceResponse struct {
	Available   float64 `json:"available"`   // 可提现余额(元)
	Frozen      float64 `json:"frozen"`      // 冻结金额(元)
	Withdrawing float64 `json:"withdrawing"` // 提现中金额(元)
}

// AdminRunnerStatisticsResponse 管理员跑腿员统计响应
type AdminRunnerStatisticsResponse struct {
	TotalRunners     int64 `json:"total_runners"`     // 总跑腿员数
	PendingAudit     int64 `json:"pending_audit"`     // 待审核数
	ApprovedRunners  int64 `json:"approved_runners"`  // 已审核通过数
	RejectedRunners  int64 `json:"rejected_runners"`  // 已拒绝数
	SuspendedRunners int64 `json:"suspended_runners"` // 暂停服务数
	BlacklistRunners int64 `json:"blacklist_runners"` // 黑名单数
	OnlineRunners    int64 `json:"online_runners"`    // 在线跑腿员数
	WorkingRunners   int64 `json:"working_runners"`   // 工作中跑腿员数
	TodayNewRunners  int64 `json:"today_new_runners"` // 今日新增跑腿员数
	ThisWeekNewRunners int64 `json:"this_week_new_runners"` // 本周新增跑腿员数
	ThisMonthNewRunners int64 `json:"this_month_new_runners"` // 本月新增跑腿员数
}
