/**
 * runner_settings_dto.go
 * 跑腿员设置相关数据传输对象
 *
 * 本文件定义了跑腿员设置相关的DTO，包括支付账户、通知设置、服务设置等
 */

package dto

// PaymentAccountsResponse 跑腿员支付账户信息响应
type PaymentAccountsResponse struct {
	ID            int64  `json:"id"`            // 账户ID
	RunnerID      int64  `json:"runner_id"`     // 跑腿员ID
	AccountType   int    `json:"account_type"`  // 账户类型：1-微信 2-支付宝 3-银行卡
	AccountName   string `json:"account_name"`  // 账户名称/持卡人姓名
	AccountNumber string `json:"account_number"` // 账号
	BankName      string `json:"bank_name"`     // 银行名称(银行卡时必填)
	IsDefault     bool   `json:"is_default"`    // 是否默认账户
	CreatedAt     string `json:"created_at"`    // 创建时间
	UpdatedAt     string `json:"updated_at"`    // 更新时间
}

// NotificationSettingsResponse 跑腿员通知设置响应
type NotificationSettingsResponse struct {
	RunnerID             int64 `json:"runner_id"`              // 跑腿员ID
	OrderNotification    bool  `json:"order_notification"`     // 订单通知
	SystemNotification   bool  `json:"system_notification"`    // 系统通知
	MarketingNotification bool `json:"marketing_notification"` // 营销通知
	PaymentNotification  bool  `json:"payment_notification"`   // 支付通知
	Sound                bool  `json:"sound"`                  // 声音提醒
	Vibration            bool  `json:"vibration"`              // 振动提醒
	UpdatedAt            string `json:"updated_at"`            // 更新时间
}

// ServiceSettingsResponse 跑腿员服务设置响应
type ServiceSettingsResponse struct {
	RunnerID            int64   `json:"runner_id"`             // 跑腿员ID
	AutoAcceptOrder     bool    `json:"auto_accept_order"`     // 自动接单
	MaxOrderDistance    float64 `json:"max_order_distance"`    // 最大接单距离(公里)
	OrderTypes          []int   `json:"order_types"`           // 可接订单类型：1-外卖 2-快递 3-买药 4-代购
	WorkingHoursStart   string  `json:"working_hours_start"`   // 工作时间开始
	WorkingHoursEnd     string  `json:"working_hours_end"`     // 工作时间结束
	RestDays            []int   `json:"rest_days"`             // 休息日(1-7表示周一到周日)
	MaxSimultaneousOrder int    `json:"max_simultaneous_order"` // 最大同时配送订单数
	UpdatedAt           string  `json:"updated_at"`            // 更新时间
}
