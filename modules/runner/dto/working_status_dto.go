/*
 * working_status_dto.go
 * 跑腿员工作状态相关DTO定义
 *
 * 本文件定义了跑腿员工作状态更新相关的数据传输对象
 */

package dto

// UpdateWorkingStatusRequest 更新跑腿员工作状态请求
type UpdateWorkingStatusRequest struct {
	WorkingStatus int    `json:"working_status" valid:"Required" description:"工作状态：0-休息中 1-接单中 2-配送中"` // 工作状态：0-休息中 1-接单中 2-配送中
	Reason        string `json:"reason" description:"原因"`                                              // 原因
}