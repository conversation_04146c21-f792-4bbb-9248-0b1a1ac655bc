/**
 * repository_factory.go
 * 仓库工厂实现
 *
 * 本文件实现了仓库工厂接口，负责创建各种仓库的实例
 */

package factory

import (
	"o_mall_backend/modules/runner/core"
	"o_mall_backend/modules/runner/repositories/impl"
)

// RepositoryFactoryImpl 仓库工厂实现
type RepositoryFactoryImpl struct{}

// NewRepositoryFactory 创建仓库工厂实例
func NewRepositoryFactory() core.RepositoryFactory {
	return &RepositoryFactoryImpl{}
}

// RunnerRepository 获取跑腿员仓库
func (f *RepositoryFactoryImpl) RunnerRepository() core.RunnerRepository {
	return impl.NewRunnerRepositoryImpl()
}

// RunnerOrderRepository 获取跑腿订单仓库
func (f *RepositoryFactoryImpl) RunnerOrderRepository() core.RunnerOrderRepository {
	runnerRepo := impl.NewRunnerRepositoryImpl()
	return impl.NewRunnerOrderRepositoryImpl(runnerRepo)
}

// RunnerIncomeRepository 获取跑腿员收入仓库
func (f *RepositoryFactoryImpl) RunnerIncomeRepository() core.RunnerIncomeRepository {
	return impl.NewRunnerIncomeRepositoryImpl()
}

// RunnerMiscRepository 获取跑腿员杂项仓库
func (f *RepositoryFactoryImpl) RunnerMiscRepository() core.RunnerMiscRepository {
	return impl.NewRunnerMiscRepositoryImpl()
}
