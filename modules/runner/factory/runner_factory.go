/**
 * runner_factory.go
 * 跑腿员模块工厂
 *
 * 本文件实现了跑腿员模块的工厂方法，用于创建各种仓库实例
 */

package factory

import (
	"o_mall_backend/modules/runner/core"
	"o_mall_backend/modules/runner/repositories/impl"
)

// NewRunnerRepository 创建跑腿员仓库实例
func NewRunnerRepository() core.RunnerRepository {
	return impl.NewRunnerRepositoryImpl()
}

// NewRunnerOrderRepository 创建跑腿订单仓库实例
func NewRunnerOrderRepository(runnerRepo core.RunnerRepository) core.RunnerOrderRepository {
	return impl.NewRunnerOrderRepositoryImpl(runnerRepo)
}

// NewRunnerIncomeRepository 创建跑腿员收入仓库实例
func NewRunnerIncomeRepository() core.RunnerIncomeRepository {
	return impl.NewRunnerIncomeRepositoryImpl()
}

// NewRunnerMiscRepository 创建跑腿员杂项仓库实例
func NewRunnerMiscRepository() core.RunnerMiscRepository {
	return impl.NewRunnerMiscRepositoryImpl()
}
