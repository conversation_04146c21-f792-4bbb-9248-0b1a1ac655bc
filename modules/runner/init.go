/**
 * runner模块初始化
 *
 * 本文件负责runner模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保runner模块的功能正常启动。
 */

package runner

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/runner/models"
	"o_mall_backend/modules/runner/routers"
)

// Init 初始化runner模块
func Init() {
	logs.Info("初始化runner模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitRouters()

	logs.Info("runner模块初始化完成")
}

// registerModels 注册模型
func registerModels() {
	// 注册跑腿员模型
	orm.RegisterModel(new(models.Runner))
	// 注册跑腿订单模型
	orm.RegisterModel(new(models.RunnerOrder))
	// 注册跑腿员位置模型
	orm.RegisterModel(new(models.RunnerLocation))
	// 注册跑腿员申请模型
	orm.RegisterModel(new(models.RunnerApply))
	// 注册跑腿员收入记录模型
	orm.RegisterModel(new(models.RunnerIncomeLog))
	// 注册跑腿员提现记录模型
	orm.RegisterModel(new(models.RunnerWithdrawal))
}
