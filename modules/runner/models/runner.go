/**
 * runner.go
 * 跑腿员模型定义
 *
 * 本文件定义了跑腿员相关的数据模型，包括跑腿员基本信息、状态、评分等
 */

package models

import (
	"time"
)

// Runner 跑腿员模型
type Runner struct {
	ID              int64     `orm:"auto;pk;column(i_d);description(跑腿员唯一标识)" json:"id"`                                          // 跑腿员ID
	UserID          int64     `orm:"unique;column(user_i_d);description(关联的用户账号ID)" json:"user_id"`                                     // 关联的用户ID
	RealName        string    `orm:"size(64);column(real_name);description(跑腿员真实姓名)" json:"real_name"`                                  // 真实姓名
	IDCardNumber    string    `orm:"size(18);unique;column(i_d_card_number);description(身份证号码,18位)" json:"id_card_number"`                           // 身份证号码
	IDCardFrontPic  string    `orm:"size(255);column(i_d_card_front_pic);description(身份证正面照片URL)" json:"id_card_front_pic"`                      // 身份证正面照
	IDCardBackPic   string    `orm:"size(255);column(i_d_card_back_pic);description(身份证背面照片URL)" json:"id_card_back_pic"`                       // 身份证背面照
	FacePic         string    `orm:"size(255);column(face_pic);description(人脸识别照片URL)" json:"face_pic"`                                // 人脸照片
	Mobile          string    `orm:"size(20);unique;column(mobile);description(联系电话)" json:"mobile"`                                        // 手机号码
	Status          int       `orm:"default(0);column(status);description(跑腿员状态:0待审核1审核通过2审核拒绝3暂停服务4黑名单)" json:"status"`             // 状态：0-待审核 1-审核通过 2-审核拒绝 3-暂停服务 4-黑名单
	CurrentLocation string    `orm:"size(255);null;column(current_location);description(当前位置描述)" json:"current_location"`                      // 当前位置
	Latitude        float64   `orm:"digits(10);decimals(6);column(latitude);description(当前位置纬度)" json:"latitude"`                      // 纬度
	Longitude       float64   `orm:"digits(10);decimals(6);column(longitude);description(当前位置经度)" json:"longitude"`                     // 经度
	IsOnline        bool      `orm:"default(false);column(is_online);description(是否在线状态)" json:"is_online"`                             // 是否在线
	WorkingStatus   int       `orm:"default(0);column(working_status);description(工作状态:0休息中1接单中2配送中)" json:"working_status"`                 // 工作状态：0-休息中 1-接单中 2-配送中
	Score           float64   `orm:"digits(2);decimals(1);default(5.0);column(score);description(跑腿员评分,5分制)" json:"score"`          // 评分
	OrderCount      int       `orm:"default(0);column(order_count);description(总接单数量)" json:"order_count"`                                // 订单数量
	SuccessCount    int       `orm:"default(0);column(success_count);description(成功完成订单数)" json:"success_count"`                            // 成功订单数
	CancelCount     int       `orm:"default(0);column(cancel_count);description(已取消订单数)" json:"cancel_count"`                              // 取消订单数
	Balance         float64   `orm:"digits(10);decimals(2);default(0);column(balance);description(账户可用余额,元)" json:"balance"`          // 账户余额
	JoinTime        time.Time `orm:"auto_now_add;type(datetime);column(join_time);description(成为跑腿员时间)" json:"join_time"`               // 加入时间
	LastLoginTime   time.Time `orm:"null;type(datetime);column(last_login_time);description(最近一次登录时间)" json:"last_login_time"`                // 最后登录时间
	LastOnlineTime  time.Time `orm:"null;type(datetime);column(last_online_time);description(最近一次在线时间)" json:"last_online_time"`               // 最后在线时间
	Wallet          float64   `orm:"digits(10);decimals(2);default(0);column(wallet);description(钱包总余额,元)" json:"wallet"`            // 钱包余额
	AreaCodes       string    `orm:"size(255);null;column(area_codes);description(服务区域编码列表,逗号分隔)" json:"area_codes"`                     // 服务区域编码，逗号分隔
	ServiceRadius   float64   `orm:"digits(5);decimals(2);default(0);column(service_radius);description(服务覆盖半径,单位km)" json:"service_radius"` // 服务半径(km)
	Remark          string    `orm:"size(500);null;column(remark);description(后台管理备注)" json:"remark"`                                // 备注
	FrontendRemark  string    `orm:"size(500);null;column(frontend_remark);description(前端展示备注信息)" json:"frontend_remark"`                     // 前端显示备注
	CreateTime      time.Time `orm:"auto_now_add;type(datetime);column(create_time);description(记录创建时间)" json:"create_time"`              // 创建时间
	UpdateTime      time.Time `orm:"auto_now;type(datetime);column(update_time);description(记录更新时间)" json:"update_time"`                  // 更新时间
}

// TableName 设置Runner模型对应的数据库表名
func (r *Runner) TableName() string {
	return "runner"
}

// RunnerOrder 跑腿订单模型
type RunnerOrder struct {
	ID                    int64     `orm:"auto;pk;description(跑腿订单唯一标识)" json:"id"`                                      // 订单ID
	OrderNo               string    `orm:"size(32);unique;description(订单唯一编号)" json:"order_no"`                          // 订单编号
	OrderType             int       `orm:"default(0);description(订单类型:0商品配送1代购2代取3其他服务)" json:"order_type"`              // 订单类型：0-商品配送 1-代购 2-代取 3-其他服务
	UserID                int64     `orm:"index;description(下单用户ID)" json:"user_id"`                                     // 用户ID
	RunnerID              int64     `orm:"index;default(0);description(配送跑腿员ID)" json:"runner_id"`                       // 跑腿员ID
	Status                int       `orm:"default(10);description(订单状态:10待支付20已支付30已接单40已取货50已完成60已取消)" json:"status"`          // 订单状态：10-待支付 20-已支付 30-已接单 40-已取货 50-已完成 60-已取消
	PayStatus             int       `orm:"default(0);description(支付状态:0未支付1已支付2已退款)" json:"pay_status"`                  // 支付状态：0-未支付 1-已支付 2-已退款
	PayMethod             int       `orm:"default(0);description(支付方式:1微信支付2支付宝3余额支付)" json:"pay_method"`                // 支付方式：1-微信支付 2-支付宝 3-余额支付
	TotalAmount           float64   `orm:"digits(10);decimals(2);description(订单总金额,元)" json:"total_amount"`              // 订单总金额
	DeliveryFee           float64   `orm:"digits(10);decimals(2);description(配送费用,元)" json:"delivery_fee"`               // 配送费用
	ServiceFee            float64   `orm:"digits(10);decimals(2);description(服务费用,元)" json:"service_fee"`                // 服务费用
	TipAmount             float64   `orm:"digits(10);decimals(2);default(0);description(小费金额,元)" json:"tip_amount"`      // 小费金额
	Distance              float64   `orm:"digits(10);decimals(2);description(配送距离,单位km)" json:"distance"`                // 配送距离(km)
	EstimateTime          int       `orm:"default(0);description(预计配送时间,单位分钟)" json:"estimate_time"`                     // 预计配送时间(分钟)
	PickupAddress         string    `orm:"size(255);description(取货地址)" json:"pickup_address"`                            // 取货地址
	PickupAddressDetail   string    `orm:"size(255);description(取货详细地址信息)" json:"pickup_address_detail"`                 // 取货详细地址
	PickupLat             float64   `orm:"digits(10);decimals(6);description(取货地址纬度)" json:"pickup_lat"`                 // 取货地址纬度
	PickupLng             float64   `orm:"digits(10);decimals(6);description(取货地址经度)" json:"pickup_lng"`                 // 取货地址经度
	PickupContact         string    `orm:"size(64);description(取货联系人姓名)" json:"pickup_contact"`                          // 取货联系人
	PickupPhone           string    `orm:"size(20);description(取货联系人电话)" json:"pickup_phone"`                            // 取货联系电话
	DeliveryAddress       string    `orm:"size(255);description(送货地址)" json:"delivery_address"`                          // 送货地址
	DeliveryAddressDetail string    `orm:"size(255);description(送货详细地址信息)" json:"delivery_address_detail"`               // 送货详细地址
	DeliveryLat           float64   `orm:"digits(10);decimals(6);description(送货地址纬度)" json:"delivery_lat"`               // 送货地址纬度
	DeliveryLng           float64   `orm:"digits(10);decimals(6);description(送货地址经度)" json:"delivery_lng"`               // 送货地址经度
	DeliveryContact       string    `orm:"size(64);description(送货联系人姓名)" json:"delivery_contact"`                        // 送货联系人
	DeliveryPhone         string    `orm:"size(20);description(送货联系人电话)" json:"delivery_phone"`                          // 送货联系电话
	Goods                 string    `orm:"size(500);description(商品描述信息)" json:"goods"`                                   // 商品描述
	GoodsWeight           float64   `orm:"digits(10);decimals(2);default(0);description(商品重量,单位kg)" json:"goods_weight"` // 商品重量(kg)
	GoodsValue            float64   `orm:"digits(10);decimals(2);default(0);description(商品价值,元)" json:"goods_value"`     // 商品价值
	Remark                string    `orm:"size(500);null;description(订单备注信息)" json:"remark"`                             // 备注
	AcceptTime            time.Time `orm:"null;type(datetime);description(跑腿员接单时间)" json:"accept_time"`                  // 接单时间
	PickupTime            time.Time `orm:"null;type(datetime);description(跑腿员取货时间)" json:"pickup_time"`                  // 取货时间
	DeliveryTime          time.Time `orm:"null;type(datetime);description(订单送达完成时间)" json:"delivery_time"`               // 送达时间
	CancelTime            time.Time `orm:"null;type(datetime);description(订单取消时间)" json:"cancel_time"`                   // 取消时间
	CancelReason          string    `orm:"size(500);null;description(订单取消原因)" json:"cancel_reason"`                      // 取消原因
	CancelUserType        int       `orm:"default(0);description(取消用户类型:0系统1用户2跑腿员)" json:"cancel_user_type"`            // 取消用户类型：0-系统 1-用户 2-跑腿员
	ScoreByUser           int       `orm:"default(0);description(用户评分,5分制)" json:"score_by_user"`                        // 用户评分
	ScoreByRunner         int       `orm:"default(0);description(跑腿员评分,5分制)" json:"score_by_runner"`                     // 跑腿员评分
	CommentByUser         string    `orm:"size(500);null;description(用户评价内容)" json:"comment_by_user"`                    // 用户评价
	CommentByRunner       string    `orm:"size(500);null;description(跑腿员评价内容)" json:"comment_by_runner"`                 // 跑腿员评价
	CreateTime            time.Time `orm:"auto_now_add;type(datetime);description(订单创建时间)" json:"create_time"`
	UpdateTime            time.Time `orm:"auto_now;type(datetime);description(记录更新时间)" json:"update_time"` // 更新时间
}

// RunnerIncomeLog 跑腿员收入记录
type RunnerIncomeLog struct {
	ID          int64     `orm:"auto;pk;description(收入记录唯一标识)" json:"id"`                            // 记录ID
	RunnerID    int64     `orm:"index;description(关联的跑腿员ID)" json:"runner_id"`                       // 跑腿员ID
	OrderID     int64     `orm:"index;description(关联的订单ID)" json:"order_id"`                         // 订单ID
	OrderNo     string    `orm:"size(32);description(关联的订单编号)" json:"order_no"`                      // 订单编号
	Amount      float64   `orm:"digits(10);decimals(2);description(收入金额,元)" json:"amount"`           // 金额
	Type        int       `orm:"default(0);description(收入类型:0配送费1小费2奖励3退款)" json:"type"`             // 类型：0-配送费 1-小费 2-奖励 3-退款
	Status      int       `orm:"default(0);description(结算状态:0未结算1已结算2已退款)" json:"status"`            // 状态：0-未结算 1-已结算 2-已退款
	Description string    `orm:"size(255);description(收入说明)" json:"description"`                     // 描述
	CreateTime  time.Time `orm:"auto_now_add;type(datetime);description(记录创建时间)" json:"create_time"` // 创建时间
	UpdateTime  time.Time `orm:"auto_now;type(datetime);description(记录更新时间)" json:"update_time"`     // 更新时间
}

// RunnerWithdrawal 跑腿员提现记录
type RunnerWithdrawal struct {
	ID               int64     `orm:"auto;pk;description(提现记录唯一标识)" json:"id"`                            // 记录ID
	RunnerID         int64     `orm:"index;description(关联的跑腿员ID)" json:"runner_id"`                       // 跑腿员ID
	WithdrawalNo     string    `orm:"size(32);unique;description(提现单号,系统生成)" json:"withdrawal_no"`        // 提现编号
	Amount           float64   `orm:"digits(10);decimals(2);description(提现金额,元)" json:"amount"`           // 提现金额
	Status           int       `orm:"default(0);description(提现状态:0处理中1已完成2已拒绝)" json:"status"`            // 状态：0-处理中 1-已完成 2-已拒绝
	WithdrawalMethod int       `orm:"default(0);description(提现方式:1微信2支付宝3银行卡)" json:"withdrawal_method"`  // 提现方式：1-微信 2-支付宝 3-银行卡
	AccountName      string    `orm:"size(64);description(收款账户名称)" json:"account_name"`                   // 账户名称
	AccountNo        string    `orm:"size(64);description(收款账号)" json:"account_no"`                       // 账号
	BankName         string    `orm:"size(64);null;description(开户银行名称)" json:"bank_name"`                 // 银行名称
	Remark           string    `orm:"size(255);null;description(提现备注说明)" json:"remark"`                   // 备注
	HandleTime       time.Time `orm:"null;type(datetime);description(提现处理时间)" json:"handle_time"`         // 处理时间
	HandleBy         int64     `orm:"default(0);description(处理人员ID)" json:"handle_by"`                    // 处理人ID
	CreateTime       time.Time `orm:"auto_now_add;type(datetime);description(记录创建时间)" json:"create_time"` // 创建时间
	UpdateTime       time.Time `orm:"auto_now;type(datetime);description(记录更新时间)" json:"update_time"`     // 更新时间
}

// RunnerApply 跑腿员申请记录
type RunnerApply struct {
	ID             int64     `orm:"auto;pk;description(申请记录唯一标识)" json:"id"`                                         // 申请ID
	UserID         int64     `orm:"unique;description(申请用户ID)" json:"user_id"`                                        // 用户ID
	RealName       string    `orm:"size(64);description(申请人真实姓名)" json:"real_name"`                                  // 真实姓名
	IDCardNumber   string    `orm:"size(18);unique;description(身份证号码,18位)" json:"id_card_number"`                           // 身份证号码
	IDCardFrontPic string    `orm:"size(255);description(身份证正面照片URL)" json:"id_card_front_pic"`                      // 身份证正面照
	IDCardBackPic  string    `orm:"size(255);description(身份证背面照片URL)" json:"id_card_back_pic"`                       // 身份证背面照
	FacePic        string    `orm:"size(255);description(人脸识别照片URL)" json:"face_pic"`                                // 人脸照片
	Mobile         string    `orm:"size(20);unique;description(联系电话)" json:"mobile"`                                        // 手机号码
	Status         int       `orm:"default(0);description(申请状态:0待审核1审核通过2审核拒绝)" json:"status"`                       // 状态：0-待审核 1-审核通过 2-审核拒绝
	RejectReason   string    `orm:"size(255);null;description(申请拒绝原因)" json:"reject_reason"`                         // 拒绝原因
	AuditTime      time.Time `orm:"null;type(datetime);description(审核操作时间)" json:"audit_time"`                       // 审核时间
	AuditBy        int64     `orm:"default(0);description(审核人员ID)" json:"audit_by"`                                  // 审核人ID
	AreaCodes      string    `orm:"size(255);null;description(服务区域编码列表,逗号分隔)" json:"area_codes"`                     // 服务区域编码，逗号分隔
	ServiceRadius  float64   `orm:"digits(5);decimals(2);default(0);description(服务覆盖半径,单位km)" json:"service_radius"` // 服务半径(km)
	CreateTime     time.Time `orm:"auto_now_add;type(datetime);description(记录创建时间)" json:"create_time"`              // 创建时间
	UpdateTime     time.Time `orm:"auto_now;type(datetime);description(记录更新时间)" json:"update_time"`                  // 更新时间
}

// RunnerLocation 跑腿员位置记录
type RunnerLocation struct {
	ID         int64     `orm:"auto;pk;description(位置记录唯一标识)" json:"id"`                            // 记录ID
	RunnerID   int64     `orm:"index;description(关联的跑腿员ID)" json:"runner_id"`                       // 跑腿员ID
	Latitude   float64   `orm:"digits(10);decimals(6);description(当前位置纬度)" json:"latitude"`         // 纬度
	Longitude  float64   `orm:"digits(10);decimals(6);description(当前位置经度)" json:"longitude"`        // 经度
	CreateTime time.Time `orm:"auto_now_add;type(datetime);description(记录创建时间)" json:"create_time"` // 创建时间
}
