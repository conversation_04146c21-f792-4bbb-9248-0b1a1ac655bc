/**
 * factory.go
 * 仓库工厂接口
 *
 * 本文件定义了仓库工厂的接口，用于依赖注入和解决循环依赖问题
 */

package repositories

// RepositoryFactory 仓库工厂接口
type RepositoryFactory interface {
	// CreateRunnerRepository 创建跑腿员仓库实例
	CreateRunnerRepository() RunnerRepository

	// CreateRunnerOrderRepository 创建跑腿订单仓库实例
	CreateRunnerOrderRepository() RunnerOrderRepository

	// CreateRunnerIncomeRepository 创建跑腿员收入仓库实例
	CreateRunnerIncomeRepository() RunnerIncomeRepository

	// CreateRunnerMiscRepository 创建跑腿员杂项仓库实例
	CreateRunnerMiscRepository() RunnerMiscRepository
}
