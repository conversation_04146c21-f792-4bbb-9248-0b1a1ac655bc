/**
 * runner_income_methods.go
 * 跑腿员收入统计相关方法实现
 *
 * 本文件实现了跑腿员收入统计相关的仓库方法
 */

package impl

import (
	"context"
	"time"
)

// GetRunnerDailyIncome 获取跑腿员今日收入
func (r *RunnerIncomeRepositoryImpl) GetRunnerDailyIncome(ctx context.Context, runnerID int64) (float64, error) {
	// 获取今日开始时间（0点）和当前时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endTime := now

	return r.GetRunnerIncomeByTimeRange(ctx, runnerID, startTime, endTime)
}

// GetRunnerWeeklyIncome 获取跑腿员本周收入
func (r *RunnerIncomeRepositoryImpl) GetRunnerWeeklyIncome(ctx context.Context, runnerID int64) (float64, error) {
	// 获取本周开始时间（周一0点）和当前时间
	now := time.Now()
	// 获取本周周一的偏移量（Go中周日是0，周一是1）
	offset := int(now.Weekday())
	if offset == 0 { // 如果今天是周日
		offset = 7
	}
	// 本周周一的日期
	weekStart := now.AddDate(0, 0, -offset+1)
	// 本周周一的0点
	startTime := time.Date(weekStart.Year(), weekStart.Month(), weekStart.Day(), 0, 0, 0, 0, now.Location())
	endTime := now

	return r.GetRunnerIncomeByTimeRange(ctx, runnerID, startTime, endTime)
}

// GetRunnerMonthlyIncome 获取跑腿员本月收入
func (r *RunnerIncomeRepositoryImpl) GetRunnerMonthlyIncome(ctx context.Context, runnerID int64) (float64, error) {
	// 获取本月开始时间（1号0点）和当前时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endTime := now

	return r.GetRunnerIncomeByTimeRange(ctx, runnerID, startTime, endTime)
}
