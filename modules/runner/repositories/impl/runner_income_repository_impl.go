/**
 * runner_income_repository_impl.go
 * 跑腿员收入仓库实现
 *
 * 本文件实现了跑腿员收入相关的数据访问接口
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/runner/constants"
	"o_mall_backend/modules/runner/core"
	"o_mall_backend/modules/runner/models"
)

// RunnerIncomeRepositoryImpl 跑腿员收入仓库实现
type RunnerIncomeRepositoryImpl struct {
	runnerRepo core.RunnerRepository
}

var _ core.RunnerIncomeRepository = (*RunnerIncomeRepositoryImpl)(nil)

// NewRunnerIncomeRepositoryImpl 创建跑腿员收入仓库实例
func NewRunnerIncomeRepositoryImpl() core.RunnerIncomeRepository {
	return &RunnerIncomeRepositoryImpl{
		runnerRepo: NewRunnerRepositoryImpl(),
	}
}

// CreateIncomeLog 创建收入记录
func (r *RunnerIncomeRepositoryImpl) CreateIncomeLog(ctx context.Context, log *models.RunnerIncomeLog) error {
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[CreateIncomeLog] 开启事务失败: %v", err)
		return fmt.Errorf("创建收入记录失败: %v", err)
	}

	_, err = tx.Insert(log)
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateIncomeLog] 创建跑腿员收入记录失败: %v", err)
		return fmt.Errorf("创建收入记录失败: %v", err)
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateIncomeLog] 提交事务失败: %v", err)
		return fmt.Errorf("创建收入记录失败: %v", err)
	}

	return nil
}

// GetIncomeLogByID 根据ID获取收入记录
func (r *RunnerIncomeRepositoryImpl) GetIncomeLogByID(ctx context.Context, id int64) (*models.RunnerIncomeLog, error) {
	o := orm.NewOrm()
	log := &models.RunnerIncomeLog{ID: id}
	err := o.Read(log)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("[GetIncomeLogByID] 获取跑腿员收入记录失败: %v", err)
		return nil, err
	}
	return log, nil
}

// ListIncomeLogs 获取收入记录列表
func (r *RunnerIncomeRepositoryImpl) ListIncomeLogs(ctx context.Context, runnerID int64, startTime, endTime time.Time) ([]*models.RunnerIncomeLog, error) {
	o := orm.NewOrm()
	var incomeLogs []*models.RunnerIncomeLog

	qs := o.QueryTable(new(models.RunnerIncomeLog)).
		Filter("runner_id", runnerID)

	if !startTime.IsZero() {
		qs = qs.Filter("create_time__gte", startTime)
	}
	if !endTime.IsZero() {
		qs = qs.Filter("create_time__lte", endTime)
	}

	_, err := qs.OrderBy("-create_time").All(&incomeLogs)
	if err != nil {
		logs.Error("[ListIncomeLogs] 获取跑腿员收入记录列表失败: %v", err)
		return nil, err
	}

	return incomeLogs, nil
}

// WithdrawIncome 提现操作
func (r *RunnerIncomeRepositoryImpl) WithdrawIncome(ctx context.Context, runnerID int64, amount float64) error {
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[WithdrawIncome] 开启事务失败: %v", err)
		return err
	}

	// 检查余额
	runner, err := r.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		tx.Rollback()
		return err
	}
	if runner == nil {
		tx.Rollback()
		return errors.New("跑腿员不存在")
	}
	if runner.Balance < amount {
		tx.Rollback()
		return errors.New("余额不足")
	}

	// 更新余额
	runner.Balance -= amount
	_, err = tx.Update(runner, "Balance")
	if err != nil {
		tx.Rollback()
		return err
	}

	// 创建提现记录
	withdrawLog := &models.RunnerIncomeLog{
		RunnerID:    runnerID,
		Type:        constants.IncomeTypeWithdraw,
		Amount:      -amount,
		Description: "提现",
		Status:      constants.IncomeStatusSuccess,
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}
	_, err = tx.Insert(withdrawLog)
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

// UpdateIncomeStatus 更新收入记录状态
func (r *RunnerIncomeRepositoryImpl) UpdateIncomeStatus(ctx context.Context, logID int64, status int) error {
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	// 获取收入记录
	log := &models.RunnerIncomeLog{ID: logID}
	err = tx.Read(log)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 更新状态
	log.Status = status
	log.UpdateTime = time.Now()
	_, err = tx.Update(log, "Status", "UpdateTime")
	if err != nil {
		tx.Rollback()
		return err
	}

	// 如果状态是已入账，更新跑腿员余额
	if status == constants.IncomeStatusSuccess {
		runner, err := r.runnerRepo.GetRunnerByID(ctx, log.RunnerID)
		if err != nil {
			tx.Rollback()
			return err
		}

		runner.Balance += log.Amount
		runner.UpdateTime = time.Now()
		_, err = tx.Update(runner, "Balance", "UpdateTime")
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit()
}

// CreateRunnerIncomeLog 创建跑腿员收入记录
func (r *RunnerIncomeRepositoryImpl) CreateRunnerIncomeLog(ctx context.Context, incomeLog *models.RunnerIncomeLog) (int64, error) {
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[CreateRunnerIncomeLog] 开启事务失败: %v", err)
		return 0, fmt.Errorf("创建收入记录失败: %v", err)
	}

	id, err := tx.Insert(incomeLog)
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateRunnerIncomeLog] 创建收入记录失败: %v", err)
		return 0, fmt.Errorf("创建收入记录失败: %v", err)
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateRunnerIncomeLog] 提交事务失败: %v", err)
		return 0, fmt.Errorf("创建收入记录失败: %v", err)
	}

	return id, nil
}

// GetRunnerIncome 获取跑腿员总收入
func (r *RunnerIncomeRepositoryImpl) GetRunnerIncome(ctx context.Context, runnerID int64) (float64, error) {
	o := orm.NewOrm()

	// 获取钱包余额
	runner, err := r.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("[GetRunnerIncome] 获取跑腿员信息失败: %v", err)
		return 0, err
	}

	// 获取待结算收入
	var pendingIncome float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_id = ? AND status = 0",
		runnerID).QueryRow(&pendingIncome)
	if err != nil {
		logs.Error("[GetRunnerIncome] 获取待结算收入失败: %v", err)
		return 0, fmt.Errorf("获取收入信息失败: %v", err)
	}

	// 获取已结算总收入
	var settledIncome float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_id = ? AND status = 1",
		runnerID).QueryRow(&settledIncome)
	if err != nil {
		logs.Error("[GetRunnerIncome] 获取已结算收入失败: %v", err)
		return 0, fmt.Errorf("获取收入信息失败: %v", err)
	}

	// 获取提现中金额
	var withdrawingAmount float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_withdrawal WHERE runner_id = ? AND status = 0",
		runnerID).QueryRow(&withdrawingAmount)
	if err != nil {
		logs.Error("[GetRunnerIncome] 获取提现中金额失败: %v", err)
		return 0, fmt.Errorf("获取收入信息失败: %v", err)
	}

	// 获取已提现金额
	var withdrawnAmount float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_withdrawal WHERE runner_id = ? AND status = 1",
		runnerID).QueryRow(&withdrawnAmount)
	if err != nil {
		logs.Error("[GetRunnerIncome] 获取已提现金额失败: %v", err)
		return 0, fmt.Errorf("获取收入信息失败: %v", err)
	}

	return runner.Wallet, nil
}

// ListRunnerIncomeLogs 获取收入记录列表
func (r *RunnerIncomeRepositoryImpl) ListRunnerIncomeLogs(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerIncomeLog, int64, error) {
	o := orm.NewOrm()
	var incomeLogs []*models.RunnerIncomeLog

	query := o.QueryTable(new(models.RunnerIncomeLog)).Filter("runner_id", runnerID)
	total, err := query.Count()
	if err != nil {
		logs.Error("[ListRunnerIncomeLogs] 获取收入记录总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取收入记录失败: %v", err)
	}

	_, err = query.OrderBy("-create_time").Limit(pageSize, (page-1)*pageSize).All(&incomeLogs)
	if err != nil {
		logs.Error("[ListRunnerIncomeLogs] 获取收入记录列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取收入记录失败: %v", err)
	}

	return incomeLogs, total, nil
}

// GetRunnerIncomeStatistics 获取跑腿员收入统计
func (r *RunnerIncomeRepositoryImpl) GetRunnerIncomeStatistics(ctx context.Context, runnerID int64, startTime, endTime time.Time) (map[string]interface{}, error) {
	o := orm.NewOrm()

	// 获取总收入
	var totalIncome float64
	err := o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_id = ? AND create_time BETWEEN ? AND ?",
		runnerID, startTime, endTime).QueryRow(&totalIncome)
	if err != nil {
		logs.Error("[GetRunnerIncomeStatistics] 获取总收入失败: %v", err)
		return nil, fmt.Errorf("获取收入统计失败: %v", err)
	}

	// 获取配送费收入
	var deliveryFeeIncome float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_id = ? AND type = 0 AND create_time BETWEEN ? AND ?",
		runnerID, startTime, endTime).QueryRow(&deliveryFeeIncome)
	if err != nil {
		logs.Error("[GetRunnerIncomeStatistics] 获取配送费收入失败: %v", err)
		return nil, fmt.Errorf("获取收入统计失败: %v", err)
	}

	// 获取小费收入
	var tipIncome float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_id = ? AND type = 1 AND create_time BETWEEN ? AND ?",
		runnerID, startTime, endTime).QueryRow(&tipIncome)
	if err != nil {
		logs.Error("[GetRunnerIncomeStatistics] 获取小费收入失败: %v", err)
		return nil, fmt.Errorf("获取收入统计失败: %v", err)
	}

	// 获取奖励收入
	var rewardIncome float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_id = ? AND type = 2 AND create_time BETWEEN ? AND ?",
		runnerID, startTime, endTime).QueryRow(&rewardIncome)
	if err != nil {
		logs.Error("[GetRunnerIncomeStatistics] 获取奖励收入失败: %v", err)
		return nil, fmt.Errorf("获取收入统计失败: %v", err)
	}

	// 获取订单数
	var orderCount int64
	err = o.Raw(`
		SELECT COUNT(DISTINCT order_id) 
		FROM runner_income_log 
		WHERE runner_id = ? AND create_time BETWEEN ? AND ?
	`, runnerID, startTime, endTime).QueryRow(&orderCount)
	if err != nil {
		logs.Error("[GetRunnerIncomeStatistics] 获取订单数失败: %v", err)
		return nil, fmt.Errorf("获取收入统计失败: %v", err)
	}

	// 组装统计信息
	statistics := map[string]interface{}{
		"total_income":        totalIncome,
		"delivery_fee_income": deliveryFeeIncome,
		"tip_income":          tipIncome,
		"reward_income":       rewardIncome,
		"order_count":         orderCount,
		"period_start":        startTime,
		"period_end":          endTime,
	}

	return statistics, nil
}

// CreateWithdrawal 创建提现记录
func (r *RunnerIncomeRepositoryImpl) CreateWithdrawal(ctx context.Context, withdrawal *models.RunnerWithdrawal) (int64, error) {
	o := orm.NewOrm()

	// 检查钱包余额是否足够
	runner, err := r.runnerRepo.GetRunnerByID(ctx, withdrawal.RunnerID)
	if err != nil {
		return 0, err
	}

	if runner.Wallet < withdrawal.Amount {
		return 0, errors.New("钱包余额不足")
	}

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[CreateWithdrawal] 开启事务失败: %v", err)
		return 0, fmt.Errorf("申请提现失败: %v", err)
	}

	// 创建提现记录
	id, err := tx.Insert(withdrawal)
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateWithdrawal] 创建提现记录失败: %v", err)
		return 0, fmt.Errorf("申请提现失败: %v", err)
	}

	// 更新钱包余额
	runner.Wallet -= withdrawal.Amount
	runner.UpdateTime = time.Now()

	_, err = tx.Update(runner, "Wallet", "UpdateTime")
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateWithdrawal] 更新钱包余额失败: %v", err)
		return 0, fmt.Errorf("申请提现失败: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateWithdrawal] 提交事务失败: %v", err)
		return 0, fmt.Errorf("申请提现失败: %v", err)
	}

	return id, nil
}

// GetWithdrawalByID 根据ID获取提现记录
func (r *RunnerIncomeRepositoryImpl) GetWithdrawalByID(ctx context.Context, withdrawalID int64) (*models.RunnerWithdrawal, error) {
	o := orm.NewOrm()
	withdrawal := &models.RunnerWithdrawal{ID: withdrawalID}
	err := o.Read(withdrawal)
	if err == orm.ErrNoRows {
		return nil, errors.New("提现记录不存在")
	}
	if err != nil {
		logs.Error("[GetWithdrawalByID] 获取提现记录失败: %v", err)
		return nil, fmt.Errorf("获取提现记录失败: %v", err)
	}
	return withdrawal, nil
}

// UpdateWithdrawalStatus 更新提现状态
func (r *RunnerIncomeRepositoryImpl) UpdateWithdrawalStatus(ctx context.Context, withdrawalID int64, status int, remark string, handleBy int64) error {
	o := orm.NewOrm()
	withdrawal, err := r.GetWithdrawalByID(ctx, withdrawalID)
	if err != nil {
		return err
	}

	// 不能重复处理
	if withdrawal.Status != 0 {
		return errors.New("该提现申请已处理")
	}

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[UpdateWithdrawalStatus] 开启事务失败: %v", err)
		return fmt.Errorf("处理提现失败: %v", err)
	}

	now := time.Now()

	// 更新提现记录
	withdrawal.Status = status
	withdrawal.Remark = remark
	withdrawal.HandleBy = handleBy
	withdrawal.HandleTime = now
	withdrawal.UpdateTime = now

	_, err = tx.Update(withdrawal, "Status", "Remark", "HandleBy", "HandleTime", "UpdateTime")
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateWithdrawalStatus] 更新提现记录失败: %v", err)
		return fmt.Errorf("处理提现失败: %v", err)
	}

	// 如果拒绝提现，需要返还余额
	if status == 2 {
		runner, err := r.runnerRepo.GetRunnerByID(ctx, withdrawal.RunnerID)
		if err != nil {
			tx.Rollback()
			return err
		}

		// 更新钱包余额
		runner.Wallet += withdrawal.Amount
		runner.UpdateTime = now

		_, err = tx.Update(runner, "Wallet", "UpdateTime")
		if err != nil {
			tx.Rollback()
			logs.Error("[UpdateWithdrawalStatus] 更新钱包余额失败: %v", err)
			return fmt.Errorf("处理提现失败: %v", err)
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateWithdrawalStatus] 提交事务失败: %v", err)
		return fmt.Errorf("处理提现失败: %v", err)
	}

	return nil
}

// ListWithdrawals 获取提现记录列表
func (r *RunnerIncomeRepositoryImpl) ListWithdrawals(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerWithdrawal, int64, error) {
	o := orm.NewOrm()
	var withdrawals []*models.RunnerWithdrawal

	query := o.QueryTable(new(models.RunnerWithdrawal)).Filter("runner_id", runnerID)

	total, err := query.Count()
	if err != nil {
		logs.Error("[ListWithdrawals] 获取提现记录总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取提现记录列表失败: %v", err)
	}

	_, err = query.OrderBy("-create_time").Limit(pageSize, (page-1)*pageSize).All(&withdrawals)
	if err != nil {
		logs.Error("[ListWithdrawals] 获取提现记录列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取提现记录列表失败: %v", err)
	}

	return withdrawals, total, nil
}

// UpdateRunnerIncomeStatus 更新跑腿员收入状态
func (r *RunnerIncomeRepositoryImpl) UpdateRunnerIncomeStatus(ctx context.Context, logID int64, status int) error {
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[UpdateRunnerIncomeStatus] 开启事务失败: %v", err)
		return err
	}

	// 获取收入记录
	log := &models.RunnerIncomeLog{ID: logID}
	err = tx.Read(log)
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateRunnerIncomeStatus] 获取收入记录失败: %v", err)
		return err
	}

	// 更新状态
	log.Status = status
	log.UpdateTime = time.Now()
	_, err = tx.Update(log, "Status", "UpdateTime")
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateRunnerIncomeStatus] 更新状态失败: %v", err)
		return err
	}

	// 如果状态是已入账，更新跑腿员余额
	if status == constants.IncomeStatusSuccess {
		runner, err := r.runnerRepo.GetRunnerByID(ctx, log.RunnerID)
		if err != nil {
			tx.Rollback()
			logs.Error("[UpdateRunnerIncomeStatus] 获取跑腿员信息失败: %v", err)
			return err
		}

		runner.Balance += log.Amount
		runner.UpdateTime = time.Now()
		_, err = tx.Update(runner, "Balance", "UpdateTime")
		if err != nil {
			tx.Rollback()
			logs.Error("[UpdateRunnerIncomeStatus] 更新余额失败: %v", err)
			return err
		}
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateRunnerIncomeStatus] 提交事务失败: %v", err)
		return err
	}

	return nil
}

// GetRunnerTotalIncome 获取跑腿员总收入
func (r *RunnerIncomeRepositoryImpl) GetRunnerTotalIncome(ctx context.Context, runnerID int64) (float64, error) {
	o := orm.NewOrm()

	var totalIncome float64
	// 注意：在SQL原生查询中使用runner_i_d而不是runner_id
	err := o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_i_d = ? AND type IN (0, 1, 2) AND status = 1",
		runnerID).QueryRow(&totalIncome)
	if err != nil {
		logs.Error("[GetRunnerTotalIncome] 获取跑腿员总收入失败: %v", err)
		return 0, fmt.Errorf("获取总收入失败: %v", err)
	}

	return totalIncome, nil
}

// GetRunnerWithdrawingAmount 获取跑腿员提现中金额
func (r *RunnerIncomeRepositoryImpl) GetRunnerWithdrawingAmount(ctx context.Context, runnerID int64) (float64, error) {
	o := orm.NewOrm()

	// 注意：在SQL原生查询中使用runner_i_d而不是runner_id
	var withdrawingAmount float64
	err := o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_withdrawal WHERE runner_i_d = ? AND status = 0",
		runnerID).QueryRow(&withdrawingAmount)
	if err != nil {
		logs.Error("[GetRunnerWithdrawingAmount] 获取跑腿员提现中金额失败: %v", err)
		return 0, fmt.Errorf("获取提现中金额失败: %v", err)
	}

	return withdrawingAmount, nil
}

// GetRunnerIncomeByTimeRange 获取跑腿员指定时间范围内的收入
func (r *RunnerIncomeRepositoryImpl) GetRunnerIncomeByTimeRange(ctx context.Context, runnerID int64, startTime, endTime time.Time) (float64, error) {
	o := orm.NewOrm()

	var income float64
	// 注意：在SQL原生查询中使用runner_i_d而不是runner_id
	err := o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_i_d = ? AND create_time BETWEEN ? AND ? AND type IN (0, 1, 2) AND status = 1",
		runnerID, startTime, endTime).QueryRow(&income)
	if err != nil {
		logs.Error("[GetRunnerIncomeByTimeRange] 获取跑腿员时间范围收入失败: %v", err)
		return 0, fmt.Errorf("获取收入失败: %v", err)
	}

	return income, nil
}

// GetRunnerIncomeLogs 获取跑腿员收入记录
func (r *RunnerIncomeRepositoryImpl) GetRunnerIncomeLogs(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerIncomeLog, int64, error) {
	o := orm.NewOrm()
	var incomeLogs []*models.RunnerIncomeLog

	// 注意：在ORM查询中使用"runnerID"而不是"runner_id"
	query := o.QueryTable(new(models.RunnerIncomeLog)).Filter("runnerID", runnerID)
	total, err := query.Count()
	if err != nil {
		logs.Error("[GetRunnerIncomeLogs] 获取收入记录总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取收入记录失败: %v", err)
	}

	_, err = query.OrderBy("-create_time").Limit(pageSize, (page-1)*pageSize).All(&incomeLogs)
	if err != nil {
		logs.Error("[GetRunnerIncomeLogs] 获取收入记录列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取收入记录失败: %v", err)
	}

	return incomeLogs, total, nil
}

// CreateRunnerWithdrawal 创建跑腿员提现记录
func (r *RunnerIncomeRepositoryImpl) CreateRunnerWithdrawal(ctx context.Context, withdrawal *models.RunnerWithdrawal) error {
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[CreateRunnerWithdrawal] 开启事务失败: %v", err)
		return fmt.Errorf("创建提现记录失败: %v", err)
	}

	_, err = tx.Insert(withdrawal)
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateRunnerWithdrawal] 创建提现记录失败: %v", err)
		return fmt.Errorf("创建提现记录失败: %v", err)
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[CreateRunnerWithdrawal] 提交事务失败: %v", err)
		return fmt.Errorf("创建提现记录失败: %v", err)
	}

	return nil
}

// DeleteRunnerWithdrawal 删除跑腿员提现记录
func (r *RunnerIncomeRepositoryImpl) DeleteRunnerWithdrawal(ctx context.Context, withdrawalID int64) error {
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[DeleteRunnerWithdrawal] 开启事务失败: %v", err)
		return fmt.Errorf("删除提现记录失败: %v", err)
	}

	withdrawal := &models.RunnerWithdrawal{ID: withdrawalID}
	_, err = tx.Delete(withdrawal)
	if err != nil {
		tx.Rollback()
		logs.Error("[DeleteRunnerWithdrawal] 删除提现记录失败: %v", err)
		return fmt.Errorf("删除提现记录失败: %v", err)
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[DeleteRunnerWithdrawal] 提交事务失败: %v", err)
		return fmt.Errorf("删除提现记录失败: %v", err)
	}

	return nil
}

// GetRunnerWithdrawals 获取跑腿员提现记录列表
func (r *RunnerIncomeRepositoryImpl) GetRunnerWithdrawals(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerWithdrawal, int64, error) {
	o := orm.NewOrm()
	var withdrawals []*models.RunnerWithdrawal

	// 注意：在ORM查询中使用"runnerID"而不是"runner_id"
	query := o.QueryTable(new(models.RunnerWithdrawal)).Filter("runnerID", runnerID)

	total, err := query.Count()
	if err != nil {
		logs.Error("[GetRunnerWithdrawals] 获取提现记录总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取提现记录列表失败: %v", err)
	}

	_, err = query.OrderBy("-create_time").Limit(pageSize, (page-1)*pageSize).All(&withdrawals)
	if err != nil {
		logs.Error("[GetRunnerWithdrawals] 获取提现记录列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取提现记录列表失败: %v", err)
	}

	return withdrawals, total, nil
}
