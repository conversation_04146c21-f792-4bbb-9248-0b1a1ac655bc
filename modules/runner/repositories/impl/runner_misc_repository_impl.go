/**
 * runner_misc_repository_impl.go
 * 跑腿员杂项仓库实现
 *
 * 本文件实现了跑腿员杂项数据操作接口，包括位置、申请、钱包等操作
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/runner/core"
	"o_mall_backend/modules/runner/models"
)

// RunnerMiscRepositoryImpl 跑腿员杂项仓库实现
type RunnerMiscRepositoryImpl struct {
}

var _ core.RunnerMiscRepository = (*RunnerMiscRepositoryImpl)(nil)

// NewRunnerMiscRepositoryImpl 创建跑腿员杂项仓库
func NewRunnerMiscRepositoryImpl() core.RunnerMiscRepository {
	return &RunnerMiscRepositoryImpl{}
}

// CreateRunnerApply 创建跑腿员申请
func (r *RunnerMiscRepositoryImpl) CreateRunnerApply(ctx context.Context, apply *models.RunnerApply) error {
	o := orm.NewOrm()
	_, err := o.Insert(apply)
	if err != nil {
		logs.Error("创建跑腿员申请失败: %v", err)
		return fmt.Errorf("创建跑腿员申请失败: %v", err)
	}
	return nil
}

// UpdateRunnerApply 更新跑腿员申请
func (r *RunnerMiscRepositoryImpl) UpdateRunnerApply(ctx context.Context, apply *models.RunnerApply) error {
	o := orm.NewOrm()
	_, err := o.Update(apply)
	if err != nil {
		logs.Error("更新跑腿员申请失败: %v", err)
		return fmt.Errorf("更新跑腿员申请失败: %v", err)
	}
	return nil
}

// GetRunnerApplyByID 根据ID获取跑腿员申请
func (r *RunnerMiscRepositoryImpl) GetRunnerApplyByID(ctx context.Context, applyID int64) (*models.RunnerApply, error) {
	o := orm.NewOrm()
	apply := &models.RunnerApply{ID: applyID}
	err := o.Read(apply)
	if err != nil {
		if errors.Is(err, orm.ErrNoRows) {
			return nil, nil
		}
		logs.Error("获取跑腿员申请失败: %v", err)
		return nil, fmt.Errorf("获取跑腿员申请失败: %v", err)
	}
	return apply, nil
}

// GetRunnerApplyByUserID 根据用户ID获取跑腿员申请
func (r *RunnerMiscRepositoryImpl) GetRunnerApplyByUserID(ctx context.Context, userID int64) (*models.RunnerApply, error) {
	o := orm.NewOrm()
	apply := &models.RunnerApply{}
	err := o.QueryTable(new(models.RunnerApply)).Filter("UserID", userID).OrderBy("-create_time").One(apply)
	if err != nil {
		if errors.Is(err, orm.ErrNoRows) {
			return nil, nil
		}
		logs.Error("根据用户ID获取跑腿员申请失败: %v", err)
		return nil, fmt.Errorf("获取跑腿员申请失败: %v", err)
	}
	return apply, nil
}

// CreateRunnerLocation 创建跑腿员位置记录
func (r *RunnerMiscRepositoryImpl) CreateRunnerLocation(ctx context.Context, location *models.RunnerLocation) error {
	o := orm.NewOrm()
	_, err := o.Insert(location)
	if err != nil {
		logs.Error("创建跑腿员位置记录失败: %v", err)
		return fmt.Errorf("创建跑腿员位置记录失败: %v", err)
	}
	return nil
}

// UpdateRunnerWallet 更新跑腿员钱包余额
func (r *RunnerMiscRepositoryImpl) UpdateRunnerWallet(ctx context.Context, runnerID int64, amount float64, isAdd bool) error {
	o := orm.NewOrm()
	runner := &models.Runner{ID: runnerID}
	err := o.Read(runner)
	if err != nil {
		logs.Error("[UpdateRunnerWallet] 获取跑腿员信息失败: %v", err)
		return fmt.Errorf("获取跑腿员信息失败: %v", err)
	}

	if isAdd {
		runner.Wallet += amount
	} else {
		if runner.Wallet < amount {
			return errors.New("钱包余额不足")
		}
		runner.Wallet -= amount
	}
	runner.UpdateTime = time.Now()

	_, err = o.Update(runner, "wallet", "update_time")
	if err != nil {
		logs.Error("[UpdateRunnerWallet] 更新跑腿员钱包余额失败: %v", err)
		return fmt.Errorf("更新跑腿员钱包余额失败: %v", err)
	}
	return nil
}
