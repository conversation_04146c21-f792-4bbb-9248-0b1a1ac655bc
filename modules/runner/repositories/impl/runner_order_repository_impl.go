/**
 * runner_order_repository_impl.go
 * 跑腿订单仓库实现
 *
 * 本文件实现了跑腿订单数据的持久化操作，包括订单的创建、查询和状态更新等
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/runner/core"
	"o_mall_backend/modules/runner/models"
)

// RunnerOrderRepositoryImpl 跑腿订单仓库实现
type RunnerOrderRepositoryImpl struct {
	runnerRepo core.RunnerRepository
}

var _ core.RunnerOrderRepository = (*RunnerOrderRepositoryImpl)(nil)

// NewRunnerOrderRepositoryImpl 创建跑腿订单仓库实例
func NewRunnerOrderRepositoryImpl(runnerRepo core.RunnerRepository) core.RunnerOrderRepository {
	return &RunnerOrderRepositoryImpl{
		runnerRepo: runnerRepo,
	}
}

// CreateRunnerOrder 创建跑腿订单
func (r *RunnerOrderRepositoryImpl) CreateRunnerOrder(ctx context.Context, order *models.RunnerOrder) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(order)
	if err != nil {
		logs.Error("创建跑腿订单失败: %v", err)
		return 0, fmt.Errorf("创建跑腿订单失败: %v", err)
	}
	return id, nil
}

// GetRunnerOrderByID 根据ID获取跑腿订单
func (r *RunnerOrderRepositoryImpl) GetRunnerOrderByID(ctx context.Context, orderID int64) (*models.RunnerOrder, error) {
	o := orm.NewOrm()
	order := &models.RunnerOrder{ID: orderID}
	err := o.Read(order)
	if err == orm.ErrNoRows {
		return nil, errors.New("订单不存在")
	}
	if err != nil {
		logs.Error("获取跑腿订单失败: %v", err)
		return nil, fmt.Errorf("获取跑腿订单失败: %v", err)
	}
	return order, nil
}

// GetRunnerOrderByOrderNo 根据订单号获取跑腿订单
func (r *RunnerOrderRepositoryImpl) GetRunnerOrderByOrderNo(ctx context.Context, orderNo string) (*models.RunnerOrder, error) {
	o := orm.NewOrm()
	order := &models.RunnerOrder{}
	err := o.QueryTable(new(models.RunnerOrder)).Filter("order_no", orderNo).One(order)
	if err == orm.ErrNoRows {
		return nil, errors.New("订单不存在")
	}
	if err != nil {
		logs.Error("获取跑腿订单失败: %v", err)
		return nil, fmt.Errorf("获取跑腿订单失败: %v", err)
	}
	return order, nil
}

// UpdateRunnerOrder 更新跑腿订单信息
func (r *RunnerOrderRepositoryImpl) UpdateRunnerOrder(ctx context.Context, order *models.RunnerOrder) error {
	o := orm.NewOrm()
	_, err := o.Update(order)
	if err != nil {
		logs.Error("更新跑腿订单失败: %v", err)
		return fmt.Errorf("更新跑腿订单失败: %v", err)
	}
	return nil
}

// UpdateRunnerOrderStatus 更新跑腿订单状态
func (r *RunnerOrderRepositoryImpl) UpdateRunnerOrderStatus(ctx context.Context, orderID int64, status int) error {
	o := orm.NewOrm()
	order, err := r.GetRunnerOrderByID(ctx, orderID)
	if err != nil {
		return err
	}

	order.Status = status
	order.UpdateTime = time.Now()
	_, err = o.Update(order, "Status", "UpdateTime")
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return fmt.Errorf("更新订单状态失败: %v", err)
	}
	return nil
}

// AcceptRunnerOrder 接单
func (r *RunnerOrderRepositoryImpl) AcceptRunnerOrder(ctx context.Context, orderID, runnerID int64) error {
	o := orm.NewOrm()
	order, err := r.GetRunnerOrderByID(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单状态
	if order.Status != 0 {
		return errors.New("订单已被接取或状态不允许接单")
	}

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[AcceptRunnerOrder] 开启事务失败: %v", err)
		return fmt.Errorf("接单失败: %v", err)
	}

	// 更新订单信息
	now := time.Now()
	order.RunnerID = runnerID
	order.Status = 1 // 已接单
	order.AcceptTime = now
	order.UpdateTime = now

	_, err = tx.Update(order, "runner_id", "status", "accept_time", "update_time")
	if err != nil {
		tx.Rollback()
		logs.Error("[AcceptRunnerOrder] 更新订单信息失败: %v", err)
		return fmt.Errorf("接单失败: %v", err)
	}

	// 更新跑腿员状态
	runner := &models.Runner{ID: runnerID}
	err = tx.Read(runner)
	if err != nil {
		tx.Rollback()
		logs.Error("[AcceptRunnerOrder] 获取跑腿员信息失败: %v", err)
		return fmt.Errorf("接单失败: %v", err)
	}

	runner.WorkingStatus = 2 // 配送中
	runner.UpdateTime = now

	_, err = tx.Update(runner, "working_status", "update_time")
	if err != nil {
		tx.Rollback()
		logs.Error("[AcceptRunnerOrder] 更新跑腿员状态失败: %v", err)
		return fmt.Errorf("接单失败: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[AcceptRunnerOrder] 提交事务失败: %v", err)
		return fmt.Errorf("接单失败: %v", err)
	}

	return nil
}

// CancelRunnerOrder 取消订单
func (r *RunnerOrderRepositoryImpl) CancelRunnerOrder(ctx context.Context, orderID int64, cancelReason string, cancelUserType int) error {
	o := orm.NewOrm()
	order, err := r.GetRunnerOrderByID(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单状态，只有待接单、已接单、取货中的订单可以取消
	if order.Status > 2 {
		return errors.New("订单状态不允许取消")
	}

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[CancelRunnerOrder] 开启事务失败: %v", err)
		return fmt.Errorf("取消订单失败: %v", err)
	}

	// 更新订单信息
	now := time.Now()
	order.Status = 5 // 已取消
	order.CancelTime = now
	order.CancelReason = cancelReason
	order.CancelUserType = cancelUserType
	order.UpdateTime = now

	_, err = tx.Update(order, "status", "cancel_time", "cancel_reason", "cancel_user_type", "update_time")
	if err != nil {
		tx.Rollback()
		logs.Error("[CancelRunnerOrder] 更新订单信息失败: %v", err)
		return fmt.Errorf("取消订单失败: %v", err)
	}

	// 如果有跑腿员接单，更新跑腿员状态和统计数据
	if order.RunnerID > 0 {
		runner := &models.Runner{ID: order.RunnerID}
		err = tx.Read(runner)
		if err != nil {
			tx.Rollback()
			logs.Error("[CancelRunnerOrder] 获取跑腿员信息失败: %v", err)
			return fmt.Errorf("取消订单失败: %v", err)
		}

		runner.WorkingStatus = 1 // 接单中
		runner.CancelCount++
		runner.UpdateTime = now

		_, err = tx.Update(runner, "working_status", "cancel_count", "update_time")
		if err != nil {
			tx.Rollback()
			logs.Error("[CancelRunnerOrder] 更新跑腿员状态失败: %v", err)
			return fmt.Errorf("取消订单失败: %v", err)
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[CancelRunnerOrder] 提交事务失败: %v", err)
		return fmt.Errorf("取消订单失败: %v", err)
	}

	return nil
}

// PickupRunnerOrder 取货
func (r *RunnerOrderRepositoryImpl) PickupRunnerOrder(ctx context.Context, orderID int64) error {
	o := orm.NewOrm()
	order, err := r.GetRunnerOrderByID(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单状态
	if order.Status != 1 {
		return errors.New("订单状态不允许取货")
	}

	// 更新订单信息
	now := time.Now()
	order.Status = 2 // 取货中
	order.PickupTime = now
	order.UpdateTime = now

	_, err = o.Update(order, "Status", "PickupTime", "UpdateTime")
	if err != nil {
		logs.Error("更新订单信息失败: %v", err)
		return fmt.Errorf("取货失败: %v", err)
	}

	return nil
}

// CompleteRunnerOrder 完成订单
func (r *RunnerOrderRepositoryImpl) CompleteRunnerOrder(ctx context.Context, orderID int64) error {
	order, err := r.GetRunnerOrderByID(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单状态
	if order.Status != 2 && order.Status != 3 {
		return errors.New("订单状态不允许完成")
	}

	// 开启事务
	txManager := NewTransactionManager()
	err = txManager.Begin()
	if err != nil {
		return fmt.Errorf("完成订单失败: %v", err)
	}
	defer func() {
		if err != nil {
			txManager.Rollback()
		}
	}()

	tx := txManager.GetTx().(orm.TxOrmer)
	now := time.Now()

	// 更新订单信息
	order.Status = 4 // 已完成
	order.DeliveryTime = now
	order.UpdateTime = now

	_, err = tx.Update(order, "status", "delivery_time", "update_time")
	if err != nil {
		logs.Error("[CompleteRunnerOrder] 更新订单信息失败: %v", err)
		return fmt.Errorf("完成订单失败: %v", err)
	}

	// 更新跑腿员状态和统计数据
	runner := &models.Runner{ID: order.RunnerID}
	err = tx.Read(runner)
	if err != nil {
		logs.Error("[CompleteRunnerOrder] 获取跑腿员信息失败: %v", err)
		return fmt.Errorf("完成订单失败: %v", err)
	}

	runner.WorkingStatus = 1 // 接单中
	runner.SuccessCount++
	runner.UpdateTime = now

	_, err = tx.Update(runner, "working_status", "success_count", "update_time")
	if err != nil {
		logs.Error("[CompleteRunnerOrder] 更新跑腿员状态失败: %v", err)
		return fmt.Errorf("完成订单失败: %v", err)
	}

	// 创建收入记录
	incomeLog := &models.RunnerIncomeLog{
		RunnerID:    order.RunnerID,
		OrderID:     order.ID,
		OrderNo:     order.OrderNo,
		Amount:      order.DeliveryFee,
		Type:        0, // 配送费
		Status:      0, // 未结算
		Description: fmt.Sprintf("订单%s配送费", order.OrderNo),
		CreateTime:  now,
		UpdateTime:  now,
	}

	_, err = tx.Insert(incomeLog)
	if err != nil {
		logs.Error("[CompleteRunnerOrder] 创建收入记录失败: %v", err)
		return fmt.Errorf("完成订单失败: %v", err)
	}

	// 如果有小费，创建小费收入记录
	if order.TipAmount > 0 {
		tipLog := &models.RunnerIncomeLog{
			RunnerID:    order.RunnerID,
			OrderID:     order.ID,
			OrderNo:     order.OrderNo,
			Amount:      order.TipAmount,
			Type:        1, // 小费
			Status:      0, // 未结算
			Description: fmt.Sprintf("订单%s小费", order.OrderNo),
			CreateTime:  now,
			UpdateTime:  now,
		}

		_, err = tx.Insert(tipLog)
		if err != nil {
			logs.Error("[CompleteRunnerOrder] 创建小费记录失败: %v", err)
			return fmt.Errorf("完成订单失败: %v", err)
		}
	}

	// 提交事务
	err = txManager.Commit()
	if err != nil {
		return fmt.Errorf("完成订单失败: %v", err)
	}

	return nil
}

// ListUserRunnerOrders 获取用户跑腿订单列表
func (r *RunnerOrderRepositoryImpl) ListUserRunnerOrders(ctx context.Context, userID int64, status, page, pageSize int) ([]*models.RunnerOrder, int64, error) {
	o := orm.NewOrm()
	var orders []*models.RunnerOrder

	query := o.QueryTable(new(models.RunnerOrder)).Filter("user_id", userID)
	if status > 0 {
		query = query.Filter("status", status)
	}

	total, err := query.Count()
	if err != nil {
		logs.Error("获取订单总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取订单列表失败: %v", err)
	}

	_, err = query.OrderBy("-create_time").Limit(pageSize, (page-1)*pageSize).All(&orders)
	if err != nil {
		logs.Error("获取订单列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取订单列表失败: %v", err)
	}

	return orders, total, nil
}

// ListRunnerOrders 获取跑腿员订单列表
func (r *RunnerOrderRepositoryImpl) ListRunnerOrders(ctx context.Context, runnerID int64, status, page, pageSize int) ([]*models.RunnerOrder, int64, error) {
	o := orm.NewOrm()
	var orders []*models.RunnerOrder

	query := o.QueryTable(new(models.RunnerOrder)).Filter("RunnerID", runnerID)
	if status > 0 {
		query = query.Filter("status", status)
	}

	total, err := query.Count()
	if err != nil {
		logs.Error("获取订单总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取订单列表失败: %v", err)
	}

	_, err = query.OrderBy("-create_time").Limit(pageSize, (page-1)*pageSize).All(&orders)
	if err != nil {
		logs.Error("获取订单列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取订单列表失败: %v", err)
	}

	return orders, total, nil
}

// GetRunnerStatistics 获取跑腿员统计信息
func (r *RunnerOrderRepositoryImpl) GetRunnerStatistics(ctx context.Context, runnerID int64) (map[string]interface{}, error) {
	o := orm.NewOrm()
	runner, err := r.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		return nil, err
	}

	// 获取各种时间段
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	weekStart := today.AddDate(0, 0, -int(now.Weekday()))
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 获取今日订单数和收入
	var todayOrderCount int64
	var todayIncome float64

	count, err := o.QueryTable(new(models.RunnerOrder)).
		Filter("runner_i_d", runnerID).
		Filter("create_time__gte", today).
		Filter("status", 4).
		Count()
	if err != nil {
		logs.Error("[GetRunnerStatistics] 获取今日订单数失败: %v", err)
	}
	todayOrderCount = count

	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_i_d = ? AND create_time >= ?",
		runnerID, today).QueryRow(&todayIncome)
	if err != nil {
		logs.Error("[GetRunnerStatistics] 获取今日收入失败: %v", err)
	}

	// 获取本周订单数和收入
	var weekOrderCount int64
	var weekIncome float64

	count, err = o.QueryTable(new(models.RunnerOrder)).
		Filter("runner_i_d", runnerID).
		Filter("create_time__gte", weekStart).
		Filter("status", 4).
		Count()
	if err != nil {
		logs.Error("[GetRunnerStatistics] 获取本周订单数失败: %v", err)
	}
	weekOrderCount = count

	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_i_d = ? AND create_time >= ?",
		runnerID, weekStart).QueryRow(&weekIncome)
	if err != nil {
		logs.Error("[GetRunnerStatistics] 获取本周收入失败: %v", err)
	}

	// 获取本月订单数和收入
	var monthOrderCount int64
	var monthIncome float64

	count, err = o.QueryTable(new(models.RunnerOrder)).
		Filter("runner_i_d", runnerID).
		Filter("create_time__gte", monthStart).
		Filter("status", 4).
		Count()
	if err != nil {
		logs.Error("[GetRunnerStatistics] 获取本月订单数失败: %v", err)
	}
	monthOrderCount = count

	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_i_d = ? AND create_time >= ?",
		runnerID, monthStart).QueryRow(&monthIncome)
	if err != nil {
		logs.Error("获取本月收入失败: %v", err)
	}

	// 计算完成率
	completionRate := 0.0
	if runner.OrderCount > 0 {
		completionRate = float64(runner.SuccessCount) / float64(runner.OrderCount) * 100
	}

	// 计算平均配送时间
	var averageTime int
	err = o.Raw(`
		SELECT IFNULL(AVG(TIMESTAMPDIFF(MINUTE, accept_time, delivery_time)), 0) 
		FROM runner_order 
		WHERE runner_i_d = ? AND status = 4
	`, runnerID).QueryRow(&averageTime)
	if err != nil {
		logs.Error("获取平均配送时间失败: %v", err)
	}

	// 获取总收入
	var totalIncome float64
	err = o.Raw("SELECT IFNULL(SUM(amount), 0) FROM runner_income_log WHERE runner_i_d = ?",
		runnerID).QueryRow(&totalIncome)
	if err != nil {
		logs.Error("获取总收入失败: %v", err)
	}

	// 组装统计信息
	statistics := map[string]interface{}{
		"total_order_count":     runner.OrderCount,
		"completed_order_count": runner.SuccessCount,
		"canceled_order_count":  runner.CancelCount,
		"completion_rate":       completionRate,
		"average_delivery_time": averageTime,
		"total_income":          totalIncome,
		"today_order_count":     todayOrderCount,
		"today_income":          todayIncome,
		"week_order_count":      weekOrderCount,
		"week_income":           weekIncome,
		"month_order_count":     monthOrderCount,
		"month_income":          monthIncome,
	}

	return statistics, nil
}

// GetRunnerAverageScore 获取跑腿员平均评分
func (r *RunnerOrderRepositoryImpl) GetRunnerAverageScore(ctx context.Context, runnerID int64) (float64, error) {
	o := orm.NewOrm()
	var avgScore float64
	err := o.Raw("SELECT IFNULL(AVG(score_by_user), 5.0) FROM runner_order WHERE runner_i_d = ? AND status = 4 AND score_by_user > 0",
		runnerID).QueryRow(&avgScore)
	if err != nil {
		logs.Error("[GetRunnerAverageScore] 获取跑腿员平均评分失败: %v", err)
		return 5.0, fmt.Errorf("获取平均评分失败: %v", err)
	}
	return avgScore, nil
}
