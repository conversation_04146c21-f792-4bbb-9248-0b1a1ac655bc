/**
 * runner_repository_impl.go
 * 跑腿员仓库实现
 *
 * 本文件实现了跑腿员数据的持久化操作，包括跑腿员信息的增删改查
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/runner/core"
	"o_mall_backend/modules/runner/models"
)

// RunnerRepositoryImpl 跑腿员仓库实现
type RunnerRepositoryImpl struct{}

var _ core.RunnerRepository = (*RunnerRepositoryImpl)(nil)

// NewRunnerRepositoryImpl 创建跑腿员仓库实例
func NewRunnerRepositoryImpl() core.RunnerRepository {
	return &RunnerRepositoryImpl{}
}

// CreateRunner 创建跑腿员
func (r *RunnerRepositoryImpl) CreateRunner(ctx context.Context, runner *models.Runner) error {
	o := orm.NewOrm()
	_, err := o.Insert(runner)
	if err != nil {
		logs.Error("创建跑腿员失败: %v", err)
		return fmt.Errorf("创建跑腿员失败: %v", err)
	}
	return nil
}

// GetRunnerByID 根据ID获取跑腿员
func (r *RunnerRepositoryImpl) GetRunnerByID(ctx context.Context, runnerID int64) (*models.Runner, error) {
	o := orm.NewOrm()
	runner := &models.Runner{ID: runnerID}
	err := o.Read(runner)
	if err == orm.ErrNoRows {
		return nil, errors.New("跑腿员不存在")
	}
	if err != nil {
		logs.Error("获取跑腿员失败: %v", err)
		return nil, fmt.Errorf("获取跑腿员失败: %v", err)
	}
	return runner, nil
}

// GetRunnerByUserID 根据用户ID获取跑腿员
func (r *RunnerRepositoryImpl) GetRunnerByUserID(ctx context.Context, userID int64) (*models.Runner, error) {
	o := orm.NewOrm()
	runner := &models.Runner{}
	err := o.QueryTable(new(models.Runner)).Filter("UserID", userID).One(runner)
	if err == orm.ErrNoRows {
		return nil, errors.New("跑腿员不存在")
	}
	if err != nil {
		logs.Error("获取跑腿员失败: %v", err)
		return nil, fmt.Errorf("获取跑腿员失败: %v", err)
	}
	return runner, nil
}

// UpdateRunner 更新跑腿员信息
func (r *RunnerRepositoryImpl) UpdateRunner(ctx context.Context, runner *models.Runner) error {
	o := orm.NewOrm()
	_, err := o.Update(runner)
	if err != nil {
		logs.Error("更新跑腿员失败: %v", err)
		return fmt.Errorf("更新跑腿员失败: %v", err)
	}
	return nil
}

// UpdateRunnerStatus 更新跑腿员状态
func (r *RunnerRepositoryImpl) UpdateRunnerStatus(ctx context.Context, runnerID int64, status int) error {
	o := orm.NewOrm()
	runner, err := r.GetRunnerByID(ctx, runnerID)
	if err != nil {
		return err
	}

	runner.Status = status
	runner.UpdateTime = time.Now()
	_, err = o.Update(runner, "Status", "UpdateTime")
	if err != nil {
		logs.Error("更新跑腿员状态失败: %v", err)
		return fmt.Errorf("更新跑腿员状态失败: %v", err)
	}
	return nil
}

// UpdateRunnerWorkingStatus 更新跑腿员工作状态
func (r *RunnerRepositoryImpl) UpdateRunnerWorkingStatus(ctx context.Context, runnerID int64, status int) error {
	o := orm.NewOrm()
	runner, err := r.GetRunnerByID(ctx, runnerID)
	if err != nil {
		return err
	}

	runner.WorkingStatus = status
	runner.UpdateTime = time.Now()
	_, err = o.Update(runner, "WorkingStatus", "UpdateTime")
	if err != nil {
		logs.Error("更新跑腿员工作状态失败: %v", err)
		return fmt.Errorf("更新跑腿员工作状态失败: %v", err)
	}
	return nil
}

// UpdateRunnerLocation 更新跑腿员位置
func (r *RunnerRepositoryImpl) UpdateRunnerLocation(ctx context.Context, runnerID int64, latitude, longitude float64) error {
	o := orm.NewOrm()
	runner, err := r.GetRunnerByID(ctx, runnerID)
	if err != nil {
		return err
	}

	runner.Latitude = latitude
	runner.Longitude = longitude
	runner.UpdateTime = time.Now()
	_, err = o.Update(runner, "Latitude", "Longitude", "UpdateTime")
	if err != nil {
		logs.Error("更新跑腿员位置失败: %v", err)
		return fmt.Errorf("更新跑腿员位置失败: %v", err)
	}
	return nil
}

// ListRunners 获取跑腿员列表
func (r *RunnerRepositoryImpl) ListRunners(ctx context.Context, page, pageSize int, status int) ([]*models.Runner, int64, error) {
	o := orm.NewOrm()
	var runners []*models.Runner

	query := o.QueryTable(new(models.Runner))
	if status > 0 {
		query = query.Filter("status", status)
	}

	total, err := query.Count()
	if err != nil {
		logs.Error("获取跑腿员总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取跑腿员总数失败: %v", err)
	}

	_, err = query.OrderBy("-create_time").Limit(pageSize, (page-1)*pageSize).All(&runners)
	if err != nil {
		logs.Error("获取跑腿员列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取跑腿员列表失败: %v", err)
	}

	return runners, total, nil
}

// ListNearbyRunners 获取附近跑腿员
func (r *RunnerRepositoryImpl) ListNearbyRunners(ctx context.Context, latitude, longitude, radius float64) ([]*models.Runner, error) {
	// 这里使用SQL直接查询，因为ORM可能不支持地理位置查询
	o := orm.NewOrm()
	var runners []*models.Runner

	// 使用地球上的Haversine公式计算距离
	sql := `
	SELECT *, 
		6371 * acos(
			cos(radians(?)) * cos(radians(latitude)) * 
			cos(radians(longitude) - radians(?)) + 
			sin(radians(?)) * sin(radians(latitude))
		) AS distance 
	FROM runner 
	WHERE status = 1 
	AND is_online = true 
	AND working_status IN (0, 1) 
	HAVING distance < ? 
	ORDER BY distance 
	LIMIT 20
	`

	_, err := o.Raw(sql, latitude, longitude, latitude, radius).QueryRows(&runners)
	if err != nil {
		logs.Error("获取附近跑腿员失败: %v", err)
		return nil, fmt.Errorf("获取附近跑腿员失败: %v", err)
	}

	return runners, nil
}

// CreateRunnerApply 创建跑腿员申请
func (r *RunnerRepositoryImpl) CreateRunnerApply(ctx context.Context, apply *models.RunnerApply) error {
	o := orm.NewOrm()
	_, err := o.Insert(apply)
	if err != nil {
		logs.Error("创建跑腿员申请失败: %v", err)
		return fmt.Errorf("创建跑腿员申请失败: %v", err)
	}
	return nil
}

// UpdateRunnerApply 更新跑腿员申请
func (r *RunnerRepositoryImpl) UpdateRunnerApply(ctx context.Context, apply *models.RunnerApply) error {
	o := orm.NewOrm()
	_, err := o.Update(apply)
	if err != nil {
		logs.Error("更新跑腿员申请失败: %v", err)
		return fmt.Errorf("更新跑腿员申请失败: %v", err)
	}
	return nil
}

// GetRunnerApplyByID 根据ID获取跑腿员申请
func (r *RunnerRepositoryImpl) GetRunnerApplyByID(ctx context.Context, applyID int64) (*models.RunnerApply, error) {
	o := orm.NewOrm()
	apply := &models.RunnerApply{ID: applyID}
	err := o.Read(apply)
	if err == orm.ErrNoRows {
		return nil, errors.New("申请不存在")
	}
	if err != nil {
		logs.Error("获取跑腿员申请失败: %v", err)
		return nil, fmt.Errorf("获取跑腿员申请失败: %v", err)
	}
	return apply, nil
}

// GetRunnerApplyByUserID 根据用户ID获取跑腿员申请
func (r *RunnerRepositoryImpl) GetRunnerApplyByUserID(ctx context.Context, userID int64) (*models.RunnerApply, error) {
	o := orm.NewOrm()
	apply := &models.RunnerApply{}
	err := o.QueryTable(new(models.RunnerApply)).Filter("user_id", userID).One(apply)
	if err == orm.ErrNoRows {
		return nil, errors.New("申请不存在")
	}
	if err != nil {
		logs.Error("获取跑腿员申请失败: %v", err)
		return nil, fmt.Errorf("获取跑腿员申请失败: %v", err)
	}
	return apply, nil
}

// CreateRunnerLocation 创建跑腿员位置记录
func (r *RunnerRepositoryImpl) CreateRunnerLocation(ctx context.Context, location *models.RunnerLocation) error {
	o := orm.NewOrm()
	_, err := o.Insert(location)
	if err != nil {
		logs.Error("创建跑腿员位置记录失败: %v", err)
		return fmt.Errorf("创建跑腿员位置记录失败: %v", err)
	}
	return nil
}

// UpdateRunnerWallet 更新跑腿员钱包余额
func (r *RunnerRepositoryImpl) UpdateRunnerWallet(ctx context.Context, runnerID int64, amount float64, isAdd bool) error {
	o := orm.NewOrm()
	runner, err := r.GetRunnerByID(ctx, runnerID)
	if err != nil {
		return err
	}

	if isAdd {
		runner.Wallet += amount
	} else {
		if runner.Wallet < amount {
			return errors.New("钱包余额不足")
		}
		runner.Wallet -= amount
	}
	runner.UpdateTime = time.Now()

	_, err = o.Update(runner, "Wallet", "UpdateTime")
	if err != nil {
		logs.Error("更新钱包余额失败: %v", err)
		return fmt.Errorf("更新钱包余额失败: %v", err)
	}
	return nil
}
