/**
 * runner_income_repository.go
 * 跑腿员收入仓库接口
 *
 * 本文件定义了跑腿员收入数据操作的接口，包括收入记录的增删改查等操作
 */

package repositories

import (
	"context"
	"time"

	"o_mall_backend/modules/runner/models"
)

// RunnerIncomeRepository 跑腿员收入仓库接口
type RunnerIncomeRepository interface {
	// RunnerIncome 跑腿员收入管理
	CreateRunnerIncomeLog(ctx context.Context, incomeLog *models.RunnerIncomeLog) (int64, error)
	GetRunnerIncome(ctx context.Context, runnerID int64) (float64, error)
	ListRunnerIncomeLogs(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerIncomeLog, int64, error)
	GetRunnerIncomeStatistics(ctx context.Context, runnerID int64, startTime, endTime time.Time) (map[string]interface{}, error)
	UpdateRunnerIncomeStatus(ctx context.Context, logID int64, status int) error

	// 新增方法
	GetRunnerTotalIncome(ctx context.Context, runnerID int64) (float64, error)
	GetRunnerWithdrawingAmount(ctx context.Context, runnerID int64) (float64, error)
	GetRunnerIncomeByTimeRange(ctx context.Context, runnerID int64, startTime, endTime time.Time) (float64, error)
	GetRunnerIncomeLogs(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerIncomeLog, int64, error)
	
	// 收入统计相关
	GetRunnerDailyIncome(ctx context.Context, runnerID int64) (float64, error)       // 获取今日收入
	GetRunnerWeeklyIncome(ctx context.Context, runnerID int64) (float64, error)      // 获取本周收入
	GetRunnerMonthlyIncome(ctx context.Context, runnerID int64) (float64, error)     // 获取本月收入
	CreateRunnerWithdrawal(ctx context.Context, withdrawal *models.RunnerWithdrawal) error
	DeleteRunnerWithdrawal(ctx context.Context, withdrawalID int64) error
	GetRunnerWithdrawals(ctx context.Context, runnerID int64, page, pageSize int) ([]*models.RunnerWithdrawal, int64, error)
}
