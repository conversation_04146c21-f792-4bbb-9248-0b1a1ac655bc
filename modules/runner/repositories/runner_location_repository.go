/**
 * 骑手位置仓库
 *
 * 本文件定义了骑手位置数据访问接口和实现，提供骑手位置数据的增删改查等功能
 */

package repositories

import (
	"context"
	
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/runner/models"
)

// RunnerLocationRepository 骑手位置仓库接口
type RunnerLocationRepository interface {
	// Create 创建骑手位置记录
	Create(ctx context.Context, runnerLocation *models.RunnerLocation) error
	
	// GetByID 根据ID获取骑手位置记录
	GetByID(ctx context.Context, id int64) (*models.RunnerLocation, error)
	
	// GetLatestByRunnerID 获取指定骑手最新的位置记录
	GetLatestByRunnerID(ctx context.Context, runnerID int64) (*models.RunnerLocation, error)
	
	// GetHistoryByRunnerID 获取指定骑手的历史位置记录
	GetHistoryByRunnerID(ctx context.Context, runnerID int64, limit, offset int) ([]*models.RunnerLocation, int64, error)
	
	// Delete 删除骑手位置记录
	Delete(ctx context.Context, id int64) error
}

// RunnerLocationRepositoryImpl 骑手位置仓库实现
type RunnerLocationRepositoryImpl struct{}

// NewRunnerLocationRepository 创建骑手位置仓库实例
func NewRunnerLocationRepository() RunnerLocationRepository {
	return &RunnerLocationRepositoryImpl{}
}

// Create 创建骑手位置记录
func (r *RunnerLocationRepositoryImpl) Create(ctx context.Context, runnerLocation *models.RunnerLocation) error {
	o := orm.NewOrm()
	_, err := o.Insert(runnerLocation)
	if err != nil {
		logs.Error("创建骑手位置记录失败: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取骑手位置记录
func (r *RunnerLocationRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.RunnerLocation, error) {
	o := orm.NewOrm()
	runnerLocation := &models.RunnerLocation{ID: id}
	err := o.Read(runnerLocation)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("根据ID查询骑手位置记录失败: %v", err)
		return nil, err
	}
	return runnerLocation, nil
}

// GetLatestByRunnerID 获取指定骑手最新的位置记录
func (r *RunnerLocationRepositoryImpl) GetLatestByRunnerID(ctx context.Context, runnerID int64) (*models.RunnerLocation, error) {
	o := orm.NewOrm()
	var runnerLocation models.RunnerLocation
	
	err := o.QueryTable(new(models.RunnerLocation)).
		Filter("RunnerID", runnerID).
		OrderBy("-create_time").  // 按创建时间倒序排序，获取最新记录
		Limit(1).
		One(&runnerLocation)
		
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("获取骑手最新位置记录失败: %v", err)
		return nil, err
	}
	
	return &runnerLocation, nil
}

// GetHistoryByRunnerID 获取指定骑手的历史位置记录
func (r *RunnerLocationRepositoryImpl) GetHistoryByRunnerID(ctx context.Context, runnerID int64, limit, offset int) ([]*models.RunnerLocation, int64, error) {
	o := orm.NewOrm()
	var runnerLocations []*models.RunnerLocation
	
	// 查询总记录数
	qs := o.QueryTable(new(models.RunnerLocation)).Filter("runner_id", runnerID)
	count, err := qs.Count()
	if err != nil {
		logs.Error("统计骑手位置记录数量失败: %v", err)
		return nil, 0, err
	}
	
	// 没有数据，返回空列表
	if count == 0 {
		return []*models.RunnerLocation{}, 0, nil
	}
	
	// 查询历史记录
	_, err = qs.OrderBy("-create_time").Limit(limit, offset).All(&runnerLocations)
	if err != nil {
		logs.Error("查询骑手历史位置记录失败: %v", err)
		return nil, 0, err
	}
	
	return runnerLocations, count, nil
}

// Delete 删除骑手位置记录
func (r *RunnerLocationRepositoryImpl) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	runnerLocation := &models.RunnerLocation{ID: id}
	_, err := o.Delete(runnerLocation)
	if err != nil {
		logs.Error("删除骑手位置记录失败: %v", err)
		return err
	}
	return nil
}
