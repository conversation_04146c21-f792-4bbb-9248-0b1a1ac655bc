/**
 * runner_misc_repository.go
 * 跑腿员杂项仓库接口
 * 
 * 本文件定义了跑腿员相关的杂项数据操作接口，包括位置、申请、钱包等操作
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/runner/models"
)

// RunnerMiscRepository 跑腿员杂项仓库接口
type RunnerMiscRepository interface {
	// CreateRunnerApply 创建跑腿员申请
	CreateRunnerApply(ctx context.Context, apply *models.RunnerApply) error
	// UpdateRunnerApply 更新跑腿员申请
	UpdateRunnerApply(ctx context.Context, apply *models.RunnerApply) error
	// GetRunnerApplyByID 根据ID获取跑腿员申请
	GetRunnerApplyByID(ctx context.Context, applyID int64) (*models.RunnerApply, error)
	// GetRunnerApplyByUserID 根据用户ID获取跑腿员申请
	GetRunnerApplyByUserID(ctx context.Context, userID int64) (*models.RunnerApply, error)
	// CreateRunnerLocation 创建跑腿员位置记录
	CreateRunnerLocation(ctx context.Context, location *models.RunnerLocation) error
	// UpdateRunnerWallet 更新跑腿员钱包余额
	UpdateRunnerWallet(ctx context.Context, runnerID int64, amount float64, isAdd bool) error
}
