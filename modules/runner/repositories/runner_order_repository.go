/**
 * runner_order_repository.go
 * 跑腿订单仓库接口
 *
 * 本文件定义了跑腿订单数据操作的接口，包括订单的增删改查等操作
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/runner/models"
)

// RunnerOrderRepository 跑腿订单仓库接口
type RunnerOrderRepository interface {
	// RunnerOrder 跑腿订单管理
	CreateRunnerOrder(ctx context.Context, order *models.RunnerOrder) (int64, error)
	GetRunnerOrderByID(ctx context.Context, orderID int64) (*models.RunnerOrder, error)
	GetRunnerOrderByOrderNo(ctx context.Context, orderNo string) (*models.RunnerOrder, error)
	UpdateRunnerOrder(ctx context.Context, order *models.RunnerOrder) error
	UpdateRunnerOrderStatus(ctx context.Context, orderID int64, status int) error
	AcceptRunnerOrder(ctx context.Context, orderID, runnerID int64) error
	CancelRunnerOrder(ctx context.Context, orderID int64, cancelReason string, cancelUserType int) error
	PickupRunnerOrder(ctx context.Context, orderID int64) error
	CompleteRunnerOrder(ctx context.Context, orderID int64) error
	ListUserRunnerOrders(ctx context.Context, userID int64, status, page, pageSize int) ([]*models.RunnerOrder, int64, error)
	ListRunnerOrders(ctx context.Context, runnerID int64, status, page, pageSize int) ([]*models.RunnerOrder, int64, error)
	GetRunnerStatistics(ctx context.Context, runnerID int64) (map[string]interface{}, error)
	GetRunnerAverageScore(ctx context.Context, runnerID int64) (float64, error)
}
