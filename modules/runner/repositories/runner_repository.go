/**
 * runner_repository.go
 * 跑腿员仓库接口
 *
 * 本文件定义了跑腿员数据操作的接口，包括跑腿员的增删改查等操作
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/runner/models"
)

// RunnerRepository 跑腿员仓库接口
type RunnerRepository interface {
	// Runner 跑腿员管理
	CreateRunner(ctx context.Context, runner *models.Runner) (int64, error)
	GetRunnerByID(ctx context.Context, runnerID int64) (*models.Runner, error)
	GetRunnerByUserID(ctx context.Context, userID int64) (*models.Runner, error)
	UpdateRunner(ctx context.Context, runner *models.Runner) error
	UpdateRunnerStatus(ctx context.Context, runnerID int64, status int, remark string) error
	UpdateRunnerLocation(ctx context.Context, runnerID int64, latitude, longitude float64, address string) error
	ListRunners(ctx context.Context, page, pageSize int, status int) ([]*models.Runner, int64, error)
	GetNearbyRunners(ctx context.Context, latitude, longitude, radius float64, limit int) ([]*models.Runner, error)

	// 兼容方法
	// GetByID 根据ID获取跑腿员信息(与GetRunnerByID相同)
	GetByID(ctx context.Context, runnerID int64) (*models.Runner, error)
	// Update 更新跑腿员信息(与UpdateRunner相同)
	Update(ctx context.Context, runner *models.Runner) error
	// FindAvailable 查询可用的跑腿员
	FindAvailable(ctx context.Context) ([]*models.Runner, error)
}
