/**
 * runner_repository_impl.go
 * 跑腿员仓库实现
 *
 * 本文件实现了跑腿员仓库接口，提供跑腿员数据的存取功能
 * 通过Beego ORM操作数据库，实现跑腿员数据的增删改查
 */

package repositories

import (
	"context"
	"time"
	
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/runner/models"
)

// RunnerRepositoryImpl 跑腿员仓库实现
type RunnerRepositoryImpl struct{}

// NewRunnerRepository 创建跑腿员仓库实例
func NewRunnerRepository() RunnerRepository {
	return &RunnerRepositoryImpl{}
}

// CreateRunner 创建跑腿员
func (r *RunnerRepositoryImpl) CreateRunner(ctx context.Context, runner *models.Runner) (int64, error) {
	o := orm.NewOrm()
	
	// 设置创建时间
	if runner.CreateTime.IsZero() {
		runner.CreateTime = time.Now()
	}
	
	// 插入数据
	id, err := o.Insert(runner)
	if err != nil {
		logs.Error("创建跑腿员失败: %v", err)
		return 0, err
	}
	
	return id, nil
}

// GetRunnerByID 根据ID获取跑腿员信息
func (r *RunnerRepositoryImpl) GetRunnerByID(ctx context.Context, runnerID int64) (*models.Runner, error) {
	o := orm.NewOrm()
	
	// 构建查询条件
	runner := &models.Runner{ID: runnerID}
	
	// 查询数据
	err := o.Read(runner)
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有找到数据
			return nil, nil
		}
		logs.Error("查询跑腿员失败: %v", err)
		return nil, err
	}
	
	return runner, nil
}

// GetByID 根据ID获取跑腿员信息(兼容旧方法名)
func (r *RunnerRepositoryImpl) GetByID(ctx context.Context, runnerID int64) (*models.Runner, error) {
	return r.GetRunnerByID(ctx, runnerID)
}

// GetRunnerByUserID 根据用户ID获取跑腿员信息
func (r *RunnerRepositoryImpl) GetRunnerByUserID(ctx context.Context, userID int64) (*models.Runner, error) {
	o := orm.NewOrm()
	
	// 构建查询条件
	runner := &models.Runner{UserID: userID}
	
	// 查询数据
	err := o.Read(runner, "user_id")
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有找到数据
			return nil, nil
		}
		logs.Error("根据用户ID查询跑腿员失败: %v", err)
		return nil, err
	}
	
	return runner, nil
}

// UpdateRunner 更新跑腿员信息
func (r *RunnerRepositoryImpl) UpdateRunner(ctx context.Context, runner *models.Runner) error {
	o := orm.NewOrm()
	
	// 设置更新时间
	runner.UpdateTime = time.Now()
	
	// 更新数据，只更新指定字段
	_, err := o.Update(runner, "real_name", "mobile", "id_card", "id_card_front", "id_card_back",
		"latitude", "longitude", "address", "status", "working_status", "is_online",
		"score", "order_count", "success_count", "last_online_time", "update_time")
	
	if err != nil {
		logs.Error("更新跑腿员信息失败: %v", err)
		return err
	}
	
	return nil
}

// Update 更新跑腿员信息(兼容旧方法名)
func (r *RunnerRepositoryImpl) Update(ctx context.Context, runner *models.Runner) error {
	return r.UpdateRunner(ctx, runner)
}

// UpdateRunnerStatus 更新跑腿员状态
func (r *RunnerRepositoryImpl) UpdateRunnerStatus(ctx context.Context, runnerID int64, status int, remark string) error {
	o := orm.NewOrm()
	
	// 构建更新对象
	runner := &models.Runner{
		ID:         runnerID,
		Status:     status,
		Remark:     remark,
		UpdateTime: time.Now(),
	}
	
	// 更新状态和备注
	_, err := o.Update(runner, "status", "remark", "update_time")
	
	if err != nil {
		logs.Error("更新跑腿员状态失败: %v", err)
		return err
	}
	
	return nil
}

// UpdateRunnerLocation 更新跑腿员位置信息
func (r *RunnerRepositoryImpl) UpdateRunnerLocation(ctx context.Context, runnerID int64, latitude, longitude float64, address string) error {
	o := orm.NewOrm()
	
	// 构建更新对象
	runner := &models.Runner{
		ID:           runnerID,
		Latitude:     latitude,
		Longitude:    longitude,
		CurrentLocation: address,  // 使用CurrentLocation存储地址信息
		UpdateTime:   time.Now(),
	}
	
	// 更新位置信息
	_, err := o.Update(runner, "latitude", "longitude", "current_location", "update_time")
	
	if err != nil {
		logs.Error("更新跑腿员位置信息失败: %v", err)
		return err
	}
	
	return nil
}

// ListRunners 获取跑腿员列表
func (r *RunnerRepositoryImpl) ListRunners(ctx context.Context, page, pageSize int, status int) ([]*models.Runner, int64, error) {
	o := orm.NewOrm()
	
	// 构建查询条件
	qs := o.QueryTable(new(models.Runner))
	
	// 筛选状态
	if status > -1 {
		qs = qs.Filter("status", status)
	}
	
	// 计算总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计跑腿员总数失败: %v", err)
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	
	// 查询数据
	var runners []*models.Runner
	_, err = qs.OrderBy("-create_time").Limit(pageSize, offset).All(&runners)
	if err != nil {
		logs.Error("查询跑腿员列表失败: %v", err)
		return nil, 0, err
	}
	
	return runners, total, nil
}

// GetNearbyRunners 获取附近的跑腿员
func (r *RunnerRepositoryImpl) GetNearbyRunners(ctx context.Context, latitude, longitude, radius float64, limit int) ([]*models.Runner, error) {
	// 这里需要复杂的地理位置查询，暂时简化处理
	// 实际实现可能需要使用数据库的地理位置函数或GeoHash等算法
	o := orm.NewOrm()
	
	// 简单实现：获取所有跑腿员，在应用层筛选
	var runners []*models.Runner
	_, err := o.QueryTable(new(models.Runner)).Filter("status", 1).All(&runners)
	if err != nil {
		logs.Error("查询跑腿员列表失败: %v", err)
		return nil, err
	}
	
	return runners, nil
}

// FindAvailable 查询可用的跑腿员
func (r *RunnerRepositoryImpl) FindAvailable(ctx context.Context) ([]*models.Runner, error) {
	o := orm.NewOrm()
	
	// 查询状态正常且在线的跑腿员
	var runners []*models.Runner
	_, err := o.QueryTable(new(models.Runner)).
		Filter("status", 1).  // 状态正常
		Filter("is_online", true).  // 在线
		All(&runners)
		
	if err != nil {
		logs.Error("查询可用跑腿员失败: %v", err)
		return nil, err
	}
	
	logs.Info("查询到%d个可用跑腿员", len(runners))
	return runners, nil
}
