/**
 * 跑腿模块路由注册
 *
 * 该文件实现了跑腿模块的路由注册，将API接口与控制器方法关联。
 * 路由定义了API的访问路径、HTTP方法以及是否需要认证。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/runner/controllers"
)

// InitRouters 初始化跑腿模块路由
func InitRouters() {
	// 获取API前缀配置
	apiPrefix, _ := web.AppConfig.String("api_prefix")
	apiVersion, _ := web.AppConfig.String("api_version")
	
	// 使用配置拼接完整路径前缀
	prefixPath := "/" + apiPrefix + "/" + apiVersion
	
	// 管理员跑腿员管理路由
	adminRunnerNS := web.NewNamespace(prefixPath + "/admin/secured",
		// 添加管理员认证中间件
		web.NSBefore(middlewares.JWTFilter),

		// 跑腿员管理相关路由
		web.NSRouter("/runners", &controllers.AdminRunnerController{}, "get:ListRunners"),
		web.NSRouter("/runners/:id/detail", &controllers.AdminRunnerController{}, "get:GetRunnerDetail"),
		web.NSRouter("/runners/:id/audit", &controllers.AdminRunnerController{}, "put:AuditRunner"),
		web.NSRouter("/runners/:id/status", &controllers.AdminRunnerController{}, "put:UpdateRunnerStatus"),
		web.NSRouter("/runners/:id", &controllers.AdminRunnerController{}, "delete:DeleteRunner"),
		web.NSRouter("/runners/statistics", &controllers.AdminRunnerController{}, "get:GetRunnerStatistics"),
		web.NSRouter("/runners/:id/remark", &controllers.AdminRunnerController{}, "put:UpdateRunnerRemark"),
		web.NSRouter("/runners/:id/orders", &controllers.AdminRunnerController{}, "get:GetRunnerOrders"),
	)

	// 跑腿员相关路由
	runnerNS := web.NewNamespace(prefixPath + "/runner",
		// 无需认证的路由
		web.NSRouter("/nearby", &controllers.RunnerController{}, "post:GetNearbyRunners"),
		web.NSRouter("/delivery-fee/calculate", &controllers.RunnerController{}, "post:CalculateDeliveryFee"),
		// 注意：要添加登录相关API，需要先在RunnerController中实现相应方法
		// web.NSRouter("/login", &controllers.RunnerController{}, "post:Login"),
		// web.NSRouter("/register-account", &controllers.RunnerController{}, "post:RegisterAccount"),
		// web.NSRouter("/refresh-token", &controllers.RunnerController{}, "post:RefreshToken"),

		// 需要认证的路由
		web.NSNamespace("/secured",
			// 添加JWT中间件
			web.NSBefore(middlewares.JWTFilter),

			// 跑腿员相关
			web.NSRouter("/register", &controllers.RunnerController{}, "post:Register"),
			web.NSRouter("/apply-status", &controllers.RunnerController{}, "get:GetApplyStatus"),

			web.NSRouter("/info", &controllers.RunnerController{}, "get:GetRunnerInfo"),
			web.NSRouter("/:id/detail", &controllers.RunnerController{}, "get:GetRunnerDetail"),
			web.NSRouter("/status", &controllers.RunnerController{}, "put:UpdateWorkingStatus"),
			web.NSRouter("/audit-status", &controllers.RunnerController{}, "put:UpdateStatus"),
			web.NSRouter("/online-status", &controllers.RunnerController{}, "put:UpdateOnlineStatus"),
			web.NSRouter("/today-stats", &controllers.RunnerController{}, "get:GetTodayStats"),
			web.NSRouter("/profile", &controllers.RunnerController{}, "get:GetProfile"),
			web.NSRouter("/payment-accounts", &controllers.RunnerController{}, "get:GetPaymentAccounts"),
			web.NSRouter("/notification-settings", &controllers.RunnerController{}, "get:GetNotificationSettings"),
			web.NSRouter("/service-settings", &controllers.RunnerController{}, "get:GetServiceSettings"),

			web.NSRouter("/location", &controllers.RunnerController{}, "put:UpdateLocation"),
			web.NSRouter("/income", &controllers.RunnerController{}, "get:GetRunnerIncome"),
			web.NSRouter("/income-list", &controllers.RunnerController{}, "get:ListIncomeLogs"),
			web.NSRouter("/withdraw", &controllers.RunnerController{}, "post:ApplyWithdrawal"),
			web.NSRouter("/withdraw-list", &controllers.RunnerController{}, "get:ListWithdrawals"),
			web.NSRouter("/statistics", &controllers.RunnerController{}, "get:GetStatistics"),
			web.NSRouter("/balance", &controllers.RunnerController{}, "get:GetBalance"),
			web.NSRouter("/income-stats", &controllers.RunnerController{}, "get:GetIncomeStats"),
		),
	)

	// 跑腿订单相关路由
	orderNS := web.NewNamespace("/api/v1/runner-order",
		// 需要认证的路由
		web.NSBefore(middlewares.JWTFilter),

		// 订单相关 - 注意：具体路由必须放在通配路由(如/:id)之前
		// 创建与查询订单
		web.NSRouter("/", &controllers.RunnerOrderController{}, "post:CreateOrder"),
		web.NSRouter("/user/list", &controllers.RunnerOrderController{}, "get:ListUserOrders"),
		web.NSRouter("/runner/list", &controllers.RunnerOrderController{}, "get:ListRunnerOrders"),

		// 订单配送流程 - 按业务流程顺序排列
		web.NSRouter("/accept", &controllers.RunnerOrderController{}, "post:AcceptOrder"),           // 接单
		web.NSRouter("/pickup", &controllers.RunnerOrderController{}, "post:PickupOrder"),           // 取货
		web.NSRouter("/start-delivery", &controllers.RunnerOrderController{}, "post:StartDelivery"), // 开始配送
		web.NSRouter("/complete", &controllers.RunnerOrderController{}, "post:CompleteOrder"),       // 完成配送

		// 其他订单操作
		web.NSRouter("/cancel", &controllers.RunnerOrderController{}, "post:CancelOrder"), // 取消订单
		web.NSRouter("/user/rate", &controllers.RunnerOrderController{}, "post:UserRateOrder"),
		web.NSRouter("/runner/rate", &controllers.RunnerOrderController{}, "post:RunnerRateOrder"),
		// 统计数据路由
		web.NSRouter("/stats", &controllers.RunnerOrderController{}, "get:GetStats"),
		// 通配路由放在最后
		web.NSRouter("/:id", &controllers.RunnerOrderController{}, "get:GetOrderDetail"),
	)

	// 注册命名空间
	web.AddNamespace(adminRunnerNS)
	web.AddNamespace(runnerNS)
	web.AddNamespace(orderNS)
}
