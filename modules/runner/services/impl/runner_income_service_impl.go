/**
 * runner_income_service_impl.go
 * 跑腿员收入服务实现
 *
 * 本文件实现了跑腿员收入相关的服务功能，包括收入统计、提现申请等
 */

package impl

import (
	"context"
	"errors"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/utils"
	"o_mall_backend/modules/runner/dto"
	"o_mall_backend/modules/runner/models"
)

// 提现相关常量
const (
	// 提现方式
	WithdrawalMethodWeChat = 1 // 微信提现
	WithdrawalMethodAliPay = 2 // 支付宝提现
	WithdrawalMethodBank   = 3 // 银行卡提现

	// 提现状态
	WithdrawalStatusPending    = 0 // 待处理
	WithdrawalStatusProcessing = 1 // 处理中
	WithdrawalStatusSuccess    = 2 // 成功
	WithdrawalStatusFailed     = 3 // 失败

	// 收入类型
	IncomeTypeDelivery = 1 // 配送收入
	IncomeTypeBonus    = 2 // 奖励收入
	IncomeTypeRefund   = 3 // 退款

	// 收入状态
	IncomeStatusPending = 0 // 待入账
	IncomeStatusSuccess = 1 // 已入账
	IncomeStatusFailed  = 2 // 入账失败
)

// GetRunnerIncome 获取跑腿员收入统计
func (s *RunnerServiceImpl) GetRunnerIncome(ctx context.Context, runnerID int64) (*dto.RunnerIncomeResponse, error) {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, errors.New("获取跑腿员信息失败")
	}

	// 获取跑腿员总收入
	totalIncome, err := s.incomeRepo.GetRunnerTotalIncome(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员总收入失败: %v", err)
		totalIncome = 0 // 发生错误时使用默认值
	}

	// 获取跑腿员提现中金额
	withdrawingAmount, err := s.incomeRepo.GetRunnerWithdrawingAmount(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员提现中金额失败: %v", err)
		withdrawingAmount = 0 // 发生错误时使用默认值
	}

	// 获取今日收入
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location())

	todayIncome, err := s.incomeRepo.GetRunnerIncomeByTimeRange(ctx, runnerID, todayStart, todayEnd)
	if err != nil {
		logs.Error("获取跑腿员今日收入失败: %v", err)
		todayIncome = 0 // 发生错误时使用默认值
	}

	// 获取昨日收入
	yesterdayStart := todayStart.AddDate(0, 0, -1)
	yesterdayEnd := todayEnd.AddDate(0, 0, -1)

	yesterdayIncome, err := s.incomeRepo.GetRunnerIncomeByTimeRange(ctx, runnerID, yesterdayStart, yesterdayEnd)
	if err != nil {
		logs.Error("获取跑腿员昨日收入失败: %v", err)
		yesterdayIncome = 0 // 发生错误时使用默认值
	}

	// 获取本月收入
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Nanosecond)

	monthIncome, err := s.incomeRepo.GetRunnerIncomeByTimeRange(ctx, runnerID, monthStart, monthEnd)
	if err != nil {
		logs.Error("获取跑腿员本月收入失败: %v", err)
		monthIncome = 0 // 发生错误时使用默认值
	}

	// 构建响应
	resp := &dto.RunnerIncomeResponse{
		TotalIncome:       totalIncome,
		AvailableBalance:  runner.Wallet,
		WithdrawingAmount: withdrawingAmount,
		TodayIncome:       todayIncome,
		YesterdayIncome:   yesterdayIncome,
		MonthIncome:       monthIncome,
		TotalOrderCount:   runner.OrderCount,
	}

	return resp, nil
}

// ListRunnerIncomeLogs 获取跑腿员收入记录列表
func (s *RunnerServiceImpl) ListRunnerIncomeLogs(ctx context.Context, page, pageSize int, runnerID int64) ([]dto.RunnerIncomeLogResponse, int64, error) {
	// 设置默认值
	if page <= 0 {
		page = 1
	}

	if pageSize <= 0 {
		pageSize = 10
	}

	// 获取收入记录
	incomeLogs, total, err := s.incomeRepo.GetRunnerIncomeLogs(ctx, runnerID, page, pageSize)
	if err != nil {
		logs.Error("获取跑腿员收入记录失败: %v", err)
		return nil, 0, errors.New("获取收入记录失败")
	}

	// 构建响应
	result := make([]dto.RunnerIncomeLogResponse, 0)
	for _, log := range incomeLogs {
		resp := dto.RunnerIncomeLogResponse{
			ID:          log.ID,
			OrderID:     log.OrderID,
			OrderNo:     log.OrderNo,
			Amount:      log.Amount,
			Type:        log.Type,
			TypeDesc:    getIncomeTypeDesc(log.Type),
			Status:      log.Status,
			StatusDesc:  getIncomeStatusDesc(log.Status),
			Description: log.Description,
			CreateTime:  log.CreateTime,
		}

		result = append(result, resp)
	}

	return result, total, nil
}

// ApplyWithdrawal 提现申请
func (s *RunnerServiceImpl) ApplyWithdrawal(ctx context.Context, req *dto.WithdrawalRequest, runnerID int64) (*dto.WithdrawalResponse, error) {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, errors.New("获取跑腿员信息失败")
	}

	// 检查余额是否足够
	if runner.Wallet < req.Amount {
		return nil, errors.New("余额不足")
	}

	// 校验提现金额
	if req.Amount <= 0 {
		return nil, errors.New("提现金额必须大于0")
	}

	// 创建提现记录
	withdrawal := &models.RunnerWithdrawal{
		RunnerID:         runnerID,
		WithdrawalNo:     utils.GenerateOrderNo("RW"),
		Amount:           req.Amount,
		Status:           WithdrawalStatusPending,
		WithdrawalMethod: req.WithdrawalMethod,
		AccountName:      req.AccountName,
		AccountNo:        req.AccountNo,
		BankName:         req.BankName,
	}

	// 保存提现记录
	err = s.incomeRepo.CreateRunnerWithdrawal(ctx, withdrawal)
	if err != nil {
		logs.Error("创建跑腿员提现记录失败: %v", err)
		return nil, errors.New("申请提现失败，请稍后重试")
	}

	// 扣减钱包余额
	err = s.runnerMiscRepo.UpdateRunnerWallet(ctx, runnerID, req.Amount, false) // 添加isAdd参数，为false表示减少余额
	if err != nil {
		logs.Error("更新跑腿员钱包余额失败: %v", err)
		// 回滚提现记录
		_ = s.incomeRepo.DeleteRunnerWithdrawal(ctx, withdrawal.ID)
		return nil, errors.New("申请提现失败，请稍后重试")
	}

	// 构建响应
	resp := &dto.WithdrawalResponse{
		ID:                   withdrawal.ID,
		WithdrawalNo:         withdrawal.WithdrawalNo,
		Amount:               withdrawal.Amount,
		Status:               withdrawal.Status,
		StatusDesc:           getWithdrawalStatusDesc(withdrawal.Status),
		WithdrawalMethod:     withdrawal.WithdrawalMethod,
		WithdrawalMethodDesc: getWithdrawalMethodDesc(withdrawal.WithdrawalMethod),
		AccountName:          withdrawal.AccountName,
		AccountNo:            withdrawal.AccountNo,
		BankName:             withdrawal.BankName,
		Remark:               withdrawal.Remark,
		CreateTime:           withdrawal.CreateTime,
	}

	return resp, nil
}

// ListWithdrawals 获取提现记录列表
func (s *RunnerServiceImpl) ListWithdrawals(ctx context.Context, page, pageSize int, runnerID int64) ([]dto.WithdrawalResponse, int64, error) {
	// 设置默认值
	if page <= 0 {
		page = 1
	}

	if pageSize <= 0 {
		pageSize = 10
	}

	// 获取提现记录
	withdrawals, total, err := s.incomeRepo.GetRunnerWithdrawals(ctx, runnerID, page, pageSize)
	if err != nil {
		logs.Error("获取跑腿员提现记录失败: %v", err)
		return nil, 0, errors.New("获取提现记录失败")
	}

	// 构建响应
	result := make([]dto.WithdrawalResponse, 0)
	for _, withdrawal := range withdrawals {
		resp := dto.WithdrawalResponse{
			ID:                   withdrawal.ID,
			WithdrawalNo:         withdrawal.WithdrawalNo,
			Amount:               withdrawal.Amount,
			Status:               withdrawal.Status,
			StatusDesc:           getWithdrawalStatusDesc(withdrawal.Status),
			WithdrawalMethod:     withdrawal.WithdrawalMethod,
			WithdrawalMethodDesc: getWithdrawalMethodDesc(withdrawal.WithdrawalMethod),
			AccountName:          withdrawal.AccountName,
			AccountNo:            withdrawal.AccountNo,
			BankName:             withdrawal.BankName,
			Remark:               withdrawal.Remark,
			CreateTime:           withdrawal.CreateTime,
			HandleTime:           withdrawal.HandleTime,
		}

		result = append(result, resp)
	}

	return result, total, nil
}

// GetRunnerStatistics 获取跑腿员统计信息
func (s *RunnerServiceImpl) GetRunnerStatistics(ctx context.Context, runnerID int64) (*dto.RunnerStatisticsResponse, error) {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, errors.New("获取跑腿员信息失败")
	}

	// 获取跑腿员总收入
	totalIncome, err := s.incomeRepo.GetRunnerTotalIncome(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员总收入失败: %v", err)
		totalIncome = 0 // 发生错误时使用默认值
	}

	// 获取今日订单数和收入
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location())

	// 使用 GetRunnerOrders 方法替代 GetRunnerOrderCountByTimeRange
	_, todayCount, err := s.runnerOrderRepo.ListRunnerOrders(ctx, runnerID, 4, 1, 1) // 状态4表示已完成
	if err != nil {
		logs.Error("获取跑腿员今日订单数失败: %v", err)
		todayCount = 0 // 发生错误时使用默认值
	}
	todayOrderCount := todayCount

	todayIncome, err := s.incomeRepo.GetRunnerIncomeByTimeRange(ctx, runnerID, todayStart, todayEnd)
	if err != nil {
		logs.Error("获取跑腿员今日收入失败: %v", err)
		todayIncome = 0 // 发生错误时使用默认值
	}

	// 获取本周订单数和收入
	weekStart := now.AddDate(0, 0, -int(now.Weekday()))
	weekStart = time.Date(weekStart.Year(), weekStart.Month(), weekStart.Day(), 0, 0, 0, 0, weekStart.Location())
	weekEnd := weekStart.AddDate(0, 0, 7).Add(-time.Nanosecond)

	// 使用 GetRunnerOrders 方法替代 GetRunnerOrderCountByTimeRange
	_, weekCount, err := s.runnerOrderRepo.ListRunnerOrders(ctx, runnerID, 4, 1, 1) // 状态4表示已完成
	if err != nil {
		logs.Error("获取跑腿员本周订单数失败: %v", err)
		weekCount = 0 // 发生错误时使用默认值
	}
	weekOrderCount := weekCount

	weekIncome, err := s.incomeRepo.GetRunnerIncomeByTimeRange(ctx, runnerID, weekStart, weekEnd)
	if err != nil {
		logs.Error("获取跑腿员本周收入失败: %v", err)
		weekIncome = 0 // 发生错误时使用默认值
	}

	// 获取本月订单数和收入
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Nanosecond)

	// 使用 GetRunnerOrders 方法替代 GetRunnerOrderCountByTimeRange
	_, monthCount, err := s.runnerOrderRepo.ListRunnerOrders(ctx, runnerID, 4, 1, 1) // 状态4表示已完成
	if err != nil {
		logs.Error("获取跑腿员本月订单数失败: %v", err)
		monthCount = 0 // 发生错误时使用默认值
	}
	monthOrderCount := monthCount

	monthIncome, err := s.incomeRepo.GetRunnerIncomeByTimeRange(ctx, runnerID, monthStart, monthEnd)
	if err != nil {
		logs.Error("获取跑腿员本月收入失败: %v", err)
		monthIncome = 0 // 发生错误时使用默认值
	}

	// 计算完成率
	completionRate := 0.0
	if runner.OrderCount > 0 {
		completionRate = float64(runner.SuccessCount) / float64(runner.OrderCount) * 100
	}

	// 计算平均配送时间（使用固定值替代）
	avgDeliveryTime := 30 // 默认30分钟

	// 构建响应
	resp := &dto.RunnerStatisticsResponse{
		TotalOrderCount:     runner.OrderCount,
		CompletedOrderCount: runner.SuccessCount,
		CanceledOrderCount:  runner.CancelCount,
		CompletionRate:      completionRate,
		AverageDeliveryTime: avgDeliveryTime,
		TotalIncome:         totalIncome,
		TodayOrderCount:     int(todayOrderCount),
		TodayIncome:         todayIncome,
		WeekOrderCount:      int(weekOrderCount),
		WeekIncome:          weekIncome,
		MonthOrderCount:     int(monthOrderCount),
		MonthIncome:         monthIncome,
	}

	return resp, nil
}

// GetRunnerTodayStats 获取跑腿员今日统计信息
func (s *RunnerServiceImpl) GetRunnerTodayStats(ctx context.Context, runnerID int64) (*dto.RunnerTodayStatsResponse, error) {
	// 验证跑腿员是否存在
	_, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, err
	}

	// 获取今日时间范围
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := todayStart.Add(24 * time.Hour)

	// 获取今日收入
	todayIncome, err := s.incomeRepo.GetRunnerIncomeByTimeRange(ctx, runnerID, todayStart, todayEnd)
	if err != nil {
		logs.Error("获取今日收入失败: %v", err)
		todayIncome = 0
	}

	// TODO: 这里需要根据实际业务需求从订单表或其他相关表获取以下数据
	// 由于当前代码中没有看到订单相关的详细统计方法，这里使用模拟数据
	// 在实际项目中，需要添加相应的repository方法来获取这些统计数据

	// 模拟今日完成订单数（实际应从订单表查询）
	completedOrderCount := 0
	
	// 模拟今日工作时长（实际应从工作记录表查询）
	workingHours := 0.0
	
	// 模拟今日配送距离（实际应从订单表查询配送距离总和）
	deliveryDistance := 0.0
	
	// 模拟今日平均评分（实际应从评价表查询）
	averageRating := 5.0
	
	// 模拟今日在线时长（实际应从在线记录表查询）
	onlineTime := 0.0
	
	// 模拟今日接单率（实际应从订单表查询接单数/推送数）
	orderAcceptRate := 0.0
	
	// 模拟今日配送成功率（实际应从订单表查询成功数/总数）
	deliverySuccessRate := 0.0

	// 构建响应
	resp := &dto.RunnerTodayStatsResponse{
		CompletedOrderCount: completedOrderCount,
		WorkingHours:        workingHours,
		DeliveryDistance:    deliveryDistance,
		AverageRating:       averageRating,
		TotalIncome:         todayIncome,
		OnlineTime:          onlineTime,
		OrderAcceptRate:     orderAcceptRate,
		DeliverySuccessRate: deliverySuccessRate,
	}

	return resp, nil
}

// 获取收入类型描述
func getIncomeTypeDesc(incomeType int) string {
	switch incomeType {
	case IncomeTypeDelivery:
		return "配送收入"
	case IncomeTypeBonus:
		return "奖励收入"
	case IncomeTypeRefund:
		return "退款"
	default:
		return "未知类型"
	}
}

// 获取收入状态描述
func getIncomeStatusDesc(status int) string {
	switch status {
	case IncomeStatusPending:
		return "待入账"
	case IncomeStatusSuccess:
		return "已入账"
	case IncomeStatusFailed:
		return "入账失败"
	default:
		return "未知状态"
	}
}

// 获取提现方式描述
func getWithdrawalMethodDesc(method int) string {
	switch method {
	case WithdrawalMethodWeChat:
		return "微信提现"
	case WithdrawalMethodAliPay:
		return "支付宝提现"
	case WithdrawalMethodBank:
		return "银行卡提现"
	default:
		return "未知方式"
	}
}

// 获取提现状态描述
func getWithdrawalStatusDesc(status int) string {
	switch status {
	case WithdrawalStatusPending:
		return "待处理"
	case WithdrawalStatusProcessing:
		return "处理中"
	case WithdrawalStatusSuccess:
		return "成功"
	case WithdrawalStatusFailed:
		return "失败"
	default:
		return "未知状态"
	}
}
