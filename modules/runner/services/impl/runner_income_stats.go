/**
 * runner_income_stats.go
 * 跑腿员收入统计服务实现
 *
 * 本文件实现了跑腿员收入统计相关的服务方法
 */

package impl

import (
	"context"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/runner/dto"
)

// GetIncomeStats 获取跑腿员收入统计数据（今日、本周、本月、累计）
func (s *RunnerServiceImpl) GetIncomeStats(ctx context.Context, runnerID int64) (*dto.RunnerIncomeStatsResponse, error) {
	// 获取各时间段收入
	today, err := s.incomeRepo.GetRunnerDailyIncome(ctx, runnerID)
	if err != nil {
		logs.Error("[GetIncomeStats] 获取今日收入失败: %v", err)
		today = 0 // 出错时默认为0
	}

	week, err := s.incomeRepo.GetRunnerWeeklyIncome(ctx, runnerID)
	if err != nil {
		logs.Error("[GetIncomeStats] 获取本周收入失败: %v", err)
		week = 0 // 出错时默认为0
	}

	month, err := s.incomeRepo.GetRunnerMonthlyIncome(ctx, runnerID)
	if err != nil {
		logs.Error("[GetIncomeStats] 获取本月收入失败: %v", err)
		month = 0 // 出错时默认为0
	}

	total, err := s.incomeRepo.GetRunnerTotalIncome(ctx, runnerID)
	if err != nil {
		logs.Error("[GetIncomeStats] 获取累计收入失败: %v", err)
		total = 0 // 出错时默认为0
	}

	// 构建响应
	response := &dto.RunnerIncomeStatsResponse{
		Today: today,
		Week:  week,
		Month: month,
		Total: total,
	}

	return response, nil
}
