/**
 * runner_order_service_impl.go
 * 跑腿订单服务接口实现
 *
 * 本文件实现了跑腿订单相关的服务功能，包括订单创建、查询、状态更新等
 */

package impl

import (
	"context"
	"errors"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/utils"
	"o_mall_backend/modules/runner/constants"
	"o_mall_backend/modules/runner/dto"
	"o_mall_backend/modules/runner/models"
)

// 使用constants包中定义的常量

// CreateRunnerOrder 创建跑腿订单
func (s *RunnerServiceImpl) CreateRunnerOrder(ctx context.Context, req *dto.CreateRunnerOrderRequest, userID int64) (*dto.RunnerOrderResponse, error) {
	// 计算订单金额
	totalAmount := req.DeliveryFee + req.ServiceFee + req.TipAmount

	// 创建订单
	// 使用请求中的OrderNo（如果存在），否则生成新的订单号
	orderNo := req.OrderNo
	if orderNo == "" {
		orderNo = utils.GenerateOrderNo("RT")
	}
	
	order := &models.RunnerOrder{
		OrderNo:               orderNo,
		OrderType:             req.OrderType,
		UserID:                userID,
		RunnerID:              userID, // 将userID同时设置为RunnerID，外卖模块分配配送员时传入的是deliveryStaffID
		Status:                constants.OrderStatusWaitingPay,
		PayStatus:             constants.PayStatusUnpaid,
		PayMethod:             req.PayMethod,
		TotalAmount:           totalAmount,
		DeliveryFee:           req.DeliveryFee,
		ServiceFee:            req.ServiceFee,
		TipAmount:             req.TipAmount,
		Distance:              req.Distance,
		EstimateTime:          req.EstimateTime,
		PickupAddress:         req.PickupAddress,
		PickupAddressDetail:   req.PickupAddressDetail,
		PickupLat:             req.PickupLat,
		PickupLng:             req.PickupLng,
		PickupContact:         req.PickupContact,
		PickupPhone:           req.PickupPhone,
		DeliveryAddress:       req.DeliveryAddress,
		DeliveryAddressDetail: req.DeliveryAddressDetail,
		DeliveryLat:           req.DeliveryLat,
		DeliveryLng:           req.DeliveryLng,
		DeliveryContact:       req.DeliveryContact,
		DeliveryPhone:         req.DeliveryPhone,
		Goods:                 req.Goods,
		GoodsWeight:           req.GoodsWeight,
		GoodsValue:            req.GoodsValue,
		Remark:                req.Remark,
	}

	// 保存订单
	orderID, err := s.runnerOrderRepo.CreateRunnerOrder(ctx, order)
	if err != nil {
		logs.Error("创建跑腿订单失败: %v", err)
		return nil, errors.New("创建订单失败，请稍后重试")
	}

	// 设置订单ID
	order.ID = orderID

	// 构建响应
	resp := &dto.RunnerOrderResponse{
		ID:                    order.ID,
		OrderNo:               order.OrderNo,
		OrderType:             order.OrderType,
		OrderTypeDesc:         getOrderTypeDesc(order.OrderType),
		UserID:                order.UserID,
		RunnerID:              order.RunnerID,
		Status:                order.Status,
		StatusDesc:            getOrderStatusDesc(order.Status),
		PayStatus:             order.PayStatus,
		PayStatusDesc:         getPayStatusDesc(order.PayStatus),
		PayMethod:             order.PayMethod,
		PayMethodDesc:         getPayMethodDesc(order.PayMethod),
		TotalAmount:           order.TotalAmount,
		DeliveryFee:           order.DeliveryFee,
		ServiceFee:            order.ServiceFee,
		TipAmount:             order.TipAmount,
		Distance:              order.Distance,
		EstimateTime:          order.EstimateTime,
		PickupAddress:         order.PickupAddress,
		PickupAddressDetail:   order.PickupAddressDetail,
		PickupContact:         order.PickupContact,
		PickupPhone:           order.PickupPhone,
		DeliveryAddress:       order.DeliveryAddress,
		DeliveryAddressDetail: order.DeliveryAddressDetail,
		DeliveryContact:       order.DeliveryContact,
		DeliveryPhone:         order.DeliveryPhone,
		Goods:                 order.Goods,
		GoodsWeight:           order.GoodsWeight,
		GoodsValue:            order.GoodsValue,
		Remark:                order.Remark,
		CreateTime:            order.CreateTime,
	}

	return resp, nil
}

// GetRunnerOrder 获取跑腿订单信息
func (s *RunnerServiceImpl) GetRunnerOrder(ctx context.Context, orderID int64) (*dto.RunnerOrderResponse, error) {
	// 获取订单信息
	order, err := s.runnerOrderRepo.GetRunnerOrderByID(ctx, orderID)
	if err != nil {
		logs.Error("获取跑腿订单信息失败: %v", err)
		return nil, errors.New("获取订单信息失败")
	}

	// 获取跑腿员信息
	var runnerName, runnerMobile string
	if order.RunnerID > 0 {
		runner, err := s.runnerRepo.GetRunnerByID(ctx, order.RunnerID)
		if err == nil {
			runnerName = runner.RealName
			runnerMobile = runner.Mobile
		}
	}

	// 构建响应
	resp := &dto.RunnerOrderResponse{
		ID:                    order.ID,
		OrderNo:               order.OrderNo,
		OrderType:             order.OrderType,
		OrderTypeDesc:         getOrderTypeDesc(order.OrderType),
		UserID:                order.UserID,
		RunnerID:              order.RunnerID,
		RunnerName:            runnerName,
		RunnerMobile:          runnerMobile,
		Status:                order.Status,
		StatusDesc:            getOrderStatusDesc(order.Status),
		PayStatus:             order.PayStatus,
		PayStatusDesc:         getPayStatusDesc(order.PayStatus),
		PayMethod:             order.PayMethod,
		PayMethodDesc:         getPayMethodDesc(order.PayMethod),
		TotalAmount:           order.TotalAmount,
		DeliveryFee:           order.DeliveryFee,
		ServiceFee:            order.ServiceFee,
		TipAmount:             order.TipAmount,
		Distance:              order.Distance,
		EstimateTime:          order.EstimateTime,
		PickupAddress:         order.PickupAddress,
		PickupAddressDetail:   order.PickupAddressDetail,
		PickupContact:         order.PickupContact,
		PickupPhone:           order.PickupPhone,
		DeliveryAddress:       order.DeliveryAddress,
		DeliveryAddressDetail: order.DeliveryAddressDetail,
		DeliveryContact:       order.DeliveryContact,
		DeliveryPhone:         order.DeliveryPhone,
		Goods:                 order.Goods,
		GoodsWeight:           order.GoodsWeight,
		GoodsValue:            order.GoodsValue,
		Remark:                order.Remark,
		AcceptTime:            order.AcceptTime,
		PickupTime:            order.PickupTime,
		DeliveryTime:          order.DeliveryTime,
		CancelTime:            order.CancelTime,
		CancelReason:          order.CancelReason,
		CancelUserType:        order.CancelUserType,
		ScoreByUser:           order.ScoreByUser,
		ScoreByRunner:         order.ScoreByRunner,
		CommentByUser:         order.CommentByUser,
		CommentByRunner:       order.CommentByRunner,
		CreateTime:            order.CreateTime,
	}

	return resp, nil
}

// ListUserRunnerOrders 获取用户跑腿订单列表
func (s *RunnerServiceImpl) ListUserRunnerOrders(ctx context.Context, req *dto.RunnerOrderListRequest, userID int64) (*dto.RunnerOrderListResponse, error) {
	// 设置默认值
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询订单列表
	orders, total, err := s.runnerOrderRepo.ListUserRunnerOrders(ctx, userID, req.Status, page, pageSize)
	if err != nil {
		logs.Error("获取用户跑腿订单列表失败: %v", err)
		return nil, errors.New("获取订单列表失败")
	}

	// 构建响应
	resp := &dto.RunnerOrderListResponse{
		Total: total,
		List:  make([]dto.RunnerOrderResponse, 0),
	}

	// 处理跑腿员信息
	runnerMap := make(map[int64]*models.Runner)

	// 转换订单
	for _, order := range orders {
		// 获取跑腿员信息
		var runnerName, runnerMobile string
		if order.RunnerID > 0 {
			runner, exists := runnerMap[order.RunnerID]
			if !exists {
				runner, err = s.runnerRepo.GetRunnerByID(ctx, order.RunnerID)
				if err == nil {
					runnerMap[order.RunnerID] = runner
				}
			}

			if runner != nil {
				runnerName = runner.RealName
				runnerMobile = runner.Mobile
			}
		}

		// 构建订单响应
		orderResp := dto.RunnerOrderResponse{
			ID:                    order.ID,
			OrderNo:               order.OrderNo,
			OrderType:             order.OrderType,
			OrderTypeDesc:         getOrderTypeDesc(order.OrderType),
			UserID:                order.UserID,
			RunnerID:              order.RunnerID,
			RunnerName:            runnerName,
			RunnerMobile:          runnerMobile,
			Status:                order.Status,
			StatusDesc:            getOrderStatusDesc(order.Status),
			PayStatus:             order.PayStatus,
			PayStatusDesc:         getPayStatusDesc(order.PayStatus),
			PayMethod:             order.PayMethod,
			PayMethodDesc:         getPayMethodDesc(order.PayMethod),
			TotalAmount:           order.TotalAmount,
			DeliveryFee:           order.DeliveryFee,
			ServiceFee:            order.ServiceFee,
			TipAmount:             order.TipAmount,
			Distance:              order.Distance,
			EstimateTime:          order.EstimateTime,
			PickupAddress:         order.PickupAddress,
			PickupAddressDetail:   order.PickupAddressDetail,
			PickupContact:         order.PickupContact,
			PickupPhone:           order.PickupPhone,
			DeliveryAddress:       order.DeliveryAddress,
			DeliveryAddressDetail: order.DeliveryAddressDetail,
			DeliveryContact:       order.DeliveryContact,
			DeliveryPhone:         order.DeliveryPhone,
			Goods:                 order.Goods,
			AcceptTime:            order.AcceptTime,
			PickupTime:            order.PickupTime,
			DeliveryTime:          order.DeliveryTime,
			CancelTime:            order.CancelTime,
			CancelReason:          order.CancelReason,
			ScoreByUser:           order.ScoreByUser,
			ScoreByRunner:         order.ScoreByRunner,
			CreateTime:            order.CreateTime,
		}

		resp.List = append(resp.List, orderResp)
	}

	return resp, nil
}

// ListRunnerOrders 获取跑腿员订单列表
func (s *RunnerServiceImpl) ListRunnerOrders(ctx context.Context, req *dto.RunnerOrderListRequest, runnerID int64) (*dto.RunnerOrderListResponse, error) {
	// 设置默认值
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询订单列表
	orders, total, err := s.runnerOrderRepo.ListRunnerOrders(ctx, runnerID, req.Status, page, pageSize)
	if err != nil {
		logs.Error("获取跑腿员订单列表失败: %v", err)
		return nil, errors.New("获取订单列表失败")
	}

	// 构建响应
	resp := &dto.RunnerOrderListResponse{
		Total: total,
		List:  make([]dto.RunnerOrderResponse, 0),
	}

	// 转换订单
	for _, order := range orders {
		// 构建订单响应
		orderResp := dto.RunnerOrderResponse{
			ID:                    order.ID,
			OrderNo:               order.OrderNo,
			OrderType:             order.OrderType,
			OrderTypeDesc:         getOrderTypeDesc(order.OrderType),
			UserID:                order.UserID,
			RunnerID:              order.RunnerID,
			Status:                order.Status,
			StatusDesc:            getOrderStatusDesc(order.Status),
			PayStatus:             order.PayStatus,
			PayStatusDesc:         getPayStatusDesc(order.PayStatus),
			PayMethod:             order.PayMethod,
			PayMethodDesc:         getPayMethodDesc(order.PayMethod),
			TotalAmount:           order.TotalAmount,
			DeliveryFee:           order.DeliveryFee,
			ServiceFee:            order.ServiceFee,
			TipAmount:             order.TipAmount,
			Distance:              order.Distance,
			EstimateTime:          order.EstimateTime,
			PickupAddress:         order.PickupAddress,
			PickupAddressDetail:   order.PickupAddressDetail,
			PickupLatitude:        order.PickupLat,
			PickupLongitude:       order.PickupLng,
			PickupContact:         order.PickupContact,
			PickupPhone:           order.PickupPhone,
			DeliveryAddress:       order.DeliveryAddress,
			DeliveryAddressDetail: order.DeliveryAddressDetail,
			DeliveryLatitude:      order.DeliveryLat,
			DeliveryLongitude:     order.DeliveryLng,
			DeliveryContact:       order.DeliveryContact,
			DeliveryPhone:         order.DeliveryPhone,
			Goods:                 order.Goods,
			AcceptTime:            order.AcceptTime,
			PickupTime:            order.PickupTime,
			DeliveryTime:          order.DeliveryTime,
			CancelTime:            order.CancelTime,
			CancelReason:          order.CancelReason,
			ScoreByUser:           order.ScoreByUser,
			ScoreByRunner:         order.ScoreByRunner,
			CreateTime:            order.CreateTime,
		}

		resp.List = append(resp.List, orderResp)
	}

	return resp, nil
}
