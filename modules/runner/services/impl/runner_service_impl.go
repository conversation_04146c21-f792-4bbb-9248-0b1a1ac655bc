/**
 * runner_service_impl.go
 * 跑腿服务接口实现
 *
 * 本文件实现了跑腿模块的服务接口，提供跑腿员管理、订单处理等功能
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/runner/core"
	"o_mall_backend/modules/runner/dto"
	runnerFactory "o_mall_backend/modules/runner/factory"
	"o_mall_backend/modules/runner/models"
	"o_mall_backend/modules/runner/services"
)

// RunnerServiceImpl 跑腿员服务实现
type RunnerServiceImpl struct {
	runnerRepo      core.RunnerRepository
	runnerOrderRepo core.RunnerOrderRepository
	incomeRepo      core.RunnerIncomeRepository
	runnerMiscRepo  core.RunnerMiscRepository
}

// NewRunnerService 创建跑腿员服务
func NewRunnerService() services.RunnerService {
	runnerRepo := runnerFactory.NewRunnerRepository()
	return &RunnerServiceImpl{
		runnerRepo:      runnerRepo,
		runnerOrderRepo: runnerFactory.NewRunnerOrderRepository(runnerRepo),
		incomeRepo:      runnerFactory.NewRunnerIncomeRepository(),
		runnerMiscRepo:  runnerFactory.NewRunnerMiscRepository(),
	}
}

// RegisterRunner 注册跑腿员
func (s *RunnerServiceImpl) RegisterRunner(ctx context.Context, req *dto.RunnerRegisterRequest, userID int64) error {
	// 检查是否已存在跑腿员
	existsRunner, err := s.runnerRepo.GetRunnerByUserID(ctx, userID)
	if err == nil && existsRunner != nil {
		return errors.New("您已注册为跑腿员，无需重复注册")
	}

	// 创建跑腿员申请
	apply := &models.RunnerApply{
		UserID:         userID,
		RealName:       req.RealName,
		IDCardNumber:   req.IDCardNumber,
		IDCardFrontPic: req.IDCardFrontPic,
		IDCardBackPic:  req.IDCardBackPic,
		FacePic:        req.FacePic,
		Mobile:         req.Mobile,
		Status:         0, // 待审核
		AreaCodes:      req.AreaCodes,
		ServiceRadius:  req.ServiceRadius,
	}

	// 保存申请
	err = s.runnerMiscRepo.CreateRunnerApply(ctx, apply)
	if err != nil {
		logs.Error("创建跑腿员申请失败: %v", err)
		return errors.New("注册失败，请稍后重试")
	}

	// 这里可以增加自动审核逻辑，或者发送通知给管理员审核
	// 自动审核示例代码
	/*
		// 自动审核通过
		apply.Status = 1
		apply.AuditTime = time.Now()
		apply.AuditBy = 0 // 系统自动审核

		err = s.runnerRepo.UpdateRunnerApply(ctx, apply)
		if err != nil {
			logs.Error("更新跑腿员申请状态失败: %v", err)
			return errors.New("审核失败，请稍后重试")
		}

		// 创建跑腿员
		runner := &models.Runner{
			UserID:        userID,
			RealName:      req.RealName,
			IDCardNumber:  req.IDCardNumber,
			IDCardFrontPic: req.IDCardFrontPic,
			IDCardBackPic: req.IDCardBackPic,
			FacePic:       req.FacePic,
			Mobile:        req.Mobile,
			Status:        1, // 审核通过
			AreaCodes:     req.AreaCodes,
			ServiceRadius: req.ServiceRadius,
		}

		err = s.runnerRepo.CreateRunner(ctx, runner)
		if err != nil {
			logs.Error("创建跑腿员失败: %v", err)
			return errors.New("创建跑腿员账号失败，请稍后重试")
		}
	*/

	return nil
}

// GetApplyStatus 获取申请状态
func (s *RunnerServiceImpl) GetApplyStatus(ctx context.Context, userID int64) (*dto.RunnerApplyStatusResponse, error) {
	// 获取申请记录
	apply, err := s.runnerMiscRepo.GetRunnerApplyByUserID(ctx, userID)
	if err != nil {
		logs.Error("获取跑腿员申请记录失败: %v", err)
		return nil, errors.New("未找到申请记录")
	}

	// 构建响应
	resp := &dto.RunnerApplyStatusResponse{
		ID:           apply.ID,
		UserID:       apply.UserID,
		RealName:     apply.RealName,
		Mobile:       apply.Mobile,
		Status:       apply.Status,
		StatusDesc:   getApplyStatusDesc(apply.Status),
		RejectReason: apply.RejectReason,
		CreateTime:   apply.CreateTime,
		AuditTime:    apply.AuditTime,
	}

	return resp, nil
}

// GetRunnerInfo 获取跑腿员信息
func (s *RunnerServiceImpl) GetRunnerInfo(ctx context.Context, runnerID int64) (*dto.RunnerResponse, error) {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, errors.New("获取跑腿员信息失败")
	}

	// 计算加入天数
	joinDays := int(time.Since(runner.JoinTime).Hours() / 24)

	// 构建响应
	resp := &dto.RunnerResponse{
		ID:                runner.ID,
		UserID:            runner.UserID,
		RealName:          runner.RealName,
		Mobile:            runner.Mobile,
		Status:            runner.Status,
		StatusDesc:        getRunnerStatusDesc(runner.Status),
		CurrentLocation:   runner.CurrentLocation,
		IsOnline:          runner.IsOnline,
		WorkingStatus:     runner.WorkingStatus,
		WorkingStatusDesc: getWorkingStatusDesc(runner.WorkingStatus),
		Score:             runner.Score,
		OrderCount:        runner.OrderCount,
		SuccessCount:      runner.SuccessCount,
		CancelCount:       runner.CancelCount,
		Wallet:            runner.Wallet,
		ServiceRadius:     runner.ServiceRadius,
		JoinDays:          joinDays,
	}

	return resp, nil
}

// GetRunnerDetail 获取跑腿员详细信息
func (s *RunnerServiceImpl) GetRunnerDetail(ctx context.Context, runnerID int64) (*dto.RunnerDetailResponse, error) {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, errors.New("获取跑腿员信息失败")
	}

	// 计算加入天数
	joinDays := int(time.Since(runner.JoinTime).Hours() / 24)

	// 构建响应
	resp := &dto.RunnerDetailResponse{
		RunnerResponse: dto.RunnerResponse{
			ID:                runner.ID,
			UserID:            runner.UserID,
			RealName:          runner.RealName,
			Mobile:            runner.Mobile,
			Status:            runner.Status,
			StatusDesc:        s.getStatusDesc(runner.Status),
			CurrentLocation:   runner.CurrentLocation,
			IsOnline:          runner.IsOnline,
			WorkingStatus:     runner.WorkingStatus,
			WorkingStatusDesc: s.getWorkingStatusDesc(runner.WorkingStatus),
			Score:             runner.Score,
			OrderCount:        runner.OrderCount,
			SuccessCount:      runner.SuccessCount,
			CancelCount:       runner.CancelCount,
			Wallet:            runner.Wallet,
			ServiceRadius:     runner.ServiceRadius,
			JoinDays:          joinDays,
		},
		IDCardNumber:   runner.IDCardNumber,
		IDCardFrontPic: runner.IDCardFrontPic,
		IDCardBackPic:  runner.IDCardBackPic,
		FacePic:        runner.FacePic,
		Latitude:       runner.Latitude,
		Longitude:      runner.Longitude,
		AreaCodes:      runner.AreaCodes,
		JoinTime:       runner.JoinTime,
		LastLoginTime:  runner.LastLoginTime,
		LastOnlineTime: runner.LastOnlineTime,
		Remark:         runner.Remark,
		FrontendRemark: runner.FrontendRemark,
	}

	return resp, nil
}

// UpdateRunnerStatus 更新跑腿员状态
func (s *RunnerServiceImpl) UpdateRunnerStatus(ctx context.Context, runnerID int64, req *dto.UpdateRunnerStatusRequest) error {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return errors.New("获取跑腿员信息失败")
	}

	// 更新状态
	runner.Status = req.Status

	// 保存更新
	err = s.runnerRepo.UpdateRunner(ctx, runner)
	if err != nil {
		logs.Error("更新跑腿员状态失败: %v", err)
		return errors.New("更新跑腿员状态失败")
	}

	return nil
}

// UpdateWorkingStatus 更新跑腿员工作状态
func (s *RunnerServiceImpl) UpdateWorkingStatus(ctx context.Context, runnerID int64, req *dto.UpdateWorkingStatusRequest) error {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		return result.ErrNotFound.WithDetails("跑腿员不存在")
	}

	// 更新工作状态
	runner.WorkingStatus = req.WorkingStatus

	// 保存更新
	err = s.runnerRepo.UpdateRunner(ctx, runner)
	if err != nil {
		return result.ErrInternalError.WithDetails("更新跑腿员工作状态失败")
	}

	return nil
}

// UpdateRunnerLocation 更新跑腿员位置
func (s *RunnerServiceImpl) UpdateRunnerLocation(ctx context.Context, runnerID int64, req *dto.UpdateRunnerLocationRequest) error {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return errors.New("获取跑腿员信息失败")
	}

	// 更新位置
	runner.Latitude = req.Latitude
	runner.Longitude = req.Longitude
	runner.CurrentLocation = req.Address

	// 保存更新
	err = s.runnerRepo.UpdateRunner(ctx, runner)
	if err != nil {
		logs.Error("更新跑腿员位置失败: %v", err)
		return errors.New("更新跑腿员位置失败")
	}

	// 记录位置历史
	locationLog := &models.RunnerLocation{
		RunnerID:  runnerID,
		Latitude:  req.Latitude,
		Longitude: req.Longitude,
	}

	err = s.runnerMiscRepo.CreateRunnerLocation(ctx, locationLog)
	if err != nil {
		logs.Error("记录跑腿员位置失败: %v", err)
		// 不返回错误，因为这只是日志记录
	}

	return nil
}

// UpdateOnlineStatus 更新跑腿员在线状态
func (s *RunnerServiceImpl) UpdateOnlineStatus(ctx context.Context, runnerID int64, isOnline bool) error {
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return errors.New("获取跑腿员信息失败")
	}

	// 更新在线状态
	runner.IsOnline = isOnline
	if isOnline {
		// 如果上线，更新最后在线时间
		runner.LastOnlineTime = time.Now()
	}

	// 保存更新
	err = s.runnerRepo.UpdateRunner(ctx, runner)
	if err != nil {
		logs.Error("更新跑腿员在线状态失败: %v", err)
		return errors.New("更新跑腿员在线状态失败")
	}

	return nil
}

// GetNearbyRunners 获取附近跑腿员
func (s *RunnerServiceImpl) GetNearbyRunners(ctx context.Context, req *dto.NearbyRunnerRequest) ([]dto.NearbyRunnerResponse, error) {
	// 设置默认值
	radius := req.Radius
	if radius <= 0 {
		radius = 5.0 // 默认5公里
	}

	limit := req.Limit
	if limit <= 0 {
		limit = 20 // 默认20条
	}

	// 查询附近跑腿员
	runners, err := s.runnerRepo.ListNearbyRunners(ctx, req.Latitude, req.Longitude, radius)
	if err != nil {
		logs.Error("查询附近跑腿员失败: %v", err)
		return nil, errors.New("查询附近跑腿员失败")
	}

	// 构建响应
	result := make([]dto.NearbyRunnerResponse, 0)
	for _, runner := range runners {
		// 计算距离
		distance := calculateDistance(req.Latitude, req.Longitude, runner.Latitude, runner.Longitude)

		// 构建响应
		resp := dto.NearbyRunnerResponse{
			ID:            runner.ID,
			RealName:      runner.RealName,
			Distance:      distance,
			Score:         runner.Score,
			OrderCount:    runner.OrderCount,
			WorkingStatus: runner.WorkingStatus,
		}

		result = append(result, resp)

		// 限制数量
		if len(result) >= limit {
			break
		}
	}

	return result, nil
}

// 计算两点之间的距离（单位：公里）
func calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const earthRadius = 6371.0 // 地球半径，单位公里

	// 转换为弧度
	lat1Rad := lat1 * math.Pi / 180
	lng1Rad := lng1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lng2Rad := lng2 * math.Pi / 180

	// 差值
	dLat := lat2Rad - lat1Rad
	dLng := lng2Rad - lng1Rad

	// Haversine公式
	a := math.Sin(dLat/2)*math.Sin(dLat/2) + math.Cos(lat1Rad)*math.Cos(lat2Rad)*math.Sin(dLng/2)*math.Sin(dLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadius * c

	// 保留两位小数
	return math.Round(distance*100) / 100
}

// 获取跑腿员状态描述
func getRunnerStatusDesc(status int) string {
	switch status {
	case 0:
		return "待审核"
	case 1:
		return "审核通过"
	case 2:
		return "审核拒绝"
	case 3:
		return "暂停服务"
	case 4:
		return "黑名单"
	default:
		return "未知状态"
	}
}

// 获取工作状态描述
func getWorkingStatusDesc(status int) string {
	switch status {
	case 0:
		return "休息中"
	case 1:
		return "接单中"
	case 2:
		return "配送中"
	default:
		return "未知状态"
	}
}

// 获取申请状态描述
func getApplyStatusDesc(status int) string {
	switch status {
	case 0:
		return "待审核"
	case 1:
		return "审核通过"
	case 2:
		return "审核拒绝"
	default:
		return "未知状态"
	}
}

// ==================== 管理员相关方法 ====================

// AdminListRunners 管理员获取跑腿员列表
func (s *RunnerServiceImpl) AdminListRunners(ctx context.Context, req *dto.AdminRunnerListRequest) (*dto.AdminRunnerListResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	var items []dto.AdminRunnerItem
	var total int64

	// 根据状态查询不同的数据源
	if req.Status == 0 { // 待审核状态，查询申请表
		applies, applyTotal, err := s.getRunnerApplies(ctx, req)
		if err != nil {
			return nil, err
		}
		items = s.convertAppliesToAdminItems(applies)
		total = applyTotal
	} else if req.Status == -1 { // 查询所有状态，需要合并申请表和跑腿员表的数据
		// 创建临时请求对象，不应用分页
		tempReq := *req
		tempReq.Page = 1
		tempReq.PageSize = 1000 // 设置较大值获取所有数据
		
		// 先查询待审核申请
		applies, applyTotal, err := s.getRunnerApplies(ctx, &tempReq)
		if err != nil {
			return nil, err
		}
		applyItems := s.convertAppliesToAdminItems(applies)
		
		// 再查询已审核的跑腿员
		runners, runnerTotal, err := s.getRunners(ctx, &tempReq)
		if err != nil {
			return nil, err
		}
		runnerItems := s.convertRunnersToAdminItems(runners)
		
		// 合并数据并按创建时间排序
		allItems := append(applyItems, runnerItems...)
		total = applyTotal + runnerTotal
		
		// 手动分页
		offset := (req.Page - 1) * req.PageSize
		end := offset + req.PageSize
		if offset >= len(allItems) {
			items = []dto.AdminRunnerItem{}
		} else {
			if end > len(allItems) {
				end = len(allItems)
			}
			items = allItems[offset:end]
		}
	} else { // 其他状态，查询跑腿员表
		runners, runnerTotal, err := s.getRunners(ctx, req)
		if err != nil {
			return nil, err
		}
		items = s.convertRunnersToAdminItems(runners)
		total = runnerTotal
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &dto.AdminRunnerListResponse{
		List:       items,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// getRunnerApplies 获取跑腿员申请列表
func (s *RunnerServiceImpl) getRunnerApplies(ctx context.Context, req *dto.AdminRunnerListRequest) ([]*models.RunnerApply, int64, error) {
	o := orm.NewOrm()
	var applies []*models.RunnerApply

	query := o.QueryTable(new(models.RunnerApply))
	
	// 关键词搜索
	if req.Keyword != "" {
		query = query.Filter("real_name__icontains", req.Keyword).Filter("mobile__icontains", req.Keyword)
	}
	
	// 状态筛选 - 待审核
	query = query.Filter("status", 0)

	// 获取总数
	total, err := query.Count()
	if err != nil {
		logs.Error("获取跑腿员申请总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取跑腿员申请总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	_, err = query.OrderBy("-create_time").Limit(req.PageSize, offset).All(&applies)
	if err != nil {
		logs.Error("获取跑腿员申请列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取跑腿员申请列表失败: %v", err)
	}

	return applies, total, nil
}

// getRunners 获取跑腿员列表
func (s *RunnerServiceImpl) getRunners(ctx context.Context, req *dto.AdminRunnerListRequest) ([]*models.Runner, int64, error) {
	o := orm.NewOrm()
	var runners []*models.Runner

	query := o.QueryTable(new(models.Runner))
	
	// 关键词搜索
	if req.Keyword != "" {
		query = query.Filter("real_name__icontains", req.Keyword).Filter("mobile__icontains", req.Keyword)
	}
	
	// 状态筛选
	if req.Status > 0 {
		query = query.Filter("status", req.Status)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		logs.Error("获取跑腿员总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取跑腿员总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	_, err = query.OrderBy("-create_time").Limit(req.PageSize, offset).All(&runners)
	if err != nil {
		logs.Error("获取跑腿员列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取跑腿员列表失败: %v", err)
	}

	return runners, total, nil
}

// convertAppliesToAdminItems 将申请转换为管理员列表项
func (s *RunnerServiceImpl) convertAppliesToAdminItems(applies []*models.RunnerApply) []dto.AdminRunnerItem {
	var items []dto.AdminRunnerItem
	for _, apply := range applies {
		item := dto.AdminRunnerItem{
			ID:             apply.ID,
			UserID:         apply.UserID,
			RealName:       apply.RealName,
			Mobile:         apply.Mobile,
			Status:         apply.Status,
			StatusDesc:     s.getStatusDesc(apply.Status),
			JoinTime:       apply.CreateTime,
			ServiceRadius:  apply.ServiceRadius,
			// 申请阶段的默认值
			IsOnline:          false,
			WorkingStatus:     0,
			WorkingStatusDesc: "未开始工作",
			Score:             0,
			OrderCount:        0,
			SuccessCount:      0,
			CancelCount:       0,
			Balance:           0,
		}
		items = append(items, item)
	}
	return items
}

// convertRunnersToAdminItems 将跑腿员转换为管理员列表项
func (s *RunnerServiceImpl) convertRunnersToAdminItems(runners []*models.Runner) []dto.AdminRunnerItem {
	var items []dto.AdminRunnerItem
	for _, runner := range runners {
		item := dto.AdminRunnerItem{
			ID:                runner.ID,
			UserID:            runner.UserID,
			RealName:          runner.RealName,
			Mobile:            runner.Mobile,
			Status:            runner.Status,
			StatusDesc:        getRunnerStatusDesc(runner.Status),
			CurrentLocation:   runner.CurrentLocation,
			IsOnline:          runner.IsOnline,
			WorkingStatus:     runner.WorkingStatus,
			WorkingStatusDesc: getWorkingStatusDesc(runner.WorkingStatus),
			Score:             runner.Score,
			OrderCount:        runner.OrderCount,
			SuccessCount:      runner.SuccessCount,
			CancelCount:       runner.CancelCount,
			Balance:           runner.Wallet,
			ServiceRadius:     runner.ServiceRadius,
			JoinTime:          runner.CreateTime,
			LastOnlineTime:    runner.LastOnlineTime,
			Remark:            runner.Remark,
		}
		items = append(items, item)
	}
	return items
}

// getStatusDesc 获取状态描述
func (s *RunnerServiceImpl) getStatusDesc(status int) string {
	switch status {
	case 0:
		return "待审核"
	case 1:
		return "审核通过"
	case 2:
		return "审核拒绝"
	case 3:
		return "暂停服务"
	case 4:
		return "黑名单"
	default:
		return "未知状态"
	}
}

// getWorkingStatusDesc 获取工作状态描述
func (s *RunnerServiceImpl) getWorkingStatusDesc(status int) string {
	switch status {
	case 0:
		return "空闲"
	case 1:
		return "忙碌"
	case 2:
		return "休息"
	default:
		return "未知状态"
	}
}

// AdminGetRunnerDetail 获取跑腿员详细信息（管理员）
func (s *RunnerServiceImpl) AdminGetRunnerDetail(ctx context.Context, runnerID int64) (*dto.AdminRunnerDetailResponse, error) {
	logs.Info("AdminGetRunnerDetail called with runnerID: %d", runnerID)
	
	// 获取跑腿员信息
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, err
	}
	
	// 构建状态描述
	statusDesc := ""
	switch runner.Status {
	case 0:
		statusDesc = "待审核"
	case 1:
		statusDesc = "审核通过"
	case 2:
		statusDesc = "审核拒绝"
	case 3:
		statusDesc = "暂停服务"
	case 4:
		statusDesc = "黑名单"
	default:
		statusDesc = "未知状态"
	}
	
	// 构建工作状态描述
	workingStatusDesc := ""
	switch runner.WorkingStatus {
	case 0:
		workingStatusDesc = "休息中"
	case 1:
		workingStatusDesc = "接单中"
	case 2:
		workingStatusDesc = "配送中"
	default:
		workingStatusDesc = "未知状态"
	}
	
	// 构建响应
	resp := &dto.AdminRunnerDetailResponse{
		ID:                runner.ID,
		UserID:            runner.UserID,
		RealName:          runner.RealName,
		IDCardNumber:      runner.IDCardNumber,
		IDCardFrontPic:    runner.IDCardFrontPic,
		IDCardBackPic:     runner.IDCardBackPic,
		FacePic:           runner.FacePic,
		Mobile:            runner.Mobile,
		Status:            runner.Status,
		StatusDesc:        statusDesc,
		CurrentLocation:   runner.CurrentLocation,
		Latitude:          runner.Latitude,
		Longitude:         runner.Longitude,
		IsOnline:          runner.IsOnline,
		WorkingStatus:     runner.WorkingStatus,
		WorkingStatusDesc: workingStatusDesc,
		Score:             runner.Score,
		OrderCount:        runner.OrderCount,
		SuccessCount:      runner.SuccessCount,
		CancelCount:       runner.CancelCount,
		Balance:           runner.Balance,
		Wallet:            runner.Wallet,
		AreaCodes:         runner.AreaCodes,
		ServiceRadius:     runner.ServiceRadius,
		Remark:            runner.Remark,
		FrontendRemark:    runner.FrontendRemark,
		JoinTime:          runner.JoinTime,
		LastLoginTime:     runner.LastLoginTime,
	}
	
	logs.Info("AdminGetRunnerDetail success for runnerID: %d", runnerID)
	return resp, nil
}

// AdminAuditRunner 审核跑腿员（管理员）
// 该方法处理管理员对跑腿员申请的审核，支持审核通过和审核拒绝
// 审核通过时会创建或更新跑腿员记录，审核拒绝时会更新现有跑腿员状态（如果存在）
func (s *RunnerServiceImpl) AdminAuditRunner(ctx context.Context, runnerID int64, req *dto.AdminAuditRunnerRequest, adminID int64) error {
	// 参数验证
	if req.Status != 1 && req.Status != 2 {
		return errors.New("无效的审核状态，只能是1（通过）或2（拒绝）")
	}
	if req.Status == 2 && req.RejectReason == "" {
		return errors.New("审核拒绝时必须提供拒绝原因")
	}

	// 获取申请记录
	apply, err := s.runnerMiscRepo.GetRunnerApplyByID(ctx, runnerID)
	if err != nil {
		return errors.New("获取申请记录失败")
	}
	if apply == nil {
		return errors.New("申请记录不存在")
	}

	// 检查申请状态
	if apply.Status != 0 {
		return errors.New("申请已被审核，无法重复审核")
	}

	// 更新申请状态
	apply.Status = req.Status
	apply.RejectReason = req.RejectReason
	apply.AuditTime = time.Now()

	err = s.runnerMiscRepo.UpdateRunnerApply(ctx, apply)
	if err != nil {
		return errors.New("更新申请状态失败")
	}

	// 如果审核通过，处理跑腿员记录
	if req.Status == 1 {
		// 检查该用户是否已经是跑腿员
		existingRunner, err := s.runnerRepo.GetRunnerByUserID(ctx, apply.UserID)
		if err != nil && err.Error() != "跑腿员不存在" {
			return errors.New("检查跑腿员状态失败")
		}

		if existingRunner != nil {
			// 跑腿员已存在，更新其状态为审核通过
			existingRunner.Status = 1 // 审核通过
			// 更新其他可能变更的信息
			existingRunner.RealName = apply.RealName
			existingRunner.IDCardNumber = apply.IDCardNumber
			existingRunner.IDCardFrontPic = apply.IDCardFrontPic
			existingRunner.IDCardBackPic = apply.IDCardBackPic
			existingRunner.FacePic = apply.FacePic
			existingRunner.Mobile = apply.Mobile
			existingRunner.AreaCodes = apply.AreaCodes
			existingRunner.ServiceRadius = apply.ServiceRadius

			err = s.runnerRepo.UpdateRunner(ctx, existingRunner)
			if err != nil {
				return errors.New("更新跑腿员状态失败")
			}
			logs.Info("管理员审核通过，更新现有跑腿员状态: userID=%d, runnerID=%d", apply.UserID, existingRunner.ID)
		} else {
			// 跑腿员不存在，创建新的跑腿员记录
			runner := &models.Runner{
				UserID:         apply.UserID,
				RealName:       apply.RealName,
				IDCardNumber:   apply.IDCardNumber,
				IDCardFrontPic: apply.IDCardFrontPic,
				IDCardBackPic:  apply.IDCardBackPic,
				FacePic:        apply.FacePic,
				Mobile:         apply.Mobile,
				Status:         1, // 审核通过
				IsOnline:       false,
				WorkingStatus:  0, // 空闲
				Score:          5.0,
				OrderCount:     0,
				SuccessCount:   0,
				CancelCount:    0,
				Wallet:         0,
				AreaCodes:      apply.AreaCodes,
				ServiceRadius:  apply.ServiceRadius,
				JoinTime:       time.Now(),
			}

			err = s.runnerRepo.CreateRunner(ctx, runner)
			if err != nil {
				return errors.New("创建跑腿员记录失败")
			}
			logs.Info("管理员审核通过，创建新跑腿员记录: userID=%d", apply.UserID)
		}
	} else if req.Status == 2 {
		// 审核拒绝，如果跑腿员已存在，更新其状态为审核拒绝
		existingRunner, err := s.runnerRepo.GetRunnerByUserID(ctx, apply.UserID)
		if err != nil && err.Error() != "跑腿员不存在" {
			return errors.New("检查跑腿员状态失败")
		}

		if existingRunner != nil {
			// 跑腿员已存在，更新其状态为审核拒绝
			existingRunner.Status = 2 // 审核拒绝
			err = s.runnerRepo.UpdateRunner(ctx, existingRunner)
			if err != nil {
				return errors.New("更新跑腿员状态失败")
			}
			logs.Info("管理员审核拒绝，更新现有跑腿员状态: userID=%d, runnerID=%d", apply.UserID, existingRunner.ID)
		} else {
			logs.Info("管理员审核拒绝，用户尚未成为跑腿员: userID=%d", apply.UserID)
		}
	}

	logs.Info("管理员审核跑腿员成功: applyID=%d, status=%d, adminID=%d", runnerID, req.Status, adminID)
	return nil
}

// AdminUpdateRunnerStatus 更新跑腿员状态（管理员）
func (s *RunnerServiceImpl) AdminUpdateRunnerStatus(ctx context.Context, runnerID int64, req *dto.AdminUpdateRunnerStatusRequest, adminID int64) error {
	// TODO: 实现管理员更新跑腿员状态功能
	logs.Info("AdminUpdateRunnerStatus called with runnerID: %d, req: %+v, adminID: %d", runnerID, req, adminID)
	return nil
}

// AdminDeleteRunner 删除跑腿员（管理员）
func (s *RunnerServiceImpl) AdminDeleteRunner(ctx context.Context, runnerID int64, adminID int64) error {
	// TODO: 实现管理员删除跑腿员功能
	logs.Info("AdminDeleteRunner called with runnerID: %d, adminID: %d", runnerID, adminID)
	return nil
}

// AdminGetRunnerStatistics 获取跑腿员统计信息（管理员）
func (s *RunnerServiceImpl) AdminGetRunnerStatistics(ctx context.Context) (*dto.AdminRunnerStatisticsResponse, error) {
	logs.Info("AdminGetRunnerStatistics called")
	
	// 获取总跑腿员数（包括所有状态）
	_, totalCount, err := s.runnerRepo.ListRunners(ctx, 1, 1, -1)
	if err != nil {
		logs.Error("获取总跑腿员数失败: %v", err)
		return nil, fmt.Errorf("获取总跑腿员数失败: %v", err)
	}
	
	// 获取各状态的跑腿员数量
	// 0-待审核
	_, pendingCount, err := s.runnerRepo.ListRunners(ctx, 1, 1, 0)
	if err != nil {
		logs.Error("获取待审核跑腿员数失败: %v", err)
		return nil, fmt.Errorf("获取待审核跑腿员数失败: %v", err)
	}
	
	// 1-审核通过
	_, approvedCount, err := s.runnerRepo.ListRunners(ctx, 1, 1, 1)
	if err != nil {
		logs.Error("获取审核通过跑腿员数失败: %v", err)
		return nil, fmt.Errorf("获取审核通过跑腿员数失败: %v", err)
	}
	
	// 2-审核拒绝
	_, rejectedCount, err := s.runnerRepo.ListRunners(ctx, 1, 1, 2)
	if err != nil {
		logs.Error("获取审核拒绝跑腿员数失败: %v", err)
		return nil, fmt.Errorf("获取审核拒绝跑腿员数失败: %v", err)
	}
	
	// 3-暂停服务
	_, suspendedCount, err := s.runnerRepo.ListRunners(ctx, 1, 1, 3)
	if err != nil {
		logs.Error("获取暂停服务跑腿员数失败: %v", err)
		return nil, fmt.Errorf("获取暂停服务跑腿员数失败: %v", err)
	}
	
	// 4-黑名单
	_, blacklistCount, err := s.runnerRepo.ListRunners(ctx, 1, 1, 4)
	if err != nil {
		logs.Error("获取黑名单跑腿员数失败: %v", err)
		return nil, fmt.Errorf("获取黑名单跑腿员数失败: %v", err)
	}
	
	// 获取在线跑腿员数（审核通过且在线）
	approvedRunners, _, err := s.runnerRepo.ListRunners(ctx, 1, 10000, 1) // 获取所有审核通过的跑腿员
	if err != nil {
		logs.Error("获取审核通过跑腿员列表失败: %v", err)
		return nil, fmt.Errorf("获取审核通过跑腿员列表失败: %v", err)
	}
	
	onlineCount := int64(0)
	workingCount := int64(0)
	for _, runner := range approvedRunners {
		if runner.IsOnline {
			onlineCount++
		}
		if runner.WorkingStatus == 2 { // 2-配送中
			workingCount++
		}
	}
	
	// 获取今日、本周、本月新增跑腿员数
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	weekStart := todayStart.AddDate(0, 0, -int(now.Weekday()))
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	
	todayNewCount := s.getNewRunnersCount(ctx, todayStart, now)
	weekNewCount := s.getNewRunnersCount(ctx, weekStart, now)
	monthNewCount := s.getNewRunnersCount(ctx, monthStart, now)
	
	return &dto.AdminRunnerStatisticsResponse{
		TotalRunners:        totalCount,
		PendingAudit:        pendingCount,
		ApprovedRunners:     approvedCount,
		RejectedRunners:     rejectedCount,
		SuspendedRunners:    suspendedCount,
		BlacklistRunners:    blacklistCount,
		OnlineRunners:       onlineCount,
		WorkingRunners:      workingCount,
		TodayNewRunners:     todayNewCount,
		ThisWeekNewRunners:  weekNewCount,
		ThisMonthNewRunners: monthNewCount,
	}, nil
}

// getNewRunnersCount 获取指定时间范围内的新增跑腿员数量
func (s *RunnerServiceImpl) getNewRunnersCount(ctx context.Context, startTime, endTime time.Time) int64 {
	o := orm.NewOrm()
	
	// 查询指定时间范围内创建的跑腿员数量
	count, err := o.QueryTable(new(models.Runner)).
		Filter("create_time__gte", startTime).
		Filter("create_time__lt", endTime).
		Count()
	
	if err != nil {
		logs.Error("获取新增跑腿员数量失败: %v", err)
		return 0
	}
	
	return count
}

// AdminUpdateRunnerRemark 更新跑腿员备注（管理员）
func (s *RunnerServiceImpl) AdminUpdateRunnerRemark(ctx context.Context, runnerID int64, req *dto.AdminUpdateRunnerRemarkRequest, adminID int64) error {
	// TODO: 实现管理员更新跑腿员备注功能
	logs.Info("AdminUpdateRunnerRemark called with runnerID: %d, req: %+v, adminID: %d", runnerID, req, adminID)
	return nil
}

// AdminGetRunnerOrders 获取跑腿员订单列表（管理员）
func (s *RunnerServiceImpl) AdminGetRunnerOrders(ctx context.Context, runnerID int64, page, pageSize int) (*dto.RunnerOrderListResponse, error) {
	// TODO: 实现管理员获取跑腿员订单列表功能
	logs.Info("AdminGetRunnerOrders called with runnerID: %d, page: %d, pageSize: %d", runnerID, page, pageSize)
	return &dto.RunnerOrderListResponse{
		List:  []dto.RunnerOrderResponse{},
		Total: 0,
	}, nil
}

// GetRunnerBalance 获取跑腿员账户余额信息
func (s *RunnerServiceImpl) GetRunnerBalance(ctx context.Context, runnerID int64) (*dto.RunnerBalanceResponse, error) {
	// 检查跑腿员是否存在
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, result.ErrNotFound.WithDetails("跑腿员不存在")
	}

	// 获取账户总余额（钱包金额）
	wallet := runner.Wallet

	// 暂时简化处理，先不使用数据库查询，避免数据库结构差异
	// 根据需求，返回三种金额信息

	// 获取账户冻结金额（根据订单未完成等原因）
	// 暂时假设冻结金额为0（实际应根据未完成订单等情况计算）
	frozen := float64(0)

	// 暂时假设提现中金额为0（原方法GetRunnerWithdrawingAmount查询出错）
	// TODO: 实现正确的提现中金额查询逻辑
	withdrawing := float64(0)

	// 计算可提现金额 = 钱包余额 - 冻结金额 - 提现中金额
	available := wallet - frozen - withdrawing
	if available < 0 {
		available = 0
	}

	// 构建响应
	return &dto.RunnerBalanceResponse{
		Available:   available,
		Frozen:      frozen,
		Withdrawing: withdrawing,
	}, nil
}

// GetPaymentAccounts 获取跑腿员支付账户信息
func (s *RunnerServiceImpl) GetPaymentAccounts(ctx context.Context, runnerID int64) ([]dto.PaymentAccountsResponse, error) {
	// 检查跑腿员是否存在
	_, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, result.ErrNotFound.WithDetails("跑腿员不存在")
	}

	// 这里我们模拟数据，实际应该从数据库中获取
	// TODO: 实现从数据库中获取跑腿员支付账户信息
	
	// 模拟数据，这里我们添加一个示例账户
	accounts := []dto.PaymentAccountsResponse{
		{
			ID:            1,
			RunnerID:      runnerID,
			AccountType:   1, // 1-微信
			AccountName:   "测试账户",
			AccountNumber: "test_account_123",
			BankName:      "",
			IsDefault:     true,
			CreatedAt:     time.Now().Format("2006-01-02 15:04:05"),
			UpdatedAt:     time.Now().Format("2006-01-02 15:04:05"),
		},
	}

	return accounts, nil
}

// GetNotificationSettings 获取跑腿员通知设置
func (s *RunnerServiceImpl) GetNotificationSettings(ctx context.Context, runnerID int64) (*dto.NotificationSettingsResponse, error) {
	// 检查跑腿员是否存在
	_, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, result.ErrNotFound.WithDetails("跑腿员不存在")
	}

	// 这里我们模拟数据，实际应该从数据库中获取
	// TODO: 实现从数据库中获取跑腿员通知设置
	
	// 返回默认设置
	return &dto.NotificationSettingsResponse{
		RunnerID:             runnerID,
		OrderNotification:    true,
		SystemNotification:   true,
		MarketingNotification: false,
		PaymentNotification:  true,
		Sound:                true,
		Vibration:            true,
		UpdatedAt:            time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

// GetServiceSettings 获取跑腿员服务设置
func (s *RunnerServiceImpl) GetServiceSettings(ctx context.Context, runnerID int64) (*dto.ServiceSettingsResponse, error) {
	// 检查跑腿员是否存在
	runner, err := s.runnerRepo.GetRunnerByID(ctx, runnerID)
	if err != nil {
		logs.Error("获取跑腿员信息失败: %v", err)
		return nil, result.ErrNotFound.WithDetails("跑腿员不存在")
	}

	// 这里我们可以使用跑腿员现有的一些设置，例如服务范围
	// 其他设置我们模拟数据，实际应该从数据库中获取
	// TODO: 实现从数据库中获取跑腿员服务设置
	
	// 返回设置，部分使用跑腿员模型中的数据
	return &dto.ServiceSettingsResponse{
		RunnerID:            runnerID,
		AutoAcceptOrder:     false,
		MaxOrderDistance:    runner.ServiceRadius,
		OrderTypes:          []int{1, 2, 3, 4}, // 1-外卖 2-快递 3-买药 4-代购
		WorkingHoursStart:   "08:00",
		WorkingHoursEnd:     "20:00",
		RestDays:            []int{7}, // 7-周日
		MaxSimultaneousOrder: 2,
		UpdatedAt:           time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}
