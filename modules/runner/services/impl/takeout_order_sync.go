/**
 * 订单状态同步逻辑
 *
 * 本文件定义了用于同步更新订单状态的功能方法，包括：
 * - 外卖订单配送状态同步：更新takeout_order_extension表的delivery_status字段
 * - 主订单状态同步：更新order表的status字段
 *
 * 所有这些同步方法都使用原生SQL直接操作数据库，避免了模块间的循环依赖。
 * 在跑腿员配送流程中，会同时更新跑腿订单状态、外卖订单配送状态及主订单状态。
 */

package impl

import (
	"context"
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// syncTakeoutOrderDeliveryStatus 同步更新外卖订单的配送状态
// orderNo: 订单号
// deliveryStatus: 配送状态值，参考takeout模块中的DeliveryStatus常量定义
func (s *RunnerServiceImpl) syncTakeoutOrderDeliveryStatus(ctx context.Context, orderNo string, deliveryStatus int) error {
	// 通过原生SQL直接更新外卖订单扩展表中的配送状态
	// 这样可以避免模块间的循环依赖问题
	o := orm.NewOrm()
	sql := "UPDATE takeout_order_extension SET delivery_status = ? WHERE order_no = ?"
	
	res, err := o.Raw(sql, deliveryStatus, orderNo).Exec()
	if err != nil {
		logs.Error("更新外卖订单配送状态失败: %v", err)
		return err
	}
	
	affected, _ := res.RowsAffected()
	if affected == 0 {
		// 没有找到匹配的外卖订单，可能不是外卖订单
		logs.Info("未找到匹配的外卖订单，订单号: %s", orderNo)
		return nil
	}
	
	logs.Info("成功更新外卖订单配送状态，订单号: %s，新状态: %d", orderNo, deliveryStatus)
	return nil
}

// syncOrderStatus 同步更新主订单的状态
// orderNo: 订单号
// orderStatus: 订单状态值，参考order模块中的OrderStatus常量定义
func (s *RunnerServiceImpl) syncOrderStatus(ctx context.Context, orderNo string, orderStatus int) error {
	// 通过原生SQL直接更新主订单表中的状态字段
	// 这样可以避免模块间的循环依赖问题
	o := orm.NewOrm()
	sql := "UPDATE `order` SET status = ? WHERE order_no = ?"
	
	res, err := o.Raw(sql, orderStatus, orderNo).Exec()
	if err != nil {
		logs.Error("更新主订单状态失败: %v", err)
		return err
	}
	
	affected, _ := res.RowsAffected()
	if affected == 0 {
		// 没有找到匹配的主订单，记录日志
		logs.Warn("未找到匹配的主订单，订单号: %s", orderNo)
		return nil
	}
	
	logs.Info("成功更新主订单状态，订单号: %s，新状态: %d", orderNo, orderStatus)
	return nil
}
