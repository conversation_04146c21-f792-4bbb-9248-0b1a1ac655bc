/**
 * 骑手位置服务
 *
 * 本文件实现了骑手位置相关的服务接口，提供骑手位置更新、查询、附近骑手查找等功能
 * 主要用于外卖配送和跑腿服务的骑手调度场景
 */

package services

import (
	"context"
	"errors"
	"math"
	"time"

	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/runner/models"
	"o_mall_backend/modules/runner/repositories"
)

// 地球半径（单位：千米）
const earthRadiusKm = 6371.0

// RunnerLocationService 骑手位置服务接口
type RunnerLocationService interface {
	// UpdateRunnerLocation 更新骑手位置信息
	UpdateRunnerLocation(ctx context.Context, runnerID int64, latitude, longitude float64) error
	
	// GetRunnerLocation 获取骑手当前位置信息
	GetRunnerLocation(ctx context.Context, runnerID int64) (*RunnerLocationInfo, error)
	
	// CalculateDistance 计算两个位置之间的距离（单位：千米）
	CalculateDistance(lat1, lng1, lat2, lng2 float64) float64
	
	// FindNearbyRunners 查找附近可接单的骑手
	FindNearbyRunners(ctx context.Context, latitude, longitude float64, radiusKm float64, limit int) ([]*NearbyRunnerInfo, error)
}

// RunnerLocationInfo 骑手位置信息
type RunnerLocationInfo struct {
	RunnerID   int64     `json:"runner_id"`   // 骑手ID
	RealName   string    `json:"real_name"`   // 骑手姓名
	Mobile     string    `json:"mobile"`      // 骑手电话
	Latitude   float64   `json:"latitude"`    // 纬度
	Longitude  float64   `json:"longitude"`   // 经度
	UpdateTime time.Time `json:"update_time"` // 位置更新时间
}

// NearbyRunnerInfo 附近骑手信息
type NearbyRunnerInfo struct {
	RunnerLocationInfo
	IsOnline      bool    `json:"is_online"`       // 是否在线
	WorkingStatus int     `json:"working_status"`  // 工作状态
	Score         float64 `json:"score"`           // 评分
	OrderCount    int     `json:"order_count"`     // 总接单数
	SuccessCount  int     `json:"success_count"`   // 成功订单数
	Distance      float64 `json:"distance"`        // 距离（千米）
}

// RunnerLocationServiceImpl 骑手位置服务实现
type RunnerLocationServiceImpl struct {
	runnerRepo     repositories.RunnerRepository
	runnerLocRepo  repositories.RunnerLocationRepository
}

// NewRunnerLocationService 创建骑手位置服务实例
func NewRunnerLocationService() RunnerLocationService {
	return &RunnerLocationServiceImpl{
		runnerRepo:    repositories.NewRunnerRepository(),
		runnerLocRepo: repositories.NewRunnerLocationRepository(),
	}
}

// UpdateRunnerLocation 更新骑手位置信息
func (s *RunnerLocationServiceImpl) UpdateRunnerLocation(ctx context.Context, runnerID int64, latitude, longitude float64) error {
	// 验证骑手ID是否有效
	runner, err := s.runnerRepo.GetByID(ctx, runnerID)
	if err != nil {
		logs.Error("查询骑手信息失败: %v", err)
		return err
	}
	
	if runner == nil {
		return errors.New("骑手不存在")
	}
	
	// 检查骑手状态
	if runner.Status != 1 { // 假设1表示审核通过状态
		return errors.New("骑手状态异常，无法更新位置信息")
	}
	
	// 1. 更新Runner表中的位置信息
	runner.Latitude = latitude
	runner.Longitude = longitude
	
	if err := s.runnerRepo.Update(ctx, runner); err != nil {
		logs.Error("更新骑手位置信息失败: %v", err)
		return err
	}
	
	// 2. 插入位置记录到RunnerLocation表
	location := &models.RunnerLocation{
		RunnerID:  runnerID,
		Latitude:  latitude,
		Longitude: longitude,
	}
	
	if err := s.runnerLocRepo.Create(ctx, location); err != nil {
		logs.Error("创建骑手位置记录失败: %v", err)
		// 即使创建位置记录失败，也不影响Runner表中位置的更新，所以这里不返回错误
		logs.Warning("骑手位置记录创建失败，但Runner表位置已更新")
	}
	
	return nil
}

// GetRunnerLocation 获取骑手当前位置信息
func (s *RunnerLocationServiceImpl) GetRunnerLocation(ctx context.Context, runnerID int64) (*RunnerLocationInfo, error) {
	// 查询骑手基本信息
	runner, err := s.runnerRepo.GetByID(ctx, runnerID)
	if err != nil {
		logs.Error("查询骑手信息失败: %v", err)
		return nil, err
	}
	
	if runner == nil {
		return nil, errors.New("骑手不存在")
	}
	
	// 查询最新的位置记录
	latestLocation, err := s.runnerLocRepo.GetLatestByRunnerID(ctx, runnerID)
	if err != nil {
		logs.Error("查询骑手最新位置记录失败: %v", err)
		// 如果查询位置记录失败，则使用Runner表中的位置数据
	}
	
	// 构建位置信息响应
	result := &RunnerLocationInfo{
		RunnerID:   runner.ID,
		RealName:   runner.RealName,
		Mobile:     runner.Mobile,
		UpdateTime: time.Now(),
	}
	
	// 优先使用位置记录表中的最新位置
	if latestLocation != nil {
		result.Latitude = latestLocation.Latitude
		result.Longitude = latestLocation.Longitude
		result.UpdateTime = latestLocation.CreateTime
	} else {
		// 如果没有位置记录，则使用Runner表中的位置数据
		result.Latitude = runner.Latitude
		result.Longitude = runner.Longitude
	}
	
	return result, nil
}

// CalculateDistance 计算两个位置之间的距离（单位：千米）
// 使用Haversine公式计算球面两点间的距离
func (s *RunnerLocationServiceImpl) CalculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	// 将角度转换为弧度
	lat1Rad := lat1 * math.Pi / 180.0
	lng1Rad := lng1 * math.Pi / 180.0
	lat2Rad := lat2 * math.Pi / 180.0
	lng2Rad := lng2 * math.Pi / 180.0
	
	// Haversine公式
	dLat := lat2Rad - lat1Rad
	dLng := lng2Rad - lng1Rad
	a := math.Pow(math.Sin(dLat/2), 2) + math.Cos(lat1Rad)*math.Cos(lat2Rad)*math.Pow(math.Sin(dLng/2), 2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadiusKm * c
	
	// 保留两位小数
	return math.Round(distance*100) / 100
}

// FindNearbyRunners 查找附近可接单的骑手
func (s *RunnerLocationServiceImpl) FindNearbyRunners(ctx context.Context, latitude, longitude float64, radiusKm float64, limit int) ([]*NearbyRunnerInfo, error) {
	// 获取所有在线且正常状态的骑手
	runners, err := s.runnerRepo.FindAvailable(ctx)
	if err != nil {
		logs.Error("查询可用骑手列表失败: %v", err)
		return nil, err
	}
	
	if len(runners) == 0 {
		return []*NearbyRunnerInfo{}, nil
	}
	
	// 计算距离并筛选满足条件的骑手
	var result []*NearbyRunnerInfo
	for _, runner := range runners {
		// 跳过位置信息不完整的骑手
		if runner.Latitude == 0 && runner.Longitude == 0 {
			continue
		}
		
		// 跳过非"接单中"状态的骑手
		if runner.WorkingStatus != 1 { // 假设1表示"接单中"状态
			continue
		}
		
		// 计算距离
		distance := s.CalculateDistance(latitude, longitude, runner.Latitude, runner.Longitude)
		
		// 如果在指定半径内，加入结果集
		if distance <= radiusKm {
			result = append(result, &NearbyRunnerInfo{
				RunnerLocationInfo: RunnerLocationInfo{
					RunnerID:   runner.ID,
					RealName:   runner.RealName,
					Mobile:     runner.Mobile,
					Latitude:   runner.Latitude,
					Longitude:  runner.Longitude,
					UpdateTime: runner.LastOnlineTime,
				},
				IsOnline:      runner.IsOnline,
				WorkingStatus: runner.WorkingStatus,
				Score:         runner.Score,
				OrderCount:    runner.OrderCount,
				SuccessCount:  runner.SuccessCount,
				Distance:      distance,
			})
		}
	}
	
	// 按距离排序（这里简单实现，实际可能需要更复杂的排序逻辑）
	// 冒泡排序
	for i := 0; i < len(result)-1; i++ {
		for j := 0; j < len(result)-i-1; j++ {
			if result[j].Distance > result[j+1].Distance {
				result[j], result[j+1] = result[j+1], result[j]
			}
		}
	}
	
	// 限制返回数量
	if limit > 0 && len(result) > limit {
		result = result[:limit]
	}
	
	return result, nil
}
