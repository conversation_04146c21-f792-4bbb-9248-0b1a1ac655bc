/**
 * runner_service.go
 * 跑腿服务接口定义
 * 
 * 本文件定义了跑腿模块的服务接口，提供跑腿员管理、订单处理等功能
 */

package services

import (
	"context"

	"o_mall_backend/modules/runner/dto"
)

// RunnerService 跑腿员服务接口
type RunnerService interface {
	// Runner 跑腿员相关
	// 注册跑腿员
	RegisterRunner(ctx context.Context, req *dto.RunnerRegisterRequest, userID int64) error
	// 获取申请状态
	GetApplyStatus(ctx context.Context, userID int64) (*dto.RunnerApplyStatusResponse, error)
	// 获取跑腿员信息
	GetRunnerInfo(ctx context.Context, runnerID int64) (*dto.RunnerResponse, error)
	// 获取跑腿员详细信息
	GetRunnerDetail(ctx context.Context, runnerID int64) (*dto.RunnerDetailResponse, error)
	// 更新跑腿员状态
	UpdateRunnerStatus(ctx context.Context, runnerID int64, req *dto.UpdateRunnerStatusRequest) error
	// 更新跑腿员工作状态
	UpdateWorkingStatus(ctx context.Context, runnerID int64, req *dto.UpdateWorkingStatusRequest) error
	// 更新跑腿员位置
	UpdateRunnerLocation(ctx context.Context, runnerID int64, req *dto.UpdateRunnerLocationRequest) error
	// 更新跑腿员在线状态
	UpdateOnlineStatus(ctx context.Context, runnerID int64, isOnline bool) error
	// 查询附近跑腿员
	GetNearbyRunners(ctx context.Context, req *dto.NearbyRunnerRequest) ([]dto.NearbyRunnerResponse, error)
	
	// RunnerOrder 跑腿订单相关
	// 创建跑腿订单
	CreateRunnerOrder(ctx context.Context, req *dto.CreateRunnerOrderRequest, userID int64) (*dto.RunnerOrderResponse, error)
	// 获取跑腿订单信息
	GetRunnerOrder(ctx context.Context, orderID int64) (*dto.RunnerOrderResponse, error)
	// 更新订单状态和支付状态
	UpdateOrderStatus(ctx context.Context, orderID int64, status int, payStatus int, remark string) error
	// 获取用户跑腿订单列表
	ListUserRunnerOrders(ctx context.Context, req *dto.RunnerOrderListRequest, userID int64) (*dto.RunnerOrderListResponse, error)
	// 获取跑腿员订单列表
	ListRunnerOrders(ctx context.Context, req *dto.RunnerOrderListRequest, runnerID int64) (*dto.RunnerOrderListResponse, error)
	// 接单
	AcceptOrder(ctx context.Context, req *dto.AcceptOrderRequest, runnerID int64) error
	// 取消订单
	CancelOrder(ctx context.Context, req *dto.CancelOrderRequest, userID int64, userType int) error
	// 取货
	PickupOrder(ctx context.Context, req *dto.PickupOrderRequest, runnerID int64) error
	// 开始配送
	StartDelivery(ctx context.Context, req *dto.StartDeliveryRequest, runnerID int64) error
	// 完成订单
	CompleteOrder(ctx context.Context, req *dto.CompleteOrderRequest, runnerID int64) error
	// 用户评价订单
	UserRateOrder(ctx context.Context, req *dto.RateOrderRequest, userID int64) error
	// 跑腿员评价订单
	RunnerRateOrder(ctx context.Context, req *dto.RateOrderRequest, runnerID int64) error
	// 计算配送费
	CalculateDeliveryFee(ctx context.Context, req *dto.DeliveryFeeCalculateRequest) (*dto.DeliveryFeeCalculateResponse, error)
	
	// RunnerIncome 跑腿员收入相关
	// 获取跑腿员收入统计
	GetRunnerIncome(ctx context.Context, runnerID int64) (*dto.RunnerIncomeResponse, error)
	// 获取跑腿员收入统计数据（今日、本周、本月、累计）
	GetIncomeStats(ctx context.Context, runnerID int64) (*dto.RunnerIncomeStatsResponse, error)
	// 获取跑腿员收入记录列表
	ListRunnerIncomeLogs(ctx context.Context, page, pageSize int, runnerID int64) ([]dto.RunnerIncomeLogResponse, int64, error)
	// 提现申请
	ApplyWithdrawal(ctx context.Context, req *dto.WithdrawalRequest, runnerID int64) (*dto.WithdrawalResponse, error)
	// 获取提现记录列表
	ListWithdrawals(ctx context.Context, page, pageSize int, runnerID int64) ([]dto.WithdrawalResponse, int64, error)
	// 获取跑腿员账户余额信息
	GetRunnerBalance(ctx context.Context, runnerID int64) (*dto.RunnerBalanceResponse, error)
	
	// RunnerStatistics 跑腿员统计相关
	// 获取跑腿员统计信息
	GetRunnerStatistics(ctx context.Context, runnerID int64) (*dto.RunnerStatisticsResponse, error)
	// 获取跑腿员今日统计信息
	GetRunnerTodayStats(ctx context.Context, runnerID int64) (*dto.RunnerTodayStatsResponse, error)
	
	// RunnerSettings 跑腿员设置相关
	// 获取跑腿员支付账户信息
	GetPaymentAccounts(ctx context.Context, runnerID int64) ([]dto.PaymentAccountsResponse, error)
	// 获取跑腿员通知设置
	GetNotificationSettings(ctx context.Context, runnerID int64) (*dto.NotificationSettingsResponse, error)
	// 获取跑腿员服务设置
	GetServiceSettings(ctx context.Context, runnerID int64) (*dto.ServiceSettingsResponse, error)

	// Admin 管理员相关
	// 获取跑腿员列表（管理员）
	AdminListRunners(ctx context.Context, req *dto.AdminRunnerListRequest) (*dto.AdminRunnerListResponse, error)
	// 获取跑腿员详细信息（管理员）
	AdminGetRunnerDetail(ctx context.Context, runnerID int64) (*dto.AdminRunnerDetailResponse, error)
	// 审核跑腿员（管理员）
	AdminAuditRunner(ctx context.Context, runnerID int64, req *dto.AdminAuditRunnerRequest, adminID int64) error
	// 更新跑腿员状态（管理员）
	AdminUpdateRunnerStatus(ctx context.Context, runnerID int64, req *dto.AdminUpdateRunnerStatusRequest, adminID int64) error
	// 删除跑腿员（管理员）
	AdminDeleteRunner(ctx context.Context, runnerID int64, adminID int64) error
	// 获取跑腿员统计信息（管理员）
	AdminGetRunnerStatistics(ctx context.Context) (*dto.AdminRunnerStatisticsResponse, error)
	// 更新跑腿员备注（管理员）
	AdminUpdateRunnerRemark(ctx context.Context, runnerID int64, req *dto.AdminUpdateRunnerRemarkRequest, adminID int64) error
	// 获取跑腿员订单列表（管理员）
	AdminGetRunnerOrders(ctx context.Context, runnerID int64, page, pageSize int) (*dto.RunnerOrderListResponse, error)
}
