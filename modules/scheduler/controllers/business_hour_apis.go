/**
 * 商家营业时间API实现
 *
 * 该文件包含商家营业时间API的补充实现，包括删除和更新营业时间的功能。
 * 使用Key方法（weekday:startTime:endTime格式）作为唯一标识符来操作特定的营业时间段。
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/scheduler/utils"
)

// DeleteBusinessHours 删除商家营业时间段
// @Title 删除商家营业时间段
// @Description 删除指定的商家营业时间段
// @Param merchantId query int false "商家ID，不传则使用当前登录商家的ID"
// @Param body body object true "要删除的营业时间信息，可以指定keys或filters"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [delete]
func (c *BusinessHourController) DeleteBusinessHours() {
	logs.Info("[BusinessHourController.DeleteBusinessHours] 删除商家营业时间段")

	// 获取商家ID
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从当前登录用户获取商家ID
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok || merchantIDFromCtx == 0 {
			c.Data["json"] = utils.Error(401, "获取商家ID失败，请重新登录")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx
	}

	// 解析请求数据
	var params struct {
		Keys    []string               `json:"keys"`    // 要删除的营业时间键列表，格式：weekday:startTime:endTime
		Filters map[string]interface{} `json:"filters"` // 过滤条件，如按星期删除指定的营业时间
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &params); err != nil {
		logs.Error("[BusinessHourController.DeleteBusinessHours] 解析请求数据失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	if len(params.Keys) == 0 && len(params.Filters) == 0 {
		c.Data["json"] = utils.Error(400, "请指定要删除的营业时间键或过滤条件")
		c.ServeJSON()
		return
	}

	// 获取当前的营业时间列表
	existingHours, err := c.businessHourService.GetMerchantBusinessHours(merchantID)
	if err != nil && err != orm.ErrNoRows {
		logs.Error("[BusinessHourController.DeleteBusinessHours] 获取现有营业时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "获取现有营业时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 如果没有营业时间段，直接返回成功
	if len(existingHours.BusinessHours) == 0 {
		c.Data["json"] = utils.Success(map[string]interface{}{
			"deleted": 0,
		})
		c.ServeJSON()
		return
	}

	// 处理删除逻辑
	var updatedHours []models.BusinessHour
	deleteCount := 0

	// 首先根据具体Key删除
	if len(params.Keys) > 0 {
		// 将Key列表转换为map便于查找
		deleteKeys := make(map[string]bool)
		for _, key := range params.Keys {
			deleteKeys[key] = true
		}

		// 筛选出要保留的营业时间
		for _, hour := range existingHours.BusinessHours {
			if !deleteKeys[hour.Key()] {
				updatedHours = append(updatedHours, hour)
			} else {
				deleteCount++
			}
		}
	} else if len(params.Filters) > 0 {
		// 根据过滤条件删除
		for _, hour := range existingHours.BusinessHours {
			shouldDelete := false

			// 检查是否指定了星期过滤
			if weekdays, exists := params.Filters["weekdays"]; exists {
				// 判断当前营业时间的星期是否在指定的星期列表中
				weekdayList, ok := weekdays.([]interface{})
				if ok {
					for _, w := range weekdayList {
						if wInt, ok := w.(float64); ok && int(wInt) == hour.Weekday {
							shouldDelete = true
							break
						}
					}
				}
			}

			// 检查是否已经完成判断
			if !shouldDelete {
				updatedHours = append(updatedHours, hour)
			} else {
				deleteCount++
			}
		}
	}

	// 如果没有删除任何营业时间段，直接返回成功
	if deleteCount == 0 {
		c.Data["json"] = utils.Success(map[string]interface{}{
			"deleted": 0,
		})
		c.ServeJSON()
		return
	}

	// 设置更新后的营业时间
	err = c.businessHourService.SetMerchantBusinessHours(merchantID, updatedHours)
	if err != nil {
		logs.Error("[BusinessHourController.DeleteBusinessHours] 更新营业时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "删除营业时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 重新安排营业时间任务
	err = c.businessHourService.ScheduleMerchantBusinessHourTasks(merchantID)
	if err != nil {
		logs.Error("[BusinessHourController.DeleteBusinessHours] 重新安排营业时间任务失败: %v", err)
		// 这里不返回错误，因为已经删除了营业时间
	}

	c.Data["json"] = utils.Success(map[string]interface{}{
		"deleted": deleteCount,
	})
	c.ServeJSON()
}

// UpdateBusinessHours 更新商家营业时间段
// @Title 更新商家营业时间段
// @Description 更新指定的商家营业时间段
// @Param merchantId query int false "商家ID，不传则使用当前登录商家的ID"
// @Param body body object true "要更新的营业时间信息，使用key标识要更新的时间段"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [put]
func (c *BusinessHourController) UpdateBusinessHours() {
	logs.Info("[BusinessHourController.UpdateBusinessHours] 更新商家营业时间段")

	// 获取商家ID
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从当前登录用户获取商家ID
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok || merchantIDFromCtx == 0 {
			c.Data["json"] = utils.Error(401, "获取商家ID失败，请重新登录")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx
	}

	// 解析请求数据
	var params struct {
		BusinessHours []struct {
			Key       string `json:"key"` // 要更新的营业时间键，格式：weekday:startTime:endTime
			Weekday   int    `json:"weekday"`
			StartTime string `json:"startTime"`
			EndTime   string `json:"endTime"`
		} `json:"business_hours"`
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &params); err != nil {
		logs.Error("[BusinessHourController.UpdateBusinessHours] 解析请求数据失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	if len(params.BusinessHours) == 0 {
		c.Data["json"] = utils.Error(400, "营业时间不能为空")
		c.ServeJSON()
		return
	}

	// 获取现有营业时间
	existingHours, err := c.businessHourService.GetMerchantBusinessHours(merchantID)
	if err != nil {
		logs.Error("[BusinessHourController.UpdateBusinessHours] 获取现有营业时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "获取现有营业时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 创建Key到营业时间的映射，用于快速查找
	existingMap := make(map[string]int) // 值是在existingHours中的索引
	for i, hour := range existingHours.BusinessHours {
		existingMap[hour.Key()] = i
	}

	// 处理更新
	updatedCount := 0
	for _, item := range params.BusinessHours {
		// 创建更新后的营业时间对象
		updatedHour := models.BusinessHour{
			Weekday:   item.Weekday,
			StartTime: item.StartTime,
			EndTime:   item.EndTime,
		}

		// 如果指定了Key，则更新对应的记录
		if item.Key != "" {
			if index, exists := existingMap[item.Key]; exists {
				existingHours.BusinessHours[index] = updatedHour
				updatedCount++
			}
		} else {
			// 对于没有Key的记录，将其视为新增
			existingHours.BusinessHours = append(existingHours.BusinessHours, updatedHour)
			updatedCount++
		}
	}

	// 设置更新后的营业时间
	err = c.businessHourService.SetMerchantBusinessHours(merchantID, existingHours.BusinessHours)
	if err != nil {
		logs.Error("[BusinessHourController.UpdateBusinessHours] 设置商家营业时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "更新商家营业时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 安排相应的定时任务
	err = c.businessHourService.ScheduleMerchantBusinessHourTasks(merchantID)
	if err != nil {
		logs.Error("[BusinessHourController.UpdateBusinessHours] 安排营业时间任务失败: %v", err)
		// 这里不返回错误，因为已经设置了营业时间
	}

	c.Data["json"] = utils.Success(map[string]interface{}{
		"updated": updatedCount,
	})
	c.ServeJSON()
}
