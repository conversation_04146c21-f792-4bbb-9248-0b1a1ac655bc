/**
 * 商家营业时间控制器
 *
 * 该文件实现了商家营业时间的控制器，提供了RESTful API接口，
 * 用于查询、设置、修改和删除商家营业时间。
 */

package controllers

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/scheduler/core"
	"o_mall_backend/modules/scheduler/services"
	"o_mall_backend/modules/scheduler/services/impl"
	"o_mall_backend/modules/scheduler/utils"
)

// BusinessHourController 商家营业时间控制器
type BusinessHourController struct {
	controllers.BaseController
	businessHourService services.BusinessHourService
}

// Prepare 初始化控制器
func (c *BusinessHourController) Prepare() {
	c.BaseController.Prepare()
	c.businessHourService = impl.NewBusinessHourService()
}

// GetBusinessHours 获取商家营业时间
// @Title 获取商家营业时间
// @Description 获取商家的营业时间列表
// @Param merchantId query int false "商家ID，不传则获取当前登录商家的营业时间"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [get]
func (c *BusinessHourController) GetBusinessHours() {
	logs.Info("[BusinessHourController.GetBusinessHours] 获取商家营业时间")

	// 获取商家ID，优先从查询参数获取，否则从登录用户获取
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok {
			c.Data["json"] = utils.Error(401, "未认证的请求")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx
		if merchantID == 0 {
			c.Data["json"] = utils.Error(401, "请先登录")
			c.ServeJSON()
			return
		}
	}

	// 获取营业时间列表及状态
	hoursWithStatus, err := c.businessHourService.GetMerchantBusinessHours(merchantID)
	if err != nil {
		// 区分没有找到记录和其他错误
		if err.Error() == "<QuerySeter> no row found" {
			// 没有数据，返回空数组和默认状态
			hoursWithStatus = models.BusinessHoursWithStatus{
				BusinessHours:   []models.BusinessHour{},
				OperationStatus: 0, // 默认休息中
			}
		} else {
			logs.Error("[BusinessHourController.GetBusinessHours] 获取商家营业时间失败: %v", err)
			c.Data["json"] = utils.Error(500, "获取商家营业时间失败: "+err.Error())
			c.ServeJSON()
			return
		}
	}

	// 获取自动排班设置（这里暂时使用固定值，后续可以从商家设置中获取）
	autoSchedule := true

	// 获取营业状态数据
	status, err := c.businessHourService.GetMerchantOperationStatus(merchantID)
	if err != nil {
		logs.Warn("[BusinessHourController.GetBusinessHours] 获取商家营业状态失败: %v", err)
		// 使用默认状态
		status = models.BusinessHourStatus{
			OperationStatus: hoursWithStatus.OperationStatus,
			AutoRestore:    false,
			RestoreTime:    time.Time{},
			Remarks:        "",
			LastUpdated:    time.Now(),
		}
	}

	c.Data["json"] = utils.Success(map[string]interface{}{
		"operation_status": status.OperationStatus,
		"auto_schedule":    autoSchedule,
		"business_hours":   hoursWithStatus.BusinessHours,
		"status_data": map[string]interface{}{
			"auto_restore": status.AutoRestore,
			"restore_time": status.RestoreTime.Format(time.RFC3339),
			"remarks":      status.Remarks,
			"last_updated": status.LastUpdated.Format(time.RFC3339),
		},
	})
	c.ServeJSON()
}

// AddBusinessHours 添加商家营业时间段
// @Title 添加商家营业时间段
// @Description 添加新的商家营业时间段
// @Param merchantId query int false "商家ID，不传则使用当前登录商家的ID"
// @Param body body object true "营业时间信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router / [post]
func (c *BusinessHourController) AddBusinessHours() {
	logs.Info("[BusinessHourController.AddBusinessHours] 添加商家营业时间段")

	// 获取商家ID
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok {
			c.Data["json"] = utils.Error(401, "未认证的请求")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx
		if merchantID == 0 {
			c.Data["json"] = utils.Error(401, "请先登录")
			c.ServeJSON()
			return
		}
	}

	// 解析请求数据
	var params struct {
		BusinessHours []models.BusinessHour `json:"business_hours"`
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &params); err != nil {
		logs.Error("[BusinessHourController.AddBusinessHours] 解析请求数据失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	if len(params.BusinessHours) == 0 {
		c.Data["json"] = utils.Error(400, "营业时间不能为空")
		c.ServeJSON()
		return
	}

	// 获取现有营业时间
	existingHours, err := c.businessHourService.GetMerchantBusinessHours(merchantID)
	if err != nil {
		logs.Error("[BusinessHourController.AddBusinessHours] 获取现有营业时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "获取现有营业时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 为新的营业时间段设置ID
	for i := range params.BusinessHours {
		params.BusinessHours[i].ID = params.BusinessHours[i].Key()
	}

	// 合并新的营业时间
	allHours := models.BusinessHoursWithStatus{
		BusinessHours:   append(existingHours.BusinessHours, params.BusinessHours...),
		OperationStatus: existingHours.OperationStatus,
	}

	// 设置商家营业时间
	err = c.businessHourService.SetMerchantBusinessHours(merchantID, allHours.BusinessHours)
	if err != nil {
		logs.Error("[BusinessHourController.AddBusinessHours] 设置商家营业时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "设置商家营业时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 安排相应的定时任务
	err = c.businessHourService.ScheduleMerchantBusinessHourTasks(merchantID)
	if err != nil {
		logs.Error("[BusinessHourController.AddBusinessHours] 安排营业时间任务失败: %v", err)
		// 这里不返回错误，因为已经设置了营业时间
	}

	// 为新添加的营业时间段生成key信息并返回
	result := map[string]interface{}{
		"updated":         true,
		"scheduled_tasks": len(params.BusinessHours) * 2, // 每个营业时间段会生成开店和关店两个任务
	}

	// 添加新营业时间段的key信息
	addedHours := make([]map[string]interface{}, 0, len(params.BusinessHours))
	for _, hour := range params.BusinessHours {
		addedHours = append(addedHours, map[string]interface{}{
			"weekday":   hour.Weekday,
			"startTime": hour.StartTime,
			"endTime":   hour.EndTime,
			"key":       hour.ID, // 使用存储的ID而不是重新生成
		})
	}
	result["added_hours"] = addedHours

	c.Data["json"] = utils.Success(result)
	c.ServeJSON()
}

// BatchSetBusinessHours 批量设置每周营业时间
// @Title 批量设置每周营业时间
// @Description 批量设置工作日和周末的营业时间
// @Param merchantId query int false "商家ID，不传则使用当前登录商家的ID"
// @Param body body object true "营业时间信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /batch [put]
func (c *BusinessHourController) BatchSetBusinessHours() {
	logs.Info("[BusinessHourController.BatchSetBusinessHours] 批量设置每周营业时间")

	// 获取商家ID
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok {
			c.Data["json"] = utils.Error(401, "未认证的请求")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx
		if merchantID == 0 {
			c.Data["json"] = utils.Error(401, "请先登录")
			c.ServeJSON()
			return
		}
	}

	// 解析请求数据
	var params struct {
		ReplaceExisting bool                  `json:"replace_existing"`
		WeekdayHours    []models.BusinessHour `json:"weekday_hours"`
		WeekendHours    []models.BusinessHour `json:"weekend_hours"`
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &params); err != nil {
		logs.Error("[BusinessHourController.BatchSetBusinessHours] 解析请求数据失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	if len(params.WeekdayHours) == 0 && len(params.WeekendHours) == 0 {
		c.Data["json"] = utils.Error(400, "营业时间不能为空")
		c.ServeJSON()
		return
	}

	// 生成所有的营业时间
	var allHours []models.BusinessHour

	// 如果不是替换现有设置，则获取现有营业时间
	if !params.ReplaceExisting {
		existingHours, err := c.businessHourService.GetMerchantBusinessHours(merchantID)
		if err != nil {
			logs.Error("[BusinessHourController.BatchSetBusinessHours] 获取现有营业时间失败: %v", err)
			c.Data["json"] = utils.Error(500, "获取现有营业时间失败: "+err.Error())
			c.ServeJSON()
			return
		}
		allHours = existingHours.BusinessHours
	}

	// 添加工作日营业时间
	for _, hour := range params.WeekdayHours {
		// 工作日：周一至周五
		for weekday := 1; weekday <= 5; weekday++ {
			newHour := hour
			newHour.Weekday = weekday
			allHours = append(allHours, newHour)
		}
	}

	// 添加周末营业时间
	for _, hour := range params.WeekendHours {
		// 周六
		saturdayHour := hour
		saturdayHour.Weekday = 6
		allHours = append(allHours, saturdayHour)

		// 周日
		sundayHour := hour
		sundayHour.Weekday = 0
		allHours = append(allHours, sundayHour)
	}

	// 设置商家营业时间
	err = c.businessHourService.SetMerchantBusinessHours(merchantID, allHours)
	if err != nil {
		logs.Error("[BusinessHourController.BatchSetBusinessHours] 设置商家营业时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "设置商家营业时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 安排相应的定时任务
	err = c.businessHourService.ScheduleMerchantBusinessHourTasks(merchantID)
	if err != nil {
		logs.Error("[BusinessHourController.BatchSetBusinessHours] 安排营业时间任务失败: %v", err)
		// 这里不返回错误，因为已经设置了营业时间
	}

	// 计算创建的定时任务数量
	taskCount := len(allHours) * 2

	c.Data["json"] = utils.Success(map[string]interface{}{
		"updated":         true,
		"scheduled_tasks": taskCount,
	})
	c.ServeJSON()
}

// UpdateOperationStatus 临时修改营业状态
// @Title 临时修改营业状态
// @Description 临时修改商家的营业状态，支持自动恢复
// @Param merchantId query int false "商家ID，不传则使用当前登录商家的ID"
// @Param body body object true "营业状态信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /operation-status [post]
func (c *BusinessHourController) UpdateOperationStatus() {
	logs.Info("[BusinessHourController.UpdateOperationStatus] 临时修改营业状态")

	// 获取商家ID
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok {
			c.Data["json"] = utils.Error(401, "未认证的请求")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx // 假设BaseController中有此方法
		if merchantID == 0 {
			c.Data["json"] = utils.Error(401, "请先登录")
			c.ServeJSON()
			return
		}
	}

	// 解析请求数据
	var params struct {
		Status      int       `json:"status"`       // 0-休息中, 1-营业中
		AutoRestore bool      `json:"auto_restore"` // 是否自动恢复
		RestoreTime time.Time `json:"restore_time"` // 恢复时间
		Remarks     string    `json:"remarks"`      // 备注说明
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &params); err != nil {
		logs.Error("[BusinessHourController.UpdateOperationStatus] 解析请求数据失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 验证状态值
	if params.Status != 0 && params.Status != 1 {
		c.Data["json"] = utils.Error(400, "无效的营业状态值")
		c.ServeJSON()
		return
	}

	// 验证恢复时间
	if params.AutoRestore && params.RestoreTime.Before(time.Now()) {
		c.Data["json"] = utils.Error(400, "恢复时间必须在当前时间之后")
		c.ServeJSON()
		return
	}

	// 调用商家服务更新营业状态
	// 这里先直接使用scheduler模块中的merchant_service更新状态
	err = core.UpdateMerchantOperationStatus(merchantID, params.Status)
	if err != nil {
		logs.Error("[BusinessHourController.UpdateOperationStatus] 更新商家营业状态失败: %v", err)
		c.Data["json"] = utils.Error(500, "更新商家营业状态失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 如果需要自动恢复，创建一个定时任务
	var taskID string
	if params.AutoRestore {
		// 准备任务负载数据
		taskPayload := map[string]interface{}{
			"merchantId":   merchantID,
			"targetStatus": 1 - params.Status, // 反转状态
			"remarks":      "自动恢复商家营业状态",
		}
		taskPayloadData, _ := json.Marshal(taskPayload)

		// 定义任务类型
		taskType := "merchant_open"
		if params.Status == 1 {
			taskType = "merchant_close"
		}

		// 创建一次性定时任务
		taskService := services.NewTaskService()
		taskID, err = taskService.CreateTask(
			taskType,
			params.RestoreTime,
			merchantID,
			"merchant",
			string(taskPayloadData),
		)

		if err != nil {
			logs.Error("[BusinessHourController.UpdateOperationStatus] 创建自动恢复任务失败: %v", err)
			// 这里不返回错误，因为已经更新了营业状态
		}
	}

	// 准备返回数据
	restoreTimeStr := ""
	if params.AutoRestore {
		restoreTimeStr = params.RestoreTime.Format(time.RFC3339)
	}

	c.Data["json"] = utils.Success(map[string]interface{}{
		"operation_status": params.Status,
		"auto_restore":     params.AutoRestore,
		"restore_time":     restoreTimeStr,
		"remarks":          params.Remarks,
		"task_id":          taskID,
	})
	c.ServeJSON()
}
