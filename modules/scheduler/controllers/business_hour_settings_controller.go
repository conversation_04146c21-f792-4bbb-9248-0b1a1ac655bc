/**
 * 商家营业时间设置控制器
 *
 * 该文件实现了商家营业时间设置的控制器，提供了RESTful API接口，
 * 用于获取和设置商家的自动开关店配置。
 */

package controllers

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/scheduler/services"
	"o_mall_backend/modules/scheduler/services/impl"
	"o_mall_backend/modules/scheduler/utils"
)

// BusinessHourSettingsController 商家营业时间设置控制器
type BusinessHourSettingsController struct {
	controllers.BaseController
	businessHourService services.BusinessHourService
}

// Prepare 初始化控制器
func (c *BusinessHourSettingsController) Prepare() {
	c.BaseController.Prepare()
	c.businessHourService = impl.NewBusinessHourService()
}

// GetAutoScheduleSettings 获取自动开关店设置
// @Title 获取自动开关店设置
// @Description 获取商家的自动开关店设置
// @Param merchantId query int false "商家ID，不传则获取当前登录商家的设置"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /auto-schedule [get]
func (c *BusinessHourSettingsController) GetAutoScheduleSettings() {
	logs.Info("[BusinessHourSettingsController.GetAutoScheduleSettings] 获取自动开关店设置")

	// 获取商家ID
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok {
			c.Data["json"] = utils.Error(401, "未认证的请求")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx
		if merchantID == 0 {
			c.Data["json"] = utils.Error(401, "请先登录")
			c.ServeJSON()
			return
		}
	}

	// 获取自动开关店设置
	settings, err := c.businessHourService.GetMerchantBusinessHourSettings(merchantID)
	if err != nil {
		logs.Error("[BusinessHourSettingsController.GetAutoScheduleSettings] 获取自动开关店设置失败: %v", err)
		c.Data["json"] = utils.Error(500, "获取自动开关店设置失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(settings)
	c.ServeJSON()
}

// SetAutoScheduleSettings 设置自动开关店配置
// @Title 设置自动开关店配置
// @Description 设置商家的自动开关店配置
// @Param merchantId query int false "商家ID，不传则设置当前登录商家的配置"
// @Param body body object true "自动开关店配置信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /auto-schedule [post]
func (c *BusinessHourSettingsController) SetAutoScheduleSettings() {
	logs.Info("[BusinessHourSettingsController.SetAutoScheduleSettings] 设置自动开关店配置")

	// 获取商家ID
	merchantIDStr := c.GetString("merchantId")
	var merchantID int64
	var err error

	if merchantIDStr != "" {
		merchantID, err = strconv.ParseInt(merchantIDStr, 10, 64)
		if err != nil {
			c.Data["json"] = utils.Error(400, "无效的商家ID")
			c.ServeJSON()
			return
		}
	} else {
		// 从上下文获取当前商家ID
		merchantIDFromCtx, ok := c.Ctx.Input.GetData("merchantID").(int64)
		if !ok {
			c.Data["json"] = utils.Error(401, "未认证的请求")
			c.ServeJSON()
			return
		}
		merchantID = merchantIDFromCtx
		if merchantID == 0 {
			c.Data["json"] = utils.Error(401, "请先登录")
			c.ServeJSON()
			return
		}
	}

	// 解析请求数据
	var settings models.BusinessHourSettings
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &settings); err != nil {
		logs.Error("[BusinessHourSettingsController.SetAutoScheduleSettings] 解析请求数据失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 设置自动开关店配置
	settings.LastUpdated = time.Now()
	err = c.businessHourService.SetMerchantBusinessHourSettings(merchantID, settings)
	if err != nil {
		logs.Error("[BusinessHourSettingsController.SetAutoScheduleSettings] 设置自动开关店配置失败: %v", err)
		c.Data["json"] = utils.Error(500, "设置自动开关店配置失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(map[string]interface{}{
		"updated":     true,
		"settings":    settings,
		"updated_at": settings.LastUpdated,
	})
	c.ServeJSON()
}
