/**
 * 定时任务控制器
 *
 * 该文件实现了定时任务的控制器，提供了RESTful API接口，
 * 用于创建、查询、更新和删除定时任务。
 */

package controllers

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/controllers"
	"o_mall_backend/modules/scheduler/models"
	"o_mall_backend/modules/scheduler/services"
	"o_mall_backend/modules/scheduler/utils"
)

// SchedulerController 定时任务控制器
type SchedulerController struct {
	controllers.BaseController
	taskService services.TaskService
}

// Prepare 初始化控制器
func (c *SchedulerController) Prepare() {
	c.BaseController.Prepare()
	c.taskService = services.NewTaskService()
}

// CreateTask 创建定时任务
// @Title 创建定时任务
// @Description 创建一个新的定时任务
// @Param body body object true "任务信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /tasks [post]
func (c *SchedulerController) CreateTask() {
	logs.Info("[SchedulerController.CreateTask] 创建定时任务")

	// 解析请求参数
	var params struct {
		TaskType     string    `json:"task_type"`
		TargetTime   time.Time `json:"target_time"`
		BusinessID   int64     `json:"business_id"`
		BusinessType string    `json:"business_type"`
		Payload      string    `json:"payload"`
		Recurrent    bool      `json:"recurrent"`
		CronExpr     string    `json:"cron_expr"`
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &params); err != nil {
		logs.Error("[SchedulerController.CreateTask] 解析请求参数失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	var taskID string
	var err error

	if params.Recurrent {
		// 创建周期性任务
		taskID, err = c.taskService.CreateRecurrentTask(
			params.TaskType,
			params.CronExpr,
			params.BusinessID,
			params.BusinessType,
			params.Payload,
		)
	} else {
		// 创建一次性任务
		taskID, err = c.taskService.CreateTask(
			params.TaskType,
			params.TargetTime,
			params.BusinessID,
			params.BusinessType,
			params.Payload,
		)
	}

	if err != nil {
		logs.Error("[SchedulerController.CreateTask] 创建任务失败: %v", err)
		c.Data["json"] = utils.Error(500, "创建任务失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(map[string]string{
		"task_id": taskID,
	})
	c.ServeJSON()
}

// GetTask 获取任务详情
// @Title 获取任务详情
// @Description 根据任务ID获取任务详情
// @Param taskId path string true "任务ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /tasks/:taskId [get]
func (c *SchedulerController) GetTask() {
	logs.Info("[SchedulerController.GetTask] 获取任务详情")

	// 获取任务ID
	taskID := c.Ctx.Input.Param(":taskId")
	if taskID == "" {
		c.Data["json"] = utils.Error(400, "任务ID不能为空")
		c.ServeJSON()
		return
	}

	// 获取任务
	task, err := c.taskService.GetTask(taskID)
	if err != nil {
		logs.Error("[SchedulerController.GetTask] 获取任务失败: %v", err)
		c.Data["json"] = utils.Error(500, "获取任务失败: "+err.Error())
		c.ServeJSON()
		return
	}

	if task == nil {
		c.Data["json"] = utils.Error(404, "任务不存在")
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(task)
	c.ServeJSON()
}

// GetTasksByBusiness 获取指定业务的任务列表
// @Title 获取业务任务列表
// @Description 获取指定业务的任务列表
// @Param businessId path int true "业务ID"
// @Param businessType query string true "业务类型"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /business/:businessId/tasks [get]
func (c *SchedulerController) GetTasksByBusiness() {
	logs.Info("[SchedulerController.GetTasksByBusiness] 获取业务任务列表")

	// 获取业务ID
	businessIDStr := c.Ctx.Input.Param(":businessId")
	businessID, err := strconv.ParseInt(businessIDStr, 10, 64)
	if err != nil {
		c.Data["json"] = utils.Error(400, "无效的业务ID")
		c.ServeJSON()
		return
	}

	// 获取业务类型
	businessType := c.GetString("businessType")
	if businessType == "" {
		c.Data["json"] = utils.Error(400, "业务类型不能为空")
		c.ServeJSON()
		return
	}

	// 获取任务列表
	tasks, err := c.taskService.GetTasksByBusiness(businessID, businessType)
	if err != nil {
		logs.Error("[SchedulerController.GetTasksByBusiness] 获取业务任务列表失败: %v", err)
		c.Data["json"] = utils.Error(500, "获取任务列表失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(tasks)
	c.ServeJSON()
}

// UpdateTaskTime 更新任务执行时间
// @Title 更新任务执行时间
// @Description 更新任务的执行时间
// @Param taskId path string true "任务ID"
// @Param body body object true "新的执行时间"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /tasks/:taskId/time [put]
func (c *SchedulerController) UpdateTaskTime() {
	logs.Info("[SchedulerController.UpdateTaskTime] 更新任务执行时间")

	// 获取任务ID
	taskID := c.Ctx.Input.Param(":taskId")
	if taskID == "" {
		c.Data["json"] = utils.Error(400, "任务ID不能为空")
		c.ServeJSON()
		return
	}

	// 解析请求参数
	var params struct {
		TargetTime time.Time `json:"target_time"`
	}

	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &params); err != nil {
		logs.Error("[SchedulerController.UpdateTaskTime] 解析请求参数失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的请求参数")
		c.ServeJSON()
		return
	}

	// 更新任务时间
	err := c.taskService.UpdateTaskTime(taskID, params.TargetTime)
	if err != nil {
		logs.Error("[SchedulerController.UpdateTaskTime] 更新任务时间失败: %v", err)
		c.Data["json"] = utils.Error(500, "更新任务时间失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(nil)
	c.ServeJSON()
}

// CancelTask 取消任务
// @Title 取消任务
// @Description 取消指定的任务
// @Param taskId path string true "任务ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /tasks/:taskId/cancel [put]
func (c *SchedulerController) CancelTask() {
	logs.Info("[SchedulerController.CancelTask] 取消任务")

	// 获取任务ID
	taskID := c.Ctx.Input.Param(":taskId")
	if taskID == "" {
		c.Data["json"] = utils.Error(400, "任务ID不能为空")
		c.ServeJSON()
		return
	}

	// 取消任务
	err := c.taskService.CancelTask(taskID)
	if err != nil {
		logs.Error("[SchedulerController.CancelTask] 取消任务失败: %v", err)
		c.Data["json"] = utils.Error(500, "取消任务失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(nil)
	c.ServeJSON()
}

// ExecuteTask 执行任务
// @Title 手动执行任务
// @Description 手动执行指定的任务
// @Param taskId path string true "任务ID"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 404 {object} result.Result
// @Failure 500 {object} result.Result
// @router /tasks/:taskId/execute [put]
func (c *SchedulerController) ExecuteTask() {
	logs.Info("[SchedulerController.ExecuteTask] 手动执行任务")

	// 获取任务ID
	taskID := c.Ctx.Input.Param(":taskId")
	if taskID == "" {
		c.Data["json"] = utils.Error(400, "任务ID不能为空")
		c.ServeJSON()
		return
	}

	// 执行任务
	err := c.taskService.ExecuteTask(taskID)
	if err != nil {
		logs.Error("[SchedulerController.ExecuteTask] 执行任务失败: %v", err)
		c.Data["json"] = utils.Error(500, "执行任务失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(nil)
	c.ServeJSON()
}

// UpdateMerchantBusinessTime 更新商家营业时间任务
// @Title 更新商家营业时间任务
// @Description 根据商家的营业时间设置创建或更新相应的定时任务
// @Param merchantId path int true "商家ID"
// @Param body body object true "营业时间信息"
// @Success 200 {object} result.Result
// @Failure 400 {object} result.Result
// @Failure 500 {object} result.Result
// @router /merchant/:merchantId/business-time [put]
func (c *SchedulerController) UpdateMerchantBusinessTime() {
	logs.Info("[SchedulerController.UpdateMerchantBusinessTime] 更新商家营业时间任务")

	// 获取商家ID
	merchantIDStr := c.Ctx.Input.Param(":merchantId")
	merchantID, err := strconv.ParseInt(merchantIDStr, 10, 64)
	if err != nil {
		c.Data["json"] = utils.Error(400, "无效的商家ID")
		c.ServeJSON()
		return
	}

	// 解析营业时间
	var businessHours []models.BusinessHour
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &businessHours); err != nil {
		logs.Error("[SchedulerController.UpdateMerchantBusinessTime] 解析营业时间失败: %v", err)
		c.Data["json"] = utils.Error(400, "无效的营业时间格式")
		c.ServeJSON()
		return
	}

	// 更新商家营业时间任务
	err = c.taskService.UpdateMerchantBusinessTimeTasks(merchantID, businessHours)
	if err != nil {
		logs.Error("[SchedulerController.UpdateMerchantBusinessTime] 更新商家营业时间任务失败: %v", err)
		c.Data["json"] = utils.Error(500, "更新商家营业时间任务失败: "+err.Error())
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.Success(nil)
	c.ServeJSON()
}
