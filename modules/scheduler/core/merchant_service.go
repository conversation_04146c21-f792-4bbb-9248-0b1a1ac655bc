/**
 * 商家服务接口
 *
 * 该文件定义了与商家模块交互的功能，负责更新商家的营业状态。
 */

package core

import (
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/merchant/models"
)

// 商家经营状态常量
const (
	MerchantOperationStatusClosed = 0 // 休息中
	MerchantOperationStatusOpen   = 1 // 营业中
)

// UpdateMerchantOperationStatus 更新商家营业状态
func UpdateMerchantOperationStatus(merchantID int64, status int) error {
	logs.Info("[UpdateMerchantOperationStatus] 更新商家营业状态: ID=%d, 状态=%d", merchantID, status)
	
	if status != MerchantOperationStatusOpen && status != MerchantOperationStatusClosed {
		return fmt.Errorf("无效的商家营业状态: %d", status)
	}
	
	// 使用原生SQL更新商家状态
	o := orm.NewOrm()
	sql := "UPDATE merchant SET operation_status = ? WHERE id = ?"
	
	res, err := o.Raw(sql, status, merchantID).Exec()
	if err != nil {
		logs.Error("[UpdateMerchantOperationStatus] 更新商家营业状态失败: %v", err)
		return err
	}
	
	affected, _ := res.RowsAffected()
	if affected == 0 {
		logs.Warn("[UpdateMerchantOperationStatus] 商家不存在或状态未变化: ID=%d", merchantID)
		return fmt.Errorf("商家不存在或状态未变化: %d", merchantID)
	}
	
	statusText := "休息中"
	if status == MerchantOperationStatusOpen {
		statusText = "营业中"
	}
	
	logs.Info("[UpdateMerchantOperationStatus] 商家 %d 状态已更新为: %s", merchantID, statusText)
	
	// 记录商家状态变更日志
	var operationType int
	if status == MerchantOperationStatusOpen {
		operationType = models.MerchantLogTypeOpenBusiness
	} else {
		operationType = models.MerchantLogTypeCloseBusiness
	}

	logEntry := &models.MerchantLog{
		MerchantID:   merchantID,
		OperatorID:   0, // 系统操作
		OperatorType: 0, // 系统
		OperationType: operationType,
		Content:      fmt.Sprintf("系统自动将商家营业状态更新为: %s", statusText),
		RequestURL:   "scheduler/auto",
		RequestData:  "",
		IP:           "127.0.0.1",
		UserAgent:    "scheduler",
		CreatedAt:    time.Now(),
	}
	
	_, err = o.Insert(logEntry)
	if err != nil {
		logs.Error("[UpdateMerchantOperationStatus] 记录商家状态变更日志失败: %v", err)
		// 不返回错误，因为这只是辅助功能
	}
	
	return nil
}
