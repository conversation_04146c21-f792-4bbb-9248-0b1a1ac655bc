/**
 * 任务优先级队列
 *
 * 该文件实现了一个基于堆的任务优先级队列，用于按照任务执行时间排序，
 * 实现高效的任务调度。
 */

package core

import (
	"o_mall_backend/modules/scheduler/models"
)

// TaskPriorityQueue 任务优先级队列，实现了heap.Interface接口
type TaskPriorityQueue []*models.ScheduledTask

// Len 返回队列长度
func (pq TaskPriorityQueue) Len() int {
	return len(pq)
}

// Less 比较两个任务的优先级，时间早的任务优先级更高
func (pq TaskPriorityQueue) Less(i, j int) bool {
	return pq[i].TargetTime.Before(pq[j].TargetTime)
}

// Swap 交换队列中的两个元素
func (pq TaskPriorityQueue) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
}

// Push 将元素添加到队列
func (pq *TaskPriorityQueue) Push(x interface{}) {
	task := x.(*models.ScheduledTask)
	*pq = append(*pq, task)
}

// Pop 从队列中弹出优先级最高的元素
func (pq *TaskPriorityQueue) Pop() interface{} {
	old := *pq
	n := len(old)
	task := old[n-1]
	*pq = old[0 : n-1]
	return task
}
