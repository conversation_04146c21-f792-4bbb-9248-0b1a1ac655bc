/**
 * 定时任务调度器
 *
 * 该文件实现了定时任务的调度器，负责管理任务队列、调度和执行定时任务。
 * 采用事件驱动机制，使用优先级队列管理任务，减少系统资源消耗。
 */

package core

import (
	"container/heap"
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/robfig/cron/v3"

	"o_mall_backend/modules/scheduler/models"
	"o_mall_backend/modules/scheduler/repositories"
	"o_mall_backend/modules/scheduler/repositories/impl"
	takeoutServices "o_mall_backend/modules/takeout/services"
)

// TaskHandler 定义任务处理器函数类型
type TaskHandler func(task *models.ScheduledTask) error

// Scheduler 定时任务调度器
type Scheduler struct {
	taskRepo           repositories.TaskRepository // 任务仓库
	taskQueue          *TaskPriorityQueue          // 任务优先级队列
	taskHandlers       map[string]TaskHandler      // 任务处理器映射
	mutex              sync.RWMutex                // 读写锁
	cronParser         cron.Parser                 // Cron表达式解析器
	wg                 sync.WaitGroup              // 等待组
	triggerChan        chan struct{}               // 触发调度的通道
	stopChan           chan struct{}               // 停止调度的通道
	workerPool         chan struct{}               // 工作池
	maxConcurrentTasks int                         // 最大并发任务数
}

var (
	scheduler *Scheduler // 单例调度器
	once      sync.Once  // 确保只初始化一次
)

// GetScheduler 获取调度器单例
func GetScheduler() *Scheduler {
	once.Do(func() {
		scheduler = &Scheduler{
			taskRepo:           impl.NewTaskRepository(),
			taskQueue:          &TaskPriorityQueue{},
			taskHandlers:       make(map[string]TaskHandler),
			cronParser:         cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow),
			triggerChan:        make(chan struct{}, 1),
			stopChan:           make(chan struct{}),
			workerPool:         make(chan struct{}, 10),
			maxConcurrentTasks: 10,
		}
		heap.Init(scheduler.taskQueue)

		// 注册内置任务处理器
		scheduler.RegisterTaskHandler(models.TaskTypeMerchantOpen, handleMerchantOpen)
		scheduler.RegisterTaskHandler(models.TaskTypeMerchantClose, handleMerchantClose)
		scheduler.RegisterTaskHandler(models.TaskTypeOrderNotification, handleOrderNotification)
		scheduler.RegisterTaskHandler(models.TaskTypeOrderCleanup, handleOrderCleanup)
	})
	return scheduler
}

// RegisterTaskHandler 注册任务处理器
func (s *Scheduler) RegisterTaskHandler(taskType string, handler TaskHandler) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.taskHandlers[taskType] = handler
	logs.Info("[Scheduler.RegisterTaskHandler] 注册任务处理器: %s", taskType)
}

// Start 启动调度器
func (s *Scheduler) Start() {
	logs.Info("[Scheduler.Start] 启动定时任务调度器")

	// 加载持久化的任务
	s.loadTasks()

	// 启动调度循环
	go s.scheduleLoop()

	logs.Info("[Scheduler.Start] 定时任务调度器已启动")
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	logs.Info("[Scheduler.Stop] 停止定时任务调度器")

	// 发送停止信号
	close(s.stopChan)

	// 等待所有任务完成
	s.wg.Wait()

	logs.Info("[Scheduler.Stop] 定时任务调度器已停止")
}

// NotifyNewTask 通知调度器有新任务或任务更新
func (s *Scheduler) NotifyNewTask() {
	// 非阻塞发送，避免阻塞调用者
	select {
	case s.triggerChan <- struct{}{}:
	default:
		// 通道已满，说明已经有通知在等待处理
	}
}

// SubmitTaskForExecution 提交任务到执行器
func (s *Scheduler) SubmitTaskForExecution(task *models.ScheduledTask) {
	s.wg.Add(1)
	go func() {
		// 获取工作池令牌，限制并发数
		s.workerPool <- struct{}{}
		defer func() {
			<-s.workerPool // 释放令牌
			s.wg.Done()    // 标记任务完成
		}()

		s.executeTask(task)
	}()
}

// 加载持久化的任务
func (s *Scheduler) loadTasks() {
	logs.Info("[Scheduler.loadTasks] 加载持久化的任务")

	// 获取所有待执行的任务
	tasks, err := s.taskRepo.GetAll()
	if err != nil {
		logs.Error("[Scheduler.loadTasks] 加载任务失败: %v", err)
		return
	}

	// 将任务加入队列
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for _, task := range tasks {
		if task.Status == models.TaskStatusPending {
			heap.Push(s.taskQueue, task)
			logs.Info("[Scheduler.loadTasks] 加载任务: ID=%s, 类型=%s, 时间=%v",
				task.TaskID, task.TaskType, task.TargetTime)
		}
	}

	logs.Info("[Scheduler.loadTasks] 已加载 %d 个待执行任务", s.taskQueue.Len())
}

// 调度循环
func (s *Scheduler) scheduleLoop() {
	logs.Info("[Scheduler.scheduleLoop] 启动调度循环")

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			// 收到停止信号，退出循环
			logs.Info("[Scheduler.scheduleLoop] 收到停止信号，退出调度循环")
			return

		case <-ticker.C:
			// 定时检查有没有到期的任务
			s.checkAndExecuteTasks()

		case <-s.triggerChan:
			// 收到新任务通知，重新加载任务
			logs.Info("[Scheduler.scheduleLoop] 收到新任务通知，重新加载任务")
			s.mutex.Lock()

			// 清空当前队列
			s.taskQueue = &TaskPriorityQueue{}
			heap.Init(s.taskQueue)

			s.mutex.Unlock()

			// 重新加载任务
			s.loadTasks()

			// 立即检查是否有可执行的任务
			s.checkAndExecuteTasks()
		}
	}
}

// 检查并执行到期任务
func (s *Scheduler) checkAndExecuteTasks() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 如果队列为空，不需要检查
	if s.taskQueue.Len() == 0 {
		return
	}

	now := time.Now()

	// 检查队列顶部任务是否到期
	for s.taskQueue.Len() > 0 {
		// 获取队列顶部任务（不移除）
		top := (*s.taskQueue)[0]

		// 如果任务还没到期，退出循环
		if top.TargetTime.After(now) {
			break
		}

		// 移除顶部任务
		task := heap.Pop(s.taskQueue).(*models.ScheduledTask)

		logs.Info("[Scheduler.checkAndExecuteTasks] 任务到期，准备执行: ID=%s, 类型=%s",
			task.TaskID, task.TaskType)

		// 提交任务到执行器
		s.SubmitTaskForExecution(task)
	}
}

// 执行任务
func (s *Scheduler) executeTask(task *models.ScheduledTask) {
	logs.Info("[Scheduler.executeTask] 开始执行任务: ID=%s, 类型=%s", task.TaskID, task.TaskType)

	// 更新任务状态为执行中
	err := s.taskRepo.UpdateStatus(task.ID, models.TaskStatusRunning, "任务执行中")
	if err != nil {
		logs.Error("[Scheduler.executeTask] 更新任务状态失败: %v", err)
	}

	// 获取任务处理器
	s.mutex.RLock()
	handler, exists := s.taskHandlers[task.TaskType]
	s.mutex.RUnlock()

	var resultMessage string
	var status int

	if !exists {
		// 找不到对应的处理器
		resultMessage = fmt.Sprintf("未找到任务处理器: %s", task.TaskType)
		status = models.TaskStatusFailed
		logs.Error("[Scheduler.executeTask] %s", resultMessage)
	} else {
		// 执行任务
		err := handler(task)
		if err != nil {
			// 任务执行失败
			resultMessage = fmt.Sprintf("任务执行失败: %v", err)
			status = models.TaskStatusFailed
			logs.Error("[Scheduler.executeTask] %s", resultMessage)

			// 如果未达到最大重试次数，创建重试任务
			if task.RetryCount < task.MaxRetries {
				s.scheduleRetry(task)
			}
		} else {
			// 任务执行成功
			resultMessage = "任务执行成功"
			status = models.TaskStatusCompleted
			logs.Info("[Scheduler.executeTask] 任务执行成功: ID=%s", task.TaskID)

			// 如果是周期性任务，创建下一次执行的任务
			if task.Recurrent && task.CronExpression != "" {
				s.scheduleNextRecurrentTask(task)
			}
		}
	}

	// 更新任务状态
	err = s.taskRepo.UpdateStatus(task.ID, status, resultMessage)
	if err != nil {
		logs.Error("[Scheduler.executeTask] 更新任务状态失败: %v", err)
	}
}

// 调度重试任务
func (s *Scheduler) scheduleRetry(task *models.ScheduledTask) {
	logs.Info("[Scheduler.scheduleRetry] 调度重试任务: ID=%s, 重试次数=%d", task.TaskID, task.RetryCount+1)

	// 创建重试任务
	retryTask := &models.ScheduledTask{
		TaskID:       task.TaskID + "_retry_" + fmt.Sprint(task.RetryCount+1),
		TaskType:     task.TaskType,
		TargetTime:   time.Now().Add(time.Minute * time.Duration(task.RetryCount+1)), // 指数退避
		Status:       models.TaskStatusPending,
		BusinessID:   task.BusinessID,
		BusinessType: task.BusinessType,
		Payload:      task.Payload,
		RetryCount:   task.RetryCount + 1,
		MaxRetries:   task.MaxRetries,
	}

	// 保存重试任务
	_, err := s.taskRepo.Create(retryTask)
	if err != nil {
		logs.Error("[Scheduler.scheduleRetry] 创建重试任务失败: %v", err)
		return
	}

	// 注意：不调用NotifyNewTask()避免死循环
	// 重试任务会在下次定时检查时被自然发现和加载
	logs.Info("[Scheduler.scheduleRetry] 重试任务已创建，将在下次定时检查时加载")
}

// 调度下一次周期性任务
func (s *Scheduler) scheduleNextRecurrentTask(task *models.ScheduledTask) {
	logs.Info("[Scheduler.scheduleNextRecurrentTask] 调度下一次周期性任务: ID=%s", task.TaskID)

	// 计算下次执行时间
	nextTime, err := GetNextExecutionTime(task.CronExpression)
	if err != nil {
		logs.Error("[Scheduler.scheduleNextRecurrentTask] 计算下次执行时间失败: %v", err)
		return
	}

	// 创建下一次任务
	nextTask := &models.ScheduledTask{
		TaskID:         task.TaskID + "_next_" + fmt.Sprint(time.Now().UnixNano()),
		TaskType:       task.TaskType,
		TargetTime:     nextTime,
		Status:         models.TaskStatusPending,
		BusinessID:     task.BusinessID,
		BusinessType:   task.BusinessType,
		Payload:        task.Payload,
		Recurrent:      true,
		CronExpression: task.CronExpression,
		NextRunTime:    nextTime,
	}

	// 保存下一次任务
	_, err = s.taskRepo.Create(nextTask)
	if err != nil {
		logs.Error("[Scheduler.scheduleNextRecurrentTask] 创建下一次任务失败: %v", err)
		return
	}

	// 注意：不调用NotifyNewTask()避免死循环
	// 新任务会在下次定时检查时被自然发现和加载
	logs.Info("[Scheduler.scheduleNextRecurrentTask] 下一次任务已创建，将在下次定时检查时加载")
}

// GetNextExecutionTime 获取Cron表达式的下次执行时间
func GetNextExecutionTime(cronExpr string) (time.Time, error) {
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	schedule, err := parser.Parse(cronExpr)
	if err != nil {
		return time.Time{}, err
	}

	return schedule.Next(time.Now()), nil
}

// 内置任务处理器 - 商家开始营业
func handleMerchantOpen(task *models.ScheduledTask) error {
	logs.Info("[handleMerchantOpen] 处理商家开始营业任务: %s", task.TaskID)

	// 解析任务数据
	var payload map[string]interface{}
	err := json.Unmarshal([]byte(task.Payload), &payload)
	if err != nil {
		logs.Error("[handleMerchantOpen] 解析任务数据失败: %v", err)
		return err
	}

	// 获取商家ID
	merchantID, ok := payload["merchant_id"].(float64)
	if !ok {
		// 兼容旧版本的字段名
		merchantID, ok = payload["merchantId"].(float64)
		if !ok {
			logs.Error("[handleMerchantClose] 任务数据缺少商家ID或格式错误")
			return fmt.Errorf("任务数据缺少商家ID或格式错误")
		}
	}

	// 调用商家服务更新营业状态
	err = UpdateMerchantOperationStatus(int64(merchantID), MerchantOperationStatusOpen)
	if err != nil {
		logs.Error("[handleMerchantOpen] 更新商家营业状态失败: %v", err)
		return err
	}

	logs.Info("[handleMerchantOpen] 商家 %d 已开始营业", int64(merchantID))
	return nil
}

// 内置任务处理器 - 商家结束营业
func handleMerchantClose(task *models.ScheduledTask) error {
	logs.Info("[handleMerchantClose] 处理商家结束营业任务: %s", task.TaskID)

	// 解析任务数据
	var payload map[string]interface{}
	err := json.Unmarshal([]byte(task.Payload), &payload)
	if err != nil {
		logs.Error("[handleMerchantClose] 解析任务数据失败: %v", err)
		return err
	}

	// 输出完整的任务负载数据用于调试
	payloadJSON, _ := json.Marshal(payload)
	logs.Info("[handleMerchantClose] 任务负载数据: %s", string(payloadJSON))

	// 获取商家ID
	merchantID, ok := payload["merchant_id"].(float64)
	if !ok {
		// 兼容旧版本的字段名
		merchantID, ok = payload["merchantId"].(float64)
		if !ok {
			logs.Error("[handleMerchantClose] 任务数据缺少商家ID或格式错误")
			return fmt.Errorf("任务数据缺少商家ID或格式错误")
		}
	}

	logs.Info("[handleMerchantClose] 获取到商家ID: %v", merchantID)

	// 调用商家服务更新营业状态
	err = UpdateMerchantOperationStatus(int64(merchantID), MerchantOperationStatusClosed)
	if err != nil {
		logs.Error("[handleMerchantClose] 更新商家营业状态失败: %v", err)
		return err
	}

	logs.Info("[handleMerchantClose] 商家 %d 已结束营业", int64(merchantID))
	return nil
}

// 内置任务处理器 - 订单状态通知
func handleOrderNotification(task *models.ScheduledTask) error {
	logs.Info("[handleOrderNotification] 处理订单状态通知任务: %s", task.TaskID)

	// 解析任务数据
	var payload map[string]interface{}
	err := json.Unmarshal([]byte(task.Payload), &payload)
	if err != nil {
		logs.Error("[handleOrderNotification] 解析任务数据失败: %v", err)
		return err
	}

	// 获取订单ID
	orderID, ok := payload["orderId"].(float64)
	if !ok {
		logs.Error("[handleOrderNotification] 任务数据缺少订单ID或格式错误")
		return fmt.Errorf("任务数据缺少订单ID或格式错误")
	}

	// 获取通知类型
	notificationType, ok := payload["notificationType"].(string)
	if !ok {
		logs.Error("[handleOrderNotification] 任务数据缺少通知类型或格式错误")
		return fmt.Errorf("任务数据缺少通知类型或格式错误")
	}

	// TODO: 调用通知服务发送订单状态通知
	logs.Info("[handleOrderNotification] 发送订单 %d 的 %s 通知", int64(orderID), notificationType)

	return nil
}

// 内置任务处理器 - 订单清理任务
func handleOrderCleanup(task *models.ScheduledTask) error {
	logs.Info("[handleOrderCleanup] 开始执行订单清理任务: %s", task.TaskID)

	// 创建上下文
	ctx := context.Background()

	// 计算30分钟前的时间
	cutoffTime := time.Now().Add(-30 * time.Minute)
	logs.Info("[handleOrderCleanup] 清理时间点: %v", cutoffTime)

	// 初始化外卖订单服务
	takeoutOrderService := takeoutServices.NewTakeoutOrderService()

	// 清理普通订单（暂时跳过，待实现）
	ordersCleanedCount := 0

	// 清理外卖订单
	takeoutOrdersCleanedCount, err := cleanupUnpaidTakeoutOrders(ctx, takeoutOrderService, cutoffTime)
	if err != nil {
		logs.Error("[handleOrderCleanup] 清理外卖订单失败: %v", err)
		return err
	}

	logs.Info("[handleOrderCleanup] 订单清理任务完成，清理普通订单: %d 个，清理外卖订单: %d 个", 
		ordersCleanedCount, takeoutOrdersCleanedCount)

	return nil
}

// cleanupUnpaidOrders 清理超时未付款的普通订单
// TODO: 待实现 - 需要正确的订单服务接口
// func cleanupUnpaidOrders(ctx context.Context, orderService orderServices.OrderService, cutoffTime time.Time) (int, error) {
//	logs.Info("[cleanupUnpaidOrders] 开始清理普通订单，截止时间: %v", cutoffTime)
//
//	// 查询超时未付款的订单
//	// 这里需要实现一个查询超时订单的方法
//	// 由于当前的OrderService接口可能没有这个方法，我们先记录日志
//	logs.Info("[cleanupUnpaidOrders] 查询状态为待付款且创建时间早于 %v 的订单", cutoffTime)
//
//	// TODO: 实现查询超时未付款订单的逻辑
//	// 1. 查询状态为 OrderStatusPending (10) 的订单
//	// 2. 创建时间早于 cutoffTime
//	// 3. 批量取消这些订单
//
//	// 暂时返回0，表示没有清理任何订单
//	logs.Info("[cleanupUnpaidOrders] 普通订单清理功能待实现")
//	return 0, nil
// }

// cleanupUnpaidTakeoutOrders 清理超时未付款的外卖订单
func cleanupUnpaidTakeoutOrders(ctx context.Context, takeoutOrderService takeoutServices.TakeoutOrderService, cutoffTime time.Time) (int, error) {
	logs.Info("[cleanupUnpaidTakeoutOrders] 开始清理外卖订单，截止时间: %v", cutoffTime)

	// 查询超时未付款的外卖订单
	logs.Info("[cleanupUnpaidTakeoutOrders] 查询状态为待付款且创建时间早于 %v 的外卖订单", cutoffTime)

	// TODO: 实现查询超时未付款外卖订单的逻辑
	// 1. 查询状态为待付款的外卖订单
	// 2. 创建时间早于 cutoffTime
	// 3. 批量取消这些订单

	// 暂时返回0，表示没有清理任何订单
	logs.Info("[cleanupUnpaidTakeoutOrders] 外卖订单清理功能待实现")
	return 0, nil
}
