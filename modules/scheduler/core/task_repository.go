/**
 * 任务仓库访问
 *
 * 该文件提供了获取任务仓库实例的函数，用于在其他模块中统一访问任务仓库。
 */

package core

import (
	"sync"

	"o_mall_backend/modules/scheduler/repositories"
	"o_mall_backend/modules/scheduler/repositories/impl"
)

var (
	taskRepo     repositories.TaskRepository
	taskRepoOnce sync.Once
)

// GetTaskRepository 获取任务仓库实例
func GetTaskRepository() repositories.TaskRepository {
	taskRepoOnce.Do(func() {
		taskRepo = impl.NewTaskRepository()
	})
	return taskRepo
}
