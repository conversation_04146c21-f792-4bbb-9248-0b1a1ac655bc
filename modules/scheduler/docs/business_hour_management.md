# 商家营业时间管理操作指南

> 本文档提供了商家在平台上管理营业时间的详细操作示例，包括界面截图、请求参数和响应结果的说明。

## 目录

- [1. 设置营业时间](#1-设置营业时间)
  - [1.1 查看当前营业时间](#11-查看当前营业时间)
  - [1.2 添加新的营业时间段](#12-添加新的营业时间段)
  - [1.3 批量设置每周营业时间](#13-批量设置每周营业时间)
  - [1.4 临时修改营业状态](#14-临时修改营业状态)
- [2. 营业时间管理](#2-营业时间管理)
  - [2.1 删除营业时间段](#21-删除营业时间段)
  - [2.2 修改营业时间段](#22-修改营业时间段)
  - [2.3 设置节假日特殊营业时间](#23-设置节假日特殊营业时间)
- [3. 营业时间自动化](#3-营业时间自动化)
  - [3.1 自动开店和关店](#31-自动开店和关店)
  - [3.2 查看自动任务记录](#32-查看自动任务记录)

## 1. 设置营业时间

### 1.1 查看当前营业时间

#### 界面示例

```
+-------------------------------------------------------+
|                    营业时间管理                         |
+-------------------------------------------------------+
| 当前营业状态: ● 营业中                                 |
| 自动开关店: ✓ 已开启                                   |
|                                                       |
| 每周营业时间:                                          |
| +--------------------------------------------------+ |
| | 星期 | 营业时间                | 状态   | 操作      | |
| |------|--------------------------|--------|----------| |
| | 周一 | 09:00-14:00, 17:00-22:00 | 正常   | [编辑]   | |
| | 周二 | 09:00-14:00, 17:00-22:00 | 正常   | [编辑]   | |
| | 周三 | 09:00-14:00, 17:00-22:00 | 正常   | [编辑]   | |
| | 周四 | 09:00-14:00, 17:00-22:00 | 正常   | [编辑]   | |
| | 周五 | 09:00-14:00, 17:00-22:00 | 正常   | [编辑]   | |
| | 周六 | 09:00-22:00              | 正常   | [编辑]   | |
| | 周日 | 09:00-22:00              | 正常   | [编辑]   | |
| +--------------------------------------------------+ |
|                                                       |
| [+ 添加营业时间段]  [批量设置]  [导出设置]             |
+-------------------------------------------------------+
```

#### API请求示例 - 获取营业时间

```
GET /api/v1/scheduler/business-hours
Authorization: Bearer {merchant_token}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "operation_status": 1,  // 0-休息中, 1-营业中
    "auto_schedule": true,  // 是否开启自动开关店
    "business_hours": [
      {
        "key": "1_09:00_14:00", // 新增唯一标识
        "weekday": 1,  // 1-周一, 2-周二, ..., 0-周日
        "startTime": "09:00",
        "endTime": "14:00"
      },
      {
        "key": "1_17:00_22:00",
        "weekday": 1,
        "startTime": "17:00",
        "endTime": "22:00"
      },
      {
        "key": "2_09:00_14:00",
        "weekday": 2,
        "startTime": "09:00",
        "endTime": "14:00"
      },
      {
        "key": "2_17:00_22:00",
        "weekday": 2,
        "startTime": "17:00",
        "endTime": "22:00"
      },
      // ... 其他时间段省略
      {
        "key": "6_09:00_22:00",
        "weekday": 6,
        "startTime": "09:00",
        "endTime": "22:00"
      },
      {
        "key": "0_09:00_22:00",
        "weekday": 0,
        "startTime": "09:00",
        "endTime": "22:00"
      }
    ],
    "settings": {  // 新增设置信息
      "open_ahead_minutes": 15,
      "close_delay_minutes": 30,
      "notifications": true
    }
  }
}
```

### 1.2 添加新的营业时间段

#### 界面示例

```
+-------------------------------------------------------+
|                 添加营业时间段                         |
+-------------------------------------------------------+
| 星期选择:                                             |
| [✓] 周一 [✓] 周二 [✓] 周三 [✓] 周四 [✓] 周五 [ ] 周六 [ ] 周日 |
|                                                       |
| 营业时间:                                             |
| 开始时间: [09:00    ▼] 结束时间: [14:00    ▼]        |
|                                                       |
| 重复设置: [✓] 每周重复                                |
|                                                       |
| [ 保存 ]   [ 取消 ]                                  |
+-------------------------------------------------------+
```

#### API请求示例 - 添加营业时间段

```json
POST /api/v1/scheduler/business-hours
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "business_hours": [
    {
      "weekday": 1,
      "startTime": "09:00",
      "endTime": "14:00"
    },
    {
      "weekday": 2,
      "startTime": "09:00",
      "endTime": "14:00"
    },
    {
      "weekday": 3,
      "startTime": "09:00",
      "endTime": "14:00"
    },
    {
      "weekday": 4,
      "startTime": "09:00",
      "endTime": "14:00"
    },
    {
      "weekday": 5,
      "startTime": "09:00",
      "endTime": "14:00"
    }
  ]
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "scheduled_tasks": 10  // 创建的定时任务数量
  }
}
```

### 1.3 批量设置每周营业时间

#### 界面示例

```
+-------------------------------------------------------+
|                 批量设置营业时间                        |
+-------------------------------------------------------+
| 工作日 (周一至周五):                                   |
| [✓] 启用  上午: [09:00 ▼] - [14:00 ▼]                |
|           下午: [17:00 ▼] - [22:00 ▼]                |
|                                                       |
| 周末 (周六、周日):                                     |
| [✓] 启用  全天: [09:00 ▼] - [22:00 ▼]                |
|                                                       |
| 替换现有设置: [✓] 是                                   |
|                                                       |
| [ 保存设置 ]   [ 取消 ]                                |
+-------------------------------------------------------+
```

#### API请求示例 - 批量设置营业时间

```json
PUT /api/v1/scheduler/business-hours/batch
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "replace_existing": true,
  "weekday_hours": [
    {
      "startTime": "09:00",
      "endTime": "14:00"
    },
    {
      "startTime": "17:00",
      "endTime": "22:00"
    }
  ],
  "weekend_hours": [
    {
      "startTime": "09:00",
      "endTime": "22:00"
    }
  ]
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "scheduled_tasks": 14
  }
}
```

### 1.4 临时修改营业状态

#### 界面示例

```
+-------------------------------------------------------+
|                临时修改营业状态                         |
+-------------------------------------------------------+
| 当前状态: ● 营业中                                     |
|                                                       |
| 修改为:                                               |
| [○ 营业中]  [● 休息中]                                |
|                                                       |
| 自动恢复: [✓] 是                                      |
| 恢复时间: [2025-05-22 ▼] [18:00 ▼]                    |
|                                                       |
| 备注说明: [午休时间暂停营业]                           |
|                                                       |
| [ 确认修改 ]   [ 取消 ]                                |
+-------------------------------------------------------+
```

#### API请求示例 - 临时修改营业状态

```json
POST /api/v1/scheduler/business-hours/operation-status
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "status": 0,  // 0-休息中, 1-营业中
  "auto_restore": true,
  "restore_time": "2025-05-22T18:00:00+08:00",
  "remarks": "午休时间暂停营业"
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "operation_status": 0,
    "auto_restore": true,
    "restore_time": "2025-05-22T18:00:00+08:00",
    "remarks": "午休时间暂停营业"
  }
}
```

## 2. 营业时间管理

### 2.1 删除营业时间段

#### 界面示例

```
+-------------------------------------------------------+
|                 删除营业时间段                          |
+-------------------------------------------------------+
| 确认要删除以下营业时间段吗？                            |
|                                                       |
| 周一 17:00-22:00                                      |
|                                                       |
| 此操作将影响商家营业状态的自动切换。                    |
|                                                       |
| [ 确认删除 ]   [ 取消 ]                                |
+-------------------------------------------------------+
```

#### API请求示例 - 删除营业时间段

```json
DELETE /api/v1/scheduler/business-hours
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "keys": ["1_09:00_14:00", "2_09:00_14:00"],
  "filters": {
    "weekdays": [1, 2, 3]
  }
}
```

> 注意：每个营业时间段使用唯一键（key）标识，格式为`weekday_startTime_endTime`，例如`1_09:00_14:00`表示周一9点到14点的营业时间段

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "deleted": true
  }
}
```

### 2.2 修改营业时间段

#### 界面示例

```
+-------------------------------------------------------+
|                 修改营业时间段                          |
+-------------------------------------------------------+
| 原营业时间: 周一 09:00-14:00                           |
|                                                       |
| 新营业时间:                                            |
| 星期: [周一     ▼]                                    |
| 开始时间: [08:30    ▼] 结束时间: [14:30    ▼]         |
|                                                       |
| [ 保存修改 ]   [ 取消 ]                                |
+-------------------------------------------------------+
```

#### API请求示例 - 修改营业时间段

```json
PUT /api/v1/scheduler/business-hours
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "business_hours": [
    {
      "id": "1_09:00_14:00",
      "weekday": 1,
      "startTime": "08:30",
      "endTime": "14:30"
    }
  ]
}
```

> 注意：通过`id`字段指定要更新的营业时间段，格式为`weekday_startTime_endTime`

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "scheduled_tasks": 2  // 更新的定时任务数量
  }
}
```

### 2.3 设置节假日特殊营业时间

#### 界面示例

```
+-------------------------------------------------------+
|               节假日特殊营业时间                        |
+-------------------------------------------------------+
| 日期: [2025-10-01 ▼] 至 [2025-10-07 ▼]               |
|                                                       |
| 营业时间:                                             |
| [✓] 全天营业 [09:00 ▼] - [22:00 ▼]                   |
|                                                       |
| 优先级: [高     ▼]                                    |
| 备注: [国庆节期间延长营业时间]                          |
|                                                       |
| [ 保存设置 ]   [ 取消 ]                                |
+-------------------------------------------------------+
```

#### API请求示例 - 设置特殊营业时间

```json
POST /api/v1/scheduler/business-hours/special
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "start_date": "2025-10-01",
  "end_date": "2025-10-07",
  "business_hours": [
    {
      "startTime": "09:00",
      "endTime": "22:00"
    }
  ],
  "priority": "high",
  "remarks": "国庆节期间延长营业时间"
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 101,
    "start_date": "2025-10-01",
    "end_date": "2025-10-07",
    "business_hours": [
      {
        "startTime": "09:00",
        "endTime": "22:00"
      }
    ],
    "priority": "high",
    "remarks": "国庆节期间延长营业时间",
    "created_at": "2025-05-22T15:30:45+08:00"
  }
}
```

## 3. 营业时间自动化

### 3.1 自动开店和关店

#### 界面示例

```
+-------------------------------------------------------+
|                 自动开关店设置                          |
+-------------------------------------------------------+
| 自动开关店: [✓] 启用                                   |
|                                                       |
| 提前开店时间: [15 ▼] 分钟                              |
| 延迟关店时间: [30 ▼] 分钟                              |
|                                                       |
| 通知设置:                                              |
| [✓] 开店前通知     提前 [30 ▼] 分钟                    |
| [✓] 关店前通知     提前 [30 ▼] 分钟                    |
| [✓] 营业状态变更通知                                   |
|                                                       |
| 通知方式:                                              |
| [✓] 短信  [✓] 应用内消息  [✓] 邮件                     |
|                                                       |
| [ 保存设置 ]   [ 取消 ]                                |
+-------------------------------------------------------+
```

#### API请求示例 - 设置自动开关店

```json
POST /api/v1/scheduler/auto-schedule
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "enabled": true,
  "open_ahead_minutes": 15,
  "close_delay_minutes": 30,
  "notifications": {
    "open_notification": {
      "enabled": true,
      "ahead_minutes": 30
    },
    "close_notification": {
      "enabled": true,
      "ahead_minutes": 30
    },
    "status_change_notification": true,
    "notification_methods": ["sms", "app", "email"]
  }
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "enabled": true,
    "open_ahead_minutes": 15,
    "close_delay_minutes": 30,
    "notifications": {
      "open_notification": {
        "enabled": true,
        "ahead_minutes": 30
      },
      "close_notification": {
        "enabled": true,
        "ahead_minutes": 30
      },
      "status_change_notification": true,
      "notification_methods": ["sms", "app", "email"]
    },
    "updated_at": "2025-05-22T15:35:20+08:00"
  }
}
```

#### 获取自动开关店设置API

```
GET /api/v1/scheduler/auto-schedule
Authorization: Bearer {merchant_token}
```

#### 获取自动开关店设置响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "enabled": true,
    "open_ahead_minutes": 15,
    "close_delay_minutes": 30,
    "notifications": {
      "open_notification": {
        "enabled": true,
        "ahead_minutes": 30
      },
      "close_notification": {
        "enabled": true,
        "ahead_minutes": 30
      },
      "status_change_notification": true,
      "notification_methods": ["sms", "app", "email"]
    },
    "last_updated": "2025-05-23T10:30:00+08:00"
  }
}
```

### 3.2 查看自动任务记录

#### 界面示例

```
+-------------------------------------------------------+
|                 自动任务执行记录                        |
+-------------------------------------------------------+
| 时间范围: [2025-05-15 ▼] 至 [2025-05-22 ▼] [查询]     |
|                                                       |
| +--------------------------------------------------+ |
| | 时间             | 任务类型 | 状态   | 详情          | |
| |------------------|----------|--------|---------------| |
| | 2025-05-22 09:00 | 开店    | 成功   | [查看]        | |
| | 2025-05-21 22:00 | 关店    | 成功   | [查看]        | |
| | 2025-05-21 09:00 | 开店    | 成功   | [查看]        | |
| | 2025-05-20 22:00 | 关店    | 成功   | [查看]        | |
| | 2025-05-20 09:00 | 开店    | 成功   | [查看]        | |
| +--------------------------------------------------+ |
|                                                       |
| 共 15 条记录，当前第 1/3 页                            |
+-------------------------------------------------------+
```

#### API请求示例 - 查询任务记录

```
GET /api/v1/scheduler/tasks?type=business_hour&start_date=2025-05-15&end_date=2025-05-22&page=1&page_size=5
Authorization: Bearer {merchant_token}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": "merchant_open_1001_1_09:00",
        "task_type": "merchant_open",
        "execute_time": "2025-05-22T09:00:00+08:00",
        "status": "completed",
        "result": "商家已开始营业",
        "created_at": "2025-05-15T10:30:00+08:00"
      },
      {
        "id": "merchant_close_1001_1_22:00",
        "task_type": "merchant_close",
        "execute_time": "2025-05-21T22:00:00+08:00",
        "status": "completed",
        "result": "商家已结束营业",
        "created_at": "2025-05-15T10:30:00+08:00"
      },
      {
        "id": "merchant_open_1001_1_09:00",
        "task_type": "merchant_open",
        "execute_time": "2025-05-21T09:00:00+08:00",
        "status": "completed",
        "result": "商家已开始营业",
        "created_at": "2025-05-15T10:30:00+08:00"
      },
      {
        "id": "merchant_close_1001_1_22:00",
        "task_type": "merchant_close",
        "execute_time": "2025-05-20T22:00:00+08:00",
        "status": "completed",
        "result": "商家已结束营业",
        "created_at": "2025-05-15T10:30:00+08:00"
      },
      {
        "id": "merchant_open_1001_1_09:00",
        "task_type": "merchant_open",
        "execute_time": "2025-05-20T09:00:00+08:00",
        "status": "completed",
        "result": "商家已开始营业",
        "created_at": "2025-05-15T10:30:00+08:00"
      }
    ],
    "total": 15,
    "page": 1,
    "page_size": 5
  }
}
```

## 4. 营业时间统计分析

### 4.1 营业时长统计

#### 界面示例

```
+-------------------------------------------------------+
|                 营业时长统计分析                        |
+-------------------------------------------------------+
| 时间范围: [近7天 ▼] [2025-05-15 - 2025-05-22]          |
|                                                       |
| 总营业时长: 94小时30分钟                               |
| 日均营业时长: 13小时30分钟                             |
|                                                       |
| 每日营业时长:                                          |
| +--------------------------------------------------+ |
| | 日期      | 营业时长   | 开店次数 | 关店次数 |   | |
| |-----------|------------|----------|----------|---| |
| | 2025-05-22| 10h        | 1        | 0        |▮▮▮| |
| | 2025-05-21| 13h        | 1        | 1        |▮▮▮| |
| | 2025-05-20| 13h        | 1        | 1        |▮▮▮| |
| | 2025-05-19| 13h        | 1        | 1        |▮▮▮| |
| | 2025-05-18| 13h        | 1        | 1        |▮▮▮| |
| | 2025-05-17| 13h        | 1        | 1        |▮▮▮| |
| | 2025-05-16| 13h        | 1        | 1        |▮▮▮| |
| | 2025-05-15| 6h30m      | 1        | 1        |▮▮ | |
| +--------------------------------------------------+ |
|                                                       |
| [ 导出数据 ]                                          |
+-------------------------------------------------------+
```

#### API请求示例 - 获取营业时长统计

```
GET /api/v1/operation-stats?period=7d
Authorization: Bearer {merchant_token}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "start_date": "2025-05-15",
    "end_date": "2025-05-22",
    "total_operation_hours": 94.5,
    "average_operation_hours": 13.5,
    "daily_stats": [
      {
        "date": "2025-05-22",
        "operation_hours": 10.0,
        "open_count": 1,
        "close_count": 0
      },
      {
        "date": "2025-05-21",
        "operation_hours": 13.0,
        "open_count": 1,
        "close_count": 1
      },
      // ... 其他日期数据省略
    ]
  }
}
```
