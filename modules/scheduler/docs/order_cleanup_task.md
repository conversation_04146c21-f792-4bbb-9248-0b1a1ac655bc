# 订单清理任务设计文档

## 概述

订单清理任务是scheduler模块的一个重要功能，用于定时清理超过30分钟未付款的订单，包括普通订单和外卖订单。该功能通过定时任务调度器实现自动化的订单状态管理。

## 功能特性

### 1. 自动清理机制
- **清理周期**: 每30分钟执行一次清理任务
- **清理条件**: 订单创建时间超过30分钟且状态为待付款
- **清理范围**: 普通订单和外卖订单
- **清理操作**: 将符合条件的订单状态更新为已取消

### 2. 任务类型
- **一次性任务**: 立即执行的清理任务，用于测试或手动触发
- **周期性任务**: 按照Cron表达式定期执行的清理任务

### 3. 支持的订单类型
- **普通订单**: 通过order模块管理的标准商品订单
- **外卖订单**: 通过takeout模块管理的外卖配送订单

## 技术架构

### 1. 模块结构
```
scheduler/
├── models/
│   └── scheduled_task.go          # 任务模型，新增TaskTypeOrderCleanup
├── core/
│   └── scheduler.go               # 调度器核心，新增handleOrderCleanup处理器
├── services/
│   └── order_cleanup_service.go   # 订单清理服务
├── controllers/
│   └── order_cleanup_controller.go # 订单清理API控制器
└── docs/
    └── order_cleanup_task.md      # 本文档
```

### 2. 核心组件

#### 2.1 任务处理器 (handleOrderCleanup)
- 位置: `core/scheduler.go`
- 功能: 执行订单清理逻辑
- 流程:
  1. 计算30分钟前的时间点
  2. 调用普通订单清理函数
  3. 调用外卖订单清理函数
  4. 记录清理结果

#### 2.2 订单清理服务 (OrderCleanupService)
- 位置: `services/order_cleanup_service.go`
- 功能: 管理订单清理任务的生命周期
- 方法:
  - `CreateOrderCleanupTask()`: 创建一次性清理任务
  - `StartOrderCleanupScheduler()`: 启动周期性清理调度器
  - `StopOrderCleanupScheduler()`: 停止清理调度器

#### 2.3 API控制器 (OrderCleanupController)
- 位置: `controllers/order_cleanup_controller.go`
- 功能: 提供HTTP API接口
- 端点:
  - `POST /api/v1/scheduler/order-cleanup/create`: 创建清理任务
  - `POST /api/v1/scheduler/order-cleanup/start`: 启动调度器
  - `POST /api/v1/scheduler/order-cleanup/stop`: 停止调度器
  - `GET /api/v1/scheduler/order-cleanup/status`: 查询状态
  - `POST /api/v1/scheduler/order-cleanup/test`: 测试功能

## 配置参数

### 1. 时间配置
- **超时时间**: 30分钟 (可在代码中调整)
- **执行周期**: 每30分钟 (Cron: `0 */30 * * * *`)
- **测试任务延迟**: 1分钟 (用于测试API)

### 2. 任务配置
- **任务类型**: `order_cleanup`
- **业务ID**: 0 (表示全局任务)
- **业务类型**: `system`
- **最大重试次数**: 3次

## 使用方法

### 1. 自动启动
订单清理调度器会在scheduler模块初始化时自动启动，无需手动配置。

### 2. 手动管理

#### 2.1 启动调度器
```bash
curl -X POST http://localhost:8080/api/v1/scheduler/order-cleanup/start
```

#### 2.2 停止调度器
```bash
curl -X POST http://localhost:8080/api/v1/scheduler/order-cleanup/stop
```

#### 2.3 创建一次性任务
```bash
curl -X POST http://localhost:8080/api/v1/scheduler/order-cleanup/create
```

#### 2.4 测试功能
```bash
curl -X POST http://localhost:8080/api/v1/scheduler/order-cleanup/test
```

#### 2.5 查询状态
```bash
curl -X GET http://localhost:8080/api/v1/scheduler/order-cleanup/status
```

## 实现细节

### 1. 订单查询逻辑

#### 1.1 普通订单
- 查询条件:
  - `status = 10` (待付款状态)
  - `created_at < NOW() - INTERVAL 30 MINUTE`
- 更新操作:
  - `status = 60` (已取消状态)
  - `cancel_reason = "超时未付款自动取消"`
  - `cancel_time = NOW()`

#### 1.2 外卖订单
- 查询条件:
  - `status = 10` (待付款状态)
  - `order_type = 1` (外卖订单类型)
  - `created_at < NOW() - INTERVAL 30 MINUTE`
- 更新操作:
  - `status = 60` (已取消状态)
  - `cancel_reason = "超时未付款自动取消"`
  - `cancel_time = NOW()`

### 2. 错误处理
- 任务执行失败时会记录错误日志
- 支持最多3次重试
- 失败的任务不会影响调度器的正常运行

### 3. 性能考虑
- 使用批量查询和更新操作
- 避免在高峰期执行大量清理操作
- 记录清理统计信息用于监控

## 监控和日志

### 1. 日志记录
- 任务开始和结束时间
- 清理的订单数量
- 错误信息和重试次数
- 性能统计信息

### 2. 监控指标
- 清理任务执行频率
- 每次清理的订单数量
- 任务执行耗时
- 失败率和重试率

## 扩展性

### 1. 支持更多订单类型
可以通过扩展清理函数来支持其他类型的订单，如:
- 团购订单
- 秒杀订单
- 预约订单

### 2. 可配置的清理策略
未来可以支持:
- 不同订单类型的不同超时时间
- 基于商家配置的个性化清理策略
- 节假日和特殊时期的清理策略调整

### 3. 通知机制
可以集成通知服务:
- 向用户发送订单取消通知
- 向商家发送订单状态变更通知
- 向管理员发送清理统计报告

## 注意事项

1. **数据一致性**: 确保订单状态更新的原子性
2. **性能影响**: 避免在业务高峰期执行大量清理操作
3. **用户体验**: 合理设置超时时间，避免过早取消订单
4. **监控告警**: 建立完善的监控和告警机制
5. **数据备份**: 在执行清理操作前确保数据已备份

## 版本历史

- **v1.0.0**: 初始版本，支持基本的订单清理功能
- 计划中的功能:
  - 支持更多订单类型
  - 可配置的清理策略
  - 完善的监控和告警
  - 用户通知机制