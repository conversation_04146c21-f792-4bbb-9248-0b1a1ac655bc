# Scheduler模块设计与实现文档

## 1. 模块概述

Scheduler模块是一个通用的定时任务调度系统，负责管理、调度和执行各种定时任务。该模块采用事件驱动机制，使用优先级队列管理任务，能够高效地处理大量定时任务，同时支持任务的持久化存储和恢复。

### 1.1 主要功能

- 定时任务的创建、更新、删除和查询
- 支持一次性任务和周期性任务
- 任务优先级管理和调度
- 任务执行状态跟踪
- 任务失败重试机制
- 任务持久化和系统重启后恢复
- 商家营业时间管理

### 1.2 适用场景

- 商家自动开店/关店
- 订单状态通知
- 促销活动定时开始/结束
- 其他需要在特定时间执行的业务逻辑

## 2. 系统架构

### 2.1 模块结构

Scheduler模块采用分层架构设计，主要包含以下几个部分：

- **核心层 (core)**: 包含调度器、优先级队列和核心业务逻辑
- **模型层 (models)**: 定义数据模型和业务实体
- **仓库层 (repositories)**: 负责数据持久化和访问
- **服务层 (services)**: 提供业务服务和对外接口
- **控制器层 (controllers)**: 处理API请求和响应
- **路由层 (routers)**: 定义API路由
- **工具层 (utils)**: 提供通用工具函数

### 2.2 目录结构

```
modules/scheduler/
├── controllers/          # 控制器目录，处理API请求
├── core/                 # 核心组件目录
│   ├── merchant_service.go  # 商家服务
│   ├── priority_queue.go    # 任务优先级队列
│   ├── scheduler.go         # 调度器实现
│   ├── start_scheduler.go   # 启动调度器
│   └── task_repository.go   # 任务仓库访问
├── docs/                 # 文档目录
├── dto/                  # 数据传输对象
├── init.go               # 模块初始化文件
├── models/               # 数据模型目录
│   ├── business_hour.go     # 商家营业时间模型
│   └── scheduled_task.go    # 定时任务模型
├── repositories/         # 仓库目录
│   ├── impl/                # 仓库实现
│   │   └── task_repository_impl.go  # 任务仓库实现
│   └── task_repository.go   # 任务仓库接口
├── routers/              # 路由目录
├── services/             # 服务目录
└── utils/                # 工具目录
```

## 3. 核心组件

### 3.1 调度器 (Scheduler)

调度器是整个模块的核心组件，负责任务的调度和执行。它使用优先级队列管理任务，采用事件驱动机制，减少系统资源消耗。

#### 3.1.1 主要职责

- 管理任务生命周期
- 任务优先级调度
- 任务执行
- 任务失败重试
- 周期性任务调度

#### 3.1.2 工作流程

1. 系统启动时，调度器初始化并从数据库加载所有未完成的任务
2. 调度器将任务添加到优先级队列中
3. 调度器定期检查队列中是否有到期的任务
4. 对于到期的任务，调度器将其分发给相应的任务处理器执行
5. 任务执行完成后，调度器更新任务状态
6. 对于失败的任务，如果未超过最大重试次数，调度器会安排重试
7. 对于周期性任务，调度器会计算下次执行时间并创建新的任务实例

### 3.2 优先级队列 (TaskPriorityQueue)

优先级队列基于Go语言的堆实现，用于按照任务执行时间排序，确保最早需要执行的任务能够被优先处理。

#### 3.2.1 实现原理

- 实现了Go标准库的`heap.Interface`接口
- 使用任务的目标执行时间作为优先级依据
- 时间越早的任务优先级越高

### 3.3 任务仓库 (TaskRepository)

任务仓库负责任务的持久化存储和访问，提供了一系列方法来创建、查询、更新和删除任务。

#### 3.3.1 主要功能

- 保存和更新任务
- 根据ID或任务标识符查询任务
- 获取指定时间前需要执行的任务
- 获取特定业务相关的任务
- 更新任务状态
- 批量操作任务

## 4. 数据模型

### 4.1 定时任务模型 (ScheduledTask)

定时任务是调度系统的核心数据实体，包含了任务的所有信息。

#### 4.1.1 主要属性

| 属性名 | 类型 | 描述 |
| ----- | --- | ---- |
| ID | int64 | 任务ID |
| TaskID | string | 任务唯一标识符 |
| TaskType | string | 任务类型 |
| TargetTime | time.Time | 目标执行时间 |
| Status | int | 任务状态 |
| BusinessID | int64 | 关联业务ID |
| BusinessType | string | 关联业务类型 |
| Payload | string | 任务负载数据 |
| RetryCount | int | 重试次数 |
| MaxRetries | int | 最大重试次数 |
| LastExecutedAt | time.Time | 上次执行时间 |
| ResultMessage | string | 执行结果消息 |
| Recurrent | bool | 是否周期性任务 |
| CronExpression | string | Cron表达式 |
| NextRunTime | time.Time | 下次运行时间 |

#### 4.1.2 任务状态

- `TaskStatusPending (0)`: 等待执行
- `TaskStatusRunning (1)`: 执行中
- `TaskStatusCompleted (2)`: 执行完成
- `TaskStatusFailed (3)`: 执行失败
- `TaskStatusCancelled (4)`: 已取消

#### 4.1.3 任务类型

- `TaskTypeMerchantOpen`: 商家开始营业
- `TaskTypeMerchantClose`: 商家结束营业
- `TaskTypeOrderNotification`: 订单状态通知
- `TaskTypePromotionStart`: 促销活动开始
- `TaskTypePromotionEnd`: 促销活动结束

### 4.2 商家营业时间模型 (BusinessHour)

用于定义商家的营业时间，支持按周几和具体时间段设置。

#### 4.2.1 主要属性

| 属性名 | 类型 | 描述 |
| ----- | --- | ---- |
| ID | int64 | 营业时间ID |
| Weekday | int | 星期几 (0-周日, 1-周一, ...) |
| StartTime | string | 开始营业时间 (HH:MM) |
| EndTime | string | 结束营业时间 (HH:MM) |

## 5. 数据流向与处理流程

### 5.1 初始化流程

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│  应用启动   │──────▶│  初始化模块  │──────▶│ 注册ORM模型 │
└─────────────┘       └─────────────┘       └─────────────┘
                                                    │
                                                    ▼
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│ 系统就绪    │◀─────│ 加载持久化任务│◀─────│ 启动调度器   │
└─────────────┘       └─────────────┘       └─────────────┘
```

1. 应用启动时，调用`scheduler.Init()`初始化模块
2. 注册ORM模型，包括`ScheduledTask`和`BusinessHour`
3. 调用`core.StartScheduler()`启动调度器
4. 调度器从数据库加载所有未完成的任务
5. 系统准备就绪，可以接收和处理任务

### 5.2 任务创建流程

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│  业务请求   │──────▶│  创建任务   │──────▶│ 设置任务属性 │
└─────────────┘       └─────────────┘       └─────────────┘
                                                    │
                                                    ▼
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│ 通知调度器  │◀─────│  保存到数据库 │◀─────│ 设置执行时间 │
└─────────────┘       └─────────────┘       └─────────────┘
```

1. 业务模块调用服务层方法创建任务
2. 设置任务的属性，包括任务类型、关联业务ID等
3. 根据任务类型和规则计算任务的执行时间
4. 将任务保存到数据库
5. 通知调度器有新任务需要处理

### 5.3 任务调度与执行流程

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│ 调度器循环  │──────▶│ 检查任务队列 │──────▶│ 发现到期任务 │
└─────────────┘       └─────────────┘       └─────────────┘
                                                    │
                                                    ▼
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│ 更新任务状态 │◀─────│  执行任务   │◀─────│ 提交到执行器 │
└─────────────┘       └─────────────┘       └─────────────┘
        │
        ▼
┌─────────────────────┐       ┌─────────────┐       ┌─────────────┐
│ 计算下次执行时间    │──────▶│ 创建新任务  │──────▶│ 加入任务队列 │
│ (周期性任务)        │       │ (周期性任务) │       │ (周期性任务) │
└─────────────────────┘       └─────────────┘       └─────────────┘
```

1. 调度器循环定期检查任务队列
2. 如果发现有到期的任务，将其从队列中取出
3. 将任务提交到执行器（工作池）
4. 执行相应的任务处理器
5. 更新任务状态（完成、失败或重试）
6. 对于周期性任务，计算下次执行时间并创建新任务

### 5.4 任务重试流程

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│  任务执行   │──────▶│  执行失败   │──────▶│ 检查重试次数 │
└─────────────┘       └─────────────┘       └─────────────┘
                                                    │
                                                    ▼
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│ 加入任务队列 │◀─────│ 计算重试时间 │◀─────│ 未超过最大次数 │
└─────────────┘       └─────────────┘       └─────────────┘
                                                    │
                                                    ▼
                                            ┌─────────────┐
                                            │ 标记为失败  │
                                            └─────────────┘
```

1. 任务执行失败
2. 检查当前重试次数是否已超过最大重试次数
3. 如果未超过最大重试次数，计算下次重试时间
4. 将任务重新加入队列，等待下次执行
5. 如果已超过最大重试次数，标记任务为永久失败

## 6. 核心代码解析

### 6.1 调度器初始化与启动

```go
// GetScheduler 获取调度器单例
func GetScheduler() *Scheduler {
    once.Do(func() {
        scheduler = &Scheduler{
            taskRepo:           impl.NewTaskRepository(),
            taskQueue:          &TaskPriorityQueue{},
            taskHandlers:       make(map[string]TaskHandler),
            cronParser:         cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow),
            triggerChan:        make(chan struct{}, 1),
            stopChan:           make(chan struct{}),
            workerPool:         make(chan struct{}, 10),
            maxConcurrentTasks: 10,
        }
        heap.Init(scheduler.taskQueue)
        
        // 注册内置任务处理器
        scheduler.RegisterTaskHandler(models.TaskTypeMerchantOpen, handleMerchantOpen)
        scheduler.RegisterTaskHandler(models.TaskTypeMerchantClose, handleMerchantClose)
        scheduler.RegisterTaskHandler(models.TaskTypeOrderNotification, handleOrderNotification)
    })
    return scheduler
}

// Start 启动调度器
func (s *Scheduler) Start() {
    logs.Info("[Scheduler.Start] 启动定时任务调度器")
    
    // 加载持久化的任务
    s.loadTasks()
    
    // 启动调度循环
    go s.scheduleLoop()
    
    logs.Info("[Scheduler.Start] 定时任务调度器已启动")
}
```

### 6.2 任务调度循环

```go
// 调度循环
func (s *Scheduler) scheduleLoop() {
    logs.Info("[Scheduler.scheduleLoop] 启动调度循环")
    
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-s.stopChan:
            // 收到停止信号，退出循环
            logs.Info("[Scheduler.scheduleLoop] 收到停止信号，退出调度循环")
            return
            
        case <-ticker.C:
            // 定时检查有没有到期的任务
            s.checkAndExecuteTasks()
            
        case <-s.triggerChan:
            // 收到新任务通知，重新加载任务
            logs.Info("[Scheduler.scheduleLoop] 收到新任务通知，重新加载任务")
            s.mutex.Lock()
            
            // 清空当前队列
            s.taskQueue = &TaskPriorityQueue{}
            heap.Init(s.taskQueue)
            
            s.mutex.Unlock()
            
            // 重新加载任务
            s.loadTasks()
            
            // 立即检查是否有可执行的任务
            s.checkAndExecuteTasks()
        }
    }
}
```

### 6.3 优先级队列实现

```go
// TaskPriorityQueue 任务优先级队列，实现了heap.Interface接口
type TaskPriorityQueue []*models.ScheduledTask

// Len 返回队列长度
func (pq TaskPriorityQueue) Len() int {
    return len(pq)
}

// Less 比较两个任务的优先级，时间早的任务优先级更高
func (pq TaskPriorityQueue) Less(i, j int) bool {
    return pq[i].TargetTime.Before(pq[j].TargetTime)
}

// Swap 交换队列中的两个元素
func (pq TaskPriorityQueue) Swap(i, j int) {
    pq[i], pq[j] = pq[j], pq[i]
}

// Push 将元素添加到队列
func (pq *TaskPriorityQueue) Push(x interface{}) {
    task := x.(*models.ScheduledTask)
    *pq = append(*pq, task)
}

// Pop 从队列中弹出优先级最高的元素
func (pq *TaskPriorityQueue) Pop() interface{} {
    old := *pq
    n := len(old)
    task := old[n-1]
    *pq = old[0 : n-1]
    return task
}
```

## 7. 扩展与优化建议

### 7.1 功能扩展

1. **分布式支持**: 实现分布式调度器，支持多节点部署
2. **任务依赖关系**: 支持任务之间的依赖关系
3. **任务优先级**: 在时间优先级基础上增加业务优先级
4. **更多周期类型**: 增加更复杂的周期设置，如工作日/节假日
5. **任务组**: 支持任务分组和批量操作
6. **任务参数化**: 支持在运行时传入参数

### 7.2 性能优化

1. **内存占用优化**: 优化任务队列的内存占用
2. **任务分片**: 支持大型任务的分片执行
3. **动态工作池**: 根据系统负载动态调整工作池大小
4. **预热机制**: 提前加载即将执行的任务到内存
5. **批量持久化**: 批量保存任务状态更新

### 7.3 可用性增强

1. **任务监控**: 实现任务执行监控和告警
2. **任务日志**: 详细记录任务执行日志
3. **任务审计**: 记录任务的变更历史
4. **手动触发**: 支持手动触发任务执行
5. **暂停/恢复**: 支持暂停和恢复整个调度系统

## 8. 总结

Scheduler模块是一个灵活、高效的定时任务调度系统，采用优先级队列和事件驱动机制，支持一次性任务和周期性任务的管理和执行。该模块通过分层架构设计，将调度逻辑、业务逻辑和数据访问分离，提高了系统的可维护性和可扩展性。

通过本文档的介绍，您应该已经了解了Scheduler模块的设计思想、架构组成、数据流向和处理流程。在实际使用过程中，您可以根据业务需求，定制和扩展该模块的功能。
