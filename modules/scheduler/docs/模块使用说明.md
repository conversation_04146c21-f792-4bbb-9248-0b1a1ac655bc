# 定时执行模块使用说明

## 模块概述

定时执行模块是一个独立的服务组件，用于管理和执行系统中的各类定时任务。该模块采用事件驱动机制，使用优先级队列进行任务调度，支持一次性任务和周期性任务，并提供完善的任务管理API。

主要功能包括：
- 任务的创建、查询、更新和取消
- 支持按业务类型和ID管理任务
- 提供任务执行结果查询
- 支持自动重试机制
- 商家营业时间自动调度功能

## 架构设计

定时执行模块采用分层设计：

1. **控制器层(Controllers)**：提供RESTful API接口，处理HTTP请求
2. **服务层(Services)**：实现业务逻辑，如任务创建、更新等
3. **仓库层(Repositories)**：负责数据持久化和查询
4. **模型层(Models)**：定义数据结构
5. **核心层(Core)**：实现任务调度和执行逻辑

## 数据模型

### ScheduledTask (定时任务)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| ID | int64 | 任务ID |
| TaskID | string | 任务唯一标识符 |
| TaskType | string | 任务类型 |
| TargetTime | time.Time | 目标执行时间 |
| Status | int | 任务状态(0-待执行,1-执行中,2-已完成,3-失败,4-已取消) |
| BusinessID | int64 | 关联业务ID |
| BusinessType | string | 关联业务类型 |
| Payload | string | 任务负载数据(JSON格式) |
| RetryCount | int | 重试次数 |
| MaxRetries | int | 最大重试次数 |
| LastExecutedAt | time.Time | 上次执行时间 |
| ResultMessage | string | 执行结果消息 |
| CreatedAt | time.Time | 创建时间 |
| UpdatedAt | time.Time | 更新时间 |
| Recurrent | bool | 是否周期性任务 |
| CronExpression | string | Cron表达式(周期性任务) |
| NextRunTime | time.Time | 下次运行时间(周期性任务) |

## API接口

### 创建定时任务

- **URL**: `/api/v1/scheduler/tasks`
- **方法**: `POST`
- **描述**: 创建一个新的定时任务
- **请求参数**:

```json
{
  "task_type": "merchant_open",
  "target_time": "2025-06-01T08:00:00+08:00",
  "business_id": 1001,
  "business_type": "merchant",
  "payload": "{\"merchantId\":1001,\"operation\":\"open\"}",
  "recurrent": false,
  "cron_expr": ""
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "task_id": "merchant_open_1001_1621324564"
  }
}
```

### 创建周期性任务

- **URL**: `/api/v1/scheduler/tasks`
- **方法**: `POST`
- **描述**: 创建一个周期性定时任务
- **请求参数**:

```json
{
  "task_type": "merchant_open",
  "business_id": 1001,
  "business_type": "merchant",
  "payload": "{\"merchantId\":1001,\"operation\":\"open\"}",
  "recurrent": true,
  "cron_expr": "0 0 8 * * 1-5"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "task_id": "merchant_open_1001_recurrent_1621324564"
  }
}
```

### 获取任务详情

- **URL**: `/api/v1/scheduler/tasks/:taskId`
- **方法**: `GET`
- **描述**: 获取指定任务ID的详细信息
- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "task_id": "merchant_open_1001_1621324564",
    "task_type": "merchant_open",
    "target_time": "2025-06-01T08:00:00+08:00",
    "status": 0,
    "business_id": 1001,
    "business_type": "merchant",
    "payload": "{\"merchantId\":1001,\"operation\":\"open\"}",
    "retry_count": 0,
    "max_retries": 3,
    "created_at": "2025-05-22T11:30:00+08:00",
    "updated_at": "2025-05-22T11:30:00+08:00"
  }
}
```

### 获取业务相关任务

- **URL**: `/api/v1/scheduler/business/:businessId/tasks?businessType=merchant`
- **方法**: `GET`
- **描述**: 获取指定业务ID和类型的所有任务
- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "task_id": "merchant_open_1001_1621324564",
      "task_type": "merchant_open",
      "target_time": "2025-06-01T08:00:00+08:00",
      "status": 0,
      "business_id": 1001,
      "business_type": "merchant",
      "payload": "{\"merchantId\":1001,\"operation\":\"open\"}",
      "retry_count": 0,
      "max_retries": 3,
      "created_at": "2025-05-22T11:30:00+08:00",
      "updated_at": "2025-05-22T11:30:00+08:00"
    },
    {
      "id": 2,
      "task_id": "merchant_close_1001_1621324565",
      "task_type": "merchant_close",
      "target_time": "2025-06-01T18:00:00+08:00",
      "status": 0,
      "business_id": 1001,
      "business_type": "merchant",
      "payload": "{\"merchantId\":1001,\"operation\":\"close\"}",
      "retry_count": 0,
      "max_retries": 3,
      "created_at": "2025-05-22T11:30:00+08:00",
      "updated_at": "2025-05-22T11:30:00+08:00"
    }
  ]
}
```

### 更新任务执行时间

- **URL**: `/api/v1/scheduler/tasks/:taskId/time`
- **方法**: `PUT`
- **描述**: 更新指定任务的执行时间
- **请求参数**:

```json
{
  "target_time": "2025-06-01T09:00:00+08:00"
}
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 取消任务

- **URL**: `/api/v1/scheduler/tasks/:taskId/cancel`
- **方法**: `PUT`
- **描述**: 取消指定的任务
- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 手动执行任务

- **URL**: `/api/v1/scheduler/tasks/:taskId/execute`
- **方法**: `PUT`
- **描述**: 立即执行指定的任务
- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

### 更新商家营业时间任务

- **URL**: `/api/v1/merchant/scheduler/:merchantId/business-time`
- **认证**: 需要有效的JWT令牌和商家权限
- **方法**: `PUT`
- **描述**: 根据商家的营业时间设置创建或更新相应的定时任务
- **请求参数**:

```json
[
  {
    "weekday": 1,
    "startTime": "08:00",
    "endTime": "18:00"
  },
  {
    "weekday": 2,
    "startTime": "08:00",
    "endTime": "18:00"
  },
  {
    "weekday": 3,
    "startTime": "08:00",
    "endTime": "18:00"
  },
  {
    "weekday": 4,
    "startTime": "08:00",
    "endTime": "18:00"
  },
  {
    "weekday": 5,
    "startTime": "08:00",
    "endTime": "18:00"
  }
]
```

- **响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 使用示例

### 商家模块集成

在商家模块中，当商家更新营业时间时，可以调用定时执行模块的API来更新相应的定时任务：

```go
// 商家控制器中更新营业时间的方法
func (c *MerchantController) UpdateBusinessHours() {
    // 获取商家ID
    merchantID, _ := c.GetInt64(":id")
    
    // 解析营业时间
    var businessHours []BusinessHour
    if err := json.Unmarshal(c.Ctx.Input.RequestBody, &businessHours); err != nil {
        c.Data["json"] = result.Error(400, "无效的营业时间格式")
        c.ServeJSON()
        return
    }
    
    // 更新商家的营业时间
    merchant, err := c.merchantService.GetByID(merchantID)
    if err != nil {
        c.Data["json"] = result.Error(500, "获取商家信息失败")
        c.ServeJSON()
        return
    }
    
    if merchant == nil {
        c.Data["json"] = result.Error(404, "商家不存在")
        c.ServeJSON()
        return
    }
    
    // 保存营业时间到商家记录
    err = merchant.SetBusinessHours(businessHours)
    if err != nil {
        c.Data["json"] = result.Error(500, "设置营业时间失败")
        c.ServeJSON()
        return
    }
    
    err = c.merchantService.Update(merchant)
    if err != nil {
        c.Data["json"] = result.Error(500, "更新商家信息失败")
        c.ServeJSON()
        return
    }
    
    // 调用定时执行模块API更新商家营业时间任务
    schedulerURL := fmt.Sprintf("/api/v1/merchant/scheduler/%d/business-time", merchantID)
    resp, err := http.DefaultClient.Post(
        schedulerURL,
        "application/json",
        bytes.NewBuffer(c.Ctx.Input.RequestBody),
    )
    if err != nil {
        // 记录错误但不影响主流程
        logs.Error("更新商家营业时间任务失败: %v", err)
    }
    
    if resp != nil && resp.Body != nil {
        defer resp.Body.Close()
    }
    
    c.Data["json"] = result.Success(nil)
    c.ServeJSON()
}
```

### 订单模块集成

在订单模块中，可以创建定时任务来定时发送订单状态提醒：

```go
// 创建订单状态提醒任务
func createOrderNotificationTask(orderID int64, notificationType string, delayMinutes int) error {
    // 构建任务负载
    payload, _ := json.Marshal(map[string]interface{}{
        "orderId":          orderID,
        "notificationType": notificationType,
    })
    
    // 设置目标执行时间
    targetTime := time.Now().Add(time.Minute * time.Duration(delayMinutes))
    
    // 构建请求参数
    requestBody, _ := json.Marshal(map[string]interface{}{
        "task_type":     "order_notification",
        "target_time":   targetTime,
        "business_id":   orderID,
        "business_type": "order",
        "payload":       string(payload),
        "recurrent":     false,
    })
    
    // 调用定时执行模块API创建任务
    resp, err := http.DefaultClient.Post(
        "/api/v1/scheduler/tasks",
        "application/json",
        bytes.NewBuffer(requestBody),
    )
    if err != nil {
        return err
    }
    
    defer resp.Body.Close()
    
    // 解析响应
    var result struct {
        Code    int    `json:"code"`
        Message string `json:"message"`
        Data    struct {
            TaskID string `json:"task_id"`
        } `json:"data"`
    }
    
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return err
    }
    
    if result.Code != 200 {
        return fmt.Errorf("创建任务失败: %s", result.Message)
    }
    
    return nil
}
```

## 任务类型说明

系统预定义了以下任务类型：

| 任务类型 | 描述 | 参数说明 |
|--------|------|---------|
| merchant_open | 商家开始营业 | merchantId: 商家ID |
| merchant_close | 商家结束营业 | merchantId: 商家ID |
| order_notification | 订单状态通知 | orderId: 订单ID, notificationType: 通知类型 |
| promotion_start | 促销活动开始 | promotionId: 活动ID |
| promotion_end | 促销活动结束 | promotionId: 活动ID |

## 注意事项

1. 任务的Payload字段必须是有效的JSON字符串
2. 时间参数应使用ISO 8601格式，并指定时区
3. 周期性任务的Cron表达式遵循标准格式: 秒 分 时 日 月 星期
4. 商家营业时间的星期几参数为: 0-周日，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六
5. 商家营业时间的时间格式为24小时制: HH:MM

## 中间件

商家相关的定时任务接口使用了以下中间件：

1. **JWTMiddleware**: 验证JWT令牌
2. **MerchantAuthMiddleware**: 验证商家权限
3. **MerchantLogMiddleware**: 记录商家操作日志

## 常见问题

**Q: 如何确保任务不会丢失？**

A: 任务信息存储在数据库中，即使系统重启也不会丢失。调度器在启动时会加载所有待执行的任务。

**Q: 一个商家可以设置多个时间段的营业时间吗？**

A: 可以。商家可以为每一天设置不同的营业时间，系统会为每个营业时间段创建对应的开始和结束任务。

**Q: 如果任务执行失败会怎样？**

A: 系统会根据设置的最大重试次数自动重试，每次重试的间隔时间会逐渐增加。

**Q: 如何取消一个已经创建的定时任务？**

A: 可以调用`/api/v1/scheduler/tasks/:taskId/cancel`接口取消特定任务，或者使用`/api/v1/scheduler/business/:businessId/tasks`查询特定业务的所有任务，然后逐个取消。

**Q: 定时任务的执行精度是多少？**

A: 系统会每5秒检查一次任务队列，因此任务执行的最小延迟可能为0-5秒。
