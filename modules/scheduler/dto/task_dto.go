/**
 * 定时任务数据传输对象
 *
 * 该文件定义了定时任务相关的数据传输对象，用于API接口的请求和响应数据。
 */

package dto

import (
	"time"
)

// TaskCreateDTO 任务创建数据传输对象
type TaskCreateDTO struct {
	TaskType     string    `json:"task_type"`     // 任务类型
	TargetTime   time.Time `json:"target_time"`   // 目标执行时间
	BusinessID   int64     `json:"business_id"`   // 关联业务ID
	BusinessType string    `json:"business_type"` // 关联业务类型
	Payload      string    `json:"payload"`       // 任务负载数据
	Recurrent    bool      `json:"recurrent"`     // 是否周期性任务
	CronExpr     string    `json:"cron_expr"`     // Cron表达式（针对周期性任务）
}

// TaskResponseDTO 任务响应数据传输对象
type TaskResponseDTO struct {
	ID             int64     `json:"id"`              // 任务ID
	TaskID         string    `json:"task_id"`         // 任务唯一标识符
	TaskType       string    `json:"task_type"`       // 任务类型
	TargetTime     time.Time `json:"target_time"`     // 目标执行时间
	Status         int       `json:"status"`          // 任务状态
	BusinessID     int64     `json:"business_id"`     // 关联业务ID
	BusinessType   string    `json:"business_type"`   // 关联业务类型
	RetryCount     int       `json:"retry_count"`     // 重试次数
	MaxRetries     int       `json:"max_retries"`     // 最大重试次数
	LastExecutedAt time.Time `json:"last_executed_at"` // 上次执行时间
	ResultMessage  string    `json:"result_message"`  // 执行结果消息
	CreatedAt      time.Time `json:"created_at"`      // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`      // 更新时间
	Recurrent      bool      `json:"recurrent"`       // 是否周期性任务
	CronExpression string    `json:"cron_expression"` // Cron表达式
	NextRunTime    time.Time `json:"next_run_time"`   // 下次运行时间
}

// TaskTimeUpdateDTO 任务时间更新数据传输对象
type TaskTimeUpdateDTO struct {
	TargetTime time.Time `json:"target_time"` // 新的目标执行时间
}

// BusinessHourDTO 商家营业时间数据传输对象
type BusinessHourDTO struct {
	Weekday   int    `json:"weekday"`   // 星期几：0-周日，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六
	StartTime string `json:"startTime"` // 开始营业时间，格式：HH:MM
	EndTime   string `json:"endTime"`   // 结束营业时间，格式：HH:MM
}
