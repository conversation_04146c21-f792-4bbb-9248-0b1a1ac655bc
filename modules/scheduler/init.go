/**
 * 定时执行模块初始化文件
 *
 * 该文件负责初始化定时执行模块，注册模块的数据模型，
 * 启动定时任务调度器并加载持久化的任务。
 */

package scheduler

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/scheduler/core"
	"o_mall_backend/modules/scheduler/models"
	"o_mall_backend/modules/scheduler/services"
	_ "o_mall_backend/modules/scheduler/routers"
)

// Init 初始化定时执行模块
func Init() {
	logs.Info("[Scheduler] 初始化定时执行模块")

	// 注册模型
	registerModels()

	// 启动定时任务调度器
	core.StartScheduler()

	// 启动订单清理调度器
	startOrderCleanupScheduler()

	logs.Info("[Scheduler] 定时执行模块初始化完成")
}

// startOrderCleanupScheduler 启动订单清理调度器
func startOrderCleanupScheduler() {
	logs.Info("[Scheduler] 启动订单清理调度器")

	// 创建订单清理服务
	orderCleanupService := services.NewOrderCleanupService()

	// 启动周期性订单清理任务
	err := orderCleanupService.StartOrderCleanupScheduler()
	if err != nil {
		logs.Error("[Scheduler] 启动订单清理调度器失败: %v", err)
		return
	}

	logs.Info("[Scheduler] 订单清理调度器启动成功")
}

// 注册ORM模型
func registerModels() {
	logs.Info("[Scheduler] 注册定时任务模块ORM模型...")

	// 注册定时任务相关模型
	orm.RegisterModel(
		new(models.ScheduledTask),
	)
}
