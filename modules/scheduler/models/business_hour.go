/**
 * 商家营业时间模型
 *
 * 该文件定义了商家营业时间模型，用于在定时模块中处理商家的营业时间信息。
 * 包含了序列化和反序列化方法，用于在数据库中存储和读取营业时间信息。
 * 提供Key()方法生成唯一标识符，用于标识和操作特定的营业时间段。
 */

package models

import (
	"encoding/json"
	"fmt"
	"time"
)

// BusinessHour 商家营业时间结构体
type BusinessHour struct {
	Weekday   int    `json:"weekday"`   // 星期几：0-周日，1-周一，2-周二，3-周三，4-周四，5-周五，6-周六
	StartTime string `json:"startTime"` // 开始营业时间，格式：HH:MM
	EndTime   string `json:"endTime"`   // 结束营业时间，格式：HH:MM
}

// SerializeBusinessHours 将营业时间列表序列化为JSON字符串
// 用于存储到数据库中的BusinessHours字段
func SerializeBusinessHours(hours []BusinessHour) (string, error) {
	if hours == nil || len(hours) == 0 {
		return "", nil
	}

	data, err := json.Marshal(hours)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

// DeserializeBusinessHours 将JSON字符串反序列化为营业时间列表
// 用于从数据库中的BusinessHours字段读取数据
func DeserializeBusinessHours(data string) ([]BusinessHour, error) {
	if data == "" {
		return []BusinessHour{}, nil
	}

	var hours []BusinessHour
	err := json.Unmarshal([]byte(data), &hours)
	return hours, err
}

// ParseTimeString 将HH:MM格式的时间字符串解析为time.Time
func ParseTimeString(timeStr string) (time.Time, error) {
	// 解析时间字符串，格式为HH:MM
	layout := "15:04"
	return time.Parse(layout, timeStr)
}

// Key 生成营业时间段的唯一标识符
// 格式：weekday:startTime:endTime，例如 1:09:00:14:00
func (h *BusinessHour) Key() string {
	return fmt.Sprintf("%d:%s:%s", h.Weekday, h.StartTime, h.EndTime)
}

// KeyFromParts 根据星期、开始时间和结束时间生成唯一标识符
func KeyFromParts(weekday int, startTime, endTime string) string {
	return fmt.Sprintf("%d:%s:%s", weekday, startTime, endTime)
}

// ParseKey 解析唯一标识符为营业时间信息
func ParseKey(key string) (*BusinessHour, error) {
	var weekday int
	var startTime, endTime string

	_, err := fmt.Sscanf(key, "%d:%s:%s", &weekday, &startTime, &endTime)
	if err != nil {
		return nil, fmt.Errorf("无效的营业时间标识符: %s", key)
	}

	return &BusinessHour{
		Weekday:   weekday,
		StartTime: startTime,
		EndTime:   endTime,
	}, nil
}

// GetNextOccurrence 获取下一次指定星期几的特定时间
func GetNextOccurrence(weekday int, timeStr string) (time.Time, error) {
	// 当前时间
	now := time.Now()
	
	// 解析时间字符串
	parsedTime, err := ParseTimeString(timeStr)
	if err != nil {
		return time.Time{}, err
	}
	
	// 创建目标时间（使用今天的日期和指定的时间）
	targetTime := time.Date(
		now.Year(), now.Month(), now.Day(),
		parsedTime.Hour(), parsedTime.Minute(), 0, 0,
		now.Location(),
	)
	
	// 计算当前是星期几
	currentWeekday := int(now.Weekday())
	
	// 计算需要加多少天才能到达目标星期几
	daysToAdd := (weekday - currentWeekday + 7) % 7
	
	// 如果是今天并且时间已经过了，那么需要推迟到下周同一天
	if daysToAdd == 0 && now.After(targetTime) {
		daysToAdd = 7
	}
	
	// 添加天数
	targetTime = targetTime.AddDate(0, 0, daysToAdd)
	
	return targetTime, nil
}
