/**
 * 定时任务模型
 *
 * 该文件定义了定时任务模型，用于存储任务的详细信息，包括任务ID、类型、
 * 目标执行时间、任务状态、关联业务信息及任务执行结果等。
 */

package models

import (
	"time"
)

// 任务状态常量
const (
	TaskStatusPending   = 0 // 等待执行
	TaskStatusRunning   = 1 // 执行中
	TaskStatusCompleted = 2 // 执行完成
	TaskStatusFailed    = 3 // 执行失败
	TaskStatusCancelled = 4 // 已取消
)

// 任务类型常量
const (
	TaskTypeMerchantOpen      = "merchant_open"      // 商家开始营业
	TaskTypeMerchantClose     = "merchant_close"     // 商家结束营业
	TaskTypeOrderNotification = "order_notification" // 订单状态通知
	TaskTypePromotionStart    = "promotion_start"    // 促销活动开始
	TaskTypePromotionEnd      = "promotion_end"      // 促销活动结束
	TaskTypeOrderCleanup      = "order_cleanup"      // 订单清理任务
)

// 任务周期类型常量
const (
	TaskRecurTypeNone     = "none"     // 非周期性
	TaskRecurTypeDaily    = "daily"    // 每日
	TaskRecurTypeWeekly   = "weekly"   // 每周
	TaskRecurTypeMonthly  = "monthly"  // 每月
	TaskRecurTypeYearly   = "yearly"   // 每年
	TaskRecurTypeCustom   = "custom"   // 自定义Cron
)

// ScheduledTask 定时任务模型
type ScheduledTask struct {
	ID             int64     `orm:"pk;auto;column(id)" json:"id"`                                          // 任务ID
	TaskID         string    `orm:"size(100);unique;column(task_id)" json:"task_id"`                       // 任务唯一标识符（如 merchant_1001_open）
	TaskType       string    `orm:"size(50);column(task_type)" json:"task_type"`                           // 任务类型
	TargetTime     time.Time `orm:"type(datetime);column(target_time)" json:"target_time"`                 // 目标执行时间
	Status         int       `orm:"default(0);column(status)" json:"status"`                               // 任务状态
	BusinessID     int64     `orm:"column(business_id)" json:"business_id"`                                // 关联业务ID（如商家ID）
	BusinessType   string    `orm:"size(50);column(business_type)" json:"business_type"`                   // 关联业务类型
	MerchantID     int64     `orm:"-" json:"merchant_id"`                                                  // 商家ID（非数据库字段）
	Description    string    `orm:"-" json:"description"`                                                   // 任务描述（非数据库字段）
	Payload        string    `orm:"type(text);column(payload)" json:"payload"`                             // 任务负载数据（JSON格式）
	RetryCount     int       `orm:"default(0);column(retry_count)" json:"retry_count"`                     // 重试次数
	MaxRetries     int       `orm:"default(3);column(max_retries)" json:"max_retries"`                     // 最大重试次数
	LastExecutedAt time.Time `orm:"type(datetime);null;column(last_executed_at)" json:"last_executed_at"`  // 上次执行时间
	ResultMessage  string    `orm:"type(text);null;column(result_message)" json:"result_message"`          // 执行结果消息
	CreatedAt      time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"`      // 创建时间
	UpdatedAt      time.Time `orm:"auto_now;type(datetime);column(updated_at)" json:"updated_at"`          // 更新时间
	Recurrent      bool      `orm:"default(false);column(recurrent)" json:"recurrent"`                     // 是否周期性任务
	RecurType      string    `orm:"-" json:"recur_type"`                                                   // 周期类型（非数据库字段）
	CronExpression string    `orm:"size(100);null;column(cron_expression)" json:"cron_expression"`         // Cron表达式（针对周期性任务）
	NextRunTime    time.Time `orm:"type(datetime);null;column(next_run_time)" json:"next_run_time"`        // 下次运行时间（针对周期性任务）
}

// TableName 指定表名
func (t *ScheduledTask) TableName() string {
	return "scheduler_task"
}
