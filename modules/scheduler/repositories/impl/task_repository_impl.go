/**
 * 定时任务仓库实现
 *
 * 该文件实现了定时任务仓库接口，提供对定时任务的数据库操作实现。
 */

package impl

import (
	"fmt"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/scheduler/models"
	"o_mall_backend/modules/scheduler/repositories"
)

// TaskRepositoryImpl 定时任务仓库实现
type TaskRepositoryImpl struct{}

// NewTaskRepository 创建一个新的任务仓库实例
func NewTaskRepository() repositories.TaskRepository {
	return &TaskRepositoryImpl{}
}

// DeleteByMerchantAndTypes 根据商家ID和任务类型列表删除任务
func (r *TaskRepositoryImpl) DeleteByMerchantAndTypes(merchantID int64, taskTypes []string) error {
	logs.Info("[TaskRepositoryImpl.DeleteByMerchantAndTypes] 删除商家 %d 的指定类型任务: %v", merchantID, taskTypes)
	
	if len(taskTypes) == 0 {
		return nil
	}
	
	o := orm.NewOrm()
	
	// 构建 IN 条件的占位符
	placeholders := make([]string, len(taskTypes))
	args := make([]interface{}, len(taskTypes)+1)
	args[0] = merchantID
	
	for i, taskType := range taskTypes {
		placeholders[i] = "?"
		args[i+1] = taskType
	}
	
	sql := fmt.Sprintf("DELETE FROM scheduler_task WHERE business_id = ? AND task_type IN (%s)", 
		strings.Join(placeholders, ","))
	
	_, err := o.Raw(sql, args...).Exec()
	if err != nil {
		logs.Error("[TaskRepositoryImpl.DeleteByMerchantAndTypes] 删除任务失败: %v", err)
		return err
	}
	
	return nil
}

// Save 保存或更新任务
func (r *TaskRepositoryImpl) Save(task *models.ScheduledTask) error {
	logs.Info("[TaskRepositoryImpl.Save] 保存任务: %s", task.TaskID)
	
	// 查询是否已存在相同任务ID的任务
	existingTask, err := r.GetByTaskID(task.TaskID)
	
	// 如果是新任务，则创建
	if err != nil || existingTask == nil {
		_, err = r.Create(task)
		return err
	}
	
	// 如果已存在，则更新
	task.ID = existingTask.ID
	return r.Update(task)
}

// Create 创建新任务
func (r *TaskRepositoryImpl) Create(task *models.ScheduledTask) (int64, error) {
	logs.Info("[TaskRepositoryImpl.Create] 创建定时任务: %v", task.TaskID)
	
	o := orm.NewOrm()
	id, err := o.Insert(task)
	if err != nil {
		logs.Error("[TaskRepositoryImpl.Create] 创建定时任务失败: %v", err)
		return 0, err
	}
	
	return id, nil
}

// GetByID 根据ID获取任务
func (r *TaskRepositoryImpl) GetByID(id int64) (*models.ScheduledTask, error) {
	logs.Info("[TaskRepositoryImpl.GetByID] 获取定时任务, ID: %d", id)
	
	o := orm.NewOrm()
	task := models.ScheduledTask{ID: id}
	
	err := o.Read(&task)
	if err != nil {
		if err == orm.ErrNoRows {
			logs.Warn("[TaskRepositoryImpl.GetByID] 任务不存在, ID: %d", id)
			return nil, nil
		}
		
		logs.Error("[TaskRepositoryImpl.GetByID] 获取定时任务失败: %v", err)
		return nil, err
	}
	
	return &task, nil
}

// GetByTaskID 根据任务唯一标识获取任务
func (r *TaskRepositoryImpl) GetByTaskID(taskID string) (*models.ScheduledTask, error) {
	logs.Info("[TaskRepositoryImpl.GetByTaskID] 获取定时任务, TaskID: %s", taskID)
	
	o := orm.NewOrm()
	var task models.ScheduledTask
	
	err := o.QueryTable(new(models.ScheduledTask)).Filter("task_id", taskID).One(&task)
	if err != nil {
		if err == orm.ErrNoRows {
			logs.Warn("[TaskRepositoryImpl.GetByTaskID] 任务不存在, TaskID: %s", taskID)
			return nil, nil
		}
		
		logs.Error("[TaskRepositoryImpl.GetByTaskID] 获取定时任务失败: %v", err)
		return nil, err
	}
	
	return &task, nil
}

// GetAll 获取所有任务
func (r *TaskRepositoryImpl) GetAll() ([]*models.ScheduledTask, error) {
	logs.Info("[TaskRepositoryImpl.GetAll] 获取所有定时任务")
	
	o := orm.NewOrm()
	var tasks []*models.ScheduledTask
	
	_, err := o.QueryTable(new(models.ScheduledTask)).All(&tasks)
	if err != nil {
		logs.Error("[TaskRepositoryImpl.GetAll] 获取所有定时任务失败: %v", err)
		return nil, err
	}
	
	return tasks, nil
}

// GetPendingTasksBeforeTime 获取指定时间前需要执行的任务
func (r *TaskRepositoryImpl) GetPendingTasksBeforeTime(t time.Time) ([]*models.ScheduledTask, error) {
	logs.Info("[TaskRepositoryImpl.GetPendingTasksBeforeTime] 获取待执行的任务, 时间: %v", t)
	
	o := orm.NewOrm()
	var tasks []*models.ScheduledTask
	
	_, err := o.QueryTable(new(models.ScheduledTask)).
		Filter("status", models.TaskStatusPending).
		Filter("target_time__lte", t).
		OrderBy("target_time").
		All(&tasks)
	
	if err != nil {
		logs.Error("[TaskRepositoryImpl.GetPendingTasksBeforeTime] 获取待执行任务失败: %v", err)
		return nil, err
	}
	
	return tasks, nil
}

// GetByBusinessIDAndType 获取指定业务ID和类型的任务
func (r *TaskRepositoryImpl) GetByBusinessIDAndType(businessID int64, businessType string) ([]*models.ScheduledTask, error) {
	logs.Info("[TaskRepositoryImpl.GetByBusinessIDAndType] 获取业务任务, ID: %d, 类型: %s", businessID, businessType)
	
	o := orm.NewOrm()
	var tasks []*models.ScheduledTask
	
	_, err := o.QueryTable(new(models.ScheduledTask)).
		Filter("business_id", businessID).
		Filter("business_type", businessType).
		All(&tasks)
	
	if err != nil {
		logs.Error("[TaskRepositoryImpl.GetByBusinessIDAndType] 获取业务任务失败: %v", err)
		return nil, err
	}
	
	return tasks, nil
}

// Update 更新任务
func (r *TaskRepositoryImpl) Update(task *models.ScheduledTask) error {
	logs.Info("[TaskRepositoryImpl.Update] 更新定时任务, ID: %d", task.ID)
	
	o := orm.NewOrm()
	_, err := o.Update(task)
	if err != nil {
		logs.Error("[TaskRepositoryImpl.Update] 更新定时任务失败: %v", err)
		return err
	}
	
	return nil
}

// Delete 删除任务
func (r *TaskRepositoryImpl) Delete(id int64) error {
	logs.Info("[TaskRepositoryImpl.Delete] 删除定时任务, ID: %d", id)
	
	o := orm.NewOrm()
	task := models.ScheduledTask{ID: id}
	
	_, err := o.Delete(&task)
	if err != nil {
		logs.Error("[TaskRepositoryImpl.Delete] 删除定时任务失败: %v", err)
		return err
	}
	
	return nil
}

// BatchSave 批量保存任务
func (r *TaskRepositoryImpl) BatchSave(tasks []*models.ScheduledTask) error {
	logs.Info("[TaskRepositoryImpl.BatchSave] 批量保存定时任务, 数量: %d", len(tasks))
	
	o := orm.NewOrm()
	// 由于没有使用事务，我们使用逐个保存的方式
	for _, task := range tasks {
		var err error
		if task.ID > 0 {
			_, err = o.Update(task)
		} else {
			_, err = o.Insert(task)
		}
		
		if err != nil {
			logs.Error("[TaskRepositoryImpl.BatchSave] 保存任务失败: %v", err)
			return err
		}
	}
	
	return nil
}

// GetRecurrentTasks 获取周期性任务
func (r *TaskRepositoryImpl) GetRecurrentTasks() ([]*models.ScheduledTask, error) {
	logs.Info("[TaskRepositoryImpl.GetRecurrentTasks] 获取周期性任务")
	
	o := orm.NewOrm()
	var tasks []*models.ScheduledTask
	
	_, err := o.QueryTable(new(models.ScheduledTask)).
		Filter("recurrent", true).
		All(&tasks)
	
	if err != nil {
		logs.Error("[TaskRepositoryImpl.GetRecurrentTasks] 获取周期性任务失败: %v", err)
		return nil, err
	}
	
	return tasks, nil
}

// UpdateStatus 更新任务状态
func (r *TaskRepositoryImpl) UpdateStatus(taskID int64, status int, resultMessage string) error {
	logs.Info("[TaskRepositoryImpl.UpdateStatus] 更新任务状态, ID: %d, 状态: %d", taskID, status)
	
	o := orm.NewOrm()
	task := models.ScheduledTask{ID: taskID}
	
	if err := o.Read(&task); err != nil {
		logs.Error("[TaskRepositoryImpl.UpdateStatus] 读取任务失败: %v", err)
		return err
	}
	
	task.Status = status
	task.ResultMessage = resultMessage
	task.LastExecutedAt = time.Now()
	
	_, err := o.Update(&task, "Status", "ResultMessage", "LastExecutedAt")
	if err != nil {
		logs.Error("[TaskRepositoryImpl.UpdateStatus] 更新任务状态失败: %v", err)
		return err
	}
	
	return nil
}

// CancelTasksByBusiness 取消指定业务的所有任务
func (r *TaskRepositoryImpl) CancelTasksByBusiness(businessID int64, businessType string) error {
	logs.Info("[TaskRepositoryImpl.CancelTasksByBusiness] 取消业务任务, ID: %d, 类型: %s", businessID, businessType)
	
	o := orm.NewOrm()
	_, err := o.QueryTable(new(models.ScheduledTask)).
		Filter("business_id", businessID).
		Filter("business_type", businessType).
		Filter("status", models.TaskStatusPending).
		Update(orm.Params{
			"status":          models.TaskStatusCancelled,
			"result_message":  "任务已被取消",
			"last_executed_at": time.Now(),
		})
	
	if err != nil {
		logs.Error("[TaskRepositoryImpl.CancelTasksByBusiness] 取消业务任务失败: %v", err)
		return err
	}
	
	return nil
}
