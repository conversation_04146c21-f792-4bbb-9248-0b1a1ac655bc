/**
 * 定时任务仓库接口
 *
 * 该文件定义了定时任务仓库的接口，提供了任务的CRUD操作方法。
 */

package repositories

import (
	"time"
	
	"o_mall_backend/modules/scheduler/models"
)

// TaskRepository 定义定时任务仓库接口
type TaskRepository interface {
	// 创建新任务
	Create(task *models.ScheduledTask) (int64, error)
	
	// 根据ID获取任务
	GetByID(id int64) (*models.ScheduledTask, error)
	
	// 根据任务唯一标识获取任务
	GetByTaskID(taskID string) (*models.ScheduledTask, error)
	
	// 获取所有任务
	GetAll() ([]*models.ScheduledTask, error)
	
	// 获取指定时间前需要执行的任务
	GetPendingTasksBeforeTime(t time.Time) ([]*models.ScheduledTask, error)
	
	// 获取指定业务ID和类型的任务
	GetByBusinessIDAndType(businessID int64, businessType string) ([]*models.ScheduledTask, error)
	
	// 更新任务
	Update(task *models.ScheduledTask) error
	
	// 删除任务
	Delete(id int64) error
	
	// 批量保存任务
	BatchSave(tasks []*models.ScheduledTask) error
	
	// 获取待执行的周期性任务
	GetRecurrentTasks() ([]*models.ScheduledTask, error)
	
	// 更新任务状态
	UpdateStatus(taskID int64, status int, resultMessage string) error
	
	// 取消指定业务的所有任务
	CancelTasksByBusiness(businessID int64, businessType string) error
	
	// 根据商家ID和任务类型列表删除任务
	DeleteByMerchantAndTypes(merchantID int64, taskTypes []string) error
	
	// 保存或更新任务
	Save(task *models.ScheduledTask) error
}
