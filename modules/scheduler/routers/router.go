/**
 * 定时执行模块路由配置
 *
 * 该文件配置了定时执行模块的API路由，将HTTP请求映射到对应的控制器方法。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/scheduler/controllers"
)

// Init 初始化定时执行模块的路由
func init() {
	// 定义命名空间
	ns := web.NewNamespace("/api/v1/scheduler",
		// 任务管理API
		web.NSRouter("/tasks", &controllers.SchedulerController{}, "post:CreateTask"),
		web.NSRouter("/tasks/:taskId", &controllers.SchedulerController{}, "get:GetTask"),
		web.NSRouter("/tasks/:taskId/time", &controllers.SchedulerController{}, "put:UpdateTaskTime"),
		web.NSRouter("/tasks/:taskId/cancel", &controllers.SchedulerController{}, "put:CancelTask"),
		web.NSRouter("/tasks/:taskId/execute", &controllers.SchedulerController{}, "put:ExecuteTask"),

		// 业务任务API
		web.NSRouter("/business/:businessId/tasks", &controllers.SchedulerController{}, "get:GetTasksByBusiness"),

		// 订单清理任务API
		web.NSRouter("/order-cleanup/create", &controllers.OrderCleanupController{}, "post:CreateCleanupTask"),
		web.NSRouter("/order-cleanup/start", &controllers.OrderCleanupController{}, "post:StartCleanupScheduler"),
		web.NSRouter("/order-cleanup/stop", &controllers.OrderCleanupController{}, "post:StopCleanupScheduler"),
		web.NSRouter("/order-cleanup/status", &controllers.OrderCleanupController{}, "get:GetCleanupTaskStatus"),
		web.NSRouter("/order-cleanup/test", &controllers.OrderCleanupController{}, "post:TestCleanupTask"),
	)
	merchantNs := web.NewNamespace("/api/v1/merchant",
		web.NSBefore(middlewares.CORSMiddleware),
		web.NSNamespace("/scheduler",
			web.NSBefore(middlewares.JWTMiddleware),
			web.NSBefore(middlewares.MerchantAuthMiddleware),
			// 添加商家日志中间件
			web.NSBefore(middlewares.MerchantLogMiddleware()),

			// 商家营业时间任务API（旧API）
			web.NSRouter("/:merchantId/business-time", &controllers.SchedulerController{}, "put:UpdateMerchantBusinessTime"),
		),
		// 新的商家营业时间管理API
		web.NSNamespace("/api",
			web.NSBefore(middlewares.JWTMiddleware),
			web.NSBefore(middlewares.MerchantAuthMiddleware),
			web.NSBefore(middlewares.MerchantLogMiddleware()),

			// 营业时间管理API
			web.NSRouter("/business-hours", &controllers.BusinessHourController{}, "get:GetBusinessHours;post:AddBusinessHours;put:UpdateBusinessHours;delete:DeleteBusinessHours"),
			web.NSRouter("/business-hours/batch", &controllers.BusinessHourController{}, "put:BatchSetBusinessHours"),
			web.NSRouter("/business-hours/operation-status", &controllers.BusinessHourController{}, "post:UpdateOperationStatus"),
			
			// 自动开关店设置API
			web.NSRouter("/business-hours/auto-schedule", &controllers.BusinessHourSettingsController{}, "get:GetAutoScheduleSettings;post:SetAutoScheduleSettings"),
		),
	)
	// 注册命名空间
	web.AddNamespace(ns)
	web.AddNamespace(merchantNs)
}
