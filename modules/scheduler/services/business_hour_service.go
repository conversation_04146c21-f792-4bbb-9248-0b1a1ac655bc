/**
 * 商家营业时间服务接口
 *
 * 该文件定义了商家营业时间的服务接口，提供获取和设置商家营业时间的方法。
 * 扩展了自动开关店设置和营业状态管理功能。
 */

package services

import (
	"o_mall_backend/modules/merchant/models"
)

// BusinessHourService 商家营业时间服务接口
type BusinessHourService interface {
	// GetMerchantBusinessHours 获取商家营业时间列表及状态
	GetMerchantBusinessHours(merchantID int64) (models.BusinessHoursWithStatus, error)

	// SetMerchantBusinessHours 设置商家营业时间列表
	SetMerchantBusinessHours(merchantID int64, hours []models.BusinessHour) error

	// ScheduleMerchantBusinessHourTasks 根据商家营业时间安排开店关店任务
	ScheduleMerchantBusinessHourTasks(merchantID int64) error

	// GetMerchantBusinessHourSettings 获取商家营业时间设置及状态
	GetMerchantBusinessHourSettings(merchantID int64) (models.BusinessHourSettings, error)

	// SetMerchantBusinessHourSettings 设置商家营业时间设置
	SetMerchantBusinessHourSettings(merchantID int64, settings models.BusinessHourSettings) error

	// GetMerchantOperationStatus 获取商家当前营业状态
	GetMerchantOperationStatus(merchantID int64) (models.BusinessHourStatus, error)

	// SetMerchantOperationStatus 设置商家当前营业状态
	SetMerchantOperationStatus(merchantID int64, status models.BusinessHourStatus) error
}
