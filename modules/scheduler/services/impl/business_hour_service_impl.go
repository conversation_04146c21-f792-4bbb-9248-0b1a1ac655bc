/**
 * 商家营业时间服务实现
 *
 * 该文件实现了商家营业时间服务接口，负责管理商家的营业时间信息，
 * 并安排相应的定时任务来自动处理商家的营业状态变化。
 * 扩展了自动开关店设置和营业状态管理功能。
 */

package impl

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/merchant/models"
	"o_mall_backend/modules/scheduler/core"
	schedulerModels "o_mall_backend/modules/scheduler/models"
	"o_mall_backend/modules/scheduler/services"
)

// 任务类型常量
const (
	TaskTypeMerchantOpen  = "merchant_open"  // 商家开始营业
	TaskTypeMerchantClose = "merchant_close" // 商家结束营业
)

// 任务状态常量
const (
	TaskStatusPending = 0 // 等待执行
)

// 任务周期类型常量
const (
	TaskRecurTypeWeekly = "weekly" // 每周
)

// BusinessHourServiceImpl 商家营业时间服务实现
type BusinessHourServiceImpl struct{}

// NewBusinessHourService 创建新的商家营业时间服务
func NewBusinessHourService() services.BusinessHourService {
	return &BusinessHourServiceImpl{}
}

// 辅助函数

// getNextOccurrence 计算给定星期几和时间的下一次出现时间
func getNextOccurrence(weekday int, timeStr string) time.Time {
	// 解析时间字符串 (格式如 "08:00")
	timeLayout := "15:04"
	timeValue, err := time.Parse(timeLayout, timeStr)
	if err != nil {
		logs.Error("解析时间字符串失败: %v", err)
		return time.Now()
	}

	// 获取当前时间
	now := time.Now()
	currentWeekday := int(now.Weekday())
	if currentWeekday == 0 { // Sunday in Go is 0, convert to 7 for consistency
		currentWeekday = 7
	}

	// 计算目标日期
	daysToAdd := (weekday - currentWeekday + 7) % 7
	if daysToAdd == 0 {
		// 如果是同一天，检查时间是否已过
		currentTime := now.Hour()*60 + now.Minute()
		targetTime := timeValue.Hour()*60 + timeValue.Minute()
		if currentTime >= targetTime {
			daysToAdd = 7 // 如果今天的时间已过，则取下周的同一天
		}
	}

	// 创建目标日期时间
	targetDate := now.Add(time.Duration(daysToAdd) * 24 * time.Hour)
	targetDateTime := time.Date(
		targetDate.Year(),
		targetDate.Month(),
		targetDate.Day(),
		timeValue.Hour(),
		timeValue.Minute(),
		0, 0, // 秒和纳秒设为0
		now.Location(),
	)

	return targetDateTime
}

// 获取商家的业务时间键（用于唯一标识一个营业时间段）
func getBusinessHourKey(hour models.BusinessHour) string {
	return fmt.Sprintf("%d:%s:%s", hour.Weekday, hour.StartTime, hour.EndTime)
}

// GetMerchantBusinessHours 获取商家营业时间列表及状态
func (s *BusinessHourServiceImpl) GetMerchantBusinessHours(merchantID int64) (models.BusinessHoursWithStatus, error) {
	logs.Info("[BusinessHourService.GetMerchantBusinessHours] 获取商家 %d 的营业时间列表及状态", merchantID)

	// 查询商家记录
	o := orm.NewOrm()
	merchant := models.Merchant{ID: merchantID}
	err := o.Read(&merchant)
	if err != nil {
		if err == orm.ErrNoRows {
			return models.BusinessHoursWithStatus{
				BusinessHours:   []models.BusinessHour{},
				OperationStatus: 0, // 默认休息中
			}, nil
		}
		logs.Error("[BusinessHourService.GetMerchantBusinessHours] 查询商家记录失败: %v", err)
		return models.BusinessHoursWithStatus{}, err
	}

	// 获取营业时间
	hours, err := merchant.GetBusinessHours()
	if err != nil {
		return models.BusinessHoursWithStatus{}, err
	}

	return models.BusinessHoursWithStatus{
		BusinessHours:   hours,
		OperationStatus: merchant.OperationStatus,
	}, nil
}

// SetMerchantBusinessHours 设置商家营业时间列表
func (s *BusinessHourServiceImpl) SetMerchantBusinessHours(merchantID int64, hours []models.BusinessHour) error {
	logs.Info("[BusinessHourService.SetMerchantBusinessHours] 设置商家 %d 的营业时间", merchantID)

	// 查询商家记录
	o := orm.NewOrm()
	merchant := models.Merchant{ID: merchantID}
	err := o.Read(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantBusinessHours] 查询商家记录失败: %v", err)
		return err
	}

	// 设置营业时间
	err = merchant.SetBusinessHours(hours)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantBusinessHours] 设置营业时间失败: %v", err)
		return err
	}

	// 更新商家记录
	_, err = o.Update(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantBusinessHours] 更新商家记录失败: %v", err)
		return err
	}

	return nil
}

// ScheduleMerchantBusinessHourTasks 根据商家营业时间安排开店关店任务
func (s *BusinessHourServiceImpl) ScheduleMerchantBusinessHourTasks(merchantID int64) error {
	logs.Info("[BusinessHourService.ScheduleMerchantBusinessHourTasks] 为商家 %d 安排营业时间任务", merchantID)

	// 获取商家营业时间
	hours, err := s.GetMerchantBusinessHours(merchantID)
	if err != nil {
		logs.Error("[BusinessHourService.ScheduleMerchantBusinessHourTasks] 获取商家营业时间失败: %v", err)
		return err
	}

	// 检查商家设置
	settings, err := s.GetMerchantBusinessHourSettings(merchantID)
	if err != nil {
		logs.Error("[BusinessHourService.ScheduleMerchantBusinessHourTasks] 获取商家营业时间设置失败: %v", err)
		return err
	}

	// 如果没有启用自动开关店，则不安排任务
	if !settings.Enabled {
		logs.Info("[BusinessHourService.ScheduleMerchantBusinessHourTasks] 商家未启用自动开关店，不安排任务")
		return nil
	}

	// 读取并清除商家现有营业时间任务
	// 注意：这里仅清除未执行的任务，不影响已经完成或失败的任务记录
	taskRepo := core.GetTaskRepository()
	err = taskRepo.DeleteByMerchantAndTypes(merchantID, []string{TaskTypeMerchantOpen, TaskTypeMerchantClose})
	if err != nil {
		logs.Error("[BusinessHourService.ScheduleMerchantBusinessHourTasks] 删除商家旧任务失败: %v", err)
		return err
	}

	// 遍历商家营业时间，为每个时间段安排开店和关店任务
	for _, hour := range hours.BusinessHours {
		// 创建开店任务
		startTime := getNextOccurrence(hour.Weekday, hour.StartTime)
		// 如果设置了提前开店，则调整时间
		if settings.OpenAheadMinutes > 0 {
			startTime = startTime.Add(time.Duration(-settings.OpenAheadMinutes) * time.Minute)
		}

		openTaskPayload := map[string]interface{}{
			"merchant_id":       merchantID,
			"business_hour_key": getBusinessHourKey(hour),
			"action":            "open",
		}
		openPayloadData, _ := json.Marshal(openTaskPayload)

		openTask := &schedulerModels.ScheduledTask{
			TaskID:       fmt.Sprintf("merchant_open_%d_%d_%s", merchantID, hour.Weekday, hour.StartTime),
			TaskType:     TaskTypeMerchantOpen,
			TargetTime:   startTime,
			Status:       TaskStatusPending,
			BusinessID:   merchantID,
			BusinessType: "merchant",
			MerchantID:   merchantID,
			Payload:      string(openPayloadData),
			Description:  fmt.Sprintf("商家 %d 在每周 %d 的 %s 开始营业", merchantID, hour.Weekday, hour.StartTime),
			Recurrent:    true,
			RecurType:    TaskRecurTypeWeekly,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		err = taskRepo.Save(openTask)
		if err != nil {
			logs.Error("[BusinessHourService.ScheduleMerchantBusinessHourTasks] 保存开店任务失败: %v", err)
		}

		// 创建关店任务
		endTime := getNextOccurrence(hour.Weekday, hour.EndTime)
		// 如果设置了延迟关店，则调整时间
		if settings.CloseDelayMinutes > 0 {
			endTime = endTime.Add(time.Duration(settings.CloseDelayMinutes) * time.Minute)
		}

		closeTaskPayload := map[string]interface{}{
			"merchant_id":       merchantID,
			"business_hour_key": getBusinessHourKey(hour),
			"action":            "close",
		}
		closePayloadData, _ := json.Marshal(closeTaskPayload)

		closeTask := &schedulerModels.ScheduledTask{
			TaskID:       fmt.Sprintf("merchant_close_%d_%d_%s", merchantID, hour.Weekday, hour.EndTime),
			TaskType:     TaskTypeMerchantClose,
			TargetTime:   endTime,
			Status:       TaskStatusPending,
			BusinessID:   merchantID,
			BusinessType: "merchant",
			MerchantID:   merchantID,
			Payload:      string(closePayloadData),
			Description:  fmt.Sprintf("商家 %d 在每周 %d 的 %s 结束营业", merchantID, hour.Weekday, hour.EndTime),
			Recurrent:    true,
			RecurType:    TaskRecurTypeWeekly,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		err = taskRepo.Save(closeTask)
		if err != nil {
			logs.Error("[BusinessHourService.ScheduleMerchantBusinessHourTasks] 保存关店任务失败: %v", err)
		}
	}

	return nil
}

// GetMerchantBusinessHourSettings 获取商家营业时间设置及营业状态
func (s *BusinessHourServiceImpl) GetMerchantBusinessHourSettings(merchantID int64) (models.BusinessHourSettings, error) {
	logs.Info("[BusinessHourService.GetMerchantBusinessHourSettings] 获取商家 %d 的营业时间设置及状态", merchantID)

	// 查询商家记录
	o := orm.NewOrm()
	merchant := models.Merchant{ID: merchantID}
	err := o.Read(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.GetMerchantBusinessHourSettings] 查询商家记录失败: %v", err)
		return models.BusinessHourSettings{}, err
	}

	// 获取营业时间设置
	settings, err := merchant.GetBusinessHourSettings()
	if err != nil {
		return models.BusinessHourSettings{}, err
	}

	// 返回包含营业状态的设置
	return models.BusinessHourSettings{
		Enabled:           settings.Enabled,
		OpenAheadMinutes:  settings.OpenAheadMinutes,
		CloseDelayMinutes: settings.CloseDelayMinutes,
		Notifications:     settings.Notifications,
		LastUpdated:       settings.LastUpdated,
	}, nil
}

// SetMerchantBusinessHourSettings 设置商家营业时间设置
func (s *BusinessHourServiceImpl) SetMerchantBusinessHourSettings(merchantID int64, settings models.BusinessHourSettings) error {
	logs.Info("[BusinessHourService.SetMerchantBusinessHourSettings] 设置商家 %d 的营业时间设置", merchantID)

	// 查询商家记录
	o := orm.NewOrm()
	merchant := models.Merchant{ID: merchantID}
	err := o.Read(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantBusinessHourSettings] 查询商家记录失败: %v", err)
		return err
	}

	// 设置营业时间设置
	err = merchant.SetBusinessHourSettings(settings)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantBusinessHourSettings] 设置营业时间设置失败: %v", err)
		return err
	}

	// 更新商家记录
	_, err = o.Update(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantBusinessHourSettings] 更新商家记录失败: %v", err)
		return err
	}

	return nil
}

// GetMerchantOperationStatus 获取商家当前营业状态
func (s *BusinessHourServiceImpl) GetMerchantOperationStatus(merchantID int64) (models.BusinessHourStatus, error) {
	logs.Info("[BusinessHourService.GetMerchantOperationStatus] 获取商家 %d 的营业状态", merchantID)

	// 查询商家记录
	o := orm.NewOrm()
	merchant := models.Merchant{ID: merchantID}
	err := o.Read(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.GetMerchantOperationStatus] 查询商家记录失败: %v", err)
		return models.BusinessHourStatus{}, err
	}

	// 获取营业状态数据
	return merchant.GetOperationStatusData()
}

// SetMerchantOperationStatus 设置商家当前营业状态
func (s *BusinessHourServiceImpl) SetMerchantOperationStatus(merchantID int64, status models.BusinessHourStatus) error {
	logs.Info("[BusinessHourService.SetMerchantOperationStatus] 设置商家 %d 的营业状态", merchantID)

	// 查询商家记录
	o := orm.NewOrm()
	merchant := models.Merchant{ID: merchantID}
	err := o.Read(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantOperationStatus] 查询商家记录失败: %v", err)
		return err
	}

	// 设置营业状态数据
	err = merchant.SetOperationStatusData(status)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantOperationStatus] 设置营业状态数据失败: %v", err)
		return err
	}

	// 更新商家记录
	_, err = o.Update(&merchant)
	if err != nil {
		logs.Error("[BusinessHourService.SetMerchantOperationStatus] 更新商家记录失败: %v", err)
		return err
	}

	return nil
}
