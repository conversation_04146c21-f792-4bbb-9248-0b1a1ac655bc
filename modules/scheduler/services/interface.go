/**
 * 定时任务服务接口
 *
 * 该文件定义了定时任务服务的接口，提供了任务的业务逻辑操作方法。
 */

package services

import (
	"time"
	
	"o_mall_backend/modules/scheduler/models"
)

// TaskService 定时任务服务接口
type TaskService interface {
	// 创建一次性定时任务
	CreateTask(taskType string, targetTime time.Time, businessID int64, businessType string, payload string) (string, error)
	
	// 创建周期性定时任务
	CreateRecurrentTask(taskType string, cronExpression string, businessID int64, businessType string, payload string) (string, error)
	
	// 更新任务的执行时间
	UpdateTaskTime(taskID string, newTime time.Time) error
	
	// 取消任务
	CancelTask(taskID string) error
	
	// 取消指定业务的所有任务
	CancelTasksByBusiness(businessID int64, businessType string) error
	
	// 获取任务信息
	GetTask(taskID string) (*models.ScheduledTask, error)
	
	// 获取指定业务的所有任务
	GetTasksByBusiness(businessID int64, businessType string) ([]*models.ScheduledTask, error)
	
	// 获取所有任务
	GetAllTasks() ([]*models.ScheduledTask, error)
	
	// 执行特定任务
	ExecuteTask(taskID string) error
	
	// 更新商家营业状态任务
	UpdateMerchantBusinessTimeTasks(merchantID int64, businessHours []models.BusinessHour) error
}
