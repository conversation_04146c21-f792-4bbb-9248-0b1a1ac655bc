/*
 * 订单清理服务
 *
 * 该文件实现了订单清理任务的管理服务，负责创建和调度定时清理超时未付款订单的任务。
 * 支持普通订单和外卖订单的自动清理功能。
 */

package services

import (
	"encoding/json"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/scheduler/models"
)// OrderCleanupService 订单清理服务接口
type OrderCleanupService interface {
	// CreateOrderCleanupTask 创建订单清理任务
	CreateOrderCleanupTask() error
	// StartOrderCleanupScheduler 启动订单清理调度器
	StartOrderCleanupScheduler() error
	// StopOrderCleanupScheduler 停止订单清理调度器
	StopOrderCleanupScheduler() error
}

// OrderCleanupServiceImpl 订单清理服务实现
type OrderCleanupServiceImpl struct {
	taskService TaskService
}

// NewOrderCleanupService 创建订单清理服务实例
func NewOrderCleanupService() OrderCleanupService {
	return &OrderCleanupServiceImpl{
		taskService: NewTaskService(),
	}
}

// CreateOrderCleanupTask 创建订单清理任务
func (s *OrderCleanupServiceImpl) CreateOrderCleanupTask() error {
	logs.Info("[OrderCleanupService.CreateOrderCleanupTask] 创建订单清理任务")

	// 创建任务负载数据
	payload := map[string]interface{}{
		"task_type":   "order_cleanup",
		"description": "定时清理超过30分钟未付款的订单",
		"timeout_minutes": 30,
		"created_at": time.Now().Format("2006-01-02 15:04:05"),
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		logs.Error("[OrderCleanupService.CreateOrderCleanupTask] 序列化任务负载失败: %v", err)
		return err
	}

	// 设置任务执行时间为当前时间后1分钟（用于测试）
	targetTime := time.Now().Add(1 * time.Minute)

	// 创建一次性任务
	taskID, err := s.taskService.CreateTask(
		models.TaskTypeOrderCleanup,
		targetTime,
		0, // businessID 设为0，表示全局任务
		"system", // businessType 设为system
		string(payloadJSON),
	)

	if err != nil {
		logs.Error("[OrderCleanupService.CreateOrderCleanupTask] 创建任务失败: %v", err)
		return err
	}

	logs.Info("[OrderCleanupService.CreateOrderCleanupTask] 订单清理任务创建成功，任务ID: %s，执行时间: %v", taskID, targetTime)
	return nil
}

// StartOrderCleanupScheduler 启动订单清理调度器
func (s *OrderCleanupServiceImpl) StartOrderCleanupScheduler() error {
	logs.Info("[OrderCleanupService.StartOrderCleanupScheduler] 启动订单清理调度器")

	// 创建周期性任务，每30分钟执行一次
	cronExpression := "0 */30 * * * *" // 每30分钟执行一次

	// 创建任务负载数据
	payload := map[string]interface{}{
		"task_type":   "order_cleanup",
		"description": "定时清理超过30分钟未付款的订单",
		"timeout_minutes": 30,
		"schedule_type": "recurring",
		"cron_expression": cronExpression,
		"created_at": time.Now().Format("2006-01-02 15:04:05"),
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		logs.Error("[OrderCleanupService.StartOrderCleanupScheduler] 序列化任务负载失败: %v", err)
		return err
	}

	// 创建周期性任务
	taskID, err := s.taskService.CreateRecurrentTask(
		models.TaskTypeOrderCleanup,
		cronExpression,
		0, // businessID 设为0，表示全局任务
		"system", // businessType 设为system
		string(payloadJSON),
	)

	if err != nil {
		logs.Error("[OrderCleanupService.StartOrderCleanupScheduler] 创建周期性任务失败: %v", err)
		return err
	}

	logs.Info("[OrderCleanupService.StartOrderCleanupScheduler] 订单清理调度器启动成功，任务ID: %s，Cron表达式: %s", taskID, cronExpression)
	return nil
}

// StopOrderCleanupScheduler 停止订单清理调度器
func (s *OrderCleanupServiceImpl) StopOrderCleanupScheduler() error {
	logs.Info("[OrderCleanupService.StopOrderCleanupScheduler] 停止订单清理调度器")

	// TODO: 实现停止周期性任务的逻辑
	// 需要根据任务类型查询并取消相关的周期性任务

	logs.Info("[OrderCleanupService.StopOrderCleanupScheduler] 订单清理调度器停止功能待实现")
	return nil
}

// GetOrderCleanupTaskStatus 获取订单清理任务状态
func (s *OrderCleanupServiceImpl) GetOrderCleanupTaskStatus() (map[string]interface{}, error) {
	logs.Info("[OrderCleanupService.GetOrderCleanupTaskStatus] 获取订单清理任务状态")

	// TODO: 实现获取任务状态的逻辑
	// 查询当前活跃的订单清理任务

	status := map[string]interface{}{
		"active_tasks": 0,
		"last_execution": nil,
		"next_execution": nil,
		"total_cleaned_orders": 0,
	}

	return status, nil
}