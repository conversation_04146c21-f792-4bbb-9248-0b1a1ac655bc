/**
 * 定时任务服务实现
 *
 * 该文件实现了定时任务服务接口，提供任务的创建、更新、取消和执行等业务逻辑功能。
 * 同时也处理商家营业时间的自动调度任务。
 */

package services

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/scheduler/core"
	"o_mall_backend/modules/scheduler/models"
	"o_mall_backend/modules/scheduler/repositories"
	"o_mall_backend/modules/scheduler/repositories/impl"
)

// TaskServiceImpl 定时任务服务实现
type TaskServiceImpl struct {
	taskRepo repositories.TaskRepository
}

// NewTaskService 创建定时任务服务实例
func NewTaskService() TaskService {
	return &TaskServiceImpl{
		taskRepo: impl.NewTaskRepository(),
	}
}

// CreateTask 创建一次性定时任务
func (s *TaskServiceImpl) CreateTask(taskType string, targetTime time.Time, businessID int64, businessType string, payload string) (string, error) {
	logs.Info("[TaskServiceImpl.CreateTask] 创建一次性任务: 类型=%s, 时间=%v, 业务ID=%d, 业务类型=%s", 
		taskType, targetTime, businessID, businessType)
	
	// 生成任务ID
	taskID := fmt.Sprintf("%s_%d_%d", taskType, businessID, time.Now().UnixNano())
	
	// 创建任务对象
	task := &models.ScheduledTask{
		TaskID:       taskID,
		TaskType:     taskType,
		TargetTime:   targetTime,
		Status:       models.TaskStatusPending,
		BusinessID:   businessID,
		BusinessType: businessType,
		Payload:      payload,
		MaxRetries:   3,
		Recurrent:    false,
	}
	
	// 保存任务
	_, err := s.taskRepo.Create(task)
	if err != nil {
		logs.Error("[TaskServiceImpl.CreateTask] 创建任务失败: %v", err)
		return "", err
	}
	
	// 通知调度器有新任务
	core.GetScheduler().NotifyNewTask()
	
	return taskID, nil
}

// CreateRecurrentTask 创建周期性定时任务
func (s *TaskServiceImpl) CreateRecurrentTask(taskType string, cronExpression string, businessID int64, businessType string, payload string) (string, error) {
	logs.Info("[TaskServiceImpl.CreateRecurrentTask] 创建周期性任务: 类型=%s, cron表达式=%s, 业务ID=%d, 业务类型=%s", 
		taskType, cronExpression, businessID, businessType)
	
	// 生成任务ID
	taskID := fmt.Sprintf("%s_%d_%d_recurrent", taskType, businessID, time.Now().UnixNano())
	
	// 计算下次执行时间
	nextRunTime, err := core.GetNextExecutionTime(cronExpression)
	if err != nil {
		logs.Error("[TaskServiceImpl.CreateRecurrentTask] 计算下次执行时间失败: %v", err)
		return "", err
	}
	
	// 创建任务对象
	task := &models.ScheduledTask{
		TaskID:         taskID,
		TaskType:       taskType,
		TargetTime:     nextRunTime,
		Status:         models.TaskStatusPending,
		BusinessID:     businessID,
		BusinessType:   businessType,
		Payload:        payload,
		MaxRetries:     3,
		Recurrent:      true,
		CronExpression: cronExpression,
		NextRunTime:    nextRunTime,
	}
	
	// 保存任务
	_, err = s.taskRepo.Create(task)
	if err != nil {
		logs.Error("[TaskServiceImpl.CreateRecurrentTask] 创建周期性任务失败: %v", err)
		return "", err
	}
	
	// 通知调度器有新任务
	core.GetScheduler().NotifyNewTask()
	
	return taskID, nil
}

// UpdateTaskTime 更新任务的执行时间
func (s *TaskServiceImpl) UpdateTaskTime(taskID string, newTime time.Time) error {
	logs.Info("[TaskServiceImpl.UpdateTaskTime] 更新任务时间: ID=%s, 新时间=%v", taskID, newTime)
	
	// 获取任务
	task, err := s.taskRepo.GetByTaskID(taskID)
	if err != nil {
		logs.Error("[TaskServiceImpl.UpdateTaskTime] 获取任务失败: %v", err)
		return err
	}
	
	if task == nil {
		logs.Warn("[TaskServiceImpl.UpdateTaskTime] 任务不存在: %s", taskID)
		return fmt.Errorf("任务不存在: %s", taskID)
	}
	
	// 只有待执行的任务才能更新时间
	if task.Status != models.TaskStatusPending {
		logs.Warn("[TaskServiceImpl.UpdateTaskTime] 只有待执行的任务才能更新时间, 当前状态: %d", task.Status)
		return fmt.Errorf("只有待执行的任务才能更新时间")
	}
	
	// 更新时间
	task.TargetTime = newTime
	
	// 保存更新
	err = s.taskRepo.Update(task)
	if err != nil {
		logs.Error("[TaskServiceImpl.UpdateTaskTime] 更新任务时间失败: %v", err)
		return err
	}
	
	// 通知调度器任务时间已更新
	core.GetScheduler().NotifyNewTask()
	
	return nil
}

// CancelTask 取消任务
func (s *TaskServiceImpl) CancelTask(taskID string) error {
	logs.Info("[TaskServiceImpl.CancelTask] 取消任务: ID=%s", taskID)
	
	// 获取任务
	task, err := s.taskRepo.GetByTaskID(taskID)
	if err != nil {
		logs.Error("[TaskServiceImpl.CancelTask] 获取任务失败: %v", err)
		return err
	}
	
	if task == nil {
		logs.Warn("[TaskServiceImpl.CancelTask] 任务不存在: %s", taskID)
		return fmt.Errorf("任务不存在: %s", taskID)
	}
	
	// 只有待执行的任务才能取消
	if task.Status != models.TaskStatusPending {
		logs.Warn("[TaskServiceImpl.CancelTask] 只有待执行的任务才能取消, 当前状态: %d", task.Status)
		return fmt.Errorf("只有待执行的任务才能取消")
	}
	
	// 更新状态为已取消
	task.Status = models.TaskStatusCancelled
	task.ResultMessage = "任务已手动取消"
	task.LastExecutedAt = time.Now()
	
	// 保存更新
	err = s.taskRepo.Update(task)
	if err != nil {
		logs.Error("[TaskServiceImpl.CancelTask] 取消任务失败: %v", err)
		return err
	}
	
	return nil
}

// CancelTasksByBusiness 取消指定业务的所有任务
func (s *TaskServiceImpl) CancelTasksByBusiness(businessID int64, businessType string) error {
	logs.Info("[TaskServiceImpl.CancelTasksByBusiness] 取消业务任务: ID=%d, 类型=%s", businessID, businessType)
	
	// 使用仓库层的方法批量取消任务
	err := s.taskRepo.CancelTasksByBusiness(businessID, businessType)
	if err != nil {
		logs.Error("[TaskServiceImpl.CancelTasksByBusiness] 取消业务任务失败: %v", err)
		return err
	}
	
	return nil
}

// GetTask 获取任务信息
func (s *TaskServiceImpl) GetTask(taskID string) (*models.ScheduledTask, error) {
	logs.Info("[TaskServiceImpl.GetTask] 获取任务: ID=%s", taskID)
	
	// 获取任务
	task, err := s.taskRepo.GetByTaskID(taskID)
	if err != nil {
		logs.Error("[TaskServiceImpl.GetTask] 获取任务失败: %v", err)
		return nil, err
	}
	
	return task, nil
}

// GetTasksByBusiness 获取指定业务的所有任务
func (s *TaskServiceImpl) GetTasksByBusiness(businessID int64, businessType string) ([]*models.ScheduledTask, error) {
	logs.Info("[TaskServiceImpl.GetTasksByBusiness] 获取业务任务: ID=%d, 类型=%s", businessID, businessType)
	
	// 使用仓库层的方法获取任务
	tasks, err := s.taskRepo.GetByBusinessIDAndType(businessID, businessType)
	if err != nil {
		logs.Error("[TaskServiceImpl.GetTasksByBusiness] 获取业务任务失败: %v", err)
		return nil, err
	}
	
	return tasks, nil
}

// GetAllTasks 获取所有任务
func (s *TaskServiceImpl) GetAllTasks() ([]*models.ScheduledTask, error) {
	logs.Info("[TaskServiceImpl.GetAllTasks] 获取所有任务")
	
	// 使用仓库层的方法获取所有任务
	tasks, err := s.taskRepo.GetAll()
	if err != nil {
		logs.Error("[TaskServiceImpl.GetAllTasks] 获取所有任务失败: %v", err)
		return nil, err
	}
	
	return tasks, nil
}

// ExecuteTask 执行特定任务
func (s *TaskServiceImpl) ExecuteTask(taskID string) error {
	logs.Info("[TaskServiceImpl.ExecuteTask] 手动执行任务: ID=%s", taskID)
	
	// 获取任务
	task, err := s.taskRepo.GetByTaskID(taskID)
	if err != nil {
		logs.Error("[TaskServiceImpl.ExecuteTask] 获取任务失败: %v", err)
		return err
	}
	
	if task == nil {
		logs.Warn("[TaskServiceImpl.ExecuteTask] 任务不存在: %s", taskID)
		return fmt.Errorf("任务不存在: %s", taskID)
	}
	
	// 只有待执行的任务才能手动执行
	if task.Status != models.TaskStatusPending {
		logs.Warn("[TaskServiceImpl.ExecuteTask] 只有待执行的任务才能手动执行, 当前状态: %d", task.Status)
		return fmt.Errorf("只有待执行的任务才能手动执行")
	}
	
	// 提交任务到执行器
	core.GetScheduler().SubmitTaskForExecution(task)
	
	return nil
}

// UpdateMerchantBusinessTimeTasks 更新商家营业状态任务
func (s *TaskServiceImpl) UpdateMerchantBusinessTimeTasks(merchantID int64, businessHours []models.BusinessHour) error {
	logs.Info("[TaskServiceImpl.UpdateMerchantBusinessTimeTasks] 更新商家营业时间任务: 商家ID=%d", merchantID)
	
	// 首先取消该商家之前的所有营业时间任务
	err := s.CancelTasksByBusiness(merchantID, "merchant")
	if err != nil {
		logs.Error("[TaskServiceImpl.UpdateMerchantBusinessTimeTasks] 取消商家现有任务失败: %v", err)
		return err
	}
	
	// 为每个营业时间段创建开始和结束任务
	for _, hour := range businessHours {
		// 计算下一次开始营业的时间
		startTime, err := models.GetNextOccurrence(hour.Weekday, hour.StartTime)
		if err != nil {
			logs.Error("[TaskServiceImpl.UpdateMerchantBusinessTimeTasks] 计算开始时间失败: %v", err)
			continue
		}
		
		// 计算下一次结束营业的时间
		endTime, err := models.GetNextOccurrence(hour.Weekday, hour.EndTime)
		if err != nil {
			logs.Error("[TaskServiceImpl.UpdateMerchantBusinessTimeTasks] 计算结束时间失败: %v", err)
			continue
		}
		
		// 创建开始营业任务
		startPayload, _ := json.Marshal(map[string]interface{}{
			"merchantId": merchantID,
			"operation": "open",
			"weekday":   hour.Weekday,
			"time":      hour.StartTime,
		})
		
		_, err = s.CreateTask(
			models.TaskTypeMerchantOpen,
			startTime,
			merchantID,
			"merchant",
			string(startPayload),
		)
		if err != nil {
			logs.Error("[TaskServiceImpl.UpdateMerchantBusinessTimeTasks] 创建开始营业任务失败: %v", err)
			continue
		}
		
		// 创建结束营业任务
		endPayload, _ := json.Marshal(map[string]interface{}{
			"merchantId": merchantID,
			"operation": "close",
			"weekday":   hour.Weekday,
			"time":      hour.EndTime,
		})
		
		_, err = s.CreateTask(
			models.TaskTypeMerchantClose,
			endTime,
			merchantID,
			"merchant",
			string(endPayload),
		)
		if err != nil {
			logs.Error("[TaskServiceImpl.UpdateMerchantBusinessTimeTasks] 创建结束营业任务失败: %v", err)
			continue
		}
	}
	
	logs.Info("[TaskServiceImpl.UpdateMerchantBusinessTimeTasks] 成功更新商家营业时间任务")
	return nil
}
