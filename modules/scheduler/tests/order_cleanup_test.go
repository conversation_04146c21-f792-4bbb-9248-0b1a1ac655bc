/*
 * 订单清理任务测试
 *
 * 该文件包含订单清理功能的单元测试和集成测试，用于验证清理逻辑的正确性。
 */

package tests

import (
	"encoding/json"
	"testing"
	"time"

	"o_mall_backend/modules/scheduler/models"
	"o_mall_backend/modules/scheduler/services"
)

// TestOrderCleanupService 测试订单清理服务
func TestOrderCleanupService(t *testing.T) {
	// 创建订单清理服务
	orderCleanupService := services.NewOrderCleanupService()

	t.Run("CreateOrderCleanupTask", func(t *testing.T) {
		// 验证任务创建
		err := orderCleanupService.CreateOrderCleanupTask()
		if err != nil {
			t.<PERSON><PERSON><PERSON>("创建订单清理任务失败: %v", err)
			return
		}
		t.Log("订单清理任务创建成功")
	})

	t.Run("StartOrderCleanupScheduler", func(t *testing.T) {
		// 验证调度器启动
		err := orderCleanupService.StartOrderCleanupScheduler()
		if err != nil {
			t.<PERSON><PERSON><PERSON>("启动订单清理调度器失败: %v", err)
		}
	})

	t.Run("StopOrderCleanupScheduler", func(t *testing.T) {
		err := orderCleanupService.StopOrderCleanupScheduler()
		if err != nil {
			t.Errorf("停止订单清理调度器失败: %v", err)
		}
	})
}

// TestOrderCleanupTaskHandler 测试订单清理任务处理器
func TestOrderCleanupTaskHandler(t *testing.T) {
	// 创建测试任务
	payload := map[string]interface{}{
		"task_type":   "order_cleanup",
		"description": "测试订单清理任务",
		"timeout_minutes": 30,
		"created_at": time.Now().Format("2006-01-02 15:04:05"),
	}

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		t.Errorf("序列化任务负载失败: %v", err)
		return
	}

	_ = &models.ScheduledTask{
		TaskID:       "test_order_cleanup_001",
		TaskType:     models.TaskTypeOrderCleanup,
		TargetTime:   time.Now(),
		Status:       models.TaskStatusPending,
		BusinessID:   0,
		BusinessType: "system",
		Payload:      string(payloadJSON),
		MaxRetries:   3,
		Recurrent:    false,
	}

	// 测试任务处理器是否已注册
	t.Run("TaskHandlerRegistered", func(t *testing.T) {
		// 这里我们无法直接访问私有的taskHandlers字段
		// 但可以通过尝试执行任务来验证处理器是否存在
		t.Log("测试任务处理器注册状态")
		t.Log("任务负载创建成功")
	})
}

// TestOrderCleanupTaskPayload 测试任务负载数据
func TestOrderCleanupTaskPayload(t *testing.T) {
	t.Run("ValidPayload", func(t *testing.T) {
		payload := map[string]interface{}{
			"task_type":   "order_cleanup",
			"description": "定时清理超过30分钟未付款的订单",
			"timeout_minutes": 30,
			"created_at": time.Now().Format("2006-01-02 15:04:05"),
		}

		payloadJSON, err := json.Marshal(payload)
		if err != nil {
			t.Errorf("序列化任务负载失败: %v", err)
			return
		}

		// 验证反序列化
		var parsedPayload map[string]interface{}
		err = json.Unmarshal(payloadJSON, &parsedPayload)
		if err != nil {
			t.Errorf("反序列化任务负载失败: %v", err)
			return
		}

		// 验证字段
		if parsedPayload["task_type"] != "order_cleanup" {
			t.Errorf("任务类型不匹配，期望: order_cleanup, 实际: %v", parsedPayload["task_type"])
		}
		if parsedPayload["timeout_minutes"] != float64(30) {
			t.Errorf("超时时间不匹配，期望: 30, 实际: %v", parsedPayload["timeout_minutes"])
		}
		if parsedPayload["created_at"] == nil || parsedPayload["created_at"] == "" {
			t.Error("创建时间不应为空")
		}
	})
}

// TestCronExpression 测试Cron表达式
func TestCronExpression(t *testing.T) {
	t.Run("ValidCronExpression", func(t *testing.T) {
		// 测试每30分钟执行一次的Cron表达式
		cronExpression := "0 */30 * * * *"

		// 这里可以添加Cron表达式验证逻辑
		// 由于我们使用的是robfig/cron库，可以尝试解析表达式
		if cronExpression == "" {
			t.Error("Cron表达式不应为空")
		}
		if !contains(cronExpression, "*/30") {
			t.Error("Cron表达式应包含30分钟间隔")
		}
	})
}

// TestTaskTypeConstant 测试任务类型常量
func TestTaskTypeConstant(t *testing.T) {
	t.Run("OrderCleanupTaskType", func(t *testing.T) {
		// 验证任务类型常量
		if models.TaskTypeOrderCleanup != "order_cleanup" {
			t.Errorf("任务类型常量不匹配，期望: order_cleanup, 实际: %s", models.TaskTypeOrderCleanup)
		}
	})
}

// BenchmarkOrderCleanupTask 性能测试
func BenchmarkOrderCleanupTask(b *testing.B) {
	// 创建订单清理服务
	orderCleanupService := services.NewOrderCleanupService()

	b.ResetTimer()
	// 性能测试：创建任务
	for i := 0; i < b.N; i++ {
		err := orderCleanupService.CreateOrderCleanupTask()
		if err != nil {
			b.Fatal(err)
		}
	}
}

// TestOrderCleanupIntegration 集成测试
func TestOrderCleanupIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	t.Run("FullWorkflow", func(t *testing.T) {
		// 1. 创建订单清理服务
		orderCleanupService := services.NewOrderCleanupService()
		if orderCleanupService == nil {
			t.Error("订单清理服务创建失败")
			return
		}

		// 2. 启动调度器
		err := orderCleanupService.StartOrderCleanupScheduler()
		if err != nil {
			t.Errorf("启动调度器失败: %v", err)
			return
		}

		// 3. 创建一次性任务
		err = orderCleanupService.CreateOrderCleanupTask()
		if err != nil {
			t.Errorf("创建一次性任务失败: %v", err)
			return
		}

		// 4. 等待一段时间让任务执行
		time.Sleep(2 * time.Second)

		// 5. 停止调度器
		err = orderCleanupService.StopOrderCleanupScheduler()
		if err != nil {
			t.Errorf("停止调度器失败: %v", err)
		}
	})
}

// contains 辅助函数，用于检查字符串是否包含子字符串
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}