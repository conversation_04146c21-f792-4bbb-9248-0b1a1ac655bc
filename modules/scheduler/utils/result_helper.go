/**
 * 结果响应助手
 *
 * 该文件提供了用于生成API响应的工具函数，简化控制器中的响应处理。
 */

package utils

import (
	"o_mall_backend/common/result"
)

// 响应状态码
const (
	CodeSuccess       = 200 // 成功
	CodeError         = 500 // 服务器错误
	CodeInvalidParams = 400 // 无效参数
	CodeUnauthorized  = 401 // 未授权
	CodeForbidden     = 403 // 禁止访问
	CodeNotFound      = 404 // 资源不存在
	CodeInternalError = 500 // 内部错误
)

// Success 创建成功响应
func Success(data interface{}) result.Result {
	return result.Result{
		Code:    CodeSuccess,
		Message: "操作成功",
		Data:    data,
	}
}

// Error 创建错误响应
func Error(code int, message string) result.Result {
	return result.Result{
		Code:    code,
		Message: message,
		Data:    nil,
	}
}
