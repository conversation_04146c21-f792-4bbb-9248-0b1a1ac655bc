/**
 * AI控制器
 *
 * 本文件实现了AI相关的控制器，提供了与DeepSeek AI API交互的接口，
 * 包括聊天补全、AI配置管理等功能。
 */

package controllers

import (
	"encoding/json"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/services/impl"
)

// AIController AI控制器
type AIController struct {
	web.Controller
}

// ChatCompletion 聊天补全API
// @Title ChatCompletion
// @Description 获取AI聊天补全响应
// @Param   body     body    dto.AIChatRequestDTO  true   "聊天请求参数"
// @Success 200 {object} result.Result 成功
// @Failure 400 {object} result.Result 请求参数错误
// @Failure 500 {object} result.Result 服务器内部错误
// @router /chat/completion [post]
func (c *AIController) ChatCompletion() {
	// 解析请求体
	var req dto.AIChatRequestDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请求参数错误")
		return
	}

	// 参数验证
	valid := validation.Validation{}
	if b, err := valid.Valid(&req); err != nil {
		logs.Error("参数验证错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "服务器内部错误")
		return
	} else if !b {
		// 验证不通过，返回第一个错误信息
		for _, err := range valid.Errors {
			logs.Warn("参数验证失败: %s %s", err.Key, err.Message)
			result.HandleError(c.Ctx, result.ErrInvalidParam, err.Key+" "+err.Message)
			return
		}
	}

	// 验证messages非空
	if len(req.Messages) == 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "消息列表不能为空")
		return
	}

	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 调用AI聊天补全服务
	resp, err := aiService.GetChatCompletion(&req)
	if err != nil {
		logs.Error("获取AI聊天补全失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "获取AI回复失败: "+err.Error())
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, resp)
}

// GetConfig 获取AI配置
// @Title GetConfig
// @Description 获取AI配置信息
// @Success 200 {object} result.Result 成功
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ai/config [get]
func (c *AIController) GetConfig() {
	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 获取AI配置
	config, err := aiService.GetAIConfig()
	if err != nil {
		logs.Error("获取AI配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "获取AI配置失败: "+err.Error())
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, config)
}

// SaveConfig 保存AI配置
// @Title SaveConfig
// @Description 保存AI配置信息
// @Param   body     body    dto.AIConfigDTO  true   "AI配置参数"
// @Success 200 {object} result.Result 成功
// @Failure 400 {object} result.Result 请求参数错误
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ai/config [post]
func (c *AIController) SaveConfig() {
	// 解析请求体
	var config dto.AIConfigDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &config); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请求参数错误")
		return
	}

	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 保存AI配置
	if err := aiService.SaveAIConfig(&config); err != nil {
		logs.Error("保存AI配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "保存AI配置失败: "+err.Error())
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, "保存AI配置成功")
}

// RefreshCache 刷新AI配置缓存
// @Title RefreshCache
// @Description 刷新AI配置缓存
// @Success 200 {object} result.Result 成功
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ai/config/refresh [post]
func (c *AIController) RefreshCache() {
	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 刷新缓存
	if err := aiService.RefreshAIConfig(); err != nil {
		logs.Error("刷新AI配置缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "刷新AI配置缓存失败: "+err.Error())
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, "刷新AI配置缓存成功")
}
