/**
 * community_address_controller.go
 * 社区地址控制器
 *
 * 本文件实现了社区地址相关的API接口，包括地址的CRUD操作、树形结构获取、地址选择器等功能。
 */

package controllers

import (
	"context"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils/common"
)

// CommunityAddressController 社区地址控制器
type CommunityAddressController struct {
	web.Controller
	addressService services.CommunityAddressService
}

// Prepare 初始化控制器
func (c *CommunityAddressController) Prepare() {
	// 获取服务实例
	c.addressService = impl.NewCommunityAddressServiceImpl()
}

// ParseRequest 通用请求参数解析方法
func (c *CommunityAddressController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// CreateAddress 创建社区地址
// @Title 创建社区地址
// @Description 创建新的社区地址
// @Param body body dto.CreateCommunityAddressRequest true "地址信息"
// @Success 200 {object} result.Response "创建成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses [post]
func (c *CommunityAddressController) CreateAddress() {
	// 解析请求参数
	req := &dto.CreateCommunityAddressRequest{}
	err := c.ParseRequest(req)
	if err != nil {
		logs.Error("[CreateAddress] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 创建社区地址
	id, err := c.addressService.CreateAddress(context.Background(), req)
	if err != nil {
		logs.Error("[CreateAddress] 创建社区地址失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{
		"id": id,
	})
}

// UpdateAddress 更新社区地址
// @Title 更新社区地址
// @Description 更新社区地址信息
// @Param body body dto.UpdateCommunityAddressRequest true "地址信息"
// @Success 200 {object} result.Response "更新成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 404 {object} result.ErrorResponse "地址不存在"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses [put]
func (c *CommunityAddressController) UpdateAddress() {
	// 解析请求参数
	req := &dto.UpdateCommunityAddressRequest{}
	err := c.ParseRequest(req)
	if err != nil {
		logs.Error("[UpdateAddress] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 更新社区地址
	err = c.addressService.UpdateAddress(context.Background(), req)
	if err != nil {
		if err == result.ErrNotFound {
			result.HandleError(c.Ctx, result.ErrNotFound)
			return
		}
		logs.Error("[UpdateAddress] 更新社区地址失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeleteAddress 删除社区地址
// @Title 删除社区地址
// @Description 删除社区地址
// @Param id path string true "地址ID"
// @Success 200 {object} result.Response "删除成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 404 {object} result.ErrorResponse "地址不存在"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses/:id [delete]
func (c *CommunityAddressController) DeleteAddress() {
	// 获取地址ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[DeleteAddress] 参数错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 删除地址
	err = c.addressService.DeleteAddress(context.Background(), id)
	if err != nil {
		if err == result.ErrNotFound {
			result.HandleError(c.Ctx, result.ErrNotFound)
			return
		}
		logs.Error("[DeleteAddress] 删除社区地址失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// GetAddress 获取社区地址详情
// @Title 获取社区地址详情
// @Description 根据ID获取社区地址详情
// @Param id path string true "地址ID"
// @Success 200 {object} dto.CommunityAddressResponse "获取成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 404 {object} result.ErrorResponse "地址不存在"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses/:id [get]
func (c *CommunityAddressController) GetAddress() {
	// 获取地址ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[GetAddress] 参数错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取地址详情
	address, err := c.addressService.GetAddress(context.Background(), id)
	if err != nil {
		if err == result.ErrNotFound {
			result.HandleError(c.Ctx, result.ErrNotFound)
			return
		}
		logs.Error("[GetAddress] 获取社区地址详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, address)
}

// ListAddresses 获取社区地址列表
// @Title 获取社区地址列表
// @Description 分页获取社区地址列表
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param name query string false "地址名称"
// @Param level query int false "级别：1-小区，2-楼栋，3-单元"
// @Param parentId query int false "父级ID"
// @Param status query int false "状态：-1-全部，0-禁用，1-启用"
// @Success 200 {object} result.ResponseWithPagination "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses [get]
func (c *CommunityAddressController) ListAddresses() {
	// 解析查询参数
	req := &dto.CommunityAddressQueryRequest{}
	if err := c.Ctx.Input.Bind(req, ""); err != nil {
		logs.Error("[ListAddresses] 解析查询参数失败: %v", err)
	}

	// 添加调试日志
	logs.Info("[ListAddresses] 接收到的参数: page=%d, pageSize=%d, name=%s, level=%d, parentId=%d, status=%d", 
		req.Page, req.PageSize, req.Name, req.Level, req.ParentId, req.Status)

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}
	// 如果status没有设置，默认设置为-1（表示查询全部，但在服务层会默认查询启用状态）
	if req.Status == 0 && c.GetString("status") == "" {
		req.Status = -1
	}

	// 获取地址列表
	resp, err := c.addressService.ListAddresses(context.Background(), req)
	if err != nil {
		logs.Error("[ListAddresses] 获取社区地址列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	logs.Info("[ListAddresses] 查询结果: total=%d, items=%d", resp.Total, len(resp.Items))
	result.OKWithPagination(c.Ctx, resp.Items, resp.Total, req.Page, req.PageSize)
}

// GetAddressTree 获取社区地址树形结构
// @Title 获取社区地址树形结构
// @Description 获取社区地址的树形结构
// @Param parentId query int false "父级ID，默认为0表示获取顶级地址"
// @Success 200 {object} dto.CommunityAddressTreeResponse "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses/tree [get]
func (c *CommunityAddressController) GetAddressTree() {
	// 解析父级ID
	parentIdStr := c.GetString("parentId")
	parentId := int64(0) // 默认为0，表示获取顶级地址
	if parentIdStr != "" {
		var err error
		parentId, err = strconv.ParseInt(parentIdStr, 10, 64)
		if err != nil {
			logs.Error("[GetAddressTree] 参数错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam)
			return
		}
	}

	// 获取地址树
	tree, err := c.addressService.GetAddressTree(context.Background(), parentId)
	if err != nil {
		logs.Error("[GetAddressTree] 获取社区地址树形结构失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, tree)
}

// GetAddressOptions 获取地址选择器选项
// @Title 获取地址选择器选项
// @Description 获取地址选择器的级联选项数据
// @Success 200 {object} dto.CommunityAddressOptionsResponse "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses/options [get]
func (c *CommunityAddressController) GetAddressOptions() {
	// 获取地址选择器选项
	options, err := c.addressService.GetAddressOptions(context.Background())
	if err != nil {
		logs.Error("[GetAddressOptions] 获取地址选择器选项失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, options)
}

// GetAddressByParent 获取子地址列表
// @Title 获取子地址列表
// @Description 根据父级ID获取子地址列表
// @Param parentId path string true "父级ID"
// @Param level query int false "地址级别，不指定则获取所有子级"
// @Success 200 {array} dto.CommunityAddressResponse "获取成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses/parent/:parentId [get]
func (c *CommunityAddressController) GetAddressByParent() {
	// 获取父级ID
	parentIdStr := c.Ctx.Input.Param(":parentId")
	parentId, err := strconv.ParseInt(parentIdStr, 10, 64)
	if err != nil {
		logs.Error("[GetAddressByParent] 参数错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 解析级别参数
	levelStr := c.GetString("level")
	var addresses interface{}
	
	if levelStr != "" {
		// 获取指定级别的子地址
		level, err := strconv.Atoi(levelStr)
		if err != nil {
			logs.Error("[GetAddressByParent] 参数错误: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam)
			return
		}
		
		addressList, err := c.addressService.GetAddressByParentAndLevel(context.Background(), parentId, level)
		if err != nil {
			logs.Error("[GetAddressByParent] 获取子地址失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInternal)
			return
		}
		addresses = addressList
	} else {
		// 获取所有级别的子地址
		tree, err := c.addressService.GetAddressTree(context.Background(), parentId)
		if err != nil {
			logs.Error("[GetAddressByParent] 获取子地址失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInternal)
			return
		}
		addresses = tree
	}

	result.OK(c.Ctx, addresses)
}

// GetFullAddressInfo 获取完整地址信息
// @Title 获取完整地址信息
// @Description 根据小区ID、楼栋ID、单元ID获取完整地址信息
// @Param body body dto.SelectedCommunityAddressRequest true "已选地址信息"
// @Success 200 {object} dto.SelectedCommunityAddressResponse "获取成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 404 {object} result.ErrorResponse "地址不存在"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses/full-info [post]
func (c *CommunityAddressController) GetFullAddressInfo() {
	// 解析请求参数
	req := &dto.SelectedCommunityAddressRequest{}
	err := c.ParseRequest(req)
	if err != nil {
		logs.Error("[GetFullAddressInfo] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取完整地址信息
	info, err := c.addressService.GetFullAddressInfo(context.Background(), req)
	if err != nil {
		if err == result.ErrNotFound {
			result.HandleError(c.Ctx, result.ErrNotFound)
			return
		}
		logs.Error("[GetFullAddressInfo] 获取完整地址信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, info)
}

// RefreshCache 刷新地址缓存
// @Title 刷新地址缓存
// @Description 手动刷新社区地址缓存
// @Success 200 {object} result.Response "刷新成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /addresses/cache/refresh [post]
func (c *CommunityAddressController) RefreshCache() {
	// 刷新缓存
	err := c.addressService.RefreshCache(context.Background())
	if err != nil {
		logs.Error("[RefreshCache] 刷新社区地址缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{
		"message": "社区地址缓存已刷新",
	})
}
