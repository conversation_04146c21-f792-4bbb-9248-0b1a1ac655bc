/*
 * file_usage_config_controller.go
 * 文件用途配置控制器
 *
 * 本文件提供文件用途配置的管理接口
 */

package controllers

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
)

// FileUsageConfigController 文件用途配置控制器
type FileUsageConfigController struct {
	web.Controller
	fileUsageConfigService services.FileUsageConfigService
}

// Prepare 初始化
func (c *FileUsageConfigController) Prepare() {
	c.fileUsageConfigService = impl.NewFileUsageConfigServiceImpl()
}

// ParseRequest 解析请求参数
func (c *FileUsageConfigController) ParseRequest() {
	// 设置响应格式
	c.Ctx.Output.Header("Content-Type", "application/json; charset=utf-8")
}

// GetList 获取文件用途配置列表
// @Title 获取文件用途配置列表
// @Description 获取文件用途配置列表
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param usageCode query string false "用途代码"
// @Param usageName query string false "用途名称"
// @Param status query int false "状态"
// @Param sortField query string false "排序字段" default(sort_order)
// @Param sortOrder query string false "排序方式" default(asc)
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /list [get]
func (c *FileUsageConfigController) GetList() {
	c.ParseRequest()

	// 解析请求参数
	req := &dto.FileUsageConfigListRequest{
		Page:     1,
		PageSize: 10,
	}

	if page, err := c.GetInt("page"); err == nil && page > 0 {
		req.Page = page
	}
	if pageSize, err := c.GetInt("pageSize"); err == nil && pageSize > 0 {
		req.PageSize = pageSize
	}
	req.UsageCode = c.GetString("usageCode")
	req.UsageName = c.GetString("usageName")
	if status, err := c.GetInt("status"); err == nil {
		status8 := int8(status)
		req.Status = &status8
	}
	req.SortField = c.GetString("sortField")
	if req.SortField == "" {
		req.SortField = "sort_order"
	}
	req.SortOrder = c.GetString("sortOrder")
	if req.SortOrder == "" {
		req.SortOrder = "asc"
	}

	// 调用服务
	listResult, err := c.fileUsageConfigService.GetFileUsageConfigList(c.Ctx.Request.Context(), req)
	if err != nil {
		logs.Error("[GetFileUsageConfigList] 获取文件用途配置列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, listResult)
}

// GetDetail 获取文件用途配置详情
// @Title 获取文件用途配置详情
// @Description 获取文件用途配置详情
// @Param id path int true "配置ID"
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /:id [get]
func (c *FileUsageConfigController) GetDetail() {
	c.ParseRequest()

	// 解析ID参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[GetFileUsageConfigByID] 无效的ID参数: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	config, err := c.fileUsageConfigService.GetFileUsageConfigByID(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[GetFileUsageConfigByID] 获取文件用途配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if config == nil {
		logs.Error("[GetFileUsageConfigByID] 配置不存在: %d", id)
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	result.OK(c.Ctx, config)
}

// Save 保存文件用途配置
// @Title 保存文件用途配置
// @Description 保存文件用途配置
// @Param body body dto.SaveFileUsageConfigRequest true "配置信息"
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /save [post]
func (c *FileUsageConfigController) Save() {
	c.ParseRequest()

	// 解析请求体
	var req dto.SaveFileUsageConfigRequest
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		logs.Error("[SaveFileUsageConfig] 请求参数格式错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 参数验证
	if req.UsageCode == "" {
		logs.Error("[SaveFileUsageConfig] 用途代码不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}
	if req.UsageName == "" {
		logs.Error("[SaveFileUsageConfig] 用途名称不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 处理允许的文件类型
	if req.AllowedTypes != "" {
		// 去除空格并转换为小写
		types := strings.Split(req.AllowedTypes, ",")
		var cleanTypes []string
		for _, t := range types {
			t = strings.TrimSpace(strings.ToLower(t))
			if t != "" {
				cleanTypes = append(cleanTypes, t)
			}
		}
		req.AllowedTypes = strings.Join(cleanTypes, ",")
	}

	// 调用服务
	config, err := c.fileUsageConfigService.SaveFileUsageConfig(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("[SaveFileUsageConfig] 保存文件用途配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, config)
}

// Delete 删除文件用途配置
// @Title 删除文件用途配置
// @Description 删除文件用途配置
// @Param id path int true "配置ID"
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /:id [delete]
func (c *FileUsageConfigController) Delete() {
	c.ParseRequest()

	// 解析ID参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[DeleteFileUsageConfig] 无效的ID参数: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	err = c.fileUsageConfigService.DeleteFileUsageConfig(c.Ctx.Request.Context(), id)
	if err != nil {
		logs.Error("[DeleteFileUsageConfig] 删除文件用途配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]string{"message": "删除成功"})
}

// GetOptions 获取文件用途选项
// @Title 获取文件用途选项
// @Description 获取文件用途选项
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /options [get]
func (c *FileUsageConfigController) GetOptions() {
	c.ParseRequest()

	// 调用服务
	options, err := c.fileUsageConfigService.GetFileUsageOptions(c.Ctx.Request.Context())
	if err != nil {
		logs.Error("[GetFileUsageOptions] 获取文件用途选项失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, options)
}

// InitDefault 初始化默认配置
// @Title 初始化默认配置
// @Description 初始化默认文件用途配置
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /init [post]
func (c *FileUsageConfigController) InitDefault() {
	c.ParseRequest()

	// 调用服务
	err := c.fileUsageConfigService.InitDefaultFileUsages(c.Ctx.Request.Context())
	if err != nil {
		logs.Error("[InitDefaultFileUsages] 初始化默认文件用途配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]string{"message": "初始化成功"})
}

// RefreshCache 刷新缓存
// @Title 刷新缓存
// @Description 刷新文件用途配置缓存
// @Success 200 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /refresh [post]
func (c *FileUsageConfigController) RefreshCache() {
	c.ParseRequest()

	// 调用服务
	err := c.fileUsageConfigService.RefreshCache(c.Ctx.Request.Context())
	if err != nil {
		logs.Error("[RefreshCache] 刷新文件用途配置缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]string{"message": "缓存刷新成功"})
}

// CheckAndInitRequiredUsages 检查并初始化必需的文件用途配置
// @Title 检查并初始化必需的文件用途配置
// @Description 检查数据库中是否存在指定的文件用途配置，如果不存在则自动添加
// @Param body body []string true "需要检查的用途代码列表"
// @Success 200 {object} utils.Response
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /check-init [post]
func (c *FileUsageConfigController) CheckAndInitRequiredUsages() {
	c.ParseRequest()

	// 解析请求参数
	var usageCodes []string
	err := json.Unmarshal(c.Ctx.Input.RequestBody, &usageCodes)
	if err != nil {
		logs.Error("[CheckAndInitRequiredUsages] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 验证参数
	if len(usageCodes) == 0 {
		logs.Error("[CheckAndInitRequiredUsages] 用途代码列表不能为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 调用服务
	err = c.fileUsageConfigService.CheckAndInitRequiredUsages(c.Ctx.Request.Context(), usageCodes)
	if err != nil {
		logs.Error("[CheckAndInitRequiredUsages] 检查并初始化必需配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{
		"message": "检查并初始化完成",
		"checked_codes": usageCodes,
	})
}