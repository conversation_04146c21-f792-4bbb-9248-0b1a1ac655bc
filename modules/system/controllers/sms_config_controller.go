/**
 * sms_config_controller.go
 * 短信配置控制器
 *
 * 本文件实现了短信配置相关的API接口，包括获取配置、更新配置和刷新缓存等。
 */

package controllers

import (
	"context"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils/common"
)

// SmsConfigController 短信配置控制器
type SmsConfigController struct {
	web.Controller
	smsConfigService services.SmsConfigService
}

// Prepare 初始化控制器
func (c *SmsConfigController) Prepare() {
	// 获取服务实例
	c.smsConfigService = impl.NewSmsConfigServiceImpl()
}

// ParseRequest 通用请求参数解析方法
func (c *SmsConfigController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// GetConfig 获取短信配置
func (c *SmsConfigController) GetConfig() {
	// 获取短信配置
	config, err := c.smsConfigService.GetSmsConfig(context.Background())
	if err != nil {
		logs.Error("[GetConfig] 获取短信配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if config == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 转换为DTO，并处理敏感信息
	response := &dto.SmsConfigResponse{
		Id:                   config.Id,
		Provider:             config.Provider,
		ProviderText:         getSmsProviderText(config.Provider),
		AccessKey:            config.AccessKey,
		AccessSecret:         maskSensitiveInfo(config.AccessSecret), // 脱敏处理
		SignName:             config.SignName,
		TemplateCodeRegister: config.TemplateCodeRegister,
		TemplateCodeLogin:    config.TemplateCodeLogin,
		TemplateCodeResetPwd: config.TemplateCodeResetPwd,
		TemplateCodeNotice:   config.TemplateCodeNotice,
		DailyLimit:           config.DailyLimit,
		Status:               config.Status,
		StatusText:           getStatusText(config.Status),
		Remark:               config.Remark,
		CreatedAt:            config.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:            config.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	result.OK(c.Ctx, response)
}

// SaveConfig 保存短信配置
func (c *SmsConfigController) SaveConfig() {
	// 解析请求参数
	req := &dto.SaveSmsConfigRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[SaveConfig] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 转换为模型对象
	config := &models.SmsConfig{
		Id:                   req.Id,
		Provider:             req.Provider,
		AccessKey:            req.AccessKey,
		AccessSecret:         req.AccessSecret,
		SignName:             req.SignName,
		TemplateCodeRegister: req.TemplateCodeRegister,
		TemplateCodeLogin:    req.TemplateCodeLogin,
		TemplateCodeResetPwd: req.TemplateCodeResetPwd,
		TemplateCodeNotice:   req.TemplateCodeNotice,
		DailyLimit:           req.DailyLimit,
		Status:               req.Status,
		Remark:               req.Remark,
	}

	// 保存配置
	err := c.smsConfigService.SaveSmsConfig(context.Background(), config)
	if err != nil {
		logs.Error("[SaveConfig] 保存短信配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// RefreshCache 刷新短信配置缓存
func (c *SmsConfigController) RefreshCache() {
	err := c.smsConfigService.RefreshCache(context.Background())
	if err != nil {
		logs.Error("[RefreshCache] 刷新短信配置缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// 辅助函数，脱敏处理
func maskSensitiveInfo(input string) string {
	if len(input) <= 8 {
		return "********"
	}
	visible := len(input) / 4
	return input[:visible] + "********" + input[len(input)-visible:]
}

// 辅助函数，获取短信提供商文本
func getSmsProviderText(provider string) string {
	switch provider {
	case "aliyun":
		return "阿里云"
	case "tencent":
		return "腾讯云"
	case "yunpian":
		return "云片"
	case "custom":
		return "自定义"
	case "submail":
		return "赛邮"
	default:
		return "未知提供商"
	}
}

// 辅助函数，获取状态文本
func getStatusText(status int8) string {
	if status == 1 {
		return "启用"
	}
	return "禁用"
}
