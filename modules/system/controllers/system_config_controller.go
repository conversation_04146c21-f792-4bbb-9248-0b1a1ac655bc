/**
 * system_config_controller.go
 * 系统配置控制器
 *
 * 本文件实现了系统配置相关的API接口，包括系统配置的CRUD操作和系统基本信息查询等。
 */

package controllers

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils/common"
)

// SystemConfigController 系统配置控制器
type SystemConfigController struct {
	web.Controller
	systemConfigService services.SystemConfigService
}

// Prepare 初始化控制器
func (c *SystemConfigController) Prepare() {
	// 获取服务实例
	c.systemConfigService = impl.NewSystemConfigServiceImpl()
}

// ParseRequest 通用请求参数解析方法
func (c *SystemConfigController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// GetSystemInfo 获取系统基本信息
// @Title 获取系统基本信息
// @Description 获取网站名称、版本、Logo等系统基本信息
// @Success 200 {object} dto.SystemInfoResponse "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /info/basic [get]
func (c *SystemConfigController) GetSystemInfo() {
	// 获取系统信息所需的配置键
	configKeys := []string{
		models.ConfigKeySiteName,
		models.ConfigKeySiteVersion,
		models.ConfigKeyApiVersion,
		models.ConfigKeyLogo,
		models.ConfigKeyFavicon,
		models.ConfigKeyCopyright,
		models.ConfigKeyConfigVersion,
		models.ConfigKeyContactEmail,
		models.ConfigKeyContactPhone,
		models.ConfigKeyContactAddress,
		models.ConfigKeyRegion,
	}

	// 获取配置值
	configMap, err := c.systemConfigService.GetConfigsByKeys(context.Background(), configKeys)
	if err != nil {
		logs.Error("[GetSystemInfo] 获取系统基本信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 构建响应数据
	systemInfo := &dto.SystemInfoResponse{
		SiteName:      configMap[models.ConfigKeySiteName],
		SiteVersion:   configMap[models.ConfigKeySiteVersion],
		ApiVersion:    configMap[models.ConfigKeyApiVersion],
		SiteLogo:      configMap[models.ConfigKeyLogo],
		SiteFavicon:   configMap[models.ConfigKeyFavicon],
		Copyright:     configMap[models.ConfigKeyCopyright],
		ConfigVersion: configMap[models.ConfigKeyConfigVersion],
		Email:         configMap[models.ConfigKeyContactEmail],
		Phone:         configMap[models.ConfigKeyContactPhone],
		Address:       configMap[models.ConfigKeyContactAddress],
		Region:        configMap[models.ConfigKeyRegion],
		UpdatedAt:     time.Now(), // 当前时间作为最后更新时间
	}

	result.OK(c.Ctx, systemInfo)
}

// GetContactInfo 获取联系信息
// @Title 获取联系信息
// @Description 获取系统联系方式信息
// @Success 200 {object} dto.ContactInfoResponse "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /info/contact [get]
func (c *SystemConfigController) GetContactInfo() {
	// 获取联系信息所需的配置键
	configKeys := []string{
		models.ConfigKeyContactEmail,
		models.ConfigKeyContactPhone,
		models.ConfigKeyContactAddress,
		"contact_work_time", // 工作时间
		"contact_qq",        // QQ客服
		"contact_wechat",    // 微信客服
		"company_name",      // 公司名称
		"contact_longitude", // 经度
		"contact_latitude",  // 纬度
	}

	// 获取配置值
	configMap, err := c.systemConfigService.GetConfigsByKeys(context.Background(), configKeys)
	if err != nil {
		logs.Error("[GetContactInfo] 获取联系信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 构建响应数据
	contactInfo := &dto.ContactInfoResponse{
		Email:       configMap[models.ConfigKeyContactEmail],
		Phone:       configMap[models.ConfigKeyContactPhone],
		Address:     configMap[models.ConfigKeyContactAddress],
		WorkTime:    configMap["contact_work_time"],
		QQ:          configMap["contact_qq"],
		WeChat:      configMap["contact_wechat"],
		CompanyName: configMap["company_name"],
		Longitude:   configMap["contact_longitude"],
		Latitude:    configMap["contact_latitude"],
	}

	result.OK(c.Ctx, contactInfo)
}

// GetMaintenanceMode 获取维护模式状态
// @Title 获取维护模式状态
// @Description 获取系统维护模式的当前状态和相关信息
// @Success 200 {object} dto.MaintenanceModeResponse "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /maintenance [get]
func (c *SystemConfigController) GetMaintenanceMode() {
	// 获取维护模式相关配置
	configKeys := []string{
		models.ConfigKeyMaintenanceMode,
		models.ConfigKeyMaintenanceMessage,
		"maintenance_recovery_time", // 预计恢复时间
	}

	// 获取配置值
	configMap, err := c.systemConfigService.GetConfigsByKeys(context.Background(), configKeys)
	if err != nil {
		logs.Error("[GetMaintenanceMode] 获取维护模式状态失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 构建响应数据
	maintenanceMode := &dto.MaintenanceModeResponse{
		Enabled:               configMap[models.ConfigKeyMaintenanceMode] == "1",
		Message:               configMap[models.ConfigKeyMaintenanceMessage],
		EstimatedRecoveryTime: configMap["maintenance_recovery_time"],
	}

	result.OK(c.Ctx, maintenanceMode)
}

// UpdateMaintenanceMode 更新维护模式
// @Title 更新维护模式
// @Description 更新系统维护模式的状态和相关信息
// @Param body body dto.MaintenanceModeRequest true "维护模式信息"
// @Success 200 {object} result.Response "更新成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /maintenance [put]
func (c *SystemConfigController) UpdateMaintenanceMode() {
	// 解析请求参数
	req := &dto.MaintenanceModeRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[UpdateMaintenanceMode] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 参数验证
	valid := validation.Validation{}
	ok, err := valid.Valid(req)
	if err != nil {
		logs.Error("[UpdateMaintenanceMode] 验证参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}
	if !ok {
		for _, err := range valid.Errors {
			logs.Error("[UpdateMaintenanceMode] 参数错误: %s - %s", err.Key, err.Message)
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 更新维护模式状态
	enabledValue := "0"
	if req.Enabled {
		enabledValue = "1"
	}

	// 批量更新配置
	configs := []*models.SystemConfig{
		{
			ConfigKey:   models.ConfigKeyMaintenanceMode,
			ConfigValue: enabledValue,
			ConfigType:  "system",
			Description: "维护模式状态：0-关闭，1-开启",
			IsSystem:    1,
		},
		{
			ConfigKey:   models.ConfigKeyMaintenanceMessage,
			ConfigValue: req.Message,
			ConfigType:  "system",
			Description: "维护模式提示消息",
			IsSystem:    1,
		},
		{
			ConfigKey:   "maintenance_recovery_time",
			ConfigValue: req.EstimatedRecoveryTime,
			ConfigType:  "system",
			Description: "维护模式预计恢复时间",
			IsSystem:    1,
		},
	}

	// 依次更新或创建
	for _, config := range configs {
		existingConfig, err := c.systemConfigService.GetConfigByKey(context.Background(), config.ConfigKey)
		if err != nil {
			logs.Error("[UpdateMaintenanceMode] 获取配置失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInternal)
			return
		}

		if existingConfig == nil {
			// 创建新配置
			_, err = c.systemConfigService.CreateConfig(context.Background(), config)
		} else {
			// 更新现有配置
			existingConfig.ConfigValue = config.ConfigValue
			err = c.systemConfigService.UpdateConfig(context.Background(), existingConfig)
		}

		if err != nil {
			logs.Error("[UpdateMaintenanceMode] 更新配置失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInternal)
			return
		}
	}

	// 刷新缓存
	err = c.systemConfigService.RefreshCache(context.Background())
	if err != nil {
		logs.Error("[UpdateMaintenanceMode] 刷新缓存失败: %v", err)
	}

	result.OK(c.Ctx, map[string]interface{}{
		"message": "维护模式配置已更新",
	})
}

// GetConfig 获取单个系统配置
// @Title 获取系统配置
// @Description 根据ID获取单个系统配置
// @Param id path string true "配置ID"
// @Success 200 {object} dto.SystemConfigResponse "获取成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 404 {object} result.ErrorResponse "配置不存在"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs/:id [get]
func (c *SystemConfigController) GetConfig() {
	// 获取配置ID
	idStr := c.Ctx.Input.Param(":id")
	_, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[GetConfig] 参数错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取配置记录
	config, err := c.systemConfigService.GetConfigByKey(context.Background(), idStr)
	if err != nil {
		logs.Error("[GetConfig] 获取系统配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if config == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 构建响应数据
	response := &dto.SystemConfigResponse{
		Id:          config.Id,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		ConfigType:  config.ConfigType,
		Description: config.Description,
		Version:     config.Version,
		Status:      config.Status,
		IsSystem:    config.IsSystem,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}

	result.OK(c.Ctx, response)
}

// GetConfigByKey 根据键名获取系统配置
// @Title 根据键名获取系统配置
// @Description 根据配置键名获取系统配置
// @Param key path string true "配置键名"
// @Success 200 {object} dto.SystemConfigResponse "获取成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 404 {object} result.ErrorResponse "配置不存在"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs/key/:key [get]
func (c *SystemConfigController) GetConfigByKey() {
	// 获取配置键名
	key := c.Ctx.Input.Param(":key")
	if key == "" {
		logs.Error("[GetConfigByKey] 参数错误: 键名为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取配置记录
	config, err := c.systemConfigService.GetConfigByKey(context.Background(), key)
	if err != nil {
		logs.Error("[GetConfigByKey] 获取系统配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if config == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 构建响应数据
	response := &dto.SystemConfigResponse{
		Id:          config.Id,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		ConfigType:  config.ConfigType,
		Description: config.Description,
		Version:     config.Version,
		Status:      config.Status,
		IsSystem:    config.IsSystem,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}

	result.OK(c.Ctx, response)
}

// GetMultiConfigs 批量获取系统配置
// @Title 批量获取系统配置
// @Description 根据键名列表批量获取系统配置值
// @Param body body dto.GetConfigsByKeysRequest true "键名列表"
// @Success 200 {object} dto.MultiConfigResponse "获取成功"
// @Failure 400 {object} result.ErrorResponse "参数错误"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs/batch [post]
func (c *SystemConfigController) GetMultiConfigs() {
	// 解析请求参数
	req := &dto.GetConfigsByKeysRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[GetMultiConfigs] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 参数验证
	if len(req.Keys) == 0 {
		logs.Error("[GetMultiConfigs] 参数错误: keys为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取配置值
	configMap, err := c.systemConfigService.GetConfigsByKeys(context.Background(), req.Keys)
	if err != nil {
		logs.Error("[GetMultiConfigs] 批量获取系统配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 转换为interface{}类型的map，以支持多种数据类型
	configsInterface := make(map[string]interface{})
	for k, v := range configMap {
		// 尝试解析JSON值
		var jsonValue interface{}
		if json.Unmarshal([]byte(v), &jsonValue) == nil {
			configsInterface[k] = jsonValue
		} else {
			configsInterface[k] = v
		}
	}

	result.OK(c.Ctx, &dto.MultiConfigResponse{
		Configs: configsInterface,
	})
}

// ListConfigs 获取系统配置列表
// @Title 获取系统配置列表
// @Description 获取所有系统配置的键值对形式
// @Success 200 {object} map[string]string "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs [get]
func (c *SystemConfigController) ListConfigs() {
	// 获取所有配置
	allConfigs, err := c.systemConfigService.GetAllConfigs(context.Background())
	if err != nil {
		logs.Error("[ListConfigs] 获取系统配置列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回所有配置
	result.OK(c.Ctx, allConfigs)
}

// ListConfigsWithDetails 获取系统配置详细列表
// @Title 获取系统配置详细列表
// @Description 获取所有系统配置的详细信息，包括键名、值、类型、描述等
// @Param keyword query string false "搜索关键词"
// @Param status query int false "状态筛选：1-启用，0-禁用"
// @Param isSystem query int false "系统配置筛选：1-是，0-否"
// @Param category query string false "配置分类筛选，如system、app_ui、banner等"
// @Param page query int false "当前页码，默认为1"
// @Param page_size query int false "每页数量，默认为10"
// @Success 200 {object} result.ResponseWithPagination "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs/details [get]
func (c *SystemConfigController) ListConfigsWithDetails() {
	// 获取查询参数
	keyword := c.GetString("keyword")
	statusStr := c.GetString("status")
	isSystemStr := c.GetString("isSystem")
	category := c.GetString("category")

	// 获取分页参数
	pageStr := c.GetString("page")
	pageSizeStr := c.GetString("page_size")

	// 设置默认分页值
	currentPage := 1
	pageSize := 10

	// 解析分页参数
	if pageStr != "" {
		if pageVal, err := strconv.Atoi(pageStr); err == nil && pageVal > 0 {
			currentPage = pageVal
		}
	}

	if pageSizeStr != "" {
		if pageSizeVal, err := strconv.Atoi(pageSizeStr); err == nil && pageSizeVal > 0 {
			pageSize = pageSizeVal
		}
	}

	// 转换状态参数
	var status int8 = -1 // 默认值-1表示不筛选
	if statusStr != "" {
		statusVal, err := strconv.ParseInt(statusStr, 10, 8)
		if err == nil {
			status = int8(statusVal)
		}
	}

	// 转换系统配置参数
	var isSystem int8 = -1 // 默认值-1表示不筛选
	if isSystemStr != "" {
		isSystemVal, err := strconv.ParseInt(isSystemStr, 10, 8)
		if err == nil {
			isSystem = int8(isSystemVal)
		}
	}

	// 从数据库获取所有配置的完整信息
	configs, err := c.systemConfigService.GetAllConfigsWithDetails(context.Background(), keyword, status, isSystem, category)
	if err != nil {
		logs.Error("[ListConfigsWithDetails] 获取系统配置详细列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 构建响应数据列表
	responseList := make([]*dto.SystemConfigResponse, 0, len(configs))
	for _, config := range configs {
		responseList = append(responseList, &dto.SystemConfigResponse{
			Id:          config.Id,
			ConfigKey:   config.ConfigKey,
			ConfigValue: config.ConfigValue,
			ConfigType:  config.ConfigType,
			Category:    config.Category,
			Description: config.Description,
			Version:     config.Version,
			Status:      config.Status,
			IsSystem:    config.IsSystem,
			CreatedAt:   config.CreatedAt,
			UpdatedAt:   config.UpdatedAt,
		})
	}

	// 处理分页
	total := int64(len(responseList))

	// 计算分页数据
	startIndex := (currentPage - 1) * pageSize
	endIndex := startIndex + pageSize

	// 边界检查
	if startIndex >= len(responseList) {
		// 页码超出范围，返回空结果
		result.OKWithPagination(c.Ctx, []*dto.SystemConfigResponse{}, total, currentPage, pageSize)
		return
	}

	if endIndex > len(responseList) {
		endIndex = len(responseList)
	}

	// 获取当前页的数据
	pagedList := responseList[startIndex:endIndex]

	// 使用分页格式返回数据
	result.OKWithPagination(c.Ctx, pagedList, total, currentPage, pageSize)
}

// CreateConfig 创建系统配置
func (c *SystemConfigController) CreateConfig() {
	// 解析请求参数
	req := &dto.CreateSystemConfigRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[CreateConfig] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 转换为模型对象
	config := &models.SystemConfig{
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		ConfigType:  req.ConfigType,
		Category:    req.Category,
		Description: req.Description,
		Status:      req.Status,
		IsSystem:    req.IsSystem,
	}

	// 创建配置
	id, err := c.systemConfigService.CreateConfig(context.Background(), config)
	if err != nil {
		logs.Error("[CreateConfig] 创建系统配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]interface{}{
		"id": id,
	})
}

// UpdateConfig 更新系统配置
func (c *SystemConfigController) UpdateConfig() {
	// 解析请求参数
	req := &dto.UpdateSystemConfigRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[UpdateConfig] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 参数验证
	if req.Id <= 0 {
		logs.Error("[UpdateConfig] 参数错误: ID无效")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 转换为模型对象
	config := &models.SystemConfig{
		Id:          req.Id,
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		ConfigType:  req.ConfigType,
		Category:    req.Category,
		Description: req.Description,
		Status:      req.Status,
		IsSystem:    req.IsSystem,
	}

	// 更新配置
	err := c.systemConfigService.UpdateConfig(context.Background(), config)
	if err != nil {
		logs.Error("[UpdateConfig] 更新系统配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// UpdateConfigValue 更新配置值
func (c *SystemConfigController) UpdateConfigValue() {
	// 解析请求参数
	req := &dto.UpdateConfigValueRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[UpdateConfigValue] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 更新配置值
	err := c.systemConfigService.UpdateConfigValue(context.Background(), req.ConfigKey, req.ConfigValue)
	if err != nil {
		logs.Error("[UpdateConfigValue] 更新配置值失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeleteConfig 删除系统配置
func (c *SystemConfigController) DeleteConfig() {
	// 获取配置ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[DeleteConfig] 参数错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 删除配置
	err = c.systemConfigService.DeleteConfig(context.Background(), id)
	if err != nil {
		logs.Error("[DeleteConfig] 删除系统配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// GetCacheInfo 获取配置缓存信息
// @Title 获取配置缓存信息
// @Description 获取系统配置缓存的状态信息
// @Success 200 {object} dto.ConfigCacheInfoResponse "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs/cache [get]
func (c *SystemConfigController) GetCacheInfo() {
	// 从 Redis 获取缓存信息
	cacheData, err := common.GetRedisCacheInfo(models.SystemConfigAllCacheKey)
	if err != nil {
		logs.Error("[GetCacheInfo] 获取缓存信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 估算缓存项数量
	totalItems := 0
	if val, ok := cacheData["keys"]; ok {
		if keys, ok := val.([]string); ok {
			totalItems = len(keys)
		}
	}

	// 获取最后刷新时间
	lastRefreshTime := time.Now()
	if val, ok := cacheData["last_refresh"]; ok {
		if ts, ok := val.(int64); ok {
			lastRefreshTime = time.Unix(ts, 0)
		}
	}

	// 构建缓存信息响应
	cacheInfo := &dto.ConfigCacheInfoResponse{
		TotalItems:      totalItems,
		LastRefreshTime: lastRefreshTime,
		CacheEnabled:    true, // 假设缓存始终启用
	}

	result.OK(c.Ctx, cacheInfo)
}

// RefreshCache 刷新系统配置缓存
// @Title 刷新系统配置缓存
// @Description 手动刷新系统配置缓存
// @Success 200 {object} result.Response "刷新成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs/cache/refresh [post]
func (c *SystemConfigController) RefreshCache() {
	// 刷新缓存
	err := c.systemConfigService.RefreshCache(context.Background())
	if err != nil {
		logs.Error("[RefreshCache] 刷新系统配置缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, map[string]interface{}{
		"message": "系统配置缓存已刷新",
	})
}

// GetPublicConfig 获取公开的系统配置信息
// @Title 获取公开的系统配置信息
// @Description 根据键名获取公开的系统配置信息，如隐私协议、用户协议等
// @Param key path string true "配置键名，如privacy_policy"
// @Success 200 {object} map[string]interface{} "获取成功"
// @Failure 404 {object} result.ErrorResponse "配置不存在"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /info/:key [get]
func (c *SystemConfigController) GetPublicConfig() {
	// 获取配置键名
	key := c.Ctx.Input.Param(":key")
	if key == "" {
		logs.Error("[GetPublicConfig] 参数错误: 键名为空")
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取配置值
	value, err := c.systemConfigService.GetConfigValueByKey(context.Background(), key)
	if err != nil {
		logs.Error("[GetPublicConfig] 获取配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 如果配置不存在，返应404
	if value == "" {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 尝试解析JSON，如果是JSON格式则返回字典结构
	var jsonData interface{}
	if err := json.Unmarshal([]byte(value), &jsonData); err == nil {
		// JSON格式，返回结构化数据
		result.OK(c.Ctx, jsonData)
		return
	}

	// 非JSON格式，返回纯文本
	result.OK(c.Ctx, map[string]interface{}{
		"content": value,
		"key":     key,
	})
}

// GetUserConfigsWithDetails 获取用户可访问的系统配置详细列表
// @Title 获取用户可访问的系统配置详细列表
// @Description 获取用户可访问的系统配置详细信息，支持按类别筛选（如deliveryFee配送费配置）
// @Param query query string false "搜索关键词"
// @Param category query string false "配置分类筛选，如deliveryFee等"
// @Param page query int false "当前页码，默认为1"
// @Param page_size query int false "每页数量，默认为10"
// @Success 200 {object} result.ResponseWithPagination "获取成功"
// @Failure 500 {object} result.ErrorResponse "服务器内部错误"
// @router /configs/details [get]
func (c *SystemConfigController) GetUserConfigsWithDetails() {
	// 获取查询参数
	query := c.GetString("query")
	category := c.GetString("category")

	// 获取分页参数
	pageStr := c.GetString("page")
	pageSizeStr := c.GetString("page_size")

	// 设置默认分页值
	currentPage := 1
	pageSize := 10

	// 解析分页参数
	if pageStr != "" {
		if pageVal, err := strconv.Atoi(pageStr); err == nil && pageVal > 0 {
			currentPage = pageVal
		}
	}

	if pageSizeStr != "" {
		if pageSizeVal, err := strconv.Atoi(pageSizeStr); err == nil && pageSizeVal > 0 {
			pageSize = pageSizeVal
		}
	}

	// 从数据库获取用户可访问的配置信息
	// 只获取启用状态的、非系统内部配置
	configs, err := c.systemConfigService.GetAllConfigsWithDetails(context.Background(), query, 1, 0, category)
	if err != nil {
		logs.Error("[GetUserConfigsWithDetails] 获取用户配置详细列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 构建响应数据列表，只返回用户需要的字段
	responseList := make([]*dto.SystemConfigResponse, 0, len(configs))
	for _, config := range configs {
		// 只返回启用状态的配置
		if config.Status == 1 {
			responseList = append(responseList, &dto.SystemConfigResponse{
				Id:          config.Id,
				ConfigKey:   config.ConfigKey,
				ConfigValue: config.ConfigValue,
				ConfigType:  config.ConfigType,
				Category:    config.Category,
				Description: config.Description,
				Version:     config.Version,
				Status:      config.Status,
				IsSystem:    config.IsSystem,
				CreatedAt:   config.CreatedAt,
				UpdatedAt:   config.UpdatedAt,
			})
		}
	}

	// 处理分页
	total := int64(len(responseList))

	// 计算分页数据
	startIndex := (currentPage - 1) * pageSize
	endIndex := startIndex + pageSize

	// 边界检查
	if startIndex >= len(responseList) {
		// 页码超出范围，返回空结果
		result.OKWithPagination(c.Ctx, []*dto.SystemConfigResponse{}, total, currentPage, pageSize)
		return
	}

	if endIndex > len(responseList) {
		endIndex = len(responseList)
	}

	// 获取当前页的数据
	pagedList := responseList[startIndex:endIndex]

	// 使用分页格式返回数据
	result.OKWithPagination(c.Ctx, pagedList, total, currentPage, pageSize)
}
