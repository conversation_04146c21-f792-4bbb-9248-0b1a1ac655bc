/**
 * system_notice_controller.go
 * 系统公告控制器
 *
 * 本文件实现了系统公告相关的API接口，包括系统公告的CRUD操作和状态管理等。
 */

package controllers

import (
	"context"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils/common"
)

// SystemNoticeController 系统公告控制器
type SystemNoticeController struct {
	web.Controller
	systemNoticeService services.SystemNoticeService
}

// Prepare 初始化控制器
func (c *SystemNoticeController) Prepare() {
	// 获取服务实例
	c.systemNoticeService = impl.NewSystemNoticeServiceImpl()
}

// ParseRequest 通用请求参数解析方法
func (c *SystemNoticeController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// GetActiveNotices 获取当前有效的系统公告（前台接口）
func (c *SystemNoticeController) GetActiveNotices() {
	// 获取目标用户类型，默认为所有用户
	targetStr := c.GetString("target", "0")
	target, err := strconv.ParseInt(targetStr, 10, 8)
	if err != nil {
		target = 0 // 默认为所有用户
	}

	// 获取有效公告
	notices, err := c.systemNoticeService.GetActiveNotices(context.Background(), int8(target))
	if err != nil {
		logs.Error("[GetActiveNotices] 获取有效公告失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 转换为前台展示DTO
	var noticeList []dto.SystemNoticeBaseResponse
	for _, notice := range notices {
		noticeList = append(noticeList, dto.SystemNoticeBaseResponse{
			Id:        notice.Id,
			Title:     notice.Title,
			Content:   notice.Content,
			Type:      notice.Type,
			TypeText:  getNoticeTypeText(notice.Type),
			StartTime: notice.StartTime.Format(time.RFC3339),
			EndTime:   notice.EndTime.Format(time.RFC3339),
			IsTop:     notice.IsTop,
		})
	}

	result.OK(c.Ctx, noticeList)
}

// GetNotice 获取单个系统公告
func (c *SystemNoticeController) GetNotice() {
	// 获取公告ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[GetNotice] 参数错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取公告详情
	notice, err := c.systemNoticeService.GetNotice(context.Background(), id)
	if err != nil {
		logs.Error("[GetNotice] 获取公告详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if notice == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 转换为DTO
	response := dto.SystemNoticeResponse{
		Id:         notice.Id,
		Title:      notice.Title,
		Content:    notice.Content,
		Type:       notice.Type,
		TypeText:   getNoticeTypeText(notice.Type),
		StartTime:  notice.StartTime.Format(time.RFC3339),
		EndTime:    notice.EndTime.Format(time.RFC3339),
		Target:     notice.Target,
		TargetText: getNoticeTargetText(notice.Target),
		Status:     notice.Status,
		StatusText: getNoticeStatusText(notice.Status),
		IsTop:      notice.IsTop,
		CreatedBy:  notice.CreatedBy,
		CreatedAt:  notice.CreatedAt.Format(time.RFC3339),
		UpdatedAt:  notice.UpdatedAt.Format(time.RFC3339),
	}

	result.OK(c.Ctx, response)
}

// GetNoticeList 获取系统公告列表（管理后台接口）
func (c *SystemNoticeController) GetNoticeList() {
	// 解析分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 解析筛选条件
	title := c.GetString("title", "")
	typeStr := c.GetString("type", "-1")
	statusStr := c.GetString("status", "-1")
	targetStr := c.GetString("target", "-1")
	isTopStr := c.GetString("isTop", "-1")

	// 转换参数类型
	noticeType, _ := strconv.ParseInt(typeStr, 10, 8)
	status, _ := strconv.ParseInt(statusStr, 10, 8)
	target, _ := strconv.ParseInt(targetStr, 10, 8)
	isTop, _ := strconv.ParseInt(isTopStr, 10, 8)

	// 构建筛选条件
	filters := make(map[string]interface{})
	if title != "" {
		filters["title"] = title
	}
	if noticeType != -1 {
		filters["type"] = int8(noticeType)
	}
	if status != -1 {
		filters["status"] = int8(status)
	}
	if target != -1 {
		filters["target"] = int8(target)
	}
	if isTop != -1 {
		filters["is_top"] = int8(isTop)
	}

	// 获取公告列表
	notices, total, err := c.systemNoticeService.GetNoticesByPage(context.Background(), page, pageSize, filters)
	if err != nil {
		logs.Error("[GetNoticeList] 获取公告列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 转换为DTO
	var noticeList []dto.SystemNoticeResponse
	for _, notice := range notices {
		noticeList = append(noticeList, dto.SystemNoticeResponse{
			Id:         notice.Id,
			Title:      notice.Title,
			Content:    notice.Content,
			Type:       notice.Type,
			TypeText:   getNoticeTypeText(notice.Type),
			StartTime:  notice.StartTime.Format(time.RFC3339),
			EndTime:    notice.EndTime.Format(time.RFC3339),
			Target:     notice.Target,
			TargetText: getNoticeTargetText(notice.Target),
			Status:     notice.Status,
			StatusText: getNoticeStatusText(notice.Status),
			IsTop:      notice.IsTop,
			CreatedBy:  notice.CreatedBy,
			CreatedAt:  notice.CreatedAt.Format(time.RFC3339),
			UpdatedAt:  notice.UpdatedAt.Format(time.RFC3339),
		})
	}

	// 构建响应
	response := dto.SystemNoticesResponse{
		Items: noticeList,
		Total: total,
	}

	result.OK(c.Ctx, response)
}

// CreateNotice 创建系统公告
func (c *SystemNoticeController) CreateNotice() {
	// 解析请求参数
	req := &dto.CreateSystemNoticeRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[CreateNotice] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 转换为模型对象
	notice := &models.SystemNotice{
		Title:     req.Title,
		Content:   req.Content,
		Type:      req.Type,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Target:    req.Target,
		Status:    req.Status,
		IsTop:     req.IsTop,
		CreatedBy: c.GetAdminId(), // 获取当前管理员ID
	}

	// 创建公告
	id, err := c.systemNoticeService.CreateNotice(context.Background(), notice)
	if err != nil {
		logs.Error("[CreateNotice] 创建系统公告失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]interface{}{
		"id": id,
	})
}

// UpdateNotice 更新系统公告
func (c *SystemNoticeController) UpdateNotice() {
	// 解析请求参数
	req := &dto.UpdateSystemNoticeRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[UpdateNotice] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 获取原公告信息，验证是否存在
	notice, err := c.systemNoticeService.GetNotice(context.Background(), req.Id)
	if err != nil {
		logs.Error("[UpdateNotice] 获取原公告信息失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if notice == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 更新公告信息
	notice.Title = req.Title
	notice.Content = req.Content
	notice.Type = req.Type
	notice.StartTime = req.StartTime
	notice.EndTime = req.EndTime
	notice.Target = req.Target
	notice.Status = req.Status
	notice.IsTop = req.IsTop

	// 更新公告
	err = c.systemNoticeService.UpdateNotice(context.Background(), notice)
	if err != nil {
		logs.Error("[UpdateNotice] 更新系统公告失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// DeleteNotice 删除系统公告
func (c *SystemNoticeController) DeleteNotice() {
	// 获取公告ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logs.Error("[DeleteNotice] 参数错误: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 删除公告
	err = c.systemNoticeService.DeleteNotice(context.Background(), id)
	if err != nil {
		logs.Error("[DeleteNotice] 删除系统公告失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// UpdateNoticeStatus 更新公告状态
func (c *SystemNoticeController) UpdateNoticeStatus() {
	// 解析请求参数
	req := &dto.UpdateNoticeStatusRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[UpdateNoticeStatus] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 更新状态
	err := c.systemNoticeService.UpdateNoticeStatus(context.Background(), req.Id, req.Status)
	if err != nil {
		logs.Error("[UpdateNoticeStatus] 更新公告状态失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// UpdateNoticeTopStatus 更新公告置顶状态
func (c *SystemNoticeController) UpdateNoticeTopStatus() {
	// 解析请求参数
	req := &dto.UpdateNoticeTopStatusRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[UpdateNoticeTopStatus] 解析参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam)
		return
	}

	// 更新置顶状态
	err := c.systemNoticeService.UpdateNoticeTopStatus(context.Background(), req.Id, req.IsTop)
	if err != nil {
		logs.Error("[UpdateNoticeTopStatus] 更新公告置顶状态失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// RefreshCache 刷新系统公告缓存
func (c *SystemNoticeController) RefreshCache() {
	err := c.systemNoticeService.RefreshCache(context.Background())
	if err != nil {
		logs.Error("[RefreshCache] 刷新系统公告缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// GetAdminId 获取当前管理员ID
func (c *SystemNoticeController) GetAdminId() int64 {
	// 从JWT中获取管理员ID，此处简化处理
	adminId, _ := c.Ctx.Input.GetData("admin_id").(int64)
	return adminId
}

// 辅助函数，获取通知类型文本
func getNoticeTypeText(noticeType int8) string {
	switch noticeType {
	case 1:
		return "普通公告"
	case 2:
		return "紧急公告"
	case 3:
		return "维护公告"
	default:
		return "未知类型"
	}
}

// 辅助函数，获取通知目标用户文本
func getNoticeTargetText(target int8) string {
	switch target {
	case 0:
		return "所有用户"
	case 1:
		return "用户"
	case 2:
		return "商家"
	case 3:
		return "跑腿员"
	case 4:
		return "管理员"
	default:
		return "未知目标"
	}
}

// 辅助函数，获取通知状态文本
func getNoticeStatusText(status int8) string {
	switch status {
	case 0:
		return "未发布"
	case 1:
		return "已发布"
	default:
		return "未知状态"
	}
}
