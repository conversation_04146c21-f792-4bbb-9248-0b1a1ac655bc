/**
 * UI生成器控制器
 *
 * 本文件实现了UI生成器相关的控制器，用于根据数据模型生成前端UI配置，
 * 调用DeepSeek AI API实现智能UI配置生成。
 */

package controllers

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/services/impl"
)

// UIGeneratorController UI生成器控制器
type UIGeneratorController struct {
	web.Controller
}

// 默认模板配置
const (
	// ProTableUITemplate 默认生成ProTable UI配置的模板
	ProTableUITemplate = `你是一个专业的前端架构师，擅长根据后端数据模型定义生成ProTable和ProForm的完整配置。请严格按照示例格式生成包含以下内容的JSON配置：

1. serviceConfig为系统服务配置：
   - baseUrl: API基础路径 一般可以使用模型名称
   - customActions: 自定义操作配置
   - messages: 消息配置
   - addTitle: 新增标题
   - editTitle: 编辑标题
   - viewTitle: 查看标题
2. tableConfig为ProTable配置：
   - columns: 列配置
	  -基础示例为：[
					{
						label: '名称',
						prop: 'name',
						disabledHeaderFilter: true,
						tooltip: '名称',
						tableColumnProps: {
						align: 'center',
						showOverflowTooltip: true
						}
					},
					{
						label: '状态',
						prop: 'status',
						valueType: 'select',
						options: [
						{
							label: '未解决',
							value: '0',
							color: 'red'
						},
						{
							label: '已解决',
							value: '1',
							color: 'blue'
						},
						{
							label: '解决中',
							value: '2',
							color: 'yellow'
						},
						{
							label: '失败',
							value: '3',
							color: 'red'
						}
						]
					},
					{
						label: '标签',
						prop: 'tag',
						valueType: 'tag',
						fieldProps: (value: string) => {
						return { type: value }
						}
					},
					{
						label: '时间',
						prop: 'time',
						valueType: 'date-picker',
						tableColumnProps: {
						align: 'center',
						sortable: true
						}
					}
					]
      -columns中的 prop支持 x.y.z形式的 多（无限）级数据形式，示例为：prop: 'level.message.tip'。
      -配置项中valueType为select、radio或checkbox 时， 配置columns中的options，表格会自动显示 value 对应的状态和 label。默认的逻辑是 表格的tableData中的实际值和 options 中 value 对比 严格相等的话，会取当前项的 label 显示在表格中，想自定义显示逻辑的话，只需配置columns 中 customGetStatus即可，示例：
	  	{
			label: '状态',
			prop: 'status',
			valueType: 'select',
			options: [
			{
				label: '未解决',
				value: '0',
				type: 'primary'
			},
			{
				label: '已解决',
				value: '1',
				type: 'success'
			},
			{
				label: '解决中',
				value: '2',
				type: 'info'
			},
			{
				label: '失败',
				value: '3',
				type: 'danger'
			},
			{
				label: '审核中',
				value: '4',
				type: 'warning'
			}
			],
			customGetStatus: ({ options, value, row }) => {
			const data = options?.find(item => item.value === value && row.id === 0)
			return data
			}
		}
	  -配置columns中的 fieldProps，不仅会作用于表单单项，也会作用于表格单项。使用 fieldProps可以做简单的样式修改，更强大的自定义样式，请使用自定义表格项，示例：
	  {
			label: '标签',
			prop: 'tag',
			valueType: 'tag',
			fieldProps: (value: string, { index }) => {
			return {
				type: value,
				style: {
				fontSize: index * 2 + 13 + 'px'
				}
			}
			}
		},
		{
			label: '时间',
			prop: 'time',
			valueType: 'date-picker',
			fieldProps: (_, { index }) => {
			return {
				style: {
				color: 'green',
				fontSize: index * 2 + 13 + 'px'
				}
			}
			}
		}。

   - actions: 操作列配置
     -配置actionBar， actionBar 中的type 支持 button，icon和 link，对应element plus 的 ElButton，ElIcon 和 ElLink 组件。配置 confirm 即可实现二次确认。配置 show 即可实现权限和动态显示，示例
	 {
		// 查看
		text: '查看',
		code: 'view',
		props: (row: any) => ({
		type: 'info',
		disabled: row.status === '1'
		}),
		show: (row: any) => row.status === '1',
		icon: View
	},
	{
		// 修改
		text: '修改',
		code: 'edit',
		// props v0.1.16 版本新增函数类型
		props: (row: any) => ({
		type: 'primary',
		disabled: row.status === '1'
		}),
		show: computed(() => true),
		icon: Edit
	},
	{
		// 删除
		text: '删除',
		code: 'delete',
		// props v0.1.16 版本新增计算属性支持
		props: computed(() => ({ type: 'danger' })),
		confirm: {
		options: { draggable: true }
		},
		icon: Delete
	}。
	ActionBarButtonsRow支持以下事件。

		事件名	类型	触发说明
		onClickv0.1.8	ActionBarButtonsRow['onClick']	点击当前按钮的时触发，可与 PlusTable 的事件 clickAction 同时触发；操作需要二次确认时：PlusTable 的事件 clickAction会在确认时触发，而当前的 onClick 是在点击时触发；
		onConfirmv0.1.8	ActionBarButtonsRow['onConfirm']	操作需要二次确认时，点击确认时触发
		onCancelv0.1.8	ActionBarButtonsRow['onCancel']	操作需要二次确认时，点击取消时触发， 可与 PlusTable 的事件 clickActionConfirmCancel 同时触发
		示例：
		{
			// 修改
			text: '修改',
			code: 'edit',
			props: {
			type: 'primary'
			},
			//  show 字段控制权限
			show: () => permissionList.includes('sys.view'),
			onClick(params: ButtonsCallBackParams) {
			if (params?.formRefs) {
				// isEdit v0.1.8 新增
				const isEdit = params.formRefs[0].isEdit.value
				isEdit ? params.formRefs[0]?.stopCellEdit() : params.formRefs[0]?.startCellEdit()
			}
			}
		},
		{
			// 删除
			text: '删除',
			code: 'delete',
			props: {
			type: 'danger'
			},
			confirm: {
			options: { draggable: true }
			},
			onClick(params: ButtonsCallBackParams) {
			console.log(params, 'onClick')
			},
			onConfirm(params: ButtonsCallBackParams) {
			console.log(params, 'onConfirm')
			},
			onCancel(params: ButtonsCallBackParams) {
			console.log(params, 'onCancel')
			}
		}
   - pagination: 分页配置
   - showSelection: 是否显示选择列
   - showIndex: 是否显示序号列
   - showActions: 是否显示操作列
   - rowKey: 行键
   - showPagination: 是否显示分页
3. formConfig为ProForm配置：
   - columns: 列配置，valueType: 支持的组件类型为，注意text对应span组件，是不可编辑的
	[
		"autocomplete",
		"cascader",
		"checkbox",
		"color-picker",
		"date-picker",
		"select",
		"input-number",
		"textarea",
		"rate",
		"switch",
		"radio",
		"slider",
		"time-picker",
		"time-select",
		"text",
		"transfer",
		"divider",
		"tree-select",
		"plus-radio",
		"plus-date-picker",
		"plus-input-tag",
		"upload",
		"select-v2"
	]
   - labelWidth: 标签宽度
   - labelPosition: 标签位置
   - rules: 字段验证规则
   - width: 表单宽度
4. toolbarConfig为工具栏配置：
   - buttons: 按钮配置
5. searchConfig为搜索配置：
   - columns: 列配置同formConfig
6. infoConfig为查看详情弹窗配置：
   - columns: 列配置
   - border: 是否显示边框
   - column: 列数
   - size: 尺寸
   - direction: 方向
   - width: 宽度

转换规则：
- 带Email验证的字段转为邮箱输入
- Range验证生成select组件
- 时间字段用date-picker
- 数值范围验证转为min/max规则
- 头像字段用upload组件
- 金额字段用money组件
- 密码字段一般不需要在表格或info中显示
- 字段排序需要合理，并且在每项配置中保持一致
- 不是每项都适合搜索，修改searchConfig中的columns来调整搜索字段

组件规范：
1. 金额显示为money类型
2. 一般操作列宽度固定200px
3. 一般表单label右对齐80px，如果label过长，请适当加大labelWidth
4. 大弹窗宽度75%，可根据内容适当调整

参考示例结构：
{
  "version": "wsdfix",
  "title": "权限",
  "serviceConfig": {
    "baseUrl": "v1/admin/secured/permissions",
    "customActions": {
      "activate": {
        "url": "/status",
        "method": "put",
        "confirmMessage": "确定要激活该权限吗？",
        "successMessage": "权限已激活"
      },
      "disable": {
        "url": "/status",
        "method": "put",
        "confirmMessage": "确定要禁用该权限吗？",
        "successMessage": "权限已禁用"
      }
    },
    "messages": {
      "addSuccess": "权限创建成功",
      "updateSuccess": "权限更新成功",
      "deleteConfirm": "确定要删除该权限吗？",
      "deleteSuccess": "权限删除成功"
    },
    "addTitle": "新增权限",
    "editTitle": "编辑权限信息",
    "viewTitle": "查看权限信息"
  },
  "tableConfig": {
    "columns": [
      {
        "label": "权限编码",
        "prop": "code"
      },
      {
        "label": "描述",
        "prop": "description"
      },
      {
        "label": "权限ID",
        "prop": "id",
        "width": 80
      },
      {
        "label": "权限级别",
        "prop": "level",
        "width": 100
      },
      {
        "label": "权限名称",
        "prop": "name"
      },
      {
        "label": "父权限ID",
        "prop": "parent_id"
      },
      {
        "label": "权限路径",
        "prop": "path"
      },
      {
        "label": "状态",
        "prop": "status",
        "width": 100,
        "valueType": "tag",
        "valueEnum": {
          "0": {
            "text": "禁用",
            "status": "danger"
          },
          "1": {
            "text": "正常",
            "status": "success"
          }
        },
        "options": [
          {
            "label": "禁用",
            "value": 0
          },
          {
            "label": "正常",
            "value": 1
          }
        ]
      },
      {
        "label": "权限类型",
        "prop": "type"
      }
    ],
    "actions": {
      "title": "操作",
      "width": 200,
      "buttons": [
        {
          "text": "激活",
          "type": "success",
          "action": "activate"
        },
        {
          "text": "锁定",
          "type": "warning",
          "action": "lock"
        },
        {
          "text": "禁用",
          "type": "danger",
          "action": "disable"
        },
        {
          "text": "编辑",
          "type": "primary",
          "action": "edit"
        }
      ]
    },
    "pagination": {
      "pageSize": 15,
      "pageSizes": [
        10,
        20,
        30,
        50
      ]
    },
    "showSelection": true,
    "showIndex": true,
    "showActions": true,
    "rowKey": "id",
    "showPagination": true
  },
  "formConfig": {
    "columns": [
      {
        "label": "权限编码",
        "prop": "code",
        "fieldProps": {
          "placeholder": "请输入权限编码"
        },
        "required": true
      },
      {
        "label": "描述",
        "prop": "description",
        "valueType": "textarea",
        "fieldProps": {
          "placeholder": "请输入描述"
        }
      },
      {
        "label": "权限级别",
        "prop": "level",
        "valueType": "number",
        "fieldProps": {
          "placeholder": "请输入权限级别"
        },
        "required": true
      },
      {
        "label": "权限名称",
        "prop": "name",
        "fieldProps": {
          "placeholder": "请输入权限名称"
        },
        "required": true
      },
      {
        "label": "父权限ID",
        "prop": "parent_id",
        "fieldProps": {
          "placeholder": "请输入父权限ID"
        },
        "required": true,
        "valueType": "input-number",
        "defaultValue": "0"
      },
      {
        "label": "排序",
        "prop": "sort",
        "fieldProps": {
          "placeholder": "请输入排序"
        },
        "defaultValue": "0"
      },
      {
        "label": "状态",
        "prop": "status",
        "valueType": "radio",
        "options": [
          {
            "label": "禁用",
            "value": 0
          },
          {
            "label": "正常",
            "value": 1
          }
        ],
        "defaultValue": 0,
        "fieldProps": {
          "placeholder": "请输入状态：0-禁用，1-正常"
        },
        "required": true
      },
      {
        "label": "权限类型",
        "prop": "type",
        "fieldProps": {
          "placeholder": "请输入权限类型"
        },
        "required": true
      }
    ],
    "labelWidth": 110,
    "labelPosition": "right",
    "rules": {
      "code": [
        {
          "required": true,
          "message": "请输入权限编码",
          "trigger": "blur"
        }
      ],
      "level": [
        {
          "required": true,
          "message": "请输入权限级别",
          "trigger": "blur"
        }
      ],
      "name": [
        {
          "required": true,
          "message": "请输入权限名称",
          "trigger": "blur"
        }
      ],
      "parent_id": [
        {
          "required": true,
          "message": "请输入父权限ID",
          "trigger": "blur"
        }
      ],
      "status": [
        {
          "required": true,
          "message": "请输入状态",
          "trigger": "blur"
        }
      ],
      "type": [
        {
          "required": true,
          "message": "请输入权限类型",
          "trigger": "blur"
        }
      ]
    },
    "width": "75%"
  },
  "toolbarConfig": {
    "buttons": [
      {
        "text": "新增权限",
        "type": "primary",
        "icon": "Plus",
        "action": "add"
      },
      {
        "text": "刷新",
        "type": "primary",
        "icon": "Refresh",
        "action": "refresh"
      }
    ]
  },
  "searchConfig": {
    "columns": [
      {
        "label": "权限编码",
        "prop": "code"
      },
      {
        "label": "权限ID",
        "prop": "id"
      },
      {
        "label": "权限级别",
        "prop": "level"
      },
      {
        "label": "权限名称",
        "prop": "name"
      },
      {
        "label": "父权限ID",
        "prop": "parent_id"
      },
      {
        "label": "权限路径",
        "prop": "path"
      },
      {
        "label": "状态",
        "prop": "status",
        "valueType": "option",
        "options": [
          {
            "label": "禁用",
            "value": 0
          },
          {
            "label": "正常",
            "value": 1
          }
        ]
      },
      {
        "label": "权限类型",
        "prop": "type"
      }
    ]
  },
  "infoConfig": {
    "columns": [
      {
        "label": "权限编码",
        "prop": "code",
        "fieldProps": {
          "placeholder": "请输入权限编码"
        },
        "required": true
      },
      {
        "label": "描述",
        "prop": "description",
        "valueType": "textarea",
        "fieldProps": {
          "placeholder": "请输入描述"
        }
      },
      {
        "label": "权限级别",
        "prop": "level",
        "valueType": "number",
        "fieldProps": {
          "placeholder": "请输入权限级别"
        },
        "required": true
      },
      {
        "label": "权限名称",
        "prop": "name",
        "fieldProps": {
          "placeholder": "请输入权限名称"
        },
        "required": true
      },
      {
        "label": "父权限ID",
        "prop": "parent_id",
        "fieldProps": {
          "placeholder": "请输入父权限ID"
        },
        "required": true
      },
      {
        "label": "状态",
        "prop": "status",
        "valueType": "select",
        "options": [
          {
            "label": "禁用",
            "value": 0,
            "color": "red"
          },
          {
            "label": "正常",
            "value": 1,
            "color": "green"
          }
        ],
        "defaultValue": 0,
        "fieldProps": {
          "placeholder": "请输入状态：0-禁用，1-正常"
        },
        "required": true
      },
      {
        "label": "权限类型",
        "prop": "type",
        "fieldProps": {
          "placeholder": "请输入权限类型"
        },
        "required": true
      }
    ],
    "border": true,
    "column": 2,
    "size": "default",
    "direction": "horizontal",
    "width": "75%"
  }
}

`

	// AntdBasicTemplate Ant Design基础模板
	AntdBasicTemplate = `你是一个专业的前端架构师，擅长使用Ant Design组件体系。根据提供的数据模型，请生成一个完整的Ant Design配置对象，包含表格和表单配置。

数据展示规则：
1. 使用Ant Design的Table组件
2. 状态字段使用Badge组件
3. 图片字段支持预览
4. 时间字段格式化为YYYY-MM-DD HH:mm:ss
5. 操作列固定在右侧

表单规则：
1. 使用Form组件
2. 日期选择使用DatePicker
3. 多选使用Select组件
4. 文本域使用TextArea
5. 添加必要的表单验证

生成完整的JSON配置，确保严格遵循JSON语法，包含columns、formItems、actions等完整配置项。`

	// ElementUITemplate Element UI模板
	ElementUITemplate = `你是一个专业的前端架构师，擅长使用Element UI组件体系。根据提供的数据模型，请生成一个完整的Element UI配置对象，包含表格和表单配置。

数据展示规则：
1. 使用el-table组件
2. 状态字段使用tag组件
3. 图片字段支持预览
4. 时间字段格式化为YYYY-MM-DD HH:mm:ss
5. 表格支持分页和排序

表单规则：
1. 使用el-form组件
2. 日期选择使用el-date-picker
3. 多选使用el-select
4. 文本域使用el-input type="textarea"
5. 添加必要的表单验证rules

生成完整的JSON配置，确保严格遵循JSON语法，包含columns、formItems、actions等完整配置项。`
)

// GenerateUIConfig 生成UI配置
// @Title GenerateUIConfig
// @Description 生成前端UI配置
// @Param   body     body    dto.UIGeneratorRequestDTO  true   "UI生成请求参数"
// @Success 200 {object} result.Result 成功
// @Failure 400 {object} result.Result 请求参数错误
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ui/generate [post]
func (c *UIGeneratorController) GenerateUIConfig() {
	// 解析请求体
	var req dto.UIGeneratorRequestDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请求参数错误")
		return
	}

	// 检查必要参数
	if req.ModelData == nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "模型数据不能为空")
		return
	}

	// 将模型数据转换为字符串
	var modelDataStr string
	switch md := req.ModelData.(type) {
	case string:
		modelDataStr = md
	case map[string]interface{}, []interface{}:
		// 将对象转换为JSON字符串
		jsonBytes, err := json.MarshalIndent(md, "", "  ")
		if err != nil {
			logs.Error("转换模型数据为JSON失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "模型数据格式错误")
			return
		}
		modelDataStr = string(jsonBytes)
	default:
		// 尝试使用通用方法
		jsonBytes, err := json.MarshalIndent(md, "", "  ")
		if err != nil {
			logs.Error("转换未知类型模型数据失败: %v, 类型: %T", err, md)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "模型数据格式不支持")
			return
		}
		modelDataStr = string(jsonBytes)
	}

	// 使用默认模板 - ProTable
	template := ProTableUITemplate

	// 如果请求中指定了模板类型，则根据模板类型选择模板
	if req.TemplateType != "" {
		switch req.TemplateType {
		case "ant_design":
			template = AntdBasicTemplate
		case "element_ui":
			template = ElementUITemplate
		default:
			template = ProTableUITemplate
		}
	}

	// 准备发送给AI的消息列表
	messages := []dto.AIChatMessageDTO{
		{
			Role:    "system",
			Content: "你是一个专业的前端架构师，擅长根据后端数据模型定义生成前端UI配置。请严格按照要求输出JSON格式的配置。",
		},
		{
			Role:    "user",
			Content: fmt.Sprintf("%s\n\n[原始数据模型]\n%s", template, modelDataStr),
		},
	}

	// 准备AI请求参数
	aiReq := &dto.AIChatRequestDTO{
		Messages:    messages,
		Temperature: 0.3,  // 低温度以获得更加确定性的回答
		MaxTokens:   4000, // 设置足够大的token数以获取完整配置
	}

	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 调用AI聊天补全服务
	resp, err := aiService.GetChatCompletion(aiReq)
	if err != nil {
		logs.Error("获取AI聊天补全失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "生成UI配置失败: "+err.Error())
		return
	}

	// 处理AI响应
	if len(resp.Choices) == 0 {
		result.HandleError(c.Ctx, result.ErrInternal, "AI未返回有效回答")
		return
	}

	// 提取回答内容
	content := resp.Choices[0].Message.Content

	// 尝试提取JSON部分
	var config interface{}
	jsonContent := extractJSON(content)

	if jsonContent != "" {
		if err := json.Unmarshal([]byte(jsonContent), &config); err != nil {
			logs.Error("解析AI返回的JSON失败: %v", err)
			// 返回原始内容给前端进行进一步处理
			result.OK(c.Ctx, &dto.UIGeneratorResponseDTO{
				Config:     content,
				Messages:   []string{"AI返回的内容不是有效的JSON格式，已返回原始内容"},
				Status:     "partial",
				RawContent: content,
			})
			return
		}
	} else {
		// 未找到JSON，返回原始内容
		result.OK(c.Ctx, &dto.UIGeneratorResponseDTO{
			Config:     content,
			Messages:   []string{"未能从AI回答中提取JSON，已返回原始内容"},
			Status:     "raw",
			RawContent: content,
		})
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, &dto.UIGeneratorResponseDTO{
		Config: config,
		Status: "success",
	})
}

// GenerateCustomUIConfig 高级自定义UI配置生成
// @Title GenerateCustomUIConfig
// @Description 使用自定义模板生成前端UI配置，支持自定义规则、格式和要求
// @Param   body     body    dto.UIGeneratorRequestDTO  true   "UI生成请求参数"
// @Success 200 {object} result.Result 成功
// @Failure 400 {object} result.Result 请求参数错误
// @Failure 500 {object} result.Result 服务器内部错误
// @router /ui/generate/custom [post]
func (c *UIGeneratorController) GenerateCustomUIConfig() {
	// 解析请求体
	var req dto.UIGeneratorRequestDTO
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logs.Error("解析请求体失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请求参数错误")
		return
	}

	// 检查必要参数
	if req.ModelData == nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "模型数据不能为空")
		return
	}

	// 将模型数据转换为字符串
	var modelDataStr string
	switch md := req.ModelData.(type) {
	case string:
		modelDataStr = md
	case map[string]interface{}, []interface{}:
		// 将对象转换为JSON字符串
		jsonBytes, err := json.MarshalIndent(md, "", "  ")
		if err != nil {
			logs.Error("转换模型数据为JSON失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "模型数据格式错误")
			return
		}
		modelDataStr = string(jsonBytes)
	default:
		// 尝试使用通用方法
		jsonBytes, err := json.MarshalIndent(md, "", "  ")
		if err != nil {
			logs.Error("转换未知类型模型数据失败: %v, 类型: %T", err, md)
			result.HandleError(c.Ctx, result.ErrInvalidParam, "模型数据格式不支持")
			return
		}
		modelDataStr = string(jsonBytes)
	}

	// 准备发送给AI的消息列表
	messages := []dto.AIChatMessageDTO{}

	// 添加系统角色指令
	systemPrompt := "你是一个专业的前端UI配置生成助手，严格按照用户的要求生成JSON格式的UI配置。"

	// 根据请求中的UI框架类型调整系统提示
	if req.UIFramework != "" {
		systemPrompt += fmt.Sprintf(" 你擅长使用%s框架和相关组件库。", req.UIFramework)
	}

	// 添加组件类型信息
	if req.ComponentTypes != "" {
		systemPrompt += fmt.Sprintf(" 你将使用%s组件库生成配置。", req.ComponentTypes)
	}

	messages = append(messages, dto.AIChatMessageDTO{
		Role:    "system",
		Content: systemPrompt,
	})

	// 构建用户提示内容
	var userPrompt strings.Builder

	// 如果有自定义模板，添加到提示中
	if req.Template != "" {
		userPrompt.WriteString(req.Template)
		userPrompt.WriteString("\n\n")
	}

	// 添加提示规则
	if req.PromptRules != "" {
		userPrompt.WriteString("【组件选择和布局规则】\n")
		userPrompt.WriteString(req.PromptRules)
		userPrompt.WriteString("\n\n")
	}

	// 添加格式设置
	if req.FormatSettings != "" {
		userPrompt.WriteString("【输出格式要求】\n")
		userPrompt.WriteString(req.FormatSettings)
		userPrompt.WriteString("\n\n")
	}

	// 添加生成要求
	if req.Requirements != "" {
		userPrompt.WriteString("【功能和设计要求】\n")
		userPrompt.WriteString(req.Requirements)
		userPrompt.WriteString("\n\n")
	}

	// 添加示例配置
	if req.Examples != "" {
		userPrompt.WriteString("【参考示例】\n")
		userPrompt.WriteString(req.Examples)
		userPrompt.WriteString("\n\n")
	}

	// 添加特殊字段处理说明
	if len(req.SpecialFields) > 0 {
		userPrompt.WriteString("【特殊字段处理】\n以下字段需要特殊处理：\n")
		for _, field := range req.SpecialFields {
			userPrompt.WriteString("- " + field + "\n")
		}
		userPrompt.WriteString("\n")
	}

	// 添加数据模型
	userPrompt.WriteString("【原始数据模型】\n")
	userPrompt.WriteString(modelDataStr)

	// 检查是否有自定义的模板
	templateStr := userPrompt.String()

	// 模板中可能包含多轮对话，需要解析
	if multiTurnMessages, err := parseTemplateToMessages(templateStr, ""); err == nil && len(multiTurnMessages) > 0 {
		messages = append(messages, multiTurnMessages...)
	} else {
		// 否则，直接添加当前用户消息
		messages = append(messages, dto.AIChatMessageDTO{
			Role:    "user",
			Content: templateStr,
		})
	}

	// 准备AI请求参数
	aiReq := &dto.AIChatRequestDTO{
		Messages:    messages,
		Temperature: 0.2,  // 更低温度以获得更加确定性的回答
		MaxTokens:   4000, // 设置足够大的token数以获取完整配置
	}

	// 获取AI服务
	aiService := impl.NewAIServiceImpl()

	// 调用AI聊天补全服务
	resp, err := aiService.GetChatCompletion(aiReq)
	if err != nil {
		logs.Error("获取AI聊天补全失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "生成UI配置失败: "+err.Error())
		return
	}

	// 处理AI响应
	if len(resp.Choices) == 0 {
		result.HandleError(c.Ctx, result.ErrInternal, "AI未返回有效回答")
		return
	}

	// 提取回答内容
	content := resp.Choices[0].Message.Content

	// 尝试提取JSON部分
	var config interface{}
	jsonContent := extractJSON(content)

	if jsonContent != "" {
		if err := json.Unmarshal([]byte(jsonContent), &config); err != nil {
			logs.Error("解析AI返回的JSON失败: %v", err)
			// 返回原始内容给前端进行进一步处理
			result.OK(c.Ctx, &dto.UIGeneratorResponseDTO{
				Config:     content,
				Messages:   []string{"AI返回的内容不是有效的JSON格式，已返回原始内容"},
				Status:     "partial",
				RawContent: content,
			})
			return
		}
	} else {
		// 未找到JSON，返回原始内容
		result.OK(c.Ctx, &dto.UIGeneratorResponseDTO{
			Config:     content,
			Messages:   []string{"未能从AI回答中提取JSON，已返回原始内容"},
			Status:     "raw",
			RawContent: content,
		})
		return
	}

	// 返回成功结果
	result.OK(c.Ctx, &dto.UIGeneratorResponseDTO{
		Config:     config,
		Status:     "success",
		RawContent: content,
	})
}

// extractJSON 从文本中提取JSON部分
func extractJSON(text string) string {
	// 尝试查找最外层的花括号
	startIdx := strings.Index(text, "{")
	if startIdx == -1 {
		return ""
	}

	// 查找最后一个闭花括号
	endIdx := strings.LastIndex(text, "}")
	if endIdx == -1 || endIdx <= startIdx {
		return ""
	}

	// 提取JSON内容
	json := text[startIdx : endIdx+1]

	// 检查是否存在Markdown代码块标记，如果有则去掉
	json = strings.ReplaceAll(json, "```json", "")
	json = strings.ReplaceAll(json, "```", "")
	json = strings.TrimSpace(json)

	return json
}

// parseTemplateToMessages 将模板字符串解析成消息列表
func parseTemplateToMessages(template, modelData string) ([]dto.AIChatMessageDTO, error) {
	messages := []dto.AIChatMessageDTO{}

	// 分割模板，查找可能的多轮对话
	lines := strings.Split(template, "\n")

	var currentRole string
	var currentContent strings.Builder

	for _, line := range lines {
		// 检查是否是角色标记
		if strings.HasPrefix(line, "system:") {
			// 如果已经有内容，先保存当前消息
			if currentRole != "" && currentContent.Len() > 0 {
				messages = append(messages, dto.AIChatMessageDTO{
					Role:    currentRole,
					Content: currentContent.String(),
				})
				currentContent.Reset()
			}

			currentRole = "system"
			currentContent.WriteString(strings.TrimPrefix(line, "system:"))
		} else if strings.HasPrefix(line, "user:") {
			// 如果已经有内容，先保存当前消息
			if currentRole != "" && currentContent.Len() > 0 {
				messages = append(messages, dto.AIChatMessageDTO{
					Role:    currentRole,
					Content: currentContent.String(),
				})
				currentContent.Reset()
			}

			currentRole = "user"
			currentContent.WriteString(strings.TrimPrefix(line, "user:"))
		} else if strings.HasPrefix(line, "assistant:") {
			// 如果已经有内容，先保存当前消息
			if currentRole != "" && currentContent.Len() > 0 {
				messages = append(messages, dto.AIChatMessageDTO{
					Role:    currentRole,
					Content: currentContent.String(),
				})
				currentContent.Reset()
			}

			currentRole = "assistant"
			currentContent.WriteString(strings.TrimPrefix(line, "assistant:"))
		} else {
			// 继续当前消息内容
			if currentRole != "" {
				if currentContent.Len() > 0 {
					currentContent.WriteString("\n")
				}
				currentContent.WriteString(line)
			} else {
				// 如果还没有确定角色，默认为用户角色
				currentRole = "user"
				currentContent.WriteString(line)
			}
		}
	}

	// 添加最后一条消息
	if currentRole != "" && currentContent.Len() > 0 {
		// 如果是用户消息且提供了模型数据，添加到内容末尾
		if currentRole == "user" && modelData != "" {
			currentContent.WriteString("\n\n[原始数据模型]\n")
			currentContent.WriteString(modelData)
		}

		messages = append(messages, dto.AIChatMessageDTO{
			Role:    currentRole,
			Content: currentContent.String(),
		})
	}

	return messages, nil
}
