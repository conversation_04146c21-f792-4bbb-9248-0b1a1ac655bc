/**
 * upload_config_controller.go
 * 上传配置控制器
 *
 * 本文件实现了上传配置相关的API接口，包括获取配置、更新配置和刷新缓存等。
 */

package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils/common"
	// "o_mall_backend/utils/config" // Removed as it's unused
	"o_mall_backend/utils/storage"
	"strings"
)

// UploadConfigController 上传配置控制器
type UploadConfigController struct {
	web.Controller
	uploadConfigService services.UploadConfigService
}

// Prepare 初始化控制器
func (c *UploadConfigController) Prepare() {
	// 获取服务实例
	c.uploadConfigService = impl.NewUploadConfigServiceImpl()
}

// ParseRequest 通用请求参数解析方法
func (c *UploadConfigController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// GetConfig 获取上传配置
func (c *UploadConfigController) GetConfig() {
	// 获取上传配置
	config, err := c.uploadConfigService.GetUploadConfig(context.Background())
	if err != nil {
		logs.Error("[GetConfig] 获取上传配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	if config == nil {
		result.HandleError(c.Ctx, result.ErrNotFound)
		return
	}

	// 转换为DTO，并处理敏感信息
	response := &dto.UploadConfigResponse{
		Id:                config.Id,
		StorageMode:       config.StorageMode,
		MaxSize:           config.MaxSize,
		MaxSizeMB:         int(config.MaxSize / 1024 / 1024), // 转换为MB
		AllowedExtensions: config.AllowedExtensions,
		EnableCdn:         config.EnableCdn,
		CdnDomain:         config.CdnDomain,
		Status:            config.Status,
		StatusText:        getStatusText(config.Status),
		Remark:            config.Remark,
		CreatedAt:         config.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:         config.UpdatedAt.Format("2006-01-02 15:04:05"),
		Config:            &dto.ConfigResponse{},
	}

	// 根据存储模式加载相应的配置
	if config.Config != "" {
		switch config.StorageMode {
		case models.StorageModeLocal:
			// 加载本地存储配置
			localConfig, err := config.GetLocalConfig()
			if err == nil && localConfig != nil {
				response.Config.LocalPath = localConfig.LocalPath
			}

		case models.StorageModeOSS:
			// 加载阿里云OSS配置
			ossConfig, err := config.GetOssConfig()
			if err == nil && ossConfig != nil {
				response.Config.OssEndpoint = ossConfig.Endpoint
				response.Config.OssBucket = ossConfig.Bucket
				response.Config.OssAccessKey = ossConfig.AccessKey
				response.Config.OssAccessSecret = uploadMaskSensitiveInfo(ossConfig.AccessSecret) // 脱敏处理
				response.Config.OssDomain = ossConfig.Domain
			}

		case models.StorageModeCOS:
			// 加载腾讯云COS配置
			cosConfig, err := config.GetCosConfig()
			if err == nil && cosConfig != nil {
				response.Config.CosRegion = cosConfig.Region
				response.Config.CosBucket = cosConfig.Bucket
				response.Config.CosSecretId = cosConfig.SecretId
				response.Config.CosSecretKey = uploadMaskSensitiveInfo(cosConfig.SecretKey) // 脱敏处理
				response.Config.CosDomain = cosConfig.Domain
			}

		case models.StorageModeS3:
			// 加载AWS S3配置
			s3Config, err := config.GetS3Config()
			if err == nil && s3Config != nil {
				response.Config.S3Region = s3Config.Region
				response.Config.S3Bucket = s3Config.Bucket
				response.Config.S3AccessKey = s3Config.AccessKey
				response.Config.S3SecretKey = uploadMaskSensitiveInfo(s3Config.SecretKey) // 脱敏处理
				response.Config.S3Domain = s3Config.Domain
			}

		case models.StorageModeQiniu:
			// 加载七牛云存储配置
			qiniuModelConfig, err := config.GetQiniuConfig()
			if err == nil && qiniuModelConfig != nil {
				// if response.Config.QiniuConfig == nil {
				// 	response.Config.QiniuConfig = &dto.QiniuConfigData{}
				// }
				response.Config.AccessKey = qiniuModelConfig.AccessKey
				response.Config.SecretKey = uploadMaskSensitiveInfo(qiniuModelConfig.SecretKey)
				response.Config.Bucket = qiniuModelConfig.Bucket
				response.Config.Domain = qiniuModelConfig.Domain
				response.Config.Zone = qiniuModelConfig.Zone
				response.Config.UseHTTPS = qiniuModelConfig.UseHTTPS
			}
		}
	}

	// 加载CDN配置（如果启用）
	if config.EnableCdn == 1 {
		cdnConfig, err := config.GetCDNConfig()
		if err == nil && cdnConfig != nil {
			response.Config.CdnAccessKey = cdnConfig.AccessKey
			response.Config.CdnSecretKey = uploadMaskSensitiveInfo(cdnConfig.SecretKey) // 脱敏处理
		}
	}

	result.OK(c.Ctx, response)
}

// SaveConfig 保存上传配置
func (c *UploadConfigController) SaveConfig() {
	// 打印原始请求体用于调试
	dataBytes, err := ioutil.ReadAll(c.Ctx.Request.Body)
	if err != nil {
		logs.Error("[SaveConfig] 读取请求体失败: %v", err)
		result.HandleError(c.Ctx, err, "请求体读取失败")
		return
	}

	// 重置请求体，以便后续解析
	c.Ctx.Request.Body = ioutil.NopCloser(bytes.NewBuffer(dataBytes))

	// 打印请求体内容用于调试
	requestBody := string(dataBytes)
	logs.Info("[SaveConfig] 原始请求体: %s", requestBody)

	// 创建一个匿名结构体解析JSON，看看实际的字段结构
	var rawData map[string]interface{}
	if err := json.Unmarshal(dataBytes, &rawData); err != nil {
		logs.Error("[SaveConfig] 解析JSON失败: %v", err)
	} else {
		// 检查七牛云配置是否在根级别或在config对象中
		logs.Info("[SaveConfig] JSON结构分析:")
		if accessKey, ok := rawData["accessKey"].(string); ok {
			logs.Info("[SaveConfig] 根级别存在accessKey: %s", accessKey)
		}
		if bucket, ok := rawData["bucket"].(string); ok {
			logs.Info("[SaveConfig] 根级别存在bucket: %s", bucket)
		}
		// 检查config对象内容
		if configData, ok := rawData["config"].(map[string]interface{}); ok {
			logs.Info("[SaveConfig] config对象存在，内容: %+v", configData)

			// 查看是否存在qiniuConfig字段
			if qiniuConfig, ok := configData["qiniuConfig"].(map[string]interface{}); ok {
				logs.Info("[SaveConfig] 检测到qiniuConfig字段: %+v", qiniuConfig)
			} else {
				// 查看是否有无前缀的通用字段
				if accessKey, ok := configData["accessKey"].(string); ok {
					logs.Info("[SaveConfig] config对象中存在accessKey: %s", accessKey)
				}
			}
		}
	}

	// 重置请求体依然可以正常解析
	c.Ctx.Request.Body = ioutil.NopCloser(bytes.NewBuffer(dataBytes))

	req := &dto.SaveUploadConfigRequest{}
	if err := c.ParseRequest(req); err != nil {
		logs.Error("[SaveConfig] 请求解析失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 打印解析后的结构体
	logs.Info("[SaveConfig] 解析后的七牛云配置: AccessKey=%s, Bucket=%s, Zone=%s",
		req.Config.QiniuConfig.AccessKey, req.Config.QiniuConfig.Bucket, req.Config.QiniuConfig.Zone)

	// 创建模型对象的基本字段
	config := &models.UploadConfig{
		Id:                req.Id,
		StorageMode:       req.StorageMode,
		MaxSize:           req.MaxSize,
		AllowedExtensions: req.AllowedExtensions,
		EnableCdn:         req.EnableCdn,
		CdnDomain:         req.CdnDomain,
		Status:            req.Status,
		Remark:            req.Remark,
	}

	// 根据存储模式设置对应的配置
	switch req.StorageMode {
	case models.StorageModeLocal:
		// 设置本地存储配置
		if req.Config.LocalPath != "" {
			localConfig := &models.LocalConfig{
				LocalPath: req.Config.LocalPath,
			}
			if err := config.SetLocalConfig(localConfig); err != nil {
				logs.Error("[SaveConfig] 设置本地存储配置失败: %v", err)
				result.HandleError(c.Ctx, result.ErrInternal)
				return
			}
		}

	case models.StorageModeOSS:
		// 设置阿里云OSS配置
		if req.Config.OssEndpoint != "" && req.Config.OssBucket != "" {
			ossConfig := &models.OssConfig{
				Endpoint:     req.Config.OssEndpoint,
				Bucket:       req.Config.OssBucket,
				AccessKey:    req.Config.OssAccessKey,
				AccessSecret: req.Config.OssAccessSecret,
				Domain:       req.Config.OssDomain,
			}
			if err := config.SetOssConfig(ossConfig); err != nil {
				logs.Error("[SaveConfig] 设置阿里云OSS配置失败: %v", err)
				result.HandleError(c.Ctx, result.ErrInternal)
				return
			}
		}

	case models.StorageModeCOS:
		// 设置腾讯云COS配置
		if req.Config.CosRegion != "" && req.Config.CosBucket != "" {
			cosConfig := &models.CosConfig{
				Region:    req.Config.CosRegion,
				Bucket:    req.Config.CosBucket,
				SecretId:  req.Config.CosSecretId,
				SecretKey: req.Config.CosSecretKey,
				Domain:    req.Config.CosDomain,
			}
			if err := config.SetCosConfig(cosConfig); err != nil {
				logs.Error("[SaveConfig] 设置腾讯云COS配置失败: %v", err)
				result.HandleError(c.Ctx, result.ErrInternal)
				return
			}
		}

	case models.StorageModeS3:
		// 设置AWS S3配置
		if req.Config.S3Region != "" && req.Config.S3Bucket != "" {
			s3Config := &models.S3Config{
				Region:    req.Config.S3Region,
				Bucket:    req.Config.S3Bucket,
				AccessKey: req.Config.S3AccessKey,
				SecretKey: req.Config.S3SecretKey,
				Domain:    req.Config.S3Domain,
			}
			if err := config.SetS3Config(s3Config); err != nil {
				logs.Error("[SaveConfig] 设置AWS S3配置失败: %v", err)
				result.HandleError(c.Ctx, result.ErrInternal)
				return
			}
		}

	case models.StorageModeQiniu:
		// 设置七牛云存储配置
		// 使用新的QiniuConfig字段
		if req.Config.QiniuConfig.AccessKey != "" && req.Config.QiniuConfig.Bucket != "" {
			// 使用QiniuConfig中的字段
			qiniuConfig := &models.QiniuConfig{
				AccessKey: req.Config.QiniuConfig.AccessKey,
				SecretKey: req.Config.QiniuConfig.SecretKey,
				Bucket:    req.Config.QiniuConfig.Bucket,
				Domain:    req.Config.QiniuConfig.Domain,
				Zone:      req.Config.QiniuConfig.Zone,
				UseHTTPS:  req.Config.QiniuConfig.UseHTTPS,
			}
			if err := config.SetQiniuConfig(qiniuConfig); err != nil {
				logs.Error("[SaveConfig] 设置七牛云存储配置失败: %v", err)
				result.HandleError(c.Ctx, result.ErrInternal)
				return
			}
			logs.Info("[SaveConfig] 使用QiniuConfig配置读取七牛云配置成功: 空间=%s, 区域=%s, 域名=%s",
				req.Config.QiniuConfig.Bucket, req.Config.QiniuConfig.Zone, req.Config.QiniuConfig.Domain)
		} else {
			logs.Error("[SaveConfig] 设置七牛云存储配置失败: 配置信息不完整")
			result.HandleError(c.Ctx, result.ErrInvalidParam)
			return
		}
	}

	// 设置CDN配置（如果启用）
	if req.EnableCdn == 1 && req.Config.CdnAccessKey != "" {
		cdnConfig := &models.CDNConfig{
			Domain:    req.CdnDomain, // 使用主配置中的CDN域名
			AccessKey: req.Config.CdnAccessKey,
			SecretKey: req.Config.CdnSecretKey,
		}
		if err := config.SetCDNConfig(cdnConfig); err != nil {
			logs.Error("[SaveConfig] 设置CDN配置失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInternal)
			return
		}
	}

	// 保存配置
	err = c.uploadConfigService.SaveUploadConfig(context.Background(), config)
	if err != nil {
		logs.Error("[SaveConfig] 保存上传配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// RefreshCache 刷新上传配置缓存
func (c *UploadConfigController) RefreshCache() {
	err := c.uploadConfigService.RefreshCache(context.Background())
	if err != nil {
		logs.Error("[RefreshCache] 刷新上传配置缓存失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal)
		return
	}

	result.OK(c.Ctx, nil)
}

// 辅助函数，脱敏处理
// TestConnection 测试上传连接并返回上传参数
// @router /upload/test-connection [post]
func (c *UploadConfigController) TestConnection() {
	// Attempt to initialize storage to get the latest status
	storage.Init()

	dbConfig, err := c.uploadConfigService.GetUploadConfig(context.Background())
	if err != nil {
		logs.Error("[TestConnection] 获取上传配置失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternal, "获取上传配置失败")
		return
	}
	if dbConfig == nil {
		logs.Warn("[TestConnection] 上传配置未找到")
		result.HandleError(c.Ctx, result.ErrNotFound, "上传配置未找到或未启用")
		return
	}

	resp := dto.TestConnectionAPIResponse{
		StorageMode:        dbConfig.StorageMode,
		MaxFileSizeMB:      dbConfig.MaxSize / 1024 / 1024,
		UploadEndpoint:     "/api/v1/files/upload", // Placeholder, replace with actual upload endpoint
		StorageInitialized: storage.IsProviderInitialized(),
	}

	// Parse AllowedExtensions string into a slice
	var allowedTypes []string
	if dbConfig.AllowedExtensions != "" {
		types := strings.Split(dbConfig.AllowedExtensions, ",")
		for _, t := range types {
			trimmedType := strings.TrimSpace(t)
			if trimmedType != "" {
				allowedTypes = append(allowedTypes, trimmedType)
			}
		}
	}
	resp.AllowedFileTypes = allowedTypes

	// Populate storage-specific configuration (masked)
	// This logic is similar to GetConfig method
	storageConf := &dto.ConfigResponse{}
	if dbConfig.Config != "" {
		switch dbConfig.StorageMode {
		case models.StorageModeLocal:
			localConfig, _ := dbConfig.GetLocalConfig()
			if localConfig != nil {
				storageConf.LocalPath = localConfig.LocalPath
			}
		case models.StorageModeOSS:
			ossConfig, _ := dbConfig.GetOssConfig()
			if ossConfig != nil {
				storageConf.OssEndpoint = ossConfig.Endpoint
				storageConf.OssBucket = ossConfig.Bucket
				storageConf.OssAccessKey = ossConfig.AccessKey // Typically public
				storageConf.OssAccessSecret = uploadMaskSensitiveInfo(ossConfig.AccessSecret)
				storageConf.OssDomain = ossConfig.Domain
			}
		case models.StorageModeCOS:
			cosConfig, _ := dbConfig.GetCosConfig()
			if cosConfig != nil {
				storageConf.CosRegion = cosConfig.Region
				storageConf.CosBucket = cosConfig.Bucket
				storageConf.CosSecretId = cosConfig.SecretId // Typically public
				storageConf.CosSecretKey = uploadMaskSensitiveInfo(cosConfig.SecretKey)
				storageConf.CosDomain = cosConfig.Domain
			}
		case models.StorageModeS3:
			s3Config, _ := dbConfig.GetS3Config()
			if s3Config != nil {
				storageConf.S3Region = s3Config.Region
				storageConf.S3Bucket = s3Config.Bucket
				storageConf.S3AccessKey = s3Config.AccessKey // Typically public
				storageConf.S3SecretKey = uploadMaskSensitiveInfo(s3Config.SecretKey)
				storageConf.S3Domain = s3Config.Domain
			}
		case models.StorageModeQiniu:
			qiniuModelConfig, _ := dbConfig.GetQiniuConfig() // from models.UploadConfig
			if qiniuModelConfig != nil {
				storageConf.AccessKey = qiniuModelConfig.AccessKey
				storageConf.SecretKey = uploadMaskSensitiveInfo(qiniuModelConfig.SecretKey)
				storageConf.Bucket = qiniuModelConfig.Bucket
				storageConf.Domain = qiniuModelConfig.Domain
				storageConf.Zone = qiniuModelConfig.Zone
				storageConf.UseHTTPS = qiniuModelConfig.UseHTTPS
			}
		}
	}
	resp.StorageConfig = storageConf

	if resp.StorageInitialized {
		resp.Message = "存储服务已配置并初始化成功。"
	} else {
		resp.Message = "存储服务配置可能不完整或初始化失败，请检查系统日志和存储配置。"
	}

	result.OK(c.Ctx, resp)
}

// 辅助函数，脱敏处理
func uploadMaskSensitiveInfo(input string) string {
	if len(input) <= 8 {
		return "********"
	}
	visible := len(input) / 4
	return input[:visible] + "********" + input[len(input)-visible:]
}
