```markdown
# 社区地址 (CommunityAddress) 功能前端开发指南

## 1. 简介

本文档为前端开发人员提供与后端社区地址 (CommunityAddress) 模块交互的详细指南。社区地址模块用于管理系统中的地理位置信息，特别是针对小区、楼栋、单元等层级结构化地址数据。它支持地址的增删改查、树形结构展示、以及便捷的地址选择功能，并包含经纬度信息。

## 2. API 接口文档

**基础路径**: (请根据实际路由配置填写，例如 `/api/system` 或 `/admin/api/system`)

### 2.1 创建社区地址

创建新的社区地址记录。

- **URL**: `{基础路径}/addresses`
- **Method**: `POST`
- **认证**: 需要 (例如：管理员Token)
- **Content-Type**: `application/json`

**请求参数 (`dto.CreateCommunityAddressRequest`)**:

| 字段名        | 类型    | 是否必须 | 描述                                   | 示例值                     |
| ------------- | ------- | -------- | -------------------------------------- | -------------------------- |
| `name`        | string  | 是       | 名称                                   | `幸福小区`                 |
| `parentId`    | int64   | 否       | 父级ID (0表示顶级，例如小区)           | `0`                        |
| `level`       | int     | 是       | 级别：1-小区，2-楼栋，3-单元             | `1`                        |
| `longitude`   | float64 | 是       | 经度                                   | `116.404000`               |
| `latitude`    | float64 | 是       | 纬度                                   | `39.915000`                |
| `sort`        | int     | 否       | 排序 (数字越小越靠前)                  | `10`                       |
| `status`      | int8    | 否       | 状态：`1`-启用 (默认)，`0`-禁用        | `1`                        |
| `description` | string  | 否       | 描述                                   | `这是一个环境优美的小区`   |

**示例请求**:

```json
{
  "name": "幸福小区",
  "parentId": 0,
  "level": 1,
  "longitude": 116.404000,
  "latitude": 39.915000,
  "sort": 10,
  "status": 1,
  "description": "这是一个环境优美的小区"
}
```

**响应**:

成功时，返回包含新创建地址ID的数据。

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 123
  }
}
```

### 2.2 更新社区地址

更新已存在的社区地址信息。

- **URL**: `{基础路径}/addresses`
- **Method**: `PUT`
- **认证**: 需要
- **Content-Type**: `application/json`

**请求参数 (`dto.UpdateCommunityAddressRequest`)**:

| 字段名        | 类型    | 是否必须 | 描述                                   | 示例值                     |
| ------------- | ------- | -------- | -------------------------------------- | -------------------------- |
| `id`          | int64   | 是       | 地址ID (要更新的记录ID)                | `123`                      |
| `name`        | string  | 是       | 名称                                   | `幸福小区 (东区)`          |
| `parentId`    | int64   | 否       | 父级ID                                 | `0`                        |
| `level`       | int     | 是       | 级别：1-小区，2-楼栋，3-单元             | `1`                        |
| `longitude`   | float64 | 是       | 经度                                   | `116.405000`               |
| `latitude`    | float64 | 是       | 纬度                                   | `39.916000`                |
| `sort`        | int     | 否       | 排序                                   | `5`                        |
| `status`      | int8    | 否       | 状态：`1`-启用，`0`-禁用                | `1`                        |
| `description` | string  | 否       | 描述                                   | `更新了小区信息`           |

**示例请求**:

```json
{
  "id": 123,
  "name": "幸福小区 (东区)",
  "parentId": 0,
  "level": 1,
  "longitude": 116.405000,
  "latitude": 39.916000,
  "sort": 5,
  "status": 1,
  "description": "更新了小区信息"
}
```

**响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 2.3 删除社区地址

删除指定的社区地址。注意：如果地址存在子级地址，可能需要先删除子级或后端有特殊处理逻辑。

- **URL**: `{基础路径}/addresses/{id}`
- **Method**: `DELETE`
- **认证**: 需要

**路径参数**:

| 参数 | 类型  | 描述   |
| ---- | ----- | ------ |
| `id` | int64 | 地址ID |

**响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

### 2.4 获取社区地址详情

根据ID获取单个社区地址的详细信息。

- **URL**: `{基础路径}/addresses/{id}`
- **Method**: `GET`
- **认证**: 需要

**路径参数**:

| 参数 | 类型  | 描述   |
| ---- | ----- | ------ |
| `id` | int64 | 地址ID |

**响应参数 (`dto.CommunityAddressResponse`)**:

| 字段名        | 类型      | 描述                                   | 示例值                     |
| ------------- | --------- | -------------------------------------- | -------------------------- |
| `id`          | int64     | 地址ID                                 | `123`                      |
| `name`        | string    | 名称                                   | `幸福小区`                 |
| `parentId`    | int64     | 父级ID                                 | `0`                        |
| `level`       | int       | 级别：1-小区，2-楼栋，3-单元             | `1`                        |
| `longitude`   | float64   | 经度                                   | `116.404000`               |
| `latitude`    | float64   | 纬度                                   | `39.915000`                |
| `fullPath`    | string    | 完整路径 (例如：幸福小区/1号楼/1单元)    | `幸福小区`                 |
| `sort`        | int       | 排序                                   | `10`                       |
| `status`      | int8      | 状态：`1`-启用，`0`-禁用                | `1`                        |
| `description` | string    | 描述                                   | `这是一个环境优美的小区`   |
| `createdAt`   | time.Time | 创建时间                               | `2023-10-01T10:00:00Z`      |
| `updatedAt`   | time.Time | 更新时间                               | `2023-10-01T12:00:00Z`      |
| `children`    | array     | 子地址列表 (在获取树形结构时可能填充)    | `[]`                       |

**示例响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 123,
    "name": "幸福小区",
    "parentId": 0,
    "level": 1,
    "longitude": 116.404000,
    "latitude": 39.915000,
    "fullPath": "幸福小区",
    "sort": 10,
    "status": 1,
    "description": "这是一个环境优美的小区",
    "createdAt": "2023-10-01T10:00:00Z",
    "updatedAt": "2023-10-01T12:00:00Z",
    "children": []
  }
}
```

### 2.5 获取社区地址列表 (分页)

分页获取社区地址列表，支持按名称、级别、父ID和状态进行筛选。

- **URL**: `{基础路径}/addresses`
- **Method**: `GET`
- **认证**: 需要

**查询参数 (`dto.CommunityAddressQueryRequest`)**:

| 字段名     | 类型   | 是否必须 | 描述                                   | 示例值     |
| ---------- | ------ | -------- | -------------------------------------- | ---------- |
| `page`     | int    | 否       | 页码 (默认 1)                          | `1`        |
| `pageSize` | int    | 否       | 每页数量 (默认 10，最大 100)           | `10`       |
| `name`     | string | 否       | 地址名称 (模糊匹配)                    | `幸福`     |
| `level`    | int    | 否       | 级别：1-小区，2-楼栋，3-单元             | `1`        |
| `parentId` | int64  | 否       | 父级ID                                 | `0`        |
| `status`   | int8   | 否       | 状态：`-1`-全部, `0`-禁用, `1`-启用     | `1`        |

**响应 (`result.ResponseWithPagination` wrapping `dto.CommunityAddressResponse`)**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "items": [
      {
        "id": 123,
        "name": "幸福小区",
        "parentId": 0,
        "level": 1,
        // ... 其他字段
      }
      // ...更多地址项
    ],
    "total": 25,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2.6 获取社区地址树形结构

获取社区地址的完整树形结构，或从指定父级ID开始的子树。

- **URL**: `{基础路径}/addresses/tree`
- **Method**: `GET`
- **认证**: 需要

**查询参数**:

| 字段名     | 类型  | 是否必须 | 描述                               | 示例值 |
| ---------- | ----- | -------- | ---------------------------------- | ------ |
| `parentId` | int64 | 否       | 父级ID (默认为0，表示获取所有顶级地址及其子树) | `0`    |

**响应 (`dto.CommunityAddressTreeResponse`)**:

`items` 字段是一个 `dto.CommunityAddressResponse` 数组，每个元素可能包含 `children` 字段来表示其子级。

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "items": [
      {
        "id": 123,
        "name": "幸福小区",
        "parentId": 0,
        "level": 1,
        // ... 其他字段
        "children": [
          {
            "id": 124,
            "name": "1号楼",
            "parentId": 123,
            "level": 2,
            // ... 其他字段
            "children": [
              {
                "id": 125,
                "name": "1单元",
                "parentId": 124,
                "level": 3
                // ... 其他字段
              }
            ]
          }
        ]
      }
      // ...更多顶级地址项
    ]
  }
}
```

### 2.7 获取地址选择器选项

获取用于级联选择器 (Cascader) 的地址选项数据。通常返回完整的地址树，但结构适配选择器。

- **URL**: `{基础路径}/addresses/options`
- **Method**: `GET`
- **认证**: 需要

**响应 (`dto.CommunityAddressOptionsResponse`)**:

`options` 字段是一个 `dto.CommunityAddressOption` 数组。

**`dto.CommunityAddressOption` 结构**:

| 字段名     | 类型    | 描述       | 示例值                     |
| ---------- | ------- | ---------- | -------------------------- |
| `value`    | int64   | 值 (地址ID)| `123`                      |
| `label`    | string  | 标签 (名称)| `幸福小区`                 |
| `children` | array   | 子选项     | `[...]`                    |
| `longitude`| float64 | 经度       | `116.404000`               |
| `latitude` | float64 | 纬度       | `39.915000`                |
| `level`    | int     | 级别       | `1`                        |

**示例响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "options": [
      {
        "value": 123,
        "label": "幸福小区",
        "longitude": 116.404000,
        "latitude": 39.915000,
        "level": 1,
        "children": [
          {
            "value": 124,
            "label": "1号楼",
            "longitude": 116.404100,
            "latitude": 39.915100,
            "level": 2,
            "children": [
              {
                "value": 125,
                "label": "1单元",
                "longitude": 116.404110,
                "latitude": 39.915110,
                "level": 3
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 2.8 根据父级ID获取子地址列表

获取指定父级ID下的直接子地址列表。可以选择性地通过 `level` 参数筛选特定级别的子地址。

- **URL**: `{基础路径}/addresses/parent/{parentId}`
- **Method**: `GET`
- **认证**: 需要

**路径参数**:

| 参数       | 类型  | 描述   |
| ---------- | ----- | ------ |
| `parentId` | int64 | 父级ID |

**查询参数**:

| 字段名 | 类型 | 是否必须 | 描述                                     | 示例值 |
| ------ | ---- | -------- | ---------------------------------------- | ------ |
| `level`| int  | 否       | 地址级别 (1-小区, 2-楼栋, 3-单元)。不指定则可能返回树形结构或所有子级。 | `2`    |

**响应**:

如果指定了 `level`，通常返回 `dto.CommunityAddressResponse` 数组。
如果未指定 `level`，响应格式可能类似于 `GetAddressTree` 的部分树结构，具体行为需参照后端实现。

**示例响应 (指定 level)**:

```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 124,
      "name": "1号楼",
      "parentId": 123,
      "level": 2,
      // ... 其他字段
    },
    {
      "id": 126,
      "name": "2号楼",
      "parentId": 123,
      "level": 2,
      // ... 其他字段
    }
  ]
}
```

### 2.9 获取完整地址信息

根据选定的小区ID、楼栋ID、单元ID（或其他层级ID），获取拼接好的完整地址路径和最终选定单元的经纬度。

- **URL**: `{基础路径}/addresses/full-info`
- **Method**: `POST`
- **认证**: 需要
- **Content-Type**: `application/json`

**请求参数 (`dto.SelectedCommunityAddressRequest`)**:

| 字段名       | 类型  | 是否必须 | 描述   | 示例值 |
| ------------ | ----- | -------- | ------ | ------ |
| `communityId`| int64 | 是       | 小区ID | `123`  |
| `buildingId` | int64 | 是       | 楼栋ID | `124`  |
| `unitId`     | int64 | 是       | 单元ID | `125`  |

**响应 (`dto.SelectedCommunityAddressResponse`)**:

| 字段名     | 类型    | 描述                                   | 示例值                     |
| ---------- | ------- | -------------------------------------- | -------------------------- |
| `fullPath` | string  | 完整地址路径                           | `幸福小区/1号楼/1单元`     |
| `longitude`| float64 | 最终选定单元的经度                     | `116.404110`               |
| `latitude` | float64 | 最终选定单元的纬度                     | `39.915110`                |

**示例响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "fullPath": "幸福小区/1号楼/1单元",
    "longitude": 116.404110,
    "latitude": 39.915110
  }
}
```

### 2.10 刷新地址缓存

手动触发后端刷新社区地址相关的缓存。

- **URL**: `{基础路径}/addresses/cache/refresh`
- **Method**: `POST`
- **认证**: 需要

**响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "message": "社区地址缓存已刷新"
  }
}
```

## 3. 前端开发注意事项

1.  **层级地址选择**: 
    *   推荐使用级联选择器 (Cascader) 组件，数据源可来自 `GET /addresses/options` 接口。
    *   当用户选择不同层级时（例如选择小区后加载楼栋，选择楼栋后加载单元），可以调用 `GET /addresses/parent/{parentId}` 并配合 `level` 参数动态加载下一级选项。

2.  **表单处理**:
    *   创建/编辑地址时，`level` 字段应根据当前操作的层级正确设置。
    *   `parentId` 字段需要根据用户在层级选择器中选择的父级来设定。
    *   经纬度 (`longitude`, `latitude`) 可以通过地图选点组件获取，或者允许用户手动输入。
    *   前端应进行必要的输入校验，如名称不为空，经纬度格式正确等。

3.  **数据显示**:
    *   列表展示时，可以使用表格组件，并支持分页和筛选。
    *   树形结构展示时，可以使用树形组件 (Tree)，数据源来自 `GET /addresses/tree`。
    *   `fullPath` 字段可以直接用于显示完整的地址信息。

4.  **状态管理**:
    *   地址的 `status` (启用/禁用) 可以通过开关 (Switch) 组件进行控制。

5.  **地图集成 (可选)**:
    *   如果业务需要，可以集成地图服务 (如高德地图、百度地图、腾讯地图等)。
    *   使用地址的经纬度信息在地图上标记位置。
    *   提供地图选点功能，方便用户选取经纬度。

6.  **错误处理**:
    *   妥善处理 API 返回的错误信息，并在界面上给予用户友好提示。
    *   例如，删除一个包含子地址的父地址时，后端可能会禁止操作，前端应提示用户先删除子地址。

7.  **用户体验**:
    *   提供清晰的层级导航和选择方式。
    *   在表单中对必填项和字段格式给出明确提示。
    *   对于耗时操作（如加载大量树形数据），提供加载状态提示。

```
