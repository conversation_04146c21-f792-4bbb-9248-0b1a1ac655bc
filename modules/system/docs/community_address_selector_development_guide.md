# 社区地址选择器前端开发指南

## 1. 概述

本文档旨在指导前端开发人员如何利用后端提供的API接口，构建一个高效、用户友好的社区地址选择器。社区地址通常包含小区、楼栋、单元等层级结构。

核心目标是实现一个能够让用户逐级选择地址（例如：选择小区 -> 选择楼栋 -> 选择单元），并最终获取完整地址信息及地理坐标的功能。

## 2. 核心数据模型

理解后端数据模型有助于前端更好地处理和展示数据。

**`CommunityAddress` 模型关键字段:**

*   `Id` (int64): 地址的唯一标识符。
*   `Name` (string): 地址名称（如 "幸福小区", "1号楼", "101单元"）。
*   `ParentId` (int64): 父级地址的ID。顶级地址（如小区）的 `ParentId` 通常为0。
*   `Level` (int): 地址层级。
    *   `1`: 小区 (Community)
    *   `2`: 楼栋 (Building)
    *   `3`: 单元 (Unit)
*   `FullPath` (string): 完整的地址路径，例如 "幸福小区/1号楼/101单元"。
*   `Longitude` (float64): 经度。
*   `Latitude` (float64): 纬度。

## 3. 地址选择器关键API接口

以下API是构建地址选择器的核心。假设API基础路径为 `/api/system` (请根据实际项目配置调整)。

### 3.1 获取地址选择器选项 (级联数据)

此接口用于获取所有层级的地址数据，适合用作级联选择器 (Cascader) 的数据源。

*   **接口**: `GET /api/system/addresses/options`
*   **描述**: 返回树形结构的地址选项，每个选项包含 `value` (ID), `label` (名称), 和 `children` (子选项数组)。
*   **响应结构 (`dto.CommunityAddressOptionsResponse`)**:
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "options": [
          {
            "value": 1, // 小区ID
            "label": "幸福小区",
            "level": 1,
            "longitude": 116.404000,
            "latitude": 39.915000,
            "children": [
              {
                "value": 101, // 楼栋ID
                "label": "1号楼",
                "level": 2,
                "longitude": 116.404100,
                "latitude": 39.915100,
                "children": [
                  {
                    "value": 10101, // 单元ID
                    "label": "1单元",
                    "level": 3,
                    "longitude": 116.404110,
                    "latitude": 39.915110,
                    "children": []
                  }
                  // ... 更多单元
                ]
              }
              // ... 更多楼栋
            ]
          }
          // ... 更多小区
        ]
      }
    }
    ```
*   **前端使用**:
    *   一次性加载所有地址数据，填充级联选择器。
    *   每个选项的 `value` 对应地址ID，`label` 对应地址名称。

### 3.2 根据父级ID动态加载子地址

如果地址数据量非常大，一次性加载所有数据可能影响性能。此接口允许根据父级ID动态加载其直接子级。

*   **接口**: `GET /api/system/addresses/parent/{parentId}`
*   **路径参数**:
    *   `parentId` (int64): 父级地址的ID。若要获取所有顶级小区，可传 `0`。
*   **查询参数 (可选)**:
    *   `level` (int): 指定要获取的子地址层级。例如，已知父级是小区 (level 1)，想获取其下的楼栋 (level 2)，则传 `level=2`。
*   **描述**: 返回指定父级ID下的子地址列表。
*   **响应结构**:
    *   如果**不指定** `level` (或后端逻辑默认返回树形子集): `dto.CommunityAddressTreeResponse` (其 `items` 字段包含 `dto.CommunityAddressResponse` 列表，每个响应对象可能递归包含 `children`)
        ```json
        // 示例: GET /api/system/addresses/parent/1 (获取ID为1的小区下的所有楼栋和单元)
        {
          "code": 0,
          "msg": "success",
          "data": {
            "items": [ // 通常是直接子级，例如楼栋
              {
                "id": 101, "name": "1号楼", "parentId": 1, "level": 2, /*...*/
                "children": [ // 该楼栋下的单元
                  { "id": 10101, "name": "1单元", "parentId": 101, "level": 3, /*...*/ }
                ]
              }
            ]
          }
        }
        ```
    *   如果**指定** `level`: `[]dto.CommunityAddressResponse` (扁平列表)
        ```json
        // 示例: GET /api/system/addresses/parent/1?level=2 (获取ID为1的小区下的所有楼栋)
        {
          "code": 0,
          "msg": "success",
          "data": [
            { "id": 101, "name": "1号楼", "parentId": 1, "level": 2, /*...*/ },
            { "id": 102, "name": "2号楼", "parentId": 1, "level": 2, /*...*/ }
          ]
        }
        ```
*   **前端使用**:
    *   级联选择器动态加载数据。当用户选择一个父级后，使用其ID调用此接口获取子级选项。
    *   需要将返回的 `dto.CommunityAddressResponse` 列表转换为级联选择器期望的 `value`, `label`, `children` 格式。

### 3.3 获取选定地址的完整信息

当用户完成所有层级的选择后，使用此接口提交各层级ID，以获取完整的地址路径和最终选定单元的精确经纬度。

*   **接口**: `POST /api/system/addresses/full-info`
*   **请求体 (`dto.SelectedCommunityAddressRequest`)**:
    ```json
    {
      "communityId": 1,  // 选定的小区ID
      "buildingId": 101, // 选定的楼栋ID
      "unitId": 10101    // 选定的单元ID
    }
    ```
*   **响应结构 (`dto.SelectedCommunityAddressResponse`)**:
    ```json
    {
      "code": 0,
      "msg": "success",
      "data": {
        "fullPath": "幸福小区/1号楼/1单元",
        "longitude": 116.404110,
        "latitude": 39.915110
      }
    }
    ```
*   **前端使用**:
    *   在用户完成选择后调用，用于显示完整的地址字符串。
    *   获取最终选择的详细地理坐标，可用于地图标记或其他基于位置的服务。

## 4. 前端实现建议

1.  **组件选择**:
    *   推荐使用UI库提供的级联选择器 (Cascader) 组件，它天然适合处理层级数据。

2.  **数据加载策略**:
    *   **策略一 (全量加载)**: 如果地址数据总体量不大，可以在组件初始化时调用 `GET /api/system/addresses/options` 获取所有数据并构建选择器。这是最简单的实现方式。
    *   **策略二 (动态分级加载)**: 如果数据量较大，为优化性能：
        1.  初始化时，调用 `GET /api/system/addresses/parent/0` (或 `GET /api/system/addresses/options` 并只取第一层) 获取所有小区作为第一级选项。
        2.  当用户选择一个小区后，使用该小区的ID作为 `parentId` 调用 `GET /api/system/addresses/parent/{communityId}` (可配合 `level=2`) 获取该小区下的楼栋列表作为第二级选项。
        3.  同理，选择楼栋后，获取其下的单元列表。
        4.  注意：使用动态加载时，需要将 `dto.CommunityAddressResponse` 转换为级联选择器期望的 `value`, `label`, `children` (并标记 `isLeaf` 或类似属性) 格式。

3.  **数据绑定与状态管理**:
    *   维护一个数组或对象来存储用户在每一级选择的ID。例如 `[communityId, buildingId, unitId]`。

4.  **获取最终结果**:
    *   当用户完成所有层级的选择（例如选择了单元），将收集到的各级ID (`communityId`, `buildingId`, `unitId`) 作为请求体，调用 `POST /api/system/addresses/full-info`。
    *   使用返回的 `fullPath` 显示给用户，`longitude` 和 `latitude` 可用于其他业务逻辑。

5.  **用户体验**:
    *   在动态加载数据时，提供加载指示器 (loading spinner)。
    *   清晰地展示当前已选择的路径。
    *   处理空数据或加载失败的情况，给予用户友好提示。

## 5. 注意事项

*   API基础路径 (`/api/system`) 可能因项目实际部署而异，请与后端开发人员确认。
*   认证与授权：确保前端请求包含了必要的认证信息（如Token）。
*   错误处理：对API请求的各种错误情况（网络错误、服务器错误、参数错误等）进行妥善处理。

通过遵循本指南，前端开发人员应能有效地集成社区地址选择功能，提供流畅的用户体验。
