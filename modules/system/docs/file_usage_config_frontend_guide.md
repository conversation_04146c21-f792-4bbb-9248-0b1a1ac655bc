# 文件用途配置 (FileUsageConfig) 功能前端开发指南

## 1. 简介

本文档为前端开发人员提供与后端文件用途配置 (FileUsageConfig) 模块交互的详细指南。文件用途配置模块用于管理系统中不同文件用途的配置信息，包括文件类型限制、大小限制、匿名上传权限等。它支持配置的增删改查、选项获取、默认配置初始化等功能。

## 2. API 接口文档

**基础路径**: `/api/v1/system/secured/file-usage` (需要认证)

### 2.1 获取文件用途配置列表

分页获取文件用途配置列表，支持按用途代码、用途名称、状态进行筛选和排序。

- **URL**: `{基础路径}/list`
- **Method**: `GET`
- **认证**: 需要 (管理员权限)

**查询参数**:

| 字段名 | 类型 | 是否必须 | 描述 | 示例值 |
|--|--|--|--|--|
| `page` | int | 否 | 页码 (默认 1) | `1` |
| `pageSize` | int | 否 | 每页数量 (默认 10，最大 100) | `10` |
| `usageCode` | string | 否 | 用途代码 (模糊匹配) | `avatar` |
| `usageName` | string | 否 | 用途名称 (模糊匹配) | `头像` |
| `status` | int | 否 | 状态：`1`-启用，`0`-禁用 | `1` |
| `sortField` | string | 否 | 排序字段 (默认 sort_order) | `sort_order` |
| `sortOrder` | string | 否 | 排序方式：`asc`-升序，`desc`-降序 (默认 asc) | `asc` |

**响应 (`FileUsageConfigListResponse`)**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 25,
    "list": [
      {
        "id": 1,
        "usage_code": "avatar",
        "usage_name": "头像",
        "description": "用户头像上传",
        "allow_anonymous": 0,
        "allow_anonymous_text": "否",
        "max_file_size": 2097152,
        "max_file_size_mb": 2,
        "allowed_types": "jpg,jpeg,png,gif",
        "sort_order": 10,
        "status": 1,
        "status_text": "启用",
        "remark": "用户头像文件",
        "created_at": "2023-10-01T10:00:00Z",
        "updated_at": "2023-10-01T12:00:00Z"
      }
      // ...更多配置项
    ]
  }
}
```

### 2.2 获取文件用途配置详情

根据ID获取单个文件用途配置的详细信息。

- **URL**: `{基础路径}/{id}`
- **Method**: `GET`
- **认证**: 需要

**路径参数**:

| 参数 | 类型 | 描述 |
|--|--|--|
| `id` | int64 | 配置ID |

**响应参数 (`FileUsageConfigResponse`)**:

| 字段名 | 类型 | 描述 | 示例值 |
|--|--|--|--|
| `id` | int64 | 配置ID | `1` |
| `usage_code` | string | 用途代码 | `avatar` |
| `usage_name` | string | 用途名称 | `头像` |
| `description` | string | 用途描述 | `用户头像上传` |
| `allow_anonymous` | int8 | 是否允许匿名上传：1-是，0-否 | `0` |
| `allow_anonymous_text` | string | 是否允许匿名上传文本 | `否` |
| `max_file_size` | int64 | 最大文件大小（字节），0表示使用全局配置 | `2097152` |
| `max_file_size_mb` | int | 最大文件大小（MB） | `2` |
| `allowed_types` | string | 允许的文件类型（逗号分隔），空表示使用全局配置 | `jpg,jpeg,png,gif` |
| `sort_order` | int | 排序顺序 | `10` |
| `status` | int8 | 状态：1-启用，0-禁用 | `1` |
| `status_text` | string | 状态文本 | `启用` |
| `remark` | string | 备注 | `用户头像文件` |
| `created_at` | string | 创建时间 | `2023-10-01T10:00:00Z` |
| `updated_at` | string | 更新时间 | `2023-10-01T12:00:00Z` |

**示例响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "usage_code": "avatar",
    "usage_name": "头像",
    "description": "用户头像上传",
    "allow_anonymous": 0,
    "allow_anonymous_text": "否",
    "max_file_size": 2097152,
    "max_file_size_mb": 2,
    "allowed_types": "jpg,jpeg,png,gif",
    "sort_order": 10,
    "status": 1,
    "status_text": "启用",
    "remark": "用户头像文件",
    "created_at": "2023-10-01T10:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 2.3 保存文件用途配置

创建新的文件用途配置或更新已存在的配置。

- **URL**: `{基础路径}/save`
- **Method**: `POST`
- **认证**: 需要
- **Content-Type**: `application/json`

**请求参数 (`SaveFileUsageConfigRequest`)**:

| 字段名 | 类型 | 是否必须 | 描述 | 示例值 |
|--|--|--|--|--|
| `id` | int64 | 否 | 配置ID (0表示新增，非0表示更新) | `0` |
| `usage_code` | string | 是 | 用途代码 (最大50字符) | `avatar` |
| `usage_name` | string | 是 | 用途名称 (最大100字符) | `头像` |
| `description` | string | 否 | 用途描述 (最大500字符) | `用户头像上传` |
| `allow_anonymous` | int8 | 是 | 是否允许匿名上传：1-是，0-否 | `0` |
| `max_file_size` | int64 | 否 | 最大文件大小（字节），0表示使用全局配置 | `2097152` |
| `allowed_types` | string | 否 | 允许的文件类型（逗号分隔），空表示使用全局配置 | `jpg,jpeg,png,gif` |
| `sort_order` | int | 否 | 排序顺序 | `10` |
| `status` | int8 | 是 | 状态：1-启用，0-禁用 | `1` |
| `remark` | string | 否 | 备注 (最大500字符) | `用户头像文件` |

**示例请求**:

```json
{
  "id": 0,
  "usage_code": "avatar",
  "usage_name": "头像",
  "description": "用户头像上传",
  "allow_anonymous": 0,
  "max_file_size": 2097152,
  "allowed_types": "jpg,jpeg,png,gif",
  "sort_order": 10,
  "status": 1,
  "remark": "用户头像文件"
}
```

**响应**:

成功时，返回保存后的配置信息。

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "usage_code": "avatar",
    "usage_name": "头像",
    // ...其他字段
  }
}
```

### 2.4 删除文件用途配置

删除指定的文件用途配置。

- **URL**: `{基础路径}/{id}`
- **Method**: `DELETE`
- **认证**: 需要

**路径参数**:

| 参数 | 类型 | 描述 |
|--|--|--|
| `id` | int64 | 配置ID |

**响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "message": "删除成功"
  }
}
```

### 2.5 获取文件用途选项

获取可用的文件用途选项，用于下拉选择或其他选择组件。

- **URL**: `{基础路径}/options`
- **Method**: `GET`
- **认证**: 需要

**响应 (`FileUsageConfigOptionsResponse`)**:

| 字段名 | 类型 | 描述 | 示例值 |
|--|--|--|--|
| `allowed_usage_types` | array | 允许的用途类型列表 | `["avatar", "product", "merchant"]` |
| `anonymous_usage_types` | array | 允许匿名上传的用途类型列表 | `["complaint"]` |

**示例响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "allowed_usage_types": ["avatar", "product", "merchant", "complaint", "other"],
    "anonymous_usage_types": ["complaint"]
  }
}
```

### 2.6 初始化默认配置

初始化系统默认的文件用途配置。

- **URL**: `{基础路径}/init`
- **Method**: `POST`
- **认证**: 需要

**响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "message": "初始化成功"
  }
}
```

### 2.7 检查并初始化必需配置

检查数据库中是否存在指定的文件用途配置，如果不存在则自动添加默认配置。

- **URL**: `{基础路径}/check-init`
- **Method**: `POST`
- **认证**: 需要
- **Content-Type**: `application/json`

**请求参数**:

请求体为字符串数组，包含需要检查的用途代码列表。

**示例请求**:

```json
["avatar", "product", "merchant"]
```

**响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "message": "检查并初始化完成",
    "checked_codes": ["avatar", "product", "merchant"]
  }
}
```

### 2.8 刷新缓存

手动触发后端刷新文件用途配置相关的缓存。

- **URL**: `{基础路径}/refresh`
- **Method**: `POST`
- **认证**: 需要

**响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "message": "缓存刷新成功"
  }
}
```

## 3. 默认文件用途类型

系统预定义了以下文件用途类型：

| 用途代码 | 用途名称 | 描述 | 默认配置 |
|--|--|--|--|
| `avatar` | 头像 | 用户头像上传 | 2MB, jpg/jpeg/png/gif, 不允许匿名 |
| `product` | 商品图片 | 商品相关图片 | 5MB, jpg/jpeg/png/gif, 不允许匿名 |
| `merchant` | 商户图片 | 商户相关图片 | 5MB, jpg/jpeg/png/gif, 不允许匿名 |
| `merchant_logo` | 商户Logo | 商户Logo图片 | 2MB, jpg/jpeg/png/gif, 不允许匿名 |
| `complaint` | 投诉图片 | 投诉举报相关图片 | 10MB, jpg/jpeg/png/gif, 允许匿名 |
| `other` | 其他用途 | 其他类型文件 | 10MB, 所有类型, 不允许匿名 |

## 4. 前端开发注意事项

### 4.1 表单处理

1. **创建/编辑配置**:
   - `id` 字段为 0 时表示新增，非 0 时表示更新
   - `usage_code` 和 `usage_name` 为必填字段
   - `allowed_types` 字段应该以逗号分隔，前端可以使用标签输入组件
   - `max_file_size` 以字节为单位，前端可以提供 MB 转换

2. **参数验证**:
   - 用途代码应该是唯一的，不能重复
   - 文件大小应该是正整数
   - 状态只能是 0 或 1

### 4.2 数据显示

1. **列表展示**:
   - 使用表格组件，支持分页和筛选
   - 文件大小可以显示为 MB 格式（`max_file_size_mb` 字段）
   - 状态可以使用开关组件（`status_text` 字段提供文本显示）
   - 匿名上传权限可以使用图标或徽章显示（`allow_anonymous_text` 字段）

2. **表单组件建议**:
   - 用途代码：输入框（新增时）或只读显示（编辑时）
   - 用途名称：输入框
   - 描述：文本域
   - 匿名上传：开关组件
   - 文件大小：数字输入框 + 单位选择（KB/MB）
   - 文件类型：标签输入组件或多选下拉框
   - 排序：数字输入框
   - 状态：开关组件
   - 备注：文本域

### 4.3 用户体验

1. **文件类型输入**:
   - 提供常用文件类型的快速选择按钮
   - 支持手动输入自定义文件类型
   - 实时验证文件类型格式

2. **文件大小设置**:
   - 提供 KB、MB、GB 单位转换
   - 显示推荐的文件大小范围
   - 0 值的特殊处理（表示使用全局配置）

3. **操作反馈**:
   - 保存成功后给予明确提示
   - 删除前进行确认
   - 初始化操作提供进度提示

### 4.4 权限控制

1. **接口权限**:
   - 所有接口都需要管理员权限
   - 前端应该检查用户权限后再显示相关功能

2. **操作权限**:
   - 某些系统预设的配置可能不允许删除
   - 根据业务需求控制编辑权限

### 4.5 错误处理

1. **常见错误**:
   - 用途代码重复
   - 参数格式错误
   - 权限不足
   - 网络错误

2. **错误提示**:
   - 提供具体的错误信息
   - 指导用户如何修正错误
   - 对于系统错误，提供联系管理员的提示

### 4.6 性能优化

1. **数据加载**:
   - 使用分页加载大量配置数据
   - 实现搜索防抖，避免频繁请求
   - 缓存选项数据，减少重复请求

2. **用户交互**:
   - 表单提交时显示加载状态
   - 大文件上传时显示进度条
   - 操作完成后及时更新列表数据

## 5. 示例代码片段

### 5.1 获取配置列表（JavaScript/Axios）

```javascript
// 获取文件用途配置列表
async function getFileUsageConfigList(params = {}) {
  try {
    const response = await axios.get('/api/v1/system/secured/file-usage/list', {
      params: {
        page: 1,
        pageSize: 10,
        ...params
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取配置列表失败:', error);
    throw error;
  }
}
```

### 5.2 保存配置（JavaScript/Axios）

```javascript
// 保存文件用途配置
async function saveFileUsageConfig(configData) {
  try {
    const response = await axios.post('/api/v1/system/secured/file-usage/save', configData);
    return response.data;
  } catch (error) {
    console.error('保存配置失败:', error);
    throw error;
  }
}
```

### 5.3 文件大小转换工具函数

```javascript
// 字节转换为 MB
function bytesToMB(bytes) {
  return Math.round(bytes / (1024 * 1024));
}

// MB 转换为字节
function mbToBytes(mb) {
  return mb * 1024 * 1024;
}

// 格式化文件大小显示
function formatFileSize(bytes) {
  if (bytes === 0) return '使用全局配置';
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
  return Math.round(bytes / (1024 * 1024)) + ' MB';
}
```

通过遵循本指南，前端开发人员应能有效地集成文件用途配置管理功能，提供完整的配置管理界面和良好的用户体验。