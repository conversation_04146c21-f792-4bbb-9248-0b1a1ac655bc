```markdown
# SMS 服务配置前端开发指南

## 1. 简介

本文档旨在为前端开发人员提供与后端 SMS（短信服务）配置模块交互的详细指南。SMS 配置模块允许管理员设置和管理系统发送短信所使用的服务提供商、API凭证、短信模板等信息。

## 2. API 接口文档

### 各短信服务商对应关系

不同的短信服务商使用不同的字段名称，下面是系统配置字段与各服务商的实际字段对应关系：

| 系统字段 | 阿里云 | 赛邮(Submail) | 说明 |
| ------------ | ------ | ---------- | ---- |
| `accessKey` | AccessKeyId | app_id | 访问密钥ID |
| `accessSecret` | AccessSecret | app_key | 访问密钥密码 |
| `signName` | SignName | 签名 | 短信签名 |
| `templateCodeRegister`等 | 模板ID | 项目ID(project) | 短信模板ID，赛邮称为项目ID |

如果选择 `submail` 作为服务商，请特别注意：

1. `accessKey` 对应赛邮的 `app_id`
2. `accessSecret` 对应赛邮的 `app_key`
3. 各种验证码模板ID字段（如`templateCodeRegister`）对应赛邮的短信项目ID（如配置中的 `3nwR4`）

**基础路径**: (请根据实际路由配置填写，例如 `/api/system` 或 `/admin/api/system`)

### 2.1 获取 SMS 配置

获取当前系统激活的 SMS 服务配置信息。

- **URL**: `{基础路径}/sms/config`
- **Method**: `GET`
- **认证**: 需要 (例如：管理员Token)

**响应参数 (`SmsConfigResponse`)**:

| 字段名                 | 类型   | 描述                                     | 示例值                     |
| ---------------------- | ------ | ---------------------------------------- | -------------------------- |
| `id`                   | int64  | 配置ID                                   | `1`                        |
| `provider`             | string | 短信服务提供商编码 (aliyun, tencent, yunpian, submail, etc.) | `aliyun`                   |
| `providerText`         | string | 短信服务提供商名称                         | `阿里云`                   |
| `accessKey`            | string | 访问密钥ID                               | `LTAIxxxxxxxxxxxxxx`       |
| `accessSecret`         | string | 访问密钥密码 (后端会进行脱敏处理)          | `********`                 |
| `signName`             | string | 短信签名                                 | `O-Mall商城`               |
| `templateCodeRegister` | string | 注册验证码模板ID                         | `SMS_12345678`             |
| `templateCodeLogin`    | string | 登录验证码模板ID                         | `SMS_12345679`             |
| `templateCodeResetPwd` | string | 重置密码验证码模板ID                     | `SMS_12345680`             |
| `templateCodeNotice`   | string | 通知消息模板ID                           | `SMS_12345681`             |
| `dailyLimit`           | int    | 每日发送上限                             | `1000`                     |
| `status`               | int8   | 状态：`1`-启用，`0`-禁用                   | `1`                        |
| `statusText`           | string | 状态文本                                 | `启用`                     |
| `remark`               | string | 备注                                     | `阿里云短信服务配置`       |
| `createdAt`            | string | 创建时间 (格式: YYYY-MM-DD HH:mm:ss)     | `2023-10-01 10:00:00`      |
| `updatedAt`            | string | 更新时间 (格式: YYYY-MM-DD HH:mm:ss)     | `2023-10-01 12:00:00`      |

**示例响应**:

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "provider": "aliyun",
    "providerText": "阿里云",
    "accessKey": "LTAIxxxxxxxxxxxxxx",
    "accessSecret": "********",
    "signName": "O-Mall商城",
    "templateCodeRegister": "SMS_12345678",
    "templateCodeLogin": "SMS_12345679",
    "templateCodeResetPwd": "SMS_12345680",
    "templateCodeNotice": "SMS_12345681",
    "dailyLimit": 1000,
    "status": 1,
    "statusText": "启用",
    "remark": "阿里云短信服务配置",
    "createdAt": "2023-10-01 10:00:00",
    "updatedAt": "2023-10-01 12:00:00"
  }
}
```

### 2.2 保存/更新 SMS 配置

创建新的 SMS 配置或更新现有配置。如果系统中通常只有一个全局短信配置，此接口主要用于更新。

- **URL**: `{基础路径}/sms/config`
- **Method**: `POST` (或 `PUT`，根据后端实现)
- **认证**: 需要 (例如：管理员Token)
- **Content-Type**: `application/json`

**请求参数 (`SaveSmsConfigRequest`)**:

| 字段名                 | 类型   | 是否必须 | 描述                                                       | 示例值                     |
| ---------------------- | ------ | -------- | ---------------------------------------------------------- | -------------------------- |
| `id`                   | int64  | 是       | 配置ID (如果为 `0` 或 `null`，表示新增；否则为更新现有配置)    | `1`                        |
| `provider`             | string | 是       | 短信服务提供商编码 (如 `aliyun`, `tencent`, `yunpian`, `submail`, `custom`) | `aliyun`                   |
| `accessKey`            | string | 是       | 访问密钥ID                                                 | `LTAIyyyyyyyyyyyyyy`       |
| `accessSecret`         | string | 是       | 访问密钥密码 (前端发送原始密码，后端存储)                    | `yourActualAccessSecret`   |
| `signName`             | string | 是       | 短信签名                                                   | `O-Mall官方`               |
| `templateCodeRegister` | string | 是       | 注册验证码模板ID                                           | `SMS_87654321`             |
| `templateCodeLogin`    | string | 是       | 登录验证码模板ID                                           | `SMS_87654322`             |
| `templateCodeResetPwd` | string | 是       | 重置密码验证码模板ID                                       | `SMS_87654323`             |
| `templateCodeNotice`   | string | 是       | 通知消息模板ID                                             | `SMS_87654324`             |
| `dailyLimit`           | int    | 是       | 每日发送上限 (必须大于0)                                   | `2000`                     |
| `status`               | int8   | 是       | 状态：`1`-启用，`0`-禁用                                     | `1`                        |
| `remark`               | string | 否       | 备注                                                       | `更新后的阿里云短信配置`   |

**示例请求**:

```json
{
  "id": 1,
  "provider": "aliyun",
  "accessKey": "LTAIyyyyyyyyyyyyyy",
  "accessSecret": "yourActualAccessSecret",
  "signName": "O-Mall官方",
  "templateCodeRegister": "SMS_87654321",
  "templateCodeLogin": "SMS_87654322",
  "templateCodeResetPwd": "SMS_87654323",
  "templateCodeNotice": "SMS_87654324",
  "dailyLimit": 2000,
  "status": 1,
  "remark": "更新后的阿里云短信配置"
}
```

**响应**:

成功时，通常返回不包含数据的成功消息。

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

失败时，返回错误信息。

```json
{
  "code": 1001, // 错误码，例如参数错误
  "msg": "Invalid parameter: dailyLimit must be greater than 0",
  "data": null
}
```

### 2.3 刷新 SMS 配置缓存 (可选)

此接口用于清除后端关于 SMS 配置的缓存，并重新加载最新配置。通常在配置更新后会自动刷新，但也可手动触发。

- **URL**: `{基础路径}/sms/config/refresh_cache`
- **Method**: `POST`
- **认证**: 需要 (例如：管理员Token)

**请求参数**: 无

**响应**:

成功时，通常返回不包含数据的成功消息。

```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

## 3. 前端开发注意事项

1.  **配置加载**:
    *   页面加载时，应调用 `GET /sms/config` 接口获取当前配置并填充表单。
    *   如果获取到的 `accessSecret` 是脱敏的（如 `********`），表单中对应字段可以留空或提示用户“如需修改请输入新密钥”。如果用户未输入新密钥，提交时**不应**将脱敏的星号串提交回去，而应由后端逻辑判断是否更新此字段。
        *   **最佳实践**：如果 `accessSecret` 字段为空，则不传递该字段或传递空字符串，后端判断不更新此密码。如果用户输入了内容，则传递新内容。

2.  **表单字段**:
    *   **赛邮（Submail）特殊说明**：
        *   当选择 `submail` 时，`accessKey` 字段应该填写赛邮的 app_id（如配置文件中的 `80121`）
        *   `accessSecret` 字段应该填写赛邮的 app_key（如配置文件中的 `9b493b9ef929312f0d4f8f55973f6a8a`）
        *   各种验证码模板ID字段应该填写赛邮的短信项目ID（如配置文件中的 `3nwR4`）
    *   **服务提供商 (`provider`)**: 应提供一个下拉选择框，选项可包括：
        *   `aliyun` (阿里云)
        *   `tencent` (腾讯云)
        *   `yunpian` (云片)
        *   `submail` (赛邮)
        *   `custom` (自定义)
        *   (可根据后端 `getSmsProviderText` 辅助函数支持的类型进行扩展)
    *   **状态 (`status`)**: 应提供一个开关（Switch）或下拉选择框：
        *   `1` (启用)
        *   `0` (禁用)
    *   **AccessKey 和 AccessSecret**: 这些是敏感信息，`AccessSecret` 在显示时应注意安全（例如，获取时不直接显示明文，仅显示脱敏后的）。保存时，用户输入的是明文。
    *   **模板ID**: 这些字段需要用户根据其在对应短信服务商平台申请的模板ID填写。
    *   **每日发送上限 (`dailyLimit`)**: 数字输入框，应有前端校验确保为正整数。

3.  **保存配置**:
    *   提交表单时，调用 `POST /sms/config` 接口。
    *   `id` 字段的值应为从 `GET` 请求中获取到的配置ID。如果系统设计为始终只有一个配置记录，那么 `id` 总是已知的。如果 `GET` 未返回任何配置（例如首次配置），则 `id` 可以为 `0` 或 `null` 来指示创建新配置。
    *   如果用户没有修改 `AccessSecret`，前端在提交 `SaveSmsConfigRequest` 时，`accessSecret` 字段可以考虑不传，或者传递一个特殊标记（需与后端约定），以避免用空值覆盖已有的密钥。更稳妥的方式是，如果用户未填写该字段，则前端不包含 `accessSecret` 在请求体中，后端服务应能处理部分更新的逻辑。
        *   **推荐**：如果用户在 `AccessSecret` 输入框中没有做任何修改（即输入框为空或保持原样），则在构建 `SaveSmsConfigRequest` 时，可以不包含 `accessSecret` 字段，或者将其值设为从 `GET` 请求中获取到的脱敏值（后端需要能识别此情况并忽略）。如果用户输入了新的密钥，则发送新密钥。
        *   **重要**：和后端确认 `AccessSecret` 的更新逻辑。通常，如果前端发送的 `accessSecret` 字段为空或与获取时脱敏后的值相同，后端不应更新它。只有当用户明确输入新值时才更新。

4.  **错误处理**:
    *   妥善处理 API 返回的错误信息，并在界面上给予用户友好提示。

5.  **用户体验**:
    *   对于 AccessKey 和 AccessSecret 等敏感信息，输入框类型可以设置为 `password` 以隐藏输入内容。
    *   提供清晰的字段说明和填写提示。

```
