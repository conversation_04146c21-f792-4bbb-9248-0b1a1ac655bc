# 系统模块 - 上传配置管理示例

> 本文档提供了系统管理员在管理后台配置文件上传功能的详细示例。

## 1. 上传配置管理

### 界面示例

```
+-------------------------------------------------------+
|                 文件上传配置                            |
+-------------------------------------------------------+
|                                                       |
| 基本配置:                                           |
|                                                       |
| 存储方式: [●本地存储] [○阿里云OSS] [○腾讯云COS] [○AWS S3] [○七牛云] |
|                                                       |
| 最大文件大小: [10           ] MB (0表示不限制)         |
|                                                       |
| 允许的文件类型: [jpg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar] |
|                                                       |
| CDN加速: [●启用] [○禁用] 域名: [https://cdn.example.com] |
|                                                       |
| 状态: [●启用] [○禁用]                                  |
|                                                       |
| 备注: [系统默认上传配置                        ]       |
|                                                       |
| 本地存储配置:                                         |
| 存储路径: [./uploads                         ]       |
|                                                       |
| [ 保存配置 ]    [ 取消 ]    [ 测试连接 ]               |
+-------------------------------------------------------+
```

### API请求示例

#### 1.1 获取上传配置

```http
GET /api/v1/system/secured/upload/config
Authorization: Bearer {admin_token}
```

#### 1.2 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "storageMode": "local",
    "maxSize": 10485760,
    "maxSizeMB": 10,
    "allowedExtensions": "jpg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar",
    "enableCdn": 1,
    "cdnDomain": "https://cdn.example.com",
    "status": 1,
    "statusText": "启用",
    "remark": "系统默认上传配置",
    "createdAt": "2025-05-10 10:00:00",
    "updatedAt": "2025-05-15 14:30:00",
    "config": {
      "localPath": "./uploads"
    }
  }
}
```

#### 1.3 更新上传配置

```http
PUT /api/v1/system/secured/upload/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "id": 1,
  "storageMode": "local",
  "maxSize": 15728640,
  "allowedExtensions": "jpg,png,gif,pdf,doc,docx,xls,xlsx,zip,rar,mp4",
  "enableCdn": 1,
  "cdnDomain": "https://cdn.new-example.com",
  "status": 1,
  "remark": "更新了允许的文件类型，增加了mp4格式",
  "config": {
    "localPath": "./public/uploads"
  }
}
```

#### 1.4 更新配置响应

```json
{
  "code": 0,
  "message": "上传配置更新成功",
  "data": {
    "id": 1,
    "updatedAt": "2025-05-28 14:45:30"
  }
}
```

## 2. 存储方式配置示例

### 2.1 七牛云存储配置

```
+-------------------------------------------------------+
|                 七牛云存储配置                          |
+-------------------------------------------------------+
|                                                       |
| AccessKey: [***************************    ]          |
|                                                       |
| SecretKey: [********************************]         |
|                                                       |
| Bucket:    [my-qiniu-bucket               ]          |
|                                                       |
| 存储区域:   [○华东] [●华北] [○华南] [○北美] [○新加坡]     |
|                                                       |
| 使用HTTPS: [●是] [○否]                                |
|                                                       |
| 自定义域名: [https://cdn.example.com        ]         |
|                                                       |
| [ 保存配置 ]    [ 取消 ]    [ 测试连接 ]               |
+-------------------------------------------------------+
```

#### API请求示例

```http
PUT /api/v1/system/secured/upload/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "storageMode": "qiniu",
  "maxSize": 52428800,
  "allowedExtensions": "jpg,png,gif,pdf,doc,docx,xls,xlsx,mp4",
  "enableCdn": 1,
  "cdnDomain": "https://cdn.example.com",
  "status": 1,
  "config": {
    "qiniuConfig": {
      "accessKey": "your-access-key",
      "secretKey": "your-secret-key",
      "bucket": "my-qiniu-bucket",
      "domain": "https://cdn.example.com",
      "zone": "z1",
      "useHTTPS": true
    }
  }
}
```

### 2.2 阿里云OSS配置

```
+-------------------------------------------------------+
|                 OSS存储配置                            |
+-------------------------------------------------------+
|                                                       |
| Endpoint: [oss-cn-hangzhou.aliyuncs.com    ]          |
|                                                       |
| Bucket:   [my-oss-bucket                    ]          |
|                                                       |
| AccessKey: [***************************    ]          |
|                                                       |
| AccessSecret: [********************************]       |
|                                                       |
| 自定义域名: [https://oss.example.com        ]          |
|                                                       |
| [ 保存配置 ]    [ 测试连接 ]                           |
+-------------------------------------------------------+
```

#### API请求示例

```http
PUT /api/v1/system/secured/upload/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "storageMode": "oss",
  "maxSize": 52428800,
  "allowedExtensions": "jpg,png,gif,pdf,doc,docx,xls,xlsx,mp4",
  "enableCdn": 1,
  "cdnDomain": "https://cdn.example.com",
  "status": 1,
  "config": {
    "endpoint": "oss-cn-hangzhou.aliyuncs.com",
    "bucket": "my-oss-bucket",
    "accessKey": "your-access-key-id",
    "accessSecret": "your-access-key-secret",
    "domain": "https://oss.example.com"
  }
}
```

### 2.2 腾讯云COS配置

```
+-------------------------------------------------------+
|                 COS存储配置                            |
+-------------------------------------------------------+
|                                                       |
| 区域: [ap-shanghai                         ] ▼        |
|                                                       |
| Bucket: [example-1250000000                ]          |
|                                                       |
| SecretId: [***************************    ]           |
|                                                       |
| SecretKey: [********************************]         |
|                                                       |
| 自定义域名: [https://cos.example.com        ]          |
|                                                       |
| [ 保存配置 ]    [ 测试连接 ]                           |
+-------------------------------------------------------+
```

#### API请求示例

```http
PUT /api/v1/system/secured/upload/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "storageMode": "cos",
  "maxSize": 52428800,
  "allowedExtensions": "jpg,png,gif,pdf,doc,docx,xls,xlsx,mp4",
  "enableCdn": 1,
  "cdnDomain": "https://cdn.example.com",
  "status": 1,
  "config": {
    "region": "ap-shanghai",
    "bucket": "example-1250000000",
    "secretId": "your-secret-id",
    "secretKey": "your-secret-key",
    "domain": "https://cos.example.com"
  }
}
```

### 2.3 AWS S3配置

```
+-------------------------------------------------------+
|                 S3存储配置                             |
+-------------------------------------------------------+
|                                                       |
| 区域: [us-east-1                          ] ▼         |
|                                                       |
| Bucket: [my-s3-bucket                    ]            |
|                                                       |
| AccessKey: [********************          ]            |
|                                                       |
| SecretKey: [********************************]         |
|                                                       |
| 自定义域名: [https://s3.example.com        ]           |
|                                                       |
| [ 保存配置 ]    [ 测试连接 ]                           |
+-------------------------------------------------------+
```

#### API请求示例

```http
PUT /api/v1/system/secured/upload/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "storageMode": "s3",
  "maxSize": 104857600,
  "allowedExtensions": "jpg,png,gif,pdf,doc,docx,xls,xlsx,mp4",
  "enableCdn": 1,
  "cdnDomain": "https://cdn.example.com",
  "status": 1,
  "config": {
    "region": "us-east-1",
    "bucket": "my-s3-bucket",
    "accessKey": "your-access-key",
    "secretKey": "your-secret-key",
    "domain": "https://s3.example.com"
  }
}
```

## 3. CDN配置

### 3.1 CDN配置界面

```
+-------------------------------------------------------+
|                 CDN加速配置                            |
+-------------------------------------------------------+
|                                                       |
| CDN加速: [●启用] [○禁用]                              |
|                                                       |
| CDN域名: [https://cdn.example.com           ]         |
|                                                       |
| AccessKey: [***************************    ]          |
|                                                       |
| SecretKey: [********************************]         |
|                                                       |
| [ 保存配置 ]    [ 测试连接 ]                           |
+-------------------------------------------------------+
```

#### API请求示例

```http
PUT /api/v1/system/secured/upload/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "id": 1,
  "storageMode": "oss",
  "maxSize": 52428800,
  "allowedExtensions": "jpg,png,gif,pdf,doc,docx,xls,xlsx,mp4",
  "enableCdn": 1,
  "cdnDomain": "https://cdn.new-example.com",
  "status": 1,
  "config": {
    "endpoint": "oss-cn-hangzhou.aliyuncs.com",
    "bucket": "my-oss-bucket",
    "accessKey": "your-access-key-id",
    "accessSecret": "your-access-key-secret",
    "domain": "https://oss.example.com",
    "cdnAccessKey": "your-cdn-access-key",
    "cdnSecretKey": "your-cdn-secret-key"
  }
}
```

## 4. 错误码说明

| 错误码 | 说明                     | 解决方案                                     |
|--------|--------------------------|--------------------------------------------|
| 40001  | 参数错误                 | 检查请求参数是否完整且格式正确               |
| 40101  | 未授权                   | 检查token是否有效或已过期                   |
| 40301  | 权限不足                 | 当前用户无权限执行此操作                    |
| 40401  | 配置不存在               | 检查配置ID是否正确                         |
| 50001  | 存储服务连接失败         | 检查存储服务配置信息是否正确               |
| 50002  | 文件上传失败             | 检查存储服务状态或联系管理员                |
| 50003  | 文件类型不支持           | 检查文件扩展名是否在允许的列表中           |
| 50004  | 文件大小超过限制         | 检查文件大小是否超过系统设置的最大值       |

## 5. 最佳实践

1. **安全性建议**
   - 为不同的环境（开发、测试、生产）使用不同的存储桶和访问凭证
   - 定期轮换访问密钥和密钥
   - 使用最小权限原则配置存储桶策略

2. **性能优化**
   - 对于大文件上传，考虑使用分片上传
   - 启用CDN加速静态资源访问
   - 配置适当的缓存策略

3. **监控与告警**
   - 监控存储空间使用情况
   - 设置流量和请求数的告警阈值
   - 定期审计访问日志

4. **备份与恢复**
   - 定期备份重要配置
   - 实现配置版本控制
   - 制定灾难恢复计划
