# 上传模块文档

## 目录
- [1. 概述](#1-概述)
- [2. 模型结构](#2-模型结构)
  - [2.1 上传配置模型](#21-上传配置模型)
  - [2.2 存储配置](#22-存储配置)
- [3. API 接口](#3-api-接口)
  - [3.1 获取上传配置](#31-获取上传配置)
  - [3.2 保存上传配置](#32-保存上传配置)
- [4. 使用示例](#4-使用示例)
  - [4.1 本地存储配置](#41-本地存储配置)
  - [4.2 OSS存储配置](#42-oss存储配置)
  - [4.3 七牛云存储配置](#43-七牛云存储配置)
  - [4.4 CDN配置](#44-cdn配置)
- [5. 错误码](#5-错误码)

## 1. 概述

上传模块负责管理系统中的文件上传配置，支持多种存储方式，包括本地存储、阿里云OSS、腾讯云COS、AWS S3和七牛云存储。配置信息使用JSON格式存储，便于扩展和维护。

## 2. 模型结构

### 2.1 上传配置模型

```go
type UploadConfig struct {
    ID          uint   `gorm:"primaryKey"`
    StorageMode string `gorm:"size:20;not null;comment:存储模式(local/oss/cos/s3)"`
    Config      string `gorm:"type:json;comment:存储配置"`
    EnableCdn   int8   `gorm:"default:0;comment:是否启用CDN"`
    CdnDomain   string `gorm:"size:255;default:'';comment:CDN域名"`
    // 其他字段...
}
```

### 2.2 存储配置

#### 本地存储配置 (local)
```json
{
    "upload_path": "uploads",
    "base_url": "/uploads",
    "max_size": 10,
    "allowed_ext": ["jpg", "png", "gif", "pdf"],
    "date_format": "20060102"
}
```

#### OSS存储配置 (oss)
```json
{
    "endpoint": "oss-cn-hangzhou.aliyuncs.com",
    "access_key_id": "your-access-key-id",
    "access_key_secret": "your-access-key-secret",
    "bucket_name": "your-bucket-name",
    "base_url": "https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com"
}
```

#### 七牛云存储配置 (qiniu)
```json
{
    "access_key": "your-access-key",
    "secret_key": "your-secret-key",
    "bucket": "your-bucket-name",
    "domain": "https://cdn.example.com",
    "zone": "z0",
    "use_https": true
}
```

## 3. API 接口

### 3.1 获取上传配置

**请求**
```
GET /api/admin/upload/config
```

**响应**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "storage_mode": "local",
        "config": {
            "upload_path": "uploads",
            "base_url": "/uploads"
            // 其他配置...
        },
        "enable_cdn": 0,
        "cdn_domain": ""
    }
}
```

### 3.2 保存上传配置

**请求**
```
POST /api/admin/upload/config
```

**请求体**
```json
{
    "storage_mode": "local",
    "config": {
        "upload_path": "uploads",
        "base_url": "/uploads"
        // 其他配置...
    },
    "enable_cdn": 0,
    "cdn_domain": ""
}
```

**响应**
```json
{
    "code": 200,
    "message": "配置保存成功"
}
```

## 4. 使用示例

### 4.1 本地存储配置

```go
config := &models.UploadConfig{
    StorageMode: "local",
    Config: models.LocalConfig{
        UploadPath: "uploads",
        BaseURL:    "/uploads",
        MaxSize:    10,
        AllowedExt: []string{"jpg", "png", "gif"},
    },
}
```

### 4.2 OSS存储配置

```go
config := &models.UploadConfig{
    StorageMode: "oss",
    Config: models.OSSConfig{
        Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
        AccessKeyID:     "your-access-key-id",
        AccessKeySecret: "your-access-key-secret",
        BucketName:      "your-bucket-name",
        BaseURL:         "https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com",
    },
}
```

### 4.3 七牛云存储配置

```go
config := &models.UploadConfig{
    StorageMode: "qiniu",
    Config: models.QiniuConfig{
        AccessKey: "your-access-key",
        SecretKey: "your-secret-key",
        Bucket:    "your-bucket-name",
        Domain:    "https://cdn.example.com",
        Zone:      "z0",  // 华东区域
        UseHTTPS:  true,
    },
}

// 或者使用方法设置
config.SetQiniuConfig(&models.QiniuConfig{
    AccessKey: "your-access-key",
    SecretKey: "your-secret-key",
    Bucket:    "your-bucket-name",
    Domain:    "https://cdn.example.com",
    Zone:      "z0",
    UseHTTPS:  true,
})
```

### 4.4 CDN配置

```go
// 启用CDN
config.EnableCdn = 1
config.CdnDomain = "https://cdn.example.com"

// 或者使用方法设置
config.SetCDNConfig(&models.CDNConfig{
    Domain: "https://cdn.example.com",
})
```

## 5. 错误码

| 错误码 | 描述 |
|-------|------|
| 10001 | 配置不存在 |
| 10002 | 存储模式不支持 |
| 10003 | 配置解析失败 |
| 10004 | 配置保存失败 |

## 6. 最佳实践

1. **配置验证**：在保存配置前验证配置项的有效性
2. **敏感信息**：确保敏感信息（如AccessKey）加密存储
3. **错误处理**：实现完善的错误处理和日志记录
4. **性能优化**：对于频繁访问的配置，考虑使用缓存
5. **备份恢复**：定期备份配置，实现配置回滚功能

## 7. 后续扩展

1. 支持更多云存储服务商
2. 实现配置版本控制
3. 添加配置变更审计日志
4. 支持配置导入导出
5. 实现配置模板功能
6. 实现自动文件备份和复制机制
