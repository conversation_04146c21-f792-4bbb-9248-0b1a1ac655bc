/**
 * AI API数据传输对象
 *
 * 本文件定义了与AI API相关的请求和响应的数据传输对象，
 * 包括DeepSeek AI聊天补全接口的请求和响应结构体。
 */

package dto

// AIChatRequestDTO 用于前端发送AI聊天请求的DTO
type AIChatRequestDTO struct {
	Messages    []AIChatMessageDTO `json:"messages" valid:"Required"`    // 消息列表
	Temperature float64            `json:"temperature,omitempty"`        // 温度参数，控制随机性，范围-2.0到2.0，默认为0
	MaxTokens   int                `json:"max_tokens,omitempty"`         // 最大生成token数，默认为4096
	Stream      bool               `json:"stream,omitempty"`             // 是否启用流式响应
	Model       string             `json:"model,omitempty"`              // 使用的模型，默认为deepseek-chat
	Role        string             `json:"role,omitempty" valid:"Alpha"` // 角色定义，用于系统消息前缀
}

// AIChatMessageDTO 聊天消息DTO
type AIChatMessageDTO struct {
	Role    string `json:"role" valid:"Required;Alpha"`   // 消息角色，可选值：system、user、assistant、tool
	Content string `json:"content" valid:"Required"`      // 消息内容
	Name    string `json:"name,omitempty" valid:"Alpha"`  // 参与者名称，可选
}

// AIChatResponseDTO AI聊天响应DTO
type AIChatResponseDTO struct {
	ID      string              `json:"id"`                // 对话的唯一标识符
	Choices []AIChatChoiceDTO   `json:"choices"`           // 模型生成的completion选择列表
	Usage   AIChatUsageDTO      `json:"usage"`             // 使用统计信息
	Created int64               `json:"created"`           // 创建时间戳
	Model   string              `json:"model"`             // 使用的模型
	Object  string              `json:"object"`            // 对象类型
}

// AIChatChoiceDTO AI聊天选择DTO
type AIChatChoiceDTO struct {
	Index        int                `json:"index"`                  // 索引
	Message      AIChatMessageDTO   `json:"message"`                // 消息内容
	FinishReason string             `json:"finish_reason"`          // 结束原因
}

// AIChatUsageDTO AI聊天使用统计DTO
type AIChatUsageDTO struct {
	PromptTokens     int `json:"prompt_tokens"`      // 提示token数
	CompletionTokens int `json:"completion_tokens"`  // 完成token数
	TotalTokens      int `json:"total_tokens"`       // 总token数
}

// AIConfigDTO AI配置DTO
type AIConfigDTO struct {
	DeepSeekAPIKey string `json:"deepseek_api_key"` // DeepSeek API密钥
	BaseURL        string `json:"base_url"`         // API基础URL
	DefaultModel   string `json:"default_model"`    // 默认使用的模型
}
