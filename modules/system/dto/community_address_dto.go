/**
 * community_address_dto.go
 * 社区地址数据传输对象
 *
 * 本文件定义了社区地址相关的请求和响应数据结构
 */

package dto

import (
	"time"
)

// 请求DTO

// CreateCommunityAddressRequest 创建社区地址请求
type CreateCommunityAddressRequest struct {
	Name        string  `json:"name" valid:"Required;MaxSize(100);NoSpecialChars" description:"名称"`  // 名称
	ParentId    int64   `json:"parentId" description:"父级ID"`                                        // 父级ID，0表示顶级
	Level       int     `json:"level" valid:"Required;Range(1,3)" description:"级别：1-小区，2-楼栋，3-单元"` // 级别
	Longitude   float64 `json:"longitude" valid:"Required" description:"经度"`                        // 经度
	Latitude    float64 `json:"latitude" valid:"Required" description:"纬度"`                         // 纬度
	Sort        int     `json:"sort" description:"排序"`                                              // 排序
	Status      int8    `json:"status" valid:"Range(0,1)" description:"状态：1-启用，0-禁用"`              // 状态：1=启用，0=禁用
	Description string  `json:"description" valid:"MaxSize(500)" description:"描述"`                   // 描述
}

// UpdateCommunityAddressRequest 更新社区地址请求
type UpdateCommunityAddressRequest struct {
	Id          int64   `json:"id" valid:"Required;Min(1)" description:"地址ID"`                       // 地址ID
	Name        string  `json:"name" valid:"Required;MaxSize(100);NoSpecialChars" description:"名称"`  // 名称
	ParentId    int64   `json:"parentId" description:"父级ID"`                                        // 父级ID
	Level       int     `json:"level" valid:"Required;Range(1,3)" description:"级别：1-小区，2-楼栋，3-单元"` // 级别
	Longitude   float64 `json:"longitude" valid:"Required" description:"经度"`                        // 经度
	Latitude    float64 `json:"latitude" valid:"Required" description:"纬度"`                         // 纬度
	Sort        int     `json:"sort" description:"排序"`                                              // 排序
	Status      int8    `json:"status" valid:"Range(0,1)" description:"状态：1-启用，0-禁用"`              // 状态：1=启用，0=禁用
	Description string  `json:"description" valid:"MaxSize(500)" description:"描述"`                   // 描述
}

// CommunityAddressQueryRequest 社区地址查询请求
type CommunityAddressQueryRequest struct {
	Page     int    `json:"page" form:"page" description:"页码"`                // 页码
	PageSize int    `json:"pageSize" form:"pageSize" description:"每页数量"`      // 每页数量
	Name     string `json:"name" form:"name" description:"名称"`               // 名称
	Level    int    `json:"level" form:"level" description:"级别"`             // 级别
	ParentId int64  `json:"parentId" form:"parentId" description:"父级ID"`     // 父级ID
	Status   int8   `json:"status" form:"status" description:"状态：-1-全部，0-禁用，1-启用"` // 状态
}

// 响应DTO

// CommunityAddressResponse 社区地址响应
type CommunityAddressResponse struct {
	Id          int64     `json:"id" description:"地址ID"`              // 地址ID
	Name        string    `json:"name" description:"名称"`              // 名称
	ParentId    int64     `json:"parentId" description:"父级ID"`         // 父级ID
	Level       int       `json:"level" description:"级别：1-小区，2-楼栋，3-单元"` // 级别
	Longitude   float64   `json:"longitude" description:"经度"`          // 经度
	Latitude    float64   `json:"latitude" description:"纬度"`           // 纬度
	FullPath    string    `json:"fullPath" description:"完整路径"`         // 完整路径
	Sort        int       `json:"sort" description:"排序"`               // 排序
	Status      int8      `json:"status" description:"状态：1-启用，0-禁用"`    // 状态
	Description string    `json:"description" description:"描述"`        // 描述
	CreatedAt   time.Time `json:"createdAt" description:"创建时间"`        // 创建时间
	UpdatedAt   time.Time `json:"updatedAt" description:"更新时间"`        // 更新时间
	Children    []CommunityAddressResponse `json:"children,omitempty" description:"子地址列表"` // 子地址列表
}

// CommunityAddressListResponse 社区地址列表响应
type CommunityAddressListResponse struct {
	Items []CommunityAddressResponse `json:"items" description:"地址列表"` // 地址列表
	Total int64                      `json:"total" description:"总记录数"` // 总记录数
}

// CommunityAddressTreeResponse 社区地址树形结构响应
type CommunityAddressTreeResponse struct {
	Items []CommunityAddressResponse `json:"items" description:"地址树形结构"` // 地址树形结构
}

// CommunityAddressOption 地址选择器选项
type CommunityAddressOption struct {
	Value    int64                   `json:"value" description:"值"`      // 值
	Label    string                  `json:"label" description:"标签"`     // 标签
	Children []CommunityAddressOption `json:"children,omitempty" description:"子选项"` // 子选项
	Longitude float64                `json:"longitude" description:"经度"`   // 经度
	Latitude  float64                `json:"latitude" description:"纬度"`    // 纬度
	Level     int                    `json:"level" description:"级别"`      // 级别
}

// CommunityAddressOptionsResponse 地址选择器选项响应
type CommunityAddressOptionsResponse struct {
	Options []CommunityAddressOption `json:"options" description:"地址选项"` // 地址选项
}

// SelectedCommunityAddressRequest 已选地址请求
type SelectedCommunityAddressRequest struct {
	CommunityId int64 `json:"communityId" valid:"Required;Min(1)" description:"小区ID"`  // 小区ID
	BuildingId  int64 `json:"buildingId" valid:"Required;Min(1)" description:"楼栋ID"`   // 楼栋ID
	UnitId      int64 `json:"unitId" valid:"Required;Min(1)" description:"单元ID"`      // 单元ID
}

// SelectedCommunityAddressResponse 已选地址响应
type SelectedCommunityAddressResponse struct {
	FullPath  string  `json:"fullPath" description:"完整地址"`  // 完整地址
	Longitude float64 `json:"longitude" description:"经度"`   // 经度
	Latitude  float64 `json:"latitude" description:"纬度"`    // 纬度
}
