/*
 * file_usage_config_dto.go
 * 文件用途配置数据传输对象
 *
 * 本文件定义了文件用途配置相关的请求和响应数据结构
 */

package dto

// 请求DTO

// SaveFileUsageConfigRequest 保存文件用途配置请求
type SaveFileUsageConfigRequest struct {
	ID             int64  `json:"id" description:"配置ID（0表示新增）"`                                                   // 配置ID（0表示新增）
	UsageCode      string `json:"usage_code" valid:"Required;MaxSize(50)" description:"用途代码"`                    // 用途代码
	UsageName      string `json:"usage_name" valid:"Required;MaxSize(100)" description:"用途名称"`                  // 用途名称
	Description    string `json:"description" valid:"MaxSize(500)" description:"用途描述"`                         // 用途描述
	AllowAnonymous int8   `json:"allow_anonymous" valid:"Range(0,1)" description:"是否允许匿名上传：1-是，0-否"`           // 是否允许匿名上传
	MaxFileSize    int64  `json:"max_file_size" valid:"Min(0)" description:"最大文件大小（字节），0表示使用全局配置"`             // 最大文件大小
	AllowedTypes   string `json:"allowed_types" valid:"MaxSize(500)" description:"允许的文件类型（逗号分隔），空表示使用全局配置"` // 允许的文件类型
	SortOrder      int    `json:"sort_order" valid:"Min(0)" description:"排序顺序"`                               // 排序顺序
	Status         int8   `json:"status" valid:"Required;Range(0,1)" description:"状态：1-启用，0-禁用"`               // 状态
	Remark         string `json:"remark" valid:"MaxSize(500)" description:"备注"`                                // 备注
}

// FileUsageConfigListRequest 文件用途配置列表请求
type FileUsageConfigListRequest struct {
	Page       int    `json:"page" valid:"Min(1)" description:"页码"`                                    // 页码
	PageSize   int    `json:"page_size" valid:"Range(1,100)" description:"每页数量"`                     // 每页数量
	UsageCode  string `json:"usage_code" valid:"MaxSize(50)" description:"用途代码（模糊查询）"`              // 用途代码（模糊查询）
	UsageName  string `json:"usage_name" valid:"MaxSize(100)" description:"用途名称（模糊查询）"`             // 用途名称（模糊查询）
	Status     *int8  `json:"status" valid:"Range(0,1)" description:"状态：1-启用，0-禁用，null-全部"`          // 状态过滤
	SortField  string `json:"sort_field" valid:"MaxSize(50)" description:"排序字段"`                    // 排序字段
	SortOrder  string `json:"sort_order" valid:"MaxSize(10)" description:"排序方向：asc-升序，desc-降序"`     // 排序方向
}

// 响应DTO

// FileUsageConfigResponse 文件用途配置响应
type FileUsageConfigResponse struct {
	ID             int64  `json:"id" description:"配置ID"`                                    // 配置ID
	UsageCode      string `json:"usage_code" description:"用途代码"`                          // 用途代码
	UsageName      string `json:"usage_name" description:"用途名称"`                          // 用途名称
	Description    string `json:"description" description:"用途描述"`                         // 用途描述
	AllowAnonymous int8   `json:"allow_anonymous" description:"是否允许匿名上传：1-是，0-否"`           // 是否允许匿名上传
	AllowAnonymousText string `json:"allow_anonymous_text" description:"是否允许匿名上传文本"`       // 是否允许匿名上传文本
	MaxFileSize    int64  `json:"max_file_size" description:"最大文件大小（字节），0表示使用全局配置"`         // 最大文件大小
	MaxFileSizeMB  int    `json:"max_file_size_mb" description:"最大文件大小（MB）"`              // 最大文件大小（MB）
	AllowedTypes   string `json:"allowed_types" description:"允许的文件类型（逗号分隔），空表示使用全局配置"` // 允许的文件类型
	SortOrder      int    `json:"sort_order" description:"排序顺序"`                         // 排序顺序
	Status         int8   `json:"status" description:"状态：1-启用，0-禁用"`                     // 状态
	StatusText     string `json:"status_text" description:"状态文本"`                        // 状态文本
	Remark         string `json:"remark" description:"备注"`                                // 备注
	CreatedAt      string `json:"created_at" description:"创建时间"`                         // 创建时间
	UpdatedAt      string `json:"updated_at" description:"更新时间"`                         // 更新时间
}

// FileUsageConfigListResponse 文件用途配置列表响应
type FileUsageConfigListResponse struct {
	Total int                          `json:"total" description:"总数量"`     // 总数量
	List  []FileUsageConfigResponse    `json:"list" description:"配置列表"`    // 配置列表
}

// FileUsageConfigSimpleResponse 文件用途配置简单响应（用于下拉选择）
type FileUsageConfigSimpleResponse struct {
	UsageCode string `json:"usage_code" description:"用途代码"` // 用途代码
	UsageName string `json:"usage_name" description:"用途名称"` // 用途名称
}

// FileUsageConfigOptionsResponse 文件用途配置选项响应
type FileUsageConfigOptionsResponse struct {
	AllowedUsageTypes   []string `json:"allowed_usage_types" description:"允许的用途类型列表"`   // 允许的用途类型列表
	AnonymousUsageTypes []string `json:"anonymous_usage_types" description:"允许匿名上传的用途类型列表"` // 允许匿名上传的用途类型列表
}