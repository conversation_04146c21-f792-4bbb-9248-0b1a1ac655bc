/**
 * sms_config_dto.go
 * 短信配置数据传输对象
 *
 * 本文件定义了短信配置相关的请求和响应数据结构
 */

package dto

// 请求DTO

// SaveSmsConfigRequest 保存短信配置请求
type SaveSmsConfigRequest struct {
	Id                   int64  `json:"id" description:"配置ID（0表示新增）"`                                                         // 配置ID（0表示新增）
	Provider             string `json:"provider" valid:"Required;MaxSize(50)" description:"短信服务提供商（aliyun/tencent/yunpian/submail等）"` // 短信服务提供商（aliyun/tencent/yunpian/submail等）
	AccessKey            string `json:"accessKey" valid:"Required;MaxSize(100)" description:"访问密钥ID（赛邮为app_id）"`                         // 访问密钥ID（赛邮为app_id）
	AccessSecret         string `json:"accessSecret" valid:"Required;MaxSize(200)" description:"访问密钥密码（赛邮为app_key）"`                      // 访问密钥密码（赛邮为app_key）
	SignName             string `json:"signName" valid:"Required;MaxSize(50)" description:"短信签名（各平台相同）"`                             // 短信签名（各平台相同）
	TemplateCodeRegister string `json:"templateCodeRegister" valid:"Required;MaxSize(50)" description:"注册验证码模板ID（赛邮为短信项目ID）"`            // 注册验证码模板ID（赛邮为短信项目ID）
	TemplateCodeLogin    string `json:"templateCodeLogin" valid:"Required;MaxSize(50)" description:"登录验证码模板ID（赛邮为短信项目ID）"`               // 登录验证码模板ID（赛邮为短信项目ID）
	TemplateCodeResetPwd string `json:"templateCodeResetPwd" valid:"Required;MaxSize(50)" description:"重置密码验证码模板ID（赛邮为短信项目ID）"`          // 重置密码验证码模板ID（赛邮为短信项目ID）
	TemplateCodeNotice   string `json:"templateCodeNotice" valid:"Required;MaxSize(50)" description:"通知消息模板ID（赛邮为短信项目ID）"`               // 通知消息模板ID（赛邮为短信项目ID）
	DailyLimit           int    `json:"dailyLimit" valid:"Required;Min(1)" description:"每日发送上限"`                              // 每日发送上限
	Status               int8   `json:"status" valid:"Required;Range(0,1)" description:"状态：1-启用，0-禁用"`                        // 状态：1=启用，0=禁用
	Remark               string `json:"remark" valid:"MaxSize(500)" description:"备注"`                                         // 备注
}

// 响应DTO

// SmsConfigResponse 短信配置响应
type SmsConfigResponse struct {
	Id                   int64  `json:"id" description:"配置ID"`                           // 配置ID
	Provider             string `json:"provider" description:"短信服务提供商（aliyun/tencent/yunpian/submail等）"`                  // 短信服务提供商（aliyun/tencent/yunpian/submail等）
	ProviderText         string `json:"providerText" description:"短信服务提供商文本"`            // 短信服务提供商文本
	AccessKey            string `json:"accessKey" description:"访问密钥ID"`                  // 访问密钥ID
	AccessSecret         string `json:"accessSecret,omitempty" description:"访问密钥密码（脱敏）"` // 访问密钥密码（脱敏）
	SignName             string `json:"signName" description:"短信签名"`                     // 短信签名
	TemplateCodeRegister string `json:"templateCodeRegister" description:"注册验证码模板ID"`    // 注册验证码模板ID
	TemplateCodeLogin    string `json:"templateCodeLogin" description:"登录验证码模板ID"`       // 登录验证码模板ID
	TemplateCodeResetPwd string `json:"templateCodeResetPwd" description:"重置密码验证码模板ID"`  // 重置密码验证码模板ID
	TemplateCodeNotice   string `json:"templateCodeNotice" description:"通知消息模板ID"`       // 通知消息模板ID
	DailyLimit           int    `json:"dailyLimit" description:"每日发送上限"`                 // 每日发送上限
	Status               int8   `json:"status" description:"状态：1-启用，0-禁用"`               // 状态：1=启用，0=禁用
	StatusText           string `json:"statusText" description:"状态文本"`                   // 状态文本
	Remark               string `json:"remark" description:"备注"`                         // 备注
	CreatedAt            string `json:"createdAt" description:"创建时间"`                    // 创建时间
	UpdatedAt            string `json:"updatedAt" description:"更新时间"`                    // 更新时间
}

// SmsConfigBaseResponse 短信配置基本信息响应（前台使用）
type SmsConfigBaseResponse struct {
	Provider   string `json:"provider" description:"短信服务提供商"`    // 短信服务提供商
	SignName   string `json:"signName" description:"短信签名"`       // 短信签名
	DailyLimit int    `json:"dailyLimit" description:"每日发送上限"`   // 每日发送上限
	Status     int8   `json:"status" description:"状态：1-启用，0-禁用"` // 状态：1=启用，0=禁用
}
