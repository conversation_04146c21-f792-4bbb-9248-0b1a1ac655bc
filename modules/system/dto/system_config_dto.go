/**
 * system_config_dto.go
 * 系统配置数据传输对象
 *
 * 本文件定义了系统配置相关的请求和响应数据结构
 */

package dto

import (
	"time"
)

// 请求DTO

// CreateSystemConfigRequest 创建系统配置请求
type CreateSystemConfigRequest struct {
	ConfigKey   string `json:"configKey" valid:"Required;MaxSize(100);ConfigKey" description:"配置键名"`  // 配置键名
	ConfigValue string `json:"configValue" valid:"Required" description:"配置值"`                        // 配置值
	ConfigType  string `json:"configType" valid:"Required;MaxSize(50);ConfigType" description:"配置类型"` // 配置类型
	Category    string `json:"category" valid:"MaxSize(50)" description:"配置分类"`                       // 配置分类
	Description string `json:"description" valid:"MaxSize(500);NoSpecialChars" description:"配置描述"`    // 配置描述
	Status      int8   `json:"status" valid:"Range(0,1)" description:"状态：1-启用，0-禁用"`                  // 状态：1=启用，0=禁用
	IsSystem    int8   `json:"isSystem" valid:"Range(0,1)" description:"是否系统级配置：1-是，0-否"`             // 是否系统级配置：1=是，0=否
}

// UpdateSystemConfigRequest 更新系统配置请求
type UpdateSystemConfigRequest struct {
	Id          int64  `json:"id" valid:"Required;Min(1)" description:"配置ID"`                         // 配置ID
	ConfigKey   string `json:"configKey" valid:"Required;MaxSize(100);ConfigKey" description:"配置键名"`  // 配置键名
	ConfigValue string `json:"configValue" valid:"Required" description:"配置值"`                        // 配置值
	ConfigType  string `json:"configType" valid:"Required;MaxSize(50);ConfigType" description:"配置类型"` // 配置类型
	Category    string `json:"category" valid:"MaxSize(50)" description:"配置分类"`                       // 配置分类
	Description string `json:"description" valid:"MaxSize(500);NoSpecialChars" description:"配置描述"`    // 配置描述
	Status      int8   `json:"status" valid:"Range(0,1)" description:"状态：1-启用，0-禁用"`                  // 状态：1=启用，0=禁用
	IsSystem    int8   `json:"isSystem" valid:"Range(0,1)" description:"是否系统级配置：1-是，0-否"`             // 是否系统级配置：1=是，0=否
}

// UpdateConfigValueRequest 更新配置值请求
type UpdateConfigValueRequest struct {
	ConfigKey   string `json:"configKey" valid:"Required;MaxSize(100);ConfigKey" description:"配置键名"` // 配置键名
	ConfigValue string `json:"configValue" valid:"Required" description:"新配置值"`                      // 新配置值
}

// GetConfigsByKeysRequest 批量获取系统配置请求
type GetConfigsByKeysRequest struct {
	Keys []string `json:"keys" valid:"Required" description:"配置键名列表"` // 配置键名列表
}

// ConfigQueryRequest 配置查询请求
type ConfigQueryRequest struct {
	// 页码
	Page int `json:"page" form:"page" description:"页码"`
	// 每页数量
	PageSize int `json:"pageSize" form:"pageSize" description:"每页数量"`
	// 配置键
	ConfigKey string `json:"configKey" form:"configKey" description:"配置键"`
	// 配置类型
	ConfigType string `json:"configType" form:"configType" description:"配置类型"`
	// 状态
	Status int8 `json:"status" form:"status" description:"状态：-1-全部，0-禁用，1-启用"`
}

// MaintenanceModeRequest 维护模式请求
type MaintenanceModeRequest struct {
	// 是否开启维护模式
	Enabled bool `json:"enabled" valid:"Required" description:"是否开启维护模式"`
	// 维护消息
	Message string `json:"message" valid:"Required;MaxSize(500);NoSpecialChars" description:"维护消息"`
	// 预计恢复时间
	EstimatedRecoveryTime string `json:"estimatedRecoveryTime" valid:"MaxSize(50)" description:"预计恢复时间"`
}

// 响应DTO

// SystemConfigResponse 系统配置响应
type SystemConfigResponse struct {
	Id          int64     `json:"id" description:"配置ID"`                  // 配置ID
	ConfigKey   string    `json:"configKey" description:"配置键名"`           // 配置键名
	ConfigValue string    `json:"configValue" description:"配置值"`          // 配置值
	ConfigType  string    `json:"configType" description:"配置类型"`          // 配置类型
	Category    string    `json:"category" description:"配置分类"`            // 配置分类
	Description string    `json:"description" description:"配置描述"`         // 配置描述
	Version     int       `json:"version" description:"版本号"`              // 版本号
	Status      int8      `json:"status" description:"状态：1-启用，0-禁用"`      // 状态：1=启用，0=禁用
	IsSystem    int8      `json:"isSystem" description:"是否系统级配置：1-是，0-否"` // 是否系统级配置：1=是，0=否
	CreatedAt   time.Time `json:"createdAt" description:"创建时间"`           // 创建时间
	UpdatedAt   time.Time `json:"updatedAt" description:"更新时间"`           // 更新时间
}

// SystemConfigKeyValueResponse 系统配置键值对响应
type SystemConfigKeyValueResponse struct {
	Key   string `json:"key" description:"配置键名"`  // 配置键名
	Value string `json:"value" description:"配置值"` // 配置值
	Type  string `json:"type" description:"配置类型"` // 配置类型
}

// SystemConfigsResponse 系统配置列表响应
type SystemConfigsResponse struct {
	Items []SystemConfigResponse `json:"items" description:"配置列表"` // 配置列表
	Total int64                  `json:"total" description:"总记录数"` // 总记录数
}

// SystemInfoResponse 系统基本信息响应
type SystemInfoResponse struct {
	SiteName      string `json:"siteName" description:"网站名称"`       // 网站名称
	SiteVersion   string `json:"siteVersion" description:"网站版本"`    // 网站版本
	ApiVersion    string `json:"apiVersion" description:"API版本"`    // API版本
	SiteLogo      string `json:"siteLogo" description:"网站Logo"`     // 网站Logo
	SiteFavicon   string `json:"siteFavicon" description:"网站图标"`    // 网站图标
	Copyright     string `json:"copyright" description:"版权信息"`      // 版权信息
	ConfigVersion string `json:"configVersion" description:"配置版本号"` // 配置版本号
	Email         string `json:"email" description:"联系邮箱"`          // 联系邮箱
	Phone         string `json:"phone" description:"联系电话"`          // 联系电话
	Address       string `json:"address" description:"联系地址"`        // 联系地址
	Region        string `json:"region" description:"地区"`           // 地区
	// 最后更新时间
	UpdatedAt time.Time `json:"updatedAt" description:"最后更新时间"`
}

// ContactInfoResponse 联系方式响应
type ContactInfoResponse struct {
	Email       string `json:"email" description:"联系邮箱"`       // 联系邮箱
	Phone       string `json:"phone" description:"联系电话"`       // 联系电话
	Address     string `json:"address" description:"联系地址"`     // 联系地址
	WorkTime    string `json:"workTime" description:"工作时间"`    // 工作时间
	QQ          string `json:"qq" description:"QQ客服"`          // QQ客服
	WeChat      string `json:"wechat" description:"微信客服"`      // 微信客服
	CompanyName string `json:"companyName" description:"公司名称"` // 公司名称
	Longitude   string `json:"longitude" description:"经度"`     // 经度
	Latitude    string `json:"latitude" description:"纬度"`      // 纬度
}

// MaintenanceModeResponse 维护模式响应
type MaintenanceModeResponse struct {
	// 是否开启维护模式
	Enabled bool `json:"enabled" description:"是否开启维护模式"`
	// 维护消息
	Message string `json:"message" description:"维护消息"`
	// 预计恢复时间
	EstimatedRecoveryTime string `json:"estimatedRecoveryTime" description:"预计恢复时间"`
}

// MultiConfigResponse 多配置查询响应
type MultiConfigResponse struct {
	// 配置数据，键值对形式
	Configs map[string]interface{} `json:"configs" description:"配置数据"`
}

// ConfigCacheInfoResponse 配置缓存信息响应
type ConfigCacheInfoResponse struct {
	// 缓存项总数
	TotalItems int `json:"totalItems" description:"缓存项总数"`
	// 上次缓存刷新时间
	LastRefreshTime time.Time `json:"lastRefreshTime" description:"上次缓存刷新时间"`
	// 是否开启缓存
	CacheEnabled bool `json:"cacheEnabled" description:"是否开启缓存"`
}
