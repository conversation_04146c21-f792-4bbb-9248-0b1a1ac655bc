/**
 * system_notice_dto.go
 * 系统公告数据传输对象
 *
 * 本文件定义了系统公告相关的请求和响应数据结构
 */

package dto

import (
	"time"
)

// 请求DTO

// CreateSystemNoticeRequest 创建系统公告请求
type CreateSystemNoticeRequest struct {
	Title     string    `json:"title" valid:"Required;MaxSize(200)" description:"公告标题"`                             // 公告标题
	Content   string    `json:"content" valid:"Required" description:"公告内容"`                                        // 公告内容
	Type      int8      `json:"type" valid:"Required;Range(1,3)" description:"公告类型：1-普通公告，2-紧急公告，3-维护公告"`           // 公告类型：1=普通公告，2=紧急公告，3=维护公告
	StartTime time.Time `json:"startTime" valid:"Required" description:"公告生效时间"`                                    // 公告生效时间
	EndTime   time.Time `json:"endTime" description:"公告结束时间"`                                                       // 公告结束时间
	Target    int8      `json:"target" valid:"Required;Range(0,4)" description:"目标用户：0-所有用户，1-用户，2-商家，3-跑腿员，4-管理员"` // 目标用户：0=所有用户，1=用户，2=商家，3=跑腿员，4=管理员
	Status    int8      `json:"status" valid:"Required;Range(0,1)" description:"状态：1-已发布，0-未发布"`                    // 状态：1=已发布，0=未发布
	IsTop     int8      `json:"isTop" valid:"Range(0,1)" description:"是否置顶：1-是，0-否"`                                // 是否置顶：1=是，0=否
}

// UpdateSystemNoticeRequest 更新系统公告请求
type UpdateSystemNoticeRequest struct {
	Id        int64     `json:"id" valid:"Required;Min(1)" description:"公告ID"`                                      // 公告ID
	Title     string    `json:"title" valid:"Required;MaxSize(200)" description:"公告标题"`                             // 公告标题
	Content   string    `json:"content" valid:"Required" description:"公告内容"`                                        // 公告内容
	Type      int8      `json:"type" valid:"Required;Range(1,3)" description:"公告类型：1-普通公告，2-紧急公告，3-维护公告"`           // 公告类型：1=普通公告，2=紧急公告，3=维护公告
	StartTime time.Time `json:"startTime" valid:"Required" description:"公告生效时间"`                                    // 公告生效时间
	EndTime   time.Time `json:"endTime" description:"公告结束时间"`                                                       // 公告结束时间
	Target    int8      `json:"target" valid:"Required;Range(0,4)" description:"目标用户：0-所有用户，1-用户，2-商家，3-跑腿员，4-管理员"` // 目标用户：0=所有用户，1=用户，2=商家，3=跑腿员，4=管理员
	Status    int8      `json:"status" valid:"Required;Range(0,1)" description:"状态：1-已发布，0-未发布"`                    // 状态：1=已发布，0=未发布
	IsTop     int8      `json:"isTop" valid:"Range(0,1)" description:"是否置顶：1-是，0-否"`                                // 是否置顶：1=是，0=否
}

// UpdateNoticeStatusRequest 更新公告状态请求
type UpdateNoticeStatusRequest struct {
	Id     int64 `json:"id" valid:"Required;Min(1)" description:"公告ID"`                   // 公告ID
	Status int8  `json:"status" valid:"Required;Range(0,1)" description:"状态：1-已发布，0-未发布"` // 状态：1=已发布，0=未发布
}

// UpdateNoticeTopStatusRequest 更新公告置顶状态请求
type UpdateNoticeTopStatusRequest struct {
	Id    int64 `json:"id" valid:"Required;Min(1)" description:"公告ID"`                // 公告ID
	IsTop int8  `json:"isTop" valid:"Required;Range(0,1)" description:"是否置顶：1-是，0-否"` // 是否置顶：1=是，0=否
}

// GetSystemNoticesRequest 获取系统公告列表请求
type GetSystemNoticesRequest struct {
	Page     int    `json:"page" valid:"Required;Min(1)" description:"页码"`                                    // 页码
	PageSize int    `json:"pageSize" valid:"Required;Range(1,100)" description:"每页记录数"`                       // 每页记录数
	Title    string `json:"title" description:"标题（模糊匹配）"`                                                     // 标题（模糊匹配）
	Type     int8   `json:"type" valid:"Range(-1,3)" description:"公告类型：-1-全部，1-普通公告，2-紧急公告，3-维护公告"`           // 公告类型：-1=全部，1=普通公告，2=紧急公告，3=维护公告
	Status   int8   `json:"status" valid:"Range(-1,1)" description:"状态：-1-全部，1-已发布，0-未发布"`                    // 状态：-1=全部，1=已发布，0=未发布
	Target   int8   `json:"target" valid:"Range(-1,4)" description:"目标用户：-1-全部，0-所有用户，1-用户，2-商家，3-跑腿员，4-管理员"` // 目标用户：-1=全部，0=所有用户，1=用户，2=商家，3=跑腿员，4=管理员
	IsTop    int8   `json:"isTop" valid:"Range(-1,1)" description:"是否置顶：-1-全部，1-是，0-否"`                       // 是否置顶：-1=全部，1=是，0=否
}

// 响应DTO

// SystemNoticeResponse 系统公告响应
type SystemNoticeResponse struct {
	Id         int64  `json:"id" description:"公告ID"`           // 公告ID
	Title      string `json:"title" description:"公告标题"`        // 公告标题
	Content    string `json:"content" description:"公告内容"`      // 公告内容
	Type       int8   `json:"type" description:"公告类型"`         // 公告类型
	TypeText   string `json:"typeText" description:"公告类型文本"`   // 公告类型文本
	StartTime  string `json:"startTime" description:"公告生效时间"`  // 公告生效时间
	EndTime    string `json:"endTime" description:"公告结束时间"`    // 公告结束时间
	Target     int8   `json:"target" description:"目标用户"`       // 目标用户
	TargetText string `json:"targetText" description:"目标用户文本"` // 目标用户文本
	Status     int8   `json:"status" description:"状态"`         // 状态
	StatusText string `json:"statusText" description:"状态文本"`   // 状态文本
	IsTop      int8   `json:"isTop" description:"是否置顶"`        // 是否置顶
	CreatedBy  int64  `json:"createdBy" description:"创建人ID"`   // 创建人ID
	CreatedAt  string `json:"createdAt" description:"创建时间"`    // 创建时间
	UpdatedAt  string `json:"updatedAt" description:"更新时间"`    // 更新时间
}

// SystemNoticesResponse 系统公告列表响应
type SystemNoticesResponse struct {
	Items []SystemNoticeResponse `json:"items" description:"公告列表"` // 公告列表
	Total int64                  `json:"total" description:"总记录数"` // 总记录数
}

// SystemNoticeBaseResponse 简化的系统公告响应（用于前台展示）
type SystemNoticeBaseResponse struct {
	Id        int64  `json:"id" description:"公告ID"`          // 公告ID
	Title     string `json:"title" description:"公告标题"`       // 公告标题
	Content   string `json:"content" description:"公告内容"`     // 公告内容
	Type      int8   `json:"type" description:"公告类型"`        // 公告类型
	TypeText  string `json:"typeText" description:"公告类型文本"`  // 公告类型文本
	StartTime string `json:"startTime" description:"公告生效时间"` // 公告生效时间
	EndTime   string `json:"endTime" description:"公告结束时间"`   // 公告结束时间
	IsTop     int8   `json:"isTop" description:"是否置顶"`       // 是否置顶
}
