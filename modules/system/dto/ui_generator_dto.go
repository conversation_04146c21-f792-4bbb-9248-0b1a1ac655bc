/**
 * UI生成器数据传输对象
 *
 * 本文件定义了用于UI配置生成的请求和响应结构体
 */

package dto

// UIGeneratorRequestDTO UI配置生成请求DTO
type UIGeneratorRequestDTO struct {
	ModelType  string `json:"model_type,omitempty"`  // 模型类型，例如"table_form"
	ModelData  interface{} `json:"model_data,omitempty"`  // 原始模型数据，可以是字符串或JSON对象
	Options    map[string]interface{} `json:"options,omitempty"`    // 可选参数
	Template   string `json:"template,omitempty"`   // 自定义模板
	// 新增字段 - 增强自定义模板功能
	PromptRules    string `json:"prompt_rules,omitempty"`    // 提示规则，如组件选择规则、布局规则等
	FormatSettings string `json:"format_settings,omitempty"` // 格式设置，如JSON结构要求
	Requirements   string `json:"requirements,omitempty"`    // 生成要求，如响应式设计、暗黑模式等
	Examples       string `json:"examples,omitempty"`        // 示例配置，提供参考
	ComponentTypes string `json:"component_types,omitempty"` // 使用的组件类型，如Ant Design、Element UI等
	UIFramework    string `json:"ui_framework,omitempty"`    // UI框架，如React、Vue等
	SpecialFields  []string `json:"special_fields,omitempty"` // 需要特殊处理的字段列表
	TemplateType   string `json:"template_type,omitempty"`   // 模板类型：basic、custom、advanced
}

// UIGeneratorResponseDTO UI配置生成响应DTO
type UIGeneratorResponseDTO struct {
	Config     interface{} `json:"config"`      // 生成的UI配置
	Messages   []string    `json:"messages,omitempty"`    // 可能的提示消息
	Status     string      `json:"status"`      // 状态，如"success"或"error"
	RawContent string      `json:"raw_content,omitempty"` // AI返回的原始内容，方便调试
}
