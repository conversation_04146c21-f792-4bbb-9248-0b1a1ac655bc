/**
 * upload_config_dto.go
 * 上传配置数据传输对象
 *
 * 本文件定义了上传配置相关的请求和响应数据结构
 */

package dto

import "encoding/json"

// 请求DTO

// SaveUploadConfigRequest 保存上传配置请求
type SaveUploadConfigRequest struct {
	Id                int64  `json:"id" description:"配置ID（0表示新增）"`                                                        // 配置ID（0表示新增）
	StorageMode       string `json:"storageMode" valid:"Required;MaxSize(20)" description:"存储模式（local/oss/cos/s3/qiniu）"` // 存储模式（local/oss/cos/s3/qiniu）
	MaxSize           int64  `json:"maxSize" valid:"Required;Min(1)" description:"最大文件大小"`                                // 最大文件大小
	AllowedExtensions string `json:"allowedExtensions" valid:"Required" description:"允许的文件扩展名（逗号分隔）"`                     // 允许的文件扩展名（逗号分隔）
	EnableCdn         int8   `json:"enableCdn" valid:"Range(0,1)" description:"是否启用CDN：1-是，0-否"`                          // 是否启用CDN：1=是，0=否
	CdnDomain         string `json:"cdnDomain" valid:"MaxSize(200)" description:"CDN域名"`                                  // CDN域名
	Status            int8   `json:"status" valid:"Required;Range(0,1)" description:"状态：1-启用，0-禁用"`                       // 状态：1=启用，0=禁用
	Remark            string `json:"remark" valid:"MaxSize(500)" description:"备注"`                                        // 备注

	// 注意: 之前在根级声明的七牛云配置字段已移至Config结构体内部
	// 前端传递的数据格式及解析方式请查看控制器处理方式

	// 配置字段，根据存储模式使用不同的配置
	Config ConfigRequest `json:"config" description:"存储方式的具体配置"` // 存储方式的具体配置
}

// ConfigRequest 配置请求，包含所有可能的配置字段
type ConfigRequest struct {
	// 存储模式
	StorageMode string `json:"storageMode" valid:"MaxSize(20)" description:"存储模式"` // 存储模式

	// 本地存储配置
	LocalPath string `json:"localPath" valid:"MaxSize(200)" description:"本地存储路径"` // 本地存储路径

	// CDN配置
	CdnAccessKey string `json:"cdnAccessKey" valid:"MaxSize(100)" description:"CDN访问密钥"` // CDN访问密钥
	CdnSecretKey string `json:"cdnSecretKey" valid:"MaxSize(200)" description:"CDN密钥密码"` // CDN密钥密码

	// 阿里云OSS配置
	OssEndpoint     string `json:"ossEndpoint" valid:"MaxSize(200)" description:"OSS服务端点"`       // OSS服务端点
	OssBucket       string `json:"ossBucket" valid:"MaxSize(100)" description:"OSS存储桶名称"`        // OSS存储桶名称
	OssAccessKey    string `json:"ossAccessKey" valid:"MaxSize(100)" description:"OSS访问密钥"`      // OSS访问密钥
	OssAccessSecret string `json:"ossAccessSecret" valid:"MaxSize(200)" description:"OSS访问密钥密码"` // OSS访问密钥密码
	OssDomain       string `json:"ossDomain" valid:"MaxSize(200)" description:"OSS自定义域名"`        // OSS自定义域名

	// 腾讯云COS配置
	CosRegion    string `json:"cosRegion" valid:"MaxSize(50)" description:"COS区域"`       // COS区域
	CosBucket    string `json:"cosBucket" valid:"MaxSize(100)" description:"COS存储桶名称"`   // COS存储桶名称
	CosSecretId  string `json:"cosSecretId" valid:"MaxSize(100)" description:"COS密钥ID"`  // COS密钥ID
	CosSecretKey string `json:"cosSecretKey" valid:"MaxSize(200)" description:"COS密钥密码"` // COS密钥密码
	CosDomain    string `json:"cosDomain" valid:"MaxSize(200)" description:"COS自定义域名"`   // COS自定义域名

	// AWS S3配置
	S3Region    string `json:"s3Region" valid:"MaxSize(50)" description:"S3区域"`       // S3区域
	S3Bucket    string `json:"s3Bucket" valid:"MaxSize(100)" description:"S3存储桶名称"`   // S3存储桶名称
	S3AccessKey string `json:"s3AccessKey" valid:"MaxSize(100)" description:"S3访问密钥"` // S3访问密钥
	S3SecretKey string `json:"s3SecretKey" valid:"MaxSize(200)" description:"S3密钥密码"` // S3密钥密码
	S3Domain    string `json:"s3Domain" valid:"MaxSize(200)" description:"S3自定义域名"`   // S3自定义域名

	// 七牛云存储配置
	QiniuConfig QiniuConfigRequest `json:"qiniuConfig" description:"七牛云存储配置"` // 七牛云存储配置

	// 这些字段用于兼容前端直接传递在config对象中的无前缀字段
	// 不会在JSON序列化中输出，仅用于接收前端数据
	AccessKey string `json:"accessKey,omitempty" valid:"MaxSize(100)" description:"通用访问密钥"`
	SecretKey string `json:"secretKey,omitempty" valid:"MaxSize(200)" description:"通用密钥密码"`
	Bucket    string `json:"bucket,omitempty" valid:"MaxSize(100)" description:"通用存储空间名称"`
	Domain    string `json:"domain,omitempty" valid:"MaxSize(200)" description:"通用自定义域名"`
	Zone      string `json:"zone,omitempty" valid:"MaxSize(50)" description:"通用存储区域"`
	UseHTTPS  bool   `json:"useHTTPS,omitempty" description:"通用是否使用HTTPS"`
}

// UnmarshalJSON 自定义JSON解析方法，处理配置对象中的字段映射
func (c *ConfigRequest) UnmarshalJSON(data []byte) error {
	// 创建临时结构体用于标准解析
	type TempConfig ConfigRequest
	var temp TempConfig

	// 先正常解析所有字段
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	// 拷贝临时结构体的值
	*c = ConfigRequest(temp)

	// 如果检测到通用字段被设置但QiniuConfig对应字段未设置，则自动映射
	// 无论是什么存储模式，都尝试映射通用字段到七牛云配置
	// 如果QiniuConfig中的字段为空，但通用字段有值，则映射过去
	if c.QiniuConfig.AccessKey == "" && c.AccessKey != "" {
		c.QiniuConfig.AccessKey = c.AccessKey
	}
	if c.QiniuConfig.SecretKey == "" && c.SecretKey != "" {
		c.QiniuConfig.SecretKey = c.SecretKey
	}
	if c.QiniuConfig.Bucket == "" && c.Bucket != "" {
		c.QiniuConfig.Bucket = c.Bucket
	}
	if c.QiniuConfig.Domain == "" && c.Domain != "" {
		c.QiniuConfig.Domain = c.Domain
	}
	if c.QiniuConfig.Zone == "" && c.Zone != "" {
		c.QiniuConfig.Zone = c.Zone
	}
	if !c.QiniuConfig.UseHTTPS && c.UseHTTPS {
		c.QiniuConfig.UseHTTPS = c.UseHTTPS
	}

	return nil
}

// QiniuConfigRequest 七牛云存储配置请求
type QiniuConfigRequest struct {
	AccessKey string `json:"accessKey" valid:"MaxSize(100)" description:"七牛云访问密钥"` // 七牛云访问密钥
	SecretKey string `json:"secretKey" valid:"MaxSize(200)" description:"七牛云密钥密码"` // 七牛云密钥密码
	Bucket    string `json:"bucket" valid:"MaxSize(100)" description:"七牛云存储空间名称"`  // 七牛云存储空间名称
	Domain    string `json:"domain" valid:"MaxSize(200)" description:"七牛云自定义域名"`   // 七牛云自定义域名
	Zone      string `json:"zone" valid:"MaxSize(50)" description:"七牛云存储区域"`       // 七牛云存储区域
	UseHTTPS  bool   `json:"useHTTPS" description:"是否使用HTTPS"`                     // 是否使用HTTPS
}

// 响应DTO

// UploadConfigResponse 上传配置响应
type UploadConfigResponse struct {
	Id                int64           `json:"id" description:"配置ID"`                            // 配置ID
	StorageMode       string          `json:"storageMode" description:"存储模式（local/oss/cos/s3）"` // 存储模式（local/oss/cos/s3）
	MaxSize           int64           `json:"maxSize" description:"最大文件大小"`                     // 最大文件大小
	MaxSizeMB         int             `json:"maxSizeMB" description:"最大文件大小（MB）"`               // 最大文件大小（MB）
	AllowedExtensions string          `json:"allowedExtensions" description:"允许的文件扩展名（逗号分隔）"`   // 允许的文件扩展名（逗号分隔）
	EnableCdn         int8            `json:"enableCdn" description:"是否启用CDN：1-是，0-否"`          // 是否启用CDN：1=是，0=否
	CdnDomain         string          `json:"cdnDomain" description:"CDN域名"`                    // CDN域名
	Config            *ConfigResponse `json:"config" description:"存储方式的具体配置"`                   // 存储方式的具体配置
	Status            int8            `json:"status" description:"状态：1-启用，0-禁用"`                // 状态：1=启用，0=禁用
	StatusText        string          `json:"statusText" description:"状态文本"`                    // 状态文本
	Remark            string          `json:"remark" description:"备注"`                          // 备注
	CreatedAt         string          `json:"createdAt" description:"创建时间"`                     // 创建时间
	UpdatedAt         string          `json:"updatedAt" description:"更新时间"`                     // 更新时间
}

// ConfigResponse 配置响应，包含全部配置信息
type ConfigResponse struct {
	// 本地存储配置
	LocalPath string `json:"localPath,omitempty" description:"本地存储路径"` // 本地存储路径

	// CDN配置
	CdnAccessKey string `json:"cdnAccessKey,omitempty" description:"CDN访问密钥"`     // CDN访问密钥
	CdnSecretKey string `json:"cdnSecretKey,omitempty" description:"CDN密钥密码（脱敏）"` // CDN密钥密码（脱敏）

	// 阿里云OSS配置
	OssEndpoint     string `json:"ossEndpoint,omitempty" description:"OSS服务端点"`           // OSS服务端点
	OssBucket       string `json:"ossBucket,omitempty" description:"OSS存储桶名称"`            // OSS存储桶名称
	OssAccessKey    string `json:"ossAccessKey,omitempty" description:"OSS访问密钥"`          // OSS访问密钥
	OssAccessSecret string `json:"ossAccessSecret,omitempty" description:"OSS访问密钥密码（脱敏）"` // OSS访问密钥密码（脱敏）
	OssDomain       string `json:"ossDomain,omitempty" description:"OSS自定义域名"`            // OSS自定义域名

	// 腾讯云COS配置
	CosRegion    string `json:"cosRegion,omitempty" description:"COS区域"`          // COS区域
	CosBucket    string `json:"cosBucket,omitempty" description:"COS存储桶名称"`       // COS存储桶名称
	CosSecretId  string `json:"cosSecretId,omitempty" description:"COS密钥ID"`      // COS密钥ID
	CosSecretKey string `json:"cosSecretKey,omitempty" description:"COS密钥密码（脱敏）"` // COS密钥密码（脱敏）
	CosDomain    string `json:"cosDomain,omitempty" description:"COS自定义域名"`       // COS自定义域名

	// AWS S3配置
	S3Region    string `json:"s3Region,omitempty" description:"S3区域"`          // S3区域
	S3Bucket    string `json:"s3Bucket,omitempty" description:"S3存储桶名称"`       // S3存储桶名称
	S3AccessKey string `json:"s3AccessKey,omitempty" description:"S3访问密钥"`     // S3访问密钥
	S3SecretKey string `json:"s3SecretKey,omitempty" description:"S3密钥密码（脱敏）"` // S3密钥密码（脱敏）
	S3Domain    string `json:"s3Domain,omitempty" description:"S3自定义域名"`       // S3自定义域名

	// 七牛云存储配置 - 嵌套对象方式
	AccessKey string `json:"accessKey,omitempty" description:"访问密钥"`
	SecretKey string `json:"secretKey,omitempty" description:"密钥密码（脱敏）"`
	Bucket    string `json:"bucket,omitempty" description:"存储空间名称"`
	Domain    string `json:"domain,omitempty" description:"自定义域名"`
	Zone      string `json:"zone,omitempty" description:"存储区域"`
	UseHTTPS  bool   `json:"useHTTPS,omitempty" description:"是否使用HTTPS"`
}

// QiniuConfigData 七牛云存储配置数据，用于嵌套在ConfigResponse中
type QiniuConfigData struct {
	AccessKey string `json:"accessKey,omitempty" description:"访问密钥"`
	SecretKey string `json:"secretKey,omitempty" description:"密钥密码（脱敏）"`
	Bucket    string `json:"bucket,omitempty" description:"存储空间名称"`
	Domain    string `json:"domain,omitempty" description:"自定义域名"`
	Zone      string `json:"zone,omitempty" description:"存储区域"`
	UseHTTPS  bool   `json:"useHTTPS,omitempty" description:"是否使用HTTPS"`
}

// UploadConfigBaseResponse 上传配置基本信息响应（前台使用）
type UploadConfigBaseResponse struct {
	StorageMode       string `json:"storageMode"`       // 存储模式
	MaxSize           int64  `json:"maxSize"`           // 最大文件大小
	MaxSizeMB         int    `json:"maxSizeMB"`         // 最大文件大小（MB）
	AllowedExtensions string `json:"allowedExtensions"` // 允许的文件扩展名
}
