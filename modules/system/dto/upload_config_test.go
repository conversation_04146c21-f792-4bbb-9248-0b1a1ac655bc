// +----------------------------------------------------------------------
// | Copyright (c) 2023-2023 http://www.o-mall.com/ All rights reserved.
// +----------------------------------------------------------------------
// | Author: 张二好 <<EMAIL>>
// +----------------------------------------------------------------------
// | Date: 2025-06-07
// +----------------------------------------------------------------------

package dto

import (
	"encoding/json"
	"testing"
)

// TestQiniuConfigUnmarshal 测试七牛云配置解析
func TestQiniuConfigUnmarshal(t *testing.T) {
	// 测试案例1: 标准嵌套qiniuConfig格式
	jsonData1 := `{
		"storageMode": "qiniu",
		"qiniuConfig": {
			"accessKey": "test-access-key",
			"secretKey": "test-secret-key",
			"bucket": "test-bucket",
			"domain": "test-domain.com",
			"zone": "z0",
			"useHTTPS": true
		}
	}`

	var config1 ConfigRequest
	if err := json.Unmarshal([]byte(jsonData1), &config1); err != nil {
		t.Fatalf("无法解析标准嵌套格式: %v", err)
	}

	if config1.QiniuConfig.AccessKey != "test-access-key" {
		t.Errorf("嵌套格式解析失败，期望AccessKey='test-access-key'，实际为'%s'", config1.QiniuConfig.AccessKey)
	}

	// 测试案例2: 无qiniuConfig前缀，字段直接在config中
	jsonData2 := `{
		"storageMode": "qiniu",
		"accessKey": "direct-access-key",
		"secretKey": "direct-secret-key",
		"bucket": "direct-bucket",
		"domain": "direct-domain.com",
		"zone": "z1",
		"useHTTPS": true
	}`

	var config2 ConfigRequest
	if err := json.Unmarshal([]byte(jsonData2), &config2); err != nil {
		t.Fatalf("无法解析无前缀格式: %v", err)
	}

	// 验证字段是否被正确映射到QiniuConfig中
	if config2.QiniuConfig.AccessKey != "direct-access-key" {
		t.Errorf("无前缀格式映射失败，期望AccessKey='direct-access-key'，实际为'%s'", config2.QiniuConfig.AccessKey)
	}
	if config2.QiniuConfig.Bucket != "direct-bucket" {
		t.Errorf("无前缀格式映射失败，期望Bucket='direct-bucket'，实际为'%s'", config2.QiniuConfig.Bucket)
	}

	// 测试案例3: 混合格式，部分字段在qiniuConfig中，部分在外层
	jsonData3 := `{
		"storageMode": "qiniu",
		"accessKey": "mixed-access-key",
		"qiniuConfig": {
			"bucket": "nested-bucket",
			"domain": "nested-domain.com"
		}
	}`

	var config3 ConfigRequest
	if err := json.Unmarshal([]byte(jsonData3), &config3); err != nil {
		t.Fatalf("无法解析混合格式: %v", err)
	}

	// 验证外层字段是否被正确映射到QiniuConfig中
	if config3.QiniuConfig.AccessKey != "mixed-access-key" {
		t.Errorf("混合格式映射失败，期望AccessKey='mixed-access-key'，实际为'%s'", config3.QiniuConfig.AccessKey)
	}
	// 验证嵌套字段是否保留
	if config3.QiniuConfig.Bucket != "nested-bucket" {
		t.Errorf("混合格式嵌套字段失败，期望Bucket='nested-bucket'，实际为'%s'", config3.QiniuConfig.Bucket)
	}
}
