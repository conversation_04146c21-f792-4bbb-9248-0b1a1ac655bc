/**
 * upload_test_connection_response.go
 * API /upload/test-connection 的响应数据传输对象
 *
 * 本文件定义了测试上传连接接口的响应结构。
 */
package dto

// TestConnectionAPIResponse API /upload/test-connection 的响应结构
type TestConnectionAPIResponse struct {
	StorageMode        string      `json:"storage_mode"`
	StorageConfig      interface{} `json:"storage_config,omitempty"` // 具体存储配置，敏感信息已脱敏. 通常是 dto.ConfigResponse
	MaxFileSizeMB      int64       `json:"max_file_size_mb"`
	AllowedFileTypes   []string    `json:"allowed_file_types"`
	UploadEndpoint     string      `json:"upload_endpoint"`    // 建议的服务器上传接口
	StorageInitialized bool        `json:"storage_initialized"` // 存储服务是否已初始化
	Message            string      `json:"message"`
}

