/**
 * system模块初始化
 *
 * 本文件负责system模块的初始化工作，包括注册模型、初始化路由和系统配置数据等。
 * 在应用启动时被调用，确保system模块的功能正常启动。
 */

package system

import (
	"context"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/routers"
	"o_mall_backend/modules/system/services/impl"
)

// Init 初始化system模块
func Init() {
	logs.Info("初始化system模块...")

	// 注册模型
	registerModels()

	// 初始化数据库索引（性能优化）
	initDatabaseIndexes()

	// 初始化路由
	routers.InitSystemRouter()

	// 初始化默认系统配置数据
	initSystemConfig()

	logs.Info("system模块初始化完成")
}

// 注册ORM模型
func registerModels() {
	logs.Info("注册system模块ORM模型...")

	// 注册system相关模型
	orm.RegisterModel(
		new(models.SystemConfig),
		new(models.SystemNotice),
		new(models.SmsConfig),
		new(models.UploadConfig),
		new(models.AIConfig),
		new(models.CommunityAddress),
		new(models.FileUsageConfig),
	)
}

// 初始化系统配置数据
func initSystemConfig() {
	logs.Info("初始化system模块默认配置数据...")

	// 获取系统配置服务
	systemConfigService := impl.NewSystemConfigServiceImpl()

	// 判断是否需要初始化默认配置
	if needInitDefault, _ := systemConfigService.NeedInitDefaultConfig(); needInitDefault {
		// 初始化默认系统配置
		if err := systemConfigService.InitDefaultSystemConfig(); err != nil {
			logs.Error("初始化默认系统配置失败: %v", err)
		}

		// 初始化默认上传配置
		if err := systemConfigService.InitDefaultUploadConfig(); err != nil {
			logs.Error("初始化默认上传配置失败: %v", err)
		}

		// 初始化默认短信配置
		if err := systemConfigService.InitDefaultSmsConfig(); err != nil {
			logs.Error("初始化默认短信配置失败: %v", err)
		}

		// 初始化默认文件用途配置
		fileUsageConfigService := impl.NewFileUsageConfigServiceImpl()
		if err := fileUsageConfigService.InitDefaultFileUsages(context.Background()); err != nil {
			logs.Error("初始化默认文件用途配置失败: %v", err)
		}
	}
}

// initDatabaseIndexes 初始化数据库索引
// 用于优化查询性能，特别是树形查询接口
func initDatabaseIndexes() {
	logs.Info("初始化system模块数据库索引...")

	// 初始化社区地址表索引
	if err := models.InitCommunityAddressIndexes(); err != nil {
		logs.Error("初始化社区地址表索引失败: %v", err)
	} else {
		logs.Info("社区地址表索引初始化成功")
	}

	// 可以在这里添加其他表的索引初始化
	// 例如：
	// if err := models.InitOtherTableIndexes(); err != nil {
	//     logs.Error("初始化其他表索引失败: %v", err)
	// }

	logs.Info("system模块数据库索引初始化完成")
}
