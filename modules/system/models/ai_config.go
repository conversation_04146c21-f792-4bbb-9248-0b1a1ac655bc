/**
 * AI配置模型
 *
 * 本文件定义了AI配置的模型，用于存储AI相关的配置信息，
 * 例如DeepSeek API的密钥、基础URL等。
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// AIConfig AI配置模型
type AIConfig struct {
	ID           int       `orm:"column(id);auto;pk"`           // 自增ID
	APIKey       string    `orm:"column(api_key);size(255)"`    // API密钥
	BaseURL      string    `orm:"column(base_url);size(255)"`   // API基础URL
	DefaultModel string    `orm:"column(default_model);size(50)"` // 默认使用的模型
	Provider     string    `orm:"column(provider);size(50)"`    // 提供商，如DeepSeek
	Enabled      bool      `orm:"column(enabled);default(true)"` // 是否启用
	CreatedAt    time.Time `orm:"column(created_at);auto_now_add"` // 创建时间
	UpdatedAt    time.Time `orm:"column(updated_at);auto_now"`     // 更新时间
}

// TableName 设置表名
func (a *AIConfig) TableName() string {
	return "system_ai_config"
}

// InsertOrUpdate 插入或更新AI配置
func (a *AIConfig) InsertOrUpdate(o orm.Ormer) error {
	if a.ID > 0 {
		_, err := o.Update(a)
		return err
	}
	_, err := o.Insert(a)
	return err
}

// GetByProvider 根据提供商获取AI配置
func (a *AIConfig) GetByProvider(o orm.Ormer, provider string) error {
	return o.QueryTable(a.TableName()).Filter("provider", provider).One(a)
}

// GetByID 根据ID获取AI配置
func (a *AIConfig) GetByID(o orm.Ormer, id int) error {
	return o.QueryTable(a.TableName()).Filter("id", id).One(a)
}
