/**
 * community_address.go
 * 社区地址配置模型
 *
 * 本模型用于社区生活圈的地址配置，支持小区-楼栋-单元的三级结构，
 * 并包含地理坐标信息，方便计算距离和路径规划。
 */

package models

import (
	"time"
)

// CommunityAddress 社区地址配置模型
type CommunityAddress struct {
	Id          int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                    // 主键ID
	Name        string    `orm:"column(name);size(100)" json:"name" description:"名称"`                                // 名称
	ParentId    int64     `orm:"column(parent_id);default(0)" json:"parentId" description:"父级ID"`                    // 父级ID，0表示顶级
	Level       int       `orm:"column(level);default(1)" json:"level" description:"级别：1-小区，2-楼栋，3-单元"`              // 级别：1=小区，2=楼栋，3=单元
	Longitude   float64   `orm:"column(longitude);digits(10);decimals(7)" json:"longitude" description:"经度"`         // 经度 GCJ02坐标系	火星坐标系，WGS84坐标系加密后的坐标系；Google国内地图、高德、QQ地图 使用
	Latitude    float64   `orm:"column(latitude);digits(10);decimals(7)" json:"latitude" description:"纬度"`           // 纬度 GCJ02坐标系	火星坐标系，WGS84坐标系加密后的坐标系；Google国内地图、高德、QQ地图 使用
	FullPath    string    `orm:"column(full_path);size(500)" json:"fullPath" description:"完整路径"`                     // 完整路径，如：xx小区/xx栋/xx单元
	Sort        int       `orm:"column(sort);default(0)" json:"sort" description:"排序"`                               // 排序
	Status      int8      `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"`                 // 状态：1=启用，0=禁用
	Description string    `orm:"column(description);size(500)" json:"description" description:"描述"`                  // 描述
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"createdAt" description:"创建时间"` // 创建时间
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updatedAt" description:"更新时间"`     // 更新时间
}

// TableName 指定数据库表名
func (m *CommunityAddress) TableName() string {
	return "system_community_address"
}

// 地址配置缓存相关常量
const (
	// CommunityAddressCacheKeyPrefix 社区地址缓存键前缀
	CommunityAddressCacheKeyPrefix = "system:community_address:"
	// CommunityAddressAllCacheKey 所有社区地址缓存键
	CommunityAddressAllCacheKey = "system:community_address:all"
	// CommunityAddressCacheExpiration 社区地址缓存过期时间（24小时）
	CommunityAddressCacheExpiration = 24 * time.Hour
)

// 社区地址相关常量
const (
	// 地址级别
	AddressLevelCommunity = 1 // 小区级别
	AddressLevelBuilding  = 2 // 楼栋级别
	AddressLevelUnit      = 3 // 单元级别

	// 系统配置键名
	ConfigKeyCommunityAddressEnabled = "community_address_enabled" // 是否启用社区地址
)
