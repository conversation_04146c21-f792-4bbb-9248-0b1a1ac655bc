/*
 * community_address_init.go
 * 社区地址模型初始化文件
 *
 * 负责创建数据库表和索引，优化查询性能
 */

package models

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// InitCommunityAddressIndexes 初始化社区地址表索引
// 用于优化树形查询性能，特别是 GetAddressTree 接口
func InitCommunityAddressIndexes() error {
	o := orm.NewOrm()
	tableName := "system_community_address"
	
	logs.Info("[InitCommunityAddressIndexes] 开始创建社区地址表索引...")
	
	// 索引定义列表
	indexes := []struct {
		name   string
		query  string
		desc   string
	}{
		{
			name:  "idx_community_address_parent_id",
			query: "CREATE INDEX idx_community_address_parent_id ON " + tableName + " (parent_id)",
			desc:  "parent_id 索引，优化树形查询",
		},
		{
			name:  "idx_community_address_status",
			query: "CREATE INDEX idx_community_address_status ON " + tableName + " (status)",
			desc:  "status 索引，优化状态过滤",
		},
		{
			name:  "idx_community_address_parent_status",
			query: "CREATE INDEX idx_community_address_parent_status ON " + tableName + " (parent_id, status)",
			desc:  "parent_id + status 复合索引，优化常用查询组合",
		},
		{
			name:  "idx_community_address_parent_status_sort",
			query: "CREATE INDEX idx_community_address_parent_status_sort ON " + tableName + " (parent_id, status, sort, id)",
			desc:  "parent_id + status + sort + id 复合索引，优化排序查询",
		},
		{
			name:  "idx_community_address_level",
			query: "CREATE INDEX idx_community_address_level ON " + tableName + " (level)",
			desc:  "level 索引，优化级别查询",
		},
	}
	
	// 创建索引
	for _, idx := range indexes {
		logs.Info("[InitCommunityAddressIndexes] 创建索引: %s - %s", idx.name, idx.desc)
		
		// 检查索引是否已存在
		var count int64
		err := o.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = ? AND index_name = ?", tableName, idx.name).QueryRow(&count)
		if err != nil {
			logs.Warning("[InitCommunityAddressIndexes] 检查索引 %s 是否存在时出错: %v", idx.name, err)
		}
		
		if count > 0 {
			logs.Info("[InitCommunityAddressIndexes] 索引 %s 已存在，跳过创建", idx.name)
			continue
		}
		
		// 创建索引
		_, err = o.Raw(idx.query).Exec()
		if err != nil {
			logs.Error("[InitCommunityAddressIndexes] 创建索引 %s 失败: %v", idx.name, err)
			return err
		}
		
		logs.Info("[InitCommunityAddressIndexes] 索引 %s 创建成功", idx.name)
	}
	
	logs.Info("[InitCommunityAddressIndexes] 社区地址表索引创建完成")
	return nil
}

// CheckCommunityAddressIndexes 检查社区地址表索引状态
func CheckCommunityAddressIndexes() error {
	o := orm.NewOrm()
	tableName := "system_community_address"
	
	logs.Info("[CheckCommunityAddressIndexes] 检查社区地址表索引状态...")
	
	// 查询表的所有索引
	type IndexInfo struct {
		TableName string `orm:"column(TABLE_NAME)"`
		IndexName string `orm:"column(INDEX_NAME)"`
		ColumnName string `orm:"column(COLUMN_NAME)"`
		SeqInIndex int    `orm:"column(SEQ_IN_INDEX)"`
	}
	
	var indexes []IndexInfo
	_, err := o.Raw("SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME, SEQ_IN_INDEX FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = ? ORDER BY INDEX_NAME, SEQ_IN_INDEX", tableName).QueryRows(&indexes)
	if err != nil {
		logs.Error("[CheckCommunityAddressIndexes] 查询索引信息失败: %v", err)
		return err
	}
	
	logs.Info("[CheckCommunityAddressIndexes] 表 %s 的索引信息:", tableName)
	for _, idx := range indexes {
		logs.Info("[CheckCommunityAddressIndexes] 索引: %s, 列: %s, 序号: %d", idx.IndexName, idx.ColumnName, idx.SeqInIndex)
	}
	
	return nil
}