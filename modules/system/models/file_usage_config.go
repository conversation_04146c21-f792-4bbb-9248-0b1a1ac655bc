/*
 * file_usage_config.go
 * 文件用途配置模型
 *
 * 本模型用于存储文件上传用途的配置信息，支持动态管理允许的文件用途类型。
 */

package models

import (
	"time"
)

// FileUsageConfig 文件用途配置模型
type FileUsageConfig struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                    // 主键ID
	UsageCode   string    `orm:"column(usage_code);size(50);unique" json:"usage_code" description:"用途代码"`        // 用途代码（如：avatar、product、merchant_logo等）
	UsageName   string    `orm:"column(usage_name);size(100)" json:"usage_name" description:"用途名称"`             // 用途名称（如：头像、商品图片、商户Logo等）
	Description string    `orm:"column(description);size(500)" json:"description" description:"用途描述"`           // 用途描述
	AllowAnonymous int8   `orm:"column(allow_anonymous);default(0)" json:"allow_anonymous" description:"是否允许匿名上传：1-是，0-否"` // 是否允许匿名上传：1=是，0=否
	MaxFileSize int64     `orm:"column(max_file_size);default(0)" json:"max_file_size" description:"最大文件大小（字节），0表示使用全局配置"` // 最大文件大小（字节），0表示使用全局配置
	AllowedTypes string   `orm:"column(allowed_types);size(500)" json:"allowed_types" description:"允许的文件类型（逗号分隔），空表示使用全局配置"` // 允许的文件类型（逗号分隔），空表示使用全局配置
	SortOrder   int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序顺序"`            // 排序顺序
	Status      int8      `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"`           // 状态：1=启用，0=禁用
	Remark      string    `orm:"column(remark);size(500)" json:"remark" description:"备注"`                      // 备注
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"created_at" description:"创建时间"` // 创建时间
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updated_at" description:"更新时间"`   // 更新时间
}

// TableName 指定数据库表名
func (m *FileUsageConfig) TableName() string {
	return "system_file_usage_config"
}

// 文件用途配置缓存相关常量
const (
	// FileUsageConfigCacheKey 文件用途配置缓存键
	FileUsageConfigCacheKey = "system:file_usage:config"
	// FileUsageConfigCacheExpiration 文件用途配置缓存过期时间（1小时）
	FileUsageConfigCacheExpiration = 1 * time.Hour
)

// 默认文件用途常量
const (
	DefaultUsageAvatar    = "avatar"      // 头像
	DefaultUsageProduct   = "product"     // 商品图片
	DefaultUsageMerchant  = "merchant"    // 商户图片
	DefaultUsageComplaint = "complaint"   // 投诉图片
	DefaultUsageOther     = "other"       // 其他用途
	DefaultUsageMerchantLogo = "merchant_logo" // 商户Logo
)

// GetDefaultFileUsages 获取默认的文件用途配置
func GetDefaultFileUsages() []FileUsageConfig {
	return []FileUsageConfig{
		{
			UsageCode:      DefaultUsageAvatar,
			UsageName:      "头像",
			Description:    "用户头像图片",
			AllowAnonymous: 0,
			SortOrder:      1,
			Status:         1,
		},
		{
			UsageCode:      DefaultUsageProduct,
			UsageName:      "商品图片",
			Description:    "商品展示图片",
			AllowAnonymous: 0,
			SortOrder:      2,
			Status:         1,
		},
		{
			UsageCode:      DefaultUsageMerchant,
			UsageName:      "商户图片",
			Description:    "商户相关图片",
			AllowAnonymous: 0,
			SortOrder:      3,
			Status:         1,
		},
		{
			UsageCode:      DefaultUsageMerchantLogo,
			UsageName:      "商户Logo",
			Description:    "商户品牌Logo图片",
			AllowAnonymous: 0,
			SortOrder:      4,
			Status:         1,
		},
		{
			UsageCode:      DefaultUsageComplaint,
			UsageName:      "投诉图片",
			Description:    "用户投诉相关图片",
			AllowAnonymous: 1,
			SortOrder:      5,
			Status:         1,
		},
		{
			UsageCode:      DefaultUsageOther,
			UsageName:      "其他用途",
			Description:    "其他类型的文件",
			AllowAnonymous: 0,
			SortOrder:      6,
			Status:         1,
		},
	}
}