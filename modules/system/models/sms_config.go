/**
 * sms_config.go
 * 短信配置模型
 *
 * 本模型用于存储短信服务的配置信息，包括短信提供商、模板ID、签名等。
 */

package models

import (
	"time"
)

// SmsConfig 短信配置模型
type SmsConfig struct {
	Id                   int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                                // 主键ID
	Provider             string    `orm:"column(provider);size(50)" json:"provider" description:"短信服务提供商（aliyun/tencent/yunpian/submail等）"`       // 短信服务提供商（aliyun/tencent/yunpian/submail等）
	AccessKey            string    `orm:"column(access_key);size(100)" json:"accessKey" description:"访问密钥ID"`                             // 访问密钥ID
	AccessSecret         string    `orm:"column(access_secret);size(200)" json:"accessSecret" description:"访问密钥密码"`                       // 访问密钥密码
	SignName             string    `orm:"column(sign_name);size(50)" json:"signName" description:"短信签名"`                                  // 短信签名
	TemplateCodeRegister string    `orm:"column(template_code_register);size(50)" json:"templateCodeRegister" description:"注册验证码模板ID"`    // 注册验证码模板ID
	TemplateCodeLogin    string    `orm:"column(template_code_login);size(50)" json:"templateCodeLogin" description:"登录验证码模板ID"`          // 登录验证码模板ID
	TemplateCodeResetPwd string    `orm:"column(template_code_reset_pwd);size(50)" json:"templateCodeResetPwd" description:"重置密码验证码模板ID"` // 重置密码验证码模板ID
	TemplateCodeNotice   string    `orm:"column(template_code_notice);size(50)" json:"templateCodeNotice" description:"通知消息模板ID"`         // 通知消息模板ID
	DailyLimit           int       `orm:"column(daily_limit);default(1000)" json:"dailyLimit" description:"每日发送上限"`                       // 每日发送上限
	Status               int8      `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"`                             // 状态：1=启用，0=禁用
	Remark               string    `orm:"column(remark);size(500)" json:"remark" description:"备注"`                                        // 备注
	CreatedAt            time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"createdAt" description:"创建时间"`             // 创建时间
	UpdatedAt            time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updatedAt" description:"更新时间"`                 // 更新时间
}

// TableName 指定数据库表名
func (m *SmsConfig) TableName() string {
	return "system_sms_config"
}

// 短信配置缓存相关常量
const (
	// SmsConfigCacheKey 短信配置缓存键
	SmsConfigCacheKey = "system:sms:config"
	// SmsConfigCacheExpiration 短信配置缓存过期时间（12小时）
	SmsConfigCacheExpiration = 12 * time.Hour
)
