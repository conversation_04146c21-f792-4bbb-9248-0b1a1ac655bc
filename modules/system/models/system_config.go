/**
 * system_config.go
 * 系统配置模型
 *
 * 本模型用于存储系统全局配置，如网站名称、版本号、配置版本号等基础信息。
 */

package models

import (
	"time"
)

// SystemConfig 系统配置模型
type SystemConfig struct {
	Id          int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                                      // 主键ID
	ConfigKey   string    `orm:"column(config_key);size(100);unique" json:"configKey" description:"配置键名（唯一）"`                          // 配置键名（唯一）
	ConfigValue string    `orm:"column(config_value);type(text)" json:"configValue" description:"配置值"`                                 // 配置值
	ConfigType  string    `orm:"column(config_type);size(50)" json:"configType" description:"配置类型（如text、number、json等）"`                // 配置类型（如text、number、json等）
	Category    string    `orm:"column(category);size(50);default(system)" json:"category" description:"配置分类（如system、app_ui、banner等）"` // 配置分类（如system、app_ui、banner等）
	Description string    `orm:"column(description);size(500)" json:"description" description:"配置描述"`                                  // 配置描述
	Version     int       `orm:"column(version);default(1)" json:"version" description:"版本号，用于控制缓存更新"`                                 // 版本号，用于控制缓存更新
	Status      int8      `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"`                                   // 状态：1=启用，0=禁用
	IsSystem    int8      `orm:"column(is_system);default(0)" json:"isSystem" description:"是否系统级配置：1-是，0-否"`                           // 是否系统级配置：1=是，0=否
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"createdAt" description:"创建时间"`                   // 创建时间
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updatedAt" description:"更新时间"`                       // 更新时间
}

// TableName 指定数据库表名
func (m *SystemConfig) TableName() string {
	return "system_config"
}

// 系统配置缓存相关常量
const (
	// SystemConfigCacheKeyPrefix 系统配置缓存键前缀
	SystemConfigCacheKeyPrefix = "system:config:"
	// SystemConfigAllCacheKey 所有系统配置缓存键
	SystemConfigAllCacheKey = "system:config:all"
	// SystemConfigCacheExpiration 系统配置缓存过期时间（24小时）
	SystemConfigCacheExpiration = 24 * time.Hour
)

// 系统配置默认键名
const (
	// 网站基本信息
	ConfigKeySiteName      = "site_name"      // 网站名称
	ConfigKeySiteVersion   = "site_version"   // 网站版本
	ConfigKeyApiVersion    = "api_version"    // API版本
	ConfigKeyConfigVersion = "config_version" // 配置版本号
	ConfigKeyLogo          = "site_logo"      // 网站Logo
	ConfigKeyFavicon       = "site_favicon"   // 网站图标
	ConfigKeyCopyright     = "site_copyright" // 版权信息
	ConfigKeyRegion        = "site_region"    // 地区

	// 联系方式
	ConfigKeyContactEmail   = "contact_email"   // 联系邮箱
	ConfigKeyContactPhone   = "contact_phone"   // 联系电话
	ConfigKeyContactAddress = "contact_address" // 联系地址

	// 系统设置
	ConfigKeyMaintenanceMode    = "maintenance_mode"    // 维护模式
	ConfigKeyMaintenanceMessage = "maintenance_message" // 维护消息
)

// 系统配置分类
const (
	// 系统基础配置
	CategorySystem = "system"

	// 小程序UI配置
	CategoryAppUI = "app_ui"

	// Banner轮播图配置
	CategoryBanner = "banner"

	// 广告配置
	CategoryAd = "ad"

	// 菜单配置
	CategoryMenu = "menu"

	// 个人中心配置
	CategoryUserCenter = "user_center"

	// 首页配置
	CategoryHomePage = "home_page"
)
