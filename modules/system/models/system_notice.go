/**
 * system_notice.go
 * 系统公告模型
 *
 * 本模型用于存储系统公告信息，包括标题、内容、发布时间等。
 */

package models

import (
	"time"
)

// SystemNotice 系统公告模型
type SystemNotice struct {
	Id        int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                         // 主键ID
	Title     string    `orm:"column(title);size(200)" json:"title" description:"公告标题"`                                 // 公告标题
	Content   string    `orm:"column(content);type(text)" json:"content" description:"公告内容"`                            // 公告内容
	Type      int8      `orm:"column(type);default(1)" json:"type" description:"公告类型：1-普通公告，2-紧急公告，3-维护公告"`             // 公告类型：1=普通公告，2=紧急公告，3=维护公告
	StartTime time.Time `orm:"column(start_time);type(datetime)" json:"startTime" description:"公告生效时间"`                 // 公告生效时间
	EndTime   time.Time `orm:"column(end_time);type(datetime)" json:"endTime" description:"公告结束时间"`                     // 公告结束时间
	Target    int8      `orm:"column(target);default(0)" json:"target" description:"目标用户：0-所有用户，1-用户，2-商家，3-跑腿员，4-管理员"` // 目标用户：0=所有用户，1=用户，2=商家，3=跑腿员，4=管理员
	Status    int8      `orm:"column(status);default(1)" json:"status" description:"状态：1-已发布，0-未发布"`                    // 状态：1=已发布，0=未发布
	IsTop     int8      `orm:"column(is_top);default(0)" json:"isTop" description:"是否置顶：1-是，0-否"`                       // 是否置顶：1=是，0=否
	CreatedBy int64     `orm:"column(created_by)" json:"createdBy" description:"创建人ID"`                                 // 创建人ID
	CreatedAt time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"createdAt" description:"创建时间"`      // 创建时间
	UpdatedAt time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updatedAt" description:"更新时间"`          // 更新时间
}

// TableName 指定数据库表名
func (m *SystemNotice) TableName() string {
	return "system_notice"
}

// 系统公告缓存相关常量
const (
	// SystemNoticeCacheKeyPrefix 系统公告缓存键前缀
	SystemNoticeCacheKeyPrefix = "system:notice:"
	// SystemNoticeListCacheKey 系统公告列表缓存键
	SystemNoticeListCacheKey = "system:notice:list"
	// SystemNoticeCacheExpiration 系统公告缓存过期时间（1小时）
	SystemNoticeCacheExpiration = 1 * time.Hour
)
