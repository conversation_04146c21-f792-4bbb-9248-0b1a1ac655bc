/**
 * upload_config.go
 * 上传配置模型
 *
 * 本模型用于存储文件上传相关的配置信息，包括存储方式、大小限制、CDN配置等。
 */

/**
 * upload_config.go
 * 上传配置模型
 *
 * 本模型用于存储文件上传相关的配置信息，包括存储方式、大小限制等。
 * 采用配置模板+JSON存储方式，提高配置的灵活性和扩展性。
 */

package models

import (
	"encoding/json"
	"errors"
	"time"
)

// UploadConfig 上传配置模型
// 采用配置模板+JSON存储方式，只保留通用字段，特定存储方式的配置存储在Config字段中
type UploadConfig struct {
	Id                int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                              // 主键ID
	StorageMode       string    `orm:"column(storage_mode);size(20)" json:"storageMode" description:"存储模式（local/oss/cos/s3/qiniu等）"` // 存储模式（local/oss/cos/s3/qiniu等）
	MaxSize           int64     `orm:"column(max_size);default(10485760)" json:"maxSize" description:"最大文件大小，默认10MB"`                // 最大文件大小，默认10MB
	AllowedExtensions string    `orm:"column(allowed_extensions);size(500)" json:"allowedExtensions" description:"允许的文件扩展名（逗号分隔）"`   // 允许的文件扩展名（逗号分隔）
	EnableCdn         int8      `orm:"column(enable_cdn);default(0)" json:"enableCdn" description:"是否启用CDN：1-是，0-否"`                 // 是否启用CDN：1=是，0=否
	CdnDomain         string    `orm:"column(cdn_domain);size(200)" json:"cdnDomain" description:"CDN域名"`                            // CDN域名
	Config            string    `orm:"column(config);type(json)" json:"config" description:"具体存储方式的配置（JSON格式）"`                      // 具体存储方式的配置（JSON格式）
	Status            int8      `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"`                           // 状态：1=启用，0=禁用
	Remark            string    `orm:"column(remark);size(500)" json:"remark" description:"备注"`                                      // 备注
	CreatedAt         time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"createdAt" description:"创建时间"`           // 创建时间
	UpdatedAt         time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updatedAt" description:"更新时间"`               // 更新时间
}

// TableName 指定数据库表名
func (m *UploadConfig) TableName() string {
	return "system_upload_config"
}

// 上传配置缓存相关常量
const (
	// UploadConfigCacheKey 上传配置缓存键
	UploadConfigCacheKey = "system:upload:config"
	// UploadConfigCacheExpiration 上传配置缓存过期时间（12小时）
	UploadConfigCacheExpiration = 12 * time.Hour
)

// 存储模式常量
const (
	StorageModeLocal = "local" // 本地存储
	StorageModeOSS   = "oss"   // 阿里云OSS
	StorageModeCOS   = "cos"   // 腾讯云COS
	StorageModeS3    = "s3"    // AWS S3
	StorageModeQiniu = "qiniu" // 七牛云存储
)

// LocalConfig 本地存储配置
type LocalConfig struct {
	LocalPath string `json:"localPath"` // 本地存储路径
}

// OssConfig 阿里云OSS配置
type OssConfig struct {
	Endpoint     string `json:"endpoint"`     // OSS服务端点
	Bucket       string `json:"bucket"`       // OSS存储桶名称
	AccessKey    string `json:"accessKey"`    // OSS访问密钥
	AccessSecret string `json:"accessSecret"` // OSS访问密钥密码
	Domain       string `json:"domain"`       // OSS自定义域名（可选）
}

// CosConfig 腾讯云COS配置
type CosConfig struct {
	Region    string `json:"region"`    // COS区域
	Bucket    string `json:"bucket"`    // COS存储桶名称
	SecretId  string `json:"secretId"`  // COS密钥ID
	SecretKey string `json:"secretKey"` // COS密钥密码
	Domain    string `json:"domain"`    // COS自定义域名（可选）
}

// S3Config AWS S3配置
type S3Config struct {
	Region    string `json:"region"`    // S3区域
	Bucket    string `json:"bucket"`    // S3存储桶名称
	AccessKey string `json:"accessKey"` // S3访问密钥
	SecretKey string `json:"secretKey"` // S3密钥密码
	Domain    string `json:"domain"`    // S3自定义域名（可选）
}

// QiniuConfig 七牛云存储配置
type QiniuConfig struct {
	AccessKey string `json:"accessKey"` // 七牛云访问密钥
	SecretKey string `json:"secretKey"` // 七牛云密钥密码
	Bucket    string `json:"bucket"`    // 七牛云存储空间名称
	Domain    string `json:"domain"`    // 七牛云自定义域名
	Zone      string `json:"zone"`      // 七牛云存储区域(z0:华东, z1:华北, z2:华南, na0:北美, as0:新加坡)
	UseHTTPS  bool   `json:"useHTTPS"`  // 是否使用HTTPS
}

// CDNConfig CDN配置
type CDNConfig struct {
	Domain    string `json:"domain"`    // CDN域名
	AccessKey string `json:"accessKey"` // CDN访问密钥
	SecretKey string `json:"secretKey"` // CDN密钥密码
}

// GetLocalConfig 获取本地存储配置
func (m *UploadConfig) GetLocalConfig() (*LocalConfig, error) {
	if m.StorageMode != StorageModeLocal {
		return nil, errors.New("当前存储模式不是本地存储")
	}

	var config LocalConfig
	err := json.Unmarshal([]byte(m.Config), &config)
	if err != nil {
		return nil, errors.New("解析本地存储配置失败: " + err.Error())
	}

	return &config, nil
}

// GetOssConfig 获取阿里云OSS配置
func (m *UploadConfig) GetOssConfig() (*OssConfig, error) {
	if m.StorageMode != StorageModeOSS {
		return nil, errors.New("当前存储模式不是阿里云OSS")
	}

	var config OssConfig
	err := json.Unmarshal([]byte(m.Config), &config)
	if err != nil {
		return nil, errors.New("解析阿里云OSS配置失败: " + err.Error())
	}

	return &config, nil
}

// GetCosConfig 获取腾讯云COS配置
func (m *UploadConfig) GetCosConfig() (*CosConfig, error) {
	if m.StorageMode != StorageModeCOS {
		return nil, errors.New("当前存储模式不是腾讯云COS")
	}

	var config CosConfig
	err := json.Unmarshal([]byte(m.Config), &config)
	if err != nil {
		return nil, errors.New("解析腾讯云COS配置失败: " + err.Error())
	}

	return &config, nil
}

// GetS3Config 获取AWS S3配置
func (m *UploadConfig) GetS3Config() (*S3Config, error) {
	if m.StorageMode != StorageModeS3 {
		return nil, errors.New("当前存储模式不是AWS S3")
	}

	var config S3Config
	err := json.Unmarshal([]byte(m.Config), &config)
	if err != nil {
		return nil, errors.New("解析AWS S3配置失败: " + err.Error())
	}

	return &config, nil
}

// GetCDNConfig 获取CDN配置
func (m *UploadConfig) GetCDNConfig() (*CDNConfig, error) {
	if m.EnableCdn != 1 {
		return nil, errors.New("CDN未启用")
	}

	var config CDNConfig
	err := json.Unmarshal([]byte(m.Config), &config)
	if err != nil || config.Domain == "" {
		// 如果解析失败或者域名为空，则仅使用 CdnDomain 字段
		return &CDNConfig{
			Domain: m.CdnDomain,
		}, nil
	}

	// 如果配置缺失域名，使用通用字段
	if config.Domain == "" {
		config.Domain = m.CdnDomain
	}

	return &config, nil
}

// SetLocalConfig 设置本地存储配置
func (m *UploadConfig) SetLocalConfig(config *LocalConfig) error {
	m.StorageMode = StorageModeLocal
	data, err := json.Marshal(config)
	if err != nil {
		return errors.New("序列化本地存储配置失败: " + err.Error())
	}
	m.Config = string(data)
	return nil
}

// SetOssConfig 设置阿里云OSS配置
func (m *UploadConfig) SetOssConfig(config *OssConfig) error {
	m.StorageMode = StorageModeOSS
	data, err := json.Marshal(config)
	if err != nil {
		return errors.New("序列化阿里云OSS配置失败: " + err.Error())
	}
	m.Config = string(data)
	return nil
}

// SetCosConfig 设置腾讯云COS配置
func (m *UploadConfig) SetCosConfig(config *CosConfig) error {
	m.StorageMode = StorageModeCOS
	data, err := json.Marshal(config)
	if err != nil {
		return errors.New("序列化腾讯云COS配置失败: " + err.Error())
	}
	m.Config = string(data)
	return nil
}

// SetS3Config 设置AWS S3配置
func (m *UploadConfig) SetS3Config(config *S3Config) error {
	m.StorageMode = StorageModeS3
	data, err := json.Marshal(config)
	if err != nil {
		return errors.New("序列化AWS S3配置失败: " + err.Error())
	}
	m.Config = string(data)
	return nil
}

// GetQiniuConfig 获取七牛云存储配置
func (m *UploadConfig) GetQiniuConfig() (*QiniuConfig, error) {
	if m.StorageMode != StorageModeQiniu {
		return nil, errors.New("当前存储模式不是七牛云存储")
	}

	var config QiniuConfig
	err := json.Unmarshal([]byte(m.Config), &config)
	if err != nil {
		return nil, errors.New("解析七牛云存储配置失败: " + err.Error())
	}

	return &config, nil
}

// SetQiniuConfig 设置七牛云存储配置
func (m *UploadConfig) SetQiniuConfig(config *QiniuConfig) error {
	m.StorageMode = StorageModeQiniu
	data, err := json.Marshal(config)
	if err != nil {
		return errors.New("序列化七牛云存储配置失败: " + err.Error())
	}
	m.Config = string(data)
	return nil
}

// SetCDNConfig 设置CDN配置
func (m *UploadConfig) SetCDNConfig(config *CDNConfig) error {
	// 启用CDN并设置CDN域名
	m.EnableCdn = 1
	m.CdnDomain = config.Domain

	// CDN的密钥信息存储在Config字段中
	// 注意：这里存在两种情况
	// 1. 如果已经有存储配置，则需要将CDN配置与存储配置混合
	// 2. 如果是纯粹的CDN配置（无存储方式），则直接存储CDN配置

	var configMap map[string]interface{}

	// 如果已经有存储配置，先解析现有配置
	if m.Config != "" {
		err := json.Unmarshal([]byte(m.Config), &configMap)
		if err != nil {
			// 解析失败，创建新的配置对象
			configMap = make(map[string]interface{})
		}
	} else {
		// 创建新的配置对象
		configMap = make(map[string]interface{})
	}

	// 将CDN配置添加到配置对象中
	configMap["cdnAccessKey"] = config.AccessKey
	configMap["cdnSecretKey"] = config.SecretKey

	// 将配置对象序列化为JSON
	data, err := json.Marshal(configMap)
	if err != nil {
		return errors.New("序列化CDN配置失败: " + err.Error())
	}

	// 存储配置
	m.Config = string(data)
	return nil
}
