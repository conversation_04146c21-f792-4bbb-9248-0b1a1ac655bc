/**
 * system_config_repository_impl.go
 * 系统配置仓库实现
 *
 * 本文件实现了SystemConfigRepository接口，提供系统配置的CRUD操作
 */

package impl

import (
	"context"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/repositories"
)

// SystemConfigRepositoryImpl 系统配置仓库实现
type SystemConfigRepositoryImpl struct{}

// NewSystemConfigRepository 创建系统配置仓库实例
func NewSystemConfigRepository() repositories.SystemConfigRepository {
	return &SystemConfigRepositoryImpl{}
}

// GetConfigByKey 根据配置键获取配置
func (r *SystemConfigRepositoryImpl) GetConfigByKey(ctx context.Context, key string) (*models.SystemConfig, error) {
	o := orm.NewOrm()
	config := &models.SystemConfig{}

	err := o.QueryTable(new(models.SystemConfig)).Filter("config_key", key).One(config)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("[GetConfigByKey] 获取系统配置失败: %v, key=%s", err, key)
		return nil, fmt.Errorf("获取系统配置失败: %v", err)
	}

	return config, nil
}

// GetConfigsByKeys 批量获取系统配置
func (r *SystemConfigRepositoryImpl) GetConfigsByKeys(ctx context.Context, keys []string) ([]*models.SystemConfig, error) {
	o := orm.NewOrm()
	var configs []*models.SystemConfig

	_, err := o.QueryTable(new(models.SystemConfig)).Filter("config_key__in", keys).All(&configs)
	if err != nil {
		logs.Error("[GetConfigsByKeys] 批量获取系统配置失败: %v, keys=%v", err, keys)
		return nil, fmt.Errorf("批量获取系统配置失败: %v", err)
	}

	return configs, nil
}

// GetAllConfigs 获取所有系统配置
func (r *SystemConfigRepositoryImpl) GetAllConfigs(ctx context.Context) ([]*models.SystemConfig, error) {
	o := orm.NewOrm()
	var configs []*models.SystemConfig

	_, err := o.QueryTable(new(models.SystemConfig)).Filter("status", 1).All(&configs)
	if err != nil {
		logs.Error("[GetAllConfigs] 获取所有系统配置失败: %v", err)
		return nil, fmt.Errorf("获取所有系统配置失败: %v", err)
	}

	return configs, nil
}

// CreateConfig 创建系统配置
func (r *SystemConfigRepositoryImpl) CreateConfig(ctx context.Context, config *models.SystemConfig) (int64, error) {
	o := orm.NewOrm()

	// 先查询是否已存在相同键名的配置
	exist := &models.SystemConfig{}
	err := o.QueryTable(new(models.SystemConfig)).Filter("config_key", config.ConfigKey).One(exist)
	if err == nil {
		logs.Warn("[CreateConfig] 配置键已存在: %s", config.ConfigKey)
		return 0, fmt.Errorf("配置键 %s 已存在", config.ConfigKey)
	} else if err != orm.ErrNoRows {
		logs.Error("[CreateConfig] 查询配置键是否存在失败: %v", err)
		return 0, fmt.Errorf("创建系统配置失败: %v", err)
	}

	// 创建新配置
	id, err := o.Insert(config)
	if err != nil {
		logs.Error("[CreateConfig] 创建系统配置失败: %v", err)
		return 0, fmt.Errorf("创建系统配置失败: %v", err)
	}

	return id, nil
}

// BatchCreateConfigs 批量创建系统配置
func (r *SystemConfigRepositoryImpl) BatchCreateConfigs(ctx context.Context, configs []*models.SystemConfig) (int64, error) {
	if len(configs) == 0 {
		return 0, nil
	}

	o := orm.NewOrm()
	successCount := int64(0)

	// 开始事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[BatchCreateConfigs] 开启事务失败: %v", err)
		return 0, fmt.Errorf("批量创建系统配置失败: %v", err)
	}

	// 批量插入
	for _, config := range configs {
		// 检查是否已存在
		exist := &models.SystemConfig{}
		err := tx.QueryTable(new(models.SystemConfig)).Filter("config_key", config.ConfigKey).One(exist)
		if err == nil {
			// 配置已存在，跳过
			logs.Info("[BatchCreateConfigs] 配置键已存在，跳过: %s", config.ConfigKey)
			continue
		} else if err != orm.ErrNoRows {
			// 查询出错，回滚事务
			tx.Rollback()
			logs.Error("[BatchCreateConfigs] 查询配置键是否存在失败: %v", err)
			return 0, fmt.Errorf("批量创建系统配置失败: %v", err)
		}

		// 插入新配置
		_, err = tx.Insert(config)
		if err != nil {
			// 插入失败，回滚事务
			tx.Rollback()
			logs.Error("[BatchCreateConfigs] 创建系统配置失败: %v", err)
			return 0, fmt.Errorf("批量创建系统配置失败: %v", err)
		}
		successCount++
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		logs.Error("[BatchCreateConfigs] 提交事务失败: %v", err)
		return 0, fmt.Errorf("批量创建系统配置失败: %v", err)
	}

	return successCount, nil
}

// UpdateConfig 更新系统配置
func (r *SystemConfigRepositoryImpl) UpdateConfig(ctx context.Context, config *models.SystemConfig) error {
	if config.Id <= 0 {
		return fmt.Errorf("无效的系统配置ID")
	}

	o := orm.NewOrm()

	// 获取原配置
	original := &models.SystemConfig{Id: config.Id}
	err := o.Read(original)
	if err != nil {
		logs.Error("[UpdateConfig] 获取原配置失败: %v, id=%d", err, config.Id)
		return fmt.Errorf("更新系统配置失败: %v", err)
	}

	// 检查配置键是否被修改，如果修改了，需要检查新键名是否已存在
	if original.ConfigKey != config.ConfigKey {
		exist := &models.SystemConfig{}
		err := o.QueryTable(new(models.SystemConfig)).Filter("config_key", config.ConfigKey).One(exist)
		if err == nil && exist.Id != config.Id {
			logs.Warn("[UpdateConfig] 新配置键已存在: %s", config.ConfigKey)
			return fmt.Errorf("配置键 %s 已存在", config.ConfigKey)
		} else if err != orm.ErrNoRows && err != nil {
			logs.Error("[UpdateConfig] 查询配置键是否存在失败: %v", err)
			return fmt.Errorf("更新系统配置失败: %v", err)
		}
	}

	// 递增版本号
	config.Version = original.Version + 1

	// 更新配置
	_, err = o.Update(config)
	if err != nil {
		logs.Error("[UpdateConfig] 更新系统配置失败: %v", err)
		return fmt.Errorf("更新系统配置失败: %v", err)
	}

	return nil
}

// UpdateConfigValueByKey 根据键名更新系统配置值
func (r *SystemConfigRepositoryImpl) UpdateConfigValueByKey(ctx context.Context, key, value string) error {
	o := orm.NewOrm()

	// 获取原配置
	config := &models.SystemConfig{}
	err := o.QueryTable(new(models.SystemConfig)).Filter("config_key", key).One(config)
	if err != nil {
		if err == orm.ErrNoRows {
			logs.Warn("[UpdateConfigValueByKey] 配置键不存在: %s", key)
			return fmt.Errorf("配置键 %s 不存在", key)
		}
		logs.Error("[UpdateConfigValueByKey] 获取原配置失败: %v, key=%s", err, key)
		return fmt.Errorf("更新系统配置失败: %v", err)
	}

	// 递增版本号，更新配置值
	config.Version += 1
	config.ConfigValue = value

	// 更新配置
	_, err = o.Update(config, "config_value", "version")
	if err != nil {
		logs.Error("[UpdateConfigValueByKey] 更新系统配置失败: %v", err)
		return fmt.Errorf("更新系统配置失败: %v", err)
	}

	return nil
}

// DeleteConfig 删除系统配置
func (r *SystemConfigRepositoryImpl) DeleteConfig(ctx context.Context, id int64) error {
	if id <= 0 {
		return fmt.Errorf("无效的系统配置ID")
	}

	o := orm.NewOrm()

	// 获取配置信息
	config := &models.SystemConfig{Id: id}
	err := o.Read(config)
	if err != nil {
		if err == orm.ErrNoRows {
			logs.Warn("[DeleteConfig] 配置不存在: id=%d", id)
			return nil
		}
		logs.Error("[DeleteConfig] 获取配置失败: %v, id=%d", err, id)
		return fmt.Errorf("删除系统配置失败: %v", err)
	}

	// 检查是否为系统级配置
	if config.IsSystem == 1 {
		logs.Warn("[DeleteConfig] 无法删除系统级配置: id=%d, key=%s", id, config.ConfigKey)
		return fmt.Errorf("无法删除系统级配置")
	}

	// 删除配置
	_, err = o.Delete(config)
	if err != nil {
		logs.Error("[DeleteConfig] 删除系统配置失败: %v", err)
		return fmt.Errorf("删除系统配置失败: %v", err)
	}

	return nil
}

// IsConfigEmpty 判断配置表是否为空
func (r *SystemConfigRepositoryImpl) IsConfigEmpty(ctx context.Context) (bool, error) {
	o := orm.NewOrm()

	count, err := o.QueryTable(new(models.SystemConfig)).Count()
	if err != nil {
		logs.Error("[IsConfigEmpty] 查询配置表失败: %v", err)
		return false, fmt.Errorf("查询配置表失败: %v", err)
	}

	return count == 0, nil
}

// SearchConfigsWithDetails 搜索系统配置并返回详细信息
func (r *SystemConfigRepositoryImpl) SearchConfigsWithDetails(ctx context.Context, keyword string, status, isSystem int8, category string) ([]*models.SystemConfig, error) {
	o := orm.NewOrm()
	cond := orm.NewCondition()
	
	// 添加关键词搜索条件
	if keyword != "" {
		// 使用条件OR连接多个搜索字段
		keywordCond := orm.NewCondition()
		keywordCond = keywordCond.Or("config_key__icontains", keyword).
		              Or("config_value__icontains", keyword).
		              Or("description__icontains", keyword)
		cond = cond.AndCond(keywordCond)
	}
	
	// 添加状态筛选
	if status != -1 {
		cond = cond.And("status", status)
	}
	
	// 添加系统配置筛选
	if isSystem != -1 {
		cond = cond.And("is_system", isSystem)
	}
	
	// 添加分类筛选
	if category != "" {
		cond = cond.And("category", category)
	}
	
	// 执行查询
	qs := o.QueryTable(new(models.SystemConfig)).SetCond(cond)
	var configs []*models.SystemConfig
	_, err := qs.OrderBy("-updated_at").All(&configs)
	if err != nil {
		logs.Error("[SearchConfigsWithDetails] 搜索系统配置失败: %v", err)
		return nil, fmt.Errorf("搜索系统配置失败: %v", err)
	}
	
	return configs, nil
}
