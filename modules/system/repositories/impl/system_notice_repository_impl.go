/**
 * system_notice_repository_impl.go
 * 系统公告仓库实现
 *
 * 本文件实现了SystemNoticeRepository接口，提供系统公告的CRUD操作
 */

package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/repositories"
)

// SystemNoticeRepositoryImpl 系统公告仓库实现
type SystemNoticeRepositoryImpl struct{}

// NewSystemNoticeRepository 创建系统公告仓库实例
func NewSystemNoticeRepository() repositories.SystemNoticeRepository {
	return &SystemNoticeRepositoryImpl{}
}

// GetNotice 获取单个系统公告
func (r *SystemNoticeRepositoryImpl) GetNotice(ctx context.Context, id int64) (*models.SystemNotice, error) {
	if id <= 0 {
		return nil, fmt.Errorf("无效的系统公告ID")
	}

	o := orm.NewOrm()
	notice := &models.SystemNotice{Id: id}

	err := o.Read(notice)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("[GetNotice] 获取系统公告失败: %v, id=%d", err, id)
		return nil, fmt.Errorf("获取系统公告失败: %v", err)
	}

	return notice, nil
}

// GetActiveNotices 获取所有有效的系统公告
func (r *SystemNoticeRepositoryImpl) GetActiveNotices(ctx context.Context, target int8) ([]*models.SystemNotice, error) {
	o := orm.NewOrm()
	var notices []*models.SystemNotice

	// 构建查询条件
	qb, err := orm.NewQueryBuilder("mysql")
	if err != nil {
		logs.Error("[GetActiveNotices] 创建查询构建器失败: %v", err)
		return nil, fmt.Errorf("获取系统公告失败: %v", err)
	}

	nowStr := time.Now().Format("2006-01-02 15:04:05")

	// 查询条件：状态为已发布，且当前时间在生效时间和结束时间之间，并且目标用户匹配
	qb.Select("*").
		From("system_notice").
		Where("status = ?").
		And("start_time <= ?").
		And("(end_time >= ? OR end_time IS NULL)").
		And("(target = ? OR target = 0)"). // target=0表示所有用户
		OrderBy("is_top DESC, id DESC").   // 置顶的公告在前，新的公告在前
		Limit(10)                          // 最多返回10条公告

	// 构建SQL
	sql := qb.String()

	// 执行查询
	_, err = o.Raw(sql, 1, nowStr, nowStr, target).QueryRows(&notices)
	if err != nil {
		logs.Error("[GetActiveNotices] 获取有效公告失败: %v", err)
		return nil, fmt.Errorf("获取系统公告失败: %v", err)
	}

	return notices, nil
}

// GetNoticesByPage 分页获取系统公告
func (r *SystemNoticeRepositoryImpl) GetNoticesByPage(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*models.SystemNotice, int64, error) {
	o := orm.NewOrm()
	var notices []*models.SystemNotice

	// 构建基本查询
	query := o.QueryTable(new(models.SystemNotice))

	// 应用过滤条件
	if filters != nil {
		if title, ok := filters["title"].(string); ok && title != "" {
			query = query.Filter("title__icontains", title)
		}
		if status, ok := filters["status"].(int8); ok && status != -1 {
			query = query.Filter("status", status)
		}
		if target, ok := filters["target"].(int8); ok && target != -1 {
			query = query.Filter("target", target)
		}
		if typ, ok := filters["type"].(int8); ok && typ != -1 {
			query = query.Filter("type", typ)
		}
		if isTop, ok := filters["is_top"].(int8); ok && isTop != -1 {
			query = query.Filter("is_top", isTop)
		}
		if startTime, ok := filters["start_time"].(time.Time); ok {
			query = query.Filter("start_time__gte", startTime)
		}
		if endTime, ok := filters["end_time"].(time.Time); ok {
			query = query.Filter("end_time__lte", endTime)
		}
	}

	// 获取总记录数
	total, err := query.Count()
	if err != nil {
		logs.Error("[GetNoticesByPage] 获取公告总数失败: %v", err)
		return nil, 0, fmt.Errorf("获取系统公告失败: %v", err)
	}

	// 分页并排序
	offset := (page - 1) * pageSize
	_, err = query.OrderBy("-is_top", "-id").Offset(offset).Limit(pageSize).All(&notices)
	if err != nil {
		logs.Error("[GetNoticesByPage] 分页获取公告失败: %v", err)
		return nil, 0, fmt.Errorf("获取系统公告失败: %v", err)
	}

	return notices, total, nil
}

// CreateNotice 创建系统公告
func (r *SystemNoticeRepositoryImpl) CreateNotice(ctx context.Context, notice *models.SystemNotice) (int64, error) {
	o := orm.NewOrm()

	// 设置创建时间和更新时间
	now := time.Now()
	notice.CreatedAt = now
	notice.UpdatedAt = now

	// 插入数据
	id, err := o.Insert(notice)
	if err != nil {
		logs.Error("[CreateNotice] 创建系统公告失败: %v", err)
		return 0, fmt.Errorf("创建系统公告失败: %v", err)
	}

	return id, nil
}

// UpdateNotice 更新系统公告
func (r *SystemNoticeRepositoryImpl) UpdateNotice(ctx context.Context, notice *models.SystemNotice) error {
	if notice.Id <= 0 {
		return fmt.Errorf("无效的系统公告ID")
	}

	o := orm.NewOrm()

	// 获取原公告信息
	original := &models.SystemNotice{Id: notice.Id}
	err := o.Read(original)
	if err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("公告不存在")
		}
		logs.Error("[UpdateNotice] 获取原公告失败: %v, id=%d", err, notice.Id)
		return fmt.Errorf("更新系统公告失败: %v", err)
	}

	// 更新时间
	notice.UpdatedAt = time.Now()

	// 更新数据，排除创建时间和创建人
	_, err = o.Update(notice, "Title", "Content", "Type", "StartTime", "EndTime", "Target", "Status", "IsTop", "UpdatedAt")
	if err != nil {
		logs.Error("[UpdateNotice] 更新系统公告失败: %v", err)
		return fmt.Errorf("更新系统公告失败: %v", err)
	}

	return nil
}

// DeleteNotice 删除系统公告
func (r *SystemNoticeRepositoryImpl) DeleteNotice(ctx context.Context, id int64) error {
	if id <= 0 {
		return fmt.Errorf("无效的系统公告ID")
	}

	o := orm.NewOrm()

	// 获取公告信息
	notice := &models.SystemNotice{Id: id}
	err := o.Read(notice)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil // 不存在，视为删除成功
		}
		logs.Error("[DeleteNotice] 获取公告失败: %v, id=%d", err, id)
		return fmt.Errorf("删除系统公告失败: %v", err)
	}

	// 删除公告
	_, err = o.Delete(notice)
	if err != nil {
		logs.Error("[DeleteNotice] 删除系统公告失败: %v", err)
		return fmt.Errorf("删除系统公告失败: %v", err)
	}

	return nil
}

// UpdateNoticeStatus 更新公告状态
func (r *SystemNoticeRepositoryImpl) UpdateNoticeStatus(ctx context.Context, id int64, status int8) error {
	if id <= 0 {
		return fmt.Errorf("无效的系统公告ID")
	}

	o := orm.NewOrm()

	// 获取公告信息
	notice := &models.SystemNotice{Id: id}
	err := o.Read(notice)
	if err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("公告不存在")
		}
		logs.Error("[UpdateNoticeStatus] 获取公告失败: %v, id=%d", err, id)
		return fmt.Errorf("更新公告状态失败: %v", err)
	}

	// 更新状态和更新时间
	notice.Status = status
	notice.UpdatedAt = time.Now()

	_, err = o.Update(notice, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("[UpdateNoticeStatus] 更新公告状态失败: %v", err)
		return fmt.Errorf("更新公告状态失败: %v", err)
	}

	return nil
}

// UpdateNoticeTopStatus 更新公告置顶状态
func (r *SystemNoticeRepositoryImpl) UpdateNoticeTopStatus(ctx context.Context, id int64, isTop int8) error {
	if id <= 0 {
		return fmt.Errorf("无效的系统公告ID")
	}

	o := orm.NewOrm()

	// 获取公告信息
	notice := &models.SystemNotice{Id: id}
	err := o.Read(notice)
	if err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("公告不存在")
		}
		logs.Error("[UpdateNoticeTopStatus] 获取公告失败: %v, id=%d", err, id)
		return fmt.Errorf("更新公告置顶状态失败: %v", err)
	}

	// 更新置顶状态和更新时间
	notice.IsTop = isTop
	notice.UpdatedAt = time.Now()

	_, err = o.Update(notice, "IsTop", "UpdatedAt")
	if err != nil {
		logs.Error("[UpdateNoticeTopStatus] 更新公告置顶状态失败: %v", err)
		return fmt.Errorf("更新公告置顶状态失败: %v", err)
	}

	return nil
}
