/**
 * interface.go
 * 系统模块仓库层接口定义
 *
 * 本文件定义了系统模块所有仓库层的接口，包括系统配置、系统公告、短信配置和上传配置等。
 */

package repositories

import (
	"context"

	"o_mall_backend/modules/system/models"
)

// SystemConfigRepository 系统配置仓库接口
type SystemConfigRepository interface {
	// 获取单个系统配置
	GetConfigByKey(ctx context.Context, key string) (*models.SystemConfig, error)
	// 批量获取系统配置
	GetConfigsByKeys(ctx context.Context, keys []string) ([]*models.SystemConfig, error)
	// 获取所有系统配置
	GetAllConfigs(ctx context.Context) ([]*models.SystemConfig, error)
	// 搜索系统配置并返回详细信息
	SearchConfigsWithDetails(ctx context.Context, keyword string, status, isSystem int8, category string) ([]*models.SystemConfig, error)
	// 创建系统配置
	CreateConfig(ctx context.Context, config *models.SystemConfig) (int64, error)
	// 批量创建系统配置
	BatchCreateConfigs(ctx context.Context, configs []*models.SystemConfig) (int64, error)
	// 更新系统配置
	UpdateConfig(ctx context.Context, config *models.SystemConfig) error
	// 根据键名更新系统配置值
	UpdateConfigValueByKey(ctx context.Context, key, value string) error
	// 删除系统配置
	DeleteConfig(ctx context.Context, id int64) error
	// 判断配置表是否为空
	IsConfigEmpty(ctx context.Context) (bool, error)
}

// SystemNoticeRepository 系统公告仓库接口
type SystemNoticeRepository interface {
	// 获取单个系统公告
	GetNotice(ctx context.Context, id int64) (*models.SystemNotice, error)
	// 获取所有有效的系统公告
	GetActiveNotices(ctx context.Context, target int8) ([]*models.SystemNotice, error)
	// 分页获取系统公告
	GetNoticesByPage(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*models.SystemNotice, int64, error)
	// 创建系统公告
	CreateNotice(ctx context.Context, notice *models.SystemNotice) (int64, error)
	// 更新系统公告
	UpdateNotice(ctx context.Context, notice *models.SystemNotice) error
	// 删除系统公告
	DeleteNotice(ctx context.Context, id int64) error
	// 更新公告状态
	UpdateNoticeStatus(ctx context.Context, id int64, status int8) error
	// 更新公告置顶状态
	UpdateNoticeTopStatus(ctx context.Context, id int64, isTop int8) error
}

// SmsConfigRepository 短信配置仓库接口
type SmsConfigRepository interface {
	// 获取短信配置
	GetSmsConfig(ctx context.Context) (*models.SmsConfig, error)
	// 创建短信配置
	CreateSmsConfig(ctx context.Context, config *models.SmsConfig) (int64, error)
	// 更新短信配置
	UpdateSmsConfig(ctx context.Context, config *models.SmsConfig) error
	// 判断短信配置表是否为空
	IsSmsConfigEmpty(ctx context.Context) (bool, error)
}

// UploadConfigRepository 上传配置仓库接口
type UploadConfigRepository interface {
	// 获取上传配置
	GetUploadConfig(ctx context.Context) (*models.UploadConfig, error)
	// 创建上传配置
	CreateUploadConfig(ctx context.Context, config *models.UploadConfig) (int64, error)
	// 更新上传配置
	UpdateUploadConfig(ctx context.Context, config *models.UploadConfig) error
	// 判断上传配置表是否为空
	IsUploadConfigEmpty(ctx context.Context) (bool, error)
}
