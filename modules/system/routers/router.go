/**
 * 系统配置路由模块
 *
 * 该文件实现了系统配置模块的API路由配置，包括系统配置、系统公告、短信配置、上传配置等路由注册。
 * 不需要认证的接口直接放在/api/v1/system下，需要认证的接口放在/api/v1/system/secured子路径下。
 * 社区地址管理接口使用/api/v1/admin/system/addresses前缀。
 */

package routers

import (
	"fmt"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/system/controllers"
)

// InitSystemRouter 初始化系统模块路由
func InitSystemRouter() {
	// 获取配置
	apiPrefix, _ := web.AppConfig.String("api_prefix")
	apiVersion, _ := web.AppConfig.String("api_version")

	// 设置默认值
	if apiPrefix == "" {
		apiPrefix = "api"
	}
	if apiVersion == "" {
		apiVersion = "v1"
	}

	// 系统路径
	systemPath := fmt.Sprintf("/%s/%s/system", apiPrefix, apiVersion)
	// 管理系统路径
	adminSystemPath := fmt.Sprintf("/%s/%s/admin", apiPrefix, apiVersion)
	// 用户系统路径
	userSystemPath := fmt.Sprintf("/%s/%s/user/system", apiPrefix, apiVersion)

	// 创建命名空间
	systemNS := web.NewNamespace(systemPath,
		// 不需要认证的接口
		// 系统基本信息相关
		web.NSRouter("/info/basic", &controllers.SystemConfigController{}, "get:GetSystemInfo"),
		web.NSRouter("/info/contact", &controllers.SystemConfigController{}, "get:GetContactInfo"),
		web.NSRouter("/info/:key", &controllers.SystemConfigController{}, "get:GetPublicConfig"),
		web.NSRouter("/maintenance", &controllers.SystemConfigController{}, "get:GetMaintenanceMode"),
		//web.NSRouter("/configs", &controllers.SystemConfigController{}, "get:ListConfigs"),
		web.NSRouter("/configs/details", &controllers.SystemConfigController{}, "get:ListConfigsWithDetails"),

		// 系统公告前台查询接口
		web.NSRouter("/notices", &controllers.SystemNoticeController{}, "get:GetActiveNotices"),
		web.NSRouter("/notices/:id", &controllers.SystemNoticeController{}, "get:GetNotice"),

		// DeepSeek AI API接口
		web.NSRouter("/chat/completion", &controllers.AIController{}, "post:ChatCompletion"),

		// 获取地址选择器选项
		web.NSRouter("/addresses/options", &controllers.CommunityAddressController{}, "get:GetAddressOptions"),

		// UI生成器接口
		web.NSRouter("/ui/generate", &controllers.UIGeneratorController{}, "post:GenerateUIConfig"),
		web.NSRouter("/ui/generate/custom", &controllers.UIGeneratorController{}, "post:GenerateCustomUIConfig"),

		// 需要认证的接口，放在secured子路径下
		web.NSNamespace("/secured",
			// 系统配置管理
			// 获取配置列表（键值对形式）
			web.NSRouter("/configs", &controllers.SystemConfigController{}, "get:ListConfigs"),
			// 获取配置列表（详细信息）
			//web.NSRouter("/configs/details", &controllers.SystemConfigController{}, "get:ListConfigsWithDetails"),
			// 创建配置
			web.NSRouter("/configs", &controllers.SystemConfigController{}, "post:CreateConfig"),
			// 根据ID获取配置
			web.NSRouter("/configs/:id", &controllers.SystemConfigController{}, "get:GetConfig"),
			// 更新配置
			web.NSRouter("/configs/:id", &controllers.SystemConfigController{}, "put:UpdateConfig"),
			// 删除配置
			web.NSRouter("/configs/:id", &controllers.SystemConfigController{}, "delete:DeleteConfig"),
			// 根据键名获取配置
			web.NSRouter("/configs/key/:key", &controllers.SystemConfigController{}, "get:GetConfigByKey"),
			// 刷新配置缓存
			web.NSRouter("/configs/refresh", &controllers.SystemConfigController{}, "post:RefreshCache"),
			// 批量获取配置
			web.NSRouter("/configs/batch", &controllers.SystemConfigController{}, "post:GetMultiConfigs"),
			// 更新配置值
			web.NSRouter("/configs/value", &controllers.SystemConfigController{}, "put:UpdateConfigValue"),
			// 获取缓存信息
			web.NSRouter("/configs/cache", &controllers.SystemConfigController{}, "get:GetCacheInfo"),
			// 刷新缓存
			web.NSRouter("/configs/cache/refresh", &controllers.SystemConfigController{}, "post:RefreshCache"),

			// 维护模式管理
			web.NSRouter("/maintenance", &controllers.SystemConfigController{}, "put:UpdateMaintenanceMode"),

			// 系统公告管理
			web.NSRouter("/notices", &controllers.SystemNoticeController{}, "get:GetNoticeList"),
			web.NSRouter("/notices", &controllers.SystemNoticeController{}, "post:CreateNotice"),
			web.NSRouter("/notices/:id", &controllers.SystemNoticeController{}, "get:GetNotice"),
			web.NSRouter("/notices/:id", &controllers.SystemNoticeController{}, "put:UpdateNotice"),
			web.NSRouter("/notices/:id", &controllers.SystemNoticeController{}, "delete:DeleteNotice"),
			web.NSRouter("/notices/:id/status", &controllers.SystemNoticeController{}, "put:UpdateNoticeStatus"),
			web.NSRouter("/notices/:id/top", &controllers.SystemNoticeController{}, "put:UpdateNoticeTopStatus"),
			web.NSRouter("/notices/refresh", &controllers.SystemNoticeController{}, "post:RefreshCache"),

			// 短信配置管理
			web.NSRouter("/sms/config", &controllers.SmsConfigController{}, "get:GetConfig"),
			web.NSRouter("/sms/config", &controllers.SmsConfigController{}, "post:SaveConfig"),
			web.NSRouter("/sms/config/refresh", &controllers.SmsConfigController{}, "post:RefreshCache"),

			// 上传配置管理
			web.NSRouter("/upload/config", &controllers.UploadConfigController{}, "get:GetConfig"),
			web.NSRouter("/upload/config", &controllers.UploadConfigController{}, "post:SaveConfig"),
			web.NSRouter("/upload/config/refresh", &controllers.UploadConfigController{}, "post:RefreshCache"),
			web.NSRouter("/upload/test-connection", &controllers.UploadConfigController{}, "post:TestConnection"),

			// 文件用途配置管理
			web.NSRouter("/file-usage/list", &controllers.FileUsageConfigController{}, "get:GetList"),
			web.NSRouter("/file-usage/save", &controllers.FileUsageConfigController{}, "post:Save"),
			web.NSRouter("/file-usage/options", &controllers.FileUsageConfigController{}, "get:GetOptions"),
			web.NSRouter("/file-usage/init", &controllers.FileUsageConfigController{}, "post:InitDefault"),
			web.NSRouter("/file-usage/check-init", &controllers.FileUsageConfigController{}, "post:CheckAndInitRequiredUsages"),
			web.NSRouter("/file-usage/refresh", &controllers.FileUsageConfigController{}, "post:RefreshCache"),
			web.NSRouter("/file-usage/:id", &controllers.FileUsageConfigController{}, "get:GetDetail"),
			web.NSRouter("/file-usage/:id", &controllers.FileUsageConfigController{}, "delete:Delete"),

			// 创建社区地址
			web.NSRouter("/addresses", &controllers.CommunityAddressController{}, "post:CreateAddress"),
			// 更新社区地址
			web.NSRouter("/addresses", &controllers.CommunityAddressController{}, "put:UpdateAddress"),
			// 删除社区地址
			web.NSRouter("/addresses/:id", &controllers.CommunityAddressController{}, "delete:DeleteAddress"),
			// 获取社区地址详情
			web.NSRouter("/addresses/:id", &controllers.CommunityAddressController{}, "get:GetAddress"),
			// 获取社区地址列表
			web.NSRouter("/addresses", &controllers.CommunityAddressController{}, "get:ListAddresses"),
			// 获取社区地址树形结构
			web.NSRouter("/addresses/tree", &controllers.CommunityAddressController{}, "get:GetAddressTree"),
			// 获取地址选择器选项
			web.NSRouter("/addresses/options", &controllers.CommunityAddressController{}, "get:GetAddressOptions"),
			// 根据父级获取子地址列表
			web.NSRouter("/addresses/parent/:parentId", &controllers.CommunityAddressController{}, "get:GetAddressByParent"),
			// 获取完整地址信息
			web.NSRouter("/addresses/full-info", &controllers.CommunityAddressController{}, "post:GetFullAddressInfo"),
			// 刷新地址缓存
			web.NSRouter("/addresses/cache/refresh", &controllers.CommunityAddressController{}, "post:RefreshCache"),
		),
	)

	// 创建社区地址管理命名空间
	addressNS := web.NewNamespace(adminSystemPath,
		// 需要认证的接口，放在secured子路径下
		web.NSNamespace("/secured",

			// 创建社区地址
			web.NSRouter("/addresses", &controllers.CommunityAddressController{}, "post:CreateAddress"),
			// 更新社区地址
			web.NSRouter("/addresses", &controllers.CommunityAddressController{}, "put:UpdateAddress"),
			// 删除社区地址
			web.NSRouter("/addresses/:id", &controllers.CommunityAddressController{}, "delete:DeleteAddress"),
			// 获取社区地址详情
			web.NSRouter("/addresses/:id", &controllers.CommunityAddressController{}, "get:GetAddress"),
			// 获取社区地址列表
			web.NSRouter("/addresses", &controllers.CommunityAddressController{}, "get:ListAddresses"),
			// 获取社区地址树形结构
			web.NSRouter("/addresses/tree", &controllers.CommunityAddressController{}, "get:GetAddressTree"),
			// 获取地址选择器选项
			web.NSRouter("/addresses/options", &controllers.CommunityAddressController{}, "get:GetAddressOptions"),
			// 根据父级获取子地址列表
			web.NSRouter("/addresses/parent/:parentId", &controllers.CommunityAddressController{}, "get:GetAddressByParent"),
			// 获取完整地址信息
			web.NSRouter("/addresses/full-info", &controllers.CommunityAddressController{}, "post:GetFullAddressInfo"),
			// 刷新地址缓存
			web.NSRouter("/addresses/cache/refresh", &controllers.CommunityAddressController{}, "post:RefreshCache"),
		),
	)

	// 创建用户系统配置命名空间
	userSystemNS := web.NewNamespace(userSystemPath,
		// 需要用户认证的接口
		web.NSBefore(middlewares.JWTFilter),
		// 获取配送费配置信息
		web.NSRouter("/configs/details", &controllers.SystemConfigController{}, "get:GetUserConfigsWithDetails"),
	)

	// 注册命名空间
	web.AddNamespace(systemNS)
	// 注册社区地址管理命名空间
	web.AddNamespace(addressNS)
	// 注册用户系统配置命名空间
	web.AddNamespace(userSystemNS)

	// 添加JWT验证过滤器，只对/secured路径下的接口进行验证
	web.InsertFilter("/api/v1/system/secured/*", web.BeforeRouter, middlewares.JWTFilter)
	web.InsertFilter("/api/v1/admin/secured/*", web.BeforeRouter, middlewares.JWTFilter)

	// 添加权限验证过滤器，对系统配置相关的管理接口进行权限控制
	// 目前使用超级管理员角色控制，后续可改为细粒度的权限控制
	web.InsertFilter("/api/v1/system/secured/configs*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/system/secured/maintenance", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/system/secured/notices*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/system/secured/sms*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/system/secured/upload/config*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter("/api/v1/system/secured/file-usage*", web.BeforeRouter, middlewares.RoleFilter("super"))
	web.InsertFilter(fmt.Sprintf("%s/addresses*", adminSystemPath), web.BeforeRouter, middlewares.RoleFilter("super"))
}
