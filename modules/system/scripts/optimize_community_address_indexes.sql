-- 社区地址表性能优化索引脚本
-- 用于优化 /api/v1/admin/secured/addresses/tree 接口的查询性能

-- 注意：MySQL 不支持 IF NOT EXISTS 语法，请在执行前检查索引是否已存在
-- 如果索引已存在，会报错但不影响功能

-- 为 parent_id 字段创建索引，优化树形查询性能
CREATE INDEX idx_community_address_parent_id 
ON system_community_address (parent_id);

-- 为 status 字段创建索引，优化状态过滤性能
CREATE INDEX idx_community_address_status 
ON system_community_address (status);

-- 为 parent_id + status 创建复合索引，优化常用查询组合
CREATE INDEX idx_community_address_parent_status 
ON system_community_address (parent_id, status);

-- 为 parent_id + status + sort + id 创建复合索引，优化排序查询
CREATE INDEX idx_community_address_parent_status_sort 
ON system_community_address (parent_id, status, sort, id);

-- 为 level 字段创建索引，优化级别查询
CREATE INDEX idx_community_address_level 
ON system_community_address (level);

-- 查看索引创建结果
SHOW INDEX FROM system_community_address;