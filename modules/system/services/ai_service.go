/**
 * AI服务接口
 *
 * 本文件定义了AI服务的接口，提供了与AI API交互的方法，
 * 包括聊天补全、AI配置管理等功能。
 */

package services

import (
	"o_mall_backend/modules/system/dto"
)

// AIService AI服务接口
type AIService interface {
	// GetChatCompletion 获取AI聊天补全响应
	GetChatCompletion(request *dto.AIChatRequestDTO) (*dto.AIChatResponseDTO, error)

	// GetAIConfig 获取AI配置
	GetAIConfig() (*dto.AIConfigDTO, error)

	// SaveAIConfig 保存AI配置
	SaveAIConfig(config *dto.AIConfigDTO) error

	// RefreshAIConfig 刷新AI配置缓存
	RefreshAIConfig() error
}
