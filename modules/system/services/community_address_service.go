/**
 * community_address_service.go
 * 社区地址服务接口
 *
 * 本文件定义了社区地址服务的接口方法，包括地址的CRUD操作、树形结构获取等功能。
 */

package services

import (
	"context"
	
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
)

// CommunityAddressService 社区地址服务接口
type CommunityAddressService interface {
	// CreateAddress 创建社区地址
	CreateAddress(ctx context.Context, req *dto.CreateCommunityAddressRequest) (int64, error)
	
	// UpdateAddress 更新社区地址
	UpdateAddress(ctx context.Context, req *dto.UpdateCommunityAddressRequest) error
	
	// DeleteAddress 删除社区地址
	DeleteAddress(ctx context.Context, id int64) error
	
	// GetAddress 获取社区地址详情
	GetAddress(ctx context.Context, id int64) (*dto.CommunityAddressResponse, error)
	
	// ListAddresses 获取社区地址列表
	ListAddresses(ctx context.Context, req *dto.CommunityAddressQueryRequest) (*dto.CommunityAddressListResponse, error)
	
	// GetAddressTree 获取社区地址树形结构
	GetAddressTree(ctx context.Context, parentId int64) (*dto.CommunityAddressTreeResponse, error)
	
	// GetAddressOptions 获取地址选择器选项
	GetAddressOptions(ctx context.Context) (*dto.CommunityAddressOptionsResponse, error)
	
	// GetFullAddressInfo 获取完整地址信息
	GetFullAddressInfo(ctx context.Context, req *dto.SelectedCommunityAddressRequest) (*dto.SelectedCommunityAddressResponse, error)
	
	// RefreshCache 刷新社区地址缓存
	RefreshCache(ctx context.Context) error
	
	// GetCommunityModel 获取社区地址模型
	GetCommunityModel(ctx context.Context, id int64) (*models.CommunityAddress, error)
	
	// GetAddressByParentAndLevel 根据父级ID和级别获取地址列表
	GetAddressByParentAndLevel(ctx context.Context, parentId int64, level int) ([]*dto.CommunityAddressResponse, error)
}
