/*
 * file_usage_config_service.go
 * 文件用途配置服务接口
 *
 * 本文件定义了文件用途配置相关的业务逻辑接口
 */

package services

import (
	"context"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
)

// FileUsageConfigService 文件用途配置服务接口
type FileUsageConfigService interface {
	// GetFileUsageConfigList 获取文件用途配置列表
	GetFileUsageConfigList(ctx context.Context, req *dto.FileUsageConfigListRequest) (*dto.FileUsageConfigListResponse, error)

	// GetFileUsageConfigByID 根据ID获取文件用途配置
	GetFileUsageConfigByID(ctx context.Context, id int64) (*models.FileUsageConfig, error)

	// GetFileUsageConfigByCode 根据用途代码获取文件用途配置
	GetFileUsageConfigByCode(ctx context.Context, usageCode string) (*models.FileUsageConfig, error)

	// SaveFileUsageConfig 保存文件用途配置
	SaveFileUsageConfig(ctx context.Context, req *dto.SaveFileUsageConfigRequest) (*models.FileUsageConfig, error)

	// DeleteFileUsageConfig 删除文件用途配置
	DeleteFileUsageConfig(ctx context.Context, id int64) error

	// GetAllowedUsageTypes 获取允许的用途类型列表
	GetAllowedUsageTypes(ctx context.Context) ([]string, error)

	// GetAnonymousUsageTypes 获取允许匿名上传的用途类型列表
	GetAnonymousUsageTypes(ctx context.Context) ([]string, error)

	// GetFileUsageOptions 获取文件用途选项（用于前端下拉选择）
	GetFileUsageOptions(ctx context.Context) (*dto.FileUsageConfigOptionsResponse, error)

	// InitDefaultFileUsages 初始化默认文件用途配置
	InitDefaultFileUsages(ctx context.Context) error

	// RefreshCache 刷新缓存
	RefreshCache(ctx context.Context) error

	// IsUsageCodeExists 检查用途代码是否已存在
	IsUsageCodeExists(ctx context.Context, usageCode string, excludeID int64) (bool, error)

	// CheckAndInitRequiredUsages 检查并初始化必需的文件用途配置
	CheckAndInitRequiredUsages(ctx context.Context, usageCodes []string) error
}