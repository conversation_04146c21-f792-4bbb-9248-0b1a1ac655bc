/**
 * AI服务实现
 *
 * 本文件实现了AI服务接口，提供了与DeepSeek AI API交互的具体方法，
 * 包括聊天补全、AI配置管理等功能的实现。
 */

package impl

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/cache"
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
)

// 声明缓存对象，用于缓存AI配置
var aiConfigCache cache.Cache

// 常量定义
const (
	AIConfigCacheKey  = "system:ai:config:deepseek" // AI配置缓存键
	CacheExpiration   = 3600 * time.Second          // 缓存过期时间（秒）
	DeepSeekProvider  = "deepseek"                  // DeepSeek提供商标识
	DefaultBaseURL    = "https://api.deepseek.com/v1" // 默认API基础URL
	DefaultModel      = "deepseek-chat"             // 默认模型
)

// AIServiceImpl AI服务实现
type AIServiceImpl struct{}

// NewAIServiceImpl 创建AI服务实现实例
func NewAIServiceImpl() services.AIService {
	// 初始化缓存（如果尚未初始化）
	if aiConfigCache == nil {
		var err error
		aiConfigCache, err = cache.NewCache("memory", `{"interval":60}`)
		if err != nil {
			logs.Error("初始化AI配置缓存失败: %v", err)
		}
	}
	return &AIServiceImpl{}
}

// GetChatCompletion 获取AI聊天补全响应
// 该方法调用DeepSeek API获取聊天补全响应
func (s *AIServiceImpl) GetChatCompletion(request *dto.AIChatRequestDTO) (*dto.AIChatResponseDTO, error) {
	// 获取AI配置
	config, err := s.GetAIConfig()
	if err != nil {
		return nil, errors.New("获取AI配置失败: " + err.Error())
	}

	// 如果未配置API密钥，返回错误
	if config.DeepSeekAPIKey == "" {
		return nil, errors.New("未配置DeepSeek API密钥")
	}

	// 确保请求中有消息
	if len(request.Messages) == 0 {
		return nil, errors.New("消息列表不能为空")
	}

	// 使用配置中的默认模型（如果请求中没有指定模型）
	if request.Model == "" {
		request.Model = config.DefaultModel
	}

	// 准备系统角色消息（如果请求中指定了角色）
	if request.Role != "" && (len(request.Messages) == 0 || request.Messages[0].Role != "system") {
		systemMsg := dto.AIChatMessageDTO{
			Role:    "system",
			Content: fmt.Sprintf("你是一个提供专业%s服务的AI助手。请以专业、友好的方式回答问题。", request.Role),
		}
		request.Messages = append([]dto.AIChatMessageDTO{systemMsg}, request.Messages...)
	}

	// 准备请求体
	requestBody := map[string]interface{}{
		"model":       request.Model,
		"messages":    request.Messages,
		"temperature": request.Temperature,
	}
	
	// 仅当指定了MaxTokens时才添加此参数
	if request.MaxTokens > 0 {
		requestBody["max_tokens"] = request.MaxTokens
	}
	
	// 仅当指定了Stream时才添加此参数
	if request.Stream {
		requestBody["stream"] = request.Stream
	}

	// 转换为JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, errors.New("请求数据序列化失败: " + err.Error())
	}

	// 确定API URL
	apiURL := config.BaseURL
	if apiURL == "" {
		apiURL = DefaultBaseURL
	}
	chatURL := apiURL + "/chat/completions"

	// 创建HTTP请求
	req, err := http.NewRequest("POST", chatURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, errors.New("创建HTTP请求失败: " + err.Error())
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+config.DeepSeekAPIKey)

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: 300 * time.Second, // 增加超时时间到5分钟（300秒）
	}

	// 发送请求
	logs.Info("开始调用DeepSeek API，URL: %s", chatURL)
	resp, err := client.Do(req)
	if err != nil {
		logs.Error("调用DeepSeek API失败: %v", err)
		
		// 检查是否是超时错误，给出更友好的提示
		if errors.Is(err, context.DeadlineExceeded) || errors.Is(err, context.Canceled) || 
		   strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "deadline exceeded") {
			return nil, errors.New("DeepSeek API响应超时，请减少模型数据或稍后重试")
		}
		
		return nil, errors.New("发送请求到DeepSeek API失败: " + err.Error())
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.New("读取DeepSeek API响应失败: " + err.Error())
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("DeepSeek API错误 (状态码: %d): %s", resp.StatusCode, string(body))
	}

	// 解析响应数据
	var response dto.AIChatResponseDTO
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, errors.New("解析DeepSeek API响应失败: " + err.Error())
	}

	return &response, nil
}

// GetAIConfig 获取AI配置
// 首先尝试从缓存获取配置，如果缓存中不存在则从数据库获取
func (s *AIServiceImpl) GetAIConfig() (*dto.AIConfigDTO, error) {
	// 尝试从缓存获取
	var config dto.AIConfigDTO
	ctx := context.Background()
	
	if aiConfigCache != nil {
		data, err := aiConfigCache.Get(ctx, AIConfigCacheKey)
		if err == nil && data != nil {
			if cachedConfig, ok := data.(dto.AIConfigDTO); ok {
				return &cachedConfig, nil
			}
		}
	}

	// 从数据库获取
	o := orm.NewOrm()
	aiConfig := models.AIConfig{}
	err := aiConfig.GetByProvider(o, DeepSeekProvider)
	if err != nil {
		// 如果记录不存在，创建默认配置
		if err == orm.ErrNoRows {
			// 创建默认配置
			aiConfig.Provider = DeepSeekProvider
			aiConfig.BaseURL = DefaultBaseURL
			aiConfig.DefaultModel = DefaultModel
			aiConfig.Enabled = true
			
			// 尝试从环境变量获取API密钥
			apiKey, _ := web.AppConfig.String("deepseek_api_key")
			aiConfig.APIKey = apiKey
			
			// 保存到数据库
			if err := aiConfig.InsertOrUpdate(o); err != nil {
				return nil, errors.New("创建默认AI配置失败: " + err.Error())
			}
		} else {
			return nil, errors.New("获取AI配置失败: " + err.Error())
		}
	}

	// 转换为DTO
	config = dto.AIConfigDTO{
		DeepSeekAPIKey: aiConfig.APIKey,
		BaseURL:        aiConfig.BaseURL,
		DefaultModel:   aiConfig.DefaultModel,
	}

	// 缓存配置
	if aiConfigCache != nil {
		aiConfigCache.Put(ctx, AIConfigCacheKey, config, CacheExpiration)
	}

	return &config, nil
}

// SaveAIConfig 保存AI配置
// 将AI配置保存到数据库，并更新缓存
func (s *AIServiceImpl) SaveAIConfig(config *dto.AIConfigDTO) error {
	if config == nil {
		return errors.New("AI配置不能为空")
	}

	// 创建或更新数据库记录
	o := orm.NewOrm()
	aiConfig := models.AIConfig{}
	_ = aiConfig.GetByProvider(o, DeepSeekProvider)
	
	// 设置值
	aiConfig.Provider = DeepSeekProvider
	aiConfig.APIKey = config.DeepSeekAPIKey
	aiConfig.BaseURL = config.BaseURL
	aiConfig.DefaultModel = config.DefaultModel
	aiConfig.Enabled = true
	
	// 保存到数据库
	if err := aiConfig.InsertOrUpdate(o); err != nil {
		return errors.New("保存AI配置失败: " + err.Error())
	}

	// 更新缓存
	ctx := context.Background()
	if aiConfigCache != nil {
		aiConfigCache.Put(ctx, AIConfigCacheKey, *config, CacheExpiration)
	}

	return nil
}

// RefreshAIConfig 刷新AI配置缓存
// 从数据库重新加载AI配置并更新缓存
func (s *AIServiceImpl) RefreshAIConfig() error {
	// 从数据库获取
	o := orm.NewOrm()
	aiConfig := models.AIConfig{}
	err := aiConfig.GetByProvider(o, DeepSeekProvider)
	if err != nil {
		return errors.New("获取AI配置失败: " + err.Error())
	}

	// 转换为DTO
	config := dto.AIConfigDTO{
		DeepSeekAPIKey: aiConfig.APIKey,
		BaseURL:        aiConfig.BaseURL,
		DefaultModel:   aiConfig.DefaultModel,
	}

	// 更新缓存
	ctx := context.Background()
	if aiConfigCache != nil {
		aiConfigCache.Put(ctx, AIConfigCacheKey, config, CacheExpiration)
	}

	return nil
}
