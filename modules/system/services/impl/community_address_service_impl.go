/**
 * community_address_service_impl.go
 * 社区地址服务实现
 *
 * 本文件实现了社区地址服务接口的各项功能，包括地址的CRUD操作、树形结构获取等。
 */

package impl

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
)

// CommunityAddressServiceImpl 社区地址服务实现
type CommunityAddressServiceImpl struct {
	// 可添加依赖的其他服务
}

// NewCommunityAddressServiceImpl 创建社区地址服务实例
func NewCommunityAddressServiceImpl() services.CommunityAddressService {
	return &CommunityAddressServiceImpl{}
}

// CreateAddress 创建社区地址
func (s *CommunityAddressServiceImpl) CreateAddress(ctx context.Context, req *dto.CreateCommunityAddressRequest) (int64, error) {
	o := orm.NewOrm()

	// 创建地址实体
	address := &models.CommunityAddress{
		Name:        req.Name,
		ParentId:    req.ParentId,
		Level:       req.Level,
		Longitude:   req.Longitude,
		Latitude:    req.Latitude,
		Sort:        req.Sort,
		Status:      req.Status,
		Description: req.Description,
	}

	// 如果存在父级，需要获取父级信息并构建完整路径
	if req.ParentId > 0 {
		// 验证父级是否存在
		parent, err := s.GetCommunityModel(ctx, req.ParentId)
		if err != nil {
			return 0, err
		}

		// 验证父级与当前级别是否符合层级关系
		if parent.Level >= req.Level {
			return 0, errors.New("父级地址的层级必须小于当前地址的层级")
		}

		// 设置完整路径
		if parent.FullPath != "" {
			address.FullPath = fmt.Sprintf("%s/%s", parent.FullPath, req.Name)
		} else {
			address.FullPath = req.Name
		}
	} else {
		// 顶级地址
		address.FullPath = req.Name
	}

	// 保存地址
	id, err := o.Insert(address)
	if err != nil {
		logs.Error("[CreateAddress] 创建社区地址失败: %v", err)
		return 0, err
	}

	// 刷新缓存
	_ = s.RefreshCache(ctx)

	return id, nil
}

// UpdateAddress 更新社区地址
func (s *CommunityAddressServiceImpl) UpdateAddress(ctx context.Context, req *dto.UpdateCommunityAddressRequest) error {
	o := orm.NewOrm()

	// 查询地址是否存在
	address, err := s.GetCommunityModel(ctx, req.Id)
	if err != nil {
		return err
	}

	// 检查是否修改了父级ID，如果修改了需要更新完整路径
	if req.ParentId != address.ParentId {
		// 验证新父级
		if req.ParentId > 0 {
			parent, err := s.GetCommunityModel(ctx, req.ParentId)
			if err != nil {
				return err
			}

			// 验证父级与当前级别是否符合层级关系
			if parent.Level >= req.Level {
				return errors.New("父级地址的层级必须小于当前地址的层级")
			}

			// 设置新的完整路径
			if parent.FullPath != "" {
				address.FullPath = fmt.Sprintf("%s/%s", parent.FullPath, req.Name)
			} else {
				address.FullPath = req.Name
			}
		} else {
			// 变为顶级地址
			address.FullPath = req.Name
		}

		// 更新所有子地址的完整路径
		err = s.updateChildrenFullPath(ctx, address.Id, address.FullPath)
		if err != nil {
			return err
		}
	} else if req.Name != address.Name {
		// 名称变更，需要更新完整路径
		pathParts := strings.Split(address.FullPath, "/")
		if len(pathParts) > 0 {
			pathParts[len(pathParts)-1] = req.Name
			address.FullPath = strings.Join(pathParts, "/")
		} else {
			address.FullPath = req.Name
		}

		// 更新所有子地址的完整路径
		err = s.updateChildrenFullPath(ctx, address.Id, address.FullPath)
		if err != nil {
			return err
		}
	}

	// 更新基本信息
	address.Name = req.Name
	address.ParentId = req.ParentId
	address.Level = req.Level
	address.Longitude = req.Longitude
	address.Latitude = req.Latitude
	address.Sort = req.Sort
	address.Status = req.Status
	address.Description = req.Description

	// 保存更新
	_, err = o.Update(address)
	if err != nil {
		logs.Error("[UpdateAddress] 更新社区地址失败: %v", err)
		return err
	}

	// 刷新缓存
	_ = s.RefreshCache(ctx)

	return nil
}

// 更新子地址的完整路径
func (s *CommunityAddressServiceImpl) updateChildrenFullPath(ctx context.Context, parentId int64, parentFullPath string) error {
	o := orm.NewOrm()

	// 查询所有子地址
	var children []*models.CommunityAddress
	_, err := o.QueryTable(new(models.CommunityAddress).TableName()).
		Filter("parent_id", parentId).
		All(&children)
	if err != nil {
		logs.Error("[updateChildrenFullPath] 查询子地址失败: %v", err)
		return err
	}

	// 逐个更新子地址的路径
	for _, child := range children {
		newFullPath := fmt.Sprintf("%s/%s", parentFullPath, child.Name)
		child.FullPath = newFullPath
		_, err = o.Update(child, "FullPath")
		if err != nil {
			logs.Error("[updateChildrenFullPath] 更新子地址路径失败: %v", err)
			return err
		}

		// 递归更新子地址的子地址
		err = s.updateChildrenFullPath(ctx, child.Id, newFullPath)
		if err != nil {
			return err
		}
	}

	return nil
}

// DeleteAddress 删除社区地址
func (s *CommunityAddressServiceImpl) DeleteAddress(ctx context.Context, id int64) error {
	o := orm.NewOrm()

	// 检查是否存在子地址
	count, err := o.QueryTable(new(models.CommunityAddress).TableName()).
		Filter("parent_id", id).
		Count()
	if err != nil {
		logs.Error("[DeleteAddress] 检查子地址失败: %v", err)
		return err
	}
	if count > 0 {
		return errors.New("该地址下存在子地址，无法删除")
	}

	// 执行删除
	_, err = o.Delete(&models.CommunityAddress{Id: id})
	if err != nil {
		logs.Error("[DeleteAddress] 删除社区地址失败: %v", err)
		return err
	}

	// 刷新缓存
	_ = s.RefreshCache(ctx)

	return nil
}

// GetAddress 获取社区地址详情
func (s *CommunityAddressServiceImpl) GetAddress(ctx context.Context, id int64) (*dto.CommunityAddressResponse, error) {
	// 获取地址模型
	address, err := s.GetCommunityModel(ctx, id)
	if err != nil {
		return nil, err
	}

	// 转换为响应DTO
	return s.convertToAddressResponse(address), nil
}

// GetCommunityModel 获取社区地址模型
func (s *CommunityAddressServiceImpl) GetCommunityModel(ctx context.Context, id int64) (*models.CommunityAddress, error) {
	o := orm.NewOrm()
	address := &models.CommunityAddress{Id: id}
	err := o.Read(address)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, result.ErrNotFound
		}
		logs.Error("[GetCommunityModel] 获取社区地址失败: %v", err)
		return nil, err
	}
	return address, nil
}

// 转换为地址响应DTO
func (s *CommunityAddressServiceImpl) convertToAddressResponse(model *models.CommunityAddress) *dto.CommunityAddressResponse {
	if model == nil {
		return nil
	}
	return &dto.CommunityAddressResponse{
		Id:          model.Id,
		Name:        model.Name,
		ParentId:    model.ParentId,
		Level:       model.Level,
		Longitude:   model.Longitude,
		Latitude:    model.Latitude,
		FullPath:    model.FullPath,
		Sort:        model.Sort,
		Status:      model.Status,
		Description: model.Description,
		CreatedAt:   model.CreatedAt,
		UpdatedAt:   model.UpdatedAt,
	}
}
