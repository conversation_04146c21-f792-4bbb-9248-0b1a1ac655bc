package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/utils/redis"
)

// ListAddresses 获取社区地址列表
func (s *CommunityAddressServiceImpl) ListAddresses(ctx context.Context, req *dto.CommunityAddressQueryRequest) (*dto.CommunityAddressListResponse, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.CommunityAddress).TableName())

	// 构建查询条件
	if req.Name != "" {
		qs = qs.Filter("name__icontains", req.Name)
	}
	if req.Level > 0 {
		qs = qs.Filter("level", req.Level)
	}
	if req.ParentId > 0 {
		qs = qs.Filter("parent_id", req.ParentId)
	}
	// 修改状态过滤逻辑：当status为-1或未设置时，默认只查询启用状态的记录
	if req.Status >= 0 && req.Status <= 1 {
		qs = qs.Filter("status", req.Status)
	} else {
		// 默认只查询启用状态的记录
		qs = qs.Filter("status", 1)
	}

	// 计算总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("[ListAddresses] 获取社区地址总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	var addresses []*models.CommunityAddress
	_, err = qs.OrderBy("sort", "id").
		Limit(req.PageSize, (req.Page-1)*req.PageSize).
		All(&addresses)
	if err != nil {
		logs.Error("[ListAddresses] 获取社区地址列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	items := make([]dto.CommunityAddressResponse, 0, len(addresses))
	for _, address := range addresses {
		items = append(items, *s.convertToAddressResponse(address))
	}

	return &dto.CommunityAddressListResponse{
		Items: items,
		Total: total,
	}, nil
}

// GetAddressTree 获取社区地址树形结构
// 优化版本：一次性查询所有数据，在内存中构建树形结构，避免递归数据库查询
func (s *CommunityAddressServiceImpl) GetAddressTree(ctx context.Context, parentId int64) (*dto.CommunityAddressTreeResponse, error) {
	// 构建缓存键，不同的父级ID使用不同的缓存键
	cacheKey := fmt.Sprintf("system:community:address:tree:%d", parentId)
	
	// 尝试从缓存获取
	cacheData, err := redis.Get(cacheKey)
	if err == nil && len(cacheData) > 0 {
		// 缓存命中，解析JSON数据
		var treeResponse dto.CommunityAddressTreeResponse
		if err := json.Unmarshal([]byte(cacheData), &treeResponse); err == nil {
			logs.Info("[GetAddressTree] 从缓存获取地址树成功，parentId=%d", parentId)
			return &treeResponse, nil
		}
		logs.Warning("[GetAddressTree] 缓存解析失败: %v", err)
	}

	// 缓存未命中，从数据库获取
	logs.Info("[GetAddressTree] 缓存未命中，从数据库查询地址树，parentId=%d", parentId)
	
	// 优化：一次性查询所有启用的地址数据
	allAddresses, err := s.getAllActiveAddresses(ctx)
	if err != nil {
		return nil, err
	}

	// 在内存中构建树形结构
	addressMap := make(map[int64][]dto.CommunityAddressResponse)
	for _, address := range allAddresses {
		addressMap[address.ParentId] = append(addressMap[address.ParentId], address)
	}

	// 获取指定父级的直接子节点
	rootItems := addressMap[parentId]
	
	// 递归构建子树
	for i := range rootItems {
		s.buildAddressTreeFromMap(&rootItems[i], addressMap)
	}

	// 构建返回结果
	result := &dto.CommunityAddressTreeResponse{
		Items: rootItems,
	}
	
	// 将结果缓存
	jsonData, err := json.Marshal(result)
	if err == nil {
		// 缓存24小时
		err = redis.Set(cacheKey, string(jsonData), 24*time.Hour)
		if err != nil {
			logs.Warning("[GetAddressTree] 缓存结果失败: %v", err)
		}
	}

	return result, nil
}

// getAllActiveAddresses 一次性获取所有启用的地址数据
func (s *CommunityAddressServiceImpl) getAllActiveAddresses(ctx context.Context) ([]dto.CommunityAddressResponse, error) {
	o := orm.NewOrm()
	var addresses []*models.CommunityAddress

	// 一次性查询所有启用的地址，按父级ID和排序字段排序
	_, err := o.QueryTable(new(models.CommunityAddress)).
		Filter("status", 1).
		OrderBy("parent_id", "sort", "id").
		All(&addresses)
	if err != nil {
		logs.Error("[getAllActiveAddresses] 获取所有地址列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	result := make([]dto.CommunityAddressResponse, 0, len(addresses))
	for _, address := range addresses {
		result = append(result, *s.convertToAddressResponse(address))
	}

	return result, nil
}

// buildAddressTreeFromMap 从地址映射中构建树形结构（内存操作，无数据库查询）
func (s *CommunityAddressServiceImpl) buildAddressTreeFromMap(parent *dto.CommunityAddressResponse, addressMap map[int64][]dto.CommunityAddressResponse) {
	// 获取当前节点的子节点
	children, exists := addressMap[parent.Id]
	if !exists || len(children) == 0 {
		return
	}

	// 设置子节点
	parent.Children = children
	
	// 递归处理子节点
	for i := range parent.Children {
		s.buildAddressTreeFromMap(&parent.Children[i], addressMap)
	}
}

// 递归构建地址树（已废弃，保留用于兼容性）
// 注意：此方法性能较差，建议使用 buildAddressTreeFromMap
func (s *CommunityAddressServiceImpl) buildAddressTree(ctx context.Context, parent *dto.CommunityAddressResponse) error {
	// 获取子地址
	children, err := s.getAddressByParent(ctx, parent.Id)
	if err != nil {
		return err
	}

	if len(children) > 0 {
		parent.Children = children
		// 递归处理子地址
		for i := range parent.Children {
			err = s.buildAddressTree(ctx, &parent.Children[i])
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// 根据父级ID获取地址列表
func (s *CommunityAddressServiceImpl) getAddressByParent(ctx context.Context, parentId int64) ([]dto.CommunityAddressResponse, error) {
	o := orm.NewOrm()
	var addresses []*models.CommunityAddress

	// 查询指定父级的地址
	_, err := o.QueryTable(new(models.CommunityAddress)).
		Filter("parent_id", parentId).
		Filter("status", 1).
		OrderBy("sort", "id").
		All(&addresses)
	if err != nil {
		logs.Error("[getAddressByParent] 获取子地址列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	result := make([]dto.CommunityAddressResponse, 0, len(addresses))
	for _, address := range addresses {
		result = append(result, *s.convertToAddressResponse(address))
	}

	return result, nil
}

// GetAddressOptions 获取地址选择器选项
func (s *CommunityAddressServiceImpl) GetAddressOptions(ctx context.Context) (*dto.CommunityAddressOptionsResponse, error) {
	// 添加调试日志
	logs.Info("开始获取地址选择器选项，调试数据库连接...")
	
	// 检查数据库表结构
	o := orm.NewOrm()
	// 判断表是否存在
	dbName := "default" // 默认数据库名
	tableName := new(models.CommunityAddress).TableName()
	logs.Info("准备查询数据库表: %s，数据库连接: %s", tableName, dbName)
	
	// 直接尝试执行一个简单查询检查表是否存在
	var count int64
	err := o.Raw("SELECT COUNT(*) FROM " + tableName).QueryRow(&count)
	if err != nil {
		logs.Error("表查询出错: %v", err)
	} else {
		logs.Info("表存在，记录数: %d", count)
	}
	// 定义缓存键
	cacheKey := "system:community:address:options"
	
	// 尝试从缓存获取数据
	cachedData, err := redis.Get(cacheKey)
	if err == nil && cachedData != "" {
		// 缓存命中，解析缓存数据
		var cachedResponse dto.CommunityAddressOptionsResponse
		err = json.Unmarshal([]byte(cachedData), &cachedResponse)
		if err == nil {
			logs.Info("[GetAddressOptions] 从缓存获取地址选择器选项成功")
			return &cachedResponse, nil
		}
		// 解析失败时记录日志但继续执行，从数据库重新获取
		logs.Warn("[GetAddressOptions] 解析缓存数据失败: %v", err)
	}

	// 缓存未命中，一次性批量获取所有地址
	allAddresses, err := s.getAllAddresses(ctx)
	if err != nil {
		return nil, err
	}

	// 构建地址映射表，按父ID分组
	addressMap := make(map[int64][]*dto.CommunityAddressResponse)
	for _, address := range allAddresses {
		addressMap[address.ParentId] = append(addressMap[address.ParentId], address)
	}

	// 获取顶级地址（小区，父ID为0）
	communities := addressMap[0]
	
	// 构建选项树
	options := make([]dto.CommunityAddressOption, 0, len(communities))
	for _, community := range communities {
		// 只处理社区级别的地址
		if community.Level != models.AddressLevelCommunity {
			continue
		}
		
		option := dto.CommunityAddressOption{
			Value:     community.Id,
			Label:     community.Name,
			Longitude: community.Longitude,
			Latitude:  community.Latitude,
			Level:     community.Level,
		}

		// 非递归构建选项树
		s.buildAddressOptionsFromMap(&option, addressMap)
		options = append(options, option)
	}

	// 构建响应对象
	response := &dto.CommunityAddressOptionsResponse{
		Options: options,
	}
	
	// 将结果存入缓存（有效期24小时）
	responseJSON, err := json.Marshal(response)
	if err != nil {
		logs.Warn("[GetAddressOptions] 序列化地址选择器选项失败: %v", err)
	} else {
		err = redis.Set(cacheKey, string(responseJSON), 24*time.Hour)
		if err != nil {
			logs.Warn("[GetAddressOptions] 缓存地址选择器选项失败: %v", err)
		}
	}

	return response, nil
}

// 递归构建地址选项树 - 保留兼容现有代码
func (s *CommunityAddressServiceImpl) buildAddressOptions(ctx context.Context, parent *dto.CommunityAddressOption) error {
	// 获取子地址
	var nextLevel int
	if parent.Level == models.AddressLevelCommunity {
		nextLevel = models.AddressLevelBuilding
	} else if parent.Level == models.AddressLevelBuilding {
		nextLevel = models.AddressLevelUnit
	} else {
		// 已经是最低级，无需继续查询
		return nil
	}

	children, err := s.GetAddressByParentAndLevel(ctx, parent.Value, nextLevel)
	if err != nil {
		return err
	}

	if len(children) > 0 {
		childOptions := make([]dto.CommunityAddressOption, 0, len(children))
		for _, child := range children {
			option := dto.CommunityAddressOption{
				Value:     child.Id,
				Label:     child.Name,
				Longitude: child.Longitude,
				Latitude:  child.Latitude,
				Level:     child.Level,
			}

			// 递归构建选项树
			if child.Level < models.AddressLevelUnit {
				err = s.buildAddressOptions(ctx, &option)
				if err != nil {
					return err
				}
			}

			childOptions = append(childOptions, option)
		}

		parent.Children = childOptions
	}

	return nil
}

// 非递归方式构建地址选项树 - 通过内存中的地址映射表构建树
func (s *CommunityAddressServiceImpl) buildAddressOptionsFromMap(parent *dto.CommunityAddressOption, addressMap map[int64][]*dto.CommunityAddressResponse) {
	// 确定下一级别
	var nextLevel int
	if parent.Level == models.AddressLevelCommunity {
		nextLevel = models.AddressLevelBuilding
	} else if parent.Level == models.AddressLevelBuilding {
		nextLevel = models.AddressLevelUnit
	} else {
		// 已经是最低级，无需继续构建
		return
	}
	
	// 从映射表中获取子地址
	children, exists := addressMap[parent.Value]
	if !exists || len(children) == 0 {
		return
	}
	
	// 构建子选项列表
	childOptions := make([]dto.CommunityAddressOption, 0)
	for _, child := range children {
		// 只处理符合下一级别的地址
		if child.Level != nextLevel {
			continue
		}
		
		option := dto.CommunityAddressOption{
			Value:     child.Id,
			Label:     child.Name,
			Longitude: child.Longitude,
			Latitude:  child.Latitude,
			Level:     child.Level,
		}
		
		// 递归构建下一级
		s.buildAddressOptionsFromMap(&option, addressMap)
		childOptions = append(childOptions, option)
	}
	
	parent.Children = childOptions
}

// getAllAddresses 获取所有有效社区地址
func (s *CommunityAddressServiceImpl) getAllAddresses(ctx context.Context) ([]*dto.CommunityAddressResponse, error) {
	o := orm.NewOrm()
	var addresses []*models.CommunityAddress
	
	// 使用新版beego ORM查询全部地址
	_, err := o.QueryTable(new(models.CommunityAddress)).
		Filter("status", 1).
		OrderBy("level", "parent_id", "sort", "id").
		All(&addresses)
	if err != nil {
		logs.Error("[getAllAddresses] 批量获取地址数据失败: %v", err)
		return nil, err
	}
	
	// 转换为响应对象
	responses := make([]*dto.CommunityAddressResponse, 0, len(addresses))
	for _, address := range addresses {
		responses = append(responses, s.convertToAddressResponse(address))
	}
	
	return responses, nil
}

// GetAddressByParentAndLevel 根据父级ID和级别获取地址列表
func (s *CommunityAddressServiceImpl) GetAddressByParentAndLevel(ctx context.Context, parentId int64, level int) ([]*dto.CommunityAddressResponse, error) {
	o := orm.NewOrm()
	var addresses []*models.CommunityAddress

	// 查询指定父级和级别的地址
	_, err := o.QueryTable(new(models.CommunityAddress)).
		Filter("parent_id", parentId).
		Filter("level", level).
		Filter("status", 1).
		OrderBy("sort", "id").
		All(&addresses)
	if err != nil {
		logs.Error("[GetAddressByParentAndLevel] 获取地址列表失败: %v", err)
		return nil, err
	}

	// 转换为响应对象
	result := make([]*dto.CommunityAddressResponse, 0, len(addresses))
	for _, address := range addresses {
		result = append(result, s.convertToAddressResponse(address))
	}

	return result, nil
}

// GetFullAddressInfo 获取完整地址信息
func (s *CommunityAddressServiceImpl) GetFullAddressInfo(ctx context.Context, req *dto.SelectedCommunityAddressRequest) (*dto.SelectedCommunityAddressResponse, error) {
	// 获取单元信息，包含完整路径
	unit, err := s.GetAddress(ctx, req.UnitId)
	if err != nil {
		return nil, err
	}

	// 验证级别是否正确
	if unit.Level != models.AddressLevelUnit {
		return nil, fmt.Errorf("选择的单元地址级别错误，当前级别: %d, 需要级别: %d", unit.Level, models.AddressLevelUnit)
	}

	return &dto.SelectedCommunityAddressResponse{
		FullPath:  unit.FullPath,
		Longitude: unit.Longitude,
		Latitude:  unit.Latitude,
	}, nil
}

// RefreshCache 刷新社区地址缓存
func (s *CommunityAddressServiceImpl) RefreshCache(ctx context.Context) error {
	o := orm.NewOrm()
	var addresses []*models.CommunityAddress

	// 获取所有地址
	_, err := o.QueryTable(new(models.CommunityAddress).TableName()).
		All(&addresses)
	if err != nil {
		logs.Error("[RefreshCache] 获取所有地址失败: %v", err)
		return err
	}

	// 构建缓存数据
	addressMap := make(map[int64]*models.CommunityAddress)
	for _, address := range addresses {
		addressMap[address.Id] = address
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(addressMap)
	if err != nil {
		logs.Error("[RefreshCache] 序列化地址数据失败: %v", err)
		return err
	}

	// 更新缓存
	err = redis.Set(models.CommunityAddressAllCacheKey, string(jsonData), models.CommunityAddressCacheExpiration)
	if err != nil {
		logs.Error("[RefreshCache] 更新地址缓存失败: %v", err)
		return err
	}

	return nil
}
