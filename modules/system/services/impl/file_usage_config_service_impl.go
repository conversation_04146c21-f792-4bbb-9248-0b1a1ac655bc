/*
 * file_usage_config_service_impl.go
 * 文件用途配置服务实现
 *
 * 本文件实现了文件用途配置相关的业务逻辑
 */

package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/dto"
	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
)

// fileUsageConfigServiceImpl 文件用途配置服务实现
type fileUsageConfigServiceImpl struct {
}

// NewFileUsageConfigServiceImpl 创建文件用途配置服务实例
func NewFileUsageConfigServiceImpl() services.FileUsageConfigService {
	return &fileUsageConfigServiceImpl{}
}

// GetFileUsageConfigList 获取文件用途配置列表
func (s *fileUsageConfigServiceImpl) GetFileUsageConfigList(ctx context.Context, req *dto.FileUsageConfigListRequest) (*dto.FileUsageConfigListResponse, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.FileUsageConfig))

	// 构建查询条件
	if req.UsageCode != "" {
		qs = qs.Filter("usage_code__icontains", req.UsageCode)
	}
	if req.UsageName != "" {
		qs = qs.Filter("usage_name__icontains", req.UsageName)
	}
	if req.Status != nil {
		qs = qs.Filter("status", *req.Status)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("[GetFileUsageConfigList] 获取总数失败: %v", err)
		return nil, fmt.Errorf("获取总数失败: %v", err)
	}

	// 设置排序
	sortField := "sort_order"
	if req.SortField != "" {
		sortField = req.SortField
	}
	if req.SortOrder == "desc" {
		sortField = "-" + sortField
	}
	qs = qs.OrderBy(sortField, "id")

	// 分页
	offset := (req.Page - 1) * req.PageSize
	qs = qs.Limit(req.PageSize, offset)

	// 查询数据
	var configs []models.FileUsageConfig
	_, err = qs.All(&configs)
	if err != nil {
		logs.Error("[GetFileUsageConfigList] 查询数据失败: %v", err)
		return nil, fmt.Errorf("查询数据失败: %v", err)
	}

	// 转换为响应对象
	var list []dto.FileUsageConfigResponse
	for _, config := range configs {
		response := s.convertToResponse(&config)
		list = append(list, *response)
	}

	return &dto.FileUsageConfigListResponse{
		Total: int(total),
		List:  list,
	}, nil
}

// GetFileUsageConfigByID 根据ID获取文件用途配置
func (s *fileUsageConfigServiceImpl) GetFileUsageConfigByID(ctx context.Context, id int64) (*models.FileUsageConfig, error) {
	o := orm.NewOrm()
	config := &models.FileUsageConfig{ID: id}
	err := o.Read(config)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("[GetFileUsageConfigByID] 查询配置失败: %v", err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}
	return config, nil
}

// GetFileUsageConfigByCode 根据用途代码获取文件用途配置
func (s *fileUsageConfigServiceImpl) GetFileUsageConfigByCode(ctx context.Context, usageCode string) (*models.FileUsageConfig, error) {
	o := orm.NewOrm()
	config := &models.FileUsageConfig{}
	err := o.QueryTable(config).Filter("usage_code", usageCode).Filter("status", 1).One(config)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("[GetFileUsageConfigByCode] 查询配置失败: %v", err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}
	return config, nil
}

// SaveFileUsageConfig 保存文件用途配置
func (s *fileUsageConfigServiceImpl) SaveFileUsageConfig(ctx context.Context, req *dto.SaveFileUsageConfigRequest) (*models.FileUsageConfig, error) {
	o := orm.NewOrm()

	// 检查用途代码是否已存在
	exists, err := s.IsUsageCodeExists(ctx, req.UsageCode, req.ID)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, fmt.Errorf("用途代码 %s 已存在", req.UsageCode)
	}

	var config *models.FileUsageConfig
	if req.ID > 0 {
		// 更新
		config, err = s.GetFileUsageConfigByID(ctx, req.ID)
		if err != nil {
			return nil, err
		}
		if config == nil {
			return nil, fmt.Errorf("配置不存在")
		}
	} else {
		// 新增
		config = &models.FileUsageConfig{}
		config.CreatedAt = time.Now()
	}

	// 设置字段值
	config.UsageCode = req.UsageCode
	config.UsageName = req.UsageName
	config.Description = req.Description
	config.AllowAnonymous = req.AllowAnonymous
	config.MaxFileSize = req.MaxFileSize
	config.AllowedTypes = req.AllowedTypes
	config.SortOrder = req.SortOrder
	config.Status = req.Status
	config.Remark = req.Remark
	config.UpdatedAt = time.Now()

	if req.ID > 0 {
		// 更新
		_, err = o.Update(config)
	} else {
		// 新增
		_, err = o.Insert(config)
	}

	if err != nil {
		logs.Error("[SaveFileUsageConfig] 保存配置失败: %v", err)
		return nil, fmt.Errorf("保存配置失败: %v", err)
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return config, nil
}

// DeleteFileUsageConfig 删除文件用途配置
func (s *fileUsageConfigServiceImpl) DeleteFileUsageConfig(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	config := &models.FileUsageConfig{ID: id}
	_, err := o.Delete(config)
	if err != nil {
		logs.Error("[DeleteFileUsageConfig] 删除配置失败: %v", err)
		return fmt.Errorf("删除配置失败: %v", err)
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// GetAllowedUsageTypes 获取允许的用途类型列表
func (s *fileUsageConfigServiceImpl) GetAllowedUsageTypes(ctx context.Context) ([]string, error) {
	o := orm.NewOrm()
	var configs []models.FileUsageConfig
	_, err := o.QueryTable(new(models.FileUsageConfig)).Filter("status", 1).OrderBy("sort_order", "id").All(&configs)
	if err != nil {
		logs.Error("[GetAllowedUsageTypes] 查询配置失败: %v", err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}

	var usageTypes []string
	for _, config := range configs {
		usageTypes = append(usageTypes, config.UsageCode)
	}

	return usageTypes, nil
}

// GetAnonymousUsageTypes 获取允许匿名上传的用途类型列表
func (s *fileUsageConfigServiceImpl) GetAnonymousUsageTypes(ctx context.Context) ([]string, error) {
	o := orm.NewOrm()
	var configs []models.FileUsageConfig
	_, err := o.QueryTable(new(models.FileUsageConfig)).Filter("status", 1).Filter("allow_anonymous", 1).OrderBy("sort_order", "id").All(&configs)
	if err != nil {
		logs.Error("[GetAnonymousUsageTypes] 查询配置失败: %v", err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}

	var usageTypes []string
	for _, config := range configs {
		usageTypes = append(usageTypes, config.UsageCode)
	}

	return usageTypes, nil
}

// GetFileUsageOptions 获取文件用途选项
func (s *fileUsageConfigServiceImpl) GetFileUsageOptions(ctx context.Context) (*dto.FileUsageConfigOptionsResponse, error) {
	allowedTypes, err := s.GetAllowedUsageTypes(ctx)
	if err != nil {
		return nil, err
	}

	anonymousTypes, err := s.GetAnonymousUsageTypes(ctx)
	if err != nil {
		return nil, err
	}

	return &dto.FileUsageConfigOptionsResponse{
		AllowedUsageTypes:   allowedTypes,
		AnonymousUsageTypes: anonymousTypes,
	}, nil
}

// InitDefaultFileUsages 初始化默认文件用途配置
func (s *fileUsageConfigServiceImpl) InitDefaultFileUsages(ctx context.Context) error {
	o := orm.NewOrm()

	// 检查是否已有配置
	count, err := o.QueryTable(new(models.FileUsageConfig)).Count()
	if err != nil {
		logs.Error("[InitDefaultFileUsages] 检查配置数量失败: %v", err)
		return fmt.Errorf("检查配置数量失败: %v", err)
	}

	if count > 0 {
		logs.Info("[InitDefaultFileUsages] 已存在文件用途配置，跳过初始化")
		return nil
	}

	// 插入默认配置
	defaultUsages := models.GetDefaultFileUsages()
	for _, usage := range defaultUsages {
		usage.CreatedAt = time.Now()
		usage.UpdatedAt = time.Now()
		_, err = o.Insert(&usage)
		if err != nil {
			logs.Error("[InitDefaultFileUsages] 插入默认配置失败: %v", err)
			return fmt.Errorf("插入默认配置失败: %v", err)
		}
	}

	logs.Info("[InitDefaultFileUsages] 初始化默认文件用途配置成功")
	return nil
}

// RefreshCache 刷新缓存
func (s *fileUsageConfigServiceImpl) RefreshCache(ctx context.Context) error {
	// TODO: 实现缓存刷新逻辑
	logs.Info("[RefreshCache] 文件用途配置缓存已刷新")
	return nil
}

// IsUsageCodeExists 检查用途代码是否已存在
func (s *fileUsageConfigServiceImpl) IsUsageCodeExists(ctx context.Context, usageCode string, excludeID int64) (bool, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.FileUsageConfig)).Filter("usage_code", usageCode)
	if excludeID > 0 {
		qs = qs.Exclude("id", excludeID)
	}

	count, err := qs.Count()
	if err != nil {
		logs.Error("[IsUsageCodeExists] 检查用途代码失败: %v", err)
		return false, fmt.Errorf("检查用途代码失败: %v", err)
	}

	return count > 0, nil
}

// CheckAndInitRequiredUsages 检查并初始化必需的文件用途配置
func (s *fileUsageConfigServiceImpl) CheckAndInitRequiredUsages(ctx context.Context, usageCodes []string) error {
	o := orm.NewOrm()

	// 获取所有默认配置
	defaultUsages := models.GetDefaultFileUsages()
	usageMap := make(map[string]models.FileUsageConfig)
	for _, usage := range defaultUsages {
		usageMap[usage.UsageCode] = usage
	}

	// 检查每个必需的用途代码
	for _, usageCode := range usageCodes {
		// 检查数据库中是否已存在
		count, err := o.QueryTable(new(models.FileUsageConfig)).Filter("usage_code", usageCode).Count()
		if err != nil {
			logs.Error("[CheckAndInitRequiredUsages] 检查用途代码 %s 失败: %v", usageCode, err)
			return fmt.Errorf("检查用途代码 %s 失败: %v", usageCode, err)
		}

		// 如果不存在，则添加默认配置
		if count == 0 {
			if defaultUsage, exists := usageMap[usageCode]; exists {
				defaultUsage.CreatedAt = time.Now()
				defaultUsage.UpdatedAt = time.Now()
				_, err = o.Insert(&defaultUsage)
				if err != nil {
					logs.Error("[CheckAndInitRequiredUsages] 插入用途代码 %s 失败: %v", usageCode, err)
					return fmt.Errorf("插入用途代码 %s 失败: %v", usageCode, err)
				}
				logs.Info("[CheckAndInitRequiredUsages] 成功添加用途代码: %s", usageCode)
			} else {
				logs.Warn("[CheckAndInitRequiredUsages] 未找到用途代码 %s 的默认配置", usageCode)
			}
		} else {
			logs.Info("[CheckAndInitRequiredUsages] 用途代码 %s 已存在，跳过", usageCode)
		}
	}

	logs.Info("[CheckAndInitRequiredUsages] 检查并初始化必需配置完成")
	return nil
}

// convertToResponse 转换为响应对象
func (s *fileUsageConfigServiceImpl) convertToResponse(config *models.FileUsageConfig) *dto.FileUsageConfigResponse {
	response := &dto.FileUsageConfigResponse{
		ID:             config.ID,
		UsageCode:      config.UsageCode,
		UsageName:      config.UsageName,
		Description:    config.Description,
		AllowAnonymous: config.AllowAnonymous,
		MaxFileSize:    config.MaxFileSize,
		AllowedTypes:   config.AllowedTypes,
		SortOrder:      config.SortOrder,
		Status:         config.Status,
		Remark:         config.Remark,
		CreatedAt:      config.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:      config.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 设置文本字段
	if config.AllowAnonymous == 1 {
		response.AllowAnonymousText = "是"
	} else {
		response.AllowAnonymousText = "否"
	}

	if config.Status == 1 {
		response.StatusText = "启用"
	} else {
		response.StatusText = "禁用"
	}

	if config.MaxFileSize > 0 {
		response.MaxFileSizeMB = int(config.MaxFileSize / 1024 / 1024)
	} else {
		response.MaxFileSizeMB = 0
	}

	return response
}