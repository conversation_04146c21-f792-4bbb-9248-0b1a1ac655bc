/**
 * sms_config_service_impl.go
 * 短信配置服务实现
 *
 * 本文件实现了SmsConfigService接口，提供短信配置的业务逻辑处理，
 * 包括配置的获取、保存和缓存处理。
 */

package impl

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/utils/redis"
)

// SmsConfigServiceImpl 短信配置服务实现
type SmsConfigServiceImpl struct{}

// NewSmsConfigServiceImpl 创建短信配置服务实例
func NewSmsConfigServiceImpl() services.SmsConfigService {
	return &SmsConfigServiceImpl{}
}

// GetSmsConfig 获取短信配置
func (s *SmsConfigServiceImpl) GetSmsConfig(ctx context.Context) (*models.SmsConfig, error) {
	// 首先尝试从Redis缓存获取
	cacheData, err := redis.Get(models.SmsConfigCacheKey)
	if err == nil && cacheData != "" {
		// 缓存命中，解析JSON数据
		config := &models.SmsConfig{}
		if err := json.Unmarshal([]byte(cacheData), config); err == nil {
			return config, nil
		}
		// 解析失败则继续从数据库查询
		logs.Error("[GetSmsConfig] 解析缓存数据失败: %v", err)
	}

	// 从数据库查询
	o := orm.NewOrm()
	var config models.SmsConfig

	// 短信配置通常只有一条记录，获取状态为启用的第一条
	err = o.QueryTable(new(models.SmsConfig)).Filter("status", 1).One(&config)
	if err != nil {
		if err == orm.ErrNoRows {
			// 如果没有启用的配置，尝试获取任何一条配置
			err = o.QueryTable(new(models.SmsConfig)).One(&config)
			if err != nil {
				if err == orm.ErrNoRows {
					return nil, fmt.Errorf("短信配置不存在")
				}
				logs.Error("[GetSmsConfig] 获取短信配置失败: %v", err)
				return nil, fmt.Errorf("获取短信配置失败: %v", err)
			}
		} else {
			logs.Error("[GetSmsConfig] 获取短信配置失败: %v", err)
			return nil, fmt.Errorf("获取短信配置失败: %v", err)
		}
	}

	// 更新缓存
	cacheBytes, err := json.Marshal(config)
	if err == nil {
		redis.Set(models.SmsConfigCacheKey, string(cacheBytes), models.SmsConfigCacheExpiration)
	}

	return &config, nil
}

// SaveSmsConfig 创建/更新短信配置
func (s *SmsConfigServiceImpl) SaveSmsConfig(ctx context.Context, config *models.SmsConfig) error {
	o := orm.NewOrm()

	if config.Id > 0 {
		// 更新现有配置
		_, err := o.Update(config)
		if err != nil {
			logs.Error("[SaveSmsConfig] 更新短信配置失败: %v", err)
			return fmt.Errorf("更新短信配置失败: %v", err)
		}
	} else {
		// 创建新配置
		_, err := o.Insert(config)
		if err != nil {
			logs.Error("[SaveSmsConfig] 创建短信配置失败: %v", err)
			return fmt.Errorf("创建短信配置失败: %v", err)
		}
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// RefreshCache 刷新缓存
func (s *SmsConfigServiceImpl) RefreshCache(ctx context.Context) error {
	// 删除缓存
	_, err := redis.Del(models.SmsConfigCacheKey)
	if err != nil {
		logs.Error("[RefreshCache] 删除短信配置缓存失败: %v", err)
	}

	// 从数据库重新获取配置并缓存
	o := orm.NewOrm()
	var config models.SmsConfig

	// 尝试获取启用的配置
	err = o.QueryTable(new(models.SmsConfig)).Filter("status", 1).One(&config)
	if err != nil {
		if err == orm.ErrNoRows {
			// 如果没有启用的配置，尝试获取任何一条配置
			err = o.QueryTable(new(models.SmsConfig)).One(&config)
			if err != nil {
				if err == orm.ErrNoRows {
					// 没有配置，不需要更新缓存
					return nil
				}
				logs.Error("[RefreshCache] 获取短信配置失败: %v", err)
				return fmt.Errorf("刷新短信配置缓存失败: %v", err)
			}
		} else {
			logs.Error("[RefreshCache] 获取短信配置失败: %v", err)
			return fmt.Errorf("刷新短信配置缓存失败: %v", err)
		}
	}

	// 更新缓存
	cacheBytes, err := json.Marshal(config)
	if err == nil {
		redis.Set(models.SmsConfigCacheKey, string(cacheBytes), models.SmsConfigCacheExpiration)
	}

	logs.Info("[RefreshCache] 短信配置缓存已刷新")
	return nil
}
