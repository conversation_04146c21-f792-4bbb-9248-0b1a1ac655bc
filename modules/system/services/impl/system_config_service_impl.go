/**
 * system_config_service_impl.go
 * 系统配置服务实现
 *
 * 本文件实现了SystemConfigService接口，提供系统配置的业务逻辑处理，
 * 包括配置的CRUD、缓存处理以及默认配置初始化。
 */

package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/repositories"
	"o_mall_backend/modules/system/repositories/impl"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/utils/redis"
)

// SystemConfigServiceImpl 系统配置服务实现
type SystemConfigServiceImpl struct {
	repo repositories.SystemConfigRepository
}

// NewSystemConfigServiceImpl 创建系统配置服务实例
func NewSystemConfigServiceImpl() services.SystemConfigService {
	return &SystemConfigServiceImpl{
		repo: impl.NewSystemConfigRepository(),
	}
}

// GetConfigByKey 根据配置键获取配置
func (s *SystemConfigServiceImpl) GetConfigByKey(ctx context.Context, key string) (*models.SystemConfig, error) {
	// 首先尝试从Redis缓存获取
	cacheKey := models.SystemConfigCacheKeyPrefix + key
	cacheData, err := redis.Get(cacheKey)
	if err == nil && cacheData != "" {
		// 缓存命中，解析JSON数据
		config := &models.SystemConfig{}
		if err := json.Unmarshal([]byte(cacheData), config); err == nil {
			return config, nil
		}
		// 解析失败则继续从数据库查询
		logs.Error("[GetConfigByKey] 解析缓存数据失败: %v", err)
	}

	// 从数据库查询
	config, err := s.repo.GetConfigByKey(ctx, key)
	if err != nil {
		return nil, err
	}

	// 如果找到配置，则更新缓存
	if config != nil {
		cacheBytes, err := json.Marshal(config)
		if err == nil {
			redis.Set(cacheKey, string(cacheBytes), models.SystemConfigCacheExpiration)
		}
	}

	return config, nil
}

// GetConfigValueByKey 获取配置值
func (s *SystemConfigServiceImpl) GetConfigValueByKey(ctx context.Context, key string) (string, error) {
	// 首先尝试从全局缓存获取
	allConfigs, err := s.GetAllConfigs(ctx)
	if err == nil {
		if value, ok := allConfigs[key]; ok {
			return value, nil
		}
	}

	// 没有在全局缓存中找到，尝试单独获取
	config, err := s.GetConfigByKey(ctx, key)
	if err != nil {
		return "", err
	}
	if config == nil {
		return "", fmt.Errorf("配置键 %s 不存在", key)
	}

	return config.ConfigValue, nil
}

// GetConfigsByKeys 批量获取系统配置
func (s *SystemConfigServiceImpl) GetConfigsByKeys(ctx context.Context, keys []string) (map[string]string, error) {
	if len(keys) == 0 {
		return make(map[string]string), nil
	}

	// 首先尝试从全局缓存获取
	allConfigs, err := s.GetAllConfigs(ctx)
	if err == nil {
		result := make(map[string]string)
		for _, key := range keys {
			if value, ok := allConfigs[key]; ok {
				result[key] = value
			}
		}
		// 如果所有请求的键都在全局缓存中找到，直接返回结果
		if len(result) == len(keys) {
			return result, nil
		}
	}

	// 从数据库批量查询
	configs, err := s.repo.GetConfigsByKeys(ctx, keys)
	if err != nil {
		return nil, err
	}

	// 转换为键值对格式
	result := make(map[string]string)
	for _, config := range configs {
		result[config.ConfigKey] = config.ConfigValue
	}

	return result, nil
}

// GetAllConfigs 获取所有系统配置
func (s *SystemConfigServiceImpl) GetAllConfigs(ctx context.Context) (map[string]string, error) {
	// 首先尝试从缓存获取
	cacheData, err := redis.Get(models.SystemConfigAllCacheKey)
	if err == nil && cacheData != "" {
		// 缓存命中，解析JSON数据
		var result map[string]string
		if err := json.Unmarshal([]byte(cacheData), &result); err == nil {
			return result, nil
		}
		// 解析失败则继续从数据库查询
		logs.Error("[GetAllConfigs] 解析缓存数据失败: %v", err)
	}

	// 从数据库查询所有配置
	configs, err := s.repo.GetAllConfigs(ctx)
	if err != nil {
		return nil, err
	}

	// 构建键值对并缓存
	result := make(map[string]string)
	for _, config := range configs {
		result[config.ConfigKey] = config.ConfigValue
	}

	// 将结果缓存到Redis
	cacheBytes, err := json.Marshal(result)
	if err == nil {
		// 保存所有配置缓存
		_ = redis.Set(models.SystemConfigAllCacheKey, string(cacheBytes), models.SystemConfigCacheExpiration)

		// 保存缓存元数据
		// 提取所有配置键名
		keys := make([]string, 0, len(result))
		for k := range result {
			keys = append(keys, k)
		}
		
		cacheInfo := map[string]interface{}{
			"keys":         keys,
			"last_refresh": time.Now().Unix(),
		}
		cacheInfoBytes, _ := json.Marshal(cacheInfo)
		_ = redis.Set(models.SystemConfigAllCacheKey+":info", string(cacheInfoBytes), models.SystemConfigCacheExpiration)
	}

	return result, nil
}

// GetAllConfigsWithDetails 获取所有系统配置的详细信息
func (s *SystemConfigServiceImpl) GetAllConfigsWithDetails(ctx context.Context, keyword string, status, isSystem int8, category string) ([]*models.SystemConfig, error) {
	// 使用仓库层的SearchConfigsWithDetails方法搜索配置
	configs, err := s.repo.SearchConfigsWithDetails(ctx, keyword, status, isSystem, category)
	if err != nil {
		logs.Error("[GetAllConfigsWithDetails] 获取系统配置详细信息失败: %v", err)
		return nil, fmt.Errorf("获取系统配置详细信息失败: %v", err)
	}
	
	return configs, nil
}

// CreateConfig 创建系统配置
func (s *SystemConfigServiceImpl) CreateConfig(ctx context.Context, config *models.SystemConfig) (int64, error) {
	// 设置默认值
	if config.Status == 0 {
		config.Status = 1 // 默认启用
	}
	if config.Version == 0 {
		config.Version = 1 // 初始版本号
	}

	// 创建配置
	id, err := s.repo.CreateConfig(ctx, config)
	if err != nil {
		return 0, err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return id, nil
}

// UpdateConfig 更新系统配置
func (s *SystemConfigServiceImpl) UpdateConfig(ctx context.Context, config *models.SystemConfig) error {
	// 更新配置
	err := s.repo.UpdateConfig(ctx, config)
	if err != nil {
		return err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// UpdateConfigValue 更新配置值
func (s *SystemConfigServiceImpl) UpdateConfigValue(ctx context.Context, key, value string) error {
	// 更新配置值
	err := s.repo.UpdateConfigValueByKey(ctx, key, value)
	if err != nil {
		return err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// DeleteConfig 删除系统配置
func (s *SystemConfigServiceImpl) DeleteConfig(ctx context.Context, id int64) error {
	// 删除配置
	err := s.repo.DeleteConfig(ctx, id)
	if err != nil {
		return err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// RefreshCache 刷新缓存
func (s *SystemConfigServiceImpl) RefreshCache(ctx context.Context) error {
	// 删除所有配置相关的缓存
	// 先删除全局配置缓存
	_, err := redis.Del(models.SystemConfigAllCacheKey)
	if err != nil {
		logs.Error("[RefreshCache] 删除全局配置缓存失败: %v", err)
	}

	// 从数据库重新获取所有配置
	configs, err := s.repo.GetAllConfigs(ctx)
	if err != nil {
		logs.Error("[RefreshCache] 获取所有配置失败: %v", err)
		return err
	}

	// 更新全局配置缓存
	result := make(map[string]string)
	for _, config := range configs {
		// 更新单个配置缓存
		cacheKey := models.SystemConfigCacheKeyPrefix + config.ConfigKey
		configBytes, err := json.Marshal(config)
		if err == nil {
			redis.Set(cacheKey, string(configBytes), models.SystemConfigCacheExpiration)
		}

		// 添加到全局配置映射
		result[config.ConfigKey] = config.ConfigValue
	}

	// 更新全局配置缓存
	allConfigBytes, err := json.Marshal(result)
	if err == nil {
		redis.Set(models.SystemConfigAllCacheKey, string(allConfigBytes), models.SystemConfigCacheExpiration)
	}

	logs.Info("[RefreshCache] 系统配置缓存已刷新")
	return nil
}

// NeedInitDefaultConfig 判断是否需要初始化默认配置
func (s *SystemConfigServiceImpl) NeedInitDefaultConfig() (bool, error) {
	// 判断配置表是否为空
	isEmpty, err := s.repo.IsConfigEmpty(context.Background())
	if err != nil {
		logs.Error("[NeedInitDefaultConfig] 判断配置表是否为空失败: %v", err)
		return false, err
	}

	// 配置表为空，需要初始化默认配置
	if isEmpty {
		logs.Info("[NeedInitDefaultConfig] 系统配置表为空，需要初始化默认配置")
		return true, nil
	}

	logs.Info("[NeedInitDefaultConfig] 系统配置表已有数据，无需初始化默认配置")
	return false, nil
}

// InitDefaultSystemConfig 初始化默认系统配置
func (s *SystemConfigServiceImpl) InitDefaultSystemConfig() error {
	logs.Info("[InitDefaultSystemConfig] 开始初始化默认系统配置...")

	// 构建默认系统配置列表
	defaultConfigs := []*models.SystemConfig{
		{
			ConfigKey:   models.ConfigKeySiteName,
			ConfigValue: "O_Mall多商家电商平台",
			ConfigType:  "text",
			Description: "网站名称",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeySiteVersion,
			ConfigValue: "v1.0.0",
			ConfigType:  "text",
			Description: "网站版本号",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyApiVersion,
			ConfigValue: "v1",
			ConfigType:  "text",
			Description: "API版本号",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyConfigVersion,
			ConfigValue: "20230101001",
			ConfigType:  "text",
			Description: "配置版本号，用于前端缓存控制",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyLogo,
			ConfigValue: "/static/images/logo.png",
			ConfigType:  "text",
			Description: "网站Logo",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyFavicon,
			ConfigValue: "/static/images/favicon.ico",
			ConfigType:  "text",
			Description: "网站图标",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyCopyright,
			ConfigValue: "© 2023 O_Mall多商家电商平台 版权所有",
			ConfigType:  "text",
			Description: "版权信息",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyContactEmail,
			ConfigValue: "<EMAIL>",
			ConfigType:  "text",
			Description: "联系邮箱",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyContactPhone,
			ConfigValue: "************",
			ConfigType:  "text",
			Description: "联系电话",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyContactAddress,
			ConfigValue: "北京市朝阳区科技园",
			ConfigType:  "text",
			Description: "联系地址",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyMaintenanceMode,
			ConfigValue: "0",
			ConfigType:  "boolean",
			Description: "维护模式：1=开启，0=关闭",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   models.ConfigKeyMaintenanceMessage,
			ConfigValue: "系统正在维护中，请稍后再试...",
			ConfigType:  "text",
			Description: "维护模式提示信息",
			Version:     1,
			Status:      1,
			IsSystem:    1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// 批量创建配置
	ctx := context.Background()
	successCount, err := s.repo.BatchCreateConfigs(ctx, defaultConfigs)
	if err != nil {
		logs.Error("[InitDefaultSystemConfig] 初始化默认系统配置失败: %v", err)
		return err
	}

	logs.Info("[InitDefaultSystemConfig] 成功初始化 %d 条默认系统配置", successCount)

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// InitDefaultUploadConfig 初始化默认上传配置
func (s *SystemConfigServiceImpl) InitDefaultUploadConfig() error {
	// 创建新的上传配置服务
	uploadConfigService := NewUploadConfigServiceImpl()

	// 判断是否需要初始化
	uploadConfig, err := uploadConfigService.GetUploadConfig(context.Background())
	if err != nil && err.Error() != "上传配置不存在" {
		logs.Error("[InitDefaultUploadConfig] 检查是否需要初始化上传配置失败: %v", err)
		return err
	}

	// 已有配置，不需要初始化
	if uploadConfig != nil && uploadConfig.Id > 0 {
		logs.Info("[InitDefaultUploadConfig] 上传配置已存在，无需初始化")
		return nil
	}

	// 创建默认上传配置
	logs.Info("[InitDefaultUploadConfig] 开始初始化默认上传配置...")
	// 创建默认配置
	config := &models.UploadConfig{
		// 默认使用本地存储，但也可以通过环境变量或配置文件来指定默认存储类型
		// 可选值：models.StorageModeLocal, models.StorageModeOSS, models.StorageModeCOS, models.StorageModeS3, models.StorageModeQiniu
		StorageMode:       models.StorageModeLocal,
		MaxSize:           10485760, // 10MB
		AllowedExtensions: "jpg,jpeg,png,gif,doc,docx,xls,xlsx,ppt,pptx,pdf,zip,rar,txt",
		EnableCdn:         0,
		Status:            1,
		Remark:            "系统默认上传配置",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
	
	// 根据不同的存储模式设置对应的默认配置
	switch config.StorageMode {
	case models.StorageModeLocal:
		// 设置默认本地存储配置
		localConfig := &models.LocalConfig{
			LocalPath: "./uploads",
		}
		err = config.SetLocalConfig(localConfig)
	
	case models.StorageModeQiniu:
		// 设置默认七牛云存储配置
		qiniuConfig := &models.QiniuConfig{
			AccessKey: "your_access_key",
			SecretKey: "your_secret_key",
			Bucket:    "your_bucket_name",
			Domain:    "your_domain", // 例如：http://cdn.example.com
			Zone:      "z0",         // 默认使用华东区域
			UseHTTPS:  true,
		}
		err = config.SetQiniuConfig(qiniuConfig)
	
	case models.StorageModeOSS:
		// 设置默认阿里云OSS配置
		ossConfig := &models.OssConfig{
			Endpoint:     "oss-cn-hangzhou.aliyuncs.com",
			Bucket:       "your_bucket_name",
			AccessKey:    "your_access_key",
			AccessSecret: "your_access_secret",
			Domain:       "your_custom_domain", // 可选
		}
		err = config.SetOssConfig(ossConfig)
	
	case models.StorageModeCOS:
		// 设置默认腾讯云COS配置
		cosConfig := &models.CosConfig{
			Region:    "ap-guangzhou", // 广州区域
			Bucket:    "your_bucket_name",
			SecretId:  "your_secret_id",
			SecretKey: "your_secret_key",
			Domain:    "your_custom_domain", // 可选
		}
		err = config.SetCosConfig(cosConfig)
	
	case models.StorageModeS3:
		// 设置默认AWS S3配置
		s3Config := &models.S3Config{
			Region:    "us-east-1",
			Bucket:    "your_bucket_name",
			AccessKey: "your_access_key",
			SecretKey: "your_secret_key",
			Domain:    "your_custom_domain", // 可选
		}
		err = config.SetS3Config(s3Config)
	}

	if err != nil {
		logs.Error("[InitDefaultUploadConfig] 设置存储配置失败: %v", err)
		return err
	}

	// 保存配置
	err = uploadConfigService.SaveUploadConfig(context.Background(), config)
	if err != nil {
		logs.Error("[InitDefaultUploadConfig] 初始化默认上传配置失败: %v", err)
		return err
	}

	logs.Info("[InitDefaultUploadConfig] 默认上传配置初始化成功，使用存储模式: %s", config.StorageMode)
	return nil
}

// InitDefaultSmsConfig 初始化默认短信配置
func (s *SystemConfigServiceImpl) InitDefaultSmsConfig() error {
	// 创建新的短信配置服务
	smsConfigService := NewSmsConfigServiceImpl()

	// 判断是否需要初始化
	smsConfig, err := smsConfigService.GetSmsConfig(context.Background())
	if err != nil && err.Error() != "短信配置不存在" {
		logs.Error("[InitDefaultSmsConfig] 检查是否需要初始化短信配置失败: %v", err)
		return err
	}

	// 已有配置，不需要初始化
	if smsConfig != nil && smsConfig.Id > 0 {
		logs.Info("[InitDefaultSmsConfig] 短信配置已存在，无需初始化")
		return nil
	}

	// 创建默认短信配置
	logs.Info("[InitDefaultSmsConfig] 开始初始化默认短信配置...")
	defaultConfig := &models.SmsConfig{
		Provider:             "aliyun",
		AccessKey:            "your_access_key",
		AccessSecret:         "your_access_secret",
		SignName:             "O_Mall电商平台",
		TemplateCodeRegister: "SMS_100000001",
		TemplateCodeLogin:    "SMS_100000002",
		TemplateCodeResetPwd: "SMS_100000003",
		TemplateCodeNotice:   "SMS_100000004",
		DailyLimit:           1000,
		Status:               0, // 默认禁用，需要配置正确的参数才能启用
		Remark:               "系统默认短信配置，请配置正确的参数后启用",
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	// 保存配置
	err = smsConfigService.SaveSmsConfig(context.Background(), defaultConfig)
	if err != nil {
		logs.Error("[InitDefaultSmsConfig] 初始化默认短信配置失败: %v", err)
		return err
	}

	logs.Info("[InitDefaultSmsConfig] 默认短信配置初始化成功")
	return nil
}
