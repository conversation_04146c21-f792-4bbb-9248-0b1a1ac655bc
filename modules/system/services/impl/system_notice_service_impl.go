/**
 * system_notice_service_impl.go
 * 系统公告服务实现
 *
 * 本文件实现了SystemNoticeService接口，提供系统公告的业务逻辑处理，
 * 包括公告的CRUD和缓存处理。
 */

package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/repositories"
	"o_mall_backend/modules/system/repositories/impl"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/utils/redis"
)

// SystemNoticeServiceImpl 系统公告服务实现
type SystemNoticeServiceImpl struct {
	repo repositories.SystemNoticeRepository
}

// NewSystemNoticeServiceImpl 创建系统公告服务实例
func NewSystemNoticeServiceImpl() services.SystemNoticeService {
	return &SystemNoticeServiceImpl{
		repo: impl.NewSystemNoticeRepository(),
	}
}

// GetNotice 获取单个系统公告
func (s *SystemNoticeServiceImpl) GetNotice(ctx context.Context, id int64) (*models.SystemNotice, error) {
	// 从缓存获取
	cacheKey := fmt.Sprintf("%s%d", models.SystemNoticeCacheKeyPrefix, id)
	cacheData, err := redis.Get(cacheKey)
	if err == nil && cacheData != "" {
		// 缓存命中
		notice := &models.SystemNotice{}
		if err := json.Unmarshal([]byte(cacheData), notice); err == nil {
			return notice, nil
		}
		logs.Error("[GetNotice] 解析缓存数据失败: %v", err)
	}

	// 从数据库获取
	notice, err := s.repo.GetNotice(ctx, id)
	if err != nil {
		return nil, err
	}

	if notice == nil {
		return nil, fmt.Errorf("公告不存在")
	}

	// 更新缓存
	cacheBytes, err := json.Marshal(notice)
	if err == nil {
		redis.Set(cacheKey, string(cacheBytes), models.SystemNoticeCacheExpiration)
	}

	return notice, nil
}

// GetActiveNotices 获取当前有效的公告
func (s *SystemNoticeServiceImpl) GetActiveNotices(ctx context.Context, target int8) ([]*models.SystemNotice, error) {
	// 构建缓存键
	cacheKey := fmt.Sprintf("%sactive:%d", models.SystemNoticeCacheKeyPrefix, target)

	// 尝试从缓存获取
	cacheData, err := redis.Get(cacheKey)
	if err == nil && cacheData != "" {
		// 缓存命中
		var notices []*models.SystemNotice
		if err := json.Unmarshal([]byte(cacheData), &notices); err == nil {
			return notices, nil
		}
		logs.Error("[GetActiveNotices] 解析缓存数据失败: %v", err)
	}

	// 从数据库获取
	notices, err := s.repo.GetActiveNotices(ctx, target)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	cacheBytes, err := json.Marshal(notices)
	if err == nil {
		// 有效公告缓存时间较短，防止用户看不到最新公告
		redis.Set(cacheKey, string(cacheBytes), 5*time.Minute)
	}

	return notices, nil
}

// GetNoticesByPage 分页获取系统公告
func (s *SystemNoticeServiceImpl) GetNoticesByPage(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*models.SystemNotice, int64, error) {
	// 分页查询不使用缓存，直接从数据库获取
	return s.repo.GetNoticesByPage(ctx, page, pageSize, filters)
}

// CreateNotice 创建系统公告
func (s *SystemNoticeServiceImpl) CreateNotice(ctx context.Context, notice *models.SystemNotice) (int64, error) {
	// 创建公告
	id, err := s.repo.CreateNotice(ctx, notice)
	if err != nil {
		return 0, err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return id, nil
}

// UpdateNotice 更新系统公告
func (s *SystemNoticeServiceImpl) UpdateNotice(ctx context.Context, notice *models.SystemNotice) error {
	// 更新公告
	err := s.repo.UpdateNotice(ctx, notice)
	if err != nil {
		return err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// DeleteNotice 删除系统公告
func (s *SystemNoticeServiceImpl) DeleteNotice(ctx context.Context, id int64) error {
	// 删除公告
	err := s.repo.DeleteNotice(ctx, id)
	if err != nil {
		return err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	// 删除单个公告的缓存
	cacheKey := fmt.Sprintf("%s%d", models.SystemNoticeCacheKeyPrefix, id)
	_, err = redis.Del(cacheKey)
	if err != nil {
		logs.Error("[DeleteNotice] 删除公告缓存失败: %v, id=%d", err, id)
	}

	return nil
}

// UpdateNoticeStatus 更新公告状态
func (s *SystemNoticeServiceImpl) UpdateNoticeStatus(ctx context.Context, id int64, status int8) error {
	// 更新状态
	err := s.repo.UpdateNoticeStatus(ctx, id, status)
	if err != nil {
		return err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	// 更新单个公告的缓存
	notice, err := s.repo.GetNotice(ctx, id)
	if err == nil && notice != nil {
		cacheKey := fmt.Sprintf("%s%d", models.SystemNoticeCacheKeyPrefix, id)
		cacheBytes, err := json.Marshal(notice)
		if err == nil {
			redis.Set(cacheKey, string(cacheBytes), models.SystemNoticeCacheExpiration)
		}
	}

	return nil
}

// UpdateNoticeTopStatus 更新公告置顶状态
func (s *SystemNoticeServiceImpl) UpdateNoticeTopStatus(ctx context.Context, id int64, isTop int8) error {
	// 更新置顶状态
	err := s.repo.UpdateNoticeTopStatus(ctx, id, isTop)
	if err != nil {
		return err
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	// 更新单个公告的缓存
	notice, err := s.repo.GetNotice(ctx, id)
	if err == nil && notice != nil {
		cacheKey := fmt.Sprintf("%s%d", models.SystemNoticeCacheKeyPrefix, id)
		cacheBytes, err := json.Marshal(notice)
		if err == nil {
			redis.Set(cacheKey, string(cacheBytes), models.SystemNoticeCacheExpiration)
		}
	}

	return nil
}

// RefreshCache 刷新缓存
func (s *SystemNoticeServiceImpl) RefreshCache(ctx context.Context) error {
	// 删除列表缓存
	_, err := redis.Del(models.SystemNoticeListCacheKey)
	if err != nil {
		logs.Error("[RefreshCache] 删除公告列表缓存失败: %v", err)
	}

	// 删除活动公告缓存
	// 循环删除各个目标用户的活动公告缓存
	for target := 0; target <= 4; target++ {
		cacheKey := fmt.Sprintf("%sactive:%d", models.SystemNoticeCacheKeyPrefix, target)
		_, err := redis.Del(cacheKey)
		if err != nil {
			logs.Error("[RefreshCache] 删除活动公告缓存失败: %v, target=%d", err, target)
		}
	}

	logs.Info("[RefreshCache] 系统公告缓存已刷新")
	return nil
}
