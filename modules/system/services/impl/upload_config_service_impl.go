/**
 * upload_config_service_impl.go
 * 上传配置服务实现
 *
 * 本文件实现了UploadConfigService接口，提供上传配置的业务逻辑处理，
 * 包括配置的获取、保存和缓存处理。
 */

package impl

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/utils/redis"
)

// UploadConfigServiceImpl 上传配置服务实现
type UploadConfigServiceImpl struct{}

// NewUploadConfigServiceImpl 创建上传配置服务实例
func NewUploadConfigServiceImpl() services.UploadConfigService {
	return &UploadConfigServiceImpl{}
}

// GetUploadConfig 获取上传配置
func (s *UploadConfigServiceImpl) GetUploadConfig(ctx context.Context) (*models.UploadConfig, error) {
	// 首先尝试从Redis缓存获取
	cacheData, err := redis.Get(models.UploadConfigCacheKey)
	if err == nil && cacheData != "" {
		// 缓存命中，解析JSON数据
		config := &models.UploadConfig{}
		if err := json.Unmarshal([]byte(cacheData), config); err == nil {
			return config, nil
		}
		// 解析失败则继续从数据库查询
		logs.Error("[GetUploadConfig] 解析缓存数据失败: %v", err)
	}

	// 从数据库查询
	o := orm.NewOrm()
	var config models.UploadConfig

	// 上传配置通常只有一条记录，获取状态为启用的第一条
	err = o.QueryTable(new(models.UploadConfig)).Filter("status", 1).One(&config)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, fmt.Errorf("上传配置不存在")
		}
		logs.Error("[GetUploadConfig] 获取上传配置失败: %v", err)
		return nil, fmt.Errorf("获取上传配置失败: %v", err)
	}

	// 更新缓存
	cacheBytes, err := json.Marshal(config)
	if err == nil {
		redis.Set(models.UploadConfigCacheKey, string(cacheBytes), models.UploadConfigCacheExpiration)
	}

	return &config, nil
}

// SaveUploadConfig 创建/更新上传配置
func (s *UploadConfigServiceImpl) SaveUploadConfig(ctx context.Context, config *models.UploadConfig) error {
	o := orm.NewOrm()

	if config.Id > 0 {
		// 更新现有配置
		_, err := o.Update(config)
		if err != nil {
			logs.Error("[SaveUploadConfig] 更新上传配置失败: %v", err)
			return fmt.Errorf("更新上传配置失败: %v", err)
		}
	} else {
		// 创建新配置
		_, err := o.Insert(config)
		if err != nil {
			logs.Error("[SaveUploadConfig] 创建上传配置失败: %v", err)
			return fmt.Errorf("创建上传配置失败: %v", err)
		}
	}

	// 刷新缓存
	s.RefreshCache(ctx)

	return nil
}

// RefreshCache 刷新缓存
func (s *UploadConfigServiceImpl) RefreshCache(ctx context.Context) error {
	// 删除缓存
	_, err := redis.Del(models.UploadConfigCacheKey)
	if err != nil {
		logs.Error("[RefreshCache] 删除上传配置缓存失败: %v", err)
	}

	// 从数据库重新获取配置并缓存
	o := orm.NewOrm()
	var config models.UploadConfig

	err = o.QueryTable(new(models.UploadConfig)).Filter("status", 1).One(&config)
	if err != nil {
		if err == orm.ErrNoRows {
			// 没有配置，不需要更新缓存
			return nil
		}
		logs.Error("[RefreshCache] 获取上传配置失败: %v", err)
		return fmt.Errorf("刷新上传配置缓存失败: %v", err)
	}

	// 更新缓存
	cacheBytes, err := json.Marshal(config)
	if err == nil {
		redis.Set(models.UploadConfigCacheKey, string(cacheBytes), models.UploadConfigCacheExpiration)
	}

	logs.Info("[RefreshCache] 上传配置缓存已刷新")
	return nil
}
