/**
 * interface.go
 * 系统模块服务层接口定义
 *
 * 本文件定义了系统模块所有服务层的接口，包括系统配置、系统公告、短信配置和上传配置等。
 */

package services

import (
	"context"

	"o_mall_backend/modules/system/models"
)

// SystemConfigService 系统配置服务接口
type SystemConfigService interface {
	// 获取单个系统配置
	GetConfigByKey(ctx context.Context, key string) (*models.SystemConfig, error)
	// 获取配置值
	GetConfigValueByKey(ctx context.Context, key string) (string, error)
	// 批量获取系统配置
	GetConfigsByKeys(ctx context.Context, keys []string) (map[string]string, error)
	// 获取所有系统配置
	GetAllConfigs(ctx context.Context) (map[string]string, error)
	// 获取所有系统配置详细信息
	GetAllConfigsWithDetails(ctx context.Context, keyword string, status, isSystem int8, category string) ([]*models.SystemConfig, error)
	// 创建系统配置
	CreateConfig(ctx context.Context, config *models.SystemConfig) (int64, error)
	// 更新系统配置
	UpdateConfig(ctx context.Context, config *models.SystemConfig) error
	// 更新配置值
	UpdateConfigValue(ctx context.Context, key, value string) error
	// 删除系统配置
	DeleteConfig(ctx context.Context, id int64) error
	// 刷新缓存
	RefreshCache(ctx context.Context) error
	// 判断是否需要初始化默认配置
	NeedInitDefaultConfig() (bool, error)
	// 初始化默认系统配置
	InitDefaultSystemConfig() error
	// 初始化默认上传配置
	InitDefaultUploadConfig() error
	// 初始化默认短信配置
	InitDefaultSmsConfig() error
}

// SystemNoticeService 系统公告服务接口
type SystemNoticeService interface {
	// 获取单个系统公告
	GetNotice(ctx context.Context, id int64) (*models.SystemNotice, error)
	// 获取当前有效的公告
	GetActiveNotices(ctx context.Context, target int8) ([]*models.SystemNotice, error)
	// 分页获取系统公告
	GetNoticesByPage(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*models.SystemNotice, int64, error)
	// 创建系统公告
	CreateNotice(ctx context.Context, notice *models.SystemNotice) (int64, error)
	// 更新系统公告
	UpdateNotice(ctx context.Context, notice *models.SystemNotice) error
	// 删除系统公告
	DeleteNotice(ctx context.Context, id int64) error
	// 更新公告状态
	UpdateNoticeStatus(ctx context.Context, id int64, status int8) error
	// 更新公告置顶状态
	UpdateNoticeTopStatus(ctx context.Context, id int64, isTop int8) error
	// 刷新缓存
	RefreshCache(ctx context.Context) error
}

// SmsConfigService 短信配置服务接口
type SmsConfigService interface {
	// 获取短信配置
	GetSmsConfig(ctx context.Context) (*models.SmsConfig, error)
	// 创建/更新短信配置
	SaveSmsConfig(ctx context.Context, config *models.SmsConfig) error
	// 刷新缓存
	RefreshCache(ctx context.Context) error
}

// UploadConfigService 上传配置服务接口
type UploadConfigService interface {
	// 获取上传配置
	GetUploadConfig(ctx context.Context) (*models.UploadConfig, error)
	// 创建/更新上传配置
	SaveUploadConfig(ctx context.Context, config *models.UploadConfig) error
	// 刷新缓存
	RefreshCache(ctx context.Context) error
}
