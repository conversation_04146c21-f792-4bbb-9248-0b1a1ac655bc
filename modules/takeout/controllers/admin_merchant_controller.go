/**
 * 管理员商家控制器
 *
 * 本文件实现了管理员端商家相关API，包括商家数据统计和商家商品列表查询功能。
 * 用于管理后台查看和管理商家信息。
 */

package controllers

import (
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// AdminMerchantController 管理员商家控制器
type AdminMerchantController struct {
	web.Controller
	foodService  services.TakeoutFoodService
	orderService services.TakeoutOrderService
}

// Prepare 初始化方法
func (c *AdminMerchantController) Prepare() {
	// 初始化服务
	c.foodService = services.NewTakeoutFoodService()
	c.orderService = services.NewTakeoutOrderService()
}

// CheckXSRFCookie 实现控制器接口
func (c *AdminMerchantController) CheckXSRFCookie() bool {
	// 管理员API不需要XSRF令牌
	return false
}

// GetMerchantStatistics 获取商家统计信息
// @router /admin/takeout/merchant/:id/statistics [get]
func (c *AdminMerchantController) GetMerchantStatistics() {
	// 获取商家ID
	idStr := c.Ctx.Input.Param(":id")
	merchantID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取商品统计
	totalCount, onSaleCount, pendingCount, soldOutCount, err := c.foodService.GetFoodStatisticsByMerchantID(merchantID)
	if err != nil {
		logs.Error("获取商家商品统计失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取订单统计
	totalOrderCount, completedCount, processingCount, cancelledCount, err := c.orderService.GetOrderStatisticsByMerchantID(merchantID)
	if err != nil {
		logs.Error("获取商家订单统计失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 构造响应数据
	statistics := dto.MerchantStatisticsDTO{
		FoodStatistics: dto.FoodStatisticsDTO{
			TotalCount:   totalCount,
			OnSaleCount:  onSaleCount,
			PendingCount: pendingCount,
			SoldOutCount: soldOutCount,
		},
		OrderStatistics: dto.OrderStatisticsDTO{
			TotalCount:      totalOrderCount,
			CompletedCount:  completedCount,
			ProcessingCount: processingCount,
			CancelledCount:  cancelledCount,
		},
	}

	// 返回统计数据
	result.OK(c.Ctx, statistics)
}

// GetMerchantFoods 获取商家商品列表
// @router /admin/takeout/merchant/:id/foods [get]
func (c *AdminMerchantController) GetMerchantFoods() {
	// 获取商家ID
	idStr := c.Ctx.Input.Param(":id")
	merchantID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取查询参数
	categoryID, _ := c.GetInt64("category_id", 0)
	keyword := c.GetString("keyword", "")
	status, _ := c.GetInt("status", -1)
	auditStatus, _ := c.GetInt("audit_status", -1)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 查询商家商品列表
	foods, total, err := c.foodService.GetFoodsPageForAdmin(merchantID, categoryID, keyword, status, auditStatus, page, pageSize)
	if err != nil {
		logs.Error("获取商家商品列表失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回商品列表
	result.OKWithPagination(c.Ctx, foods, total, page, pageSize)
}
