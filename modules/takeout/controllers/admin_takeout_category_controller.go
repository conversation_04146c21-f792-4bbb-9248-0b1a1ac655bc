/**
 * 管理员外卖分类控制器
 *
 * 本文件实现了管理员外卖分类相关API，包括分类列表查询、创建分类、
 * 更新分类和删除分类功能。用于管理后台管理外卖分类。
 */

package controllers

import (
	"net/http"
	"strconv"

	//"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
)

// AdminTakeoutCategoryController 管理员外卖分类控制器
type AdminTakeoutCategoryController struct {
	web.Controller
	categoryService services.TakeoutCategoryService
}

// Prepare 初始化方法
func (c *AdminTakeoutCategoryController) Prepare() {
	// 初始化分类服务
	c.categoryService = services.NewTakeoutCategoryService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *AdminTakeoutCategoryController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 查询分类列表
// @Title 获取外卖分类列表
// @Description 分页获取外卖分类列表，支持按关键词和商家ID筛选
// @Tags 外卖分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param merchant_id query int false "商家ID"
// @Param keyword query string false "分类关键词"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} result.Response{data=[]dto.CategoryResponse} "成功"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/categories [get]
func (c *AdminTakeoutCategoryController) List() {
	// 获取查询参数
	merchantID, _ := c.GetInt64("merchant_id", 0)
	keyword := c.GetString("keyword", "")
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询分类列表
	categories, total, err := c.categoryService.GetCategoriesPage(merchantID, keyword, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回分类列表
	result.OKWithPagination(c.Ctx, categories, total, page, pageSize)
}

// Get 获取分类详情
// @Title 获取外卖分类详情
// @Description 根据分类ID获取外卖分类的详细信息
// @Tags 外卖分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "分类ID"
// @Success 200 {object} result.Response{data=dto.CategoryResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "分类不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/categories/{id} [get]
func (c *AdminTakeoutCategoryController) Get() {
	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询分类详情
	category, err := c.categoryService.GetCategoryByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回分类详情
	result.OK(c.Ctx, category)
}

// Create 创建分类
// @Title 创建外卖分类
// @Description 创建一个新的外卖商品分类
// @Tags 外卖分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param body body dto.CreateTakeoutCategoryRequest true "分类创建参数"
// @Success 200 {object} result.Response{data=map[string]int64} "成功返回创建的分类ID"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/categories [post]
func (c *AdminTakeoutCategoryController) Create() {
	// 使用ParseRequest解析请求参数
	req := dto.CreateTakeoutCategoryRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 校验必填参数
	if req.Name == "" {
		result.HandleError(c.Ctx, result.NewError(400, "分类名称不能为空"))
		return
	}

	// 创建分类
	id, err := c.categoryService.CreateCategory(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// Update 更新分类
// @Title 更新外卖分类
// @Description 根据分类ID更新外卖分类信息
// @Tags 外卖分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "分类ID"
// @Param body body dto.UpdateCategoryRequest true "分类更新参数"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "分类不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/categories/{id} [put]
func (c *AdminTakeoutCategoryController) Update() {
	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用ParseRequest解析请求参数
	req := dto.UpdateTakeoutCategoryRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id

	// 校验必填参数
	if req.Name == "" {
		result.HandleError(c.Ctx, result.NewError(http.StatusBadRequest, "分类名称不能为空"))
		return
	}

	// 更新分类
	if err := c.categoryService.UpdateCategory(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// Delete 删除分类
// @Title 删除外卖分类
// @Description 根据分类ID删除外卖分类
// @Tags 外卖分类管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "分类ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "分类不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/categories/{id} [delete]
func (c *AdminTakeoutCategoryController) Delete() {
	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 删除分类
	if err := c.categoryService.DeleteCategory(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *AdminTakeoutCategoryController) CheckXSRFCookie() bool {
	// 管理员分类管理API不需要XSRF令牌
	return false
}
