/**
 * 管理员外卖套餐控制器
 *
 * 本文件实现了管理员外卖套餐相关API，包括套餐组件和套餐选项的管理功能。
 * 用于管理后台管理外卖套餐的组件和选项。
 */

package controllers

import (
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// AdminTakeoutComboController 管理员外卖套餐控制器
type AdminTakeoutComboController struct {
	web.Controller
	comboService services.TakeoutComboService
}

// Prepare 初始化方法
func (c *AdminTakeoutComboController) Prepare() {
	// 初始化套餐服务
	c.comboService = services.NewTakeoutComboService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *AdminTakeoutComboController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// ListItems 查询套餐组件列表
// @Title 获取外卖套餐组件列表
// @Description 获取指定食品的套餐组件列表
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "食品ID"
// @Success 200 {object} result.Response{data=[]dto.ComboItemResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/foods/{id}/combo-items [get]
func (c *AdminTakeoutComboController) ListItems() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询套餐组件列表
	items, err := c.comboService.ListComboItemsByFoodID(foodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	total := int64(len(items))

	// 返回套餐组件列表（带分页信息）
	result.OKWithPagination(c.Ctx, items, total, page, pageSize)
}

// GetItem 获取套餐组件详情
// @Title 获取外卖套餐组件详情
// @Description 根据ID获取套餐组件的详细信息
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组件ID"
// @Success 200 {object} result.Response{data=dto.ComboItemResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "组件不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-items/{id} [get]
func (c *AdminTakeoutComboController) GetItem() {
	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询套餐组件详情
	item, err := c.comboService.GetComboItemByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回套餐组件详情
	result.OK(c.Ctx, item)
}

// CreateItem 创建套餐组件
// @Title 创建外卖套餐组件
// @Description 为指定食品创建一个新的套餐组件
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "食品ID"
// @Param body body dto.CreateComboItemRequest true "套餐组件创建参数"
// @Success 200 {object} result.Response{data=map[string]int64} "成功返回创建的组件ID"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/foods/{id}/combo-items [post]
func (c *AdminTakeoutComboController) CreateItem() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	req := dto.CreateComboItemRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.FoodID = foodID

	// 创建套餐组件
	// 进行类型转换，将 CreateComboItemRequest 转换为 TakeoutComboItemRequest
	comboReq := dto.TakeoutComboItemRequest(req)
	id, err := c.comboService.CreateComboItem(&comboReq)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// UpdateItem 更新套餐组件
// @Title 更新外卖套餐组件
// @Description 根据ID更新套餐组件信息
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组件ID"
// @Param body body dto.UpdateComboItemRequest true "套餐组件更新参数"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "组件不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-items/{id} [put]
func (c *AdminTakeoutComboController) UpdateItem() {
	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	req := dto.UpdateComboItemRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id

	// 更新套餐组件
	if err := c.comboService.UpdateComboItem(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// DeleteItem 删除套餐组件
// @Title 删除外卖套餐组件
// @Description 根据ID删除套餐组件
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组件ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "组件不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-items/{id} [delete]
func (c *AdminTakeoutComboController) DeleteItem() {
	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 删除套餐组件
	if err := c.comboService.DeleteComboItem(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// ListOptions 查询套餐选项列表
// @Title 获取外卖套餐选项列表
// @Description 获取指定套餐组件的选项列表
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组件ID"
// @Success 200 {object} result.Response{data=[]dto.ComboOptionResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-items/{id}/options [get]
func (c *AdminTakeoutComboController) ListOptions() {
	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	itemID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询套餐选项列表
	options, err := c.comboService.ListComboOptionsByItemID(itemID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回套餐选项列表
	result.OK(c.Ctx, options)
}

// GetOption 获取套餐选项详情
// @Title 获取外卖套餐选项详情
// @Description 根据ID获取套餐选项的详细信息
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "选项ID"
// @Success 200 {object} result.Response{data=dto.ComboOptionResponse} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "选项不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-options/{id} [get]
func (c *AdminTakeoutComboController) GetOption() {
	// 获取套餐选项ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询套餐选项详情
	option, err := c.comboService.GetComboOptionByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回套餐选项详情
	result.OK(c.Ctx, option)
}

// CreateOption 创建套餐选项
// @Title 创建外卖套餐选项
// @Description 为指定套餐组件创建一个新的选项
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "组件ID"
// @Param body body dto.CreateComboOptionRequest true "套餐选项创建参数"
// @Success 200 {object} result.Response{data=map[string]int64} "成功返回创建的选项ID"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-items/{id}/options [post]
func (c *AdminTakeoutComboController) CreateOption() {
	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	itemID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	req := dto.CreateComboOptionRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ItemID = itemID

	// 创建套餐选项
	id, err := c.comboService.CreateComboOption(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// UpdateOption 更新套餐选项
// @Title 更新外卖套餐选项
// @Description 根据ID更新套餐选项信息
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "选项ID"
// @Param body body dto.UpdateComboOptionRequest true "套餐选项更新参数"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "选项不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-options/{id} [put]
func (c *AdminTakeoutComboController) UpdateOption() {
	// 获取套餐选项ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	req := dto.UpdateComboOptionRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id

	// 更新套餐选项
	if err := c.comboService.UpdateComboOption(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// DeleteOption 删除套餐选项
// @Title 删除外卖套餐选项
// @Description 根据ID删除套餐选项
// @Tags 外卖套餐管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "选项ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "选项不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /admin/api/takeout/combo-options/{id} [delete]
func (c *AdminTakeoutComboController) DeleteOption() {
	// 获取套餐选项ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 删除套餐选项
	if err := c.comboService.DeleteComboOption(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *AdminTakeoutComboController) CheckXSRFCookie() bool {
	// 管理员API不需要XSRF令牌
	return false
}
