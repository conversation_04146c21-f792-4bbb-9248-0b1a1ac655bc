/**
 * 管理员外卖食品控制器
 *
 * 本文件实现了管理员外卖食品相关API，包括食品列表查询、创建食品、
 * 更新食品、删除食品和更新食品状态功能。用于管理后台管理外卖食品。
 */

package controllers

import (
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// AdminTakeoutFoodController 管理员外卖食品控制器
type AdminTakeoutFoodController struct {
	web.Controller
	foodService services.TakeoutFoodService
}

// Prepare 初始化方法
func (c *AdminTakeoutFoodController) Prepare() {
	// 初始化食品服务
	c.foodService = services.NewTakeoutFoodService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *AdminTakeoutFoodController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 查询食品列表
// @router /admin/api/takeout/foods [get]
func (c *AdminTakeoutFoodController) List() {
	// 获取查询参数
	merchantID, _ := c.GetInt64("merchant_id", 0)
	categoryID, _ := c.GetInt64("category_id", 0)
	keyword := c.GetString("keyword", "")
	status, _ := c.GetInt("status", -1)
	auditStatus, _ := c.GetInt("audit_status", -1)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 查询食品列表
	foods, total, err := c.foodService.GetFoodsPageForAdmin(merchantID, categoryID, keyword, status, auditStatus, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回食品列表
	result.OKWithPagination(c.Ctx, foods, total, page, pageSize)
}

// Get 获取食品详情
// @router /admin/api/takeout/foods/:id [get]
func (c *AdminTakeoutFoodController) Get() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodDetailForAdmin(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回食品详情
	result.OK(c.Ctx, food)
}

// Create 创建食品
// @router /admin/api/takeout/foods [post]
func (c *AdminTakeoutFoodController) Create() {
	// 解析请求参数
	req := dto.CreateFoodRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 创建食品
	id, err := c.foodService.CreateFoodForAdmin(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// Update 更新食品
// @router /admin/api/takeout/foods/:id [put]
func (c *AdminTakeoutFoodController) Update() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	req := dto.UpdateFoodRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id

	// 更新食品
	if err := c.foodService.UpdateFoodForAdmin(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// Delete 删除食品
// @router /admin/api/takeout/foods/:id [delete]
func (c *AdminTakeoutFoodController) Delete() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 删除食品
	if err := c.foodService.DeleteFoodForAdmin(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// UpdateStatus 更新食品状态
// @router /admin/api/takeout/foods/:id/status [put]
func (c *AdminTakeoutFoodController) UpdateStatus() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	req := dto.UpdateFoodStatusRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id

	// 更新食品状态
	if err := c.foodService.UpdateFoodStatusForAdmin(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// AuditFood 审核食品
// @router /admin/api/takeout/foods/:id/audit [put]
func (c *AdminTakeoutFoodController) AuditFood() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取审核人ID，从JWT中获取当前管理员ID
	adminID := c.Ctx.Input.GetData("user_id").(int64)

	// 解析请求参数
	req := dto.AuditFoodRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id
	req.AuditorID = adminID

	// 进行食品审核
	if err := c.foodService.AuditFoodForAdmin(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回审核结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *AdminTakeoutFoodController) CheckXSRFCookie() bool {
	// 管理员API不需要XSRF令牌
	return false
}
