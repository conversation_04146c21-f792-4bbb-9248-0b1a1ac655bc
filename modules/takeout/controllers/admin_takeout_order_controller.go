/**
 * 管理员外卖订单控制器
 * 
 * 本文件实现了管理员外卖订单相关API，包括订单列表查询、
 * 订单详情查询和订单更新功能。用于管理后台管理外卖订单。
 */

package controllers

import (
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// AdminTakeoutOrderController 管理员外卖订单控制器
type AdminTakeoutOrderController struct {
	web.Controller
	orderService services.TakeoutOrderService
}

// Prepare 初始化方法
func (c *AdminTakeoutOrderController) Prepare() {
	// 初始化订单服务
	c.orderService = services.NewTakeoutOrderService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *AdminTakeoutOrderController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 查询订单列表
// @router /admin/api/takeout/orders [get]
func (c *AdminTakeoutOrderController) List() {
	// 获取查询参数
	merchantID, _ := c.GetInt64("merchant_id", 0)
	orderNumber := c.GetString("order_number", "")
	status, _ := c.GetInt("status", -1)
	startTime := c.GetString("start_time", "")
	endTime := c.GetString("end_time", "")
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)
	
	// 查询订单列表
	orders, total, err := c.orderService.GetOrdersPageForAdmin(merchantID, orderNumber, status, startTime, endTime, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 返回订单列表
	result.OKWithPagination(c.Ctx, orders, total, page, pageSize)
}

// Get 获取订单详情
// @router /admin/api/takeout/orders/:id [get]
func (c *AdminTakeoutOrderController) Get() {
	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 查询订单详情
	order, err := c.orderService.GetOrderDetailForAdmin(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 返回订单详情
	result.OK(c.Ctx, order)
}

// Update 更新订单
// @router /admin/api/takeout/orders/:id [put]
func (c *AdminTakeoutOrderController) Update() {
	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 解析请求参数
	var req dto.UpdateOrderRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id
	
	// 更新订单
	if err := c.orderService.UpdateOrderForAdmin(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *AdminTakeoutOrderController) CheckXSRFCookie() bool {
	// 管理员API不需要XSRF令牌
	return false
}
