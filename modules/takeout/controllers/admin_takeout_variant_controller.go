/**
 * 管理员外卖规格控制器
 * 
 * 本文件实现了管理员外卖规格相关API，包括规格列表查询、创建规格、
 * 更新规格和删除规格功能。用于管理后台管理外卖商品的规格选项。
 */

package controllers

import (
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// AdminTakeoutVariantController 管理员外卖规格控制器
type AdminTakeoutVariantController struct {
	web.Controller
	variantService services.TakeoutVariantService
}

// Prepare 初始化方法
func (c *AdminTakeoutVariantController) Prepare() {
	// 初始化规格服务
	c.variantService = services.NewTakeoutVariantService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *AdminTakeoutVariantController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 查询规格列表
// @router /admin/api/takeout/foods/:id/variants [get]
func (c *AdminTakeoutVariantController) List() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	
	// 查询规格列表
	variants, err := c.variantService.GetVariantsByFoodID(foodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 计算总数
	total := int64(len(variants))
	
	// 返回规格列表
	result.OKWithPagination(c.Ctx, variants, total, page, pageSize)
}

// Get 获取规格详情
// @router /admin/api/takeout/variants/:id [get]
func (c *AdminTakeoutVariantController) Get() {
	// 获取规格ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 查询规格详情
	variant, err := c.variantService.GetVariantByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 返回规格详情
	result.OK(c.Ctx, variant)
}

// Create 创建规格
// @router /admin/api/takeout/foods/:id/variants [post]
func (c *AdminTakeoutVariantController) Create() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 解析请求参数
	var req dto.CreateVariantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.FoodID = foodID
	
	// 创建规格
	id, err := c.variantService.CreateVariant(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// Update 更新规格
// @router /admin/api/takeout/variants/:id [put]
func (c *AdminTakeoutVariantController) Update() {
	// 获取规格ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 解析请求参数
	var req dto.UpdateVariantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id
	
	// 更新规格
	if err := c.variantService.UpdateVariant(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// Delete 删除规格
// @router /admin/api/takeout/variants/:id [delete]
func (c *AdminTakeoutVariantController) Delete() {
	// 获取规格ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}
	
	// 删除规格
	if err := c.variantService.DeleteVariant(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	
	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *AdminTakeoutVariantController) CheckXSRFCookie() bool {
	// 管理员API不需要XSRF令牌
	return false
}
