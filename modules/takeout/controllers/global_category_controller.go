/**
 * 全局商品分类控制器
 *
 * 本文件实现了全局商品分类的控制器层，处理与全局分类相关的HTTP请求。
 * 提供了分类管理的各种接口，包括创建、更新、删除、查询等功能。
 */

package controllers

import (
	"encoding/json"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/services"
)

// GlobalCategoryController 全局商品分类控制器
type GlobalCategoryController struct {
	web.Controller
	service *services.GlobalCategoryService
}

// Prepare 初始化控制器
func (c *GlobalCategoryController) Prepare() {
	logs.Info("--- [GlobalCategoryController] Prepare() 方法被调用 ---")
	c.service = services.NewGlobalCategoryService()
}

// CreateCategory 创建全局分类
// @Title 创建全局分类
// @Description 创建一个新的全局商品分类
// @Param body body models.GlobalCategory true "分类信息"
// @Success 200 {object} models.GlobalCategory
// @Failure 400 请求参数错误
// @Failure 401 未授权
// @Failure 500 服务器内部错误
// @router / [post]
func (c *GlobalCategoryController) CreateCategory() {
	// 检查用户是否为管理员
	if !c.IsAdmin() {
		c.ResponseError(401, "只有管理员才能创建全局分类")
		return
	}

	// 解析请求体
	var category models.GlobalCategory
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &category); err != nil {
		c.ResponseError(400, "请求参数错误: "+err.Error())
		return
	}

	// 设置创建者ID
	adminID, _ := c.GetAdminID()
	category.CreatedBy = adminID
	category.UpdatedBy = adminID

	// 创建分类
	id, err := c.service.CreateCategory(&category)
	if err != nil {
		c.ResponseError(400, err.Error())
		return
	}

	// 获取创建后的分类信息
	createdCategory, err := c.service.GetCategoryByID(id)
	if err != nil {
		c.ResponseError(500, "获取创建后的分类信息失败: "+err.Error())
		return
	}

	c.ResponseSuccess(createdCategory)
}

// UpdateCategory 更新全局分类
// @Title 更新全局分类
// @Description 更新指定ID的全局商品分类信息
// @Param id path string true "分类ID"
// @Param body body models.GlobalCategory true "分类信息"
// @Success 200 {object} models.GlobalCategory
// @Failure 400 请求参数错误
// @Failure 401 未授权
// @Failure 404 分类不存在
// @Failure 500 服务器内部错误
// @router /:id [put]
func (c *GlobalCategoryController) UpdateCategory() {
	// 检查用户是否为管理员
	if !c.IsAdmin() {
		c.ResponseError(401, "只有管理员才能更新全局分类")
		return
	}

	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.ResponseError(400, "无效的分类ID")
		return
	}

	// 检查分类是否存在
	existingCategory, err := c.service.GetCategoryByID(id)
	if err != nil {
		c.ResponseError(500, "获取分类信息失败: "+err.Error())
		return
	}
	if existingCategory == nil {
		c.ResponseError(404, "分类不存在")
		return
	}

	// 解析请求体
	var category models.GlobalCategory
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &category); err != nil {
		c.ResponseError(400, "请求参数错误: "+err.Error())
		return
	}

	// 设置分类ID和更新者ID
	category.ID = id
	adminID, _ := c.GetAdminID()
	category.UpdatedBy = adminID

	// 更新分类
	if err := c.service.UpdateCategory(&category); err != nil {
		c.ResponseError(400, err.Error())
		return
	}

	// 获取更新后的分类信息
	updatedCategory, err := c.service.GetCategoryByID(id)
	if err != nil {
		c.ResponseError(500, "获取更新后的分类信息失败: "+err.Error())
		return
	}

	c.ResponseSuccess(updatedCategory)
}

// DeleteCategory 删除全局分类
// @Title 删除全局分类
// @Description 删除指定ID的全局商品分类
// @Param id path string true "分类ID"
// @Success 200 {object} string
// @Failure 400 请求参数错误
// @Failure 401 未授权
// @Failure 404 分类不存在
// @Failure 500 服务器内部错误
// @router /:id [delete]
func (c *GlobalCategoryController) DeleteCategory() {
	// 检查用户是否为管理员
	if !c.IsAdmin() {
		c.ResponseError(401, "只有管理员才能删除全局分类")
		return
	}

	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.ResponseError(400, "无效的分类ID")
		return
	}

	// 删除分类
	if err := c.service.DeleteCategory(id); err != nil {
		c.ResponseError(400, err.Error())
		return
	}

	c.ResponseSuccess("分类删除成功")
}

// GetCategory 获取指定分类
// @Title 获取分类详情
// @Description 获取指定ID的全局商品分类详情
// @Param id path string true "分类ID"
// @Success 200 {object} models.GlobalCategory
// @Failure 400 请求参数错误
// @Failure 404 分类不存在
// @Failure 500 服务器内部错误
// @router /:id [get]
func (c *GlobalCategoryController) GetCategory() {
	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.ResponseError(400, "无效的分类ID")
		return
	}

	// 获取分类
	category, err := c.service.GetCategoryByID(id)
	if err != nil {
		c.ResponseError(500, "获取分类信息失败: "+err.Error())
		return
	}
	if category == nil {
		c.ResponseError(404, "分类不存在")
		return
	}

	c.ResponseSuccess(category)
}

// GetCategoryList 获取分类列表
// @Title 获取分类列表
// @Description 获取全局商品分类列表
// @Param parent_id query string false "父分类ID，不传表示获取所有分类"
// @Success 200 {array} models.GlobalCategory
// @Failure 500 服务器内部错误
// @router / [get]
func (c *GlobalCategoryController) GetCategoryList() {
	logs.Info("[GlobalCategoryController] 接收到获取分类列表请求")
	logs.Info("请求参数: %+v", c.Ctx.Input.Params())
	logs.Info("查询参数: %+v", c.Ctx.Request.URL.Query())
	logs.Info("请求头: %+v", c.Ctx.Request.Header)
	// 获取父分类ID参数
	parentIDStr := c.GetString("parent_id")
	
	var categories []*models.GlobalCategory
	var err error
	
	if parentIDStr != "" {
		// 获取指定父分类的子分类列表
		parentID, err := strconv.ParseInt(parentIDStr, 10, 64)
		if err != nil {
			c.ResponseError(400, "无效的父分类ID")
			return
		}
		
		categories, err = c.service.GetChildCategories(parentID)
	} else {
		// 获取所有分类
		categories, err = c.service.GetAllCategories()
	}
	
	if err != nil {
		c.ResponseError(500, "获取分类列表失败: "+err.Error())
		return
	}
	
	c.ResponseSuccess(categories)
}

// GetCategoryTree 获取分类树
// @Title 获取分类树
// @Description 获取全局商品分类树结构
// @Success 200 {array} models.GlobalCategoryTreeNode
// @Failure 500 服务器内部错误
// @router /tree [get]
func (c *GlobalCategoryController) GetCategoryTree() {
	tree, err := c.service.GetCategoryTree()
	if err != nil {
		c.ResponseError(500, "获取分类树失败: "+err.Error())
		return
	}
	
	c.ResponseSuccess(tree)
}

// GetCategoryPath 获取分类路径
// @Title 获取分类路径
// @Description 获取从根分类到指定分类的完整路径
// @Param id path string true "分类ID"
// @Success 200 {array} models.GlobalCategory
// @Failure 400 请求参数错误
// @Failure 404 分类不存在
// @Failure 500 服务器内部错误
// @router /:id/path [get]
func (c *GlobalCategoryController) GetCategoryPath() {
	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.ResponseError(400, "无效的分类ID")
		return
	}
	
	// 获取分类路径
	path, err := c.service.GetCategoryPath(id)
	if err != nil {
		c.ResponseError(500, "获取分类路径失败: "+err.Error())
		return
	}
	
	// 如果路径为空，表示分类不存在
	if len(path) == 0 {
		c.ResponseError(404, "分类不存在")
		return
	}
	
	c.ResponseSuccess(path)
}

// ResponseSuccess 成功响应
func (c *GlobalCategoryController) ResponseSuccess(data interface{}) {
	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"message": "操作成功",
		"data": data,
	}
	c.ServeJSON()
}

// ResponseError 错误响应
func (c *GlobalCategoryController) ResponseError(code int, message string) {
	c.Data["json"] = map[string]interface{}{
		"code": code,
		"message": message,
	}
	c.ServeJSON()
}

// IsAdmin 检查当前用户是否为管理员
func (c *GlobalCategoryController) IsAdmin() bool {
	// TODO: 实现管理员权限检查逻辑
	return true
}

// GetAdminID 获取当前管理员ID
func (c *GlobalCategoryController) GetAdminID() (int64, error) {
	// TODO: 从会话或令牌中获取管理员ID
	return 1, nil
}
