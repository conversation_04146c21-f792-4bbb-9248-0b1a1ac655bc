/**
 * 商户仪表板控制器
 *
 * 本文件实现了商户仪表板相关的API接口，处理商户统计数据的获取请求。
 * 负责接收商家请求，调用服务层处理业务逻辑，并返回统计数据。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/services"
)

// MerchantDashboardController 商户仪表板控制器
type MerchantDashboardController struct {
	web.Controller
	dashboardService services.MerchantDashboardService
}

// Prepare 控制器预处理
func (c *MerchantDashboardController) Prepare() {
	c.dashboardService = services.NewMerchantDashboardService()
}

// CheckXSRFCookie 实现控制器接口
func (c *MerchantDashboardController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}

// GetStatistics 获取商户仪表板统计数据
// @router /api/v1/merchant/dashboard/statistics [get]
func (c *MerchantDashboardController) GetStatistics() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务获取统计数据
	statistics, err := c.dashboardService.GetDashboardStatistics(merchantID)
	if err != nil {
		logs.Error("获取商户仪表板统计数据失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, statistics)
}
