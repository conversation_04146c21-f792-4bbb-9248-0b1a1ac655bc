/**
 * 商家外卖分类控制器
 *
 * 本文件实现了商家外卖分类相关API，包括分类列表查询、创建分类、
 * 更新分类和删除分类功能。用于商家后台管理自己店铺的外卖分类。
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// MerchantTakeoutCategoryController 商家外卖分类控制器
type MerchantTakeoutCategoryController struct {
	web.Controller
	categoryService services.TakeoutCategoryService
}

// Prepare 初始化方法
func (c *MerchantTakeoutCategoryController) Prepare() {
	// 初始化分类服务
	c.categoryService = services.NewTakeoutCategoryService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *MerchantTakeoutCategoryController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 查询分类列表
// @router /merchant/api/takeout/categories [get]
func (c *MerchantTakeoutCategoryController) List() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取查询参数
	keyword := c.GetString("keyword", "")
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 查询分类列表
	categories, total, err := c.categoryService.GetCategoriesPage(merchantID, keyword, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回分类列表
	result.OKWithPagination(c.Ctx, categories, total, page, pageSize)
}

// Get 获取分类详情
// @router /merchant/api/takeout/categories/:id [get]
func (c *MerchantTakeoutCategoryController) Get() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取分类结果
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询分类详情
	category, err := c.categoryService.GetCategoryByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if category.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 返回分类详情
	result.OK(c.Ctx, category)
}

// Create 创建分类
// @router /merchant/api/takeout/categories [post]
func (c *MerchantTakeoutCategoryController) Create() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.CreateTakeoutCategoryRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 设置商家ID
	req.MerchantID = merchantID

	// 创建分类
	id, err := c.categoryService.CreateCategory(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// Update 更新分类
// @router /merchant/api/takeout/categories/:id [put]
func (c *MerchantTakeoutCategoryController) Update() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取分类结果
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证分类所有权
	category, err := c.categoryService.GetCategoryByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if category.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var req dto.UpdateTakeoutCategoryRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id
	req.MerchantID = merchantID // 确保商家ID不变

	// 更新分类
	if err := c.categoryService.UpdateCategory(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// Delete 删除分类
// @router /merchant/api/takeout/categories/:id [delete]
func (c *MerchantTakeoutCategoryController) Delete() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取分类结果
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证分类所有权
	category, err := c.categoryService.GetCategoryByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if category.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 删除分类
	if err := c.categoryService.DeleteCategory(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *MerchantTakeoutCategoryController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}
