/**
 * 商家外卖套餐控制器
 *
 * 本文件实现了商家外卖套餐相关API，包括套餐组件和套餐选项的管理功能。
 * 用于商家后台管理自己店铺的外卖套餐组件和选项。
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// MerchantTakeoutComboController 商家外卖套餐控制器
type MerchantTakeoutComboController struct {
	web.Controller
	comboService services.TakeoutComboService
	foodService  services.TakeoutFoodService
}

// Prepare 初始化方法
func (c *MerchantTakeoutComboController) Prepare() {
	// 初始化套餐服务和食品服务
	c.comboService = services.NewTakeoutComboService()
	c.foodService = services.NewTakeoutFoodService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *MerchantTakeoutComboController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// ListItems 查询套餐组件列表
// @router /merchant/api/takeout/foods/:id/combo-items [get]
func (c *MerchantTakeoutComboController) ListItems() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询食品信息验证商家所有权
	food, err := c.foodService.GetFoodByID(foodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 查询套餐组件列表
	items, err := c.comboService.ListComboItemsByFoodID(foodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	total := int64(len(items))

	// 返回套餐组件列表
	result.OKWithPagination(c.Ctx, items, total, page, pageSize)
}

// GetItem 获取套餐组件详情
// @router /merchant/api/takeout/combo-items/:id [get]
func (c *MerchantTakeoutComboController) GetItem() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询套餐组件详情
	item, err := c.comboService.GetComboItemByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取项目关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 返回套餐组件详情
	result.OK(c.Ctx, item)
}

// CreateItem 创建套餐组件
// @router /merchant/api/takeout/foods/:id/combo-items [post]
func (c *MerchantTakeoutComboController) CreateItem() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品信息验证商家所有权
	food, err := c.foodService.GetFoodByID(foodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var req dto.TakeoutComboItemRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.FoodID = foodID

	// 创建套餐组件
	id, err := c.comboService.CreateComboItem(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// UpdateItem 更新套餐组件
// @router /merchant/api/takeout/combo-items/:id [put]
func (c *MerchantTakeoutComboController) UpdateItem() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询组件信息
	item, err := c.comboService.GetComboItemByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取项目关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var req dto.UpdateComboItemRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	// 设置组件ID
	req.ID = id

	// 更新套餐组件
	if err := c.comboService.UpdateComboItem(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// DeleteItem 删除套餐组件
// @router /merchant/api/takeout/combo-items/:id [delete]
func (c *MerchantTakeoutComboController) DeleteItem() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询组件信息
	item, err := c.comboService.GetComboItemByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取项目关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 删除套餐组件
	if err := c.comboService.DeleteComboItem(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// ListOptions 查询套餐选项列表
// @router /merchant/api/takeout/combo-items/:id/options [get]
func (c *MerchantTakeoutComboController) ListOptions() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	itemID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询组件信息
	item, err := c.comboService.GetComboItemByID(itemID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取项目关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 查询套餐选项列表
	options, err := c.comboService.ListComboOptionsByItemID(itemID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	total := int64(len(options))

	// 返回套餐选项列表
	result.OKWithPagination(c.Ctx, options, total, page, pageSize)
}

// GetOption 获取套餐选项详情
// @router /merchant/api/takeout/combo-options/:id [get]
func (c *MerchantTakeoutComboController) GetOption() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐选项ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询套餐选项详情
	option, err := c.comboService.GetComboOptionByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 查询组件信息
	item, err := c.comboService.GetComboItemByID(option.ItemID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取组件关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 返回套餐选项详情
	result.OK(c.Ctx, option)
}

// CreateOption 创建套餐选项
// @router /merchant/api/takeout/combo-items/:id/options [post]
func (c *MerchantTakeoutComboController) CreateOption() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐组件ID
	idStr := c.Ctx.Input.Param(":id")
	itemID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询组件信息
	item, err := c.comboService.GetComboItemByID(itemID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取组件关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var req dto.CreateComboOptionRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ItemID = itemID

	// 创建套餐选项
	id, err := c.comboService.CreateComboOption(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// UpdateOption 更新套餐选项
// @router /merchant/api/takeout/combo-options/:id [put]
func (c *MerchantTakeoutComboController) UpdateOption() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐选项ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询选项信息
	option, err := c.comboService.GetComboOptionByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 查询组件信息
	item, err := c.comboService.GetComboItemByID(option.ItemID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取组件关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var req dto.UpdateComboOptionRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	// 设置选项ID和关联关系
	req.ID = id
	req.ItemID = option.ItemID
	req.ComboItemID = option.ComboItemID

	// 更新套餐选项
	if err := c.comboService.UpdateComboOption(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// DeleteOption 删除套餐选项
// @router /merchant/api/takeout/combo-options/:id [delete]
func (c *MerchantTakeoutComboController) DeleteOption() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取套餐选项ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询选项信息
	option, err := c.comboService.GetComboOptionByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 查询组件信息
	item, err := c.comboService.GetComboItemByID(option.ItemID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取组件关联的食品
	food, err := c.foodService.GetFoodByID(item.FoodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 删除套餐选项
	if err := c.comboService.DeleteComboOption(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *MerchantTakeoutComboController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}
