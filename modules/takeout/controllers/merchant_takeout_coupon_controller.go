/**
 * 外卖模块 - 商家优惠券控制器
 * 描述：实现商家端优惠券管理相关的接口
 * 作者：系统
 * 创建时间：2025-05-14
 */

package controllers

import (
	"strconv"

	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	merchantRepos "o_mall_backend/modules/merchant/repositories"
	takeoutdto "o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/repositories"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// MerchantTakeoutCouponController 商家优惠券控制器
type MerchantTakeoutCouponController struct {
	web.Controller
	couponService services.ITakeoutCouponService
}

// Prepare 初始化
func (c *MerchantTakeoutCouponController) Prepare() {
	// 创建服务实例
	c.couponService = services.NewTakeoutCouponService(
		repositories.NewTakeoutCouponRepository(),
		repositories.NewTakeoutUserCouponRepository(),
		&merchantRepos.MerchantRepositoryImpl{},
	)
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *MerchantTakeoutCouponController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 获取优惠券列表
// @Title 获取商家优惠券列表
// @Description 分页获取商家优惠券列表
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} result.Response{data=map[string]interface{}} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons [get]
func (c *MerchantTakeoutCouponController) List() {
	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 从请求中获取商户ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取商户的优惠券列表
	couponListResponse, err := c.couponService.GetMerchantCoupons(merchantID, page, pageSize)
	if err != nil {
		logs.Error("获取商家优惠券列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 使用result包的OKWithPagination方法返回结果
	result.OKWithPagination(c.Ctx, couponListResponse.List, int64(couponListResponse.Total), page, pageSize)
}

// Create 创建优惠券
// @Title 创建商家优惠券
// @Description 创建新的商家优惠券
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param body body takeoutdto.CreateCouponRequest true "优惠券创建参数"
// @Success 200 {object} result.Response{data=models.TakeoutCoupon} "成功返回创建的优惠券"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons [post]
func (c *MerchantTakeoutCouponController) Create() {
	// 从请求中获取商户ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 使用ParseRequest解析请求参数
	req := takeoutdto.CreateCouponRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 设置商户ID
	req.MerchantID = merchantID

	// 调用服务层创建优惠券
	coupon, err := c.couponService.CreateCoupon(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, coupon)
}

// Get 获取优惠券详情
// @Title 获取商家优惠券详情
// @Description 根据优惠券ID获取商家优惠券的详细信息
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param id path int true "优惠券ID"
// @Success 200 {object} result.Response{data=models.TakeoutCoupon} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "优惠券不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons/{id} [get]
func (c *MerchantTakeoutCouponController) Get() {
	// 获取优惠券ID
	idStr := c.Ctx.Input.Param(":id")
	couponID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 从请求中获取商户ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取优惠券详情（验证商家权限）
	coupon, err := c.couponService.GetCouponByMerchantIDAndID(merchantID, couponID)
	if err != nil {
		logs.Error("获取优惠券详情失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(404, err.Error()))
		return
	}

	// 返回优惠券详情
	result.OK(c.Ctx, coupon)
}

// Update 更新优惠券
// @Title 更新商家优惠券
// @Description 根据优惠券ID更新商家优惠券信息
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param id path int true "优惠券ID"
// @Param body body takeoutdto.UpdateCouponRequest true "优惠券更新参数"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "优惠券不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons/{id} [put]
func (c *MerchantTakeoutCouponController) Update() {
	// 获取优惠券ID
	idStr := c.Ctx.Input.Param(":id")
	couponID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 从请求中获取商户ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 使用ParseRequest解析请求参数
	req := takeoutdto.UpdateCouponRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = couponID // 设置ID参数

	// 此处应该调用服务层更新优惠券
	// 在实际实现中应该使用couponID和merchantID进行验证和操作
	_ = merchantID // 避免未使用变量的警告

	// 由于服务层未提供该方法，这里返回一个错误
	result.HandleError(c.Ctx, result.NewError(500, "暂未实现该功能"))
}

// Delete 删除优惠券
// @Title 删除商家优惠券
// @Description 根据优惠券ID删除商家优惠券
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param id path int true "优惠券ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "优惠券不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons/{id} [delete]
func (c *MerchantTakeoutCouponController) Delete() {
	// 获取优惠券ID
	idStr := c.Ctx.Input.Param(":id")
	couponID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 此处应该调用服务层删除优惠券
	// 在实际实现中应该使用couponID进行操作
	_ = couponID // 避免未使用变量的警告

	// 由于服务层未提供该方法，这里返回一个错误
	result.HandleError(c.Ctx, result.NewError(500, "暂未实现该功能"))
}

// Issue 发放优惠券到指定用户
// @Title 发放优惠券到指定用户
// @Description 将指定优惠券发放给特定用户
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param id path int true "优惠券ID"
// @Param body body object true "包含用户ID的JSON对象"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons/{id}/issue [post]
func (c *MerchantTakeoutCouponController) Issue() {
	// 获取优惠券ID
	idStr := c.Ctx.Input.Param(":id")
	couponID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 使用ParseRequest解析请求参数
	req := struct {
		UserID int64 `json:"user_id"`
	}{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层发放优惠券
	err = c.couponService.IssueCouponToUser(couponID, req.UserID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// Publish 发布优惠券
// @Title 发布商家优惠券
// @Description 将待发布状态的优惠券发布为可用状态
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param id path int true "优惠券ID"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 404 {object} result.Response "优惠券不存在"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons/{id}/publish [post]
func (c *MerchantTakeoutCouponController) Publish() {
	// 获取优惠券ID
	idStr := c.Ctx.Input.Param(":id")
	couponID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 从请求中获取商户ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层发布优惠券
	err = c.couponService.PublishCoupon(couponID, merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回发布成功
	result.OK(c.Ctx, nil)
}

// BatchIssue 批量发放优惠券
// @Title 批量发放商家优惠券
// @Description 批量将指定优惠券发放给多个用户
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Param body body object true "包含优惠券ID和用户ID列表的JSON对象"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons/batch-issue [post]
func (c *MerchantTakeoutCouponController) BatchIssue() {
	// 使用ParseRequest解析请求参数
	req := struct {
		CouponID int64   `json:"coupon_id"`
		UserIDs  []int64 `json:"user_ids"`
	}{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 校验参数
	if req.CouponID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "无效的优惠券ID"))
		return
	}

	if len(req.UserIDs) == 0 {
		result.HandleError(c.Ctx, result.NewError(400, "用户ID列表不能为空"))
		return
	}

	// 调用服务层批量发放优惠券
	err := c.couponService.IssueCouponToUsers(req.CouponID, req.UserIDs)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetStatistics 获取优惠券统计信息
// @Title 获取商家优惠券统计信息
// @Description 获取商家优惠券相关的统计数据
// @Tags 商家优惠券管理
// @Accept json
// @Produce json
// @Security MerchantAuth
// @Success 200 {object} result.Response{data=map[string]interface{}} "成功"
// @Failure 400 {object} result.Response "参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @Router /merchant/api/takeout/coupons/statistics [get]
func (c *MerchantTakeoutCouponController) GetStatistics() {
	// 从请求中获取商户ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 实际应用中应该调用服务层获取统计数据
	// 这里只是模拟数据，可以使用merchantID进行查询
	_ = merchantID // 避免未使用变量的警告

	// 此处返回一个模拟的统计信息
	statistics := map[string]interface{}{
		"total_coupons":         0, // 总优惠券数量
		"issued_coupons":        0, // 已发放优惠券数量
		"used_coupons":          0, // 已使用优惠券数量
		"unused_coupons":        0, // 未使用优惠券数量
		"expired_coupons":       0, // 已过期优惠券数量
		"total_discount_amount": 0, // 总优惠金额
	}

	// 返回成功响应
	result.OK(c.Ctx, statistics)
}
