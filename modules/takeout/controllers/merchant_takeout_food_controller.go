/**
 * 商家外卖食品控制器
 *
 * 本文件实现了商家外卖食品相关API，包括食品列表查询、创建食品、
 * 更新食品、删除食品和更新食品状态功能。用于商家后台管理自己店铺的外卖食品。
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// MerchantTakeoutFoodController 商家外卖食品控制器
type MerchantTakeoutFoodController struct {
	web.Controller
	foodService services.TakeoutFoodService
}

// Prepare 初始化方法
func (c *MerchantTakeoutFoodController) Prepare() {
	// 初始化食品服务
	c.foodService = services.NewTakeoutFoodService()
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *MerchantTakeoutFoodController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 查询食品列表
// @router /merchant/api/takeout/foods [get]
func (c *MerchantTakeoutFoodController) List() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("[DEBUG] Failed to get merchant ID from context: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("[DEBUG] List API called by merchant ID: %d", merchantID)

	// 获取查询参数
	categoryIDStr := c.GetString("category_id", "0")
	keyword := c.GetString("keyword", "")
	statusStr := c.GetString("status", "-1")
	auditStatusStr := c.GetString("audit_status", "-1")
	pageStr := c.GetString("page", "1")
	pageSizeStr := c.GetString("page_size", "20")

	// 打印原始请求参数和请求URL
	logs.Info("[DEBUG] 请求URL: %s, 请求方法: %s", c.Ctx.Request.URL.String(), c.Ctx.Request.Method)
	logs.Info("[DEBUG] 所有请求参数: %v", c.Ctx.Request.URL.Query())
	logs.Info("[DEBUG] Raw query parameters: category_id=%s, keyword=%s, status=%s, page=%s, page_size=%s",
		categoryIDStr, keyword, statusStr, auditStatusStr, pageStr, pageSizeStr)

	// 转换参数
	categoryID, _ := strconv.ParseInt(categoryIDStr, 10, 64)
	status, _ := strconv.Atoi(statusStr)
	auditStatus, _ := strconv.Atoi(auditStatusStr)
	page, _ := strconv.Atoi(pageStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)

	// 构造查询请求
	query := &dto.TakeoutFoodQueryRequest{
		MerchantID:  merchantID,
		CategoryID:  categoryID,
		Keyword:     keyword,
		Status:      status,
		AuditStatus: auditStatus,
		Page:        page,
		PageSize:    pageSize,
	}

	logs.Info("[DEBUG] Constructed query: MerchantID=%d, CategoryID=%d, Keyword=%s, Status=%d, Page=%d, PageSize=%d",
		query.MerchantID, query.CategoryID, query.Keyword, query.Status, query.Page, query.PageSize)

	// 我们就直接修改查询参数来测试不同的情况
	// 将status设为小于0的值，表示不过滤状态
	if query.Status == -1 {
		logs.Info("[DEBUG] 状态过滤值为-1，这可能导致仅过滤状态为-1的食品而不是返回所有")
	}

	// 查询食品列表
	foodList, err := c.foodService.ListFoods(query)
	if err != nil {
		logs.Error("[DEBUG] Food service error: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("[DEBUG] Food service returned %d items, total=%d", len(foodList.List), foodList.Total)
	for i, food := range foodList.List {
		logs.Info("[DEBUG] Returned food %d: ID=%d, Name=%s, Status=%d", i+1, food.ID, food.Name, food.Status)
	}

	// 返回结果
	result.OKWithPagination(c.Ctx, foodList.List, int64(foodList.Total), page, pageSize)
}

// Get 获取食品详情
// @router /merchant/api/takeout/foods/:id [get]
func (c *MerchantTakeoutFoodController) Get() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 返回食品详情
	result.OK(c.Ctx, food)
}

// Create 创建食品
// @router /merchant/api/takeout/foods [post]
func (c *MerchantTakeoutFoodController) Create() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.CreateTakeoutFoodRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 设置商家ID
	req.MerchantID = merchantID

	// 设置初始状态为草稿，审核状态为待审核
	req.Status = models.FoodStatusDraft         // 0-草稿状态
	req.AuditStatus = models.AuditStatusPending // 0-待审核状态

	// 创建食品
	id, err := c.foodService.CreateFood(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// Update 更新食品
// @router /merchant/api/takeout/foods/:id [put]
func (c *MerchantTakeoutFoodController) Update() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var req dto.UpdateTakeoutFoodRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id

	// 检查全局分类ID是否存在
	if req.GlobalCategoryID != nil {
		// 这里可以添加额外的验证逻辑，例如检查全局分类是否存在
		// 如果全局分类ID为0，表示清空关联
	}

	// 更新食品
	if err := c.foodService.UpdateFood(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// Delete 删除食品
// @router /merchant/api/takeout/foods/:id [delete]
func (c *MerchantTakeoutFoodController) Delete() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 删除食品
	if err := c.foodService.DeleteFood(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// UpdateStatus 更新食品状态（向后兼容旧API）
// @router /merchant/api/takeout/foods/:id/status [put]
func (c *MerchantTakeoutFoodController) UpdateStatus() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var status int
	if err := c.ParseRequest(&status); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 更新食品状态
	if err := c.foodService.UpdateFoodStatus(id, status); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// Enable 上架食品（设置状态为上架销售中）
// @router /merchant/api/takeout/foods/:id/enable [put]
func (c *MerchantTakeoutFoodController) Enable() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 检查商品当前状态，如果已经是上架状态则不需要更新
	if food.Status == models.FoodStatusOnSale {
		result.OK(c.Ctx, map[string]interface{}{
			"message": "食品已经处于上架状态",
		})
		return
	}

	// 更新食品状态为上架销售中
	if err := c.foodService.UpdateFoodStatus(id, models.FoodStatusOnSale); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, map[string]interface{}{
		"message": "食品上架成功",
	})
}

// Disable 下架食品（设置状态为已下架）
// @router /merchant/api/takeout/foods/:id/disable [put]
func (c *MerchantTakeoutFoodController) Disable() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if food.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 检查商品当前状态，如果已经是下架状态则不需要更新
	if food.Status == models.FoodStatusOffSale {
		result.OK(c.Ctx, map[string]interface{}{
			"message": "食品已经处于下架状态",
		})
		return
	}

	// 更新食品状态为已下架
	if err := c.foodService.UpdateFoodStatus(id, models.FoodStatusOffSale); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, map[string]interface{}{
		"message": "食品下架成功",
	})
}

// ClearCache 清理商家所有外卖商品缓存
// @router /merchant/api/takeout/foods/clear-cache [post]
func (c *MerchantTakeoutFoodController) ClearCache() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 清理商家所有食品相关缓存
	if err := c.foodService.ClearMerchantFoodCache(merchantID); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回清理结果
	result.OK(c.Ctx, map[string]interface{}{
		"message": "商家外卖商品缓存清理成功",
	})
}

// CheckXSRFCookie 实现控制器接口
func (c *MerchantTakeoutFoodController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}
