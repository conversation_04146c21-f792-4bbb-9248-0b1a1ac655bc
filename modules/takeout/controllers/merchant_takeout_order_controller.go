/**
 * 商家外卖订单控制器
 *
 * 本文件实现了商家外卖订单相关API，包括订单列表查询、订单详情查询、
 * 订单更新、接受订单、分配配送员等功能。用于商家后台管理自己店铺的外卖订单。
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// MerchantTakeoutOrderController 商家外卖订单控制器
type MerchantTakeoutOrderController struct {
	web.Controller
	orderService services.TakeoutOrderService
}

// Prepare 初始化方法
func (c *MerchantTakeoutOrderController) Prepare() {
	// 初始化订单服务
	c.orderService = services.NewTakeoutOrderService()
}

// ParseRequest 解析请求参数
func (c *MerchantTakeoutOrderController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// List 查询订单列表
// @router /merchant/api/takeout/orders [get]
func (c *MerchantTakeoutOrderController) List() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取查询参数
	status, _ := c.GetInt("status", -1)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)
	orderNumber := c.GetString("order_number")
	startTime := c.GetString("start_time")
	endTime := c.GetString("end_time")

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 调用专门的商家订单查询方法
	response, total, err := c.orderService.GetOrdersPageForAdmin(merchantID, orderNumber, status, startTime, endTime, page, pageSize)
	if err != nil {
		logs.Error("获取商家订单列表失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回订单列表
	result.OKWithPagination(c.Ctx, response, total, page, pageSize)
}

// Get 获取订单详情
// @router /merchant/api/takeout/orders/:id [get]
func (c *MerchantTakeoutOrderController) Get() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询订单详情
	order, err := c.orderService.GetOrderByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if order.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 返回订单详情
	result.OK(c.Ctx, order)
}

// Update 更新订单
// @router /merchant/api/takeout/orders/:id [put]
func (c *MerchantTakeoutOrderController) Update() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取订单ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证订单存在并属于当前商家
	order, err := c.orderService.GetOrderByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if order.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数 - 这里直接完成订单
	// 当前只允许逻辑简单的操作，复杂操作请使用专用API
	if err := c.orderService.CompleteOrder(id); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// AcceptOrder 接受订单
// @router /merchant/api/takeout/orders/accept [post]
func (c *MerchantTakeoutOrderController) AcceptOrder() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req struct {
		OrderID int64 `json:"order_id"`
	}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证订单存在并属于当前商家
	order, err := c.orderService.GetOrderByID(req.OrderID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if order.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 接受订单
	if err := c.orderService.AcceptOrder(req.OrderID, merchantID); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回接受结果
	result.OK(c.Ctx, nil)
}

// AssignDelivery 分配配送员
// @router /merchant/api/takeout/orders/assign [post]
func (c *MerchantTakeoutOrderController) AssignDelivery() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req struct {
		OrderID         int64 `json:"order_id"`
		DeliveryStaffID int64 `json:"delivery_staff_id"`
	}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证订单存在并属于当前商家
	order, err := c.orderService.GetOrderByID(req.OrderID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证商家所有权
	if order.MerchantID != merchantID {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 分配配送员
	if err := c.orderService.AssignDelivery(req.OrderID, req.DeliveryStaffID, merchantID); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回分配结果
	result.OK(c.Ctx, nil)
}

// GetMerchantOrderStatistics 获取商家订单统计
// @router /merchant/api/takeout/orders/statistics [get]
func (c *MerchantTakeoutOrderController) GetMerchantOrderStatistics() {
	// 获取统计参数
	startDate := c.GetString("start_date", "")
	endDate := c.GetString("end_date", "")

	// 转换时间格式
	startTime, _ := time.Parse("2006-01-02", startDate)
	endTime, _ := time.Parse("2006-01-02", endDate)

	// 如果时间格式有误，默认取过去30天
	if startTime.IsZero() {
		startTime = time.Now().AddDate(0, 0, -30)
	}
	if endTime.IsZero() {
		endTime = time.Now()
	}

	// 获取订单统计 - 这里使用的订单统计接口需要已商家ID过滤
	// 在接口中需要根据 merchantID 过滤数据
	statistics, err := c.orderService.GetOrderStatistics(startTime, endTime)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回统计数据
	result.OK(c.Ctx, statistics)
}

// CheckXSRFCookie 实现控制器接口
func (c *MerchantTakeoutOrderController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}
