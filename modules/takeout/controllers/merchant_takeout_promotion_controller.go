/**
 * 外卖模块 - 商家促销活动控制器
 * 描述：实现商家端促销活动管理相关的接口
 * 作者：系统
 * 创建时间：2025-05-14
 * 更新时间：2025-05-14
 * 更新内容：引入通用请求参数解析方法，统一返回格式
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	//"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
)

// MerchantTakeoutPromotionController 商家促销活动控制器
type MerchantTakeoutPromotionController struct {
	web.Controller
	promotionService services.ITakeoutPromotionService
}

// Prepare 初始化
func (c *MerchantTakeoutPromotionController) Prepare() {
	// 创建服务实例
	c.promotionService = services.NewTakeoutPromotionService(
		repositories.NewTakeoutPromotionRepository(),
		repositories.NewTakeoutFoodPromotionRepository(),
		repositories.NewTakeoutFoodRepository(),
		repositories.NewTakeoutCouponRepository(),
		repositories.NewTakeoutUserCouponRepository(),
		repositories.NewTakeoutUserPromotionRepository(),
	)
}

// ParseRequest 通用请求参数解析方法
// 根据Content-Type自动处理不同格式的请求参数
// 支持application/json、application/x-www-form-urlencoded和multipart/form-data格式
func (c *MerchantTakeoutPromotionController) ParseRequest(req interface{}) error {
	// 使用utils/common包中的通用ParseRequest函数
	return common.ParseRequest(c.Ctx, req)
}

// List 获取促销活动列表
func (c *MerchantTakeoutPromotionController) List() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 调用服务层获取促销活动列表
	response, err := c.promotionService.GetPromotionsByMerchantID(merchantID, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回分页数据
	result.OKWithPagination(c.Ctx, response.List, int64(response.Total), page, pageSize)
}

// Create 创建促销活动
func (c *MerchantTakeoutPromotionController) Create() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求体
	req := dto.CreatePromotionRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "请求参数错误"))
		return
	}

	// 设置商户ID
	req.MerchantID = merchantID

	// 调用服务层创建促销活动
	promotion, err := c.promotionService.CreatePromotion(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建成功的数据
	result.OK(c.Ctx, promotion)
}

// Get 获取促销活动详情
func (c *MerchantTakeoutPromotionController) Get() {
	// 获取活动ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "无效的活动ID"))
		return
	}

	// 先获取当前商家ID（与List方法保持一致）
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层获取指定商家的促销活动详情
	promotion, err := c.promotionService.GetPromotionByMerchantIDAndID(merchantID, id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回促销活动详情
	result.OK(c.Ctx, promotion)
}

// Update 更新促销活动
func (c *MerchantTakeoutPromotionController) Update() {
	// 获取活动ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "无效的活动ID"))
		return
	}

	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求体
	req := dto.UpdatePromotionRequest{}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "请求参数错误"))
		return
	}

	// 设置ID和商户ID
	req.ID = id
	req.MerchantID = merchantID

	// 调用服务层更新促销活动
	err = c.promotionService.UpdatePromotion(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新成功
	result.OK(c.Ctx, nil)
}

// Delete 删除促销活动
func (c *MerchantTakeoutPromotionController) Delete() {
	// 获取活动ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "无效的活动ID"))
		return
	}

	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 此处应该实现删除促销活动的逻辑
	// 由于服务层未提供删除方法，这里暂时使用取消活动的方法代替
	err = c.promotionService.CancelPromotion(id, merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除成功
	result.OK(c.Ctx, nil)
}

// Publish 发布促销活动
func (c *MerchantTakeoutPromotionController) Publish() {
	// 获取活动ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "无效的活动ID"))
		return
	}

	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层发布促销活动
	err = c.promotionService.PublishPromotion(id, merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回发布成功
	result.OK(c.Ctx, nil)
}

// Cancel 取消促销活动
func (c *MerchantTakeoutPromotionController) Cancel() {
	// 获取活动ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "无效的活动ID"))
		return
	}

	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 调用服务层取消促销活动
	err = c.promotionService.CancelPromotion(id, merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回取消成功
	result.OK(c.Ctx, nil)
}

// GetStatistics 获取促销活动统计信息
func (c *MerchantTakeoutPromotionController) GetStatistics() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("获取商户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 从服务层获取促销活动统计信息
	statistics, err := c.promotionService.GetStatistics(merchantID)
	if err != nil {
		logs.Error("获取促销活动统计信息失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(500, "获取促销活动统计信息失败"))
		return
	}

	// 返回统计信息
	result.OK(c.Ctx, statistics)
}
