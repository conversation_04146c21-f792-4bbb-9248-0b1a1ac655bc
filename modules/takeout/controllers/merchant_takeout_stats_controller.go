/*
 * 商户外卖统计控制器
 *
 * 本文件实现了商户外卖统计相关的HTTP接口，为前端提供商户外卖业务的核心统计数据。
 * 包括今日销售额、今日订单数、待处理订单数、库存不足商品数以及销售和订单趋势分析。
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/dto"
	"o_mall_backend/modules/takeout/services"

	"github.com/beego/beego/v2/server/web"
)

// MerchantTakeoutStatsController 商户外卖统计控制器
type MerchantTakeoutStatsController struct {
	web.Controller
	statsService services.MerchantTakeoutStatsService
}

// Prepare 控制器预处理
func (c *MerchantTakeoutStatsController) Prepare() {
	c.statsService = services.NewMerchantTakeoutStatsService()
}

// CheckXSRFCookie 实现控制器接口
func (c *MerchantTakeoutStatsController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}

// GetMerchantTakeoutStats 获取商户外卖统计数据
// @Title 获取商户外卖统计数据
// @Description 获取商户外卖业务的核心统计指标，包括今日销售额、今日订单数、待处理订单数、库存不足商品数以及销售和订单趋势
// @Success 200 {object} dto.MerchantTakeoutStatsDTO "统计数据"
// @Failure 401 {object} utils.Response "未授权"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /merchant/api/takeout/stats [get]
func (c *MerchantTakeoutStatsController) GetMerchantTakeoutStats() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取统计数据
	stats, err := c.statsService.GetMerchantTakeoutStats(c.Ctx.Request.Context(), merchantID)
	if err != nil {
		c.Data["json"] = dto.NewErrorResponse(dto.CodeInternalError, "获取统计数据失败: "+err.Error())
		c.ServeJSON()
		return
	}

	// 返回成功响应
	c.Data["json"] = dto.NewSuccessResponse(stats)
	c.ServeJSON()
}
