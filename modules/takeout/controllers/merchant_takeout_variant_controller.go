/**
 * 商家外卖规格控制器
 *
 * 本文件实现了商家外卖规格相关API，包括规格列表查询、创建规格、
 * 更新规格和删除规格功能。用于商家后台管理自己店铺的外卖规格选项。
 *
 * 重构内容：
 * 1. 添加统一的请求解析方法
 * 2. 标准化错误处理和响应格式
 * 3. 增强安全校验
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// MerchantTakeoutVariantController 商家外卖规格控制器
type MerchantTakeoutVariantController struct {
	web.Controller
	variantService services.TakeoutVariantService
	foodService    services.TakeoutFoodService
}

// ParseRequest 解析请求数据
func (c *MerchantTakeoutVariantController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// BindJSON 绑定JSON请求体
func (c *MerchantTakeoutVariantController) BindJSON(obj interface{}) error {
	return c.Ctx.Input.Bind(obj, "json")
}

// Prepare 初始化方法
func (c *MerchantTakeoutVariantController) Prepare() {
	// 初始化规格服务和食品服务
	c.variantService = services.NewTakeoutVariantService()
	c.foodService = services.NewTakeoutFoodService()
}

// List 查询规格列表
// @router /merchant/api/takeout/foods/:id/variants [get]
func (c *MerchantTakeoutVariantController) List() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证商家拥有该食品
	isOwner, err := c.foodService.ValidateFoodOwnership(foodID, merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if !isOwner {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 查询规格列表 - 获取总数和列表
	variants, err := c.variantService.GetVariantsByFoodID(foodID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	total := len(variants)

	// 返回规格列表
	// 这里没有分页参数，使用默认值，建议服务层实现分页方法
	result.OKWithPagination(c.Ctx, variants, int64(total), 1, total)
}

// Get 获取规格详情
// @router /merchant/api/takeout/variants/:id [get]
func (c *MerchantTakeoutVariantController) Get() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取规格ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询规格详情
	variant, err := c.variantService.GetVariantByIDForMerchant(id, merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回规格详情
	result.OK(c.Ctx, variant)
}

// Create 创建规格
// @router /merchant/api/takeout/foods/:id/variants [post]
func (c *MerchantTakeoutVariantController) Create() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	foodID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证商家拥有该食品
	isOwner, err := c.foodService.ValidateFoodOwnership(foodID, merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if !isOwner {
		result.HandleError(c.Ctx, result.ErrForbidden)
		return
	}

	// 解析请求参数
	var req dto.CreateVariantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.FoodID = foodID

	// 创建规格
	id, err := c.variantService.CreateVariant(&req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回创建结果
	result.OK(c.Ctx, map[string]int64{"id": id})
}

// Update 更新规格
// @router /merchant/api/takeout/variants/:id [put]
func (c *MerchantTakeoutVariantController) Update() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取规格ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var req dto.UpdateVariantRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	req.ID = id

	// 更新规格
	if err := c.variantService.UpdateVariantForMerchant(&req, merchantID); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回更新结果
	result.OK(c.Ctx, nil)
}

// Delete 删除规格
// @router /merchant/api/takeout/variants/:id [delete]
func (c *MerchantTakeoutVariantController) Delete() {
	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取规格ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 删除规格
	if err := c.variantService.DeleteVariantForMerchant(id, merchantID); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回删除结果
	result.OK(c.Ctx, nil)
}

// CheckXSRFCookie 实现控制器接口
func (c *MerchantTakeoutVariantController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}
