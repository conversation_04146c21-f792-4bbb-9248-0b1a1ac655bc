/**
 * 外卖购物车控制器
 *
 * 本文件实现了外卖购物车相关API，包括添加商品到购物车、更新购物车商品数量、
 * 移除购物车商品、购物车列表查询、购物车商品计数，及结算功能。
 *
 * 重构内容：
 * 1. 添加统一的请求解析方法
 * 2. 标准化错误处理和响应格式
 */

package controllers

import (
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"

	"strconv"

	"github.com/beego/beego/v2/server/web"
)

// TakeoutCartController 外卖购物车控制器
type TakeoutCartController struct {
	web.Controller
	cartService services.TakeoutCartService
}

// ParseRequest 解析请求数据
func (c *TakeoutCartController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// BindJSON 绑定JSON请求体
func (c *TakeoutCartController) BindJSON(obj interface{}) error {
	return c.Ctx.Input.Bind(obj, "json")
}

// Prepare 初始化方法
func (c *TakeoutCartController) Prepare() {
	// 初始化购物车服务
	c.cartService = services.NewTakeoutCartService()
}

// Add 添加商品到购物车
// @router /api/v1/user/takeout/cart/add [post]
func (c *TakeoutCartController) Add() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.AddTakeoutToCartRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 添加到购物车
	cartItemID, err := c.cartService.AddToCart(userID, &req)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回购物车项ID
	data := map[string]int64{"cart_item_id": cartItemID}

	// 返回成功
	result.OK(c.Ctx, data)
}

// Update 更新购物车商品数量
// @router /api/v1/user/takeout/cart/update [post]
func (c *TakeoutCartController) Update() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req struct {
		CartItemId int64 `json:"cartItemId"`
		Quantity   int   `json:"quantity"`
	}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 构造更新请求并更新购物车
	updateReq := &dto.UpdateTakeoutCartRequest{
		CartItems: []dto.CartItemUpdateInfo{
			{
				CartItemID: req.CartItemId,
				Quantity:   req.Quantity,
				Selected:   true,
			},
		},
	}

	if err := c.cartService.UpdateCart(userID, updateReq); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功，包含 cartItemId
	result.OK(c.Ctx, map[string]interface{}{
		"cartItemId": req.CartItemId,
	})
}

// Remove 移除购物车商品
// @router /api/v1/user/takeout/cart/remove [post]
func (c *TakeoutCartController) Remove() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req struct {
		CartItemId int64 `json:"cartItemId"`
	}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 移除购物车商品
	if err := c.cartService.RemoveFromCart(userID, req.CartItemId); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功，包含 cartItemId
	result.OK(c.Ctx, map[string]interface{}{
		"cartItemId": req.CartItemId,
	})
}

// List 查询购物车列表
// @router /api/v1/user/takeout/cart/list [get]
func (c *TakeoutCartController) List() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 查询购物车
	cartItems, err := c.cartService.GetCartItems(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回购物车数据
	result.OK(c.Ctx, cartItems)
}

// Count 获取购物车商品计数
// @router /api/v1/user/takeout/cart/count [get]
func (c *TakeoutCartController) Count() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 检查是否需要详细信息
	detailed := c.GetString("detailed", "false")

	if detailed == "true" {
		// 查询购物车详细计数
		countDetails, err := c.cartService.CountCartItemsWithDetails(userID)
		if err != nil {
			result.HandleError(c.Ctx, err)
			return
		}

		// 返回详细计数信息
		result.OK(c.Ctx, countDetails)
	} else {
		// 查询购物车简单计数
		count, err := c.cartService.CountCartItems(userID)
		if err != nil {
			result.HandleError(c.Ctx, err)
			return
		}

		// 返回简单计数
		result.OK(c.Ctx, map[string]int64{"count": int64(count)})
	}
}

// CountDetails 获取购物车详细计数信息
// @router /api/v1/user/takeout/cart/count-details [get]
func (c *TakeoutCartController) CountDetails() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 查询购物车详细计数
	countDetails, err := c.cartService.CountCartItemsWithDetails(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回详细计数信息
	result.OK(c.Ctx, countDetails)
}

// Select 选择/取消选择购物车商品
// @router /api/v1/user/takeout/cart/select [post]
func (c *TakeoutCartController) Select() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.SelectCartItemRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 选择购物车商品
	if err := c.cartService.SelectCartItem(userID, &req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, map[string]interface{}{
		"success": true,
	})
}

// Checkout 结算购物车
// @router /api/v1/user/takeout/cart/checkout [post]
func (c *TakeoutCartController) Checkout() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req struct {
		CartItemIDs []int64 `json:"cart_item_ids"`
		AddressID   int64   `json:"address_id"`
	}
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 查询已选中的购物车商品
	checkoutInfo, err := c.cartService.CheckoutCart(userID, req.CartItemIDs, req.AddressID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回结算信息
	result.OK(c.Ctx, checkoutInfo)
}

// Validate 验证购物车商品有效性
// @router /api/v1/user/takeout/cart/validate [post]
func (c *TakeoutCartController) Validate() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 验证购物车商品有效性
	invalidItems, err := c.cartService.ValidateCartItems(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回无效商品列表
	result.OK(c.Ctx, map[string]interface{}{
		"invalidItems": invalidItems,
	})
}

// ClearInvalid 清除无效购物车商品
// @router /api/v1/user/takeout/cart/clear-invalid [delete]
func (c *TakeoutCartController) ClearInvalid() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 清除无效购物车商品
	err = c.cartService.ClearInvalidCartItems(userID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功
	result.OK(c.Ctx, map[string]interface{}{
		"success": true,
		"message": "无效商品已清除",
	})
}

// RemoveById 通过ID删除购物车商品
// @router /api/v1/user/takeout/cart/:id [delete]
func (c *TakeoutCartController) RemoveById() {
	// 获取用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 从URL路径参数中获取购物车项ID
	cartItemIDStr := c.Ctx.Input.Param(":id")
	cartItemID, err := strconv.ParseInt(cartItemIDStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.NewError(400, "无效的购物车项ID"))
		return
	}

	// 移除购物车商品
	if err := c.cartService.RemoveFromCart(userID, cartItemID); err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功，包含 cartItemId
	result.OK(c.Ctx, map[string]interface{}{
		"cartItemId": cartItemID,
		"success": true,
		"message": "购物车商品已删除",
	})
}

// CheckXSRFCookie 实现控制器接口
func (c *TakeoutCartController) CheckXSRFCookie() bool {
	// 外卖购物车API不需要XSRF令牌
	return false
}

// Options 处理OPTIONS预检请求
// @Summary 处理OPTIONS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Tags 外卖购物车
// @Accept json
// @Produce json
// @Success 200 {string} string "OK"
// @Router /api/v1/user/takeout/cart/add [options]
// @Router /api/v1/user/takeout/cart/update [options]
// @Router /api/v1/user/takeout/cart/remove [options]
// @Router /api/v1/user/takeout/cart/:id [options]
// @Router /api/v1/user/takeout/cart/list [options]
// @Router /api/v1/user/takeout/cart/count [options]
// @Router /api/v1/user/takeout/cart/select [options]
// @Router /api/v1/user/takeout/cart/checkout [options]
func (c *TakeoutCartController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}
