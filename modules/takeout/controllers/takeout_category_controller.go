/**
 * 外卖分类控制器
 *
 * 本文件实现了外卖分类相关API，包括分类列表查询和详情查询。
 * 用于客户端显示外卖菜单分类。
 *
 * 重构内容：
 * 1. 添加统一的请求解析方法
 * 2. 标准化错误处理和响应格式
 */

package controllers

import (
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// TakeoutCategoryController 外卖分类控制器
type TakeoutCategoryController struct {
	web.Controller
	categoryService services.TakeoutCategoryService
}

// ParseRequest 解析请求数据
func (c *TakeoutCategoryController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// BindJSON 绑定JSON请求体
func (c *TakeoutCategoryController) BindJSON(obj interface{}) error {
	return c.Ctx.Input.Bind(obj, "json")
}

// Prepare 初始化方法
func (c *TakeoutCategoryController) Prepare() {
	// 初始化分类服务
	c.categoryService = services.NewTakeoutCategoryService()
}

// List 查询分类列表
// @router /api/v1/user/takeout/categories [get]
func (c *TakeoutCategoryController) List() {
	// 获取查询参数
	merchantID, _ := c.GetInt64("merchant_id", 0)

	// 查询分类列表
	categories, err := c.categoryService.GetCategoryTree(merchantID)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	total := len(categories)

	// 返回分类列表
	result.OKWithPagination(c.Ctx, categories, int64(total), 1, total)
}

// GetMerchantCategories 获取指定商家的分类列表
// @router /api/v1/user/takeout/merchants/:id/categories [get]
func (c *TakeoutCategoryController) GetMerchantCategories() {
	// 获取商家ID
	idStr := c.Ctx.Input.Param(":id")
	merchantID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || merchantID <= 0 {
		logs.Error("无效的商家ID: %s", idStr)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询商家分类列表
	categories, err := c.categoryService.GetCategoryTree(merchantID)
	if err != nil {
		logs.Error("获取商家[%d]分类列表失败: %v", merchantID, err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	total := len(categories)

	// 返回分类列表
	result.OKWithPagination(c.Ctx, categories, int64(total), 1, total)
}

// GetDetail 查询分类详情
// @router /api/v1/user/takeout/categories/:id [get]
func (c *TakeoutCategoryController) GetDetail() {
	// 获取分类ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询分类详情
	category, err := c.categoryService.GetCategoryByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回分类详情
	result.OK(c.Ctx, category)
}

// CheckXSRFCookie 实现控制器接口
func (c *TakeoutCategoryController) CheckXSRFCookie() bool {
	// 分类查询API不需要XSRF令牌
	return false
}
