/*
 * 外卖模块用户优惠券控制器
 *
 * 本文件实现了用户端优惠券相关的API接口，包括获取用户优惠券列表、
 * 验证优惠券可用性、在订单中使用优惠券等功能。
 */

package controllers

import (
	"strconv"
	"strings"

	"o_mall_backend/common/result"
	merchantRepos "o_mall_backend/modules/merchant/repositories"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// TakeoutCouponController 用户优惠券控制器
type TakeoutCouponController struct {
	web.Controller
	takeoutCouponService services.ITakeoutCouponService
}

// Prepare 初始化
func (c *TakeoutCouponController) Prepare() {
	// 初始化服务
	c.takeoutCouponService = services.NewTakeoutCouponService(
		repositories.NewTakeoutCouponRepository(),
		repositories.NewTakeoutUserCouponRepository(),
		&merchantRepos.MerchantRepositoryImpl{},
	)
}

// ParseRequest 解析请求数据
func (c *TakeoutCouponController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// Options
func (c *TakeoutCouponController) Options() {
	c.Data["json"] = map[string]interface{}{"status": "success"}
	c.ServeJSON()
}

// GetMyCoupons 获取我的优惠券列表
// @Title Get My Coupons
// @Description Get user's coupon list
// @Param refresh query bool false "是否刷新获取所有状态优惠券，true-获取所有状态，false-按status过滤"
// @Param status query int false "Coupon status filter (1-unused, 2-used, 3-expired), default is 1"
// @Param page query int false "Page number"
// @Param page_size query int false "Items per page"
// @Success 200 {object} result.Result{data=dto.UserCouponListResponse} "Success"
// @Failure 401 {object} result.Result "Unauthorized"
// @router /my-list [get]
func (c *TakeoutCouponController) GetMyCoupons() {
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 检查是否为刷新请求
	refresh, _ := c.GetBool("refresh", false)

	var status int
	if refresh {
		// 如果是刷新请求，获取所有状态的优惠券
		status = -1 // -1表示获取所有状态
		logs.Info("刷新请求，获取用户所有状态的优惠券，用户ID: %d", userID)
	} else {
		// 否则按指定状态过滤，默认获取未使用的
		status, _ = c.GetInt("status", models.UserCouponStatusUnused)
	}

	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	resp, err := c.takeoutCouponService.GetUserCoupons(userID, status, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, resp)
}

// GetCouponCenterList 获取优惠券中心列表
// @Title Get Coupon Center List
// @Description Get list of coupons available in the coupon center
// @Param category query string false "Category filter"
// @Param page query int false "Page number"
// @Param page_size query int false "Items per page"
// @Success 200 {object} result.Result{data=[]dto.CouponCenterItemDTO} "Success"
// @Failure 401 {object} result.Result "Unauthorized"
// @router /center [get]
func (c *TakeoutCouponController) GetCouponCenterList() {
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	category := c.GetString("category", "all")
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	resp, err := c.takeoutCouponService.GetCouponCenterList(userID, category, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, resp)
}

// GetUserCoupons
// @Title
// @Description
// @Param status query int false "0-，1-，2-，-1-"
// @Param page query int false "，1"
// @Param page_size query int false "，10"
// @Param status query int false "优惠券状态：0-未使用，1-已使用，2-已过期，-1-全部"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Success 200 {object} dto.UserCouponListResponse
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /list [get]
func (c *TakeoutCouponController) GetUserCoupons() {
	// 获取用户ID（从JWT中间件获取）
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 获取查询参数
	status, _ := c.GetInt("status", -1) // -1表示获取所有状态
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// 调用服务获取用户优惠券列表
	couponResult, err := c.takeoutCouponService.GetUserCoupons(userID, status, page, pageSize)
	if err != nil {
		logs.Error("获取用户优惠券列表失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("获取优惠券列表失败"))
		return
	}

	result.OK(c.Ctx, couponResult)
}

// GetCouponDetail 获取优惠券详情
// @Title 获取优惠券详情
// @Description 获取指定优惠券的详细信息
// @Param id path int true "优惠券ID"
// @Success 200 {object} dto.CouponResponse
// @Failure 400 {object} utils.Response
// @Failure 404 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /:id [get]
func (c *TakeoutCouponController) GetCouponDetail() {
	// 获取优惠券ID
	couponIDStr := c.Ctx.Input.Param(":id")
	couponID, err := strconv.ParseInt(couponIDStr, 10, 64)
	if err != nil || couponID <= 0 {
		logs.Error("无效的优惠券ID: %s", couponIDStr)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的优惠券ID"))
		return
	}

	// 调用服务获取优惠券详情
	coupon, err := c.takeoutCouponService.GetCouponByID(couponID)
	if err != nil {
		logs.Error("获取优惠券详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("获取优惠券详情失败"))
		return
	}

	if coupon == nil {
		result.HandleError(c.Ctx, result.ErrNotFound.WithDetails("优惠券不存在"))
		return
	}

	result.OK(c.Ctx, coupon)
}

// CheckCouponAvailability 检查优惠券可用性
// @Title 检查优惠券可用性
// @Description 检查指定优惠券是否可用于当前订单
// @Param body body dto.CouponValidationRequest true "验证请求"
// @Success 200 {object} dto.CouponValidationResult
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /check [post]
func (c *TakeoutCouponController) CheckCouponAvailability() {
	// 获取用户ID
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 解析请求参数
	var req dto.CouponValidationRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("请求参数格式错误"))
		return
	}

	// 参数验证
	if req.CouponID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("优惠券ID不能为空"))
		return
	}

	if req.TotalAmount <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("订单金额必须大于0"))
		return
	}

	// 调用服务检查优惠券可用性
	validationResult, err := c.takeoutCouponService.CheckCouponAvailability(userID, req.CouponID, req.TotalAmount)
	if err != nil {
		logs.Error("检查优惠券可用性失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("检查优惠券可用性失败"))
		return
	}

	result.OK(c.Ctx, validationResult)
}

// ValidateCouponForOrder 验证优惠券是否适用于订单
// @Title 验证优惠券是否适用于订单
// @Description 验证指定优惠券是否适用于当前订单的商品
// @Param body body dto.OrderCouponValidationRequest true "订单验证请求"
// @Success 200 {object} dto.CouponValidationResult
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /validate [post]
func (c *TakeoutCouponController) ValidateCouponForOrder() {
	// 获取用户ID
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 解析请求参数
	var req dto.OrderCouponValidationRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("请求参数格式错误"))
		return
	}

	// 参数验证
	if req.CouponID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("优惠券ID不能为空"))
		return
	}

	if req.Order == nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("订单信息不能为空"))
		return
	}

	if len(req.Order.Items) == 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("订单商品不能为空"))
		return
	}

	// 调用服务验证优惠券
	validationResult, err := c.takeoutCouponService.ValidateCouponForOrder(userID, req.CouponID, req.Order)
	if err != nil {
		logs.Error("验证优惠券失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("验证优惠券失败"))
		return
	}

	result.OK(c.Ctx, validationResult)
}

// GetAvailableCoupons 获取可用优惠券列表
// @Title 获取可用优惠券列表
// @Description 获取用户在指定订单金额下可用的优惠券列表
// @Param total_amount query float64 true "订单总金额"
// @Param merchant_id query int false "商家ID，用于筛选特定商家的优惠券"
// @Success 200 {object} dto.UserCouponListResponse
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /available [get]
func (c *TakeoutCouponController) GetAvailableCoupons() {
	// 获取用户ID
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 获取查询参数
	totalAmountStr := c.GetString("total_amount")
	if totalAmountStr == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("订单总金额不能为空"))
		return
	}

	totalAmount, err := strconv.ParseFloat(totalAmountStr, 64)
	if err != nil || totalAmount <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的订单金额"))
		return
	}

	merchantID, _ := c.GetInt64("merchant_id", 0)

	// 获取用户所有未使用的优惠券
	userCoupons, err := c.takeoutCouponService.GetUserCoupons(userID, 0, 1, 100) // 0表示未使用状态
	if err != nil {
		logs.Error("获取用户优惠券失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("获取优惠券列表失败"))
		return
	}

	// 筛选可用的优惠券
	availableCoupons := make([]dto.UserCouponResponse, 0)
	for _, userCoupon := range userCoupons.List {
		// 检查商家限制
		if merchantID > 0 && userCoupon.Coupon != nil && userCoupon.Coupon.MerchantID != merchantID {
			continue
		}

		// 检查优惠券可用性
		if userCoupon.Coupon != nil {
			validationResult, err := c.takeoutCouponService.CheckCouponAvailability(userID, userCoupon.CouponID, totalAmount)
			if err == nil && validationResult.Valid {
				availableCoupons = append(availableCoupons, userCoupon)
			}
		}
	}

	couponListResult := &dto.UserCouponListResponse{
		Total: len(availableCoupons),
		List:  availableCoupons,
	}

	result.OK(c.Ctx, couponListResult)
}

// GetCouponUsageHistory 获取优惠券使用历史
// @Title 获取优惠券使用历史
// @Description 获取用户的优惠券使用历史记录
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Success 200 {object} dto.UserCouponListResponse
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /history [get]
func (c *TakeoutCouponController) GetCouponUsageHistory() {
	// 获取用户ID
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 获取查询参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// 获取已使用的优惠券（状态为1）
	historyResult, err := c.takeoutCouponService.GetUserCoupons(userID, 1, page, pageSize)
	if err != nil {
		logs.Error("获取优惠券使用历史失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("获取使用历史失败"))
		return
	}

	result.OK(c.Ctx, historyResult)
}

// CalculateOrderDiscount 计算订单优惠
// @Title 计算订单优惠
// @Description 计算使用指定优惠券后的订单优惠金额
// @Param body body dto.OrderDiscountCalculationRequest true "计算请求"
// @Success 200 {object} dto.OrderDiscountInfo
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /calculate [post]
func (c *TakeoutCouponController) CalculateOrderDiscount() {
	// 获取用户ID
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 解析请求参数
	var req dto.OrderDiscountCalculationRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("请求参数格式错误"))
		return
	}

	// 参数验证
	if req.Order == nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("订单信息不能为空"))
		return
	}

	if req.Order.TotalAmount <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("订单金额必须大于0"))
		return
	}

	// 初始化优惠信息
	discountInfo := &dto.OrderDiscountInfo{
		OriginalAmount: req.Order.TotalAmount,
		DiscountAmount: 0,
		FinalAmount:    req.Order.TotalAmount,
		CouponID:       0,
		PromotionIDs:   make([]int64, 0),
	}

	// 如果指定了优惠券，计算优惠
	if req.CouponID > 0 {
		// 验证优惠券
		validationResult, err := c.takeoutCouponService.ValidateCouponForOrder(userID, req.CouponID, req.Order)
		if err != nil {
			logs.Error("验证优惠券失败: %v", err)
			result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("验证优惠券失败"))
			return
		}

		if !validationResult.Valid {
			result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(validationResult.Message))
			return
		}

		// 应用优惠
		discountInfo.CouponID = req.CouponID
		discountInfo.DiscountAmount = validationResult.DiscountAmount
		discountInfo.FinalAmount = req.Order.TotalAmount - validationResult.DiscountAmount

		// 确保最终金额不为负数
		if discountInfo.FinalAmount < 0 {
			discountInfo.FinalAmount = 0
			discountInfo.DiscountAmount = req.Order.TotalAmount
		}
	}

	result.OK(c.Ctx, discountInfo)
}

// GetAvailableCouponsForOrder 获取订单可用优惠券列表
// @Title 获取订单可用优惠券列表
// @Description 获取用户在指定订单下可用的优惠券列表
// @Param merchant_id query int true "商家ID"
// @Param total_amount query float true "订单总金额"
// @Param food_ids query string true "商品ID列表（逗号分隔）"
// @Success 200 {object} result.Result{data=map[string]interface{}} "Success"
// @Failure 400 {object} result.Result "Bad Request"
// @Failure 401 {object} result.Result "Unauthorized"
// @router /available-for-order [get]
func (c *TakeoutCouponController) GetAvailableCouponsForOrder() {
	// 获取用户ID
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 获取查询参数
	merchantIDStr := c.GetString("merchant_id")
	if merchantIDStr == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("商家ID不能为空"))
		return
	}
	merchantID, err := strconv.ParseInt(merchantIDStr, 10, 64)
	if err != nil || merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的商家ID"))
		return
	}

	totalAmountStr := c.GetString("total_amount")
	if totalAmountStr == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("订单总金额不能为空"))
		return
	}
	totalAmount, err := strconv.ParseFloat(totalAmountStr, 64)
	if err != nil || totalAmount <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的订单金额"))
		return
	}

	foodIDsStr := c.GetString("food_ids")
	if foodIDsStr == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("商品ID列表不能为空"))
		return
	}

	// 解析商品ID列表
	foodIDStrings := strings.Split(foodIDsStr, ",")
	foodIDs := make([]int64, 0, len(foodIDStrings))
	for _, idStr := range foodIDStrings {
		id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
		if err == nil && id > 0 {
			foodIDs = append(foodIDs, id)
		}
	}

	if len(foodIDs) == 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的商品ID列表"))
		return
	}

	logs.Debug("获取订单可用优惠券, 用户ID: %d, 商家ID: %d, 订单金额: %.2f, 商品IDs: %v", userID, merchantID, totalAmount, foodIDs)

	// 获取用户所有未使用的优惠券
	userCoupons, err := c.takeoutCouponService.GetUserCoupons(userID, models.UserCouponStatusUnused, 1, 100) // 获取未使用状态的优惠券
	if err != nil {
		logs.Error("获取用户优惠券失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("获取优惠券列表失败"))
		return
	}

	// 准备订单信息用于验证
	orderItems := make([]dto.OrderItemValidation, 0, len(foodIDs))
	for _, foodID := range foodIDs {
		orderItems = append(orderItems, dto.OrderItemValidation{
			FoodID:     foodID,
			Quantity:   1, // 默认数量，实际计算时不影响优惠券可用性判断
			Price:      0, // 默认单价，验证可用性时不需要精确价格
			CategoryID: 0, // 分类ID待填充，此处简化处理
		})
	}

	orderInfo := &dto.OrderValidationInfo{
		TotalAmount: totalAmount,
		Items:       orderItems,
	}

	// 筛选可用的优惠券
	availableCoupons := make([]map[string]interface{}, 0)
	unavailableCoupons := make([]map[string]interface{}, 0)

	for _, userCoupon := range userCoupons.List {
		// 检查商家限制
		if userCoupon.Coupon != nil && userCoupon.Coupon.MerchantID > 0 && userCoupon.Coupon.MerchantID != merchantID {
			// 添加到不可用列表，指明原因
			unavailableCoupons = append(unavailableCoupons, map[string]interface{}{
				"id":               userCoupon.ID,
				"coupon_id":        userCoupon.CouponID,
				"name":             userCoupon.Coupon.Name,
				"amount":           userCoupon.Coupon.Amount,
				"min_order_amount": userCoupon.Coupon.MinOrderAmount,
				"can_use":          false,
				"reason":           "该优惠券不适用于当前商家",
			})
			continue
		}

		// 检查优惠券可用性
		if userCoupon.Coupon != nil {
			// 使用正确的优惠券ID而非用户优惠券ID
			validationResult, err := c.takeoutCouponService.ValidateCouponForOrder(userID, userCoupon.CouponID, orderInfo)

			// 如果优惠券适用于所有商品，但验证结果为失败，则手动重新验证
			if err != nil && userCoupon.Coupon.ApplyToAll == true &&
				totalAmount >= userCoupon.Coupon.MinOrderAmount &&
				userCoupon.Coupon.MerchantID == merchantID {
				logs.Warning("优惠券设置了apply_to_all=true，但验证失败，尝试手动验证，错误: %v", err)

				// 手动计算优惠金额
				var discountAmount float64
				if userCoupon.Coupon.Type == models.CouponTypeAmount {
					discountAmount = userCoupon.Coupon.Amount
				} else if userCoupon.Coupon.Type == models.CouponTypeDiscount {
					discountRate := userCoupon.Coupon.Amount / 10.0
					discountAmount = totalAmount * (1 - discountRate)
					if userCoupon.Coupon.MaxDiscountAmount > 0 && discountAmount > userCoupon.Coupon.MaxDiscountAmount {
						discountAmount = userCoupon.Coupon.MaxDiscountAmount
					}
				}

				// 使用手动计算的结果
				validationResult = &dto.CouponValidationResult{
					CouponID:       userCoupon.CouponID,
					Valid:          true,
					DiscountAmount: discountAmount,
					Message:        "优惠券可用",
				}
				err = nil
			}

			if err == nil && validationResult.Valid {
				// 可用优惠券，计算实际优惠金额
				finalAmount := totalAmount - validationResult.DiscountAmount
				if finalAmount < 0 {
					finalAmount = 0
				}

				availableCoupons = append(availableCoupons, map[string]interface{}{
					"id":               userCoupon.ID,
					"coupon_id":        userCoupon.CouponID,
					"name":             userCoupon.Coupon.Name,
					"amount":           userCoupon.Coupon.Amount,
					"min_order_amount": userCoupon.Coupon.MinOrderAmount,
					"can_use":          true,
					"reason":           "",
					"discount_amount":  validationResult.DiscountAmount,
					"final_amount":     finalAmount,
				})
			} else {
				// 不可用优惠券，指明原因
				reason := "优惠券不适用于当前订单"
				if err == nil && !validationResult.Valid && validationResult.Message != "" {
					reason = validationResult.Message
				}

				unavailableCoupons = append(unavailableCoupons, map[string]interface{}{
					"id":               userCoupon.ID,
					"coupon_id":        userCoupon.CouponID,
					"name":             userCoupon.Coupon.Name,
					"amount":           userCoupon.Coupon.Amount,
					"min_order_amount": userCoupon.Coupon.MinOrderAmount,
					"can_use":          false,
					"reason":           reason,
				})
			}
		}
	}

	responseData := map[string]interface{}{
		"available_coupons":   availableCoupons,
		"unavailable_coupons": unavailableCoupons,
	}

	logs.Debug("可用优惠券数量: %d, 不可用优惠券数量: %d", len(availableCoupons), len(unavailableCoupons))

	result.OK(c.Ctx, responseData)
}

// ClaimCoupon 领取优惠券
// @Title 领取优惠券
// @Description 用户领取指定的优惠券
// @Param body body dto.ClaimCouponRequest true "领取优惠券请求"
// @Success 200 {object} dto.UserCouponResponse
// @Failure 400 {object} utils.Response
// @Failure 401 {object} utils.Response
// @Failure 500 {object} utils.Response
// @router /claim [post]
func (c *TakeoutCouponController) ClaimCoupon() {
	// 获取用户ID
	userIDRaw := c.Ctx.Input.GetData("userID")
	userID, ok := userIDRaw.(int64)
	if !ok || userID <= 0 {
		logs.Error("从上下文中获取用户ID失败或类型不匹配")
		result.HandleError(c.Ctx, result.ErrUnauthorized.WithDetails("用户身份验证失败"))
		return
	}

	// 解析请求参数
	var req dto.ClaimCouponRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("请求参数格式错误"))
		return
	}

	// 参数验证
	if req.CouponID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("优惠券ID无效"))
		return
	}

	// 调用服务领取优惠券
	userCoupon, err := c.takeoutCouponService.ClaimCoupon(userID, req.CouponID)
	if err != nil {
		logs.Error("领取优惠券失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 获取优惠券详情
	coupon, err := c.takeoutCouponService.GetCouponByID(userCoupon.CouponID)
	if err != nil {
		logs.Error("获取优惠券详情失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInternalError.WithDetails("获取优惠券详情失败"))
		return
	}

	// 获取商家信息
	merchant, err := c.takeoutCouponService.GetMerchantByID(coupon.MerchantID)
	if err != nil {
		logs.Warn("获取商家信息失败，商家ID: %d, 错误: %v", coupon.MerchantID, err)
	}

	// 构造商家信息
	merchantName := "未知商家"
	merchantLogo := ""
	if merchant != nil {
		merchantName = merchant.Name
		merchantLogo = merchant.Logo
	}

	// 返回响应（包含商家信息）
	result.OK(c.Ctx, dto.ConvertToUserCouponResponseWithMerchant(userCoupon, coupon, merchantName, merchantLogo))
}
