/**
 * 外卖配送控制器
 *
 * 本文件实现了外卖配送相关的API接口，处理配送员开始配送、完成配送等操作。
 * 负责接收配送员请求，调用服务层处理业务逻辑，并返回处理结果。
 *
 * 重构内容：
 * 1. 添加统一的请求解析方法
 * 2. 标准化错误处理和响应格式
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
)

// TakeoutDeliveryController 外卖配送控制器
type TakeoutDeliveryController struct {
	web.Controller
	orderService services.TakeoutOrderService
}

// Prepare 控制器预处理
func (c *TakeoutDeliveryController) Prepare() {
	c.orderService = services.NewTakeoutOrderService()
}

// CheckXSRFCookie 实现控制器接口
func (c *TakeoutDeliveryController) CheckXSRFCookie() bool {
	// API不需要XSRF令牌
	return false
}

// ParseRequest 解析请求数据
func (c *TakeoutDeliveryController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// StartDelivery 开始配送
// @router /api/takeout/delivery/start [post]
func (c *TakeoutDeliveryController) StartDelivery() {
	// 获取当前配送员ID
	deliveryStaffID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if deliveryStaffID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req struct {
		OrderID int64 `json:"order_id"`
	}
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	if req.OrderID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务开始配送
	err = c.orderService.StartDelivery(req.OrderID, deliveryStaffID)
	if err != nil {
		logs.Error("开始配送失败: %v, 订单ID: %d, 配送员ID: %d", err, req.OrderID, deliveryStaffID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, nil)
}

// CompleteDelivery 完成配送
// @router /api/takeout/delivery/complete [post]
func (c *TakeoutDeliveryController) CompleteDelivery() {
	// 获取当前配送员ID
	deliveryStaffID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if deliveryStaffID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req struct {
		OrderID int64 `json:"order_id"`
	}
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	if req.OrderID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务完成配送
	err = c.orderService.CompleteDelivery(req.OrderID, deliveryStaffID)
	if err != nil {
		logs.Error("完成配送失败: %v, 订单ID: %d, 配送员ID: %d", err, req.OrderID, deliveryStaffID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 自动完成订单
	err = c.orderService.CompleteOrder(req.OrderID)
	if err != nil {
		logs.Warn("自动完成订单失败: %v, 订单ID: %d", err, req.OrderID)
		// 不影响配送完成结果，只记录警告日志
	}

	result.OK(c.Ctx, nil)
}

// ListDeliveryOrders 获取配送员订单列表
// @router /api/takeout/delivery/list [get]
func (c *TakeoutDeliveryController) ListDeliveryOrders() {
	// 获取当前配送员ID
	deliveryStaffID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if deliveryStaffID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取查询参数
	status, _ := c.GetInt("status", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 参数校验
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 50 {
		pageSize = 10
	}

	// 调用服务获取订单列表
	// 此处应该有专门的配送员订单列表方法，但为简化示例复用了用户订单列表方法
	response, err := c.orderService.ListOrdersByUserID(deliveryStaffID, status, page, pageSize)
	if err != nil {
		logs.Error("获取配送员订单列表失败: %v, 配送员ID: %d", err, deliveryStaffID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	orderList := response.List
	total := response.Total

	// 返回订单列表
	result.OKWithPagination(c.Ctx, orderList, int64(total), page, pageSize)
}
