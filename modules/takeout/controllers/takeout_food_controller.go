/**
 * 外卖食品控制器
 *
 * 本文件实现了外卖食品相关API，包括食品列表查询、详情查询、
 * 规格查询以及套餐选项查询。用于客户端显示外卖菜单。
 *
 * 重构内容：
 * 1. 添加统一的请求解析方法
 * 2. 标准化错误处理和响应格式
 */

package controllers

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
)

// TakeoutFoodController 外卖食品控制器
type TakeoutFoodController struct {
	web.Controller
	foodService  services.TakeoutFoodService
	comboService services.TakeoutComboService
}

// Prepare 初始化方法
func (c *TakeoutFoodController) Prepare() {
	// 初始化食品服务
	c.foodService = services.NewTakeoutFoodService()
	c.comboService = services.NewTakeoutComboService()
}

// ParseRequest 解析请求数据
func (c *TakeoutFoodController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// List 查询商家列表或单个商家信息
// @router /api/v1/user/takeout/merchants [get]
func (c *TakeoutFoodController) List() {
	// 获取查询参数
	id, _ := c.GetInt64("id", 0) // 获取id参数，默认为0
	keyword := c.GetString("keyword", "")
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)
	globalCategoryID, _ := c.GetInt64("global_category_id", 0) // 获取全局分类ID参数
	operationStatus, _ := c.GetInt("operation_status", -1)     // 获取营业状态参数

	// 如果提供了id参数，则查询指定id的商家信息
	if id > 0 {
		logs.Info("根据id=%d查询单个商家信息", id)

		// 使用新的匹配方式查询指定商家
		merchants, total, err := c.foodService.ListMerchants("id="+strconv.FormatInt(id, 10), 1, 1, 0, -1)
		if err != nil {
			logs.Error("根据ID查询商家失败: %v, 商家ID: %d", err, id)
			result.HandleError(c.Ctx, err)
			return
		}

		// 如果没有找到对应商家
		if total == 0 || len(merchants) == 0 {
			logs.Warn("未找到对应商家, ID: %d", id)
			result.HandleError(c.Ctx, result.ErrNotFound)
			return
		}

		// 返回单个商家信息
		result.OK(c.Ctx, merchants[0])
		return
	}

	// 查询商家列表，支持全局分类筛选
	merchants, total, err := c.foodService.ListMerchants(keyword, page, pageSize, globalCategoryID, operationStatus)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回商家列表
	result.OKWithPagination(c.Ctx, merchants, total, page, pageSize)
}

// GetMerchantDetail 获取商家详情信息
// @router /api/v1/user/takeout/merchants/:id [get]
func (c *TakeoutFoodController) GetMerchantDetail() {
	// 获取商家ID
	idStr := c.Ctx.Input.Param(":id")
	logs.Info("GetMerchantDetail - 接收到的商家ID参数: %s", idStr)

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		logs.Error("GetMerchantDetail - 商家ID解析失败: %s, 错误: %v", idStr, err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的商家ID"))
		return
	}

	// 调用服务获取商家详情
	merchantDetail, err := c.foodService.GetMerchantDetail(id)
	if err != nil {
		logs.Error("获取商家详情失败: %v, 商家ID: %d", err, id)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回商家详情
	result.OK(c.Ctx, merchantDetail)
}

// 通过传入商家的id数组，返回对应商家的促销和优惠卷信息详情数组

// MerchantPromotionRequest 商家促销请求结构体
// 支持字符串数字数组自动转换为int64数组
type MerchantPromotionRequest struct {
	MerchantIDs FlexibleInt64Array `json:"merchant_ids" valid:"required"` // 商家ID数组
}

// FlexibleInt64Array 灵活的int64数组类型，支持字符串数字自动转换
type FlexibleInt64Array []int64

// UnmarshalJSON 自定义JSON解析方法，支持字符串数字数组转换
func (f *FlexibleInt64Array) UnmarshalJSON(data []byte) error {
	// 尝试解析为字符串数组
	var strArray []string
	if err := json.Unmarshal(data, &strArray); err == nil {
		// 成功解析为字符串数组，转换为int64数组
		result := make([]int64, 0, len(strArray))
		for _, str := range strArray {
			if id, err := strconv.ParseInt(str, 10, 64); err == nil {
				result = append(result, id)
			} else {
				logs.Warn("无法将字符串 %s 转换为int64: %v", str, err)
				return fmt.Errorf("无效的商家ID: %s", str)
			}
		}
		*f = FlexibleInt64Array(result)
		return nil
	}

	// 尝试解析为int64数组
	var intArray []int64
	if err := json.Unmarshal(data, &intArray); err == nil {
		*f = FlexibleInt64Array(intArray)
		return nil
	}

	// 都失败了，返回错误
	return fmt.Errorf("merchant_ids必须是字符串数组或数字数组")
}

// GetMerchantsPromotionsAndCoupons 获取用户在指定商家的促销和优惠券信息
// @router /api/v1/user/takeout/merchants/promotions-coupons [post]
func (c *TakeoutFoodController) GetMerchantsPromotionsAndCoupons() {
	// 从上下文中获取已验证的用户ID，由auth中间件设置
	userID := int64(0)

	// 首先检查上下文中是否存在"userID"
	// 添加调试日志
	logs.Info("[GetMerchantsPromotionsAndCoupons] 开始获取用户ID")

	// 检查Input是否为nil
	if c.Ctx != nil && c.Ctx.Input != nil {
		logs.Info("[GetMerchantsPromotionsAndCoupons] 上下文已初始化")

		// 直接检查userID是否存在
		logs.Info("[GetMerchantsPromotionsAndCoupons] 尝试获取userID")

		// 从beego上下文中获取"userID"
		userIDValue := c.Ctx.Input.GetData("userID")
		if userIDValue != nil {
			logs.Info("[GetMerchantsPromotionsAndCoupons] 获取到userID值: %v, 类型: %T", userIDValue, userIDValue)

			// 使用类型断言获取int64类型的userID
			switch v := userIDValue.(type) {
			case int64:
				userID = v
			case int:
				userID = int64(v)
			case float64:
				userID = int64(v)
			case string:
				if id, err := strconv.ParseInt(v, 10, 64); err == nil {
					userID = id
				}
			}
		} else {
			logs.Info("[GetMerchantsPromotionsAndCoupons] 上下文中未找到userID")
		}
	} else {
		logs.Error("[GetMerchantsPromotionsAndCoupons] 上下文对象为nil")
	}

	// 如果无法获取用户ID，则视为游客
	if userID == 0 {
		logs.Info("[GetMerchantsPromotionsAndCoupons] 获取用户ID失败或用户未登录，以游客身份继续。")
	}

	// 增加调试日志，以确认userID的获取情况
	logs.Info("[Debug] GetMerchantsPromotionsAndCoupons - UserID: %d", userID)

	// 解析请求体
	var req MerchantPromotionRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails(err.Error()))
		return
	}

	// 转换为[]int64类型
	merchantIDs := []int64(req.MerchantIDs)

	// 调用服务获取用户在指定商家的促销和优惠券信息
	// 服务层需要能处理userID为0的情况（即游客）
	merchantInfos, err := c.foodService.GetMerchantsPromotionsAndCoupons(userID, merchantIDs)
	if err != nil {
		logs.Error("获取用户商家促销和优惠券信息失败: %v, 商家IDs: %v", err, merchantIDs)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, merchantInfos)
}

// GetMerchantAvailableCoupons 获取商家可领取的优惠券和促销信息
// @router /api/v1/user/takeout/merchants/:merchant_id/available-coupons [get]
func (c *TakeoutFoodController) GetMerchantAvailableCoupons() {
	// 获取商家ID
	merchantIDStr := c.Ctx.Input.Param(":merchant_id")
	logs.Info("GetMerchantAvailableCoupons - 接收到的商家ID参数: %s", merchantIDStr)

	merchantID, err := strconv.ParseInt(merchantIDStr, 10, 64)
	if err != nil || merchantID <= 0 {
		logs.Error("GetMerchantAvailableCoupons - 商家ID解析失败: %s, 错误: %v", merchantIDStr, err)
		result.HandleError(c.Ctx, result.ErrInvalidParams.WithDetails("无效的商家ID"))
		return
	}

	// 调用服务获取商家可领取的优惠券和促销信息
	merchantInfo, err := c.foodService.GetMerchantAvailableCoupons(merchantID)
	if err != nil {
		logs.Error("获取商家可领取优惠券信息失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回结果
	result.OK(c.Ctx, merchantInfo)
}

// GetMerchantFoods 获取指定商家的食品列表
// @router /api/takeout/merchants/:id/foods [get]
func (c *TakeoutFoodController) GetMerchantFoods() {
	// 获取商家ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取查询参数
	categoryID, _ := c.GetInt64("category_id", 0)
	keyword := c.GetString("keyword", "")
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 查询指定商家的食品列表
	response, err := c.foodService.GetFoodsByMerchantID(id, categoryID, keyword, page, pageSize)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回食品列表
	result.OKWithPagination(c.Ctx, response.List, int64(response.Total), page, pageSize)
}

// GetDetail 查询食品详情
// @router /api/takeout/foods/:id [get]
func (c *TakeoutFoodController) GetDetail() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品详情
	food, err := c.foodService.GetFoodByID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回食品详情
	result.OK(c.Ctx, food)
}

// GetVariants 查询食品规格
// @router /api/takeout/foods/:id/variants [get]
func (c *TakeoutFoodController) GetVariants() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询食品规格
	// 创建变体服务实例
	variantService := services.NewTakeoutVariantService()
	variants, err := variantService.ListVariantsByFoodID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回食品规格
	result.OK(c.Ctx, variants)
}

// GetComboItems 查询套餐选项
// @router /api/takeout/foods/:id/combo-items [get]
func (c *TakeoutFoodController) GetComboItems() {
	// 获取食品ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 查询套餐选项
	comboItems, err := c.comboService.ListComboItemsByFoodID(id)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回套餐选项
	result.OK(c.Ctx, comboItems)
}

// CheckXSRFCookie 实现控制器接口
func (c *TakeoutFoodController) CheckXSRFCookie() bool {
	// 食品查询API不需要XSRF令牌
	return false
}
