/**
 * 外卖商家订单控制器
 *
 * 本文件实现了外卖商家管理订单的API接口，处理接单、拒单、分配配送等商家操作。
 * 负责接收商家请求，调用服务层处理业务逻辑，并返回处理结果。
 *
 * 重构内容：
 * 1. 添加统一的请求解析方法
 * 2. 标准化错误处理和响应格式
 */

package controllers

import (
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils/common"
)

// TakeoutMerchantOrderController 外卖商家订单控制器
type TakeoutMerchantOrderController struct {
	web.Controller
	orderService services.TakeoutOrderService
}

// Prepare 控制器预处理
func (c *TakeoutMerchantOrderController) Prepare() {
	c.orderService = services.NewTakeoutOrderService()
}

// CheckXSRFCookie 实现控制器接口
func (c *TakeoutMerchantOrderController) CheckXSRFCookie() bool {
	// 商家API不需要XSRF令牌
	return false
}

// ParseRequest 解析请求数据
func (c *TakeoutMerchantOrderController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetMerchantOrderStatistics 获取商家订单统计
// @router /merchant/takeout/orders/statistics [get]
func (c *TakeoutMerchantOrderController) GetMerchantOrderStatistics() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取查询时间范围
	startDateStr := c.GetString("start_date")
	endDateStr := c.GetString("end_date")

	// 默认查询最近30天
	startDate := time.Now().AddDate(0, 0, -30)
	endDate := time.Now()

	// 解析时间参数
	if startDateStr != "" {
		parsedDate, err := time.Parse("2006-01-02", startDateStr)
		if err == nil {
			startDate = parsedDate
		}
	}

	if endDateStr != "" {
		parsedDate, err := time.Parse("2006-01-02", endDateStr)
		if err == nil {
			// 设置为当天结束时间
			endDate = parsedDate.Add(24*time.Hour - time.Second)
		}
	}

	// 调用服务获取统计数据
	statistics, err := c.orderService.GetOrderStatistics(startDate, endDate)
	if err != nil {
		logs.Error("获取商家订单统计失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, statistics)
}

// AcceptOrder 商家接单
// @router /merchant/takeout/orders/accept [post]
func (c *TakeoutMerchantOrderController) AcceptOrder() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req struct {
		OrderID int64 `json:"order_id"`
	}
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	if req.OrderID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务接单
	err = c.orderService.AcceptOrder(req.OrderID, merchantID)
	if err != nil {
		logs.Error("商家接单失败: %v, 订单ID: %d, 商家ID: %d", err, req.OrderID, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, nil)
}

// AssignDelivery 分配配送员
// @router /merchant/takeout/orders/assign [post]
func (c *TakeoutMerchantOrderController) AssignDelivery() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 解析请求参数
	var req struct {
		OrderID         int64 `json:"order_id"`
		DeliveryStaffID int64 `json:"delivery_staff_id"`
	}
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	if req.OrderID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	if req.DeliveryStaffID <= 0 {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 调用服务分配配送员
	err = c.orderService.AssignDelivery(req.OrderID, req.DeliveryStaffID, merchantID)
	if err != nil {
		logs.Error("分配配送员失败: %v, 订单ID: %d, 配送员ID: %d, 商家ID: %d",
			err, req.OrderID, req.DeliveryStaffID, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, nil)
}

// CancelOrder 商家取消订单
// @router /merchant/takeout/orders/:id/cancel [post]
func (c *TakeoutMerchantOrderController) CancelOrder() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取订单ID
	orderIDStr := c.Ctx.Input.Param(":id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil || orderID <= 0 {
		logs.Error("无效的订单ID: %s", orderIDStr)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var req struct {
		Reason string `json:"reason"`
	}
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 参数校验
	if req.Reason == "" {
		result.HandleError(c.Ctx, result.NewError(400, "取消原因不能为空"))
		return
	}

	// 调用服务取消订单
	err = c.orderService.MerchantCancelOrder(orderID, merchantID, req.Reason)
	if err != nil {
		logs.Error("商家取消订单失败: %v, 订单ID: %d, 商家ID: %d", err, orderID, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, nil)
}

// ProcessRefund 商家处理退款申请
// @Title 商家处理退款申请
// @Description 商家同意或拒绝用户的退款申请
// @Param body body dto.MerchantProcessRefundRequest true "退款处理请求"
// @Success 200 {object} dto.Response{data=dto.MerchantProcessRefundResponse} 成功返回处理结果
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 403 {object} dto.Response 无权限处理
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /refund/process [post]
func (c *TakeoutMerchantOrderController) ProcessRefund() {
	logs.Info("[商家处理退款] 开始处理退款申请")

	// 解析请求参数
	var req dto.MerchantProcessRefundRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("[商家处理退款] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 验证必填参数
	if req.RefundID == "" {
		result.HandleError(c.Ctx, result.NewError(400, "退款ID不能为空"))
		return
	}

	if req.Action == "" {
		result.HandleError(c.Ctx, result.NewError(400, "操作类型不能为空"))
		return
	}

	if req.Action != "approve" && req.Action != "reject" {
		result.HandleError(c.Ctx, result.NewError(400, "操作类型只能是approve或reject"))
		return
	}

	if req.Action == "reject" && req.ProcessRemark == "" {
		result.HandleError(c.Ctx, result.NewError(400, "拒绝退款时必须填写拒绝理由"))
		return
	}

	// 获取商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("[商家处理退款] 获取商家ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 调用服务处理退款
	response, err := c.orderService.MerchantProcessRefund(merchantID, &req)
	if err != nil {
		logs.Error("[商家处理退款] 处理失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("[商家处理退款] 处理成功 - 退款ID: %s, 操作: %s", req.RefundID, req.Action)
	result.OK(c.Ctx, response)
}

// ListMerchantOrders 获取商家订单列表
// @router /merchant/takeout/orders/list [get]
func (c *TakeoutMerchantOrderController) ListMerchantOrders() {
	// 获取当前商家ID
	merchantID, err := auth.GetMerchantIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	if merchantID <= 0 {
		result.HandleError(c.Ctx, result.ErrUnauthorized)
		return
	}

	// 获取查询参数
	status, _ := c.GetInt("status", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)

	// 参数校验
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 50 {
		pageSize = 10
	}

	// 此处应该调用商家订单列表服务
	// 由于示例需要，这里复用了用户订单列表方法
	// 实际实现中应该有专门的商家订单列表方法
	response, err := c.orderService.ListOrdersByUserID(merchantID, status, page, pageSize)
	if err != nil {
		logs.Error("获取商家订单列表失败: %v, 商家ID: %d", err, merchantID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 计算总数
	orderList := response.List
	total := response.Total

	// 返回订单列表
	result.OKWithPagination(c.Ctx, orderList, int64(total), page, pageSize)
}
