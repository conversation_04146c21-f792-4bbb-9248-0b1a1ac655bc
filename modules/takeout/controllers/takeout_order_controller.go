/**
 * 外卖订单控制器
 *
 * 本文件实现了外卖订单相关的API接口，处理订单创建、查询、状态更新等HTTP请求。
 * 负责接收用户请求参数，调用服务层处理业务逻辑，并返回处理结果。
 *
 * 支持外卖订单项的特殊字段处理和商品快照功能，确保订单数据一致性。
 */

package controllers

import (
	"context"
	"encoding/json"
	"o_mall_backend/utils/common"

	"github.com/beego/beego/v2/core/logs"

	//"time"
	"o_mall_backend/common/auth"
	"o_mall_backend/common/controllers"
	"o_mall_backend/common/result"
	merchantRepos "o_mall_backend/modules/merchant/repositories"
	merchantServices "o_mall_backend/modules/merchant/services"
	orderModels "o_mall_backend/modules/order/models"
	orderRepositories "o_mall_backend/modules/order/repositories"
	orderServices "o_mall_backend/modules/order/services"
	paymentModels "o_mall_backend/modules/payment/models"
	systemServicesImpl "o_mall_backend/modules/system/services/impl"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/repositories"
	"o_mall_backend/modules/takeout/services"
	userServices "o_mall_backend/modules/user/services"
)

// merchantServiceAdapter 商家服务适配器
type merchantServiceAdapter struct {
	merchantService merchantServices.MerchantService
}

// GetMerchantByID 实现接口方法
func (m *merchantServiceAdapter) GetMerchantByID(ctx context.Context, id int64) (interface{}, error) {
	return m.merchantService.GetMerchantByID(ctx, id)
}

// TakeoutOrderController 外卖订单控制器
type TakeoutOrderController struct {
	controllers.BaseController
	orderService              services.TakeoutOrderService
	multiMerchantOrderService services.MultiMerchantOrderService
}

// Prepare 控制器预处理
func (c *TakeoutOrderController) Prepare() {
	c.BaseController.Prepare()
	c.orderService = services.NewTakeoutOrderService()
	c.multiMerchantOrderService = services.NewMultiMerchantOrderService(
		orderServices.NewOrderService(),
		c.orderService,
		services.NewTakeoutCartService(),
		userServices.NewAddressService(),
		repositories.NewTakeoutFoodRepository(),
		repositories.NewTakeoutVariantRepository(),
		repositories.NewTakeoutOrderRepository(),
		orderRepositories.NewOrderRepository(),
		systemServicesImpl.NewSystemConfigServiceImpl(),
		services.NewTakeoutPromotionService(
			repositories.NewTakeoutPromotionRepository(),
			repositories.NewTakeoutFoodPromotionRepository(),
			repositories.NewTakeoutFoodRepository(),
			repositories.NewTakeoutCouponRepository(),
			repositories.NewTakeoutUserCouponRepository(),
			repositories.NewTakeoutUserPromotionRepository(),
		),
		services.NewTakeoutCouponService(
			repositories.NewTakeoutCouponRepository(),
			repositories.NewTakeoutUserCouponRepository(),
			&merchantRepos.MerchantRepositoryImpl{},
		),
		&merchantServiceAdapter{merchantService: merchantServices.NewMerchantService()}, // 添加商家服务适配器
	)
}

// ParseRequest 解析请求数据
func (c *TakeoutOrderController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// CreateOrder 创建订单
// @Title 创建外卖订单（支持多商家）
// @Description 从购物车创建外卖订单，自动按商家拆分为多个订单
// @Param body body dto.CreateTakeoutOrderRequest true "创建订单请求参数"
// @Success 200 {object} []dto.TakeoutOrderDTO "订单信息列表"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /create [post]
func (c *TakeoutOrderController) CreateOrder() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 记录当前请求的用户ID
	logs.Info("开始创建多商家外卖订单，用户ID: %d", userID)

	// 记录原始请求参数用于调试
	logs.Info("原始请求参数: %s", string(c.Ctx.Input.RequestBody))

	// 解析请求参数
	var req dto.CreateTakeoutOrderRequest
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "参数错误"))
		return
	}

	// 记录请求的商家订单信息
	logs.Info("创建多商家订单，商家数量: %d, 订单项类型: %s", len(req.MerchantOrders), orderModels.ItemTypeTakeout)

	// 详细记录每个商家订单的促销信息
	for i, merchantOrder := range req.MerchantOrders {
		logs.Info("商家订单[%d] - 商家ID: %d, 优惠券ID: %d, 单个促销ID: %d, 促销ID数组: %v",
			i, merchantOrder.MerchantID, merchantOrder.CouponID, merchantOrder.PromotionID, merchantOrder.PromotionIDs)
	}

	// 参数校验
	if req.TakeoutAddressID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "配送地址不能为空"))
		return
	}

	if len(req.MerchantOrders) == 0 {
		result.HandleError(c.Ctx, result.NewError(400, "购物车不能为空"))
		return
	}

	// 校验每个商家订单的购物车项
	for _, merchantOrder := range req.MerchantOrders {
		if len(merchantOrder.CartItemIDs) == 0 {
			result.HandleError(c.Ctx, result.NewError(400, "商家购物车项不能为空"))
			return
		}
	}

	// 调用多商家订单服务创建订单
	orders, err := c.multiMerchantOrderService.CreateOrdersFromCart(userID, &req)
	if err != nil {
		logs.Error("创建多商家订单失败: %v, 用户ID: %d", err, userID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 创建成功，记录订单信息
	logs.Info("多商家外卖订单创建成功，用户ID: %d, 订单数量: %d", userID, len(orders))
	result.OK(c.Ctx, orders)
}

// GetOrderPayment 获取订单支付信息
// @Title 获取订单支付信息
// @Description 获取外卖订单的支付信息
// @Param orderID path int true "订单ID"
// @Success 200 {object} dto.TakeoutOrderPaymentDTO "订单支付信息"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /payment/:orderID [get]
func (c *TakeoutOrderController) GetOrderPayment() {
	// 获取订单ID
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}

	// 获取支付信息
	payment, err := c.orderService.GetOrderPaymentInfo(orderID)
	if err != nil {
		logs.Error("获取订单支付信息失败: %v, 订单ID: %d", err, orderID)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("获取外卖订单支付信息，订单ID: %d, 订单项类型: %s", orderID, orderModels.ItemTypeTakeout)
	result.OK(c.Ctx, payment)
}

// GetOrderDetail 获取订单详情
// @Title 获取订单详情
// @Description 获取外卖订单详情信息
// @Param orderID path int true "订单ID"
// @Success 200 {object} dto.TakeoutOrderDTO "订单详情"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /detail/:orderID [get]
func (c *TakeoutOrderController) GetOrderDetail() {
	// 获取订单ID
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}

	// 获取订单详情
	order, err := c.orderService.GetOrderByID(orderID)
	if err != nil {
		logs.Error("获取订单详情失败: %v, 订单ID: %d", err, orderID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 日志记录订单类型为外卖类型
	logs.Info("获取到外卖订单详情，订单ID: %d, 订单项类型: %s", orderID, orderModels.ItemTypeTakeout)

	result.OK(c.Ctx, order)
}

// ListUserOrders 获取用户订单列表
// @Title 获取用户订单列表
// @Description 获取当前用户的外卖订单列表
// @Param status query int false "订单状态，0表示全部"
// @Param page query int false "页码，默认1"
// @Param pageSize query int false "每页数量，默认10"
// @Success 200 {object} dto.TakeoutOrderListDTO "订单列表"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /list [get]
func (c *TakeoutOrderController) ListUserOrders() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取查询参数
	status, _ := c.GetInt("status", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("pageSize", 10)

	// 参数校验
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 50 {
		pageSize = 10
	}

	// 调用服务获取订单列表
	orderList, err := c.orderService.ListOrdersByUserID(userID, status, page, pageSize)
	if err != nil {
		logs.Error("获取用户订单列表失败: %v, 用户ID: %d", err, userID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回分页结果
	result.OK(c.Ctx, orderList)
}

// CountUserOrders 统计用户订单数量
// @Title 统计用户订单数量
// @Description 统计当前用户的外卖订单数量
// @Param status query int false "订单状态，0表示全部"
// @Success 200 {object} controllers.Response "订单数量"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /count [get]
func (c *TakeoutOrderController) CountUserOrders() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取状态参数
	status, _ := c.GetInt("status", 0)

	// 调用服务统计订单数量
	count, err := c.orderService.CountOrdersByUserID(userID, status)
	if err != nil {
		logs.Error("统计用户订单数量失败: %v, 用户ID: %d", err, userID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, map[string]int64{"count": int64(count)})
}

// CancelOrder 取消订单
// @Title 取消订单
// @Description 用户取消外卖订单
// @Param orderID path int true "订单ID"
// @Param body body dto.CancelOrderRequest true "取消订单请求参数"
// @Success 200 {object} controllers.Response "操作结果"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /cancel/:orderID [post]
func (c *TakeoutOrderController) CancelOrder() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 从URL获取订单ID
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}

	// 解析请求参数
	var req struct {
		Reason string `json:"reason"`
	}
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "参数错误"))
		return
	}

	// 调用服务取消订单
	err = c.orderService.CancelOrder(orderID, userID, req.Reason)
	if err != nil {
		logs.Error("取消订单失败: %v, 订单ID: %d, 用户ID: %d", err, orderID, userID)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, nil)
}

// RateOrder 评价订单
// @Title 评价订单
// @Description 用户评价已完成的外卖订单
// @Param orderID path int true "订单ID"
// @Param body body dto.RateTakeoutOrderRequest true "评价订单请求参数"
// @Success 200 {object} controllers.Response "操作结果"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /rate/:orderID [post]
func (c *TakeoutOrderController) RateOrder() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 解析请求参数
	var req dto.RateTakeoutOrderRequest
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "参数错误"))
		return
	}

	// 获取路径参数中的订单ID
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}

	// 参数校验
	if req.Rating < 1 || req.Rating > 5 {
		result.HandleError(c.Ctx, result.NewError(400, "评分必须在1-5之间"))
		return
	}

	if req.DeliveryRating < 1 || req.DeliveryRating > 5 {
		result.HandleError(c.Ctx, result.NewError(400, "配送评分必须在1-5之间"))
		return
	}

	// 调用服务评价订单
	err = c.orderService.RateOrder(orderID, userID, &req)
	if err != nil {
		logs.Error("评价订单失败: %v, 订单ID: %d, 用户ID: %d", err, orderID, userID)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("成功评价外卖订单，订单ID: %d, 用户ID: %d, 订单项类型: %s", orderID, userID, orderModels.ItemTypeTakeout)
	result.OK(c.Ctx, nil)
}

// CreatePayment 创建订单支付
// @Title 创建订单支付
// @Description 为外卖订单创建支付记录
// @Param orderID path int true "订单ID"
// @Param body body dto.CreateTakeoutPaymentRequest true "创建支付请求参数"
// @Success 200 {object} dto.TakeoutPaymentResponse "支付信息"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /pay/:orderID/create [post]
func (c *TakeoutOrderController) CreatePayment() {
	logs.Info("[外卖订单控制器] 收到创建支付请求")

	// 获取当前用户ID
	logs.Info("[外卖订单控制器] 步骤1: 获取用户ID")
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("[外卖订单控制器] 获取用户ID失败")
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("[外卖订单控制器] 用户ID获取成功: %v", userID)

	// 获取订单ID
	logs.Info("[外卖订单控制器] 步骤2: 解析订单ID")
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		logs.Error("[外卖订单控制器] 订单ID格式错误: %v, 原始值: %s", err, c.Ctx.Input.Param(":orderID"))
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}
	logs.Info("[外卖订单控制器] 订单ID解析成功: %d", orderID)

	// 解析请求参数
	logs.Info("[外卖订单控制器] 步骤3: 解析请求参数")
	var req dto.CreateTakeoutPaymentRequest

	// 输出原始请求参数进行调试
	logs.Info("[外卖订单控制器] 原始请求体: %s", string(c.Ctx.Input.RequestBody))

	// 使用 ParseRequest 方法解析前端参数
	err = c.ParseRequest(&req)
	if err != nil {
		logs.Error("[外卖订单控制器] 解析支付请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "参数错误"))
		return
	}

	// 打印解析后的请求参数
	logs.Info("[外卖订单控制器] 请求参数解析完成 - 用户ID: %v, 订单ID: %d, 支付方式: %d, 客户端IP: %s",
		userID, orderID, req.PaymentMethod, c.Ctx.Request.RemoteAddr)

	// 如果是字符串类型的支付方式，进行转换
	if req.PaymentMethod <= 0 {
		// 尝试解析原始请求体，查找 payment_method 字段
		var rawRequest map[string]interface{}
		err = json.Unmarshal(c.Ctx.Input.RequestBody, &rawRequest)
		if err == nil {
			// 检查前端是否传递了字符串类型的 payment_method
			if paymentMethodStr, ok := rawRequest["payment_method"].(string); ok {
				logs.Info("发现字符串类型的支付方式: %s", paymentMethodStr)
				// 将字符串类型的支付方式转换为对应的枚举值
				switch paymentMethodStr {
				case "wechat":
					req.PaymentMethod = int(paymentModels.PaymentMethodWechat)
				case "alipay":
					req.PaymentMethod = int(paymentModels.PaymentMethodAlipay)
				case "creditcard":
					req.PaymentMethod = int(paymentModels.PaymentMethodCreditCard)
				case "bank_transfer":
					req.PaymentMethod = int(paymentModels.PaymentMethodBankTransfer)
				case "balance":
					req.PaymentMethod = int(paymentModels.PaymentMethodBalance)
					logs.Info("设置余额支付方式: %d", req.PaymentMethod)
				case "combination":
					req.PaymentMethod = int(paymentModels.PaymentMethodCombination)
				default:
					logs.Error("不支持的支付方式字符串: %s", paymentMethodStr)
					result.HandleError(c.Ctx, result.NewError(400, "不支持的支付方式"))
					return
				}
				logs.Info("转换后的支付方式枚举值: %d", req.PaymentMethod)
			}

			// 检查前端是否传递了数值类型的 payment_amount
			if paymentAmount, ok := rawRequest["payment_amount"].(float64); ok {
				logs.Info("发现数值类型的支付金额: %.2f", paymentAmount)
				// 设置支付金额，这里不需要做任何转换，因为服务层会从订单中获取正确的金额
			}
		}

		// 如果仍然没有支付方式，返回错误
		if req.PaymentMethod <= 0 {
			logs.Error("支付方式为空或无效")
			result.HandleError(c.Ctx, result.NewError(400, "支付方式不能为空"))
			return
		}
	}

	// 设置订单ID
	req.OrderID = orderID

	if req.PaymentMethod <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "支付方式不能为空"))
		return
	}

	// 调用服务创建支付
	logs.Info("[外卖订单控制器] 步骤5: 调用服务层创建支付 - 用户ID: %d, 订单ID: %d", userID, orderID)
	paymentResp, err := c.orderService.CreateOrderPayment(userID, &req)
	if err != nil {
		logs.Error("[外卖订单控制器] 创建支付失败: %v, 用户ID: %d, 订单ID: %d", err, userID, orderID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	logs.Info("[外卖订单控制器] 支付创建成功，返回响应 - 用户ID: %d, 订单ID: %d, 支付ID: %d",
		userID, orderID, paymentResp.PaymentID)
	result.OK(c.Ctx, paymentResp)
}

// QueryPayment 查询订单支付状态
// @Title 查询订单支付状态
// @Description 查询外卖订单的支付状态
// @Param orderID path int true "订单ID"
// @Success 200 {object} dto.TakeoutPaymentStatusResponse "支付状态信息"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /pay/query/:orderID [get]
func (c *TakeoutOrderController) QueryPayment() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取订单ID
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}

	// 调用服务查询支付状态
	paymentStatus, err := c.orderService.QueryOrderPaymentStatus(userID, orderID)
	if err != nil {
		logs.Error("查询订单支付状态失败: %v, 订单ID: %d, 用户ID: %d", err, orderID, userID)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("成功查询外卖订单支付状态，订单ID: %d, 用户ID: %d, 支付状态: %d", orderID, userID, paymentStatus.PaymentStatus)
	result.OK(c.Ctx, paymentStatus)
}

// ClosePayment 关闭订单支付
// @Title 关闭订单支付
// @Description 关闭外卖订单的支付记录
// @Param orderID path int true "订单ID"
// @Param body body dto.CloseTakeoutPaymentRequest true "关闭支付请求参数"
// @Success 200 {object} controllers.Response "关闭结果"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /pay/close/:orderID [post]
func (c *TakeoutOrderController) ClosePayment() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取订单ID
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}

	// 解析请求参数
	var req dto.CloseTakeoutPaymentRequest
	err = json.Unmarshal(c.Ctx.Input.RequestBody, &req)
	if err != nil {
		logs.Error("解析关闭支付请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "参数错误"))
		return
	}

	// 设置订单ID
	req.OrderID = orderID

	// 调用服务关闭支付
	success, err := c.orderService.CloseOrderPayment(userID, &req)
	if err != nil {
		logs.Error("关闭订单支付失败: %v, 订单ID: %d, 用户ID: %d", err, orderID, userID)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Info("成功关闭外卖订单支付，订单ID: %d, 用户ID: %d, 结果: %t", orderID, userID, success)
	result.OK(c.Ctx, map[string]bool{"success": success})
}

// Options 处理OPTIONS预检请求
// @Summary 处理OPTIONS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Tags 外卖订单
// @Accept json
// @Produce json
// @Success 200 {string} string "OK"
// @Router /api/v1/user/takeout/order/create [options]
// @Router /api/v1/user/takeout/order/payment/{orderID} [options]
// @Router /api/v1/user/takeout/order/detail/{orderID} [options]
// @Router /api/v1/user/takeout/order/list [options]
// @Router /api/v1/user/takeout/order/count [options]
// @Router /api/v1/user/takeout/order/cancel/{orderID} [options]
// @Router /api/v1/user/takeout/order/rate [options]
// @Router /api/v1/user/takeout/order/pay/{orderID}/create [options]
// @Router /api/v1/user/takeout/order/pay/query/{orderID} [options]
// @Router /api/v1/user/takeout/order/pay/close/{orderID} [options]
func (c *TakeoutOrderController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}

// CreateBatchPayment 创建多订单合并支付
// @Title 创建多订单合并支付
// @Description 为多个外卖订单创建合并支付
// @Param body body dto.CreateBatchTakeoutPaymentRequest true "合并支付请求参数"
// @Success 200 {object} dto.BatchTakeoutPaymentResponse "支付信息"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /pay/batch/create [post]
func (c *TakeoutOrderController) CreateBatchPayment() {
	logs.Info("[外卖订单控制器] 收到创建多订单合并支付请求")

	// 获取当前用户ID
	logs.Info("[外卖订单控制器] 步骤1: 获取用户ID")
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("[外卖订单控制器] 获取用户ID失败")
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("[外卖订单控制器] 用户ID获取成功: %v", userID)

	// 解析请求参数
	logs.Info("[外卖订单控制器] 步骤2: 解析请求参数")
	var req dto.CreateBatchTakeoutPaymentRequest

	// 输出原始请求参数进行调试
	logs.Info("[外卖订单控制器] 原始请求体: %s", string(c.Ctx.Input.RequestBody))

	// 使用 ParseRequest 方法解析前端参数
	err = c.ParseRequest(&req)
	if err != nil {
		logs.Error("[外卖订单控制器] 解析合并支付请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "参数错误"))
		return
	}

	// 打印解析后的请求参数
	logs.Info("[外卖订单控制器] 请求参数解析完成 - 用户ID: %v, 订单数量: %d, 支付方式: %d, 客户端IP: %s",
		userID, len(req.OrderIDs), req.PaymentMethod, c.Ctx.Request.RemoteAddr)

	// 参数校验
	if len(req.OrderIDs) == 0 {
		logs.Error("[外卖订单控制器] 订单ID列表不能为空")
		result.HandleError(c.Ctx, result.NewError(400, "订单ID列表不能为空"))
		return
	}

	if req.PaymentMethod <= 0 {
		logs.Error("[外卖订单控制器] 支付方式不能为空")
		result.HandleError(c.Ctx, result.NewError(400, "支付方式不能为空"))
		return
	}

	// 调用服务创建合并支付
	logs.Info("[外卖订单控制器] 步骤3: 调用服务层创建合并支付 - 用户ID: %d, 订单数量: %d", userID, len(req.OrderIDs))
	paymentResp, err := c.orderService.CreateBatchOrderPayment(userID, &req)
	if err != nil {
		logs.Error("[外卖订单控制器] 创建合并支付失败: %v, 用户ID: %d, 订单数量: %d", err, userID, len(req.OrderIDs))
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	logs.Info("[外卖订单控制器] 合并支付创建成功，返回响应 - 用户ID: %d, 批量支付ID: %d, 总金额: %.2f",
		userID, paymentResp.BatchPaymentID, paymentResp.TotalAmount)
	result.OK(c.Ctx, paymentResp)
}

// ApplyRefund 申请退款
// @Title 申请订单退款
// @Description 用户申请外卖订单退款
// @Param orderID path int true "订单ID"
// @Param body body dto.ApplyRefundRequest true "退款申请参数"
// @Success 200 {object} dto.RefundResponse "退款申请信息"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /:orderID/refund [post]
func (c *TakeoutOrderController) ApplyRefund() {
	logs.Info("[外卖订单控制器] 收到申请退款请求")

	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		logs.Error("[外卖订单控制器] 获取用户ID失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 获取订单ID
	orderID, err := c.GetInt64(":orderID")
	if err != nil || orderID <= 0 {
		logs.Error("[外卖订单控制器] 订单ID无效: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
		return
	}

	// 解析请求参数
	var req dto.ApplyRefundRequest
	err = c.ParseRequest(&req)
	if err != nil {
		logs.Error("[外卖订单控制器] 解析退款申请参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "参数错误"))
		return
	}

	// 设置订单ID
	req.OrderID = orderID

	// 参数校验
	if req.Reason == "" {
		logs.Error("[外卖订单控制器] 退款原因不能为空")
		result.HandleError(c.Ctx, result.NewError(400, "退款原因不能为空"))
		return
	}

	logs.Info("[外卖订单控制器] 申请退款参数 - 用户ID: %d, 订单ID: %d, 退款原因: %s", userID, orderID, req.Reason)

	// 调用服务申请退款
	refundResp, err := c.orderService.ApplyRefund(userID, &req)
	if err != nil {
		logs.Error("[外卖订单控制器] 申请退款失败: %v, 用户ID: %d, 订单ID: %d", err, userID, orderID)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	logs.Info("[外卖订单控制器] 退款申请成功，返回响应 - 用户ID: %d, 订单ID: %d, 退款ID: %d",
		userID, orderID, refundResp.RefundID)
	result.OK(c.Ctx, refundResp)
}
