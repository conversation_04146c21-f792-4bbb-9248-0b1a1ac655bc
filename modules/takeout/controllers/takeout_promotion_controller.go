/**
 * 外卖模块 - 用户促销活动控制器
 * 描述：实现用户查询商家促销活动相关的接口
 * 作者：系统
 * 创建时间：2025-07-02
 * 更新时间：2025-07-02
 */

package controllers

import (
	"o_mall_backend/common/result"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/repositories"
	"o_mall_backend/modules/takeout/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// TakeoutPromotionController 用户促销活动控制器
type TakeoutPromotionController struct {
	web.Controller
	promotionService services.ITakeoutPromotionService
}

// Prepare 初始化
func (c *TakeoutPromotionController) Prepare() {
	// 创建服务实例
	c.promotionService = services.NewTakeoutPromotionService(
		repositories.NewTakeoutPromotionRepository(),
		repositories.NewTakeoutFoodPromotionRepository(),
		repositories.NewTakeoutFoodRepository(),
		repositories.NewTakeoutCouponRepository(),
		repositories.NewTakeoutUserCouponRepository(),
		repositories.NewTakeoutUserPromotionRepository(),
	)
}

// ParseRequest 通用请求参数解析方法
func (c *TakeoutPromotionController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetPromotionsByConditions 根据条件获取商家促销活动
// POST /api/v1/takeout/merchant/:merchant_id/promotions
func (c *TakeoutPromotionController) GetPromotionsByConditions() {
	// 获取merchant_id路径参数
	merchantIDStr := c.Ctx.Input.Param(":merchant_id")
	merchantID := common.ParseInt64(merchantIDStr, 0)
	if merchantID <= 0 {
		logs.Error("无效的商家ID: %s", merchantIDStr)
		result.HandleError(c.Ctx, result.NewError(400, "无效的商家ID"))
		return
	}

	// 解析请求体
	req := struct {
		TotalAmount float64 `json:"total_amount"` // 订单总金额
		FoodIDs     string  `json:"food_ids"`     // 商品ID列表，逗号分隔
	}{}

	if err := c.ParseRequest(&req); err != nil {
		logs.Error("解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(400, "请求参数错误"))
		return
	}

	// 解析商品ID列表
	var foodIDs []int64
	if req.FoodIDs != "" {
		idStrs := strings.Split(req.FoodIDs, ",")
		for _, idStr := range idStrs {
			id := common.ParseInt64(idStr, 0)
			if id <= 0 {
				logs.Error("无效的商品ID, 原始值: %s", idStr)
				continue
			}
			foodIDs = append(foodIDs, id)
		}
	}

	// 尝试获取用户ID（可选认证）
	var userID int64 = 0
	if token := utils.GetTokenFromRequest(c.Ctx); token != "" {
		if claims, err := utils.ParseToken(token); err == nil {
			userID = claims.UserID
			logs.Info("获取到用户ID: %d", userID)
		}
	}

	// 调用服务层获取符合条件的促销活动
	promotions, err := c.promotionService.GetEligiblePromotions(merchantID, req.TotalAmount, foodIDs, userID)
	if err != nil {
		logs.Error("获取促销活动失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(500, "获取促销活动失败"))
		return
	}

	// 构建符合文档的返回格式
	response := struct {
		PromotionInfo string                  `json:"promotion_info"` // 促销信息摘要
		Promotions    []dto.PromotionResponse `json:"promotions"`     // 促销活动详情列表
	}{
		PromotionInfo: buildPromotionInfo(promotions),
		Promotions:    promotions,
	}

	// 返回结果
	result.OK(c.Ctx, response)
}

// GetUserPromotionUsage 获取用户促销使用记录（管理接口）
// @Title 获取用户促销使用记录
// @Description 获取指定用户对指定促销活动的使用记录
// @Param user_id query int64 true "用户ID"
// @Param promotion_id query int64 true "促销活动ID"
// @Success 200 {object} controllers.Response "成功"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /user-usage [get]
func (c *TakeoutPromotionController) GetUserPromotionUsage() {
	userID := common.ParseInt64(c.GetString("user_id"), 0)
	promotionID := common.ParseInt64(c.GetString("promotion_id"), 0)

	if userID <= 0 || promotionID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "用户ID和促销活动ID不能为空"))
		return
	}

	// 获取使用记录
	records, err := c.promotionService.GetUserPromotionUsage(userID, promotionID)
	if err != nil {
		logs.Error("获取用户促销使用记录失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(500, "获取用户促销使用记录失败"))
		return
	}

	// 统计使用次数
	count, err := c.promotionService.CountUserPromotionUsage(userID, promotionID)
	if err != nil {
		logs.Error("统计用户促销使用次数失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(500, "统计用户促销使用次数失败"))
		return
	}

	response := map[string]interface{}{
		"user_id":      userID,
		"promotion_id": promotionID,
		"usage_count":  count,
		"records":      records,
	}

	result.OK(c.Ctx, response)
}

// ClearUserPromotionUsage 清除用户促销使用记录（管理接口）
// @Title 清除用户促销使用记录
// @Description 清除指定用户对指定促销活动的使用记录（仅用于测试）
// @Param user_id query int64 true "用户ID"
// @Param promotion_id query int64 true "促销活动ID"
// @Success 200 {object} controllers.Response "成功"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /clear-user-usage [delete]
func (c *TakeoutPromotionController) ClearUserPromotionUsage() {
	userID := common.ParseInt64(c.GetString("user_id"), 0)
	promotionID := common.ParseInt64(c.GetString("promotion_id"), 0)

	if userID <= 0 || promotionID <= 0 {
		result.HandleError(c.Ctx, result.NewError(400, "用户ID和促销活动ID不能为空"))
		return
	}

	// 清除使用记录
	err := c.promotionService.ClearUserPromotionUsage(userID, promotionID)
	if err != nil {
		logs.Error("清除用户促销使用记录失败: %v", err)
		result.HandleError(c.Ctx, result.NewError(500, "清除用户促销使用记录失败"))
		return
	}

	response := map[string]interface{}{
		"message":      "用户促销使用记录已清除",
		"user_id":      userID,
		"promotion_id": promotionID,
	}

	result.OK(c.Ctx, response)
}

// Options 处理OPTIONS请求，用于CORS
func (c *TakeoutPromotionController) Options() {
	result.OK(c.Ctx, nil)
}

// buildPromotionInfo 构建促销信息摘要
func buildPromotionInfo(promotions []dto.PromotionResponse) string {
	if len(promotions) == 0 {
		return "无可用促销活动"
	}

	// 如果有满减活动，优先展示
	for _, p := range promotions {
		if p.Type == 4 { // 满减活动
			return "满减优惠(" + p.Name + ")"
		}
	}

	// 否则展示第一个活动
	return promotions[0].TypeText + "(" + promotions[0].Name + ")"
}
