# 外卖食品缓存Bug修复文档

## 问题描述

### Bug现象
当缓存中去掉某一个商品时，无论哪个API都无法再找到这种商品。例如在 `ListFoods` 方法中，仅仅返回了缓存中的商品，但是数据库中该商品正常，只是无法被商家和用户检索到。

### 问题根源
1. **缓存优先策略问题**：原始代码采用"缓存优先"策略，当缓存存在时直接返回，不会查询数据库
2. **缓存清理不完整**：`DeleteFoodCache` 方法只删除单个食品的缓存，没有清除包含该食品的列表缓存
3. **缓存键格式不匹配**：`ClearMerchantFoodCache` 中的模式匹配与实际缓存键格式不完全一致

## 修复方案

### 1. 修复缓存键格式匹配问题

**文件**: `takeout_food_cache_service.go`

**修改内容**:
- 修正了 `ClearMerchantFoodCache` 方法中的缓存键模式匹配
- 确保能正确清除所有相关的列表缓存

```go
// 修复前的问题模式
fmt.Sprintf("takeout:food:list:merchant:%d:*", merchantID),
fmt.Sprintf("takeout:food:merchant:%d:*", merchantID),

// 修复后的正确模式
fmt.Sprintf("takeout:food:merchant:list:%d:*", merchantID),
fmt.Sprintf("takeout:food:merchant:%d:category:*", merchantID),
```

### 2. 创建缓存验证服务

**新文件**: `takeout_food_cache_validation_service.go`

**核心功能**:
- **缓存验证机制**：在返回缓存数据前，随机抽样验证缓存数据的有效性
- **智能缓存策略**：缓存存在时先验证，验证失败则查询数据库并更新缓存
- **数据一致性保证**：确保返回的数据与数据库状态一致

**验证逻辑**:
```go
// 随机抽样验证前几个商品
for i := 0; i < sampleSize; i++ {
    food, err := s.foodRepo.GetByID(foodItem.ID)
    // 检查食品是否存在
    // 检查状态是否一致
    // 检查商家ID是否匹配
    // 检查分类ID是否匹配
}
```

### 3. 修改ListFoods方法

**文件**: `takeout_food_service.go`

**修改策略**:
- 使用新的缓存验证服务替代原来的直接缓存查询
- 确保数据一致性的同时保持缓存性能优势

```go
// 修复后的实现
func (s *takeoutFoodService) ListFoods(query *dto.TakeoutFoodQueryRequest) (*dto.TakeoutFoodResponseList, error) {
    cacheValidationService := NewTakeoutFoodCacheValidationService(s.foodRepo, s.categoryRepo, s.cacheService)
    return cacheValidationService.ValidateAndGetFoodList(query)
}
```

## 修复效果

### 解决的问题
1. ✅ **数据一致性**：确保API返回的数据与数据库状态一致
2. ✅ **缓存失效处理**：当商品被删除或修改时，相关缓存会被正确清理
3. ✅ **性能优化**：在保证数据准确性的前提下，仍然利用缓存提升性能
4. ✅ **自动修复**：当检测到缓存数据不一致时，自动清理并重新生成缓存

### 性能影响
- **缓存命中且验证通过**：性能几乎无影响（只增加少量验证开销）
- **缓存验证失败**：会查询数据库，但会自动更新缓存，后续请求受益
- **缓存未命中**：与原来逻辑相同，查询数据库并设置缓存

## 使用建议

### 1. 监控缓存验证失败率
建议添加监控来跟踪缓存验证失败的频率：
```go
logs.Warn("缓存数据验证失败，清理缓存并查询数据库, 商家ID: %d", query.MerchantID)
```

### 2. 定期清理缓存
可以使用 `ValidateCacheConsistency` 方法定期验证和清理缓存：
```go
// 在后台任务中调用
cacheValidationService.ValidateCacheConsistency(merchantID)
```

### 3. 缓存策略配置
如果需要完全依赖缓存（高性能场景），可以：
- 确保在所有商品变更操作中正确调用 `ClearMerchantFoodCache`
- 定期运行缓存一致性检查任务
- 监控缓存命中率和数据一致性指标

## 测试建议

### 1. 功能测试
- 测试商品删除后，列表API是否还能正确返回结果
- 测试商品状态变更后，缓存是否能正确更新
- 测试不同查询条件下的缓存行为

### 2. 性能测试
- 对比修复前后的API响应时间
- 测试高并发场景下的缓存表现
- 验证缓存验证机制的性能开销

### 3. 一致性测试
- 模拟缓存数据不一致的场景
- 验证自动修复机制是否正常工作
- 测试缓存清理的完整性

## 总结

这次修复从根本上解决了缓存数据不一致的问题，通过引入缓存验证机制，在保证性能的同时确保了数据的准确性。修复方案具有以下特点：

- **向后兼容**：不影响现有API接口
- **渐进式修复**：缓存验证失败时自动修复，不需要手动干预
- **性能友好**：大部分情况下性能影响很小
- **可监控**：提供了足够的日志来监控缓存状态

建议在生产环境部署后，密切监控缓存验证失败的日志，以便及时发现和解决潜在的数据一致性问题。