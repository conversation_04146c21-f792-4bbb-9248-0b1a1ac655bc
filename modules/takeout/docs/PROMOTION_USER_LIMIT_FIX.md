# 外卖促销活动用户使用次数限制功能修复

## 🐛 问题描述

在外卖模块的促销活动中，商家设置了 `per_user_limit`（每个用户限制使用次数）参数，例如：

```json
{
  "rules": "{\"coupon\":{\"name\":\"首单减免\",\"type\":1,\"amount\":5,\"min_order_amount\":5.01,\"per_user_limit\":1,\"valid_days\":30}}"
}
```

但是经过测试发现，一个用户可以不断使用同一个促销活动，`per_user_limit` 限制没有生效。

## 🔍 问题分析

通过深入分析代码，发现以下问题：

1. **缺少用户促销使用记录表**：系统中没有专门记录用户使用促销活动的表
2. **订单表缺少促销字段**：订单扩展表中没有记录使用的促销活动ID
3. **缺少用户使用次数检查逻辑**：在获取符合条件的促销活动时，没有检查用户的使用次数
4. **缺少使用记录创建逻辑**：在订单创建时，没有记录用户使用促销活动的历史

## 🛠️ 解决方案

### 1. 新增数据模型

#### 1.1 用户促销使用记录模型
```go
// TakeoutUserPromotion 用户促销使用记录模型
type TakeoutUserPromotion struct {
    ID             int64     `orm:"pk;auto;column(id)" json:"id"`
    UserID         int64     `orm:"column(user_id);index" json:"user_id"`
    PromotionID    int64     `orm:"column(promotion_id);index" json:"promotion_id"`
    OrderID        int64     `orm:"column(order_id);index" json:"order_id"`
    MerchantID     int64     `orm:"column(merchant_id);index" json:"merchant_id"`
    DiscountAmount float64   `orm:"column(discount_amount);digits(10);decimals(2)" json:"discount_amount"`
    UsedTime       time.Time `orm:"column(used_time)" json:"used_time"`
    CreatedAt      time.Time `orm:"column(created_at);auto_now_add" json:"created_at"`
    UpdatedAt      time.Time `orm:"column(updated_at);auto_now" json:"updated_at"`
}
```

#### 1.2 订单扩展模型增强
在 `TakeoutOrderExtension` 模型中添加：
```go
PromotionIDs      string  `orm:"column(promotion_ids);size(500)" json:"promotion_ids"`
PromotionDiscount float64 `orm:"column(promotion_discount);digits(10);decimals(2)" json:"promotion_discount"`
```

### 2. 新增仓储层

#### 2.1 用户促销使用记录仓储
```go
type ITakeoutUserPromotionRepository interface {
    Create(userPromotion *models.TakeoutUserPromotion) (int64, error)
    CountUserPromotionUsage(userID int64, promotionID int64) (int64, error)
    GetUserPromotionUsage(userID int64, promotionID int64) ([]*models.TakeoutUserPromotion, error)
    // ... 其他方法
}
```

### 3. 增强服务层

#### 3.1 促销服务增强
```go
// 检查用户是否可以使用促销活动
CheckUserPromotionUsage(userID int64, promotionID int64, perUserLimit int64) (bool, error)

// 记录用户使用促销活动
RecordUserPromotionUsage(userID int64, promotionID int64, orderID int64, merchantID int64, discountAmount float64) error
```

#### 3.2 GetEligiblePromotions 方法增强
- 添加 `userID` 参数
- 在返回促销活动前检查用户使用次数限制
- 解析 `rules` 中的 `per_user_limit` 参数

### 4. 控制器层增强

#### 4.1 支持可选用户认证
```go
// 尝试获取用户ID（可选认证）
var userID int64 = 0
if token := utils.GetTokenFromRequest(c.Ctx); token != "" {
    if claims, err := utils.ParseToken(token); err == nil {
        userID = claims.UserID
    }
}
```

### 5. 订单创建流程增强

在订单创建成功后，自动记录用户使用的促销活动：

```go
// 记录用户使用促销活动
if len(group.PromotionIDs) > 0 && promotionDiscountAmount > 0 {
    for _, promotionID := range group.PromotionIDs {
        discountPerPromotion := promotionDiscountAmount / float64(len(group.PromotionIDs))
        err = s.promotionSvc.RecordUserPromotionUsage(userID, promotionID, baseOrder.ID, group.MerchantID, discountPerPromotion)
        // 错误处理...
    }
}
```

## 📊 数据库变更

### 1. 新增表
```sql
CREATE TABLE `takeout_user_promotion` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `promotion_id` bigint(20) NOT NULL COMMENT '促销活动ID',
  `order_id` bigint(20) NOT NULL COMMENT '关联订单ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `used_time` datetime NOT NULL COMMENT '使用时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_promotion` (`user_id`, `promotion_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2. 修改现有表
```sql
ALTER TABLE `takeout_order_extension` 
ADD COLUMN `promotion_ids` varchar(500) DEFAULT '' COMMENT '使用的促销活动ID列表，逗号分隔',
ADD COLUMN `promotion_discount` decimal(10,2) DEFAULT '0.00' COMMENT '促销优惠金额';
```

## 🧪 测试验证

### 1. 测试场景
1. **首次使用**：用户第一次使用促销活动，应该成功
2. **达到限制**：用户使用次数达到 `per_user_limit` 后，应该无法再使用
3. **不同用户**：不同用户应该各自独立计算使用次数
4. **不同促销**：同一用户对不同促销活动的使用次数应该独立计算

### 2. 测试步骤
1. 创建促销活动，设置 `per_user_limit: 1`
2. 用户A第一次下单使用促销，应该成功
3. 用户A第二次尝试使用同一促销，应该失败
4. 用户B第一次使用同一促销，应该成功

## 📈 性能优化

### 1. 数据库索引
- `idx_user_promotion`: 用于快速查询用户使用次数
- `idx_promotion_stats`: 用于促销活动统计

### 2. 缓存策略
可以考虑在Redis中缓存用户的促销使用次数，减少数据库查询：
```
key: promotion_usage:{user_id}:{promotion_id}
value: usage_count
expire: 24小时
```

## 🔄 部署步骤

1. **执行数据库迁移脚本**
   ```bash
   mysql -u username -p database_name < modules/takeout/migrations/add_user_promotion_usage_tracking.sql
   ```

2. **重启应用服务**
   ```bash
   # 重启Go应用
   systemctl restart o-mall-backend
   ```

3. **验证功能**
   - 测试促销活动的用户使用次数限制
   - 检查数据库中的使用记录

## 📝 注意事项

1. **向后兼容**：新功能对现有API保持向后兼容
2. **可选认证**：促销查询接口支持可选的用户认证
3. **错误处理**：促销使用记录创建失败不影响订单创建流程
4. **数据一致性**：确保促销使用记录与订单数据的一致性

## 🎯 预期效果

修复完成后：
- ✅ 用户使用促销活动会被正确记录
- ✅ `per_user_limit` 限制会正确生效
- ✅ 商家可以有效控制促销活动的使用次数
- ✅ 系统可以提供详细的促销使用统计数据
