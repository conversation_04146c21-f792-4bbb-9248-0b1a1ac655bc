# 外卖模块文档

## 文档目录

本文档提供了外卖模块（modules/takeout）的详细设计、使用说明和API参考。

### 核心功能文档

- [模块概述](./overview.md) - 外卖模块的总体介绍和架构设计
- [数据模型](./models.md) - 数据库表结构和对象关系
- [API接口](./api.md) - API接口详细说明
- [业务流程](./workflows.md) - 核心业务流程和状态转换
- [集成指南](./integration.md) - 与其他模块的集成方法
- [配送费配置](./delivery_fee_configuration.md) - 配送费计算逻辑和配置参数详解
- [订单创建API详解](./create_order_api_detailed.md) - 订单创建接口的详细说明

### 优惠券系统文档

- [优惠券系统完整指南](./coupon_system_complete_guide.md) - 优惠券系统整体架构和业务流程
- [商家端优惠券功能](./coupon_example.md) - 商家发布和管理优惠券的完整指南
- [用户端优惠券使用指南](./user_coupon_usage_complete_guide.md) - 用户领取、查看和使用优惠券的详细流程
- [用户端集成指南](./user_coupon_integration_guide.md) - 前端集成优惠券功能的API和组件设计

## 优惠券系统概览

### 系统架构

```
商家优惠券模板 (TakeoutCoupon) ──一对多──→ 用户优惠券实例 (TakeoutUserCoupon)
        ↓                                           ↓
   商家发布活动                                  用户领取使用
```

### 状态常量说明

**商家优惠券状态**：
- `CouponStatusUnused (1)` - 可发放
- `CouponStatusUsed (2)` - 已使用  
- `CouponStatusExpired (3)` - 已过期
- `CouponStatusDisabled (4)` - 已禁用

**用户优惠券状态**：
- `UserCouponStatusUnused (1)` - 未使用
- `UserCouponStatusUsed (2)` - 已使用
- `UserCouponStatusExpired (3)` - 已过期

### 业务流程

1. **商家发布** - 创建优惠券模板，设置发放规则
2. **用户发现** - 通过优惠券中心或商家页面发现优惠券
3. **用户领取** - 验证条件后创建用户优惠券实例
4. **用户使用** - 在订单中选择并验证优惠券
5. **状态更新** - 使用后更新状态，记录使用历史

## 最近更新

- **2025-01-15**: **新增接口** - 新增 `/api/v1/user/takeout/merchants/{merchant_id}/available-coupons` 接口，用于获取商家可领取的优惠券和促销信息
- **2025-01-15**: **重要修复** - 修复 `/api/v1/user/takeout/merchants/promotions-coupons` 接口，现在正确返回用户优惠券实例而非商家优惠券模板
- **2025-05-15** - 完善优惠券系统文档，修正状态常量定义，新增完整的用户端使用指南
- **2025-05-14** - 新增外卖促销功能，包括促销活动、优惠券和商品促销关联等功能
- **2025-05-14** - 新增商品审核功能，商家创建商品后需经管理员审核才能上架销售
- **2025-05-14** - 完成模块使用文档
