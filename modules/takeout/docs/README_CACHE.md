# 外卖食品缓存服务说明

## 概述

本文档说明外卖食品模块的Redis缓存实现，用于优化购物、购物车、订单等功能中的食品数据访问性能。

## 缓存架构

### 缓存服务
- **文件**: `takeout_food_cache_service.go`
- **接口**: `TakeoutFoodCacheService`
- **实现**: `takeoutFoodCacheService`

### 缓存键设计

#### 单个食品缓存
- **食品详情**: `takeout:food:detail:{foodID}`
- **食品基本信息**: `takeout:food:info:{foodID}`
- **食品库存**: `takeout:food:stock:{foodID}`
- **食品价格区间**: `takeout:food:price_range:{foodID}`

#### 食品列表缓存
- **商家食品列表**: `takeout:food:list:merchant:{merchantID}:page:{page}:size:{pageSize}`
- **商家分类食品列表**: `takeout:food:list:merchant:{merchantID}:category:{categoryID}:page:{page}:size:{pageSize}`
- **商家推荐食品**: `takeout:food:recommend:merchant:{merchantID}:page:{page}:size:{pageSize}`
- **搜索结果**: `takeout:food:search:{keyword}:merchant:{merchantID}:page:{page}:size:{pageSize}`

#### 带规格的食品列表缓存
- **商家食品列表(带规格)**: `takeout:food:list_with_variants:merchant:{merchantID}:page:{page}:size:{pageSize}`
- **商家分类食品列表(带规格)**: `takeout:food:list_with_variants:merchant:{merchantID}:category:{categoryID}:page:{page}:size:{pageSize}`
- **搜索结果(带规格)**: `takeout:food:search_with_variants:{keyword}:merchant:{merchantID}:page:{page}:size:{pageSize}`

### 缓存过期时间

- **食品详情**: 30分钟
- **食品列表**: 15分钟
- **搜索结果**: 10分钟
- **推荐食品**: 20分钟
- **库存信息**: 5分钟
- **价格区间**: 60分钟

## 缓存策略

### 读取策略
1. **缓存优先**: 优先从Redis缓存读取数据
2. **缓存穿透**: 缓存未命中时从数据库读取
3. **异步更新**: 从数据库读取后异步更新缓存

### 更新策略
1. **创建食品**: 清理相关商家和分类缓存
2. **更新食品**: 删除单个食品缓存，清理相关列表缓存
3. **删除食品**: 删除单个食品缓存，清理相关列表缓存
4. **状态变更**: 删除单个食品缓存，清理相关列表缓存

### 缓存清理

#### 自动清理场景
- 食品创建/更新/删除时
- 食品状态变更时
- 食品售罄状态变更时

#### 手动清理方法
- `ClearMerchantFoodCache(merchantID)`: 清理商家所有食品缓存
- `ClearCategoryFoodCache(categoryID)`: 清理分类所有食品缓存
- `ClearAllFoodCache()`: 清理所有食品缓存
- `DeleteFoodCache(foodID)`: 删除单个食品缓存

## 集成说明

### 服务层集成

在 `takeout_food_service.go` 中已集成缓存服务：

```go
type takeoutFoodService struct {
    foodRepo     repositories.TakeoutFoodRepository
    variantRepo  repositories.TakeoutVariantRepository
    comboRepo    repositories.TakeoutComboRepository
    categoryRepo repositories.TakeoutCategoryRepository
    cacheService TakeoutFoodCacheService  // 缓存服务
}
```

### 主要方法缓存集成

1. **GetFoodByID**: 优先从缓存获取食品详情
2. **ListFoods**: 根据查询条件从缓存获取食品列表
3. **GetFoodsByMerchantID**: 从缓存获取带规格的食品列表
4. **CreateFood**: 创建后清理相关缓存
5. **UpdateFood**: 更新后清理相关缓存
6. **DeleteFood**: 删除后清理相关缓存
7. **UpdateFoodStatus**: 状态更新后清理相关缓存
8. **UpdateSoldOutStatus**: 售罄状态更新后清理相关缓存

## 性能优化

### 异步处理
- 缓存设置和清理操作均采用异步处理，不影响主业务流程
- 使用 `go func()` 协程处理缓存操作

### 批量操作
- 支持批量获取多个食品信息
- 支持批量删除多个食品缓存

### 库存管理
- 提供原子性的库存增减操作
- 支持库存不足时的回滚机制

## 监控和日志

### 日志级别
- **Debug**: 缓存操作成功日志
- **Warn**: 缓存操作失败但不影响业务的警告
- **Error**: 严重的缓存错误

### 监控指标
- 缓存命中率
- 缓存操作耗时
- 缓存大小统计

## 注意事项

1. **数据一致性**: 缓存更新采用最终一致性模型
2. **缓存雪崩**: 设置了不同的过期时间避免同时失效
3. **缓存穿透**: 对空结果也进行短时间缓存
4. **内存使用**: 定期清理过期缓存，控制内存使用
5. **网络异常**: 缓存操作失败不影响主业务逻辑

## 使用示例

```go
// 获取食品详情（自动使用缓存）
foodDetail, err := foodService.GetFoodByID(foodID)

// 获取商家食品列表（自动使用缓存）
foodList, err := foodService.ListFoods(&dto.TakeoutFoodQueryRequest{
    MerchantID: merchantID,
    Page:       1,
    PageSize:   20,
})

// 手动清理缓存
cacheService := NewTakeoutFoodCacheService()
cacheService.ClearMerchantFoodCache(merchantID)
```

## 配置要求

确保 `app.conf` 中配置了Redis连接信息：

```ini
# Redis配置
redis_host = 127.0.0.1
redis_port = 6379
redis_password = 
redis_db = 0
```