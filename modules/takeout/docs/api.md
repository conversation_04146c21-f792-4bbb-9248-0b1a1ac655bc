# 外卖模块API接口文档

## 目录

- [1. 客户端 API](#1-客户端-api)
  - [1.1 商品浏览](#11-商品浏览)
  - [1.2 购物车管理](#12-购物车管理)
  - [1.3 订单管理](#13-订单管理)
- [2. 管理员 API](#2-管理员-api)
  - [2.1 分类管理](#21-分类管理)
  - [2.2 商品管理](#22-商品管理)
  - [2.3 规格管理](#23-规格管理)
  - [2.4 套餐组合管理](#24-套餐组合管理)
  - [2.5 订单管理](#25-订单管理)
  - [2.6 商品审核](#26-商品审核)
  - [2.7 促销管理](#27-促销管理)
- [3. 商家 API](#3-商家-api)
  - [3.1 分类管理](#31-分类管理)
  - [3.2 商品管理](#32-商品管理)
  - [3.3 规格管理](#33-规格管理)
  - [3.4 套餐组合管理](#34-套餐组合管理)
  - [3.5 订单管理](#35-订单管理)
  - [3.6 配送管理](#36-配送管理)
  - [3.7 促销管理](#37-促销管理)
  - [3.8 优惠券管理](#38-优惠券管理)

## 1. 客户端 API

客户端 API 以 `/api/v1/takeout/` 为前缀，主要用于客户端用户浏览商品、管理购物车和订单。

### 1.1 商品浏览

#### 1.1.1 获取外卖分类列表

- **请求方式**: GET
- **URL**: `/api/v1/takeout/categories`
- **描述**: 获取外卖商品分类列表
- **参数**:
  - `merchant_id`: 商家ID（可选）
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": [
      {
        "id": 1,
        "name": "热销套餐",
        "description": "精选热销套餐",
        "image": "http://example.com/images/hot_sale.jpg",
        "sort_order": 1
      },
      {
        "id": 2,
        "name": "单品主食",
        "description": "精选单品主食",
        "image": "http://example.com/images/main_food.jpg",
        "sort_order": 2
      }
    ]
  }
  ```

#### 1.1.2 获取外卖商品列表

- **请求方式**: GET
- **URL**: `/api/v1/takeout/foods`
- **描述**: 获取外卖商品列表
- **参数**:
  - `merchant_id`: 商家ID（可选）
  - `category_id`: 分类ID（可选）
  - `keyword`: 搜索关键词（可选）
  - `tags`: 标签过滤（可选，逗号分隔）
  - `page`: 页码（默认1）
  - `page_size`: 每页数量（默认10）
  - `sort_by`: 排序字段（可选，默认sort_order）
  - `sort_order`: 排序方式（asc/desc，默认asc）
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "total": 100,
      "page": 1,
      "page_size": 10,
      "list": [
        {
          "id": 1,
          "name": "香辣鸡腿堡",
          "brief": "鲜嫩多汁，口感丰富",
          "image": "http://example.com/images/chicken.jpg",
          "price": 15.9,
          "original_price": 18.9,
          "preparation_time": 10,
          "is_spicy": true,
          "is_combination": false,
          "has_variants": true,
          "min_price": 15.9,
          "max_price": 21.9,
          "total_sold": 2000,
          "tags": ["热销", "推荐", "人气"],
          "category_id": 1,
          "category_name": "汉堡",
          "status": 4
        }
      ]
    }
  }
  ```

#### 1.1.3 获取外卖商品详情

- **请求方式**: GET
- **URL**: `/api/v1/takeout/foods/:id`
- **描述**: 获取外卖商品详情
- **参数**:
  - `id`: 商品ID（路径参数）
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 1,
      "merchant_id": 100,
      "merchant_name": "乐享汉堡",
      "name": "香辣鸡腿堡",
      "description": "使用新鲜鸡腿肉制作，配以特制酱料",
      "brief": "鲜嫩多汁，口感丰富",
      "image": "http://example.com/images/chicken.jpg",
      "price": 15.9,
      "original_price": 18.9,
      "packaging_fee": 1.0,
      "preparation_time": 10,
      "is_combination": false,
      "is_spicy": true,
      "has_variants": true,
      "sold_out": false,
      "total_sold": 2000,
      "tags": ["热销", "推荐", "人气"],
      "keywords": ["汉堡", "鸡腿", "辣"],
      "category_id": 1,
      "category_name": "汉堡",
      "status": 4,
      "min_price": 15.9,
      "max_price": 21.9,
      "variants": [
        {
          "id": 1,
          "food_id": 1,
          "name": "标准单层",
          "price": 15.9,
          "original_price": 18.9,
          "is_default": true
        },
        {
          "id": 2,
          "food_id": 1,
          "name": "豪华双层",
          "price": 21.9,
          "original_price": 24.9,
          "is_default": false
        }
      ],
      "created_at": "2025-05-01T10:00:00+08:00",
      "updated_at": "2025-05-14T09:30:00+08:00"
    }
  }
  ```

### 1.2 购物车管理

#### 1.2.1 添加商品到购物车

- **请求方式**: POST
- **URL**: `/api/v1/takeout/cart/add`
- **描述**: 添加商品到购物车
- **授权**: 需要JWT认证
- **请求体**:
  ```json
  {
    "food_id": 123,
    "quantity": 2,
    "variant_id": 456,
    "combo_selections": [
      {
        "combo_item_id": 1,
        "option_id": 101
      },
      {
        "combo_item_id": 2,
        "option_id": 201
      }
    ],
    "remark": "不要辣椒，多加一点酱"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "cart_id": 789,
      "cart_count": 5
    }
  }
  ```

### 1.3 订单管理

#### 1.3.1 创建外卖订单

- **请求方式**: POST
- **URL**: `/api/v1/takeout/order/create`
- **描述**: 创建外卖订单
- **授权**: 需要JWT认证
- **请求体**:
  ```json
  {
    "cart_ids": [1, 2, 3],
    "address_id": 100,
    "payment_method": "wechat",
    "expected_time": "2025-05-14T12:30:00+08:00",
    "tableware_quantity": 2,
    "delivery_type": 0,
    "remark": "请尽快送达，谢谢"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "order_id": 1001,
      "order_no": "TO20250514123456",
      "total_amount": 85.5,
      "payment_url": "https://payment.example.com/pay?order_id=1001"
    }
  }
  ```

## 2. 管理员 API

管理员 API 以 `/admin/api/takeout/` 为前缀，主要用于系统管理员进行平台级别的管理操作。

### 2.1 分类管理

#### 2.1.1 获取分类列表

- **请求方式**: GET
- **URL**: `/admin/api/takeout/categories`
- **描述**: 获取外卖商品分类列表
- **授权**: 需要JWT认证(管理员)

### 2.2 商品管理

#### 2.2.1 获取商品列表

- **请求方式**: GET
- **URL**: `/admin/api/takeout/foods`
- **描述**: 获取外卖商品列表
- **授权**: 需要JWT认证(管理员)

### 2.6 商品审核

#### 2.6.1 审核商品

- **请求方式**: PUT
- **URL**: `/admin/api/takeout/foods/:id/audit`
- **描述**: 管理员审核商品，可以通过或拒绝
- **授权**: 需要JWT认证(管理员)
- **请求体**:
  ```json
  {
    "audit_status": 1,  // 1=通过，2=拒绝
    "audit_reason": "图片清晰，描述详实，符合上架条件"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

## 3. 商家 API

商家 API 以 `/merchant/api/takeout/` 为前缀，主要用于商家管理自己店铺的商品和订单。

### 3.6 配送管理

#### 3.6.1 手动分配配送员

- **请求方式**: POST
- **URL**: `/merchant/api/takeout/orders/:id/assign`
- **描述**: 商家手动分配配送员（系统会同步在跑腿员模块创建订单记录，确保配送员可查看）
- **授权**: 需要JWT认证(商家)
- **请求体**:
  ```json
  {
    "delivery_staff_id": 123
  }
  ```

- **业务逻辑**:
  - 验证订单状态和配送状态
  - 更新外卖订单扩展表中的配送员信息（ID、姓名、电话）
  - 同步创建跑腿员订单记录（RunnerOrder），包含以下信息：
    - 跑腿员ID：确保订单与跑腿员关联
    - 商家信息：取货地址、联系人、经纬度坐标
    - 收货人信息：送货地址、联系人、电话
    - 订单金额、配送费等数据
  - 更新配送状态为"已分配配送员"

- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "order_id": 1001,
      "delivery_status": "已分配配送员",
      "delivery_staff_id": 123,
      "delivery_staff_name": "张三",
      "runner_order_created": true
    }
  }
  ```

**注意**: 商家创建的商品默认设置为“待审核”状态，需经管理员审核通过后才能上架销售。

### 2.7 促销管理

#### 2.7.1 获取促销活动列表

- **请求方式**: GET
- **URL**: `/admin/api/takeout/promotions`
- **描述**: 管理员获取所有商户的促销活动列表
- **授权**: 需要JWT认证(管理员)
- **参数**:
  - `merchant_id`: 商户ID（可选）
  - `status`: 活动状态（可选）
  - `page`: 页码（默认1）
  - `page_size`: 每页数量（默认10）

#### 2.7.2 管理促销活动状态

- **请求方式**: PUT
- **URL**: `/admin/api/takeout/promotions/:id/status`
- **描述**: 管理员可批准、暂停或取消促销活动
- **授权**: 需要JWT认证(管理员)
- **请求体**:
  ```json
  {
    "status": 1,  // 1=活动中，2=暂停，4=取消
    "reason": "经审核，活动内容合规"
  }
  ```

### 3.7 促销管理

#### 3.7.1 创建促销活动

- **请求方式**: POST
- **URL**: `/merchant/api/takeout/promotions`
- **描述**: 商家创建各类促销活动
- **授权**: 需要JWT认证(商家)
- **请求体**:
  ```json
  {
    "name": "新用户首单立减10元",
    "description": "新用户下单立减10元，无门槛",
    "type": 1,
    "start_time": "2025-05-01 00:00:00",
    "end_time": "2025-05-31 23:59:59",
    "max_usage_count": 1000,
    "rules": "{\"discount_amount\": 10, \"min_order_amount\": 0}"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "id": 12
    }
  }
  ```

#### 3.7.2 更新促销活动

- **请求方式**: PUT
- **URL**: `/merchant/api/takeout/promotions/:id`
- **描述**: 商家可修改已创建的促销活动信息
- **授权**: 需要JWT认证(商家)
- **请求体**:
  ```json
  {
    "name": "新用户首单立减15元",
    "description": "新用户下单立减15元，无门槛",
    "end_time": "2025-06-30 23:59:59",
    "rules": "{\"discount_amount\": 15, \"min_order_amount\": 0}"
  }
  ```

#### 3.7.3 获取促销活动列表

- **请求方式**: GET
- **URL**: `/merchant/api/takeout/promotions`
- **描述**: 商家获取自己的促销活动列表
- **授权**: 需要JWT认证(商家)
- **参数**:
  - `status`: 活动状态（可选）
  - `type`: 活动类型（可选）
  - `page`: 页码（默认1）
  - `page_size`: 每页数量（默认10）

#### 3.7.4 取消促销活动

- **请求方式**: PUT
- **URL**: `/merchant/api/takeout/promotions/:id/cancel`
- **描述**: 商家取消正在进行的促销活动
- **授权**: 需要JWT认证(商家)

#### 3.7.5 创建商品促销关联

- **请求方式**: POST
- **URL**: `/merchant/api/takeout/food-promotions`
- **描述**: 商家设置特定商品的促销折扣
- **授权**: 需要JWT认证(商家)
- **请求体**:
  ```json
  {
    "food_id": 123,
    "promotion_id": 12,
    "discount_type": 1,
    "discount_value": 8.5
  }
  ```

### 3.8 优惠券管理

#### 3.8.1 创建优惠券

- **请求方式**: POST
- **URL**: `/merchant/api/takeout/coupons`
- **描述**: 商家创建优惠券
- **授权**: 需要JWT认证(商家)
- **请求体**:
  ```json
  {
    "promotion_id": 3,
    "name": "满50兹10优惠券",
    "description": "订单满50元可使用此券减元10元",
    "type": 1,
    "amount": 10,
    "min_order_amount": 50,
    "max_discount_amount": 10,
    "apply_to_all": true,
    "per_user_limit": 1,
    "total_limit": 500,
    "start_time": "2025-05-01 00:00:00",
    "end_time": "2025-05-31 23:59:59"
  }
  ```

#### 3.8.2 获取优惠券列表

- **请求方式**: GET
- **URL**: `/merchant/api/takeout/coupons`
- **描述**: 商家获取自己的优惠券列表
- **授权**: 需要JWT认证(商家)
- **参数**:
  - `status`: 优惠券状态（可选）
  - `type`: 优惠券类型（可选）
  - `page`: 页码（默认1）
  - `page_size`: 每页数量（默认10）

#### 3.8.3 发布优惠券

- **请求方式**: POST
- **URL**: `/merchant/api/takeout/coupons/{id}/publish`
- **描述**: 发布待发布状态的优惠券，使其可被用户领取
- **授权**: 需要JWT认证(商家)
- **路径参数**:
  - `id`: 优惠券ID
- **发布条件**:
  - 优惠券状态必须为待发布(0)
  - 优惠券开始时间不能早于当前时间
  - 商家必须拥有该优惠券的权限
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": null
  }
  ```

#### 3.8.4 向用户发放优惠券

- **请求方式**: POST
- **URL**: `/merchant/api/takeout/coupons/:id/issue`
- **描述**: 商家向指定用户发放优惠券
- **授权**: 需要JWT认证(商家)
- **请求体**:
  ```json
  {
    "user_ids": [1001, 1002, 1003]
  }
  ```

#### 3.8.5 批量发放优惠券

#### 1.4.1 获取用户优惠券列表

- **请求方式**: GET
- **URL**: `/api/v1/takeout/user/coupons`
- **描述**: 用户获取自己的优惠券列表
- **授权**: 需要JWT认证
- **参数**:
  - `status`: 优惠券状态（可选，0=未使用，1=已使用，2=已过期）
  - `page`: 页码（默认1）
  - `page_size`: 每页数量（默认10）

#### 1.4.2 领取优惠券

- **请求方式**: POST
- **URL**: `/api/v1/takeout/coupons/:id/claim`
- **描述**: 用户领取优惠券
- **授权**: 需要JWT认证

#### 1.4.3 验证优惠券可用性

- **请求方式**: POST
- **URL**: `/api/v1/takeout/coupons/validate`
- **描述**: 验证优惠券对当前购物车是否可用
- **授权**: 需要JWT认证
- **请求体**:
  ```json
  {
    "coupon_id": 101,
    "cart_ids": [1, 2, 3]
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 0,
    "message": "success",
    "data": {
      "valid": true,
      "discount_amount": 10,
      "message": "优惠券可用"
    }
  }
  ```
