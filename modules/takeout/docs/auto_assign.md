# 外卖自动分配骑手功能说明文档

## 功能概述
自动分配骑手功能（Auto Rider Assignment）是为了提高外卖配送效率，系统在商家接单后自动为订单分配最合适的骑手。该功能通过综合考虑骑手位置、评分、工作量等因素，选择最优的骑手进行配送任务分配，减少人工操作，提高配送效率。

## 触发时机
当商家接单（AcceptOrder）成功后，系统会异步触发自动分配骑手功能。为避免与其他操作冲突，系统会等待3秒后再执行分配过程。

## 分配流程

1. **状态检查**：
   - 验证订单存在且为外卖订单类型
   - 验证订单处于已接单状态
   - 验证订单配送状态为"等待配送"
   - 检查分配尝试次数是否超过上限

2. **骑手筛选**：
   - 查询商家周边指定半径内的可用骑手
   - 筛选工作状态正常的骑手

3. **评分排序**：
   - 根据距离因素计算得分（越近得分越高）
   - 根据骑手评分计算得分（评分越高得分越高）
   - 根据工作量计算得分（当前订单数越少得分越高）
   - 综合加权计算总分并排序

4. **分配执行**：
   - 选择评分最高的骑手进行分配
   - 如分配失败，尝试下一个骑手

5. **结果记录**：
   - 记录分配尝试次数和时间
   - 记录分配结果（成功/失败）
   - 记录详细的分配日志

## 配置参数

自动分配功能支持以下配置参数：

| 配置项 | 说明 | 默认值 |
|-------|------|-------|
| Enabled | 是否启用自动分配 | true |
| RadiusKm | 搜索半径(公里) | 5.0 |
| MaxAttempts | 最大尝试次数 | 3 |
| DistanceWeight | 距离权重 | 0.5 |
| ScoreWeight | 评分权重 | 0.3 |
| WorkloadWeight | 工作量权重 | 0.2 |

## 异常处理

系统对以下异常情况进行了处理：

1. **订单状态异常**：如订单不存在、非外卖订单、状态不允许分配等
2. **无可用骑手**：附近没有找到可用骑手
3. **分配失败**：尝试分配给骑手但失败
4. **重试超限**：分配尝试次数达到上限

每次分配尝试都会记录详细日志，包括成功或失败原因，便于后续排查。

## 数据模型扩展

为支持自动分配功能，在`TakeoutOrderExtension`模型中添加了以下字段：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| auto_assigned | int | 是否自动分配(0:否,1:是) |
| assign_attempts | int | 分配尝试次数 |
| last_assign_time | datetime | 最后分配时间 |

## 接口集成

自动分配功能与现有系统的集成点：

1. 商家接单流程（AcceptOrder）：触发自动分配
2. 分配配送员流程（AssignDelivery）：执行实际分配
3. 订单状态管理：更新配送状态

## 注意事项

1. 自动分配功能可通过配置开关控制是否启用
2. 分配过程异步执行，不会阻塞商家接单流程
3. 可根据业务需要调整权重参数优化分配结果
4. 分配尝试次数有上限，避免系统资源浪费
5. 商家也可选择手动分配，覆盖自动分配结果
