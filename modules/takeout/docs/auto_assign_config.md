# 外卖自动分配骑手配置说明文档

## 概述

本文档详细说明了外卖自动分配骑手功能的配置方法和参数说明。自动分配功能通过系统配置表进行管理，支持动态调整配置参数而无需重启服务。

## 配置键说明

### 配置键名称
```
takeout_auto_assign
```

### 配置存储位置
- **数据库表**: `system_config`
- **配置键**: `takeout_auto_assign`
- **配置类型**: `json`
- **配置分类**: `takeout`

## 配置参数详解

### 完整配置示例
```json
{
  "enabled": true,
  "radius_km": 5.0,
  "max_attempts": 3,
  "distance_weight": 0.5,
  "score_weight": 0.3,
  "workload_weight": 0.2
}
```

### 参数说明

#### 1. enabled (布尔值)
- **说明**: 是否启用自动分配功能
- **默认值**: `true`
- **可选值**: `true` | `false`
- **影响**: 当设置为 `false` 时，商家接单后不会触发自动分配流程

#### 2. radius_km (浮点数)
- **说明**: 搜索骑手的半径范围（单位：公里）
- **默认值**: `5.0`
- **取值范围**: `1.0` - `50.0`
- **建议值**: 
  - 城市中心区域: `3.0` - `5.0`
  - 郊区或配送员较少区域: `8.0` - `15.0`
- **影响**: 半径越大，可选择的骑手越多，但可能增加配送距离

#### 3. max_attempts (整数)
- **说明**: 自动分配的最大尝试次数
- **默认值**: `3`
- **取值范围**: `1` - `10`
- **建议值**: `3` - `5`
- **影响**: 尝试次数越多，分配成功率越高，但可能延长分配时间

#### 4. distance_weight (浮点数)
- **说明**: 距离因素在综合评分中的权重
- **默认值**: `0.5`
- **取值范围**: `0.0` - `1.0`
- **计算方式**: 距离越近得分越高
- **建议值**: `0.4` - `0.6`

#### 5. score_weight (浮点数)
- **说明**: 骑手评分在综合评分中的权重
- **默认值**: `0.3`
- **取值范围**: `0.0` - `1.0`
- **计算方式**: 骑手评分越高得分越高
- **建议值**: `0.2` - `0.4`

#### 6. workload_weight (浮点数)
- **说明**: 工作量因素在综合评分中的权重
- **默认值**: `0.2`
- **取值范围**: `0.0` - `1.0`
- **计算方式**: 当前配送订单数越少得分越高
- **建议值**: `0.1` - `0.3`

### 权重配置注意事项

1. **权重总和**: `distance_weight + score_weight + workload_weight` 应该等于 `1.0`
2. **权重调整建议**:
   - 优先考虑配送速度: 增加 `distance_weight`
   - 优先考虑服务质量: 增加 `score_weight`
   - 优先考虑负载均衡: 增加 `workload_weight`

## 配置方法

### 方法一：通过数据库直接配置

```sql
-- 插入新配置（如果不存在）
INSERT INTO system_config (
    config_key, 
    config_value, 
    config_type, 
    category, 
    description, 
    status, 
    is_system, 
    version,
    created_at, 
    updated_at
) VALUES (
    'takeout_auto_assign',
    '{"enabled":true,"radius_km":5.0,"max_attempts":3,"distance_weight":0.5,"score_weight":0.3,"workload_weight":0.2}',
    'json',
    'takeout',
    '外卖自动分配骑手配置',
    1,
    0,
    1,
    NOW(),
    NOW()
);

-- 更新现有配置
UPDATE system_config 
SET config_value = '{"enabled":true,"radius_km":8.0,"max_attempts":5,"distance_weight":0.6,"score_weight":0.25,"workload_weight":0.15}',
    updated_at = NOW(),
    version = version + 1
WHERE config_key = 'takeout_auto_assign';
```

### 方法二：通过系统配置API

```bash
# 创建配置
curl -X POST "http://localhost:8181/api/v1/admin/system/configs" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "config_key": "takeout_auto_assign",
    "config_value": "{\"enabled\":true,\"radius_km\":5.0,\"max_attempts\":3,\"distance_weight\":0.5,\"score_weight\":0.3,\"workload_weight\":0.2}",
    "config_type": "json",
    "category": "takeout",
    "description": "外卖自动分配骑手配置"
  }'

# 更新配置
curl -X PUT "http://localhost:8181/api/v1/admin/system/configs/takeout_auto_assign" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "config_value": "{\"enabled\":true,\"radius_km\":8.0,\"max_attempts\":5,\"distance_weight\":0.6,\"score_weight\":0.25,\"workload_weight\":0.15}"
  }'
```

## 默认配置行为

当系统配置中不存在 `takeout_auto_assign` 配置键时，系统会自动使用以下默认配置：

```json
{
  "enabled": true,
  "radius_km": 5.0,
  "max_attempts": 3,
  "distance_weight": 0.5,
  "score_weight": 0.3,
  "workload_weight": 0.2
}
```

这确保了即使没有手动配置，自动分配功能也能正常工作。

## 配置生效机制

1. **缓存机制**: 配置会被缓存在内存和Redis中，提高访问性能
2. **缓存过期**: 缓存有效期为30分钟
3. **动态更新**: 配置更新后会自动刷新缓存，无需重启服务
4. **降级策略**: 当配置获取失败时，自动使用默认配置

## 配置验证

### 验证配置是否生效

```bash
# 查看当前配置
curl -X GET "http://localhost:8181/api/v1/admin/system/configs/takeout_auto_assign" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 测试自动分配功能

1. 创建一个测试订单
2. 商家接单
3. 观察日志输出，确认自动分配流程是否按预期执行

## 性能调优建议

### 高并发场景
- 适当减少 `radius_km` 以减少查询范围
- 设置合理的 `max_attempts` 避免过度重试

### 配送员较少的区域
- 增加 `radius_km` 扩大搜索范围
- 增加 `max_attempts` 提高分配成功率

### 优化配送效率
- 提高 `distance_weight` 优先选择距离近的骑手
- 适当降低 `score_weight` 和 `workload_weight`

## 故障排查

### 常见问题

1. **自动分配不生效**
   - 检查 `enabled` 是否为 `true`
   - 检查配置格式是否正确
   - 查看系统日志确认配置是否加载成功

2. **分配成功率低**
   - 增加 `radius_km` 扩大搜索范围
   - 增加 `max_attempts` 提高重试次数
   - 检查骑手状态和位置信息

3. **配置不生效**
   - 检查配置键名是否正确
   - 检查JSON格式是否有效
   - 清除缓存重新加载配置

### 日志监控

关键日志关键词：
- `自动分配骑手配置`
- `使用默认配置`
- `AutoAssignDelivery`
- `配置键 takeout_auto_assign 不存在`

## 版本更新说明

- **v1.0**: 初始版本，支持基本的自动分配配置
- **v1.1**: 增加默认配置降级机制，优化错误处理
- **v1.2**: 优化缓存机制，提高配置加载性能