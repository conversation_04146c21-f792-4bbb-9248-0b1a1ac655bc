# 外卖订单创建时余额支付集成文档

## 功能概述

本文档描述了在外卖订单创建过程中集成余额支付功能的实现。当用户在创建订单时选择余额支付方式，系统将自动完成余额支付流程，无需用户进行额外的支付操作。

## 功能特性

1. **订单创建与支付一体化**：在订单创建成功后，如果支付方式为余额支付，系统自动处理支付流程
2. **支付方式兼容性**：支持多种支付方式参数格式（字符串、数字、描述性文本）
3. **事务安全性**：订单创建和支付处理在同一个业务流程中，确保数据一致性
4. **错误处理**：完善的错误处理机制，支付失败时提供详细的错误信息

## API接口变更

### CreateTakeoutOrderRequest 结构体增强

```go
type CreateTakeoutOrderRequest struct {
    TakeoutAddressID int64                   `json:"takeoutAddressID"` // 外卖配送地址ID
    AddressLat       float64                 `json:"addressLat"`       // 送货地址纬度
    AddressLng       float64                 `json:"addressLng"`       // 送货地址经度
    PaymentMethod    interface{}             `json:"paymentMethod"`    // 支付方式（兼容string和int类型）
    PaymentMethodInt int                     `json:"paymentMethodInt"` // 支付方式（数字类型）：1-微信支付，2-支付宝，3-余额支付
    MerchantOrders   []*MerchantOrderRequest `json:"merchantOrders"`   // 按商家分组的订单信息
    ClientIP         string                  `json:"clientIP"`         // 客户端IP（余额支付时需要）
    DeviceInfo       string                  `json:"deviceInfo"`       // 设备信息（余额支付时需要）
}
```

### 支付方式常量

```go
const (
    PayMethodWechat  = 1 // 微信支付
    PayMethodAlipay  = 2 // 支付宝
    PayMethodBalance = 3 // 余额支付
)
```

## 使用示例

### 1. 使用数字类型支付方式

```json
{
    "takeoutAddressID": 123,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethodInt": 3,
    "clientIP": "*************",
    "deviceInfo": "iPhone 13",
    "merchantOrders": [
        {
            "merchantID": 1,
            "cartItemIDs": [1, 2, 3],
            "couponID": 0,
            "promotionID": 0,
            "deliveryTime": "2024-01-15 12:00:00",
            "remark": "不要辣"
        }
    ]
}
```

### 2. 使用字符串类型支付方式

```json
{
    "takeoutAddressID": 123,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethod": "3",
    "clientIP": "*************",
    "deviceInfo": "iPhone 13",
    "merchantOrders": [
        {
            "merchantID": 1,
            "cartItemIDs": [1, 2, 3],
            "couponID": 0,
            "promotionID": 0,
            "deliveryTime": "2024-01-15 12:00:00",
            "remark": "不要辣"
        }
    ]
}
```

### 3. 使用描述性文本支付方式

```json
{
    "takeoutAddressID": 123,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethod": "余额支付",
    "clientIP": "*************",
    "deviceInfo": "iPhone 13",
    "merchantOrders": [
        {
            "merchantID": 1,
            "cartItemIDs": [1, 2, 3],
            "couponID": 0,
            "promotionID": 0,
            "deliveryTime": "2024-01-15 12:00:00",
            "remark": "不要辣"
        }
    ]
}
```

## 处理流程

### 1. 订单创建流程

1. **参数解析**：解析请求参数，包括支付方式的多种格式
2. **订单创建**：调用多商家订单服务创建订单
3. **支付方式判断**：检查是否为余额支付（PayMethodBalance = 3）
4. **余额支付处理**：如果是余额支付，自动处理支付流程

### 2. 余额支付处理流程

1. **构造支付请求**：创建CreateTakeoutPaymentRequest对象
2. **调用支付服务**：调用CreateOrderPayment方法处理支付
3. **余额扣减**：支付服务自动扣减用户余额
4. **订单状态更新**：支付成功后自动更新订单状态为已支付

### 3. 错误处理

- **余额不足**：返回"余额不足"错误信息
- **用户账户不存在**：返回"用户账户不存在"错误信息
- **支付处理失败**：返回具体的支付失败原因

## 响应格式

### 成功响应

```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "orderID": 12345,
            "orderNo": "TO202401151200001",
            "merchantID": 1,
            "merchantName": "美味餐厅",
            "totalAmount": 58.50,
            "payAmount": 55.50,
            "status": 20,
            "payStatus": 1,
            "deliveryStatus": 1,
            "items": [...],
            "deliveryInfo": {...}
        }
    ]
}
```

### 错误响应

```json
{
    "code": 500,
    "message": "余额支付失败: 余额不足",
    "data": null
}
```

## 注意事项

1. **余额检查**：系统会在支付前检查用户余额是否充足
2. **事务处理**：余额扣减和订单状态更新在同一个事务中完成
3. **日志记录**：完整记录支付处理过程，便于问题排查
4. **兼容性**：保持与现有支付流程的兼容性，不影响其他支付方式

## 相关文件

- `modules/takeout/controllers/takeout_order_controller.go` - 订单控制器
- `modules/takeout/dto/takeout_order_dto.go` - 数据传输对象
- `modules/takeout/services/takeout_order_service_impl.go` - 订单服务实现
- `modules/payment/services/impl/balance_payment_processor.go` - 余额支付处理器
- `modules/order/constants/order_payment_constants.go` - 支付常量定义
