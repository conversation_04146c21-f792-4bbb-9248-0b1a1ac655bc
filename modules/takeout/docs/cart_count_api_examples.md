# 购物车计数 API 使用示例

## 1. API 接口说明

### 1.1 基础计数接口
**接口**: `GET /api/v1/user/takeout/cart/count`
**功能**: 获取购物车商品项数量（简单计数）

### 1.2 详细计数接口
**接口**: `GET /api/v1/user/takeout/cart/count-details`
**功能**: 获取购物车详细统计信息

### 1.3 参数化计数接口
**接口**: `GET /api/v1/user/takeout/cart/count?detailed=true`
**功能**: 通过参数控制返回详细信息

## 2. 请求示例

### 2.1 简单计数请求
```bash
curl -X GET "http://localhost:8080/api/v1/user/takeout/cart/count" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "count": 5
  }
}
```

### 2.2 详细计数请求
```bash
curl -X GET "http://localhost:8080/api/v1/user/takeout/cart/count-details" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_items": 5,
    "total_quantity": 8,
    "selected_items": 3,
    "selected_quantity": 5,
    "unselected_items": 2,
    "unselected_quantity": 3,
    "total_amount": 156.50,
    "selected_amount": 98.50,
    "merchant_count": 2
  }
}
```

### 2.3 参数化详细计数请求
```bash
curl -X GET "http://localhost:8080/api/v1/user/takeout/cart/count?detailed=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**响应示例**: 同详细计数接口

## 3. 前端集成示例

### 3.1 JavaScript/Vue.js 示例
```javascript
// 购物车服务
class CartService {
  constructor(apiClient) {
    this.apiClient = apiClient;
  }

  // 获取简单计数
  async getCartCount() {
    try {
      const response = await this.apiClient.get('/api/v1/user/takeout/cart/count');
      return response.data.data.count;
    } catch (error) {
      console.error('获取购物车计数失败:', error);
      return 0;
    }
  }

  // 获取详细计数
  async getCartCountDetails() {
    try {
      const response = await this.apiClient.get('/api/v1/user/takeout/cart/count-details');
      return response.data.data;
    } catch (error) {
      console.error('获取购物车详细计数失败:', error);
      return null;
    }
  }

  // 参数化获取计数
  async getCartCountWithOption(detailed = false) {
    try {
      const url = detailed 
        ? '/api/v1/user/takeout/cart/count?detailed=true'
        : '/api/v1/user/takeout/cart/count';
      const response = await this.apiClient.get(url);
      return response.data.data;
    } catch (error) {
      console.error('获取购物车计数失败:', error);
      return detailed ? null : 0;
    }
  }
}

// Vue 组件示例
export default {
  data() {
    return {
      cartCount: 0,
      cartDetails: null,
      loading: false
    };
  },
  
  async mounted() {
    await this.loadCartInfo();
  },
  
  methods: {
    // 加载购物车信息
    async loadCartInfo() {
      this.loading = true;
      try {
        // 同时获取简单计数和详细信息
        const [count, details] = await Promise.all([
          this.cartService.getCartCount(),
          this.cartService.getCartCountDetails()
        ]);
        
        this.cartCount = count;
        this.cartDetails = details;
      } catch (error) {
        this.$message.error('加载购物车信息失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 更新购物车后刷新计数
    async refreshCartCount() {
      this.cartCount = await this.cartService.getCartCount();
    }
  }
};
```

### 3.2 React Hooks 示例
```javascript
import { useState, useEffect, useCallback } from 'react';

// 自定义 Hook
export const useCartCount = () => {
  const [cartCount, setCartCount] = useState(0);
  const [cartDetails, setCartDetails] = useState(null);
  const [loading, setLoading] = useState(false);

  const cartService = new CartService(apiClient);

  // 获取简单计数
  const fetchCartCount = useCallback(async () => {
    setLoading(true);
    try {
      const count = await cartService.getCartCount();
      setCartCount(count);
    } catch (error) {
      console.error('获取购物车计数失败:', error);
    } finally {
      setLoading(false);
    }
  }, [cartService]);

  // 获取详细计数
  const fetchCartDetails = useCallback(async () => {
    setLoading(true);
    try {
      const details = await cartService.getCartCountDetails();
      setCartDetails(details);
    } catch (error) {
      console.error('获取购物车详细计数失败:', error);
    } finally {
      setLoading(false);
    }
  }, [cartService]);

  // 刷新所有信息
  const refreshAll = useCallback(async () => {
    await Promise.all([fetchCartCount(), fetchCartDetails()]);
  }, [fetchCartCount, fetchCartDetails]);

  useEffect(() => {
    fetchCartCount();
  }, [fetchCartCount]);

  return {
    cartCount,
    cartDetails,
    loading,
    fetchCartCount,
    fetchCartDetails,
    refreshAll
  };
};

// 组件使用示例
const CartIcon = () => {
  const { cartCount, loading, fetchCartCount } = useCartCount();

  return (
    <div className="cart-icon" onClick={() => fetchCartCount()}>
      <Icon name="cart" />
      {cartCount > 0 && (
        <span className="cart-badge">{cartCount}</span>
      )}
      {loading && <Spinner size="small" />}
    </div>
  );
};
```

## 4. 性能优化建议

### 4.1 缓存策略
- 在前端也实现短时间缓存，避免频繁请求
- 使用防抖技术，合并短时间内的多次请求
- 在购物车操作后智能刷新计数

### 4.2 用户体验优化
```javascript
// 防抖获取计数
const debouncedGetCount = debounce(async () => {
  const count = await cartService.getCartCount();
  setCartCount(count);
}, 300);

// 乐观更新
const addToCart = async (item) => {
  // 乐观更新UI
  setCartCount(prev => prev + 1);
  
  try {
    await cartService.addToCart(item);
    // 成功后获取真实计数
    debouncedGetCount();
  } catch (error) {
    // 失败时回滚
    setCartCount(prev => prev - 1);
    showError('添加失败');
  }
};
```

## 5. 错误处理

### 5.1 常见错误码
- `401`: 未授权，需要重新登录
- `500`: 服务器内部错误
- `429`: 请求过于频繁

### 5.2 错误处理示例
```javascript
const handleCartCountError = (error) => {
  switch (error.response?.status) {
    case 401:
      // 重定向到登录页
      router.push('/login');
      break;
    case 429:
      // 请求过于频繁，稍后重试
      setTimeout(() => fetchCartCount(), 1000);
      break;
    case 500:
      // 服务器错误，显示友好提示
      showMessage('服务暂时不可用，请稍后重试');
      break;
    default:
      showMessage('获取购物车信息失败');
  }
};
```

## 6. 测试用例

### 6.1 单元测试示例
```javascript
describe('CartService', () => {
  let cartService;
  let mockApiClient;

  beforeEach(() => {
    mockApiClient = {
      get: jest.fn()
    };
    cartService = new CartService(mockApiClient);
  });

  test('should get cart count successfully', async () => {
    mockApiClient.get.mockResolvedValue({
      data: { data: { count: 5 } }
    });

    const count = await cartService.getCartCount();
    expect(count).toBe(5);
    expect(mockApiClient.get).toHaveBeenCalledWith('/api/v1/user/takeout/cart/count');
  });

  test('should handle cart count error', async () => {
    mockApiClient.get.mockRejectedValue(new Error('Network error'));

    const count = await cartService.getCartCount();
    expect(count).toBe(0);
  });

  test('should get cart details successfully', async () => {
    const mockDetails = {
      total_items: 5,
      total_quantity: 8,
      selected_items: 3
    };
    
    mockApiClient.get.mockResolvedValue({
      data: { data: mockDetails }
    });

    const details = await cartService.getCartCountDetails();
    expect(details).toEqual(mockDetails);
  });
});
```

## 7. 监控与调试

### 7.1 性能监控
```javascript
// 添加性能监控
const monitoredCartService = {
  async getCartCount() {
    const startTime = performance.now();
    try {
      const result = await cartService.getCartCount();
      const duration = performance.now() - startTime;
      
      // 记录性能指标
      analytics.track('cart_count_request', {
        duration,
        success: true,
        cached: duration < 50 // 假设小于50ms为缓存命中
      });
      
      return result;
    } catch (error) {
      analytics.track('cart_count_request', {
        duration: performance.now() - startTime,
        success: false,
        error: error.message
      });
      throw error;
    }
  }
};
```

### 7.2 调试工具
```javascript
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  window.cartDebug = {
    getCount: () => cartService.getCartCount(),
    getDetails: () => cartService.getCartCountDetails(),
    clearCache: () => localStorage.removeItem('cart_cache')
  };
}
```

这些示例展示了如何在实际项目中使用改进后的购物车计数 API，包括性能优化、错误处理和测试等方面的最佳实践。
