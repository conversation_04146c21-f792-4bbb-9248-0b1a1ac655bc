# 外卖套餐模块实现文档

## 1. 功能概述

外卖套餐模块实现了复杂的外卖商品组合功能，支持以下特性：

- 灵活的套餐组件定义（如"主食"、"配菜"、"饮料"等）
- 每个组件可设置必选/可选、单选/多选等规则
- 组件选项支持额外加价、库存管理等
- 支持默认选项设置
- 支持选项库存和销量统计
- 支持套餐价格动态计算

## 2. 数据模型

### 2.1 套餐组件 (TakeoutComboItem)

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 组件ID，主键 |
| food_id | int64 | 是 | 关联的食品ID |
| name | string | 是 | 组件名称（如"主食"、"配菜"） |
| description | string | 否 | 组件描述 |
| is_required | bool | 是 | 是否必选 |
| min_select | int | 是 | 最少选择数量 |
| max_select | int | 是 | 最多选择数量 |
| sort_order | int | 是 | 排序权重 |
| created_at | time.Time | 是 | 创建时间 |
| updated_at | time.Time | 是 | 更新时间 |

### 2.2 套餐选项 (TakeoutComboOption)

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | int64 | 是 | 选项ID，主键 |
| combo_item_id | int64 | 是 | 关联的组件ID |
| name | string | 是 | 选项名称 |
| description | string | 否 | 选项描述 |
| image | string | 否 | 选项图片URL |
| extra_price | float64 | 是 | 额外加价 |
| stock | int | 是 | 库存（-1表示不限） |
| sold_count | int | 是 | 已售数量 |
| is_default | bool | 是 | 是否默认选中 |
| max_per_order | int | 是 | 单订单最大数量 |
| is_individual | bool | 是 | 是否独立加料品 |
| sort_order | int | 是 | 排序权重 |
| created_at | time.Time | 是 | 创建时间 |
| updated_at | time.Time | 是 | 更新时间 |

## 3. API接口

### 3.1 套餐组件管理

#### 3.1.1 创建套餐组件

```http
POST /merchant/api/takeout/foods/{food_id}/combo-items
```

**请求参数**

```json
{
  "name": "主食",
  "description": "请选择主食",
  "is_required": true,
  "min_select": 1,
  "max_select": 1,
  "sort_order": 1
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

#### 3.1.2 查询套餐组件列表

```http
GET /merchant/api/takeout/foods/{food_id}/combo-items?page=1&page_size=10
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "food_id": 1001,
        "name": "主食",
        "description": "请选择主食",
        "is_required": true,
        "min_select": 1,
        "max_select": 1,
        "sort_order": 1,
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z",
        "options": [
          {
            "id": 1,
            "combo_item_id": 1,
            "name": "牛肉面",
            "description": "招牌牛肉面",
            "extra_price": 0.00,
            "stock": 100,
            "sold_count": 50,
            "is_default": true,
            "max_per_order": 0,
            "is_individual": false,
            "sort_order": 1,
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-01T00:00:00Z"
          }
        ]
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

#### 3.1.3 更新套餐组件

```http
PUT /merchant/api/takeout/combo-items/{id}
```

**请求参数**

```json
{
  "name": "主食（必选）",
  "description": "请选择一种主食",
  "is_required": true,
  "min_select": 1,
  "max_select": 2,
  "sort_order": 1
}
```

**响应**

```json
{
  "code": 0,
  "message": "success"
}
```

#### 3.1.4 删除套餐组件

```http
DELETE /merchant/api/takeout/combo-items/{id}
```

**响应**

```json
{
  "code": 0,
  "message": "success"
}
```

### 3.2 套餐选项管理

#### 3.2.1 创建套餐选项

```http
POST /merchant/api/takeout/combo-items/{item_id}/options
```

**请求参数**

```json
{
  "name": "牛肉面",
  "description": "招牌牛肉面",
  "extra_price": 0.00,
  "stock": 100,
  "is_default": true,
  "max_per_order": 0,
  "is_individual": false,
  "sort_order": 1
}
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

#### 3.2.2 查询套餐选项列表

```http
GET /merchant/api/takeout/combo-items/{item_id}/options?page=1&page_size=10
```

**响应**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "combo_item_id": 1,
        "name": "牛肉面",
        "description": "招牌牛肉面",
        "extra_price": 0.00,
        "stock": 100,
        "sold_count": 50,
        "is_default": true,
        "max_per_order": 0,
        "is_individual": false,
        "sort_order": 1,
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

## 4. 业务逻辑

### 4.1 套餐选择验证

在用户提交订单前，系统会验证套餐选择是否合法：

1. 检查必选组件是否都已选择
2. 检查每个组件的选择数量是否在允许范围内
3. 检查选项库存是否充足
4. 检查单次购买数量是否超过限制

### 4.2 价格计算

套餐总价 = 基础价格 + ∑(选中选项的额外加价)

## 5. 使用示例

### 5.1 创建一个包含组件的套餐

1. 创建食品（如果尚未创建）
2. 为食品添加套餐组件（如"主食"、"配菜"）
3. 为每个组件添加选项（如"牛肉面"、"鸡肉饭"）
4. 设置组件的必选/可选规则

### 5.2 用户选择套餐

1. 用户查看套餐详情，看到所有可选的组件和选项
2. 用户根据规则选择需要的选项
3. 系统实时计算总价
4. 用户提交订单时，系统验证选择是否合法

## 6. 注意事项

1. 删除组件时会同时删除其下的所有选项
2. 修改组件的选择范围时，需要确保不会影响已存在的有效订单
3. 选项库存为0时将不可选
4. 套餐组件和选项的排序由sort_order字段控制，数值越小越靠前
