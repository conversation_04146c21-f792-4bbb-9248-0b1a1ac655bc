# 商家套餐管理操作指南

> 本文档提供了商家在外卖平台上管理套餐商品的详细操作示例，包括界面截图、请求参数和响应结果的说明。

## 目录

- [1. 创建套餐商品](#1-创建套餐商品)
  - [1.1 基本信息设置](#11-基本信息设置)
  - [1.2 添加套餐组件](#12-添加套餐组件)
  - [1.3 设置组件选项](#13-设置组件选项)
  - [1.4 套餐价格设置](#14-套餐价格设置)
- [2. 管理套餐商品](#2-管理套餐商品)
  - [2.1 套餐列表](#21-套餐列表)
  - [2.2 编辑套餐](#22-编辑套餐)
  - [2.3 上下架管理](#23-上下架管理)

## 1. 创建套餐商品

### 1.1 基本信息设置

#### 界面示例

```
+-------------------------------------------------------+
|                    创建套餐商品                         |
+-------------------------------------------------------+
| 套餐名称: [双人超值套餐                     ] *必填    |
|                                                       |
| 套餐分类: [套餐     v] *必填                          |
|                                                       |
| 套餐描述:                                             |
| [包含主食、小食和饮料，满足双人用餐需求     ] *必填    |
|                                                       |
| 原价: [88.0          ] 元 *必填                      |
| 优惠价: [68.0          ] 元 *必填                    |
|                                                       |
| 库存数量: [100           ] *必填                      |
| 每人限购: [2            ] 份 (0表示不限购)            |
|                                                       |
| 上架状态: [○立即上架 ●保存草稿 ○暂不上架    ]         |
+-------------------------------------------------------+
```

#### API请求示例

```json
POST /merchant/api/takeout/foods
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "双人超值套餐",
  "category_id": 5,
  "description": "包含主食、小食和饮料，满足双人用餐需求",
  "price": 68.0,
  "original_price": 88.0,
  "is_combo": true,
  "stock": 100,
  "limit_per_user": 2,
  "status": 0,  // 0-草稿 1-上架 2-下架
  "tags": ["热销", "推荐"]
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2001,
    "name": "双人超值套餐",
    "category_id": 5,
    "description": "包含主食、小食和饮料，满足双人用餐需求",
    "price": 68.0,
    "original_price": 88.0,
    "is_combo": true,
    "stock": 100,
    "limit_per_user": 2,
    "status": 0,
    "tags": ["热销", "推荐"],
    "created_at": "2025-05-21T10:30:45+08:00",
    "updated_at": "2025-05-21T10:30:45+08:00"
  }
}
```

### 1.2 添加套餐组件

#### 界面示例

```
+-------------------------------------------------------+
|                    添加套餐组件                         |
+-------------------------------------------------------+
| 套餐名称: 双人超值套餐                                   |
|                                                       |
| 添加组件:                                               |
|                                                       |
| 组件名称: [主食选择                         ] *必填    |
| 组件描述: [请选择一种主食                     ]           |
| 是否必选: [✓] 是                                        |
| 选择方式: [● 单选] [○ 多选]                            |
| 最少选择: [1] 项  最多选择: [1] 项                       |
|                                                       |
| 组件选项:                                               |
| +--------------------------------------------------+ |
| | 选项名称      | 加价 | 库存 | 默认 | 操作           | |
| |---------------|------|------|------|----------------| |
| | 香辣鸡腿堡    | +0.0 | 100  | [✓]  | [编辑] [删除]  | |
| | 牛肉汉堡      | +2.0 | 100  | [ ]  | [编辑] [删除]  | |
| | 鸡肉卷        | +1.0 | 80   | [ ]  | [编辑] [删除]  | |
| +--------------------------------------------------+ |
|                                                       |
| [+ 添加选项]                                           |
|                                                       |
| [ 保存组件 ]   [ 取消 ]                                 |
+-------------------------------------------------------+
```

#### API请求示例 - 添加组件

```json
POST /merchant/api/takeout/foods/2001/combo-items
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "主食选择",
  "description": "请选择一种主食",
  "is_required": true,
  "min_select": 1,
  "max_select": 1,
  "sort_order": 1
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 3001,
    "food_id": 2001,
    "name": "主食选择",
    "description": "请选择一种主食",
    "is_required": true,
    "min_select": 1,
    "max_select": 1,
    "sort_order": 1,
    "created_at": "2025-05-21T10:32:15+08:00",
    "updated_at": "2025-05-21T10:32:15+08:00"
  }
}
```

### 1.3 设置组件选项

#### API请求示例 - 添加选项

```json
POST /merchant/api/takeout/combo-items/3001/options
Content-Type: application/json
Authorization: Bearer {merchant_token}

[
  {
    "name": "香辣鸡腿堡",
    "extra_price": 0.0,
    "stock": 100,
    "is_default": true,
    "sort_order": 1
  },
  {
    "name": "牛肉汉堡",
    "extra_price": 2.0,
    "stock": 100,
    "is_default": false,
    "sort_order": 2
  },
  {
    "name": "鸡肉卷",
    "extra_price": 1.0,
    "stock": 80,
    "is_default": false,
    "sort_order": 3
  }
]
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "item_id": 3001,
    "options": [
      {
        "id": 4001,
        "name": "香辣鸡腿堡",
        "extra_price": 0.0,
        "stock": 100,
        "is_default": true,
        "sort_order": 1
      },
      {
        "id": 4002,
        "name": "牛肉汉堡",
        "extra_price": 2.0,
        "stock": 100,
        "is_default": false,
        "sort_order": 2
      },
      {
        "id": 4003,
        "name": "鸡肉卷",
        "extra_price": 1.0,
        "stock": 80,
        "is_default": false,
        "sort_order": 3
      }
    ]
  }
}
```

### 1.4 套餐价格设置

#### 界面示例

```
+-------------------------------------------------------+
|                    套餐价格设置                         |
+-------------------------------------------------------+
| 套餐名称: 双人超值套餐                                   |
|                                                       |
| 套餐原价: [88.0           ] 元                         |
| 套餐售价: [68.0           ] 元 *必填                   |
|                                                       |
| 成本价:   [50.0           ] 元                         |
| 毛利率:   26.47%                                       |
|                                                       |
| 会员价设置:                                             |
| [✓] 启用会员价                                         |
|                                                       |
| 会员等级    | 折扣率   | 会员价    | 操作               |
| -----------|----------|-----------|-------------------|
| 普通会员    | 95%      | 64.6      | [编辑] [删除]      |
| 黄金会员    | 90%      | 61.2      | [编辑] [删除]      |
| 铂金会员    | 85%      | 57.8      | [编辑] [删除]      |
| 钻石会员    | 80%      | 54.4      | [编辑] [删除]      |
|                                                       |
| [+ 添加会员价]                                         |
|                                                       |
| [ 保存价格 ]   [ 取消 ]                                 |
+-------------------------------------------------------+
```

## 2. 管理套餐商品

### 2.1 套餐列表

#### 界面示例

```
+-------------------------------------------------------+
|                    套餐商品列表                         |
+-------------------------------------------------------+
| 搜索: [                ] [搜索]  [重置]  [+ 新建套餐]  |
|                                                       |
| +--------------------------------------------------+ |
| | 商品ID | 商品名称     | 分类 | 价格 | 库存 | 状态   | |
| |--------|--------------|------|------|------|--------| |
| | 2001  | 双人超值套餐 | 套餐 | 68.0 | 100  | 销售中 | |
| | 2002  | 单人工作餐   | 套餐 | 32.0 | 50   | 已下架 | |
| | 2003  | 家庭分享餐   | 套餐 | 128.0| 30   | 草稿   | |
| +--------------------------------------------------+ |
|                                                       |
| 共 3 条记录，当前第 1/1 页                                |
+-------------------------------------------------------+
```

### 2.2 编辑套餐

#### 界面示例

```
+-------------------------------------------------------+
|                    编辑套餐商品                         |
+-------------------------------------------------------+
| 基本信息 | 套餐组件 | 价格设置 | 商品图片 | 其他设置         |
+-------------------------------------------------------+
|                                                       |
| 套餐名称: [双人超值套餐                     ] *必填    |
|                                                       |
| 套餐分类: [套餐     v] *必填                          |
|                                                       |
| 套餐描述:                                             |
| [包含主食、小食和饮料，满足双人用餐需求     ] *必填    |
|                                                       |
| 原价: [88.0          ] 元 *必填                      |
| 优惠价: [68.0          ] 元 *必填                    |
|                                                       |
| 库存数量: [100           ] *必填                      |
| 每人限购: [2            ] 份 (0表示不限购)            |
|                                                       |
| 上架状态: [● 立即上架 ] [○ 保存草稿 ] [○ 下架 ]      |
|                                                       |
| [ 保存修改 ]   [ 取消 ]                               |
+-------------------------------------------------------+
```

### 2.3 上下架管理

#### 界面示例 - 批量操作

```
+-------------------------------------------------------+
|                    批量操作                           |
+-------------------------------------------------------+
| 已选择 2 个商品:                                       |
| - 双人超值套餐                                        |
| - 单人工作餐                                         |
|                                                       |
| 批量操作: [上架   v] [下架] [删除] [导出]             |
|                                                       |
| [ 确认执行 ]   [ 取消 ]                               |
+-------------------------------------------------------+
```

#### API请求示例 - 批量上架

```json
POST /merchant/api/takeout/foods/batch-update
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "ids": [2001, 2002],
  "action": "publish",
  "status": 1
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success_count": 2,
    "fail_count": 0,
    "failed_items": []
  }
}
```

## 3. 套餐订单管理

### 3.1 订单列表

#### 界面示例

```
+-------------------------------------------------------+
|                    套餐订单列表                         |
+-------------------------------------------------------+
| 订单号       | 套餐名称     | 用户   | 金额 | 状态     |
|-------------|--------------|--------|------|----------|
| 202505211001 | 双人超值套餐 | 张**  | 68.0 | 已完成   |
| 202505211002 | 家庭分享餐   | 李**  | 128.0| 配送中   |
| 202505211003 | 双人超值套餐 | 王**  | 68.0 | 待支付   |
+-------------------------------------------------------+
```

#### API请求示例 - 查询订单

```
GET /merchant/api/takeout/orders?type=combo&status=1&start_time=2025-05-21&end_time=2025-05-21
Authorization: Bearer {merchant_token}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [
      {
        "order_id": "202505211001",
        "combo_name": "双人超值套餐",
        "customer_name": "张**",
        "amount": 68.0,
        "status": 4,  // 4-已完成
        "created_at": "2025-05-21T11:30:15+08:00",
        "selections": [
          {
            "item_name": "主食选择",
            "option_name": "香辣鸡腿堡"
          },
          {
            "item_name": "饮料选择",
            "option_name": "可乐(大杯)"
          }
        ]
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```
