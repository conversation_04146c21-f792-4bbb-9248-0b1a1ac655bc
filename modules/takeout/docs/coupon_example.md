# 外卖模块优惠券发放活动示例

> 本文档提供了商家在外卖平台上创建和管理优惠券活动的详细示例。
> 完整的优惠券系统流程请参考：[优惠券系统完整指南](./coupon_system_complete_guide.md)

## 优惠券系统概述

外卖模块的优惠券系统采用**商家优惠券模板**和**用户优惠券实例**分离的设计：
- **商家优惠券模板（TakeoutCoupon）**：商家创建的优惠券模板，定义优惠券规则
- **用户优惠券实例（TakeoutUserCoupon）**：用户领取的具体优惠券，关联到模板

### 状态说明

#### 商家优惠券模板状态
- `status: 1` - 未使用（可发放）
- `status: 2` - 已使用（已停止发放）
- `status: 3` - 已过期
- `status: 4` - 已禁用

#### 用户优惠券实例状态
- `status: 1` - 未使用
- `status: 2` - 已使用
- `status: 3` - 已过期

## 1. 优惠券发放活动

### 界面示例

```
+-------------------------------------------------------+
|                 新建优惠券活动                            |
+-------------------------------------------------------+
|                                                       |
| 活动基本信息                                            |
|                                                       |
| 活动名称: [新客专享优惠券                    ] *必填    |
|                                                       |
| 活动描述: [新用户注册即可领取，满100元减15元           ] *必填    |
|                                                       |
| 活动类型: [优惠券活动  v]                                 |
|                                                       |
| 开始时间: [2025-05-15] [00:00:00]                        |
|                                                       |
| 结束时间: [2025-08-15] [23:59:59]                        |
|                                                       |
| 最大发放数量: [5000          ]   (0表示不限制)              |
|                                                       |
|                                                       |
| 优惠券设置                                             |
|                                                       |
| 优惠券名称: [新客满减券                      ] *必填    |
|                                                       |
| 优惠券类型: [●满减券] [○折扣券]                             |
|                                                       |
| 优惠金额: [15            ] 元                           |
|                                                       |
| 使用门槛: 订单满 [100           ] 元                     |
|                                                       |
| 每人限领: [1             ] 张  (0表示不限制)              |
|                                                       |
| 有效期: [30            ] 天 (自领取日起计算)              |
|                                                       |
| 适用范围:                                              |
|                                                       |
| [●所有商品] [○指定分类] [○指定商品]                         |
|                                                       |
| 领取渠道:                                              |
|                                                       |
| [√] 注册自动发放 [√] 首页弹窗 [√] 优惠券中心                |
|                                                       |
| [ 保存为草稿 ]    [ 预览 ]    [ 发布活动 ]                  |
+-------------------------------------------------------+
```

### API请求示例

```json
POST /merchant/api/takeout/promotions
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "新客专享优惠券",
  "description": "新用户注册即可领取，满100元减15元",
  "type": 4,  // 优惠券活动
  "start_time": "2025-05-15 00:00:00",
  "end_time": "2025-08-15 23:59:59",
  "max_usage_count": 5000,
  "status": 1,  // 可发放状态
  "rules": "{\"coupon\":{\"name\":\"新客满减券\",\"type\":1,\"amount\":15,\"min_order_amount\":100,\"per_user_limit\":1,\"valid_days\":30,\"apply_to_all\":true},\"channels\":[\"register\",\"popup\",\"center\"]}",
  "food_ids": []  // 所有商品都参与
}
```

### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 503,
    "merchant_id": 502,
    "name": "新客专享优惠券",
    "description": "新用户注册即可领取，满100元减15元",
    "type": 4,
    "start_time": "2025-05-15T00:00:00+08:00",
    "end_time": "2025-08-15T23:59:59+08:00",
    "status": 1,  // 可发放状态
    "rules": "{\"coupon\":{\"name\":\"新客满减券\",\"type\":1,\"amount\":15,\"min_order_amount\":100,\"per_user_limit\":1,\"valid_days\":30,\"apply_to_all\":true},\"channels\":[\"register\",\"popup\",\"center\"]}",
    "max_usage_count": 5000,
    "usage_count": 0,
    "created_at": "2025-05-14T16:10:35+08:00",
    "updated_at": "2025-05-14T16:10:35+08:00",
    "coupon_info": {
      "id": 201,
      "name": "新客满减券",
      "type": 1,
      "amount": 15,
      "min_order_amount": 100,
      "valid_days": 30,
      "per_user_limit": 1,
      "apply_to_all": true
    }
  }
}
```

## 2. 活动预览和发布

在创建活动后，商家可以预览活动效果并发布：

### 活动预览界面

```
+-------------------------------------------------------+
|                  活动预览                                |
+-------------------------------------------------------+
|                                                       |
| +---------------+                                     |
| |               |                                     |
| |   活动图片     |  新客专享优惠券                        |
| |               |                                     |
| +---------------+  新用户注册即可领取，满100元减15元         |
|                                                       |
| 有效期: 2025-05-15 至 2025-08-15                       |
|                                                       |
| 优惠券详情:                                            |
| - 满100元可用                                           |
| - 优惠金额15元                                          |
| - 适用于所有商品                                        |
| - 每人限领1张                                           |
|                                                       |
| 用户领取展示效果:                                       |
|                                                       |
| +---------------+          +---------------+          |
| |   新客满减券    |          |   立即领取     |          |
| |   满100减15   |          |               |          |
| +---------------+          +---------------+          |
|                                                       |
| [返回编辑]                     [确认发布]               |
+-------------------------------------------------------+
```

### 活动发布成功通知

```
+-------------------------------------------------------+
|                 活动发布成功                            |
+-------------------------------------------------------+
|                                                       |
| ✓ 恭喜，您的活动已成功发布！                              |
|                                                       |
| 活动ID: 503                                            |
| 活动名称: 新客专享优惠券                                 |
| 活动时间: 2025-05-15 至 2025-08-15                     |
| 发布时间: 2025-05-14 16:15:20                         |
|                                                       |
| 活动已在以下渠道展示:                                    |
| - 用户注册页面                                          |
| - APP首页弹窗                                           |
| - 优惠券中心                                            |
|                                                       |
| 您可以在「促销活动管理」中查看活动数据和效果               |
|                                                       |
| [ 查看活动详情 ]     [ 返回活动列表 ]                     |
+-------------------------------------------------------+
```

## 3. 优惠券使用示例

### 用户使用优惠券界面

```
+-------------------------------------------------------+
|                  结算页面                                |
+-------------------------------------------------------+
|                                                       |
| 订单信息:                                               |
|                                                       |
| 2x 香辣鸡腿堡             37.60元                 |
| 1x 藏江鲜牛腊汤         28.00元                 |
| 1x 龙须冰糖水             12.00元                 |
|                                                       |
| 小计:                              77.60元                 |
| 配送费:                           5.00元                 |
| 工具餐具费:                    0.50元                 |
|                                                       |
| 优惠:                                                  |
|                                                       |
| [√] 选用优惠券                                           |
|                                                       |
| +---------------+  +---------------+  +---------------+ |
| |  新客满减券    |  |  满100减10券   |  |  5折比腾券    | |
| |  满100减15    |  |  满80减10     |  |  最高50元    | |
| |  还有 29天过期 |  |  还有 10天过期 |  |  还有 15天过期 | |
| |  [✓ 已选中]     |  |  [选择使用]     |  |  [选择使用]     | |
| +---------------+  +---------------+  +---------------+ |
|                                                       |
| 当前选择优惠: -15.00元                               |
|                                                       |
| 应付金额: 68.10元                                     |
|                                                       |
| [ 提交订单 ]                                           |
+-------------------------------------------------------+
```

### 优惠券验证API请求

```json
POST /api/takeout/orders/verify_coupon
Content-Type: application/json
Authorization: Bearer {user_token}

{
  "coupon_id": 201,
  "order_items": [
    {
      "food_id": 1024,
      "quantity": 2,
      "unit_price": 18.8
    },
    {
      "food_id": 2048,
      "quantity": 1,
      "unit_price": 28.0
    },
    {
      "food_id": 3072,
      "quantity": 1,
      "unit_price": 12.0
    }
  ],
  "delivery_fee": 5.0,
  "packaging_fee": 0.5
}
```

### 优惠券验证API响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "coupon_id": 201,
    "coupon_name": "新客满减券",
    "original_amount": 83.10,
    "discount_amount": 15.00,
    "final_amount": 68.10,
    "valid": true,
    "message": "优惠券可用"
  }
}
```
