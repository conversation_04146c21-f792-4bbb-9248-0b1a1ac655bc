# 外卖模块优惠券系统完整指南

## 概述

外卖模块的优惠券系统采用**一对多**的设计模式：
- **商家优惠券模板（TakeoutCoupon）**：商家创建的优惠券模板，定义优惠券的基本信息和规则
- **用户优惠券实例（TakeoutUserCoupon）**：用户领取的具体优惠券实例，关联到优惠券模板

## 系统架构

### 数据模型关系

```
商家优惠券模板 (TakeoutCoupon)
    |
    | 1:N
    |
用户优惠券实例 (TakeoutUserCoupon)
    |
    | N:1
    |
订单 (TakeoutOrder)
```

### 状态常量定义

#### 商家优惠券模板状态
```go
const (
    CouponStatusPending   = 0 // 待发布
    CouponStatusUnused    = 1 // 未使用（已发布，可发放）
    CouponStatusUsed      = 2 // 已使用（已停止发放）
    CouponStatusExpired   = 3 // 已过期
    CouponStatusDisabled  = 4 // 已禁用
)
```

#### 用户优惠券实例状态
```go
const (
    UserCouponStatusUnused   = 1 // 未使用
    UserCouponStatusUsed     = 2 // 已使用
    UserCouponStatusExpired  = 3 // 已过期
)
```

## 完整业务流程

### 第一阶段：商家发布优惠券

#### 1.1 商家创建优惠券模板

**API接口**: `POST /merchant/api/takeout/coupons`

**请求示例**:
```json
{
  "promotion_id": 3,
  "name": "满50减10优惠券",
  "description": "订单满50元可使用此券减10元",
  "type": 1,  // 1:满减券 2:折扣券 3:兑换券
  "amount": 10,
  "min_order_amount": 50,
  "max_discount_amount": 10,
  "apply_to_all": true,
  "per_user_limit": 1,
  "total_limit": 500,
  "start_time": "2025-05-01 00:00:00",
  "end_time": "2025-05-31 23:59:59",
  "status": 1  // 可发放状态
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 201,
    "code": "COUPON_202505_001",
    "name": "满50减10优惠券",
    "status": 1
  }
}
```

#### 1.2 发布优惠券

**API接口**: `POST /api/v1/merchant/takeout/coupons/{id}/publish`

**请求示例**:
```bash
curl -X POST \
  http://localhost:8080/api/v1/merchant/takeout/coupons/2/publish \
  -H 'Authorization: Bearer {merchant_jwt_token}' \
  -H 'Content-Type: application/json'
```

**发布条件检查**:
- 优惠券状态必须为待发布(0)
- 优惠券开始时间不能早于当前时间
- 优惠券结束时间不能早于开始时间
- 商家必须拥有该优惠券的权限

**发布后效果**:
- 优惠券状态变更为已发布(1)
- 用户可以在优惠券中心看到并领取
- 用户可以在订单中使用该优惠券

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

#### 1.3 优惠券模板状态管理

- **待发布(0)**: 优惠券模板已创建，但尚未发布，用户无法看到和领取
- **已发布(1)**: 优惠券模板已发布，可以被用户领取和使用
- **已使用(2)**: 优惠券模板已停止发放（达到发放上限或商家主动停止）
- **已过期(3)**: 优惠券模板已过期，不能再被领取
- **已禁用(4)**: 优惠券模板被商家禁用

### 第二阶段：用户领取优惠券

#### 2.1 用户发现优惠券

用户可以通过以下渠道发现优惠券：
- 商家页面的优惠券展示
- 优惠券中心
- 首页推荐
- 注册赠送
- 活动页面

#### 2.2 用户领取优惠券

**API接口**: `POST /api/v1/user/takeout/coupons/claim`

**请求示例**:
```json
{
  "coupon_id": 201,
  "merchant_id": 502
}
```

**系统处理流程**:
1. 验证优惠券模板是否可领取（状态、时间、库存）
2. 验证用户是否符合领取条件（等级、领取次数限制）
3. 创建用户优惠券实例
4. 更新优惠券模板的发放统计

**响应示例**:
```json
{
  "code": 0,
  "message": "领取成功",
  "data": {
    "user_coupon_id": 1001,
    "coupon_name": "满50减10优惠券",
    "expire_time": "2025-05-31 23:59:59",
    "status": 1
  }
}
```

#### 2.3 用户优惠券实例创建

```sql
INSERT INTO takeout_user_coupon (
    user_id, coupon_id, status, created_at, updated_at
) VALUES (
    12345, 201, 1, NOW(), NOW()
);
```

### 第三阶段：用户使用优惠券

#### 3.1 订单页面选择优惠券

**API接口**: `GET /api/v1/user/takeout/coupons/available`

**查询参数**:
- `merchant_id`: 商家ID
- `total_amount`: 订单总金额
- `food_ids`: 商品ID列表（可选）

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "available_coupons": [
      {
        "user_coupon_id": 1001,
        "coupon_id": 201,
        "name": "满50减10优惠券",
        "discount_amount": 10,
        "min_order_amount": 50,
        "can_use": true,
        "reason": ""
      }
    ]
  }
}
```

#### 3.2 验证优惠券可用性

**API接口**: `POST /api/v1/user/takeout/coupons/validate`

**请求示例**:
```json
{
  "user_coupon_id": 1001,
  "order": {
    "merchant_id": 502,
    "total_amount": 60.00,
    "items": [
      {
        "food_id": 1024,
        "quantity": 2,
        "price": 25.00,
        "category_id": 1
      }
    ]
  }
}
```

**验证规则**:
1. 优惠券状态必须为未使用(1)
2. 优惠券未过期
3. 订单金额满足最低消费要求
4. 商品范围符合优惠券适用规则
5. 商家匹配

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "valid": true,
    "discount_amount": 10.00,
    "final_amount": 50.00,
    "reason": ""
  }
}
```

#### 3.3 订单提交时使用优惠券

**API接口**: `POST /api/v1/user/takeout/orders`

**请求示例**:
```json
{
  "merchant_id": 502,
  "items": [
    {
      "food_id": 1024,
      "quantity": 2,
      "price": 25.00
    }
  ],
  "user_coupon_id": 1001,
  "delivery_address_id": 123,
  "remark": "不要辣"
}
```

**系统处理流程**:
1. 再次验证优惠券可用性
2. 计算优惠金额
3. 创建订单
4. 更新用户优惠券状态为已使用(2)
5. 记录使用时间和订单ID

#### 3.4 优惠券使用记录更新

```sql
UPDATE takeout_user_coupon 
SET 
    status = 2,  -- 已使用
    used_time = NOW(),
    order_id = 12345,
    updated_at = NOW()
WHERE id = 1001 AND user_id = 12345 AND status = 1;
```

### 第四阶段：优惠券状态管理

#### 4.1 过期处理

系统定时任务每天检查并更新过期的用户优惠券：

```sql
UPDATE takeout_user_coupon uc
JOIN takeout_coupon c ON uc.coupon_id = c.id
SET uc.status = 3, uc.updated_at = NOW()
WHERE uc.status = 1 
  AND c.end_time < NOW();
```

#### 4.2 用户优惠券查询

**API接口**: `GET /api/v1/user/takeout/coupons/list`

**查询参数**:
- `status`: 状态筛选 (1:未使用 2:已使用 3:已过期)
- `page`: 页码
- `page_size`: 每页数量

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "coupons": [
      {
        "id": 1001,
        "coupon_id": 201,
        "name": "满50减10优惠券",
        "type": 1,
        "amount": 10,
        "min_order_amount": 50,
        "merchant_name": "美味餐厅",
        "status": 1,
        "status_text": "未使用",
        "expire_time": "2025-05-31 23:59:59",
        "received_time": "2025-05-15 10:30:00"
      }
    ],
    "total": 5,
    "page": 1,
    "page_size": 10
  }
}
```

## 业务规则说明

### 优惠券领取规则

1. **时间限制**: 只能在优惠券模板的有效期内领取
2. **数量限制**: 
   - 总发放数量限制（total_limit）
   - 每人领取数量限制（per_user_limit）
   - 每日发放数量限制（daily_limit）
3. **用户等级限制**: 根据用户等级限制领取（user_level_limit）
4. **商家限制**: 只能领取指定商家的优惠券

### 优惠券使用规则

1. **状态检查**: 必须是未使用状态
2. **时间检查**: 在优惠券有效期内
3. **金额检查**: 订单金额满足最低消费要求
4. **商品范围检查**: 
   - 全部商品适用（apply_to_all = true）
   - 指定分类适用（apply_to_categories）
   - 指定商品适用（apply_to_foods）
   - 排除商品（exclude_foods）
5. **商家匹配**: 订单商家与优惠券商家一致

### 优惠金额计算

#### 满减券计算
```go
if orderAmount >= coupon.MinOrderAmount {
    discountAmount = coupon.Amount
    finalAmount = orderAmount - discountAmount
}
```

#### 折扣券计算
```go
if orderAmount >= coupon.MinOrderAmount {
    discountAmount = orderAmount * (coupon.Amount / 100)
    if discountAmount > coupon.MaxDiscountAmount {
        discountAmount = coupon.MaxDiscountAmount
    }
    finalAmount = orderAmount - discountAmount
}
```

## 错误处理

### 常见错误码

- `40001`: 优惠券不存在
- `40002`: 优惠券已过期
- `40003`: 优惠券已被使用
- `40004`: 优惠券已达领取上限
- `40005`: 用户已达领取次数限制
- `40006`: 订单金额不满足使用条件
- `40007`: 商品不在优惠券适用范围内
- `40008`: 商家不匹配

### 错误响应示例

```json
{
  "code": 40002,
  "message": "优惠券已过期",
  "data": null
}
```

## 性能优化建议

### 数据库索引

```sql
-- 用户优惠券表索引
CREATE INDEX idx_user_coupon_user_status ON takeout_user_coupon(user_id, status);
CREATE INDEX idx_user_coupon_coupon_id ON takeout_user_coupon(coupon_id);
CREATE INDEX idx_user_coupon_order_id ON takeout_user_coupon(order_id);

-- 优惠券表索引
CREATE INDEX idx_coupon_merchant_status ON takeout_coupon(merchant_id, status);
CREATE INDEX idx_coupon_time ON takeout_coupon(start_time, end_time);
```

### 缓存策略

1. **优惠券模板缓存**: 缓存热门商家的优惠券模板信息
2. **用户优惠券缓存**: 缓存用户的可用优惠券列表
3. **验证结果缓存**: 缓存优惠券验证结果（短时间）

## 监控指标

### 业务指标

- 优惠券发放数量
- 优惠券使用率
- 优惠券过期率
- 平均优惠金额
- 用户领取转化率

### 技术指标

- API响应时间
- 数据库查询性能
- 缓存命中率
- 错误率

## 总结

外卖模块的优惠券系统通过**商家优惠券模板**和**用户优惠券实例**的分离设计，实现了灵活的优惠券管理。商家可以创建各种类型的优惠券模板，用户可以领取并在订单中使用，系统会自动处理状态流转和业务规则验证，确保优惠券的正确使用和统计。