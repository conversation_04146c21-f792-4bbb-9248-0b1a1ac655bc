# 外卖配送费配置与计算文档

> 本文档详细说明了外卖订单中配送费的计算方法、配置参数、技术实现以及相关业务逻辑。

## 目录

- [1. 配送费计算概述](#1-配送费计算概述)
- [2. 技术架构与实现](#2-技术架构与实现)
- [3. 配置参数详解](#3-配置参数详解)
- [4. 计算逻辑说明](#4-计算逻辑说明)
- [5. 配置示例](#5-配置示例)
- [6. API接口说明](#6-api接口说明)
- [7. 日志与监控](#7-日志与监控)
- [8. 注意事项](#8-注意事项)

## 1. 配送费计算概述

### 1.1 计算原理

外卖配送费采用基于系统配置的动态计算方式，支持以下功能：

- **基础配送费**: 每个订单的基础配送费用
- **满额免配送费**: 订单金额达到指定金额时免收配送费
- **满额配送费折扣**: 订单金额达到指定金额时配送费享受折扣
- **多商家订单**: 每个商家单独计算配送费

### 1.2 计算时机

配送费在以下时机进行计算：

1. **订单创建时**: 根据购物车商品总金额计算配送费
2. **按商家拆分**: 多商家订单时，每个商家的订单单独计算配送费
3. **实时计算**: 每次订单创建都会重新获取最新的配送费配置

## 2. 技术架构与实现

### 2.1 核心组件

配送费计算系统由以下核心组件构成：

- **配送费计算服务**: `multiMerchantOrderService.calculateDeliveryFee()`
- **系统配置服务**: `systemConfigService.GetAllConfigsWithDetails()`
- **配置缓存层**: Redis缓存系统
- **数据存储层**: `system_config` 数据表

### 2.2 数据库结构

#### 2.2.1 system_config 表结构

```sql
CREATE TABLE `system_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键名（唯一）',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型（如text、number、json等）',
  `category` varchar(50) DEFAULT 'system' COMMENT '配置分类',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `version` int(11) DEFAULT 1 COMMENT '版本号，用于控制缓存更新',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `is_system` tinyint(4) DEFAULT 0 COMMENT '是否系统级配置：1-是，0-否',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

#### 2.2.2 配送费配置存储

- **ConfigKey**: `deliveryFee`
- **Category**: `deliveryFee`
- **ConfigType**: `json`
- **Status**: `1` (启用状态)
- **ConfigValue**: JSON格式的配置数据

### 2.3 缓存机制

#### 2.3.1 缓存策略

系统采用多层缓存策略优化配送费计算性能：

1. **Redis全局缓存**: 缓存所有系统配置
   - 缓存键: `system:config:all`
   - 过期时间: 24小时
   - 数据格式: JSON字符串

2. **应用层缓存**: 服务内存缓存热点配置

3. **缓存更新机制**:
   - 配置更新时自动刷新缓存
   - 版本号控制缓存一致性
   - 缓存穿透保护

#### 2.3.2 缓存实现代码

```go
// 从缓存获取配置
cacheData, err := redis.Get(models.SystemConfigAllCacheKey)
if err == nil && cacheData != "" {
    var result map[string]string
    if err := json.Unmarshal([]byte(cacheData), &result); err == nil {
        return result, nil
    }
}
```

### 2.4 服务调用流程

```mermaid
sequenceDiagram
    participant Order as 订单服务
    participant Config as 配置服务
    participant Cache as Redis缓存
    participant DB as 数据库
    
    Order->>Config: GetAllConfigsWithDetails("deliveryFee")
    Config->>Cache: 查询配置缓存
    alt 缓存命中
        Cache-->>Config: 返回缓存数据
    else 缓存未命中
        Config->>DB: 查询数据库
        DB-->>Config: 返回配置数据
        Config->>Cache: 更新缓存
    end
    Config-->>Order: 返回配置数据
    Order->>Order: calculateDeliveryFee()
```

## 3. 配置参数详解

### 3.1 配置存储

配送费配置存储在 `system_config` 表中：

- **ConfigKey**: `deliveryFee`
- **Category**: `deliveryFee`
- **ConfigType**: `json`
- **Status**: `1` (启用状态)

### 3.2 配置参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `deliveryBaseFee` | float64 | 是 | 10.0 | 基础配送费（元） |
| `deliveryFreeEnabled` | bool | 是 | false | 是否启用满额免配送费 |
| `deliveryFreeAmount` | float64 | 否 | 30.0 | 免配送费门槛金额（元） |
| `deliveryDiscountEnabled` | bool | 是 | false | 是否启用配送费折扣 |
| `deliveryDiscountAmount` | float64 | 否 | 20.0 | 配送费折扣门槛金额（元） |
| `deliveryDiscountRate` | float64 | 否 | 0.8 | 配送费折扣率（0.8表示8折） |
| `deliveryKmFee` | float64 | 否 | 2.0 | 每公里配送费（预留字段） |
| `deliveryMinOrderAmount` | float64 | 否 | 2.0 | 最低起送金额（预留字段） |

### 3.3 配置示例

```json
{
  "deliveryBaseFee": 5.0,
  "deliveryFreeEnabled": true,
  "deliveryFreeAmount": 50.0,
  "deliveryDiscountEnabled": true,
  "deliveryDiscountAmount": 30.0,
  "deliveryDiscountRate": 0.8,
  "deliveryKmFee": 2.0,
  "deliveryMinOrderAmount": 10.0
}
```

## 4. 计算逻辑说明

### 4.1 计算流程

配送费计算遵循以下优先级顺序：

```
1. 检查满额免配送费条件
   ↓ (不满足)
2. 检查满额配送费折扣条件
   ↓ (不满足)
3. 使用基础配送费
```

### 4.2 详细计算逻辑

#### 4.2.1 满额免配送费

```go
// 条件：启用免配送费 && 订单金额 >= 免配送费门槛
if deliveryFreeEnabled && totalAmount >= deliveryFreeAmount {
    return 0.0  // 免收配送费
}
```

**示例**：
- 配置：`deliveryFreeEnabled: true`, `deliveryFreeAmount: 50.0`
- 订单金额：60元
- 结果：配送费 = 0元

#### 4.2.2 满额配送费折扣

```go
// 条件：启用配送费折扣 && 订单金额 >= 折扣门槛
if deliveryDiscountEnabled && totalAmount >= deliveryDiscountAmount {
    return deliveryBaseFee * deliveryDiscountRate
}
```

**示例**：
- 配置：`deliveryBaseFee: 10.0`, `deliveryDiscountEnabled: true`, `deliveryDiscountAmount: 30.0`, `deliveryDiscountRate: 0.8`
- 订单金额：35元
- 结果：配送费 = 10.0 × 0.8 = 8.0元

#### 4.2.3 基础配送费

```go
// 不满足任何优惠条件时使用基础配送费
return deliveryBaseFee
```

**示例**：
- 配置：`deliveryBaseFee: 10.0`
- 订单金额：20元（不满足任何优惠条件）
- 结果：配送费 = 10.0元

### 4.3 多商家订单处理

### 4.4 核心计算函数实现

```go
// calculateDeliveryFee 计算配送费
func (s *multiMerchantOrderService) calculateDeliveryFee(merchantID int64, totalAmount float64) float64 {
    // 1. 获取配送费配置
    ctx := context.Background()
    configs, err := s.systemConfigSvc.GetAllConfigsWithDetails(ctx, "", 1, -1, "deliveryFee")
    if err != nil {
        logs.Error("获取配送费配置失败: %v，使用默认配置", err)
        return 10.0 // 默认配送费
    }
    
    // 2. 解析JSON配置
    var deliveryConfig map[string]interface{}
    if len(configs) > 0 {
        err = json.Unmarshal([]byte(configs[0].ConfigValue), &deliveryConfig)
        if err != nil {
            logs.Error("解析配送费配置失败: %v，使用默认配置", err)
            return 10.0
        }
    }
    
    // 3. 提取配置参数（带默认值）
    baseDeliveryFee := extractFloat64(deliveryConfig, "deliveryBaseFee", 10.0)
    freeDeliveryEnabled := extractBool(deliveryConfig, "deliveryFreeEnabled", false)
    freeDeliveryAmount := extractFloat64(deliveryConfig, "deliveryFreeAmount", 30.0)
    discountEnabled := extractBool(deliveryConfig, "deliveryDiscountEnabled", false)
    discountAmount := extractFloat64(deliveryConfig, "deliveryDiscountAmount", 20.0)
    discountRate := extractFloat64(deliveryConfig, "deliveryDiscountRate", 0.8)
    
    // 4. 计算逻辑（按优先级）
    // 4.1 满额免配送费（最高优先级）
    if freeDeliveryEnabled && totalAmount >= freeDeliveryAmount {
        return 0.0
    }
    
    // 4.2 满额配送费折扣
    if discountEnabled && totalAmount >= discountAmount {
        return baseDeliveryFee * discountRate
    }
    
    // 4.3 基础配送费
    return baseDeliveryFee
}
```

对于包含多个商家商品的订单：

1. **按商家拆分**: 系统自动将订单按商家拆分为多个子订单
2. **独立计算**: 每个商家的订单独立计算配送费
3. **配置共享**: 所有商家使用相同的配送费配置

**示例**：
- 商家A订单金额：30元 → 配送费：8元（享受8折优惠）
- 商家B订单金额：15元 → 配送费：10元（基础配送费）
- 总配送费：18元

## 5. 配置示例

### 5.1 基础配置

```json
{
  "deliveryBaseFee": 5.0,
  "deliveryFreeEnabled": false,
  "deliveryDiscountEnabled": false
}
```

**说明**: 所有订单统一收取5元配送费，无任何优惠。

### 5.2 满额免配送费配置

```json
{
  "deliveryBaseFee": 6.0,
  "deliveryFreeEnabled": true,
  "deliveryFreeAmount": 50.0,
  "deliveryDiscountEnabled": false
}
```

**说明**: 基础配送费6元，满50元免配送费。

### 5.3 阶梯优惠配置

```json
{
  "deliveryBaseFee": 8.0,
  "deliveryFreeEnabled": true,
  "deliveryFreeAmount": 80.0,
  "deliveryDiscountEnabled": true,
  "deliveryDiscountAmount": 40.0,
  "deliveryDiscountRate": 0.5
}
```

**说明**: 
- 基础配送费8元
- 满40元配送费5折（4元）
- 满80元免配送费

## 6. API接口说明

### 6.1 配送费计算接口

配送费计算集成在订单创建API中，无需单独调用。

**接口路径**: `POST /api/v1/user/takeout/order/create`

**计算时机**: 订单创建过程中自动计算

**返回字段**: 
```json
{
  "deliveryFee": 8.0,  // 配送费金额
  "totalAmount": 68.0  // 订单总金额（包含配送费）
}
```

### 6.2 配置管理接口

配送费配置通过系统配置管理接口进行管理：

**获取配置**: `GET /api/v1/admin/system/configs?category=deliveryFee`

**更新配置**: `PUT /api/v1/admin/system/configs/{id}`

## 7. 日志与监控

### 7.1 日志记录

配送费计算过程中会记录详细的日志信息，便于问题排查和性能监控：

#### 7.1.1 配置获取日志

```go
logs.Info("开始获取配送费配置，category: deliveryFee")
logs.Info("获取到配置数量: %d", len(configs))
for i, config := range configs {
    logs.Info("配置[%d]: ConfigKey=%s, ConfigValue=%s, Category=%s", 
        i, config.ConfigKey, config.ConfigValue, config.Category)
}
```

#### 7.1.2 计算过程日志

```go
logs.Info("开始计算配送费 - 商家ID: %d, 订单金额: %.2f", merchantID, totalAmount)
logs.Info("配送费配置 - 基础费用: %.2f, 免费门槛: %.2f, 折扣门槛: %.2f, 折扣率: %.2f", 
    baseDeliveryFee, freeDeliveryAmount, discountAmount, discountRate)
```

#### 7.1.3 计算结果日志

```go
// 满额免配送费
logs.Info("满%.2f元免配送费，配送费: 0.00", freeDeliveryAmount)

// 满额折扣
logs.Info("满%.2f元配送费%.0f折，原价: %.2f，折后: %.2f", 
    discountAmount, discountRate*100, baseDeliveryFee, discountedFee)

// 标准配送费
logs.Info("使用标准配送费: %.2f", baseDeliveryFee)
```

#### 7.1.4 错误日志

```go
logs.Error("获取配送费配置失败: %v，使用默认配置", err)
logs.Error("解析配送费配置失败: %v，使用默认配置", err)
logs.Warn("未找到配送费配置，使用默认配置")
```

### 7.2 监控指标

建议监控以下关键指标：

1. **配送费计算成功率**: 监控计算过程中的错误率
2. **配置获取延迟**: 监控配置服务响应时间
3. **缓存命中率**: 监控Redis缓存的命中情况
4. **默认配置使用率**: 监控使用默认配置的频率
5. **配送费分布**: 统计不同配送费金额的分布情况

### 7.3 告警规则

建议设置以下告警规则：

- 配送费计算错误率 > 5%
- 配置获取延迟 > 1000ms
- 缓存命中率 < 90%
- 默认配置使用率 > 10%

## 8. 注意事项

### 8.1 配置更新

1. **实时生效**: 配置更新后立即生效，无需重启服务
2. **缓存刷新**: 系统会自动刷新相关缓存
3. **版本控制**: 配置变更会更新版本号

### 8.2 异常处理

1. **配置缺失**: 当配送费配置不存在时，使用默认配送费10元
2. **配置解析失败**: 配置JSON格式错误时，使用默认配送费10元
3. **参数异常**: 配置参数类型错误时，使用对应的默认值

### 8.3 业务规则

1. **最小配送费**: 配送费不能为负数
2. **优惠互斥**: 满额免配送费优先级高于配送费折扣
3. **商家独立**: 多商家订单中，每个商家的配送费独立计算
4. **金额精度**: 配送费计算结果保留2位小数

### 8.4 性能考虑

1. **配置缓存**: 系统会缓存配送费配置，减少数据库查询
2. **批量计算**: 多商家订单的配送费会批量计算
3. **日志记录**: 详细记录配送费计算过程，便于问题排查

### 8.5 扩展功能

### 8.6 开发调试

#### 8.6.1 本地调试

在本地开发环境中调试配送费计算：

1. **查看日志**: 关注 `logs.Info` 输出的计算过程
2. **配置验证**: 确保 `system_config` 表中存在正确的配送费配置
3. **缓存清理**: 必要时清理Redis缓存以获取最新配置

#### 8.6.2 测试用例

```go
// 测试配送费计算
func TestCalculateDeliveryFee(t *testing.T) {
    service := &multiMerchantOrderService{}
    
    // 测试基础配送费
    fee := service.calculateDeliveryFee(1, 10.0)
    assert.Equal(t, 10.0, fee)
    
    // 测试满额免配送费
    fee = service.calculateDeliveryFee(1, 50.0)
    assert.Equal(t, 0.0, fee)
    
    // 测试满额折扣
    fee = service.calculateDeliveryFee(1, 30.0)
    assert.Equal(t, 8.0, fee)
}
```

#### 8.6.3 配置管理工具

可以通过以下方式管理配送费配置：

1. **管理后台**: 通过Web管理界面修改配置
2. **API接口**: 通过REST API更新配置
3. **数据库直接操作**: 紧急情况下直接修改数据库

```sql
-- 查询当前配送费配置
SELECT * FROM system_config WHERE config_key = 'deliveryFee';

-- 更新配送费配置
UPDATE system_config 
SET config_value = '{"deliveryBaseFee":8.0,"deliveryFreeEnabled":true,"deliveryFreeAmount":50.0}'
WHERE config_key = 'deliveryFee';
```

当前配置中包含以下预留字段，用于未来功能扩展：

- `deliveryKmFee`: 基于距离的配送费计算
- `deliveryMinOrderAmount`: 最低起送金额限制

这些字段目前不参与配送费计算，但已在配置结构中预留。