# 商家操作示例指南

> 本文档提供了商家在外卖平台上新增商品和管理促销活动的详细操作示例，包括界面截图、请求参数和响应结果的说明。

## 目录

- [1. 商家新增商品示例](#1-商家新增商品示例)
  - [1.1 商品基本信息填写](#11-商品基本信息填写)
  - [1.2 商品规格设置](#12-商品规格设置)
  - [1.3 商品图片上传](#13-商品图片上传)
  - [1.4 商品提交与审核](#14-商品提交与审核)
- [2. 商家创建促销活动示例](#2-商家创建促销活动示例)
  - [2.1 折扣促销活动](#21-折扣促销活动)
  - [2.2 满减优惠活动](#22-满减优惠活动)
  - [2.3 优惠券发放活动](#23-优惠券发放活动)
  - [2.4 活动预览和发布](#24-活动预览和发布)

## 1. 商家新增商品示例

商家登录平台后，可以通过管理后台新增商品。以下是详细的操作步骤和示例。

### 1.1 商品基本信息填写

#### 界面示例

```
+-------------------------------------------------------+
|                    新增商品                           |
+-------------------------------------------------------+
| 商品名称: [例：香辣鸡腿堡                   ] *必填    |
|                                                       |
| 商品分类: [汉堡     v] *必填                          |
|                                                       |
| 商品描述:                                             |
| [香辣鸡腿搭配新鲜生菜和特制酱料，口感鲜美   ] *必填    |
|                                                       |
| 商品价格: [18.8          ] 元 *必填                   |
|                                                       |
| 商品标签: [□热销 ■新品 □推荐                ]         |
|                                                       |
| 库存数量: [100           ] *必填                      |
|                                                       |
| 商品排序: [10            ]                            |
|                                                       |
| 上架状态: [○待提交 ●立即上架 ○暂不上架      ]         |
+-------------------------------------------------------+
```

#### API请求示例

```json
POST /merchant/api/takeout/foods
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "香辣鸡腿堡",
  "category_id": 12,
  "description": "香辣鸡腿搭配新鲜生菜和特制酱料，口感鲜美",
  "price": 18.8,
  "tags": ["新品"],
  "stock": 100,
  "sort_order": 10,
  "status": 1
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1024,
    "merchant_id": 502,
    "name": "香辣鸡腿堡",
    "category_id": 12,
    "description": "香辣鸡腿搭配新鲜生菜和特制酱料，口感鲜美",
    "price": 18.8,
    "tags": ["新品"],
    "stock": 100,
    "sales": 0,
    "sort_order": 10,
    "status": 1,
    "created_at": "2025-05-14T11:30:45+08:00",
    "updated_at": "2025-05-14T11:30:45+08:00"
  }
}
```

### 1.2 商品规格设置

对于存在多种规格选择的商品（如尺寸、口味、配料等），商家可以设置不同规格及对应价格。

#### 界面示例

```
+-------------------------------------------------------+
|                    商品规格设置                           |
+-------------------------------------------------------+
| 是否启用多规格: [√] 是  [ ] 否                          |
|                                                       |
| 添加规格组:                                              |
|                                                       |
| [规格组名称: 尺寸        ]  [+ 添加规格组]                   |
|                                                       |
| 规格选项:                                                |
|                                                       |
| [√] 大杯    额外价格: [+2.0] 元                          |
| [√] 中杯    额外价格: [+0.0] 元  (默认)                   |
| [√] 小杯    额外价格: [-1.0] 元                          |
| [+ 添加规格选项]                                         |
|                                                       |
| [规格组名称: 辣度        ]  [+ 添加规格组]                   |
|                                                       |
| 规格选项:                                                |
|                                                       |
| [√] 不辣    额外价格: [+0.0] 元                          |
| [√] 微辣    额外价格: [+0.0] 元  (默认)                   |
| [√] 中辣    额外价格: [+0.0] 元                          |
| [√] 特辣    额外价格: [+0.0] 元                          |
| [+ 添加规格选项]                                         |
|                                                       |
|                   [保存规格设置]                         |
+-------------------------------------------------------+
```

#### API请求示例

```json
POST /merchant/api/takeout/foods/1024/specs
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "specs": [
    {
      "name": "尺寸",
      "options": [
        {
          "name": "大杯",
          "price_adjustment": 2.0,
          "is_default": false
        },
        {
          "name": "中杯",
          "price_adjustment": 0.0,
          "is_default": true
        },
        {
          "name": "小杯",
          "price_adjustment": -1.0,
          "is_default": false
        }
      ]
    },
    {
      "name": "辣度",
      "options": [
        {
          "name": "不辣",
          "price_adjustment": 0.0,
          "is_default": false
        },
        {
          "name": "微辣",
          "price_adjustment": 0.0,
          "is_default": true
        },
        {
          "name": "中辣",
          "price_adjustment": 0.0,
          "is_default": false
        },
        {
          "name": "特辣",
          "price_adjustment": 0.0,
          "is_default": false
        }
      ]
    }
  ]
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "food_id": 1024,
    "specs": [
      {
        "id": 201,
        "name": "尺寸",
        "options": [
          {
            "id": 501,
            "name": "大杯",
            "price_adjustment": 2.0,
            "is_default": false
          },
          {
            "id": 502,
            "name": "中杯",
            "price_adjustment": 0.0,
            "is_default": true
          },
          {
            "id": 503,
            "name": "小杯",
            "price_adjustment": -1.0,
            "is_default": false
          }
        ]
      },
      {
        "id": 202,
        "name": "辣度",
        "options": [
          {
            "id": 504,
            "name": "不辣",
            "price_adjustment": 0.0,
            "is_default": false
          },
          {
            "id": 505,
            "name": "微辣",
            "price_adjustment": 0.0,
            "is_default": true
          },
          {
            "id": 506,
            "name": "中辣",
            "price_adjustment": 0.0,
            "is_default": false
          },
          {
            "id": 507,
            "name": "特辣",
            "price_adjustment": 0.0,
            "is_default": false
          }
        ]
      }
    ]
  }
}
```

### 1.3 商品图片上传

#### 界面示例

```
+-------------------------------------------------------+
|                   商品图片上传                            |
+-------------------------------------------------------+
|                                                       |
| 主图上传：(必传，建议尺寸800×600像素)                      |
|                                                       |
| +---------------+                                     |
| |               |                                     |
| |     点击或    |    [ 本地上传 ]    [ 从图库选择 ]          |
| |   拖拽上传图片  |                                     |
| |               |                                     |
| +---------------+                                     |
|                                                       |
| 详情图上传：(可多张，展示商品细节)                          |
|                                                       |
| +---------------+  +---------------+  +---------------+ |
| |               |  |               |  |               | |
| |   图片预览1    |  |   图片预览2    |  |    点击上传    | |
| |               |  |               |  |               | |
| | [设为主图][删除] |  | [设为主图][删除] |  |               | |
| +---------------+  +---------------+  +---------------+ |
|                                                       |
| [ 保存图片 ]                                           |
+-------------------------------------------------------+
```

#### API请求示例

```
POST /merchant/api/takeout/foods/1024/images
Content-Type: multipart/form-data
Authorization: Bearer {merchant_token}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="main_image"; filename="main.jpg"
Content-Type: image/jpeg

(二进制图片数据)
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="detail_images[]"; filename="detail1.jpg"
Content-Type: image/jpeg

(二进制图片数据)
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="detail_images[]"; filename="detail2.jpg"
Content-Type: image/jpeg

(二进制图片数据)
------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "food_id": 1024,
    "main_image": "https://cdn.example.com/takeout/foods/1024/main.jpg",
    "detail_images": [
      "https://cdn.example.com/takeout/foods/1024/detail1.jpg",
      "https://cdn.example.com/takeout/foods/1024/detail2.jpg"
    ]
  }
}
```

### 1.4 商品提交与审核

完成商品基本信息、规格和图片上传后，商家可以提交商品等待平台审核。

#### 界面示例

```
+-------------------------------------------------------+
|                   商品提交审核                           |
+-------------------------------------------------------+
|                                                       |
| 商品信息预览：                                           |
|                                                       |
| 商品ID: 1024                                           |
| 商品名称: 香辣鸡腿堡                                    |
| 商品分类: 汉堡                                         |
| 商品价格: 18.8元 (有多种规格)                           |
| 商品图片: 已上传 (1张主图，2张详情图)                      |
|                                                       |
| 提交说明：                                              |
| [新品上架，主打香辣口味，希望能通过审核           ]         |
|                                                       |
| [ 提交审核 ]           [ 保存草稿 ]                      |
+-------------------------------------------------------+
```

#### API请求示例

```json
POST /merchant/api/takeout/foods/1024/submit
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "submit_note": "新品上架，主打香辣口味，希望能通过审核"
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "商品已提交审核",
  "data": {
    "food_id": 1024,
    "audit_status": 1,  // 1表示待审核状态
    "submit_time": "2025-05-14T11:45:21+08:00"
  }
}
```

#### 审核结果通知

商品审核通过后，商家会收到通知：

```
+-------------------------------------------------------+
|                 商品审核通知                             |
+-------------------------------------------------------+
|                                                       |
| ✓ 恭喜，您的商品已通过审核！                               |
|                                                       |
| 商品ID: 1024                                           |
| 商品名称: 香辣鸡腿堡                                    |
| 审核时间: 2025-05-14 14:30:45                         |
| 审核人员: admin001                                     |
|                                                       |
| 审核意见:                                              |
| 商品符合平台规范，已审核通过。建议增加一些商品的细节描述。      |
|                                                       |
| 商品状态: [ 立即上架 ]  [ 暂不上架 ]                     |
|                                                       |
| [ 查看详情 ]                                           |
+-------------------------------------------------------+
```

## 2. 商家创建促销活动示例

商家可以创建多种类型的促销活动，包括商品折扣、满减优惠和优惠券发放等。以下是详细的操作步骤和示例。

### 2.1 折扣促销活动

#### 界面示例

```
+-------------------------------------------------------+
|                  新建折扣促销                            |
+-------------------------------------------------------+
|                                                       |
| 活动基本信息                                            |
|                                                       |
| 活动名称: [夏日特惠折扣                     ] *必填    |
|                                                       |
| 活动描述: [夏日来临，精选商品享受8折优惠         ] *必填    |
|                                                       |
| 活动类型: [商品折扣  v]                                  |
|                                                       |
| 开始时间: [2025-05-20] [00:00:00]                        |
|                                                       |
| 结束时间: [2025-06-20] [23:59:59]                        |
|                                                       |
| 最大使用次数: [1000          ]   (0表示不限制)              |
|                                                       |
|                                                       |
| 选择参与活动的商品                                       |
|                                                       |
| [√] 选择分类: [汉堡     v] [+ 添加]                       |
| [ ] 选择指定商品  [选择商品]                              |
|                                                       |
| 选中商品(12): 香辣鸡腿堡, 爆浆牛肉堡, 双层芝士堡...         |
|                                                       |
| 折扣设置                                                |
|                                                       |
| 折扣类型: [●百分比折扣] [○固定金额优惠]                    |
|                                                       |
| 折扣比例: [8.0           ] (如输入8表示8折)                 |
|                                                       |
| [ 保存为草稿 ]    [ 预览 ]    [ 发布活动 ]                  |
+-------------------------------------------------------+
```

#### API请求示例

```json
POST /merchant/api/takeout/promotions
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "夏日特惠折扣",
  "description": "夏日来临，精选商品享受8折优惠",
  "type": 2,  // 折扣促销
  "start_time": "2025-05-20 00:00:00",
  "end_time": "2025-06-20 23:59:59",
  "max_usage_count": 1000,
  "status": 1,  // 活动中
  "rules": "{\"discount_type\":1,\"discount_value\":8.0}",
  "food_ids": [1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035]
}
```

#### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 501,
    "merchant_id": 502,
    "name": "夏日特惠折扣",
    "description": "夏日来临，精选商品享受8折优惠",
    "type": 2,
    "start_time": "2025-05-20T00:00:00+08:00",
    "end_time": "2025-06-20T23:59:59+08:00",
    "status": 1,
    "rules": "{\"discount_type\":1,\"discount_value\":8.0}",
    "max_usage_count": 1000,
    "usage_count": 0,
    "created_at": "2025-05-14T14:15:23+08:00",
    "updated_at": "2025-05-14T14:15:23+08:00",
    "affected_foods": [
      {
        "food_id": 1024,
        "food_name": "香辣鸡腿堡",
        "original_price": 18.8,
        "discount_price": 15.04
      },
      {
        "food_id": 1025,
        "food_name": "爆浆牛肉堡",
        "original_price": 22.5,
        "discount_price": 18.0
      },
      // ... 其他商品 ...
    ]
  }
}
```
