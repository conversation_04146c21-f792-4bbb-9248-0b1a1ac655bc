# 外卖食品审核API文档

## 1. 接口说明

本接口用于管理员审核商家提交的外卖食品信息。审核通过后，食品将上架显示；审核不通过需要填写拒绝原因。

## 2. 接口详情

### 2.1 基本信息

- **接口路径**: `/admin/takeout/foods/:id/audit`
- **请求方法**: PUT
- **接口描述**: 审核外卖食品
- **权限要求**: 需要管理员权限

### 2.2 请求参数

#### 路径参数

| 参数名 | 类型   | 必填 | 描述       |
| ------ | ------ | ---- | ---------- |
| id     | int64  | 是   | 食品ID     |

#### 请求体参数

```json
{
  "audit_status": 1,
  "audit_reason": "审核通过，商品信息完整"
}
```

| 参数名        | 类型   | 必填 | 描述                                                         |
| ------------- | ------ | ---- | ------------------------------------------------------------ |
| audit_status  | int    | 是   | 审核状态：1-通过，2-拒绝                                       |
| audit_reason  | string | 否   | 审核意见或拒绝原因，审核通过时可不填，拒绝时建议填写详细原因 |

> 注意：auditor_id（审核人ID）会从JWT token中自动获取，无需前端传递。

### 2.3 响应参数

#### 成功响应

HTTP状态码: 200

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 错误响应

HTTP状态码: 4xx/5xx

```json
{
  "code": 400,
  "message": "无效的请求参数",
  "data": null
}
```

### 2.4 状态码说明

| 状态码 | 说明                 |
| ------ | -------------------- |
| 200    | 操作成功             |
| 400    | 请求参数错误         |
| 401    | 未授权，请重新登录   |
| 403    | 没有权限执行此操作   |
| 404    | 食品不存在           |
| 422    | 食品状态不允许此操作 |
| 500    | 服务器内部错误       |

## 3. 使用示例

### 3.1 审核通过

**请求**

```http
PUT /admin/takeout/foods/123/audit
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "audit_status": 1,
  "audit_reason": "商品信息完整，图片清晰，符合要求"
}
```

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.2 审核拒绝

**请求**
```http
PUT /admin/takeout/foods/123/audit
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "audit_status": 2,
  "audit_reason": "商品图片不清晰，请重新上传"
}
```

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 4. 业务规则

1. 只有状态为"待审核"的食品才能进行审核操作
2. 审核通过后，食品状态变更为"已上架"
3. 审核拒绝时，必须填写拒绝原因
4. 只有具有管理员权限的用户才能调用此接口
5. 审核记录会记录审核人、审核时间和审核意见

## 5. 相关状态码

食品状态码定义：

| 状态码 | 状态说明 |
| ------ | -------- |
| 0      | 已下架   |
| 1      | 待审核   |
| 2      | 已上架   |
| 3      | 已拒绝   |
| 4      | 已售罄   |

审核状态码定义：

| 状态码 | 状态说明 |
| ------ | -------- |
| 1      | 审核通过 |
| 2      | 审核拒绝 |

## 6. 错误处理

当发生错误时，接口会返回相应的错误码和错误信息。常见的错误情况包括：

1. 食品不存在
2. 食品当前状态不允许进行审核操作
3. 请求参数不合法
4. 用户未登录或没有权限
5. 服务器内部错误

## 7. 注意事项

1. 审核操作不可逆，请谨慎操作
2. 审核拒绝时，建议提供详细的拒绝原因，方便商家修改
3. 审核通过后，食品会立即上架显示
4. 系统会记录所有审核操作日志，包括操作人、操作时间和操作内容

## 8. 更新日志

| 版本  | 日期       | 描述           | 作者   |
| ----- | ---------- | -------------- | ------ |
| 1.0.0 | 2025-05-20 | 初始版本       | 系统   |
|       |            |                |        |
