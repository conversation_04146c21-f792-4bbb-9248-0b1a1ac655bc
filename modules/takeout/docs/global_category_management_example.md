# 全局商品分类管理示例

> 本文档提供了平台管理员如何管理全局商品分类的详细示例。

## 1. 分类管理界面

### 1.1 分类列表

```
+----------------------------------------------------------------+
|                    全局商品分类管理                             |
+----------------------------------------------------------------+
| 搜索: [                    ] [搜索] [添加分类] [批量操作] [导出] |
+----------------------------------------------------------------+
| 分类名称      | 分类编码  | 层级 | 排序 | 状态  | 操作          |
+----------------------------------------------------------------+
| 美食         | food      | 1   | 1    | 启用  | 编辑 | 删除 |
| ├─ 中餐      | food_zh   | 2   | 1    | 启用  | 编辑 | 删除 |
| │  ├─ 川菜   | food_zh_sc| 3   | 1    | 启用  | 编辑 | 删除 |
| │  └─ 粤菜   | food_zh_gd| 3   | 2    | 启用  | 编辑 | 删除 |
| └─ 西餐      | food_we   | 2   | 2    | 启用  | 编辑 | 删除 |
| 生活服务     | life      | 1   | 2    | 启用  | 编辑 | 删除 |
| 超市便利     | mart      | 1   | 3    | 启用  | 编辑 | 删除 |
+----------------------------------------------------------------+
| 共 7 条记录  每页: 20 条  第 1/1 页  [<] [1] [>]                |
+----------------------------------------------------------------+
```

### 1.2 添加/编辑分类

```
+-------------------------------------------------------+
|                 编辑分类 - 川菜                         |
+-------------------------------------------------------+
| 分类名称: [川菜                          ] *必填        |
| 分类编码: [food_zh_sc                  ] *必填        |
| 父级分类: [中餐                         ▼]             |
| 分类描述: [四川特色菜系，以麻辣鲜香著称               ]  |
| 排序值:   [10                         ] (数字越小越靠前)|
| 状态:    [● 启用] [○ 禁用]                              |
| 分类图标: [ 点击上传 ]                                  |
|                                                       |
| [ 取消 ]                     [ 保存 ]                  |
+-------------------------------------------------------+
```

## 2. API 接口示例

### 2.1 创建分类

```http
POST /admin/api/takeout/global-categories
Content-Type: application/json
Authorization: Bearer {admin_token}

{
  "name": "川菜",
  "code": "food_zh_sc",
  "parent_id": 2,
  "description": "四川特色菜系，以麻辣鲜香著称",
  "sort_order": 10,
  "is_active": true
}
```

### 2.2 获取分类树

```http
GET /admin/api/takeout/global-categories/tree
Authorization: Bearer {admin_token}
```

### 2.3 更新分类

```http
PUT /admin/api/takeout/global-categories/3
Content-Type: application/json
Authorization: Bearer {admin_token}

{
  "name": "川菜",
  "description": "四川特色菜系，以麻辣鲜香著称，包括火锅、水煮鱼等",
  "sort_order": 5,
  "is_active": true
}
```

### 2.4 删除分类

```http
DELETE /admin/api/takeout/global-categories/3
Authorization: Bearer {admin_token}
```

## 3. 分类关联商品

### 3.1 商品关联分类

```
+-------------------------------------------------------+
|                 商品管理 - 编辑商品                     |
+-------------------------------------------------------+
| 商品名称: [水煮鱼                      ]               |
| 分类: [选择分类 ▼] [选择全局分类 ▼]                   |
|                                                       |
| 分类选择器:                                           |
| ┌─────────────────┐  ┌─────────────────┐           |
| │ 美食            │  │ 美食            │           |
| │ ├─ 中餐         │  │ ├─ 中餐        │           |
| │ │  ├─ 川菜      │  │ │  ├─ 川菜     │           |
| │ │  └─ 粤菜      │  │ │  └─ 粤菜     │           |
| └─────────────────┘  └─────────────────┘           |
|                                                       |
| [ 保存 ]                                             |
+-------------------------------------------------------+
```

### 3.2 商品关联分类API

```http
PUT /admin/api/takeout/foods/123
Content-Type: application/json
Authorization: Bearer {admin_token}

{
  "name": "水煮鱼",
  "category_id": 5,
  "global_category_id": 3,
  "price": 88.0
}
```

## 4. 数据统计

### 4.1 分类销售统计

```
+-------------------------------------------------------+
|                 分类销售统计                           |
+-------------------------------------------------------+
| 时间范围: [2025-05-01] 至 [2025-05-21] [查询]         |
|                                                       |
| 排名 | 分类名称   | 订单数 | 销售额   | 商品数 | 占比  |
+-------------------------------------------------------+
| 1   | 川菜      | 1,234 | ¥34,560 | 156   | 28.6% |
| 2   | 粤菜      | 987   | ¥24,672 | 132   | 20.4% |
| 3   | 西餐      | 765   | 18,360  | 98    | 15.2% |
+-------------------------------------------------------+
| 总计                          | 2,986 | ¥77,592 | 386   | 64.2% |
+-------------------------------------------------------+
```

### 4.2 获取分类统计API

```http
GET /admin/api/takeout/statistics/category?start_date=2025-05-01&end_date=2025-05-21
Authorization: Bearer {admin_token}
```

## 5. 批量操作

### 5.1 批量导出分类

```http
GET /admin/api/takeout/global-categories/export
Authorization: Bearer {admin_token}
```

### 5.2 批量导入分类

```http
POST /admin/api/takeout/global-categories/import
Content-Type: multipart/form-data
Authorization: Bearer {admin_token}

{
  "file": "[binary file data]"
}
```

## 6. 常见问题

### 6.1 如何添加子分类？
在分类列表中找到父分类，点击"添加子分类"按钮，填写子分类信息即可。

### 6.2 分类编码有什么要求？
分类编码需唯一，建议使用英文小写字母、数字和下划线组合，如："fast_food"。

### 6.3 删除分类会删除商品吗？
不会删除商品，但会解除商品与该分类的关联关系。

### 6.4 如何调整分类顺序？
通过修改分类的"排序值"，数字越小排序越靠前。
