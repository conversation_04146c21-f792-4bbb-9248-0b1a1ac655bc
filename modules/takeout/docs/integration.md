# 外卖模块集成指南

## 目录

- [1. 模块依赖关系](#1-模块依赖关系)
- [2. 与用户系统集成](#2-与用户系统集成)
- [3. 与支付系统集成](#3-与支付系统集成)
- [4. 与商家系统集成](#4-与商家系统集成)
- [5. 与配送系统集成](#5-与配送系统集成)
- [6. 事件与通知](#6-事件与通知)
- [7. 实现商品审核功能](#7-实现商品审核功能)
- [8. 实现促销功能](#8-实现促销功能)

## 1. 模块依赖关系

外卖模块与系统其他部分有以下依赖关系。

```
+-----------------+     +-----------------+     +-----------------+
|                 |     |                 |     |                 |
|  用户系统     |<--->|  外卖模块     |<--->|  支付系统     |
|                 |     |                 |     |                 |
+-----------------+     +--------^--------+     +-----------------+
                                 |
                                 |
           +-----------------+    |    +-----------------+
           |                 |    |    |                 |
           |  商家系统     |<---+    |  配送系统     |
           |                 |         |                 |
           +-----------------+         +-----------------+
```

## 2. 与用户系统集成

外卖模块需要与用户系统集成，主要用于认证、授权和用户信息管理。

### 2.1 用户认证

外卖模块使用JWT令牌进-行用户认证，以下为购物车控制器中获取用户ID的示例代码：

```go
// 获取用户ID
userID, err := auth.GetUserIDFromContext(c.Ctx)
if err != nil {
    result.HandleError(c.Ctx, err)
    return
}
```

### 2.2 用户权限

不同角色的用户导航到不同的APl接口：

1. **普通用户**：`/api/v1/takeout/...`
2. **商家用户**：`/merchant/api/takeout/...`
3. **管理员**：`/admin/api/takeout/...`

这些端点由不同的中间件进行保-护：

```go
// 客户端中间件（需要登录）
web.NSBefore(middlewares.JWTFilter)

// 商家中间件
web.NSBefore(middlewares.MerchantJWTFilter)

// 管理员中间件
web.NSBefore(middlewares.AdminJWTFilter)
```

## 3. 与支付系统集栠

与支付系统的集成主要在订单模块中实现。

### 3.1 支付流程

1. 创建订单后，调用支付服务生成支付单
2. 返回支付URL或支付参数
3. 待用户支付完成后，支付平台回调通知
4. 系统将订单状态更新为待处理

### 3.2 支付回调

需要实现支付回调接口处理各类支付结果。

```go
// 支付回调
web.NSRouter("/payment/callback/:provider", &controllers.PaymentController{}, "post:Callback")
```

## 4. 与商家系统集成

外卖模块需要与商家系统集成，获取商家信息和订单管理。

### 4.1 获取商家信息

商品列表和详情中需要展示商家信息：

```go
// 在服务中调用商家服务
merchant, err := s.merchantService.GetMerchantByID(merchantID)
if err != nil {
    return nil, err
}

// 填充商家信息
foodDTO.MerchantName = merchant.Name
foodDTO.MerchantLogo = merchant.Logo
```

### 4.2 商家限制

商家可以管理自己的商品和订单，哈管理员可以管理所有商家的商品和订单。

```go
// 验证商品所有权
isOwner, err := c.foodService.ValidateFoodOwnership(foodID, merchantID)
if !isOwner {
    result.HandleError(c.Ctx, result.ErrForbidden)
    return
}
```

## 5. 与配送系统集成

外卖模块需要与配送系统集成，实现订单配送管理。

### 5.1 碰态信息同步

当配送状态变化时，需要更新订单的配送状态：

```go
// 更新配送状态
func (s *takeoutDeliveryService) UpdateDeliveryStatus(orderID int64, status int) error {
    // 获取订单扩展
    orderExt, err := s.orderExtRepo.GetByOrderID(orderID)
    if err != nil {
        return err
    }
    
    // 更新状态
    orderExt.DeliveryStatus = status
    
    // 根据状态更新时间
    if status == models.DeliveryStatusCompleted {
        orderExt.DeliveryCompletedAt = time.Now()
    }
    
    // 保-存变更
    return s.orderExtRepo.Update(orderExt)
}
```

## 6. 事件与通知

外卖模块使用事件系统进行模块间通信，让不同模块对外卖相关事件作函应。

### 6.1 事件定义

```go
// 外卖模块事件常量
const (
    EventOrderCreated          = "takeout.order.created"
    EventOrderStatusChanged    = "takeout.order.status_changed"
    EventOrderDeliveryAssigned = "takeout.order.delivery_assigned"
    EventOrderDeliveryStarted  = "takeout.order.delivery_started"
    EventOrderDeliveryCompleted = "takeout.order.delivery_completed"
    EventFoodApproved          = "takeout.food.approved"
    EventFoodRejected          = "takeout.food.rejected"
)
```

### 6.2 事件触发

```go
// 在订单创建后触发事件
func (s *takeoutOrderService) CreateOrder(req *dto.CreateOrderRequest) (int64, error) {
    // ... 创建订单逻辑 ...
    
    // 触发订单创建事件
    event.Publish(EventOrderCreated, map[string]interface{}{
        "order_id": orderID,
        "merchant_id": order.MerchantID,
        "user_id": order.UserID,
        "amount": order.TotalAmount,
    })
    
    return orderID, nil
}
```

### 6.3 事件监听

```go
// 监听外卖订单事件
func init() {
    // 监听订单创建事件
    event.Subscribe(EventOrderCreated, handleOrderCreated)
    
    // 监听商品审核事件
    event.Subscribe(EventFoodApproved, handleFoodApproved)
    event.Subscribe(EventFoodRejected, handleFoodRejected)
}

// 处理订单创建事件
func handleOrderCreated(data map[string]interface{}) {
    // 处理逻辑
    orderID := data["order_id"].(int64)
    merchantID := data["merchant_id"].(int64)
    
    // 发送通知给商家
    notificationService.SendToMerchant(merchantID, "新订单通知", 
        fmt.Sprintf("您收到了新的订单 #%d", orderID))
}
```

## 7. 实现商品审核功能

外卖模块新增了商品审核功能。在这个流程中，商家创建的商品需要经管理员审核后才能上架销售。

### 7.1 简要实现步骤

1. 在 TakeoutFood 模型中添加审核相关字段：
   - `AuditStatus`：审核状态
   - `AuditorID`：审核人ID
   - `AuditReason`：审核意见
   - `AuditTime`：审核时间

2. 添加审核状态常量

```go
const (
    FoodStatusDraft       = 0 // 草稿状态
    FoodStatusPendingReview = 1 // 待审核状态
    FoodStatusRejected     = 2 // 审核拒绝
    FoodStatusApproved     = 3 // 审核通过
    FoodStatusOnSale       = 4 // 上架销售中
    FoodStatusOffSale      = 5 // 已下架
    FoodStatusSoldOut      = 6 // 已售罄
)
```

3. 添加审核API

```go
// 审核食品
// @router /admin/api/takeout/foods/:id/audit [put]
func (c *AdminTakeoutFoodController) AuditFood() {
    // 获取食品ID
    idStr := c.Ctx.Input.Param(":id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil {
        result.HandleError(c.Ctx, result.ErrInvalidParams)
        return
    }

    // 获取审核人ID
    adminID := c.Ctx.Input.GetData("user_id").(int64)

    // 解析请求参数
    req := dto.AuditFoodRequest{}
    if err := c.ParseRequest(&req); err != nil {
        result.HandleError(c.Ctx, err)
        return
    }
    req.ID = id
    req.AuditorID = adminID

    // 进行食品审核
    if err := c.foodService.AuditFoodForAdmin(&req); err != nil {
        result.HandleError(c.Ctx, err)
        return
    }

    // 返回审核结果
    result.OK(c.Ctx, nil)
}
```

4. 实现审核业务逻辑

```go
// AuditFoodForAdmin 管理员审核食品
func (s *takeoutFoodService) AuditFoodForAdmin(req *dto.AuditFoodRequest) error {
    // 获取食品信息
    food, err := s.foodRepo.GetByID(req.ID)
    if err != nil {
        logs.Error("获取食品信息失败: %v, ID: %d", err, req.ID)
        return errors.New("获取食品信息失败")
    }
    
    // 检查食品状态是否为待审核状态
    if food.Status != models.FoodStatusPendingReview {
        return errors.New("该食品不在待审核状态，无法进行审核操作")
    }
    
    // 更新审核信息
    food.AuditorID = req.AuditorID
    food.AuditStatus = req.AuditStatus
    food.AuditReason = req.AuditReason
    food.AuditTime = time.Now()
    
    // 根据审核结果更新食品状态
    if req.AuditStatus == 1 { // 审核通过
        food.Status = models.FoodStatusApproved
    } else if req.AuditStatus == 2 { // 审核拒绝
        food.Status = models.FoodStatusRejected
    } else {
        return errors.New("无效的审核状态")
    }
    
    // 保-存更新
    err = s.foodRepo.Update(food)
    if err != nil {
        logs.Error("更新食品审核信息失败: %v, 请求: %+v", err, req)
        return errors.New("更新食品审核信息失败")
    }
    
    // 触发审核事件
    event := models.EventFoodApproved
    if req.AuditStatus == 2 {
        event = models.EventFoodRejected
    }
    
    // 发布审核结果事件
    eventbus.Publish(event, map[string]interface{}{
        "food_id": food.ID,
        "merchant_id": food.MerchantID,
        "audit_reason": food.AuditReason,
    })
    
    return nil
}
```

### 7.2 审核状态流转

商品从"待审核"到"上架销售"需要经过以下状态流转过程。

```
商家创建商品 -> 待审核 -> 审核通过 -> 商家上架销售 -> 上架销售中
                                 -> 审核拒绝 -> 商家修-改 -> 重新提交
```

### 7.3 实时变更通知

1. 当商品审核状态发生变化时，系统应向商家发送通知：
   - 审核通过通知
   - 审核拒绝 通知，并附带拒绝 原因

2. 在管理员控制台中展示待审核商品数量的进-度提醒


## 8. 实现促销功能

外卖模块新增促销功能，支持促销活动、优惠券和商品折扣等多种促销方式。

### 8.1 实现步骤

1. 新增促销相关模型

```go
// 促销活动模型
type TakeoutPromotion struct {
    ID            int64     // 活动ID
    MerchantID    int64     // 商户ID
    Name          string    // 活动名称
    Description   string    // 活动描述
    Type          int       // 活动类型
    StartTime     time.Time // 开始时间
    EndTime       time.Time // 结束时间
    Status        int       // 状态
    Rules         string    // 活动规则
    MaxUsageCount int       // 最大使用次数
    UsageCount    int       // 已使用次数
    CreatedAt     time.Time // 创建时间
    UpdatedAt     time.Time // 更新时间
}

// 优惠券模型
type TakeoutCoupon struct {
    ID                int64   // 优惠券ID
    PromotionID       int64   // 关联活动ID
    MerchantID        int64   // 商户ID
    Code              string  // 优惠券码
    Name              string  // 优惠券名称
    Description       string  // 描述
    Type              int     // 优惠券类型
    Amount            float64 // 金额/折扣值
    MinOrderAmount    float64 // 最低订单金额要求
    MaxDiscountAmount float64 // 最高优惠金额
    ApplyToAll        bool    // 是否适用于所有商品
    ApplyToCategories string   // 适用分类ID
    ApplyToFoods      string   // 适用商品ID
    // ... 更多字段 ...
}

// 用户优惠券模型
type TakeoutUserCoupon struct {
    ID         int64     // ID
    UserID     int64     // 用户ID
    CouponID   int64     // 优惠券ID
    Status     int       // 状态
    UsedTime   time.Time // 使用时间
    OrderID    int64     // 关联订单ID
    CreatedAt  time.Time // 创建时间
    UpdatedAt  time.Time // 更新时间
}

// 商品促销关联模型
type TakeoutFoodPromotion struct {
    ID           int64     // ID
    FoodID       int64     // 商品ID
    PromotionID  int64     // 促销活动ID
    DiscountType int       // 折扣类型
    DiscountValue float64   // 折扣值
    CreatedAt    time.Time // 创建时间
    UpdatedAt    time.Time // 更新时间
}
```

2. 添加状态和类型常量

```go
// 活动类型常量
const (
    PromotionTypeFirstOrder = 1 // 首单签到
    PromotionTypeDiscount   = 2 // 折扣促销
    PromotionTypeCoupon     = 3 // 优惠券活动
    PromotionTypeFlashSale  = 4 // 限时特价
)

// 活动状态常量
const (
    PromotionStatusDraft     = 0 // 草稿状态
    PromotionStatusActive    = 1 // 活动中
    PromotionStatusPaused    = 2 // 暂停
    PromotionStatusFinished  = 3 // 已结束
    PromotionStatusCancelled = 4 // 已取消
)

// 优惠券类型常量
const (
    CouponTypeAmount   = 1 // 满减券
    CouponTypeDiscount = 2 // 折扣券
)

// 优惠券状态常量
const (
    CouponStatusUnused    = 0 // 可用
    CouponStatusUsed      = 1 // 已使用
    CouponStatusExpired   = 2 // 已过期
)
```

3. 实现促销服务

```go
// 创建促销活动
func (s *TakeoutPromotionService) CreatePromotion(req *dto.CreatePromotionRequest) (*models.TakeoutPromotion, error) {
    // 解析时间
    startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime)
    if err != nil {
        return nil, errors.New("开始时间格式错误")
    }
    
    endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime)
    if err != nil {
        return nil, errors.New("结束时间格式错误")
    }
    
    // 创建活动对象
    promotion := &models.TakeoutPromotion{
        MerchantID:    req.MerchantID,
        Name:          req.Name,
        Description:   req.Description,
        Type:          req.Type,
        StartTime:     startTime,
        EndTime:       endTime,
        Status:        models.PromotionStatusDraft,
        Rules:         req.Rules,
        MaxUsageCount: req.MaxUsageCount,
        CreatedAt:     time.Now(),
        UpdatedAt:     time.Now(),
    }
    
    // 保存到数据库
    id, err := s.promotionRepo.Create(promotion)
    if err != nil {
        return nil, err
    }
    
    promotion.ID = id
    return promotion, nil
}
```

4. 实现订单优惠计算逻辑

```go
// 计算订单优惠
func (s *TakeoutPromotionService) CalculateOrderDiscount(orderID int64, userID int64) (*dto.OrderDiscountInfo, error) {
    // 获取订单信息
    order, err := s.orderService.GetOrderByID(orderID)
    if err != nil {
        return nil, err
    }
    
    // 初始化返回对象
    discountInfo := &dto.OrderDiscountInfo{
        OrderID:        orderID,
        OriginalAmount: order.TotalAmount,
        DiscountAmount: 0,
        FinalAmount:    order.TotalAmount,
    }
    
    // 获取用户选择的优惠券
    selectedCouponID := order.CouponID
    if selectedCouponID > 0 {
        couponResult, err := s.couponService.CheckCouponAvailability(userID, selectedCouponID, order.TotalAmount)
        if err == nil && couponResult.Valid {
            // 添加优惠券优惠
            discountInfo.DiscountAmount += couponResult.DiscountAmount
            discountInfo.CouponDiscount = couponResult.DiscountAmount
            discountInfo.CouponID = selectedCouponID
        }
    }
    
    // 计算最终金额
    discountInfo.FinalAmount = discountInfo.OriginalAmount - discountInfo.DiscountAmount
    if discountInfo.FinalAmount < 0 {
        discountInfo.FinalAmount = 0
    }
    
    return discountInfo, nil
}
```

5. 优惠券验证逻辑

```go
// 检查优惠券是否可用
func (s *TakeoutCouponService) CheckCouponAvailability(userID int64, couponID int64, totalAmount float64) (*dto.CouponValidationResult, error) {
    // 获取优惠券信息
    coupon, err := s.couponRepo.GetByID(couponID)
    if err != nil {
        return &dto.CouponValidationResult{
            CouponID: couponID,
            Valid:    false,
            Message:  "优惠券不存在",
        }, nil
    }
    
    // 检查优惠券是否过期
    now := time.Now()
    if now.Before(coupon.StartTime) || now.After(coupon.EndTime) {
        return &dto.CouponValidationResult{
            CouponID: couponID,
            Valid:    false,
            Message:  "优惠券不在有效期内",
        }, nil
    }
    
    // 检查订单金额限制
    if totalAmount < coupon.MinOrderAmount {
        return &dto.CouponValidationResult{
            CouponID: couponID,
            Valid:    false,
            Message:  fmt.Sprintf("订单金额不满足优惠券使用条件，最低需要%.2f元", coupon.MinOrderAmount),
        }, nil
    }
    
    // 计算优惠金额
    var discountAmount float64
    if coupon.Type == models.CouponTypeAmount {
        // 满减券
        discountAmount = coupon.Amount
    } else if coupon.Type == models.CouponTypeDiscount {
        // 折扣券
        discountRate := coupon.Amount / 10.0 // 如8.5表示85折
        discountAmount = totalAmount * (1 - discountRate)
        
        // 如果有最高优惠限制
        if coupon.MaxDiscountAmount > 0 && discountAmount > coupon.MaxDiscountAmount {
            discountAmount = coupon.MaxDiscountAmount
        }
    }
    
    return &dto.CouponValidationResult{
        CouponID:       couponID,
        Valid:          true,
        DiscountAmount: discountAmount,
        Message:        "优惠券可用",
    }, nil
}
```

### 8.2 促销事件

添加相关促销事件，用于周知其他模块内的变化。

```go
// 促销活动事件
const (
    EventPromotionCreated  = "takeout.promotion.created"
    EventPromotionStarted  = "takeout.promotion.started"
    EventPromotionEnded    = "takeout.promotion.ended"
    EventCouponIssued      = "takeout.coupon.issued"
    EventCouponUsed        = "takeout.coupon.used"
)

// 发布促销活动开始事件
func (s *TakeoutPromotionService) PublishPromotionStartedEvent(promotionID int64) {
    eventbus.Publish(EventPromotionStarted, map[string]interface{}{
        "promotion_id": promotionID,
        "start_time":   time.Now(),
    })
}
```

### 8.3 促销状态流转

促销活动状态流转图：

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  草稿状态      +------>|  活动中        +------>|  已结束        |
|                |      |                |      |                |
+----------------+      +-------+--------+      +----------------+
                                |
                                |
                                v
                         +----------------+
                         |                |
                         |  已取消        |
                         |                |
                         +----------------+
```

### 8.4 定时任务

添加定时任务管理促销活动状态和优惠券状态：

```go
// 初始化定时任务
func InitPromotionTasks() {
    // 每小时检查促销活动状态
    go startPromotionStatusUpdater()
    
    // 每天检查优惠券过期状态
    go startCouponExpirationChecker()
}

// 检查并更新促销活动状态
func startPromotionStatusUpdater() {
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for range ticker.C {
        updatePromotionStatus()
    }
}

// 更新促销活动状态
func updatePromotionStatus() {
    now := time.Now()
    promotionService := services.NewTakeoutPromotionService(...)
    
    // 更新到活动中状态
    promotionService.UpdatePromotionsToActive(now)
    
    // 更新到已结束状态
    promotionService.UpdatePromotionsToFinished(now)
}
```
