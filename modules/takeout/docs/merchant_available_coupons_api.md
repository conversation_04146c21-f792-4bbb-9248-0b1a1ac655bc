# 商家可领取优惠券API接口文档

## 接口概述

本文档描述了新增的商家可领取优惠券和促销信息API接口，用于展示商家发布的优惠券模板和促销活动，供用户浏览和领取。

## API接口详情

### 获取商家可领取的优惠券和促销信息

**接口地址**: `GET /api/v1/user/takeout/merchants/{merchant_id}/available-coupons`

**接口描述**: 获取指定商家发布的可领取优惠券模板和促销活动信息

**请求参数**:
- `merchant_id` (路径参数): 商家ID

**请求示例**:
```http
GET /api/v1/user/takeout/merchants/123/available-coupons
Authorization: Bearer {token}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "merchant_id": 123,
    "merchant_name": "美味餐厅",
    "has_promotion": true,
    "has_coupon": true,
    "promotions": [
      {
        "id": 1,
        "name": "首单优惠",
        "description": "新用户首单立减10元",
        "type": 1,
        "type_name": "首单优惠",
        "rules": "满30元可用",
        "start_time": "2025-01-01 00:00:00",
        "end_time": "2025-12-31 23:59:59"
      }
    ],
    "coupons": [
      {
        "id": 1,
        "promotion_id": 1,
        "merchant_id": 123,
        "name": "满30减10优惠券",
        "description": "满30元立减10元",
        "type": 1,
        "type_text": "满减券",
        "amount": 10.00,
        "min_order_amount": 30.00,
        "max_discount_amount": 10.00,
        "apply_to_all": true,
        "apply_to_categories": "",
        "apply_to_foods": "",
        "exclude_foods": "",
        "user_level_limit": 0,
        "per_user_limit": 1,
        "daily_limit": 100,
        "total_limit": 1000,
        "start_time": "2025-01-01T00:00:00Z",
        "end_time": "2025-12-31T23:59:59Z",
        "status": 1,
        "status_text": "未使用",
        "created_at": "2025-01-15T10:00:00Z"
      }
    ]
  }
}
```

## 数据结构说明

### MerchantAvailableCouponsInfo

| 字段名 | 类型 | 说明 |
|--------|------|------|
| merchant_id | int64 | 商家ID |
| merchant_name | string | 商家名称 |
| has_promotion | bool | 是否有促销活动 |
| has_coupon | bool | 是否有可领取优惠券 |
| promotions | []PromotionInfoDTO | 促销活动列表 |
| coupons | []CouponResponse | 可领取优惠券列表 |

### PromotionInfoDTO

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 促销活动ID |
| name | string | 促销活动名称 |
| description | string | 促销活动描述 |
| type | int | 促销活动类型 |
| type_name | string | 促销活动类型名称 |
| rules | string | 促销规则 |
| start_time | string | 开始时间 |
| end_time | string | 结束时间 |

### CouponResponse

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 优惠券ID |
| promotion_id | int64 | 关联活动ID |
| merchant_id | int64 | 商户ID |
| name | string | 优惠券名称 |
| description | string | 描述 |
| type | int | 优惠券类型(1:满减券,2:折扣券,3:商品兑换券) |
| type_text | string | 类型文本 |
| amount | float64 | 金额/折扣值 |
| min_order_amount | float64 | 最低订单金额要求 |
| max_discount_amount | float64 | 最高优惠金额 |
| apply_to_all | bool | 是否适用于所有商品 |
| apply_to_categories | string | 适用分类ID |
| apply_to_foods | string | 适用商品ID |
| exclude_foods | string | 排除商品ID |
| user_level_limit | int | 用户等级限制 |
| per_user_limit | int | 每人可领取次数 |
| daily_limit | int | 每日发放限制 |
| total_limit | int | 总发放数量限制 |
| start_time | time.Time | 开始时间 |
| end_time | time.Time | 结束时间 |
| status | int | 状态 |
| status_text | string | 状态文本 |
| created_at | time.Time | 创建时间 |

## 业务逻辑说明

1. **商家状态检查**: 只返回已审核通过的商家的优惠券和促销信息
2. **优惠券状态**: 只返回有效期内且状态正常的优惠券模板
3. **促销活动**: 只返回当前时间内有效的促销活动
4. **数据来源**: 
   - 促销信息来自促销活动表
   - 优惠券信息来自优惠券模板表（非用户优惠券实例）

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 商家不存在 |
| 403 | 商家未审核通过 |
| 500 | 服务器内部错误 |

## 使用场景

1. **商家详情页**: 展示商家的优惠券和促销信息
2. **优惠券中心**: 用户浏览可领取的优惠券
3. **促销活动页**: 展示商家的促销活动详情

## 注意事项

1. 此接口返回的是优惠券模板信息，用户需要通过优惠券领取接口来获得优惠券实例
2. 接口需要用户登录认证
3. 返回的优惠券信息包含领取限制，前端需要根据这些信息判断用户是否可以领取