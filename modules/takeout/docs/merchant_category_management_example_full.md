# 商家管理商品分类页面示例

## 概述

本文档提供了商家管理外卖商品分类的页面示例和操作流程，旨在为前端开发人员提供UI设计和功能实现的参考。

## 页面布局

### 1. 分类管理主页面

![分类管理主页面示意图](../assets/images/category_management_main.png)

**页面组成部分：**

- **顶部操作栏**：包含搜索框、新增分类按钮
- **左侧分类树**：以树形结构展示所有分类
- **右侧分类列表**：以表格形式展示当前选中分类及其子分类
- **分页控件**：控制分类列表的分页展示

### 2. 分类详情/编辑页面

![分类详情/编辑页面示意图](../assets/images/category_detail_edit.png)

**页面组成部分：**

- **基本信息区域**：分类名称、描述、排序权重等基本信息
- **分类图片上传区**：上传分类图片
- **父分类选择器**：选择上级分类
- **可见性设置**：控制分类是否在前端可见
- **操作按钮**：保存、取消、删除等操作按钮

## 功能描述

### 1. 分类列表查询

- **接口**：`GET /merchant/api/takeout/categories`
- **功能**：查询商家的所有分类，支持按名称关键字搜索、按父分类ID筛选
- **参数**：
  - `keyword`：关键字搜索
  - `page`：页码，默认1
  - `page_size`：每页数量，默认10
  - `parent_id`：父分类ID，可选
  - `include_children`：是否包含子分类，可选

**示例请求：**

```http
GET /merchant/api/takeout/categories?keyword=主食&page=1&page_size=10
```

**示例响应：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 5,
    "list": [
      {
        "id": 1,
        "merchant_id": 1001,
        "name": "主食类",
        "description": "各类主食，包括米饭、面食等",
        "image": "https://example.com/images/main_food.jpg",
        "parent_id": 0,
        "level": 1,
        "sort_order": 1,
        "is_visible": true,
        "created_at": "2025-05-10T10:00:00Z",
        "updated_at": "2025-05-10T10:00:00Z",
        "food_count": 15,
        "children": [
          {
            "id": 2,
            "merchant_id": 1001,
            "name": "米饭",
            "description": "各种米饭",
            "image": "https://example.com/images/rice.jpg",
            "parent_id": 1,
            "level": 2,
            "sort_order": 1,
            "is_visible": true,
            "created_at": "2025-05-10T10:05:00Z",
            "updated_at": "2025-05-10T10:05:00Z",
            "food_count": 5,
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 2. 获取分类详情

- **接口**：`GET /merchant/api/takeout/categories/:id`
- **功能**：获取指定ID的分类详情
- **参数**：
  - `id`：分类ID (路径参数)

**示例请求：**

```http
GET /merchant/api/takeout/categories/1
```

**示例响应：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "merchant_id": 1001,
    "name": "主食类",
    "description": "各类主食，包括米饭、面食等",
    "image": "https://example.com/images/main_food.jpg",
    "parent_id": 0,
    "level": 1,
    "sort_order": 1,
    "is_visible": true,
    "created_at": "2025-05-10T10:00:00Z",
    "updated_at": "2025-05-10T10:00:00Z",
    "food_count": 15,
    "children": []
  }
}
```

### 3. 创建分类

- **接口**：`POST /merchant/api/takeout/categories`
- **功能**：创建新的商品分类
- **请求体**：

```json
{
  "name": "饮品类",
  "description": "各类饮品，包括茶饮、果汁等",
  "image": "https://example.com/images/drinks.jpg",
  "parent_id": 0,
  "sort_order": 2,
  "is_visible": true
}
```

**示例响应：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 3
  }
}
```

### 4. 更新分类

- **接口**：`PUT /merchant/api/takeout/categories/:id`
- **功能**：更新指定ID的分类信息
- **参数**：
  - `id`：分类ID (路径参数)
- **请求体**：

```json
{
  "name": "饮品类",
  "description": "更新后的饮品描述",
  "image": "https://example.com/images/drinks_updated.jpg",
  "parent_id": 0,
  "sort_order": 3,
  "is_visible": true
}
```

**示例响应：**

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 5. 删除分类

- **接口**：`DELETE /merchant/api/takeout/categories/:id`
- **功能**：删除指定ID的分类
- **参数**：
  - `id`：分类ID (路径参数)

**示例请求：**

```http
DELETE /merchant/api/takeout/categories/3
```

**示例响应：**

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

## 操作流程

### 创建分类流程

1. 商家登录商家后台
2. 进入「外卖管理」-「分类管理」页面
3. 点击「新增分类」按钮，打开分类创建表单
4. 填写分类基本信息（名称、描述等）
5. 上传分类图片（可选）
6. 选择父分类（若创建子分类）
7. 设置排序权重和可见性
8. 点击「保存」按钮提交表单
9. 系统创建成功后自动刷新分类列表

### 更新分类流程

1. 商家登录商家后台
2. 进入「外卖管理」-「分类管理」页面
3. 在分类列表中找到需要编辑的分类
4. 点击「编辑」按钮，打开分类编辑表单
5. 修改分类信息
6. 点击「保存」按钮提交修改
7. 系统更新成功后自动刷新分类列表

### 删除分类流程

1. 商家登录商家后台
2. 进入「外卖管理」-「分类管理」页面
3. 在分类列表中找到需要删除的分类
4. 点击「删除」按钮
5. 系统弹出确认对话框
6. 点击「确认」按钮确认删除
7. 系统删除成功后自动刷新分类列表

## 最佳实践

### 分类管理建议

1. **分类层级控制**：建议控制在2-3层以内，过深的分类层级会影响用户体验
2. **分类数量控制**：每个层级的分类数量建议控制在10个以内，避免过多分类导致用户选择困难
3. **分类命名规范**：
   - 分类名称简洁明了，一般不超过5个字
   - 同一级分类名称不要重复
   - 使用常见、易理解的分类名称
4. **分类排序策略**：
   - 将热门分类排在前面
   - 将促销分类排在前面
   - 按照用户浏览习惯排序（如先主食后饮品）

### 图片规范

1. **图片尺寸**：建议使用正方形图片，尺寸为200x200像素或更高
2. **图片格式**：支持JPG、PNG格式，推荐使用PNG格式以保持透明背景
3. **图片质量**：清晰度高，无明显模糊、变形
4. **图片内容**：与分类相关，能直观表达分类内容
5. **图片大小**：建议控制在100KB以内，以提高加载速度

### 界面设计建议

1. **响应式设计**：确保页面在不同设备上（PC、平板、手机）都能正常显示和操作
2. **表单验证**：实时验证表单输入，减少提交后的错误率
3. **批量操作**：支持批量选择和操作，提高管理效率
4. **拖拽排序**：支持拖拽方式调整分类顺序，更直观高效
5. **即时预览**：提供分类图片和信息的即时预览功能

## 总结

本文档提供了商家管理外卖商品分类的页面示例和操作流程，包含了分类管理的主要功能和界面设计建议。前端开发人员可以根据本文档进行相关页面的设计和开发，同时也可以根据实际需求进行适当的调整和扩展。
