/**
 * 商家订单管理文档
 *
 * 本文档详细介绍了外卖模块中商家管理订单的功能、API接口以及使用示例。
 * 包括订单列表查询、订单详情查询、订单状态更新、接单、分配配送等核心功能。
 */

# 商家订单管理

## 概述

外卖模块的商家订单管理系统提供了一套完整的API，用于商家管理自己店铺的外卖订单。包括但不限于：订单列表查询、订单详情查询、订单状态更新、接单、分配配送员、开始配送、完成配送、完成订单等功能。

商家订单管理的核心流程为：
1. 查看订单列表与订单详情
2. 接受新订单
3. 分配配送员
4. 开始配送
5. 完成配送
6. 完成订单

## API接口列表

所有商家订单管理API均以 `/api/v1/merchant/takeout/orders` 为前缀，需要商家登录验证。

| 接口 | 请求方式 | 描述 |
| --- | --- | --- |
| `/api/v1/merchant/takeout/orders` | GET | 查询订单列表 |
| `/api/v1/merchant/takeout/orders/:id` | GET | 获取订单详情 |
| `/api/v1/merchant/takeout/orders/:id` | PUT | 更新订单状态（当前实现为完成订单） |
| `/api/v1/merchant/takeout/orders/accept` | POST | 接受订单 |
| `/api/v1/merchant/takeout/orders/assign` | POST | 分配配送员 |
| `/api/v1/merchant/takeout/orders/statistics` | GET | 获取商家订单统计 |
| `/api/v1/merchant/takeout/orders/refund/process` | POST | 商家处理退款申请 |
| `/api/v1/merchant/takeout/delivery/start` | POST | 开始配送 |
| `/api/v1/merchant/takeout/delivery/complete` | POST | 完成配送 |
| `/api/v1/merchant/takeout/delivery/list` | GET | 查询配送订单列表 |

## 订单状态

订单状态是商家管理订单的核心，订单流转过程中会经历以下状态：

```
OrderStatusPending     = 10 // 待支付
OrderStatusPaid        = 20 // 已支付
OrderStatusProcessing  = 30 // 处理中（商家已接单，准备中）
OrderStatusDelivering  = 40 // 配送中
OrderStatusCompleted   = 50 // 已完成
OrderStatusCancelled   = 60 // 已取消
OrderStatusRefunding   = 70 // 退款中
OrderStatusRefunded    = 80 // 已退款
```

## 使用示例

以下是商家订单管理的API使用示例：

### 1. 查询订单列表

**请求示例：**

```http
GET /api/v1/merchant/takeout/orders?status=20&page=1&page_size=10
```

参数说明：
- `status`: 订单状态，-1表示全部，20表示已支付待处理的订单
- `page`: 页码，默认为1
- `page_size`: 每页记录数，默认为10

**响应示例：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 10086,
        "order_number": "TK20250605123456",
        "user_id": 1001,
        "merchant_id": 2001,
        "merchant_name": "好味餐厅",
        "status": 20,
        "status_text": "已支付",
        "total_amount": 45.5,
        "items_count": 3,
        "address": "北京市海淀区中关村大街1号",
        "created_at": "2025-06-05T09:30:25+08:00",
        "paid_at": "2025-06-05T09:32:10+08:00",
        "updated_at": "2025-06-05T09:32:10+08:00"
      },
      ...
    ],
    "total": 28,
    "page": 1,
    "page_size": 10
  }
}
```

### 2. 获取订单详情

**请求示例：**

```http
GET /api/v1/merchant/takeout/orders/10086
```

**响应示例：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 10086,
    "order_number": "TK20250605123456",
    "user_id": 1001,
    "user_name": "张三",
    "user_phone": "138****1234",
    "merchant_id": 2001,
    "merchant_name": "好味餐厅",
    "status": 20,
    "status_text": "已支付",
    "total_amount": 45.5,
    "order_items": [
      {
        "id": 1,
        "food_id": 101,
        "food_name": "宫保鸡丁",
        "food_image": "https://example.com/images/kungpao.jpg",
        "variant_name": "标准辣度",
        "quantity": 1,
        "price": 28.0,
        "subtotal": 28.0,
        "combo_selections": [
          {
            "combo_name": "配菜选择",
            "options": [
              {
                "option_name": "米饭",
                "quantity": 1,
                "extra_price": 2.0
              }
            ]
          }
        ]
      },
      {
        "id": 2,
        "food_id": 102,
        "food_name": "可乐",
        "food_image": "https://example.com/images/cola.jpg",
        "variant_name": "中杯",
        "quantity": 1,
        "price": 5.0,
        "subtotal": 5.0,
        "combo_selections": []
      },
      {
        "id": 3,
        "food_id": 103,
        "food_name": "薯条",
        "food_image": "https://example.com/images/fries.jpg",
        "variant_name": "标准",
        "quantity": 1,
        "price": 8.0,
        "subtotal": 8.0,
        "combo_selections": []
      }
    ],
    "delivery_fee": 5.0,
    "packaging_fee": 1.5,
    "discount_amount": 2.0,
    "address": {
      "id": 100,
      "receiver": "张三",
      "phone": "13812341234",
      "province": "北京市",
      "city": "北京市",
      "district": "海淀区",
      "detail": "中关村大街1号",
      "is_default": true
    },
    "remark": "不要放葱，多加一点辣",
    "delivery_info": {
      "delivery_staff_id": 0,
      "delivery_staff_name": "",
      "delivery_staff_phone": "",
      "delivery_status": 0,
      "estimated_arrival_time": null,
      "delivery_started_at": null,
      "delivery_completed_at": null
    },
    "created_at": "2025-06-05T09:30:25+08:00",
    "paid_at": "2025-06-05T09:32:10+08:00",
    "accepted_at": null,
    "completed_at": null,
    "cancelled_at": null,
    "refunding_at": null,
    "refunded_at": null,
    "updated_at": "2025-06-05T09:32:10+08:00",
    "has_refund": true,
    "refund_no": "RF20250605123789",
    "refund_status": 1,
    "refund_amount": 45.5,
    "refund_reason": "商品有质量问题，申请全额退款",
    "refund_time": "2025-06-05T10:15:25+08:00"
  }
}
```

### 3. 接受订单

**请求示例：**

```http
POST /api/v1/merchant/takeout/orders/accept
Content-Type: application/json

{
  "order_id": 10086
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "订单接受成功",
  "data": null
}
```

接受订单后，订单状态将从已支付(`OrderStatusPaid`=20)变更为处理中(`OrderStatusProcessing`=30)。

### 4. 分配配送员

**请求示例：**

```http
POST /api/v1/merchant/takeout/orders/assign
Content-Type: application/json

{
  "order_id": 10086,
  "delivery_staff_id": 3001
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "配送员分配成功",
  "data": null
}
```

### 5. 开始配送

**请求示例：**

```http
POST /api/v1/merchant/takeout/delivery/start
Content-Type: application/json

{
  "order_id": 10086,
  "delivery_staff_id": 3001,
  "estimated_arrival_time": "2025-06-05T10:25:00+08:00"
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "开始配送成功",
  "data": null
}
```

开始配送后，订单状态将从处理中(`OrderStatusProcessing`=30)变更为配送中(`OrderStatusDelivering`=40)。

### 6. 完成配送

**请求示例：**

```http
POST /api/v1/merchant/takeout/delivery/complete
Content-Type: application/json

{
  "order_id": 10086,
  "delivery_staff_id": 3001
}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "配送完成",
  "data": null
}
```

### 7. 完成订单

**请求示例：**

```http
PUT /api/v1/merchant/takeout/orders/10086
Content-Type: application/json

{}
```

**响应示例：**

```json
{
  "code": 0,
  "message": "订单已完成",
  "data": null
}
```

完成订单后，订单状态将变更为已完成(`OrderStatusCompleted`=50)。

### 8. 获取订单统计数据

**请求示例：**

```http
GET /api/v1/merchant/takeout/orders/statistics
```

**响应示例：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_count": 250,
    "completed_count": 180,
    "processing_count": 15,
    "cancelled_count": 5,
    "today_orders": 25,
    "today_amount": 2568.50,
    "month_orders": 150,
    "month_amount": 15420.80
  }
}
```

### 9. 商家处理退款申请

**请求示例：**

```http
POST /api/v1/merchant/takeout/orders/refund/process
Content-Type: application/json

{
  "refundID": 5001,
  "action": "approve",
  "processRemark": "退款已同意，感谢您的理解"  
}
```

参数说明：
- `refundID`: 退款记录ID，必填
- `action`: 处理动作，必填，可选值为:
  - `approve`: 同意退款
  - `reject`: 拒绝退款
- `processRemark`: 处理备注，在拒绝退款时必填，同意退款时可选

**同意退款响应示例：**

```json
{
  "code": 0,
  "message": "退款处理成功",
  "data": {
    "refundID": 5001,
    "orderID": 10086,
    "refundNo": "TKR20250605123456",
    "refundStatus": 30,
    "processTime": "2025-06-05T15:23:45+08:00",
    "processRemark": "退款已同意，感谢您的理解",
    "statusText": "已同意退款"
  }
}
```

**拒绝退款请求示例：**

```http
POST /api/v1/merchant/takeout/orders/refund/process
Content-Type: application/json

{
  "refundID": 5002,
  "action": "reject",
  "processRemark": "菜品已消费，不符合退款条件"  
}
```

**拒绝退款响应示例：**

```json
{
  "code": 0,
  "message": "退款处理成功",
  "data": {
    "refundID": 5002,
    "orderID": 10087,
    "refundNo": "TKR20250605123457",
    "refundStatus": 40,
    "processTime": "2025-06-05T15:30:22+08:00",
    "processRemark": "菜品已消费，不符合退款条件",
    "statusText": "已拒绝退款"
  }
}
```

**错误响应示例：**

```json
{
  "code": 400,
  "message": "拒绝退款时必须填写拒绝理由",
  "data": null
}
```

其他可能的错误响应：

- 400: 退款ID不能为空
- 400: 操作类型不能为空
- 400: 操作类型只能是approve或reject
- 403: 无权限处理此退款申请
- 500: 服务器内部错误

处理退款后，若同意退款，订单状态将从退款中(`OrderStatusRefunding`=70)变更为已退款(`OrderStatusRefunded`=80)；若拒绝退款，订单将恢复到之前的状态。

## 常见问题与解决方案

### 问题1：无法接单或状态无法更新

请确保订单处于正确的状态，例如只有已支付(`OrderStatusPaid`=20)的订单才能被接受。

### 问题2：接单成功但客户端未更新

接单成功后，客户端需要主动刷新或通过WebSocket接收服务器推送的通知。

### 问题3：配送员无法分配

请确保：
1. 订单已经被商家接受(状态为`OrderStatusProcessing`=30)
2. 配送员ID存在且有效
3. 配送员未被分配到其他订单

### 问题4：订单统计数据不准确

统计数据可能有缓存延迟，如需实时数据，请在请求URL后添加时间戳参数，如：`?_t=1622943656`。

### 问题5：退款处理失败

请确保：
1. 退款申请存在且状态为待处理
2. 商家有权限处理该退款（即退款申请对应的订单属于该商家）
3. 拒绝退款时必须提供拒绝理由（processRemark不能为空）

## 最佳实践

1. **定期轮询新订单** - 建议每30秒查询一次新订单（已支付状态），以便及时处理
2. **快速响应** - 建议在2分钟内接受或拒绝新订单，提高客户满意度
3. **正确流转状态** - 严格按照订单状态流程处理订单，避免状态错乱
4. **异常处理** - 在网络异常或服务器错误时，添加适当的重试逻辑
5. **完整记录** - 记录所有订单状态变更，便于后续问题排查

## 订单流程图

```
+---------------+     +--------------+     +----------------+
| 用户下单支付  | --> | 商家接单处理 | --> | 分配配送员     |
+---------------+     +--------------+     +----------------+
        |                                          |
        v                                          v
+---------------+     +--------------+     +----------------+
| 订单已取消    | <-- | 订单已完成   | <-- | 配送进行中     |
+---------------+     +--------------+     +----------------+
        ^
        |
        v
+---------------+     +--------------+
| 退款申请中    | --> | 退款已完成   |
+---------------+     +--------------+
```
