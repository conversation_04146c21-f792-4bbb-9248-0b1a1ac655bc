# 商家管理促销和优惠活动前端设计指导文档

## 一、概述

本文档为外卖模块商家端促销和优惠活动管理功能的前端设计提供指导，包括页面布局、交互设计、组件规范和用户体验优化建议。

## 二、整体设计原则

### 2.1 设计理念
- **简洁高效**：界面简洁明了，操作流程高效便捷
- **数据驱动**：以数据展示为核心，帮助商家做出决策
- **响应式设计**：适配不同设备和屏幕尺寸
- **一致性**：保持与整体系统的设计风格一致

### 2.2 用户体验原则
- **易用性**：降低学习成本，提供直观的操作体验
- **可访问性**：支持键盘导航，提供适当的视觉反馈
- **容错性**：提供清晰的错误提示和恢复机制
- **效率性**：减少操作步骤，提供批量操作功能

## 三、页面架构设计

### 3.1 导航结构

```
商家后台
├── 营销管理
    ├── 促销活动
    │   ├── 活动列表
    │   ├── 创建活动
    │   ├── 活动详情
    │   └── 活动统计
    ├── 优惠券管理
    │   ├── 优惠券列表
    │   ├── 创建优惠券
    │   ├── 优惠券详情
    │   ├── 发放管理
    │   └── 使用统计
    └── 营销数据
        ├── 整体概览
        ├── 活动效果分析
        └── 用户行为分析
```

### 3.2 页面布局

**标准页面布局**：
- **顶部导航栏**：面包屑导航 + 操作按钮
- **侧边栏**：功能菜单（可折叠）
- **主内容区**：数据展示 + 操作区域
- **底部**：分页器（如需要）

## 四、促销活动管理界面设计

### 4.1 促销活动列表页

#### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 面包屑导航: 营销管理 > 促销活动                                    │
├─────────────────────────────────────────────────────────────┤
│ [+ 创建活动] [批量操作▼] [导出数据]                    [搜索框] │
├─────────────────────────────────────────────────────────────┤
│ 筛选器: [状态▼] [时间范围] [活动类型▼] [重置] [应用]              │
├─────────────────────────────────────────────────────────────┤
│ 数据表格:                                                    │
│ ┌─┬────────┬──────┬──────┬──────┬──────┬────────┬──────┐ │
│ │☐│活动名称  │状态   │类型   │开始时间│结束时间│参与人数  │操作   │ │
│ ├─┼────────┼──────┼──────┼──────┼──────┼────────┼──────┤ │
│ │☐│春季大促销│进行中 │满减   │03-01  │03-31  │1,234   │详情/编辑│ │
│ │☐│新用户专享│已结束 │折扣   │02-15  │02-28  │856     │详情/复制│ │
│ └─┴────────┴──────┴──────┴──────┴──────┴────────┴──────┘ │
├─────────────────────────────────────────────────────────────┤
│                        [< 上一页] 1 2 3 [下一页 >]              │
└─────────────────────────────────────────────────────────────┘
```

#### 功能特性
- **状态标识**：使用颜色和图标区分活动状态（未开始/进行中/已结束/已取消）
- **快速操作**：提供发布、暂停、复制、删除等快速操作
- **批量操作**：支持批量发布、暂停、删除活动
- **数据筛选**：按状态、时间、类型等维度筛选
- **实时统计**：显示参与人数、使用次数等关键指标

### 4.2 创建/编辑促销活动页

#### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 面包屑导航: 营销管理 > 促销活动 > 创建活动                        │
├─────────────────────────────────────────────────────────────┤
│ [保存草稿] [预览] [发布活动]                          [返回列表] │
├─────────────────────────────────────────────────────────────┤
│ 步骤导航: ● 基本信息 ● 活动规则 ● 适用范围 ● 预览发布             │
├─────────────────────────────────────────────────────────────┤
│ 表单内容区域:                                                │
│ ┌─────────────────┬─────────────────────────────────────┐ │
│ │ 基本信息         │ 预览区域                              │ │
│ │ [活动名称输入框]  │ ┌─────────────────────────────────┐ │ │
│ │ [活动描述文本域]  │ │ 活动预览卡片                      │ │ │
│ │ [活动类型选择]    │ │ ┌─────────────────────────────┐ │ │ │
│ │ [开始时间选择器]  │ │ │ 春季大促销                    │ │ │ │
│ │ [结束时间选择器]  │ │ │ 满100减20，限时优惠           │ │ │ │
│ │                 │ │ │ 2024.03.01 - 2024.03.31    │ │ │ │
│ │ 活动规则         │ │ └─────────────────────────────┘ │ │ │
│ │ [优惠类型选择]    │ └─────────────────────────────────┘ │ │
│ │ [优惠金额设置]    │                                     │ │
│ │ [使用条件设置]    │                                     │ │
│ └─────────────────┴─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    [上一步] [下一步]                          │
└─────────────────────────────────────────────────────────────┘
```

#### 设计要点
- **分步骤引导**：将复杂的创建流程分解为多个步骤
- **实时预览**：右侧提供活动效果预览
- **表单验证**：实时验证输入内容，提供友好的错误提示
- **保存草稿**：支持保存未完成的活动配置
- **模板功能**：提供常用活动模板快速创建

### 4.3 促销活动详情页

#### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 面包屑导航: 营销管理 > 促销活动 > 春季大促销                      │
├─────────────────────────────────────────────────────────────┤
│ [编辑] [复制] [发布/暂停] [删除]                      [返回列表] │
├─────────────────────────────────────────────────────────────┤
│ 活动概览卡片:                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 春季大促销                                    [进行中]    │ │
│ │ 活动时间: 2024.03.01 10:00 - 2024.03.31 23:59          │ │
│ │ 活动描述: 春季特惠，满100减20，数量有限，先到先得           │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 数据统计:                                                    │
│ ┌──────────┬──────────┬──────────┬──────────┬──────────┐ │
│ │ 参与用户数 │ 使用次数   │ 优惠金额   │ 订单增长   │ 转化率     │ │
│ │ 1,234     │ 2,456     │ ¥12,340   │ +23.5%    │ 15.6%     │ │
│ └──────────┴──────────┴──────────┴──────────┴──────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 详细配置:                                                    │
│ [活动规则] [适用范围] [使用限制] [关联优惠券]                   │
├─────────────────────────────────────────────────────────────┤
│ 使用记录:                                                    │
│ [数据表格显示使用记录]                                        │
└─────────────────────────────────────────────────────────────┘
```

## 五、优惠券管理界面设计

### 5.1 优惠券列表页

#### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 面包屑导航: 营销管理 > 优惠券管理                               │
├─────────────────────────────────────────────────────────────┤
│ [+ 创建优惠券] [批量发放] [导入用户] [导出数据]        [搜索框] │
├─────────────────────────────────────────────────────────────┤
│ 筛选器: [类型▼] [状态▼] [关联活动▼] [时间范围] [重置] [应用]    │
├─────────────────────────────────────────────────────────────┤
│ 优惠券卡片网格:                                              │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐ │
│ │ 满减券       │ 折扣券       │ 兑换券       │ 新用户专享   │ │
│ │ ┌─────────┐ │ ┌─────────┐ │ ┌─────────┐ │ ┌─────────┐ │ │
│ │ │满100减20 │ │ │8.8折优惠 │ │ │免费配送  │ │ │首单立减  │ │ │
│ │ │有效期至   │ │ │有效期至   │ │ │有效期至   │ │ │有效期至   │ │ │
│ │ │03-31     │ │ │03-31     │ │ │03-31     │ │ │03-31     │ │ │
│ │ │已发放:123│ │ │已发放:456│ │ │已发放:789│ │ │已发放:321│ │ │
│ │ │已使用:45 │ │ │已使用:78 │ │ │已使用:12 │ │ │已使用:67 │ │ │
│ │ └─────────┘ │ └─────────┘ │ └─────────┘ │ └─────────┘ │ │
│ │ [详情][编辑] │ [详情][编辑] │ [详情][编辑] │ [详情][编辑] │ │
│ │ [发放][删除] │ [发放][删除] │ [发放][删除] │ [发放][删除] │ │
│ └─────────────┴─────────────┴─────────────┴─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 设计特点
- **卡片式布局**：使用卡片展示优惠券，直观展示优惠内容
- **状态标识**：通过颜色和标签区分优惠券状态
- **关键数据**：显示发放数量、使用数量等关键指标
- **快速操作**：提供发放、编辑、删除等快速操作按钮

### 5.2 创建优惠券页

#### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 面包屑导航: 营销管理 > 优惠券管理 > 创建优惠券                   │
├─────────────────────────────────────────────────────────────┤
│ [保存] [保存并发放] [预览]                            [返回列表] │
├─────────────────────────────────────────────────────────────┤
│ 表单区域:                                                    │
│ ┌─────────────────┬─────────────────────────────────────┐ │
│ │ 基本信息         │ 优惠券预览                            │ │
│ │ ┌─────────────┐ │ ┌─────────────────────────────────┐ │ │
│ │ │优惠券名称    │ │ │ ┌─────────────────────────────┐ │ │ │
│ │ │[输入框]     │ │ │ │ 满100减20优惠券              │ │ │ │
│ │ └─────────────┘ │ │ │ 满100元可用，立减20元        │ │ │ │
│ │ ┌─────────────┐ │ │ │ 有效期：2024.03.01-03.31    │ │ │ │
│ │ │优惠券类型    │ │ │ │ 适用：全部商品              │ │ │ │
│ │ │○满减券      │ │ │ └─────────────────────────────┘ │ │ │
│ │ │○折扣券      │ │ └─────────────────────────────────┘ │ │
│ │ │○兑换券      │ │                                     │ │
│ │ └─────────────┘ │ 使用限制                              │ │
│ │                 │ ┌─────────────────────────────────┐ │ │
│ │ 优惠设置         │ │ □ 限制商品分类                    │ │ │
│ │ ┌─────────────┐ │ │ □ 限制用户等级                    │ │ │
│ │ │优惠金额      │ │ │ □ 限制使用次数                    │ │ │
│ │ │[20] 元      │ │ │ □ 限制每日发放                    │ │ │
│ │ └─────────────┘ │ └─────────────────────────────────┘ │ │
│ └─────────────────┴─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 优惠券发放管理页

#### 页面结构
```
┌─────────────────────────────────────────────────────────────┐
│ 面包屑导航: 营销管理 > 优惠券管理 > 发放管理                     │
├─────────────────────────────────────────────────────────────┤
│ 发放方式选择:                                                │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐ │
│ │ 指定用户发放 │ 批量导入发放 │ 条件筛选发放 │ 公开领取发放 │ │
│ │ [选择]      │ [选择]      │ [选择]      │ [选择]      │ │
│ └─────────────┴─────────────┴─────────────┴─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 发放配置:                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 选择优惠券: [下拉选择框]                                  │ │
│ │ 发放数量: [输入框] 张/人                                  │ │
│ │ 有效期设置: ○ 使用优惠券原有效期 ○ 自定义有效期              │ │
│ │ 发放时间: ○ 立即发放 ○ 定时发放 [时间选择器]               │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 用户选择区域:                                                │
│ [用户搜索框] [添加用户] [批量导入]                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 已选用户列表:                                            │ │
│ │ ┌──────┬────────┬──────┬──────┬──────┬──────┐        │ │
│ │ │用户ID │用户昵称  │手机号  │等级   │状态   │操作   │        │ │
│ │ ├──────┼────────┼──────┼──────┼──────┼──────┤        │ │
│ │ │12345 │张三     │138*** │VIP   │正常   │移除   │        │ │
│ │ └──────┴────────┴──────┴──────┴──────┴──────┘        │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    [预览发放] [确认发放]                      │
└─────────────────────────────────────────────────────────────┘
```

## 六、组件设计规范

### 6.1 通用组件

#### 状态标签组件
```javascript
// 状态标签配置
const statusConfig = {
  promotion: {
    'not_started': { color: 'gray', text: '未开始' },
    'active': { color: 'green', text: '进行中' },
    'paused': { color: 'orange', text: '已暂停' },
    'ended': { color: 'blue', text: '已结束' },
    'cancelled': { color: 'red', text: '已取消' }
  },
  coupon: {
    'unused': { color: 'green', text: '未使用' },
    'used': { color: 'blue', text: '已使用' },
    'expired': { color: 'gray', text: '已过期' },
    'disabled': { color: 'red', text: '已禁用' }
  }
};
```

#### 数据统计卡片组件
```javascript
// 统计卡片组件结构
const StatCard = {
  title: '参与用户数',
  value: '1,234',
  unit: '人',
  trend: '+23.5%',
  trendType: 'up', // up, down, stable
  icon: 'user'
};
```

### 6.2 表单组件

#### 优惠券类型选择器
```javascript
// 优惠券类型配置
const couponTypes = [
  {
    type: 'amount',
    name: '满减券',
    description: '满足最低消费金额后减免固定金额',
    icon: 'discount',
    fields: ['amount', 'minOrderAmount']
  },
  {
    type: 'discount',
    name: '折扣券',
    description: '按比例折扣，可设置最高优惠金额',
    icon: 'percentage',
    fields: ['discountRate', 'maxDiscountAmount']
  },
  {
    type: 'exchange',
    name: '兑换券',
    description: '免费兑换指定商品或服务',
    icon: 'gift',
    fields: ['exchangeItems']
  }
];
```

### 6.3 数据展示组件

#### 优惠券卡片组件
```javascript
// 优惠券卡片组件结构
const CouponCard = {
  id: 1,
  name: '满100减20优惠券',
  type: 'amount',
  amount: 20,
  minOrderAmount: 100,
  validFrom: '2024-03-01',
  validTo: '2024-03-31',
  issuedCount: 123,
  usedCount: 45,
  status: 'active',
  actions: ['view', 'edit', 'issue', 'delete']
};
```

## 七、交互设计规范

### 7.1 操作反馈

#### 成功操作反馈
- **创建成功**：显示成功提示，自动跳转到详情页或列表页
- **更新成功**：显示成功提示，保持在当前页面
- **删除成功**：显示成功提示，从列表中移除对应项

#### 错误处理
- **表单验证错误**：实时显示字段级错误提示
- **网络错误**：显示重试按钮和错误信息
- **权限错误**：显示权限不足提示，提供联系管理员选项

### 7.2 确认操作

#### 危险操作确认
```javascript
// 删除确认对话框
const deleteConfirm = {
  title: '确认删除',
  content: '删除后无法恢复，确定要删除这个促销活动吗？',
  confirmText: '确认删除',
  cancelText: '取消',
  type: 'danger'
};

// 批量操作确认
const batchConfirm = {
  title: '批量发放优惠券',
  content: '即将向 123 个用户发放优惠券，确认执行吗？',
  confirmText: '确认发放',
  cancelText: '取消',
  type: 'primary'
};
```

### 7.3 加载状态

#### 页面加载
- **列表加载**：显示骨架屏或加载动画
- **表单提交**：按钮显示加载状态，禁用重复提交
- **数据导出**：显示进度条和预计完成时间

## 八、响应式设计

### 8.1 断点设置
```css
/* 响应式断点 */
@media (max-width: 768px) {
  /* 移动端样式 */
  .promotion-list {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* 平板端样式 */
  .promotion-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  /* 桌面端样式 */
  .promotion-list {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

### 8.2 移动端适配
- **导航优化**：使用抽屉式侧边栏
- **表格适配**：使用卡片式布局替代表格
- **操作按钮**：增大点击区域，优化手指操作
- **表单优化**：使用适合移动端的输入组件

## 九、性能优化建议

### 9.1 数据加载优化
- **分页加载**：大列表使用分页或虚拟滚动
- **懒加载**：图片和非关键内容使用懒加载
- **缓存策略**：合理使用浏览器缓存和应用缓存

### 9.2 渲染优化
- **组件复用**：提取通用组件，减少重复渲染
- **状态管理**：使用合适的状态管理方案
- **代码分割**：按路由或功能模块分割代码

## 十、可访问性设计

### 10.1 键盘导航
- **Tab 顺序**：确保合理的 Tab 键导航顺序
- **快捷键**：提供常用操作的快捷键支持
- **焦点管理**：明确的焦点指示和管理

### 10.2 屏幕阅读器支持
- **语义化标签**：使用正确的 HTML 语义化标签
- **ARIA 属性**：添加必要的 ARIA 属性
- **替代文本**：为图片和图标提供替代文本

## 十一、测试建议

### 11.1 功能测试
- **创建流程测试**：测试完整的创建和编辑流程
- **权限测试**：测试不同角色的权限控制
- **数据验证测试**：测试表单验证和数据完整性

### 11.2 用户体验测试
- **可用性测试**：邀请真实用户进行操作测试
- **A/B 测试**：对关键界面进行 A/B 测试
- **性能测试**：测试在不同网络条件下的表现

## 十二、总结

本设计指导文档为商家管理促销和优惠活动功能提供了全面的前端设计规范，包括：

1. **完整的页面架构**：涵盖了所有主要功能页面的设计
2. **详细的组件规范**：提供了可复用的组件设计标准
3. **良好的用户体验**：注重操作流程的简化和用户反馈
4. **响应式设计**：确保在不同设备上的良好体验
5. **可访问性支持**：考虑了特殊用户群体的使用需求

通过遵循本指导文档，可以构建出功能完善、体验良好的商家促销管理界面，有效提升商家的运营效率和用户满意度。