# 外卖模块数据模型

## 数据模型关系图

```
+----------------+       +-------------------+       +----------------+
| TakeoutFood    |------>| TakeoutFoodVariant|       | TakeoutCategory|
+----------------+       +-------------------+       +----------------+
       |                        |                            ^
       |                        |                            |
       |                        |                            |
       |                        |                            |
       |                        |                            |
       |                        v                            |
       |               +-------------------+                 |
       |-------------->| GlobalCategory    |                 |
       |               +-------------------+                 |
       |                                                     |
       v                                                     |
+----------------+       +-------------------+               |
| TakeoutComboItem|------>| TakeoutComboOption|               |
+----------------+       +-------------------+               |
       |                                                     |
       |                                                     |
       v                                                     |
+----------------+       +-------------------+               |
| TakeoutOrder   |------>| TakeoutOrderItem  |---------------| 
+----------------+       +-------------------+               
       |                                                   
       |                                                   
       v                                                   
+----------------+      +----------------+      +----------------+
| TakeoutOrderRating|    | TakeoutPromotion |     | TakeoutCoupon    |
+----------------+      +----------------+      +----------------+
                              |                        |
                              v                        v
                        +----------------+     +----------------+
                        | TakeoutFoodPromotion| | TakeoutUserCoupon|
                        +----------------+     +----------------+
```

## 核心数据模型

### 1. 外卖商品（TakeoutFood）

```go
type TakeoutFood struct {
    ID              int64     // 商品ID
    MerchantID      int64     // 商家ID
    CategoryID      int64     // 分类ID，关联商家自定义分类
    GlobalCategoryID int64     // 全局分类ID，关联平台统一分类
    Name            string    // 商品名称
    Description     string    // 商品详细描述
    Brief           string    // 商品简介
    Image           string    // 商品图片
    Price           float64   // 基础价格
    OriginalPrice   float64   // 原价
    PackagingFee    float64   // 包装费
    PreparationTime int       // 备餐时间(分钟)
    IsCombination   bool      // 是否为套餐组合
    IsSpicy         bool      // 是否辣味
    HasVariants     bool      // 是否有规格变体
    SoldOut         bool      // 是否售罄
    DailyLimit      int       // 每日限量
    SoldCount       int       // 今日已售数量
    TotalSold       int       // 累计销售总量
    Tags            string    // 标签，逗号分隔
    Keywords        string    // 关键词
    IsVisible       bool      // 是否可见
    IsRecommend     bool      // 是否推荐
    Status          int       // 商品状态
    AuditStatus     int       // 审核状态
    AuditorID       int64     // 审核人ID
    AuditReason     string    // 审核意见或拒绝原因
    AuditTime       time.Time // 审核时间
    SortOrder       int       // 排序值
    ViewCount       int       // 浏览次数
    CommentCount    int       // 评论数量
    FavoriteCount   int       // 收藏数量
    Rating          float64   // 平均评分
    CreatedAt       time.Time // 创建时间
    UpdatedAt       time.Time // 更新时间
}
```

#### 商品状态常量

```go
const (
    // 商品状态（Status）
    FoodStatusDraft       = 0 // 草稿状态
    FoodStatusOnSale       = 1 // 上架销售中
    FoodStatusOffSale      = 2 // 已下架
    FoodStatusSoldOut      = 3 // 已售罄
    
    // 审核状态（AuditStatus）
    AuditStatusPending     = 0 // 未审核/待审核
    AuditStatusApproved    = 1 // 审核通过
    AuditStatusRejected    = 2 // 审核拒绝
)
```

### 2. 商品规格变体（TakeoutFoodVariant）

```go
type TakeoutFoodVariant struct {
    ID            int64     // 规格ID
    FoodID        int64     // 商品ID
    Name          string    // 规格名称
    Description   string    // 规格描述
    Image         string    // 规格图片
    Price         float64   // 规格价格
    OriginalPrice float64   // 原价
    Stock         int       // 库存
    SoldCount     int       // 已售数量
    IsDefault     bool      // 是否默认规格
    SortOrder     int       // 排序值
    CreatedAt     time.Time // 创建时间
    UpdatedAt     time.Time // 更新时间
}
```

### 3. 套餐组件（TakeoutComboItem）

```go
type TakeoutComboItem struct {
    ID          int64     // 组件ID
    FoodID      int64     // 商品ID
    Name        string    // 组件名称
    Description string    // 组件描述
    Required    bool      // 是否必选
    MinSelect   int       // 最少选择数量
    MaxSelect   int       // 最多选择数量
    SortOrder   int       // 排序值
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
}
```

### 4. 套餐选项（TakeoutComboOption）

```go
type TakeoutComboOption struct {
    ID          int64     // 选项ID
    ComboItemID int64     // 组件ID
    Name        string    // 选项名称
    Description string    // 选项描述
    Image       string    // 选项图片
    Price       float64   // 选项价格
    IsDefault   bool      // 是否默认选中
    SortOrder   int       // 排序值
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
}
```

### 5. 商品分类（TakeoutCategory）

```go
type TakeoutCategory struct {
    ID          int64     // 分类ID
    MerchantID  int64     // 商家ID
    Name        string    // 分类名称
    Description string    // 分类描述
    Image       string    // 分类图片
    ParentID    int64     // 父分类ID，0表示顶级分类
    Level       int       // 分类层级，1表示一级分类
    SortOrder   int       // 排序值
    IsVisible   bool      // 是否可见
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
}
```

### 6. 全局商品分类（GlobalCategory）

```go
type GlobalCategory struct {
    ID          int64     // 分类ID
    Name        string    // 分类名称
    Code        string    // 分类编码，唯一，用于系统识别
    Description string    // 分类描述
    ParentID    int64     // 父分类ID，0表示顶级分类
    Level       int       // 分类层级，1表示一级分类
    SortOrder   int       // 排序值
    IsActive    bool      // 是否激活
    CreatedAt   time.Time // 创建时间
    UpdatedAt   time.Time // 更新时间
    CreatedBy   int64     // 创建人ID，关联管理员账号
    UpdatedBy   int64     // 更新人ID，关联管理员账号
}
```

#### 全局分类树节点

```go
type GlobalCategoryTreeNode struct {
    Category  *GlobalCategory         // 分类信息
    Children  []*GlobalCategoryTreeNode // 子分类
    FoodCount int                     // 该分类下的商品数量
}
```

### 7. 外卖订单扩展（TakeoutOrderExtension）

```go
type TakeoutOrderExtension struct {
    ID                    int64     // 主键ID
    OrderID               int64     // 关联的订单ID
    OrderNo               string    // 订单编号
    MerchantID            int64     // 商家ID
    OrderType             int       // 订单类型
    ExpectedDeliveryTime  time.Time // 预计送达时间
    DeliveryStaffID       int64     // 配送员ID
    DeliveryStaffName     string    // 配送员姓名
    DeliveryStaffPhone    string    // 配送员电话
    DeliveryStatus        int       // 配送状态
    DeliveryType          int       // 配送类型
    DeliveryFee           float64   // 配送费
    PackagingFee          float64   // 包装费总额
    TablewareQuantity     int       // 餐具数量
    DistanceKm            float64   // 配送距离(公里)
    EstimatedDeliveryTime time.Time // 预计送达时间
    IsPreOrder            bool      // 是否预订单
    PreOrderTime          time.Time // 预订时间
    CreatedAt             time.Time // 创建时间
    UpdatedAt             time.Time // 更新时间
}
```

### 7. 外卖订单项扩展（TakeoutOrderItem）

```go
type TakeoutOrderItem struct {
    ID              int64     // 主键ID
    OrderItemID     int64     // 关联的订单项ID
    OrderID         int64     // 订单ID
    FoodID          int64     // 外卖商品ID
    VariantID       int64     // 规格变体ID
    VariantName     string    // 规格变体名称
    PackagingFee    float64   // 包装费
    ComboSelectData string    // 套餐选择数据，JSON格式
    Remark          string    // 商品备注
    CreatedAt       time.Time // 创建时间
    UpdatedAt       time.Time // 更新时间
}
```

### 8. 外卖订单评价（TakeoutOrderRating）

```go
type TakeoutOrderRating struct {
    ID             int64     // 评价ID
    OrderID        int64     // 订单ID
    UserID         int64     // 用户ID
    Rating         int       // 评分，1-5星
    DeliveryRating int       // 配送评分，1-5星
    Comment        string    // 评价内容
    CreatedAt      time.Time // 创建时间
    UpdatedAt      time.Time // 更新时间
}
```

## 数据库索引设计

为确保系统高效运行，以下字段已设置索引：

1. `TakeoutFood.merchant_id` - 用于按商家筛选商品
2. `TakeoutFood.category_id` - 用于按分类筛选商品
3. `TakeoutFoodVariant.food_id` - 用于获取商品的变体规格
4. `TakeoutComboItem.food_id` - 用于获取套餐商品的组件
5. `TakeoutComboOption.combo_item_id` - 用于获取套餐组件的选项
6. `TakeoutOrderExtension.order_id` - 用于关联订单信息
7. `TakeoutOrderExtension.order_no` - 用于根据订单号查询
8. `TakeoutOrderItem.order_id` - 用于获取订单的商品项
9. `TakeoutOrderRating.order_id` - 用于关联订单评价

### 8. 促销活动（TakeoutPromotion）

```go
type TakeoutPromotion struct {
    ID            int64     // 活动ID
    MerchantID    int64     // 商户ID
    Name          string    // 活动名称
    Description   string    // 活动描述
    Type          int       // 活动类型
    StartTime     time.Time // 开始时间
    EndTime       time.Time // 结束时间
    Status        int       // 状态
    Rules         string    // 活动规则
    MaxUsageCount int       // 最大使用次数
    UsageCount    int       // 已使用次数
    CreatedAt     time.Time // 创建时间
    UpdatedAt     time.Time // 更新时间
}
```

#### 活动类型常量

```go
const (
    PromotionTypeFirstOrder = 1 // 首单签到
    PromotionTypeDiscount   = 2 // 折扣促销
    PromotionTypeCoupon     = 3 // 优惠券活动
    PromotionTypeFlashSale  = 4 // 限时特价
    PromotionTypeGift       = 5 // 赠品活动
)
```

#### 活动状态常量

```go
const (
    PromotionStatusDraft     = 0 // 草稿状态
    PromotionStatusActive    = 1 // 活动中
    PromotionStatusPaused    = 2 // 暂停
    PromotionStatusFinished  = 3 // 已结束
    PromotionStatusCancelled = 4 // 已取消
)
```

### 9. 优惠券（TakeoutCoupon）

```go
type TakeoutCoupon struct {
    ID                int64   // 优惠券ID
    PromotionID       int64   // 关联活动ID
    MerchantID        int64   // 商户ID
    Code              string  // 优惠券码
    Name              string  // 优惠券名称
    Description       string  // 描述
    Type              int     // 优惠券类型
    Amount            float64 // 金额/折扣值
    MinOrderAmount    float64 // 最低订单金额要求
    MaxDiscountAmount float64 // 最高优惠金额
    ApplyToAll        bool    // 是否适用于所有商品
    ApplyToCategories string   // 适用分类ID
    ApplyToFoods      string   // 适用商品ID
    ExcludeFoods      string   // 排除商品ID
    UserLevelLimit    int     // 用户等级限制
    PerUserLimit      int     // 每人可领取次数
    DailyLimit        int     // 每日发放限制
    TotalLimit        int     // 总发放数量限制
    StartTime         time.Time // 开始时间
    EndTime           time.Time // 结束时间
    Status            int     // 状态
    CreatedAt         time.Time // 创建时间
    UpdatedAt         time.Time // 更新时间
}
```

#### 优惠券类型常量

```go
const (
    CouponTypeAmount   = 1 // 满减券
    CouponTypeDiscount = 2 // 折扣券
)
```

#### 优惠券状态常量

```go
const (
    CouponStatusUnused    = 1 // 未使用
    CouponStatusUsed      = 2 // 已使用
    CouponStatusExpired   = 3 // 已过期
    CouponStatusDisabled  = 4 // 已禁用
)
```

### 10. 用户优惠券（TakeoutUserCoupon）

```go
type TakeoutUserCoupon struct {
    ID         int64     // ID
    UserID     int64     // 用户ID
    CouponID   int64     // 优惠券ID
    Status     int       // 状态
    UsedTime   time.Time // 使用时间
    OrderID    int64     // 关联订单ID
    CreatedAt  time.Time // 创建时间
    UpdatedAt  time.Time // 更新时间
}
```

#### 用户优惠券状态常量

```go
const (
    UserCouponStatusUnused  = 1 // 未使用
    UserCouponStatusUsed    = 2 // 已使用
    UserCouponStatusExpired = 3 // 已过期
)
```

### 11. 商品促销关联（TakeoutFoodPromotion）

```go
type TakeoutFoodPromotion struct {
    ID           int64     // ID
    FoodID       int64     // 商品ID
    PromotionID  int64     // 促销活动ID
    DiscountType int       // 折扣类型
    DiscountValue float64   // 折扣值
    CreatedAt    time.Time // 创建时间
    UpdatedAt    time.Time // 更新时间
}
```

#### 商品折扣类型常量

```go
const (
    FoodDiscountTypePercent = 1 // 按比例折扣
    FoodDiscountTypeAmount  = 2 // 按金额直降
)
```

## 状态设计

### 商品状态流程

```
   ┌───────────┐                  ┌───────────┐
   │           │                  │           │
   │   草稿    │────────────────▶│  待审核   │
   │           │   提交审核      │           │
   └───────────┘                  └─────┬─────┘
                                        │
                                        │ 审核
                                        ▼
             ┌───────────┐      ┌───────────┐
             │           │      │           │
             │ 审核拒绝  │◀─────│ 审核通过  │
             │           │      │           │
             └───────────┘      └─────┬─────┘
                                      │
                                      │ 上架
                                      ▼
                               ┌───────────┐
                               │           │
                               │  上架中   │
                               │           │
                               └─────┬─────┘
                                     │
                                     │
          ┌────────────┐             │
          │            │             │
          │   售罄    │◀────────────┼───────────┐
          │            │             │           │
          └────────────┘      ┌─────▼─────┐     │
                               │           │     │
                               │   下架   │◀────┘
                               │           │
                               └───────────┘
```

### 订单状态流程

```
┌───────────┐      ┌───────────┐      ┌───────────┐      ┌───────────┐
│           │      │           │      │           │      │           │
│  待付款   │────▶│  待接单   │────▶│  待配送   │────▶│  配送中   │
│           │      │           │      │           │      │           │
└───────────┘      └───────────┘      └───────────┘      └─────┬─────┘
       │                                                        │
       │                                                        │
       ▼                                                        ▼
┌───────────┐                                            ┌───────────┐
│           │                                            │           │
│  已取消   │                                            │  已送达   │
│           │                                            │           │
└───────────┘                                            └─────┬─────┘
                                                               │
                                                               │
                                                               ▼
                                                        ┌───────────┐
                                                        │           │
                                                        │  已完成   │
                                                        │           │
                                                        └───────────┘
```
