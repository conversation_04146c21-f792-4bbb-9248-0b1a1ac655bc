# 外卖模块概述

## 模块简介

外卖模块（modules/takeout）是一个完整的外卖商品管理和订单处理系统，提供从商品管理、规格设置、套餐组合到订单处理、配送管理等全流程功能。该模块支持与购物车和订单系统对接，实现外卖业务的完整流程。

## 目录结构

```
modules/takeout/
├── controllers/      # 控制器目录，处理API请求
├── docs/             # 模块文档目录
├── dto/              # 数据传输对象目录
├── init.go           # 模块初始化文件
├── models/           # 数据模型目录
├── repositories/     # 数据仓库目录
├── routers/          # 路由配置目录
└── services/         # 业务逻辑服务目录
```

## 架构设计

外卖模块采用经典的三层架构设计：

1. **控制层（Controller）**：处理HTTP请求，转换输入参数，调用服务层方法并构造响应
2. **服务层（Service）**：实现业务逻辑，实体关系管理，数据转换等
3. **仓库层（Repository）**：实现数据访问，提供数据存储和查询方法

顺序图：

```
Client/Browser --> 控制器(Controller) --> 服务(Service) --> 仓库(Repository) --> 数据库(Database)
```

## 核心功能

### 1. 商品管理
- 外卖商品的创建、查询、更新和删除
- 商品规格变体管理
- 套餐组合管理
- 商品分类管理
- 商品审核流程

### 2. 购物车管理
- 添加商品到购物车
- 更新购物车商品
- 移除购物车商品
- 购物车结算

### 3. 订单管理
- 创建外卖订单
- 订单状态管理
- 订单配送管理
- 订单评价

### 4. 促销管理
- 促销活动创建与管理
- 优惠券发放与使用
- 商品折扣关联
- 订单优惠计算

### 5. 用户角色
模块支持三类用户角色：
- **普通用户**：浏览商品、领取优惠券、下单、评价
- **商家**：管理自己店铺的商品、订单、配送和促销活动
- **管理员**：平台全局管理，包括商品审核、促销活动管理、全平台数据统计等

## 集成点

外卖模块与系统其他部分的集成点包括：

1. **用户系统**：用户认证和授权
2. **支付系统**：处理订单支付
3. **商家系统**：商家信息管理
4. **配送系统**：订单配送管理
5. **评价系统**：订单评价和评分

## 特色功能

1. **商品审核流程**：商家创建商品后，必须经管理员审核后才能上架销售
2. **多规格支持**：商品支持多种规格选择，每种规格可设置不同价格
3. **套餐组合**：支持套餐组合商品，灵活配置套餐项及选项
4. **配送管理**：集成配送流程，支持配送状态跟踪
5. **订单评价**：完善的订单评价系统，包括商品和配送评分
6. **灵活促销策略**：支持首单优惠、商品折扣、限时特价等多种促销方式
7. **多级优惠券**：支持满减券和折扣券，可设置适用范围和使用限制
8. **智能优惠计算**：自动计算订单最优优惠组合，确保用户获得最大折扣
