# 外卖模块支付使用示例

> 本文档提供了外卖平台支付流程的详细示例，包括订单支付、支付方式选择、支付状态查询等功能。

## 1. 订单支付流程

### 支付页面界面示例

```
+-------------------------------------------------------+
|                  订单支付                              |
+-------------------------------------------------------+
|                                                       |
| 订单信息                                               |
|                                                       |
| 订单号: #TO202505140001                               |
| 商家: 美味小厨                                         |
| 下单时间: 2025-05-14 18:30:25                         |
|                                                       |
| 商品明细:                                              |
| 2x 香辣鸡腿堡             ¥37.60                     |
| 1x 藏江鲜牛腊汤           ¥28.00                     |
| 1x 龙须冰糖水             ¥12.00                     |
|                                                       |
| 小计:                     ¥77.60                     |
| 配送费:                   ¥5.00                      |
| 包装费:                   ¥0.50                      |
| 优惠券抵扣:               -¥15.00                     |
|                                                       |
| 实付金额:                 ¥68.10                     |
|                                                       |
| 支付方式:                                              |
|                                                       |
| [●] 微信支付    [○] 支付宝    [○] 银行卡               |
|                                                       |
| 配送信息:                                              |
| 收货地址: 北京市朝阳区建国路88号SOHO现代城A座1201       |
| 联系电话: 138****8888                                  |
| 预计送达: 19:15 - 19:30                               |
|                                                       |
| [ 立即支付 ¥68.10 ]                                   |
+-------------------------------------------------------+
```

### 创建支付订单API请求

```json
POST /api/takeout/orders/123/payment
Content-Type: application/json
Authorization: Bearer {user_token}

{
  "payment_method": "wechat",
  "payment_amount": 68.10,
  "coupon_id": 201,
  "delivery_address": {
    "address": "北京市朝阳区建国路88号SOHO现代城A座1201",
    "phone": "13888888888",
    "contact_name": "张先生"
  },
  "remark": "少放辣椒，谢谢"
}
```

### 创建支付订单API响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "payment_id": "PAY202505140001",
    "order_id": 123,
    "payment_method": "wechat",
    "payment_amount": 68.10,
    "original_amount": 83.10,
    "discount_amount": 15.00,
    "status": "pending",
    "created_at": "2025-05-14T18:35:20+08:00",
    "expires_at": "2025-05-14T18:50:20+08:00",
    "payment_info": {
      "qr_code": "weixin://wxpay/bizpayurl?pr=abc123xyz",
      "prepay_id": "wx14183520123456789",
      "code_url": "https://api.mch.weixin.qq.com/pay/qrcode?token=abc123"
    }
  }
}
```

## 2. 支付方式选择

### 微信支付界面

```
+-------------------------------------------------------+
|                  微信支付                              |
+-------------------------------------------------------+
|                                                       |
| 订单金额: ¥68.10                                      |
|                                                       |
| 请使用微信扫描下方二维码完成支付                         |
|                                                       |
|               +---------------+                       |
|               |               |                       |
|               |   [QR CODE]   |                       |
|               |               |                       |
|               +---------------+                       |
|                                                       |
| 支付剩余时间: 14:32                                    |
|                                                       |
| 支付说明:                                              |
| • 请在15分钟内完成支付                                 |
| • 支付成功后将自动跳转                                 |
| • 如遇问题请联系客服                                   |
|                                                       |
| [ 刷新二维码 ]    [ 选择其他支付方式 ]                   |
+-------------------------------------------------------+
```

### 支付宝支付界面

```
+-------------------------------------------------------+
|                  支付宝支付                            |
+-------------------------------------------------------+
|                                                       |
| 订单金额: ¥68.10                                      |
|                                                       |
| 支付方式选择:                                          |
|                                                       |
| [●] 扫码支付    [○] 手机支付                           |
|                                                       |
| 请使用支付宝扫描下方二维码                             |
|                                                       |
|               +---------------+                       |
|               |               |                       |
|               |   [QR CODE]   |                       |
|               |               |                       |
|               +---------------+                       |
|                                                       |
| 或点击下方按钮跳转支付宝APP                            |
|                                                       |
| [ 打开支付宝APP支付 ]                                  |
|                                                       |
| 支付剩余时间: 14:32                                    |
+-------------------------------------------------------+
```

## 3. 支付状态查询

### 支付状态查询API请求

```json
GET /api/takeout/payments/PAY202505140001/status
Authorization: Bearer {user_token}
```

### 支付状态查询API响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "payment_id": "PAY202505140001",
    "order_id": 123,
    "status": "paid",
    "payment_method": "wechat",
    "payment_amount": 68.10,
    "paid_at": "2025-05-14T18:37:45+08:00",
    "transaction_id": "4200001234567890123456789",
    "payment_info": {
      "bank_type": "CFT",
      "cash_fee": 6810,
      "fee_type": "CNY"
    }
  }
}
```

## 4. 支付成功页面

### 支付成功界面

```
+-------------------------------------------------------+
|                  支付成功                              |
+-------------------------------------------------------+
|                                                       |
|                    ✓                                  |
|               支付成功！                               |
|                                                       |
| 订单号: #TO202505140001                               |
| 支付金额: ¥68.10                                      |
| 支付方式: 微信支付                                     |
| 支付时间: 2025-05-14 18:37:45                         |
| 交易流水: 4200001234567890123456789                   |
|                                                       |
| 订单状态: 商家已接单，正在准备中                        |
| 预计送达: 19:15 - 19:30                               |
|                                                       |
| 配送信息:                                              |
| 收货地址: 北京市朝阳区建国路88号SOHO现代城A座1201       |
| 联系电话: 138****8888                                  |
|                                                       |
| [ 查看订单详情 ]    [ 继续购物 ]                        |
+-------------------------------------------------------+
```

## 5. 支付失败处理

### 支付失败界面

```
+-------------------------------------------------------+
|                  支付失败                              |
+-------------------------------------------------------+
|                                                       |
|                    ✗                                  |
|               支付失败                                 |
|                                                       |
| 订单号: #TO202505140001                               |
| 失败原因: 余额不足                                     |
| 失败时间: 2025-05-14 18:40:12                         |
|                                                       |
| 订单状态: 待支付                                       |
| 剩余支付时间: 10分32秒                                 |
|                                                       |
| 解决方案:                                              |
| • 请检查账户余额是否充足                               |
| • 可以选择其他支付方式                                 |
| • 联系银行或支付平台客服                               |
|                                                       |
| [ 重新支付 ]    [ 选择其他支付方式 ]    [ 取消订单 ]     |
+-------------------------------------------------------+
```

### 支付失败API响应

```json
{
  "code": 1001,
  "message": "支付失败",
  "data": {
    "payment_id": "PAY202505140001",
    "order_id": 123,
    "status": "failed",
    "error_code": "INSUFFICIENT_BALANCE",
    "error_message": "余额不足",
    "failed_at": "2025-05-14T18:40:12+08:00",
    "retry_allowed": true,
    "expires_at": "2025-05-14T18:50:20+08:00"
  }
}
```

## 6. 支付超时处理

### 支付超时提醒

```
+-------------------------------------------------------+
|                  支付超时                              |
+-------------------------------------------------------+
|                                                       |
|                    ⏰                                 |
|               支付已超时                               |
|                                                       |
| 订单号: #TO202505140001                               |
| 订单金额: ¥68.10                                      |
| 超时时间: 2025-05-14 18:50:20                         |
|                                                       |
| 订单状态: 已取消                                       |
|                                                       |
| 说明:                                                  |
| • 订单已自动取消，无需支付                             |
| • 如使用优惠券，已自动返还                             |
| • 可重新下单购买                                       |
|                                                       |
| [ 重新下单 ]    [ 返回首页 ]                           |
+-------------------------------------------------------+
```

## 7. 退款处理

### 申请退款API请求

```json
POST /api/takeout/payments/PAY202505140001/refund
Content-Type: application/json
Authorization: Bearer {user_token}

{
  "refund_amount": 68.10,
  "refund_reason": "商家取消订单",
  "refund_type": "full"
}
```

### 申请退款API响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "refund_id": "REF202505140001",
    "payment_id": "PAY202505140001",
    "refund_amount": 68.10,
    "refund_reason": "商家取消订单",
    "status": "processing",
    "created_at": "2025-05-14T19:15:30+08:00",
    "estimated_arrival": "1-3个工作日"
  }
}
```

### 退款成功通知

```
+-------------------------------------------------------+
|                  退款成功                              |
+-------------------------------------------------------+
|                                                       |
|                    ✓                                  |
|               退款已完成                               |
|                                                       |
| 订单号: #TO202505140001                               |
| 退款金额: ¥68.10                                      |
| 退款方式: 原路返回                                     |
| 退款时间: 2025-05-15 10:30:25                         |
| 退款流水: REF202505140001                             |
|                                                       |
| 退款说明:                                              |
| • 退款已原路返回至您的支付账户                         |
| • 到账时间: 1-3个工作日                                |
| • 如有疑问请联系客服                                   |
|                                                       |
| [ 查看退款详情 ]    [ 继续购物 ]                        |
+-------------------------------------------------------+
```

## 8. 支付安全

### 支付验证流程

1. **订单验证**：验证订单状态、金额、用户权限
2. **支付方式验证**：检查支付方式是否可用
3. **金额校验**：确保支付金额与订单金额一致
4. **防重复支付**：检查订单是否已支付
5. **超时检查**：验证订单是否在有效期内

### 安全措施

- **数据加密**：敏感信息传输采用HTTPS加密
- **签名验证**：支付回调采用数字签名验证
- **防篡改**：关键参数进行哈希校验
- **限流控制**：防止恶意频繁支付请求
- **日志记录**：完整记录支付操作日志

## 9. 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | success | 操作成功 |
| 1001 | 支付失败 | 通用支付失败 |
| 1002 | 订单不存在 | 订单ID无效 |
| 1003 | 订单已支付 | 重复支付 |
| 1004 | 订单已取消 | 订单状态异常 |
| 1005 | 支付金额错误 | 金额不匹配 |
| 1006 | 支付方式不支持 | 支付方式无效 |
| 1007 | 支付超时 | 超过支付时限 |
| 1008 | 余额不足 | 账户余额不够 |
| 1009 | 银行卡异常 | 银行卡问题 |
| 1010 | 网络异常 | 网络连接问题 |

## 10. 注意事项

1. **支付时效**：订单创建后15分钟内必须完成支付，否则自动取消
2. **金额精度**：所有金额计算保留两位小数，避免精度丢失
3. **并发控制**：同一订单同时只能有一个支付请求
4. **异常处理**：支付过程中的异常要及时处理和通知用户
5. **日志记录**：所有支付相关操作都要记录详细日志
6. **回调处理**：支付回调要做好幂等性处理
7. **退款限制**：已配送完成的订单不支持退款
8. **优惠券处理**：支付失败时要及时释放已使用的优惠券