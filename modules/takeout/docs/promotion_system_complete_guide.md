# 外卖模块促销系统详尽指南

## 概述

本文档详细说明外卖平台促销系统的完整机制，包括商家发布促销活动、用户参与方式、使用限制以及与优惠券系统的对比分析。促销系统作为平台营销的核心功能，为商家提供了灵活多样的营销工具。

## 促销系统与优惠券系统的核心差异

### 1. 系统定位对比

| 维度 | 促销系统 | 优惠券系统 |
|------|----------|------------|
| **系统层级** | 上层营销概念 | 具体营销工具 |
| **覆盖范围** | 多种营销形式 | 专注优惠券 |
| **用户参与** | 直接享受优惠 | 需要领取后使用 |
| **实例化** | 无需用户实例 | 需要创建用户实例 |
| **使用限制** | 基于活动规则 | 基于券的属性 |
| **数据存储** | 单一促销表 | 模板+用户券双表 |

### 2. 业务流程对比

**促销系统流程**：
```
商家创建促销活动 → 发布活动 → 用户下单时自动享受 → 系统计算优惠 → 更新使用统计
```

**优惠券系统流程**：
```
商家创建优惠券模板 → 用户主动领取 → 创建用户券实例 → 下单时选择使用 → 券状态变更
```

## 促销活动类型详解

### 1. 促销活动类型定义

```go
// 促销活动类型常量
const (
    PromotionTypeFirstOrder   = 1 // 首单优惠
    PromotionTypeProductDiscount = 2 // 商品折扣
    PromotionTypeCoupon        = 3 // 优惠券活动
    PromotionTypeFull          = 4 // 满减活动
    PromotionTypeLimitedTime   = 5 // 限时特价
)
```

### 2. 各类型促销详细说明

#### 2.1 首单优惠 (PromotionTypeFirstOrder)

**特点**：
- 针对新用户的首次下单优惠
- 无需用户主动领取
- 系统自动识别用户首单状态
- 通常与用户注册绑定

**业务规则**：
```json
{
  "type": 1,
  "rules": {
    "discount_type": "amount", // amount: 减免金额, percent: 折扣比例
    "discount_value": 10.0,    // 减免10元或打9折(0.9)
    "min_order_amount": 30.0,  // 最低订单金额
    "max_discount": 15.0,      // 最高优惠金额(折扣券适用)
    "user_type": "new_user"    // 限制用户类型
  }
}
```

**使用场景**：
- 新用户注册后的首次下单
- 拉新活动的核心工具
- 提高用户转化率

#### 2.2 商品折扣 (PromotionTypeProductDiscount)

**特点**：
- 针对特定商品的直接折扣
- 在商品展示时直接显示优惠价格
- 无需用户额外操作
- 可设置商品范围和时间限制

**业务规则**：
```json
{
  "type": 2,
  "rules": {
    "apply_to_foods": "1001,1002,1003", // 适用商品ID列表
    "apply_to_categories": "10,11",     // 适用分类ID列表
    "discount_type": "percent",         // 折扣类型
    "discount_value": 0.8,              // 8折
    "max_quantity": 5                   // 每单最多优惠数量
  }
}
```

**使用场景**：
- 清仓促销
- 新品推广
- 节日特价

#### 2.3 满减活动 (PromotionTypeFull)

**特点**：
- 基于订单总金额的阶梯式优惠
- 可设置多档满减规则
- 鼓励用户增加订单金额
- 提升客单价的有效工具

**业务规则**：
```json
{
  "type": 4,
  "rules": {
    "tiers": [
      {"min_amount": 50, "discount": 5},   // 满50减5
      {"min_amount": 100, "discount": 15}, // 满100减15
      {"min_amount": 200, "discount": 35}  // 满200减35
    ],
    "can_combine": false,                   // 是否可与其他优惠叠加
    "apply_to_delivery": true              // 是否包含配送费
  }
}
```

**使用场景**：
- 提升客单价
- 节假日促销
- 会员专享活动

#### 2.4 限时特价 (PromotionTypeLimitedTime)

**特点**：
- 在特定时间段内的特殊优惠
- 营造紧迫感和稀缺性
- 可结合秒杀、抢购等玩法
- 时间敏感性强

**业务规则**：
```json
{
  "type": 5,
  "rules": {
    "time_slots": [
      {"start": "11:00", "end": "14:00"},  // 午餐时段
      {"start": "17:00", "end": "20:00"}   // 晚餐时段
    ],
    "discount_type": "amount",
    "discount_value": 8.0,
    "daily_limit": 100,                    // 每日限量
    "per_user_limit": 1                    // 每人限购
  }
}
```

**使用场景**：
- 高峰时段引流
- 库存清理
- 节日抢购

## 促销活动生命周期管理

### 1. 活动状态定义

```go
// 促销活动状态常量
const (
    PromotionStatusPending   = 1 // 待发布
    PromotionStatusActive    = 2 // 进行中
    PromotionStatusPaused    = 3 // 已暂停
    PromotionStatusEnded     = 4 // 已结束
    PromotionStatusCancelled = 5 // 已取消
)
```

### 2. 状态流转规则

```
待发布 → 进行中 → 已结束
   ↓        ↓
已取消   已暂停 → 进行中
```

### 3. 商家发布促销活动流程

#### 3.1 创建促销活动

**API接口**: `POST /api/v1/merchant/takeout/promotions`

**请求示例**:
```json
{
  "name": "午餐时光满减活动",
  "description": "工作日午餐时段，满50减10，满100减25",
  "type": 4,
  "start_time": "2025-06-01 11:00:00",
  "end_time": "2025-06-30 14:00:00",
  "rules": {
    "tiers": [
      {"min_amount": 50, "discount": 10},
      {"min_amount": 100, "discount": 25}
    ],
    "time_slots": [
      {"start": "11:00", "end": "14:00"}
    ],
    "weekdays_only": true
  },
  "max_usage_count": 1000,
  "per_user_limit": 3
}
```

**系统验证流程**:
1. 验证商家权限和状态
2. 验证活动时间的合理性
3. 验证活动规则的完整性
4. 检查与现有活动的冲突
5. 保存活动信息（状态为待发布）

#### 3.2 发布促销活动

**API接口**: `PUT /api/v1/merchant/takeout/promotions/{id}/publish`

**发布条件检查**:
- 活动状态必须为待发布或已取消
- 活动开始时间不能早于当前时间
- 活动规则必须完整有效
- 商家账户状态正常

**发布后效果**:
- 活动状态变更为进行中
- 开始在用户端展示
- 用户下单时自动应用优惠

### 4. 活动管理操作

#### 4.1 暂停活动

**API接口**: `PUT /api/v1/merchant/takeout/promotions/{id}/pause`

**使用场景**:
- 临时调整活动规则
- 应对突发情况
- 控制活动节奏

#### 4.2 恢复活动

**API接口**: `PUT /api/v1/merchant/takeout/promotions/{id}/resume`

#### 4.3 取消活动

**API接口**: `PUT /api/v1/merchant/takeout/promotions/{id}/cancel`

**注意事项**:
- 已开始的活动取消后不可恢复
- 需要处理已下单但未完成的订单
- 影响用户体验，需谨慎操作

## 用户参与促销活动机制

### 1. 自动参与 vs 主动领取

#### 促销活动（自动参与）
- **无需用户操作**: 满足条件自动享受优惠
- **实时计算**: 下单时系统自动计算最优优惠
- **透明展示**: 在商品页面和订单页面明确显示优惠信息
- **无实例概念**: 不创建用户相关的促销实例

#### 优惠券（主动领取）
- **需要领取**: 用户必须主动领取优惠券
- **创建实例**: 领取时创建用户优惠券实例
- **状态管理**: 实例有未使用、已使用、已过期等状态
- **选择使用**: 下单时需要主动选择使用哪张优惠券

### 2. 促销活动的用户体验流程

#### 2.1 发现促销

**商家页面展示**:
```json
{
  "merchant_info": {
    "id": 502,
    "name": "美味餐厅",
    "active_promotions": [
      {
        "id": 301,
        "name": "满50减10",
        "type": 4,
        "description": "全场满50元立减10元",
        "end_time": "2025-06-30T23:59:59+08:00",
        "is_active": true
      }
    ]
  }
}
```

**商品页面展示**:
```json
{
  "food_info": {
    "id": 1024,
    "name": "宫保鸡丁",
    "original_price": 28.0,
    "current_price": 22.4,
    "promotion_info": {
      "type": "discount",
      "discount_text": "限时8折",
      "promotion_id": 302
    }
  }
}
```

#### 2.2 订单计算

**API接口**: `POST /api/v1/user/takeout/orders/calculate`

**请求示例**:
```json
{
  "merchant_id": 502,
  "items": [
    {"food_id": 1024, "quantity": 2, "price": 28.0},
    {"food_id": 1025, "quantity": 1, "price": 15.0}
  ],
  "delivery_address_id": 123
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "original_amount": 71.0,
    "delivery_fee": 5.0,
    "total_before_discount": 76.0,
    "applied_promotions": [
      {
        "promotion_id": 301,
        "promotion_name": "满50减10",
        "discount_amount": 10.0,
        "discount_type": "full_reduction"
      },
      {
        "promotion_id": 302,
        "promotion_name": "限时8折",
        "discount_amount": 11.2,
        "discount_type": "product_discount",
        "affected_items": [1024]
      }
    ],
    "total_discount": 21.2,
    "final_amount": 54.8
  }
}
```

### 3. 促销优惠计算逻辑

#### 3.1 优惠叠加规则

```go
// 优惠计算优先级
// 1. 商品级别折扣（商品折扣、限时特价）
// 2. 订单级别优惠（满减活动、首单优惠）
// 3. 优惠券（如果允许叠加）

func CalculateOrderDiscount(order *OrderInfo, promotions []*TakeoutPromotion) *DiscountResult {
    result := &DiscountResult{
        OriginalAmount: order.TotalAmount,
        FinalAmount:    order.TotalAmount,
    }
    
    // 第一步：应用商品级别促销
    for _, promotion := range promotions {
        if promotion.Type == PromotionTypeProductDiscount || promotion.Type == PromotionTypeLimitedTime {
            discount := calculateProductDiscount(order, promotion)
            result.ProductDiscounts = append(result.ProductDiscounts, discount)
            result.FinalAmount -= discount.Amount
        }
    }
    
    // 第二步：应用订单级别促销
    for _, promotion := range promotions {
        if promotion.Type == PromotionTypeFull || promotion.Type == PromotionTypeFirstOrder {
            discount := calculateOrderDiscount(result.FinalAmount, promotion)
            if discount.Amount > 0 {
                result.OrderDiscounts = append(result.OrderDiscounts, discount)
                result.FinalAmount -= discount.Amount
            }
        }
    }
    
    return result
}
```

#### 3.2 满减活动计算

```go
func calculateFullReductionDiscount(amount float64, rules *PromotionRules) float64 {
    var maxDiscount float64
    
    for _, tier := range rules.Tiers {
        if amount >= tier.MinAmount && tier.Discount > maxDiscount {
            maxDiscount = tier.Discount
        }
    }
    
    return maxDiscount
}
```

#### 3.3 商品折扣计算

```go
func calculateProductDiscount(order *OrderInfo, promotion *TakeoutPromotion) *DiscountInfo {
    var totalDiscount float64
    var affectedItems []int64
    
    for _, item := range order.Items {
        if isItemApplicable(item, promotion.Rules) {
            var itemDiscount float64
            
            if promotion.Rules.DiscountType == "percent" {
                itemDiscount = item.Price * item.Quantity * (1 - promotion.Rules.DiscountValue)
            } else if promotion.Rules.DiscountType == "amount" {
                itemDiscount = math.Min(promotion.Rules.DiscountValue * item.Quantity, item.Price * item.Quantity)
            }
            
            totalDiscount += itemDiscount
            affectedItems = append(affectedItems, item.FoodID)
        }
    }
    
    return &DiscountInfo{
        PromotionID:    promotion.ID,
        PromotionName:  promotion.Name,
        Amount:         totalDiscount,
        AffectedItems:  affectedItems,
    }
}
```

## 促销活动使用限制机制

### 1. 时间限制

#### 1.1 活动有效期
```go
type TakeoutPromotion struct {
    StartTime time.Time `orm:"column(start_time)" json:"start_time"`
    EndTime   time.Time `orm:"column(end_time)" json:"end_time"`
}
```

#### 1.2 时段限制
```json
{
  "rules": {
    "time_slots": [
      {"start": "11:00", "end": "14:00"},  // 午餐时段
      {"start": "17:00", "end": "20:00"}   // 晚餐时段
    ],
    "weekdays_only": true,                // 仅工作日
    "exclude_holidays": true             // 排除节假日
  }
}
```

### 2. 使用次数限制

#### 2.1 活动总使用次数
```go
type TakeoutPromotion struct {
    MaxUsageCount int64 `orm:"column(max_usage_count)" json:"max_usage_count"` // 最大使用次数
    UsageCount    int64 `orm:"column(usage_count)" json:"usage_count"`         // 已使用次数
}
```

#### 2.2 用户使用次数限制

**实现方式**：
- 促销活动本身不创建用户实例
- 通过订单记录追踪用户使用情况
- 在下单时检查用户历史使用次数

```go
func CheckUserPromotionUsage(userID, promotionID int64, perUserLimit int64) (bool, error) {
    // 查询用户使用该促销活动的订单数量
    var count int64
    o := orm.NewOrm()
    
    err := o.Raw(`
        SELECT COUNT(*) 
        FROM takeout_order 
        WHERE user_id = ? 
          AND promotion_id = ? 
          AND status NOT IN (?, ?)
    `, userID, promotionID, OrderStatusCancelled, OrderStatusRefunded).QueryRow(&count)
    
    if err != nil {
        return false, err
    }
    
    return count < perUserLimit, nil
}
```

### 3. 商品适用范围限制

#### 3.1 分类限制
```json
{
  "rules": {
    "apply_to_categories": "10,11,12",    // 适用分类ID
    "exclude_categories": "20,21"        // 排除分类ID
  }
}
```

#### 3.2 商品限制
```json
{
  "rules": {
    "apply_to_foods": "1001,1002,1003",  // 适用商品ID
    "exclude_foods": "2001,2002"         // 排除商品ID
  }
}
```

#### 3.3 适用性检查逻辑
```go
func isItemApplicable(item *OrderItem, rules *PromotionRules) bool {
    // 检查排除商品
    if strings.Contains(rules.ExcludeFoods, fmt.Sprintf("%d", item.FoodID)) {
        return false
    }
    
    // 检查排除分类
    if strings.Contains(rules.ExcludeCategories, fmt.Sprintf("%d", item.CategoryID)) {
        return false
    }
    
    // 如果设置了适用商品，检查是否在范围内
    if rules.ApplyToFoods != "" {
        return strings.Contains(rules.ApplyToFoods, fmt.Sprintf("%d", item.FoodID))
    }
    
    // 如果设置了适用分类，检查是否在范围内
    if rules.ApplyToCategories != "" {
        return strings.Contains(rules.ApplyToCategories, fmt.Sprintf("%d", item.CategoryID))
    }
    
    // 默认适用于所有商品
    return true
}
```

### 4. 用户等级限制

```json
{
  "rules": {
    "user_level_limit": 2,               // 限制用户等级
    "new_user_only": true,               // 仅限新用户
    "member_only": false                 // 仅限会员
  }
}
```

## 促销活动统计与分析

### 1. 实时统计数据

**API接口**: `GET /api/v1/merchant/takeout/promotions/{id}/statistics`

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "promotion_info": {
      "id": 301,
      "name": "满50减10活动",
      "status": 2,
      "start_time": "2025-06-01T00:00:00+08:00",
      "end_time": "2025-06-30T23:59:59+08:00"
    },
    "usage_statistics": {
      "total_usage_count": 1250,           // 总使用次数
      "unique_user_count": 890,            // 参与用户数
      "total_discount_amount": 12500.0,    // 总优惠金额
      "total_order_amount": 125000.0,      // 总订单金额
      "average_order_amount": 100.0,       // 平均订单金额
      "conversion_rate": 0.15              // 转化率
    },
    "daily_statistics": [
      {
        "date": "2025-06-01",
        "usage_count": 45,
        "discount_amount": 450.0,
        "order_amount": 4500.0
      }
    ],
    "hourly_distribution": {
      "11": 120, "12": 180, "13": 150,    // 11点-13点使用分布
      "18": 200, "19": 250, "20": 180     // 18点-20点使用分布
    }
  }
}
```

### 2. 效果分析指标

#### 2.1 核心指标
- **参与率**: 参与促销的订单数 / 总订单数
- **转化率**: 促销带来的新订单数 / 促销曝光次数
- **客单价提升**: 促销期间平均订单金额 vs 非促销期间
- **复购率**: 参与促销用户的后续复购比例
- **ROI**: (促销带来的额外收入 - 促销成本) / 促销成本

#### 2.2 用户行为分析
- **时段偏好**: 用户使用促销的时间分布
- **商品偏好**: 促销商品的受欢迎程度
- **用户画像**: 参与促销用户的特征分析

## 促销系统技术实现细节

### 1. 数据模型设计

```go
type TakeoutPromotion struct {
    ID              int64     `orm:"pk;auto;column(id)" json:"id"`
    MerchantID      int64     `orm:"column(merchant_id)" json:"merchant_id"`
    Name            string    `orm:"column(name);size(100)" json:"name"`
    Description     string    `orm:"column(description);size(500)" json:"description"`
    Type            int       `orm:"column(type)" json:"type"`
    Status          int       `orm:"column(status)" json:"status"`
    Rules           string    `orm:"column(rules);type(text)" json:"rules"`
    StartTime       time.Time `orm:"column(start_time)" json:"start_time"`
    EndTime         time.Time `orm:"column(end_time)" json:"end_time"`
    MaxUsageCount   int64     `orm:"column(max_usage_count)" json:"max_usage_count"`
    UsageCount      int64     `orm:"column(usage_count)" json:"usage_count"`
    CreatedAt       time.Time `orm:"column(created_at);auto_now_add" json:"created_at"`
    UpdatedAt       time.Time `orm:"column(updated_at);auto_now" json:"updated_at"`
}
```

### 2. 服务层接口设计

```go
type ITakeoutPromotionService interface {
    // 商家端接口
    CreatePromotion(request *dto.CreatePromotionRequest) (*dto.PromotionResponse, error)
    UpdatePromotion(id int64, request *dto.UpdatePromotionRequest) (*dto.PromotionResponse, error)
    GetPromotionByID(id int64) (*dto.PromotionResponse, error)
    GetPromotionsByMerchantID(merchantID int64, page, pageSize int) (*dto.PromotionListResponse, error)
    PublishPromotion(id int64) error
    CancelPromotion(id int64) error
    PausePromotion(id int64) error
    ResumePromotion(id int64) error
    
    // 用户端接口
    GetActivePromotions(merchantID int64) ([]*dto.PromotionResponse, error)
    CalculateOrderDiscount(order *dto.OrderInfo) (*dto.DiscountResult, error)
    
    // 统计接口
    GetPromotionStatistics(id int64) (*dto.PromotionStatistics, error)
    GetMerchantPromotionSummary(merchantID int64) (*dto.PromotionSummary, error)
}
```

### 3. 缓存策略

```go
// 活跃促销活动缓存
func (s *TakeoutPromotionService) GetActivePromotions(merchantID int64) ([]*dto.PromotionResponse, error) {
    cacheKey := fmt.Sprintf("active_promotions:%d", merchantID)
    
    // 尝试从缓存获取
    if cached := s.cacheService.Get(cacheKey); cached != nil {
        var promotions []*dto.PromotionResponse
        if err := json.Unmarshal(cached, &promotions); err == nil {
            return promotions, nil
        }
    }
    
    // 从数据库获取
    promotions, err := s.promotionRepo.GetActivePromotions(merchantID)
    if err != nil {
        return nil, err
    }
    
    // 转换为DTO
    var result []*dto.PromotionResponse
    for _, promotion := range promotions {
        result = append(result, dto.ConvertToPromotionResponse(promotion))
    }
    
    // 缓存结果（5分钟）
    if data, err := json.Marshal(result); err == nil {
        s.cacheService.Set(cacheKey, data, 5*time.Minute)
    }
    
    return result, nil
}
```

## 促销系统最佳实践

### 1. 活动设计原则

#### 1.1 目标明确
- **拉新**: 首单优惠、新用户专享
- **促活**: 限时特价、满减活动
- **提升客单价**: 阶梯满减、商品组合优惠
- **清库存**: 特定商品折扣

#### 1.2 时间规划
- **预热期**: 提前1-3天预告活动
- **活动期**: 根据目标设定合理时长
- **收尾期**: 活动结束前的最后冲刺

#### 1.3 规则设计
- **简单易懂**: 避免复杂的计算规则
- **公平合理**: 确保所有用户都有参与机会
- **防刷机制**: 设置合理的使用限制

### 2. 运营策略建议

#### 2.1 活动组合
```json
{
  "strategy": "组合促销",
  "activities": [
    {
      "type": "product_discount",
      "target": "新品推广",
      "discount": "8折"
    },
    {
      "type": "full_reduction",
      "target": "提升客单价",
      "rule": "满100减20"
    },
    {
      "type": "limited_time",
      "target": "高峰引流",
      "time": "11:30-13:30"
    }
  ]
}
```

#### 2.2 数据监控
- **实时监控**: 活动参与情况、转化率
- **异常预警**: 使用量异常、成本超标
- **效果评估**: 活动结束后的全面分析

### 3. 风险控制

#### 3.1 成本控制
- 设置活动总预算上限
- 监控实时优惠金额
- 建立预警机制

#### 3.2 防刷机制
- 用户使用次数限制
- IP地址限制
- 设备指纹识别
- 行为模式分析

#### 3.3 系统稳定性
- 高并发处理能力
- 数据一致性保证
- 降级方案准备

## 总结

### 促销系统的核心优势

1. **用户体验优化**: 无需额外操作，自动享受优惠
2. **运营效率提升**: 灵活的活动配置和实时调整能力
3. **数据驱动决策**: 完善的统计分析功能
4. **技术架构合理**: 可扩展的设计和高性能实现

### 与优惠券系统的互补关系

- **促销系统**: 适合大规模、自动化的营销活动
- **优惠券系统**: 适合精准营销和用户留存
- **组合使用**: 可以设计促销活动发放优惠券，形成营销闭环

### 未来发展方向

1. **智能化**: 基于AI的个性化促销推荐
2. **社交化**: 结合分享、拼团等社交玩法
3. **全渠道**: 线上线下一体化的促销体系
4. **实时化**: 更加灵活的实时调整能力

促销系统作为外卖平台的核心营销工具，通过合理的设计和运营，能够有效提升用户活跃度、增加订单量、提高客单价，为平台和商家创造更大的价值。