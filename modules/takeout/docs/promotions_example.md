# 外卖模块促销功能示例

> 本文档提供了商家在外卖平台上创建和管理促销活动的详细示例，包括折扣活动、满减活动和优惠券发放等。

## 目录

- [1. 折扣促销活动](#1-折扣促销活动)
- [2. 满减优惠活动](#2-满减优惠活动)
- [3. 优惠券发放活动](#3-优惠券发放活动)
- [4. 活动预览与发布](#4-活动预览与发布)

## 1. 折扣促销活动

### 界面示例

```
+-------------------------------------------------------+
|                  新建折扣促销                            |
+-------------------------------------------------------+
|                                                       |
| 活动基本信息                                            |
|                                                       |
| 活动名称: [夏日特惠折扣                     ] *必填    |
|                                                       |
| 活动描述: [夏日来临，精选商品享7折优惠         ] *必填    |
|                                                       |
| 活动类型: [商品折扣  v]                                  |
|                                                       |
| 开始时间: [2025-05-20] [00:00:00]                        |
|                                                       |
| 结束时间: [2025-06-20] [23:59:59]                        |
|                                                       |
| 最大使用次数: [1000          ]   (0表示不限制)              |
|                                                       |
|                                                       |
| 选择参与活动的商品                                       |
|                                                       |
| [√] 选择分类: [汉堡     v] [+ 添加]                       |
| [ ] 选择指定商品  [选择商品]                              |
|                                                       |
| 选中商品(12): 香辣鸡腿堡, 爆汁牛肉堡, 双层芝士堡...         |
|                                                       |
| 折扣设置                                                |
|                                                       |
| 折扣类型: [◯百分比折扣] [◱固定金额优惠]                    |
|                                                       |
| 折扣比例: [8.0           ] (如输入8表示8折)                 |
|                                                       |
| [ 保存为草稿 ]    [ 预览 ]    [ 发布活动 ]                  |
+-------------------------------------------------------+
```

### API请求示例

```json
POST /merchant/api/takeout/promotions
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "夏日特惠折扣",
  "description": "夏日来临，精选商品享受8折优惠",
  "type": 2,  // 折扣促销
  "start_time": "2025-05-20 00:00:00",
  "end_time": "2025-06-20 23:59:59",
  "max_usage_count": 1000,
  "status": 1,  // 活动中
  "rules": "{\"discount_type\":1,\"discount_value\":8.0}",
  "food_ids": [1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035]
}
```

### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 501,
    "merchant_id": 502,
    "name": "夏日特惠折扣",
    "description": "夏日来临，精选商品享受8折优惠",
    "type": 2,
    "start_time": "2025-05-20T00:00:00+08:00",
    "end_time": "2025-06-20T23:59:59+08:00",
    "status": 1,
    "rules": "{\"discount_type\":1,\"discount_value\":8.0}",
    "max_usage_count": 1000,
    "usage_count": 0,
    "created_at": "2025-05-14T14:15:23+08:00",
    "updated_at": "2025-05-14T14:15:23+08:00",
    "affected_foods": [
      {
        "food_id": 1024,
        "food_name": "香辣鸡腿堡",
        "original_price": 18.8,
        "discount_price": 15.04
      },
      {
        "food_id": 1025,
        "food_name": "爆浆牛肉堡",
        "original_price": 22.5,
        "discount_price": 18.0
      },
      // ... 其他商品 ...
    ]
  }
}
```

## 2. 满减优惠活动

### 界面示例

```
+-------------------------------------------------------+
|                  新建满减活动                            |
+-------------------------------------------------------+
|                                                       |
| 活动基本信息                                            |
|                                                       |
| 活动名称: [外卖满减专享                     ] *必填    |
|                                                       |
| 活动描述: [下单满88元减10元，满128元减20元，满158元减30元 ] *必填 |
|                                                       |
| 活动类型: [满减优惠  v]                                  |
|                                                       |
| 开始时间: [2025-05-15] [00:00:00]                        |
|                                                       |
| 结束时间: [2025-06-15] [23:59:59]                        |
|                                                       |
| 最大使用次数: [2000          ]   (0表示不限制)              |
|                                                       |
|                                                       |
| 满减规则设置                                           |
|                                                       |
| 满减条件:                                              |
|                                                       |
| [1] 满 [88       ] 元, 减 [10       ] 元  [+ 删除]        |
| [2] 满 [128      ] 元, 减 [20       ] 元  [+ 删除]        |
| [3] 满 [158      ] 元, 减 [30       ] 元  [+ 删除]        |
| [+ 添加满减条件]                                       |
|                                                       |
| 优惠限制                                               |
|                                                       |
| [√] 所有商品参与       [√] 与其他优惠可叠加                 |
|                                                       |
| [ 保存为草稿 ]    [ 预览 ]    [ 发布活动 ]                  |
+-------------------------------------------------------+
```

### API请求示例

```json
POST /merchant/api/takeout/promotions
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "外卖满减专享",
  "description": "下单满88元减10元，满128元减20元，满158元减30元",
  "type": 3,  // 满减优惠
  "start_time": "2025-05-15 00:00:00",
  "end_time": "2025-06-15 23:59:59",
  "max_usage_count": 2000,
  "status": 1,  // 活动中
  "rules": "{\"conditions\":[{\"threshold\":88,\"discount\":10},{\"threshold\":128,\"discount\":20},{\"threshold\":158,\"discount\":30}],\"apply_to_all\":true,\"can_combine\":true}",
  "food_ids": []  // 所有商品都参与
}
```

### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 502,
    "merchant_id": 502,
    "name": "外卖满减专享",
    "description": "下单满88元减10元，满128元减20元，满158元减30元",
    "type": 3,
    "start_time": "2025-05-15T00:00:00+08:00",
    "end_time": "2025-06-15T23:59:59+08:00",
    "status": 1,
    "rules": "{\"conditions\":[{\"threshold\":88,\"discount\":10},{\"threshold\":128,\"discount\":20},{\"threshold\":158,\"discount\":30}],\"apply_to_all\":true,\"can_combine\":true}",
    "max_usage_count": 2000,
    "usage_count": 0,
    "created_at": "2025-05-14T15:20:10+08:00",
    "updated_at": "2025-05-14T15:20:10+08:00"
  }
}
```

## 3. 优惠券发放活动

### 界面示例

```
+-------------------------------------------------------+
|                  新建优惠券活动                        |
+-------------------------------------------------------+
|                                                       |
| 活动基本信息                                            |
|                                                       |
| 活动名称: [新客专享优惠券                    ] *必填    |
|                                                       |
| 活动描述: [新用户注册即可领取，满100元减15元      ] *必填    |
|                                                       |
| 活动类型: [优惠券发放  v]                                |
|                                                       |
| 开始时间: [2025-05-15] [00:00:00]                        |
|                                                       |
| 结束时间: [2025-08-15] [23:59:59]                        |
|                                                       |
| 最大使用次数: [5000          ]   (0表示不限制)              |
|                                                       |
|                                                       |
| 优惠券设置                                              |
|                                                       |
| 优惠券名称: [新客满减券                     ] *必填    |
|                                                       |
| 优惠券类型: [○满减券] [○折扣券] [●固定金额券]                 |
|                                                       |
| 优惠金额: [15.00         ] 元                            |
|                                                       |
| 最低订单金额: [100          ] 元                          |
|                                                       |
| 每人限领: [1            ] 张                            |
|                                                       |
| 有效期: [30           ] 天                              |
|                                                       |
| 适用范围: [●所有商品] [○指定分类] [○指定商品]                 |
|                                                       |
| 发放渠道                                               |
|                                                       |
| [√] 注册赠券  [√] 首页弹窗  [√] 优惠券中心                  |
|                                                       |
| [ 保存为草稿 ]    [ 预览 ]    [ 发布活动 ]                  |
+-------------------------------------------------------+
```

### API请求示例

```json
POST /merchant/api/takeout/promotions
Content-Type: application/json
Authorization: Bearer {merchant_token}

{
  "name": "新客专享优惠券",
  "description": "新用户注册即可领取，满100元减15元",
  "type": 4,  // 优惠券活动
  "start_time": "2025-05-15 00:00:00",
  "end_time": "2025-08-15 23:59:59",
  "max_usage_count": 5000,
  "status": 1,  // 活动中
  "rules": "{\"coupon\":{\"name\":\"新客满减券\",\"type\":1,\"amount\":15,\"min_order_amount\":100,\"per_user_limit\":1,\"valid_days\":30,\"apply_to_all\":true},\"channels\":[\"register\",\"popup\",\"center\"]}",
  "food_ids": []  // 所有商品都参与
}
```

### API响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 503,
    "merchant_id": 502,
    "name": "新客专享优惠券",
    "description": "新用户注册即可领取，满100元减15元",
    "type": 4,
    "start_time": "2025-05-15T00:00:00+08:00",
    "end_time": "2025-08-15T23:59:59+08:00",
    "status": 1,
    "rules": "{\"coupon\":{\"name\":\"新客满减券\",\"type\":1,\"amount\":15,\"min_order_amount\":100,\"per_user_limit\":1,\"valid_days\":30,\"apply_to_all\":true},\"channels\":[\"register\",\"popup\",\"center\"]}",
    "max_usage_count": 5000,
    "usage_count": 0,
    "created_at": "2025-05-14T16:10:35+08:00",
    "updated_at": "2025-05-14T16:10:35+08:00",
    "coupon_info": {
      "id": 201,
      "name": "新客满减券",
      "type": 1,
      "amount": 15,
      "min_order_amount": 100,
      "valid_days": 30,
      "per_user_limit": 1,
      "apply_to_all": true
    }
  }
}
```

## 4. 活动预览与发布

### 活动预览界面

```
+-------------------------------------------------------+
|                  活动预览                                |
+-------------------------------------------------------+
|                                                       |
| +---------------+                                     |
| |               |                                     |
| |   活动图片     |  新客专享优惠券                        |
| |               |                                     |
| +---------------+  新用户注册即可领取，满100元减15元         |
|                                                       |
| 有效期: 2025-05-15 至 2025-08-15                       |
|                                                       |
| 优惠券详情:                                            |
| - 满100元可用                                           |
| - 优惠金额15元                                          |
| - 适用于所有商品                                        |
| - 每人限领1张                                           |
|                                                       |
| 用户领取展示效果:                                       |
|                                                       |
| +---------------+          +---------------+          |
| |   新客满减券    |          |   立即领取     |          |
| |   满100减15   |          |               |          |
| +---------------+          +---------------+          |
|                                                       |
| [返回编辑]                     [确认发布]               |
+-------------------------------------------------------+
```

### 活动发布成功通知

```
+-------------------------------------------------------+
|                 活动发布成功                            |
+-------------------------------------------------------+
|                                                       |
| ✓ 恭喜，您的活动已成功发布！                              |
|                                                       |
| 活动ID: 503                                            |
| 活动名称: 新客专享优惠券                                 |
| 活动时间: 2025-05-15 至 2025-08-15                     |
| 发布时间: 2025-05-14 16:15:20                         |
|                                                       |
| 活动已在以下渠道展示:                                    |
| - 用户注册页面                                          |
| - APP首页弹窗                                           |
| - 优惠券中心                                            |
|                                                       |
| 您可以在「促销活动管理」中查看活动数据和效果               |
|                                                       |
| [ 查看活动详情 ]     [ 返回活动列表 ]                     |
+-------------------------------------------------------+
```
