# 外卖跑腿业务流程文档

## 1. 业务概述

外卖跑腿（配送）业务是外卖订单履行的关键环节，负责将商品从商家配送到用户手中。本文档详细描述外卖跑腿业务的完整流程，包括订单创建、骑手分配、配送状态流转以及异常处理机制等。

### 1.1 核心流程

外卖跑腿业务的核心流程包括：

1. 用户下单并支付
2. 商家接单
3. 商家分配配送员
4. 配送员开始配送
5. 配送员完成配送
6. 订单完成

### 1.2 主要参与角色

- **用户**：下单并接收配送服务
- **商家**：接单、准备商品和分配配送员
- **配送员（骑手）**：取餐并配送至用户指定地点
- **系统**：协调和管理整个配送流程

## 2. 跑腿订单创建时机与流程

### 2.1 创建时机

跑腿订单的创建主要有两个关键时机：

1. **用户下单支付后**：当用户成功支付外卖订单后，通过支付回调触发系统自动创建跑腿订单
2. **商家手动接单后**：商家确认接单后，系统将订单状态更新为"处理中"，配送状态更新为"已接单"

### 2.2 创建流程

#### 2.2.1 从购物车创建订单流程

1. 用户在购物车中选择商品并提交订单
2. 系统验证购物车有效性
3. 系统创建基础订单记录
4. 系统同时创建外卖订单扩展记录（`TakeoutOrderExtension`）
   - 设置初始配送状态为`DeliveryStatusPending`（待配送）
   - 配送员ID、姓名、电话等字段设置为空
5. 创建订单项及扩展记录
6. 生成支付信息并等待用户支付

#### 2.2.2 支付成功后的跑腿订单处理

支付回调处理时，系统会：

1. 根据支付流水号查询对应的订单
2. 获取外卖订单扩展信息
3. 检查订单是否已处理（`DeliveryStatus >= 1`）
4. 调用`createRunnerOrder`方法创建跑腿订单（可能涉及第三方跑腿平台）
5. 更新外卖订单扩展信息的配送状态为`DeliveryStatusPending`（待接单）

## 3. 骑手分配业务逻辑

### 3.1 分配条件

骑手分配必须满足以下条件：

1. 订单类型必须是外卖订单（`order.OrderType == 1`）
2. 订单状态必须是处理中（`order.Status == OrderStatusProcessing`）
3. 配送状态必须是已接单（`takeoutOrder.DeliveryStatus == DeliveryStatusAccepted`）

### 3.2 分配流程

#### 3.2.1 手动分配流程

1. 商家通过后台管理系统访问`/merchant/api/takeout/orders/assign`接口
2. 提供订单ID和配送员ID
3. 系统校验订单归属权和状态
4. 系统调用`AssignDelivery`方法分配骑手
5. 系统获取配送员信息（姓名、电话）
6. 更新外卖订单扩展信息：
   - 设置配送状态为`DeliveryStatusAssigned`（已分配配送员）
   - 记录配送员ID、姓名、电话
7. **创建跑腿员订单记录**：
   - 调用`runnerService.CreateRunnerOrder`方法
   - 传递配送员ID作为跑腿员ID
   - 从商家信息获取取货地址、联系人、经纬度
   - 从订单地址获取收货地址、联系人、电话
   - 创建完整的RunnerOrder记录，确保跑腿员可以通过自己的接口查询订单
8. 记录订单操作日志

#### 3.2.2 自动分配流程

1. 商家接单成功后，系统异步触发自动分配流程（3秒延迟，避免冲突）
2. 系统调用`AutoAssignDelivery`方法：
   - 检查订单状态和配送状态是否允许分配
   - 检查自动分配功能是否启用
   - 检查分配尝试次数是否超限
3. 获取商家位置信息
4. 查询指定半径内的可用骑手
5. 系统对骑手进行评分（基于距离、评分和工作量）
6. 选择最高评分骑手尝试分配：
   - 若分配成功，记录分配结果并更新订单信息
   - 若分配失败，尝试下一位骑手
   - 若全部失败，记录分配失败原因
7. 每次分配尝试都记录到订单扩展信息和日志中

### 3.3 骑手分配的API接口

```
POST /merchant/api/takeout/orders/assign
参数:
  - order_id: 订单ID
  - delivery_staff_id: 配送员ID
返回:
  - 成功/失败状态
```

## 4. 配送状态流转

### 4.1 配送状态定义

外卖系统中配送状态的定义和流转如下：

| 状态码 | 状态名称 | 说明 |
|--------|---------|------|
| 0 | DeliveryStatusWaiting | 等待配送（初始状态） |
| 1 | DeliveryStatusPicking | 正在取餐（支付后） |
| 2 | DeliveryStatusDelivering | 配送中 |
| 3 | DeliveryStatusCompleted | 已送达 |
| 4 | DeliveryStatusCancelled | 已取消 |

系统服务层中还定义了更细粒度的状态：

| 状态码 | 状态名称 | 说明 |
|--------|---------|------|
| 0 | DeliveryStatusPending | 待配送 |
| 1 | DeliveryStatusAccepted | 已接单 |
| 2 | DeliveryStatusAssigned | 已分配配送员 |
| 3 | DeliveryStatusPickup | 配送员取餐中 |
| 4 | DeliveryStatusInProgress | 配送中 |
| 5 | DeliveryStatusCompleted | 已送达 |
| 6 | DeliveryStatusCancelled | 已取消 |

### 4.2 状态流转图

```
创建订单 -> 待配送(Pending) 
             |
  用户支付   V
             已接单(Accepted) 
             |
  分配骑手   V
             已分配配送员(Assigned)
             |
  骑手取餐   V
             配送中(InProgress)
             |
  送达      V
             已送达(Completed) -> 订单完成
             
  (任何阶段) -> 已取消(Cancelled)
```

### 4.3 状态流转控制

系统严格控制状态流转顺序，每次状态变更前都会校验当前状态是否允许变更：

1. **分配骑手**：只有当配送状态为`DeliveryStatusAccepted`时才能分配骑手
2. **开始配送**：只有当配送状态为`DeliveryStatusAssigned`且由分配的骑手本人操作时才能开始配送
3. **完成配送**：只有当配送状态为`DeliveryStatusInProgress`且由分配的骑手本人操作时才能完成配送

## 5. 骑手配送流程

### 5.1 开始配送

1. 骑手通过移动端访问`/api/takeout/delivery/start`接口
2. 提供订单ID
3. 系统验证骑手身份和订单状态
4. 系统调用`StartDelivery`方法
5. 记录配送开始时间
6. 更新配送状态为`DeliveryStatusInProgress`（配送中）
7. 记录订单操作日志

### 5.2 完成配送

1. 骑手通过移动端访问`/api/takeout/delivery/complete`接口
2. 提供订单ID
3. 系统验证骑手身份和订单状态
4. 系统调用`CompleteDelivery`方法
5. 记录配送结束时间
6. 更新配送状态为`DeliveryStatusCompleted`（已送达）
7. 自动调用`CompleteOrder`方法尝试完成整个订单
8. 记录订单操作日志

## 6. 异常处理机制

### 6.1 订单取消

#### 6.1.1 用户取消订单

用户只能取消处于待支付或待接单状态且配送状态为待配送的订单：

```go
// 检查订单状态，只有待支付和待接单的订单可以取消
if order.Status != OrderStatusPending && order.Status != OrderStatusPaid {
    return errors.New("当前订单状态不允许取消")
}

// 检查配送状态
if takeoutOrder.DeliveryStatus != DeliveryStatusPending {
    return errors.New("订单已开始配送，无法取消")
}
```

#### 6.1.2 商家取消订单

商家有更多权限取消订单，但不能取消配送中或已送达的订单：

```go
if takeoutOrder.DeliveryStatus == DeliveryStatusInProgress || takeoutOrder.DeliveryStatus == DeliveryStatusCompleted {
    return errors.New("订单配送中或已完成，无法取消")
}
```

### 6.2 事务控制

系统在所有状态变更操作中使用数据库事务，确保数据一致性：

1. 开始事务：`tx, err := o.Begin()`
2. 操作失败时回滚：`tx.Rollback()`
3. 操作成功时提交：`err = tx.Commit()`

### 6.3 错误日志记录

系统在关键操作点记录详细日志，便于问题追踪：

```go
logs.Error("分配配送员失败: %v, 订单ID: %d", err, orderID)
```

### 6.4 订单操作日志

系统记录所有订单状态变更的操作日志，包括：

1. 操作人ID
2. 操作类型（接单、分配配送、开始配送、完成配送等）
3. 操作备注
4. 操作时间

## 7. 跑腿业务与内部模块和第三方平台集成

### 7.1 跑腿员模块数据同步

系统在分配跑腿员时，会进行内部数据同步，确保跑腿员可以查看自己被分配的订单：

1. 当商家成功分配跑腿员时，系统会在外卖订单扩展表中记录跑腿员ID、姓名、电话等信息
2. 同时，系统会自动调用`runnerService.CreateRunnerOrder`方法在跑腿员模块创建对应的订单记录
3. 创建RunnerOrder时包含以下关键数据：
   - 跑腿员ID（runner_id）：确保订单与特定跑腿员关联
   - 商家信息：包括取货地址、联系人、联系电话、经纬度坐标
   - 收货人信息：包括送货地址、联系人、联系电话
   - 订单金额、配送费、商品描述等信息
4. 跑腿员可通过`/api/v1/runner-order/runner/list`接口查询自己的订单
5. 查询时系统根据请求上下文中的runner_id过滤订单，确保跑腿员只能查看属于自己的订单

### 7.2 第三方跑腿平台集成

系统支持与第三方跑腿平台集成，在支付成功后通过`createRunnerOrder`方法创建第三方跑腿订单。具体集成逻辑包括：

1. 获取订单和商家信息
2. 构造第三方平台所需的订单数据（商品信息、地址、金额等）
3. 调用第三方平台API创建跑腿订单
4. 处理响应结果和异常情况

## 8. 总结

外卖跑腿业务流程是一个复杂而完整的系统，涉及多个参与方和状态流转。系统通过严格的状态控制、事务管理和异常处理，确保订单配送过程的可靠性和数据一致性。关键流程包括订单创建、商家接单、骑手分配、配送开始和完成，每个环节都有明确的业务规则和状态校验，确保整个流程顺畅进行。

系统的跨模块数据同步机制确保外卖订单、跑腿员订单信息的一致性，让跑腿员可以通过专用接口查看和更新自己的配送订单，提升了系统整体的可用性和用户体验。
