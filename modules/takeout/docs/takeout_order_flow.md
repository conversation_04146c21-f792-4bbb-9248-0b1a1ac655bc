# 外卖订单流程文档

## 1. 订单模块概述

外卖订单模块是整个外卖系统的核心组件，负责处理订单的创建、支付、配送和完成等完整生命周期。该模块与购物车、商品、用户、支付等多个模块紧密集成，实现了完整的外卖点餐下单流程。

### 1.1 主要功能

- 从购物车创建外卖订单
- 订单支付处理与状态管理
- 订单配送流程跟踪
- 订单取消与退款处理
- 订单评价与反馈
- 订单统计与分析

### 1.2 核心组件

- **控制器层**：处理HTTP请求和响应
- **服务层**：实现业务逻辑和流程处理
- **仓库层**：数据访问和持久化
- **模型层**：数据结构定义
- **DTO层**：数据传输对象定义

## 2. 数据模型设计

### 2.1 TakeoutOrderExtension 外卖订单扩展

外卖订单扩展模型用于存储外卖特有的订单信息，通过关联现有的订单系统，实现外卖商品订单的处理流程。

```go
type TakeoutOrderExtension struct {
    ID                    int64     // 主键ID
    OrderID               int64     // 关联的订单ID
    OrderNo               string    // 订单编号
    MerchantID            int64     // 商家ID
    OrderType             int       // 订单类型:1外卖订单,2其他
    ExpectedDeliveryTime  time.Time // 预计送达时间
    DeliveryStaffID       int64     // 配送员ID
    DeliveryStaffName     string    // 配送员姓名
    DeliveryStaffPhone    string    // 配送员电话
    DeliveryStatus        int       // 配送状态
    DeliveryType          int       // 配送类型
    DeliveryFee           float64   // 配送费
    PackagingFee          float64   // 包装费总额
    TablewareQuantity     int       // 餐具数量
    EatingStyle           int       // 用餐方式
    DistanceKm            float64   // 配送距离(公里)
    EstimatedDeliveryTime time.Time // 预计送达时间
    IsPreOrder            bool      // 是否预订单
    PreOrderTime          time.Time // 预订时间
    DeliveryStartTime     time.Time // 配送开始时间
    DeliveryEndTime       time.Time // 配送结束时间
    IsRated               bool      // 是否已评价
    Remark                string    // 订单备注
    CreatedAt             time.Time // 创建时间
    UpdatedAt             time.Time // 更新时间
}
```

### 2.2 TakeoutOrderItem 外卖订单项扩展

外卖订单项扩展用于存储外卖订单项的特殊属性，如规格选择、套餐组合等。

```go
type TakeoutOrderItem struct {
    ID              int64     // 主键ID
    OrderItemID     int64     // 关联的订单项ID
    OrderID         int64     // 订单ID
    FoodID          int64     // 外卖商品ID
    VariantID       int64     // 规格变体ID
    VariantName     string    // 规格变体名称
    PackagingFee    float64   // 包装费
    ComboSelectData string    // 套餐选择数据，JSON格式
    Remark          string    // 商品备注
    CreatedAt       time.Time // 创建时间
    UpdatedAt       time.Time // 更新时间
}
```

### 2.3 TakeoutOrderLog 外卖订单日志

记录订单状态变更和操作历史。

```go
type TakeoutOrderLog struct {
    ID          int64     // 日志ID
    OrderID     int64     // 订单ID
    UserID      int64     // 用户ID
    Action      string    // 操作类型
    Description string    // 操作描述
    Remark      string    // 备注
    IP          string    // 操作IP
    CreatedAt   time.Time // 创建时间
}
```

### 2.4 TakeoutOrderRating 订单评价

存储用户对订单的评价信息。

```go
type TakeoutOrderRating struct {
    ID             int64     // 评价ID
    OrderID        int64     // 订单ID
    UserID         int64     // 用户ID
    Rating         int       // 评分，1-5星
    DeliveryRating int       // 配送评分，1-5星
    Comment        string    // 评价内容
    CreatedAt      time.Time // 创建时间
    UpdatedAt      time.Time // 更新时间
}
```

## 3. 订单状态与流转

### 3.1 订单状态定义

```go
// 订单状态常量
const (
    OrderStatusPending    = 10 // 待支付
    OrderStatusPaid       = 20 // 已支付
    OrderStatusProcessing = 30 // 处理中
    OrderStatusDelivering = 40 // 配送中
    OrderStatusCompleted  = 50 // 已完成
    OrderStatusCancelled  = 60 // 已取消
    OrderStatusRefunding  = 70 // 退款中
    OrderStatusRefunded   = 80 // 已退款
)
```

### 3.2 配送状态定义

```go
// 配送状态常量
const (
    DeliveryStatusWaiting    = 0  // 待配送（初始状态）
    DeliveryStatusPending    = 10 // 待接单
    DeliveryStatusAccepted   = 20 // 已接单
    DeliveryStatusPicking    = 30 // 取餐中
    DeliveryStatusPickedUp   = 40 // 已取餐
    DeliveryStatusDelivering = 50 // 配送中
    DeliveryStatusCompleted  = 60 // 已送达
    DeliveryStatusCancelled  = 70 // 已取消
)
```

### 3.3 状态流转图

```
创建订单 -> 待支付 -> 已支付 -> 处理中 -> 配送中 -> 已完成
                 \         \         \
                  \         \         -> 已取消
                   \         -> 退款中 -> 已退款
                    -> 已取消
```

## 4. 订单创建流程

### 4.1 创建订单的步骤

1. 用户在购物车中选择商品
2. 提交订单请求，包含配送地址、支付方式等信息
3. 系统验证购物车商品有效性
4. 计算订单金额、配送费、包装费等
5. 创建基础订单记录
6. 创建外卖订单扩展记录
7. 创建订单项记录
8. 创建外卖订单项扩展记录
9. 清空购物车中已下单的商品
10. 生成支付信息
11. 返回订单创建结果

### 4.2 订单创建示例代码

```go
// CreateOrderFromCart 从购物车创建订单
func (s *takeoutOrderService) CreateOrderFromCart(userID int64, req *dto.CreateTakeoutOrderRequest) (*dto.TakeoutOrderDTO, error) {
    // 检查购物车项是否存在
    if len(req.CartItemIDs) == 0 {
        return nil, errors.New("购物车为空")
    }

    // 获取购物车汇总信息
    cartSummary, err := s.cartService.GetCartSummary(userID, req.CartItemIDs)
    if err != nil {
        return nil, err
    }

    // 开始数据库事务
    o := orm.NewOrm()
    tx, err := o.Begin()
    
    // 处理购物车项目，转换为订单项
    // 计算配送费、包装费等
    // 创建订单记录
    // 创建订单项记录
    // 提交事务
    
    // 返回订单信息
}
```

## 5. 订单查询功能

### 5.1 查询单个订单详情

```go
// GetOrderByID 根据ID获取订单详情
func (s *takeoutOrderService) GetOrderByID(orderID int64) (*dto.TakeoutOrderDTO, error) {
    // 实现获取订单详情的逻辑
}
```

### 5.2 查询用户订单列表

```go
// ListOrdersByUserID 查询用户订单列表
func (s *takeoutOrderService) ListOrdersByUserID(userID int64, status int, page, pageSize int) (*dto.TakeoutOrderListDTO, error) {
    // 实现分页查询用户订单列表的逻辑
}
```

## 6. 订单状态管理

### 6.1 取消订单

```go
// CancelOrder 取消订单
func (s *takeoutOrderService) CancelOrder(orderID int64, userID int64, reason string) error {
    // 实现取消订单的逻辑
}
```

### 6.2 商家接单

```go
// AcceptOrder 商家接单
func (s *takeoutOrderService) AcceptOrder(orderID int64, operatorID int64) error {
    // 实现商家接单的逻辑
}
```

### 6.3 分配配送

```go
// AssignDelivery 分配配送员
func (s *takeoutOrderService) AssignDelivery(orderID int64, deliveryStaffID int64, operatorID int64) error {
    // 实现分配配送员的逻辑
}
```

### 6.4 开始配送

```go
// StartDelivery 开始配送
func (s *takeoutOrderService) StartDelivery(orderID int64, deliveryStaffID int64) error {
    // 实现开始配送的逻辑
}
```

### 6.5 完成配送

```go
// CompleteDelivery 完成配送
func (s *takeoutOrderService) CompleteDelivery(orderID int64, deliveryStaffID int64) error {
    // 实现完成配送的逻辑
}
```

### 6.6 完成订单

```go
// CompleteOrder 完成订单
func (s *takeoutOrderService) CompleteOrder(orderID int64) error {
    // 实现完成订单的逻辑
}
```

## 7. 订单评价

```go
// RateOrder 评价订单
func (s *takeoutOrderService) RateOrder(orderID int64, userID int64, req *dto.RateTakeoutOrderRequest) error {
    // 实现评价订单的逻辑
}
```

## 8. 订单统计功能

### 8.1 基础订单统计

```go
// GetOrderStatistics 获取订单统计
func (s *takeoutOrderService) GetOrderStatistics(startDate, endDate time.Time) (*dto.TakeoutOrderStatisticsDTO, error) {
    // 实现订单统计的逻辑
}
```

### 8.2 商家订单统计

```go
// GetOrderStatisticsByMerchantID 获取商家订单统计
func (s *takeoutOrderService) GetOrderStatisticsByMerchantID(merchantID int64) (totalCount, completedCount, processingCount, cancelledCount int, err error) {
    // 实现商家订单统计的逻辑
}
```

## 9. 管理员订单管理

### 9.1 管理员订单查询

```go
// GetOrdersPageForAdmin 管理员查询订单列表
func (s *takeoutOrderService) GetOrdersPageForAdmin(merchantID int64, orderNumber string, status int, startTime, endTime string, page, pageSize int) ([]dto.AdminOrderListItemDTO, int64, error) {
    // 实现管理员查询订单列表的逻辑
}
```

### 9.2 管理员查看订单详情

```go
// GetOrderDetailForAdmin 管理员查看订单详情
func (s *takeoutOrderService) GetOrderDetailForAdmin(orderID int64) (*dto.AdminOrderDetailDTO, error) {
    // 实现管理员查看订单详情的逻辑
}
```

### 9.3 管理员更新订单

```go
// UpdateOrderForAdmin 管理员更新订单
func (s *takeoutOrderService) UpdateOrderForAdmin(req *dto.UpdateOrderRequest) error {
    // 实现管理员更新订单的逻辑
}
```

## 10. 最佳实践与注意事项

1. 订单创建操作应在事务中进行，确保数据一致性
2. 订单状态变更应记录日志，方便追踪和问题排查
3. 订单金额计算应考虑精度问题，避免浮点数精度丢失
4. 订单查询接口应支持多种条件过滤和排序
5. 订单评价应在订单完成后进行，避免提前评价
6. 订单取消和退款应考虑各种场景和条件限制
7. 配送状态更新应实时通知用户，提升用户体验
