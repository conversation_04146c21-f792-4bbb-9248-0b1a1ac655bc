# 外卖支付集成文档

## 1. 支付模块概述

外卖模块与支付模块的集成是实现订单完整流程的关键环节。本文档详细说明了外卖订单的支付流程、支付状态管理以及与支付模块的交互方式。

### 1.1 支付相关功能

- 订单支付信息生成
- 支付状态追踪与更新
- 支付结果处理
- 退款流程处理
- 支付日志记录

### 1.2 支付模块组件

- **支付记录（Payment）**：记录支付信息和状态
- **退款记录（Refund）**：记录退款信息和状态
- **支付账户（PaymentAccount）**：平台支付账户配置
- **支付日志（PaymentLog）**：支付操作日志
- **支付账单（PaymentBill）**：支付对账记录

## 2. 支付数据模型

### 2.1 支付方式枚举

```go
// PaymentMethod 支付方式枚举
type PaymentMethod int

const (
    // PaymentMethodUnknown 未知支付方式
    PaymentMethodUnknown PaymentMethod = iota
    // PaymentMethodWechat 微信支付
    PaymentMethodWechat
    // PaymentMethodAlipay 支付宝
    PaymentMethodAlipay
    // PaymentMethodCreditCard 信用卡
    PaymentMethodCreditCard
    // PaymentMethodBankTransfer 银行转账
    PaymentMethodBankTransfer
    // PaymentMethodBalance 余额支付
    PaymentMethodBalance
    // PaymentMethodCombination 组合支付
    PaymentMethodCombination
)
```

### 2.2 支付状态枚举

```go
// PaymentStatus 支付状态枚举
type PaymentStatus int

const (
    // PaymentStatusPending 待支付
    PaymentStatusPending PaymentStatus = iota
    // PaymentStatusProcessing 处理中
    PaymentStatusProcessing
    // PaymentStatusSuccess 支付成功
    PaymentStatusSuccess
    // PaymentStatusFailed 支付失败
    PaymentStatusFailed
    // PaymentStatusCancelled 已取消
    PaymentStatusCancelled
    // PaymentStatusRefunding 退款中
    PaymentStatusRefunding
    // PaymentStatusRefunded 已退款
    PaymentStatusRefunded
    // PaymentStatusPartialRefunded 部分退款
    PaymentStatusPartialRefunded
)
```

### 2.3 支付记录模型

```go
// Payment 支付记录
type Payment struct {
    ID              int64         // 支付记录ID
    OrderID         int64         // 关联订单ID
    UserID          int64         // 支付用户ID
    Amount          float64       // 支付金额
    Method          PaymentMethod // 支付方式
    Status          PaymentStatus // 支付状态
    TransactionNo   string        // 交易流水号
    ExternalTradeNo string        // 外部支付平台交易号
    PaymentTime     *time.Time    // 支付完成时间
    ExpireTime      *time.Time    // 支付过期时间
    CallbackData    string        // 支付回调原始数据
    ClientIP        string        // 客户端IP
    DeviceInfo      string        // 设备信息
    Remark          string        // 备注
    CreatedAt       time.Time     // 创建时间
    UpdatedAt       time.Time     // 更新时间
}
```

### 2.4 退款记录模型

```go
// Refund 退款记录
type Refund struct {
    ID             int64        // 退款记录ID
    PaymentID      int64        // 关联支付记录ID
    OrderID        int64        // 关联订单ID
    UserID         int64        // 用户ID
    Amount         float64      // 退款金额
    Status         RefundStatus // 退款状态
    RefundNo       string       // 退款流水号
    ExternalNo     string       // 外部退款号
    Reason         string       // 退款原因
    RefundTime     *time.Time   // 退款完成时间
    CallbackData   string       // 退款回调原始数据
    OperatorID     int64        // 操作人ID
    ApprovalStatus int          // 审批状态
    ApproverID     int64        // 审批人ID
    ApprovalTime   *time.Time   // 审批时间
    ApprovalRemark string       // 审批备注
    CreatedAt      time.Time    // 创建时间
    UpdatedAt      time.Time    // 更新时间
}
```

## 3. 订单支付流程

### 3.1 获取订单支付信息

用户在创建订单后，需要获取支付信息进行支付。外卖模块提供了获取订单支付信息的接口：

```go
// GetOrderPayment 获取订单支付信息
// @Title 获取订单支付信息
// @Description 获取外卖订单的支付信息
// @Param orderID path int true "订单ID"
// @Success 200 {object} dto.TakeoutOrderPaymentDTO "订单支付信息"
// @Failure 400 {object} controllers.Response "参数错误"
// @Failure 500 {object} controllers.Response "服务器内部错误"
// @router /payment/:orderID [get]
func (c *TakeoutOrderController) GetOrderPayment() {
    // 获取订单ID
    orderID, err := c.GetInt64(":orderID")
    if err != nil || orderID <= 0 {
        result.HandleError(c.Ctx, result.NewError(400, "订单ID无效"))
        return
    }

    // 获取支付信息
    payment, err := c.orderService.GetOrderPaymentInfo(orderID)
    if err != nil {
        logs.Error("获取订单支付信息失败: %v, 订单ID: %d", err, orderID)
        result.HandleError(c.Ctx, err)
        return
    }

    result.OK(c.Ctx, payment)
}
```

### 3.2 支付信息DTO

```go
// TakeoutOrderPaymentDTO 外卖订单支付信息
type TakeoutOrderPaymentDTO struct {
    OrderID           int64      // 订单ID
    OrderNo           string     // 订单编号
    TotalAmount       float64    // 订单总金额
    DiscountAmount    float64    // 折扣金额
    PackagingFee      float64    // 包装费
    DeliveryFee       float64    // 配送费
    CouponDiscount    float64    // 优惠券折扣
    PointsDiscount    float64    // 积分折扣
    PayAmount         float64    // 支付金额
    PaymentMethod     string     // 支付方式
    PayStatus         int        // 支付状态
    PaymentStatus     int        // 支付状态(与数据库字段对应)
    PayTime           *time.Time // 支付时间
    ExpireTime        time.Time  // 过期时间
    PaymentExpireTime time.Time  // 支付过期时间
    QRCodeURL         string     // 支付二维码URL
    PaymentURL        string     // 支付跳转URL
}
```

### 3.3 支付流程图

```
用户创建订单 -> 获取支付信息 -> 选择支付方式 -> 调用支付接口
                                             |
                                             v
订单更新为已支付 <- 处理支付回调 <- 支付平台处理支付 <- 跳转到支付页面/扫码支付
```

## 4. 支付状态管理

### 4.1 支付状态更新

支付模块通过回调接口通知外卖模块支付结果，外卖模块根据支付结果更新订单状态：

```go
// 处理支付回调
func (s *paymentService) HandlePaymentCallback(callbackData map[string]string) error {
    // 验证回调数据
    // 获取订单ID
    // 更新支付状态
    // 通知外卖模块更新订单状态
}
```

### 4.2 订单状态与支付状态的关系

| 支付状态 | 订单状态 | 说明 |
|--------|--------|------|
| PaymentStatusPending | OrderStatusPending | 待支付 |
| PaymentStatusProcessing | OrderStatusPending | 支付处理中 |
| PaymentStatusSuccess | OrderStatusPaid | 支付成功，订单已支付 |
| PaymentStatusFailed | OrderStatusPending | 支付失败，订单仍待支付 |
| PaymentStatusCancelled | OrderStatusCancelled | 支付已取消，订单已取消 |
| PaymentStatusRefunding | OrderStatusRefunding | 退款中 |
| PaymentStatusRefunded | OrderStatusRefunded | 已退款 |
| PaymentStatusPartialRefunded | OrderStatusRefunded/OrderStatusPaid | 部分退款 |

## 5. 支付模块接口调用

### 5.1 创建支付记录

```go
// 创建支付记录
func (s *paymentService) CreatePayment(orderID, userID int64, amount float64, method PaymentMethod) (*Payment, error) {
    // 实现创建支付记录的逻辑
    payment := &Payment{
        OrderID:       orderID,
        UserID:        userID,
        Amount:        amount,
        Method:        method,
        Status:        PaymentStatusPending,
        TransactionNo: generateTransactionNo(),
        ExpireTime:    time.Now().Add(time.Hour * 2), // 支付有效期2小时
        ClientIP:      clientIP,
    }
    
    // 保存支付记录
    // 返回支付记录
}
```

### 5.2 获取支付信息

```go
// 获取支付信息
func (s *paymentService) GetPaymentInfo(orderID int64) (*PaymentInfoDTO, error) {
    // 实现获取支付信息的逻辑
    // 查询支付记录
    // 生成支付参数
    // 返回支付信息
}
```

### 5.3 更新支付状态

```go
// 更新支付状态
func (s *paymentService) UpdatePaymentStatus(transactionNo string, status PaymentStatus, externalTradeNo string, callbackData string) error {
    // 实现更新支付状态的逻辑
    // 查询支付记录
    // 更新支付状态
    // 记录支付日志
    // 通知订单模块
}
```

### 5.4 申请退款

```go
// 申请退款
func (s *paymentService) ApplyRefund(orderID int64, userID int64, amount float64, reason string) (*Refund, error) {
    // 实现申请退款的逻辑
    // 查询支付记录
    // 创建退款记录
    // 调用支付平台退款接口
    // 返回退款记录
}
```

## 6. 外卖订单支付方法实现

### 6.1 获取订单支付信息

```go
// GetOrderPaymentInfo 获取订单支付信息
func (s *takeoutOrderService) GetOrderPaymentInfo(orderID int64) (*dto.TakeoutOrderPaymentDTO, error) {
    // 查询订单基本信息
    order, err := s.orderRepo.GetByOrderID(orderID)
    if err != nil {
        return nil, err
    }
    
    // 查询支付信息
    payment, err := s.paymentService.GetPaymentByOrderID(orderID)
    if err != nil {
        // 如果没有支付记录，创建新的支付记录
        // ...
    }
    
    // 构建支付信息DTO
    paymentDTO := &dto.TakeoutOrderPaymentDTO{
        OrderID:           orderID,
        OrderNo:           order.OrderNo,
        TotalAmount:       order.TotalAmount,
        DiscountAmount:    order.DiscountAmount,
        PackagingFee:      order.PackagingFee,
        DeliveryFee:       order.DeliveryFee,
        CouponDiscount:    order.CouponDiscount,
        PointsDiscount:    order.PointsDiscount,
        PayAmount:         order.PayAmount,
        PaymentMethod:     payment.Method.String(),
        PayStatus:         int(payment.Status),
        PaymentStatus:     int(payment.Status),
        PayTime:           payment.PaymentTime,
        ExpireTime:        *payment.ExpireTime,
        PaymentExpireTime: *payment.ExpireTime,
        QRCodeURL:         generateQRCodeURL(payment.TransactionNo),
        PaymentURL:        generatePaymentURL(payment.TransactionNo),
    }
    
    return paymentDTO, nil
}
```

### 6.2 处理支付结果

```go
// 处理支付结果
func (s *takeoutOrderService) HandlePaymentResult(orderID int64, paymentStatus int, transactionNo string) error {
    // 获取订单信息
    order, err := s.orderRepo.GetByOrderID(orderID)
    if err != nil {
        return err
    }
    
    // 更新订单状态
    switch paymentStatus {
    case int(PaymentStatusSuccess):
        // 支付成功，更新订单状态为已支付
        return s.updateOrderStatus(orderID, OrderStatusPaid)
    case int(PaymentStatusFailed):
        // 支付失败，保持订单状态为待支付
        return nil
    case int(PaymentStatusCancelled):
        // 支付取消，更新订单状态为已取消
        return s.CancelOrder(orderID, order.UserID, "支付已取消")
    case int(PaymentStatusRefunded), int(PaymentStatusPartialRefunded):
        // 已退款，更新订单状态为已退款
        return s.updateOrderStatus(orderID, OrderStatusRefunded)
    default:
        return errors.New("未知的支付状态")
    }
}
```

## 7. 支付集成最佳实践

### 7.1 安全性考虑

1. **支付信息加密**：敏感支付信息（如密钥、卡号等）应该加密存储
2. **防止重复支付**：实现幂等性处理，避免重复支付问题
3. **回调验签**：验证支付回调的签名，防止伪造回调
4. **支付超时处理**：设置合理的支付超时时间，超时自动取消订单

### 7.2 异常处理

1. **支付失败重试**：支付失败时，允许用户重新选择支付方式
2. **订单状态不一致**：定期检查订单状态与支付状态是否一致，自动修正不一致问题
3. **退款失败处理**：退款失败时，记录日志并通知管理员手动处理
4. **支付回调超时**：当支付成功但回调未到达时，通过主动查询支付状态进行同步

### 7.3 性能优化

1. **异步处理支付回调**：使用消息队列异步处理支付回调，提高系统响应速度
2. **缓存支付信息**：缓存常用支付信息，减少数据库查询
3. **批量处理退款**：对于大量退款请求，采用批量处理方式提高效率
4. **支付状态定时同步**：定时与支付平台同步支付状态，保证数据一致性

### 7.4 监控与报警

1. **支付异常监控**：监控支付失败率，当失败率超过阈值时报警
2. **退款异常监控**：监控退款失败情况，及时处理退款异常
3. **支付超时监控**：监控支付处理时间，当处理时间过长时报警
4. **支付账单对账**：每日对账，确保支付记录与实际收款一致

## 8. 支付测试

### 8.1 测试环境配置

1. 配置测试环境支付账户，使用支付平台提供的沙箱环境
2. 模拟各种支付场景，包括支付成功、支付失败、支付超时等
3. 测试支付回调处理逻辑
4. 测试退款流程

### 8.2 常见测试案例

1. **正常支付流程**：创建订单 -> 获取支付信息 -> 支付成功 -> 订单状态更新
2. **支付失败处理**：创建订单 -> 获取支付信息 -> 支付失败 -> 重新支付
3. **支付取消处理**：创建订单 -> 获取支付信息 -> 取消支付 -> 订单取消
4. **支付超时处理**：创建订单 -> 获取支付信息 -> 支付超时 -> 订单自动取消
5. **退款流程测试**：已支付订单 -> 申请退款 -> 退款成功 -> 订单状态更新

## 9. 常见问题与解决方案

### 9.1 支付回调未收到

**问题**：用户完成支付，但系统未收到支付平台的回调通知。

**解决方案**：
1. 设置支付结果查询定时任务，定期查询未完成支付的订单状态
2. 提供手动同步支付状态的接口，用户可主动触发同步
3. 确保回调URL可访问，避免防火墙拦截等问题

### 9.2 订单重复支付

**问题**：同一订单被多次支付，导致用户多付款。

**解决方案**：
1. 在创建支付记录时检查是否已存在成功的支付记录
2. 实现幂等性处理，对于同一订单的重复支付请求，返回原支付结果
3. 定期检查重复支付情况，自动发起退款

### 9.3 退款失败

**问题**：申请退款后，退款处理失败。

**解决方案**：
1. 记录退款失败日志，并设置重试机制
2. 设置退款状态监控，对长时间未完成的退款进行报警
3. 提供手动处理接口，允许管理员手动处理异常退款

### 9.4 支付状态与订单状态不一致

**问题**：支付状态显示已支付，但订单状态仍为待支付。

**解决方案**：
1. 实现订单状态与支付状态的定期对账机制
2. 发现不一致时，根据支付状态自动修正订单状态
3. 记录状态修正日志，便于后续分析问题原因

## 10. 总结

外卖模块与支付模块的集成是实现完整外卖订单流程的关键环节。通过合理设计支付流程、处理支付结果、管理支付状态，可以为用户提供流畅的支付体验。同时，通过实施安全措施、异常处理、性能优化和监控报警，确保支付系统的稳定性和可靠性。
