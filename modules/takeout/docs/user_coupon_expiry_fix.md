# 用户优惠券过期状态修复

## 问题描述

前端访问GetMyCoupons方法时，携带以下参数：
- refresh: true
- page: 1
- page_size: 20

后端应该返回全部状态的优惠券，但是当优惠券过期之后，状态应该显示为"已过期"而不是"未使用"。

## 问题根源

1. **实时状态检查缺失**：在获取用户优惠券列表时，系统没有实时检查优惠券的`end_time`与当前时间的关系
2. **状态更新机制不完善**：虽然有`UpdateExpiredCoupons`方法，但需要定时调用，在API调用时没有被触发
3. **前端参数处理不完整**：控制器没有正确处理`refresh`参数

## 解决方案

### 1. 修改控制器层 (TakeoutCouponController)

在`GetMyCoupons`方法中添加对`refresh`参数的处理：

```go
// 检查是否为刷新请求
refresh, _ := c.GetBool("refresh", false)

var status int
if refresh {
    // 如果是刷新请求，获取所有状态的优惠券
    status = -1 // -1表示获取所有状态
    logs.Info("刷新请求，获取用户所有状态的优惠券，用户ID: %d", userID)
} else {
    // 否则按指定状态过滤，默认获取未使用的
    status, _ = c.GetInt("status", models.UserCouponStatusUnused)
}
```

### 2. 修改服务层 (TakeoutCouponService)

在`GetUserCoupons`方法中添加实时过期检查：

```go
// 实时检查并更新过期的优惠券状态
now := time.Now()
var updatedUserCoupons []*models.TakeoutUserCoupon
var updatedCoupons []*models.TakeoutCoupon

for i, userCoupon := range userCoupons {
    if i < len(coupons) {
        coupon := coupons[i]
        // 检查优惠券是否已过期但状态仍为未使用
        if userCoupon.Status == models.UserCouponStatusUnused && now.After(coupon.EndTime) {
            // 更新数据库中的状态为已过期
            err := s.userCouponRepo.UpdateStatus(userCoupon.ID, models.UserCouponStatusExpired, 0)
            if err != nil {
                logs.Error("更新过期优惠券状态失败: %v", err)
            } else {
                // 更新内存中的状态
                userCoupon.Status = models.UserCouponStatusExpired
                logs.Info("优惠券已过期，状态已更新: 用户优惠券ID=%d, 优惠券ID=%d", userCoupon.ID, coupon.ID)
            }
        }
        updatedUserCoupons = append(updatedUserCoupons, userCoupon)
        updatedCoupons = append(updatedCoupons, coupon)
    }
}
```

## 测试验证

### API测试

**请求示例**：
```
GET /api/v1/user/takeout/coupons/my-list?refresh=true&page=1&page_size=20
```

**期望响应**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 1,
        "list": [
            {
                "id": 1,
                "user_id": 2,
                "coupon_id": 1,
                "coupon": {
                    "id": 1,
                    "name": "大额满减劵",
                    "end_time": "2025-07-20T11:37:50+08:00"
                },
                "status": 3,
                "status_text": "已过期",
                "created_at": "2025-07-01T23:17:17+08:00"
            }
        ]
    }
}
```

### 状态转换逻辑

1. **未使用 + 未过期** → 状态保持为"未使用"
2. **未使用 + 已过期** → 状态更新为"已过期"
3. **已使用** → 状态保持为"已使用"
4. **已过期** → 状态保持为"已过期"

## 优化建议

1. **定时任务**：建议保留定时任务来批量更新过期优惠券，减少API调用时的处理负担
2. **缓存优化**：考虑对优惠券状态进行缓存，避免频繁的数据库更新
3. **日志记录**：增加详细的日志记录，便于问题排查和监控

## 相关文件

- `modules/takeout/controllers/takeout_coupon_controller.go`
- `modules/takeout/services/takeout_coupon_service.go`
- `modules/takeout/repositories/takeout_user_coupon_repository.go`
- `modules/takeout/dto/coupon_dto.go`
