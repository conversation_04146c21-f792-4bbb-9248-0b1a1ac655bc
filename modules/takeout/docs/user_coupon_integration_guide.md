# 外卖模块用户端优惠券集成指南

## 概述

本文档为外卖模块用户端优惠券功能提供完整的集成指南，包括API接口说明、前端集成方案、用户体验设计建议等。

## API接口说明

### 基础信息

- **基础URL**: `/api/v1/user/takeout/coupons`
- **认证方式**: JWT Token（需要用户登录）
- **请求头**: `Authorization: Bearer {token}`

### 接口列表

#### 1. 获取用户优惠券列表

```http
GET /api/v1/user/takeout/coupons/list
```

**查询参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认10
- `status` (string): 优惠券状态 (unused/used/expired)
- `merchant_id` (int): 商家ID（可选）

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "coupons": [
      {
        "id": 1,
        "coupon_id": 101,
        "name": "满50减10",
        "type": "discount",
        "discount_amount": 10.00,
        "min_order_amount": 50.00,
        "merchant_id": 1,
        "merchant_name": "美味餐厅",
        "status": "unused",
        "expire_time": "2024-12-31T23:59:59Z",
        "received_time": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 5,
    "page": 1,
    "page_size": 10
  }
}
```

#### 2. 获取优惠券详情

```http
GET /api/v1/user/takeout/coupons/{id}
```

**路径参数**:
- `id` (int): 用户优惠券ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "coupon_id": 101,
    "name": "满50减10",
    "description": "订单满50元可使用",
    "type": "discount",
    "discount_amount": 10.00,
    "min_order_amount": 50.00,
    "merchant_id": 1,
    "merchant_name": "美味餐厅",
    "status": "unused",
    "expire_time": "2024-12-31T23:59:59Z",
    "received_time": "2024-01-01T00:00:00Z",
    "usage_rules": "仅限外卖订单使用，不可与其他优惠同享"
  }
}
```

#### 3. 检查优惠券可用性

```http
POST /api/v1/user/takeout/coupons/{id}/check
```

**请求体**:
```json
{
  "coupon_id": 101,
  "total_amount": 60.00
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "available": true,
    "reason": "",
    "discount_amount": 10.00
  }
}
```

#### 4. 验证优惠券是否适用于订单

```http
POST /api/v1/user/takeout/coupons/{id}/validate
```

**请求体**:
```json
{
  "coupon_id": 101,
  "order": {
    "merchant_id": 1,
    "total_amount": 60.00,
    "items": [
      {
        "food_id": 1,
        "quantity": 2,
        "price": 25.00,
        "category_id": 1
      }
    ]
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "valid": true,
    "discount_amount": 10.00,
    "final_amount": 50.00,
    "reason": ""
  }
}
```

#### 5. 获取可用优惠券列表

```http
GET /api/v1/user/takeout/coupons/available
```

**查询参数**:
- `merchant_id` (int): 商家ID
- `total_amount` (float): 订单总金额

#### 6. 获取商家可领取的优惠券和促销信息

```http
GET /api/v1/user/takeout/merchants/{merchant_id}/available-coupons
```

**说明**: 获取指定商家发布的可领取优惠券模板和促销活动信息，用于展示给用户进行领取

**路径参数**:
- `merchant_id` (int): 商家ID

**响应数据**:
```json
{
  "merchant_id": 123,
  "merchant_name": "美味餐厅",
  "has_promotion": true,
  "has_coupon": true,
  "promotions": [
    {
      "id": 1,
      "name": "首单优惠",
      "description": "新用户首单立减10元",
      "type": 1,
      "type_name": "首单优惠",
      "rules": "满30元可用",
      "start_time": "2025-01-01 00:00:00",
      "end_time": "2025-12-31 23:59:59"
    }
  ],
  "coupons": [
    {
      "id": 1,
      "name": "满30减10优惠券",
      "description": "满30元立减10元",
      "type": 1,
      "type_text": "满减券",
      "amount": 10.00,
      "min_order_amount": 30.00,
      "per_user_limit": 1,
      "start_time": "2025-01-01T00:00:00Z",
      "end_time": "2025-12-31T23:59:59Z"
    }
  ]
}
```

**数据说明**:
- 返回的是优惠券模板信息，非用户优惠券实例
- 用户需要通过优惠券领取接口来获得优惠券实例
- 只返回有效期内且状态正常的优惠券和促销活动

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "coupons": [
      {
        "id": 1,
        "coupon_id": 101,
        "name": "满50减10",
        "discount_amount": 10.00,
        "can_use": true,
        "reason": ""
      }
    ]
  }
}
```

#### 6. 获取优惠券使用历史

```http
GET /api/v1/user/takeout/coupons/history
```

**查询参数**:
- `page` (int): 页码，默认1
- `page_size` (int): 每页数量，默认10

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "history": [
      {
        "id": 1,
        "coupon_name": "满50减10",
        "order_id": 12345,
        "discount_amount": 10.00,
        "used_time": "2024-01-15T12:30:00Z",
        "merchant_name": "美味餐厅"
      }
    ],
    "total": 3,
    "page": 1,
    "page_size": 10
  }
}
```

#### 6.1 获取用户在指定商家的促销和优惠券信息

**接口说明：** 获取用户在指定商家的促销活动和用户优惠券实例信息

**重要说明：** 此接口返回的是用户自己的优惠券实例，而不是商家的优惠券模板。只有用户已经领取的优惠券才会在此接口中返回。

**请求示例：**
```http
POST /api/v1/user/takeout/merchants/promotions-coupons
Content-Type: application/json
Authorization: Bearer <token>

{
  "merchant_ids": [1, 2, 3]
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "merchant_id": 1,
      "merchant_name": "美味餐厅",
      "promotions": [
        {
          "id": 1,
          "name": "满减活动",
          "description": "满100减20",
          "type": 4,
          "type_name": "满减活动",
          "rules": "{\"min_amount\":100,\"discount_amount\":20}",
          "start_time": "2024-01-01 00:00:00",
          "end_time": "2024-12-31 23:59:59"
        }
      ],
      "coupons": [
        {
          "id": 123,           // 用户优惠券实例ID
          "user_id": 456,      // 用户ID
          "coupon_id": 1,      // 优惠券模板ID
          "coupon": {
            "id": 1,
            "name": "新用户专享",
            "description": "新用户首单立减10元",
            "type": 1,
            "amount": 10.0,
            "min_order_amount": 30.0,
            "start_time": "2024-01-01T00:00:00Z",
            "end_time": "2024-12-31T23:59:59Z",
            "status": 1,
            "status_text": "可用"
          },
          "status": 1,         // 用户优惠券状态（1=未使用，2=已使用，3=已过期）
          "status_text": "未使用",
          "created_at": "2024-01-01T00:00:00Z"  // 用户领取时间
        }
      ],
      "has_promotion": true,
      "has_coupon": true
    }
  ]
}
```

**数据说明：**
- `coupons` 数组中的每个元素代表用户已领取的优惠券实例
- `id` 字段是用户优惠券实例的唯一标识
- `coupon_id` 字段是优惠券模板的ID
- `status` 字段表示用户优惠券的使用状态
- `created_at` 字段表示用户领取优惠券的时间

#### 7. 计算订单优惠

```http
POST /api/v1/user/takeout/coupons/calculate-discount
```

**请求体**:
```json
{
  "coupon_id": 101,
  "order": {
    "merchant_id": 1,
    "total_amount": 60.00,
    "items": [
      {
        "food_id": 1,
        "quantity": 2,
        "price": 25.00,
        "category_id": 1
      }
    ]
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "original_amount": 60.00,
    "discount_amount": 10.00,
    "final_amount": 50.00,
    "coupon_id": 101,
    "promotion_ids": []
  }
}
```

## 前端集成方案

### 1. 优惠券列表页面

#### 页面结构
```vue
<template>
  <div class="coupon-list">
    <!-- 筛选器 -->
    <div class="filter-bar">
      <select v-model="statusFilter">
        <option value="">全部</option>
        <option value="unused">未使用</option>
        <option value="used">已使用</option>
        <option value="expired">已过期</option>
      </select>
    </div>
    
    <!-- 优惠券列表 -->
    <div class="coupon-items">
      <div 
        v-for="coupon in coupons" 
        :key="coupon.id"
        class="coupon-item"
        :class="{
          'expired': coupon.status === 'expired',
          'used': coupon.status === 'used'
        }"
      >
        <div class="coupon-info">
          <h3>{{ coupon.name }}</h3>
          <p class="amount">¥{{ coupon.discount_amount }}</p>
          <p class="condition">满¥{{ coupon.min_order_amount }}可用</p>
          <p class="merchant">{{ coupon.merchant_name }}</p>
          <p class="expire">有效期至：{{ formatDate(coupon.expire_time) }}</p>
        </div>
        <div class="coupon-action">
          <button 
            v-if="coupon.status === 'unused'"
            @click="useCoupon(coupon)"
            class="use-btn"
          >
            立即使用
          </button>
          <span v-else class="status-text">
            {{ getStatusText(coupon.status) }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="pagination">
      <!-- 分页组件 -->
    </div>
  </div>
</template>
```

#### 数据获取
```javascript
export default {
  data() {
    return {
      coupons: [],
      statusFilter: '',
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  
  async mounted() {
    await this.loadCoupons()
  },
  
  methods: {
    async loadCoupons() {
      try {
        const params = {
          page: this.currentPage,
          page_size: this.pageSize
        }
        
        if (this.statusFilter) {
          params.status = this.statusFilter
        }
        
        const response = await this.$api.get('/api/v1/user/takeout/coupons/list', {
          params
        })
        
        this.coupons = response.data.coupons
        this.total = response.data.total
      } catch (error) {
        this.$message.error('获取优惠券列表失败')
      }
    },
    
    useCoupon(coupon) {
      // 跳转到商家页面或订单页面
      this.$router.push({
        path: `/merchant/${coupon.merchant_id}`,
        query: { coupon_id: coupon.id }
      })
    }
  }
}
```

### 2. 订单页面优惠券选择

#### 优惠券选择组件
```vue
<template>
  <div class="coupon-selector">
    <div class="coupon-entry" @click="showCouponModal = true">
      <span v-if="!selectedCoupon">选择优惠券</span>
      <span v-else>{{ selectedCoupon.name }} -¥{{ selectedCoupon.discount_amount }}</span>
      <i class="arrow-right"></i>
    </div>
    
    <!-- 优惠券选择弹窗 -->
    <div v-if="showCouponModal" class="coupon-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>选择优惠券</h3>
          <button @click="showCouponModal = false">×</button>
        </div>
        
        <div class="available-coupons">
          <div 
            v-for="coupon in availableCoupons"
            :key="coupon.id"
            class="coupon-option"
            :class="{ 'selected': selectedCoupon?.id === coupon.id }"
            @click="selectCoupon(coupon)"
          >
            <div class="coupon-info">
              <h4>{{ coupon.name }}</h4>
              <p>-¥{{ coupon.discount_amount }}</p>
              <p class="condition">满¥{{ coupon.min_order_amount }}可用</p>
            </div>
            <div class="coupon-status">
              <span v-if="coupon.can_use" class="available">可使用</span>
              <span v-else class="unavailable">{{ coupon.reason }}</span>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="confirmSelection">确认选择</button>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 优惠券逻辑处理
```javascript
export default {
  props: {
    merchantId: Number,
    totalAmount: Number
  },
  
  data() {
    return {
      showCouponModal: false,
      availableCoupons: [],
      selectedCoupon: null,
      tempSelectedCoupon: null
    }
  },
  
  watch: {
    totalAmount: {
      handler() {
        this.loadAvailableCoupons()
        this.validateSelectedCoupon()
      },
      immediate: true
    }
  },
  
  methods: {
    async loadAvailableCoupons() {
      try {
        const response = await this.$api.get('/api/v1/user/takeout/coupons/available', {
          params: {
            merchant_id: this.merchantId,
            total_amount: this.totalAmount
          }
        })
        
        this.availableCoupons = response.data.coupons
      } catch (error) {
        console.error('获取可用优惠券失败:', error)
      }
    },
    
    async validateSelectedCoupon() {
      if (!this.selectedCoupon) return
      
      try {
        const response = await this.$api.post(
          `/api/v1/user/takeout/coupons/${this.selectedCoupon.id}/check`,
          {
            coupon_id: this.selectedCoupon.coupon_id,
            total_amount: this.totalAmount
          }
        )
        
        if (!response.data.available) {
          this.selectedCoupon = null
          this.$message.warning('所选优惠券不可用：' + response.data.reason)
          this.$emit('coupon-changed', null)
        }
      } catch (error) {
        console.error('验证优惠券失败:', error)
      }
    },
    
    selectCoupon(coupon) {
      this.tempSelectedCoupon = coupon.can_use ? coupon : null
    },
    
    confirmSelection() {
      this.selectedCoupon = this.tempSelectedCoupon
      this.showCouponModal = false
      this.$emit('coupon-changed', this.selectedCoupon)
    }
  }
}
```

### 3. 订单确认页面集成

```javascript
// 订单确认页面
export default {
  data() {
    return {
      orderData: {
        merchant_id: 1,
        items: [],
        total_amount: 0
      },
      selectedCoupon: null,
      discountInfo: null
    }
  },
  
  methods: {
    async onCouponChanged(coupon) {
      this.selectedCoupon = coupon
      
      if (coupon) {
        await this.calculateDiscount()
      } else {
        this.discountInfo = null
      }
    },
    
    async calculateDiscount() {
      if (!this.selectedCoupon) return
      
      try {
        const response = await this.$api.post(
          '/api/v1/user/takeout/coupons/calculate-discount',
          {
            coupon_id: this.selectedCoupon.coupon_id,
            order: this.orderData
          }
        )
        
        this.discountInfo = response.data
      } catch (error) {
        this.$message.error('计算优惠失败')
      }
    },
    
    async submitOrder() {
      const orderPayload = {
        ...this.orderData,
        coupon_id: this.selectedCoupon?.coupon_id
      }
      
      // 提交订单逻辑
    }
  }
}
```

## 用户体验设计建议

### 1. 视觉设计

#### 优惠券卡片设计
- **未使用**: 明亮的主题色，突出优惠金额
- **已使用**: 灰色调，添加"已使用"标识
- **已过期**: 暗灰色，添加"已过期"标识
- **即将过期**: 橙色警告色，提醒用户尽快使用

#### 状态指示
```css
.coupon-item {
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
}

.coupon-item.unused {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
}

.coupon-item.used {
  background: #f5f5f5;
  color: #999;
}

.coupon-item.expired {
  background: #e0e0e0;
  color: #666;
}

.coupon-item.expiring {
  background: linear-gradient(135deg, #ffa726, #ffcc02);
  color: white;
}
```

### 2. 交互设计

#### 优惠券选择流程
1. **入口明显**: 在订单页面显著位置放置优惠券选择入口
2. **实时验证**: 选择优惠券后立即验证可用性
3. **价格更新**: 选择优惠券后实时更新订单金额
4. **错误提示**: 优惠券不可用时给出明确的原因说明

#### 状态反馈
```javascript
// 优惠券状态提示
const statusMessages = {
  'min_amount_not_met': '订单金额未达到使用条件',
  'merchant_not_match': '该优惠券不适用于当前商家',
  'expired': '优惠券已过期',
  'used': '优惠券已使用',
  'category_not_match': '商品类别不符合使用条件'
}
```

### 3. 性能优化

#### 数据缓存
```javascript
// 缓存用户优惠券列表
const CouponCache = {
  cache: new Map(),
  
  get(key) {
    const item = this.cache.get(key)
    if (item && Date.now() - item.timestamp < 5 * 60 * 1000) { // 5分钟缓存
      return item.data
    }
    return null
  },
  
  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
}
```

#### 防抖处理
```javascript
// 订单金额变化时的优惠券验证防抖
import { debounce } from 'lodash'

export default {
  methods: {
    validateCoupon: debounce(async function() {
      // 验证逻辑
    }, 500)
  }
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 40001 | 优惠券不存在 | 提示用户优惠券无效 |
| 40002 | 优惠券已过期 | 提示用户选择其他优惠券 |
| 40003 | 优惠券已使用 | 提示用户选择其他优惠券 |
| 40004 | 订单金额不满足条件 | 提示最低使用金额 |
| 40005 | 商家不匹配 | 提示优惠券适用商家 |
| 40006 | 商品类别不匹配 | 提示适用商品类别 |

### 错误处理示例

```javascript
const handleCouponError = (error) => {
  const errorMessages = {
    40001: '优惠券不存在或已失效',
    40002: '优惠券已过期，请选择其他优惠券',
    40003: '优惠券已使用，请选择其他优惠券',
    40004: '订单金额未达到优惠券使用条件',
    40005: '该优惠券不适用于当前商家',
    40006: '购物车中的商品不符合优惠券使用条件'
  }
  
  const message = errorMessages[error.code] || '优惠券使用失败'
  this.$message.error(message)
}
```

## 测试建议

### 1. 功能测试

- 优惠券列表加载和筛选
- 优惠券详情查看
- 优惠券可用性检查
- 订单优惠计算
- 优惠券使用历史

### 2. 边界测试

- 网络异常情况
- 优惠券过期边界
- 订单金额边界
- 并发使用同一优惠券

### 3. 用户体验测试

- 页面加载速度
- 交互响应时间
- 错误提示清晰度
- 视觉效果一致性

## 总结

本指南提供了外卖模块用户端优惠券功能的完整集成方案，包括：

1. **完整的API接口文档**，涵盖所有优惠券相关操作
2. **详细的前端集成代码**，提供可直接使用的组件示例
3. **用户体验设计建议**，确保良好的用户交互体验
4. **性能优化方案**，提升应用响应速度
5. **错误处理机制**，保证系统稳定性

通过遵循本指南，可以快速、高质量地完成用户端优惠券功能的开发和集成。