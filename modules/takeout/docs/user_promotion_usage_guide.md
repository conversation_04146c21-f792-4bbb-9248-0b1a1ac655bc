# 外卖模块用户促销活动使用指南

## 概述

本文档详细介绍了外卖模块中用户如何使用商家促销活动，特别是满减优惠活动的完整使用流程。

## 促销活动类型

### 1. 满减活动（type: 4）

满减活动是商家设置的订单金额达到一定条件后自动减免的优惠活动。

#### 活动示例
```json
{
  "id": 1,
  "name": "新用户首单减免",
  "description": "新用户首单减免",
  "type": 4,
  "type_name": "满减活动",
  "rules": "{\"coupon\":{\"name\":\"首单减免\",\"type\":1,\"amount\":5,\"min_order_amount\":5.01,\"per_user_limit\":1,\"valid_days\":30}}",
  "start_time": "2025-06-19 22:25:33",
  "end_time": "2027-06-30 16:00:00"
}
```

## 用户使用流程

### 第一步：浏览商家页面

1. 用户进入商家店铺页面
2. 系统自动展示该商家的促销活动信息
3. 在商家页面顶部或显眼位置显示：
   - 促销标题："满减优惠(新用户首单减免)"
   - 活动详情："新用户首单减免"
   - 优惠条件："满5.01元减5元"
   - 使用限制："每人限用1次"
   - 有效期："2025-06-19至2027-06-30"

### 第二步：选择商品

1. 用户浏览商品列表
2. 选择心仪的商品加入购物车
3. 系统实时计算订单金额
4. 当订单金额达到满减条件时，显示可享受的优惠

### 第三步：购物车确认

1. 用户进入购物车页面
2. 系统显示：
   - 商品总金额
   - 可用的促销活动
   - 预计优惠金额
   - 最终支付金额

#### 购物车显示示例
```
商品总计：¥15.50
满减优惠：-¥5.00 (新用户首单减免)
实付金额：¥10.50
```

### 第四步：下单结算

1. 用户点击"去结算"按钮
2. 进入订单确认页面
3. 系统自动应用满减优惠
4. 显示优惠详情和最终金额

### 第五步：支付完成

1. 用户选择支付方式
2. 完成支付
3. 系统记录促销活动使用情况
4. 更新用户的促销使用次数

## 促销规则解析

### 规则字段说明

基于示例中的rules字段：
```json
{
  "coupon": {
    "name": "首单减免",
    "type": 1,
    "amount": 5,
    "min_order_amount": 5.01,
    "per_user_limit": 1,
    "valid_days": 30
  }
}
```

- **name**: 优惠券名称
- **type**: 优惠类型（1=固定金额减免）
- **amount**: 减免金额（5元）
- **min_order_amount**: 最低订单金额（5.01元）
- **per_user_limit**: 每用户使用限制（1次）
- **valid_days**: 有效天数（30天）

### 使用条件

1. **用户资格**：新用户（首次下单）
2. **订单金额**：订单总金额≥5.01元
3. **使用次数**：每个用户仅限使用1次
4. **时间限制**：活动期间内（2025-06-19至2027-06-30）
5. **有效期**：获得优惠券后30天内有效

## 前端展示建议

### 1. 商家页面展示

```html
<div class="promotion-banner">
  <div class="promotion-tag">满减优惠</div>
  <div class="promotion-title">新用户首单减免</div>
  <div class="promotion-desc">满5.01元减5元，每人限用1次</div>
  <div class="promotion-time">有效期至2027-06-30</div>
</div>
```

### 2. 购物车优惠提示

```html
<div class="cart-promotion">
  <div class="promotion-applied">
    <span class="icon">✓</span>
    <span class="text">已享受"新用户首单减免"优惠 -¥5.00</span>
  </div>
</div>
```

### 3. 订单确认页面

```html
<div class="order-summary">
  <div class="item">
    <span>商品总计</span>
    <span>¥15.50</span>
  </div>
  <div class="item promotion">
    <span>满减优惠（新用户首单减免）</span>
    <span class="discount">-¥5.00</span>
  </div>
  <div class="item total">
    <span>实付金额</span>
    <span>¥10.50</span>
  </div>
</div>
```

## API接口调用

### 1. 获取商家促销活动

**接口**: `POST /api/v1/takeout/merchant/{merchant_id}/promotions`

**请求参数**:
```json
{
  "total_amount": 100.0,  // 订单总金额
  "food_ids": "1,2,3"    // 商品ID列表，英文逗号分隔
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "promotion_info": "满减优惠(新用户首单减免)",
    "promotions": [
      {
        "id": 1,
        "name": "新用户首单减免",
        "description": "新用户首单减免",
        "type": 4,
        "type_name": "满减活动",
        "rules": "{\"coupon\":{\"name\":\"首单减免\",\"type\":1,\"amount\":5,\"min_order_amount\":5.01,\"per_user_limit\":1,\"valid_days\":30}}",
        "start_time": "2025-06-19 22:25:33",
        "end_time": "2027-06-30 16:00:00"
      }
    ]
  }
}
```

### 2. 计算订单优惠

**接口**: `POST /api/v1/takeout/order/calculate-discount`

**请求参数**:
```json
{
  "merchant_id": 100,
  "user_id": 1001,
  "items": [
    {
      "food_id": 123,
      "quantity": 2,
      "price": 7.75
    }
  ],
  "promotion_ids": [1]
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "original_amount": 15.50,
    "discount_amount": 5.00,
    "final_amount": 10.50,
    "applied_promotions": [
      {
        "promotion_id": 1,
        "promotion_name": "新用户首单减免",
        "discount_amount": 5.00
      }
    ]
  }
}
```

### 3. 创建订单（应用促销）

**接口**: `POST /api/v1/takeout/order/create`

**请求参数**:
```json
{
  "cart_ids": [1, 2, 3],
  "address_id": 100,
  "payment_method": "wechat",
  "promotion_ids": [1],
  "remark": "请尽快送达，谢谢"
}
```

## 用户体验优化建议

### 1. 智能提示

- 当用户订单金额接近满减条件时，提示"再加X元即可享受满减优惠"
- 在商品列表页面标注参与促销的商品
- 实时显示当前可享受的优惠金额

### 2. 视觉设计

- 使用醒目的颜色标识促销活动
- 在购物车和结算页面突出显示优惠信息
- 使用动画效果增强用户感知

### 3. 用户引导

- 新用户首次进入时，主动展示首单优惠
- 在商品详情页面提醒用户可享受的优惠
- 提供促销活动的详细说明页面

## 常见问题处理

### 1. 用户不符合条件

**问题**: 非新用户尝试使用新用户优惠
**处理**: 显示"该优惠仅限新用户使用"提示

### 2. 订单金额不足

**问题**: 订单金额低于最低消费要求
**处理**: 提示"订单满5.01元即可享受5元减免"

### 3. 超出使用次数

**问题**: 用户已使用过该优惠
**处理**: 显示"该优惠您已使用过，每人限用1次"

### 4. 活动已过期

**问题**: 促销活动已结束
**处理**: 隐藏过期活动，显示其他可用优惠

## 数据统计

### 促销效果追踪

1. **使用率统计**：记录促销活动的使用次数和用户数
2. **转化率分析**：分析促销对订单转化的影响
3. **用户行为**：追踪用户在促销活动中的行为路径
4. **收益分析**：计算促销活动的ROI

### 关键指标

- 促销活动参与用户数
- 促销订单占比
- 平均订单金额变化
- 用户复购率
- 促销成本与收益比

## 技术实现要点

### 1. 实时计算

- 购物车金额变化时实时计算可用优惠
- 使用缓存提高计算效率
- 确保优惠计算的准确性

### 2. 并发控制

- 处理高并发场景下的促销使用
- 防止超量使用（如限量促销）
- 确保数据一致性

### 3. 异常处理

- 处理促销规则变更的情况
- 处理支付失败后的优惠回滚
- 记录异常情况的日志

## 总结

商家促销活动是提升用户体验和增加订单转化的重要工具。通过合理的展示、清晰的使用流程和良好的用户体验设计，可以最大化促销活动的效果。同时，需要注意技术实现的稳定性和数据统计的准确性，为商家提供有价值的促销效果分析。