# 外卖模块业务流程

## 目录
- [1. 商品上架流程](#1-商品上架流程)
- [2. 下单流程](#2-下单流程)
- [3. 配送流程](#3-配送流程)
- [4. 评价流程](#4-评价流程)
- [5. 商品审核流程](#5-商品审核流程)
- [6. 促销管理流程](#6-促销管理流程)

## 1. 商品上架流程

### 流程图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  商家创建商品   +----->|  管理员审核商品  +----->|   商品上架销售  |
|                |      |                |      |                |
+----------------+      +-------+--------+      +----------------+
                                |
                                |
                                v
                        +----------------+
                        |                |
                        |   审核不通过    |
                        |                |
                        +----------------+
```

### 详细步骤

1. **商家创建商品**
   - 商家登录商家后台
   - 填写商品基本信息：名称、描述、图片、价格等
   - 根据需要添加规格变体
   - 如商品为套餐，配置套餐组合项和选项
   - 提交商品，系统自动将商品状态设置为「待审核」

2. **管理员审核商品**
   - 管理员登录管理后台
   - 查看待审核的商品列表
   - 查看商品详情进行审核
   - 通过审核：填写审核意见，将商品状态设置为「审核通过」
   - 拒绝审核：填写拒绝原因，将商品状态设置为「审核拒绝」

3. **商品上架**
   - 审核通过后，商家可以将商品状态从「审核通过」设置为「上架销售中」
   - 商品在客户端前台展示，用户可以购买

## 2. 下单流程

### 流程图

```
+----------------+      +----------------+      +----------------+      +----------------+
|                |      |                |      |                |      |                |
|  浏览商品列表   +----->|  添加到购物车   +----->|  购物车结算    +----->|  创建订单      |
|                |      |                |      |                |      |                |
+----------------+      +----------------+      +----------------+      +-------+--------+
                                                                                |
                                                                                |
                                                                                v
+----------------+      +----------------+      +----------------+      +----------------+
|                |      |                |      |                |      |                |
|  商家接单处理   <------+  系统通知商家   <------+  支付成功      <------+  选择支付方式   |
|                |      |                |      |                |      |                |
+----------------+      +----------------+      +----------------+      +----------------+
```

### 详细步骤

1. **用户浏览商品**
   - 用户浏览商品列表和分类
   - 查看商品详情、规格和价格

2. **添加到购物车**
   - 选择商品规格（如有）
   - 选择套餐选项（如是套餐商品）
   - 设置购买数量
   - 添加到购物车

3. **购物车结算**
   - 查看购物车商品列表
   - 选择要结算的商品
   - 进入结算页面

4. **创建订单**
   - 选择配送地址
   - 选择期望送达时间
   - 填写备注信息
   - 确认订单金额
   - 创建订单

5. **支付订单**
   - 选择支付方式（微信、支付宝等）
   - 完成支付流程
   - 系统确认支付成功

6. **商家接单**
   - 系统通知商家有新订单
   - 商家确认接单
   - 开始准备餐食

## 3. 配送流程

### 流程图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  商家准备餐食   +----->|  分配配送员     +----->|  开始配送      |
|                |      |                |      |                |
+----------------+      +----------------+      +-------+--------+
                                                        |
                                                        |
                                                        v
                                                +----------------+      +----------------+
                                                |                |      |                |
                                                |  送达客户      +----->|  完成配送      |
                                                |                |      |                |
                                                +----------------+      +----------------+
```

### 详细步骤

1. **商家准备餐食**
   - 商家根据订单信息准备餐食
   - 完成打包和餐具准备

2. **分配配送员**
   - 商家或平台分配配送员
   - 配送员信息更新到订单中

3. **开始配送**
   - 配送员取餐
   - 系统更新订单状态为「配送中」
   - 用户可实时查看配送进度

4. **送达客户**
   - 配送员将餐食送达客户指定地址
   - 确认交付

5. **完成配送**
   - 系统更新订单状态为「已送达」
   - 配送员完成本次配送任务

## 4. 评价流程

### 流程图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  订单完成      +----->|  用户评价      +----->|  商家回复      |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
```

### 详细步骤

1. **订单完成**
   - 系统将订单状态标记为「已完成」
   - 用户收到评价提醒

2. **用户评价**
   - 用户对订单进行评分（1-5星）
   - 对配送服务进行评分
   - 填写评价内容和建议
   - 提交评价

3. **商家回复**
   - 商家查看用户评价
   - 可以对评价进行回复
   - 针对问题进行改进

4. **评价展示**
   - 评价在商品详情页展示
   - 影响商品的平均评分

## 5. 商品审核流程

### 流程图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  商家提交商品   +----->|  商品待审核    +----->|  管理员审核    |
|                |      |                |      |                |
+----------------+      +----------------+      +-------+--------+
                                                        |
                                 +---------------------+ +---------------------+
                                 |                     |                       |
                                 v                     v                       v
                        +----------------+    +----------------+     +----------------+
                        |                |    |                |     |                |
                        |  审核拒绝      |    |  审核通过      |     |  退回修改      |
                        |                |    |                |     |                |
                        +-------+--------+    +-------+--------+     +-------+--------+
                                |                     |                       |
                                v                     v                       v
                        +----------------+    +----------------+     +----------------+
                        |                |    |                |     |                |
                        |  商家修改重提  |    |  商家上架销售  |     |  商家修改内容  |
                        |                |    |                |     |                |
                        +----------------+    +----------------+     +----------------+
```

### 详细步骤

1. **商家提交商品**
   - 商家创建或更新商品信息
   - 填写完整的商品信息和图片
   - 点击提交按钮

2. **商品待审核**
   - 系统自动将商品状态设置为「待审核」
   - 商品在管理员审核队列中等待处理

3. **管理员审核**
   - 管理员查看待审核商品信息
   - 检查商品名称、描述、图片等是否合规
   - 验证价格和规格设置是否合理

4. **审核结果处理**
   - **审核通过**：
     - 管理员填写审核通过意见
     - 系统将商品状态改为「审核通过」
     - 商家收到通知，可以将商品上架销售
   
   - **审核拒绝**：
     - 管理员填写拒绝理由
     - 系统将商品状态改为「审核拒绝」
     - 商家收到通知，需要重新提交审核
   
   - **退回修改**：
     - 管理员提出修改建议
     - 商家按照建议修改后重新提交

5. **后续操作**
   - 商品审核通过后，商家可将其状态改为「上架销售中」
   - 审核拒绝的商品，商家修改后可再次提交审核
   - 系统记录完整的审核日志和历史

## 6. 促销管理流程

### 流程图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  商家创建活动   +------>|  活动生效      +------>|  客户参与      |
|                |      |                |      |                |
+----------------+      +-------+--------+      +----------------+
                                |
                                |
                                v
                        +----------------+      +----------------+
                        |                |      |                |
                        |  关联商品        +------>|  计算订单优惠   |
                        |                |      |                |
                        +----------------+      +----------------+
```

### 详细步骤

1. **商家创建促销活动**
   - 商家登录商家后台
   - 选择创建促销活动
   - 选择活动类型：首单优惠、折扣促销、优惠券活动等
   - 设置活动名称、描述、开始和结束时间
   - 配置活动规则和使用限制

2. **活动生效**
   - 系统检查活动时间
   - 到达开始时间后，活动自动生效
   - 系统将活动状态更新为「活动中」

3. **关联商品**
   - 对于商品折扣类型的活动，商家需要关联具体商品
   - 选择参与促销的商品
   - 设置折扣类型（比例折扣或直降金额）和折扣值

4. **客户参与**
   - 用户在客户端查看促销活动
   - 对于优惠券活动，用户可领取优惠券
   - 用户购买参与折扣的商品

5. **计算订单优惠**
   - 用户提交订单时，系统自动计算适用的促销优惠
   - 应用商品折扣
   - 验证并应用用户选择的优惠券
   - 生成最终的订单金额

6. **活动结束**
   - 到达结束时间或达到最大使用次数后，活动自动结束
   - 系统将活动状态更新为「已结束」
   - 商家可以查看活动统计数据

### 7. 优惠券业务流程

### 流程图

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
|  商家创建优惠券 +------>|  用户领取优惠券  +------>|  用户使用优惠券  |
|                |      |                |      |                |
+----------------+      +-------+--------+      +-------+--------+
                                |                       |
                                |                       |
                                v                       v
                        +----------------+      +----------------+
                        |                |      |                |
                        |  优惠券过期      |      |  优惠券已使用    |
                        |                |      |                |
                        +----------------+      +----------------+
```

### 详细步骤

1. **商家创建优惠券**
   - 商家登录后台创建优惠券
   - 设置优惠券类型（满减券或折扣券）
   - 配置优惠券金额、最低订单金额等参数
   - 设置有效期、发放限制和适用范围
   - 发布优惠券

2. **用户领取优惠券**
   - 用户在客户端浏览可领取的优惠券
   - 点击领取按钮
   - 系统检查用户是否满足领取条件
   - 系统创建用户优惠券记录，状态为「未使用」

3. **用户使用优惠券**
   - 用户下单时选择使用优惠券
   - 系统验证优惠券是否满足当前订单的使用条件
   - 查看优惠金额和结算金额
   - 提交订单时，系统将优惠券状态更新为「已使用」
   - 记录使用时间和关联的订单

4. **优惠券过期**
   - 系统定期检查优惠券状态
   - 对于超过有效期的未使用优惠券，自动更新状态为「已过期」

5. **数据统计与分析**
   - 商家可查看优惠券的领取率、使用率等数据
   - 分析优惠券的效果和转化情况
   - 基于分析结果调整促销策略
