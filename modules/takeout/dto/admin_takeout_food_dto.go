/**
 * 管理员外卖食品DTO
 *
 * 本文件定义了管理员操作外卖食品相关的数据传输对象，用于后台管理API接口的请求和响应。
 * 包含了列表查询、详情查询和创建/更新的请求和响应结构。
 */

package dto

import (
	"time"
)

// 食品状态常量
const (
	TakeoutFoodStatusDisabled = 0 // 禁用
	TakeoutFoodStatusEnabled  = 1 // 启用
)

// CreateFoodRequest 创建食品请求
type CreateFoodRequest struct {
	MerchantID      int64   `json:"merchant_id" valid:"Required"`       // 商家ID
	CategoryID      int64   `json:"category_id" valid:"Required"`       // 分类ID
	Name            string  `json:"name" valid:"Required;MaxSize(100)"` // 商品名称
	Description     string  `json:"description"`                        // 商品描述
	Brief           string  `json:"brief" valid:"MaxSize(200)"`         // 商品简介
	Image           string  `json:"image" valid:"Required"`             // 商品图片
	Price           float64 `json:"price" valid:"Required"`             // 基础价格
	OriginalPrice   float64 `json:"original_price"`                     // 原价
	PackagingFee    float64 `json:"packaging_fee"`                      // 包装费
	PreparationTime int     `json:"preparation_time"`                   // 备餐时间(分钟)
	IsCombination   bool    `json:"is_combination"`                     // 是否为套餐组合
	IsSpicy         bool    `json:"is_spicy"`                           // 是否辣味
	HasVariants     bool    `json:"has_variants"`                       // 是否有规格变体
	DailyLimit      int     `json:"daily_limit"`                        // 每日限量
	Tags            []string  `json:"tags"`                               // 标签数组
	Keywords        string  `json:"keywords"`                           // 关键词，逗号分隔
	Status          int     `json:"status"`                             // 状态：0-禁用，1-启用
	IsRecommend     bool    `json:"is_recommend"`                       // 是否推荐
	SortOrder       int     `json:"sort_order"`                         // 排序值
}

// UpdateFoodRequest 更新食品请求
type UpdateFoodRequest struct {
	ID              int64   `json:"id" valid:"Required"`               // 食品ID
	MerchantID      int64   `json:"merchant_id"`                       // 商家ID
	CategoryID      int64   `json:"category_id"`                       // 分类ID
	Name            string  `json:"name" valid:"MaxSize(100)"`         // 商品名称
	Description     string  `json:"description"`                        // 商品描述
	Brief           string  `json:"brief" valid:"MaxSize(200)"`         // 商品简介
	Image           string  `json:"image"`                             // 商品图片
	Price           float64 `json:"price"`                             // 基础价格
	OriginalPrice   float64 `json:"original_price"`                     // 原价
	PackagingFee    float64 `json:"packaging_fee"`                      // 包装费
	PreparationTime int     `json:"preparation_time"`                   // 备餐时间(分钟)
	IsCombination   bool    `json:"is_combination"`                     // 是否为套餐组合
	IsSpicy         bool    `json:"is_spicy"`                           // 是否辣味
	HasVariants     bool    `json:"has_variants"`                       // 是否有规格变体
	SoldOut         bool    `json:"sold_out"`                           // 是否售罄
	DailyLimit      int     `json:"daily_limit"`                        // 每日限量
	Tags            []string  `json:"tags"`                               // 标签数组
	Keywords        string  `json:"keywords"`                           // 关键词，逗号分隔
	Status          int     `json:"status"`                             // 状态：0-禁用，1-启用
	IsRecommend     bool    `json:"is_recommend"`                       // 是否推荐
	SortOrder       int     `json:"sort_order"`                         // 排序值
}

// UpdateFoodStatusRequest 更新食品状态请求
type UpdateFoodStatusRequest struct {
	ID     int64 `json:"id" valid:"Required"`    // 食品ID
	Status int   `json:"status" valid:"Required"` // 状态：0-禁用，1-启用
}

// AuditFoodRequest 审核食品请求
type AuditFoodRequest struct {
	ID         int64  `json:"id" valid:"Required"`          // 食品ID
	AuditorID  int64  `json:"auditor_id"`                  // 审核人ID
	AuditStatus int    `json:"audit_status" valid:"Required"` // 审核状态：1-通过，2-拒绝
	AuditReason string `json:"audit_reason"`                 // 审核意见或拒绝原因
}

// AdminFoodListItemDTO 管理员食品列表项DTO
type AdminFoodListItemDTO struct {
	ID               int64     `json:"id"`                  // 商品ID
	MerchantID       int64     `json:"merchant_id"`         // 商家ID
	MerchantName     string    `json:"merchant_name"`       // 商家名称
	Name             string    `json:"name"`                // 商品名称
	Brief            string    `json:"brief"`               // 商品简介
	Image            string    `json:"image"`               // 商品图片
	Price            float64   `json:"price"`               // 基础价格
	OriginalPrice    float64   `json:"original_price"`      // 原价
	PackagingFee     float64   `json:"packaging_fee"`       // 包装费
	PreparationTime  int       `json:"preparation_time"`    // 备餐时间(分钟)
	IsSpicy          bool      `json:"is_spicy"`            // 是否辣味
	IsCombination    bool      `json:"is_combination"`      // 是否为套餐组合
	HasVariants      bool      `json:"has_variants"`        // 是否有规格变体
	SoldOut          bool      `json:"sold_out"`            // 是否售罄
	MinPrice         float64   `json:"min_price"`           // 最低价格
	MaxPrice         float64   `json:"max_price"`           // 最高价格
	TotalSold        int       `json:"total_sold"`          // 累计销售数量
	Tags             []string  `json:"tags"`                // 标签列表
	CategoryID       int64     `json:"category_id"`         // 分类ID
	CategoryName     string    `json:"category_name"`       // 分类名称
	Status           int       `json:"status"`              // 商品状态
	AuditStatus      int       `json:"audit_status"`        // 审核状态
	IsRecommend      bool      `json:"is_recommend"`        // 是否推荐
	SortOrder        int       `json:"sort_order"`          // 排序值
	CreatedAt        time.Time `json:"created_at"`          // 创建时间
}

// AdminFoodDetailDTO 管理员食品详情DTO
type AdminFoodDetailDTO struct {
	ID               int64                      `json:"id"`                  // 商品ID
	MerchantID       int64                      `json:"merchant_id"`         // 商家ID
	MerchantName     string                     `json:"merchant_name"`       // 商家名称
	Name             string                     `json:"name"`                // 商品名称
	Description      string                     `json:"description"`         // 商品描述
	Brief            string                     `json:"brief"`               // 商品简介
	Image            string                     `json:"image"`               // 商品图片
	Price            float64                    `json:"price"`               // 基础价格
	OriginalPrice    float64                    `json:"original_price"`      // 原价
	PackagingFee     float64                    `json:"packaging_fee"`       // 包装费
	PreparationTime  int                        `json:"preparation_time"`    // 备餐时间(分钟)
	IsCombination    bool                       `json:"is_combination"`      // 是否为套餐组合
	IsSpicy          bool                       `json:"is_spicy"`            // 是否辣味
	HasVariants      bool                       `json:"has_variants"`        // 是否有规格变体
	SoldOut          bool                       `json:"sold_out"`            // 是否售罄
	DailyLimit       int                        `json:"daily_limit"`         // 每日限量
	TotalSold        int                        `json:"total_sold"`          // 累计销售数量
	Tags             []string                   `json:"tags"`                // 标签列表
	Keywords         []string                   `json:"keywords"`            // 关键词列表
	CategoryID       int64                      `json:"category_id"`         // 分类ID
	CategoryName     string                     `json:"category_name"`       // 分类名称
	Status           int                        `json:"status"`              // 商品状态
	AuditStatus      int                        `json:"audit_status"`        // 审核状态
	AuditorID        int64                      `json:"auditor_id"`          // 审核人ID
	AuditReason      string                     `json:"audit_reason"`        // 审核意见
	AuditTime        time.Time                  `json:"audit_time"`          // 审核时间
	IsRecommend      bool                       `json:"is_recommend"`        // 是否推荐
	SortOrder        int                        `json:"sort_order"`          // 排序值
	MinPrice         float64                    `json:"min_price"`           // 最低价格
	MaxPrice         float64                    `json:"max_price"`           // 最高价格
	Variants         []TakeoutFoodVariantDTO    `json:"variants"`            // 规格变体列表
	ComboItems       []TakeoutComboItemDTO      `json:"combo_items"`         // 套餐组件列表
	CreatedAt        time.Time                  `json:"created_at"`          // 创建时间
	UpdatedAt        time.Time                  `json:"updated_at"`          // 更新时间
}
