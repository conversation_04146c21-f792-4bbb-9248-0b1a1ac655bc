/**
 * 管理员外卖订单DTO
 *
 * 本文件定义了管理员操作外卖订单相关的数据传输对象，用于后台管理API接口的请求和响应。
 * 包含了订单列表查询、详情查询和操作的请求和响应结构。
 */

package dto

import (
	"time"
)

// UpdateOrderRequest 更新订单请求
type UpdateOrderRequest struct {
	ID             int64   `json:"id" valid:"Required"`                    // 订单ID
	Status         int     `json:"status"`                                 // 订单状态
	DeliveryStatus int     `json:"delivery_status"`                        // 配送状态
	DeliveryStaffID int64  `json:"delivery_staff_id"`                      // 配送员ID
	DeliveryFee    float64 `json:"delivery_fee"`                           // 配送费
	Remark         string  `json:"remark" valid:"MaxSize(200)"`            // 订单备注
	RefundAmount   float64 `json:"refund_amount"`                          // 退款金额
	RefundReason   string  `json:"refund_reason" valid:"MaxSize(200)"`     // 退款原因
}

// AdminOrderListItemDTO 管理员订单列表项DTO
type AdminOrderListItemDTO struct {
	ID              int64     `json:"id"`                   // 订单ID
	OrderNumber     string    `json:"order_number"`         // 订单编号
	MerchantID      int64     `json:"merchant_id"`          // 商家ID
	MerchantName    string    `json:"merchant_name"`        // 商家名称
	UserID          int64     `json:"user_id"`              // 用户ID
	UserName        string    `json:"user_name"`            // 用户名称
	Status          int       `json:"status"`               // 订单状态
	StatusText      string    `json:"status_text"`          // 订单状态文本
	DeliveryStatus  int       `json:"delivery_status"`      // 配送状态
	DeliveryStatusText string  `json:"delivery_status_text"`// 配送状态文本
	DeliveryStaffID int64     `json:"delivery_staff_id"`    // 配送员ID
	DeliveryStaffName string  `json:"delivery_staff_name"`  // 配送员姓名
	TotalAmount     float64   `json:"total_amount"`         // 订单总金额
	PayAmount       float64   `json:"pay_amount"`           // 实付金额
	ItemCount       int       `json:"item_count"`           // 商品数量
	PaymentMethod   int       `json:"payment_method"`       // 支付方式
	PaymentStatus   int       `json:"payment_status"`       // 支付状态
	PaymentStatusText string  `json:"payment_status_text"`  // 支付状态文本
	CreatedAt       time.Time `json:"created_at"`           // 创建时间
}

// AdminOrderDetailDTO 管理员订单详情DTO
type AdminOrderDetailDTO struct {
	ID              int64                 `json:"id"`                   // 订单ID
	OrderNumber     string                `json:"order_number"`         // 订单编号
	Type            int                   `json:"type"`                 // 订单类型
	MerchantID      int64                 `json:"merchant_id"`          // 商家ID
	MerchantName    string                `json:"merchant_name"`        // 商家名称
	UserID          int64                 `json:"user_id"`              // 用户ID
	UserName        string                `json:"user_name"`            // 用户名称
	UserPhone       string                `json:"user_phone"`           // 用户电话
	Status          int                   `json:"status"`               // 订单状态
	StatusText      string                `json:"status_text"`          // 订单状态文本
	DeliveryStatus  int                   `json:"delivery_status"`      // 配送状态
	DeliveryStatusText string             `json:"delivery_status_text"` // 配送状态文本
	DeliveryType    int                   `json:"delivery_type"`        // 配送类型
	DeliveryTypeText string               `json:"delivery_type_text"`   // 配送类型文本
	DeliveryFee     float64               `json:"delivery_fee"`         // 配送费
	DeliveryAddress string                `json:"delivery_address"`     // 配送地址
	DeliveryTime    time.Time             `json:"delivery_time"`        // 配送时间
	DeliveryStaffID int64                 `json:"delivery_staff_id"`    // 配送员ID
	DeliveryStaffName string              `json:"delivery_staff_name"`  // 配送员姓名
	DeliveryStaffPhone string             `json:"delivery_staff_phone"` // 配送员电话
	ItemAmount      float64               `json:"item_amount"`          // 商品金额
	PackagingFee    float64               `json:"packaging_fee"`        // 包装费
	DiscountAmount  float64               `json:"discount_amount"`      // 优惠金额
	TotalAmount     float64               `json:"total_amount"`         // 订单总金额
	PayMethod       int                   `json:"pay_method"`           // 支付方式
	PayMethodText   string                `json:"pay_method_text"`      // 支付方式文本
	PayTime         time.Time             `json:"pay_time"`             // 支付时间
	PayStatus       int                   `json:"pay_status"`           // 支付状态
	PayStatusText   string                `json:"pay_status_text"`      // 支付状态文本
	AcceptedTime    *time.Time            `json:"accepted_time"`        // 商家接单时间
	RefundAmount    float64               `json:"refund_amount"`        // 退款金额
	RefundTime      time.Time             `json:"refund_time"`          // 退款时间
	RefundReason    string                `json:"refund_reason"`        // 退款原因
	CancelReason    string                `json:"cancel_reason"`        // 取消原因
	Remark          string                `json:"remark"`               // 订单备注
	Items           []OrderItemDTO        `json:"items"`                // 订单项列表
	IsPreOrder      bool                  `json:"is_pre_order"`         // 是否预约订单
	PreOrderTime    time.Time             `json:"pre_order_time"`       // 预约时间
	CreatedAt       time.Time             `json:"created_at"`           // 创建时间
	UpdatedAt       time.Time             `json:"updated_at"`           // 更新时间
}

// OrderItemDTO 订单项DTO
type OrderItemDTO struct {
	ID           int64   `json:"id"`            // 订单项ID
	OrderID      int64   `json:"order_id"`      // 订单ID
	FoodID       int64   `json:"food_id"`       // 食品ID
	FoodName     string  `json:"food_name"`     // 食品名称
	FoodImage    string  `json:"food_image"`    // 食品图片
	VariantID    int64   `json:"variant_id"`    // 规格ID
	VariantName  string  `json:"variant_name"`  // 规格名称
	Price        float64 `json:"price"`         // 价格
	Quantity     int     `json:"quantity"`      // 数量
	Amount       float64 `json:"amount"`        // 金额
	PackagingFee float64 `json:"packaging_fee"` // 包装费
	CombosInfo   string  `json:"combos_info"`   // 套餐信息
}

// AdminOrderStatisticsDTO 管理员订单统计DTO
type AdminOrderStatisticsDTO struct {
	TotalOrders       int                 `json:"total_orders"`        // 总订单数
	TotalAmount       float64             `json:"total_amount"`        // 总金额
	TodayOrders       int                 `json:"today_orders"`        // 今日订单数
	TodayAmount       float64             `json:"today_amount"`        // 今日金额
	PendingOrders     int                 `json:"pending_orders"`      // 待处理订单数
	ProcessingOrders  int                 `json:"processing_orders"`   // 处理中订单数
	CompletedOrders   int                 `json:"completed_orders"`    // 已完成订单数
	CancelledOrders   int                 `json:"cancelled_orders"`    // 已取消订单数
	RefundAmount      float64             `json:"refund_amount"`       // 退款金额
	OrderTrend        map[string]int      `json:"order_trend"`         // 订单走势
	AmountTrend       map[string]float64  `json:"amount_trend"`        // 金额走势
}
