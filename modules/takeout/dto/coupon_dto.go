/**
 * 外卖模块 - 优惠券DTO
 * 描述：定义优惠券相关的数据传输对象
 * 作者：系统
 * 创建时间：2025-05-14
 */

package dto

import (
	"time"

	"o_mall_backend/modules/takeout/models"
)

// CreateCouponRequest 创建优惠券请求
type CreateCouponRequest struct {
	PromotionID       int64   `json:"promotion_id" valid:"optional"`        // 关联活动ID
	MerchantID        int64   `json:"merchant_id" valid:"required"`         // 商户ID
	Name              string  `json:"name" valid:"required"`                // 优惠券名称
	Description       string  `json:"description" valid:"required"`         // 描述
	Type              int     `json:"type" valid:"required"`                // 优惠券类型
	Amount            float64 `json:"amount" valid:"required"`              // 金额/折扣值
	MinOrderAmount    float64 `json:"min_order_amount" valid:"required"`    // 最低订单金额要求
	MaxDiscountAmount float64 `json:"max_discount_amount" valid:"optional"` // 最高优惠金额
	ApplyToAll        bool    `json:"apply_to_all" valid:"required"`        // 是否适用于所有商品
	ApplyToCategories string  `json:"apply_to_categories" valid:"optional"` // 适用分类ID
	ApplyToFoods      string  `json:"apply_to_foods" valid:"optional"`      // 适用商品ID
	ExcludeFoods      string  `json:"exclude_foods" valid:"optional"`       // 排除商品ID
	UserLevelLimit    int     `json:"user_level_limit" valid:"optional"`    // 用户等级限制
	PerUserLimit      int     `json:"per_user_limit" valid:"required"`      // 每人可领取次数
	DailyLimit        int     `json:"daily_limit" valid:"optional"`         // 每日发放限制
	TotalLimit        int     `json:"total_limit" valid:"optional"`         // 总发放数量限制
	StartTime         string  `json:"start_time" valid:"required"`          // 开始时间
	EndTime           string  `json:"end_time" valid:"required"`            // 结束时间
}

// UpdateCouponRequest 更新优惠券请求
type UpdateCouponRequest struct {
	ID                int64   `json:"id" valid:"required"`                  // 优惠券ID
	MerchantID        int64   `json:"merchant_id" valid:"required"`         // 商户ID
	Name              string  `json:"name" valid:"optional"`                // 优惠券名称
	Description       string  `json:"description" valid:"optional"`         // 描述
	Amount            float64 `json:"amount" valid:"optional"`              // 金额/折扣值
	MinOrderAmount    float64 `json:"min_order_amount" valid:"optional"`    // 最低订单金额要求
	MaxDiscountAmount float64 `json:"max_discount_amount" valid:"optional"` // 最高优惠金额
	ApplyToAll        bool    `json:"apply_to_all" valid:"optional"`        // 是否适用于所有商品
	ApplyToCategories string  `json:"apply_to_categories" valid:"optional"` // 适用分类ID
	ApplyToFoods      string  `json:"apply_to_foods" valid:"optional"`      // 适用商品ID
	ExcludeFoods      string  `json:"exclude_foods" valid:"optional"`       // 排除商品ID
	UserLevelLimit    int     `json:"user_level_limit" valid:"optional"`    // 用户等级限制
	PerUserLimit      int     `json:"per_user_limit" valid:"optional"`      // 每人可领取次数
	DailyLimit        int     `json:"daily_limit" valid:"optional"`         // 每日发放限制
	TotalLimit        int     `json:"total_limit" valid:"optional"`         // 总发放数量限制
	StartTime         string  `json:"start_time" valid:"optional"`          // 开始时间
	EndTime           string  `json:"end_time" valid:"optional"`            // 结束时间
}

// IssueCouponRequest 发放优惠券请求
type IssueCouponRequest struct {
	CouponID int64   `json:"coupon_id" valid:"required"` // 优惠券ID
	UserIDs  []int64 `json:"user_ids" valid:"required"`  // 用户ID列表
}

// CouponResponse 优惠券响应
type CouponResponse struct {
	ID                int64     `json:"id"`                  // 优惠券ID
	PromotionID       int64     `json:"promotion_id"`        // 关联活动ID
	MerchantID        int64     `json:"merchant_id"`         // 商户ID
	MerchantName      string    `json:"merchant_name"`       // 商户名称
	MerchantLogo      string    `json:"merchant_logo"`       // 商户Logo
	Name              string    `json:"name"`                // 优惠券名称
	Description       string    `json:"description"`         // 描述
	Type              int       `json:"type"`                // 优惠券类型
	TypeText          string    `json:"type_text"`           // 类型文本
	Amount            float64   `json:"amount"`              // 金额/折扣值
	MinOrderAmount    float64   `json:"min_order_amount"`    // 最低订单金额要求
	MaxDiscountAmount float64   `json:"max_discount_amount"` // 最高优惠金额
	ApplyToAll        bool      `json:"apply_to_all"`        // 是否适用于所有商品
	ApplyToCategories string    `json:"apply_to_categories"` // 适用分类ID
	ApplyToFoods      string    `json:"apply_to_foods"`      // 适用商品ID
	ExcludeFoods      string    `json:"exclude_foods"`       // 排除商品ID
	UserLevelLimit    int       `json:"user_level_limit"`    // 用户等级限制
	PerUserLimit      int       `json:"per_user_limit"`      // 每人可领取次数
	DailyLimit        int       `json:"daily_limit"`         // 每日发放限制
	TotalLimit        int       `json:"total_limit"`         // 总发放数量限制
	IssuedCount       int       `json:"issued_count"`        // 已发放数量
	StartTime         time.Time `json:"start_time"`          // 开始时间
	EndTime           time.Time `json:"end_time"`            // 结束时间
	Status            int       `json:"status"`              // 状态
	StatusText        string    `json:"status_text"`         // 状态文本
	CreatedAt         time.Time `json:"created_at"`          // 创建时间
}

// CouponListResponse 优惠券列表响应
type CouponListResponse struct {
	Total int              `json:"total"` // 总记录数
	List  []CouponResponse `json:"list"`  // 数据列表
}

// UserCouponResponse 用户优惠券响应
type UserCouponResponse struct {
	ID         int64           `json:"id"`          // ID
	UserID     int64           `json:"user_id"`     // 用户ID
	CouponID   int64           `json:"coupon_id"`   // 优惠券ID
	Coupon     *CouponResponse `json:"coupon"`      // 优惠券详情
	Status     int             `json:"status"`      // 状态
	StatusText string          `json:"status_text"` // 状态文本
	UsedTime   time.Time       `json:"used_time"`   // 使用时间
	OrderID    int64           `json:"order_id"`    // 关联订单ID
	CreatedAt  time.Time       `json:"created_at"`  // 创建时间
}

// UserCouponListResponse 用户优惠券列表响应
type UserCouponListResponse struct {
	Total int                  `json:"total"` // 总记录数
	List  []UserCouponResponse `json:"list"`  // 数据列表
}

// OrderValidationInfo 订单验证信息
type OrderValidationInfo struct {
	TotalAmount float64               `json:"total_amount"` // 订单总金额
	Items       []OrderItemValidation `json:"items"`        // 订单商品
}

// OrderItemValidation 订单商品验证
type OrderItemValidation struct {
	FoodID     int64   `json:"food_id"`     // 商品ID
	CategoryID int64   `json:"category_id"` // 分类ID
	Price      float64 `json:"price"`       // 价格
	Quantity   int     `json:"quantity"`    // 数量
}

// CouponValidationResult 优惠券验证结果
type CouponValidationResult struct {
	CouponID       int64   `json:"coupon_id"`       // 优惠券ID
	Valid          bool    `json:"valid"`           // 是否有效
	DiscountAmount float64 `json:"discount_amount"` // 优惠金额
	Message        string  `json:"message"`         // 提示信息
}

// OrderDiscountInfo 订单优惠信息
type OrderDiscountInfo struct {
	OriginalAmount float64 `json:"original_amount"` // 原始金额
	DiscountAmount float64 `json:"discount_amount"` // 优惠金额
	FinalAmount    float64 `json:"final_amount"`    // 最终金额
	CouponID       int64   `json:"coupon_id"`       // 使用的优惠券ID
	PromotionIDs   []int64 `json:"promotion_ids"`   // 使用的促销活动ID
}

// CouponValidationRequest 优惠券验证请求
type CouponValidationRequest struct {
	CouponID    int64   `json:"coupon_id"`    // 优惠券ID
	TotalAmount float64 `json:"total_amount"` // 订单总金额
}

// OrderCouponValidationRequest 订单优惠券验证请求
type OrderCouponValidationRequest struct {
	CouponID int64                `json:"coupon_id"` // 优惠券ID
	Order    *OrderValidationInfo `json:"order"`     // 订单信息
}

// OrderDiscountCalculationRequest 订单优惠计算请求
type OrderDiscountCalculationRequest struct {
	CouponID int64                `json:"coupon_id"` // 优惠券ID（可选）
	Order    *OrderValidationInfo `json:"order"`     // 订单信息
}

// ClaimCouponRequest 领取优惠券请求
type ClaimCouponRequest struct {
	CouponID int64 `json:"coupon_id" valid:"required"` // 优惠券ID
}

// CouponCenterItemDTO 优惠券中心项目DTO
type CouponCenterItemDTO struct {
	CouponResponse
	CanClaim        bool   `json:"can_claim"`         // 当前用户是否可以领取
	ClaimStatusText string `json:"claim_status_text"` // 领取状态描述，如“立即领取”、“已领取”、“已领完”
}

// CouponCenterListResponse 优惠券中心列表响应
type CouponCenterListResponse struct {
	Total int64                  `json:"total"`
	List  []*CouponCenterItemDTO `json:"list"`
}

// 将模型转换为DTO
func ConvertToCouponResponse(coupon *models.TakeoutCoupon) *CouponResponse {
	return ConvertToCouponResponseWithMerchant(coupon, "", "")
}

// 将模型转换为DTO（包含商家信息）
func ConvertToCouponResponseWithMerchant(coupon *models.TakeoutCoupon, merchantName, merchantLogo string) *CouponResponse {
	if coupon == nil {
		return nil
	}

	// 获取类型文本
	typeText := ""
	switch coupon.Type {
	case models.CouponTypeAmount:
		typeText = "满减券"
	case models.CouponTypeDiscount:
		typeText = "折扣券"
	case models.CouponTypeExchange:
		typeText = "商品兑换券"
	}

	// 获取状态文本
	statusText := ""
	switch coupon.Status {
	case models.CouponStatusPending:
		statusText = "待发布"
	case models.CouponStatusUnused:
		statusText = "已发布"
	case models.CouponStatusUsed:
		statusText = "已使用"
	case models.CouponStatusExpired:
		statusText = "已过期"
	case models.CouponStatusDisabled:
		statusText = "已禁用"
	}

	return &CouponResponse{
		ID:                coupon.ID,
		PromotionID:       coupon.PromotionID,
		MerchantID:        coupon.MerchantID,
		MerchantName:      merchantName,
		MerchantLogo:      merchantLogo,
		Name:              coupon.Name,
		Description:       coupon.Description,
		Type:              coupon.Type,
		TypeText:          typeText,
		Amount:            coupon.Amount,
		MinOrderAmount:    coupon.MinOrderAmount,
		MaxDiscountAmount: coupon.MaxDiscountAmount,
		ApplyToAll:        coupon.ApplyToAll,
		ApplyToCategories: coupon.ApplyToCategories,
		ApplyToFoods:      coupon.ApplyToFoods,
		ExcludeFoods:      coupon.ExcludeFoods,
		UserLevelLimit:    coupon.UserLevelLimit,
		PerUserLimit:      coupon.PerUserLimit,
		DailyLimit:        coupon.DailyLimit,
		TotalLimit:        coupon.TotalLimit,
		IssuedCount:       coupon.IssuedCount,
		StartTime:         coupon.StartTime,
		EndTime:           coupon.EndTime,
		Status:            coupon.Status,
		StatusText:        statusText,
		CreatedAt:         coupon.CreatedAt,
	}
}

// 将用户优惠券模型转换为DTO
func ConvertToUserCouponResponse(userCoupon *models.TakeoutUserCoupon, coupon *models.TakeoutCoupon) *UserCouponResponse {
	return ConvertToUserCouponResponseWithMerchant(userCoupon, coupon, "", "")
}

// 将用户优惠券模型转换为DTO（包含商家信息）
func ConvertToUserCouponResponseWithMerchant(userCoupon *models.TakeoutUserCoupon, coupon *models.TakeoutCoupon, merchantName, merchantLogo string) *UserCouponResponse {
	if userCoupon == nil {
		return nil
	}

	// 获取状态文本
	statusText := ""
	switch userCoupon.Status {
	case models.UserCouponStatusUnused:
		statusText = "未使用"
	case models.UserCouponStatusUsed:
		statusText = "已使用"
	case models.UserCouponStatusExpired:
		statusText = "已过期"
	}

	return &UserCouponResponse{
		ID:         userCoupon.ID,
		UserID:     userCoupon.UserID,
		CouponID:   userCoupon.CouponID,
		Coupon:     ConvertToCouponResponseWithMerchant(coupon, merchantName, merchantLogo),
		Status:     userCoupon.Status,
		StatusText: statusText,
		UsedTime:   userCoupon.UsedTime,
		OrderID:    userCoupon.OrderID,
		CreatedAt:  userCoupon.CreatedAt,
	}
}
