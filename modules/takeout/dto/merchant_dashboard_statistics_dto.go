/**
 * 商户仪表板统计DTO
 *
 * 本文件定义了商户仪表板统计相关的数据传输对象，用于前后端数据交互。
 * 包含商户日常经营数据统计，如今日订单数、销售额、待处理订单等。
 */

package dto

// MerchantDashboardStatisticsDTO 商户仪表板统计数据
type MerchantDashboardStatisticsDTO struct {
	TodayOrders      int     `json:"todayOrders"`      // 今日订单数
	TodaySales       float64 `json:"todaySales"`       // 今日销售额
	PendingOrders    int     `json:"pendingOrders"`    // 待处理订单数
	PendingShipment  int     `json:"pendingShipment"`  // 待发货订单数
	PendingRefund    int     `json:"pendingRefund"`    // 待退款订单数
	LowStockProducts int     `json:"lowStockProducts"` // 库存不足商品数
}
