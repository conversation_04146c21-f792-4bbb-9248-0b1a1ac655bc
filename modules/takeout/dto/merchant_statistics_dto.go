/**
 * 商家统计数据DTO
 *
 * 本文件定义了商家统计相关的数据传输对象，用于API接口的响应。
 * 包含了商品统计和订单统计的数据结构。
 */

package dto

// MerchantStatisticsDTO 商家统计数据DTO
type MerchantStatisticsDTO struct {
	FoodStatistics  FoodStatisticsDTO  `json:"food_statistics"`  // 商品统计
	OrderStatistics OrderStatisticsDTO `json:"order_statistics"` // 订单统计
}

// FoodStatisticsDTO 商品统计DTO
type FoodStatisticsDTO struct {
	TotalCount    int `json:"total_count"`    // 商品总数
	OnSaleCount   int `json:"on_sale_count"`  // 在售商品数
	PendingCount  int `json:"pending_count"`  // 待审核商品数
	SoldOutCount  int `json:"sold_out_count"` // 已售罄商品数
}

// OrderStatisticsDTO 订单统计DTO
type OrderStatisticsDTO struct {
	TotalCount     int `json:"total_count"`     // 订单总数
	CompletedCount int `json:"completed_count"` // 已完成订单数
	ProcessingCount int `json:"processing_count"` // 处理中订单数
	CancelledCount int `json:"cancelled_count"` // 已取消订单数
}
