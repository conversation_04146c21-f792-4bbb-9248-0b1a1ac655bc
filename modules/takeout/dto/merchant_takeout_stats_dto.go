/*
 * 商户外卖统计DTO
 *
 * 本文件定义了商户外卖统计相关的数据传输对象，用于前后端数据交互。
 * 包含商户外卖业务的核心统计指标，支持商户实时了解业务状况。
 */

package dto

// MerchantTakeoutStatsDTO 商户外卖统计数据
type MerchantTakeoutStatsDTO struct {
	TodaySales       float64 `json:"todaySales" description:"今日销售额"`           // 今日销售额
	TodayOrders      int     `json:"todayOrders" description:"今日订单数"`          // 今日订单数
	PendingOrders    int     `json:"pendingOrders" description:"待处理订单数"`       // 待处理订单数
	LowStockProducts int     `json:"lowStockProducts" description:"库存不足商品数"`   // 库存不足商品数
	SalesTrend       float64 `json:"salesTrend" description:"销售趋势百分比(与昨日对比)"`  // 销售趋势百分比(与昨日对比)
	OrdersTrend      float64 `json:"ordersTrend" description:"订单趋势百分比(与昨日对比)"` // 订单趋势百分比(与昨日对比)
}
