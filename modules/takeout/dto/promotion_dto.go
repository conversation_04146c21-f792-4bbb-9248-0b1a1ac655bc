/**
 * 外卖模块 - 促销活动DTO
 * 描述：定义促销活动相关的数据传输对象
 * 作者：系统
 * 创建时间：2025-05-14
 */

package dto

import (
	"time"
	
	"o_mall_backend/modules/takeout/models"
)

// CreatePromotionRequest 创建促销活动请求
type CreatePromotionRequest struct {
	MerchantID    int64  `json:"merchant_id" valid:"required"`      // 商户ID
	Name          string `json:"name" valid:"required"`            // 活动名称
	Description   string `json:"description" valid:"required"`     // 活动描述
	Type          int    `json:"type" valid:"required"`            // 活动类型
	StartTime     string `json:"start_time" valid:"required"`      // 开始时间
	EndTime       string `json:"end_time" valid:"required"`        // 结束时间
	Rules         string `json:"rules" valid:"required"`           // 活动规则(JSON)
	MaxUsageCount int    `json:"max_usage_count" valid:"optional"` // 最大使用次数
}

// UpdatePromotionRequest 更新促销活动请求
type UpdatePromotionRequest struct {
	ID            int64  `json:"id" valid:"required"`              // 活动ID
	MerchantID    int64  `json:"merchant_id" valid:"required"`      // 商户ID
	Name          string `json:"name" valid:"optional"`            // 活动名称
	Description   string `json:"description" valid:"optional"`     // 活动描述
	StartTime     string `json:"start_time" valid:"optional"`      // 开始时间
	EndTime       string `json:"end_time" valid:"optional"`        // 结束时间
	Rules         string `json:"rules" valid:"optional"`           // 活动规则
	MaxUsageCount int    `json:"max_usage_count" valid:"optional"` // 最大使用次数
}

// PromotionResponse 促销活动响应
type PromotionResponse struct {
	ID            int64     `json:"id"`            // 活动ID
	MerchantID    int64     `json:"merchant_id"`    // 商户ID
	Name          string    `json:"name"`          // 活动名称
	Description   string    `json:"description"`   // 活动描述
	Type          int       `json:"type"`          // 活动类型
	TypeText      string    `json:"type_text"`     // 活动类型文本
	StartTime     time.Time `json:"start_time"`    // 开始时间
	EndTime       time.Time `json:"end_time"`      // 结束时间
	Status        int       `json:"status"`        // 状态
	StatusText    string    `json:"status_text"`   // 状态文本
	Rules         string    `json:"rules"`         // 活动规则
	MaxUsageCount int       `json:"max_usage_count"` // 最大使用次数
	UsageCount    int       `json:"usage_count"`    // 已使用次数
	CreatedAt     time.Time `json:"created_at"`    // 创建时间
}

// PromotionListResponse 促销活动列表响应
type PromotionListResponse struct {
	Total int                 `json:"total"` // 总记录数
	List  []PromotionResponse `json:"list"`  // 数据列表
}

// 将模型转换为DTO
func ConvertToPromotionResponse(promotion *models.TakeoutPromotion) *PromotionResponse {
	if promotion == nil {
		return nil
	}
	
	// 获取类型文本
	typeText := ""
	switch promotion.Type {
	case models.PromotionTypeFirstOrder:
		typeText = "首单优惠"
	case models.PromotionTypeProductDiscount:
		typeText = "商品折扣"
	case models.PromotionTypeCoupon:
		typeText = "优惠券"
	case models.PromotionTypeFull:
		typeText = "满减活动"
	case models.PromotionTypeLimitedTime:
		typeText = "限时特价"
	}
	
	// 获取状态文本
	statusText := ""
	switch promotion.Status {
	case models.PromotionStatusPending:
		statusText = "待发布"
	case models.PromotionStatusActive:
		statusText = "进行中"
	case models.PromotionStatusEnded:
		statusText = "已结束"
	case models.PromotionStatusCanceled:
		statusText = "已取消"
	}
	
	return &PromotionResponse{
		ID:            promotion.ID,
		MerchantID:    promotion.MerchantID,
		Name:          promotion.Name,
		Description:   promotion.Description,
		Type:          promotion.Type,
		TypeText:      typeText,
		StartTime:     promotion.StartTime,
		EndTime:       promotion.EndTime,
		Status:        promotion.Status,
		StatusText:    statusText,
		Rules:         promotion.Rules,
		MaxUsageCount: promotion.MaxUsageCount,
		UsageCount:    promotion.UsageCount,
		CreatedAt:     promotion.CreatedAt,
	}
}
