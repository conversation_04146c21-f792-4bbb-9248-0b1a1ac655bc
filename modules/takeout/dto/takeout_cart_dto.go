/**
 * 外卖购物车DTO
 *
 * 本文件定义了外卖商品购物车相关的数据传输对象，用于API接口的请求和响应。
 * 设计与现有购物车模块兼容，支持规格选择和套餐组合场景。
 */

package dto

// AddTakeoutToCartRequest 添加外卖商品到购物车请求
type AddTakeoutToCartRequest struct {
	FoodID          int64                       `json:"food_id" valid:"Required"`         // 外卖商品ID
	VariantID       int64                       `json:"variant_id"`                       // 规格变体ID，如果有规格选择
	Quantity        int                         `json:"quantity" valid:"Required;Min(1)"` // 数量
	Remark          string                      `json:"remark" valid:"MaxSize(255)"`      // 备注
	ComboSelections []CartComboSelectionRequest `json:"combo_selections"`                 // 套餐选择，如果是套餐商品
}

// CartComboSelectionRequest 购物车套餐选择请求
type CartComboSelectionRequest struct {
	ComboItemID     int64                         `json:"combo_item_id"`    // 套餐组件ID
	SelectedOptions []ComboOptionSelectionRequest `json:"selected_options"` // 选中的选项
}

// ComboOptionSelectionRequest 套餐选项选择请求
type ComboOptionSelectionRequest struct {
	OptionID int64 `json:"option_id"`               // 选项ID
	Quantity int   `json:"quantity" valid:"Min(1)"` // 数量
}

// UpdateTakeoutCartRequest 整体更新购物车请求
type UpdateTakeoutCartRequest struct {
	MerchantID int64                `json:"merchant_id"` // 商家ID
	CartItems  []CartItemUpdateInfo `json:"cart_items"`  // 购物车项更新信息
}

// CartItemUpdateInfo 购物车项更新信息
type CartItemUpdateInfo struct {
	CartItemID int64 `json:"cart_item_id" valid:"Required"`    // 购物车项ID
	Quantity   int   `json:"quantity" valid:"Required;Min(1)"` // 数量
	Selected   bool  `json:"selected"`                         // 是否选中
}

// SelectCartItemRequest 选中购物车项请求
type SelectCartItemRequest struct {
	CartItemIDs []int64 `json:"cart_item_ids"` // 购物车项ID列表
	Selected    bool    `json:"selected"`      // 是否选中
}

// UpdateTakeoutCartItemRequest 更新购物车中外卖商品请求
type UpdateTakeoutCartItemRequest struct {
	CartItemID      int64                   `json:"cart_item_id" valid:"Required"`    // 购物车项ID
	Quantity        int                     `json:"quantity" valid:"Required;Min(1)"` // 数量
	Remark          string                  `json:"remark" valid:"MaxSize(255)"`      // 备注
	ComboSelections []ComboSelectionRequest `json:"combo_selections"`                 // 套餐选择，如果有变更
}

// TakeoutCartItemDTO 外卖购物车项DTO
type TakeoutCartItemDTO struct {
	CartItemID        int64                    `json:"cart_item_id"`       // 购物车项ID
	MerchantID        int64                    `json:"merchant_id"`        // 商家ID
	MerchantName      string                   `json:"merchant_name"`      // 商家名称
	MerchantLongitude float64                  `json:"merchant_longitude"` // 商家经度
	MerchantLatitude  float64                  `json:"merchant_latitude"`  // 商家纬度
	FoodID            int64                    `json:"food_id"`            // 外卖商品ID
	FoodName          string                   `json:"food_name"`          // 外卖商品名称
	FoodImage         string                   `json:"food_image"`         // 外卖商品图片
	VariantID         int64                    `json:"variant_id"`         // 规格变体ID
	VariantName       string                   `json:"variant_name"`       // 规格变体名称
	Price             float64                  `json:"price"`              // 单价
	OriginalPrice     float64                  `json:"original_price"`     // 原价
	Quantity          int                      `json:"quantity"`           // 数量
	PackagingFee      float64                  `json:"packaging_fee"`      // 包装费
	Subtotal          float64                  `json:"subtotal"`           // 小计
	ComboSelections   []ComboSelectionResponse `json:"combo_selections"`   // 套餐选择
	Remark            string                   `json:"remark"`             // 备注
	Selected          bool                     `json:"selected"`           // 是否选中
	PromotionInfo     string                   `json:"promotion_info"`     // 促销信息(简要描述)
	Promotions        []PromotionInfoDTO       `json:"promotions"`         // 详细促销活动列表
}

// ComboSelectionResponse 套餐选择响应
type ComboSelectionResponse struct {
	ComboItemID     int64                          `json:"combo_item_id"`    // 组件ID
	ComboItemName   string                         `json:"combo_item_name"`  // 组件名称
	ComboID         int64                          `json:"combo_id"`         // 套餐ID（兼容订单服务调用）
	ComboName       string                         `json:"combo_name"`       // 套餐名称（兼容订单服务调用）
	SelectedOptions []ComboOptionSelectionResponse `json:"selected_options"` // 选中的选项
}

// ComboOptionSelectionResponse 套餐选项选择响应
type ComboOptionSelectionResponse struct {
	OptionID   int64   `json:"option_id"`   // 选项ID
	OptionName string  `json:"option_name"` // 选项名称
	ExtraPrice float64 `json:"extra_price"` // 额外价格
	Quantity   int     `json:"quantity"`    // 数量
}

// TakeoutCartSummaryDTO 外卖购物车汇总DTO
type TakeoutCartSummaryDTO struct {
	TotalItems         int                  `json:"total_items"`          // 商品总数
	TotalQuantity      int                  `json:"total_quantity"`       // 总数量
	TotalAmount        float64              `json:"total_amount"`         // 总金额
	OriginalAmount     float64              `json:"original_amount"`      // 原始金额
	PackagingFeeAmount float64              `json:"packaging_fee_amount"` // 包装费总额
	DiscountAmount     float64              `json:"discount_amount"`      // 优惠金额
	Items              []TakeoutCartItemDTO `json:"items"`                // 购物车项列表
}

// TakeoutCartCheckoutDTO 外卖购物车结算DTO
type TakeoutCartCheckoutDTO struct {
	CartID                int64                `json:"cart_id"`                 // 购物车ID
	TotalItems            int                  `json:"total_items"`             // 商品总数
	TotalQuantity         int                  `json:"total_quantity"`          // 总数量
	TotalAmount           float64              `json:"total_amount"`            // 总金额
	OriginalAmount        float64              `json:"original_amount"`         // 原始金额
	PackagingFeeAmount    float64              `json:"packaging_fee_amount"`    // 包装费总额
	DeliveryFee           float64              `json:"delivery_fee"`            // 配送费
	DiscountAmount        float64              `json:"discount_amount"`         // 优惠金额
	PayAmount             float64              `json:"pay_amount"`              // 实付金额
	Items                 []TakeoutCartItemDTO `json:"items"`                   // 购物车项列表
	EatingStyle           int                  `json:"eating_style"`            // 用餐方式:0外卖配送,1到店自取
	DistanceKm            float64              `json:"distance_km"`             // 配送距离(公里)
	EstimatedDeliveryTime int                  `json:"estimated_delivery_time"` // 预计送达时间(分钟)
	TablewareQuantity     int                  `json:"tableware_quantity"`      // 餐具数量
}

// CartCountDetailsDTO 购物车计数详情DTO
type CartCountDetailsDTO struct {
	TotalItems         int     `json:"total_items"`         // 购物车项总数
	TotalQuantity      int     `json:"total_quantity"`      // 商品总数量
	SelectedItems      int     `json:"selected_items"`      // 已选中的购物车项数
	SelectedQuantity   int     `json:"selected_quantity"`   // 已选中商品的总数量
	UnselectedItems    int     `json:"unselected_items"`    // 未选中的购物车项数
	UnselectedQuantity int     `json:"unselected_quantity"` // 未选中商品的总数量
	TotalAmount        float64 `json:"total_amount"`        // 总金额
	SelectedAmount     float64 `json:"selected_amount"`     // 已选中商品的总金额
	MerchantCount      int     `json:"merchant_count"`      // 涉及的商家数量
}
