/**
 * 外卖分类DTO
 *
 * 本文件定义了外卖商品分类相关的数据传输对象，用于API接口的请求和响应。
 * 支持多级分类管理，方便商家对外卖商品进行科学分类。
 */

package dto

import (
	"time"
)

// TakeoutCategoryDTO 外卖分类DTO
type TakeoutCategoryDTO struct {
	ID          int64     `json:"id"`           // 分类ID
	MerchantID  int64     `json:"merchant_id"`  // 商家ID
	Name        string    `json:"name"`         // 分类名称
	Description string    `json:"description"`  // 分类描述
	Image       string    `json:"image"`        // 分类图片
	ParentID    int64     `json:"parent_id"`    // 父分类ID
	Level       int       `json:"level"`        // 分类层级
	SortOrder   int       `json:"sort_order"`   // 排序值
	IsVisible   bool      `json:"is_visible"`   // 是否可见
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`   // 更新时间
	FoodCount   int       `json:"food_count"`   // 分类下商品数量
	Children    []TakeoutCategoryDTO `json:"children"`  // 子分类列表
}

// CreateTakeoutCategoryRequest 创建外卖分类请求
type CreateTakeoutCategoryRequest struct {
	MerchantID  int64  `json:"merchant_id" valid:"Required"`        // 商家ID
	Name        string `json:"name" valid:"Required;MaxSize(50)"`   // 分类名称
	Description string `json:"description" valid:"MaxSize(255)"`     // 分类描述
	Image       string `json:"image"`                               // 分类图片
	ParentID    int64  `json:"parent_id"`                           // 父分类ID
	SortOrder   int    `json:"sort_order"`                          // 排序值
	IsVisible   bool   `json:"is_visible"`                          // 是否可见
}

// UpdateTakeoutCategoryRequest 更新外卖分类请求
type UpdateTakeoutCategoryRequest struct {
	ID          int64  `json:"id" valid:"Required"`                 // 分类ID
	Name        string `json:"name" valid:"MaxSize(50)"`            // 分类名称
	Description string `json:"description" valid:"MaxSize(255)"`     // 分类描述
	Image       string `json:"image"`                               // 分类图片
	ParentID    int64  `json:"parent_id"`                           // 父分类ID
	SortOrder   int    `json:"sort_order"`                          // 排序值
	IsVisible   bool   `json:"is_visible"`                          // 是否可见
	MerchantID  int64  `json:"merchant_id"`                         // 商家ID
}

// TakeoutCategoryQueryRequest 外卖分类查询请求
type TakeoutCategoryQueryRequest struct {
	MerchantID     int64  `json:"merchant_id" form:"merchant_id"`       // 商家ID
	ParentID       int64  `json:"parent_id" form:"parent_id"`           // 父分类ID
	Level          int    `json:"level" form:"level"`                   // 分类层级
	IncludeFood    bool   `json:"include_food" form:"include_food"`      // 是否包含商品数量
	IncludeHidden  bool   `json:"include_hidden" form:"include_hidden"`  // 是否包含隐藏分类
	IncludeChildren bool  `json:"include_children" form:"include_children"` // 是否包含子分类
	Keyword        string `json:"keyword" form:"keyword"`               // 关键词搜索
	Page           int    `json:"page" form:"page"`                     // 页码
	PageSize       int    `json:"page_size" form:"page_size"`           // 每页数量
}

// TakeoutCategoryResponseList 外卖分类列表响应
type TakeoutCategoryResponseList struct {
	Total int                 `json:"total"`     // 总数
	List  []TakeoutCategoryDTO `json:"list"`     // 列表
}
