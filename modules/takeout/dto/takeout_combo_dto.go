/**
 * 外卖套餐DTO
 *
 * 本文件定义了外卖套餐相关的数据传输对象，包括套餐组件和选项的请求响应结构。
 * 用于外卖套餐管理和用户点餐时的套餐选择。
 */

package dto

// TakeoutComboItemRequest 套餐组件请求参数
type TakeoutComboItemRequest struct {
	FoodID      int64   `json:"food_id"`      // 关联的食品ID
	Name        string  `json:"name"`         // 组件名称，如"配菜选择"、"加料"
	Description string  `json:"description"`  // 组件描述
	IsRequired  bool    `json:"is_required"`  // 是否必选
	Multiple    bool    `json:"multiple"`     // 是否可多选
	MinSelect   int     `json:"min_select"`   // 最少选择数量
	MaxSelect   int     `json:"max_select"`   // 最多选择数量
	DisplaySort int     `json:"display_sort"` // 显示排序
	SortOrder   int     `json:"sort_order"`   // 排序权重
	Status      int     `json:"status"`       // 状态：1-启用，0-禁用
}

// CreateComboItemRequest 创建套餐组件请求参数（与 TakeoutComboItemRequest 保持一致）
type CreateComboItemRequest TakeoutComboItemRequest

// UpdateComboItemRequest 更新套餐组件请求参数
type UpdateComboItemRequest struct {
	ID          int64   `json:"id"`           // 组件ID
	Name        string  `json:"name"`         // 组件名称
	Description string  `json:"description"`  // 组件描述
	IsRequired  bool    `json:"required"`     // 是否必选
	Multiple    bool    `json:"multiple"`     // 是否可多选
	MinSelect   int     `json:"min_select"`   // 最少选择数量
	MaxSelect   int     `json:"max_select"`   // 最多选择数量
	DisplaySort int     `json:"display_sort"` // 显示排序
	SortOrder   int     `json:"sort_order"`   // 排序权重
	Status      int     `json:"status"`       // 状态
}

// TakeoutComboItemDTO 套餐组件数据传输对象
type TakeoutComboItemDTO struct {
	ID          int64   `json:"id"`           // 组件ID
	FoodID      int64   `json:"food_id"`      // 关联的食品ID
	Name        string  `json:"name"`         // 组件名称
	Description string  `json:"description"`  // 组件描述
	IsRequired  bool    `json:"is_required"`  // 是否必选
	Multiple    bool    `json:"multiple"`     // 是否可多选
	MinSelect   int     `json:"min_select"`   // 最少选择数量
	MaxSelect   int     `json:"max_select"`   // 最多选择数量
	DisplaySort int     `json:"display_sort"` // 显示排序
	SortOrder   int     `json:"sort_order"`   // 排序权重
	Status      int     `json:"status"`       // 状态
	Options     []TakeoutComboOptionDTO `json:"options,omitempty"` // 选项列表
	CreatedAt   string  `json:"created_at"`   // 创建时间
	UpdatedAt   string  `json:"updated_at"`   // 更新时间
}

// ComboItemResponse 套餐组件响应结构
type ComboItemResponse TakeoutComboItemDTO

// TakeoutComboOptionRequest 套餐选项请求参数
type TakeoutComboOptionRequest struct {
	ItemID       int64   `json:"item_id"`      // 关联的组件ID(兼容旧字段)
	ComboItemID  int64   `json:"combo_item_id"` // 套餐组件ID
	Name         string  `json:"name"`         // 选项名称
	Description  string  `json:"description"`  // 选项描述
	Image        string  `json:"image"`        // 选项图片
	Price        float64 `json:"price"`        // 选项价格(兼容旧字段)
	ExtraPrice   float64 `json:"extra_price"`  // 选项额外费用
	Stock        int     `json:"stock"`        // 库存量
	IsDefault    bool    `json:"is_default"`   // 是否默认选中
	MaxPerOrder  int     `json:"max_per_order"` // 单次最大订购数量
	IsIndividual bool    `json:"is_individual"` // 是否单独计量
	DisplaySort  int     `json:"display_sort"` // 显示排序(兼容旧字段)
	SortOrder    int     `json:"sort_order"`    // 排序权重
	Status       int     `json:"status"`        // 状态：1-启用，0-禁用
}

// CreateComboOptionRequest 创建套餐选项请求参数
type CreateComboOptionRequest TakeoutComboOptionRequest

// UpdateComboOptionRequest 更新套餐选项请求参数
type UpdateComboOptionRequest struct {
	ID          int64   `json:"id"`           // 选项ID
	ItemID      int64   `json:"item_id"`      // 关联的组件ID(兼容旧字段)
	ComboItemID int64   `json:"combo_item_id"` // 套餐组件ID
	Name        string  `json:"name"`         // 选项名称
	Description string  `json:"description"`  // 选项描述
	Image       string  `json:"image"`        // 选项图片
	Price       float64 `json:"price"`        // 选项价格
	ExtraPrice  float64 `json:"extra_price"`  // 选项额外费用
	IsDefault   bool    `json:"is_default"`   // 是否默认选中
	DisplaySort int     `json:"display_sort"` // 显示排序
	SortOrder   int     `json:"sort_order"`   // 排序权重
	Stock       int     `json:"stock"`        // 库存量
	MaxPerOrder int     `json:"max_per_order"` // 单次最大订购数量
	Status      int     `json:"status"`       // 状态
}

// TakeoutComboOptionDTO 套餐选项数据传输对象
type TakeoutComboOptionDTO struct {
	ID          int64   `json:"id"`           // 选项ID
	ItemID      int64   `json:"item_id"`      // 关联的组件ID
	ComboItemID int64   `json:"combo_item_id"` // 套餐组件ID
	Name        string  `json:"name"`         // 选项名称
	Description string  `json:"description"`  // 选项描述
	Image       string  `json:"image"`        // 选项图片
	Price       float64 `json:"price"`        // 选项价格
	ExtraPrice  float64 `json:"extra_price"`  // 选项额外费用
	Stock       int     `json:"stock"`        // 库存量
	SoldCount   int     `json:"sold_count"`   // 已售数量
	IsDefault   bool    `json:"is_default"`   // 是否默认选中
	MaxPerOrder int     `json:"max_per_order"` // 单次最大订购数量
	IsIndividual bool    `json:"is_individual"` // 是否单独计量
	SortOrder   int     `json:"sort_order"`   // 排序权重
	DisplaySort int     `json:"display_sort"` // 显示排序
	Status      int     `json:"status"`       // 状态
	CreatedAt   string  `json:"created_at"`   // 创建时间
	UpdatedAt   string  `json:"updated_at"`   // 更新时间
}

// ComboOptionResponse 套餐选项响应结构
type ComboOptionResponse TakeoutComboOptionDTO

// ComboSelectionRequest 套餐选择请求参数
type ComboSelectionRequest struct {
	ItemID    int64   `json:"item_id"`    // 组件ID
	OptionIDs []int64 `json:"option_ids"` // 选项ID列表
}
