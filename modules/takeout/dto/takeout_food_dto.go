/**
 * 外卖食品DTO
 *
 * 本文件定义了外卖食品相关的数据传输对象，用于API接口的请求和响应。
 * 包含了列表查询、详情查询和创建/更新的请求和响应结构。
 */

package dto

import (
	"time"
)

// TakeoutFoodListItemDTO 外卖食品列表项DTO
// TakeoutFoodListItemDTO 外卖食品列表项DTO
// 增加Variants字段用于返回食品所有规格
// 详见TakeoutFoodVariantDTO

type TakeoutFoodListItemDTO struct {
	ID              int64     `json:"id"`               // 商品ID
	Name            string    `json:"name"`             // 商品名称
	Brief           string    `json:"brief"`            // 商品简介
	Image           string    `json:"image"`            // 商品图片
	Price           float64   `json:"price"`            // 基础价格
	OriginalPrice   float64   `json:"original_price"`   // 原价
	PackagingFee    float64   `json:"packaging_fee"`    // 包装费
	PreparationTime int       `json:"preparation_time"` // 备餐时间(分钟)
	IsSpicy         bool      `json:"is_spicy"`         // 是否辣味
	IsCombination   bool      `json:"is_combination"`   // 是否为套餐组合
	HasVariants     bool      `json:"has_variants"`     // 是否有规格变体
	SoldOut         bool      `json:"sold_out"`         // 是否售罄
	MinPrice        float64   `json:"min_price"`        // 最低价格
	DailyLimit      int       `json:"daily_limit"`      // 每日限量
	MaxPrice        float64   `json:"max_price"`        // 最高价格
	TotalSold       int       `json:"total_sold"`       // 累计销售数量
	Tags            []string  `json:"tags"`             // 标签列表
	CategoryID      int64     `json:"category_id"`      // 分类ID
	CategoryName    string    `json:"category_name"`    // 分类名称
	Status          int       `json:"status"`           // 商品状态
	AuditStatus     int       `json:"audit_status"`     // 审核状态
	IsRecommend     bool      `json:"is_recommend"`     // 是否推荐
	CreatedAt       time.Time `json:"created_at"`       // 创建时间

	Variants        []TakeoutFoodVariantDTO `json:"variants"` // 食品规格列表
}

// TakeoutFoodDetailDTO 外卖食品详情DTO
type TakeoutFoodDetailDTO struct {
	ID               int64                   `json:"id"`                // 商品ID
	MerchantID       int64                   `json:"merchant_id"`       // 商家ID
	MerchantName     string                  `json:"merchant_name"`     // 商家名称
	Name             string                  `json:"name"`              // 商品名称
	Description      string                  `json:"description"`       // 商品描述
	Brief            string                  `json:"brief"`             // 商品简介
	Image            string                  `json:"image"`             // 商品图片
	Price            float64                 `json:"price"`             // 基础价格
	OriginalPrice    float64                 `json:"original_price"`    // 原价
	PackagingFee     float64                 `json:"packaging_fee"`     // 包装费
	PreparationTime  int                     `json:"preparation_time"`  // 备餐时间(分钟)
	IsCombination    bool                    `json:"is_combination"`    // 是否为套餐组合
	IsSpicy          bool                    `json:"is_spicy"`          // 是否辣味
	HasVariants      bool                    `json:"has_variants"`      // 是否有规格变体
	SoldOut          bool                    `json:"sold_out"`          // 是否售罄
	DailyLimit       int                     `json:"daily_limit"`       // 每日限量
	TotalSold        int                     `json:"total_sold"`        // 累计销售数量
	Tags             []string                `json:"tags"`              // 标签列表
	Keywords         []string                `json:"keywords"`          // 关键词列表
	CategoryID       int64                   `json:"category_id"`       // 分类ID
	CategoryName     string                  `json:"category_name"`     // 分类名称
	GlobalCategoryID int64                   `json:"global_category_id"` // 全局分类ID
	Status           int                     `json:"status"`            // 商品状态
	AuditStatus      int                     `json:"audit_status"`      // 审核状态
	AuditReason      string                  `json:"audit_reason"`      // 审核意见
	AuditTime        time.Time               `json:"audit_time"`        // 审核时间
	MinPrice         float64                 `json:"min_price"`         // 最低价格
	MaxPrice         float64                 `json:"max_price"`         // 最高价格
	Variants         []TakeoutFoodVariantDTO `json:"variants"`          // 规格变体列表
	ComboItems       []TakeoutComboItemDTO   `json:"combo_items"`       // 套餐组件列表
	CreatedAt        time.Time               `json:"created_at"`        // 创建时间
	UpdatedAt        time.Time               `json:"updated_at"`        // 更新时间
}

// TakeoutFoodVariantDTO 外卖食品规格DTO
type TakeoutFoodVariantDTO struct {
	ID            int64   `json:"id"`             // 规格ID
	FoodID        int64   `json:"food_id"`        // 食品ID
	Name          string  `json:"name"`           // 规格名称
	Description   string  `json:"description"`    // 规格描述
	Image         string  `json:"image"`          // 规格图片
	Price         float64 `json:"price"`          // 规格价格
	OriginalPrice float64 `json:"original_price"` // 原价
	Stock         int     `json:"stock"`          // 库存
	SoldCount     int     `json:"sold_count"`     // 已售数量
	IsDefault     bool    `json:"is_default"`     // 是否默认规格
	SortOrder     int     `json:"sort_order"`     // 排序值
}

// CreateTakeoutFoodRequest 创建外卖食品请求
type CreateTakeoutFoodRequest struct {
	MerchantID      int64    `json:"merchant_id" valid:"Required"`       // 商家ID
	CategoryID      int64    `json:"category_id" valid:"Required"`       // 分类ID
	Name            string   `json:"name" valid:"Required;MaxSize(100)"` // 商品名称
	Description     string   `json:"description"`                        // 商品描述
	Brief           string   `json:"brief" valid:"MaxSize(200)"`         // 商品简介
	Image           string   `json:"image" valid:"Required"`             // 商品图片
	Price           float64  `json:"price" valid:"Required"`             // 基础价格
	OriginalPrice   float64  `json:"original_price"`                     // 原价
	PackagingFee    float64  `json:"packaging_fee"`                      // 包装费
	PreparationTime int      `json:"preparation_time"`                   // 备餐时间(分钟)
	IsCombination   bool     `json:"is_combination"`                     // 是否为套餐组合
	IsSpicy         bool     `json:"is_spicy"`                           // 是否辣味
	HasVariants     bool     `json:"has_variants"`                       // 是否有规格变体
	DailyLimit      int      `json:"daily_limit"`                        // 每日限量
	Tags            []string `json:"tags"`                               // 标签数组
	Keywords        string   `json:"keywords"`                           // 关键词，逗号分隔
	Status          int      `json:"status"`                             // 商品状态
	AuditStatus     int      `json:"audit_status"`                       // 审核状态
	IsRecommend     bool     `json:"is_recommend"`                       // 是否推荐
	SortOrder       int      `json:"sort_order"`                         // 排序值
}

// UpdateTakeoutFoodRequest 更新外卖食品请求
type UpdateTakeoutFoodRequest struct {
	ID               int64      `json:"id" valid:"Required"`        // 商品ID
	CategoryID       *int64     `json:"category_id"`                // 分类ID
	GlobalCategoryID *int64     `json:"global_category_id"`         // 全局分类ID
	Name             string     `json:"name" valid:"MaxSize(100)"`  // 商品名称
	Description      string     `json:"description"`                // 商品描述
	Brief            string     `json:"brief" valid:"MaxSize(200)"` // 商品简介
	Image            string     `json:"image"`                      // 商品图片
	Price            *float64   `json:"price"`                      // 基础价格
	OriginalPrice    *float64   `json:"original_price"`             // 原价
	PackagingFee     *float64   `json:"packaging_fee"`              // 包装费
	PreparationTime  *int       `json:"preparation_time"`           // 备餐时间(分钟)
	SoldOut          *bool      `json:"sold_out"`                   // 是否售罄
	DailyLimit       *int       `json:"daily_limit"`                // 每日限量
	Tags             []string   `json:"tags"`                       // 标签数组
	Keywords         []string   `json:"keywords"`                   // 关键词列表
	Status           *int       `json:"status"`                     // 商品状态
	AuditStatus      *int       `json:"audit_status"`               // 审核状态
	IsRecommend      *bool      `json:"is_recommend"`               // 是否推荐
	SortOrder        *int       `json:"sort_order"`                 // 排序值
}

// TakeoutFoodQueryRequest 外卖食品查询请求
type TakeoutFoodQueryRequest struct {
	MerchantID      int64   `json:"merchant_id" form:"merchant_id"`           // 商家ID
	CategoryID      int64   `json:"category_id" form:"category_id"`           // 分类ID
	Keyword         string  `json:"keyword" form:"keyword"`                   // 搜索关键词
	PriceMin        float64 `json:"price_min" form:"price_min"`               // 最低价格
	PriceMax        float64 `json:"price_max" form:"price_max"`               // 最高价格
	Tags            string  `json:"tags" form:"tags"`                         // 标签，逗号分隔
	Status          int     `json:"status" form:"status"`                     // 状态
	AuditStatus     int     `json:"audit_status" form:"audit_status"`         // 审核状态
	IsRecommend     bool    `json:"is_recommend" form:"is_recommend"`         // 是否推荐
	IsCombination   bool    `json:"is_combination" form:"is_combination"`     // 是否为套餐组合
	HasVariants     bool    `json:"has_variants" form:"has_variants"`         // 是否有规格变体
	IsSpicy         bool    `json:"is_spicy" form:"is_spicy"`                 // 是否辣味
	IncludeVariants bool    `json:"include_variants" form:"include_variants"` // 是否包含规格变体
	IncludeCombo    bool    `json:"include_combo" form:"include_combo"`       // 是否包含套餐信息
	SortBy          string  `json:"sort_by" form:"sort_by"`                   // 排序字段
	SortOrder       string  `json:"sort_order" form:"sort_order"`             // 排序方式
	Page            int     `json:"page" form:"page"`                         // 页码
	PageSize        int     `json:"page_size" form:"page_size"`               // 每页数量
}

// TakeoutFoodResponseList 外卖食品列表响应
type TakeoutFoodResponseList struct {
	Total int                      `json:"total"` // 总数
	List  []TakeoutFoodListItemDTO `json:"list"`  // 列表
}

// TakeoutFoodVariantRequest 外卖食品规格变体请求
type TakeoutFoodVariantRequest struct {
	FoodID        int64   `json:"food_id" valid:"Required"` // 食品ID
	Name          string  `json:"name" valid:"Required"`    // 规格名称
	Description   string  `json:"description"`              // 规格描述
	Image         string  `json:"image"`                    // 规格图片
	Price         float64 `json:"price" valid:"Required"`   // 规格价格
	OriginalPrice float64 `json:"original_price"`           // 原价
	Stock         int     `json:"stock"`                    // 库存
	IsDefault     bool    `json:"is_default"`               // 是否默认规格
	SortOrder     int     `json:"sort_order"`               // 排序值
}

// 使用takeout_combo_dto.go中定义的TakeoutComboItemRequest

// 使用takeout_combo_dto.go中定义的TakeoutComboOptionRequest
