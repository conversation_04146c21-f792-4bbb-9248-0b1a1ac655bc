/**
 * 外卖商家DTO
 *
 * 本文件定义了外卖商家相关的数据传输对象，用于API接口的请求和响应。
 * 主要用于客户端展示外卖商家列表和详情信息。
 */

package dto

import (
	"time"
)

// PromotionInfoDTO 促销活动信息DTO
type PromotionInfoDTO struct {
	ID          int64  `json:"id"`          // 促销活动ID
	Name        string `json:"name"`        // 促销活动名称
	Description string `json:"description"` // 促销活动描述
	Type        int    `json:"type"`        // 促销活动类型
	TypeName    string `json:"type_name"`   // 促销活动类型名称
	Rules       string `json:"rules"`       // 促销规则
	StartTime   string `json:"start_time"`  // 开始时间
	EndTime     string `json:"end_time"`    // 结束时间
}

// MerchantListItemDTO 外卖商家列表项DTO
type MerchantListItemDTO struct {
	ID               int64               `json:"id"`                 // 商家ID
	Name             string              `json:"name"`               // 商家名称
	Logo             string              `json:"logo"`               // 商家Logo
	Description      string              `json:"description"`        // 商家描述
	CategoryID       int64               `json:"category_id"`        // 商家分类ID
	CategoryName     string              `json:"category_name"`      // 商家分类名称
	Address          string              `json:"address"`            // 商家地址
	Longitude        float64             `json:"longitude"`          // 经度
	Latitude         float64             `json:"latitude"`           // 纬度
	DeliveryFee      float64             `json:"delivery_fee"`       // 配送费
	MinOrderAmount   float64             `json:"min_order_amount"`   // 最低订单金额
	DeliveryTime     int                 `json:"delivery_time"`      // 配送时间(分钟)
	OperationStatus  int                 `json:"operation_status"`   // 营业状态(1:营业中,2:休息中)
	Rating           float64             `json:"rating"`             // 评分
	MonthSales       int                 `json:"month_sales"`        // 月销量
	FoodCount        int                 `json:"food_count"`         // 食品数量
	IsRecommend      bool                `json:"is_recommend"`       // 是否推荐
	Distance         float64             `json:"distance,omitempty"` // 距离(单位:米)，可选字段
	PromotionInfo    string              `json:"promotion_info"`     // 促销信息(简要描述)
	Promotions       []PromotionInfoDTO  `json:"promotions"`         // 详细促销活动列表
	BusinessHours    string              `json:"business_hours"`     // 营业时间
	OpeningTime      string              `json:"opening_time"`       // 开始营业时间
	ClosingTime      string              `json:"closing_time"`       // 结束营业时间
	CreatedAt        time.Time           `json:"created_at"`         // 创建时间
}

// MerchantDetailDTO 外卖商家详情DTO
type MerchantDetailDTO struct {
	ID               int64               `json:"id"`                // 商家ID
	Name             string              `json:"name"`              // 商家名称
	Logo             string              `json:"logo"`              // 商家Logo
	Description      string              `json:"description"`       // 商家描述
	CategoryID       int64               `json:"category_id"`       // 商家分类ID
	CategoryName     string              `json:"category_name"`     // 商家分类名称
	ContactName      string              `json:"contact_name"`      // 联系人姓名
	ContactMobile    string              `json:"contact_mobile"`    // 联系人手机号
	Address          string              `json:"address"`           // 商家地址
	Longitude        float64             `json:"longitude"`         // 经度
	Latitude         float64             `json:"latitude"`          // 纬度
	DeliveryFee      float64             `json:"delivery_fee"`      // 配送费
	MinOrderAmount   float64             `json:"min_order_amount"`  // 最低订单金额
	DeliveryTime     int                 `json:"delivery_time"`     // 配送时间(分钟)
	OperationStatus  int                 `json:"operation_status"`  // 营业状态(1:营业中,2:休息中)
	Rating           float64             `json:"rating"`            // 评分
	MonthSales       int                 `json:"month_sales"`       // 月销量
	FoodCount        int                 `json:"food_count"`        // 食品数量
	IsRecommend      bool                `json:"is_recommend"`      // 是否推荐
	PromotionInfo    string              `json:"promotion_info"`    // 促销信息(简要描述)
	Promotions       []PromotionInfoDTO  `json:"promotions"`         // 详细促销活动列表
	BusinessHours    string              `json:"business_hours"`    // 营业时间
	OpeningTime      string              `json:"opening_time"`      // 开始营业时间
	ClosingTime      string              `json:"closing_time"`      // 结束营业时间
	Photos           []string            `json:"photos"`            // 商家照片列表
	LatestAnnouncement string            `json:"latest_announcement"` // 最新公告
	CreatedAt        time.Time           `json:"created_at"`        // 创建时间
	UpdatedAt        time.Time           `json:"updated_at"`        // 更新时间
}

// MerchantQueryRequest 外卖商家查询请求
type MerchantQueryRequest struct {
	Keyword       string  `json:"keyword" form:"keyword"`             // 搜索关键词
	CategoryID    int64   `json:"category_id" form:"category_id"`     // 分类ID
	DistanceMax   float64 `json:"distance_max" form:"distance_max"`   // 最大距离
	Latitude      float64 `json:"latitude" form:"latitude"`           // 纬度
	Longitude     float64 `json:"longitude" form:"longitude"`         // 经度
	MinRating     float64 `json:"min_rating" form:"min_rating"`       // 最低评分
	HasPromotion  bool    `json:"has_promotion" form:"has_promotion"` // 是否有促销
	IsRecommend   bool    `json:"is_recommend" form:"is_recommend"`   // 是否推荐
	SortBy        string  `json:"sort_by" form:"sort_by"`             // 排序字段
	SortOrder     string  `json:"sort_order" form:"sort_order"`       // 排序方式
	Page          int     `json:"page" form:"page"`                   // 页码
	PageSize      int     `json:"page_size" form:"page_size"`         // 每页数量
}

// MerchantResponseList 外卖商家列表响应
type MerchantResponseList struct {
	Total int                  `json:"total"` // 总数
	List  []MerchantListItemDTO `json:"list"`  // 列表
}

// MerchantPromotionCouponInfo 商家促销和优惠券信息
type MerchantPromotionCouponInfo struct {
	MerchantID   int64                   `json:"merchant_id"`   // 商家ID
	MerchantName string                  `json:"merchant_name"` // 商家名称
	Promotions   []PromotionInfoDTO      `json:"promotions"`    // 促销活动列表
	Coupons      []UserCouponResponse    `json:"coupons"`       // 可用优惠券列表
	HasPromotion bool                    `json:"has_promotion"` // 是否有促销活动
	HasCoupon    bool                    `json:"has_coupon"`    // 是否有可用优惠券
}

// MerchantAvailableCouponsInfo 商家可领取优惠券信息
type MerchantAvailableCouponsInfo struct {
	MerchantID   int64              `json:"merchant_id"`   // 商家ID
	MerchantName string             `json:"merchant_name"` // 商家名称
	Promotions   []PromotionInfoDTO `json:"promotions"`    // 促销活动列表
	Coupons      []CouponResponse   `json:"coupons"`       // 可领取优惠券列表
	HasPromotion bool               `json:"has_promotion"` // 是否有促销活动
	HasCoupon    bool               `json:"has_coupon"`    // 是否有可领取优惠券
}
