/**
 * 外卖订单DTO
 *
 * 本文件定义了外卖订单相关的数据传输对象，用于前后端数据交互。
 * 包含订单创建、查询、支付等相关的请求和响应结构体。
 */

package dto

import (
	"time"
)

// CreateTakeoutOrderRequest 创建外卖订单请求（支持多商家）
type CreateTakeoutOrderRequest struct {
	TakeoutAddressID int64                   `json:"takeoutAddressID"` // 外卖配送地址ID
	AddressLat       float64                 `json:"addressLat"`       // 送货地址纬度
	AddressLng       float64                 `json:"addressLng"`       // 送货地址经度
	PaymentMethod    interface{}             `json:"paymentMethod"`    // 支付方式（兼容string和int类型）
	PaymentMethodInt int                     `json:"paymentMethodInt"` // 支付方式（数字类型）：1-微信支付，2-支付宝，3-余额支付
	MerchantOrders   []*MerchantOrderRequest `json:"merchantOrders"`   // 按商家分组的订单信息
	ClientIP         string                  `json:"clientIP"`         // 客户端IP（余额支付时需要）
	DeviceInfo       string                  `json:"deviceInfo"`       // 设备信息（余额支付时需要）
}

// MerchantOrderRequest 单个商家的订单请求
type MerchantOrderRequest struct {
	MerchantID   int64   `json:"merchantID"`   // 商家ID
	CartItemIDs  []int64 `json:"cartItemIDs"`  // 该商家的购物车项ID列表
	CouponID     int64   `json:"couponID"`     // 该商家的优惠券ID
	PromotionID  int64   `json:"promotionID"`  // 该商家要应用的促销活动ID（兼容前端单个ID格式）
	PromotionIDs []int64 `json:"promotionIDs"` // 该商家要应用的促销活动ID列表（支持多个促销活动）
	DeliveryTime string  `json:"deliveryTime"` // 期望配送时间
	Remark       string  `json:"remark"`       // 对该商家的订单备注
}

// CreateTakeoutOrderRequestLegacy 创建外卖订单请求（兼容旧版本）
// @deprecated 请使用 CreateTakeoutOrderRequest 新版本
type CreateTakeoutOrderRequestLegacy struct {
	TakeoutAddressID int64   `json:"takeoutAddressID"` // 外卖配送地址ID
	DeliveryTime     string  `json:"deliveryTime"`     // 期望配送时间
	Remark           string  `json:"remark"`           // 订单备注
	PaymentMethod    string  `json:"paymentMethod"`    // 支付方式
	CouponID         int64   `json:"couponID"`         // 优惠券ID
	CartItemIDs      []int64 `json:"cartItemIDs"`      // 购物车项ID列表
}

// TakeoutOrderDTO 外卖订单详情
type TakeoutOrderDTO struct {
	OrderID        int64                  `json:"orderID"`        // 订单ID
	OrderNo        string                 `json:"orderNo"`        // 订单编号
	UserID         int64                  `json:"userID"`         // 用户ID
	MerchantID     int64                  `json:"merchantID"`     // 商家ID
	TotalAmount    float64                `json:"totalAmount"`    // 订单总金额
	PayAmount      float64                `json:"payAmount"`      // 实付金额
	DiscountAmount float64                `json:"discountAmount"` // 优惠金额
	OrderStatus    int                    `json:"orderStatus"`    // 订单状态
	PayStatus      int                    `json:"payStatus"`      // 支付状态
	DeliveryStatus int                    `json:"deliveryStatus"` // 配送状态
	DeliveryFee    float64                `json:"deliveryFee"`    // 配送费
	PackagingFee   float64                `json:"packagingFee"`   // 包装费
	DeliveryInfo   *TakeoutDeliveryDTO    `json:"deliveryInfo"`   // 配送信息
	Items          []*TakeoutOrderItemDTO `json:"items"`          // 订单项
	PaymentMethod  string                 `json:"paymentMethod"`  // 支付方式
	Remark         string                 `json:"remark"`         // 订单备注
	CouponInfo     *CouponInfoDTO         `json:"couponInfo"`     // 优惠券信息
	IsRated        bool                   `json:"isRated"`        // 是否已评价
	HasRefund      bool                   `json:"hasRefund"`      // 是否已申请退款
	RefundNo       string                 `json:"refundNo"`       // 退款单号
	RefundStatus   int                    `json:"refundStatus"`   // 退款状态：0-无退款，1-申请中，2-退款中，3-已退款，4-退款失败
	RefundAmount   float64                `json:"refundAmount"`   // 退款金额
	RefundReason   string                 `json:"refundReason"`   // 退款原因
	RefundTime     *time.Time             `json:"refundTime"`     // 退款申请时间
	CreateTime     time.Time              `json:"createTime"`     // 创建时间
	PayTime        *time.Time             `json:"payTime"`        // 支付时间
	AcceptedTime   *time.Time             `json:"acceptedTime"`   // 商家接单时间
	DeliveryTime   *time.Time             `json:"deliveryTime"`   // 送达时间
	CompleteTime   *time.Time             `json:"completeTime"`   // 完成时间
	CancelTime     *time.Time             `json:"cancelTime"`     // 取消时间
	CancelReason   string                 `json:"cancelReason"`   // 取消原因
	StatusText     string                 `json:"statusText"`     // 状态文本
}

// TakeoutOrderItemDTO 外卖订单项
type TakeoutOrderItemDTO struct {
	ID            int64    `json:"id"`            // 订单项ID
	OrderID       int64    `json:"orderID"`       // 订单ID
	ProductID     int64    `json:"productID"`     // 商品ID
	ProductName   string   `json:"productName"`   // 商品名称
	ProductType   string   `json:"productType"`   // 商品类型
	Price         float64  `json:"price"`         // 商品单价
	Quantity      int      `json:"quantity"`      // 商品数量
	Amount        float64  `json:"amount"`        // 商品总价
	SpecText      string   `json:"specText"`      // 规格文本
	Image         string   `json:"image"`         // 商品图片
	IsCombination bool     `json:"isCombination"` // 是否是套餐
	ComboItems    []string `json:"comboItems"`    // 套餐项
}

// TakeoutDeliveryDTO 外卖配送信息
type TakeoutDeliveryDTO struct {
	DeliveryStaffID    int64      `json:"deliveryStaffID"`    // 配送员ID
	DeliveryStaffName  string     `json:"deliveryStaffName"`  // 配送员姓名
	DeliveryStaffPhone string     `json:"deliveryStaffPhone"` // 配送员电话
	DeliveryStatus     int        `json:"deliveryStatus"`     // 配送状态
	DeliveryAddress    string     `json:"deliveryAddress"`    // 配送地址
	DeliveryLat        float64    `json:"deliveryLat"`        // 送货地址纬度
	DeliveryLng        float64    `json:"deliveryLng"`        // 送货地址经度
	ReceiverName       string     `json:"receiverName"`       // 收货人姓名
	ReceiverPhone      string     `json:"receiverPhone"`      // 收货人电话
	DeliveryDistance   float64    `json:"deliveryDistance"`   // 配送距离(km)
	ExpectedTime       string     `json:"expectedTime"`       // 预计送达时间
	StartTime          *time.Time `json:"startTime"`          // 开始配送时间
	EndTime            *time.Time `json:"endTime"`            // 结束配送时间
}

// CouponInfoDTO 优惠券信息
type CouponInfoDTO struct {
	CouponID    int64   `json:"couponID"`    // 优惠券ID
	CouponName  string  `json:"couponName"`  // 优惠券名称
	CouponType  int     `json:"couponType"`  // 优惠券类型
	CouponValue float64 `json:"couponValue"` // 优惠券面值
}

// TakeoutOrderListDTO 外卖订单列表
type TakeoutOrderListDTO struct {
	Total     int                `json:"total"`     // 总数
	Page      int                `json:"page"`      // 页码
	PageSize  int                `json:"pageSize"`  // 每页数量
	TotalPage int                `json:"totalPage"` // 总页数
	List      []*TakeoutOrderDTO `json:"list"`      // 列表数据
}

// TakeoutOrderPaymentDTO 外卖订单支付信息
type TakeoutOrderPaymentDTO struct {
	OrderID           int64      `json:"orderID"`           // 订单ID
	OrderNo           string     `json:"orderNo"`           // 订单编号
	TotalAmount       float64    `json:"totalAmount"`       // 订单总金额
	DiscountAmount    float64    `json:"discountAmount"`    // 折扣金额
	PackagingFee      float64    `json:"packagingFee"`      // 包装费
	DeliveryFee       float64    `json:"deliveryFee"`       // 配送费
	CouponDiscount    float64    `json:"couponDiscount"`    // 优惠券折扣
	PointsDiscount    float64    `json:"pointsDiscount"`    // 积分折扣
	PayAmount         float64    `json:"payAmount"`         // 支付金额
	PaymentMethod     string     `json:"paymentMethod"`     // 支付方式
	PayStatus         int        `json:"payStatus"`         // 支付状态
	PaymentStatus     int        `json:"paymentStatus"`     // 支付状态(与数据库字段对应)
	PayTime           *time.Time `json:"payTime"`           // 支付时间
	ExpireTime        time.Time  `json:"expireTime"`        // 过期时间
	PaymentExpireTime time.Time  `json:"paymentExpireTime"` // 支付过期时间
	QRCodeURL         string     `json:"qrCodeURL"`         // 支付二维码URL
	PaymentURL        string     `json:"paymentURL"`        // 支付跳转URL
}

// CreateTakeoutPaymentRequest 创建外卖订单支付请求
type CreateTakeoutPaymentRequest struct {
	OrderID       int64  `json:"orderID" valid:"Required"`       // 订单ID
	PaymentMethod int    `json:"paymentMethod" valid:"Required"` // 支付方式：1-微信支付，2-支付宝，3-余额支付
	ClientIP      string `json:"clientIP"`                       // 客户端IP
	DeviceInfo    string `json:"deviceInfo"`                     // 设备信息
	ReturnURL     string `json:"returnURL"`                      // 支付完成后跳转URL
	Remark        string `json:"remark"`                         // 备注
}

// TakeoutPaymentResponse 外卖订单支付响应
type TakeoutPaymentResponse struct {
	PaymentID     int64  `json:"paymentID"`     // 支付记录ID
	TransactionNo string `json:"transactionNo"` // 交易流水号
	PaymentURL    string `json:"paymentURL"`    // 支付URL（跳转到支付页面）
	QrCodeURL     string `json:"qrCodeURL"`     // 二维码URL（扫码支付）
	AppPayParams  string `json:"appPayParams"`  // APP支付参数（JSON字符串）
	WebPayParams  string `json:"webPayParams"`  // Web支付参数（JSON字符串）
	ExpireTime    int64  `json:"expireTime"`    // 支付过期时间戳（秒）
}

// TakeoutPaymentStatusResponse 外卖订单支付状态响应
type TakeoutPaymentStatusResponse struct {
	PaymentID     int64      `json:"paymentID"`     // 支付记录ID
	TransactionNo string     `json:"transactionNo"` // 交易流水号
	OrderID       int64      `json:"orderID"`       // 订单ID
	Amount        float64    `json:"amount"`        // 支付金额
	PaymentMethod int        `json:"paymentMethod"` // 支付方式
	PaymentStatus int        `json:"paymentStatus"` // 支付状态：0-待支付，1-支付成功，2-支付失败，3-已取消
	PaymentTime   *time.Time `json:"paymentTime"`   // 支付时间
	ExpireTime    *time.Time `json:"expireTime"`    // 过期时间
	StatusText    string     `json:"statusText"`    // 状态文本描述
}

// CloseTakeoutPaymentRequest 关闭外卖订单支付请求
type CloseTakeoutPaymentRequest struct {
	OrderID int64  `json:"orderID" valid:"Required"` // 订单ID
	Reason  string `json:"reason"`                   // 关闭原因
}

// CreateBatchTakeoutPaymentRequest 创建多订单合并支付请求
type CreateBatchTakeoutPaymentRequest struct {
	OrderIDs      []int64 `json:"orderIDs" valid:"Required"`      // 订单ID列表
	PaymentMethod int     `json:"paymentMethod" valid:"Required"` // 支付方式：1-微信支付，2-支付宝，3-余额支付
	ClientIP      string  `json:"clientIP"`                       // 客户端IP
	DeviceInfo    string  `json:"deviceInfo"`                     // 设备信息
	ReturnURL     string  `json:"returnURL"`                      // 支付完成后跳转URL
	Remark        string  `json:"remark"`                         // 备注
}

// BatchTakeoutPaymentResponse 多订单合并支付响应
type BatchTakeoutPaymentResponse struct {
	BatchPaymentID int64   `json:"batchPaymentID"` // 批量支付记录ID
	OrderIDs       []int64 `json:"orderIDs"`       // 订单ID列表
	TotalAmount    float64 `json:"totalAmount"`    // 总支付金额
	TransactionNo  string  `json:"transactionNo"`  // 交易流水号
	PaymentURL     string  `json:"paymentURL"`     // 支付URL（跳转到支付页面）
	QrCodeURL      string  `json:"qrCodeURL"`      // 二维码URL（扫码支付）
	AppPayParams   string  `json:"appPayParams"`   // APP支付参数（JSON字符串）
	WebPayParams   string  `json:"webPayParams"`   // Web支付参数（JSON字符串）
	ExpireTime     int64   `json:"expireTime"`     // 支付过期时间戳（秒）
}

// ApplyRefundRequest 申请退款请求
type ApplyRefundRequest struct {
	OrderID int64   `json:"orderID" valid:"Required"` // 订单ID
	Reason  string  `json:"reason" valid:"Required"`  // 退款原因
	Amount  float64 `json:"amount"`                   // 退款金额（可选，默认为订单实付金额）
	Remark  string  `json:"remark"`                   // 退款备注
}

// RefundResponse 退款响应
type RefundResponse struct {
	RefundID      int64      `json:"refundID"`      // 退款记录ID
	OrderID       int64      `json:"orderID"`       // 订单ID
	RefundNo      string     `json:"refundNo"`      // 退款单号
	RefundAmount  float64    `json:"refundAmount"`  // 退款金额
	RefundStatus  int        `json:"refundStatus"`  // 退款状态：0-申请中，1-商家同意，2-退款成功，3-退款失败，4-已拒绝
	RefundReason  string     `json:"refundReason"`  // 退款原因
	ApplyTime     time.Time  `json:"applyTime"`     // 申请时间
	ProcessTime   *time.Time `json:"processTime"`   // 处理时间
	CompleteTime  *time.Time `json:"completeTime"`  // 完成时间
	StatusText    string     `json:"statusText"`    // 状态文本描述
	ProcessRemark string     `json:"processRemark"` // 处理备注
}

// MerchantProcessRefundRequest 商家处理退款申请请求
type MerchantProcessRefundRequest struct {
	RefundID      string `json:"refundID" valid:"Required"` // 退款记录ID或退款单号
	Action        string `json:"action" valid:"Required"`   // 处理动作：approve-同意，reject-拒绝
	ProcessRemark string `json:"processRemark"`             // 处理备注（拒绝时必填）
}

// MerchantProcessRefundResponse 商家处理退款申请响应
type MerchantProcessRefundResponse struct {
	RefundID      int64     `json:"refundID"`      // 退款记录ID
	OrderID       int64     `json:"orderID"`       // 订单ID
	RefundNo      string    `json:"refundNo"`      // 退款单号
	RefundStatus  int       `json:"refundStatus"`  // 退款状态
	ProcessTime   time.Time `json:"processTime"`   // 处理时间
	ProcessRemark string    `json:"processRemark"` // 处理备注
	StatusText    string    `json:"statusText"`    // 状态文本描述
}
