/**
 * 外卖订单评价DTO
 *
 * 本文件定义了外卖订单评价相关的数据传输对象，用于前后端数据交互。
 * 包含订单评价的请求和响应结构体。
 */

package dto

import (
	"time"
)

// RateTakeoutOrderRequest 评价外卖订单请求
type RateTakeoutOrderRequest struct {
	OrderID        int64    `json:"orderID"`        // 订单ID
	Rating         int      `json:"rating"`         // 总体评分(1-5)
	DeliveryRating int      `json:"deliveryRating"` // 配送评分(1-5)
	FoodRating     int      `json:"foodRating"`     // 食品评分(1-5)
	Comment        string   `json:"comment"`        // 评价内容
	Anonymous      bool     `json:"anonymous"`      // 是否匿名评价
	Images         []string `json:"images"`         // 图片列表
}

// TakeoutOrderRatingDTO 外卖订单评价详情
type TakeoutOrderRatingDTO struct {
	ID             int64     `json:"id"`             // 评价ID
	OrderID        int64     `json:"orderID"`        // 订单ID
	UserID         int64     `json:"userID"`         // 用户ID
	UserName       string    `json:"userName"`       // 用户名称
	UserAvatar     string    `json:"userAvatar"`     // 用户头像
	Rating         int       `json:"rating"`         // 总体评分
	DeliveryRating int       `json:"deliveryRating"` // 配送评分
	FoodRating     int       `json:"foodRating"`     // 食品评分
	Comment        string    `json:"comment"`        // 评价内容
	Anonymous      bool      `json:"anonymous"`      // 是否匿名评价
	HasImages      bool      `json:"hasImages"`      // 是否有图片
	Images         []string  `json:"images"`         // 图片列表
	ReplyContent   string    `json:"replyContent"`   // 商家回复内容
	ReplyTime      *time.Time `json:"replyTime"`     // 商家回复时间
	CreateTime     time.Time  `json:"createTime"`    // 创建时间
}

// TakeoutOrderRatingListDTO 外卖订单评价列表
type TakeoutOrderRatingListDTO struct {
	Total     int                     `json:"total"`     // 总数
	Page      int                     `json:"page"`      // 页码
	PageSize  int                     `json:"pageSize"`  // 每页数量
	TotalPage int                     `json:"totalPage"` // 总页数
	List      []*TakeoutOrderRatingDTO `json:"list"`     // 列表数据
}

// MerchantReplyRatingRequest 商家回复评价请求
type MerchantReplyRatingRequest struct {
	RatingID     int64  `json:"ratingID"`     // 评价ID
	ReplyContent string `json:"replyContent"` // 回复内容
}
