/**
 * 外卖订单统计DTO
 *
 * 本文件定义了外卖订单统计相关的数据传输对象，用于前后端数据交互。
 * 包含订单统计的请求和响应结构体，支持多维度的订单数据分析。
 */

package dto

import (
	"time"
)

// TakeoutOrderStatisticsDTO 外卖订单统计数据
type TakeoutOrderStatisticsDTO struct {
	StartDate        time.Time           `json:"startDate"`        // 统计开始日期
	EndDate          time.Time           `json:"endDate"`          // 统计结束日期
	TotalOrders      int                 `json:"totalOrders"`      // 总订单数
	PendingOrders    int                 `json:"pendingOrders"`    // 待支付订单数
	PaidOrders       int                 `json:"paidOrders"`       // 已支付订单数
	ProcessingOrders int                 `json:"processingOrders"` // 处理中订单数
	DeliveringOrders int                 `json:"deliveringOrders"` // 配送中订单数
	CompletedOrders  int                 `json:"completedOrders"`  // 已完成订单数
	CancelledOrders  int                 `json:"cancelledOrders"`  // 已取消订单数
	TotalAmount      float64             `json:"totalAmount"`      // 总金额
	AvgOrderAmount   float64             `json:"avgOrderAmount"`   // 平均订单金额
	AvgDeliveryTime  float64             `json:"avgDeliveryTime"`  // 平均配送时间(分钟)
	DailyOrderTrends []DailyOrderTrendDTO `json:"dailyOrderTrends"` // 每日订单趋势
	HotFoods         []*HotFoodStatisticsDTO `json:"hotFoods"`     // 热门食品统计
}

// DailyOrderTrendDTO 每日订单趋势数据
type DailyOrderTrendDTO struct {
	Date       string `json:"date"`       // 日期，格式：YYYY-MM-DD
	OrderCount int    `json:"orderCount"` // 订单数量
}

// HotFoodStatisticsDTO 热门食品统计数据
type HotFoodStatisticsDTO struct {
	FoodID       int64  `json:"foodID"`       // 食品ID
	FoodName     string `json:"foodName"`     // 食品名称
	OrderCount   int    `json:"orderCount"`   // 订单数量
	TotalQuantity int   `json:"totalQuantity"` // 总销售数量
}

// OrderStatisticsByTimeRequest 按时间统计订单请求
type OrderStatisticsByTimeRequest struct {
	StartDate string `json:"startDate"` // 开始日期，格式：YYYY-MM-DD
	EndDate   string `json:"endDate"`   // 结束日期，格式：YYYY-MM-DD
	GroupBy   string `json:"groupBy"`   // 分组方式：day,week,month
}

// OrderStatisticsByMerchantRequest 按商家统计订单请求
type OrderStatisticsByMerchantRequest struct {
	MerchantID int64  `json:"merchantID"` // 商家ID，0表示所有商家
	StartDate  string `json:"startDate"`  // 开始日期，格式：YYYY-MM-DD
	EndDate    string `json:"endDate"`    // 结束日期，格式：YYYY-MM-DD
}

// OrderStatisticsByFoodRequest 按食品统计订单请求
type OrderStatisticsByFoodRequest struct {
	CategoryID int64  `json:"categoryID"` // 分类ID，0表示所有分类
	MerchantID int64  `json:"merchantID"` // 商家ID，0表示所有商家
	StartDate  string `json:"startDate"`  // 开始日期，格式：YYYY-MM-DD
	EndDate    string `json:"endDate"`    // 结束日期，格式：YYYY-MM-DD
	Limit      int    `json:"limit"`      // 限制数量，默认10
}

// DeliveryStatisticsDTO 配送统计数据
type DeliveryStatisticsDTO struct {
	TotalDeliveries     int     `json:"totalDeliveries"`     // 总配送数
	CompletedDeliveries int     `json:"completedDeliveries"` // 已完成配送数
	AvgDeliveryTime     float64 `json:"avgDeliveryTime"`     // 平均配送时间(分钟)
	AvgDeliveryDistance float64 `json:"avgDeliveryDistance"` // 平均配送距离(公里)
}
