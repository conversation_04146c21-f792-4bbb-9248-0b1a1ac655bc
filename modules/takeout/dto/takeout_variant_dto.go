/**
 * 外卖规格变体DTO
 *
 * 本文件定义了外卖规格变体相关的数据传输对象，用于API接口的请求和响应。
 * 包含了规格变体的创建、更新、查询等请求和响应结构。
 */

package dto

// CreateVariantRequest 创建外卖规格变体请求
type CreateVariantRequest struct {
	FoodID        int64   `json:"food_id" valid:"Required"`              // 关联的食品ID
	Name          string  `json:"name" valid:"Required;MaxSize(50)"`     // 规格名称
	Description   string  `json:"description" valid:"MaxSize(200)"`      // 规格描述
	Image         string  `json:"image"`                                 // 规格图片
	Price         float64 `json:"price" valid:"Required"`                // 规格价格
	OriginalPrice float64 `json:"original_price"`                        // 原价
	Stock         int     `json:"stock"`                                 // 库存(-1表示不限)
	IsDefault     bool    `json:"is_default"`                            // 是否为默认规格
	SortOrder     int     `json:"sort_order"`                            // 排序值
}

// UpdateVariantRequest 更新外卖规格变体请求
type UpdateVariantRequest struct {
	ID            int64   `json:"id" valid:"Required"`                   // 规格ID
	FoodID        int64   `json:"food_id" valid:"Required"`              // 关联的食品ID
	Name          string  `json:"name" valid:"Required;MaxSize(50)"`     // 规格名称
	Description   string  `json:"description" valid:"MaxSize(200)"`      // 规格描述
	Image         string  `json:"image"`                                 // 规格图片
	Price         float64 `json:"price" valid:"Required"`                // 规格价格
	OriginalPrice float64 `json:"original_price"`                        // 原价
	Stock         int     `json:"stock"`                                 // 库存(-1表示不限)
	IsDefault     bool    `json:"is_default"`                            // 是否为默认规格
	SortOrder     int     `json:"sort_order"`                            // 排序值
}

// VariantListItemDTO 规格列表项DTO
type VariantListItemDTO struct {
	ID            int64   `json:"id"`                  // 规格ID
	FoodID        int64   `json:"food_id"`             // 关联的食品ID
	Name          string  `json:"name"`                // 规格名称
	Description   string  `json:"description"`         // 规格描述
	Image         string  `json:"image"`               // 规格图片
	Price         float64 `json:"price"`               // 规格价格
	OriginalPrice float64 `json:"original_price"`      // 原价
	Stock         int     `json:"stock"`               // 库存
	IsDefault     bool    `json:"is_default"`          // 是否为默认规格
	SortOrder     int     `json:"sort_order"`          // 排序值
	CreatedAt     string  `json:"created_at"`          // 创建时间
	UpdatedAt     string  `json:"updated_at"`          // 更新时间
}
