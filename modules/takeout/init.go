/**
 * 外卖商品模块初始化
 *
 * 本文件负责初始化外卖商品模块，注册数据库模型并提供模块初始化入口。
 * 该模块处理外卖商品、规格、套餐组合等功能，支持与购物车和订单系统对接。
 */

package takeout

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/cart"

	paymentServices "o_mall_backend/modules/payment/services"
	paymentImpl "o_mall_backend/modules/payment/services/impl"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/routers"
	"o_mall_backend/modules/takeout/services"
)

// 初始化模块
func Init() {
	// 初始化购物车模块，确保基础表已创建
	cart.Init()

	logs.Info("初始化外卖模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitTakeoutRouter()

	// 注册支付回调处理器
	registerPaymentCallback()

	logs.Info("外卖模块初始化完成")
}

// registerPaymentCallback 注册支付回调处理器
func registerPaymentCallback() {
	logs.Info("注册外卖支付回调处理器...")

	// 创建外卖支付回调处理器
	callback := services.NewTakeoutPaymentCallback()

	// 获取支付服务实例并设置回调处理器
	paymentService := paymentServices.NewPaymentService()
	if paymentServiceImpl, ok := paymentService.(*paymentImpl.PaymentServiceImpl); ok {
		paymentServiceImpl.SetOrderPaymentCallback(callback)
		logs.Info("外卖支付回调处理器注册成功")
	} else {
		logs.Error("无法获取支付服务实例，支付回调处理器注册失败")
	}
}

// 注册数据库模型
func registerModels() {
	logs.Info("Registering takeout models...")

	// 注册外卖商品相关模型
	orm.RegisterModel(
		new(models.TakeoutFood),
		new(models.TakeoutFoodVariant),
		new(models.TakeoutComboItem),
		new(models.TakeoutComboOption),
		new(models.TakeoutCategory),
		new(models.TakeoutOrderRating),
		new(models.TakeoutOrderItem),
		new(models.TakeoutOrderLog),
		new(models.TakeoutOrderExtension),
		new(models.GlobalCategory),
		new(models.TakeoutCoupon),
		new(models.TakeoutCartItem),
		new(models.TakeoutCartItemLog),
		new(models.TakeoutPromotion),
		new(models.TakeoutUserCoupon),
		new(models.TakeoutUserPromotion),
	)
}
