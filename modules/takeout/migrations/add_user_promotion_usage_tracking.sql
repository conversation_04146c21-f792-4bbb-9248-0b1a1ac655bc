-- 外卖模块促销活动用户使用次数限制功能数据库迁移脚本
-- 创建时间：2025-07-21
-- 描述：添加用户促销使用记录表和订单表促销字段，解决per_user_limit限制问题

-- 1. 创建用户促销使用记录表
CREATE TABLE IF NOT EXISTS `takeout_user_promotion` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `promotion_id` bigint(20) NOT NULL COMMENT '促销活动ID',
  `order_id` bigint(20) NOT NULL COMMENT '关联订单ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `used_time` datetime NOT NULL COMMENT '使用时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_promotion` (`user_id`, `promotion_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_promotion_id` (`promotion_id`),
  KEY `idx_used_time` (`used_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户促销使用记录表';

-- 2. 为外卖订单扩展表添加促销相关字段
ALTER TABLE `takeout_order_extension` 
ADD COLUMN `promotion_ids` varchar(500) DEFAULT '' COMMENT '使用的促销活动ID列表，逗号分隔' AFTER `is_rated`,
ADD COLUMN `promotion_discount` decimal(10,2) DEFAULT '0.00' COMMENT '促销优惠金额' AFTER `promotion_ids`;

-- 3. 为用户促销使用记录表添加索引优化
-- 复合索引：用于快速查询用户对特定促销活动的使用次数
CREATE INDEX `idx_user_promotion_count` ON `takeout_user_promotion` (`user_id`, `promotion_id`, `created_at`);

-- 单独索引：用于促销活动统计
CREATE INDEX `idx_promotion_stats` ON `takeout_user_promotion` (`promotion_id`, `created_at`);

-- 4. 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE `takeout_user_promotion` 
-- ADD CONSTRAINT `fk_user_promotion_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_user_promotion_promotion` FOREIGN KEY (`promotion_id`) REFERENCES `takeout_promotion` (`id`) ON DELETE CASCADE,
-- ADD CONSTRAINT `fk_user_promotion_order` FOREIGN KEY (`order_id`) REFERENCES `order` (`id`) ON DELETE CASCADE;

-- 5. 插入测试数据（可选）
-- INSERT INTO `takeout_user_promotion` (`user_id`, `promotion_id`, `order_id`, `merchant_id`, `discount_amount`, `used_time`) 
-- VALUES 
-- (1, 1, 1, 1, 5.00, NOW()),
-- (2, 1, 2, 1, 5.00, NOW());

-- 6. 创建视图：用于方便查询促销活动使用统计
CREATE VIEW `v_promotion_usage_stats` AS
SELECT 
    p.id as promotion_id,
    p.name as promotion_name,
    p.merchant_id,
    COUNT(up.id) as total_usage_count,
    COUNT(DISTINCT up.user_id) as unique_user_count,
    SUM(up.discount_amount) as total_discount_amount,
    AVG(up.discount_amount) as avg_discount_amount,
    MIN(up.used_time) as first_used_time,
    MAX(up.used_time) as last_used_time
FROM `takeout_promotion` p
LEFT JOIN `takeout_user_promotion` up ON p.id = up.promotion_id
GROUP BY p.id, p.name, p.merchant_id;

-- 7. 创建存储过程：检查用户促销使用次数
DELIMITER //
CREATE PROCEDURE `CheckUserPromotionUsage`(
    IN p_user_id BIGINT,
    IN p_promotion_id BIGINT,
    IN p_per_user_limit INT,
    OUT p_can_use BOOLEAN,
    OUT p_used_count INT
)
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    -- 统计用户使用该促销活动的次数
    SELECT COUNT(*) INTO v_count
    FROM `takeout_user_promotion`
    WHERE `user_id` = p_user_id AND `promotion_id` = p_promotion_id;
    
    SET p_used_count = v_count;
    
    -- 判断是否可以使用
    IF p_per_user_limit <= 0 THEN
        SET p_can_use = TRUE;  -- 没有限制
    ELSE
        SET p_can_use = (v_count < p_per_user_limit);
    END IF;
END //
DELIMITER ;

-- 8. 创建触发器：自动更新促销活动使用次数（可选）
DELIMITER //
CREATE TRIGGER `tr_update_promotion_usage_count` 
AFTER INSERT ON `takeout_user_promotion`
FOR EACH ROW
BEGIN
    UPDATE `takeout_promotion` 
    SET `usage_count` = `usage_count` + 1 
    WHERE `id` = NEW.promotion_id;
END //
DELIMITER ;

-- 9. 创建函数：获取用户对促销活动的使用次数
DELIMITER //
CREATE FUNCTION `GetUserPromotionUsageCount`(
    p_user_id BIGINT,
    p_promotion_id BIGINT
) RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_count
    FROM `takeout_user_promotion`
    WHERE `user_id` = p_user_id AND `promotion_id` = p_promotion_id;
    
    RETURN v_count;
END //
DELIMITER ;

-- 10. 添加注释说明
ALTER TABLE `takeout_user_promotion` COMMENT = '用户促销使用记录表：记录用户使用促销活动的历史，用于实现per_user_limit限制功能';
ALTER TABLE `takeout_order_extension` COMMENT = '外卖订单扩展表：存储外卖订单的特殊属性，包括促销信息';

-- 迁移完成提示
SELECT 'takeout_user_promotion_usage_tracking migration completed successfully!' as message;
