/**
 * 全局商品分类模型
 *
 * 本文件定义了平台全局商品分类的数据模型，由管理员统一维护。
 * 支持多级分类结构，便于统一平台上的商品类别。
 * 商家在编辑商品时需要选择匹配全局分类，方便用户进行跨商家的商品搜索和分类浏览。
 */

package models

import (
	"time"
)

// GlobalCategory 全局商品分类
type GlobalCategory struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"分类ID，主键自增"`                        // 主键ID
	Name        string    `orm:"column(name);size(50);unique" json:"name" description:"分类名称，如'快餐'、'甜点'等"`    // 分类名称
	Code        string    `orm:"column(code);size(50);unique" json:"code" description:"分类编码，唯一，用于系统识别"`      // 分类编码
	Description string    `orm:"column(description);size(255)" json:"description" description:"分类描述"`        // 分类描述
	ParentID    int64     `orm:"column(parent_id);default(0)" json:"parent_id" description:"父分类ID，0表示顶级分类"`  // 父分类ID，0表示顶级分类
	Level       int       `orm:"column(level);default(1)" json:"level" description:"分类层级，1表示一级分类"`          // 分类层级
	SortOrder   int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序权重，数字越小越靠前"` // 排序值
	IsActive    bool      `orm:"column(is_active);default(true)" json:"is_active" description:"是否激活"`        // 是否激活
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`       // 创建时间
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`           // 更新时间
	CreatedBy   int64     `orm:"column(created_by)" json:"created_by" description:"创建人ID，关联管理员账号"`           // 创建人ID
	UpdatedBy   int64     `orm:"column(updated_by)" json:"updated_by" description:"更新人ID，关联管理员账号"`           // 更新人ID
}

// TableName 设置表名
func (g *GlobalCategory) TableName() string {
	return "global_category"
}

// TreeNode 返回分类树节点结构
type GlobalCategoryTreeNode struct {
	ID          int64                   `json:"id"`
	Name        string                  `json:"name"`
	Code        string                  `json:"code"`
	Description string                  `json:"description"`
	ParentID    int64                   `json:"parent_id"`
	Level       int                     `json:"level"`
	SortOrder   int                     `json:"sort_order"`
	IsActive    bool                    `json:"is_active"`
	CreatedAt   time.Time               `json:"created_at"`
	UpdatedAt   time.Time               `json:"updated_at"`
	CreatedBy   int64                   `json:"created_by"`
	UpdatedBy   int64                   `json:"updated_by"`
	Children    []*GlobalCategoryTreeNode `json:"children,omitempty"`
	FoodCount   int                     `json:"food_count,omitempty"` // 该分类下的商品数量
}
