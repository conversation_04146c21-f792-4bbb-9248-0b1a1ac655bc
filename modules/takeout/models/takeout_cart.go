/**
 * 外卖购物车扩展模型
 *
 * 本文件定义了外卖商品在购物车中的扩展数据模型，用于存储外卖特有的购物车信息。
 * 通过关联现有的购物车系统，实现外卖商品添加到购物车的功能。
 */

package models

import (
	"encoding/json"
	"time"
)

// TakeoutCartItem 外卖购物车项扩展
// 用于存储外卖商品在购物车中的特殊属性，如规格选择、套餐组合等
type TakeoutCartItem struct {
	ID              int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                           // 主键ID
	CartItemID      int64     `orm:"column(cart_item_id);unique;index" json:"cart_item_id" description:"关联的购物车项ID"`             // 关联的购物车项ID
	FoodID          int64     `orm:"column(food_id);index" json:"food_id" description:"外卖商品ID"`                                 // 外卖商品ID
	VariantID       int64     `orm:"column(variant_id);default(0)" json:"variant_id" description:"规格变体ID"`                      // 规格变体ID
	VariantName     string    `orm:"column(variant_name);size(100)" json:"variant_name" description:"规格变体名称"`                   // 规格变体名称
	PackagingFee    float64   `orm:"column(packaging_fee);digits(10);decimals(2)" json:"packaging_fee" description:"包装费"`       // 包装费
	ComboSelectData string    `orm:"column(combo_select_data);type(text)" json:"combo_select_data" description:"套餐选择数据，JSON格式"` // 套餐选择数据，JSON格式
	Remark          string    `orm:"column(remark);size(255)" json:"remark" description:"订单备注"`                                 // 订单备注
	CreatedAt       time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`                      // 创建时间
	UpdatedAt       time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`                          // 更新时间
}

// TableName 设置表名
func (t *TakeoutCartItem) TableName() string {
	return "takeout_cart_item"
}

// TableIndex 设置索引
func (t *TakeoutCartItem) TableIndex() [][]string {
	return [][]string{
		{"cart_item_id"},          // 购物车项ID索引（用于关联查询）
		{"food_id"},               // 外卖商品ID索引（用于商品查询）
		{"variant_id"},            // 规格变体ID索引（用于规格查询）
		{"food_id", "variant_id"}, // 商品规格复合索引（用于去重和查询优化）
		{"created_at"},            // 创建时间索引（用于时间范围查询）
	}
}

// ComboSelection 套餐选择项结构
type ComboSelection struct {
	ComboItemID     int64               `json:"combo_item_id"`    // 套餐组件ID
	ComboItemName   string              `json:"combo_item_name"`  // 套餐组件名称
	SelectedOptions []int64             `json:"selected_options"` // 选中的选项ID列表
	OptionDetails   []ComboOptionDetail `json:"option_details"`   // 选项详情
}

// ComboOptionDetail 套餐选项详情
type ComboOptionDetail struct {
	OptionID   int64   `json:"option_id"`   // 选项ID
	OptionName string  `json:"option_name"` // 选项名称
	ExtraPrice float64 `json:"extra_price"` // 额外价格
	Quantity   int     `json:"quantity"`    // 数量
}

// GetComboSelections 获取套餐选择项
func (t *TakeoutCartItem) GetComboSelections() ([]ComboSelection, error) {
	if t.ComboSelectData == "" {
		return []ComboSelection{}, nil
	}

	var selections []ComboSelection
	err := json.Unmarshal([]byte(t.ComboSelectData), &selections)
	return selections, err
}

// SetComboSelections 设置套餐选择项
func (t *TakeoutCartItem) SetComboSelections(selections []ComboSelection) error {
	data, err := json.Marshal(selections)
	if err != nil {
		return err
	}

	t.ComboSelectData = string(data)
	return nil
}

// TakeoutCartItemLog 外卖购物车操作日志
type TakeoutCartItemLog struct {
	ID           int64     `orm:"pk;auto;column(id)" json:"id" description:"日志ID"`                                  // 日志ID
	UserID       int64     `orm:"column(user_id);index" json:"user_id" description:"用户ID"`                          // 用户ID
	CartItemID   int64     `orm:"column(cart_item_id);index" json:"cart_item_id" description:"购物车项ID"`              // 购物车项ID
	FoodID       int64     `orm:"column(food_id);index" json:"food_id" description:"外卖商品ID"`                        // 外卖商品ID
	VariantID    int64     `orm:"column(variant_id)" json:"variant_id" description:"规格变体ID"`                        // 规格变体ID
	Action       string    `orm:"column(action);size(20)" json:"action" description:"操作类型：add,update,delete"`       // 操作类型
	Quantity     int       `orm:"column(quantity);default(1)" json:"quantity" description:"数量"`                     // 数量
	IP           string    `orm:"column(ip);size(50)" json:"ip" description:"操作IP"`                                 // 操作IP
	ComboChanged bool      `orm:"column(combo_changed);default(false)" json:"combo_changed" description:"套餐选项是否变更"` // 套餐是否变更
	CreatedAt    time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`             // 创建时间
}

// TableName 设置表名
func (t *TakeoutCartItemLog) TableName() string {
	return "takeout_cart_item_log"
}

// TableIndex 设置索引
func (t *TakeoutCartItemLog) TableIndex() [][]string {
	return [][]string{
		{"user_id"},               // 用户ID索引（用于用户日志查询）
		{"cart_item_id"},          // 购物车项ID索引（用于关联查询）
		{"food_id"},               // 外卖商品ID索引（用于商品操作日志）
		{"user_id", "created_at"}, // 用户时间复合索引（用于用户操作历史查询）
		{"action", "created_at"},  // 操作类型时间复合索引（用于操作统计）
		{"created_at"},            // 创建时间索引（用于日志清理和时间范围查询）
	}
}
