/**
 * 外卖分类模型
 *
 * 本文件定义了外卖商品分类的数据模型，用于对外卖商品进行分类管理。
 * 支持多级分类结构，方便商家对外卖商品进行科学分类。
 */

package models

import (
	"time"
)

// TakeoutCategory 外卖商品分类
type TakeoutCategory struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"分类ID，主键自增"`                          // 主键ID
	MerchantID  int64     `orm:"column(merchant_id);index" json:"merchant_id" description:"商家ID，外键关联merchant表"` // 商家ID
	Name        string    `orm:"column(name);size(50)" json:"name" description:"分类名称，如'主食'、'饮品'等"`            // 分类名称
	Description string    `orm:"column(description);size(255)" json:"description" description:"分类描述"`          // 分类描述
	Image       string    `orm:"column(image);size(255)" json:"image" description:"分类图片URL"`                   // 分类图片
	ParentID    int64     `orm:"column(parent_id);default(0)" json:"parent_id" description:"父分类ID，0表示顶级分类"`    // 父分类ID，0表示顶级分类
	Level       int       `orm:"column(level);default(1)" json:"level" description:"分类层级，1表示一级分类"`            // 分类层级
	SortOrder   int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序权重，数字越小越靠前"`   // 排序值
	IsVisible   bool      `orm:"column(is_visible);default(true)" json:"is_visible" description:"是否在前端可见"`     // 是否可见
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`         // 创建时间
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`             // 更新时间
}

// TableName 设置表名
func (t *TakeoutCategory) TableName() string {
	return "takeout_category"
}
