/**
 * 外卖套餐组合模型
 *
 * 本文件定义了外卖套餐组件和选项的数据模型，用于管理套餐组合类商品。
 * 支持例如"粉面可加粉或加鸡蛋"等复杂的组合场景，允许用户自定义选择。
 */

package models

import (
	"time"
)

// TakeoutComboItem 套餐组件模型
// 例如一个"牛肉面套餐"可以有"主食"和"配料"两个组件
type TakeoutComboItem struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"组件ID，主键自增"`                          // 主键ID
	FoodID      int64     `orm:"column(food_id);index" json:"food_id" description:"关联的套餐食品ID，外键关联takeout_food表"`   // 关联的套餐ID
	Name        string    `orm:"column(name);size(100)" json:"name" description:"组件名称，如'主食'、'配料'等"`              // 组件名称，如"主食"
	Description string    `orm:"column(description);size(255)" json:"description" description:"组件描述，提示用户如何选择"` // 组件描述
	MinSelect   int       `orm:"column(min_select);default(1)" json:"min_select" description:"最少选择数量"`           // 最少选择数量
	MaxSelect   int       `orm:"column(max_select);default(1)" json:"max_select" description:"最多选择数量"`           // 最多选择数量
	IsRequired  bool      `orm:"column(is_required);default(true)" json:"is_required" description:"是否必选项"`       // 是否必选
	SortOrder   int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序权重，数字越小越靠前"`      // 排序值
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`           // 创建时间
	UpdatedAt   time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`               // 更新时间
}

// TableName 设置表名
func (t *TakeoutComboItem) TableName() string {
	return "takeout_combo_item"
}

// TakeoutComboOption 套餐选项模型
// 例如"主食"组件下可以有"粗面"、"细面"两个选项
type TakeoutComboOption struct {
	ID           int64     `orm:"pk;auto;column(id)" json:"id" description:"选项ID，主键自增"`                              // 主键ID
	ComboItemID  int64     `orm:"column(combo_item_id);index" json:"combo_item_id" description:"关联的套餐组件ID"`           // 关联的套餐组件ID
	Name         string    `orm:"column(name);size(100)" json:"name" description:"选项名称，如'加鸡蛋'、'加肉'等"`                // 选项名称，如"米饭"
	Description  string    `orm:"column(description);size(255)" json:"description" description:"选项描述，说明该选项的特点"`     // 选项描述
	Image        string    `orm:"column(image);size(255)" json:"image" description:"选项图片URL"`                         // 选项图片
	ExtraPrice   float64   `orm:"column(extra_price);digits(10);decimals(2)" json:"extra_price" description:"额外加价，0表示不加价"` // 额外加价
	Stock        int       `orm:"column(stock);default(-1)" json:"stock" description:"选项库存，-1表示不限制"`                   // 库存(-1表示不限)
	SoldCount    int       `orm:"column(sold_count);default(0)" json:"sold_count" description:"已售数量"`                  // 已售数量
	IsDefault    bool      `orm:"column(is_default);default(false)" json:"is_default" description:"是否默认选中"`           // 是否默认选中
	MaxPerOrder  int       `orm:"column(max_per_order);default(0)" json:"max_per_order" description:"单个订单最多可选数量，0表示不限"` // 单订单最大数量
	IsIndividual bool      `orm:"column(is_individual);default(false)" json:"is_individual" description:"是否为独立加料品，不可单独出售"` // 是否为独立加料品
	SortOrder    int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序权重，数字越小越靠前"`          // 排序值
	CreatedAt    time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`                // 创建时间
	UpdatedAt    time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`                    // 更新时间
}

// TableName 设置表名
func (t *TakeoutComboOption) TableName() string {
	return "takeout_combo_option"
}
