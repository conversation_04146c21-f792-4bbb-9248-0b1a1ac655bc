/**
 * 外卖模块 - 优惠券模型
 * 描述：定义优惠券的数据结构和状态常量
 * 作者：系统
 * 创建时间：2025-05-14
 */

package models

import "time"

// 优惠券类型常量
const (
	CouponTypeAmount   = 1 // 满减券
	CouponTypeDiscount = 2 // 折扣券
	CouponTypeExchange = 3 // 商品兑换券
)

// 优惠券状态常量
const (
	CouponStatusPending  = 0 // 待发布
	CouponStatusUnused   = 1 // 未使用（已发布）
	CouponStatusUsed     = 2 // 已使用
	CouponStatusExpired  = 3 // 已过期
	CouponStatusDisabled = 4 // 已禁用
)

// TakeoutCoupon 优惠券模型
type TakeoutCoupon struct {
	ID                int64     `orm:"pk;auto;column(id)" json:"id"`                                      // 优惠券ID
	PromotionID       int64     `orm:"column(promotion_id)" json:"promotion_id"`                          // 关联活动ID
	MerchantID        int64     `orm:"column(merchant_id)" json:"merchant_id"`                            // 商户ID
	Code              string    `orm:"column(code);size(50)" json:"code"`                                 // 优惠券码
	Name              string    `orm:"column(name);size(100)" json:"name"`                                // 优惠券名称
	Description       string    `orm:"column(description);size(500)" json:"description"`                  // 描述
	Type              int       `orm:"column(type)" json:"type"`                                          // 优惠券类型
	Amount            float64   `orm:"column(amount)" json:"amount"`                                      // 金额/折扣值
	MinOrderAmount    float64   `orm:"column(min_order_amount)" json:"min_order_amount"`                  // 最低订单金额要求
	MaxDiscountAmount float64   `orm:"column(max_discount_amount)" json:"max_discount_amount"`            // 最高优惠金额(针对折扣券)
	ApplyToAll        bool      `orm:"column(apply_to_all)" json:"apply_to_all"`                          // 是否适用于所有商品
	ApplyToCategories string    `orm:"column(apply_to_categories);size(1000)" json:"apply_to_categories"` // 适用分类ID,逗号分隔
	ApplyToFoods      string    `orm:"column(apply_to_foods);size(1000)" json:"apply_to_foods"`           // 适用商品ID,逗号分隔
	ExcludeFoods      string    `orm:"column(exclude_foods);size(1000)" json:"exclude_foods"`             // 排除商品ID,逗号分隔
	UserLevelLimit    int       `orm:"column(user_level_limit)" json:"user_level_limit"`                  // 用户等级限制(0表示不限)
	PerUserLimit      int       `orm:"column(per_user_limit)" json:"per_user_limit"`                      // 每人可领取/使用次数
	DailyLimit        int       `orm:"column(daily_limit)" json:"daily_limit"`                            // 每日发放限制
	TotalLimit        int       `orm:"column(total_limit)" json:"total_limit"`                            // 总发放数量限制
	IssuedCount       int       `orm:"column(issued_count)" json:"issued_count"`                          // 已发放数量
	StartTime         time.Time `orm:"column(start_time)" json:"start_time"`                              // 开始时间
	EndTime           time.Time `orm:"column(end_time)" json:"end_time"`                                  // 结束时间
	Status            int       `orm:"column(status)" json:"status"`                                      // 状态
	CreatedAt         time.Time `orm:"column(created_at);auto_now_add" json:"created_at"`                 // 创建时间
	UpdatedAt         time.Time `orm:"column(updated_at);auto_now" json:"updated_at"`                     // 更新时间
}

// TableName 设置表名
func (t *TakeoutCoupon) TableName() string {
	return "takeout_coupon"
}
