/**
 * 外卖食品模型
 *
 * 本文件定义了外卖食品的基本数据模型，用于存储外卖商品的基本信息。
 * 支持普通商品和套餐组合商品，可以与现有的购物车和订单系统对接。
 */

package models

import (
	"time"
)

// 外卖商品状态常量
const (
	FoodStatusDraft   = 0 // 草稿状态
	FoodStatusOnSale  = 1 // 上架销售中
	FoodStatusOffSale = 2 // 已下架
	FoodStatusSoldOut = 3 // 已售罄
)

// 外卖商品审核状态常量
const (
	AuditStatusPending  = 0 // 未审核/待审核
	AuditStatusApproved = 1 // 审核通过
	AuditStatusRejected = 2 // 审核拒绝
)

// TakeoutFood 外卖食品模型
type TakeoutFood struct {
	ID                int64     `orm:"pk;auto;column(id)" json:"id" description:"外卖商品ID，主键自增"`                                             // 主键ID
	MerchantID        int64     `orm:"column(merchant_id);index" json:"merchant_id" description:"商家ID，关联商家表"`                              // 商家ID
	MerchantName      string    `orm:"-" json:"merchant_name" description:"商家名称，不存储数据库"`                                                   // 商家名称
	MerchantLongitude float64   `orm:"-" json:"merchant_longitude" description:"商家经度，不存储数据库"`                                              // 商家经度
	MerchantLatitude  float64   `orm:"-" json:"merchant_latitude" description:"商家纬度，不存储数据库"`                                                   // 商家名称
	CategoryID       int64     `orm:"column(category_id);index" json:"category_id" description:"分类ID，关联外卖分类表"`                            // 分类ID
	CategoryName     string    `orm:"-" json:"category_name" description:"分类名称，不存储数据库"`                                                   // 分类名称
	GlobalCategoryID int64     `orm:"column(global_category_id);index;default(0)" json:"global_category_id" description:"全局分类ID，关联全局分类表"` // 全局分类ID
	Name             string    `orm:"column(name);size(100)" json:"name" description:"商品名称，最多100个字符"`                                     // 商品名称
	Description      string    `orm:"column(description);type(text)" json:"description" description:"商品详细描述，支持富文本"`                       // 商品描述
	Brief            string    `orm:"column(brief);size(200)" json:"brief" description:"商品简介，最多200个字符"`                                   // 商品简介
	Image            string    `orm:"column(image);size(255)" json:"image" description:"商品主图URL，用于列表展示"`                                  // 商品图片
	MainImage        string    `orm:"-" json:"main_image" description:"商品主图别名，用于订单服务兼容"`                                                  // 商品主图别名
	Price            float64   `orm:"column(price);digits(10);decimals(2)" json:"price" description:"商品基础价格"`                             // 基础价格
	OriginalPrice    float64   `orm:"column(original_price);digits(10);decimals(2)" json:"original_price" description:"商品原价，用于显示优惠"`      // 原价
	PreparationTime  int       `orm:"column(preparation_time);default(15)" json:"preparation_time" description:"备餐时间，单位分钟"`               // 备餐时间(分钟)
	PackagingFee     float64   `orm:"column(packaging_fee);digits(10);decimals(2)" json:"packaging_fee" description:"餐盒费"`                // 包装费
	Weight           float64   `orm:"column(weight);digits(10);decimals(3);default(0)" json:"weight" description:"商品重量，单位千克(kg)，用于计算运费"` // 商品重量(kg)
	IsCombination   bool      `orm:"column(is_combination);default(false)" json:"is_combination" description:"是否为套餐组合商品"`           // 是否为套餐组合
	IsCombo        bool      `orm:"-" json:"is_combo" description:"是否为套餐，用于订单服务兼容"`                          // 是否为套餐（别名）
	IsSpicy          bool      `orm:"column(is_spicy);default(false)" json:"is_spicy" description:"是否为辣味食品"`                              // 是否辣味
	HasVariants      bool      `orm:"column(has_variants);default(false)" json:"has_variants" description:"是否有多规格变体"`                     // 是否有规格变体
	SoldOut          bool      `orm:"column(sold_out);default(false)" json:"sold_out" description:"是否售罄"`                                 // 是否售罄
	DailyLimit       int       `orm:"column(daily_limit);default(0)" json:"daily_limit" description:"每日限量，0表示不限量"`                        // 每日限量(0表示不限)
	SoldCount        int       `orm:"column(sold_count);default(0)" json:"sold_count" description:"已售数量"`                                 // 今日已售数量
	SalesCount       int       `orm:"column(sales_count);default(0)" json:"sales_count" description:"累计销售数量"`                             // 累计销售数量
	TotalSold        int       `orm:"column(total_sold);default(0)" json:"total_sold" description:"累计销售总量"`                               // 累计销售总量
	Tags             string    `orm:"column(tags);size(255)" json:"tags" description:"商品标签，多个用逗号分隔"`                                      // 标签，逗号分隔
	Keywords         string    `orm:"column(keywords);size(255)" json:"keywords" description:"商品搜索关键词，用于模糊搜索"`                            // 关键词
	IsVisible        bool      `orm:"column(is_visible);default(true)" json:"is_visible" description:"是否可见，控制在前端是否展示"`                    // 是否可见
	IsRecommend      bool      `orm:"column(is_recommend);default(false)" json:"is_recommend" description:"是否推荐，用于首页推荐"`                  // 是否推荐
	Status           int       `orm:"column(status);default(0)" json:"status" description:"商品状态：0-草稿，1-上架销售中，2-已下架，3-已售罄"`                // 商品状态
	AuditStatus      int       `orm:"column(audit_status);default(0)" json:"audit_status" description:"审核状态：0-未审核/待审核，1-审核通过，2-审核拒绝"`     // 审核状态
	AuditorID        int64     `orm:"column(auditor_id);default(0)" json:"auditor_id" description:"审核人ID"`                                // 审核人ID
	AuditReason      string    `orm:"column(audit_reason);size(255)" json:"audit_reason" description:"审核意见或拒绝原因"`                         // 审核意见
	AuditTime        time.Time `orm:"column(audit_time);null" json:"audit_time" description:"审核时间"`                                       // 审核时间
	SortOrder        int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序权重，数字越小越靠前"`                         // 排序值
	ViewCount        int       `orm:"column(view_count);default(0)" json:"view_count" description:"浏览次数"`                                 // 浏览次数
	CommentCount     int       `orm:"column(comment_count);default(0)" json:"comment_count" description:"评论数量"`                           // 评论数量
	FavoriteCount    int       `orm:"column(favorite_count);default(0)" json:"favorite_count" description:"收藏数量"`                         // 收藏数量
	Rating           float64   `orm:"column(rating);digits(10);decimals(2);default(0)" json:"rating" description:"平均评分，基于所有评价计算"`         // 平均评分
	RatingTotal      int       `orm:"column(rating_total);default(0)" json:"rating_total" description:"评分总和，用于计算平均分"`                     // 评分总和
	RatingCount      int       `orm:"column(rating_count);default(0)" json:"rating_count" description:"评分次数，用于计算平均分"`                     // 评分次数
	CreatedAt        time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间，系统自动生成"`                        // 创建时间
	UpdatedAt        time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间，系统自动维护"`                            // 更新时间
}

// TableName 设置表名
func (t *TakeoutFood) TableName() string {
	return "takeout_food"
}

// GetPriceRange 获取价格区间
// 如果有多个变体规格，返回最低价格和最高价格；如果没有变体，则返回基础价格
func (t *TakeoutFood) GetPriceRange() (min float64, max float64) {
	if !t.HasVariants {
		return t.Price, t.Price
	}
	// 注意：此方法需要在服务层调用时通过加载变体来计算价格区间
	return 0, 0
}
