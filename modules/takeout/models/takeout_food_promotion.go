/**
 * 外卖模块 - 商品促销关联模型
 * 描述：定义商品与促销活动的关联数据结构
 * 作者：系统
 * 创建时间：2025-05-14
 */

package models

import "time"

// 折扣类型常量
const (
	DiscountTypeAmount  = 1 // 金额减免
	DiscountTypePercent = 2 // 折扣百分比
)

// TakeoutFoodPromotion 商品促销关联模型
type TakeoutFoodPromotion struct {
	ID            int64     `orm:"pk;auto;column(id)" json:"id"`            // ID
	FoodID        int64     `orm:"column(food_id)" json:"food_id"`          // 商品ID
	PromotionID   int64     `orm:"column(promotion_id)" json:"promotion_id"` // 活动ID
	DiscountType  int       `orm:"column(discount_type)" json:"discount_type"` // 折扣类型
	DiscountValue float64   `orm:"column(discount_value)" json:"discount_value"` // 折扣值
	CreatedAt     time.Time `orm:"column(created_at);auto_now_add" json:"created_at"` // 创建时间
	UpdatedAt     time.Time `orm:"column(updated_at);auto_now" json:"updated_at"` // 更新时间
}

// TableName 设置表名
func (t *TakeoutFoodPromotion) TableName() string {
	return "takeout_food_promotion"
}
