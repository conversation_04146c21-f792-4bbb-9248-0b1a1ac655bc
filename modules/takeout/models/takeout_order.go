/**
 * 外卖订单扩展模型
 *
 * 本文件定义了外卖订单的扩展数据模型，用于存储外卖特有的订单信息。
 * 通过关联现有的订单系统，实现外卖商品订单的处理流程。
 */

package models

import (
	"encoding/json"
	"time"
)

// 订单类型常量
const (
	OrderTypeTakeout = 1 // 外卖订单
	OrderTypeOther   = 2 // 其他类型订单
)

// 配送状态常量
const (
	DeliveryStatusWaiting    = 0  // 待配送（初始状态）
	DeliveryStatusPending    = 10 // 待接单
	DeliveryStatusAccepted   = 20 // 已接单
	DeliveryStatusPicking    = 30 // 取餐中
	DeliveryStatusPickedUp   = 40 // 已取餐
	DeliveryStatusDelivering = 50 // 配送中
	DeliveryStatusCompleted  = 60 // 已送达
	DeliveryStatusCancelled  = 70 // 已取消
)

// TakeoutOrderExtension 外卖订单扩展
// 用于存储外卖订单的特殊属性，如配送时间、外卖专送员等
type TakeoutOrderExtension struct {
	ID                    int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                                                              // 主键ID
	OrderID               int64     `orm:"column(order_id);unique;index" json:"order_id" description:"关联的订单ID"`                                                          // 关联的订单ID
	OrderNo               string    `orm:"column(order_no);size(32);index" json:"order_no" description:"订单编号"`                                                           // 订单编号
	MerchantID            int64     `orm:"column(merchant_id);index" json:"merchant_id" description:"商家ID"`                                                              // 商家ID
	OrderType             int       `orm:"column(order_type);default(1)" json:"order_type" description:"订单类型:1外卖订单,2其他"`                                                 // 订单类型
	ExpectedDeliveryTime  time.Time `orm:"column(expected_delivery_time);type(datetime)" json:"expected_delivery_time" description:"预计送达时间"`                             // 预计送达时间
	DeliveryStaffID       int64     `orm:"column(delivery_staff_id);default(0)" json:"delivery_staff_id" description:"配送员ID"`                                            // 配送员ID
	DeliveryStaffName     string    `orm:"column(delivery_staff_name);size(50)" json:"delivery_staff_name" description:"配送员姓名"`                                          // 配送员姓名
	DeliveryStaffPhone    string    `orm:"column(delivery_staff_phone);size(20)" json:"delivery_staff_phone" description:"配送员电话"`                                        // 配送员电话
	DeliveryStatus        int       `orm:"column(delivery_status);default(0)" json:"delivery_status" description:"配送状态: 0待配送，10待接单，20已接单，30取餐中，40已取餐，50配送中，60已送达，70已取消"` // 配送状态
	DeliveryType          int       `orm:"column(delivery_type);default(0)" json:"delivery_type" description:"配送类型: 0-平台配送,1-商家自配,2-到店自取"`                               // 配送类型
	DeliveryFee           float64   `orm:"column(delivery_fee);digits(10);decimals(2)" json:"delivery_fee" description:"配送费"`                                            // 配送费
	PackagingFee          float64   `orm:"column(packaging_fee);digits(10);decimals(2)" json:"packaging_fee" description:"包装费总额"`                                        // 包装费总额
	TablewareQuantity     int       `orm:"column(tableware_quantity);default(0)" json:"tableware_quantity" description:"餐具数量"`                                           // 餐具数量
	EatingStyle           int       `orm:"column(eating_style);default(0)" json:"eating_style" description:"用餐方式: 0-外卖配送,1-到店自取"`                                        // 用餐方式
	DistanceKm            float64   `orm:"column(distance_km);digits(10);decimals(2)" json:"distance_km" description:"配送距离(公里)"`                                         // 配送距离(公里)
	EstimatedDeliveryTime time.Time `orm:"column(estimated_delivery_time)" json:"estimated_delivery_time" description:"预计送达时间"`                                          // 预计送达时间
	IsPreOrder            bool      `orm:"column(is_pre_order);default(false)" json:"is_pre_order" description:"是否预订单"`                                                  // 是否预订单
	PreOrderTime          time.Time `orm:"column(pre_order_time)" json:"pre_order_time" description:"预订时间"`                                                              // 预订时间
	// 送货地址经纬度信息
	DeliveryLat float64 `orm:"column(delivery_lat);digits(12);decimals(8)" json:"delivery_lat" description:"送货地址纬度"` // 送货地址纬度
	DeliveryLng float64 `orm:"column(delivery_lng);digits(12);decimals(8)" json:"delivery_lng" description:"送货地址经度"` // 送货地址经度
	// 以下是自动分配骑手相关字段
	AutoAssigned      int       `orm:"column(auto_assigned);default(0)" json:"auto_assigned" description:"是否自动分配骑手:0-否,1-是"`             // 是否自动分配骑手
	AssignAttempts    int       `orm:"column(assign_attempts);default(0)" json:"assign_attempts" description:"分配尝试次数"`                   // 分配尝试次数
	LastAssignTime    time.Time `orm:"column(last_assign_time);null" json:"last_assign_time" description:"最后分配时间"`                       // 最后分配时间
	AcceptedTime      time.Time `orm:"column(accepted_time);null" json:"accepted_time" description:"商家接单时间"`                             // 商家接单时间
	DeliveryStartTime time.Time `orm:"column(delivery_start_time)" json:"delivery_start_time" description:"配送开始时间"`                      // 配送开始时间
	DeliveryEndTime   time.Time `orm:"column(delivery_end_time)" json:"delivery_end_time" description:"配送结束时间"`                          // 配送结束时间
	IsRated           bool      `orm:"column(is_rated);default(false)" json:"is_rated" description:"是否已评价"`                              // 是否已评价
	PromotionIDs      string    `orm:"column(promotion_ids);size(500)" json:"promotion_ids" description:"使用的促销活动ID列表，逗号分隔"`              // 使用的促销活动ID列表
	PromotionDiscount float64   `orm:"column(promotion_discount);digits(10);decimals(2)" json:"promotion_discount" description:"促销优惠金额"` // 促销优惠金额
	Remark            string    `orm:"column(remark);size(255)" json:"remark" description:"订单备注"`                                        // 订单备注
	CreatedAt         time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`                             // 创建时间
	UpdatedAt         time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`                                 // 更新时间
}

// TableName 设置表名
func (t *TakeoutOrderExtension) TableName() string {
	return "takeout_order_extension"
}

// TakeoutOrderLog 外卖订单日志模型
type TakeoutOrderLog struct {
	ID          int64     `orm:"pk;auto;column(id)" json:"id" description:"日志ID"`                      // 日志ID
	OrderID     int64     `orm:"column(order_id);index" json:"order_id" description:"订单ID"`            // 订单ID
	UserID      int64     `orm:"column(user_id);index" json:"user_id" description:"用户ID"`              // 用户ID
	Action      string    `orm:"column(action);size(50)" json:"action" description:"操作类型"`             // 操作类型
	Description string    `orm:"column(description);size(255)" json:"description" description:"操作描述"`  // 操作描述
	Remark      string    `orm:"column(remark);size(255)" json:"remark" description:"备注"`              // 备注
	IP          string    `orm:"column(ip);size(50)" json:"ip" description:"操作IP"`                     // 操作IP
	CreatedAt   time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"` // 创建时间
}

// TableName 设置表名
func (t *TakeoutOrderLog) TableName() string {
	return "takeout_order_log"
}

// TakeoutOrderRating 外卖订单评价模型
type TakeoutOrderRating struct {
	ID             int64     `orm:"pk;auto;column(id)" json:"id" description:"评价ID"`                                   // 评价ID
	OrderID        int64     `orm:"column(order_id);unique;index" json:"order_id" description:"订单ID"`                  // 订单ID
	UserID         int64     `orm:"column(user_id);index" json:"user_id" description:"用户ID"`                           // 用户ID
	Rating         int       `orm:"column(rating);default(0)" json:"rating" description:"评分，1-5星"`                     // 评分
	DeliveryRating int       `orm:"column(delivery_rating);default(0)" json:"delivery_rating" description:"配送评分，1-5星"` // 配送评分
	Comment        string    `orm:"column(comment);type(text)" json:"comment" description:"评价内容"`                      // 评价内容
	CreatedAt      time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`              // 创建时间
	UpdatedAt      time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`                  // 更新时间
}

// TableName 设置表名
func (t *TakeoutOrderRating) TableName() string {
	return "takeout_order_rating"
}

// TakeoutOrderItem 外卖订单项扩展
// 用于存储外卖订单项的特殊属性，如规格选择、套餐组合等
type TakeoutOrderItem struct {
	ID              int64     `orm:"pk;auto;column(id)" json:"id" description:"主键ID"`                                           // 主键ID
	OrderItemID     int64     `orm:"column(order_item_id);unique;index" json:"order_item_id" description:"关联的订单项ID"`            // 关联的订单项ID
	OrderID         int64     `orm:"column(order_id);index" json:"order_id" description:"订单ID"`                                 // 订单ID
	FoodID          int64     `orm:"column(food_id);index" json:"food_id" description:"外卖商品ID"`                                 // 外卖商品ID
	VariantID       int64     `orm:"column(variant_id);default(0)" json:"variant_id" description:"规格变体ID"`                      // 规格变体ID
	VariantName     string    `orm:"column(variant_name);size(100)" json:"variant_name" description:"规格变体名称"`                   // 规格变体名称
	PackagingFee    float64   `orm:"column(packaging_fee);digits(10);decimals(2)" json:"packaging_fee" description:"包装费"`       // 包装费
	ComboSelectData string    `orm:"column(combo_select_data);type(text)" json:"combo_select_data" description:"套餐选择数据，JSON格式"` // 套餐选择数据，JSON格式
	Remark          string    `orm:"column(remark);size(255)" json:"remark" description:"商品备注"`                                 // 商品备注
	CreatedAt       time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`                      // 创建时间
	UpdatedAt       time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`                          // 更新时间
}

// TableName 设置表名
func (t *TakeoutOrderItem) TableName() string {
	return "takeout_order_item"
}

// GetComboSelections 获取套餐选择项
func (t *TakeoutOrderItem) GetComboSelections() ([]ComboSelection, error) {
	if t.ComboSelectData == "" {
		return []ComboSelection{}, nil
	}

	var selections []ComboSelection
	err := json.Unmarshal([]byte(t.ComboSelectData), &selections)
	return selections, err
}

// SetComboSelections 设置套餐选择项
func (t *TakeoutOrderItem) SetComboSelections(selections []ComboSelection) error {
	data, err := json.Marshal(selections)
	if err != nil {
		return err
	}

	t.ComboSelectData = string(data)
	return nil
}
