/**
 * 外卖模块 - 促销活动模型
 * 描述：定义促销活动的数据结构和状态常量
 * 作者：系统
 * 创建时间：2025-05-14
 */

package models

import "time"

// 促销活动类型常量
const (
	PromotionTypeFirstOrder   = 1 // 首单优惠
	PromotionTypeProductDiscount = 2 // 商品折扣
	PromotionTypeCoupon        = 3 // 优惠券
	PromotionTypeFull          = 4 // 满减活动
	PromotionTypeLimitedTime   = 5 // 限时特价
)

// 促销活动状态常量
const (
	PromotionStatusPending  = 1 // 待发布
	PromotionStatusActive   = 2 // 进行中
	PromotionStatusEnded    = 3 // 已结束
	PromotionStatusCanceled = 4 // 已取消
)

// TakeoutPromotion 促销活动模型
type TakeoutPromotion struct {
	ID            int64      `orm:"pk;auto;column(id)" json:"id"`                  // 活动ID
	MerchantID    int64      `orm:"column(merchant_id)" json:"merchant_id"`        // 商户ID
	Name          string     `orm:"column(name);size(100)" json:"name"`            // 活动名称
	Description   string     `orm:"column(description);size(500)" json:"description"` // 活动描述
	Type          int        `orm:"column(type)" json:"type"`                      // 活动类型
	StartTime     time.Time  `orm:"column(start_time)" json:"start_time"`          // 开始时间
	EndTime       time.Time  `orm:"column(end_time)" json:"end_time"`              // 结束时间
	Status        int        `orm:"column(status)" json:"status"`                  // 状态
	Rules         string     `orm:"column(rules);type(text)" json:"rules"`          // 活动规则(JSON格式)
	MaxUsageCount int        `orm:"column(max_usage_count)" json:"max_usage_count"` // 最大使用次数
	UsageCount    int        `orm:"column(usage_count)" json:"usage_count"`        // 已使用次数
	CreatedAt     time.Time  `orm:"column(created_at);auto_now_add" json:"created_at"` // 创建时间
	UpdatedAt     time.Time  `orm:"column(updated_at);auto_now" json:"updated_at"`   // 更新时间
}

// TableName 设置表名
func (t *TakeoutPromotion) TableName() string {
	return "takeout_promotion"
}
