/*
 * 外卖退款模型
 *
 * 本文件定义了外卖订单退款相关的数据模型，用于存储退款申请、处理和完成的记录。
 * 支持退款状态流转和退款金额管理。
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// TakeoutRefund 外卖退款模型
type TakeoutRefund struct {
	ID            int64     `orm:"auto;pk;column(id)" json:"id"`                         // 退款记录ID
	RefundNo      string    `orm:"size(32);unique;column(refund_no)" json:"refundNo"`     // 退款单号
	OrderID       int64     `orm:"column(order_id);index" json:"orderID"`                // 订单ID
	UserID        int64     `orm:"column(user_id);index" json:"userID"`                  // 用户ID
	MerchantID    int64     `orm:"column(merchant_id);index" json:"merchantID"`          // 商家ID
	RefundAmount  float64   `orm:"digits(10);decimals(2);column(refund_amount)" json:"refundAmount"` // 退款金额
	RefundReason  string    `orm:"size(500);column(refund_reason)" json:"refundReason"`  // 退款原因
	RefundRemark  string    `orm:"size(500);column(refund_remark)" json:"refundRemark"`  // 退款备注
	RefundStatus  int       `orm:"column(refund_status);default(0)" json:"refundStatus"` // 退款状态：0-申请中，1-商家同意，2-退款成功，3-退款失败，4-已拒绝
	ApplyTime     time.Time `orm:"auto_now_add;type(datetime);column(apply_time)" json:"applyTime"` // 申请时间
	ProcessTime   *time.Time `orm:"null;type(datetime);column(process_time)" json:"processTime"`    // 处理时间
	CompleteTime  *time.Time `orm:"null;type(datetime);column(complete_time)" json:"completeTime"`  // 完成时间
	ProcessRemark string    `orm:"size(500);column(process_remark)" json:"processRemark"` // 处理备注
	ProcessorID   int64     `orm:"column(processor_id);null" json:"processorID"`         // 处理人ID
	CreateTime    time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"createTime"` // 创建时间
	UpdateTime    time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"updateTime"`    // 更新时间
}

// TableName 设置表名
func (t *TakeoutRefund) TableName() string {
	return "takeout_refunds"
}

// 退款状态常量
const (
	RefundStatusApplying  = 0 // 申请中
	RefundStatusApproved  = 1 // 商家同意
	RefundStatusCompleted = 2 // 退款成功
	RefundStatusFailed    = 3 // 退款失败
	RefundStatusRejected  = 4 // 已拒绝
)

// GetRefundStatusText 获取退款状态文本
func GetRefundStatusText(status int) string {
	switch status {
	case RefundStatusApplying:
		return "申请中"
	case RefundStatusApproved:
		return "商家同意"
	case RefundStatusCompleted:
		return "退款成功"
	case RefundStatusFailed:
		return "退款失败"
	case RefundStatusRejected:
		return "已拒绝"
	default:
		return "未知状态"
	}
}

// init 注册模型
func init() {
	orm.RegisterModel(new(TakeoutRefund))
}