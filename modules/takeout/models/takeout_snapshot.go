/**
 * 外卖商品快照模型
 *
 * 本文件定义了外卖商品的快照相关模型，用于在订单创建时冻结商品和规格的状态信息。
 * 快照模型不存储在数据库中，而是序列化为JSON字符串后存储在订单项中。
 */

package models

import (
	"encoding/json"
)

// TakeoutSnapshot 外卖商品快照
type TakeoutSnapshot struct {
	ID            int64   `json:"id"`            // 商品ID
	Name          string  `json:"name"`          // 商品名称
	Description   string  `json:"description"`   // 商品描述
	Image         string  `json:"image"`         // 商品图片
	Price         float64 `json:"price"`         // 商品价格
	CategoryID    int64   `json:"category_id"`   // 分类ID
	CategoryName  string  `json:"category_name"` // 分类名称
	Status        int     `json:"status"`        // 状态
	SalesCount    int     `json:"sales_count"`   // 销售数量
	MerchantID    int64   `json:"merchant_id"`   // 商家ID
	IsCombo       bool    `json:"is_combo"`      // 是否套餐
	PackagingFee  float64 `json:"packaging_fee"` // 包装费
	Rating        float64 `json:"rating"`        // 评分
	Tags          string  `json:"tags"`          // 标签
}

// TakeoutVariantSnapshot 外卖规格快照
type TakeoutVariantSnapshot struct {
	ID            int64   `json:"id"`             // 规格ID
	FoodID        int64   `json:"food_id"`        // 关联的商品ID
	Name          string  `json:"name"`           // 规格名称
	Price         float64 `json:"price"`          // 价格
	OriginalPrice float64 `json:"original_price"` // 原价
	Stock         int     `json:"stock"`          // 库存
	Status        int     `json:"status"`         // 状态
	Attributes    string  `json:"attributes"`     // 规格属性
	PackagingFee  float64 `json:"packaging_fee"`  // 包装费
}

// TakeoutComboSelectionSnapshot 套餐选择快照
type TakeoutComboSelectionSnapshot struct {
	ComboID         int64                        `json:"combo_id"`         // 套餐ID
	ComboName       string                       `json:"combo_name"`       // 套餐名称
	ComboItemID     int64                        `json:"combo_item_id"`    // 套餐组件ID
	ComboItemName   string                       `json:"combo_item_name"`  // 套餐组件名称
	SelectedOptions []TakeoutComboOptionSnapshot `json:"selected_options"` // 选中的选项
}

// TakeoutComboOptionSnapshot 套餐选项快照
type TakeoutComboOptionSnapshot struct {
	OptionID   int64   `json:"option_id"`   // 选项ID
	OptionName string  `json:"option_name"` // 选项名称
	ExtraPrice float64 `json:"extra_price"` // 额外价格
	Quantity   int     `json:"quantity"`    // 数量
}

// ParseTakeoutSnapshot 解析外卖商品快照JSON字符串
func ParseTakeoutSnapshot(snapshotJSON string) (*TakeoutSnapshot, error) {
	if snapshotJSON == "" {
		return nil, nil
	}
	var snapshot TakeoutSnapshot
	err := json.Unmarshal([]byte(snapshotJSON), &snapshot)
	if err != nil {
		return nil, err
	}
	return &snapshot, nil
}

// ParseTakeoutVariantSnapshot 解析外卖规格快照JSON字符串
func ParseTakeoutVariantSnapshot(snapshotJSON string) (*TakeoutVariantSnapshot, error) {
	if snapshotJSON == "" {
		return nil, nil
	}
	var snapshot TakeoutVariantSnapshot
	err := json.Unmarshal([]byte(snapshotJSON), &snapshot)
	if err != nil {
		return nil, err
	}
	return &snapshot, nil
}

// ParseTakeoutComboSelections 解析套餐选择JSON字符串
func ParseTakeoutComboSelections(selectionsJSON string) ([]TakeoutComboSelectionSnapshot, error) {
	if selectionsJSON == "" {
		return nil, nil
	}
	var selections []TakeoutComboSelectionSnapshot
	err := json.Unmarshal([]byte(selectionsJSON), &selections)
	if err != nil {
		return nil, err
	}
	return selections, nil
}
