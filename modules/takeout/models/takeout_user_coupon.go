/**
 * 外卖模块 - 用户优惠券模型
 * 描述：定义用户获取的优惠券数据结构和状态常量
 * 作者：系统
 * 创建时间：2025-05-14
 */

package models

import "time"

// 用户优惠券状态常量
const (
	UserCouponStatusUnused  = 1 // 未使用
	UserCouponStatusUsed    = 2 // 已使用
	UserCouponStatusExpired = 3 // 已过期
)

// TakeoutUserCoupon 用户优惠券模型
type TakeoutUserCoupon struct {
	ID        int64     `orm:"pk;auto;column(id)" json:"id"`                      // ID
	UserID    int64     `orm:"column(user_id)" json:"user_id"`                    // 用户ID
	CouponID  int64     `orm:"column(coupon_id)" json:"coupon_id"`                // 优惠券ID
	Status    int       `orm:"column(status)" json:"status"`                      // 状态
	UsedTime  time.Time `orm:"column(used_time);null" json:"used_time"`           // 使用时间
	OrderID   int64     `orm:"column(order_id);null" json:"order_id"`             // 关联订单ID
	CreatedAt time.Time `orm:"column(created_at);auto_now_add" json:"created_at"` // 创建时间
	UpdatedAt time.Time `orm:"column(updated_at);auto_now" json:"updated_at"`     // 更新时间
}

// TableName 设置表名
func (t *TakeoutUserCoupon) TableName() string {
	return "takeout_user_coupon"
}

// TakeoutUserPromotion 用户促销使用记录模型
// 用于记录用户使用促销活动的历史记录，解决per_user_limit限制问题
type TakeoutUserPromotion struct {
	ID             int64     `orm:"pk;auto;column(id)" json:"id"`                                          // ID
	UserID         int64     `orm:"column(user_id);index" json:"user_id"`                                  // 用户ID
	PromotionID    int64     `orm:"column(promotion_id);index" json:"promotion_id"`                        // 促销活动ID
	OrderID        int64     `orm:"column(order_id);index" json:"order_id"`                                // 关联订单ID
	MerchantID     int64     `orm:"column(merchant_id);index" json:"merchant_id"`                          // 商家ID
	DiscountAmount float64   `orm:"column(discount_amount);digits(10);decimals(2)" json:"discount_amount"` // 优惠金额
	UsedTime       time.Time `orm:"column(used_time)" json:"used_time"`                                    // 使用时间
	CreatedAt      time.Time `orm:"column(created_at);auto_now_add" json:"created_at"`                     // 创建时间
	UpdatedAt      time.Time `orm:"column(updated_at);auto_now" json:"updated_at"`                         // 更新时间
}

// TableName 设置表名
func (t *TakeoutUserPromotion) TableName() string {
	return "takeout_user_promotion"
}
