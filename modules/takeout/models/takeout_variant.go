/**
 * 外卖食品规格模型
 *
 * 本文件定义了外卖食品规格变体的数据模型，用于管理不同规格的外卖商品。
 * 例如饮料的大杯、中杯、小杯等不同规格，每种规格可以有不同的价格和库存。
 */

package models

import (
	"time"
)

// TakeoutFoodVariant 外卖食品规格模型
type TakeoutFoodVariant struct {
	ID            int64     `orm:"pk;auto;column(id)" json:"id" description:"规格ID，主键自增"`                     // 主键ID
	FoodID        int64     `orm:"column(food_id);index" json:"food_id" description:"关联的食品ID，外键关联takeout_food表"` // 关联的食品ID
	Name          string    `orm:"column(name);size(100)" json:"name" description:"规格名称，如'大杯'"`              // 规格名称，如"大杯"
	Description   string    `orm:"column(description);size(255)" json:"description" description:"规格描述"`      // 规格描述
	Image         string    `orm:"column(image);size(255)" json:"image" description:"规格图片URL"`                // 规格图片
	Price         float64   `orm:"column(price);digits(10);decimals(2)" json:"price" description:"规格价格"`     // 规格价格
	OriginalPrice float64   `orm:"column(original_price);digits(10);decimals(2)" json:"original_price" description:"规格原价"` // 原价
	Stock         int       `orm:"column(stock);default(-1)" json:"stock" description:"库存数量，-1表示不限制"`           // 库存(-1表示不限)
	SoldCount     int       `orm:"column(sold_count);default(0)" json:"sold_count" description:"已售数量"`        // 已售数量
	IsDefault     bool      `orm:"column(is_default);default(false)" json:"is_default" description:"是否为默认规格"`  // 是否默认规格
	Status        int       `orm:"column(status);default(1)" json:"status" description:"规格状态：0-下架，1-上架"` // 规格状态
	Attributes    string    `orm:"column(attributes);size(500)" json:"attributes" description:"规格属性JSON字符串"` // 规格属性
	PackagingFee  float64   `orm:"column(packaging_fee);digits(10);decimals(2);default(0)" json:"packaging_fee" description:"包装费"` // 包装费
	SortOrder     int       `orm:"column(sort_order);default(0)" json:"sort_order" description:"排序权重，数字越小越靠前"` // 排序值
	CreatedAt     time.Time `orm:"column(created_at);auto_now_add" json:"created_at" description:"创建时间"`      // 创建时间
	UpdatedAt     time.Time `orm:"column(updated_at);auto_now" json:"updated_at" description:"更新时间"`          // 更新时间
}

// TableName 设置表名
func (t *TakeoutFoodVariant) TableName() string {
	return "takeout_food_variant"
}
