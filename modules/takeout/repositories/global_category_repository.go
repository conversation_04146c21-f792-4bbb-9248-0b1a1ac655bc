/**
 * 全局商品分类资源库
 *
 * 本文件实现了全局商品分类的数据访问层，提供对全局分类的增删改查等基本操作。
 * 支持分层结构的分类树查询，方便前端展示分类树形结构。
 */

package repositories

import (
	"errors"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/takeout/models"
)

// GlobalCategoryRepository 全局商品分类资源库
type GlobalCategoryRepository struct {
	ormer orm.Ormer
}

// NewGlobalCategoryRepository 创建全局商品分类资源库实例
func NewGlobalCategoryRepository() *GlobalCategoryRepository {
	return &GlobalCategoryRepository{
		ormer: orm.NewOrm(),
	}
}

// Create 创建全局分类
func (r *GlobalCategoryRepository) Create(category *models.GlobalCategory) (int64, error) {
	// 检查父分类是否存在
	if category.ParentID > 0 {
		parentExists, err := r.Exists(category.ParentID)
		if err != nil {
			return 0, err
		}
		if !parentExists {
			return 0, errors.New("父分类不存在")
		}

		// 设置分类层级
		parent, err := r.GetByID(category.ParentID)
		if err != nil {
			return 0, err
		}
		category.Level = parent.Level + 1
	} else {
		category.Level = 1
	}

	id, err := r.ormer.Insert(category)
	if err != nil {
		logs.Error("创建全局分类失败: %v", err)
		return 0, err
	}
	return id, nil
}

// Update 更新全局分类
func (r *GlobalCategoryRepository) Update(category *models.GlobalCategory) error {
	// 检查父分类是否存在且不是自己
	if category.ParentID > 0 {
		if category.ParentID == category.ID {
			return errors.New("父分类不能是自己")
		}

		parentExists, err := r.Exists(category.ParentID)
		if err != nil {
			return err
		}
		if !parentExists {
			return errors.New("父分类不存在")
		}

		// 检查是否会形成循环依赖
		isCircular, err := r.isCircularDependency(category.ID, category.ParentID)
		if err != nil {
			return err
		}
		if isCircular {
			return errors.New("不能设置自己的子分类为父分类，这会导致循环依赖")
		}

		// 更新分类层级
		parent, err := r.GetByID(category.ParentID)
		if err != nil {
			return err
		}
		category.Level = parent.Level + 1
	} else {
		category.Level = 1
	}

	_, err := r.ormer.Update(category)
	if err != nil {
		logs.Error("更新全局分类失败: %v", err)
		return err
	}
	return nil
}

// Delete 删除全局分类
func (r *GlobalCategoryRepository) Delete(id int64) error {
	// 检查是否有子分类
	hasChildren, err := r.HasChildren(id)
	if err != nil {
		return err
	}
	if hasChildren {
		return errors.New("该分类下有子分类，不能直接删除")
	}

	// 检查是否有关联的商品
	hasProducts, err := r.hasRelatedProducts(id)
	if err != nil {
		return err
	}
	if hasProducts {
		return errors.New("该分类下有关联商品，不能直接删除")
	}

	category := &models.GlobalCategory{ID: id}
	_, err = r.ormer.Delete(category)
	if err != nil {
		logs.Error("删除全局分类失败: %v", err)
		return err
	}
	return nil
}

// GetByID 根据ID获取全局分类
func (r *GlobalCategoryRepository) GetByID(id int64) (*models.GlobalCategory, error) {
	category := &models.GlobalCategory{ID: id}
	err := r.ormer.Read(category)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("获取全局分类失败: %v", err)
		return nil, err
	}
	return category, nil
}

// GetByCode 根据编码获取全局分类
func (r *GlobalCategoryRepository) GetByCode(code string) (*models.GlobalCategory, error) {
	category := &models.GlobalCategory{}
	err := r.ormer.QueryTable(category.TableName()).Filter("code", code).One(category)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("根据编码获取全局分类失败: %v", err)
		return nil, err
	}
	return category, nil
}

// GetAll 获取所有全局分类
func (r *GlobalCategoryRepository) GetAll() ([]*models.GlobalCategory, error) {
	var categories []*models.GlobalCategory
	_, err := r.ormer.QueryTable(new(models.GlobalCategory).TableName()).
		OrderBy("sort_order", "id").All(&categories)
	if err != nil {
		logs.Error("获取所有全局分类失败: %v", err)
		return nil, err
	}
	return categories, nil
}

// GetRootCategories 获取所有根分类
func (r *GlobalCategoryRepository) GetRootCategories() ([]*models.GlobalCategory, error) {
	var categories []*models.GlobalCategory
	_, err := r.ormer.QueryTable(new(models.GlobalCategory).TableName()).
		Filter("parent_id", 0).
		Filter("is_active", true).
		OrderBy("sort_order", "id").All(&categories)
	if err != nil {
		logs.Error("获取所有根分类失败: %v", err)
		return nil, err
	}
	return categories, nil
}

// GetChildren 获取子分类列表
func (r *GlobalCategoryRepository) GetChildren(parentID int64) ([]*models.GlobalCategory, error) {
	var categories []*models.GlobalCategory
	_, err := r.ormer.QueryTable(new(models.GlobalCategory).TableName()).
		Filter("parent_id", parentID).
		OrderBy("sort_order", "id").All(&categories)
	if err != nil {
		logs.Error("获取子分类失败: %v", err)
		return nil, err
	}
	return categories, nil
}

// BuildCategoryTree 构建分类树
func (r *GlobalCategoryRepository) BuildCategoryTree() ([]*models.GlobalCategoryTreeNode, error) {
	// 获取所有分类
	categories, err := r.GetAll()
	if err != nil {
		return nil, err
	}

	// 按ParentID构建哈希表
	categoryMap := make(map[int64]*models.GlobalCategory)
	for _, category := range categories {
		categoryMap[category.ID] = category
	}

	// 构建树形结构
	var rootNodes []*models.GlobalCategoryTreeNode
	nodeMap := make(map[int64]*models.GlobalCategoryTreeNode)

	// 创建所有节点
	for _, category := range categories {
		node := &models.GlobalCategoryTreeNode{
			ID:          category.ID,
			Name:        category.Name,
			Code:        category.Code,
			Description: category.Description,
			ParentID:    category.ParentID,
			Level:       category.Level,
			SortOrder:   category.SortOrder,
			IsActive:    category.IsActive,
			CreatedAt:   category.CreatedAt,
			UpdatedAt:   category.UpdatedAt,
			CreatedBy:   category.CreatedBy,
			UpdatedBy:   category.UpdatedBy,
			Children:    make([]*models.GlobalCategoryTreeNode, 0),
		}
		nodeMap[category.ID] = node

		// 如果是根节点，加入根节点列表
		if category.ParentID == 0 {
			rootNodes = append(rootNodes, node)
		}
	}

	// 构建父子关系
	for _, node := range nodeMap {
		parentID := node.ParentID
		if parentID > 0 {
			if parentNode, exists := nodeMap[parentID]; exists {
				parentNode.Children = append(parentNode.Children, node)
			}
		}
	}

	return rootNodes, nil
}

// Exists 检查分类是否存在
func (r *GlobalCategoryRepository) Exists(id int64) (bool, error) {
	exist := r.ormer.QueryTable(new(models.GlobalCategory).TableName()).
		Filter("id", id).Exist()
	return exist, nil
}

// HasChildren 检查是否有子分类
func (r *GlobalCategoryRepository) HasChildren(id int64) (bool, error) {
	exist := r.ormer.QueryTable(new(models.GlobalCategory).TableName()).
		Filter("parent_id", id).Exist()
	return exist, nil
}

// hasRelatedProducts 检查是否有关联的商品
func (r *GlobalCategoryRepository) hasRelatedProducts(id int64) (bool, error) {
	exist := r.ormer.QueryTable(new(models.TakeoutFood).TableName()).
		Filter("global_category_id", id).Exist()
	return exist, nil
}

// isCircularDependency 检查是否形成循环依赖
func (r *GlobalCategoryRepository) isCircularDependency(categoryID, parentID int64) (bool, error) {
	visited := make(map[int64]bool)
	current := parentID

	for current != 0 {
		if current == categoryID {
			return true, nil
		}

		if visited[current] {
			return true, nil
		}

		visited[current] = true

		parent, err := r.GetByID(current)
		if err != nil {
			return false, err
		}
		if parent == nil {
			break
		}

		current = parent.ParentID
	}

	return false, nil
}

// GetCategoryPath 获取分类路径（从根分类到当前分类）
func (r *GlobalCategoryRepository) GetCategoryPath(id int64) ([]*models.GlobalCategory, error) {
	var path []*models.GlobalCategory
	
	current, err := r.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	if current == nil {
		return path, nil
	}
	
	// 从当前节点向上追溯到根节点
	for current != nil && current.ID > 0 {
		// 将当前节点添加到路径的开头
		path = append([]*models.GlobalCategory{current}, path...)
		
		if current.ParentID == 0 {
			break
		}
		
		current, err = r.GetByID(current.ParentID)
		if err != nil {
			return nil, err
		}
	}
	
	return path, nil
}
