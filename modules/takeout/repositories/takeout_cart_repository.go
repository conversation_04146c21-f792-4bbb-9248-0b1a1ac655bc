/**
 * 外卖购物车仓库
 *
 * 本文件实现了外卖购物车相关的数据库操作，提供数据持久化和查询功能。
 * 负责takeout_cart_item表的CRUD操作，与现有购物车系统集成。
 */

package repositories

import (
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutCartRepository 外卖购物车仓库接口
type TakeoutCartRepository interface {
	// 基础CRUD
	CreateCartItem(item *models.TakeoutCartItem) (int64, error)
	GetCartItemByID(id int64) (*models.TakeoutCartItem, error)
	UpdateCartItem(item *models.TakeoutCartItem) error
	DeleteCartItem(cartItemID int64) error
	HardDeleteCartItem(cartItemID int64, userID int64) (int64, error)
	
	// 查询操作
	GetCartItemByCartItemID(cartItemID int64) (*models.TakeoutCartItem, error)
	ListCartItemsByUserID(userID int64) ([]*models.TakeoutCartItem, error)
	ListCartItemsByCartIDs(cartItemIDs []int64) ([]*models.TakeoutCartItem, error)
	
	// 日志记录
	CreateCartItemLog(log *models.TakeoutCartItemLog) (int64, error)
}

// takeoutCartRepository 外卖购物车仓库实现
type takeoutCartRepository struct {
	ormer orm.Ormer
}

// NewTakeoutCartRepository 创建外卖购物车仓库实例
func NewTakeoutCartRepository() TakeoutCartRepository {
	return &takeoutCartRepository{
		ormer: orm.NewOrm(),
	}
}

// CreateCartItem 创建购物车项
func (r *takeoutCartRepository) CreateCartItem(item *models.TakeoutCartItem) (int64, error) {
	item.CreatedAt = time.Now()
	item.UpdatedAt = time.Now()
	return r.ormer.Insert(item)
}

// GetCartItemByID 根据ID获取购物车项
func (r *takeoutCartRepository) GetCartItemByID(id int64) (*models.TakeoutCartItem, error) {
	item := &models.TakeoutCartItem{ID: id}
	err := r.ormer.Read(item)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("购物车项不存在")
		}
		return nil, err
	}
	return item, nil
}

// UpdateCartItem 更新购物车项
func (r *takeoutCartRepository) UpdateCartItem(item *models.TakeoutCartItem) error {
	item.UpdatedAt = time.Now()
	_, err := r.ormer.Update(item)
	return err
}

// DeleteCartItem 删除购物车项
func (r *takeoutCartRepository) DeleteCartItem(cartItemID int64) error {
	item := &models.TakeoutCartItem{CartItemID: cartItemID}
	err := r.ormer.Read(item, "CartItemID")
	if err != nil {
		if err == orm.ErrNoRows {
			return nil // 已经不存在，视为成功
		}
		return err
	}
	
	_, err = r.ormer.Delete(item)
	return err
}

// GetCartItemByCartItemID 根据CartItemID获取购物车项
func (r *takeoutCartRepository) GetCartItemByCartItemID(cartItemID int64) (*models.TakeoutCartItem, error) {
	item := &models.TakeoutCartItem{CartItemID: cartItemID}
	err := r.ormer.Read(item, "CartItemID")
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("购物车项不存在")
		}
		return nil, err
	}
	return item, nil
}

// ListCartItemsByUserID 根据用户ID查询购物车项列表
func (r *takeoutCartRepository) ListCartItemsByUserID(userID int64) ([]*models.TakeoutCartItem, error) {
	var items []*models.TakeoutCartItem
	
	// 先获取用户所有的购物车项ID
	var cartItemIDs []int64
	_, err := r.ormer.Raw("SELECT id FROM cart_items WHERE user_id = ? AND status = 1", userID).QueryRows(&cartItemIDs)
	if err != nil {
		return nil, err
	}
	
	if len(cartItemIDs) == 0 {
		return items, nil
	}
	
	// 根据购物车项ID查询外卖购物车项
	_, err = r.ormer.QueryTable(new(models.TakeoutCartItem)).
		Filter("cart_item_id__in", cartItemIDs).
		All(&items)
	
	return items, err
}

// ListCartItemsByCartIDs 根据购物车项ID列表查询外卖购物车项
func (r *takeoutCartRepository) ListCartItemsByCartIDs(cartItemIDs []int64) ([]*models.TakeoutCartItem, error) {
	var items []*models.TakeoutCartItem
	
	if len(cartItemIDs) == 0 {
		return items, nil
	}
	
	_, err := r.ormer.QueryTable(new(models.TakeoutCartItem)).
		Filter("cart_item_id__in", cartItemIDs).
		All(&items)
	
	return items, err
}

// CreateCartItemLog 创建购物车操作日志
func (r *takeoutCartRepository) CreateCartItemLog(log *models.TakeoutCartItemLog) (int64, error) {
	log.CreatedAt = time.Now()
	return r.ormer.Insert(log)
}

// HardDeleteCartItem 彻底从数据库删除购物车项（物理删除）
func (r *takeoutCartRepository) HardDeleteCartItem(cartItemID int64, userID int64) (int64, error) {
	// 直接执行原始SQL以彻底删除购物车项
	// 使用正确的表名 cart_items
	result, err := r.ormer.Raw("DELETE FROM cart_items WHERE id = ? AND user_id = ?", cartItemID, userID).Exec()
	if err != nil {
		return 0, err
	}
	
	// 返回影响的行数
	rowsAffected, _ := result.RowsAffected()
	return rowsAffected, nil
}
