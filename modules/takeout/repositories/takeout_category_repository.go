/**
 * 外卖分类仓库
 *
 * 本文件实现了外卖分类相关的数据库操作，提供数据持久化和查询功能。
 * 负责takeout_category表的CRUD操作，支持多级分类查询。
 */

package repositories

import (
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutCategoryRepository 外卖分类仓库接口
type TakeoutCategoryRepository interface {
	// 基础CRUD
	Create(category *models.TakeoutCategory) (int64, error)
	GetByID(id int64) (*models.TakeoutCategory, error)
	GetByIDs(ids []int64) (map[int64]*models.TakeoutCategory, error)
	Update(category *models.TakeoutCategory) error
	Delete(id int64) error
	
	// 查询列表
	List(query *dto.TakeoutCategoryQueryRequest) ([]*models.TakeoutCategory, error)
	ListByParentID(merchantID, parentID int64) ([]*models.TakeoutCategory, error)
	ListByMerchantID(merchantID int64) ([]*models.TakeoutCategory, error)
	
	// 分类树
	GetCategoryTree(merchantID int64) ([]*models.TakeoutCategory, error)
	
	// 检查分类是否存在子分类
	HasChildren(categoryID int64) (bool, error)
	
	// 检查分类是否存在商品
	HasProducts(categoryID int64) (bool, error)
}

// takeoutCategoryRepository 外卖分类仓库实现
type takeoutCategoryRepository struct {
	ormer orm.Ormer
}

// NewTakeoutCategoryRepository 创建外卖分类仓库实例
func NewTakeoutCategoryRepository() TakeoutCategoryRepository {
	return &takeoutCategoryRepository{
		ormer: orm.NewOrm(),
	}
}

// Create 创建外卖分类
func (r *takeoutCategoryRepository) Create(category *models.TakeoutCategory) (int64, error) {
	category.CreatedAt = time.Now()
	category.UpdatedAt = time.Now()
	return r.ormer.Insert(category)
}

// GetByID 根据ID获取外卖分类
func (r *takeoutCategoryRepository) GetByID(id int64) (*models.TakeoutCategory, error) {
	category := &models.TakeoutCategory{ID: id}
	err := r.ormer.Read(category)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("分类不存在")
		}
		return nil, err
	}
	return category, nil
}

// GetByIDs 批量根据ID获取外卖分类
// 返回map[categoryID]*TakeoutCategory的形式，避免N+1查询问题
func (r *takeoutCategoryRepository) GetByIDs(ids []int64) (map[int64]*models.TakeoutCategory, error) {
	if len(ids) == 0 {
		return make(map[int64]*models.TakeoutCategory), nil
	}

	var categories []*models.TakeoutCategory
	_, err := r.ormer.QueryTable(new(models.TakeoutCategory)).Filter("id__in", ids).All(&categories)
	if err != nil {
		return nil, err
	}

	// 转换为map格式
	categoryMap := make(map[int64]*models.TakeoutCategory)
	for _, category := range categories {
		categoryMap[category.ID] = category
	}

	return categoryMap, nil
}

// Update 更新外卖分类
func (r *takeoutCategoryRepository) Update(category *models.TakeoutCategory) error {
	category.UpdatedAt = time.Now()
	_, err := r.ormer.Update(category)
	return err
}

// Delete 删除外卖分类
func (r *takeoutCategoryRepository) Delete(id int64) error {
	// 检查是否有子分类
	hasChildren, err := r.HasChildren(id)
	if err != nil {
		return err
	}
	
	if hasChildren {
		return errors.New("该分类下存在子分类，无法删除")
	}
	
	// 检查是否有关联的商品
	hasProducts, err := r.HasProducts(id)
	if err != nil {
		return err
	}
	
	if hasProducts {
		return errors.New("该分类下存在商品，无法删除")
	}
	
	category := &models.TakeoutCategory{ID: id}
	_, err = r.ormer.Delete(category)
	return err
}

// List 查询外卖分类列表
func (r *takeoutCategoryRepository) List(query *dto.TakeoutCategoryQueryRequest) ([]*models.TakeoutCategory, error) {
	var categories []*models.TakeoutCategory
	
	qs := r.ormer.QueryTable(new(models.TakeoutCategory))
	
	if query.MerchantID > 0 {
		qs = qs.Filter("merchant_id", query.MerchantID)
	}
	
	if query.ParentID >= 0 {
		qs = qs.Filter("parent_id", query.ParentID)
	}
	
	if query.Level > 0 {
		qs = qs.Filter("level", query.Level)
	}
	
	if !query.IncludeHidden {
		qs = qs.Filter("is_visible", true)
	}
	
	_, err := qs.OrderBy("sort_order", "id").All(&categories)
	
	return categories, err
}

// ListByParentID 根据父级ID查询分类列表
func (r *takeoutCategoryRepository) ListByParentID(merchantID, parentID int64) ([]*models.TakeoutCategory, error) {
	var categories []*models.TakeoutCategory
	
	_, err := r.ormer.QueryTable(new(models.TakeoutCategory)).
		Filter("merchant_id", merchantID).
		Filter("parent_id", parentID).
		Filter("is_visible", true).
		OrderBy("sort_order", "id").
		All(&categories)
	
	return categories, err
}

// ListByMerchantID 根据商家ID查询分类列表
func (r *takeoutCategoryRepository) ListByMerchantID(merchantID int64) ([]*models.TakeoutCategory, error) {
	var categories []*models.TakeoutCategory
	
	_, err := r.ormer.QueryTable(new(models.TakeoutCategory)).
		Filter("merchant_id", merchantID).
		Filter("is_visible", true).
		OrderBy("sort_order", "id").
		All(&categories)
	
	return categories, err
}

// GetCategoryTree 获取完整分类树
func (r *takeoutCategoryRepository) GetCategoryTree(merchantID int64) ([]*models.TakeoutCategory, error) {
	// 获取所有分类
	var allCategories []*models.TakeoutCategory
	
	_, err := r.ormer.QueryTable(new(models.TakeoutCategory)).
		Filter("merchant_id", merchantID).
		OrderBy("sort_order", "id").
		All(&allCategories)
	
	if err != nil {
		return nil, err
	}
	
	// 先构建树形结构
	treeCats := buildCategoryTree(allCategories, 0)
	
	// 然后转换成所需的平面数组结构
	return convertToTakeoutCategory(treeCats), nil
}

// CategoryTree 树形分类展示结构
type CategoryTree struct {
	*models.TakeoutCategory
	Children []*CategoryTree `json:"children,omitempty"`
}

// buildCategoryTree 构建分类树的辅助函数
func buildCategoryTree(categories []*models.TakeoutCategory, parentID int64) []*CategoryTree {
	var result []*CategoryTree
	
	for _, category := range categories {
		if category.ParentID == parentID {
			// 递归获取子分类
			children := buildCategoryTree(categories, category.ID)
			
			// 创建带子分类的树形结构
			categoryNode := &CategoryTree{
				TakeoutCategory: category,
				Children:        children,
			}
			
			result = append(result, categoryNode)
		}
	}
	
	return result
}

// convertToTakeoutCategory 将树形结构转换为TakeoutCategory数组
func convertToTakeoutCategory(categoryTrees []*CategoryTree) []*models.TakeoutCategory {
	result := make([]*models.TakeoutCategory, 0, len(categoryTrees))
	
	for _, tree := range categoryTrees {
		// 创建副本避免引用问题
		category := *tree.TakeoutCategory
		result = append(result, &category)
		
		// 递归处理子分类
		if len(tree.Children) > 0 {
			childCategories := convertToTakeoutCategory(tree.Children)
			result = append(result, childCategories...)
		}
	}
	
	return result
}

// HasChildren 检查分类是否存在子分类
func (r *takeoutCategoryRepository) HasChildren(categoryID int64) (bool, error) {
	count, err := r.ormer.QueryTable(new(models.TakeoutCategory)).
		Filter("parent_id", categoryID).
		Count()
	
	if err != nil {
		return false, err
	}
	
	return count > 0, nil
}

// HasProducts 检查分类是否存在商品
func (r *takeoutCategoryRepository) HasProducts(categoryID int64) (bool, error) {
	count, err := r.ormer.QueryTable("takeout_food").
		Filter("category_id", categoryID).
		Count()
	
	if err != nil {
		return false, err
	}
	
	return count > 0, nil
}
