/**
 * 外卖套餐组件和选项仓库
 *
 * 本文件实现了外卖套餐组件和选项相关的数据库操作，提供数据持久化和查询功能。
 * 负责takeout_combo_item和takeout_combo_option表的CRUD操作。
 */

package repositories

import (
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutComboRepository 外卖套餐仓库接口
type TakeoutComboRepository interface {
	// 套餐组件CRUD
	CreateComboItem(item *models.TakeoutComboItem) (int64, error)
	GetComboItemByID(id int64) (*models.TakeoutComboItem, error)
	UpdateComboItem(item *models.TakeoutComboItem) error
	DeleteComboItem(id int64) error
	ListComboItemsByFoodID(foodID int64) ([]*models.TakeoutComboItem, error)
	
	// 套餐选项CRUD
	CreateComboOption(option *models.TakeoutComboOption) (int64, error)
	GetComboOptionByID(id int64) (*models.TakeoutComboOption, error)
	UpdateComboOption(option *models.TakeoutComboOption) error
	DeleteComboOption(id int64) error
	ListComboOptionsByItemID(itemID int64) ([]*models.TakeoutComboOption, error)
	
	// 批量操作
	BatchGetComboOptionsByIDs(ids []int64) ([]*models.TakeoutComboOption, error)
	BatchUpdateOptionStock(items map[int64]int) error
}

// takeoutComboRepository 外卖套餐仓库实现
type takeoutComboRepository struct {
	ormer orm.Ormer
}

// NewTakeoutComboRepository 创建外卖套餐仓库实例
func NewTakeoutComboRepository() TakeoutComboRepository {
	return &takeoutComboRepository{
		ormer: orm.NewOrm(),
	}
}

// CreateComboItem 创建套餐组件
func (r *takeoutComboRepository) CreateComboItem(item *models.TakeoutComboItem) (int64, error) {
	item.CreatedAt = time.Now()
	item.UpdatedAt = time.Now()
	return r.ormer.Insert(item)
}

// GetComboItemByID 根据ID获取套餐组件
func (r *takeoutComboRepository) GetComboItemByID(id int64) (*models.TakeoutComboItem, error) {
	item := &models.TakeoutComboItem{ID: id}
	err := r.ormer.Read(item)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("套餐组件不存在")
		}
		return nil, err
	}
	return item, nil
}

// UpdateComboItem 更新套餐组件
func (r *takeoutComboRepository) UpdateComboItem(item *models.TakeoutComboItem) error {
	item.UpdatedAt = time.Now()
	_, err := r.ormer.Update(item)
	return err
}

// DeleteComboItem 删除套餐组件
func (r *takeoutComboRepository) DeleteComboItem(id int64) error {
	// 首先检查是否有关联的选项
	count, err := r.ormer.QueryTable(new(models.TakeoutComboOption)).
		Filter("combo_item_id", id).
		Count()
	
	if err != nil {
		return err
	}
	
	if count > 0 {
		return errors.New("该套餐组件下存在选项，请先删除选项")
	}
	
	item := &models.TakeoutComboItem{ID: id}
	_, err = r.ormer.Delete(item)
	return err
}

// ListComboItemsByFoodID 根据食品ID查询套餐组件列表
func (r *takeoutComboRepository) ListComboItemsByFoodID(foodID int64) ([]*models.TakeoutComboItem, error) {
	var items []*models.TakeoutComboItem
	
	_, err := r.ormer.QueryTable(new(models.TakeoutComboItem)).
		Filter("food_id", foodID).
		OrderBy("sort_order", "id").
		All(&items)
	
	return items, err
}

// CreateComboOption 创建套餐选项
func (r *takeoutComboRepository) CreateComboOption(option *models.TakeoutComboOption) (int64, error) {
	option.CreatedAt = time.Now()
	option.UpdatedAt = time.Now()
	return r.ormer.Insert(option)
}

// GetComboOptionByID 根据ID获取套餐选项
func (r *takeoutComboRepository) GetComboOptionByID(id int64) (*models.TakeoutComboOption, error) {
	option := &models.TakeoutComboOption{ID: id}
	err := r.ormer.Read(option)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("套餐选项不存在")
		}
		return nil, err
	}
	return option, nil
}

// UpdateComboOption 更新套餐选项
func (r *takeoutComboRepository) UpdateComboOption(option *models.TakeoutComboOption) error {
	option.UpdatedAt = time.Now()
	_, err := r.ormer.Update(option)
	return err
}

// DeleteComboOption 删除套餐选项
func (r *takeoutComboRepository) DeleteComboOption(id int64) error {
	option := &models.TakeoutComboOption{ID: id}
	_, err := r.ormer.Delete(option)
	return err
}

// ListComboOptionsByItemID 根据组件ID查询套餐选项列表
func (r *takeoutComboRepository) ListComboOptionsByItemID(itemID int64) ([]*models.TakeoutComboOption, error) {
	var options []*models.TakeoutComboOption
	
	_, err := r.ormer.QueryTable(new(models.TakeoutComboOption)).
		Filter("combo_item_id", itemID).
		OrderBy("sort_order", "id").
		All(&options)
	
	return options, err
}

// BatchGetComboOptionsByIDs 批量获取套餐选项
func (r *takeoutComboRepository) BatchGetComboOptionsByIDs(ids []int64) ([]*models.TakeoutComboOption, error) {
	var options []*models.TakeoutComboOption
	
	if len(ids) == 0 {
		return options, nil
	}
	
	_, err := r.ormer.QueryTable(new(models.TakeoutComboOption)).
		Filter("id__in", ids).
		All(&options)
	
	return options, err
}

// BatchUpdateOptionStock 批量更新选项库存
func (r *takeoutComboRepository) BatchUpdateOptionStock(items map[int64]int) error {
	if len(items) == 0 {
		return nil
	}
	
	for id, count := range items {
		if count > 0 {
			_, err := r.ormer.Raw("UPDATE takeout_combo_option SET sold_count = sold_count + ?, updated_at = ? WHERE id = ?", 
				count, time.Now(), id).Exec()
			if err != nil {
				return err
			}
		} else {
			// 退货，只处理销量
			_, err := r.ormer.Raw("UPDATE takeout_combo_option SET sold_count = GREATEST(0, sold_count + ?), updated_at = ? WHERE id = ?", 
				count, time.Now(), id).Exec()
			if err != nil {
				return err
			}
		}
	}
	
	return nil
}
