/**
 * 外卖模块 - 优惠券仓储接口
 * 描述：定义优惠券的数据访问层接口和实现
 * 作者：系统
 * 创建时间：2025-05-14
 */

package repositories

import (
	"errors"
	"o_mall_backend/modules/takeout/models"
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// ITakeoutCouponRepository 优惠券仓储接口
type ITakeoutCouponRepository interface {
	// 创建优惠券
	Create(coupon *models.TakeoutCoupon) (int64, error)

	// 更新优惠券
	Update(coupon *models.TakeoutCoupon) error

	// 根据ID获取优惠券
	GetByID(id int64) (*models.TakeoutCoupon, error)

	// 获取商户的优惠券列表
	GetByMerchantID(merchantID int64, page, pageSize int) ([]*models.TakeoutCoupon, int64, error)

	// 获取特定类型的优惠券列表
	GetByCouponType(couponType int, page, pageSize int) ([]*models.TakeoutCoupon, int64, error)

	// 获取当前有效的优惠券列表
	GetValidCoupons(merchantID int64) ([]*models.TakeoutCoupon, error)

	// 获取当日发放数量
	GetDailyIssueCount(couponID int64) (int64, error)

	// 更新优惠券状态
	UpdateStatus(id int64, status int) error

	// 增加已发放数量
	IncrementIssuedCount(id int64) error

	// 原子性增加已发放数量（带限制检查）
	IncrementIssuedCountWithLimit(id int64, totalLimit int) error

	// FindAvailableCoupons 获取可用的优惠券列表（用于优惠券中心）
	FindAvailableCoupons(category string) ([]models.TakeoutCoupon, error)
}

// TakeoutCouponRepository 优惠券仓储实现
type TakeoutCouponRepository struct {
}

// NewTakeoutCouponRepository 创建优惠券仓储
func NewTakeoutCouponRepository() ITakeoutCouponRepository {
	return &TakeoutCouponRepository{}
}

// Create 创建优惠券
func (r *TakeoutCouponRepository) Create(coupon *models.TakeoutCoupon) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(coupon)
	return id, err
}

// Update 更新优惠券
func (r *TakeoutCouponRepository) Update(coupon *models.TakeoutCoupon) error {
	o := orm.NewOrm()
	_, err := o.Update(coupon)
	return err
}

// GetByID 根据ID获取优惠券
func (r *TakeoutCouponRepository) GetByID(id int64) (*models.TakeoutCoupon, error) {
	o := orm.NewOrm()
	coupon := models.TakeoutCoupon{ID: id}
	err := o.Read(&coupon)
	if err != nil {
		return nil, err
	}
	return &coupon, nil
}

// GetByMerchantID 获取商户的优惠券列表
func (r *TakeoutCouponRepository) GetByMerchantID(merchantID int64, page, pageSize int) ([]*models.TakeoutCoupon, int64, error) {
	o := orm.NewOrm()
	var coupons []*models.TakeoutCoupon
	qs := o.QueryTable(new(models.TakeoutCoupon)).Filter("merchant_id", merchantID)

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&coupons)
	if err != nil {
		return nil, 0, err
	}

	return coupons, total, nil
}

// GetByCouponType 获取特定类型的优惠券列表
func (r *TakeoutCouponRepository) GetByCouponType(couponType int, page, pageSize int) ([]*models.TakeoutCoupon, int64, error) {
	o := orm.NewOrm()
	var coupons []*models.TakeoutCoupon
	qs := o.QueryTable(new(models.TakeoutCoupon)).Filter("type", couponType)

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&coupons)
	if err != nil {
		return nil, 0, err
	}

	return coupons, total, nil
}

// GetValidCoupons 获取当前有效的优惠券列表
func (r *TakeoutCouponRepository) GetValidCoupons(merchantID int64) ([]*models.TakeoutCoupon, error) {
	o := orm.NewOrm()
	var coupons []*models.TakeoutCoupon
	now := time.Now()

	// 过滤状态：排除已使用(2)的优惠券，只获取未使用(1)的优惠券
	qs := o.QueryTable(new(models.TakeoutCoupon)).Filter("status", models.CouponStatusUnused)
	if merchantID > 0 {
		qs = qs.Filter("merchant_id", merchantID)
	}

	_, err := qs.Filter("start_time__lte", now).Filter("end_time__gte", now).All(&coupons)
	if err != nil {
		return nil, err
	}

	return coupons, nil
}

// GetDailyIssueCount 获取当日发放数量
func (r *TakeoutCouponRepository) GetDailyIssueCount(couponID int64) (int64, error) {
	o := orm.NewOrm()
	now := time.Now()
	start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	end := start.AddDate(0, 0, 1)

	count, err := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("coupon_id", couponID).Filter("created_at__gte", start).Filter("created_at__lt", end).Count()
	return count, err
}

// UpdateStatus 更新优惠券状态
func (r *TakeoutCouponRepository) UpdateStatus(id int64, status int) error {
	o := orm.NewOrm()
	_, err := o.QueryTable(new(models.TakeoutCoupon)).Filter("id", id).Update(orm.Params{
		"status": status,
	})
	return err
}

// FindAvailableCoupons 获取可用的优惠券列表（用于优惠券中心）
func (r *TakeoutCouponRepository) FindAvailableCoupons(category string) ([]models.TakeoutCoupon, error) {
	o := orm.NewOrm()
	now := time.Now()
	var coupons []models.TakeoutCoupon

	qs := o.QueryTable(new(models.TakeoutCoupon)).
		Filter("status", models.CouponStatusUnused). // 状态为已发布（可领取）
		Filter("start_time__lte", now).              // 开始时间 <= 当前时间
		Filter("end_time__gte", now)                 // 结束时间 >= 当前时间

	if category != "" && category != "all" {
		qs = qs.Filter("apply_to_categories__icontains", category)
	}

	// 添加排序，按创建时间倒序
	qs = qs.OrderBy("-created_at")

	_, err := qs.All(&coupons)
	if err != nil {
		return nil, err
	}

	// 添加调试日志
	logs := orm.NewOrm().Driver().Name()
	if logs != "" { // 简单的日志检查
		// 这里可以添加更详细的日志，但为了避免循环依赖，暂时简化
	}

	return coupons, nil
}

// IncrementIssuedCount 增加已发放数量
func (r *TakeoutCouponRepository) IncrementIssuedCount(id int64) error {
	o := orm.NewOrm()

	// 使用原子操作更新已发放数量
	_, err := o.Raw("UPDATE takeout_coupon SET issued_count = issued_count + 1 WHERE id = ?", id).Exec()
	if err != nil {
		return err
	}

	return nil
}

// IncrementIssuedCountWithLimit 原子性增加已发放数量（带限制检查）
func (r *TakeoutCouponRepository) IncrementIssuedCountWithLimit(id int64, totalLimit int) error {
	o := orm.NewOrm()

	// 使用原子操作更新已发放数量，同时检查限制
	// 只有在当前已发放数量小于总限制时才更新
	result, err := o.Raw("UPDATE takeout_coupon SET issued_count = issued_count + 1 WHERE id = ? AND (total_limit = 0 OR issued_count < total_limit)", id).Exec()
	if err != nil {
		return err
	}

	// 检查是否有行被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return errors.New("优惠券已经领完")
	}

	return nil
}
