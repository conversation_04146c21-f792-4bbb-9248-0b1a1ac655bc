/**
 * 外卖模块 - 商品促销关联仓储接口
 * 描述：定义商品与促销活动关联的数据访问层接口和实现
 * 作者：系统
 * 创建时间：2025-05-14
 */

package repositories

import (
	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/takeout/models"
)

// ITakeoutFoodPromotionRepository 商品促销关联仓储接口
type ITakeoutFoodPromotionRepository interface {
	// 创建商品促销关联
	Create(foodPromotion *models.TakeoutFoodPromotion) (int64, error)
	
	// 更新商品促销关联
	Update(foodPromotion *models.TakeoutFoodPromotion) error
	
	// 删除商品促销关联
	Delete(id int64) error
	
	// 根据ID获取商品促销关联
	GetByID(id int64) (*models.TakeoutFoodPromotion, error)
	
	// 根据商品ID获取促销关联
	GetByFoodID(foodID int64) ([]*models.TakeoutFoodPromotion, error)
	
	// 根据促销活动ID获取商品关联
	GetByPromotionID(promotionID int64) ([]*models.TakeoutFoodPromotion, error)
	
	// 根据商品ID和促销活动ID获取关联
	GetByFoodIDAndPromotionID(foodID int64, promotionID int64) (*models.TakeoutFoodPromotion, error)
	
	// 批量创建商品促销关联
	BatchCreate(foodPromotions []*models.TakeoutFoodPromotion) error
}

// TakeoutFoodPromotionRepository 商品促销关联仓储实现
type TakeoutFoodPromotionRepository struct {
}

// NewTakeoutFoodPromotionRepository 创建商品促销关联仓储
func NewTakeoutFoodPromotionRepository() ITakeoutFoodPromotionRepository {
	return &TakeoutFoodPromotionRepository{}
}

// Create 创建商品促销关联
func (r *TakeoutFoodPromotionRepository) Create(foodPromotion *models.TakeoutFoodPromotion) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(foodPromotion)
	return id, err
}

// Update 更新商品促销关联
func (r *TakeoutFoodPromotionRepository) Update(foodPromotion *models.TakeoutFoodPromotion) error {
	o := orm.NewOrm()
	_, err := o.Update(foodPromotion)
	return err
}

// Delete 删除商品促销关联
func (r *TakeoutFoodPromotionRepository) Delete(id int64) error {
	o := orm.NewOrm()
	_, err := o.Delete(&models.TakeoutFoodPromotion{ID: id})
	return err
}

// GetByID 根据ID获取商品促销关联
func (r *TakeoutFoodPromotionRepository) GetByID(id int64) (*models.TakeoutFoodPromotion, error) {
	o := orm.NewOrm()
	foodPromotion := models.TakeoutFoodPromotion{ID: id}
	err := o.Read(&foodPromotion)
	if err != nil {
		return nil, err
	}
	return &foodPromotion, nil
}

// GetByFoodID 根据商品ID获取促销关联
func (r *TakeoutFoodPromotionRepository) GetByFoodID(foodID int64) ([]*models.TakeoutFoodPromotion, error) {
	o := orm.NewOrm()
	var foodPromotions []*models.TakeoutFoodPromotion
	_, err := o.QueryTable(new(models.TakeoutFoodPromotion)).Filter("food_id", foodID).All(&foodPromotions)
	if err != nil {
		return nil, err
	}
	return foodPromotions, nil
}

// GetByPromotionID 根据促销活动ID获取商品关联
func (r *TakeoutFoodPromotionRepository) GetByPromotionID(promotionID int64) ([]*models.TakeoutFoodPromotion, error) {
	o := orm.NewOrm()
	var foodPromotions []*models.TakeoutFoodPromotion
	_, err := o.QueryTable(new(models.TakeoutFoodPromotion)).Filter("promotion_id", promotionID).All(&foodPromotions)
	if err != nil {
		return nil, err
	}
	return foodPromotions, nil
}

// GetByFoodIDAndPromotionID 根据商品ID和促销活动ID获取关联
func (r *TakeoutFoodPromotionRepository) GetByFoodIDAndPromotionID(foodID int64, promotionID int64) (*models.TakeoutFoodPromotion, error) {
	o := orm.NewOrm()
	foodPromotion := models.TakeoutFoodPromotion{FoodID: foodID, PromotionID: promotionID}
	err := o.Read(&foodPromotion, "food_id", "promotion_id")
	if err != nil {
		return nil, err
	}
	return &foodPromotion, nil
}

// BatchCreate 批量创建商品促销关联
func (r *TakeoutFoodPromotionRepository) BatchCreate(foodPromotions []*models.TakeoutFoodPromotion) error {
	o := orm.NewOrm()
	_, err := o.InsertMulti(len(foodPromotions), foodPromotions)
	return err
}
