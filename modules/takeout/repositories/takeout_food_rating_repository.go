/**
 * 外卖食品评分仓库
 *
 * 本文件实现了外卖食品评分相关的数据库操作，提供数据持久化和查询功能。
 * 扩展TakeoutFoodRepository接口，添加更新食品评分的功能。
 */

package repositories

import (
	//"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// UpdateFoodRating 更新食品评分
func (r *takeoutFoodRepository) UpdateFoodRating(foodID int64, rating int) error {
	// 获取食品信息
	food, err := r.GetByID(foodID)
	if err != nil {
		logs.Error("获取食品信息失败: %v, 食品ID: %d", err, foodID)
		return err
	}

	// 计算新的平均评分
	newRatingTotal := food.RatingTotal + rating
	newRatingCount := food.RatingCount + 1
	newRating := float64(newRatingTotal) / float64(newRatingCount)

	// 更新食品评分信息
	food.Rating = newRating
	food.RatingTotal = newRatingTotal
	food.RatingCount = newRatingCount

	// 保存更新
	return r.Update(food)
}
