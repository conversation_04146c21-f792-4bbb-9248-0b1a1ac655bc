/**
 * 外卖食品仓库
 *
 * 本文件实现了外卖食品相关的数据库操作，提供数据持久化和查询功能。
 * 负责takeout_food表的CRUD操作，支持复杂查询和统计。
 */

package repositories

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs" // 添加日志包
	//merchantModels "o_mall_backend/modules/merchant/models"
	merchantRepos "o_mall_backend/modules/merchant/repositories"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutFoodRepository 外卖食品仓库接口
type TakeoutFoodRepository interface {
	// 基础CRUD
	Create(food *models.TakeoutFood) (int64, error)
	GetByID(id int64) (*models.TakeoutFood, error)
	GetFoodByID(id int64) (*models.TakeoutFood, error) // 增加 GetFoodByID 方法
	Update(food *models.TakeoutFood) error
	Delete(id int64) error

	// 列表查询和统计
	List(query *dto.TakeoutFoodQueryRequest) ([]*models.TakeoutFood, int64, error)
	CountByCategory(categoryID int64) (int, error)
	CountByCategoryBatch(categoryIDs []int64) (map[int64]int, error)
	CountByMerchant(merchantID int64) (int, error)

	// 业务操作
	UpdateSoldCount(id int64, count int) error
	UpdateStatus(id int64, status int) error
	UpdateSoldOutStatus(id int64, soldOut bool) error
	IncrementViewCount(id int64) error

	// 批量操作
	BatchGetByIDs(ids []int64) ([]*models.TakeoutFood, error)
	BatchUpdateStock(items map[int64]int) error

	// 商户仪表板统计
	CountLowStockFoodsByMerchant(merchantID int64, threshold int) (int, error)
	CountLowStockFoodsByMerchantDefault(merchantID int64) (int, error)

	// 商户食品审核状态统计
	CountFoodsByMerchant(merchantID int64, status int) (int64, error)
	CountFoodsByMerchantAndAuditStatus(merchantID int64, auditStatus int) (int64, error)
	// 商家商品统计方法
	CountByMerchantID(merchantID int64) (int, error)

	// GetByIDWithMerchant 根据ID获取外卖食品信息（包含商家名称）
	GetByIDWithMerchant(id int64) (*models.TakeoutFood, error)
	CountByMerchantIDAndStatus(merchantID int64, status int, soldOut bool) (int, error)
	CountByMerchantIDAndAuditStatus(merchantID int64, auditStatus int) (int, error)
	CountByMerchantIDAndSoldOut(merchantID int64, soldOut bool) (int, error)
}

// takeoutFoodRepository 外卖食品仓库实现
type takeoutFoodRepository struct {
	ormer        orm.Ormer
	merchantRepo merchantRepos.MerchantRepository
}

// NewTakeoutFoodRepository 创建外卖食品仓库实例
func NewTakeoutFoodRepository() TakeoutFoodRepository {
	return &takeoutFoodRepository{
		ormer:        orm.NewOrm(),
		merchantRepo: merchantRepos.NewMerchantRepository(),
	}
}

// Create 创建外卖食品
func (r *takeoutFoodRepository) Create(food *models.TakeoutFood) (int64, error) {
	food.CreatedAt = time.Now()
	food.UpdatedAt = time.Now()
	return r.ormer.Insert(food)
}

// GetByID 根据ID获取外卖食品
func (r *takeoutFoodRepository) GetByID(id int64) (*models.TakeoutFood, error) {
	food := &models.TakeoutFood{ID: id}
	err := r.ormer.Read(food)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("食品不存在")
		}
		return nil, err
	}
	return food, nil
}

// GetFoodByID 根据ID获取外卖食品
func (r *takeoutFoodRepository) GetFoodByID(id int64) (*models.TakeoutFood, error) {
	return r.GetByID(id)
}

// GetByIDWithMerchant 根据ID获取外卖食品信息（包含商家名称、经纬度）
func (r *takeoutFoodRepository) GetByIDWithMerchant(id int64) (*models.TakeoutFood, error) {
	// 首先获取食品信息
	food := &models.TakeoutFood{ID: id}
	err := r.ormer.Read(food)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("食品不存在")
		}
		return nil, err
	}

	// 获取商家信息
	merchant, err := r.merchantRepo.GetByID(context.Background(), food.MerchantID)
	if err != nil {
		// 如果获取商家信息失败，记录错误但仍返回食品信息
		// 这里可以根据业务需求决定是否返回错误
		return food, nil
	}

	// 设置商家名称和经纬度信息
	if merchant != nil {
		food.MerchantName = merchant.Name
		food.MerchantLongitude = merchant.Longitude
		food.MerchantLatitude = merchant.Latitude
	}

	return food, nil
}

// Update 更新外卖食品
func (r *takeoutFoodRepository) Update(food *models.TakeoutFood) error {
	food.UpdatedAt = time.Now()
	// 明确指定要更新的字段，确保所有字段都能正确更新
	_, err := r.ormer.Update(food, "name", "description", "brief", "image", "price", "original_price", "packaging_fee", "preparation_time", "sold_out", "daily_limit", "tags", "keywords", "status", "audit_status", "is_recommend", "sort_order", "category_id", "global_category_id", "updated_at")
	return err
}

// Delete 删除外卖食品
func (r *takeoutFoodRepository) Delete(id int64) error {
	food := &models.TakeoutFood{ID: id}
	_, err := r.ormer.Delete(food)
	return err
}

// List 查询外卖食品列表
func (r *takeoutFoodRepository) List(query *dto.TakeoutFoodQueryRequest) ([]*models.TakeoutFood, int64, error) {
	var foods []*models.TakeoutFood

	// 构建查询条件
	qb, _ := orm.NewQueryBuilder("mysql")
	qb.Select("*").From("takeout_food")

	// 添加查询条件
	conditions := []string{}
	params := []interface{}{}

	if query.MerchantID > 0 {
		conditions = append(conditions, "merchant_id = ?")
		params = append(params, query.MerchantID)
	}

	if query.CategoryID > 0 {
		conditions = append(conditions, "category_id = ?")
		params = append(params, query.CategoryID)
	}

	// 只有当状态值大于等于0时才添加状态过滤条件（Status=-1表示不过滤状态）
	if query.Status >= 0 {
		conditions = append(conditions, "status = ?")
		params = append(params, query.Status)
		logs.Info("[DEBUG] 添加状态过滤条件: status = %d", query.Status)
	} else {
		logs.Info("[DEBUG] 状态值为 %d，不添加状态过滤条件", query.Status)
	}

	// 添加审核状态过滤条件（AuditStatus=-1表示不过滤审核状态）
	if query.AuditStatus >= 0 {
		conditions = append(conditions, "audit_status = ?")
		params = append(params, query.AuditStatus)
		logs.Info("[DEBUG] 添加审核状态过滤条件: audit_status = %d", query.AuditStatus)
	} else {
		logs.Info("[DEBUG] 审核状态值为 %d，不添加审核状态过滤条件", query.AuditStatus)
	}

	if query.Keyword != "" {
		conditions = append(conditions, "(name LIKE ? OR keywords LIKE ? OR description LIKE ?)")
		keyword := "%" + query.Keyword + "%"
		params = append(params, keyword, keyword, keyword)
	}

	if query.IsRecommend {
		conditions = append(conditions, "is_recommend = 1")
	}

	if query.IsCombination {
		conditions = append(conditions, "is_combination = 1")
	}

	if query.HasVariants {
		conditions = append(conditions, "has_variants = 1")
	}

	if query.IsSpicy {
		conditions = append(conditions, "is_spicy = 1")
	}

	if query.PriceMin > 0 {
		conditions = append(conditions, "price >= ?")
		params = append(params, query.PriceMin)
	}

	if query.PriceMax > 0 {
		conditions = append(conditions, "price <= ?")
		params = append(params, query.PriceMax)
	}

	// 组合查询条件
	if len(conditions) > 0 {
		qb.Where(strings.Join(conditions, " AND "))
	}

	// 打印所有查询条件和参数
	logs.Info("[SQL DEBUG] 查询条件: %v", conditions)
	logs.Info("[SQL DEBUG] 查询参数: %v", params)

	// 排序
	sortField := "sort_order"
	sortOrder := "ASC"

	if query.SortBy != "" {
		validSortFields := map[string]bool{
			"price": true, "created_at": true, "total_sold": true, "sort_order": true,
		}
		if validSortFields[query.SortBy] {
			sortField = query.SortBy
		}
	}

	if query.SortOrder != "" && (query.SortOrder == "DESC" || query.SortOrder == "desc") {
		sortOrder = "DESC"
	}

	qb.OrderBy(sortField + " " + sortOrder)

	// 分页
	page := query.Page
	if page <= 0 {
		page = 1
	}

	pageSize := query.PageSize
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	qb.Limit(pageSize).Offset((page - 1) * pageSize)

	// 执行查询
	sql := qb.String()

	// 打印完整SQL查询语句和参数
	logs.Info("[SQL DEBUG] 执行查询SQL: %s", sql)
	logs.Info("[SQL DEBUG] 查询参数: %v", params)

	_, err := r.ormer.Raw(sql, params...).QueryRows(&foods)
	if err != nil {
		return nil, 0, err
	}

	// 打印查询结果数量
	logs.Info("[SQL DEBUG] 查询结果: 找到 %d 条食品记录", len(foods))

	// 查询总数
	countSql := "SELECT COUNT(*) FROM takeout_food"
	if len(conditions) > 0 {
		countSql += " WHERE " + strings.Join(conditions, " AND ")
	}

	// 打印总数查询SQL和参数
	logs.Info("[SQL DEBUG] 总数查询SQL: %s", countSql)
	logs.Info("[SQL DEBUG] 总数查询参数: %v", params)

	var count int64
	err = r.ormer.Raw(countSql, params...).QueryRow(&count)
	if err != nil {
		return nil, 0, err
	}

	// 打印总数查询结果
	logs.Info("[SQL DEBUG] 总数查询结果: 总计 %d 条记录", count)

	return foods, count, nil
}

// CountByCategory 统计分类下食品数量
func (r *takeoutFoodRepository) CountByCategory(categoryID int64) (int, error) {
	var count int
	err := r.ormer.Raw("SELECT COUNT(*) FROM takeout_food WHERE category_id = ?", categoryID).QueryRow(&count)
	return count, err
}

// CountByCategoryBatch 批量统计多个分类下的食品数量
// 返回map[categoryID]count的形式，避免N+1查询问题
func (r *takeoutFoodRepository) CountByCategoryBatch(categoryIDs []int64) (map[int64]int, error) {
	if len(categoryIDs) == 0 {
		return make(map[int64]int), nil
	}

	// 构建IN查询的占位符
	placeholders := make([]string, len(categoryIDs))
	params := make([]interface{}, len(categoryIDs))
	for i, id := range categoryIDs {
		placeholders[i] = "?"
		params[i] = id
	}

	// 执行批量查询
	sql := "SELECT category_id, COUNT(*) as count FROM takeout_food WHERE category_id IN (" + strings.Join(placeholders, ",") + ") GROUP BY category_id"
	
	type CategoryCount struct {
		CategoryID int64 `orm:"column(category_id)"`
		Count      int   `orm:"column(count)"`
	}
	
	var results []CategoryCount
	_, err := r.ormer.Raw(sql, params...).QueryRows(&results)
	if err != nil {
		return nil, err
	}

	// 转换为map格式
	countMap := make(map[int64]int)
	for _, result := range results {
		countMap[result.CategoryID] = result.Count
	}

	// 确保所有请求的分类ID都有对应的计数（没有食品的分类计数为0）
	for _, categoryID := range categoryIDs {
		if _, exists := countMap[categoryID]; !exists {
			countMap[categoryID] = 0
		}
	}

	return countMap, nil
}

// CountByMerchant 统计商家食品数量
func (r *takeoutFoodRepository) CountByMerchant(merchantID int64) (int, error) {
	var count int
	err := r.ormer.Raw("SELECT COUNT(*) FROM takeout_food WHERE merchant_id = ?", merchantID).QueryRow(&count)
	return count, err
}

// UpdateSoldCount 更新售出数量
func (r *takeoutFoodRepository) UpdateSoldCount(id int64, count int) error {
	_, err := r.ormer.Raw("UPDATE takeout_food SET sold_count = sold_count + ?, total_sold = total_sold + ?, updated_at = ? WHERE id = ?",
		count, count, time.Now(), id).Exec()
	return err
}

// UpdateStatus 更新状态
func (r *takeoutFoodRepository) UpdateStatus(id int64, status int) error {
	_, err := r.ormer.Raw("UPDATE takeout_food SET status = ?, updated_at = ? WHERE id = ?",
		status, time.Now(), id).Exec()
	return err
}

// UpdateSoldOutStatus 更新售罄状态
func (r *takeoutFoodRepository) UpdateSoldOutStatus(id int64, soldOut bool) error {
	var soldOutValue int
	if soldOut {
		soldOutValue = 1
	} else {
		soldOutValue = 0
	}

	_, err := r.ormer.Raw("UPDATE takeout_food SET sold_out = ?, updated_at = ? WHERE id = ?",
		soldOutValue, time.Now(), id).Exec()
	return err
}

// IncrementViewCount 增加浏览次数
func (r *takeoutFoodRepository) IncrementViewCount(id int64) error {
	_, err := r.ormer.Raw("UPDATE takeout_food SET view_count = view_count + 1 WHERE id = ?", id).Exec()
	return err
}

// BatchGetByIDs 批量获取食品信息
func (r *takeoutFoodRepository) BatchGetByIDs(ids []int64) ([]*models.TakeoutFood, error) {
	if len(ids) == 0 {
		return []*models.TakeoutFood{}, nil
	}

	var foods []*models.TakeoutFood
	marks := make([]string, len(ids))
	params := make([]interface{}, len(ids))

	for i, id := range ids {
		marks[i] = "?"
		params[i] = id
	}

	sql := "SELECT * FROM takeout_food WHERE id IN (" + strings.Join(marks, ",") + ")"
	_, err := r.ormer.Raw(sql, params...).QueryRows(&foods)

	return foods, err
}

// BatchUpdateStock 批量更新库存
func (r *takeoutFoodRepository) BatchUpdateStock(items map[int64]int) error {
	tx, err := r.ormer.Begin()
	if err != nil {
		return err
	}

	for foodID, stockChange := range items {
		// 更新库存
		sql := "UPDATE takeout_food SET stock = stock + ?, updated_at = NOW() WHERE id = ?"
		_, err := tx.Raw(sql, stockChange, foodID).Exec()
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit()
}

// CountLowStockFoodsByMerchant 统计商户低库存商品数
func (r *takeoutFoodRepository) CountLowStockFoodsByMerchant(merchantID int64, threshold int) (int, error) {
	var count int

	// 构建查询SQL
	// 注意：根据错误日志，takeout_food表可能没有stock字段，使用inventory字段代替
	sql := "SELECT COUNT(*) FROM takeout_food WHERE merchant_id = ? AND status = 1 AND has_variants = 0 AND inventory < ?"

	// 执行查询
	err := r.ormer.Raw(sql, merchantID, threshold).QueryRow(&count)
	if err != nil {
		// 如果查询出错，可能是字段不存在，返回0
		return 0, nil
	}

	return count, nil
}

// CountLowStockFoodsByMerchantDefault 统计商户低库存商品数（使用默认阈值）
func (r *takeoutFoodRepository) CountLowStockFoodsByMerchantDefault(merchantID int64) (int, error) {
	// 使用默认阈值10
	return r.CountLowStockFoodsByMerchant(merchantID, 10)
}

// CountFoodsByMerchant 根据商户ID和状态统计食品数量
func (r *takeoutFoodRepository) CountFoodsByMerchant(merchantID int64, status int) (int64, error) {
	var count int64

	// 构建查询SQL
	sql := "SELECT COUNT(*) FROM takeout_food WHERE merchant_id = ?"
	params := []interface{}{merchantID}

	// 如果指定了状态过滤条件，则添加状态条件
	if status >= 0 {
		sql += " AND status = ?"
		params = append(params, status)
	}

	// 执行查询
	err := r.ormer.Raw(sql, params...).QueryRow(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// CountFoodsByMerchantAndAuditStatus 根据商户ID和审核状态统计食品数量
func (r *takeoutFoodRepository) CountFoodsByMerchantAndAuditStatus(merchantID int64, auditStatus int) (int64, error) {
	var count int64

	// 构建查询SQL
	sql := "SELECT COUNT(*) FROM takeout_food WHERE merchant_id = ?"
	params := []interface{}{merchantID}

	// 如果指定了审核状态过滤条件，则添加审核状态条件
	if auditStatus >= 0 {
		sql += " AND audit_status = ?"
		params = append(params, auditStatus)
	}

	// 执行查询
	err := r.ormer.Raw(sql, params...).QueryRow(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}
