/**
 * 外卖食品仓库扩展方法
 *
 * 本文件实现了外卖食品仓库的扩展方法，提供统计相关的数据库操作。
 */

package repositories

import (
	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/models"
)

// CountByMerchantID 统计商家的商品总数
func (r *takeoutFoodRepository) CountByMerchantID(merchantID int64) (int, error) {
	count, err := r.ormer.QueryTable(new(models.TakeoutFood)).
		Filter("merchant_id", merchantID).
		Count()
	
	if err != nil {
		logs.Error("统计商家商品总数失败: %v, 商家ID: %d", err, merchantID)
		return 0, err
	}
	
	return int(count), nil
}

// CountByMerchantIDAndStatus 统计商家特定状态的商品数量
func (r *takeoutFoodRepository) CountByMerchantIDAndStatus(merchantID int64, status int, soldOut bool) (int, error) {
	qs := r.ormer.QueryTable(new(models.TakeoutFood)).
		Filter("merchant_id", merchantID).
		Filter("status", status).
		Filter("sold_out", soldOut)
	
	count, err := qs.Count()
	if err != nil {
		logs.Error("统计商家特定状态商品数量失败: %v, 商家ID: %d, 状态: %d", err, merchantID, status)
		return 0, err
	}
	
	return int(count), nil
}

// CountByMerchantIDAndAuditStatus 统计商家特定审核状态的商品数量
func (r *takeoutFoodRepository) CountByMerchantIDAndAuditStatus(merchantID int64, auditStatus int) (int, error) {
	qs := r.ormer.QueryTable(new(models.TakeoutFood)).
		Filter("merchant_id", merchantID).
		Filter("audit_status", auditStatus)
	
	count, err := qs.Count()
	if err != nil {
		logs.Error("统计商家特定审核状态商品数量失败: %v, 商家ID: %d, 审核状态: %d", err, merchantID, auditStatus)
		return 0, err
	}
	
	return int(count), nil
}

// CountByMerchantIDAndSoldOut 统计商家已售罄商品数量
func (r *takeoutFoodRepository) CountByMerchantIDAndSoldOut(merchantID int64, soldOut bool) (int, error) {
	qs := r.ormer.QueryTable(new(models.TakeoutFood)).
		Filter("merchant_id", merchantID).
		Filter("sold_out", soldOut)
	
	count, err := qs.Count()
	if err != nil {
		logs.Error("统计商家售罄商品数量失败: %v, 商家ID: %d", err, merchantID)
		return 0, err
	}
	
	return int(count), nil
}
