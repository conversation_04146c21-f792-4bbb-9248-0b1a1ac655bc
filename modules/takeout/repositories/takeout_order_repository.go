/**
 * 外卖订单仓库接口
 *
 * 本文件定义了外卖订单仓库的接口，包含外卖订单的基本CRUD操作和查询方法。
 * 该接口被服务层调用，用于访问数据库中的外卖订单信息。
 */

package repositories

import (
	"time"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutOrderRepository 外卖订单仓库接口
type TakeoutOrderRepository interface {
	// 订单扩展信息
	CreateOrderExtension(order *models.TakeoutOrderExtension) (int64, error)
	GetOrderExtensionByOrderID(orderID int64) (*models.TakeoutOrderExtension, error)
	UpdateOrderExtension(order *models.TakeoutOrderExtension) error

	// 订单查询
	QueryOrders(params map[string]interface{}) ([]*models.TakeoutOrderExtension, int64, error)
	GetByOrderID(orderID int64) (*models.TakeoutOrderExtension, error)
	Update(order *models.TakeoutOrderExtension) error

	// 订单日志
	CreateOrderLog(log *models.TakeoutOrderLog) (int64, error)
	GetOrderLogsByOrderID(orderID int64) ([]*models.TakeoutOrderLog, error)

	// 订单评价
	CreateOrderRating(rating *models.TakeoutOrderRating) (int64, error)
	GetOrderRatingByOrderID(orderID int64) (*models.TakeoutOrderRating, error)
	CheckOrderRated(orderID int64) (bool, error)
	UpdateOrderRated(orderID int64) error

	// 订单统计
	CountOrdersByDateRange(startDate, endDate time.Time, status int) (int, error)
	SumOrderAmountByDateRange(startDate, endDate time.Time) (float64, error)
	GetAverageDeliveryTimeByDateRange(startDate, endDate time.Time) (float64, error)
	GetDailyOrderCountsByDateRange(startDate, endDate time.Time) (map[string]int, error)
	GetHotFoodsByDateRange(startDate, endDate time.Time, limit int) ([]*dto.HotFoodStatisticsDTO, error)

	// 商户仪表板统计
	CountOrdersByMerchantAndDateRange(merchantID int64, startDate, endDate time.Time, status int) (int, error)
	SumOrderAmountByMerchantAndDateRange(merchantID int64, startDate, endDate time.Time) (float64, error)
	CountOrdersByMerchantAndStatus(merchantID int64, status int) (int, error)

	// 商户订单统计
	CountOrdersByMerchant(merchantID int64, status int) (int64, error)
	CountProcessingOrdersByMerchant(merchantID int64) (int64, error)

	// 管理员商家订单统计
	CountByMerchantID(merchantID int64) (int, error)
	CountByMerchantIDAndStatus(merchantID int64, status int) (int, error)
	CountByMerchantIDAndStatusIn(merchantID int64, statusList []int) (int, error)

	// 商户外卖统计新增方法
	GetTotalSalesByMerchantAndDateRange(merchantID int64, startDate, endDate time.Time) (float64, error)
	CountOrdersByMerchantAndDateRangeWithoutStatus(merchantID int64, startDate, endDate time.Time) (int, error)
	CountPendingOrdersByMerchant(merchantID int64) (int, error)
}
