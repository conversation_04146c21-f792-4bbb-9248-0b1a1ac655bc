/**
 * 外卖订单仓库扩展方法
 *
 * 本文件实现了外卖订单仓库的扩展方法，提供按商家统计订单数据相关的功能。
 */

package repositories

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/models"
)

// CountByMerchantID 统计商家订单总数
func (r *TakeoutOrderRepositoryImpl) CountByMerchantID(merchantID int64) (int, error) {
	o := orm.NewOrm()
	count, err := o.QueryTable(new(models.TakeoutOrderExtension)).
		Filter("merchant_id", merchantID).
		Count()
	
	if err != nil {
		logs.Error("统计商家订单总数失败: %v, 商家ID: %d", err, merchantID)
		return 0, err
	}
	
	return int(count), nil
}

// CountByMerchantIDAndStatus 统计商家特定状态的订单数量
func (r *TakeoutOrderRepositoryImpl) CountByMerchantIDAndStatus(merchantID int64, status int) (int, error) {
	o := orm.NewOrm()
	// 注意：TakeoutOrderExtension没有status字段，这里先统计总数
	qs := o.QueryTable(new(models.TakeoutOrderExtension)).
		Filter("merchant_id", merchantID)
	
	count, err := qs.Count()
	if err != nil {
		logs.Error("统计商家特定状态订单数量失败: %v, 商家ID: %d, 状态: %d", err, merchantID, status)
		return 0, err
	}
	
	return int(count), nil
}

// CountByMerchantIDAndStatusIn 统计商家多个状态的订单数量
func (r *TakeoutOrderRepositoryImpl) CountByMerchantIDAndStatusIn(merchantID int64, statusList []int) (int, error) {
	if len(statusList) == 0 {
		return 0, nil
	}
	
	o := orm.NewOrm()
	// 注意：TakeoutOrderExtension没有status字段，这里先统计总数
	qs := o.QueryTable(new(models.TakeoutOrderExtension)).
		Filter("merchant_id", merchantID)
	
	count, err := qs.Count()
	if err != nil {
		logs.Error("统计商家多状态订单数量失败: %v, 商家ID: %d", err, merchantID)
		return 0, err
	}
	
	return int(count), nil
}
