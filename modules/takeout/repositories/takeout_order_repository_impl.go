/**
 * 外卖订单仓库实现
 *
 * 本文件实现了外卖订单仓库接口，提供了数据库操作的具体实现。
 * 使用 Beego ORM 进行数据库操作，实现了订单扩展信息、订单日志、订单评价等相关功能。
 */

package repositories

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutOrderRepositoryImpl 外卖订单仓库实现
type TakeoutOrderRepositoryImpl struct{}

// NewTakeoutOrderRepository 创建外卖订单仓库实例
func NewTakeoutOrderRepository() TakeoutOrderRepository {
	return &TakeoutOrderRepositoryImpl{}
}

// CreateOrderExtension 创建订单扩展信息
func (r *TakeoutOrderRepositoryImpl) CreateOrderExtension(order *models.TakeoutOrderExtension) (int64, error) {
	o := orm.NewOrm()
	return o.Insert(order)
}

// GetOrderExtensionByOrderID 根据订单ID获取扩展信息
func (r *TakeoutOrderRepositoryImpl) GetOrderExtensionByOrderID(orderID int64) (*models.TakeoutOrderExtension, error) {
	o := orm.NewOrm()
	extension := &models.TakeoutOrderExtension{OrderID: orderID}
	err := o.Read(extension, "OrderID")
	if err != nil {
		return nil, err
	}
	return extension, nil
}

// UpdateOrderExtension 更新订单扩展信息
func (r *TakeoutOrderRepositoryImpl) UpdateOrderExtension(order *models.TakeoutOrderExtension) error {
	o := orm.NewOrm()
	_, err := o.Update(order)
	return err
}

// QueryOrders 查询订单列表
func (r *TakeoutOrderRepositoryImpl) QueryOrders(params map[string]interface{}) ([]*models.TakeoutOrderExtension, int64, error) {
	o := orm.NewOrm()
	
	// 首先查询符合条件的订单ID
	orderQs := o.QueryTable("order")
	
	// 屏蔽未支付和已取消的订单
	// 排除订单状态为待支付(10)和已取消(60)的订单
	orderQs = orderQs.Exclude("status", 10).Exclude("status", 60)
	// 排除支付状态为未支付(0)的订单
	orderQs = orderQs.Exclude("pay_status", 0)
	
	if orderNumber, ok := params["orderNumber"]; ok && orderNumber.(string) != "" {
		orderQs = orderQs.Filter("order_number__icontains", orderNumber)
	}
	
	if status, ok := params["status"]; ok && status.(int) >= 0 {
		orderQs = orderQs.Filter("status", status)
	}
	
	// 获取符合条件的订单ID列表
	var orderIDs orm.ParamsList
	_, err := orderQs.ValuesFlat(&orderIDs, "id")
	if err != nil {
		return nil, 0, err
	}
	
	// 转换为int64切片
	var validOrderIDs []int64
	for _, id := range orderIDs {
		if orderID, ok := id.(int64); ok {
			validOrderIDs = append(validOrderIDs, orderID)
		}
	}
	
	// 如果没有符合条件的订单，直接返回空结果
	if len(validOrderIDs) == 0 {
		return []*models.TakeoutOrderExtension{}, 0, nil
	}
	
	// 查询外卖订单扩展表
	qs := o.QueryTable(new(models.TakeoutOrderExtension))
	
	// 只查询符合条件的订单ID
	qs = qs.Filter("OrderID__in", validOrderIDs)

	// 应用查询条件
	if merchantID, ok := params["merchantID"]; ok && merchantID.(int64) > 0 {
		qs = qs.Filter("MerchantID", merchantID)
	}

	if startTime, ok := params["startTime"]; ok && startTime.(string) != "" {
		startDate, err := time.Parse("2006-01-02", startTime.(string))
		if err == nil {
			qs = qs.Filter("CreatedAt__gte", startDate)
		}
	}

	if endTime, ok := params["endTime"]; ok && endTime.(string) != "" {
		endDate, err := time.Parse("2006-01-02", endTime.(string))
		if err == nil {
			// 将结束日期设置为当天的最后一刻
			endDate = endDate.Add(24 * time.Hour).Add(-time.Second)
			qs = qs.Filter("CreatedAt__lte", endDate)
		}
	}

	// 计算总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 设置分页
	page, _ := params["page"].(int)
	pageSize, _ := params["pageSize"].(int)

	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	qs = qs.OrderBy("-CreatedAt").Limit(pageSize).Offset((page - 1) * pageSize)

	// 执行查询
	var orders []*models.TakeoutOrderExtension
	_, err = qs.All(&orders)

	return orders, total, err
}

// GetByOrderID 根据ID获取订单
func (r *TakeoutOrderRepositoryImpl) GetByOrderID(orderID int64) (*models.TakeoutOrderExtension, error) {
	o := orm.NewOrm()
	extension := &models.TakeoutOrderExtension{OrderID: orderID}
	err := o.Read(extension, "OrderID")
	if err != nil {
		return nil, err
	}
	return extension, nil
}

// Update 更新订单
func (r *TakeoutOrderRepositoryImpl) Update(order *models.TakeoutOrderExtension) error {
	o := orm.NewOrm()
	_, err := o.Update(order)
	return err
}

// CreateOrderLog 创建订单操作日志
func (r *TakeoutOrderRepositoryImpl) CreateOrderLog(log *models.TakeoutOrderLog) (int64, error) {
	o := orm.NewOrm()
	return o.Insert(log)
}

// GetOrderLogsByOrderID 获取订单的操作日志
func (r *TakeoutOrderRepositoryImpl) GetOrderLogsByOrderID(orderID int64) ([]*models.TakeoutOrderLog, error) {
	o := orm.NewOrm()
	var logs []*models.TakeoutOrderLog
	_, err := o.QueryTable(new(models.TakeoutOrderLog)).
		Filter("order_id", orderID).
		OrderBy("-create_time").
		All(&logs)
	return logs, err
}

// CreateOrderRating 创建订单评价
func (r *TakeoutOrderRepositoryImpl) CreateOrderRating(rating *models.TakeoutOrderRating) (int64, error) {
	o := orm.NewOrm()
	return o.Insert(rating)
}

// GetOrderRatingByOrderID 获取订单评价
func (r *TakeoutOrderRepositoryImpl) GetOrderRatingByOrderID(orderID int64) (*models.TakeoutOrderRating, error) {
	o := orm.NewOrm()
	rating := &models.TakeoutOrderRating{OrderID: orderID}
	err := o.Read(rating, "OrderID")
	if err != nil {
		return nil, err
	}
	return rating, nil
}

// CheckOrderRated 检查订单是否已评价
func (r *TakeoutOrderRepositoryImpl) CheckOrderRated(orderID int64) (bool, error) {
	o := orm.NewOrm()
	exist := o.QueryTable(new(models.TakeoutOrderRating)).
		Filter("order_id", orderID).
		Exist()
	return exist, nil
}

// UpdateOrderRated 更新订单为已评价状态
func (r *TakeoutOrderRepositoryImpl) UpdateOrderRated(orderID int64) error {
	o := orm.NewOrm()
	extension, err := r.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	extension.IsRated = true
	_, err = o.Update(extension, "IsRated")
	return err
}

// CountOrdersByDateRange 根据日期范围和状态统计订单数量
func (r *TakeoutOrderRepositoryImpl) CountOrdersByDateRange(startDate, endDate time.Time, status int) (int, error) {
	o := orm.NewOrm()
	qb, _ := orm.NewQueryBuilder("mysql")

	// 构建查询
	qb.Select("COUNT(*)")
	qb.From("order_main o")
	// qb.Join("INNER JOIN", "takeout_order_extension te", "o.order_id = te.order_id")
	qb.Where("o.order_type = 'takeout'")
	qb.And("o.create_time >= ?")
	qb.And("o.create_time <= ?")

	// 如果指定了状态，添加状态过滤条件
	if status > 0 {
		qb.And("o.order_status = ?")
	}

	// 执行查询
	sql := qb.String()
	var count int
	var err error

	if status > 0 {
		err = o.Raw(sql, startDate, endDate, status).QueryRow(&count)
	} else {
		err = o.Raw(sql, startDate, endDate).QueryRow(&count)
	}

	if err != nil {
		logs.Error("统计订单数量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// SumOrderAmountByDateRange 根据日期范围统计订单总金额
func (r *TakeoutOrderRepositoryImpl) SumOrderAmountByDateRange(startDate, endDate time.Time) (float64, error) {
	o := orm.NewOrm()
	qb, _ := orm.NewQueryBuilder("mysql")

	// 构建查询
	qb.Select("IFNULL(SUM(o.total_amount), 0) as total")
	qb.From("order_main o")
	// qb.Join("INNER JOIN", "takeout_order_extension te", "o.order_id = te.order_id")
	qb.Where("o.order_type = 'takeout'")
	qb.And("o.order_status > 1") // 只统计已支付及以上状态的订单
	qb.And("o.create_time >= ?")
	qb.And("o.create_time <= ?")

	// 执行查询
	sql := qb.String()
	var total float64
	err := o.Raw(sql, startDate, endDate).QueryRow(&total)

	if err != nil {
		logs.Error("统计订单总金额失败: %v", err)
		return 0, err
	}

	return total, nil
}

// GetAverageDeliveryTimeByDateRange 获取指定日期范围内的平均配送时间（分钟）
func (r *TakeoutOrderRepositoryImpl) GetAverageDeliveryTimeByDateRange(startDate, endDate time.Time) (float64, error) {
	o := orm.NewOrm()
	qb, _ := orm.NewQueryBuilder("mysql")

	// 构建查询
	qb.Select("AVG(TIMESTAMPDIFF(MINUTE, te.delivery_start_time, te.delivery_end_time)) as avg_time")
	qb.From("takeout_order_extension te")
	// qb.Join("INNER JOIN", "order_main o", "o.order_id = te.order_id")
	qb.Where("te.delivery_status = 2") // 已完成配送
	qb.And("te.delivery_start_time IS NOT NULL")
	qb.And("te.delivery_end_time IS NOT NULL")
	qb.And("te.create_time >= ?")
	qb.And("te.create_time <= ?")

	// 执行查询
	sql := qb.String()
	var avgTime float64
	err := o.Raw(sql, startDate, endDate).QueryRow(&avgTime)

	if err != nil {
		logs.Error("计算平均配送时间失败: %v", err)
		return 0, err
	}

	return avgTime, nil
}

// GetDailyOrderCountsByDateRange 获取指定日期范围内的每日订单数量
func (r *TakeoutOrderRepositoryImpl) GetDailyOrderCountsByDateRange(startDate, endDate time.Time) (map[string]int, error) {
	o := orm.NewOrm()
	qb, _ := orm.NewQueryBuilder("mysql")

	// 构建查询
	qb.Select("DATE(o.create_time) as order_date, COUNT(*) as count")
	qb.From("order_main o")
	// qb.Join("INNER JOIN", "takeout_order_extension te", "o.order_id = te.order_id")
	qb.Where("o.order_type = 'takeout'")
	qb.And("o.create_time >= ?")
	qb.And("o.create_time <= ?")
	qb.GroupBy("DATE(o.create_time)")
	qb.OrderBy("order_date ASC")

	// 执行查询
	sql := qb.String()
	type Result struct {
		OrderDate string
		Count     int
	}
	var results []Result
	_, err := o.Raw(sql, startDate, endDate).QueryRows(&results)

	if err != nil {
		logs.Error("获取每日订单数量失败: %v", err)
		return nil, err
	}

	// 转换为map结构
	dailyOrderCounts := make(map[string]int)
	for _, r := range results {
		dailyOrderCounts[r.OrderDate] = r.Count
	}

	return dailyOrderCounts, nil
}

// GetHotFoodsByDateRange 获取指定日期范围内的热门食品
func (r *TakeoutOrderRepositoryImpl) GetHotFoodsByDateRange(startDate, endDate time.Time, limit int) ([]*dto.HotFoodStatisticsDTO, error) {
	o := orm.NewOrm()
	qb, _ := orm.NewQueryBuilder("mysql")

	// 构建查询
	qb.Select("oi.product_id, tf.food_name, COUNT(oi.id) as order_count, SUM(oi.quantity) as total_quantity")
	qb.From("order_item oi")
	// qb.Join("INNER JOIN", "order_main o", "oi.order_id = o.id")
	// qb.Join("INNER JOIN", "takeout_order_extension te", "o.id = te.order_id")
	// qb.Join("INNER JOIN", "takeout_food tf", "oi.product_id = tf.id")
	qb.Where("oi.product_type = 'takeout'")
	qb.And("oi.create_time >= ?")
	qb.And("oi.create_time <= ?")
	qb.GroupBy("oi.product_id, tf.food_name")
	qb.OrderBy("total_quantity DESC")
	qb.Limit(limit)

	// 执行查询
	sql := qb.String()
	var results []*dto.HotFoodStatisticsDTO
	_, err := o.Raw(sql, startDate, endDate).QueryRows(&results)

	if err != nil {
		logs.Error("获取热门食品失败: %v", err)
		return nil, err
	}

	return results, nil
}
