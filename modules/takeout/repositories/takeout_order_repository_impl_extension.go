/*
 * 外卖订单仓库实现（商户仪表板扩展）
 *
 * 本文件扩展了外卖订单仓库实现，添加了商户仪表板统计相关的方法。
 * 包括按商户和日期范围统计订单数量、销售额，以及按商户和状态统计订单数量。
 */

package repositories

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// CountOrdersByMerchantAndDateRange 根据商户ID和日期范围统计订单数量
func (r *TakeoutOrderRepositoryImpl) CountOrdersByMerchantAndDateRange(merchantID int64, startDate, endDate time.Time, status int) (int, error) {
	o := orm.NewOrm()

	// 直接使用SQL查询
	sql := `SELECT COUNT(*) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ? 
		AND o.created_at >= ? 
		AND o.created_at <= ?`

	// 如果指定了状态，添加状态过滤条件
	var params []interface{}
	params = append(params, merchantID, startDate, endDate)
	if status > 0 {
		sql += " AND o.status = ?"
		params = append(params, status)
	}

	// 执行查询
	var count int
	err := o.Raw(sql, params...).QueryRow(&count)
	if err != nil {
		logs.Error("根据商户和日期范围统计订单数量失败: %v, 商户ID: %d, 日期范围: %v - %v, 状态: %d",
			err, merchantID, startDate, endDate, status)
		return 0, err
	}

	return count, nil
}

// SumOrderAmountByMerchantAndDateRange 根据商户ID和日期范围统计订单总金额
func (r *TakeoutOrderRepositoryImpl) SumOrderAmountByMerchantAndDateRange(merchantID int64, startDate, endDate time.Time) (float64, error) {
	o := orm.NewOrm()

	// 直接使用SQL查询
	sql := `SELECT COALESCE(SUM(o.total_amount), 0) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ? 
		AND o.created_at >= ? 
		AND o.created_at <= ?`

	// 执行查询
	var totalAmount float64
	err := o.Raw(sql, merchantID, startDate, endDate).QueryRow(&totalAmount)
	if err != nil {
		logs.Error("根据商户和日期范围统计订单总金额失败: %v, 商户ID: %d, 日期范围: %v - %v",
			err, merchantID, startDate, endDate)
		return 0, err
	}

	return totalAmount, nil
}

// CountOrdersByMerchantAndStatus 根据商户ID和状态统计订单数量
func (r *TakeoutOrderRepositoryImpl) CountOrdersByMerchantAndStatus(merchantID int64, status int) (int, error) {
	o := orm.NewOrm()

	// 直接使用SQL查询
	sql := `SELECT COUNT(*) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ?`

	// 添加状态过滤条件
	var params []interface{}
	params = append(params, merchantID)
	if status > 0 {
		sql += " AND o.status = ?"
		params = append(params, status)
	}

	// 执行查询
	var count int
	err := o.Raw(sql, params...).QueryRow(&count)
	if err != nil {
		logs.Error("根据商户和状态统计订单数量失败: %v, 商户ID: %d, 状态: %d",
			err, merchantID, status)
		return 0, err
	}

	return count, nil
}

// CountOrdersByMerchant 根据商户ID和状态统计订单数量
func (r *TakeoutOrderRepositoryImpl) CountOrdersByMerchant(merchantID int64, status int) (int64, error) {
	o := orm.NewOrm()

	// 直接使用SQL查询
	sql := `SELECT COUNT(*) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ?`

	// 如果指定了状态过滤条件，则添加状态条件
	params := []interface{}{merchantID}
	if status >= 0 {
		sql += " AND o.status = ?"
		params = append(params, status)
	}

	// 执行查询
	var count int64
	err := o.Raw(sql, params...).QueryRow(&count)
	if err != nil {
		logs.Error("统计商户订单数量失败: %v, 商户ID: %d, 状态: %d", err, merchantID, status)
		return 0, err
	}

	return count, nil
}

// CountProcessingOrdersByMerchant 统计商户进行中的订单数量
// 进行中订单包括：已支付(20)、处理中(25)、已发货(30)的订单
func (r *TakeoutOrderRepositoryImpl) CountProcessingOrdersByMerchant(merchantID int64) (int64, error) {
	o := orm.NewOrm()

	// 直接使用SQL查询
	sql := `SELECT COUNT(*) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ? 
		AND o.status IN (20, 25, 30)` // 20:已支付, 25:处理中, 30:已发货

	// 执行查询
	var count int64
	err := o.Raw(sql, merchantID).QueryRow(&count)
	if err != nil {
		logs.Error("统计商户进行中订单数量失败: %v, 商户ID: %d", err, merchantID)
		return 0, err
	}

	return count, nil
}

// GetTotalSalesByMerchantAndDateRange 根据商户ID和日期范围统计销售总额
func (r *TakeoutOrderRepositoryImpl) GetTotalSalesByMerchantAndDateRange(merchantID int64, startDate, endDate time.Time) (float64, error) {
	o := orm.NewOrm()

	// 先查询所有订单状态，用于调试
	debugSQL := `SELECT o.status, COUNT(*) as count, COALESCE(SUM(o.total_amount), 0) as total
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ? 
		AND o.created_at >= ? 
		AND o.created_at < ? 
		GROUP BY o.status`

	type StatusSummary struct {
		Status int     `orm:"column(status)"`
		Count  int     `orm:"column(count)"`
		Total  float64 `orm:"column(total)"`
	}

	var statusSummaries []StatusSummary
	_, err := o.Raw(debugSQL, merchantID, startDate, endDate).QueryRows(&statusSummaries)
	if err == nil {
		logs.Info("商户%d在%v到%v期间的订单状态统计:", merchantID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
		for _, summary := range statusSummaries {
			logs.Info("状态%d: %d个订单, 总金额%.2f", summary.Status, summary.Count, summary.Total)
		}
	}

	// 直接使用SQL查询，只统计已完成的订单
	sql := `SELECT COALESCE(SUM(o.total_amount), 0) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ? 
		AND o.created_at >= ? 
		AND o.created_at < ? 
		AND o.status = 40` // 40:已完成

	// 执行查询
	var totalSales float64
	err = o.Raw(sql, merchantID, startDate, endDate).QueryRow(&totalSales)
	if err != nil {
		logs.Error("根据商户和日期范围统计销售总额失败: %v, 商户ID: %d, 日期范围: %v - %v",
			err, merchantID, startDate, endDate)
		return 0, err
	}

	logs.Info("商户%d在%v到%v期间已完成订单销售总额: %.2f", merchantID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), totalSales)
	return totalSales, nil
}

// CountOrdersByMerchantAndDateRangeWithoutStatus 根据商户ID和日期范围统计订单数量（不带状态过滤）
func (r *TakeoutOrderRepositoryImpl) CountOrdersByMerchantAndDateRangeWithoutStatus(merchantID int64, startDate, endDate time.Time) (int, error) {
	o := orm.NewOrm()

	// 直接使用SQL查询
	sql := `SELECT COUNT(*) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ? 
		AND o.created_at >= ? 
		AND o.created_at < ?`

	// 执行查询
	var count int
	err := o.Raw(sql, merchantID, startDate, endDate).QueryRow(&count)
	if err != nil {
		logs.Error("根据商户和日期范围统计订单数量失败: %v, 商户ID: %d, 日期范围: %v - %v",
			err, merchantID, startDate, endDate)
		return 0, err
	}

	return count, nil
}

// CountPendingOrdersByMerchant 统计商户待处理订单数量
// 待处理订单包括：待支付(10)、已支付(20)、处理中(25)的订单
func (r *TakeoutOrderRepositoryImpl) CountPendingOrdersByMerchant(merchantID int64) (int, error) {
	o := orm.NewOrm()

	// 直接使用SQL查询
	sql := `SELECT COUNT(*) 
		FROM ` + "`order`" + ` o 
		INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
		WHERE te.merchant_id = ? 
		AND o.status IN (10, 20, 25)` // 10:待支付, 20:已支付, 25:处理中

	// 执行查询
	var count int
	err := o.Raw(sql, merchantID).QueryRow(&count)
	if err != nil {
		logs.Error("统计商户待处理订单数量失败: %v, 商户ID: %d", err, merchantID)
		return 0, err
	}

	return count, nil
}
