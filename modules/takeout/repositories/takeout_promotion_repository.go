/**
 * 外卖模块 - 促销活动仓储接口
 * 描述：定义促销活动的数据访问层接口和实现
 * 作者：系统
 * 创建时间：2025-05-14
 */

package repositories

import (
	"time"
	
	"github.com/beego/beego/v2/client/orm"
	// "github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/models"
)

// ITakeoutPromotionRepository 促销活动仓储接口
type ITakeoutPromotionRepository interface {
	// 创建促销活动
	Create(promotion *models.TakeoutPromotion) (int64, error)
	
	// 更新促销活动
	Update(promotion *models.TakeoutPromotion) error
	
	// 根据ID获取促销活动
	GetByID(id int64) (*models.TakeoutPromotion, error)
	
	// 获取商户的促销活动列表
	GetByMerchantID(merchantID int64, page, pageSize int) ([]*models.TakeoutPromotion, int64, error)
	
	// 获取特定类型的活动列表
	GetByType(promotionType int, page, pageSize int) ([]*models.TakeoutPromotion, int64, error)
	
	// 获取当前活跃的活动
	GetActivePromotions(merchantID int64) ([]*models.TakeoutPromotion, error)
	
	// 更新活动使用次数
	IncrementUsageCount(id int64) error
	
	// 更新活动状态
	UpdateStatus(id int64, status int) error
	
	// 获取促销活动统计信息
	GetStatistics(merchantID int64) (map[string]interface{}, error)
}

// TakeoutPromotionRepository 促销活动仓储实现
type TakeoutPromotionRepository struct {
}

// NewTakeoutPromotionRepository 创建促销活动仓储
func NewTakeoutPromotionRepository() ITakeoutPromotionRepository {
	return &TakeoutPromotionRepository{}
}

// Create 创建促销活动
func (r *TakeoutPromotionRepository) Create(promotion *models.TakeoutPromotion) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(promotion)
	return id, err
}

// Update 更新促销活动
func (r *TakeoutPromotionRepository) Update(promotion *models.TakeoutPromotion) error {
	o := orm.NewOrm()
	_, err := o.Update(promotion)
	return err
}

// GetByID 根据ID获取促销活动
func (r *TakeoutPromotionRepository) GetByID(id int64) (*models.TakeoutPromotion, error) {
	o := orm.NewOrm()
	promotion := models.TakeoutPromotion{ID: id}
	err := o.Read(&promotion)
	if err != nil {
		return nil, err
	}
	return &promotion, nil
}

// GetByMerchantID 获取商户的促销活动列表
func (r *TakeoutPromotionRepository) GetByMerchantID(merchantID int64, page, pageSize int) ([]*models.TakeoutPromotion, int64, error) {
	o := orm.NewOrm()
	var promotions []*models.TakeoutPromotion
	qs := o.QueryTable(new(models.TakeoutPromotion)).Filter("merchant_id", merchantID)
	
	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&promotions)
	if err != nil {
		return nil, 0, err
	}
	
	return promotions, total, nil
}

// GetByType 获取特定类型的活动列表
func (r *TakeoutPromotionRepository) GetByType(promotionType int, page, pageSize int) ([]*models.TakeoutPromotion, int64, error) {
	o := orm.NewOrm()
	var promotions []*models.TakeoutPromotion
	qs := o.QueryTable(new(models.TakeoutPromotion)).Filter("type", promotionType)
	
	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&promotions)
	if err != nil {
		return nil, 0, err
	}
	
	return promotions, total, nil
}

// GetActivePromotions 获取当前活跃的活动
func (r *TakeoutPromotionRepository) GetActivePromotions(merchantID int64) ([]*models.TakeoutPromotion, error) {
	o := orm.NewOrm()
	var promotions []*models.TakeoutPromotion
	now := time.Now()
	
	// logs.Info("GetActivePromotions: 商家ID=%d, 当前时间=%s, 查询状态=%d", merchantID, now.Format("2006-01-02 15:04:05"), models.PromotionStatusActive)
	
	qs := o.QueryTable(new(models.TakeoutPromotion)).Filter("status", models.PromotionStatusActive)
	if merchantID > 0 {
		qs = qs.Filter("merchant_id", merchantID)
	}
	
	_, err := qs.Filter("start_time__lte", now).Filter("end_time__gte", now).All(&promotions)
	// logs.Info("GetActivePromotions: 查询结果 err=%v, 促销活动数量=%d", err, len(promotions))
	// for i, p := range promotions {
		// logs.Info("GetActivePromotions: 促销活动[%d] ID=%d, 商家ID=%d, 名称=%s, 状态=%d, 开始时间=%s, 结束时间=%s", 
		//	i, p.ID, p.MerchantID, p.Name, p.Status, p.StartTime.Format("2006-01-02 15:04:05"), p.EndTime.Format("2006-01-02 15:04:05"))
	// }
	if err != nil {
		return nil, err
	}
	
	return promotions, nil
}

// IncrementUsageCount 更新活动使用次数
func (r *TakeoutPromotionRepository) IncrementUsageCount(id int64) error {
	o := orm.NewOrm()
	_, err := o.QueryTable(new(models.TakeoutPromotion)).Filter("id", id).Update(orm.Params{
		"usage_count": orm.ColValue(orm.ColAdd, 1),
	})
	return err
}

// UpdateStatus 更新活动状态
func (r *TakeoutPromotionRepository) UpdateStatus(id int64, status int) error {
	o := orm.NewOrm()
	_, err := o.QueryTable(new(models.TakeoutPromotion)).Filter("id", id).Update(orm.Params{
		"status": status,
	})
	return err
}

// GetStatistics 获取促销活动统计信息
func (r *TakeoutPromotionRepository) GetStatistics(merchantID int64) (map[string]interface{}, error) {
	o := orm.NewOrm()
	now := time.Now()
	result := make(map[string]interface{})
	
	// 获取总促销活动数
	totalCount, err := o.QueryTable(new(models.TakeoutPromotion)).Filter("merchant_id", merchantID).Count()
	if err != nil {
		return nil, err
	}
	result["total_promotions"] = totalCount
	
	// 获取进行中的活动数
	activeCount, err := o.QueryTable(new(models.TakeoutPromotion)).Filter("merchant_id", merchantID).Filter("status", models.PromotionStatusActive).Filter("start_time__lte", now).Filter("end_time__gte", now).Count()
	if err != nil {
		return nil, err
	}
	result["active_promotions"] = activeCount
	
	// 获取待发布的活动数
	pendingCount, err := o.QueryTable(new(models.TakeoutPromotion)).Filter("merchant_id", merchantID).Filter("status", models.PromotionStatusPending).Count()
	if err != nil {
		return nil, err
	}
	result["pending_promotions"] = pendingCount
	
	// 获取已结束的活动数
	endedCount, err := o.QueryTable(new(models.TakeoutPromotion)).Filter("merchant_id", merchantID).Filter("status", models.PromotionStatusEnded).Count()
	if err != nil {
		return nil, err
	}
	result["ended_promotions"] = endedCount
	
	// 获取所有活动的总使用次数
	var totalUsage int64
	err = o.Raw("SELECT SUM(usage_count) FROM takeout_promotion WHERE merchant_id = ?", merchantID).QueryRow(&totalUsage)
	if err != nil {
		totalUsage = 0 // 如果出错，默认为0
	}
	result["total_usage"] = totalUsage
	
	// 计算总优惠金额需要更多的业务逻辑和数据模型
	// 这里我们暂时将其设置为0，或者可以在将来实现更复杂的逻辑
	result["total_discount_amount"] = 0
	
	return result, nil
}
