/**
 * 外卖退款仓库
 *
 * 本文件实现了外卖退款的数据访问层，处理退款数据的增删改查操作。
 * 提供商户退款数据统计查询功能。
 */

package repositories

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutRefundRepository 外卖退款仓库接口
type TakeoutRefundRepository interface {
	// CountPendingRefundsByMerchant 统计商户待处理退款订单数
	CountPendingRefundsByMerchant(merchantID int64) (int, error)
	
	// CreateRefund 创建退款记录
	CreateRefund(refund *models.TakeoutRefund) error
	
	// GetRefundByOrderID 根据订单ID获取退款记录
	GetRefundByOrderID(orderID int64) (*models.TakeoutRefund, error)
	
	// GetRefundByID 根据退款ID获取退款记录
	GetRefundByID(refundID int64) (*models.TakeoutRefund, error)
	
	// GetRefundByRefundNo 根据退款单号获取退款记录
	GetRefundByRefundNo(refundNo string) (*models.TakeoutRefund, error)
	
	// UpdateRefund 更新退款记录
	UpdateRefund(refund *models.TakeoutRefund) error
}

// takeoutRefundRepository 外卖退款仓库实现
type takeoutRefundRepository struct{}

// NewTakeoutRefundRepository 创建外卖退款仓库实例
func NewTakeoutRefundRepository() TakeoutRefundRepository {
	return &takeoutRefundRepository{}
}

// CountPendingRefundsByMerchant 统计商户待处理退款订单数
func (r *takeoutRefundRepository) CountPendingRefundsByMerchant(merchantID int64) (int, error) {
	o := orm.NewOrm()
	// 根据系统设计，退款申请处于待处理状态的记录
	var count int64
	sql := `SELECT COUNT(*) FROM refund r 
			WHERE r.status = 0 
			AND r.order_i_d IN (
				SELECT o.i_d FROM ` + "`order`" + ` o 
				INNER JOIN takeout_order_extension te ON o.i_d = te.order_id 
				WHERE te.merchant_id = ?
			)`

	// 执行SQL语句
	err := o.Raw(sql, merchantID).QueryRow(&count)
	if err != nil {
		logs.Error("统计商户待处理退款订单数失败: %v, 商家ID: %d", err, merchantID)
		return 0, err
	}

	return int(count), nil
}

// CreateRefund 创建退款记录
func (r *takeoutRefundRepository) CreateRefund(refund *models.TakeoutRefund) error {
	o := orm.NewOrm()
	
	// 插入退款记录
	id, err := o.Insert(refund)
	if err != nil {
		logs.Error("创建退款记录失败: %v, 订单ID: %d", err, refund.OrderID)
		return err
	}
	
	// 设置生成的ID
	refund.ID = id
	logs.Info("退款记录创建成功 - 退款ID: %d, 订单ID: %d, 退款金额: %.2f", refund.ID, refund.OrderID, refund.RefundAmount)
	return nil
}

// GetRefundByOrderID 根据订单ID获取退款记录
func (r *takeoutRefundRepository) GetRefundByOrderID(orderID int64) (*models.TakeoutRefund, error) {
	o := orm.NewOrm()
	refund := &models.TakeoutRefund{}
	
	err := o.QueryTable("takeout_refunds").Filter("order_id", orderID).One(refund)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没有找到退款记录
		}
		logs.Error("查询退款记录失败: %v, 订单ID: %d", err, orderID)
		return nil, err
	}
	
	return refund, nil
}

// GetRefundByID 根据退款ID获取退款记录
func (r *takeoutRefundRepository) GetRefundByID(refundID int64) (*models.TakeoutRefund, error) {
	o := orm.NewOrm()
	refund := &models.TakeoutRefund{}
	
	err := o.QueryTable("takeout_refunds").Filter("id", refundID).One(refund)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没有找到退款记录
		}
		logs.Error("查询退款记录失败: %v, 退款ID: %d", err, refundID)
		return nil, err
	}
	
	return refund, nil
}

// GetRefundByRefundNo 根据退款单号获取退款记录
func (r *takeoutRefundRepository) GetRefundByRefundNo(refundNo string) (*models.TakeoutRefund, error) {
	o := orm.NewOrm()
	refund := &models.TakeoutRefund{}
	
	err := o.QueryTable("takeout_refunds").Filter("refund_no", refundNo).One(refund)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没有找到退款记录
		}
		logs.Error("查询退款记录失败: %v, 退款单号: %s", err, refundNo)
		return nil, err
	}
	
	return refund, nil
}

// UpdateRefund 更新退款记录
func (r *takeoutRefundRepository) UpdateRefund(refund *models.TakeoutRefund) error {
	o := orm.NewOrm()
	
	_, err := o.Update(refund)
	if err != nil {
		logs.Error("更新退款记录失败: %v, 退款ID: %d", err, refund.ID)
		return err
	}
	
	logs.Info("退款记录更新成功 - 退款ID: %d, 状态: %d", refund.ID, refund.RefundStatus)
	return nil
}
