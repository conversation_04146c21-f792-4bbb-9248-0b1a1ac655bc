/**
 * 外卖模块 - 用户优惠券仓储接口
 * 描述：定义用户优惠券的数据访问层接口和实现
 * 作者：系统
 * 创建时间：2025-05-14
 */

package repositories

import (
	"time"
	
	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/takeout/models"
)

// ITakeoutUserCouponRepository 用户优惠券仓储接口
type ITakeoutUserCouponRepository interface {
	// 创建用户优惠券
	Create(userCoupon *models.TakeoutUserCoupon) (int64, error)
	
	// 根据ID获取用户优惠券
	GetByID(id int64) (*models.TakeoutUserCoupon, error)
	
	// 获取用户的优惠券列表
	GetByUserID(userID int64, status int, page, pageSize int) ([]*models.TakeoutUserCoupon, int64, error)
	
	// 获取用户的优惠券详情列表（包含优惠券信息）
	GetUserCouponsWithDetail(userID int64, status int, page, pageSize int) ([]*models.TakeoutUserCoupon, []*models.TakeoutCoupon, int64, error)
	
	// 更新用户优惠券状态
	UpdateStatus(id int64, status int, orderID int64) error
	
	// 检查用户是否拥有指定优惠券
	ExistsByUserIDAndCouponID(userID int64, couponID int64) (bool, error)
	
	// 统计用户对指定优惠券的使用次数
	CountUserUsage(userID int64, couponID int64) (int, error)
	
	// 检查优惠券总发放数量
	CountTotalIssued(couponID int64) (int64, error)
	
	// 更新过期优惠券状态
	UpdateExpiredCoupons() (int64, error)

	// GetUserCouponIDs 获取用户拥有的所有优惠券ID
	GetUserCouponIDs(userID int64) ([]int64, error)
}

// TakeoutUserCouponRepository 用户优惠券仓储实现
type TakeoutUserCouponRepository struct {
}

// NewTakeoutUserCouponRepository 创建用户优惠券仓储
func NewTakeoutUserCouponRepository() ITakeoutUserCouponRepository {
	return &TakeoutUserCouponRepository{}
}

// Create 创建用户优惠券
func (r *TakeoutUserCouponRepository) Create(userCoupon *models.TakeoutUserCoupon) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(userCoupon)
	return id, err
}

// GetByID u6839u636eIDu83b7u53d6u7528u6237u4f18u60e0u5238
func (r *TakeoutUserCouponRepository) GetByID(id int64) (*models.TakeoutUserCoupon, error) {
	o := orm.NewOrm()
	userCoupon := models.TakeoutUserCoupon{ID: id}
	err := o.Read(&userCoupon)
	if err != nil {
		return nil, err
	}
	return &userCoupon, nil
}

// GetByUserID u83b7u53d6u7528u6237u7684u4f18u60e0u5238u5217u8868
func (r *TakeoutUserCouponRepository) GetByUserID(userID int64, status int, page, pageSize int) ([]*models.TakeoutUserCoupon, int64, error) {
	o := orm.NewOrm()
	var userCoupons []*models.TakeoutUserCoupon
	qs := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("user_id", userID)
	
	// u5982u679cu6307u5b9au4e86u72b6u6001uff0cu5219u8fc7u6ee4u72b6u6001
	if status > 0 {
		qs = qs.Filter("status", status)
	}
	
	// u83b7u53d6u603bu6570
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}
	
	// u5206u9875u67e5u8be2
	_, err = qs.OrderBy("-created_at").Limit(pageSize, (page-1)*pageSize).All(&userCoupons)
	if err != nil {
		return nil, 0, err
	}
	
	return userCoupons, total, nil
}

// GetUserCouponsWithDetail u83b7u53d6u7528u6237u7684u4f18u60e0u5238u8be6u60c5u5217u8868
func (r *TakeoutUserCouponRepository) GetUserCouponsWithDetail(userID int64, status int, page, pageSize int) ([]*models.TakeoutUserCoupon, []*models.TakeoutCoupon, int64, error) {
	// u83b7u53d6u7528u6237u4f18u60e0u5238u5217u8868
	userCoupons, total, err := r.GetByUserID(userID, status, page, pageSize)
	if err != nil {
		return nil, nil, 0, err
	}
	
	if len(userCoupons) == 0 {
		return userCoupons, []*models.TakeoutCoupon{}, total, nil
	}
	
	// u63d0u53d6u6240u6709u4f18u60e0u5238ID
	couponIDs := make([]int64, len(userCoupons))
	for i, uc := range userCoupons {
		couponIDs[i] = uc.CouponID
	}
	
	// u83b7u53d6u4f18u60e0u5238u8be6u60c5
	o := orm.NewOrm()
	var coupons []*models.TakeoutCoupon
	_, err = o.QueryTable(new(models.TakeoutCoupon)).Filter("id__in", couponIDs).All(&coupons)
	if err != nil {
		return nil, nil, 0, err
	}
	
	return userCoupons, coupons, total, nil
}

// UpdateStatus u66f4u65b0u7528u6237u4f18u60e0u5238u72b6u6001
func (r *TakeoutUserCouponRepository) UpdateStatus(id int64, status int, orderID int64) error {
	o := orm.NewOrm()
	params := orm.Params{
		"status": status,
	}
	
	// u5982u679cu662fu5df2u4f7fu7528u72b6u6001uff0cu9700u8981u8bb0u5f55u4f7fu7528u65f6u95f4u548cu8ba2u5355ID
	if status == models.UserCouponStatusUsed {
		params["used_time"] = time.Now()
		params["order_id"] = orderID
	}
	
	_, err := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("id", id).Update(params)
	return err
}

// ExistsByUserIDAndCouponID u68c0u67e5u7528u6237u662fu5426u62e5u6709u6307u5b9au4f18u60e0u5238
func (r *TakeoutUserCouponRepository) ExistsByUserIDAndCouponID(userID int64, couponID int64) (bool, error) {
	o := orm.NewOrm()
	exists := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("user_id", userID).Filter("coupon_id", couponID).Exist()
	return exists, nil
}

// CountUserUsage u7edfu8ba1u7528u6237u5bf9u6307u5b9au4f18u60e0u5238u7684u4f7fu7528u6b21u6570
func (r *TakeoutUserCouponRepository) CountUserUsage(userID int64, couponID int64) (int, error) {
	o := orm.NewOrm()
	count, err := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("user_id", userID).Filter("coupon_id", couponID).Count()
	return int(count), err
}

// CountTotalIssued u68c0u67e5u4f18u60e0u5238u603bu53d1u653eu6570u91cf
func (r *TakeoutUserCouponRepository) CountTotalIssued(couponID int64) (int64, error) {
	o := orm.NewOrm()
	count, err := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("coupon_id", couponID).Count()
	return count, err
}

// UpdateExpiredCoupons u66f4u65b0u8fc7u671fu4f18u60e0u5238u72b6u6001
func (r *TakeoutUserCouponRepository) UpdateExpiredCoupons() (int64, error) {
	o := orm.NewOrm()
	now := time.Now()
	
	// u83b7u53d6u6240u6709u672au4f7fu7528u4f46u5df2u8fc7u671fu7684u4f18u60e0u5238
	var userCoupons []*models.TakeoutUserCoupon
	_, err := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("status", models.UserCouponStatusUnused).All(&userCoupons)
	if err != nil {
		return 0, err
	}
	
	var expired int64 = 0
	for _, uc := range userCoupons {
		// u83b7u53d6u4f18u60e0u5238u4fe1u606fu68c0u67e5u662fu5426u8fc7u671f
		coupon := models.TakeoutCoupon{ID: uc.CouponID}
		err := o.Read(&coupon)
		if err != nil {
			continue
		}
		
		// u5982u679cu4f18u60e0u5238u5df2u8fc7u671fuff0cu5219u66f4u65b0u72b6u6001
		if now.After(coupon.EndTime) {
			_, err := o.QueryTable(new(models.TakeoutUserCoupon)).Filter("id", uc.ID).Update(orm.Params{
				"status": models.UserCouponStatusExpired,
			})
			if err == nil {
				expired++
			}
		}
	}
	
	return expired, nil
}

// GetUserCouponIDs 获取用户拥有的所有优惠券ID
func (r *TakeoutUserCouponRepository) GetUserCouponIDs(userID int64) ([]int64, error) {
	o := orm.NewOrm()
	var userCoupons []*models.TakeoutUserCoupon
	_, err := o.QueryTable(new(models.TakeoutUserCoupon)).
		Filter("user_id", userID).
		All(&userCoupons, "CouponID")
	if err != nil {
		return nil, err
	}

	couponIDs := make([]int64, len(userCoupons))
	for i, uc := range userCoupons {
		couponIDs[i] = uc.CouponID
	}

	return couponIDs, nil
}
