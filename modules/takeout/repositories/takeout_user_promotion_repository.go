/**
 * 外卖模块 - 用户促销使用记录仓储
 * 描述：定义用户促销使用记录的数据访问接口和实现
 * 作者：系统
 * 创建时间：2025-07-21
 */

package repositories

import (
	"time"

	"o_mall_backend/modules/takeout/models"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// ITakeoutUserPromotionRepository 用户促销使用记录仓储接口
type ITakeoutUserPromotionRepository interface {
	// 创建用户促销使用记录
	Create(userPromotion *models.TakeoutUserPromotion) (int64, error)
	
	// 根据ID获取用户促销使用记录
	GetByID(id int64) (*models.TakeoutUserPromotion, error)
	
	// 统计用户使用某个促销活动的次数
	CountUserPromotionUsage(userID int64, promotionID int64) (int64, error)
	
	// 获取用户使用某个促销活动的记录列表
	GetUserPromotionUsage(userID int64, promotionID int64) ([]*models.TakeoutUserPromotion, error)
	
	// 获取用户的促销使用历史
	GetUserPromotionHistory(userID int64, page, pageSize int) ([]*models.TakeoutUserPromotion, int64, error)
	
	// 获取促销活动的使用统计
	GetPromotionUsageStats(promotionID int64) (map[string]interface{}, error)
	
	// 批量创建用户促销使用记录
	BatchCreate(userPromotions []*models.TakeoutUserPromotion) error
}

// TakeoutUserPromotionRepository 用户促销使用记录仓储实现
type TakeoutUserPromotionRepository struct{}

// NewTakeoutUserPromotionRepository 创建用户促销使用记录仓储
func NewTakeoutUserPromotionRepository() ITakeoutUserPromotionRepository {
	return &TakeoutUserPromotionRepository{}
}

// Create 创建用户促销使用记录
func (r *TakeoutUserPromotionRepository) Create(userPromotion *models.TakeoutUserPromotion) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(userPromotion)
	if err != nil {
		logs.Error("创建用户促销使用记录失败: %v", err)
		return 0, err
	}
	return id, nil
}

// GetByID 根据ID获取用户促销使用记录
func (r *TakeoutUserPromotionRepository) GetByID(id int64) (*models.TakeoutUserPromotion, error) {
	o := orm.NewOrm()
	userPromotion := &models.TakeoutUserPromotion{ID: id}
	err := o.Read(userPromotion)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("根据ID获取用户促销使用记录失败: %v", err)
		return nil, err
	}
	return userPromotion, nil
}

// CountUserPromotionUsage 统计用户使用某个促销活动的次数
func (r *TakeoutUserPromotionRepository) CountUserPromotionUsage(userID int64, promotionID int64) (int64, error) {
	o := orm.NewOrm()
	count, err := o.QueryTable(new(models.TakeoutUserPromotion)).
		Filter("user_id", userID).
		Filter("promotion_id", promotionID).
		Count()
	if err != nil {
		logs.Error("统计用户促销使用次数失败: %v", err)
		return 0, err
	}
	return count, nil
}

// GetUserPromotionUsage 获取用户使用某个促销活动的记录列表
func (r *TakeoutUserPromotionRepository) GetUserPromotionUsage(userID int64, promotionID int64) ([]*models.TakeoutUserPromotion, error) {
	o := orm.NewOrm()
	var userPromotions []*models.TakeoutUserPromotion
	_, err := o.QueryTable(new(models.TakeoutUserPromotion)).
		Filter("user_id", userID).
		Filter("promotion_id", promotionID).
		OrderBy("-created_at").
		All(&userPromotions)
	if err != nil {
		logs.Error("获取用户促销使用记录失败: %v", err)
		return nil, err
	}
	return userPromotions, nil
}

// GetUserPromotionHistory 获取用户的促销使用历史
func (r *TakeoutUserPromotionRepository) GetUserPromotionHistory(userID int64, page, pageSize int) ([]*models.TakeoutUserPromotion, int64, error) {
	o := orm.NewOrm()
	
	// 计算偏移量
	offset := (page - 1) * pageSize
	
	// 获取总数
	total, err := o.QueryTable(new(models.TakeoutUserPromotion)).
		Filter("user_id", userID).
		Count()
	if err != nil {
		logs.Error("统计用户促销使用历史总数失败: %v", err)
		return nil, 0, err
	}
	
	// 获取分页数据
	var userPromotions []*models.TakeoutUserPromotion
	_, err = o.QueryTable(new(models.TakeoutUserPromotion)).
		Filter("user_id", userID).
		OrderBy("-created_at").
		Limit(pageSize, offset).
		All(&userPromotions)
	if err != nil {
		logs.Error("获取用户促销使用历史失败: %v", err)
		return nil, 0, err
	}
	
	return userPromotions, total, nil
}

// GetPromotionUsageStats 获取促销活动的使用统计
func (r *TakeoutUserPromotionRepository) GetPromotionUsageStats(promotionID int64) (map[string]interface{}, error) {
	o := orm.NewOrm()
	result := make(map[string]interface{})
	
	// 统计总使用次数
	totalUsage, err := o.QueryTable(new(models.TakeoutUserPromotion)).
		Filter("promotion_id", promotionID).
		Count()
	if err != nil {
		logs.Error("统计促销活动总使用次数失败: %v", err)
		return nil, err
	}
	result["total_usage"] = totalUsage
	
	// 统计使用用户数
	var uniqueUsers int64
	err = o.Raw("SELECT COUNT(DISTINCT user_id) FROM takeout_user_promotion WHERE promotion_id = ?", promotionID).QueryRow(&uniqueUsers)
	if err != nil {
		logs.Error("统计促销活动使用用户数失败: %v", err)
		uniqueUsers = 0
	}
	result["unique_users"] = uniqueUsers
	
	// 统计总优惠金额
	var totalDiscount float64
	err = o.Raw("SELECT COALESCE(SUM(discount_amount), 0) FROM takeout_user_promotion WHERE promotion_id = ?", promotionID).QueryRow(&totalDiscount)
	if err != nil {
		logs.Error("统计促销活动总优惠金额失败: %v", err)
		totalDiscount = 0
	}
	result["total_discount"] = totalDiscount
	
	// 统计今日使用次数
	today := time.Now().Format("2006-01-02")
	var todayUsage int64
	err = o.Raw("SELECT COUNT(*) FROM takeout_user_promotion WHERE promotion_id = ? AND DATE(created_at) = ?", promotionID, today).QueryRow(&todayUsage)
	if err != nil {
		logs.Error("统计促销活动今日使用次数失败: %v", err)
		todayUsage = 0
	}
	result["today_usage"] = todayUsage
	
	return result, nil
}

// BatchCreate 批量创建用户促销使用记录
func (r *TakeoutUserPromotionRepository) BatchCreate(userPromotions []*models.TakeoutUserPromotion) error {
	if len(userPromotions) == 0 {
		return nil
	}
	
	o := orm.NewOrm()
	_, err := o.InsertMulti(len(userPromotions), userPromotions)
	if err != nil {
		logs.Error("批量创建用户促销使用记录失败: %v", err)
		return err
	}
	return nil
}
