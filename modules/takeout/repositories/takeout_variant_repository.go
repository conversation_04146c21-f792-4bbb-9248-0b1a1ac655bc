/**
 * 外卖食品规格变体仓库
 *
 * 本文件实现了外卖食品规格变体相关的数据库操作，提供数据持久化和查询功能。
 * 负责takeout_food_variant表的CRUD操作，支持按食品ID查询等功能。
 */

package repositories

import (
	"errors"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/takeout/models"
)

// TakeoutVariantRepository 外卖规格变体仓库接口
type TakeoutVariantRepository interface {
	// 基础CRUD
	Create(variant *models.TakeoutFoodVariant) (int64, error)
	GetByID(id int64) (*models.TakeoutFoodVariant, error)
	GetVariantByID(id int64) (*models.TakeoutFoodVariant, error) // 增加 GetVariantByID 方法
	Update(variant *models.TakeoutFoodVariant) error
	Delete(id int64) error
	
	// 查询列表
	ListByFoodID(foodID int64) ([]*models.TakeoutFoodVariant, error)
	GetDefaultVariant(foodID int64) (*models.TakeoutFoodVariant, error)
	
	// 规格默认状态管理
	ResetDefaultExcept(foodID int64, exceptID int64) error
	
	// 库存管理
	UpdateStock(id int64, count int) error
	BatchUpdateStock(items map[int64]int) error
}

// takeoutVariantRepository 外卖规格变体仓库实现
type takeoutVariantRepository struct {
	ormer orm.Ormer
}

// NewTakeoutVariantRepository 创建外卖规格变体仓库实例
func NewTakeoutVariantRepository() TakeoutVariantRepository {
	return &takeoutVariantRepository{
		ormer: orm.NewOrm(),
	}
}

// Create 创建规格变体
func (r *takeoutVariantRepository) Create(variant *models.TakeoutFoodVariant) (int64, error) {
	variant.CreatedAt = time.Now()
	variant.UpdatedAt = time.Now()
	return r.ormer.Insert(variant)
}

// GetByID 根据ID获取规格变体
func (r *takeoutVariantRepository) GetByID(id int64) (*models.TakeoutFoodVariant, error) {
	variant := &models.TakeoutFoodVariant{ID: id}
	err := r.ormer.Read(variant)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("规格变体不存在")
		}
		return nil, err
	}
	return variant, nil
}

// GetVariantByID 根据ID获取规格变体
func (r *takeoutVariantRepository) GetVariantByID(id int64) (*models.TakeoutFoodVariant, error) {
	return r.GetByID(id)
}

// Update 更新规格变体
func (r *takeoutVariantRepository) Update(variant *models.TakeoutFoodVariant) error {
	variant.UpdatedAt = time.Now()
	_, err := r.ormer.Update(variant)
	return err
}

// Delete 删除规格变体
func (r *takeoutVariantRepository) Delete(id int64) error {
	variant := &models.TakeoutFoodVariant{ID: id}
	_, err := r.ormer.Delete(variant)
	return err
}

// ListByFoodID 根据食品ID查询规格变体列表
func (r *takeoutVariantRepository) ListByFoodID(foodID int64) ([]*models.TakeoutFoodVariant, error) {
	var variants []*models.TakeoutFoodVariant
	
	_, err := r.ormer.QueryTable(new(models.TakeoutFoodVariant)).
		Filter("food_id", foodID).
		OrderBy("sort_order", "id").
		All(&variants)
	
	return variants, err
}

// GetDefaultVariant 获取默认规格变体
func (r *takeoutVariantRepository) GetDefaultVariant(foodID int64) (*models.TakeoutFoodVariant, error) {
	var variant models.TakeoutFoodVariant
	
	err := r.ormer.QueryTable(new(models.TakeoutFoodVariant)).
		Filter("food_id", foodID).
		Filter("is_default", true).
		One(&variant)
	
	if err != nil {
		if err == orm.ErrNoRows {
			// 如果没有设置默认变体，则返回排序最靠前的变体
			err = r.ormer.QueryTable(new(models.TakeoutFoodVariant)).
				Filter("food_id", foodID).
				OrderBy("sort_order", "id").
				Limit(1).
				One(&variant)
			
			if err != nil {
				if err == orm.ErrNoRows {
					return nil, errors.New("该食品没有规格变体")
				}
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	
	return &variant, nil
}

// UpdateStock 更新库存和销量
func (r *takeoutVariantRepository) UpdateStock(id int64, count int) error {
	// 对于无限库存(-1)，仅更新销量
	var res orm.RawSeter
	if count > 0 {
		res = r.ormer.Raw("UPDATE takeout_food_variant SET sold_count = sold_count + ?, updated_at = ? WHERE id = ?", 
			count, time.Now(), id)
	} else {
		// 退货，只处理销量
		res = r.ormer.Raw("UPDATE takeout_food_variant SET sold_count = GREATEST(0, sold_count + ?), updated_at = ? WHERE id = ?", 
			count, time.Now(), id)
	}
	
	_, err := res.Exec()
	return err
}

// BatchUpdateStock 批量更新库存
func (r *takeoutVariantRepository) BatchUpdateStock(items map[int64]int) error {
	if len(items) == 0 {
		return nil
	}
	
	for id, count := range items {
		err := r.UpdateStock(id, count)
		if err != nil {
			return err
		}
	}
	
	return nil
}

// ResetDefaultExcept 重置除指定ID外的所有规格变体为非默认
func (r *takeoutVariantRepository) ResetDefaultExcept(foodID int64, exceptID int64) error {
	// 将指定食品下除exceptID外的所有规格的IsDefault设置为false
	_, err := r.ormer.QueryTable(new(models.TakeoutFoodVariant)).Filter("FoodID", foodID).Exclude("ID", exceptID).Update(orm.Params{
		"IsDefault": false,
		"UpdatedAt": time.Now(),
	})
	
	return err
}
