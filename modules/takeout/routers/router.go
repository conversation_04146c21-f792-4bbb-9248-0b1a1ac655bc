/**
 * 外卖模块路由配置
 *
 * 本文件定义了外卖模块的API路由配置，包括外卖商品、规格、套餐等相关接口。
 * 路由注册到系统中，支持与现有购物车和订单系统对接。
 */

package routers

import (
	"o_mall_backend/middlewares"
	"o_mall_backend/modules/takeout/controllers"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// InitTakeoutRouter 初始化外卖模块路由
func InitTakeoutRouter() {
	logs.Info("初始化外卖模块路由...")

	// 初始化外卖路由配置
	configureTakeoutRouter()

	logs.Info("外卖模块路由初始化完成")
}

// configureTakeoutRouter 配置外卖模块路由
func configureTakeoutRouter() {
	logs.Info("配置外卖模块路由...")

	// 客户端API前缀
	apiNs := web.NewNamespace("/api/v1/user",
		// 外卖商品相关API
		web.NSNamespace("/takeout",
			// 无需登录的API
			web.NSRouter("/categories", &controllers.TakeoutCategoryController{}, "get:List"),
			web.NSRouter("/categories/:id", &controllers.TakeoutCategoryController{}, "get:GetDetail"),
			web.NSRouter("/merchants/:id/categories", &controllers.TakeoutCategoryController{}, "get:GetMerchantCategories"),
			web.NSRouter("/global-categories", &controllers.GlobalCategoryController{}, "get:GetCategoryList"),
			web.NSRouter("/global-categories/tree", &controllers.GlobalCategoryController{}, "get:GetCategoryTree"),
			web.NSRouter("/merchants", &controllers.TakeoutFoodController{}, "get:List"),
			web.NSRouter("/merchants/:id", &controllers.TakeoutFoodController{}, "get:GetMerchantDetail"),
			web.NSRouter("/merchants/:id/foods", &controllers.TakeoutFoodController{}, "get:GetMerchantFoods"),
			web.NSRouter("/merchant/:merchant_id/promotions", &controllers.TakeoutPromotionController{}, "post:GetPromotionsByConditions;options:Options"),
			// 促销使用记录管理接口（用于调试和测试）
			web.NSRouter("/promotions/user-usage", &controllers.TakeoutPromotionController{}, "get:GetUserPromotionUsage"),
			web.NSRouter("/promotions/clear-user-usage", &controllers.TakeoutPromotionController{}, "delete:ClearUserPromotionUsage"),

			web.NSRouter("/foods/:id", &controllers.TakeoutFoodController{}, "get:GetDetail"),
			web.NSRouter("/foods/:id/variants", &controllers.TakeoutFoodController{}, "get:GetVariants"),
			web.NSRouter("/foods/:id/combo-items", &controllers.TakeoutFoodController{}, "get:GetComboItems"),
			web.NSBefore(middlewares.JWTFilter),
			// 获取商家促销和优惠券信息
			web.NSRouter("/merchants/promotions-coupons", &controllers.TakeoutFoodController{}, "post:GetMerchantsPromotionsAndCoupons"),
			// 获取商家可领取的优惠券和促销信息
			web.NSRouter("/merchants/:merchant_id/available-coupons", &controllers.TakeoutFoodController{}, "get:GetMerchantAvailableCoupons"),

			// 优惠券相关API (需要登录)
			web.NSNamespace("/coupons",
				web.NSBefore(middlewares.JWTFilter),
				web.NSRouter("/my-list", &controllers.TakeoutCouponController{}, "get:GetMyCoupons;options:Options"),

				web.NSRouter("/center", &controllers.TakeoutCouponController{}, "get:GetCouponCenterList;options:Options"),
				// 订单可用优惠券查询API
				web.NSRouter("/available-for-order", &controllers.TakeoutCouponController{}, "get:GetAvailableCouponsForOrder;options:Options"),
			),

			// 需要登录的API
			web.NSNamespace("/cart",
				web.NSBefore(middlewares.JWTFilter),
				web.NSRouter("/add", &controllers.TakeoutCartController{}, "post:Add;options:Options"),
				web.NSRouter("/update", &controllers.TakeoutCartController{}, "post:Update;options:Options"),
				web.NSRouter("/remove", &controllers.TakeoutCartController{}, "post:Remove;options:Options"),
				// 增加一个单独删除购物车商品的接口
				web.NSRouter("/:id", &controllers.TakeoutCartController{}, "delete:RemoveById;options:Options"),
				web.NSRouter("/list", &controllers.TakeoutCartController{}, "get:List;options:Options"),
				web.NSRouter("/count", &controllers.TakeoutCartController{}, "get:Count;options:Options"),
				web.NSRouter("/count-details", &controllers.TakeoutCartController{}, "get:CountDetails;options:Options"),
				web.NSRouter("/select", &controllers.TakeoutCartController{}, "post:Select;options:Options"),
				web.NSRouter("/checkout", &controllers.TakeoutCartController{}, "post:Checkout;options:Options"),
				web.NSRouter("/validate", &controllers.TakeoutCartController{}, "post:Validate;options:Options"),
				web.NSRouter("/clear-invalid", &controllers.TakeoutCartController{}, "delete:ClearInvalid;options:Options"),
			),

			// 订单相关API（需要登录）
			web.NSNamespace("/order",
				web.NSBefore(middlewares.JWTFilter),
				// 初始化控制器
				web.NSRouter("/create", &controllers.TakeoutOrderController{}, "post:CreateOrder;options:Options"),
				web.NSRouter("/payment/:orderID", &controllers.TakeoutOrderController{}, "get:GetOrderPayment;options:Options"),
				web.NSRouter("/detail/:orderID", &controllers.TakeoutOrderController{}, "get:GetOrderDetail;options:Options"),
				web.NSRouter("/list", &controllers.TakeoutOrderController{}, "get:ListUserOrders;options:Options"),
				web.NSRouter("/count", &controllers.TakeoutOrderController{}, "get:CountUserOrders;options:Options"),
				web.NSRouter("/cancel/:orderID", &controllers.TakeoutOrderController{}, "post:CancelOrder;options:Options"),
				web.NSRouter("/rate", &controllers.TakeoutOrderController{}, "post:RateOrder;options:Options"),

				// 支付相关API
				web.NSRouter("/pay/:orderID/create", &controllers.TakeoutOrderController{}, "post:CreatePayment;options:Options"),
				web.NSRouter("/pay/query/:orderID", &controllers.TakeoutOrderController{}, "get:QueryPayment;options:Options"),
				web.NSRouter("/pay/close/:orderID", &controllers.TakeoutOrderController{}, "post:ClosePayment;options:Options"),

				// 退款相关API
				web.NSRouter("/:orderID/refund", &controllers.TakeoutOrderController{}, "post:ApplyRefund;options:Options"),

				// 多订单合并支付API
				web.NSRouter("/pay/batch/create", &controllers.TakeoutOrderController{}, "post:CreateBatchPayment;options:Options"),
			),

			// 优惠券相关API（需要登录）
			web.NSNamespace("/coupons",
				web.NSBefore(middlewares.JWTFilter),
				web.NSRouter("/list", &controllers.TakeoutCouponController{}, "get:GetUserCoupons;options:Options"),
				web.NSRouter("/:id", &controllers.TakeoutCouponController{}, "get:GetCouponDetail;options:Options"),
				web.NSRouter("/:id/check", &controllers.TakeoutCouponController{}, "post:CheckCouponAvailability;options:Options"),
				web.NSRouter("/:id/validate", &controllers.TakeoutCouponController{}, "post:ValidateCouponForOrder;options:Options"),
				web.NSRouter("/available", &controllers.TakeoutCouponController{}, "get:GetAvailableCoupons;options:Options"),
				web.NSRouter("/history", &controllers.TakeoutCouponController{}, "get:GetCouponUsageHistory;options:Options"),
				web.NSRouter("/claim", &controllers.TakeoutCouponController{}, "post:ClaimCoupon;options:Options"),
				web.NSRouter("/calculate-discount", &controllers.TakeoutCouponController{}, "post:CalculateOrderDiscount;options:Options"),
			),
		),
	)

	// 管理后台API前缀
	adminNs := web.NewNamespace("/api/v1/admin",
		web.NSNamespace("/takeout",
			web.NSBefore(middlewares.JWTFilter),

			// 全局商品分类管理
			web.NSRouter("/global-categories", &controllers.GlobalCategoryController{}, "get:GetCategoryList"),
			web.NSRouter("/global-categories/tree", &controllers.GlobalCategoryController{}, "get:GetCategoryTree"),
			web.NSRouter("/global-categories", &controllers.GlobalCategoryController{}, "post:CreateCategory"),
			web.NSRouter("/global-categories/:id", &controllers.GlobalCategoryController{}, "get:GetCategory;put:UpdateCategory;delete:DeleteCategory"),

			web.NSRouter("/global-categories/:id/path", &controllers.GlobalCategoryController{}, "get:GetCategoryPath"),

			// 外卖分类管理
			web.NSRouter("/categories", &controllers.AdminTakeoutCategoryController{}, "get:List;post:Create"),
			web.NSRouter("/categories/:id", &controllers.AdminTakeoutCategoryController{}, "get:Get;put:Update;delete:Delete"),

			// 外卖商品管理
			web.NSRouter("/foods", &controllers.AdminTakeoutFoodController{}, "get:List;post:Create"),
			web.NSRouter("/foods/:id", &controllers.AdminTakeoutFoodController{}, "get:Get;put:Update;delete:Delete"),
			web.NSRouter("/foods/:id/status", &controllers.AdminTakeoutFoodController{}, "put:UpdateStatus"),
			web.NSRouter("/foods/:id/audit", &controllers.AdminTakeoutFoodController{}, "put:AuditFood"),

			// 规格管理
			web.NSRouter("/foods/:id/variants", &controllers.AdminTakeoutVariantController{}, "get:List;post:Create"),
			web.NSRouter("/variants/:id", &controllers.AdminTakeoutVariantController{}, "get:Get;put:Update;delete:Delete"),

			// 套餐组件管理
			web.NSRouter("/foods/:id/combo-items", &controllers.AdminTakeoutComboController{}, "get:ListItems;post:CreateItem"),
			web.NSRouter("/combo-items/:id", &controllers.AdminTakeoutComboController{}, "get:GetItem;put:UpdateItem;delete:DeleteItem"),

			// 套餐选项管理
			web.NSRouter("/combo-items/:id/options", &controllers.AdminTakeoutComboController{}, "get:ListOptions;post:CreateOption"),
			web.NSRouter("/combo-options/:id", &controllers.AdminTakeoutComboController{}, "get:GetOption;put:UpdateOption;delete:DeleteOption"),

			// 订单管理
			web.NSRouter("/orders", &controllers.AdminTakeoutOrderController{}, "get:List"),
			web.NSRouter("/orders/:id", &controllers.AdminTakeoutOrderController{}, "get:Get;put:Update"),
			web.NSRouter("/orders/statistics", &controllers.TakeoutMerchantOrderController{}, "get:GetMerchantOrderStatistics"),

			// 商家管理
			web.NSRouter("/merchant/:id/statistics", &controllers.AdminMerchantController{}, "get:GetMerchantStatistics"),
			web.NSRouter("/merchant/:id/foods", &controllers.AdminMerchantController{}, "get:GetMerchantFoods"),
		),
	)

	// 商家后台API前缀
	merchantNs := web.NewNamespace("/api/v1/merchant",
		web.NSNamespace("/takeout",
			web.NSBefore(middlewares.JWTMiddleware),
			web.NSBefore(middlewares.MerchantAuthMiddleware),
			// 添加商家日志中间件
			web.NSBefore(middlewares.MerchantLogMiddleware()),

			// 商户仪表板
			web.NSRouter("/statistics", &controllers.MerchantDashboardController{}, "get:GetStatistics"),

			web.NSRouter("/global-categories", &controllers.GlobalCategoryController{}, "get:GetCategoryList"),
			web.NSRouter("/global-categories/tree", &controllers.GlobalCategoryController{}, "get:GetCategoryTree"),

			// 外卖分类管理
			web.NSRouter("/categories", &controllers.MerchantTakeoutCategoryController{}, "get:List;post:Create"),
			web.NSRouter("/categories/:id", &controllers.MerchantTakeoutCategoryController{}, "get:Get;put:Update;delete:Delete"),

			// 外卖商品管理
			web.NSRouter("/foods", &controllers.MerchantTakeoutFoodController{}, "get:List;post:Create"),
			web.NSRouter("/foods/:id", &controllers.MerchantTakeoutFoodController{}, "get:Get;put:Update;delete:Delete"),
			web.NSRouter("/foods/:id/enable", &controllers.MerchantTakeoutFoodController{}, "put:Enable"),
			web.NSRouter("/foods/:id/disable", &controllers.MerchantTakeoutFoodController{}, "put:Disable"),
			web.NSRouter("/foods/clear-cache", &controllers.MerchantTakeoutFoodController{}, "post:ClearCache"),

			// 规格管理
			web.NSRouter("/foods/:id/variants", &controllers.MerchantTakeoutVariantController{}, "get:List;post:Create"),
			web.NSRouter("/variants/:id", &controllers.MerchantTakeoutVariantController{}, "get:Get;put:Update;delete:Delete"),

			// 套餐组件管理
			web.NSRouter("/foods/:id/combo-items", &controllers.MerchantTakeoutComboController{}, "get:ListItems;post:CreateItem"),
			web.NSRouter("/combo-items/:id", &controllers.MerchantTakeoutComboController{}, "get:GetItem;put:UpdateItem;delete:DeleteItem"),

			// 套餐选项管理
			web.NSRouter("/combo-items/:id/options", &controllers.MerchantTakeoutComboController{}, "get:ListOptions;post:CreateOption"),
			web.NSRouter("/combo-options/:id", &controllers.MerchantTakeoutComboController{}, "get:GetOption;put:UpdateOption;delete:DeleteOption"),

			// 订单管理
			web.NSRouter("/orders", &controllers.MerchantTakeoutOrderController{}, "get:List"),
			web.NSRouter("/orders/:id", &controllers.MerchantTakeoutOrderController{}, "get:Get;put:Update"),
			web.NSRouter("/orders/accept", &controllers.TakeoutMerchantOrderController{}, "post:AcceptOrder"),
			web.NSRouter("/orders/:id/cancel", &controllers.TakeoutMerchantOrderController{}, "post:CancelOrder"),
			// @Title 分配配送员
			// @Description 商家分配配送员接口，将创建已支付状态的runner订单
			web.NSRouter("/orders/assign", &controllers.TakeoutMerchantOrderController{}, "post:AssignDelivery"),
			web.NSRouter("/orders/statistics", &controllers.TakeoutMerchantOrderController{}, "get:GetMerchantOrderStatistics"),
			web.NSRouter("/orders/refund/process", &controllers.TakeoutMerchantOrderController{}, "post:ProcessRefund"),

			// 配送管理
			web.NSRouter("/delivery/start", &controllers.TakeoutDeliveryController{}, "post:StartDelivery"),
			web.NSRouter("/delivery/complete", &controllers.TakeoutDeliveryController{}, "post:CompleteDelivery"),
			web.NSRouter("/delivery/list", &controllers.TakeoutDeliveryController{}, "get:ListDeliveryOrders"),

			// 促销活动管理
			web.NSRouter("/promotions", &controllers.MerchantTakeoutPromotionController{}, "get:List;post:Create"),
			web.NSRouter("/promotions/:id", &controllers.MerchantTakeoutPromotionController{}, "get:Get;put:Update;delete:Delete"),
			web.NSRouter("/promotions/:id/publish", &controllers.MerchantTakeoutPromotionController{}, "put:Publish"),
			web.NSRouter("/promotions/:id/cancel", &controllers.MerchantTakeoutPromotionController{}, "put:Cancel"),
			web.NSRouter("/promotions/statistics", &controllers.MerchantTakeoutPromotionController{}, "get:GetStatistics"),

			// 优惠券管理
			web.NSRouter("/coupons", &controllers.MerchantTakeoutCouponController{}, "get:List;post:Create"),
			web.NSRouter("/coupons/:id", &controllers.MerchantTakeoutCouponController{}, "get:Get;put:Update;delete:Delete"),
			web.NSRouter("/coupons/:id/publish", &controllers.MerchantTakeoutCouponController{}, "post:Publish"),
			web.NSRouter("/coupons/:id/issue", &controllers.MerchantTakeoutCouponController{}, "post:Issue"),
			web.NSRouter("/coupons/batch-issue", &controllers.MerchantTakeoutCouponController{}, "post:BatchIssue"),
			web.NSRouter("/coupons/statistics", &controllers.MerchantTakeoutCouponController{}, "get:GetStatistics"),

			// 商户外卖统计
			web.NSRouter("/stats", &controllers.MerchantTakeoutStatsController{}, "get:GetMerchantTakeoutStats"),
		),
	)

	// 注册命名空间
	web.AddNamespace(apiNs)
	web.AddNamespace(adminNs)
	web.AddNamespace(merchantNs)

	logs.Info("注册外卖模块中间件...")

	// 注册中间件
	// 可以在这里添加外卖模块特定的中间件
}
