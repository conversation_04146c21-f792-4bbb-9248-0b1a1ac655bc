/**
 * 管理员外卖食品服务
 *
 * 本文件实现了管理员端外卖食品的业务逻辑层，处理后台管理系统的商品管理功能。
 * 与普通食品服务分离，以便于维护和扩展管理员特有功能。
 */

package services

import (
	"context"
	"errors"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
)

// GetFoodsPageForAdmin 管理员获取食品分页列表
func (s *takeoutFoodService) GetFoodsPageForAdmin(merchantID int64, categoryID int64, keyword string, status int, auditStatus int, page, pageSize int) ([]dto.AdminFoodListItemDTO, int64, error) {
	// 构建查询条件
	query := &dto.TakeoutFoodQueryRequest{
		MerchantID:  merchantID,
		CategoryID:  categoryID,
		Keyword:     keyword,
		Status:      status,
		AuditStatus: auditStatus,
		Page:        page,
		PageSize:    pageSize,
	}

	// 查询食品列表
	foods, total, err := s.foodRepo.List(query)
	if err != nil {
		logs.Error("管理员查询外卖食品列表失败: %v, 查询条件: %+v", err, query)
		return nil, 0, errors.New("查询食品列表失败")
	}

	// 批量获取分类信息，避免N+1查询问题
	categoryIDs := make([]int64, 0)
	categoryIDSet := make(map[int64]bool)
	merchantIDs := make([]int64, 0)
	merchantIDSet := make(map[int64]bool)
	for _, food := range foods {
		if !categoryIDSet[food.CategoryID] {
			categoryIDs = append(categoryIDs, food.CategoryID)
			categoryIDSet[food.CategoryID] = true
		}
		if !merchantIDSet[food.MerchantID] {
			merchantIDs = append(merchantIDs, food.MerchantID)
			merchantIDSet[food.MerchantID] = true
		}
	}
	
	// 批量查询分类信息
	categoryMap := make(map[int64]string)
	if len(categoryIDs) > 0 {
		categories, err := s.categoryRepo.GetByIDs(categoryIDs)
		if err == nil {
			for categoryID, category := range categories {
				categoryMap[categoryID] = category.Name
			}
		}
	}
	
	// 批量查询商家信息
	merchantMap := make(map[int64]string)
	ctx := context.Background()
	for _, merchantID := range merchantIDs {
		merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
		if err == nil && merchant != nil {
			merchantMap[merchantID] = merchant.Name
		}
	}

	// 构造响应DTO
	foodDTOs := make([]dto.AdminFoodListItemDTO, 0, len(foods))
	for _, food := range foods {
		// 从批量查询结果中获取商家名称
		merchantName := merchantMap[food.MerchantID]

		// 从批量查询结果中获取分类名称
		categoryName := categoryMap[food.CategoryID]

		dto := dto.AdminFoodListItemDTO{
			ID:              food.ID,
			MerchantID:      food.MerchantID,
			MerchantName:    merchantName,
			Name:            food.Name,
			Brief:           food.Brief,
			Image:           food.Image,
			Price:           food.Price,
			OriginalPrice:   food.OriginalPrice,
			PackagingFee:    food.PackagingFee,
			PreparationTime: food.PreparationTime,
			IsSpicy:         food.IsSpicy,
			IsCombination:   food.IsCombination,
			HasVariants:     food.HasVariants,
			SoldOut:         food.SoldOut,
			TotalSold:       food.TotalSold,
			CategoryID:      food.CategoryID,
			CategoryName:    categoryName,
			Status:          food.Status,
			AuditStatus:     food.AuditStatus,
			IsRecommend:     food.IsRecommend,
			SortOrder:       food.SortOrder,
			CreatedAt:       food.CreatedAt,
		}

		// 转换标签
		if food.Tags != "" {
			dto.Tags = strings.Split(food.Tags, ",")
		}

		// 如果有多规格，计算价格区间
		if food.HasVariants {
			minPrice, maxPrice, err := s.GetFoodPriceRange(food.ID)
			if err == nil {
				dto.MinPrice = minPrice
				dto.MaxPrice = maxPrice
			} else {
				dto.MinPrice = food.Price
				dto.MaxPrice = food.Price
			}
		} else {
			dto.MinPrice = food.Price
			dto.MaxPrice = food.Price
		}

		foodDTOs = append(foodDTOs, dto)
	}

	return foodDTOs, total, nil
}

// GetFoodDetailForAdmin 管理员获取食品详情
func (s *takeoutFoodService) GetFoodDetailForAdmin(id int64) (*dto.AdminFoodDetailDTO, error) {
	// 获取食品基本信息
	food, err := s.foodRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 获取分类信息
	category, err := s.categoryRepo.GetByID(food.CategoryID)
	if err != nil {
		logs.Warn("获取分类信息失败: %v, 食品ID: %d", err, id)
		// 不影响食品信息返回
	}

	categoryName := ""
	if category != nil {
		categoryName = category.Name
	}

	// 获取商家名称
	merchantName := ""
	ctx := context.Background()
	merchant, err := s.merchantRepo.GetByID(ctx, food.MerchantID)
	if err == nil && merchant != nil {
		merchantName = merchant.Name
	}

	// 构造响应DTO
	detailDTO := &dto.AdminFoodDetailDTO{
		ID:              food.ID,
		MerchantID:      food.MerchantID,
		MerchantName:    merchantName,
		Name:            food.Name,
		Description:     food.Description,
		Brief:           food.Brief,
		Image:           food.Image,
		Price:           food.Price,
		OriginalPrice:   food.OriginalPrice,
		PackagingFee:    food.PackagingFee,
		PreparationTime: food.PreparationTime,
		IsCombination:   food.IsCombination,
		IsSpicy:         food.IsSpicy,
		HasVariants:     food.HasVariants,
		SoldOut:         food.SoldOut,
		DailyLimit:      food.DailyLimit,
		TotalSold:       food.TotalSold,
		CategoryID:      food.CategoryID,
		CategoryName:    categoryName,
		Status:          food.Status,
		AuditStatus:     food.AuditStatus,
		AuditorID:       food.AuditorID,
		AuditReason:     food.AuditReason,
		AuditTime:       food.AuditTime,
		IsRecommend:     food.IsRecommend,
		SortOrder:       food.SortOrder,
		CreatedAt:       food.CreatedAt,
		UpdatedAt:       food.UpdatedAt,
	}

	// 转换标签
	if food.Tags != "" {
		detailDTO.Tags = strings.Split(food.Tags, ",")
	}

	// 转换关键词
	if food.Keywords != "" {
		detailDTO.Keywords = strings.Split(food.Keywords, ",")
	}

	// 获取变体信息（如果有）
	if food.HasVariants {
		variants, err := s.variantRepo.ListByFoodID(id)
		if err != nil {
			logs.Warn("获取规格变体失败: %v, 食品ID: %d", err, id)
		} else {
			variantDTOs := make([]dto.TakeoutFoodVariantDTO, 0, len(variants))
			for _, v := range variants {
				variantDTOs = append(variantDTOs, dto.TakeoutFoodVariantDTO{
					ID:            v.ID,
					FoodID:        v.FoodID,
					Name:          v.Name,
					Description:   v.Description,
					Image:         v.Image,
					Price:         v.Price,
					OriginalPrice: v.OriginalPrice,
					Stock:         v.Stock,
					SoldCount:     v.SoldCount,
					IsDefault:     v.IsDefault,
					SortOrder:     v.SortOrder,
				})
			}
			detailDTO.Variants = variantDTOs
		}

		// 计算价格区间
		minPrice, maxPrice, err := s.GetFoodPriceRange(id)
		if err == nil {
			detailDTO.MinPrice = minPrice
			detailDTO.MaxPrice = maxPrice
		} else {
			detailDTO.MinPrice = food.Price
			detailDTO.MaxPrice = food.Price
		}
	} else {
		detailDTO.MinPrice = food.Price
		detailDTO.MaxPrice = food.Price
	}

	// 获取套餐组件信息（如果是套餐组合）
	if food.IsCombination {
		comboItems, err := s.comboRepo.ListComboItemsByFoodID(id)
		if err != nil {
			logs.Warn("获取套餐组件失败: %v, 食品ID: %d", err, id)
		} else {
			comboItemDTOs := make([]dto.TakeoutComboItemDTO, 0, len(comboItems))
			for _, item := range comboItems {
				// 获取套餐选项
				options, err := s.comboRepo.ListComboOptionsByItemID(item.ID)
				if err != nil {
					logs.Warn("获取套餐选项失败: %v, 组件ID: %d", err, item.ID)
					continue
				}

				optionDTOs := make([]dto.TakeoutComboOptionDTO, 0, len(options))
				for _, opt := range options {
					optionDTOs = append(optionDTOs, dto.TakeoutComboOptionDTO{
						ID:           opt.ID,
						ComboItemID:  opt.ComboItemID,
						Name:         opt.Name,
						Description:  opt.Description,
						Image:        opt.Image,
						ExtraPrice:   opt.ExtraPrice,
						Stock:        opt.Stock,
						SoldCount:    opt.SoldCount,
						IsDefault:    opt.IsDefault,
						MaxPerOrder:  opt.MaxPerOrder,
						IsIndividual: opt.IsIndividual,
						SortOrder:    opt.SortOrder,
					})
				}

				comboItemDTOs = append(comboItemDTOs, dto.TakeoutComboItemDTO{
					ID:          item.ID,
					FoodID:      item.FoodID,
					Name:        item.Name,
					Description: item.Description,
					MinSelect:   item.MinSelect,
					MaxSelect:   item.MaxSelect,
					IsRequired:  item.IsRequired,
					SortOrder:   item.SortOrder,
					Options:     optionDTOs,
				})
			}
			detailDTO.ComboItems = comboItemDTOs
		}
	}

	return detailDTO, nil
}

// CreateFoodForAdmin 管理员创建食品
func (s *takeoutFoodService) CreateFoodForAdmin(req *dto.CreateFoodRequest) (int64, error) {
	// 检查分类是否存在
	_, err := s.categoryRepo.GetByID(req.CategoryID)
	if err != nil {
		return 0, err
	}

	// 创建食品模型
	food := &models.TakeoutFood{
		MerchantID:      req.MerchantID,
		CategoryID:      req.CategoryID,
		Name:            req.Name,
		Description:     req.Description,
		Brief:           req.Brief,
		Image:           req.Image,
		Price:           req.Price,
		OriginalPrice:   req.OriginalPrice,
		PackagingFee:    req.PackagingFee,
		PreparationTime: req.PreparationTime,
		IsCombination:   req.IsCombination,
		IsSpicy:         req.IsSpicy,
		HasVariants:     req.HasVariants,
		DailyLimit:      req.DailyLimit,
		Tags:            strings.Join(req.Tags, ","),
		Keywords:        req.Keywords,
		Status:          req.Status,
		IsRecommend:     req.IsRecommend,
		SortOrder:       req.SortOrder,
		IsVisible:       true,
	}

	// 如果没有设置原价，则使用当前价格
	if food.OriginalPrice <= 0 {
		food.OriginalPrice = food.Price
	}

	// 如果没有设置备餐时间，则默认为15分钟
	if food.PreparationTime <= 0 {
		food.PreparationTime = 15
	}

	// 保存到数据库
	id, err := s.foodRepo.Create(food)
	if err != nil {
		logs.Error("管理员创建外卖食品失败: %v, 请求: %+v", err, req)
		return 0, errors.New("创建食品失败")
	}

	return id, nil
}

// UpdateFoodForAdmin 管理员更新食品
func (s *takeoutFoodService) UpdateFoodForAdmin(req *dto.UpdateFoodRequest) error {
	// 获取现有食品信息
	food, err := s.foodRepo.GetByID(req.ID)
	if err != nil {
		return err
	}

	// 如果分类ID有变更，检查新分类是否存在
	if req.CategoryID > 0 && req.CategoryID != food.CategoryID {
		_, err = s.categoryRepo.GetByID(req.CategoryID)
		if err != nil {
			return err
		}
		food.CategoryID = req.CategoryID
	}

	// 更新字段
	if req.Name != "" {
		food.Name = req.Name
	}
	if req.Description != "" {
		food.Description = req.Description
	}
	if req.Brief != "" {
		food.Brief = req.Brief
	}
	if req.Image != "" {
		food.Image = req.Image
	}
	if req.Price > 0 {
		food.Price = req.Price
	}
	if req.OriginalPrice > 0 {
		food.OriginalPrice = req.OriginalPrice
	}
	if req.PackagingFee >= 0 {
		food.PackagingFee = req.PackagingFee
	}
	if req.PreparationTime > 0 {
		food.PreparationTime = req.PreparationTime
	}
	if req.DailyLimit >= 0 {
		food.DailyLimit = req.DailyLimit
	}
	if len(req.Tags) > 0 {
		food.Tags = strings.Join(req.Tags, ",")
	}
	if req.Keywords != "" {
		food.Keywords = req.Keywords
	}
	if req.Status >= 0 {
		food.Status = req.Status
	}
	food.SoldOut = req.SoldOut
	food.IsRecommend = req.IsRecommend
	food.SortOrder = req.SortOrder

	// 保存到数据库
	err = s.foodRepo.Update(food)
	if err != nil {
		logs.Error("管理员更新外卖食品失败: %v, 请求: %+v", err, req)
		return errors.New("更新食品失败")
	}

	return nil
}

// UpdateFoodStatusForAdmin 管理员更新食品状态
func (s *takeoutFoodService) UpdateFoodStatusForAdmin(req *dto.UpdateFoodStatusRequest) error {
	return s.UpdateFoodStatus(req.ID, req.Status)
}

// DeleteFoodForAdmin 管理员删除食品
func (s *takeoutFoodService) DeleteFoodForAdmin(id int64) error {
	// TODO: 检查该食品是否已被订单引用

	// 删除食品
	err := s.foodRepo.Delete(id)
	if err != nil {
		logs.Error("管理员删除外卖食品失败: %v, ID: %d", err, id)
		return errors.New("删除食品失败")
	}

	return nil
}

// ValidateFoodOwnership 验证食品所有权
func (s *takeoutFoodService) ValidateFoodOwnership(foodID int64, merchantID int64) (bool, error) {
	// 获取食品信息
	food, err := s.foodRepo.GetByID(foodID)
	if err != nil {
		return false, err
	}

	// 检查商家ID是否匹配
	return food.MerchantID == merchantID, nil
}
