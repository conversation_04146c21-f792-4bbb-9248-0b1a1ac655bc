/**
 * 管理员外卖订单服务
 *
 * 本文件实现了管理员端外卖订单的业务逻辑层，处理后台管理系统的订单管理功能。
 * 与普通订单服务分离，以便于维护和扩展管理员特有功能。
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/dto"
	takeoutModels "o_mall_backend/modules/takeout/models"
)

// GetOrdersPageForAdmin 管理员获取订单分页列表
func (s *takeoutOrderService) GetOrdersPageForAdmin(merchantID int64, orderNumber string, status int, startTime, endTime string, page, pageSize int) ([]dto.AdminOrderListItemDTO, int64, error) {
	// 构造查询条件
	queryParams := map[string]interface{}{
		"merchantID":   merchantID,
		"orderNumber":  orderNumber,
		"status":       status,
		"startTime":    startTime,
		"endTime":      endTime,
		"page":         page,
		"pageSize":     pageSize,
	}

	// 查询订单列表
	orders, total, err := s.orderRepo.QueryOrders(queryParams)
	if err != nil {
		logs.Error("管理员查询订单列表失败: %v, 查询条件: %+v", err, queryParams)
		return nil, 0, errors.New("查询订单列表失败")
	}

	// 构造响应DTO
	orderDTOs := make([]dto.AdminOrderListItemDTO, 0, len(orders))
	for _, order := range orders {
		// 获取订单基本信息
		baseOrder, err := s.baseOrderSvc.GetOrder(context.Background(), order.OrderID)
		if err != nil {
			logs.Error("获取基础订单信息失败: %v, 订单ID: %d", err, order.OrderID)
			continue
		}

		// 获取配送员信息
		deliveryStaffName := ""
		// TODO: 从配送员服务获取配送员名称

		// 构造DTO
		orderDTO := dto.AdminOrderListItemDTO{
			ID:                 baseOrder.ID,
			OrderNumber:        order.OrderNo,
			MerchantID:         order.MerchantID,
			MerchantName:       "", // 需从商家服务获取
			UserID:             baseOrder.UserID,
			UserName:           baseOrder.UserName,
			Status:             baseOrder.Status,
			StatusText:         getOrderStatusText(baseOrder.Status),
			DeliveryStatus:     order.DeliveryStatus,
			DeliveryStatusText: getDeliveryStatusText(order.DeliveryStatus),
			DeliveryStaffID:    order.DeliveryStaffID,
			DeliveryStaffName:  deliveryStaffName,
			TotalAmount:        baseOrder.TotalAmount,
			PayAmount:          baseOrder.TotalAmount - baseOrder.DiscountAmount, // 实付金额 = 总金额 - 优惠金额
			ItemCount:          0, // 需要计算或从服务获取
			PaymentMethod:      baseOrder.PayMethod,
			PaymentStatus:      baseOrder.PayStatus,
			PaymentStatusText:  getPaymentStatusText(baseOrder.PayStatus),
			CreatedAt:          baseOrder.CreatedAt,
		}

		orderDTOs = append(orderDTOs, orderDTO)
	}

	return orderDTOs, total, nil
}

// GetOrderDetailForAdmin 管理员获取订单详情
func (s *takeoutOrderService) GetOrderDetailForAdmin(orderID int64) (*dto.AdminOrderDetailDTO, error) {
	// 查询外卖订单扩展信息
	orderExt, err := s.orderRepo.GetByOrderID(orderID)
	if err != nil {
		logs.Error("获取外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return nil, errors.New("获取订单详情失败")
	}

	// 验证订单类型
	if orderExt.OrderType != takeoutModels.OrderTypeTakeout {
		return nil, errors.New("不是外卖订单")
	}

	// 获取基础订单信息
	baseOrder, err := s.baseOrderSvc.GetOrder(context.Background(), orderID)
	if err != nil {
		logs.Error("获取基础订单信息失败: %v, 订单ID: %d", err, orderID)
		return nil, errors.New("获取订单详情失败")
	}

	// 当前假设从外卖订单项表查询，而不是使用baseOrderSvc.GetOrderItems
	// 这里可能需要根据实际情况来实现获取订单项的逻辑
	// 这里模拟一个空的订单项列表
	orderItems := []struct{
		ID          int64
		OrderID     int64
		ProductID   int64
		ProductName string
		ProductImage string
		VariantID   int64
		VariantName string
		Price       float64
		Quantity    int
		Amount      float64
		PackagingFee float64
		ComboInfo   string
	}{}
	
	// 构造订单项DTO
	itemDTOs := make([]dto.OrderItemDTO, 0, len(orderItems))
	for _, item := range orderItems {
		itemDTO := dto.OrderItemDTO{
			ID:           item.ID,
			OrderID:      item.OrderID,
			FoodID:       item.ProductID,
			FoodName:     item.ProductName,
			FoodImage:    item.ProductImage,
			VariantID:    item.VariantID,
			VariantName:  item.VariantName,
			Price:        item.Price,
			Quantity:     item.Quantity,
			Amount:       item.Amount,
			PackagingFee: item.PackagingFee,
			CombosInfo:   item.ComboInfo,
		}
		itemDTOs = append(itemDTOs, itemDTO)
	}

	// 获取配送员信息
	deliveryStaffName := ""
	deliveryStaffPhone := ""
	// TODO: 从配送员服务获取配送员信息

	// 需要处理收货地址，将Address对象转换为字符串
	deliveryAddress := ""
	if baseOrder.Address != nil {
		deliveryAddress = fmt.Sprintf("%s %s %s %s", 
			baseOrder.Address.Province,
			baseOrder.Address.City,
			baseOrder.Address.District,
			baseOrder.Address.Detail)
	}

	// 构造订单详情DTO
	// 处理AcceptedTime的空时间判断
	var acceptedTime *time.Time
	if !orderExt.AcceptedTime.IsZero() {
		temp := orderExt.AcceptedTime
		acceptedTime = &temp
	}

	detailDTO := &dto.AdminOrderDetailDTO{
		ID:                 baseOrder.ID,
		OrderNumber:        orderExt.OrderNo,
		Type:               orderExt.OrderType,
		MerchantID:         orderExt.MerchantID,
		MerchantName:       "", // 需从商家服务获取
		UserID:             baseOrder.UserID,
		UserName:           baseOrder.UserName,
		UserPhone:          "", // 可能需要从用户服务获取
		Status:             baseOrder.Status,
		StatusText:         getOrderStatusText(baseOrder.Status),
		DeliveryStatus:     orderExt.DeliveryStatus,
		DeliveryStatusText: getDeliveryStatusText(orderExt.DeliveryStatus),
		DeliveryType:       orderExt.DeliveryType,
		DeliveryTypeText:   getDeliveryTypeText(orderExt.DeliveryType),
		DeliveryFee:        orderExt.DeliveryFee,
		DeliveryAddress:    deliveryAddress,
		DeliveryTime:       orderExt.EstimatedDeliveryTime,
		DeliveryStaffID:    orderExt.DeliveryStaffID,
		DeliveryStaffName:  deliveryStaffName,
		DeliveryStaffPhone: deliveryStaffPhone,
		ItemAmount:         0, // 需要从订单里计算
		PackagingFee:       orderExt.PackagingFee,
		DiscountAmount:     baseOrder.DiscountAmount,
		TotalAmount:        baseOrder.TotalAmount,
		PayMethod:          baseOrder.PayMethod,
		PayMethodText:      getPaymentMethodText(baseOrder.PayMethod),
		PayTime:            time.Now(), // 由于类型不匹配，暂时使用当前时间
		PayStatus:          baseOrder.PayStatus,
		PayStatusText:      getPaymentStatusText(baseOrder.PayStatus),
		AcceptedTime:       acceptedTime, // 商家接单时间
		RefundAmount:       baseOrder.RefundAmount,
		RefundTime:         time.Now(), // 由于类型不匹配，暂时使用当前时间
		RefundReason:       "", // 需要从退款记录获取
		CancelReason:       "", // 可能需要从订单日志获取
		Remark:             orderExt.Remark,
		Items:              itemDTOs,
		IsPreOrder:         orderExt.IsPreOrder,
		PreOrderTime:       orderExt.PreOrderTime,
		CreatedAt:          baseOrder.CreatedAt,
		UpdatedAt:          baseOrder.UpdatedAt,
	}

	return detailDTO, nil
}

// UpdateOrderForAdmin 管理员更新订单
func (s *takeoutOrderService) UpdateOrderForAdmin(req *dto.UpdateOrderRequest) error {
	// 查询外卖订单扩展信息
	orderExt, err := s.orderRepo.GetByOrderID(req.ID)
	if err != nil {
		logs.Error("获取外卖订单扩展信息失败: %v, 订单ID: %d", err, req.ID)
		return errors.New("获取订单信息失败")
	}

	// 验证订单类型
	if orderExt.OrderType != takeoutModels.OrderTypeTakeout {
		return errors.New("不是外卖订单")
	}

	// 获取基础订单信息
	baseOrder, err := s.baseOrderSvc.GetOrder(context.Background(), req.ID)
	if err != nil {
		logs.Error("获取基础订单信息失败: %v, 订单ID: %d", err, req.ID)
		return errors.New("获取订单信息失败")
	}

	// 更新订单状态
	if req.Status > 0 && req.Status != baseOrder.Status {
		// 使用事务更新状态
		err = updateOrderStatusWithTransaction(s, req.ID, req.Status)
		if err != nil {
			logs.Error("管理员更新订单状态失败: %v, 订单ID: %d, 状态: %d", err, req.ID, req.Status)
			return fmt.Errorf("更新订单状态失败: %v", err)
		}
	}

	// 更新外卖订单扩展信息
	needUpdateExt := false
	
	// 更新配送状态
	if req.DeliveryStatus > 0 && req.DeliveryStatus != orderExt.DeliveryStatus {
		orderExt.DeliveryStatus = req.DeliveryStatus
		needUpdateExt = true
	}
	
	// 更新配送员
	if req.DeliveryStaffID > 0 && req.DeliveryStaffID != orderExt.DeliveryStaffID {
		orderExt.DeliveryStaffID = req.DeliveryStaffID
		needUpdateExt = true
	}
	
	// 更新配送费
	if req.DeliveryFee >= 0 && req.DeliveryFee != orderExt.DeliveryFee {
		orderExt.DeliveryFee = req.DeliveryFee
		needUpdateExt = true
	}
	
	// 如果需要更新扩展信息
	if needUpdateExt {
		err = s.orderRepo.Update(orderExt)
		if err != nil {
			logs.Error("更新外卖订单扩展信息失败: %v, 订单ID: %d", err, req.ID)
			return fmt.Errorf("更新订单信息失败: %v", err)
		}
	}
	
	// 更新订单备注
	if req.Remark != "" && req.Remark != baseOrder.Remark {
		// TODO: 调用基础订单服务更新备注
	}
	
	// 处理退款
	if req.RefundAmount > 0 && req.RefundAmount != baseOrder.RefundAmount {
		// TODO: 调用基础订单服务处理退款
	}

	return nil
}

// 辅助函数：使用事务更新订单状态
func updateOrderStatusWithTransaction(s *takeoutOrderService, orderID int64, status int) error {
	// 查询订单
	order, err := s.orderRepo.GetByOrderID(orderID)
	if err != nil {
		logs.Error("订单状态更新失败，查询订单失败: %v", err)
		return errors.New("订单不存在")
	}

	// 当前状态与请求状态相同，无需更新
	if order.DeliveryStatus == status {
		return nil
	}

	// 更新状态
	order.DeliveryStatus = status
	order.UpdatedAt = time.Now()

	// 根据状态设置其他字段
	switch status {
	case takeoutModels.DeliveryStatusWaiting: // 等待配送
		// 无需特殊处理
	case takeoutModels.DeliveryStatusPicking: // 取餐中
		order.DeliveryStartTime = time.Now()
	case takeoutModels.DeliveryStatusDelivering: // 配送中
		// 处理配送中状态
	case takeoutModels.DeliveryStatusCompleted: // 已送达
		order.DeliveryEndTime = time.Now()
	}

	// 执行更新
	err = s.orderRepo.Update(order)
	if err != nil {
		logs.Error("更新订单状态失败: %v", err)
		return errors.New("更新订单状态失败")
	}

	return nil
}

// 辅助函数：获取订单状态文本
func getOrderStatusText(status int) string {
	switch status {
	case 0:
		return "待付款"
	case 1:
		return "已支付"
	case 2:
		return "处理中"
	case 3:
		return "已发货"
	case 4:
		return "已完成"
	case 5:
		return "已取消"
	default:
		return "未知状态"
	}
}

// 辅助函数：获取配送状态文本
func getDeliveryStatusText(status int) string {
	switch status {
	case takeoutModels.DeliveryStatusWaiting:
		return "待配送"
	case takeoutModels.DeliveryStatusPicking:
		return "取餐中"
	case takeoutModels.DeliveryStatusDelivering:
		return "配送中"
	case takeoutModels.DeliveryStatusCompleted:
		return "已送达"
	case takeoutModels.DeliveryStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// 辅助函数：获取支付状态文本
func getPaymentStatusText(status int) string {
	switch status {
	case 0:
		return "未支付"
	case 1:
		return "已支付"
	case 2:
		return "已退款"
	default:
		return "未知状态"
	}
}

// 辅助函数：获取支付方式文本
func getPaymentMethodText(method int) string {
	switch method {
	case 1:
		return "微信支付"
	case 2:
		return "支付宝"
	case 3:
		return "银行卡"
	case 4:
		return "现金"
	case 5:
		return "余额支付"
	default:
		return "未知支付方式"
	}
}

// 辅助函数：获取配送类型文本
func getDeliveryTypeText(deliveryType int) string {
	switch deliveryType {
	case 1:
		return "商家自送"
	case 2:
		return "第三方配送"
	case 3:
		return "到店自取"
	default:
		return "未知配送类型"
	}
}
