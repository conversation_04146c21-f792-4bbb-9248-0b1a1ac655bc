/**
 * 全局商品分类服务层
 *
 * 本文件实现了全局商品分类的业务逻辑，作为控制器和资源库之间的桥梁。
 * 提供分类的CRUD操作、分类树构建等功能。
 */

package services

import (
	"errors"

	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
)

// GlobalCategoryService 全局商品分类服务
type GlobalCategoryService struct {
	repo *repositories.GlobalCategoryRepository
}

// NewGlobalCategoryService 创建全局商品分类服务实例
func NewGlobalCategoryService() *GlobalCategoryService {
	return &GlobalCategoryService{
		repo: repositories.NewGlobalCategoryRepository(),
	}
}

// CreateCategory 创建全局分类
func (s *GlobalCategoryService) CreateCategory(category *models.GlobalCategory) (int64, error) {
	// 检查分类名称是否为空
	if category.Name == "" {
		return 0, errors.New("分类名称不能为空")
	}

	// 检查分类编码是否为空
	if category.Code == "" {
		return 0, errors.New("分类编码不能为空")
	}

	// 检查编码唯一性
	existingCategory, err := s.repo.GetByCode(category.Code)
	if err != nil {
		return 0, err
	}
	if existingCategory != nil {
		return 0, errors.New("分类编码已存在")
	}

	return s.repo.Create(category)
}

// UpdateCategory 更新全局分类
func (s *GlobalCategoryService) UpdateCategory(category *models.GlobalCategory) error {
	// 检查分类是否存在
	existingCategory, err := s.repo.GetByID(category.ID)
	if err != nil {
		return err
	}
	if existingCategory == nil {
		return errors.New("分类不存在")
	}

	// 检查分类名称是否为空
	if category.Name == "" {
		return errors.New("分类名称不能为空")
	}

	// 检查分类编码是否为空
	if category.Code == "" {
		return errors.New("分类编码不能为空")
	}

	// 检查编码唯一性
	codeDuplicate, err := s.repo.GetByCode(category.Code)
	if err != nil {
		return err
	}
	if codeDuplicate != nil && codeDuplicate.ID != category.ID {
		return errors.New("分类编码已存在")
	}

	return s.repo.Update(category)
}

// DeleteCategory 删除全局分类
func (s *GlobalCategoryService) DeleteCategory(id int64) error {
	// 检查分类是否存在
	existingCategory, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}
	if existingCategory == nil {
		return errors.New("分类不存在")
	}

	return s.repo.Delete(id)
}

// GetCategoryByID 根据ID获取全局分类
func (s *GlobalCategoryService) GetCategoryByID(id int64) (*models.GlobalCategory, error) {
	return s.repo.GetByID(id)
}

// GetAllCategories 获取所有全局分类
func (s *GlobalCategoryService) GetAllCategories() ([]*models.GlobalCategory, error) {
	return s.repo.GetAll()
}

// GetRootCategories 获取所有根分类
func (s *GlobalCategoryService) GetRootCategories() ([]*models.GlobalCategory, error) {
	return s.repo.GetRootCategories()
}

// GetChildCategories 获取子分类列表
func (s *GlobalCategoryService) GetChildCategories(parentID int64) ([]*models.GlobalCategory, error) {
	return s.repo.GetChildren(parentID)
}

// GetCategoryTree 获取分类树
func (s *GlobalCategoryService) GetCategoryTree() ([]*models.GlobalCategoryTreeNode, error) {
	return s.repo.BuildCategoryTree()
}

// GetCategoryPath 获取分类路径（从根分类到当前分类）
func (s *GlobalCategoryService) GetCategoryPath(id int64) ([]*models.GlobalCategory, error) {
	return s.repo.GetCategoryPath(id)
}
