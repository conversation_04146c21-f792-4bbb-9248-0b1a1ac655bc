/**
 * 商户仪表板服务
 *
 * 本文件实现了商户仪表板相关的业务逻辑，处理商户日常经营数据统计。
 * 包括今日订单数、销售额、待处理订单等多种统计数据的计算和汇总。
 */

package services

import (
	"time"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// MerchantDashboardService 商户仪表板服务接口
type MerchantDashboardService interface {
	// GetDashboardStatistics 获取商户仪表板统计数据
	GetDashboardStatistics(merchantID int64) (*dto.MerchantDashboardStatisticsDTO, error)
}

// merchantDashboardService 商户仪表板服务实现
type merchantDashboardService struct {
	orderRepo  repositories.TakeoutOrderRepository
	foodRepo   repositories.TakeoutFoodRepository
	refundRepo repositories.TakeoutRefundRepository
}

// NewMerchantDashboardService 创建商户仪表板服务实例
func NewMerchantDashboardService() MerchantDashboardService {
	return &merchantDashboardService{
		orderRepo:  repositories.NewTakeoutOrderRepository(),
		foodRepo:   repositories.NewTakeoutFoodRepository(),
		refundRepo: repositories.NewTakeoutRefundRepository(),
	}
}

// GetDashboardStatistics 获取商户仪表板统计数据
func (s *merchantDashboardService) GetDashboardStatistics(merchantID int64) (*dto.MerchantDashboardStatisticsDTO, error) {
	// 获取今日开始和结束时间
	todayStart := time.Now().Truncate(24 * time.Hour)
	todayEnd := todayStart.Add(24*time.Hour - time.Second)

	// 获取今日订单数
	todayOrders, err := s.orderRepo.CountOrdersByMerchantAndDateRange(merchantID, todayStart, todayEnd, 0)
	if err != nil {
		logs.Error("统计今日订单数失败: %v, 商家ID: %d", err, merchantID)
		todayOrders = 0
	}

	// 获取今日销售额
	todaySales, err := s.orderRepo.SumOrderAmountByMerchantAndDateRange(merchantID, todayStart, todayEnd)
	if err != nil {
		logs.Error("统计今日销售额失败: %v, 商家ID: %d", err, merchantID)
		todaySales = 0
	}

	// 获取待处理订单数（已支付但未完成配送的订单）
	pendingOrders, err := s.orderRepo.CountOrdersByMerchantAndStatus(merchantID, OrderStatusPaid)
	if err != nil {
		logs.Error("统计待处理订单数失败: %v, 商家ID: %d", err, merchantID)
		pendingOrders = 0
	}

	// 获取待发货订单数（已接单但未开始配送的订单）
	pendingShipment, err := s.orderRepo.CountOrdersByMerchantAndStatus(merchantID, OrderStatusProcessing)
	if err != nil {
		logs.Error("统计待发货订单数失败: %v, 商家ID: %d", err, merchantID)
		pendingShipment = 0
	}

	// 获取待退款订单数
	pendingRefund, err := s.refundRepo.CountPendingRefundsByMerchant(merchantID)
	if err != nil {
		logs.Error("统计待退款订单数失败: %v, 商家ID: %d", err, merchantID)
		pendingRefund = 0
	}

	// 获取库存不足商品数
	lowStockThreshold := 10 // 库存阈值，低于此值视为库存不足
	lowStockProducts, err := s.foodRepo.CountLowStockFoodsByMerchant(merchantID, lowStockThreshold)
	if err != nil {
		logs.Error("统计库存不足商品数失败: %v, 商家ID: %d", err, merchantID)
		lowStockProducts = 0
	}

	// 返回统计结果
	return &dto.MerchantDashboardStatisticsDTO{
		TodayOrders:      todayOrders,
		TodaySales:       todaySales,
		PendingOrders:    pendingOrders,
		PendingShipment:  pendingShipment,
		PendingRefund:    pendingRefund,
		LowStockProducts: lowStockProducts,
	}, nil
}
