/**
 * 商户外卖统计服务
 *
 * 本文件实现了商户外卖统计相关的业务逻辑，包括商户的食品统计和订单统计。
 * 用于商户管理模块获取商户详情时展示其外卖业务数据。
 */

package services

import (
	"context"
	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/admin/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
)

// MerchantTakeoutStatisticsService 商户外卖统计服务接口
type MerchantTakeoutStatisticsService interface {
	// GetMerchantTakeoutStatistics 获取商户外卖统计数据
	GetMerchantTakeoutStatistics(ctx context.Context, merchantID int64) (*dto.MerchantTakeoutStatisticsDTO, error)
}

// merchantTakeoutStatisticsService 商户外卖统计服务实现
type merchantTakeoutStatisticsService struct {
	foodRepo  repositories.TakeoutFoodRepository
	orderRepo repositories.TakeoutOrderRepository
}

// NewMerchantTakeoutStatisticsService 创建商户外卖统计服务实例
func NewMerchantTakeoutStatisticsService() MerchantTakeoutStatisticsService {
	return &merchantTakeoutStatisticsService{
		foodRepo:  repositories.NewTakeoutFoodRepository(),
		orderRepo: repositories.NewTakeoutOrderRepository(),
	}
}

// GetMerchantTakeoutStatistics 获取商户外卖统计数据
func (s *merchantTakeoutStatisticsService) GetMerchantTakeoutStatistics(ctx context.Context, merchantID int64) (*dto.MerchantTakeoutStatisticsDTO, error) {
	result := &dto.MerchantTakeoutStatisticsDTO{}

	// 获取食品统计信息
	// 全部食品数量
	totalFoods, err := s.foodRepo.CountFoodsByMerchant(merchantID, -1) // -1表示所有状态
	if err != nil {
		logs.Error("统计商户全部食品数量失败: %v, 商家ID: %d", err, merchantID)
		totalFoods = 0
	}

	// 上架食品数量（状态为上架销售中）
	onSaleFoods, err := s.foodRepo.CountFoodsByMerchant(merchantID, models.FoodStatusOnSale) // 4表示上架销售中状态
	if err != nil {
		logs.Error("统计商户上架食品数量失败: %v, 商家ID: %d", err, merchantID)
		onSaleFoods = 0
	}

	// 待审核食品数量（审核状态为待审核）
	pendingFoods, err := s.foodRepo.CountFoodsByMerchantAndAuditStatus(merchantID, models.AuditStatusPending) // 0表示待审核状态
	if err != nil {
		logs.Error("统计商户待审核食品数量失败: %v, 商家ID: %d", err, merchantID)
		pendingFoods = 0
	}

	// 食品统计信息赋值
	result.FoodStatistics = dto.FoodStatisticsDTO{
		TotalFoods:   totalFoods,
		OnSaleFoods:  onSaleFoods,
		PendingFoods: pendingFoods,
	}

	// 获取订单统计信息
	// 全部订单数量
	totalOrders, err := s.orderRepo.CountOrdersByMerchant(merchantID, -1) // -1表示所有状态
	if err != nil {
		logs.Error("统计商户全部订单数量失败: %v, 商家ID: %d", err, merchantID)
		totalOrders = 0
	}

	// 已完成订单数量
	completedOrders, err := s.orderRepo.CountOrdersByMerchant(merchantID, OrderStatusCompleted)
	if err != nil {
		logs.Error("统计商户已完成订单数量失败: %v, 商家ID: %d", err, merchantID)
		completedOrders = 0
	}

	// 进行中订单数量（待处理+已接单+配送中）
	processingOrders, err := s.orderRepo.CountProcessingOrdersByMerchant(merchantID)
	if err != nil {
		logs.Error("统计商户进行中订单数量失败: %v, 商家ID: %d", err, merchantID)
		processingOrders = 0
	}

	// 已取消订单数量
	cancelledOrders, err := s.orderRepo.CountOrdersByMerchant(merchantID, OrderStatusCancelled)
	if err != nil {
		logs.Error("统计商户已取消订单数量失败: %v, 商家ID: %d", err, merchantID)
		cancelledOrders = 0
	}

	// 订单统计信息赋值
	result.OrderStatistics = dto.OrderStatisticsDTO{
		TotalOrders:     totalOrders,
		CompletedOrders: completedOrders,
		ProcessingOrders: processingOrders,
		CancelledOrders: cancelledOrders,
	}

	return result, nil
}
