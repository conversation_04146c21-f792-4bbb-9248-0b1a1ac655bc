/*
 * 商户外卖统计服务
 *
 * 本文件实现了商户外卖统计相关的业务逻辑，包括今日销售额、今日订单数、
 * 待处理订单数、库存不足商品数以及销售和订单趋势分析。
 * 为商户提供实时的业务数据分析和决策支持。
 */

package services

import (
	"context"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/repositories"
	"time"

	"github.com/beego/beego/v2/core/logs"
)

// MerchantTakeoutStatsService 商户外卖统计服务接口
type MerchantTakeoutStatsService interface {
	// GetMerchantTakeoutStats 获取商户外卖统计数据
	GetMerchantTakeoutStats(ctx context.Context, merchantID int64) (*dto.MerchantTakeoutStatsDTO, error)
}

// merchantTakeoutStatsService 商户外卖统计服务实现
type merchantTakeoutStatsService struct {
	orderRepo repositories.TakeoutOrderRepository
	foodRepo  repositories.TakeoutFoodRepository
}

// NewMerchantTakeoutStatsService 创建商户外卖统计服务实例
func NewMerchantTakeoutStatsService() MerchantTakeoutStatsService {
	return &merchantTakeoutStatsService{
		orderRepo: repositories.NewTakeoutOrderRepository(),
		foodRepo:  repositories.NewTakeoutFoodRepository(),
	}
}

// GetMerchantTakeoutStats 获取商户外卖统计数据
func (s *merchantTakeoutStatsService) GetMerchantTakeoutStats(ctx context.Context, merchantID int64) (*dto.MerchantTakeoutStatsDTO, error) {
	result := &dto.MerchantTakeoutStatsDTO{}

	// 获取今日时间范围
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := todayStart.Add(24 * time.Hour)

	// 获取昨日时间范围
	yesterdayStart := todayStart.Add(-24 * time.Hour)
	yesterdayEnd := todayStart

	// 1. 获取今日销售额（只统计已完成的订单）
	todaySales, err := s.orderRepo.GetTotalSalesByMerchantAndDateRange(merchantID, todayStart, todayEnd)
	if err != nil {
		logs.Error("获取今日销售额失败: %v, 商家ID: %d", err, merchantID)
		todaySales = 0
	}
	result.TodaySales = todaySales

	// 2. 获取今日订单数（所有状态的订单）
	todayOrders, err := s.orderRepo.CountOrdersByMerchantAndDateRangeWithoutStatus(merchantID, todayStart, todayEnd)
	if err != nil {
		logs.Error("获取今日订单数失败: %v, 商家ID: %d", err, merchantID)
		todayOrders = 0
	}
	result.TodayOrders = todayOrders

	// 3. 获取待处理订单数（已支付但未完成的订单）
	pendingOrders, err := s.orderRepo.CountPendingOrdersByMerchant(merchantID)
	if err != nil {
		logs.Error("获取待处理订单数失败: %v, 商家ID: %d", err, merchantID)
		pendingOrders = 0
	}
	result.PendingOrders = pendingOrders

	// 4. 获取库存不足商品数
	lowStockProducts, err := s.foodRepo.CountLowStockFoodsByMerchantDefault(merchantID)
	if err != nil {
		logs.Error("获取库存不足商品数失败: %v, 商家ID: %d", err, merchantID)
		lowStockProducts = 0
	}
	result.LowStockProducts = lowStockProducts

	// 5. 获取昨日销售额用于计算趋势
	yesterdaySales, err := s.orderRepo.GetTotalSalesByMerchantAndDateRange(merchantID, yesterdayStart, yesterdayEnd)
	if err != nil {
		logs.Error("获取昨日销售额失败: %v, 商家ID: %d", err, merchantID)
		yesterdaySales = 0
	}

	// 6. 计算销售趋势（与昨日对比）
	if yesterdaySales > 0 {
		result.SalesTrend = ((todaySales - yesterdaySales) / yesterdaySales) * 100
	} else if todaySales > 0 {
		result.SalesTrend = 100 // 昨日无销售，今日有销售，增长100%
	} else {
		result.SalesTrend = 0 // 两日都无销售
	}

	// 7. 获取昨日订单数用于计算趋势
	yesterdayOrders, err := s.orderRepo.CountOrdersByMerchantAndDateRangeWithoutStatus(merchantID, yesterdayStart, yesterdayEnd)
	if err != nil {
		logs.Error("获取昨日订单数失败: %v, 商家ID: %d", err, merchantID)
		yesterdayOrders = 0
	}

	// 8. 计算订单趋势（与昨日对比）
	if yesterdayOrders > 0 {
		result.OrdersTrend = ((float64(todayOrders) - float64(yesterdayOrders)) / float64(yesterdayOrders)) * 100
	} else if todayOrders > 0 {
		result.OrdersTrend = 100 // 昨日无订单，今日有订单，增长100%
	} else {
		result.OrdersTrend = 0 // 两日都无订单
	}

	logs.Info("获取商户外卖统计数据成功, 商家ID: %d, 今日销售额: %.2f, 今日订单数: %d, 待处理订单数: %d, 库存不足商品数: %d, 销售趋势: %.2f%%, 订单趋势: %.2f%%",
		merchantID, result.TodaySales, result.TodayOrders, result.PendingOrders, result.LowStockProducts, result.SalesTrend, result.OrdersTrend)

	return result, nil
}
