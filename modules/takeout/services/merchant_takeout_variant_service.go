/**
 * 商家外卖规格变体服务
 *
 * 本文件实现了商家端外卖规格变体的业务逻辑，处理商家管理自己店铺食品规格的功能。
 * 提供了规格查询、创建、修改和删除等方法，并进行必要的权限验证。
 */

package services

import (
	"errors"
	"fmt"

	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/dto"
)

// GetVariantsByFoodID 根据食品ID获取规格列表
func (s *takeoutVariantService) GetVariantsByFoodID(foodID int64) ([]dto.TakeoutFoodVariantDTO, error) {
	// 直接复用ListVariantsByFoodID方法
	return s.ListVariantsByFoodID(foodID)
}

// GetVariantByIDForMerchant 商家获取规格详情
func (s *takeoutVariantService) GetVariantByIDForMerchant(id int64, merchantID int64) (*dto.TakeoutFoodVariantDTO, error) {
	// 获取规格信息
	variant, err := s.variantRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	// 获取关联的食品，用于验证权限
	food, err := s.foodRepo.GetByID(variant.FoodID)
	if err != nil {
		return nil, err
	}
	
	// 验证商家权限
	if food.MerchantID != merchantID {
		return nil, errors.New("无权访问该规格")
	}
	
	// 转换为DTO
	return &dto.TakeoutFoodVariantDTO{
		ID:            variant.ID,
		FoodID:        variant.FoodID,
		Name:          variant.Name,
		Description:   variant.Description,
		Image:         variant.Image,
		Price:         variant.Price,
		OriginalPrice: variant.OriginalPrice,
		Stock:         variant.Stock,
		SoldCount:     variant.SoldCount,
		IsDefault:     variant.IsDefault,
		SortOrder:     variant.SortOrder,
	}, nil
}

// UpdateVariantForMerchant 商家更新规格
func (s *takeoutVariantService) UpdateVariantForMerchant(req *dto.UpdateVariantRequest, merchantID int64) error {
	// 获取规格信息
	variant, err := s.variantRepo.GetByID(req.ID)
	if err != nil {
		return err
	}
	
	// 获取关联的食品，用于验证权限
	food, err := s.foodRepo.GetByID(variant.FoodID)
	if err != nil {
		return err
	}
	
	// 验证商家权限
	if food.MerchantID != merchantID {
		return errors.New("无权更新该规格")
	}
	
	// 更新字段
	if req.Name != "" {
		variant.Name = req.Name
	}
	if req.Description != "" {
		variant.Description = req.Description
	}
	if req.Image != "" {
		variant.Image = req.Image
	}
	if req.Price > 0 {
		variant.Price = req.Price
	}
	if req.OriginalPrice > 0 {
		variant.OriginalPrice = req.OriginalPrice
	}
	if req.Stock != 0 {
		variant.Stock = req.Stock
	}
	variant.IsDefault = req.IsDefault
	if req.SortOrder > 0 {
		variant.SortOrder = req.SortOrder
	}
	
	// 保存更新
	err = s.variantRepo.Update(variant)
	if err != nil {
		logs.Error("商家更新规格失败: %v, 规格ID: %d, 商家ID: %d", err, req.ID, merchantID)
		return fmt.Errorf("更新规格失败: %v", err)
	}
	
	// 如果设置为默认规格，需要取消其他规格的默认标记
	if variant.IsDefault {
		err = s.variantRepo.ResetDefaultExcept(variant.FoodID, variant.ID)
		if err != nil {
			logs.Warn("重置其他规格默认标记失败: %v, 食品ID: %d, 规格ID: %d", err, variant.FoodID, variant.ID)
			// 不影响更新结果
		}
	}
	
	return nil
}

// DeleteVariantForMerchant 商家删除规格
func (s *takeoutVariantService) DeleteVariantForMerchant(id int64, merchantID int64) error {
	// 获取规格信息
	variant, err := s.variantRepo.GetByID(id)
	if err != nil {
		return err
	}
	
	// 获取关联的食品，用于验证权限
	food, err := s.foodRepo.GetByID(variant.FoodID)
	if err != nil {
		return err
	}
	
	// 验证商家权限
	if food.MerchantID != merchantID {
		return errors.New("无权删除该规格")
	}
	
	// 获取食品所有规格，检查是否是唯一规格
	variants, err := s.variantRepo.ListByFoodID(variant.FoodID)
	if err != nil {
		return err
	}
	
	// 如果只有一个规格且食品HasVariants=true，需要更新食品信息
	if len(variants) == 1 && food.HasVariants {
		food.HasVariants = false
		err = s.foodRepo.Update(food)
		if err != nil {
			logs.Warn("更新食品HasVariants字段失败: %v, 食品ID: %d", err, food.ID)
			// 不影响删除操作
		}
	}
	
	// 删除规格
	err = s.variantRepo.Delete(id)
	if err != nil {
		logs.Error("商家删除规格失败: %v, 规格ID: %d, 商家ID: %d", err, id, merchantID)
		return fmt.Errorf("删除规格失败: %v", err)
	}
	
	return nil
}

// GetVariantsByFoodIDForAdmin 管理员获取食品规格列表
func (s *takeoutVariantService) GetVariantsByFoodIDForAdmin(foodID int64) ([]dto.TakeoutFoodVariantDTO, error) {
	// 直接复用ListVariantsByFoodID方法
	return s.ListVariantsByFoodID(foodID)
}

// GetVariantByIDForAdmin 管理员获取规格详情
func (s *takeoutVariantService) GetVariantByIDForAdmin(id int64) (*dto.TakeoutFoodVariantDTO, error) {
	// 获取规格信息
	variant, err := s.variantRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	// 转换为DTO
	return &dto.TakeoutFoodVariantDTO{
		ID:            variant.ID,
		FoodID:        variant.FoodID,
		Name:          variant.Name,
		Description:   variant.Description,
		Image:         variant.Image,
		Price:         variant.Price,
		OriginalPrice: variant.OriginalPrice,
		Stock:         variant.Stock,
		SoldCount:     variant.SoldCount,
		IsDefault:     variant.IsDefault,
		SortOrder:     variant.SortOrder,
	}, nil
}

// UpdateVariantForAdmin 管理员更新规格
func (s *takeoutVariantService) UpdateVariantForAdmin(req *dto.UpdateVariantRequest) error {
	// 获取规格信息
	variant, err := s.variantRepo.GetByID(req.ID)
	if err != nil {
		return err
	}
	
	// 更新字段
	if req.Name != "" {
		variant.Name = req.Name
	}
	if req.Description != "" {
		variant.Description = req.Description
	}
	if req.Image != "" {
		variant.Image = req.Image
	}
	if req.Price > 0 {
		variant.Price = req.Price
	}
	if req.OriginalPrice > 0 {
		variant.OriginalPrice = req.OriginalPrice
	}
	if req.Stock != 0 {
		variant.Stock = req.Stock
	}
	variant.IsDefault = req.IsDefault
	if req.SortOrder > 0 {
		variant.SortOrder = req.SortOrder
	}
	
	// 保存更新
	err = s.variantRepo.Update(variant)
	if err != nil {
		logs.Error("管理员更新规格失败: %v, 规格ID: %d", err, req.ID)
		return fmt.Errorf("更新规格失败: %v", err)
	}
	
	// 如果设置为默认规格，需要取消其他规格的默认标记
	if variant.IsDefault {
		err = s.variantRepo.ResetDefaultExcept(variant.FoodID, variant.ID)
		if err != nil {
			logs.Warn("重置其他规格默认标记失败: %v, 食品ID: %d, 规格ID: %d", err, variant.FoodID, variant.ID)
			// 不影响更新结果
		}
	}
	
	return nil
}

// DeleteVariantForAdmin 管理员删除规格
func (s *takeoutVariantService) DeleteVariantForAdmin(id int64) error {
	// 获取规格信息
	variant, err := s.variantRepo.GetByID(id)
	if err != nil {
		return err
	}
	
	// 获取食品信息
	food, err := s.foodRepo.GetByID(variant.FoodID)
	if err != nil {
		return err
	}
	
	// 获取食品所有规格，检查是否是唯一规格
	variants, err := s.variantRepo.ListByFoodID(variant.FoodID)
	if err != nil {
		return err
	}
	
	// 如果只有一个规格且食品HasVariants=true，需要更新食品信息
	if len(variants) == 1 && food.HasVariants {
		food.HasVariants = false
		err = s.foodRepo.Update(food)
		if err != nil {
			logs.Warn("更新食品HasVariants字段失败: %v, 食品ID: %d", err, food.ID)
			// 不影响删除操作
		}
	}
	
	// 删除规格
	err = s.variantRepo.Delete(id)
	if err != nil {
		logs.Error("管理员删除规格失败: %v, 规格ID: %d", err, id)
		return fmt.Errorf("删除规格失败: %v", err)
	}
	
	return nil
}
