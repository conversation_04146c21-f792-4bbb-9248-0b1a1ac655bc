/*
 * 多商家订单服务
 *
 * 本文件实现了支持多商家的外卖订单创建逻辑。
 * 当用户购物车中包含多个商家的商品时，系统会自动按商家拆分为多个订单。
 */

package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	baseDTO "o_mall_backend/modules/order/dto"
	orderModels "o_mall_backend/modules/order/models"
	orderRepositories "o_mall_backend/modules/order/repositories"
	orderServices "o_mall_backend/modules/order/services"
	systemServices "o_mall_backend/modules/system/services"
	"o_mall_backend/modules/takeout/dto"
	takeoutModels "o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
	userDTO "o_mall_backend/modules/user/dto"
	userServices "o_mall_backend/modules/user/services"
)

// MultiMerchantOrderService 多商家订单服务接口
type MultiMerchantOrderService interface {
	// CreateOrdersFromCart 从购物车创建多个订单（按商家拆分）
	CreateOrdersFromCart(userID int64, req *dto.CreateTakeoutOrderRequest) ([]*dto.TakeoutOrderDTO, error)
}

// multiMerchantOrderService 多商家订单服务实现
type multiMerchantOrderService struct {
	baseOrderSvc    orderServices.OrderService
	takeoutSvc      TakeoutOrderService
	cartService     TakeoutCartService
	addrService     userServices.AddressService
	foodRepo        repositories.TakeoutFoodRepository
	variantRepo     repositories.TakeoutVariantRepository
	orderRepo       repositories.TakeoutOrderRepository
	baseOrderRepo   orderRepositories.OrderRepository
	systemConfigSvc systemServices.SystemConfigService
	promotionSvc    ITakeoutPromotionService // 促销活动服务
	couponSvc       ITakeoutCouponService    // 优惠券服务
	merchantSvc     interface {              // 商家服务接口
		GetMerchantByID(ctx context.Context, id int64) (interface{}, error)
	}
}

// NewMultiMerchantOrderService 创建多商家订单服务
func NewMultiMerchantOrderService(
	baseOrderSvc orderServices.OrderService,
	takeoutSvc TakeoutOrderService,
	cartService TakeoutCartService,
	addrService userServices.AddressService,
	foodRepo repositories.TakeoutFoodRepository,
	variantRepo repositories.TakeoutVariantRepository,
	orderRepo repositories.TakeoutOrderRepository,
	baseOrderRepo orderRepositories.OrderRepository,
	systemConfigSvc systemServices.SystemConfigService,
	promotionSvc ITakeoutPromotionService,
	couponSvc ITakeoutCouponService,
	merchantSvc interface {
		GetMerchantByID(ctx context.Context, id int64) (interface{}, error)
	},
) MultiMerchantOrderService {
	return &multiMerchantOrderService{
		baseOrderSvc:    baseOrderSvc,
		takeoutSvc:      takeoutSvc,
		cartService:     cartService,
		addrService:     addrService,
		foodRepo:        foodRepo,
		variantRepo:     variantRepo,
		orderRepo:       orderRepo,
		baseOrderRepo:   baseOrderRepo,
		systemConfigSvc: systemConfigSvc,
		promotionSvc:    promotionSvc,
		couponSvc:       couponSvc,
		merchantSvc:     merchantSvc,
	}
}

// MerchantOrderGroup 商家订单分组
type MerchantOrderGroup struct {
	MerchantID   int64                    `json:"merchant_id"`
	MerchantName string                   `json:"merchant_name"`
	Items        []dto.TakeoutCartItemDTO `json:"items"`
	TotalAmount  float64                  `json:"total_amount"`
	PackagingFee float64                  `json:"packaging_fee"`
	CouponID     int64                    `json:"coupon_id"`
	PromotionIDs []int64                  `json:"promotion_ids"` // 促销活动ID列表
	DeliveryTime string                   `json:"delivery_time"`
	Remark       string                   `json:"remark"`
}

// CreateOrdersFromCart 从购物车创建多个订单（按商家拆分），支持优惠券和促销活动
func (s *multiMerchantOrderService) CreateOrdersFromCart(userID int64, req *dto.CreateTakeoutOrderRequest) ([]*dto.TakeoutOrderDTO, error) {
	logs.Info("开始创建多商家订单，用户ID: %d", userID)

	// 检查商家订单是否存在
	if len(req.MerchantOrders) == 0 {
		return nil, errors.New("商家订单为空")
	}

	// 收集所有购物车项ID
	allCartItemIDs := make([]int64, 0)
	for _, merchantOrder := range req.MerchantOrders {
		allCartItemIDs = append(allCartItemIDs, merchantOrder.CartItemIDs...)
	}

	// 获取购物车汇总信息
	cartSummary, err := s.cartService.GetCartSummary(userID, allCartItemIDs)
	if err != nil {
		return nil, err
	}

	if len(cartSummary.Items) == 0 {
		return nil, errors.New("购物车不包含有效商品")
	}

	// 按商家分组购物车项（使用新的分组方法）
	merchantGroups, err := s.groupItemsByMerchantFromRequest(cartSummary.Items, req.MerchantOrders)
	if err != nil {
		return nil, err
	}

	logs.Info("购物车商品按商家分组完成，共%d个商家", len(merchantGroups))

	// 获取配送地址信息
	ctx := context.Background()
	addressInfo, err := s.addrService.GetAddressByID(ctx, userID, req.TakeoutAddressID)
	if err != nil {
		logs.Error("获取地址信息失败: %v, 地址ID: %d, 用户ID: %d", err, req.TakeoutAddressID, userID)
		return nil, errors.New("获取配送地址信息失败")
	}

	// 开始事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, err
	}

	// 定义延迟函数，确保在发生错误时回滚事务
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logs.Error("多商家订单创建过程中发生异常: %v", r)
		}
	}()

	var createdOrders []*dto.TakeoutOrderDTO

	// 为每个商家创建订单
	for _, group := range merchantGroups {
		logs.Info("为商家%d创建订单，商品数量: %d", group.MerchantID, len(group.Items))

		// 创建单个商家的订单
		order, err := s.createSingleMerchantOrder(userID, group, req, addressInfo)
		if err != nil {
			tx.Rollback()
			logs.Error("为商家%d创建订单失败: %v", group.MerchantID, err)
			return nil, fmt.Errorf("为商家%s创建订单失败: %v", group.MerchantName, err)
		}

		createdOrders = append(createdOrders, order)
		logs.Info("商家%d订单创建成功，订单ID: %d", group.MerchantID, order.OrderID)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		logs.Error("提交事务失败: %v", err)
		return nil, errors.New("订单创建失败")
	}

	// 清空购物车
	err = s.cartService.BatchRemoveFromCart(userID, allCartItemIDs)
	if err != nil {
		logs.Warn("清空购物车失败: %v", err)
		// 不影响订单创建结果
	}

	logs.Info("多商家订单创建完成，用户ID: %d, 订单数量: %d", userID, len(createdOrders))
	return createdOrders, nil
}

// groupItemsByMerchantFromRequest 根据请求按商家分组购物车项
func (s *multiMerchantOrderService) groupItemsByMerchantFromRequest(items []dto.TakeoutCartItemDTO, merchantOrders []*dto.MerchantOrderRequest) ([]*MerchantOrderGroup, error) {
	// 创建购物车项ID到购物车项的映射
	cartItemMap := make(map[int64]dto.TakeoutCartItemDTO)
	for _, item := range items {
		cartItemMap[item.CartItemID] = item
	}

	var groups []*MerchantOrderGroup

	// 为每个商家订单创建分组
	for _, merchantOrder := range merchantOrders {
		// 处理促销活动ID：支持单个promotionID和多个promotionIDs
		var promotionIDs []int64
		if len(merchantOrder.PromotionIDs) > 0 {
			// 如果有PromotionIDs数组，使用数组
			promotionIDs = merchantOrder.PromotionIDs
			logs.Info("使用PromotionIDs数组 - 商家ID: %d, 促销活动: %v", merchantOrder.MerchantID, promotionIDs)
		} else if merchantOrder.PromotionID > 0 {
			// 如果只有单个PromotionID，转换为数组
			promotionIDs = []int64{merchantOrder.PromotionID}
			logs.Info("转换单个PromotionID为数组 - 商家ID: %d, 促销活动ID: %d, 转换后: %v", merchantOrder.MerchantID, merchantOrder.PromotionID, promotionIDs)
		} else {
			logs.Info("没有促销活动 - 商家ID: %d", merchantOrder.MerchantID)
		}

		group := &MerchantOrderGroup{
			MerchantID:   merchantOrder.MerchantID,
			MerchantName: "默认商家", // 实际应从商家服务获取
			Items:        make([]dto.TakeoutCartItemDTO, 0),
			TotalAmount:  0,
			PackagingFee: 0,
			CouponID:     merchantOrder.CouponID,
			PromotionIDs: promotionIDs, // 使用处理后的促销活动ID列表
			DeliveryTime: merchantOrder.DeliveryTime,
			Remark:       merchantOrder.Remark,
		}

		// 添加该商家的购物车项
		for _, cartItemID := range merchantOrder.CartItemIDs {
			if item, exists := cartItemMap[cartItemID]; exists {
				// 验证商品是否属于该商家
				food, err := s.foodRepo.GetFoodByID(item.FoodID)
				if err != nil {
					logs.Error("获取商品信息失败: %v, 商品ID: %d", err, item.FoodID)
					return nil, fmt.Errorf("获取商品信息失败: %v", err)
				}

				if food.MerchantID != merchantOrder.MerchantID {
					return nil, fmt.Errorf("购物车项%d不属于商家%d", cartItemID, merchantOrder.MerchantID)
				}

				group.Items = append(group.Items, item)
				group.TotalAmount += item.Price * float64(item.Quantity)

				// 计算包装费（统一使用商品的包装费，不使用规格包装费）
				logs.Info("计算包装费 - 购物车项ID: %d, 商品ID: %d, 规格ID: %d, 数量: %d", item.CartItemID, item.FoodID, item.VariantID, item.Quantity)
				packagingFeeForItem := food.PackagingFee * float64(item.Quantity)
				group.PackagingFee += packagingFeeForItem
				logs.Info("商品包装费 - 商品ID: %d, 单价包装费: %.2f, 数量: %d, 小计包装费: %.2f, 累计包装费: %.2f",
					item.FoodID, food.PackagingFee, item.Quantity, packagingFeeForItem, group.PackagingFee)
			}
		}

		groups = append(groups, group)
	}

	return groups, nil
}

// groupItemsByMerchant 按商家分组购物车项（保留原方法用于兼容）
func (s *multiMerchantOrderService) groupItemsByMerchant(items []dto.TakeoutCartItemDTO) ([]*MerchantOrderGroup, error) {
	merchantMap := make(map[int64]*MerchantOrderGroup)

	for _, item := range items {
		// 获取商品信息以确定商家ID
		food, err := s.foodRepo.GetFoodByID(item.FoodID)
		if err != nil {
			logs.Error("获取商品信息失败: %v, 商品ID: %d", err, item.FoodID)
			return nil, fmt.Errorf("获取商品信息失败: %v", err)
		}

		merchantID := food.MerchantID
		merchantName := "默认商家" // 实际应从商家服务获取

		// 如果商家分组不存在，创建新分组
		if _, exists := merchantMap[merchantID]; !exists {
			merchantMap[merchantID] = &MerchantOrderGroup{
				MerchantID:   merchantID,
				MerchantName: merchantName,
				Items:        make([]dto.TakeoutCartItemDTO, 0),
				TotalAmount:  0,
				PackagingFee: 0,
				CouponID:     0,
				DeliveryTime: "",
				Remark:       "",
			}
		}

		// 添加商品到对应商家分组
		group := merchantMap[merchantID]
		group.Items = append(group.Items, item)
		group.TotalAmount += item.Price * float64(item.Quantity)

		// 计算包装费（统一使用商品的包装费，不使用规格包装费）
		group.PackagingFee += food.PackagingFee * float64(item.Quantity)
	}

	// 转换为切片
	groups := make([]*MerchantOrderGroup, 0, len(merchantMap))
	for _, group := range merchantMap {
		groups = append(groups, group)
	}

	return groups, nil
}

// createSingleMerchantOrder 为单个商家创建订单
func (s *multiMerchantOrderService) createSingleMerchantOrder(
	userID int64,
	group *MerchantOrderGroup,
	req *dto.CreateTakeoutOrderRequest,
	addressInfo *userDTO.AddressResponse,
) (*dto.TakeoutOrderDTO, error) {

	// 构建订单项
	orderItems := make([]*baseDTO.OrderItemRequest, 0, len(group.Items))
	for _, item := range group.Items {
		// 获取商品详情用于快照
		food, err := s.foodRepo.GetFoodByID(item.FoodID)
		if err != nil {
			return nil, fmt.Errorf("获取商品详情失败: %v", err)
		}

		// 获取规格详情
		var variant *takeoutModels.TakeoutFoodVariant
		if item.VariantID > 0 {
			variant, _ = s.variantRepo.GetVariantByID(item.VariantID)
		}

		// 创建快照
		productSnapshot, variantSnapshot, comboSelections := s.createSnapshots(food, variant, &item)

		// 计算包装费（统一使用商品的包装费，单价 × 数量）
		totalPackagingFee := food.PackagingFee * float64(item.Quantity)

		orderItem := &baseDTO.OrderItemRequest{
			ProductID:       item.FoodID,
			SkuID:           item.VariantID,
			Quantity:        item.Quantity,
			Price:           item.Price,
			ItemType:        orderModels.ItemTypeTakeout,
			MerchantID:      group.MerchantID,
			MerchantName:    group.MerchantName,
			PackagingFee:    totalPackagingFee, // 使用总包装费而不是单价
			Remark:          item.Remark,
			ProductSnapshot: productSnapshot,
			VariantSnapshot: variantSnapshot,
			ComboSelections: comboSelections,
		}
		orderItems = append(orderItems, orderItem)
	}

	// 计算配送费（需要传入配送地址信息）
	deliveryFee := s.calculateDeliveryFee(group.MerchantID, group.TotalAmount, addressInfo)
	logs.Info("配送费计算完成 - 商家ID: %d, 订单金额: %.2f, 配送费: %.2f", group.MerchantID, group.TotalAmount, deliveryFee)
	logs.Info("包装费信息 - 商家ID: %d, 包装费: %.2f", group.MerchantID, group.PackagingFee)

	// 构建基础订单请求
	createOrderReq := &baseDTO.CreateOrderRequest{
		UserID:       userID,
		MerchantID:   group.MerchantID, // 设置商家ID
		OrderType:    1,                // 外卖订单类型
		Items:        orderItems,
		Remark:       group.Remark, // 使用商家特定的备注
		PayMethod:    1,            // 默认支付方式
		DeliveryType: 1,            // 默认配送方式
		Source:       1,            // 默认订单来源
		Address: &baseDTO.OrderAddressRequest{
			ReceiverName:  addressInfo.ReceiverName,
			ReceiverPhone: addressInfo.ReceiverMobile,
			Province:      addressInfo.Province,
			City:          addressInfo.City,
			District:      addressInfo.District,
			Detail:        addressInfo.DetailedAddress,
		},
	}

	// 调用基础订单服务创建订单
	ctx := context.Background()
	baseOrder, err := s.baseOrderSvc.CreateOrder(ctx, createOrderReq)
	if err != nil {
		return nil, fmt.Errorf("创建基础订单失败: %v", err)
	}

	// 更新订单金额（包含配送费和包装费）
	totalAmount := group.TotalAmount + deliveryFee + group.PackagingFee

	// 初始化折扣金额
	couponDiscountAmount := 0.0
	promotionDiscountAmount := 0.0
	totalDiscountAmount := 0.0

	// 处理优惠券
	if group.CouponID > 0 {
		logs.Info("应用优惠券 - 订单ID: %d, 优惠券ID: %d, 用户ID: %d, 订单金额: %.2f", baseOrder.ID, group.CouponID, userID, totalAmount)

		// 使用优惠券服务检查优惠券可用性
		couponValidation, err := s.couponSvc.CheckCouponAvailability(userID, group.CouponID, totalAmount)
		if err != nil {
			logs.Error("检查优惠券可用性失败 - 订单ID: %d, 优惠券ID: %d, 错误: %v",
				baseOrder.ID, group.CouponID, err)
		} else {
			logs.Info("优惠券验证结果 - 订单ID: %d, 优惠券ID: %d, 有效性: %t, 折扣金额: %.2f, 消息: %s",
				baseOrder.ID, group.CouponID, couponValidation.Valid, couponValidation.DiscountAmount, couponValidation.Message)

			if couponValidation.Valid {
				couponDiscountAmount = couponValidation.DiscountAmount
				logs.Info("优惠券折扣应用成功 - 订单ID: %d, 优惠券ID: %d, 折扣金额: %.2f",
					baseOrder.ID, group.CouponID, couponDiscountAmount)

				// 使用优惠券
				err = s.couponSvc.UseCoupon(userID, group.CouponID, baseOrder.ID)
				if err != nil {
					logs.Error("使用优惠券失败 - 订单ID: %d, 优惠券ID: %d, 错误: %v",
						baseOrder.ID, group.CouponID, err)
					// 重置优惠券折扣金额
					couponDiscountAmount = 0.0
				} else {
					logs.Info("优惠券使用成功 - 订单ID: %d, 优惠券ID: %d", baseOrder.ID, group.CouponID)
				}
			} else {
				logs.Info("优惠券使用条件不满足 - 订单ID: %d, 优惠券ID: %d, 原因: %s",
					baseOrder.ID, group.CouponID, couponValidation.Message)
			}
		}
	} else {
		logs.Info("订单中没有指定优惠券")
	}

	// 处理促销活动
	if len(group.PromotionIDs) > 0 {
		logs.Info("应用促销活动 - 订单ID: %d, 促销活动: %v", baseOrder.ID, group.PromotionIDs)

		// 收集商品ID用于促销活动匹配
		foodIDs := make([]int64, 0, len(group.Items))
		for _, item := range group.Items {
			foodIDs = append(foodIDs, item.FoodID)
		}
		logs.Info("促销活动匹配 - 商家ID: %d, 订单金额: %.2f, 商品ID列表: %v", group.MerchantID, totalAmount, foodIDs)

		// 获取符合条件的促销活动
		eligiblePromotions, err := s.promotionSvc.GetEligiblePromotions(group.MerchantID, totalAmount, foodIDs, userID)
		if err != nil {
			logs.Error("获取符合条件的促销活动失败 - 订单ID: %d, 错误: %v", baseOrder.ID, err)
		} else {
			logs.Info("获取到符合条件的促销活动数量: %d", len(eligiblePromotions))
			for i, promo := range eligiblePromotions {
				logs.Info("符合条件的促销活动[%d]: ID=%d, 名称=%s, 类型=%d", i, promo.ID, promo.Name, promo.Type)
			}

			// 遍历请求的促销活动ID，检查是否在符合条件的列表中
			for _, requestedPromotionID := range group.PromotionIDs {
				logs.Info("检查请求的促销活动ID: %d", requestedPromotionID)
				found := false
				for _, eligiblePromotion := range eligiblePromotions {
					if eligiblePromotion.ID == requestedPromotionID {
						found = true
						// 计算促销折扣
						discount := s.calculatePromotionDiscount(&eligiblePromotion, totalAmount)
						promotionDiscountAmount += discount
						logs.Info("促销活动应用成功 - 订单ID: %d, 促销ID: %d, 促销名称: %s, 折扣金额: %.2f",
							baseOrder.ID, requestedPromotionID, eligiblePromotion.Name, discount)
						break
					}
				}
				if !found {
					logs.Info("请求的促销活动ID %d 不在符合条件的列表中", requestedPromotionID)
				}
			}
		}
	} else {
		logs.Info("订单中没有指定促销活动")
	}

	// 计算总折扣金额
	totalDiscountAmount = couponDiscountAmount + promotionDiscountAmount
	if totalDiscountAmount > totalAmount {
		// 防止折扣超过总金额
		totalDiscountAmount = totalAmount
		logs.Info("折扣金额超过订单总额，调整为: %.2f", totalDiscountAmount)
	}

	// 计算实付金额
	payAmount := totalAmount - totalDiscountAmount

	logs.Info("订单折扣计算完成 - 订单ID: %d, 商品总额: %.2f, 优惠券折扣: %.2f, 促销折扣: %.2f, 总折扣: %.2f, 实付金额: %.2f",
		baseOrder.ID, totalAmount, couponDiscountAmount, promotionDiscountAmount, totalDiscountAmount, payAmount)

	// 获取订单模型并更新金额信息
	orderModel, err := s.baseOrderRepo.GetOrderByID(ctx, baseOrder.ID)
	if err != nil {
		return nil, fmt.Errorf("获取订单失败: %v", err)
	}
	orderModel.TotalAmount = totalAmount
	orderModel.PayAmount = payAmount
	orderModel.DiscountAmount = totalDiscountAmount
	orderModel.CouponAmount = couponDiscountAmount
	orderModel.PromotionAmount = promotionDiscountAmount

	// 如果有使用优惠券，记录优惠券ID
	if group.CouponID > 0 {
		couponIDs := fmt.Sprintf("%d", group.CouponID)
		orderModel.CouponIDs = couponIDs
	}

	// 如果有促销活动，记录促销信息
	if len(group.PromotionIDs) > 0 {
		promotionInfo, _ := json.Marshal(group.PromotionIDs)
		orderModel.PromotionInfo = string(promotionInfo)
	}

	// 保存更新后的订单金额到数据库
	err = s.baseOrderRepo.UpdateOrder(ctx, orderModel)
	if err != nil {
		return nil, fmt.Errorf("更新订单金额失败: %v", err)
	}

	logs.Info("订单金额更新成功 - 订单ID: %d, 商品总额: %.2f, 配送费: %.2f, 包装费: %.2f, 总折扣: %.2f, 最终总额: %.2f",
		baseOrder.ID, group.TotalAmount, deliveryFee, group.PackagingFee, totalDiscountAmount, payAmount)

	// 创建外卖订单扩展信息
	estimatedDeliveryTime := time.Now().Add(time.Minute * 30)
	currentTime := time.Now()
	logs.Info("创建订单扩展 - 订单ID: %d, 配送费: %.2f, 包装费: %.2f", baseOrder.ID, deliveryFee, group.PackagingFee)

	// 获取送货地址经纬度
	logs.Info("获取地址经纬度 - 订单ID: %d, 经度: %f, 纬度: %f", baseOrder.ID, addressInfo.LocationLongitude, addressInfo.LocationLatitude)

	// 构建促销活动ID字符串
	var promotionIDsStr string
	if len(group.PromotionIDs) > 0 {
		promotionIDStrs := make([]string, len(group.PromotionIDs))
		for i, id := range group.PromotionIDs {
			promotionIDStrs[i] = fmt.Sprintf("%d", id)
		}
		promotionIDsStr = strings.Join(promotionIDStrs, ",")
	}

	takeoutExtension := &takeoutModels.TakeoutOrderExtension{
		OrderID:               baseOrder.ID,
		OrderNo:               baseOrder.OrderNo,
		MerchantID:            group.MerchantID,
		OrderType:             takeoutModels.OrderTypeTakeout,
		ExpectedDeliveryTime:  estimatedDeliveryTime,
		DeliveryStatus:        takeoutModels.DeliveryStatusWaiting,
		DeliveryType:          0, // 平台配送
		DeliveryFee:           deliveryFee,
		PackagingFee:          group.PackagingFee,
		PromotionIDs:          promotionIDsStr,         // 记录使用的促销活动ID
		PromotionDiscount:     promotionDiscountAmount, // 记录促销优惠金额
		EatingStyle:           0,                       // 外卖配送
		DistanceKm:            0,                       // 需要计算
		EstimatedDeliveryTime: estimatedDeliveryTime,
		IsPreOrder:            false,
		PreOrderTime:          currentTime,           // 设置预订时间为当前时间
		DeliveryStartTime:     currentTime,           // 设置配送开始时间为当前时间
		DeliveryEndTime:       estimatedDeliveryTime, // 设置配送结束时间为预计送达时间
		Remark:                group.Remark,          // 使用商家特定的备注
		// 保存经纬度信息
		DeliveryLng: addressInfo.LocationLongitude, // 送货地址经度
		DeliveryLat: addressInfo.LocationLatitude,  // 送货地址纬度
	}

	// 保存外卖订单扩展信息
	_, err = s.orderRepo.CreateOrderExtension(takeoutExtension)
	if err != nil {
		return nil, fmt.Errorf("创建外卖订单扩展失败: %v", err)
	}

	// 记录用户使用促销活动
	if len(group.PromotionIDs) > 0 && promotionDiscountAmount > 0 {
		logs.Info("开始记录用户促销使用 - 订单ID: %d, 用户ID: %d, 促销活动: %v, 优惠金额: %.2f",
			baseOrder.ID, userID, group.PromotionIDs, promotionDiscountAmount)

		// 为每个使用的促销活动创建使用记录
		for _, promotionID := range group.PromotionIDs {
			// 计算该促销活动的优惠金额（简化处理，平均分配）
			discountPerPromotion := promotionDiscountAmount / float64(len(group.PromotionIDs))

			err = s.promotionSvc.RecordUserPromotionUsage(userID, promotionID, baseOrder.ID, group.MerchantID, discountPerPromotion)
			if err != nil {
				logs.Error("记录用户促销使用失败 - 促销ID: %d, 用户ID: %d, 错误: %v", promotionID, userID, err)
				// 不返回错误，避免影响订单创建流程
			} else {
				logs.Info("用户促销使用记录成功 - 促销ID: %d, 用户ID: %d, 优惠金额: %.2f", promotionID, userID, discountPerPromotion)
			}
		}
	}

	// 构建返回的订单DTO
	logs.Info("构建订单DTO - 订单ID: %d, 总金额: %.2f, 实付金额: %.2f, 配送费: %.2f, 包装费: %.2f",
		baseOrder.ID, baseOrder.TotalAmount, baseOrder.PayAmount, deliveryFee, group.PackagingFee)
	orderDTO := &dto.TakeoutOrderDTO{
		OrderID:        baseOrder.ID,
		OrderNo:        baseOrder.OrderNo,
		MerchantID:     group.MerchantID,
		UserID:         userID,
		OrderStatus:    baseOrder.Status,
		PayStatus:      baseOrder.PayStatus,
		TotalAmount:    baseOrder.TotalAmount,
		PayAmount:      baseOrder.PayAmount,
		DeliveryFee:    deliveryFee,
		PackagingFee:   group.PackagingFee, // 添加包装费字段
		DeliveryStatus: takeoutExtension.DeliveryStatus,
		Remark:         group.Remark, // 使用商家特定的备注
		CreateTime:     baseOrder.CreatedAt,
	}

	return orderDTO, nil
}

// calculateDeliveryFee 计算配送费（支持距离计算）
func (s *multiMerchantOrderService) calculateDeliveryFee(merchantID int64, totalAmount float64, addressInfo *userDTO.AddressResponse) float64 {
	// 从数据库获取配送费配置
	ctx := context.Background()
	logs.Info("开始获取配送费配置，category: deliveryFee")
	configs, err := s.systemConfigSvc.GetAllConfigsWithDetails(ctx, "", 1, -1, "deliveryFee")
	if err != nil {
		logs.Error("获取配送费配置失败: %v，使用默认配置", err)
		// 使用默认配置
		return 10.0
	}

	logs.Info("获取到配置数量: %d", len(configs))
	for i, config := range configs {
		logs.Info("配置[%d]: ConfigKey=%s, ConfigValue=%s, Category=%s", i, config.ConfigKey, config.ConfigValue, config.Category)
	}

	// 解析配送费配置
	var deliveryConfig map[string]interface{}
	if len(configs) > 0 {
		logs.Info("开始解析配送费配置: %s", configs[0].ConfigValue)
		err = json.Unmarshal([]byte(configs[0].ConfigValue), &deliveryConfig)
		if err != nil {
			logs.Error("解析配送费配置失败: %v，使用默认配置", err)
			return 10.0
		}
		logs.Info("解析后的配送费配置: %+v", deliveryConfig)
	} else {
		logs.Warn("未找到配送费配置，使用默认配置")
		return 10.0
	}

	// 提取配置值
	baseDeliveryFee := 10.0
	if val, ok := deliveryConfig["deliveryBaseFee"]; ok {
		if fee, ok := val.(float64); ok {
			baseDeliveryFee = fee
		}
	}

	freeDeliveryEnabled := false
	if val, ok := deliveryConfig["deliveryFreeEnabled"]; ok {
		if enabled, ok := val.(bool); ok {
			freeDeliveryEnabled = enabled
		}
	}

	freeDeliveryAmount := 30.0
	if val, ok := deliveryConfig["deliveryFreeAmount"]; ok {
		if amount, ok := val.(float64); ok {
			freeDeliveryAmount = amount
		}
	}

	discountEnabled := false
	if val, ok := deliveryConfig["deliveryDiscountEnabled"]; ok {
		if enabled, ok := val.(bool); ok {
			discountEnabled = enabled
		}
	}

	discountAmount := 20.0
	if val, ok := deliveryConfig["deliveryDiscountAmount"]; ok {
		if amount, ok := val.(float64); ok {
			discountAmount = amount
		}
	}

	discountRate := 0.8
	if val, ok := deliveryConfig["deliveryDiscountRate"]; ok {
		if rate, ok := val.(float64); ok {
			discountRate = rate
		}
	}

	// 提取距离费用配置
	deliveryKmFee := 0.0
	if val, ok := deliveryConfig["deliveryKmFee"]; ok {
		if fee, ok := val.(float64); ok {
			deliveryKmFee = fee
		}
	}

	logs.Info("开始计算配送费 - 商家ID: %d, 订单金额: %.2f", merchantID, totalAmount)
	logs.Info("配送费配置 - 基础费用: %.2f, 免费门槛: %.2f, 折扣门槛: %.2f, 折扣率: %.2f, 距离费用: %.2f/km",
		baseDeliveryFee, freeDeliveryAmount, discountAmount, discountRate, deliveryKmFee)

	// 计算配送距离（如果有地址信息）
	var deliveryDistance float64 = 0.0
	if addressInfo != nil && addressInfo.LocationLatitude != 0 && addressInfo.LocationLongitude != 0 {
		// 获取商家位置（这里需要实现获取商家位置的逻辑）
		merchantLat, merchantLng := s.getMerchantLocation(merchantID)
		if merchantLat != 0 && merchantLng != 0 {
			deliveryDistance = s.calculateDistance(addressInfo.LocationLatitude, addressInfo.LocationLongitude, merchantLat, merchantLng)
			logs.Info("配送距离计算 - 用户坐标: (%.6f, %.6f), 商家坐标: (%.6f, %.6f), 距离: %.2fkm",
				addressInfo.LocationLatitude, addressInfo.LocationLongitude, merchantLat, merchantLng, deliveryDistance)
		}
	}

	// 计算基础配送费（考虑距离）
	finalDeliveryFee := baseDeliveryFee
	if deliveryKmFee > 0 && deliveryDistance > 0 {
		// 使用与前端一致的距离计算逻辑
		freeDistance := 3.0 // 免费配送距离3km
		if deliveryDistance > freeDistance {
			extraDistance := deliveryDistance - freeDistance
			// 向上取整，与前端保持一致
			extraDistanceCeil := math.Ceil(extraDistance)
			extraFee := extraDistanceCeil * deliveryKmFee
			finalDeliveryFee = baseDeliveryFee + extraFee
			logs.Info("距离费用计算 - 超出距离: %.2fkm, 向上取整: %.0fkm, 额外费用: %.2f, 总配送费: %.2f",
				extraDistance, extraDistanceCeil, extraFee, finalDeliveryFee)
		} else {
			logs.Info("距离%.2fkm在免费范围内(%.0fkm)，使用基础配送费: %.2f", deliveryDistance, freeDistance, finalDeliveryFee)
		}
	}

	// 满额免配送费逻辑
	if freeDeliveryEnabled && totalAmount >= freeDeliveryAmount {
		logs.Info("满%.2f元免配送费，配送费: 0.00", freeDeliveryAmount)
		return 0.0
	}

	// 根据订单金额调整配送费
	if discountEnabled && totalAmount >= discountAmount {
		discountedFee := finalDeliveryFee * discountRate
		logs.Info("满%.2f元配送费%.0f折，原价: %.2f，折后: %.2f", discountAmount, discountRate*100, finalDeliveryFee, discountedFee)
		return discountedFee
	}

	logs.Info("最终配送费: %.2f (基础费: %.2f, 距离: %.2fkm)", finalDeliveryFee, baseDeliveryFee, deliveryDistance)
	return finalDeliveryFee
}

// createSnapshots 创建商品、规格和套餐快照
func (s *multiMerchantOrderService) createSnapshots(
	food *takeoutModels.TakeoutFood,
	variant *takeoutModels.TakeoutFoodVariant,
	item *dto.TakeoutCartItemDTO,
) (string, string, string) {
	// 创建商品快照
	productSnapshot := takeoutModels.TakeoutSnapshot{
		ID:           food.ID,
		Name:         food.Name,
		Description:  food.Description,
		Image:        food.Image,
		Price:        food.Price,
		CategoryID:   food.CategoryID,
		CategoryName: food.CategoryName,
		Status:       food.Status,
		SalesCount:   food.SalesCount,
		MerchantID:   food.MerchantID,
		IsCombo:      food.IsCombo,
		PackagingFee: food.PackagingFee,
		Rating:       food.Rating,
		Tags:         food.Tags,
	}

	// 创建规格快照
	var variantSnapshot takeoutModels.TakeoutVariantSnapshot
	if variant != nil {
		variantSnapshot = takeoutModels.TakeoutVariantSnapshot{
			ID:            variant.ID,
			FoodID:        variant.FoodID,
			Name:          variant.Name,
			Price:         variant.Price,
			OriginalPrice: variant.OriginalPrice,
			Stock:         variant.Stock,
			Status:        variant.Status,
			Attributes:    variant.Attributes,
			PackagingFee:  variant.PackagingFee,
		}
	} else {
		variantSnapshot = takeoutModels.TakeoutVariantSnapshot{
			FoodID:        food.ID,
			Name:          "标准规格",
			Price:         food.Price,
			OriginalPrice: food.Price,
			PackagingFee:  food.PackagingFee,
		}
	}

	// 处理套餐选择
	comboSelections := make([]takeoutModels.TakeoutComboSelectionSnapshot, 0)
	for _, combo := range item.ComboSelections {
		selectedOptions := make([]takeoutModels.TakeoutComboOptionSnapshot, 0)
		for _, option := range combo.SelectedOptions {
			selectedOptions = append(selectedOptions, takeoutModels.TakeoutComboOptionSnapshot{
				OptionID:   option.OptionID,
				OptionName: option.OptionName,
				ExtraPrice: option.ExtraPrice,
				Quantity:   option.Quantity,
			})
		}

		comboSelections = append(comboSelections, takeoutModels.TakeoutComboSelectionSnapshot{
			ComboID:         combo.ComboID,
			ComboName:       combo.ComboName,
			SelectedOptions: selectedOptions,
		})
	}

	// 转换为JSON字符串
	productSnapshotJSON, _ := json.Marshal(productSnapshot)
	variantSnapshotJSON, _ := json.Marshal(variantSnapshot)
	comboSelectionsJSON, _ := json.Marshal(comboSelections)

	return string(productSnapshotJSON), string(variantSnapshotJSON), string(comboSelectionsJSON)
}

// calculatePromotionDiscount 计算促销活动折扣金额
func (s *multiMerchantOrderService) calculatePromotionDiscount(promotion *dto.PromotionResponse, totalAmount float64) float64 {
	// 解析促销活动规则
	var rules map[string]interface{}
	err := json.Unmarshal([]byte(promotion.Rules), &rules)
	if err != nil {
		logs.Error("解析促销活动规则失败: %v, 促销ID: %d", err, promotion.ID)
		return 0.0
	}

	switch promotion.Type {
	case takeoutModels.PromotionTypeFirstOrder: // 首单优惠
		if coupon, ok := rules["coupon"].(map[string]interface{}); ok {
			// 检查最低订单金额
			if minAmount, exists := coupon["min_order_amount"]; exists {
				minOrderAmount, ok := minAmount.(float64)
				if ok && totalAmount >= minOrderAmount {
					// 获取优惠金额
					if amount, exists := coupon["amount"]; exists {
						if discountAmount, ok := amount.(float64); ok {
							return discountAmount
						}
					}
				}
			}
		}

	case takeoutModels.PromotionTypeFull: // 满减活动
		if coupon, ok := rules["coupon"].(map[string]interface{}); ok {
			// 检查最低订单金额
			if minAmount, exists := coupon["min_order_amount"]; exists {
				minOrderAmount, ok := minAmount.(float64)
				if ok && totalAmount >= minOrderAmount {
					// 获取优惠金额 - 满减活动使用amount字段
					if discountAmount, exists := coupon["amount"]; exists {
						if amount, ok := discountAmount.(float64); ok {
							logs.Info("满减活动计算成功 - 促销ID: %d, 订单金额: %.2f, 最低金额: %.2f, 折扣金额: %.2f",
								promotion.ID, totalAmount, minOrderAmount, amount)
							return amount
						}
					}
				} else {
					logs.Info("满减活动不满足条件 - 促销ID: %d, 订单金额: %.2f, 最低金额: %.2f",
						promotion.ID, totalAmount, minOrderAmount)
				}
			}
		}

	case takeoutModels.PromotionTypeProductDiscount: // 商品折扣
		if discount, ok := rules["discount"].(map[string]interface{}); ok {
			// 获取折扣率
			if discountRate, exists := discount["discount_rate"]; exists {
				if rate, ok := discountRate.(float64); ok {
					// 计算折扣金额
					discountAmount := totalAmount * (rate / 100.0)

					// 检查最大折扣限制
					if maxDiscount, exists := discount["max_discount"]; exists {
						if max, ok := maxDiscount.(float64); ok && discountAmount > max {
							discountAmount = max
						}
					}

					return discountAmount
				}
			}
		}

	case takeoutModels.PromotionTypeLimitedTime: // 限时特价
		if limitedTime, ok := rules["limited_time"].(map[string]interface{}); ok {
			// 获取特价折扣率
			if discountRate, exists := limitedTime["discount_rate"]; exists {
				if rate, ok := discountRate.(float64); ok {
					return totalAmount * (rate / 100.0)
				}
			}
		}

	default:
		logs.Warn("未知的促销活动类型: %d, 促销ID: %d", promotion.Type, promotion.ID)
	}

	return 0.0
}

// calculateDistance 计算两点之间的距离（使用Haversine公式）
func (s *multiMerchantOrderService) calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	// 地球半径（公里）
	const R = 6371.0

	// 将角度转换为弧度
	lat1Rad := lat1 * math.Pi / 180
	lng1Rad := lng1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lng2Rad := lng2 * math.Pi / 180

	// 计算差值
	dLat := lat2Rad - lat1Rad
	dLng := lng2Rad - lng1Rad

	// Haversine公式
	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(dLng/2)*math.Sin(dLng/2)

	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	// 距离（公里）
	distance := R * c

	// 保留2位小数
	return math.Round(distance*100) / 100
}

// getMerchantLocation 获取商家位置
func (s *multiMerchantOrderService) getMerchantLocation(merchantID int64) (float64, float64) {
	// 从商家服务获取商家信息
	ctx := context.Background()
	merchantInfo, err := s.merchantSvc.GetMerchantByID(ctx, merchantID)
	if err != nil {
		logs.Error("获取商家信息失败: %v, 商家ID: %d", err, merchantID)
		// 返回默认坐标（贵阳市中心）
		return 26.372, 106.63
	}

	logs.Info("获取到商家信息: %+v", merchantInfo)

	// 尝试多种类型断言方式
	switch merchant := merchantInfo.(type) {
	case map[string]interface{}:
		// 处理map类型
		if lat, exists := merchant["latitude"]; exists {
			if lng, exists := merchant["longitude"]; exists {
				if latFloat, ok := lat.(float64); ok && latFloat != 0 {
					if lngFloat, ok := lng.(float64); ok && lngFloat != 0 {
						logs.Info("获取商家位置成功 - 商家ID: %d, 纬度: %.6f, 经度: %.6f", merchantID, latFloat, lngFloat)
						return latFloat, lngFloat
					}
				}
			}
		}
	default:
		// 尝试通过反射获取字段
		logs.Info("商家信息类型: %T", merchantInfo)
		// 这里可以添加更多的类型处理逻辑
	}

	logs.Warn("商家%d没有设置有效的经纬度坐标，使用默认坐标", merchantID)
	// 返回默认坐标（贵阳市中心）
	return 26.372, 106.63
}
