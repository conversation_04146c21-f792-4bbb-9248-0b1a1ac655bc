/**
 * 外卖购物车缓存服务
 *
 * 本文件实现了外卖购物车的Redis缓存功能，提供购物车信息的缓存存储、获取和更新操作。
 * 支持用户购物车列表缓存，提高系统性能，减少数据库查询压力。
 */

package services

import (
	"encoding/json"
	"fmt"
	"time"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/utils/redis"

	"github.com/beego/beego/v2/core/logs"
)

// 缓存键常量定义
const (
	// TakeoutCartCacheKeyPrefix 外卖购物车缓存键前缀
	TakeoutCartCacheKeyPrefix = "takeout:cart:"
	// TakeoutCartListCacheKey 用户购物车列表缓存键
	TakeoutCartListCacheKey = TakeoutCartCacheKeyPrefix + "list:user:%d"
	// TakeoutCartCountCacheKey 用户购物车计数缓存键
	TakeoutCartCountCacheKey = TakeoutCartCacheKeyPrefix + "count:user:%d"

	// 缓存过期时间
	TakeoutCartCacheExpiration      = 5 * time.Minute // 购物车缓存5分钟
	TakeoutCartCountCacheExpiration = 2 * time.Minute // 购物车计数缓存2分钟（更短的过期时间）
)

// TakeoutCartCacheService 外卖购物车缓存服务接口
type TakeoutCartCacheService interface {
	// 购物车列表缓存
	GetCartItems(userID int64) ([]dto.TakeoutCartItemDTO, bool, error)
	SetCartItems(userID int64, cartItems []dto.TakeoutCartItemDTO) error

	// 购物车计数缓存
	GetCartCount(userID int64) (int, bool, error)
	SetCartCount(userID int64, count int) error
	GetCartCountDetails(userID int64) (*dto.CartCountDetailsDTO, bool, error)
	SetCartCountDetails(userID int64, details *dto.CartCountDetailsDTO) error

	// 缓存刷新与清理
	RefreshCartCache(userID int64, listCartItemsFunc func(int64) ([]dto.TakeoutCartItemDTO, error)) error
	// AsyncRefreshCartCache 异步刷新购物车缓存，不阻塞当前请求
	AsyncRefreshCartCache(userID int64, listCartItemsFunc func(int64) ([]dto.TakeoutCartItemDTO, error))
	DeleteCartCache(userID int64) error
}

// takeoutCartCacheService 外卖购物车缓存服务实现
type takeoutCartCacheService struct{}

// NewTakeoutCartCacheService 创建外卖购物车缓存服务实例
func NewTakeoutCartCacheService() TakeoutCartCacheService {
	return &takeoutCartCacheService{}
}

// GetCartItems 从缓存获取购物车列表
func (s *takeoutCartCacheService) GetCartItems(userID int64) ([]dto.TakeoutCartItemDTO, bool, error) {
	if userID <= 0 {
		return nil, false, fmt.Errorf("用户ID无效")
	}

	cacheKey := fmt.Sprintf(TakeoutCartListCacheKey, userID)
	cacheData, err := redis.Get(cacheKey)
	if err != nil {
		// 缓存不存在或获取失败，返回false表示未命中缓存
		return nil, false, nil
	}

	var cartItems []dto.TakeoutCartItemDTO
	err = json.Unmarshal([]byte(cacheData), &cartItems)
	if err != nil {
		logs.Error("解析购物车缓存数据失败: %v, 用户ID: %d", err, userID)
		return nil, false, err
	}

	logs.Debug("从缓存获取购物车列表成功, 用户ID: %d, 条目数: %d", userID, len(cartItems))
	return cartItems, true, nil
}

// SetCartItems 将购物车列表存入缓存
func (s *takeoutCartCacheService) SetCartItems(userID int64, cartItems []dto.TakeoutCartItemDTO) error {
	if userID <= 0 {
		return fmt.Errorf("用户ID无效")
	}

	cacheKey := fmt.Sprintf(TakeoutCartListCacheKey, userID)
	cacheData, err := json.Marshal(cartItems)
	if err != nil {
		logs.Error("序列化购物车数据失败: %v, 用户ID: %d", err, userID)
		return err
	}

	err = redis.Set(cacheKey, string(cacheData), TakeoutCartCacheExpiration)
	if err != nil {
		logs.Error("存储购物车缓存失败: %v, 用户ID: %d", err, userID)
		return err
	}

	logs.Debug("购物车列表缓存存储成功, 用户ID: %d, 条目数: %d", userID, len(cartItems))
	return nil
}

// RefreshCartCache 刷新用户购物车缓存
// listCartItemsFunc 传入的函数用于获取最新的购物车数据
func (s *takeoutCartCacheService) RefreshCartCache(userID int64, listCartItemsFunc func(int64) ([]dto.TakeoutCartItemDTO, error)) error {
	if userID <= 0 {
		return fmt.Errorf("用户ID无效")
	}

	logs.Info("开始从数据库获取最新购物车数据, 用户ID: %d", userID)
	// 获取最新的购物车数据
	cartItems, err := listCartItemsFunc(userID)
	if err != nil {
		logs.Error("获取购物车数据失败: %v, 用户ID: %d", err, userID)
		return err
	}

	logs.Info("从数据库获取到购物车数据, 用户ID: %d, 条目数: %d", userID, len(cartItems))

	// 更新缓存
	err = s.SetCartItems(userID, cartItems)
	if err != nil {
		logs.Error("刷新购物车缓存失败: %v, 用户ID: %d", err, userID)
		return err
	}

	logs.Info("刷新购物车缓存成功, 用户ID: %d, 条目数: %d", userID, len(cartItems))
	return nil
}

// AsyncRefreshCartCache 异步刷新购物车缓存，不阻塞当前请求
func (s *takeoutCartCacheService) AsyncRefreshCartCache(userID int64, listCartItemsFunc func(int64) ([]dto.TakeoutCartItemDTO, error)) {
	if userID <= 0 {
		logs.Error("异步刷新缓存失败: 用户ID无效")
		return
	}

	// 使用goroutine异步执行缓存刷新
	go func(uid int64, listFunc func(int64) ([]dto.TakeoutCartItemDTO, error)) {
		// 获取最新的购物车数据
		cartItems, err := listFunc(uid)
		if err != nil {
			logs.Error("异步获取购物车数据失败: %v, 用户ID: %d", err, uid)
			return
		}

		// 更新缓存
		err = s.SetCartItems(uid, cartItems)
		if err != nil {
			logs.Error("异步刷新购物车缓存失败: %v, 用户ID: %d", err, uid)
			return
		}

		logs.Debug("异步刷新购物车缓存成功, 用户ID: %d, 条目数: %d", uid, len(cartItems))
	}(userID, listCartItemsFunc)

	logs.Debug("已启动异步刷新购物车缓存任务, 用户ID: %d", userID)
}

// DeleteCartCache 删除用户购物车缓存
func (s *takeoutCartCacheService) DeleteCartCache(userID int64) error {
	if userID <= 0 {
		return fmt.Errorf("用户ID无效")
	}

	// 删除购物车列表缓存
	listCacheKey := fmt.Sprintf(TakeoutCartListCacheKey, userID)
	_, err := redis.Del(listCacheKey)
	if err != nil {
		logs.Warn("删除购物车列表缓存失败: %v, 用户ID: %d", err, userID)
	}

	// 删除购物车计数缓存
	countCacheKey := fmt.Sprintf(TakeoutCartCountCacheKey, userID)
	_, err = redis.Del(countCacheKey)
	if err != nil {
		logs.Error("删除购物车计数缓存失败: %v, 用户ID: %d", err, userID)
	}

	// 删除购物车详细计数缓存
	countDetailsCacheKey := fmt.Sprintf(TakeoutCartCountCacheKey+"_details", userID)
	_, err = redis.Del(countDetailsCacheKey)
	if err != nil {
		logs.Error("删除购物车详细计数缓存失败: %v, 用户ID: %d", err, userID)
	}

	logs.Debug("删除购物车缓存成功, 用户ID: %d", userID)
	return nil
}

// GetCartCount 从缓存获取购物车计数
func (s *takeoutCartCacheService) GetCartCount(userID int64) (int, bool, error) {
	if userID <= 0 {
		return 0, false, fmt.Errorf("用户ID无效")
	}

	cacheKey := fmt.Sprintf(TakeoutCartCountCacheKey, userID)
	cacheData, err := redis.Get(cacheKey)
	if err != nil {
		return 0, false, nil
	}

	var count int
	err = json.Unmarshal([]byte(cacheData), &count)
	if err != nil {
		logs.Error("解析购物车计数缓存数据失败: %v, 用户ID: %d", err, userID)
		return 0, false, err
	}

	logs.Debug("从缓存获取购物车计数成功, 用户ID: %d, 计数: %d", userID, count)
	return count, true, nil
}

// SetCartCount 将购物车计数存入缓存
func (s *takeoutCartCacheService) SetCartCount(userID int64, count int) error {
	if userID <= 0 {
		return fmt.Errorf("用户ID无效")
	}

	cacheKey := fmt.Sprintf(TakeoutCartCountCacheKey, userID)
	cacheData, err := json.Marshal(count)
	if err != nil {
		logs.Error("序列化购物车计数数据失败: %v, 用户ID: %d", err, userID)
		return err
	}

	err = redis.Set(cacheKey, string(cacheData), TakeoutCartCountCacheExpiration)
	if err != nil {
		logs.Error("存储购物车计数缓存失败: %v, 用户ID: %d", err, userID)
		return err
	}

	logs.Debug("购物车计数缓存存储成功, 用户ID: %d, 计数: %d", userID, count)
	return nil
}

// GetCartCountDetails 从缓存获取购物车详细计数
func (s *takeoutCartCacheService) GetCartCountDetails(userID int64) (*dto.CartCountDetailsDTO, bool, error) {
	if userID <= 0 {
		return nil, false, fmt.Errorf("用户ID无效")
	}

	cacheKey := fmt.Sprintf(TakeoutCartCountCacheKey+"_details", userID)
	cacheData, err := redis.Get(cacheKey)
	if err != nil {
		return nil, false, nil
	}

	var details dto.CartCountDetailsDTO
	err = json.Unmarshal([]byte(cacheData), &details)
	if err != nil {
		logs.Error("解析购物车详细计数缓存数据失败: %v, 用户ID: %d", err, userID)
		return nil, false, err
	}

	logs.Debug("从缓存获取购物车详细计数成功, 用户ID: %d", userID)
	return &details, true, nil
}

// SetCartCountDetails 将购物车详细计数存入缓存
func (s *takeoutCartCacheService) SetCartCountDetails(userID int64, details *dto.CartCountDetailsDTO) error {
	if userID <= 0 {
		return fmt.Errorf("用户ID无效")
	}

	cacheKey := fmt.Sprintf(TakeoutCartCountCacheKey+"_details", userID)
	cacheData, err := json.Marshal(details)
	if err != nil {
		logs.Error("序列化购物车详细计数数据失败: %v, 用户ID: %d", err, userID)
		return err
	}

	err = redis.Set(cacheKey, string(cacheData), TakeoutCartCountCacheExpiration)
	if err != nil {
		logs.Error("存储购物车详细计数缓存失败: %v, 用户ID: %d", err, userID)
		return err
	}

	logs.Debug("购物车详细计数缓存存储成功, 用户ID: %d", userID)
	return nil
}
