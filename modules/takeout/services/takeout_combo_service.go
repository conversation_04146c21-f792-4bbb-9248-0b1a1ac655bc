/**
 * 外卖套餐服务
 *
 * 本文件实现了外卖套餐组合的业务逻辑层，处理套餐组件和选项的创建、查询、更新等操作。
 * 支持如"粉面可加粉或加鸡蛋"等复杂组合场景，实现灵活的套餐组合功能。
 */

package services

import (
	"errors"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// TakeoutComboService 外卖套餐服务接口
type TakeoutComboService interface {
	// 套餐组件操作
	CreateComboItem(req *dto.TakeoutComboItemRequest) (int64, error)
	GetComboItemByID(id int64) (*dto.TakeoutComboItemDTO, error)
	UpdateComboItem(req *dto.UpdateComboItemRequest) error
	DeleteComboItem(id int64) error
	ListComboItemsByFoodID(foodID int64) ([]dto.TakeoutComboItemDTO, error)

	// 套餐选项操作
	CreateComboOption(req *dto.CreateComboOptionRequest) (int64, error)
	GetComboOptionByID(id int64) (*dto.TakeoutComboOptionDTO, error)
	UpdateComboOption(req *dto.UpdateComboOptionRequest) error
	DeleteComboOption(id int64) error
	ListComboOptionsByItemID(itemID int64) ([]dto.TakeoutComboOptionDTO, error)

	// 业务逻辑
	ValidateComboSelections(foodID int64, selections []dto.ComboSelectionRequest) error
	CalculateComboPrice(foodID int64, selections []dto.ComboSelectionRequest) (float64, error)
}

// takeoutComboService 外卖套餐服务实现
type takeoutComboService struct {
	comboRepo repositories.TakeoutComboRepository
	foodRepo  repositories.TakeoutFoodRepository
}

// NewTakeoutComboService 创建外卖套餐服务实例
func NewTakeoutComboService() TakeoutComboService {
	return &takeoutComboService{
		comboRepo: repositories.NewTakeoutComboRepository(),
		foodRepo:  repositories.NewTakeoutFoodRepository(),
	}
}

// CreateComboItem 创建套餐组件
func (s *takeoutComboService) CreateComboItem(req *dto.TakeoutComboItemRequest) (int64, error) {
	// 检查关联的食品是否存在
	food, err := s.foodRepo.GetByID(req.FoodID)
	if err != nil {
		return 0, err
	}

	// 创建套餐组件
	item := &models.TakeoutComboItem{
		FoodID:      req.FoodID,
		Name:        req.Name,
		Description: req.Description,
		MinSelect:   req.MinSelect,
		MaxSelect:   req.MaxSelect,
		IsRequired:  req.IsRequired,
		SortOrder:   req.SortOrder,
	}

	// 默认值处理
	if item.MinSelect <= 0 {
		item.MinSelect = 1
	}

	if item.MaxSelect <= 0 || item.MaxSelect < item.MinSelect {
		item.MaxSelect = item.MinSelect
	}

	// 保存到数据库
	id, err := s.comboRepo.CreateComboItem(item)
	if err != nil {
		logs.Error("创建套餐组件失败: %v, 请求: %+v", err, req)
		return 0, errors.New("创建套餐组件失败")
	}

	// 如果这是第一个组件，更新食品的IsCombination字段
	if !food.IsCombination {
		food.IsCombination = true
		err = s.foodRepo.Update(food)
		if err != nil {
			logs.Warn("更新食品IsCombination字段失败: %v, 食品ID: %d", err, food.ID)
			// 不影响组件创建结果
		}
	}

	return id, nil
}

// GetComboItemByID 根据ID获取套餐组件
func (s *takeoutComboService) GetComboItemByID(id int64) (*dto.TakeoutComboItemDTO, error) {
	// 获取套餐组件
	item, err := s.comboRepo.GetComboItemByID(id)
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	dto := &dto.TakeoutComboItemDTO{
		ID:          item.ID,
		FoodID:      item.FoodID,
		Name:        item.Name,
		Description: item.Description,
		MinSelect:   item.MinSelect,
		MaxSelect:   item.MaxSelect,
		IsRequired:  item.IsRequired,
		SortOrder:   item.SortOrder,
	}

	return dto, nil
}

// UpdateComboItem 更新套餐组件
func (s *takeoutComboService) UpdateComboItem(req *dto.UpdateComboItemRequest) error {
	// 从请求参数中获取ID
	id := req.ID
	// 获取现有套餐组件
	item, err := s.comboRepo.GetComboItemByID(id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != "" {
		item.Name = req.Name
	}
	if req.Description != "" {
		item.Description = req.Description
	}

	// 选择数量逻辑处理
	if req.MinSelect > 0 {
		item.MinSelect = req.MinSelect
	}

	if req.MaxSelect > 0 {
		item.MaxSelect = req.MaxSelect
	}

	// 确保最大选择数不小于最小选择数
	if item.MaxSelect < item.MinSelect {
		item.MaxSelect = item.MinSelect
	}

	item.IsRequired = req.IsRequired
	if req.SortOrder > 0 {
		item.SortOrder = req.SortOrder
	}

	// 保存到数据库
	err = s.comboRepo.UpdateComboItem(item)
	if err != nil {
		logs.Error("更新套餐组件失败: %v, ID: %d, 请求: %+v", err, id, req)
		return errors.New("更新套餐组件失败")
	}

	return nil
}

// DeleteComboItem 删除套餐组件
func (s *takeoutComboService) DeleteComboItem(id int64) error {
	// 获取当前组件信息
	item, err := s.comboRepo.GetComboItemByID(id)
	if err != nil {
		return err
	}

	// 检查是否有关联的选项
	options, err := s.comboRepo.ListComboOptionsByItemID(id)
	if err != nil {
		return err
	}

	if len(options) > 0 {
		return errors.New("该组件下存在选项，请先删除选项")
	}

	// 删除组件
	err = s.comboRepo.DeleteComboItem(id)
	if err != nil {
		logs.Error("删除套餐组件失败: %v, ID: %d", err, id)
		return errors.New("删除套餐组件失败")
	}

	// 检查食品是否还有其他组件
	items, err := s.comboRepo.ListComboItemsByFoodID(item.FoodID)
	if err == nil && len(items) == 0 {
		// 如果没有其他组件，更新食品的IsCombination字段
		food, err := s.foodRepo.GetByID(item.FoodID)
		if err == nil && food.IsCombination {
			food.IsCombination = false
			_ = s.foodRepo.Update(food)
		}
	}

	return nil
}

// ListComboItemsByFoodID 根据食品ID查询套餐组件列表
func (s *takeoutComboService) ListComboItemsByFoodID(foodID int64) ([]dto.TakeoutComboItemDTO, error) {
	// 获取套餐组件列表
	items, err := s.comboRepo.ListComboItemsByFoodID(foodID)
	if err != nil {
		logs.Error("查询套餐组件列表失败: %v, 食品ID: %d", err, foodID)
		return nil, errors.New("查询套餐组件列表失败")
	}

	// 转换为DTO
	itemDTOs := make([]dto.TakeoutComboItemDTO, 0, len(items))
	for _, item := range items {
		// 获取选项列表
		options, err := s.comboRepo.ListComboOptionsByItemID(item.ID)
		if err != nil {
			logs.Warn("获取套餐选项列表失败: %v, 组件ID: %d", err, item.ID)
			continue
		}

		optionDTOs := make([]dto.TakeoutComboOptionDTO, 0, len(options))
		for _, opt := range options {
			optionDTOs = append(optionDTOs, dto.TakeoutComboOptionDTO{
				ID:           opt.ID,
				ComboItemID:  opt.ComboItemID,
				Name:         opt.Name,
				Description:  opt.Description,
				Image:        opt.Image,
				ExtraPrice:   opt.ExtraPrice,
				Stock:        opt.Stock,
				SoldCount:    opt.SoldCount,
				IsDefault:    opt.IsDefault,
				MaxPerOrder:  opt.MaxPerOrder,
				IsIndividual: opt.IsIndividual,
				SortOrder:    opt.SortOrder,
			})
		}

		itemDTOs = append(itemDTOs, dto.TakeoutComboItemDTO{
			ID:          item.ID,
			FoodID:      item.FoodID,
			Name:        item.Name,
			Description: item.Description,
			MinSelect:   item.MinSelect,
			MaxSelect:   item.MaxSelect,
			IsRequired:  item.IsRequired,
			SortOrder:   item.SortOrder,
			Options:     optionDTOs,
		})
	}

	return itemDTOs, nil
}

// CreateComboOption 创建套餐选项
func (s *takeoutComboService) CreateComboOption(req *dto.CreateComboOptionRequest) (int64, error) {
	// 检查关联的套餐组件是否存在
	// 兼容旧版参数，优先使用 ComboItemID，如果不存在则使用 ItemID
	comboItemID := req.ComboItemID
	if comboItemID == 0 {
		comboItemID = req.ItemID
	}
	
	_, err := s.comboRepo.GetComboItemByID(comboItemID)
	if err != nil {
		return 0, err
	}

	// 创建套餐选项
	option := &models.TakeoutComboOption{
		ComboItemID:  req.ComboItemID,
		Name:         req.Name,
		Description:  req.Description,
		Image:        req.Image,
		ExtraPrice:   req.ExtraPrice,
		Stock:        req.Stock,
		IsDefault:    req.IsDefault,
		MaxPerOrder:  req.MaxPerOrder,
		IsIndividual: req.IsIndividual,
		SortOrder:    req.SortOrder,
	}

	// 库存默认值处理
	if option.Stock == 0 {
		option.Stock = -1 // 默认不限制库存
	}

	// 保存到数据库
	id, err := s.comboRepo.CreateComboOption(option)
	if err != nil {
		logs.Error("创建套餐选项失败: %v, 请求: %+v", err, req)
		return 0, errors.New("创建套餐选项失败")
	}

	return id, nil
}

// GetComboOptionByID 根据ID获取套餐选项
func (s *takeoutComboService) GetComboOptionByID(id int64) (*dto.TakeoutComboOptionDTO, error) {
	// 获取套餐选项
	option, err := s.comboRepo.GetComboOptionByID(id)
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	dto := &dto.TakeoutComboOptionDTO{
		ID:           option.ID,
		ComboItemID:  option.ComboItemID,
		Name:         option.Name,
		Description:  option.Description,
		Image:        option.Image,
		ExtraPrice:   option.ExtraPrice,
		Stock:        option.Stock,
		SoldCount:    option.SoldCount,
		IsDefault:    option.IsDefault,
		MaxPerOrder:  option.MaxPerOrder,
		IsIndividual: option.IsIndividual,
		SortOrder:    option.SortOrder,
	}

	return dto, nil
}

// UpdateComboOption 更新套餐选项
func (s *takeoutComboService) UpdateComboOption(req *dto.UpdateComboOptionRequest) error {
	// 获取选项ID
	id := req.ID
	// 获取现有套餐选项
	option, err := s.comboRepo.GetComboOptionByID(id)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != "" {
		option.Name = req.Name
	}
	if req.Description != "" {
		option.Description = req.Description
	}
	if req.Image != "" {
		option.Image = req.Image
	}

	// 更新价格相关字段
	if req.ExtraPrice > 0 {
		option.ExtraPrice = req.ExtraPrice
	} else if req.Price > 0 {
		// 兼容性处理，如果旧版使用 Price 字段，将其转换为 ExtraPrice
		option.ExtraPrice = req.Price
	}
	
	// 更新库存相关字段
	if req.Stock > 0 {
		option.Stock = req.Stock
	}
	if req.MaxPerOrder > 0 {
		option.MaxPerOrder = req.MaxPerOrder
	}

	// 更新布尔值
	option.IsDefault = req.IsDefault
	
	// 更新排序
	if req.SortOrder > 0 {
		option.SortOrder = req.SortOrder
	} else if req.DisplaySort > 0 {
		// 兼容性处理，如果旧版使用 DisplaySort 字段，将其转换为 SortOrder
		option.SortOrder = req.DisplaySort
	}

	// 保存到数据库
	err = s.comboRepo.UpdateComboOption(option)
	if err != nil {
		logs.Error("更新套餐选项失败: %v, ID: %d, 请求: %+v", err, id, req)
		return errors.New("更新套餐选项失败")
	}

	return nil
}

// DeleteComboOption 删除套餐选项
func (s *takeoutComboService) DeleteComboOption(id int64) error {
	// 删除选项
	err := s.comboRepo.DeleteComboOption(id)
	if err != nil {
		logs.Error("删除套餐选项失败: %v, ID: %d", err, id)
		return errors.New("删除套餐选项失败")
	}

	return nil
}

// ListComboOptionsByItemID 根据组件ID查询套餐选项列表
func (s *takeoutComboService) ListComboOptionsByItemID(itemID int64) ([]dto.TakeoutComboOptionDTO, error) {
	// 获取套餐选项列表
	options, err := s.comboRepo.ListComboOptionsByItemID(itemID)
	if err != nil {
		logs.Error("查询套餐选项列表失败: %v, 组件ID: %d", err, itemID)
		return nil, errors.New("查询套餐选项列表失败")
	}

	// 转换为DTO
	optionDTOs := make([]dto.TakeoutComboOptionDTO, 0, len(options))
	for _, opt := range options {
		optionDTOs = append(optionDTOs, dto.TakeoutComboOptionDTO{
			ID:           opt.ID,
			ComboItemID:  opt.ComboItemID,
			Name:         opt.Name,
			Description:  opt.Description,
			Image:        opt.Image,
			ExtraPrice:   opt.ExtraPrice,
			Stock:        opt.Stock,
			SoldCount:    opt.SoldCount,
			IsDefault:    opt.IsDefault,
			MaxPerOrder:  opt.MaxPerOrder,
			IsIndividual: opt.IsIndividual,
			SortOrder:    opt.SortOrder,
		})
	}

	return optionDTOs, nil
}

// ValidateComboSelections 验证套餐选择是否有效
func (s *takeoutComboService) ValidateComboSelections(foodID int64, selections []dto.ComboSelectionRequest) error {
	// 获取食品信息
	food, err := s.foodRepo.GetByID(foodID)
	if err != nil {
		return err
	}

	// 如果不是套餐组合，但提供了选择项，则报错
	if !food.IsCombination && len(selections) > 0 {
		return errors.New("该商品不是套餐组合，不支持选择套餐组件")
	}

	// 如果是套餐组合，但没有提供选择项，也不需要验证
	if food.IsCombination && len(selections) == 0 {
		return errors.New("该商品是套餐组合，请选择套餐组件")
	}

	// 如果不是套餐组合，无需验证
	if !food.IsCombination {
		return nil
	}

	// 获取所有组件
	items, err := s.comboRepo.ListComboItemsByFoodID(foodID)
	if err != nil {
		return err
	}

	// 将组件映射为map，便于查找
	itemMap := make(map[int64]*models.TakeoutComboItem)
	for _, item := range items {
		itemMap[item.ID] = item
	}

	// 检查必选项是否都选择了
	for _, item := range items {
		if !item.IsRequired {
			continue
		}

		// 查找该组件是否有选择
		found := false
		for _, selection := range selections {
			if selection.ItemID == item.ID {
				found = true

				// 检查选择数量是否符合要求
				if len(selection.OptionIDs) < item.MinSelect {
					return errors.New("组件 " + item.Name + " 至少需要选择 " + string(rune(item.MinSelect)) + " 项")
				}

				if len(selection.OptionIDs) > item.MaxSelect {
					return errors.New("组件 " + item.Name + " 最多只能选择 " + string(rune(item.MaxSelect)) + " 项")
				}

				break
			}
		}

		if !found {
			return errors.New("组件 " + item.Name + " 为必选项，请选择")
		}
	}

	// 收集所有选项ID，用于批量查询
	var optionIDs []int64
	for _, selection := range selections {
		for _, optionID := range selection.OptionIDs {
			optionIDs = append(optionIDs, optionID)
		}
	}

	// 批量获取选项信息
	options, err := s.comboRepo.BatchGetComboOptionsByIDs(optionIDs)
	if err != nil {
		return err
	}

	// 将选项映射为map，便于查找
	optionMap := make(map[int64]*models.TakeoutComboOption)
	for _, option := range options {
		optionMap[option.ID] = option
	}

	// 检查每个选择是否有效
	for _, selection := range selections {
		// 检查组件是否存在
		item, ok := itemMap[selection.ItemID]
		if !ok {
			return errors.New("选择了不存在的套餐组件")
		}
		
		// 使用 item 变量来检查组件的必选性
		if item.IsRequired && len(selection.OptionIDs) == 0 {
			return errors.New("必选组件 " + item.Name + " 没有选择选项")
		}

		// 检查每个选项是否属于该组件
		// 针对ComboSelectionRequest结构体，处理OptionIDs字段
		// OptionIDs是一个int64切片，代表选项ID列表
		for _, optionID := range selection.OptionIDs {
			option, ok := optionMap[optionID]
			if !ok {
				return errors.New("选择了不存在的选项")
			}

			if option.ComboItemID != selection.ItemID {
				return errors.New("选项不属于该组件")
			}

			// 检查单个选项的数量限制
			// 由于ComboSelectionRequest不包含数量信息，这里只检查选项存在性
			if option.MaxPerOrder > 0 {
				return errors.New("选项 " + option.Name + " 单次最多只能选择 " + string(rune(option.MaxPerOrder)) + " 份")
			}

			// 检查库存
			// 由于ComboSelectionRequest不包含数量信息，这里只检查选项存在性
			if option.Stock > 0 {
				return errors.New("选项 " + option.Name + " 库存不足")
			}
		}
	}

	return nil
}

// CalculateComboPrice 计算套餐价格
func (s *takeoutComboService) CalculateComboPrice(foodID int64, selections []dto.ComboSelectionRequest) (float64, error) {
	// 获取食品基本价格
	food, err := s.foodRepo.GetByID(foodID)
	if err != nil {
		return 0, err
	}

	// 基础价格
	totalPrice := food.Price

	// 如果不是套餐或没有选择项，直接返回基本价格
	if !food.IsCombination || len(selections) == 0 {
		return totalPrice, nil
	}

	// 收集所有选项ID和数量
	optionIDCounts := make(map[int64]int)
	for _, selection := range selections {
		// 针对ComboSelectionRequest结构体，处理OptionIDs字段
		// OptionIDs是一个int64切片，代表选项ID列表
		for _, optionID := range selection.OptionIDs {
			// 由于ComboSelectionRequest不包含数量信息，默认每个选项数量为1
			optionIDCounts[optionID] = 1
		}
	}

	// 获取所有选项的价格信息
	var optionIDs []int64
	for id := range optionIDCounts {
		optionIDs = append(optionIDs, id)
	}

	options, err := s.comboRepo.BatchGetComboOptionsByIDs(optionIDs)
	if err != nil {
		return 0, err
	}

	// 计算额外价格
	for _, option := range options {
		count := optionIDCounts[option.ID]
		totalPrice += option.ExtraPrice * float64(count)
	}

	return totalPrice, nil
}
