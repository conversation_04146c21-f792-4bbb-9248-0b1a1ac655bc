/**
 * takeout_config_service.go
 * 外卖模块配置服务
 *
 * 本文件实现了外卖模块的配置服务，提供动态配置加载和刷新功能
 * 支持自动分配骑手等功能的配置参数动态调整
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils/redis"
)

// ConfigKey 配置键
type ConfigKey string

const (
	// 自动分配骑手配置键
	ConfigKeyAutoAssign ConfigKey = "takeout_auto_assign"
	
	// Redis缓存键前缀
	redisCachePrefix = "takeout:config:"
	
	// 缓存过期时间（秒）
	cacheTTL = 1800 // 30分钟
)

// TakeoutConfigService 外卖配置服务接口
type TakeoutConfigService interface {
	// GetAutoAssignConfig 获取自动分配骑手配置
	GetAutoAssignConfig(ctx context.Context) (AutoAssignConfig, error)
	
	// SaveAutoAssignConfig 保存自动分配骑手配置
	SaveAutoAssignConfig(ctx context.Context, config AutoAssignConfig) error
	
	// ReloadConfig 重新加载配置
	ReloadConfig(ctx context.Context) error
}

// takeoutConfigService 外卖配置服务实现
type takeoutConfigService struct {
	systemConfigSvc services.SystemConfigService
	
	// 配置缓存
	configCache     map[ConfigKey]interface{}
	cacheMutex      sync.RWMutex
	cacheExpiration map[ConfigKey]time.Time
}

var (
	_configService     TakeoutConfigService
	_configServiceOnce sync.Once
)

// GetTakeoutConfigService 获取外卖配置服务单例
func GetTakeoutConfigService() TakeoutConfigService {
	_configServiceOnce.Do(func() {
		_configService = NewTakeoutConfigService()
		
		// 启动时加载配置
		ctx := context.Background()
		if err := _configService.ReloadConfig(ctx); err != nil {
			logs.Error("初始化外卖配置服务失败: %v", err)
		}
	})
	return _configService
}

// NewTakeoutConfigService 创建外卖配置服务实例
func NewTakeoutConfigService() TakeoutConfigService {
	return &takeoutConfigService{
		systemConfigSvc: impl.NewSystemConfigServiceImpl(),
		configCache:     make(map[ConfigKey]interface{}),
		cacheExpiration: make(map[ConfigKey]time.Time),
	}
}

// GetAutoAssignConfig 获取自动分配骑手配置
func (s *takeoutConfigService) GetAutoAssignConfig(ctx context.Context) (AutoAssignConfig, error) {
	// 默认配置，如果获取失败则使用这个
	defaultConfig := AutoAssignConfig{
		Enabled:        true,  // 默认启用
		RadiusKm:       5.0,   // 默认5公里
		MaxAttempts:    3,     // 默认最多尝试3次
		DistanceWeight: 0.5,   // 距离权重
		ScoreWeight:    0.3,   // 评分权重
		WorkloadWeight: 0.2,   // 工作量权重
	}

	// 尝试从缓存中获取配置
	s.cacheMutex.RLock()
	cachedConfig, exists := s.configCache[ConfigKeyAutoAssign]
	expiration, _ := s.cacheExpiration[ConfigKeyAutoAssign]
	s.cacheMutex.RUnlock()

	if exists && time.Now().Before(expiration) {
		// 缓存存在且未过期
		if config, ok := cachedConfig.(AutoAssignConfig); ok {
			return config, nil
		}
	}

	// 尝试从Redis缓存获取
	redisKey := redisCachePrefix + string(ConfigKeyAutoAssign)
	configStr, err := redis.Get(redisKey)
	if err == nil && configStr != "" {
		var config AutoAssignConfig
		if err := json.Unmarshal([]byte(configStr), &config); err == nil {
			// 更新本地缓存
			s.cacheMutex.Lock()
			s.configCache[ConfigKeyAutoAssign] = config
			s.cacheExpiration[ConfigKeyAutoAssign] = time.Now().Add(time.Duration(cacheTTL) * time.Second)
			s.cacheMutex.Unlock()
			return config, nil
		}
		// 解析出错，记录日志
		logs.Error("解析Redis中的自动分配骑手配置失败: %v", err)
	}

	// 从系统配置服务获取
	configValue, err := s.systemConfigSvc.GetConfigValueByKey(ctx, string(ConfigKeyAutoAssign))
	if err != nil {
		// 检查是否是配置键不存在的错误
		if err.Error() == fmt.Sprintf("配置键 %s 不存在", string(ConfigKeyAutoAssign)) {
			logs.Info("系统配置中未找到自动分配骑手配置键 %s，使用默认配置", string(ConfigKeyAutoAssign))
			return defaultConfig, nil
		}
		logs.Error("从系统配置服务获取自动分配骑手配置失败: %v", err)
		return defaultConfig, nil
	}

	if configValue == "" {
		logs.Info("系统配置中未找到自动分配骑手配置，使用默认值")
		return defaultConfig, nil
	}

	// 解析配置
	var config AutoAssignConfig
	if err := json.Unmarshal([]byte(configValue), &config); err != nil {
		logs.Error("解析自动分配骑手配置失败: %v, 使用默认配置", err)
		return defaultConfig, nil
	}

	// 更新本地缓存和Redis缓存
	s.cacheMutex.Lock()
	s.configCache[ConfigKeyAutoAssign] = config
	s.cacheExpiration[ConfigKeyAutoAssign] = time.Now().Add(time.Duration(cacheTTL) * time.Second)
	s.cacheMutex.Unlock()

	// 更新Redis缓存
	if configBytes, err := json.Marshal(config); err == nil {
		redis.Set(redisKey, string(configBytes), cacheTTL)
	}

	return config, nil
}

// SaveAutoAssignConfig 保存自动分配骑手配置
func (s *takeoutConfigService) SaveAutoAssignConfig(ctx context.Context, config AutoAssignConfig) error {
	// 将配置序列化为JSON
	configBytes, err := json.Marshal(config)
	if err != nil {
		logs.Error("序列化自动分配骑手配置失败: %v", err)
		return err
	}

	// 保存到系统配置
	if err := s.systemConfigSvc.UpdateConfigValue(ctx, string(ConfigKeyAutoAssign), string(configBytes)); err != nil {
		logs.Error("保存自动分配骑手配置到系统配置失败: %v", err)
		return err
	}

	// 更新本地缓存
	s.cacheMutex.Lock()
	s.configCache[ConfigKeyAutoAssign] = config
	s.cacheExpiration[ConfigKeyAutoAssign] = time.Now().Add(time.Duration(cacheTTL) * time.Second)
	s.cacheMutex.Unlock()

	// 更新Redis缓存
	redisKey := redisCachePrefix + string(ConfigKeyAutoAssign)
	redis.Set(redisKey, string(configBytes), cacheTTL)

	logs.Info("成功保存自动分配骑手配置")
	return nil
}

// ReloadConfig 重新加载配置
func (s *takeoutConfigService) ReloadConfig(ctx context.Context) error {
	// 清空缓存
	s.cacheMutex.Lock()
	s.configCache = make(map[ConfigKey]interface{})
	s.cacheExpiration = make(map[ConfigKey]time.Time)
	s.cacheMutex.Unlock()

	// 加载自动分配骑手配置
	_, err := s.GetAutoAssignConfig(ctx)
	if err != nil {
		logs.Error("加载自动分配骑手配置失败: %v", err)
		return err
	}

	logs.Info("成功重新加载外卖模块配置")
	return nil
}
