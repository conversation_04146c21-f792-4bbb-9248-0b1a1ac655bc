/**
 * 外卖模块 - 优惠券服务
 * 描述：定义优惠券相关的业务逻辑
 * 作者：系统
 * 创建时间：2025-05-14
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	merchantModels "o_mall_backend/modules/merchant/models"
	merchantRepos "o_mall_backend/modules/merchant/repositories"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// ITakeoutCouponService 优惠券服务接口
type ITakeoutCouponService interface {
	// 创建优惠券
	CreateCoupon(req *dto.CreateCouponRequest) (*models.TakeoutCoupon, error)

	// 根据ID获取优惠券详情
	GetCouponByID(couponID int64) (*models.TakeoutCoupon, error)

	// 根据商家ID和优惠券ID获取优惠券详情（验证商家权限）
	GetCouponByMerchantIDAndID(merchantID int64, couponID int64) (*models.TakeoutCoupon, error)

	// 获取商家优惠券列表
	GetMerchantCoupons(merchantID int64, page, pageSize int) (*dto.CouponListResponse, error)

	// 向用户发放优惠券
	IssueCouponToUser(couponID int64, userID int64) error

	// 批量向用户发放优惠券
	IssueCouponToUsers(couponID int64, userIDs []int64) error

	// 用户领取优惠券
	ClaimCoupon(userID int64, couponID int64) (*models.TakeoutUserCoupon, error)

	// 获取用户的优惠券列表
	GetUserCoupons(userID int64, status int, page, pageSize int) (*dto.UserCouponListResponse, error)

	// 使用优惠券
	UseCoupon(userID int64, couponID int64, orderID int64) error

	// 检查优惠券是否可用
	CheckCouponAvailability(userID int64, couponID int64, totalAmount float64) (*dto.CouponValidationResult, error)

	// 验证优惠券与订单的匹配性
	ValidateCouponForOrder(userID int64, couponID int64, order *dto.OrderValidationInfo) (*dto.CouponValidationResult, error)

	// GetCouponCenterList 获取优惠券中心列表
	GetCouponCenterList(userID int64, category string, page, pageSize int) (*dto.CouponCenterListResponse, error)

	// 发布优惠券
	PublishCoupon(couponID int64, merchantID int64) error

	// 获取商家信息
	GetMerchantByID(merchantID int64) (*merchantModels.Merchant, error)
}

// TakeoutCouponService 优惠券服务实现
type TakeoutCouponService struct {
	couponRepo     repositories.ITakeoutCouponRepository
	userCouponRepo repositories.ITakeoutUserCouponRepository
	merchantRepo   merchantRepos.MerchantRepository
}

// NewTakeoutCouponService 创建优惠券服务
func NewTakeoutCouponService(
	couponRepo repositories.ITakeoutCouponRepository,
	userCouponRepo repositories.ITakeoutUserCouponRepository,
	merchantRepo merchantRepos.MerchantRepository,
) ITakeoutCouponService {
	return &TakeoutCouponService{
		couponRepo:     couponRepo,
		userCouponRepo: userCouponRepo,
		merchantRepo:   merchantRepo,
	}
}

// CreateCoupon 创建优惠券
func (s *TakeoutCouponService) CreateCoupon(req *dto.CreateCouponRequest) (*models.TakeoutCoupon, error) {
	// 解析时间（使用本地时区）
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		return nil, errors.New("开始时间格式错误")
	}

	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		return nil, errors.New("结束时间格式错误")
	}

	// 验证时间
	if startTime.Before(time.Now()) {
		return nil, errors.New("开始时间不能早于当前时间")
	}

	if endTime.Before(startTime) {
		return nil, errors.New("结束时间不能早于开始时间")
	}

	// 验证优惠券类型
	if req.Type < 1 || req.Type > 3 {
		return nil, errors.New("无效的优惠券类型")
	}

	// 验证最低订单金额
	if req.MinOrderAmount < 0 {
		return nil, errors.New("最低订单金额不能为负")
	}

	// 生成优惠券码
	couponCode := fmt.Sprintf("%s%d", time.Now().Format("20060102"), time.Now().UnixNano()%10000)

	// u521bu5efau4f18u60e0u5238
	coupon := &models.TakeoutCoupon{
		PromotionID:       req.PromotionID,
		MerchantID:        req.MerchantID,
		Code:              couponCode,
		Name:              req.Name,
		Description:       req.Description,
		Type:              req.Type,
		Amount:            req.Amount,
		MinOrderAmount:    req.MinOrderAmount,
		MaxDiscountAmount: req.MaxDiscountAmount,
		ApplyToAll:        req.ApplyToAll,
		ApplyToCategories: req.ApplyToCategories,
		ApplyToFoods:      req.ApplyToFoods,
		ExcludeFoods:      req.ExcludeFoods,
		UserLevelLimit:    req.UserLevelLimit,
		PerUserLimit:      req.PerUserLimit,
		DailyLimit:        req.DailyLimit,
		TotalLimit:        req.TotalLimit,
		StartTime:         startTime,
		EndTime:           endTime,
		Status:            models.CouponStatusPending, // 创建时默认为待发布状态
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// u4fddu5b58u5230u6570u636eu5e93
	id, err := s.couponRepo.Create(coupon)
	if err != nil {
		logs.Error("u521bu5efau4f18u60e0u5238u5931u8d25: %v", err)
		return nil, errors.New("u521bu5efau4f18u60e0u5238u5931u8d25")
	}

	coupon.ID = id
	return coupon, nil
}

// GetCouponByID 根据ID获取优惠券详情
func (s *TakeoutCouponService) GetCouponByID(couponID int64) (*models.TakeoutCoupon, error) {
	if couponID <= 0 {
		return nil, errors.New("无效的优惠券ID")
	}

	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		logs.Error("获取优惠券详情失败: %v", err)
		return nil, errors.New("获取优惠券详情失败")
	}

	return coupon, nil
}

// GetCouponByMerchantIDAndID 根据商家ID和优惠券ID获取优惠券详情（验证商家权限）
func (s *TakeoutCouponService) GetCouponByMerchantIDAndID(merchantID int64, couponID int64) (*models.TakeoutCoupon, error) {
	if merchantID <= 0 {
		return nil, errors.New("无效的商家ID")
	}

	if couponID <= 0 {
		return nil, errors.New("无效的优惠券ID")
	}

	// 获取优惠券信息
	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		logs.Error("获取优惠券详情失败: %v", err)
		return nil, errors.New("获取优惠券详情失败")
	}

	// 验证商家权限
	if coupon.MerchantID != merchantID {
		return nil, errors.New("无权访问此优惠券")
	}

	return coupon, nil
}

// GetMerchantCoupons 获取商家优惠券列表
func (s *TakeoutCouponService) GetMerchantCoupons(merchantID int64, page, pageSize int) (*dto.CouponListResponse, error) {
	if merchantID <= 0 {
		return nil, errors.New("无效的商家ID")
	}

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 从仓储层获取商家优惠券列表
	coupons, total, err := s.couponRepo.GetByMerchantID(merchantID, page, pageSize)
	if err != nil {
		logs.Error("获取商家优惠券列表失败: %v", err)
		return nil, errors.New("获取商家优惠券列表失败")
	}

	// 转换为响应格式
	var couponResponses []dto.CouponResponse
	for _, coupon := range coupons {
		couponResponse := dto.ConvertToCouponResponse(coupon)
		if couponResponse != nil {
			couponResponses = append(couponResponses, *couponResponse)
		}
	}

	return &dto.CouponListResponse{
		Total: int(total),
		List:  couponResponses,
	}, nil
}

// IssueCouponToUser 向用户发放优惠券
func (s *TakeoutCouponService) IssueCouponToUser(couponID int64, userID int64) error {
	// 获取优惠券信息
	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		return errors.New("优惠券不存在")
	}

	// 检查优惠券状态，只有已发布（未使用）状态的优惠券才能发放
	if coupon.Status != models.CouponStatusUnused {
		return errors.New("只有已发布的优惠券才能发放给用户")
	}

	// 检查优惠券是否在有效期内
	now := time.Now()
	if now.Before(coupon.StartTime) || now.After(coupon.EndTime) {
		return errors.New("优惠券不在有效期内")
	}

	// 检查用户是否已经拥有该优惠券
	exists, err := s.userCouponRepo.ExistsByUserIDAndCouponID(userID, couponID)
	if err != nil {
		logs.Error("检查用户是否拥有优惠券失败: %v", err)
		return errors.New("检查用户是否拥有优惠券失败")
	}

	if exists {
		return errors.New("用户已拥有该优惠券")
	}

	// 检查每人领取限制
	if coupon.PerUserLimit > 0 {
		count, err := s.userCouponRepo.CountUserUsage(userID, couponID)
		if err != nil {
			logs.Error("统计用户领取次数失败: %v", err)
			return errors.New("统计用户领取次数失败")
		}

		if count >= coupon.PerUserLimit {
			return errors.New("超过每人可领取次数限制")
		}
	}

	// 检查当日发放限制
	if coupon.DailyLimit > 0 {
		dailyCount, err := s.couponRepo.GetDailyIssueCount(couponID)
		if err != nil {
			logs.Error("统计当日发放次数失败: %v", err)
			return errors.New("统计当日发放次数失败")
		}

		if int(dailyCount) >= coupon.DailyLimit {
			return errors.New("超过每日发放限制")
		}
	}

	// 检查总发放限制
	if coupon.TotalLimit > 0 {
		totalCount, err := s.userCouponRepo.CountTotalIssued(couponID)
		if err != nil {
			logs.Error("统计总发放次数失败: %v", err)
			return errors.New("统计总发放次数失败")
		}

		if int(totalCount) >= coupon.TotalLimit {
			return errors.New("超过总发放限制")
		}
	}

	// 创建用户优惠券
	userCoupon := &models.TakeoutUserCoupon{
		UserID:    userID,
		CouponID:  couponID,
		Status:    models.UserCouponStatusUnused,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 保存到数据库
	_, err = s.userCouponRepo.Create(userCoupon)
	if err != nil {
		logs.Error("创建用户优惠券失败: %v", err)
		return errors.New("创建用户优惠券失败")
	}

	return nil
}

// IssueCouponToUsers 批量向用户发放优惠券
func (s *TakeoutCouponService) IssueCouponToUsers(couponID int64, userIDs []int64) error {
	if len(userIDs) == 0 {
		return errors.New("用户列表不能为空")
	}

	// 获取优惠券信息
	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		return errors.New("优惠券不存在")
	}

	// 检查优惠券状态
	if coupon.Status != models.CouponStatusUnused {
		return errors.New("优惠券状态无效")
	}

	// 检查总发放限制
	if coupon.TotalLimit > 0 {
		totalCount, err := s.userCouponRepo.CountTotalIssued(couponID)
		if err != nil {
			logs.Error("统计总发放次数失败: %v", err)
			return errors.New("统计总发放次数失败")
		}

		if int(totalCount)+len(userIDs) > coupon.TotalLimit {
			return errors.New("超过总发放限制")
		}
	}

	// 为每个用户发放优惠券
	for _, userID := range userIDs {
		err := s.IssueCouponToUser(couponID, userID)
		if err != nil {
			logs.Warn("为用户 %d 发放优惠券失败: %v", userID, err)
			// 继续为其他用户发放
		}
	}

	return nil
}

// GetUserCoupons 获取用户的优惠券列表
func (s *TakeoutCouponService) GetUserCoupons(userID int64, status int, page, pageSize int) (*dto.UserCouponListResponse, error) {
	// 获取用户优惠券列表
	userCoupons, coupons, total, err := s.userCouponRepo.GetUserCouponsWithDetail(userID, status, page, pageSize)
	if err != nil {
		return nil, errors.New("获取用户优惠券失败")
	}

	// 实时检查并更新过期的优惠券状态
	now := time.Now()
	var updatedUserCoupons []*models.TakeoutUserCoupon
	var updatedCoupons []*models.TakeoutCoupon

	for i, userCoupon := range userCoupons {
		if i < len(coupons) {
			coupon := coupons[i]
			// 检查优惠券是否已过期但状态仍为未使用
			if userCoupon.Status == models.UserCouponStatusUnused && now.After(coupon.EndTime) {
				// 更新数据库中的状态为已过期
				err := s.userCouponRepo.UpdateStatus(userCoupon.ID, models.UserCouponStatusExpired, 0)
				if err != nil {
					logs.Error("更新过期优惠券状态失败: %v", err)
				} else {
					// 更新内存中的状态
					userCoupon.Status = models.UserCouponStatusExpired
					logs.Info("优惠券已过期，状态已更新: 用户优惠券ID=%d, 优惠券ID=%d", userCoupon.ID, coupon.ID)
				}
			}
			updatedUserCoupons = append(updatedUserCoupons, userCoupon)
			updatedCoupons = append(updatedCoupons, coupon)
		}
	}

	// 1. 收集所有涉及的商家ID
	merchantIDs := make(map[int64]struct{})
	for _, coupon := range updatedCoupons {
		if coupon.MerchantID > 0 {
			merchantIDs[coupon.MerchantID] = struct{}{}
		}
	}

	// 2. 批量获取商家信息
	merchantInfoMap := make(map[int64]*merchantModels.Merchant)
	ctx := context.Background()
	for merchantID := range merchantIDs {
		merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
		if err != nil {
			logs.Warn("获取商家信息失败，商家ID: %d, 错误: %v", merchantID, err)
			merchantInfoMap[merchantID] = &merchantModels.Merchant{
				ID:   merchantID,
				Name: "未知商家",
				Logo: "",
			}
		} else if merchant == nil {
			merchantInfoMap[merchantID] = &merchantModels.Merchant{
				ID:   merchantID,
				Name: "未知商家",
				Logo: "",
			}
		} else {
			merchantInfoMap[merchantID] = merchant
		}
	}

	// 3. 构建响应
	userCouponList := make([]dto.UserCouponResponse, 0, len(updatedUserCoupons))
	for _, userCoupon := range updatedUserCoupons {
		// 获取对应的优惠券详情
		var coupon *models.TakeoutCoupon
		// 在返回的优惠券列表中查找匹配的优惠券
		for _, c := range updatedCoupons {
			if c.ID == userCoupon.CouponID {
				coupon = c
				break
			}
		}

		// 如果在列表中未找到，则从数据库获取
		if coupon == nil {
			var err error
			coupon, err = s.couponRepo.GetByID(userCoupon.CouponID)
			if err != nil {
				continue
			}
		}

		// 获取商家信息
		var merchantName, merchantLogo string
		if coupon.MerchantID > 0 {
			if merchant, exists := merchantInfoMap[coupon.MerchantID]; exists && merchant != nil {
				merchantName = merchant.Name
				merchantLogo = merchant.Logo
			} else {
				merchantName = "未知商家"
				merchantLogo = ""
			}
		}

		// 转换为响应
		response := *dto.ConvertToUserCouponResponseWithMerchant(userCoupon, coupon, merchantName, merchantLogo)
		userCouponList = append(userCouponList, response)
	}

	return &dto.UserCouponListResponse{
		List:  userCouponList,
		Total: int(total),
	}, nil
}

// UseCoupon 使用优惠券
func (s *TakeoutCouponService) UseCoupon(userID int64, couponID int64, orderID int64) error {
	// 获取用户优惠券信息
	userCoupons, _, _, err := s.userCouponRepo.GetUserCouponsWithDetail(userID, models.UserCouponStatusUnused, 1, 100)
	if err != nil {
		logs.Error("获取用户优惠券失败: %v", err)
		return errors.New("获取用户优惠券失败")
	}

	// 查找指定优惠券
	var targetUserCoupon *models.TakeoutUserCoupon
	for _, uc := range userCoupons {
		if uc.CouponID == couponID {
			targetUserCoupon = uc
			break
		}
	}

	if targetUserCoupon == nil {
		return errors.New("用户没有该优惠券或优惠券已使用")
	}

	// 更新优惠券状态
	err = s.userCouponRepo.UpdateStatus(targetUserCoupon.ID, models.UserCouponStatusUsed, orderID)
	if err != nil {
		logs.Error("更新优惠券状态失败: %v", err)
		return errors.New("更新优惠券状态失败")
	}

	return nil
}

// ClaimCoupon 用户领取优惠券
func (s *TakeoutCouponService) ClaimCoupon(userID int64, couponID int64) (*models.TakeoutUserCoupon, error) {
	// 获取优惠券信息
	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		return nil, errors.New("优惠券不存在")
	}

	// 检查优惠券状态
	if coupon.Status != models.CouponStatusUnused {
		return nil, errors.New("优惠券状态无效")
	}

	// 检查优惠券时间有效性
	now := time.Now()
	if now.Before(coupon.StartTime) || now.After(coupon.EndTime) {
		return nil, errors.New("优惠券不在有效期内")
	}

	// 检查用户是否已经领取过
	exists, err := s.userCouponRepo.ExistsByUserIDAndCouponID(userID, couponID)
	if err != nil {
		return nil, errors.New("检查优惠券领取状态失败")
	}

	if exists {
		return nil, errors.New("您已经领取过这张优惠券")
	}

	// 检查用户等级限制
	if coupon.UserLevelLimit > 0 {
		// 实际应用中需要根据用户系统获取用户等级
		// 这里为了简化，暂时不进行验证
	}

	// 检查每日领取限制
	if coupon.DailyLimit > 0 {
		// TODO: 实现每日领取限制检查
	}

	// 🔧 修复：使用原子操作更新已发放数量（带限制检查）
	err = s.couponRepo.IncrementIssuedCountWithLimit(couponID, coupon.TotalLimit)
	if err != nil {
		return nil, err // 如果更新失败（比如已领完），直接返回错误
	}

	// 创建用户优惠券记录
	userCoupon := &models.TakeoutUserCoupon{
		UserID:    userID,
		CouponID:  couponID,
		Status:    models.UserCouponStatusUnused,
		CreatedAt: time.Now(),
	}

	// 保存用户优惠券
	id, err := s.userCouponRepo.Create(userCoupon)
	if err != nil {
		// 如果保存用户优惠券失败，需要回滚已发放数量
		// 这里简化处理，记录错误日志
		logs.Error("保存用户优惠券失败，需要手动检查数据一致性: %v", err)
		return nil, errors.New("保存用户优惠券失败")
	}

	userCoupon.ID = id
	return userCoupon, nil
}

// CheckCouponAvailability 检查优惠券是否可用
func (s *TakeoutCouponService) CheckCouponAvailability(userID int64, couponID int64, totalAmount float64) (*dto.CouponValidationResult, error) {
	// 获取优惠券信息
	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		return &dto.CouponValidationResult{
			CouponID: couponID,
			Valid:    false,
			Message:  "优惠券不存在",
		}, nil
	}

	// 检查优惠券是否过期
	now := time.Now()
	if now.Before(coupon.StartTime) || now.After(coupon.EndTime) {
		return &dto.CouponValidationResult{
			CouponID: couponID,
			Valid:    false,
			Message:  "优惠券不在有效期内",
		}, nil
	}

	// 检查优惠券状态
	if coupon.Status != models.CouponStatusUnused {
		return &dto.CouponValidationResult{
			CouponID: couponID,
			Valid:    false,
			Message:  "优惠券状态无效",
		}, nil
	}

	// 检查订单金额限制
	if totalAmount < coupon.MinOrderAmount {
		return &dto.CouponValidationResult{
			CouponID: couponID,
			Valid:    false,
			Message:  fmt.Sprintf("订单金额不满足优惠券使用条件，最低需要%.2f元", coupon.MinOrderAmount),
		}, nil
	}

	// 检查用户是否拥有该优惠券
	exists, err := s.userCouponRepo.ExistsByUserIDAndCouponID(userID, couponID)
	if err != nil || !exists {
		return &dto.CouponValidationResult{
			CouponID: couponID,
			Valid:    false,
			Message:  "用户没有该优惠券",
		}, nil
	}

	// 计算优惠金额
	var discountAmount float64
	if coupon.Type == models.CouponTypeAmount {
		// 满减券
		discountAmount = coupon.Amount
	} else if coupon.Type == models.CouponTypeDiscount {
		// 折扣券
		discountRate := coupon.Amount / 10.0 // 假设存储的是折扣率，如8.5表示85折
		discountAmount = totalAmount * (1 - discountRate)

		// 如果有最高优惠限制
		if coupon.MaxDiscountAmount > 0 && discountAmount > coupon.MaxDiscountAmount {
			discountAmount = coupon.MaxDiscountAmount
		}
	}

	return &dto.CouponValidationResult{
		CouponID:       couponID,
		Valid:          true,
		DiscountAmount: discountAmount,
		Message:        "优惠券可用",
	}, nil
}

// ValidateCouponForOrder 验证优惠券与订单的匹配性
func (s *TakeoutCouponService) ValidateCouponForOrder(userID int64, couponID int64, order *dto.OrderValidationInfo) (*dto.CouponValidationResult, error) {
	// 获取优惠券信息
	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		return nil, errors.New("优惠券不存在")
	}

	// 检查优惠券是否过期
	now := time.Now()
	if now.Before(coupon.StartTime) || now.After(coupon.EndTime) {
		return nil, errors.New("优惠券不在有效期内")
	}

	// 检查优惠券状态
	if coupon.Status != models.CouponStatusUnused {
		return nil, errors.New("优惠券状态无效")
	}

	// 检查订单金额限制
	if order.TotalAmount < coupon.MinOrderAmount {
		return nil, fmt.Errorf("订单金额不满足优惠券使用条件，最低需要%.2f元", coupon.MinOrderAmount)
	}

	// 检查商品适用性
	if !coupon.ApplyToAll {
		isValid := false
		// 检查商品是否在适用范围内
		for _, item := range order.Items {
			// 检查分类是否适用
			if strings.Contains(coupon.ApplyToCategories, fmt.Sprintf(",%d,", item.CategoryID)) {
				isValid = true
				break
			}

			// 检查商品是否适用
			if strings.Contains(coupon.ApplyToFoods, fmt.Sprintf(",%d,", item.FoodID)) {
				isValid = true
				break
			}
		}

		if !isValid {
			return nil, errors.New("当前购物车商品不满足优惠券使用条件")
		}

		// 检查是否有排除商品
		for _, item := range order.Items {
			if strings.Contains(coupon.ExcludeFoods, fmt.Sprintf(",%d,", item.FoodID)) {
				return nil, errors.New("购物车中包含不可使用该优惠券的商品")
			}
		}
	}

	// 检查用户使用次数
	usedCount, err := s.userCouponRepo.CountUserUsage(userID, couponID)
	if err == nil && usedCount >= coupon.PerUserLimit && coupon.PerUserLimit > 0 {
		return nil, errors.New("您已达到该优惠券使用上限")
	}

	// 计算优惠金额
	var discountAmount float64
	if coupon.Type == models.CouponTypeAmount {
		// 满减券
		discountAmount = coupon.Amount
	} else if coupon.Type == models.CouponTypeDiscount {
		// 折扣券
		discountRate := coupon.Amount / 10.0 // 假设存储的是折扣率，如8.5表示85折
		discountAmount = order.TotalAmount * (1 - discountRate)

		// 如果有最高优惠限制
		if coupon.MaxDiscountAmount > 0 && discountAmount > coupon.MaxDiscountAmount {
			discountAmount = coupon.MaxDiscountAmount
		}
	}

	// 返回验证结果
	return &dto.CouponValidationResult{
		CouponID:       couponID,
		Valid:          true,
		DiscountAmount: discountAmount,
		Message:        "优惠券可用",
	}, nil
}

// GetCouponCenterList 获取优惠券中心列表
func (s *TakeoutCouponService) GetCouponCenterList(userID int64, category string, page, pageSize int) (*dto.CouponCenterListResponse, error) {
	logs.Info("获取优惠券中心列表: userID=%d, category=%s, page=%d, pageSize=%d", userID, category, page, pageSize)

	// 1. 获取所有潜在可用的优惠券
	allCoupons, err := s.couponRepo.FindAvailableCoupons(category)
	if err != nil {
		logs.Error("Failed to get available coupons: %v", err)
		return nil, errors.New("获取优惠券列表失败")
	}

	logs.Info("从数据库获取到 %d 个潜在可用优惠券", len(allCoupons))

	// 2. 在内存中过滤掉已领完的优惠券
	var filteredCoupons []models.TakeoutCoupon
	for _, coupon := range allCoupons {
		// 如果 TotalLimit > 0 (0表示不限量), 则检查库存
		if coupon.TotalLimit > 0 && coupon.IssuedCount >= coupon.TotalLimit {
			logs.Debug("优惠券 %d (%s) 已领完，跳过 (已发放: %d, 限制: %d)", coupon.ID, coupon.Name, coupon.IssuedCount, coupon.TotalLimit)
			continue // 已领完，跳过
		}
		logs.Debug("优惠券 %d (%s) 可用 (状态: %d, 已发放: %d, 限制: %d)", coupon.ID, coupon.Name, coupon.Status, coupon.IssuedCount, coupon.TotalLimit)
		filteredCoupons = append(filteredCoupons, coupon)
	}

	logs.Info("过滤后剩余 %d 个可用优惠券", len(filteredCoupons))

	// 3. 按创建时间降序排序
	sort.Slice(filteredCoupons, func(i, j int) bool {
		return filteredCoupons[i].CreatedAt.After(filteredCoupons[j].CreatedAt)
	})

	// 4. 获取用户已领取的优惠券ID列表
	userCouponIDs, err := s.userCouponRepo.GetUserCouponIDs(userID)
	if err != nil {
		logs.Error("Failed to get user coupon IDs: %v", err)
		return nil, errors.New("获取用户优惠券信息失败")
	}
	userCouponIDSet := make(map[int64]struct{})
	for _, id := range userCouponIDs {
		userCouponIDSet[id] = struct{}{}
	}

	// 5. 手动分页
	total := int64(len(filteredCoupons))
	start := (page - 1) * pageSize
	end := start + pageSize
	if start > len(filteredCoupons) {
		start = len(filteredCoupons)
	}
	if end > len(filteredCoupons) {
		end = len(filteredCoupons)
	}
	paginatedCoupons := filteredCoupons[start:end]

	// 6. 获取所有涉及的商家ID
	merchantIDs := make(map[int64]struct{})
	for _, coupon := range paginatedCoupons {
		merchantIDs[coupon.MerchantID] = struct{}{}
	}

	// 7. 批量获取商家信息
	merchantInfoMap := make(map[int64]*merchantModels.Merchant)
	ctx := context.Background() // 创建上下文
	for merchantID := range merchantIDs {
		merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
		if err != nil {
			logs.Warn("获取商家信息失败，商家ID: %d, 错误: %v", merchantID, err)
			// 如果获取商家信息失败，使用默认值
			merchantInfoMap[merchantID] = &merchantModels.Merchant{
				ID:   merchantID,
				Name: "未知商家",
				Logo: "",
			}
		} else if merchant == nil {
			// 商家不存在的情况
			logs.Warn("商家不存在，商家ID: %d", merchantID)
			merchantInfoMap[merchantID] = &merchantModels.Merchant{
				ID:   merchantID,
				Name: "未知商家",
				Logo: "",
			}
		} else {
			merchantInfoMap[merchantID] = merchant
		}
	}

	// 8. 组装DTO（包含商家信息）
	var dtoList []*dto.CouponCenterItemDTO
	for _, coupon := range paginatedCoupons {
		// 需要传递指针
		couponCopy := coupon

		// 获取商家信息
		merchant := merchantInfoMap[coupon.MerchantID]
		merchantName := "未知商家"
		merchantLogo := ""
		if merchant != nil {
			merchantName = merchant.Name
			merchantLogo = merchant.Logo
		}

		itemDTO := &dto.CouponCenterItemDTO{
			CouponResponse: *dto.ConvertToCouponResponseWithMerchant(&couponCopy, merchantName, merchantLogo),
		}

		// 检查用户是否已领取
		if _, ok := userCouponIDSet[coupon.ID]; ok {
			itemDTO.CanClaim = false
			itemDTO.ClaimStatusText = "已领取"
		} else {
			// 在这里，所有券都是可领取的（因为已过滤掉领完的）
			itemDTO.CanClaim = true
			itemDTO.ClaimStatusText = "立即领取"
		}

		dtoList = append(dtoList, itemDTO)
	}

	return &dto.CouponCenterListResponse{
		Total: total,
		List:  dtoList,
	}, nil
}

// PublishCoupon 发布优惠券
func (s *TakeoutCouponService) PublishCoupon(couponID int64, merchantID int64) error {
	// 获取优惠券信息
	coupon, err := s.couponRepo.GetByID(couponID)
	if err != nil {
		return errors.New("优惠券不存在")
	}

	// 验证商户权限
	if coupon.MerchantID != merchantID {
		return errors.New("无权发布此优惠券")
	}

	// 验证优惠券状态
	if coupon.Status != models.CouponStatusPending {
		return errors.New("只有待发布状态的优惠券才能发布")
	}

	// 验证优惠券时间
	now := time.Now()

	// 检查结束时间不能早于当前时间（已过期的优惠券不能发布）
	if coupon.EndTime.Before(now) {
		return errors.New("优惠券已过期，无法发布")
	}

	// 检查结束时间不能早于开始时间
	if coupon.EndTime.Before(coupon.StartTime) {
		return errors.New("结束时间不能早于开始时间")
	}

	// 注意：允许开始时间早于当前时间，这样可以发布立即生效的优惠券

	// 更新优惠券状态为已发布（未使用）
	err = s.couponRepo.UpdateStatus(couponID, models.CouponStatusUnused)
	if err != nil {
		logs.Error("发布优惠券失败: %v", err)
		return errors.New("发布优惠券失败")
	}

	logs.Info("优惠券发布成功，优惠券ID: %d, 商户ID: %d", couponID, merchantID)
	return nil
}

// GetMerchantByID 获取商家信息
func (s *TakeoutCouponService) GetMerchantByID(merchantID int64) (*merchantModels.Merchant, error) {
	ctx := context.Background()
	merchant, err := s.merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		logs.Error("获取商家信息失败: %v", err)
		return nil, err
	}
	return merchant, nil
}
