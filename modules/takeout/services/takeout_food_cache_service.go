/**
 * 外卖食品缓存服务
 *
 * 本文件实现了外卖食品的Redis缓存功能，提供食品信息的缓存存储、获取和更新操作。
 * 支持单个食品缓存、批量缓存、分类食品列表缓存等功能，提高系统性能。
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/utils/redis"

	"github.com/beego/beego/v2/core/logs"
)

// 缓存键常量定义
const (
	// TakeoutFoodCacheKeyPrefix 外卖食品缓存键前缀
	TakeoutFoodCacheKeyPrefix = "takeout:food:"
	// TakeoutFoodDetailCacheKey 单个食品详情缓存键
	TakeoutFoodDetailCacheKey = "takeout:food:detail:%d"
	// FoodInfoCacheKey 食品基本信息缓存键
	FoodInfoCacheKey = "takeout:food:info:%d"
	// FoodDetailCacheKey 食品详情缓存键
	FoodDetailCacheKey = "takeout:food:detail:%d"
	// TakeoutFoodListCacheKey 食品列表缓存键（按商家和分类）
	TakeoutFoodListCacheKey = "takeout:food:list:merchant:%d:category:%d"
	// TakeoutFoodMerchantListCacheKey 商家食品列表缓存键
	TakeoutFoodMerchantListCacheKey = "takeout:food:merchant:%d:status:%d"
	// TakeoutFoodRecommendListCacheKey 推荐食品列表缓存键
	TakeoutFoodRecommendListCacheKey = "takeout:food:recommend:merchant:%d"
	// TakeoutFoodSearchCacheKey 搜索结果缓存键
	TakeoutFoodSearchCacheKey = "takeout:food:search:%s:merchant:%d"
	// TakeoutFoodStockCacheKey 食品库存缓存键
	TakeoutFoodStockCacheKey = "takeout:food:stock:%d"
	// FoodStockCacheKey 食品库存缓存键
	FoodStockCacheKey = "takeout:food:stock:%d"
	// TakeoutFoodPriceCacheKey 食品价格区间缓存键
	TakeoutFoodPriceCacheKey = "takeout:food:price:%d"
	// FoodPriceRangeCacheKey 食品价格区间缓存键
	FoodPriceRangeCacheKey = "takeout:food:price:%d"

	// 缓存过期时间
	TakeoutFoodCacheExpiration       = 30 * time.Minute // 食品详情缓存30分钟
	FoodDetailCacheExpiration        = 30 * time.Minute // 食品详情缓存30分钟
	TakeoutFoodListCacheExpiration   = 15 * time.Minute // 食品列表缓存15分钟
	FoodListCacheExpiration          = 15 * time.Minute // 食品列表缓存15分钟
	TakeoutFoodStockCacheExpiration  = 5 * time.Minute  // 库存缓存5分钟
	FoodStockCacheExpiration         = 5 * time.Minute  // 库存缓存5分钟
	TakeoutFoodSearchCacheExpiration = 10 * time.Minute // 搜索结果缓存10分钟
	SearchResultCacheExpiration      = 10 * time.Minute // 搜索结果缓存10分钟
	RecommendFoodCacheExpiration     = 15 * time.Minute // 推荐食品缓存15分钟
	FoodPriceRangeCacheExpiration    = 30 * time.Minute // 价格区间缓存30分钟
)

// TakeoutFoodCacheService 外卖食品缓存服务接口
type TakeoutFoodCacheService interface {
	// 单个食品缓存操作
	GetFood(foodID int64) (*models.TakeoutFood, error)
	SetFood(foodID int64, food *models.TakeoutFood) error
	GetMultipleFoods(foodIDs []int64) ([]*models.TakeoutFood, error)
	SetMultipleFoods(foods []*models.TakeoutFood) error
	DeleteFoodCache(foodID int64) error

	// 食品详情缓存
	GetFoodDetail(foodID int64) (*dto.TakeoutFoodDetailDTO, error)
	SetFoodDetail(foodID int64, detail *dto.TakeoutFoodDetailDTO) error

	// 食品列表缓存操作
	GetFoodList(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetFoodList(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error

	// 商家食品列表缓存
	GetMerchantFoodList(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetMerchantFoodList(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error

	// 商家分类食品列表缓存
	GetMerchantCategoryFoodList(merchantID, categoryID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetMerchantCategoryFoodList(merchantID, categoryID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error

	// 推荐食品缓存
	GetMerchantRecommendFoodList(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetMerchantRecommendFoodList(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error

	// 搜索结果缓存
	GetSearchFoodList(keyword string, merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetSearchFoodList(keyword string, merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error

	// 带规格的食品列表缓存
	GetMerchantFoodListWithVariants(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetMerchantFoodListWithVariants(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error
	GetMerchantCategoryFoodListWithVariants(merchantID, categoryID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetMerchantCategoryFoodListWithVariants(merchantID, categoryID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error
	GetSearchFoodListWithVariants(keyword string, merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error)
	SetSearchFoodListWithVariants(keyword string, merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error

	// 库存管理
	GetFoodStock(foodID int64) (int, error)
	SetFoodStock(foodID int64, stock int) error
	DecrementFoodStock(foodID int64, quantity int) (int, error)
	IncrementFoodStock(foodID int64, quantity int) (int, error)

	// 价格区间缓存
	GetFoodPriceRange(foodID int64) (float64, float64, error)
	SetFoodPriceRange(foodID int64, minPrice, maxPrice float64) error

	// 缓存清理
	ClearMerchantFoodCache(merchantID int64) error
	ClearCategoryFoodCache(categoryID int64) error
	ClearAllFoodCache() error
}

// takeoutFoodCacheService 外卖食品缓存服务实现
type takeoutFoodCacheService struct{}

// NewTakeoutFoodCacheService 创建外卖食品缓存服务实例
func NewTakeoutFoodCacheService() TakeoutFoodCacheService {
	return &takeoutFoodCacheService{}
}

// GetFood 从缓存获取单个食品信息
func (s *takeoutFoodCacheService) GetFood(foodID int64) (*models.TakeoutFood, error) {
	cacheKey := fmt.Sprintf(TakeoutFoodDetailCacheKey, foodID)
	cacheData, err := redis.Get(cacheKey)
	if err != nil {
		return nil, err
	}

	var food models.TakeoutFood
	err = json.Unmarshal([]byte(cacheData), &food)
	if err != nil {
		logs.Error("解析食品缓存数据失败: %v, 食品ID: %d", err, foodID)
		return nil, err
	}

	return &food, nil
}

// SetFood 将单个食品信息存入缓存
func (s *takeoutFoodCacheService) SetFood(foodID int64, food *models.TakeoutFood) error {
	cacheKey := fmt.Sprintf(FoodInfoCacheKey, foodID)
	cacheData, err := json.Marshal(food)
	if err != nil {
		logs.Error("序列化食品数据失败: %v, 食品ID: %d", err, foodID)
		return err
	}

	err = redis.Set(cacheKey, string(cacheData), TakeoutFoodCacheExpiration)
	if err != nil {
		logs.Error("存储食品缓存失败: %v, 食品ID: %d", err, foodID)
		return err
	}

	logs.Debug("食品缓存存储成功, 食品ID: %d", foodID)
	return nil
}

// GetMultipleFoods 从缓存批量获取食品信息
func (s *takeoutFoodCacheService) GetMultipleFoods(foodIDs []int64) ([]*models.TakeoutFood, error) {
	if len(foodIDs) == 0 {
		return []*models.TakeoutFood{}, nil
	}

	var foods []*models.TakeoutFood
	for _, foodID := range foodIDs {
		food, err := s.GetFood(foodID)
		if err != nil {
			// 如果某个食品缓存不存在，跳过该食品
			continue
		}
		foods = append(foods, food)
	}

	return foods, nil
}

// SetMultipleFoods 批量将食品信息存入缓存
func (s *takeoutFoodCacheService) SetMultipleFoods(foods []*models.TakeoutFood) error {
	for _, food := range foods {
		err := s.SetFood(food.ID, food)
		if err != nil {
			logs.Error("批量存储食品缓存失败: %v, 食品ID: %d", err, food.ID)
			// 继续处理其他食品，不中断整个批量操作
		}
	}
	return nil
}

// GetFoodList 获取食品列表缓存
func (s *takeoutFoodCacheService) GetFoodList(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	key := fmt.Sprintf("takeout:food:list:merchant:%d:page:%d:size:%d", merchantID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetFoodList 设置食品列表缓存
func (s *takeoutFoodCacheService) SetFoodList(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	key := fmt.Sprintf("takeout:food:list:merchant:%d:page:%d:size:%d", merchantID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), FoodListCacheExpiration)
}

// GetMerchantFoodList 获取商家食品列表缓存
func (s *takeoutFoodCacheService) GetMerchantFoodList(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	key := fmt.Sprintf("takeout:food:merchant:list:%d:page:%d:size:%d", merchantID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetMerchantFoodList 设置商家食品列表缓存
func (s *takeoutFoodCacheService) SetMerchantFoodList(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	key := fmt.Sprintf("takeout:food:merchant:list:%d:page:%d:size:%d", merchantID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), FoodListCacheExpiration)
}

// GetMerchantCategoryFoodList 获取商家分类食品列表缓存
func (s *takeoutFoodCacheService) GetMerchantCategoryFoodList(merchantID, categoryID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	key := fmt.Sprintf("takeout:food:merchant:%d:category:%d:page:%d:size:%d", merchantID, categoryID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetMerchantCategoryFoodList 设置商家分类食品列表缓存
func (s *takeoutFoodCacheService) SetMerchantCategoryFoodList(merchantID, categoryID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	key := fmt.Sprintf("takeout:food:merchant:%d:category:%d:page:%d:size:%d", merchantID, categoryID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), FoodListCacheExpiration)
}

// GetSearchFoodList 获取搜索食品列表缓存
func (s *takeoutFoodCacheService) GetSearchFoodList(keyword string, merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	// 对关键词进行处理，避免特殊字符影响缓存键
	safeKeyword := strings.ReplaceAll(keyword, ":", "_")
	safeKeyword = strings.ReplaceAll(safeKeyword, " ", "_")
	key := fmt.Sprintf("takeout:food:search:%s:merchant:%d:page:%d:size:%d", safeKeyword, merchantID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetSearchFoodList 设置搜索食品列表缓存
func (s *takeoutFoodCacheService) SetSearchFoodList(keyword string, merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	// 对关键词进行处理，避免特殊字符影响缓存键
	safeKeyword := strings.ReplaceAll(keyword, ":", "_")
	safeKeyword = strings.ReplaceAll(safeKeyword, " ", "_")
	key := fmt.Sprintf("takeout:food:search:%s:merchant:%d:page:%d:size:%d", safeKeyword, merchantID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), SearchResultCacheExpiration)
}

// GetMerchantFoodListWithVariants 获取商家食品列表(带规格)缓存
func (s *takeoutFoodCacheService) GetMerchantFoodListWithVariants(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	key := fmt.Sprintf("takeout:food:list_with_variants:merchant:%d:page:%d:size:%d", merchantID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetMerchantFoodListWithVariants 设置商家食品列表(带规格)缓存
func (s *takeoutFoodCacheService) SetMerchantFoodListWithVariants(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	key := fmt.Sprintf("takeout:food:list_with_variants:merchant:%d:page:%d:size:%d", merchantID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), FoodListCacheExpiration)
}

// GetMerchantCategoryFoodListWithVariants 获取商家分类食品列表(带规格)缓存
func (s *takeoutFoodCacheService) GetMerchantCategoryFoodListWithVariants(merchantID, categoryID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	key := fmt.Sprintf("takeout:food:list_with_variants:merchant:%d:category:%d:page:%d:size:%d", merchantID, categoryID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetMerchantCategoryFoodListWithVariants 设置商家分类食品列表(带规格)缓存
func (s *takeoutFoodCacheService) SetMerchantCategoryFoodListWithVariants(merchantID, categoryID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	key := fmt.Sprintf("takeout:food:list_with_variants:merchant:%d:category:%d:page:%d:size:%d", merchantID, categoryID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), FoodListCacheExpiration)
}

// GetSearchFoodListWithVariants 获取搜索食品列表(带规格)缓存
func (s *takeoutFoodCacheService) GetSearchFoodListWithVariants(keyword string, merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	key := fmt.Sprintf("takeout:food:search_with_variants:%s:merchant:%d:page:%d:size:%d", keyword, merchantID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetSearchFoodListWithVariants 设置搜索食品列表(带规格)缓存
func (s *takeoutFoodCacheService) SetSearchFoodListWithVariants(keyword string, merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	key := fmt.Sprintf("takeout:food:search_with_variants:%s:merchant:%d:page:%d:size:%d", keyword, merchantID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), SearchResultCacheExpiration)
}

// GetFoodDetail 获取食品详情缓存
func (s *takeoutFoodCacheService) GetFoodDetail(foodID int64) (*dto.TakeoutFoodDetailDTO, error) {
	key := fmt.Sprintf(FoodDetailCacheKey, foodID)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var detail dto.TakeoutFoodDetailDTO
	err = json.Unmarshal([]byte(cachedData), &detail)
	if err != nil {
		return nil, err
	}

	return &detail, nil
}

// SetFoodDetail 设置食品详情缓存
func (s *takeoutFoodCacheService) SetFoodDetail(foodID int64, detail *dto.TakeoutFoodDetailDTO) error {
	key := fmt.Sprintf(FoodDetailCacheKey, foodID)
	data, err := json.Marshal(detail)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), FoodDetailCacheExpiration)
}

// DeleteFoodCache 删除单个食品的所有相关缓存
func (s *takeoutFoodCacheService) DeleteFoodCache(foodID int64) error {
	// 删除食品详情缓存
	detailKey := fmt.Sprintf(FoodDetailCacheKey, foodID)
	_, err := redis.Del(detailKey)
	if err != nil {
		logs.Warn("删除食品详情缓存失败: %v, 食品ID: %d", err, foodID)
	}

	// 删除食品基本信息缓存
	infoKey := fmt.Sprintf(FoodInfoCacheKey, foodID)
	_, err = redis.Del(infoKey)
	if err != nil {
		logs.Warn("删除食品基本信息缓存失败: %v, 食品ID: %d", err, foodID)
	}

	// 删除食品库存缓存
	stockKey := fmt.Sprintf(FoodStockCacheKey, foodID)
	_, err = redis.Del(stockKey)
	if err != nil {
		logs.Warn("删除食品库存缓存失败: %v, 食品ID: %d", err, foodID)
	}

	// 删除食品价格区间缓存
	priceKey := fmt.Sprintf(FoodPriceRangeCacheKey, foodID)
	_, err = redis.Del(priceKey)
	if err != nil {
		logs.Warn("删除食品价格区间缓存失败: %v, 食品ID: %d", err, foodID)
	}

	logs.Debug("删除食品缓存完成, 食品ID: %d", foodID)
	return nil
}

// GetFoodStock 从缓存获取食品库存
func (s *takeoutFoodCacheService) GetFoodStock(foodID int64) (int, error) {
	cacheKey := fmt.Sprintf(FoodStockCacheKey, foodID)
	cacheData, err := redis.Get(cacheKey)
	if err != nil {
		return 0, err
	}

	stock, err := strconv.Atoi(cacheData)
	if err != nil {
		logs.Error("解析食品库存缓存数据失败: %v, 食品ID: %d", err, foodID)
		return 0, err
	}

	return stock, nil
}

// SetFoodStock 将食品库存存入缓存
func (s *takeoutFoodCacheService) SetFoodStock(foodID int64, stock int) error {
	cacheKey := fmt.Sprintf(FoodStockCacheKey, foodID)
	err := redis.Set(cacheKey, strconv.Itoa(stock), FoodStockCacheExpiration)
	if err != nil {
		logs.Error("存储食品库存缓存失败: %v, 食品ID: %d, 库存: %d", err, foodID, stock)
		return err
	}

	logs.Debug("食品库存缓存存储成功, 食品ID: %d, 库存: %d", foodID, stock)
	return nil
}

// IncrementFoodStock 增加食品库存（原子操作）
func (s *takeoutFoodCacheService) IncrementFoodStock(foodID int64, quantity int) (int, error) {
	cacheKey := fmt.Sprintf(FoodStockCacheKey, foodID)

	// 使用Redis工具类的INCRBY命令进行原子加法操作
	result, err := redis.IncrBy(cacheKey, int64(quantity))
	if err != nil {
		logs.Error("增加食品库存失败: %v, 食品ID: %d, 增加数量: %d", err, foodID, quantity)
		return 0, err
	}

	logs.Debug("食品库存增加成功, 食品ID: %d, 增加数量: %d, 当前库存: %d", foodID, quantity, result)
	return int(result), nil
}

// DecrementFoodStock 减少食品库存（原子操作）
func (s *takeoutFoodCacheService) DecrementFoodStock(foodID int64, quantity int) (int, error) {
	cacheKey := fmt.Sprintf(FoodStockCacheKey, foodID)

	// 使用Redis工具类的DECRBY命令进行原子减法操作
	result, err := redis.DecrBy(cacheKey, int64(quantity))
	if err != nil {
		logs.Error("减少食品库存失败: %v, 食品ID: %d, 减少数量: %d", err, foodID, quantity)
		return 0, err
	}

	// 如果库存变为负数，需要回滚
	if result < 0 {
		// 回滚操作
		redis.IncrBy(cacheKey, int64(quantity))
		return 0, fmt.Errorf("库存不足，当前库存: %d", result+int64(quantity))
	}

	logs.Debug("食品库存减少成功, 食品ID: %d, 减少数量: %d, 剩余库存: %d", foodID, quantity, result)
	return int(result), nil
}

// GetFoodPriceRange 从缓存获取食品价格区间
func (s *takeoutFoodCacheService) GetFoodPriceRange(foodID int64) (float64, float64, error) {
	cacheKey := fmt.Sprintf(FoodPriceRangeCacheKey, foodID)
	cacheData, err := redis.Get(cacheKey)
	if err != nil {
		return 0, 0, err
	}

	var priceRange struct {
		MinPrice float64 `json:"min_price"`
		MaxPrice float64 `json:"max_price"`
	}
	err = json.Unmarshal([]byte(cacheData), &priceRange)
	if err != nil {
		logs.Error("解析食品价格区间缓存数据失败: %v, 食品ID: %d", err, foodID)
		return 0, 0, err
	}

	return priceRange.MinPrice, priceRange.MaxPrice, nil
}

// SetFoodPriceRange 将食品价格区间存入缓存
func (s *takeoutFoodCacheService) SetFoodPriceRange(foodID int64, minPrice, maxPrice float64) error {
	cacheKey := fmt.Sprintf(FoodPriceRangeCacheKey, foodID)
	priceRange := struct {
		MinPrice float64 `json:"min_price"`
		MaxPrice float64 `json:"max_price"`
	}{
		MinPrice: minPrice,
		MaxPrice: maxPrice,
	}

	cacheData, err := json.Marshal(priceRange)
	if err != nil {
		logs.Error("序列化食品价格区间数据失败: %v, 食品ID: %d", err, foodID)
		return err
	}

	err = redis.Set(cacheKey, string(cacheData), FoodPriceRangeCacheExpiration)
	if err != nil {
		logs.Error("存储食品价格区间缓存失败: %v, 食品ID: %d", err, foodID)
		return err
	}

	logs.Debug("食品价格区间缓存存储成功, 食品ID: %d, 价格区间: %.2f-%.2f", foodID, minPrice, maxPrice)
	return nil
}

// GetMerchantRecommendFoodList 获取商家推荐食品列表缓存
func (s *takeoutFoodCacheService) GetMerchantRecommendFoodList(merchantID int64, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	// 获取推荐食品列表缓存
	key := fmt.Sprintf("takeout:food:recommend:merchant:%d:page:%d:size:%d", merchantID, page, pageSize)
	cachedData, err := redis.Get(key)
	if err != nil {
		return nil, err
	}

	var foodList dto.TakeoutFoodResponseList
	err = json.Unmarshal([]byte(cachedData), &foodList)
	if err != nil {
		return nil, err
	}

	return &foodList, nil
}

// SetMerchantRecommendFoodList 设置商家推荐食品列表缓存
func (s *takeoutFoodCacheService) SetMerchantRecommendFoodList(merchantID int64, page, pageSize int, foodList *dto.TakeoutFoodResponseList) error {
	key := fmt.Sprintf("takeout:food:recommend:merchant:%d:page:%d:size:%d", merchantID, page, pageSize)
	data, err := json.Marshal(foodList)
	if err != nil {
		return err
	}

	return redis.Set(key, string(data), RecommendFoodCacheExpiration)
}

// ClearMerchantFoodCache 清理商家的所有食品相关缓存
func (s *takeoutFoodCacheService) ClearMerchantFoodCache(merchantID int64) error {
	// 构建模式匹配键
	patterns := []string{
		// 商家食品列表缓存键
		fmt.Sprintf("takeout:food:merchant:list:%d:*", merchantID),
		// 商家分类食品列表缓存键
		fmt.Sprintf("takeout:food:merchant:%d:category:*", merchantID),
		// 推荐食品列表缓存键
		fmt.Sprintf("takeout:food:recommend:merchant:%d:*", merchantID),
		// 搜索结果缓存键
		fmt.Sprintf("takeout:food:search:*:merchant:%d:*", merchantID),
		// 带规格的食品列表缓存键
		fmt.Sprintf("takeout:food:list_with_variants:merchant:%d:*", merchantID),
		fmt.Sprintf("takeout:food:search_with_variants:*:merchant:%d:*", merchantID),
		// 兼容旧格式的缓存键
		fmt.Sprintf("takeout:food:list:merchant:%d:*", merchantID),
		fmt.Sprintf("takeout:food:merchant:%d:status:*", merchantID),
	}

	client := redis.GetClient()
	ctx := context.Background()
	for _, pattern := range patterns {
		keys, err := client.Keys(ctx, pattern).Result()
		if err != nil {
			logs.Error("获取缓存键失败: %v, 模式: %s", err, pattern)
			continue
		}

		if len(keys) > 0 {
			_, err = client.Del(ctx, keys...).Result()
			if err != nil {
				logs.Error("删除缓存键失败: %v, 键数量: %d", err, len(keys))
			} else {
				logs.Debug("删除商家缓存键成功, 商家ID: %d, 键数量: %d", merchantID, len(keys))
			}
		}
	}

	logs.Debug("商家食品相关缓存清理完成, 商家ID: %d", merchantID)
	return nil
}

// ClearCategoryFoodCache 清理分类的所有食品相关缓存
func (s *takeoutFoodCacheService) ClearCategoryFoodCache(categoryID int64) error {
	// 构建模式匹配键
	patterns := []string{
		fmt.Sprintf("takeout:food:list:merchant:*:category:%d", categoryID),
		fmt.Sprintf("takeout:food:merchant:*:category:%d:*", categoryID),                    // 修复：添加正确的缓存键格式
		fmt.Sprintf("takeout:food:list_with_variants:merchant:*:category:%d:*", categoryID), // 添加带规格的缓存键
	}

	client := redis.GetClient()
	ctx := context.Background()
	totalDeleted := 0
	for _, pattern := range patterns {
		keys, err := client.Keys(ctx, pattern).Result()
		if err != nil {
			logs.Error("获取分类缓存键失败: %v, 分类ID: %d, 模式: %s", err, categoryID, pattern)
			continue
		}

		if len(keys) > 0 {
			_, err = client.Del(ctx, keys...).Result()
			if err != nil {
				logs.Error("删除分类缓存键失败: %v, 分类ID: %d, 键数量: %d", err, categoryID, len(keys))
			} else {
				totalDeleted += len(keys)
				logs.Debug("删除分类缓存键成功, 分类ID: %d, 模式: %s, 键数量: %d", categoryID, pattern, len(keys))
			}
		}
	}

	logs.Debug("分类食品相关缓存清理完成, 分类ID: %d, 总删除键数: %d", categoryID, totalDeleted)
	return nil
}

// ClearAllFoodCache 清理所有食品相关缓存
func (s *takeoutFoodCacheService) ClearAllFoodCache() error {
	// 构建模式匹配键
	patterns := []string{
		"takeout:food:*",
	}

	client := redis.GetClient()
	ctx := context.Background()
	for _, pattern := range patterns {
		keys, err := client.Keys(ctx, pattern).Result()
		if err != nil {
			logs.Error("获取所有食品缓存键失败: %v, 模式: %s", err, pattern)
			continue
		}

		if len(keys) > 0 {
			_, err = client.Del(ctx, keys...).Result()
			if err != nil {
				logs.Error("删除所有食品缓存键失败: %v, 键数量: %d", err, len(keys))
			} else {
				logs.Debug("删除所有食品缓存键成功, 键数量: %d", len(keys))
			}
		}
	}

	logs.Info("所有食品相关缓存清理完成")
	return nil
}
