/*
 * 外卖食品缓存验证服务
 *
 * 本文件实现了外卖食品缓存的验证机制，解决缓存数据不一致的问题。
 * 提供缓存数据验证、智能缓存策略等功能，确保数据的准确性和一致性。
 */

package services

import (
	"strings"
	"time"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// TakeoutFoodCacheValidationService 外卖食品缓存验证服务接口
type TakeoutFoodCacheValidationService interface {
	// ValidateAndGetFoodList 验证并获取食品列表（带缓存验证）
	ValidateAndGetFoodList(query *dto.TakeoutFoodQueryRequest) (*dto.TakeoutFoodResponseList, error)
	// ValidateCacheConsistency 验证缓存一致性
	ValidateCacheConsistency(merchantID int64) error
	// GetFoodListWithValidation 获取食品列表（带验证机制）
	GetFoodListWithValidation(query *dto.TakeoutFoodQueryRequest, useCache bool) (*dto.TakeoutFoodResponseList, error)
}

// takeoutFoodCacheValidationService 外卖食品缓存验证服务实现
type takeoutFoodCacheValidationService struct {
	foodRepo     repositories.TakeoutFoodRepository
	categoryRepo repositories.TakeoutCategoryRepository
	cacheService TakeoutFoodCacheService
}

// NewTakeoutFoodCacheValidationService 创建外卖食品缓存验证服务实例
func NewTakeoutFoodCacheValidationService(
	foodRepo repositories.TakeoutFoodRepository,
	categoryRepo repositories.TakeoutCategoryRepository,
	cacheService TakeoutFoodCacheService,
) TakeoutFoodCacheValidationService {
	return &takeoutFoodCacheValidationService{
		foodRepo:     foodRepo,
		categoryRepo: categoryRepo,
		cacheService: cacheService,
	}
}

// ValidateAndGetFoodList 验证并获取食品列表（带缓存验证）
func (s *takeoutFoodCacheValidationService) ValidateAndGetFoodList(query *dto.TakeoutFoodQueryRequest) (*dto.TakeoutFoodResponseList, error) {
	// 添加调试日志
	logs.Info("[DEBUG] ValidateAndGetFoodList called with query: MerchantID=%d, CategoryID=%d, Status=%d, Page=%d, PageSize=%d",
		query.MerchantID, query.CategoryID, query.Status, query.Page, query.PageSize)

	// 特殊处理：当Status=-1（表示不过滤状态）时，跳过缓存直接查询数据库
	// 这是因为缓存通常只保存特定状态的数据，而Status=-1需要返回所有状态的商品
	if query.Status == -1 {
		logs.Info("[DEBUG] Status=-1（不过滤状态），跳过缓存直接查询数据库")
		return s.GetFoodListWithValidation(query, true)
	}

	// 策略1：混合模式 - 先尝试缓存，然后验证关键数据
	if query.MerchantID > 0 {
		var cachedList *dto.TakeoutFoodResponseList
		var err error

		// 尝试从缓存获取
		if query.CategoryID > 0 {
			cachedList, err = s.cacheService.GetMerchantCategoryFoodList(query.MerchantID, query.CategoryID, query.Page, query.PageSize)
			logs.Info("[DEBUG] Trying to get category food list from cache: MerchantID=%d, CategoryID=%d", query.MerchantID, query.CategoryID)
		} else if query.IsRecommend {
			cachedList, err = s.cacheService.GetMerchantRecommendFoodList(query.MerchantID, query.Page, query.PageSize)
			logs.Info("[DEBUG] Trying to get recommend food list from cache: MerchantID=%d", query.MerchantID)
		} else if query.Keyword != "" {
			cachedList, err = s.cacheService.GetSearchFoodList(query.Keyword, query.MerchantID, query.Page, query.PageSize)
			logs.Info("[DEBUG] Trying to get search food list from cache: MerchantID=%d, Keyword=%s", query.MerchantID, query.Keyword)
		} else {
			cachedList, err = s.cacheService.GetMerchantFoodList(query.MerchantID, query.Page, query.PageSize)
			logs.Info("[DEBUG] Trying to get merchant food list from cache: MerchantID=%d", query.MerchantID)
		}

		// 如果缓存存在，进行快速验证
		if err == nil && cachedList != nil && len(cachedList.List) > 0 {
			logs.Info("[DEBUG] Cache hit, found %d items, starting validation", len(cachedList.List))
			// 验证缓存数据的有效性
			if s.isValidCachedData(cachedList, query) {
				logs.Info("[DEBUG] 缓存数据验证通过，返回缓存结果, 商家ID: %d", query.MerchantID)
				return cachedList, nil
			} else {
				logs.Warn("[DEBUG] 缓存数据验证失败，清理缓存并查询数据库, 商家ID: %d", query.MerchantID)
				// 清理可能过期的缓存
				go s.cacheService.ClearMerchantFoodCache(query.MerchantID)
			}
		} else {
			if err != nil {
				logs.Warn("[DEBUG] Cache get error: %v", err)
			} else {
				logs.Info("[DEBUG] Cache miss or empty result")
			}
		}
	}

	// 缓存未命中或验证失败，查询数据库
	logs.Info("[DEBUG] Querying database for MerchantID=%d", query.MerchantID)
	return s.GetFoodListWithValidation(query, true)
}

// isValidCachedData 验证缓存数据的有效性
func (s *takeoutFoodCacheValidationService) isValidCachedData(cachedList *dto.TakeoutFoodResponseList, query *dto.TakeoutFoodQueryRequest) bool {
	if cachedList == nil || len(cachedList.List) == 0 {
		logs.Warn("[DEBUG] Cache validation failed: empty cached list")
		return false
	}

	logs.Info("[DEBUG] Starting cache validation for %d items", len(cachedList.List))

	// 策略1：随机抽样验证（验证前几个商品是否仍然存在且状态正确）
	sampleSize := 3
	if len(cachedList.List) < sampleSize {
		sampleSize = len(cachedList.List)
	}

	logs.Info("[DEBUG] Validating %d sample items", sampleSize)

	for i := 0; i < sampleSize; i++ {
		foodItem := cachedList.List[i]
		logs.Info("[DEBUG] Validating item %d: ID=%d, Name=%s, Status=%d, SoldOut=%t",
			i+1, foodItem.ID, foodItem.Name, foodItem.Status, foodItem.SoldOut)

		// 检查食品是否仍然存在且状态正确
		food, err := s.foodRepo.GetByID(foodItem.ID)
		if err != nil || food == nil {
			logs.Warn("[DEBUG] 缓存验证失败：食品不存在, ID: %d, error: %v", foodItem.ID, err)
			return false
		}

		logs.Info("[DEBUG] Database item: ID=%d, Status=%d, SoldOut=%t, MerchantID=%d, CategoryID=%d",
			food.ID, food.Status, food.SoldOut, food.MerchantID, food.CategoryID)

		// 检查关键状态是否一致
		if food.Status != foodItem.Status || food.SoldOut != foodItem.SoldOut {
			logs.Warn("[DEBUG] 缓存验证失败：食品状态不一致, ID: %d, 缓存状态: %d, 数据库状态: %d, 缓存售罄: %t, 数据库售罄: %t",
				foodItem.ID, foodItem.Status, food.Status, foodItem.SoldOut, food.SoldOut)
			return false
		}

		// 检查商家ID是否匹配
		if query.MerchantID > 0 && food.MerchantID != query.MerchantID {
			logs.Warn("[DEBUG] 缓存验证失败：商家ID不匹配, 食品ID: %d, 查询商家ID: %d, 实际商家ID: %d",
				foodItem.ID, query.MerchantID, food.MerchantID)
			return false
		}

		// 检查分类ID是否匹配（如果有分类查询条件）
		if query.CategoryID > 0 && food.CategoryID != query.CategoryID {
			logs.Warn("[DEBUG] 缓存验证失败：分类ID不匹配, 食品ID: %d, 查询分类ID: %d, 实际分类ID: %d",
				foodItem.ID, query.CategoryID, food.CategoryID)
			return false
		}

		logs.Info("[DEBUG] Item %d validation passed", i+1)
	}

	logs.Info("[DEBUG] All sample items validation passed")
	return true
}

// GetFoodListWithValidation 获取食品列表（带验证机制）
func (s *takeoutFoodCacheValidationService) GetFoodListWithValidation(query *dto.TakeoutFoodQueryRequest, updateCache bool) (*dto.TakeoutFoodResponseList, error) {
	logs.Info("[DEBUG] GetFoodListWithValidation called: MerchantID=%d, updateCache=%t", query.MerchantID, updateCache)

	// 查询食品列表
	foods, total, err := s.foodRepo.List(query)
	if err != nil {
		logs.Error("查询外卖食品列表失败: %v, 查询条件: %+v", err, query)
		return nil, err
	}

	logs.Info("[DEBUG] Database query result: found %d foods, total=%d", len(foods), total)
	for i, food := range foods {
		logs.Info("[DEBUG] Food %d: ID=%d, Name=%s, Status=%d, MerchantID=%d", i+1, food.ID, food.Name, food.Status, food.MerchantID)
	}

	// 获取分类信息
	categoryMap := make(map[int64]string)
	for _, food := range foods {
		if _, exists := categoryMap[food.CategoryID]; !exists && food.CategoryID > 0 {
			category, err := s.categoryRepo.GetByID(food.CategoryID)
			if err != nil {
				logs.Warn("获取分类信息失败: %v, 分类ID: %d", err, food.CategoryID)
				categoryMap[food.CategoryID] = ""
			} else if category != nil {
				categoryMap[food.CategoryID] = category.Name
			}
		}
	}

	// 构造响应DTO
	foodDTOs := make([]dto.TakeoutFoodListItemDTO, 0, len(foods))
	for _, food := range foods {
		foodDTO := dto.TakeoutFoodListItemDTO{
			ID:              food.ID,
			Name:            food.Name,
			Brief:           food.Brief,
			Image:           food.Image,
			Price:           food.Price,
			OriginalPrice:   food.OriginalPrice,
			PackagingFee:    food.PackagingFee,
			PreparationTime: food.PreparationTime,
			IsSpicy:         food.IsSpicy,
			IsCombination:   food.IsCombination,
			HasVariants:     food.HasVariants,
			SoldOut:         food.SoldOut,
			TotalSold:       food.TotalSold,
			DailyLimit:      food.DailyLimit,
			CategoryID:      food.CategoryID,
			CategoryName:    categoryMap[food.CategoryID],
			Status:          food.Status,
			AuditStatus:     food.AuditStatus,
			IsRecommend:     food.IsRecommend,
			CreatedAt:       food.CreatedAt,
		}

		// 转换标签
		if food.Tags != "" {
			foodDTO.Tags = strings.Split(food.Tags, ",")
		}

		// 设置价格区间
		foodDTO.MinPrice = food.Price
		foodDTO.MaxPrice = food.Price

		foodDTOs = append(foodDTOs, foodDTO)
	}

	// 构造响应结果
	response := &dto.TakeoutFoodResponseList{
		Total: int(total),
		List:  foodDTOs,
	}

	// 异步更新缓存（如果需要）
	if updateCache && query.MerchantID > 0 {
		go func() {
			time.Sleep(100 * time.Millisecond) // 短暂延迟确保数据库事务完成
			if query.CategoryID > 0 {
				err := s.cacheService.SetMerchantCategoryFoodList(query.MerchantID, query.CategoryID, query.Page, query.PageSize, response)
				if err != nil {
					logs.Warn("设置商家分类食品列表缓存失败: %v, 商家ID: %d, 分类ID: %d", err, query.MerchantID, query.CategoryID)
				}
			} else if query.IsRecommend {
				err := s.cacheService.SetMerchantRecommendFoodList(query.MerchantID, query.Page, query.PageSize, response)
				if err != nil {
					logs.Warn("设置商家推荐食品列表缓存失败: %v, 商家ID: %d", err, query.MerchantID)
				}
			} else if query.Keyword != "" {
				err := s.cacheService.SetSearchFoodList(query.Keyword, query.MerchantID, query.Page, query.PageSize, response)
				if err != nil {
					logs.Warn("设置搜索食品列表缓存失败: %v, 商家ID: %d, 关键词: %s", err, query.MerchantID, query.Keyword)
				}
			} else {
				err := s.cacheService.SetMerchantFoodList(query.MerchantID, query.Page, query.PageSize, response)
				if err != nil {
					logs.Warn("设置商家食品列表缓存失败: %v, 商家ID: %d", err, query.MerchantID)
				}
			}
			logs.Debug("缓存更新完成, 商家ID: %d", query.MerchantID)
		}()
	}

	return response, nil
}

// ValidateCacheConsistency 验证缓存一致性
func (s *takeoutFoodCacheValidationService) ValidateCacheConsistency(merchantID int64) error {
	// 这个方法可以用于定期验证和清理不一致的缓存
	// 可以在后台任务中调用
	logs.Info("开始验证商家缓存一致性, 商家ID: %d", merchantID)

	// 清理该商家的所有缓存，强制下次查询时重新生成
	err := s.cacheService.ClearMerchantFoodCache(merchantID)
	if err != nil {
		logs.Error("清理商家缓存失败: %v, 商家ID: %d", err, merchantID)
		return err
	}

	logs.Info("商家缓存一致性验证完成, 商家ID: %d", merchantID)
	return nil
}
