/**
 * 外卖订单评价服务
 *
 * 本文件实现了外卖订单的评价相关的业务逻辑层，处理订单评价、查询评价等操作。
 * 将其从主订单服务分离，以保持代码的清晰性和可维护性。
 */

package services

import (
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/takeout/dto"
	takeoutModels "o_mall_backend/modules/takeout/models"
)

// RateOrder 对订单进行评价
func (s *takeoutOrderService) RateOrder(orderID int64, userID int64, req *dto.RateTakeoutOrderRequest) error {
	// 1. 验证订单是否存在并属于当前用户
	order, err := s.GetOrderByID(orderID)
	if err != nil {
		logs.Error("获取订单失败: %v, 订单ID: %d", err, orderID)
		return errors.New("订单不存在或已被删除")
	}
	
	// 验证订单是否属于当前用户
	if order.UserID != userID {
		logs.Warn("用户尝试评价非本人订单: 用户ID: %d, 订单ID: %d", userID, orderID)
		return errors.New("无法评价非本人订单")
	}
	
	// 2. 验证订单状态是否可评价
	if order.OrderStatus != 3 { // 假设3表示已完成状态
		return errors.New("只能评价已完成的订单")
	}
	
	// 3. 检查订单是否已评价
	o := orm.NewOrm()
	
	// 获取订单扩展信息
	var orderExt takeoutModels.TakeoutOrderExtension
	err = o.QueryTable(new(takeoutModels.TakeoutOrderExtension)).Filter("order_id", orderID).One(&orderExt)
	if err != nil {
		logs.Error("获取订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("订单信息异常")
	}
	
	// 检查是否已评价
	if orderExt.IsRated {
		return errors.New("订单已评价，不能重复评价")
	}
	
	// 评分校验 (1-5分)
	if req.Rating < 1 || req.Rating > 5 || req.DeliveryRating < 1 || req.DeliveryRating > 5 {
		return errors.New("评分必须在1-5分之间")
	}
	
	// 开始事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return errors.New("系统繁忙，请稍后重试")
	}
	
	// 4. 创建订单评价记录
	rating := &takeoutModels.TakeoutOrderRating{
		OrderID:        orderID,
		UserID:         userID,
		Rating:         req.Rating,
		DeliveryRating: req.DeliveryRating,
		Comment:        req.Comment,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	
	// 保存评价记录
	_, err = tx.Insert(rating)
	if err != nil {
		tx.Rollback()
		logs.Error("保存订单评价失败: %v, 订单ID: %d", err, orderID)
		return errors.New("保存评价信息失败")
	}
	
	// 5. 更新订单扩展信息评价状态
	orderExt.IsRated = true
	_, err = tx.Update(&orderExt, "is_rated")
	if err != nil {
		tx.Rollback()
		logs.Error("更新订单评价状态失败: %v, 订单ID: %d", err, orderID)
		return errors.New("更新订单状态失败")
	}
	
	// 6. 更新食品的评分信息（如果有foodID信息）
	// 获取订单中的食品项
	var orderItems []*takeoutModels.TakeoutOrderItem
	_, err = tx.QueryTable(new(takeoutModels.TakeoutOrderItem)).Filter("order_id", orderID).All(&orderItems)
	if err == nil && len(orderItems) > 0 {
		// 更新每个食品的评分
		for _, item := range orderItems {
			err = s.updateFoodRating(tx, item.FoodID, req.Rating)
			if err != nil {
				logs.Warn("更新食品评分失败: %v, 食品ID: %d", err, item.FoodID)
				// 继续处理，不影响整体事务
			}
		}
	}
	
	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("系统繁忙，请稍后重试")
	}
	
	// 记录日志
	logs.Info("用户成功评价订单: 用户ID: %d, 订单ID: %d, 评分: %d", userID, orderID, req.Rating)
	return nil
}

// updateFoodRating 更新食品评分
func (s *takeoutOrderService) updateFoodRating(o orm.TxOrmer, foodID int64, rating int) error {
	// 获取食品信息
	var food takeoutModels.TakeoutFood
	err := o.QueryTable(new(takeoutModels.TakeoutFood)).Filter("id", foodID).One(&food)
	if err != nil {
		return fmt.Errorf("食品不存在: %v", err)
	}
	
	// 更新评分总和和次数
	food.RatingTotal += rating
	food.RatingCount += 1
	
	// 计算平均评分（保留一位小数）
	if food.RatingCount > 0 {
		food.Rating = float64(food.RatingTotal) / float64(food.RatingCount)
	}
	
	// 更新食品信息
	_, err = o.Update(&food, "rating_total", "rating_count", "rating")
	if err != nil {
		return fmt.Errorf("更新食品评分失败: %v", err)
	}
	
	return nil
}
