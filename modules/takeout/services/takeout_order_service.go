/**
 * 外卖订单服务
 *
 * 本文件实现了外卖订单的业务逻辑层，处理订单创建、状态更新、查询等操作。
 * 与现有订单系统集成，支持外卖特有的配送信息和状态流转。
 */

package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	merchantDTO "o_mall_backend/modules/merchant/dto"
	merchantServices "o_mall_backend/modules/merchant/services"
	baseDTO "o_mall_backend/modules/order/dto"
	orderModels "o_mall_backend/modules/order/models"
	orderRepositories "o_mall_backend/modules/order/repositories"
	orderServices "o_mall_backend/modules/order/services"
	paymentDTO "o_mall_backend/modules/payment/dto"
	paymentRepositories "o_mall_backend/modules/payment/repositories"
	paymentServices "o_mall_backend/modules/payment/services"
	runnerDTO "o_mall_backend/modules/runner/dto"
	runnerSvc "o_mall_backend/modules/runner/services"
	runnerImpl "o_mall_backend/modules/runner/services/impl"
	"o_mall_backend/modules/takeout/dto"
	takeoutModels "o_mall_backend/modules/takeout/models" // 使用别名避免与其他包冲突
	"o_mall_backend/modules/takeout/repositories"
	userDTO "o_mall_backend/modules/user/dto"           // 引入用户DTO包
	userServices "o_mall_backend/modules/user/services" // 引入用户服务包
	"o_mall_backend/utils/geo"
)

// TakeoutOrderService 外卖订单服务接口
type TakeoutOrderService interface {
	// 订单创建与支付
	CreateOrderFromCart(userID int64, req *dto.CreateTakeoutOrderRequestLegacy) (*dto.TakeoutOrderDTO, error)
	GetOrderPaymentInfo(orderID int64) (*dto.TakeoutOrderPaymentDTO, error)

	// 支付相关方法
	CreateOrderPayment(userID int64, req *dto.CreateTakeoutPaymentRequest) (*dto.TakeoutPaymentResponse, error)
	CreateBatchOrderPayment(userID int64, req *dto.CreateBatchTakeoutPaymentRequest) (*dto.BatchTakeoutPaymentResponse, error)
	QueryOrderPaymentStatus(userID int64, orderID int64) (*dto.TakeoutPaymentStatusResponse, error)
	CloseOrderPayment(userID int64, req *dto.CloseTakeoutPaymentRequest) (bool, error)

	// 退款相关方法
	ApplyRefund(userID int64, req *dto.ApplyRefundRequest) (*dto.RefundResponse, error)
	MerchantProcessRefund(merchantID int64, req *dto.MerchantProcessRefundRequest) (*dto.MerchantProcessRefundResponse, error)

	// 订单查询
	GetOrderByID(orderID int64) (*dto.TakeoutOrderDTO, error)
	ListOrdersByUserID(userID int64, status int, page, pageSize int) (*dto.TakeoutOrderListDTO, error)
	CountOrdersByUserID(userID int64, status int) (int, error)

	// 订单状态管理
	CancelOrder(orderID int64, userID int64, reason string) error
	MerchantCancelOrder(orderID int64, merchantID int64, reason string) error
	AcceptOrder(orderID int64, operatorID int64) error
	AssignDelivery(orderID int64, deliveryStaffID int64, operatorID int64) error
	StartDelivery(orderID int64, deliveryStaffID int64) error
	CompleteDelivery(orderID int64, deliveryStaffID int64) error
	CompleteOrder(orderID int64) error

	// 订单评价
	RateOrder(orderID int64, userID int64, req *dto.RateTakeoutOrderRequest) error

	// 订单统计
	GetOrderStatistics(startDate, endDate time.Time) (*dto.TakeoutOrderStatisticsDTO, error)
	GetOrderStatisticsByMerchantID(merchantID int64) (totalCount, completedCount, processingCount, cancelledCount int, err error)

	// 管理员订单管理
	GetOrdersPageForAdmin(merchantID int64, orderNumber string, status int, startTime, endTime string, page, pageSize int) ([]dto.AdminOrderListItemDTO, int64, error)
	GetOrderDetailForAdmin(orderID int64) (*dto.AdminOrderDetailDTO, error)
	UpdateOrderForAdmin(req *dto.UpdateOrderRequest) error
}

// takeoutOrderService 外卖订单服务实现
type takeoutOrderService struct {
	orderRepo       repositories.TakeoutOrderRepository
	foodRepo        repositories.TakeoutFoodRepository
	variantRepo     repositories.TakeoutVariantRepository
	comboRepo       repositories.TakeoutComboRepository
	refundRepo      repositories.TakeoutRefundRepository // 添加退款仓储依赖
	cartService     TakeoutCartService
	baseOrderSvc    orderServices.OrderService
	baseOrderRepo   orderRepositories.OrderRepository // 添加基础订单仓储依赖
	comboSvc        TakeoutComboService
	addrService     userServices.AddressService           // 添加地址服务依赖
	userService     userServices.UserService              // 添加用户服务依赖
	refundSvc       paymentServices.RefundService         // 添加退款服务依赖
	paymentRepo     paymentRepositories.PaymentRepository // 添加支付仓储依赖
	runnerService   runnerSvc.RunnerService               // 添加跑腿员服务依赖
	merchantService merchantServices.MerchantService      // 添加商家服务依赖
}

// NewTakeoutOrderService 创建外卖订单服务实例
func NewTakeoutOrderService() TakeoutOrderService {
	return &takeoutOrderService{
		orderRepo:       repositories.NewTakeoutOrderRepository(),
		foodRepo:        repositories.NewTakeoutFoodRepository(),
		variantRepo:     repositories.NewTakeoutVariantRepository(),
		comboRepo:       repositories.NewTakeoutComboRepository(),
		refundRepo:      repositories.NewTakeoutRefundRepository(), // 初始化退款仓储
		cartService:     NewTakeoutCartService(),
		baseOrderSvc:    orderServices.NewOrderService(),
		baseOrderRepo:   orderRepositories.NewOrderRepository(), // 初始化基础订单仓储
		comboSvc:        NewTakeoutComboService(),
		addrService:     userServices.NewAddressService(),           // 初始化地址服务
		userService:     userServices.NewUserService(nil),           // 初始化用户服务
		refundSvc:       paymentServices.NewRefundService(),         // 初始化退款服务
		paymentRepo:     paymentRepositories.NewPaymentRepository(), // 初始化支付仓储
		runnerService:   runnerImpl.NewRunnerService(),              // 初始化跑腿员服务
		merchantService: merchantServices.NewMerchantService(),      // 初始化商家服务
	}
}

// CreateOrderFromCart 从购物车创建订单
func (s *takeoutOrderService) CreateOrderFromCart(userID int64, req *dto.CreateTakeoutOrderRequestLegacy) (*dto.TakeoutOrderDTO, error) {
	// 检查购物车项是否存在
	if len(req.CartItemIDs) == 0 {
		return nil, errors.New("购物车为空")
	}

	// 获取购物车汇总信息
	cartSummary, err := s.cartService.GetCartSummary(userID, req.CartItemIDs)
	if err != nil {
		return nil, err
	}

	if len(cartSummary.Items) == 0 {
		return nil, errors.New("购物车不包含有效商品")
	}

	// 优化事务处理
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		logs.Error("开始事务失败: %v", err)
		return nil, err
	}

	// 定义延迟函数，确保在发生错误时回滚事务
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logs.Error("订单创建过程中发生异常: %v", r)
		}
	}()

	// 我们需要配合开发其他服务的订单统一接口
	// 由于 OrderItemRequest 类型在当前上下文中没有定义，我们先定义一个临时的类型
	type OrderItemTemp struct {
		ProductID       int64
		ProductName     string
		ProductImage    string
		ProductType     string
		SkuID           int64
		SkuName         string
		Quantity        int
		Price           float64
		OriginalPrice   float64
		SubtotalAmount  float64
		Remark          string
		MerchantID      int64
		MerchantName    string
		PackagingFee    float64
		ProductSnapshot string
		VariantSnapshot string
		ComboSelections string
	}

	// 处理购物车项目，转换为订单项
	orderItems := make([]OrderItemTemp, 0, len(cartSummary.Items))
	for _, item := range cartSummary.Items {
		// 计算加料费用
		var addonAmount float64
		var packageFee float64 = 0

		// 获取外卖商品详情，用于冻结帧
		food, err := s.foodRepo.GetFoodByID(item.FoodID)
		if err != nil {
			logs.Error("获取外卖商品详情失败: %v, 商品ID: %d", err, item.FoodID)
			continue
		}

		// 获取规格详情，用于冻结帧
		var variant *takeoutModels.TakeoutFoodVariant
		if item.VariantID > 0 {
			variant, err = s.variantRepo.GetVariantByID(item.VariantID)
			if err != nil {
				logs.Warn("获取外卖规格详情失败: %v, 规格ID: %d，将继续创建订单", err, item.VariantID)
				// 不阻断订单创建流程，规格变体不是必须的
			}
		} else {
			logs.Info("外卖商品没有规格变体，商品ID: %d", item.FoodID)
		}

		// 处理套餐选择
		comboSelections := make([]takeoutModels.TakeoutComboSelectionSnapshot, 0)
		for _, combo := range item.ComboSelections {
			selectedOptions := make([]takeoutModels.TakeoutComboOptionSnapshot, 0)
			for _, option := range combo.SelectedOptions {
				addonAmount += option.ExtraPrice * float64(option.Quantity)
				selectedOptions = append(selectedOptions, takeoutModels.TakeoutComboOptionSnapshot{
					OptionID:   option.OptionID,
					OptionName: option.OptionName,
					ExtraPrice: option.ExtraPrice,
					Quantity:   option.Quantity,
				})
			}

			comboSelections = append(comboSelections, takeoutModels.TakeoutComboSelectionSnapshot{
				ComboID:         combo.ComboID,
				ComboName:       combo.ComboName,
				SelectedOptions: selectedOptions,
			})
		}

		// 商家信息
		merchantID := food.MerchantID
		merchantName := "默认商家" // 实际应从数据库获取

		// 打包费（单价）
		var packageFeePerItem float64
		if variant != nil {
			packageFeePerItem = variant.PackagingFee
		} else {
			// 如果没有规格变体，使用商品的打包费
			packageFeePerItem = food.PackagingFee
		}
		// 计算总打包费（单价 × 数量）
		packageFee = packageFeePerItem * float64(item.Quantity)

		// 创建商品快照
		productSnapshot := takeoutModels.TakeoutSnapshot{
			ID:           food.ID,
			Name:         food.Name,
			Description:  food.Description,
			Image:        food.Image, // 确保商品图片存在
			Price:        food.Price,
			CategoryID:   food.CategoryID,
			CategoryName: food.CategoryName,
			Status:       food.Status,
			SalesCount:   food.SalesCount,
			MerchantID:   food.MerchantID,
			IsCombo:      food.IsCombo,
			PackagingFee: food.PackagingFee,
			Rating:       food.Rating,
			Tags:         food.Tags,
		}

		// 创建规格快照
		var variantSnapshot takeoutModels.TakeoutVariantSnapshot
		if variant != nil {
			variantSnapshot = takeoutModels.TakeoutVariantSnapshot{
				ID:            variant.ID,
				FoodID:        variant.FoodID,
				Name:          variant.Name,
				Price:         variant.Price,
				OriginalPrice: variant.OriginalPrice,
				Stock:         variant.Stock,
				Status:        variant.Status,
				Attributes:    variant.Attributes,
				PackagingFee:  variant.PackagingFee,
			}
		} else {
			// 创建一个默认的规格快照，使用商品的基本信息
			variantSnapshot = takeoutModels.TakeoutVariantSnapshot{
				FoodID:        food.ID,
				Name:          "标准规格",
				Price:         food.Price,
				OriginalPrice: food.Price,
				PackagingFee:  food.PackagingFee,
			}
		}

		// 将快照转换为JSON字符串
		productSnapshotJSON, _ := json.Marshal(productSnapshot)
		variantSnapshotJSON, _ := json.Marshal(variantSnapshot)
		comboSelectionsJSON, _ := json.Marshal(comboSelections)

		// 添加调试信息：检查FoodImage字段
		logs.Info("[调试] CreateOrderFromCart - 购物车项ID: %d, FoodName: '%s', FoodImage: '%s'",
			item.CartItemID, item.FoodName, item.FoodImage)

		orderItems = append(orderItems, OrderItemTemp{
			ProductID:       item.FoodID,
			ProductName:     item.FoodName,
			ProductImage:    item.FoodImage,
			ProductType:     orderModels.ItemTypeTakeout,
			SkuID:           item.VariantID,
			SkuName:         item.VariantName,
			Quantity:        item.Quantity,
			Price:           item.Price,
			OriginalPrice:   item.OriginalPrice,
			SubtotalAmount:  item.Price*float64(item.Quantity) + addonAmount,
			Remark:          item.Remark,
			MerchantID:      merchantID,
			MerchantName:    merchantName,
			PackagingFee:    packageFee,
			ProductSnapshot: string(productSnapshotJSON),
			VariantSnapshot: string(variantSnapshotJSON),
			ComboSelections: string(comboSelectionsJSON),
		})
	}

	// 计算配送费
	var deliveryFee float64 = 5.0

	// 定义一个临时的订单请求类型
	type OrderRequestTemp struct {
		UserID          int64
		OrderType       int // 修改为int类型
		Items           []OrderItemTemp
		TotalAmount     float64
		DiscountAmount  float64
		PayAmount       float64
		AddressID       int64
		PaymentMethod   int // 修改为int类型
		Remark          string
		DeliveryFee     float64
		PackagingFee    float64
		CouponID        int64
		PointsDiscount  float64
		PlatformOrderNo string
	}

	// 使用 TakeoutAddressID 替代 AddressID
	addressID := req.TakeoutAddressID

	// 构建基础订单请求
	baseOrderReq := &OrderRequestTemp{
		UserID:          userID,
		OrderType:       1, // 外卖订单类型为1
		Items:           orderItems,
		TotalAmount:     cartSummary.TotalAmount,
		DiscountAmount:  cartSummary.DiscountAmount,
		PayAmount:       cartSummary.TotalAmount + cartSummary.PackagingFeeAmount + deliveryFee,
		AddressID:       addressID,
		PaymentMethod:   1, // 默认支付方式，可以根据实际需求调整
		Remark:          req.Remark,
		DeliveryFee:     deliveryFee,
		PackagingFee:    cartSummary.PackagingFeeAmount,
		CouponID:        req.CouponID,
		PointsDiscount:  0,  // 积分折扣金额需要计算
		PlatformOrderNo: "", // 平台订单号由订单服务生成
	}

	// 由于 s.baseOrderSvc.CreateOrder 需要 context 和特定类型的参数
	// 我们暂时使用直接生成一个临时的订单ID
	// 真正完整实现需要同时开发基础订单服务和外卖订单模块

	// 创建基础订单
	ctx := context.Background()

	// 根据TakeoutAddressID获取实际地址信息
	addressInfo, err := s.addrService.GetAddressByID(ctx, userID, req.TakeoutAddressID)
	if err != nil {
		tx.Rollback()
		logs.Error("获取地址信息失败: %v, 地址ID: %d, 用户ID: %d", err, req.TakeoutAddressID, userID)
		return nil, errors.New("获取配送地址信息失败")
	}

	// 将之前准备的baseOrderReq转换为基础订单类型
	createOrderReq := &baseDTO.CreateOrderRequest{
		UserID:       baseOrderReq.UserID,
		OrderType:    baseOrderReq.OrderType,
		Items:        make([]*baseDTO.OrderItemRequest, 0, len(baseOrderReq.Items)),
		Remark:       baseOrderReq.Remark,
		PayMethod:    baseOrderReq.PaymentMethod,
		DeliveryType: 1, // 默认配送方式
		Source:       1, // 默认订单来源
		// 使用实际地址信息
		Address: &baseDTO.OrderAddressRequest{
			ReceiverName:  addressInfo.ReceiverName,
			ReceiverPhone: addressInfo.ReceiverMobile,
			Province:      addressInfo.Province,
			City:          addressInfo.City,
			District:      addressInfo.District,
			Detail:        addressInfo.DetailedAddress,
		},
	}

	// 转换订单项
	for _, item := range baseOrderReq.Items {
		// 添加调试日志，查看快照中的图片字段
		var snapshotMap map[string]interface{}
		if err := json.Unmarshal([]byte(item.ProductSnapshot), &snapshotMap); err == nil {
			logs.Info("商品快照解析成功，图片字段: %v", snapshotMap["image"])
		} else {
			logs.Error("商品快照解析失败: %v", err)
		}

		// 添加调试信息：检查传递给订单服务的ProductImage字段
		logs.Info("[调试] 传递给订单服务 - ProductID: %d, ProductName: '%s', ProductImage: '%s'",
			item.ProductID, item.ProductName, item.ProductImage)

		// 创建支持外卖特有字段和商品冻结帧的订单项请求
		orderItemReq := &baseDTO.OrderItemRequest{
			ProductID:       item.ProductID,
			ProductName:     item.ProductName,
			ProductImage:    item.ProductImage,
			SkuID:           item.SkuID,
			Quantity:        item.Quantity,
			Price:           item.Price,
			ItemType:        orderModels.ItemTypeTakeout,
			MerchantID:      item.MerchantID,
			MerchantName:    item.MerchantName,
			PackagingFee:    item.PackagingFee,
			Remark:          item.Remark,
			ProductSnapshot: item.ProductSnapshot,
			VariantSnapshot: item.VariantSnapshot,
			ComboSelections: item.ComboSelections,
		}
		createOrderReq.Items = append(createOrderReq.Items, orderItemReq)
		logs.Info("添加外卖订单项，商品ID: %d, 订单项类型: %s, 商家ID: %d",
			item.ProductID, orderModels.ItemTypeTakeout, item.MerchantID)
	}

	// 调用基础订单服务创建订单
	baseOrder, err := s.baseOrderSvc.CreateOrder(ctx, createOrderReq)
	if err != nil {
		tx.Rollback()
		logs.Error("创建基础订单失败: %v, 用户ID: %d", err, userID)
		return nil, errors.New("创建订单失败")
	}

	// 使用创建的订单ID
	tmpOrderID := baseOrder.ID

	// 记录订单创建信息
	logs.Info("创建基础订单成功，用户ID: %d, 订单ID: %d", userID, tmpOrderID)

	// 预计送达时间
	estimatedDeliveryTime := time.Now().Add(time.Minute * 30)

	// 处理期望配送时间，如果前端未提供，则默认为"尽快送达"（当前时间+30分钟）
	expectedDeliveryTime := estimatedDeliveryTime
	// 默认非预订单
	isPreOrder := false
	// 默认预订时间为当前时间
	preOrderTime := time.Now()
	// 默认配送开始时间和结束时间
	deliveryStartTime := time.Now()
	deliveryEndTime := expectedDeliveryTime

	if req.DeliveryTime != "" {
		// 如果前端提供了期望配送时间，则解析该时间（使用本地时区）
		parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.DeliveryTime, time.Local)
		if err == nil {
			expectedDeliveryTime = parsedTime

			// 如果期望配送时间比当前时间晚超过1小时，则视为预订单
			if time.Until(expectedDeliveryTime) > time.Hour {
				isPreOrder = true
				preOrderTime = expectedDeliveryTime
				logs.Info("检测到预订单，预订时间: %v", preOrderTime.Format("2006-01-02 15:04:05"))
			}
		} else {
			logs.Warn("解析期望配送时间失败: %v, 使用默认时间", err)
		}
	} else {
		logs.Info("前端未提供期望配送时间，使用默认时间（尽快送达）")
	}

	// 回填订单扩展信息所需的默认值
	// 定义了一个默认商家ID，实际应从商品或购物车中获取
	// 后续可增加商家信息获取逻辑
	var defaultMerchantID int64 = 1

	// 创建外卖订单扩展信息
	takeoutOrder := &takeoutModels.TakeoutOrderExtension{
		OrderID:               tmpOrderID,
		MerchantID:            defaultMerchantID, // 使用默认商家ID
		EstimatedDeliveryTime: estimatedDeliveryTime,
		ExpectedDeliveryTime:  expectedDeliveryTime, // 设置期望配送时间
		DeliveryFee:           deliveryFee,
		PackagingFee:          cartSummary.PackagingFeeAmount,
		IsPreOrder:            isPreOrder,        // 设置是否为预订单
		PreOrderTime:          preOrderTime,      // 设置预订时间
		DeliveryStartTime:     deliveryStartTime, // 设置配送开始时间
		DeliveryEndTime:       deliveryEndTime,   // 设置配送结束时间
		// 使用已有字段代替缺失的字段
		DeliveryStatus: 0, // 配送状态先设为0表示未配送
	}

	// 存储外卖订单扩展信息
	_, err = s.orderRepo.CreateOrderExtension(takeoutOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("创建外卖订单扩展信息失败: %v, 订单ID: %d", err, tmpOrderID)
		return nil, errors.New("创建订单失败")
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, tmpOrderID)
		return nil, errors.New("创建订单失败")
	}

	// 异步批量移除购物车项，不阻塞主流程
	go func(uid int64, cartIds []int64) {
		err := s.cartService.BatchRemoveFromCart(uid, cartIds)
		if err != nil {
			logs.Warn("异步批量移除购物车项失败: %v, 用户ID: %d", err, uid)
		}
	}(userID, req.CartItemIDs)

	// 直接构建返回对象，避免二次查询数据库
	// 构造地址字符串
	addressStr := ""
	if baseOrder.Address != nil {
		addr := baseOrder.Address
		addressStr = addr.Province + addr.City + addr.District + addr.Detail
	}

	// 构造配送信息
	deliveryInfo := &dto.TakeoutDeliveryDTO{
		DeliveryStaffID:    takeoutOrder.DeliveryStaffID,
		DeliveryStaffName:  takeoutOrder.DeliveryStaffName,
		DeliveryStaffPhone: takeoutOrder.DeliveryStaffPhone,
		DeliveryStatus:     takeoutOrder.DeliveryStatus,
		DeliveryAddress:    addressStr,
		ExpectedTime:       takeoutOrder.EstimatedDeliveryTime.Format("2006-01-02 15:04:05"),
	}

	// 构造订单DTO
	orderDTO := &dto.TakeoutOrderDTO{
		OrderID:        tmpOrderID,
		OrderNo:        baseOrder.OrderNo,
		UserID:         baseOrder.UserID,
		OrderStatus:    baseOrder.Status,
		PayStatus:      baseOrder.PayStatus,
		DeliveryStatus: takeoutOrder.DeliveryStatus,
		TotalAmount:    baseOrder.TotalAmount,
		DiscountAmount: baseOrder.DiscountAmount,
		DeliveryFee:    takeoutOrder.DeliveryFee,
		PayAmount:      baseOrder.PayAmount,
		PaymentMethod:  baseOrder.PayMethodText,
		Remark:         baseOrder.Remark,
		IsRated:        false,                               // 新订单未评价
		Items:          make([]*dto.TakeoutOrderItemDTO, 0), // 新创建的订单项，需要时可以转换
		CreateTime:     baseOrder.CreatedAt,
		PayTime:        baseOrder.PayTime,
		DeliveryInfo:   deliveryInfo,
	}

	return orderDTO, nil
}

// GetOrderPaymentInfo 获取订单支付信息
func (s *takeoutOrderService) GetOrderPaymentInfo(orderID int64) (*dto.TakeoutOrderPaymentDTO, error) {
	// 创建上下文
	ctx := context.Background()

	// 获取基础订单信息
	baseOrder, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return nil, err
	}

	// 检查订单类型
	if baseOrder.OrderType != 1 { // 假设1表示外卖订单类型
		return nil, errors.New("非外卖订单")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return nil, err
	}

	// 构造支付信息
	paymentInfo := &dto.TakeoutOrderPaymentDTO{
		OrderID:           orderID,
		OrderNo:           baseOrder.OrderNo,
		TotalAmount:       baseOrder.TotalAmount,
		DiscountAmount:    baseOrder.DiscountAmount,
		PackagingFee:      takeoutOrder.PackagingFee,
		DeliveryFee:       takeoutOrder.DeliveryFee,
		CouponDiscount:    baseOrder.CouponAmount,   // 使用CouponAmount字段
		PointsDiscount:    baseOrder.IntegralAmount, // 使用IntegralAmount字段
		PayAmount:         baseOrder.PayAmount,
		PaymentMethod:     fmt.Sprintf("%d", baseOrder.PayMethod),    // 转换为字符串
		PayStatus:         baseOrder.PayStatus,                       // 使用PayStatus字段
		PaymentStatus:     baseOrder.PayStatus,                       // 使用PayStatus字段
		ExpireTime:        baseOrder.CreatedAt.Add(time.Minute * 30), // 支付有效期30分钟
		PaymentExpireTime: baseOrder.CreatedAt.Add(time.Minute * 30), // 支付有效期30分钟
	}

	return paymentInfo, nil
}

// parseVariantSnapshot 从商品规格快照中解析规格文本（支持多种格式）
func (s *takeoutOrderService) parseVariantSnapshot(itemID int64, orderID int64, snapshot string) string {
	if snapshot == "" {
		return ""
	}

	// 方法1: 解析为map[string]interface{}
	var variantData map[string]interface{}
	if err := json.Unmarshal([]byte(snapshot), &variantData); err == nil {
		// 构建规格文本
		tmpSpecText := ""

		// 获取规格名称
		if name, ok := variantData["name"].(string); ok && name != "标准规格" {
			tmpSpecText = name
		}

		// 获取规格属性
		if attributes, ok := variantData["attributes"].(string); ok && attributes != "" {
			if tmpSpecText != "" {
				tmpSpecText += ", " + attributes
			} else {
				tmpSpecText = attributes
			}
		}

		// 设置规格文本
		if tmpSpecText != "" {
			return tmpSpecText
		}
	}

	// 方法2: 属性数组结构
	var attributes []struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	}
	if err := json.Unmarshal([]byte(snapshot), &attributes); err == nil && len(attributes) > 0 {
		var specParts []string
		for _, attr := range attributes {
			specParts = append(specParts, fmt.Sprintf("%s：%s", attr.Name, attr.Value))
		}
		specText := strings.Join(specParts, "； ")
		return specText
	}

	// 方法3: 直接是map结构
	var attrMap map[string]string
	if err := json.Unmarshal([]byte(snapshot), &attrMap); err == nil && len(attrMap) > 0 {
		var specParts []string
		for k, v := range attrMap {
			specParts = append(specParts, fmt.Sprintf("%s：%s", k, v))
		}
		specText := strings.Join(specParts, "； ")
		return specText
	}

	// 方法4: TakeoutVariantSnapshot 结构
	var variantSnapshot orderModels.TakeoutVariantSnapshot
	if err := json.Unmarshal([]byte(snapshot), &variantSnapshot); err == nil && len(variantSnapshot.Attributes) > 0 {
		var specParts []string
		for k, v := range variantSnapshot.Attributes {
			specParts = append(specParts, fmt.Sprintf("%s：%s", k, v))
		}
		specText := strings.Join(specParts, "； ")
		return specText
	}

	// 解析失败时只记录Debug级别日志，减少日志输出
	logs.Debug("[VariantSnapshotParser] 解析失败 - OrderItem: %d, OrderID: %d", itemID, orderID)
	return ""
}

// GetOrderByID 根据ID获取订单详情
func (s *takeoutOrderService) GetOrderByID(orderID int64) (*dto.TakeoutOrderDTO, error) {
	// 创建上下文
	ctx := context.Background()

	// 获取基础订单信息
	baseOrder, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return nil, err
	}

	// 检查订单类型
	if baseOrder.OrderType != 1 { // 假设1表示外卖订单类型
		return nil, errors.New("非外卖订单")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return nil, err
	}

	// 初始化订单项DTO列表
	items := make([]*dto.TakeoutOrderItemDTO, 0)

	// 处理订单项数据
	if len(baseOrder.Items) > 0 {
		for _, item := range baseOrder.Items {
			// 准备规格文本
			specText := item.SkuAttributes

			// 如果规格文本为空，尝试从规格快照中提取
			if specText == "" && item.VariantSnapshot != "" {
				// 使用公共方法解析规格快照
				parsedSpecText := s.parseVariantSnapshot(item.ID, orderID, item.VariantSnapshot)
				if parsedSpecText != "" {
					specText = parsedSpecText
				}
			}

			// 转换为外卖订单项DTO
			takeoutItem := &dto.TakeoutOrderItemDTO{
				ID:          item.ID,
				OrderID:     item.OrderID,
				ProductID:   item.ProductID,
				ProductName: item.ProductName,
				ProductType: "takeout", // 外卖商品类型
				Price:       item.Price,
				Quantity:    item.Quantity,
				Amount:      item.SubtotalAmount,
				SpecText:    specText,
				Image:       item.ProductImage,
			}

			// 如果商品图片为空，尝试从商品快照中获取
			if takeoutItem.Image == "" {
				// 可以在这里添加从商品快照中解析图片的逻辑
				logs.Info("订单项商品图片为空，商品ID: %d, 订单ID: %d", item.ProductID, orderID)
			}

			items = append(items, takeoutItem)
		}
		logs.Info("成功转换订单项，订单ID: %d, 订单项数量: %d", orderID, len(items))
	} else {
		logs.Warn("订单没有订单项数据，订单ID: %d", orderID)
	}
	// 创建一个订单配送信息
	// 构造地址字符串
	addressStr := ""
	if baseOrder.Address != nil {
		addr := baseOrder.Address
		addressStr = addr.Province + addr.City + addr.District + addr.Detail
	}

	deliveryInfo := &dto.TakeoutDeliveryDTO{
		DeliveryStaffID:    takeoutOrder.DeliveryStaffID,
		DeliveryStaffName:  takeoutOrder.DeliveryStaffName,
		DeliveryStaffPhone: takeoutOrder.DeliveryStaffPhone,
		DeliveryStatus:     takeoutOrder.DeliveryStatus,
		DeliveryAddress:    addressStr,               // 从基础订单获取地址
		DeliveryLat:        takeoutOrder.DeliveryLat, // 从外卖订单扩展表获取送货地址纬度
		DeliveryLng:        takeoutOrder.DeliveryLng, // 从外卖订单扩展表获取送货地址经度
		ReceiverName:       "",                       // 默认为空，后面会设置
		ReceiverPhone:      "",                       // 默认为空，后面会设置
		DeliveryDistance:   takeoutOrder.DistanceKm,
		ExpectedTime:       takeoutOrder.EstimatedDeliveryTime.Format("2006-01-02 15:04:05"),
		StartTime:          nil, // 配送开始时间
		EndTime:            nil, // 配送结束时间
	}

	// 设置配送开始时间和结束时间（如果不为零值）
	if !takeoutOrder.DeliveryStartTime.IsZero() {
		deliveryInfo.StartTime = &takeoutOrder.DeliveryStartTime
	}
	if !takeoutOrder.DeliveryEndTime.IsZero() {
		deliveryInfo.EndTime = &takeoutOrder.DeliveryEndTime
	}

	// 设置收货人信息
	if baseOrder.Address != nil {
		deliveryInfo.ReceiverName = baseOrder.Address.ReceiverName
		deliveryInfo.ReceiverPhone = baseOrder.Address.ReceiverPhone
	}

	// 查询退款信息
	refund, err := s.refundRepo.GetRefundByOrderID(orderID)
	var hasRefund bool
	var refundNo string
	var refundStatus int
	var refundAmount float64
	var refundReason string
	var refundTime *time.Time

	if err == nil && refund != nil {
		hasRefund = true
		refundNo = refund.RefundNo
		refundStatus = refund.RefundStatus
		refundAmount = refund.RefundAmount
		refundReason = refund.RefundReason
		refundTime = &refund.CreateTime
		logs.Info("[外卖订单服务] 订单存在退款记录 - 订单ID: %d, 退款单号: %s, 退款状态: %d, 退款金额: %.2f, 退款原因: %s",
			orderID, refundNo, refundStatus, refundAmount, refundReason)
	} else {
		hasRefund = false
		refundNo = ""
		refundStatus = 0 // 无退款
		refundAmount = 0
		refundReason = ""
		refundTime = nil
		if err != nil {
			logs.Debug("[外卖订单服务] 查询退款记录失败或不存在 - 订单ID: %d, 错误: %v", orderID, err)
		}
	}

	// 获取订单状态文本
	statusText := s.getOrderStatusText(baseOrder.Status)
	logs.Info("订单状态转换 - 订单ID: %d, 状态码: %d, 状态文本: %s", orderID, baseOrder.Status, statusText)

	// 处理acceptedTime的空时间判断
	var acceptedTime *time.Time
	if !takeoutOrder.AcceptedTime.IsZero() {
		temp := takeoutOrder.AcceptedTime
		acceptedTime = &temp
	}

	// 构造订单DTO
	orderDTO := &dto.TakeoutOrderDTO{
		OrderID:        orderID,
		OrderNo:        baseOrder.OrderNo,
		UserID:         baseOrder.UserID,
		MerchantID:     takeoutOrder.MerchantID,     // 商家ID
		OrderStatus:    baseOrder.Status,            // 订单状态
		StatusText:     statusText,                  // 状态文本
		PayStatus:      baseOrder.PayStatus,         // 支付状态
		DeliveryStatus: takeoutOrder.DeliveryStatus, // 配送状态
		TotalAmount:    baseOrder.TotalAmount,       // 总金额
		DiscountAmount: baseOrder.DiscountAmount,    // 折扣金额
		DeliveryFee:    takeoutOrder.DeliveryFee,    // 配送费
		PayAmount:      baseOrder.PayAmount,         // 支付金额
		PaymentMethod:  baseOrder.PayMethodText,     // 支付方式
		Remark:         baseOrder.Remark,            // 备注
		IsRated:        takeoutOrder.IsRated,        // 是否已评价
		PackagingFee:   takeoutOrder.PackagingFee,   // 包装费
		HasRefund:      hasRefund,                   // 是否已申请退款
		RefundNo:       refundNo,                    // 退款单号
		RefundStatus:   refundStatus,                // 退款状态
		RefundAmount:   refundAmount,                // 退款金额
		RefundReason:   refundReason,                // 退款原因
		RefundTime:     refundTime,                  // 退款申请时间
		Items:          items,                       // 订单项
		CreateTime:     baseOrder.CreatedAt,         // 创建时间
		PayTime:        baseOrder.PayTime,           // 支付时间
		AcceptedTime:   acceptedTime,                // 商家接单时间
		DeliveryInfo:   deliveryInfo,                // 配送信息
	}

	return orderDTO, nil
}

// getOrderStatusText 根据订单状态码获取对应的文本描述
func (s *takeoutOrderService) getOrderStatusText(status int) string {
	switch status {
	case 10:
		return "待支付"
	case 20:
		return "已支付待接单"
	case 30:
		return "已接单待取货"
	case 40:
		return "已取货配送中"
	case 50:
		return "已完成"
	case 60:
		return "已取消"
	default:
		return "未知状态"
	}
}

// ListOrdersByUserID 查询用户订单列表
func (s *takeoutOrderService) ListOrdersByUserID(userID int64, status int, page, pageSize int) (*dto.TakeoutOrderListDTO, error) {
	// 创建上下文
	ctx := context.Background()

	// 创建查询请求
	req := &baseDTO.OrderQueryRequest{
		UserID:    userID,
		Status:    status,
		OrderType: 1, // 外卖订单类型
		Page:      page,
		PageSize:  pageSize,
	}

	// 使用 ListOrders 方法代替 ListOrdersByUserID
	ordersResult, err := s.baseOrderSvc.ListOrders(ctx, req)
	if err != nil {
		return nil, err
	}

	// 构造返回结果
	result := &dto.TakeoutOrderListDTO{
		Total:    int(ordersResult.Total), // 转换为int类型
		Page:     ordersResult.Page,
		PageSize: ordersResult.PageSize,
		List:     make([]*dto.TakeoutOrderDTO, 0, len(ordersResult.Items)),
	}

	// 遍历订单列表，获取外卖扩展信息和转换订单项
	for _, orderResp := range ordersResult.Items {
		// 获取外卖订单扩展信息
		takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderResp.ID)
		if err != nil {
			logs.Warn("获取外卖订单扩展信息失败: %v, 订单ID: %d", err, orderResp.ID)
			continue
		}

		// 转换订单项到DTO
		itemDTOs := make([]*dto.TakeoutOrderItemDTO, 0, len(orderResp.Items))
		for _, item := range orderResp.Items {
			// 提取套餐选项文本
			comboItems := make([]string, 0)
			// 判断是否有SKU属性作为规格文本
			specText := ""
			if item.SkuAttributes != "" {
				specText = item.SkuAttributes
			} else if item.VariantSnapshot != "" {
				// 使用公共方法解析规格快照
				parsedSpecText := s.parseVariantSnapshot(item.ID, item.OrderID, item.VariantSnapshot)
				if parsedSpecText != "" {
					specText = parsedSpecText
				}
			}

			// 构造订单项DTO
			itemDTO := &dto.TakeoutOrderItemDTO{
				ID:            item.ID,
				OrderID:       item.OrderID,
				ProductID:     item.ProductID,
				ProductName:   item.ProductName,
				ProductType:   "takeout", // 固定为外卖类型
				Price:         item.Price,
				Quantity:      item.Quantity,
				Amount:        item.SubtotalAmount, // 使用小计金额
				SpecText:      specText,
				Image:         item.ProductImage,
				IsCombination: false, // 默认为非套餐
				ComboItems:    comboItems,
			}

			itemDTOs = append(itemDTOs, itemDTO)
		}

		// 处理配送信息
		deliveryStatus := takeoutOrder.DeliveryStatus

		// 构造地址文本
		addressText := ""
		if orderResp.Address != nil {
			addr := orderResp.Address
			addressText = addr.Province + addr.City + addr.District + addr.Detail
		}

		// 构造配送信息DTO
		deliveryInfo := &dto.TakeoutDeliveryDTO{
			DeliveryStaffID:    takeoutOrder.DeliveryStaffID,
			DeliveryStaffName:  takeoutOrder.DeliveryStaffName,
			DeliveryStaffPhone: takeoutOrder.DeliveryStaffPhone,
			DeliveryStatus:     takeoutOrder.DeliveryStatus,
			DeliveryAddress:    addressText,
			ExpectedTime:       takeoutOrder.ExpectedDeliveryTime.Format("2006-01-02 15:04:05"),
		}

		// 判断是否已评价
		var isRated bool
		if takeoutOrder != nil {
			// 检查订单扩展中是否有IsRated字段
			// 由于TakeoutOrderExtension结构体中可能没有IsRated字段，这里做个安全检查
			// 如果没有此字段，则默认为未评价
			isRated = false
		}

		// 查询退款信息
		refund, err := s.refundRepo.GetRefundByOrderID(orderResp.ID)
		var hasRefund bool
		var refundStatus int
		var refundAmount float64
		var refundTime *time.Time

		if err == nil && refund != nil {
			hasRefund = true
			refundStatus = refund.RefundStatus
			refundAmount = refund.RefundAmount
			refundTime = &refund.CreateTime
		} else {
			hasRefund = false
			refundStatus = 0 // 无退款
			refundAmount = 0
			refundTime = nil
		}

		// 构造订单DTO
		orderDTO := &dto.TakeoutOrderDTO{
			OrderID:        orderResp.ID,
			OrderNo:        orderResp.OrderNo,
			UserID:         orderResp.UserID,
			OrderStatus:    orderResp.Status,
			PayStatus:      orderResp.PayStatus,
			DeliveryStatus: deliveryStatus,
			PaymentMethod:  orderResp.PayMethodText,
			TotalAmount:    orderResp.TotalAmount,
			PayAmount:      orderResp.PayAmount,
			Remark:         orderResp.Remark,
			IsRated:        isRated,
			HasRefund:      hasRefund,    // 是否已申请退款
			RefundStatus:   refundStatus, // 退款状态
			RefundAmount:   refundAmount, // 退款金额
			RefundTime:     refundTime,   // 退款申请时间
			CreateTime:     orderResp.CreatedAt,
			PayTime:        orderResp.PayTime,
			DeliveryInfo:   deliveryInfo,
			CancelTime:     orderResp.CancelTime,
			Items:          itemDTOs, // 添加订单项
		}

		result.List = append(result.List, orderDTO)
	}

	return result, nil
}

// CountOrdersByUserID 统计用户订单数量
func (s *takeoutOrderService) CountOrdersByUserID(userID int64, status int) (int, error) {
	// 创建上下文
	ctx := context.Background()

	// 创建查询请求对象
	req := &baseDTO.OrderQueryRequest{
		UserID:    userID,
		Status:    status,
		OrderType: 1, // 外卖订单类型
		Page:      1,
		PageSize:  1, // 只需要数量，不需要实际数据
	}

	// 使用 ListOrders 查询结果后获取总数
	result, err := s.baseOrderSvc.ListOrders(ctx, req)
	if err != nil {
		return 0, err
	}

	return int(result.Total), nil
}

// 定义订单状态常量
const (
	// 订单状态
	OrderStatusPending    = 10 // 待支付
	OrderStatusPaid       = 20 // 已支付
	OrderStatusProcessing = 30 // 处理中
	OrderStatusDelivering = 40 // 配送中
	OrderStatusCompleted  = 50 // 已完成
	OrderStatusCancelled  = 60 // 已取消
	OrderStatusRefunding  = 70 // 退款中
	OrderStatusRefunded   = 80 // 已退款
)

// 定义配送状态常量
const (
	// 配送状态
	DeliveryStatusPending    = 0 // 待配送
	DeliveryStatusAccepted   = 1 // 已接单
	DeliveryStatusAssigned   = 2 // 已分配配送员
	DeliveryStatusPickup     = 3 // 配送员取餐中
	DeliveryStatusInProgress = 4 // 配送中
	DeliveryStatusCompleted  = 5 // 已送达
	DeliveryStatusCancelled  = 6 // 已取消
)

// CancelOrder 取消订单
func (s *takeoutOrderService) CancelOrder(orderID int64, userID int64, reason string) error {
	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单所属用户
	if order.UserID != userID {
		return errors.New("无法取消不属于您的订单")
	}

	// 检查订单类型
	if order.OrderType != 1 { // 1表示外卖订单类型
		return errors.New("非外卖订单不能通过此接口取消")
	}

	// 检查订单状态，只有待支付和待接单的订单可以取消
	if order.Status != OrderStatusPending && order.Status != OrderStatusPaid {
		return errors.New("当前订单状态不允许取消")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查配送状态
	if takeoutOrder.DeliveryStatus != DeliveryStatusPending {
		return errors.New("订单已开始配送，无法取消")
	}

	// 开始数据库事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		return err
	}

	// 直接更新订单状态为已取消，因为基础订单服务只允许取消待付款订单
	// 但商家需要能够取消已支付和处理中的订单
	err = s.baseOrderRepo.UpdateOrderStatus(ctx, orderID, OrderStatusCancelled)
	if err != nil {
		tx.Rollback()
		logs.Error("更新订单状态失败: %v, 订单ID: %d", err, orderID)
		return errors.New("取消订单失败")
	}

	// 更新外卖订单扩展信息
	takeoutOrder.DeliveryStatus = DeliveryStatusCancelled
	err = s.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("更新外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("取消订单失败")
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("取消订单失败")
	}

	// 记录订单操作日志
	log := &takeoutModels.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  userID,
		Action:  "cancel",
		Remark:  reason,
		IP:      "", // 在控制器层填充
	}

	_, _ = s.orderRepo.CreateOrderLog(log)

	return nil
}

// MerchantCancelOrder 商家取消订单
func (s *takeoutOrderService) MerchantCancelOrder(orderID int64, merchantID int64, reason string) error {
	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单类型
	if order.OrderType != 1 { // 1表示外卖订单类型
		return errors.New("非外卖订单不能通过此接口取消")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查商家权限
	if takeoutOrder.MerchantID != merchantID {
		return errors.New("无法取消不属于您的订单")
	}

	// 添加调试信息
	logs.Info("商家取消订单调试信息: 订单ID=%d, 当前状态=%d, OrderStatusPaid=%d, OrderStatusProcessing=%d", orderID, order.Status, OrderStatusPaid, OrderStatusProcessing)

	// 检查订单状态，商家可以取消已支付和处理中的订单
	if order.Status != OrderStatusPaid && order.Status != OrderStatusProcessing {
		logs.Error("订单状态检查失败: 订单ID=%d, 当前状态=%d, 期望状态=%d或%d", orderID, order.Status, OrderStatusPaid, OrderStatusProcessing)
		return errors.New("当前订单状态不允许取消")
	}

	// 检查配送状态
	if takeoutOrder.DeliveryStatus == DeliveryStatusInProgress || takeoutOrder.DeliveryStatus == DeliveryStatusCompleted {
		return errors.New("订单已开始配送或已完成，无法取消")
	}

	// 开始数据库事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		return err
	}

	// 直接更新订单状态为已取消
	err = s.baseOrderRepo.UpdateOrderStatus(ctx, orderID, int(OrderStatusCancelled))
	if err != nil {
		tx.Rollback()
		logs.Error("更新订单状态失败: %v, 订单ID: %d", err, orderID)
		return errors.New("取消订单失败")
	}

	// 更新外卖订单扩展信息
	takeoutOrder.DeliveryStatus = DeliveryStatusCancelled
	err = s.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("更新外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("取消订单失败")
	}

	// 如果订单已支付，需要退款
	if order.Status == OrderStatusPaid || order.Status == OrderStatusProcessing {
		// 调用退款服务进行退款
		logs.Info("商家取消订单，开始退款: 订单ID=%d, 金额=%f", orderID, order.TotalAmount)

		// 获取支付记录
		payment, err := s.paymentRepo.GetPaymentByOrderID(orderID)
		if err != nil {
			tx.Rollback()
			logs.Error("获取支付记录失败: %v, 订单ID: %d", err, orderID)
			return fmt.Errorf("取消订单失败：无法获取支付记录 - %v", err)
		}

		if payment == nil {
			tx.Rollback()
			logs.Error("支付记录不存在, 订单ID: %d", orderID)
			return errors.New("取消订单失败：支付记录不存在")
		}

		// 创建退款请求
		refundReq := &paymentDTO.RefundCreateRequest{
			PaymentID:    payment.ID, // 使用支付记录ID
			OrderID:      orderID,
			UserID:       order.UserID,
			Amount:       order.TotalAmount,
			Reason:       "商家取消订单",
			OperatorID:   merchantID,
			NeedApproval: false, // 商家取消订单不需要审批，直接退款
		}

		// 调用退款服务
		refundResp, err := s.refundSvc.CreateRefund(refundReq)
		if err != nil {
			tx.Rollback()
			logs.Error("创建退款失败: %v, 订单ID: %d", err, orderID)
			return fmt.Errorf("取消订单失败：退款处理异常 - %v", err)
		}

		logs.Info("退款创建成功: 订单ID=%d, 退款ID=%d, 退款流水号=%s, 状态=%d",
			orderID, refundResp.RefundID, refundResp.RefundNo, refundResp.Status)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("取消订单失败")
	}

	// 记录订单操作日志
	log := &takeoutModels.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  merchantID, // 这里记录商家ID
		Action:  "merchant_cancel",
		Remark:  reason,
		IP:      "", // 在控制器层填充
	}

	_, _ = s.orderRepo.CreateOrderLog(log)

	return nil
}

// StartDelivery 开始配送

// AcceptOrder 商家接单
func (s *takeoutOrderService) AcceptOrder(orderID int64, operatorID int64) error {
	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单类型
	if order.OrderType != 1 { // 1表示外卖订单类型
		return errors.New("非外卖订单不能通过此接口接单")
	}

	// 检查订单状态，只有已支付的订单可以接单
	if order.Status != OrderStatusPaid {
		return errors.New("只有已支付的订单可以接单")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查配送状态
	if takeoutOrder.DeliveryStatus != DeliveryStatusPending {
		return errors.New("订单状态异常，无法接单")
	}

	// 计算配送距离
	// 1. 获取商家位置信息
	merchantID := takeoutOrder.MerchantID
	merchantLocationSvc := merchantServices.NewMerchantLocationService() // 导入merchant服务
	merchantLocation, err := merchantLocationSvc.GetMerchantLocation(ctx, merchantID)
	if err != nil {
		logs.Warn("[AcceptOrder] 获取商家位置信息失败: %v, 订单ID: %d, 商家ID: %d", err, orderID, merchantID)
		// 获取位置失败不影响接单流程，继续执行
	} else if merchantLocation != nil && order.Address != nil {
		// 2. 计算商家与配送地址之间的距离
		// 商家的经纬度
		merchantLat := merchantLocation.Latitude
		merchantLng := merchantLocation.Longitude

		// 送货地址的经纬度（之前已经保存在订单扩展表中）
		deliveryLat := takeoutOrder.DeliveryLat
		deliveryLng := takeoutOrder.DeliveryLng

		// 检查坐标是否有效
		if geo.IsValidCoordinate(merchantLat, merchantLng) &&
			geo.IsValidCoordinate(deliveryLat, deliveryLng) &&
			(deliveryLat != 0 || deliveryLng != 0) {
			// 使用Haversine公式计算距离（单位：公里）
			distanceKm := geo.CalculateDistance(merchantLat, merchantLng, deliveryLat, deliveryLng)

			// 更新订单扩展表中的配送距离
			takeoutOrder.DistanceKm = distanceKm
			logs.Info("[AcceptOrder] 计算配送距离成功: %.2f公里, 订单ID: %d, 商家(%.6f,%.6f) -> 送货地址(%.6f,%.6f)",
				distanceKm, orderID, merchantLat, merchantLng, deliveryLat, deliveryLng)
		} else {
			logs.Warn("[AcceptOrder] 商家或送货地址经纬度无效，无法计算距离. 订单ID: %d, 商家经纬度(%.6f,%.6f), 送货经纬度(%.6f,%.6f)",
				orderID, merchantLat, merchantLng, deliveryLat, deliveryLng)
		}
	}

	// 开始数据库事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		return err
	}

	// 更新主订单状态为处理中
	err = s.baseOrderRepo.UpdateOrderStatus(ctx, orderID, OrderStatusProcessing)
	if err != nil {
		tx.Rollback()
		logs.Error("更新主订单状态失败: %v, 订单ID: %d", err, orderID)
		return errors.New("接单失败")
	}

	// 更新外卖订单扩展信息
	takeoutOrder.DeliveryStatus = DeliveryStatusAccepted
	// 记录商家接单时间
	takeoutOrder.AcceptedTime = time.Now()
	err = s.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("更新外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("接单失败")
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("接单失败")
	}

	// 记录订单操作日志
	log := &takeoutModels.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  operatorID,
		Action:  "accept",
		Remark:  "商家已接单",
		IP:      "", // 在控制器层填充
	}

	_, _ = s.orderRepo.CreateOrderLog(log)

	// 获取自动分配配置
	autoAssignConfig := GetAutoAssignConfig()

	// 如果启用了自动分配功能，则触发自动分配骑手
	if autoAssignConfig.Enabled {
		logs.Info("订单接单成功，触发自动分配骑手, 订单ID: %d", orderID)
		// 异步执行自动分配，避免阻塞主流程
		go func() {
			// 创建新的上下文，避免使用已关闭的上下文
			asyncCtx := context.Background()
			// 等待3秒再开始分配，避免和其他操作冲突
			time.Sleep(3 * time.Second)
			err := s.AutoAssignDelivery(asyncCtx, orderID)
			if err != nil {
				logs.Error("[自动分配] 订单 %d 自动分配失败: %v", orderID, err)
			}
		}()
	}

	return nil
}

// ...
// AssignDelivery 分配配送员
func (s *takeoutOrderService) AssignDelivery(orderID int64, deliveryStaffID int64, operatorID int64) error {
	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单类型
	if order.OrderType != 1 { // 1表示外卖订单类型
		return errors.New("非外卖订单不能分配配送员")
	}

	// 检查订单状态，只有处理中的订单可以分配配送员
	if order.Status != OrderStatusProcessing {
		return errors.New("只有处理中的订单可以分配配送员")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查配送状态
	if takeoutOrder.DeliveryStatus != DeliveryStatusAccepted {
		return errors.New("订单状态异常，无法分配配送员")
	}

	// 获取配送员信息
	var deliveryStaff *userDTO.UserResponse
	deliveryStaff, err = s.userService.GetUserByID(ctx, deliveryStaffID)
	if err != nil {
		logs.Error("获取配送员信息失败: %v, 配送员ID: %d", err, deliveryStaffID)
		return errors.New("获取配送员信息失败")
	}

	deliveryStaffName := deliveryStaff.Nickname
	deliveryStaffPhone := deliveryStaff.Mobile

	// 如果昵称为空，使用用户名
	if deliveryStaffName == "" {
		deliveryStaffName = deliveryStaff.Username
	}
	// 如果用户名也为空，使用默认名称
	if deliveryStaffName == "" {
		deliveryStaffName = "配送员"
	}

	// 开始数据库事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		return err
	}

	// 更新外卖订单扩展信息
	takeoutOrder.DeliveryStatus = DeliveryStatusAssigned
	takeoutOrder.DeliveryStaffID = deliveryStaffID
	takeoutOrder.DeliveryStaffName = deliveryStaffName
	takeoutOrder.DeliveryStaffPhone = deliveryStaffPhone

	err = s.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("更新外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("分配配送员失败")
	}

	// 创建RunnerOrder记录，确保跑腿员可以在runner模块查看到此订单
	// 构建创建RunnerOrder的请求
	// 获取商家信息
	// 通过MerchantID获取商家信息
	merchant, err := s.merchantService.GetMerchantByID(ctx, takeoutOrder.MerchantID)
	if err != nil {
		logs.Warning("获取商家信息失败: %v, 商家ID: %d", err, takeoutOrder.MerchantID)
		// 如果无法获取商家信息，使用默认值
		merchant = &merchantDTO.MerchantResponse{
			Name:          "未知商家",
			Address:       "未知地址",
			ContactMobile: "00000000000",
			ContactName:   "商家联系人",
			Longitude:     0,
			Latitude:      0,
		}
	}

	// 使用商家信息
	merchantName := merchant.Name
	merchantPhone := merchant.ContactMobile
	merchantAddress := merchant.Address
	merchantLng := merchant.Longitude
	merchantLat := merchant.Latitude
	merchantContact := merchant.ContactName
	if merchantContact == "" {
		merchantContact = merchantName
	}

	// 从订单中获取收货人信息
	receiverName := takeoutOrder.DeliveryStaffName   // 默认值
	receiverPhone := takeoutOrder.DeliveryStaffPhone // 默认值
	receiverAddress := "未知地址"                        // 默认值
	// 使用外卖订单扩展表中的经纬度信息
	receiverLat := takeoutOrder.DeliveryLat
	receiverLng := takeoutOrder.DeliveryLng
	logs.Info("获取送货地址经纬度 - 订单ID: %d, 经度: %f, 纬度: %f", orderID, receiverLng, receiverLat)

	// 如果订单有地址信息，使用订单地址
	if order.Address != nil {
		receiverName = order.Address.ReceiverName
		receiverPhone = order.Address.ReceiverPhone
		// 组合完整地址
		receiverAddress = fmt.Sprintf("%s%s%s%s",
			order.Address.Province,
			order.Address.City,
			order.Address.District,
			order.Address.Detail)
	}

	// 构建创建RunnerOrder的请求
	runnerOrderReq := &runnerDTO.CreateRunnerOrderRequest{
		OrderType:             1,                                      // 外卖订单类型
		OrderNo:               order.OrderNo,                          // 使用主订单的订单号，确保同步匹配
		DeliveryFee:           takeoutOrder.DeliveryFee,               // 配送费
		ServiceFee:            0,                                      // 服务费
		Distance:              takeoutOrder.DistanceKm,                // 配送距离
		EstimateTime:          30,                                     // 预计配送时间(分钟)
		PickupAddress:         merchantAddress,                        // 取货地址
		PickupAddressDetail:   "",                                     // 取货详细地址
		PickupLat:             merchantLat,                            // 取货纬度(使用商家真实坐标)
		PickupLng:             merchantLng,                            // 取货经度(使用商家真实坐标)
		PickupContact:         merchantContact,                        // 取货联系人
		PickupPhone:           merchantPhone,                          // 取货联系电话
		DeliveryAddress:       receiverAddress,                        // 送货地址
		DeliveryAddressDetail: "",                                     // 送货详细地址
		DeliveryLat:           receiverLat,                            // 送货纬度
		DeliveryLng:           receiverLng,                            // 送货经度
		DeliveryContact:       receiverName,                           // 送货联系人
		DeliveryPhone:         receiverPhone,                          // 送货联系电话
		Goods:                 fmt.Sprintf("外卖订单 #%s", order.OrderNo), // 商品描述
		GoodsWeight:           1.0,                                    // 商品重量(kg)
		GoodsValue:            order.TotalAmount,                      // 商品价值
		Remark:                takeoutOrder.Remark,                    // 备注
		PayMethod:             1,                                      // 支付方式
	}

	// 调用RunnerService创建RunnerOrder
	// 注意：CreateRunnerOrder接口需要传递userID参数，这里使用deliveryStaffID作为userID
	// CreateRunnerOrder(ctx context.Context, req *dto.CreateRunnerOrderRequest, userID int64) (*dto.RunnerOrderResponse, error)
	runnerOrderResp, createErr := s.runnerService.CreateRunnerOrder(ctx, runnerOrderReq, deliveryStaffID)
	if createErr != nil {
		tx.Rollback()
		logs.Error("创建跑腿员订单失败: %v, 订单ID: %d, 跑腿员ID: %d", createErr, orderID, deliveryStaffID)
		return errors.New("分配配送员失败：无法创建跑腿员订单")
	}

	// 获取runner订单ID
	runnerOrderID := runnerOrderResp.ID

	// 更新runner订单状态为已支付，因为外卖订单在分配配送员时，用户已经完成支付
	// 参考runner_order_lifecycle.md文档，订单状态 OrderStatusPaid = 20, PayStatus = 1
	orderUpdateErr := s.runnerService.UpdateOrderStatus(ctx, runnerOrderID, 20, 1, "外卖订单分配配送员，自动更新为已支付状态")
	if orderUpdateErr != nil {
		tx.Rollback()
		logs.Error("更新跑腿员订单状态失败: %v, 订单ID: %d, 跑腿员ID: %d, runner订单ID: %d",
			orderUpdateErr, orderID, deliveryStaffID, runnerOrderID)
		return errors.New("分配配送员失败：无法更新跑腿员订单状态")
	}

	logs.Info("成功创建跑腿员订单，订单ID: %d, 跑腿员订单ID: %d, 跑腿员ID: %d", orderID, runnerOrderID, deliveryStaffID)

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("分配配送员失败")
	}

	// 记录订单操作日志
	log := &takeoutModels.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  operatorID,
		Action:  "assign_delivery",
		Remark:  fmt.Sprintf("已分配配送员: %s", deliveryStaffName),
		IP:      "", // 在控制器层填充
	}

	_, _ = s.orderRepo.CreateOrderLog(log)

	return nil
}

// StartDelivery 开始配送
func (s *takeoutOrderService) StartDelivery(orderID int64, deliveryStaffID int64) error {
	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单类型
	if order.OrderType != 1 { // 1表示外卖订单类型
		return errors.New("非外卖订单不能通过此接口开始配送")
	}

	// 检查订单状态，只有处理中的订单可以开始配送
	if order.Status != OrderStatusProcessing {
		return errors.New("只有处理中的订单可以开始配送")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查配送状态
	if takeoutOrder.DeliveryStatus != DeliveryStatusAssigned {
		return errors.New("订单状态异常，无法开始配送")
	}

	// 检查配送员身份
	if takeoutOrder.DeliveryStaffID != deliveryStaffID {
		return errors.New("您不是该订单的配送员，无法操作")
	}

	// 开始数据库事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		return err
	}

	// 设置配送开始时间
	now := time.Now()
	takeoutOrder.DeliveryStartTime = now
	takeoutOrder.DeliveryStatus = DeliveryStatusInProgress

	err = s.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("更新外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("开始配送失败")
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("开始配送失败")
	}

	// 记录订单操作日志
	log := &takeoutModels.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  deliveryStaffID,
		Action:  "start_delivery",
		Remark:  "配送员已取餐，开始配送",
		IP:      "", // 在控制器层填充
	}

	_, _ = s.orderRepo.CreateOrderLog(log)

	return nil
}

// CompleteDelivery 完成配送
func (s *takeoutOrderService) CompleteDelivery(orderID int64, deliveryStaffID int64) error {
	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单类型
	if order.OrderType != 1 { // 1表示外卖订单类型
		return errors.New("非外卖订单不能完成配送")
	}

	// 检查订单状态，只有处理中的订单可以完成配送
	if order.Status != OrderStatusProcessing {
		return errors.New("只有处理中的订单可以完成配送")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查配送状态
	if takeoutOrder.DeliveryStatus != DeliveryStatusInProgress {
		return errors.New("订单状态异常，无法完成配送")
	}

	// 检查是否是分配的配送员
	if takeoutOrder.DeliveryStaffID != deliveryStaffID {
		return errors.New("您不是该订单的配送员，无法完成配送")
	}

	// 开始数据库事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		return err
	}

	// 注意：因为 OrderService 接口没有 UpdateOrderStatus 方法
	// 我们这里仅更新外卖订单的状态信息
	// TODO: 建议实现订单状态更新方法

	// 更新外卖订单扩展信息
	takeoutOrder.DeliveryStatus = DeliveryStatusCompleted
	takeoutOrder.DeliveryEndTime = time.Now()

	err = s.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		tx.Rollback()
		logs.Error("更新外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return errors.New("完成配送失败")
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("完成配送失败")
	}

	// 记录订单操作日志
	log := &takeoutModels.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  deliveryStaffID,
		Action:  "complete_delivery",
		Remark:  "配送员已送达，完成配送",
		IP:      "", // 在控制器层填充
	}

	_, _ = s.orderRepo.CreateOrderLog(log)

	return nil
}

// CompleteOrder 完成订单
func (s *takeoutOrderService) CompleteOrder(orderID int64) error {
	// 创建上下文
	ctx := context.Background()

	// 获取订单信息
	order, err := s.baseOrderSvc.GetOrder(ctx, orderID)
	if err != nil {
		return err
	}

	// 检查订单类型
	if order.OrderType != 1 { // 1表示外卖订单类型
		return errors.New("非外卖订单不能通过此接口完成")
	}

	// 检查订单状态，只有配送中的订单可以完成
	if order.Status != OrderStatusDelivering {
		return errors.New("只有配送中的订单可以完成")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := s.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查配送状态
	if takeoutOrder.DeliveryStatus != DeliveryStatusCompleted {
		return errors.New("订单配送状态异常，无法完成订单")
	}

	// 开始数据库事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	if err != nil {
		return err
	}

	// 注意：因为 OrderService 接口没有 UpdateOrderStatus 方法
	// 我们这里使用 CompleteOrder 方法完成订单
	err = s.baseOrderSvc.CompleteOrder(ctx, orderID)
	if err != nil {
		tx.Rollback()
		logs.Error("更新订单状态失败: %v, 订单ID: %d", err, orderID)
		return errors.New("完成订单失败")
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v, 订单ID: %d", err, orderID)
		return errors.New("完成订单失败")
	}

	// 记录订单操作日志
	log := &takeoutModels.TakeoutOrderLog{
		OrderID: orderID,
		UserID:  order.UserID,
		Action:  "complete_order",
		Remark:  "订单已完成",
		IP:      "", // 在控制器层填充
	}

	_, _ = s.orderRepo.CreateOrderLog(log)

	return nil
}
