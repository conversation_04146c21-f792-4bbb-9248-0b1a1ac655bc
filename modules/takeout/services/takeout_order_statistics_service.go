/**
 * 外卖订单统计服务
 *
 * 本文件实现了外卖订单统计相关的业务逻辑，处理订单数量统计、金额统计、时间段筛选等操作。
 * 将其从主订单服务分离，以保持代码的清晰性和可维护性。
 */

package services

import (
	"time"

	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/takeout/dto"
)

// GetOrderStatistics 获取订单统计
func (s *takeoutOrderService) GetOrderStatistics(startDate, endDate time.Time) (*dto.TakeoutOrderStatisticsDTO, error) {
	// 统计总订单数
	totalOrders, err := s.orderRepo.CountOrdersByDateRange(startDate, endDate, 0)
	if err != nil {
		logs.Error("统计总订单数失败: %v, 时间范围: %v - %v", err, startDate, endDate)
		return nil, err
	}
	
	// 统计各状态订单数
	pendingOrders, _ := s.orderRepo.CountOrdersByDateRange(startDate, endDate, OrderStatusPending)
	paidOrders, _ := s.orderRepo.CountOrdersByDateRange(startDate, endDate, OrderStatusPaid)
	processingOrders, _ := s.orderRepo.CountOrdersByDateRange(startDate, endDate, OrderStatusProcessing)
	deliveringOrders, _ := s.orderRepo.CountOrdersByDateRange(startDate, endDate, OrderStatusDelivering)
	completedOrders, _ := s.orderRepo.CountOrdersByDateRange(startDate, endDate, OrderStatusCompleted)
	cancelledOrders, _ := s.orderRepo.CountOrdersByDateRange(startDate, endDate, OrderStatusCancelled)
	
	// 统计总金额
	totalAmount, err := s.orderRepo.SumOrderAmountByDateRange(startDate, endDate)
	if err != nil {
		logs.Warn("统计订单总金额失败: %v, 时间范围: %v - %v", err, startDate, endDate)
		totalAmount = 0
	}
	
	// 统计订单平均金额
	var avgOrderAmount float64
	if totalOrders > 0 {
		avgOrderAmount = totalAmount / float64(totalOrders)
	}
	
	// 统计平均配送时间（分钟）
	avgDeliveryTime, err := s.orderRepo.GetAverageDeliveryTimeByDateRange(startDate, endDate)
	if err != nil {
		logs.Warn("统计平均配送时间失败: %v, 时间范围: %v - %v", err, startDate, endDate)
		avgDeliveryTime = 0
	}
	
	// 统计每日订单数量趋势
	dailyOrderCounts, err := s.orderRepo.GetDailyOrderCountsByDateRange(startDate, endDate)
	if err != nil {
		logs.Warn("统计每日订单趋势失败: %v, 时间范围: %v - %v", err, startDate, endDate)
		dailyOrderCounts = make(map[string]int)
	}
	
	// 统计热门食品
	hotFoods, err := s.orderRepo.GetHotFoodsByDateRange(startDate, endDate, 10)
	if err != nil {
		logs.Warn("统计热门食品失败: %v, 时间范围: %v - %v", err, startDate, endDate)
		hotFoods = make([]*dto.HotFoodStatisticsDTO, 0)
	}
	
	// 转换每日订单趋势为DTO格式
	dailyTrends := make([]dto.DailyOrderTrendDTO, 0, len(dailyOrderCounts))
	for date, count := range dailyOrderCounts {
		dailyTrends = append(dailyTrends, dto.DailyOrderTrendDTO{
			Date:       date,
			OrderCount: count,
		})
	}
	
	// 返回统计结果
	return &dto.TakeoutOrderStatisticsDTO{
		StartDate:         startDate,
		EndDate:           endDate,
		TotalOrders:       totalOrders,
		PendingOrders:     pendingOrders,
		PaidOrders:        paidOrders,
		ProcessingOrders:  processingOrders,
		DeliveringOrders:  deliveringOrders,
		CompletedOrders:   completedOrders,
		CancelledOrders:   cancelledOrders,
		TotalAmount:       totalAmount,
		AvgOrderAmount:    avgOrderAmount,
		AvgDeliveryTime:   avgDeliveryTime,
		DailyOrderTrends:  dailyTrends,
		HotFoods:          hotFoods,
	}, nil
}
