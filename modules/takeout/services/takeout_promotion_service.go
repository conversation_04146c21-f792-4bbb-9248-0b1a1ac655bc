/**
 * 外卖模块 -
促销活动服务
 * 描述：定义促销活动相关的业务逻辑
 * 作者：系统
 * 创建时间：2025-05-14
*/

package services

import (
	"encoding/json"
	"errors"

	//"fmt"
	//"strconv"
	"time"

	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// ITakeoutPromotionService 促销活动服务接口
type ITakeoutPromotionService interface {
	// 创建促销活动
	CreatePromotion(req *dto.CreatePromotionRequest) (*models.TakeoutPromotion, error)

	// 更新促销活动
	UpdatePromotion(req *dto.UpdatePromotionRequest) error

	// 获取促销活动详情
	GetPromotionByID(id int64) (*dto.PromotionResponse, error)

	// 根据商户ID和促销活动ID获取促销活动详情
	GetPromotionByMerchantIDAndID(merchantID int64, id int64) (*dto.PromotionResponse, error)

	// 获取商户的促销活动列表
	GetPromotionsByMerchantID(merchantID int64, page, pageSize int) (*dto.PromotionListResponse, error)

	// 取消促销活动
	CancelPromotion(id int64, merchantID int64) error

	// 发布促销活动
	PublishPromotion(id int64, merchantID int64) error

	// 计算订单的优惠金额
	CalculateOrderDiscount(orderID int64, userID int64) (*dto.OrderDiscountInfo, error)

	// 获取促销活动统计信息
	GetStatistics(merchantID int64) (map[string]interface{}, error)

	// 根据条件获取符合的促销活动
	GetEligiblePromotions(merchantID int64, totalAmount float64, foodIDs []int64, userID int64) ([]dto.PromotionResponse, error)

	// 检查用户是否可以使用促销活动（考虑per_user_limit限制）
	CheckUserPromotionUsage(userID int64, promotionID int64, perUserLimit int64) (bool, error)

	// 记录用户使用促销活动
	RecordUserPromotionUsage(userID int64, promotionID int64, orderID int64, merchantID int64, discountAmount float64) error

	// 获取用户促销使用记录
	GetUserPromotionUsage(userID int64, promotionID int64) ([]*models.TakeoutUserPromotion, error)

	// 统计用户促销使用次数
	CountUserPromotionUsage(userID int64, promotionID int64) (int64, error)

	// 清除用户促销使用记录（仅用于测试）
	ClearUserPromotionUsage(userID int64, promotionID int64) error
}

// TakeoutPromotionService 促销活动服务实现
type TakeoutPromotionService struct {
	promotionRepo     repositories.ITakeoutPromotionRepository
	foodPromoRepo     repositories.ITakeoutFoodPromotionRepository
	foodRepo          repositories.TakeoutFoodRepository
	couponRepo        repositories.ITakeoutCouponRepository
	userCouponRepo    repositories.ITakeoutUserCouponRepository
	userPromotionRepo repositories.ITakeoutUserPromotionRepository
}

// NewTakeoutPromotionService 创建促销活动服务
func NewTakeoutPromotionService(
	promotionRepo repositories.ITakeoutPromotionRepository,
	foodPromoRepo repositories.ITakeoutFoodPromotionRepository,
	foodRepo repositories.TakeoutFoodRepository,
	couponRepo repositories.ITakeoutCouponRepository,
	userCouponRepo repositories.ITakeoutUserCouponRepository,
	userPromotionRepo repositories.ITakeoutUserPromotionRepository,
) ITakeoutPromotionService {
	return &TakeoutPromotionService{
		promotionRepo:     promotionRepo,
		foodPromoRepo:     foodPromoRepo,
		foodRepo:          foodRepo,
		couponRepo:        couponRepo,
		userCouponRepo:    userCouponRepo,
		userPromotionRepo: userPromotionRepo,
	}
}

// CreatePromotion 创建促销活动
func (s *TakeoutPromotionService) CreatePromotion(req *dto.CreatePromotionRequest) (*models.TakeoutPromotion, error) {
	// 解析时间 - 支持多种时间格式
	timeFormats := []string{
		"2006-01-02T15:04:05Z07:00", // ISO 8601格式，如：2025-06-20T05:21:54+08:00
		"2006-01-02 15:04:05",       // 标准格式，如：2025-06-20 05:21:54
		"2006-01-02T15:04:05",       // ISO格式无时区，如：2025-06-20T05:21:54
	}

	var startTime time.Time
	var parseErr error

	for _, format := range timeFormats {
		startTime, parseErr = time.Parse(format, req.StartTime)
		if parseErr == nil {
			break
		}
	}

	if parseErr != nil {
		return nil, errors.New("开始时间格式错误，支持格式：2025-06-20T05:21:54+08:00 或 2025-06-20 05:21:54")
	}

	var endTime time.Time

	for _, format := range timeFormats {
		endTime, parseErr = time.Parse(format, req.EndTime)
		if parseErr == nil {
			break
		}
	}

	if parseErr != nil {
		return nil, errors.New("结束时间格式错误，支持格式：2027-06-30T16:00:00+08:00 或 2027-06-30 16:00:00")
	}

	// 验证时间 - 允许今天的任意时间作为开始时间
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	if startTime.Before(today) {
		return nil, errors.New("开始时间不能早于今天")
	}

	if endTime.Before(startTime) {
		return nil, errors.New("结束时间不能早于开始时间")
	}

	// 验证活动类型
	if req.Type < 1 || req.Type > 5 {
		return nil, errors.New("无效的活动类型")
	}

	// 验证规则格式
	if req.Rules != "" {
		var rules interface{}
		if err := json.Unmarshal([]byte(req.Rules), &rules); err != nil {
			return nil, errors.New("活动规则格式错误，必须是有效的JSON")
		}
	}

	// 创建活动
	promotion := &models.TakeoutPromotion{
		MerchantID:    req.MerchantID,
		Name:          req.Name,
		Description:   req.Description,
		Type:          req.Type,
		StartTime:     startTime,
		EndTime:       endTime,
		Status:        models.PromotionStatusPending, // 默认为待发布状态
		Rules:         req.Rules,
		MaxUsageCount: req.MaxUsageCount,
		UsageCount:    0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 保存到数据库
	id, err := s.promotionRepo.Create(promotion)
	if err != nil {
		logs.Error("创建促销活动失败: %v", err)
		return nil, errors.New("创建促销活动失败")
	}

	promotion.ID = id
	return promotion, nil
}

// UpdatePromotion 更新促销活动
func (s *TakeoutPromotionService) UpdatePromotion(req *dto.UpdatePromotionRequest) error {
	// 获取活动信息
	promotion, err := s.promotionRepo.GetByID(req.ID)
	if err != nil {
		return errors.New("促销活动不存在")
	}

	// 验证商户
	if promotion.MerchantID != req.MerchantID {
		return errors.New("无权修改此促销活动")
	}

	// 验证活动状态，只有待发布和已取消状态才能修改
	if promotion.Status != models.PromotionStatusPending && promotion.Status != models.PromotionStatusCanceled {
		return errors.New("只有待发布或已取消状态的活动才能修改")
	}

	// 如果是已取消状态，编辑后自动改为待发布状态
	if promotion.Status == models.PromotionStatusCanceled {
		promotion.Status = models.PromotionStatusPending
		logs.Info("促销活动从已取消状态恢复为待发布状态，活动ID: %d", req.ID)
	}

	// 更新活动信息
	if req.Name != "" {
		promotion.Name = req.Name
	}

	if req.Description != "" {
		promotion.Description = req.Description
	}

	if req.StartTime != "" {
		// 支持多种时间格式
		timeFormats := []string{
			"2006-01-02T15:04:05Z07:00", // ISO 8601格式，如：2025-06-20T05:21:54+08:00
			"2006-01-02 15:04:05",       // 标准格式，如：2025-06-20 05:21:54
			"2006-01-02T15:04:05",       // ISO格式无时区，如：2025-06-20T05:21:54
		}

		var startTime time.Time
		var parseErr error

		for i, format := range timeFormats {
			if i == 0 {
				// 对于带时区的格式，使用time.Parse
				startTime, parseErr = time.Parse(format, req.StartTime)
			} else {
				// 对于不带时区的格式，使用time.ParseInLocation以本地时区解析
				startTime, parseErr = time.ParseInLocation(format, req.StartTime, time.Local)
			}
			if parseErr == nil {
				break
			}
		}

		if parseErr != nil {
			return errors.New("开始时间格式错误，支持格式：2025-06-20T05:21:54+08:00 或 2025-06-20 05:21:54")
		}

		// 对于已取消状态恢复的活动，需要重新验证时间
		now := time.Now()
		today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		if startTime.Before(today) {
			return errors.New("开始时间不能早于今天")
		}

		promotion.StartTime = startTime
	}

	if req.EndTime != "" {
		// 支持多种时间格式
		timeFormats := []string{
			"2006-01-02T15:04:05Z07:00", // ISO 8601格式，如：2027-06-30T16:00:00+08:00
			"2006-01-02 15:04:05",       // 标准格式，如：2027-06-30 16:00:00
			"2006-01-02T15:04:05",       // ISO格式无时区，如：2027-06-30T16:00:00
		}

		var endTime time.Time
		var parseErr error

		for i, format := range timeFormats {
			if i == 0 {
				// 对于带时区的格式，使用time.Parse
				endTime, parseErr = time.Parse(format, req.EndTime)
			} else {
				// 对于不带时区的格式，使用time.ParseInLocation以本地时区解析
				endTime, parseErr = time.ParseInLocation(format, req.EndTime, time.Local)
			}
			if parseErr == nil {
				break
			}
		}

		if parseErr != nil {
			return errors.New("结束时间格式错误，支持格式：2027-06-30T16:00:00+08:00 或 2027-06-30 16:00:00")
		}

		if endTime.Before(promotion.StartTime) {
			return errors.New("结束时间不能早于开始时间")
		}

		promotion.EndTime = endTime
	}

	if req.Rules != "" {
		// 验证规则格式
		var rules interface{}
		if err := json.Unmarshal([]byte(req.Rules), &rules); err != nil {
			return errors.New("活动规则格式错误，必须是有效的JSON")
		}

		promotion.Rules = req.Rules
	}

	if req.MaxUsageCount > 0 {
		promotion.MaxUsageCount = req.MaxUsageCount
	}

	promotion.UpdatedAt = time.Now()

	// 保存更新
	err = s.promotionRepo.Update(promotion)
	if err != nil {
		logs.Error("更新促销活动失败: %v", err)
		return errors.New("更新促销活动失败")
	}

	return nil
}

// GetPromotionByID 获取促销活动详情
func (s *TakeoutPromotionService) GetPromotionByID(id int64) (*dto.PromotionResponse, error) {
	// 获取活动信息
	promotion, err := s.promotionRepo.GetByID(id)
	if err != nil {
		return nil, errors.New("促销活动不存在")
	}

	// 转换为DTO
	response := dto.ConvertToPromotionResponse(promotion)
	return response, nil
}

// GetPromotionByMerchantIDAndID 根据商户ID和促销活动ID获取促销活动详情
func (s *TakeoutPromotionService) GetPromotionByMerchantIDAndID(merchantID int64, id int64) (*dto.PromotionResponse, error) {
	// 获取活动信息
	promotion, err := s.promotionRepo.GetByID(id)
	if err != nil {
		return nil, errors.New("促销活动不存在")
	}

	// 验证商户权限
	if promotion.MerchantID != merchantID {
		return nil, errors.New("无权访问此促销活动")
	}

	// 转换为DTO
	response := dto.ConvertToPromotionResponse(promotion)
	return response, nil
}

// GetPromotionsByMerchantID 获取商户的促销活动列表
func (s *TakeoutPromotionService) GetPromotionsByMerchantID(merchantID int64, page, pageSize int) (*dto.PromotionListResponse, error) {
	// 获取活动列表
	promotions, total, err := s.promotionRepo.GetByMerchantID(merchantID, page, pageSize)
	if err != nil {
		logs.Error("获取商户促销活动列表失败: %v", err)
		return nil, errors.New("获取商户促销活动列表失败")
	}

	// 转换为DTO
	responseList := make([]dto.PromotionResponse, 0)
	for _, promotion := range promotions {
		responseList = append(responseList, *dto.ConvertToPromotionResponse(promotion))
	}

	return &dto.PromotionListResponse{
		Total: int(total),
		List:  responseList,
	}, nil
}

// CancelPromotion 取消促销活动
func (s *TakeoutPromotionService) CancelPromotion(id int64, merchantID int64) error {
	// 获取活动信息
	promotion, err := s.promotionRepo.GetByID(id)
	if err != nil {
		return errors.New("促销活动不存在")
	}

	// 验证商户
	if promotion.MerchantID != merchantID {
		return errors.New("无权取消此促销活动")
	}

	// 验证活动状态
	if promotion.Status == models.PromotionStatusCanceled {
		return errors.New("该活动已经取消")
	}

	if promotion.Status == models.PromotionStatusEnded {
		return errors.New("已结束的活动不能取消")
	}

	// 更新活动状态
	err = s.promotionRepo.UpdateStatus(id, models.PromotionStatusCanceled)
	if err != nil {
		logs.Error("取消促销活动失败: %v", err)
		return errors.New("取消促销活动失败")
	}

	return nil
}

// PublishPromotion 发布促销活动
func (s *TakeoutPromotionService) PublishPromotion(id int64, merchantID int64) error {
	// 获取活动信息
	promotion, err := s.promotionRepo.GetByID(id)
	if err != nil {
		return errors.New("促销活动不存在")
	}

	// 验证商户
	if promotion.MerchantID != merchantID {
		return errors.New("无权发布此促销活动")
	}

	// 验证活动状态
	if promotion.Status != models.PromotionStatusPending {
		return errors.New("只有待发布状态的活动才能发布")
	}

	// 验证活动时间
	if promotion.StartTime.Before(time.Now()) {
		return errors.New("开始时间不能早于当前时间")
	}

	// 更新活动状态
	err = s.promotionRepo.UpdateStatus(id, models.PromotionStatusActive)
	if err != nil {
		logs.Error("发布促销活动失败: %v", err)
		return errors.New("发布促销活动失败")
	}

	return nil
}

// CalculateOrderDiscount 计算订单的优惠金额
func (s *TakeoutPromotionService) CalculateOrderDiscount(orderID int64, userID int64) (*dto.OrderDiscountInfo, error) {
	// 这里需要根据实际情况实现订单优惠计算逻辑
	// 由于涉及到订单详情，需要在具体的业务场景中实现
	// 这里提供一个基本框架
	return &dto.OrderDiscountInfo{
		OriginalAmount: 0,
		DiscountAmount: 0,
		FinalAmount:    0,
		CouponID:       0,
		PromotionIDs:   []int64{},
	}, nil
}

// GetStatistics 获取促销活动统计信息
func (s *TakeoutPromotionService) GetStatistics(merchantID int64) (map[string]interface{}, error) {
	// 从仓库层获取基本统计信息
	statistics, err := s.promotionRepo.GetStatistics(merchantID)
	if err != nil {
		logs.Error("获取促销活动统计信息失败: %v", err)
		return nil, errors.New("获取促销活动统计信息失败")
	}

	// 在这里可以添加更多的业务逻辑，例如计算更详细的统计数据
	// 例如：可以计算平均每个活动的使用次数，优惠券的使用率等

	// 计算平均每个活动的使用次数
	totalPromotions := statistics["total_promotions"].(int64)
	totalUsage := statistics["total_usage"].(int64)

	if totalPromotions > 0 {
		avgUsagePerPromotion := float64(totalUsage) / float64(totalPromotions)
		statistics["avg_usage_per_promotion"] = avgUsagePerPromotion
	} else {
		statistics["avg_usage_per_promotion"] = 0.0
	}

	// 计算总优惠金额（如果有相关数据模型支持的话）
	// 这里暂时使用一个模拟值，实际应该从数据库查询
	// 例如从订单表中统计所有使用了此商户促销活动的订单的优惠总额
	var totalDiscountAmount float64 = 0
	// 实际实现可能需要额外的数据查询，这里简化处理
	statistics["total_discount_amount"] = totalDiscountAmount

	return statistics, nil
}

// GetEligiblePromotions 根据条件获取符合的促销活动
func (s *TakeoutPromotionService) GetEligiblePromotions(merchantID int64, totalAmount float64, foodIDs []int64, userID int64) ([]dto.PromotionResponse, error) {
	// 检查商家ID是否有效
	if merchantID <= 0 {
		return nil, errors.New("无效的商家ID")
	}

	// 步骤1: 获取商家的所有有效促销活动
	// 在真实实现中，应该根据商家ID和活动状态进行筛选
	// 这里简化处理，直接使用分页查询的结果，然后在内存中进行过滤
	response, err := s.GetPromotionsByMerchantID(merchantID, 1, 100) // 获取前100个
	if err != nil {
		logs.Error("获取商家促销活动失败: %v", err)
		return nil, errors.New("获取商家促销活动失败")
	}

	// 活跃促销活动列表
	var activePromotions []dto.PromotionResponse

	// 当前时间
	now := time.Now()

	// 步骤2: 过滤出当前有效的促销活动
	for _, promotion := range response.List {
		// 检查状态 - 只保留进行中的活动
		if promotion.Status != models.PromotionStatusActive {
			continue
		}

		// 检查时间范围 - 当前时间应在开始和结束时间之间
		if now.Before(promotion.StartTime) || now.After(promotion.EndTime) {
			continue
		}

		// 添加到合格活动列表
		activePromotions = append(activePromotions, promotion)
	}

	// 步骤3: 根据条件过滤出符合的促销活动
	eligiblePromotions := []dto.PromotionResponse{}

	for _, promotion := range activePromotions {
		// 解析活动规则
		var rules map[string]interface{}
		err := json.Unmarshal([]byte(promotion.Rules), &rules)
		if err != nil {
			logs.Error("解析活动规则失败: %v, 活动ID: %d", err, promotion.ID)
			continue
		}

		// 检查用户使用次数限制
		var perUserLimit int64 = 0
		if coupon, ok := rules["coupon"].(map[string]interface{}); ok {
			if limit, exists := coupon["per_user_limit"]; exists {
				if limitFloat, ok := limit.(float64); ok {
					perUserLimit = int64(limitFloat)
				}
			}
		}

		// 如果设置了用户使用限制，检查用户是否还能使用
		if perUserLimit > 0 && userID > 0 {
			canUse, err := s.CheckUserPromotionUsage(userID, promotion.ID, perUserLimit)
			if err != nil {
				logs.Error("检查用户促销使用次数失败: %v, 促销ID: %d, 用户ID: %d", err, promotion.ID, userID)
				continue
			}
			if !canUse {
				logs.Info("用户已达到促销使用次数限制 - 促销ID: %d, 用户ID: %d, 限制: %d", promotion.ID, userID, perUserLimit)
				continue
			}
		}

		// 检查满减活动的订单金额条件
		if promotion.Type == models.PromotionTypeFull { // 满减活动
			// 解析优惠券规则
			if coupon, ok := rules["coupon"].(map[string]interface{}); ok {
				// 检查最低订单金额
				if minAmount, exists := coupon["min_order_amount"]; exists {
					minOrderAmount, ok := minAmount.(float64)
					if ok && totalAmount >= minOrderAmount {
						// 满足金额条件，添加到符合条件的促销活动列表
						eligiblePromotions = append(eligiblePromotions, promotion)
					}
				}
			}
		} else if promotion.Type == models.PromotionTypeProductDiscount { // 商品折扣
			// 检查是否含有指定商品
			if len(foodIDs) > 0 {
				// 在真实实现中，应该查询数据库确认这些商品是否参与了当前活动
				// 这里简化处理，假设所有商品折扣活动都符合条件
				eligiblePromotions = append(eligiblePromotions, promotion)
			}
		} else if promotion.Type == models.PromotionTypeFirstOrder { // 首单优惠
			// 首单优惠需要查询用户是否是新用户
			// 在实际实现中需要根据用户信息进行判断
			// 这里简化处理，将所有首单优惠都添加到符合条件列表
			eligiblePromotions = append(eligiblePromotions, promotion)
		} else {
			// 其他类型的活动，可根据需求添加更多判断逻辑
			eligiblePromotions = append(eligiblePromotions, promotion)
		}
	}

	// 返回符合条件的促销活动
	return eligiblePromotions, nil
}

// CheckUserPromotionUsage 检查用户是否可以使用促销活动（考虑per_user_limit限制）
func (s *TakeoutPromotionService) CheckUserPromotionUsage(userID int64, promotionID int64, perUserLimit int64) (bool, error) {
	// 如果没有设置每用户限制，则允许使用
	if perUserLimit <= 0 {
		return true, nil
	}

	// 统计用户已使用该促销活动的次数
	usedCount, err := s.userPromotionRepo.CountUserPromotionUsage(userID, promotionID)
	if err != nil {
		logs.Error("统计用户促销使用次数失败: %v", err)
		return false, err
	}

	// 检查是否超过限制
	canUse := usedCount < perUserLimit
	logs.Info("用户促销使用次数检查 - 用户ID: %d, 促销ID: %d, 已使用: %d, 限制: %d, 可使用: %v",
		userID, promotionID, usedCount, perUserLimit, canUse)

	return canUse, nil
}

// RecordUserPromotionUsage 记录用户使用促销活动
func (s *TakeoutPromotionService) RecordUserPromotionUsage(userID int64, promotionID int64, orderID int64, merchantID int64, discountAmount float64) error {
	// 创建用户促销使用记录
	userPromotion := &models.TakeoutUserPromotion{
		UserID:         userID,
		PromotionID:    promotionID,
		OrderID:        orderID,
		MerchantID:     merchantID,
		DiscountAmount: discountAmount,
		UsedTime:       time.Now(),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 保存记录
	_, err := s.userPromotionRepo.Create(userPromotion)
	if err != nil {
		logs.Error("记录用户促销使用失败: %v", err)
		return err
	}

	// 更新促销活动的使用次数
	err = s.promotionRepo.IncrementUsageCount(promotionID)
	if err != nil {
		logs.Error("更新促销活动使用次数失败: %v", err)
		// 这里不返回错误，因为用户记录已经创建成功
	}

	logs.Info("用户促销使用记录创建成功 - 用户ID: %d, 促销ID: %d, 订单ID: %d, 优惠金额: %.2f",
		userID, promotionID, orderID, discountAmount)

	return nil
}

// GetUserPromotionUsage 获取用户促销使用记录
func (s *TakeoutPromotionService) GetUserPromotionUsage(userID int64, promotionID int64) ([]*models.TakeoutUserPromotion, error) {
	return s.userPromotionRepo.GetUserPromotionUsage(userID, promotionID)
}

// CountUserPromotionUsage 统计用户促销使用次数
func (s *TakeoutPromotionService) CountUserPromotionUsage(userID int64, promotionID int64) (int64, error) {
	return s.userPromotionRepo.CountUserPromotionUsage(userID, promotionID)
}

// ClearUserPromotionUsage 清除用户促销使用记录（仅用于测试）
func (s *TakeoutPromotionService) ClearUserPromotionUsage(userID int64, promotionID int64) error {
	o := orm.NewOrm()

	// 删除用户的促销使用记录
	_, err := o.QueryTable(new(models.TakeoutUserPromotion)).
		Filter("user_id", userID).
		Filter("promotion_id", promotionID).
		Delete()

	if err != nil {
		logs.Error("清除用户促销使用记录失败: %v", err)
		return err
	}

	logs.Info("已清除用户ID %d 对促销活动ID %d 的使用记录", userID, promotionID)
	return nil
}
