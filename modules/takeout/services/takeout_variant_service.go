/**
 * 外卖规格变体服务
 *
 * 本文件实现了外卖食品规格变体的业务逻辑层，处理不同规格变体的创建、查询、更新等操作。
 * 支持如饮料大中小杯等不同规格的管理，与食品服务协同工作。
 */

package services

import (
	"errors"

	"github.com/beego/beego/v2/core/logs"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
)

// TakeoutVariantService 外卖规格变体服务接口
type TakeoutVariantService interface {
	// 基础CRUD操作
	CreateVariant(req *dto.CreateVariantRequest) (int64, error)
	GetVariantByID(id int64) (*dto.TakeoutFoodVariantDTO, error)
	UpdateVariant(req *dto.UpdateVariantRequest) error
	DeleteVariant(id int64) error
	
	// 查询列表
	ListVariantsByFoodID(foodID int64) ([]dto.TakeoutFoodVariantDTO, error)
	GetDefaultVariant(foodID int64) (*dto.TakeoutFoodVariantDTO, error)
	
	// 库存管理
	UpdateStock(id int64, count int) error

	// 商家端方法
	GetVariantsByFoodID(foodID int64) ([]dto.TakeoutFoodVariantDTO, error)
	GetVariantByIDForMerchant(id int64, merchantID int64) (*dto.TakeoutFoodVariantDTO, error)
	UpdateVariantForMerchant(req *dto.UpdateVariantRequest, merchantID int64) error
	DeleteVariantForMerchant(id int64, merchantID int64) error

	// 管理员方法
	GetVariantsByFoodIDForAdmin(foodID int64) ([]dto.TakeoutFoodVariantDTO, error)
	GetVariantByIDForAdmin(id int64) (*dto.TakeoutFoodVariantDTO, error)
	UpdateVariantForAdmin(req *dto.UpdateVariantRequest) error
	DeleteVariantForAdmin(id int64) error
}

// takeoutVariantService 外卖规格变体服务实现
type takeoutVariantService struct {
	variantRepo repositories.TakeoutVariantRepository
	foodRepo    repositories.TakeoutFoodRepository
}

// NewTakeoutVariantService 创建外卖规格变体服务实例
func NewTakeoutVariantService() TakeoutVariantService {
	return &takeoutVariantService{
		variantRepo: repositories.NewTakeoutVariantRepository(),
		foodRepo:    repositories.NewTakeoutFoodRepository(),
	}
}

// CreateVariant 创建规格变体
func (s *takeoutVariantService) CreateVariant(req *dto.CreateVariantRequest) (int64, error) {
	// 检查关联的食品是否存在
	food, err := s.foodRepo.GetByID(req.FoodID)
	if err != nil {
		return 0, err
	}
	
	// 创建规格变体
	variant := &models.TakeoutFoodVariant{
		FoodID:        req.FoodID,
		Name:          req.Name,
		Description:   req.Description,
		Image:         req.Image,
		Price:         req.Price,
		OriginalPrice: req.OriginalPrice,
		Stock:         req.Stock,
		IsDefault:     req.IsDefault,
		SortOrder:     req.SortOrder,
	}
	
	// 如果没有设置原价，则使用当前价格
	if variant.OriginalPrice <= 0 {
		variant.OriginalPrice = variant.Price
	}
	
	// 如果没有设置库存，默认为不限制
	if variant.Stock == 0 {
		variant.Stock = -1
	}
	
	// 保存到数据库
	id, err := s.variantRepo.Create(variant)
	if err != nil {
		logs.Error("创建规格变体失败: %v, 请求: %+v", err, req)
		return 0, errors.New("创建规格变体失败")
	}
	
	// 如果这是第一个规格变体，更新食品的HasVariants字段
	if !food.HasVariants {
		food.HasVariants = true
		err = s.foodRepo.Update(food)
		if err != nil {
			logs.Warn("更新食品HasVariants字段失败: %v, 食品ID: %d", err, food.ID)
			// 不影响变体创建结果
		}
	}
	
	// 如果设置为默认规格，需要取消其他规格的默认标记
	if variant.IsDefault {
		variants, err := s.variantRepo.ListByFoodID(req.FoodID)
		if err == nil {
			for _, v := range variants {
				if v.ID != id && v.IsDefault {
					v.IsDefault = false
					_ = s.variantRepo.Update(v)
				}
			}
		}
	}
	
	return id, nil
}

// GetVariantByID 根据ID获取规格变体
func (s *takeoutVariantService) GetVariantByID(id int64) (*dto.TakeoutFoodVariantDTO, error) {
	// 获取规格变体
	variant, err := s.variantRepo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	// 转换为DTO
	dto := &dto.TakeoutFoodVariantDTO{
		ID:            variant.ID,
		FoodID:        variant.FoodID,
		Name:          variant.Name,
		Description:   variant.Description,
		Image:         variant.Image,
		Price:         variant.Price,
		OriginalPrice: variant.OriginalPrice,
		Stock:         variant.Stock,
		SoldCount:     variant.SoldCount,
		IsDefault:     variant.IsDefault,
		SortOrder:     variant.SortOrder,
	}
	
	return dto, nil
}

// UpdateVariant 更新规格变体
func (s *takeoutVariantService) UpdateVariant(req *dto.UpdateVariantRequest) error {
	// 获取现有规格变体
	variant, err := s.variantRepo.GetByID(req.ID)
	if err != nil {
		return err
	}
	
	// 更新字段
	if req.Name != "" {
		variant.Name = req.Name
	}
	if req.Description != "" {
		variant.Description = req.Description
	}
	if req.Image != "" {
		variant.Image = req.Image
	}
	if req.Price > 0 {
		variant.Price = req.Price
	}
	if req.OriginalPrice > 0 {
		variant.OriginalPrice = req.OriginalPrice
	}
	
	// 库存特殊处理：可以设置为0或-1
	variant.Stock = req.Stock
	
	// 更新排序值
	variant.SortOrder = req.SortOrder
	
	// 保存到数据库
	err = s.variantRepo.Update(variant)
	if err != nil {
		logs.Error("更新规格变体失败: %v, ID: %d, 请求: %+v", err, req.ID, req)
		return errors.New("更新规格变体失败")
	}
	
	// 处理默认规格设置
	if req.IsDefault && !variant.IsDefault {
		variant.IsDefault = true
		err = s.variantRepo.Update(variant)
		if err != nil {
			logs.Error("设置默认规格失败: %v, ID: %d", err, req.ID)
			return errors.New("设置默认规格失败")
		}
		
		// 取消其他规格的默认标记
		variants, err := s.variantRepo.ListByFoodID(variant.FoodID)
		if err == nil {
			for _, v := range variants {
				if v.ID != req.ID && v.IsDefault {
					v.IsDefault = false
					_ = s.variantRepo.Update(v)
				}
			}
		}
	}
	
	return nil
}

// DeleteVariant 删除规格变体
func (s *takeoutVariantService) DeleteVariant(id int64) error {
	// 获取当前变体信息
	variant, err := s.variantRepo.GetByID(id)
	if err != nil {
		return err
	}
	
	// 删除变体
	err = s.variantRepo.Delete(id)
	if err != nil {
		logs.Error("删除规格变体失败: %v, ID: %d", err, id)
		return errors.New("删除规格变体失败")
	}
	
	// 如果删除的是默认变体，需要设置新的默认变体
	if variant.IsDefault {
		variants, err := s.variantRepo.ListByFoodID(variant.FoodID)
		if err == nil && len(variants) > 0 {
			// 设置第一个变体为默认
			variants[0].IsDefault = true
			_ = s.variantRepo.Update(variants[0])
		}
	}
	
	// 检查食品是否还有其他变体
	variants, err := s.variantRepo.ListByFoodID(variant.FoodID)
	if err == nil && len(variants) == 0 {
		// 如果没有其他变体，更新食品的HasVariants字段
		food, err := s.foodRepo.GetByID(variant.FoodID)
		if err == nil && food.HasVariants {
			food.HasVariants = false
			_ = s.foodRepo.Update(food)
		}
	}
	
	return nil
}

// ListVariantsByFoodID 根据食品ID查询规格变体列表
func (s *takeoutVariantService) ListVariantsByFoodID(foodID int64) ([]dto.TakeoutFoodVariantDTO, error) {
	// 获取规格变体列表
	variants, err := s.variantRepo.ListByFoodID(foodID)
	if err != nil {
		logs.Error("查询规格变体列表失败: %v, 食品ID: %d", err, foodID)
		return nil, errors.New("查询规格变体列表失败")
	}
	
	// 转换为DTO
	variantDTOs := make([]dto.TakeoutFoodVariantDTO, 0, len(variants))
	for _, v := range variants {
		variantDTOs = append(variantDTOs, dto.TakeoutFoodVariantDTO{
			ID:            v.ID,
			FoodID:        v.FoodID,
			Name:          v.Name,
			Description:   v.Description,
			Image:         v.Image,
			Price:         v.Price,
			OriginalPrice: v.OriginalPrice,
			Stock:         v.Stock,
			SoldCount:     v.SoldCount,
			IsDefault:     v.IsDefault,
			SortOrder:     v.SortOrder,
		})
	}
	
	return variantDTOs, nil
}

// GetDefaultVariant 获取默认规格变体
func (s *takeoutVariantService) GetDefaultVariant(foodID int64) (*dto.TakeoutFoodVariantDTO, error) {
	// 获取默认规格变体
	variant, err := s.variantRepo.GetDefaultVariant(foodID)
	if err != nil {
		return nil, err
	}
	
	// 转换为DTO
	dto := &dto.TakeoutFoodVariantDTO{
		ID:            variant.ID,
		FoodID:        variant.FoodID,
		Name:          variant.Name,
		Description:   variant.Description,
		Image:         variant.Image,
		Price:         variant.Price,
		OriginalPrice: variant.OriginalPrice,
		Stock:         variant.Stock,
		SoldCount:     variant.SoldCount,
		IsDefault:     variant.IsDefault,
		SortOrder:     variant.SortOrder,
	}
	
	return dto, nil
}

// UpdateStock 更新库存
func (s *takeoutVariantService) UpdateStock(id int64, count int) error {
	return s.variantRepo.UpdateStock(id, count)
}
