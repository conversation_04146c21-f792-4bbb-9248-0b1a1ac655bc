# 用户优惠券接口修复测试

## 问题描述
原来的 `/api/v1/user/takeout/merchants/promotions-coupons` 接口返回的是商家的优惠券模板信息，而不是用户自己的优惠券实例。

## 修复内容

### 1. 控制器层修改
- 在 `TakeoutFoodController.GetMerchantsPromotionsAndCoupons()` 方法中添加用户身份验证
- 获取用户ID并传递给服务层
- 更新接口注释，明确说明返回用户优惠券实例

### 2. 服务层修改
- 修改 `TakeoutFoodService` 接口，添加 `userID` 参数
- 修改 `GetMerchantsPromotionsAndCoupons` 方法实现：
  - 添加用户优惠券仓库实例
  - 获取用户的未使用优惠券实例（状态为1）
  - 筛选属于指定商家的用户优惠券
  - 使用 `dto.ConvertToUserCouponResponse` 转换数据格式

### 3. 修复前后对比

**修复前：**
```go
// 获取商家的可用优惠券模板
availableCoupons, err := couponRepo.GetValidCoupons(merchantID)
// 返回商家优惠券模板信息
```

**修复后：**
```go
// 获取用户在该商家的可用优惠券实例
userCoupons, coupons, _, err := userCouponRepo.GetUserCouponsWithDetail(userID, models.UserCouponStatusUnused, 1, 100)
// 筛选属于当前商家的用户优惠券
// 返回用户优惠券实例信息
```

## 测试验证

### 测试场景
1. 用户已领取某商家的优惠券
2. 调用 `/api/v1/user/takeout/merchants/promotions-coupons` 接口
3. 验证返回的是用户自己的优惠券实例，而不是商家的优惠券模板

### 预期结果
- 接口返回用户在指定商家的优惠券实例
- 包含用户优惠券的状态、领取时间等用户相关信息
- 不再返回商家的优惠券模板信息

### API 请求示例
```json
POST /api/v1/user/takeout/merchants/promotions-coupons
Headers: {
  "Authorization": "Bearer <user_token>"
}
Body: {
  "merchant_ids": [1, 2, 3]
}
```

### 预期响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "merchant_id": 1,
      "merchant_name": "商家名称",
      "promotions": [...],
      "coupons": [
        {
          "id": 123,           // 用户优惠券实例ID
          "coupon_id": 456,    // 优惠券模板ID
          "user_id": 789,      // 用户ID
          "status": 1,         // 用户优惠券状态（1=未使用）
          "status_text": "未使用",
          "created_at": "2025-01-01T00:00:00Z", // 用户领取时间
          "coupon": {
            "id": 456,
            "name": "满100减20",
            "description": "满100元减20元",
            "type": 1,
            "amount": 20.0,
            "min_order_amount": 100.0
            // ... 其他优惠券详情
          }
        }
      ],
      "has_promotion": true,
      "has_coupon": true
    }
  ]
}
```

## 注意事项
1. 确保用户身份验证正常工作
2. 验证用户优惠券状态筛选逻辑
3. 确认商家ID筛选逻辑正确
4. 测试边界情况（用户无优惠券、商家不存在等）