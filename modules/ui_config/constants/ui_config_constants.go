package constants

// UI配置类型常量
const (
	// ConfigTypeForm 表单配置类型
	ConfigTypeForm = "form"

	// ConfigTypeTable 表格配置类型
	ConfigTypeTable = "table"

	// ConfigTypeService 服务配置类型
	ConfigTypeService = "service"

	// ConfigTypeInfo 详情配置类型
	ConfigTypeInfo = "info"

	// ConfigTypeSearch 搜索配置类型
	ConfigTypeSearch = "search"

	// ConfigTypeTools 工具栏配置类型
	ConfigTypeTools = "tools"

	// ConfigTypePage 页面配置类型
	ConfigTypePage = "page"
)

// UI配置状态常量
const (
	// StatusDisabled 禁用状态
	StatusDisabled = 0

	// StatusEnabled 启用状态
	StatusEnabled = 1
)

// 错误消息常量
const (
	// ErrConfigNotFound 配置不存在
	ErrConfigNotFound = "配置不存在"

	// ErrConfigKeyExists 配置键已存在
	ErrConfigKeyExists = "配置键已存在"

	// ErrConfigDisabled 配置已禁用
	ErrConfigDisabled = "配置已禁用"

	// ErrInvalidJSON JSON格式无效
	ErrInvalidJSON = "JSON格式无效"

	// ErrInvalidConfigType 不支持的配置类型
	ErrInvalidConfigType = "不支持的配置类型"

	// ErrFormMissingSchema 表单配置缺少schema字段
	ErrFormMissingSchema = "表单配置缺少必要字段: schema"

	// ErrFormMissingUISchema 表单配置缺少uiSchema字段
	ErrFormMissingUISchema = "表单配置缺少必要字段: uiSchema"

	// ErrTableMissingColumnDefs 表格配置缺少columnDefs字段
	ErrTableMissingColumnDefs = "表格配置缺少必要字段: columnDefs"
)
