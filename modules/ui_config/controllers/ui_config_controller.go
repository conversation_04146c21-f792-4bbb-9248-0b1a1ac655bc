package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	uiConfigDto "o_mall_backend/modules/ui_config/dto"
	"o_mall_backend/modules/ui_config/services"
	"o_mall_backend/modules/ui_config/services/impl"
	"o_mall_backend/utils/common"
)

// UIConfigController 控制UI配置的控制器
type UIConfigController struct {
	web.Controller
	UIConfigService services.UIConfigService
}

// Prepare 初始化控制器
func (c *UIConfigController) Prepare() {
	// 初始化服务
	c.UIConfigService = impl.NewUIConfigService()
}

// ParseRequest 通用请求参数解析方法
func (c *UIConfigController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// CreateUIConfig 创建UI配置
// @Title 创建UI配置
// @Description 创建新的UI配置
// @Param   body    body    uiConfigDto.UIConfigCreateDTO  true    "UI配置创建信息"
// @Success 200 {object} dto.Response 成功返回创建的配置ID
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs [post]
func (c *UIConfigController) CreateUIConfig() {
	// 打印原始请求数据
	logs.Debug("[CreateUIConfig] 收到原始请求数据: %s", string(c.Ctx.Input.RequestBody))

	// 解析请求参数
	var createDTO uiConfigDto.UIConfigCreateDTO
	err := c.ParseRequest(&createDTO)
	if err != nil {
		logs.Error("[CreateUIConfig] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 创建UI配置
	id, err := c.UIConfigService.CreateUIConfig(&createDTO)
	if err != nil {
		logs.Error("[CreateUIConfig] 创建UI配置失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, id)
}

// UpdateUIConfig 更新UI配置
// @Title 更新UI配置
// @Description 更新现有的UI配置
// @Param   id      path    int                     true    "UI配置ID"
// @Param   body    body    uiConfigDto.UIConfigUpdateDTO   true    "UI配置更新信息"
// @Success 200 {object} dto.Response 成功返回true
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 404 {object} dto.Response 配置不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs/:id [put]
func (c *UIConfigController) UpdateUIConfig() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[UpdateUIConfig] ID参数无效: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 解析请求参数
	var updateDTO uiConfigDto.UIConfigUpdateDTO
	err = c.ParseRequest(&updateDTO)
	if err != nil {
		logs.Error("[UpdateUIConfig] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 更新UI配置
	err = c.UIConfigService.UpdateUIConfig(id, &updateDTO)
	if err != nil {
		logs.Error("[UpdateUIConfig] 更新UI配置失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, true)
}

// DeleteUIConfig 删除UI配置
// @Title 删除UI配置
// @Description 删除指定的UI配置
// @Param   id      path    int     true    "UI配置ID"
// @Success 200 {object} dto.Response 成功返回true
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 404 {object} dto.Response 配置不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs/:id [delete]
func (c *UIConfigController) DeleteUIConfig() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[DeleteUIConfig] ID参数无效: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 删除UI配置
	err = c.UIConfigService.DeleteUIConfig(id)
	if err != nil {
		logs.Error("[DeleteUIConfig] 删除UI配置失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, true)
}

// GetUIConfig 获取UI配置详情
// @Title 获取UI配置详情
// @Description 获取指定ID的UI配置详情
// @Param   id      path    int     true    "UI配置ID"
// @Success 200 {object} dto.Response 成功返回UI配置详情
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 404 {object} dto.Response 配置不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs/:id [get]
func (c *UIConfigController) GetUIConfig() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[GetUIConfig] ID参数无效: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 获取UI配置
	config, err := c.UIConfigService.GetUIConfig(id)
	if err != nil {
		logs.Error("[GetUIConfig] 获取UI配置失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, config)
}

// GetUIConfigByKey 根据Key获取UI配置
// @Title 根据Key获取UI配置
// @Description 根据配置键获取UI配置
// @Param   key     query   string  true    "配置键"
// @Success 200 {object} dto.Response 成功返回UI配置
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 404 {object} dto.Response 配置不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs/key [get]
func (c *UIConfigController) GetUIConfigByKey() {
	// 获取查询参数
	key := c.GetString("key")
	if key == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 根据Key获取UI配置
	config, err := c.UIConfigService.GetUIConfigByKey(key)
	if err != nil {
		logs.Error("[GetUIConfigByKey] 获取UI配置失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, config)
}

// ListUIConfigs 分页查询UI配置
// @Title 分页查询UI配置
// @Description 根据条件分页查询UI配置
// @Param   frontendPath    query   string  false   "前端路径"
// @Param   configType      query   string  false   "配置类型：form, table"
// @Param   configKey       query   string  false   "配置键"
// @Param   status          query   int     false   "状态：1-启用，0-禁用"
// @Param   page            query   int     false   "页码，默认1"
// @Param   pageSize        query   int     false   "每页数量，默认10"
// @Success 200 {object} dto.Response 成功返回UI配置列表和总数
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs [get]
func (c *UIConfigController) ListUIConfigs() {
	// 构造查询DTO
	queryDTO := &uiConfigDto.UIConfigQueryDTO{
		FrontendPath: c.GetString("frontendPath"),
		ConfigType:   c.GetString("configType"),
		ConfigKey:    c.GetString("config_key"),
		Module:       c.GetString("module"),
		Group:        c.GetString("group"),
		Page:         c.GetIntWithDefault("page", 1),
		PageSize:     c.GetIntWithDefault("pageSize", 10),
	}

	// 转换status参数
	statusStr := c.GetString("status")
	if statusStr != "" {
		status, err := strconv.Atoi(statusStr)
		if err == nil {
			queryDTO.Status = status
		}
	} else {
		queryDTO.Status = -1 // 表示不按状态筛选
	}

	logs.Debug("[ListUIConfigs] 查询参数: %+v", queryDTO)

	// 查询UI配置
	configs, total, err := c.UIConfigService.ListUIConfigs(queryDTO)
	if err != nil {
		logs.Error("[ListUIConfigs] 查询UI配置失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	logs.Debug("[ListUIConfigs] 查询到 %d 条配置数据", len(configs))

	// 使用ListUIConfigsResponse包装结果
	response := &uiConfigDto.ListUIConfigsResponse{
		List:  configs,
		Total: total,
	}

	// 确保所有配置项都有grid_items字段
	response.EnsureGridItemsField()

	// 序列化结果进行检查
	for i, config := range configs {
		logs.Debug("[ListUIConfigs] 配置项 #%d (ID=%d) 的 GridItems 是否为nil: %v, 长度: %d",
			i, config.ID, config.GridItems == nil, len(config.GridItems))
	}

	result.OKWithPagination(c.Ctx, configs, total, queryDTO.Page, queryDTO.PageSize)
}

// GetConfigByPath 根据前端路径获取所有相关配置
// @Title 根据前端路径获取配置
// @Description 根据前端路径获取所有相关配置
// @Param   path    query   string  true    "前端路径"
// @Success 200 {object} dto.Response 成功返回UI配置列表
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs/path [get]
func (c *UIConfigController) GetConfigByPath() {
	// 获取查询参数
	path := c.GetString("path")
	if path == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 根据路径获取配置
	configs, err := c.UIConfigService.GetConfigByPath(path)
	if err != nil {
		logs.Error("[GetConfigByPath] 获取UI配置失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, configs)
}

// UpdateVersion 更新配置版本号
// @Title 更新配置版本号
// @Description 更新指定UI配置的版本号
// @Param   id      path    int     true    "UI配置ID"
// @Success 200 {object} dto.Response 成功返回新的版本号
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 404 {object} dto.Response 配置不存在
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs/:id/version [put]
func (c *UIConfigController) UpdateVersion() {
	// 获取路径参数
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logs.Error("[UpdateVersion] ID参数无效: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParams)
		return
	}

	// 更新版本号
	version, err := c.UIConfigService.UpdateVersion(id)
	if err != nil {
		logs.Error("[UpdateVersion] 更新版本号失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, version)
}

// GetFrontendPaths 获取所有前端路径分组列表
// @Title 获取前端路径分组列表
// @Description 获取按模块分组的前端路径列表
// @Success 200 {object} dto.Response 成功返回前端路径分组列表
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /ui-configs/frontend-paths [get]
func (c *UIConfigController) GetFrontendPaths() {
	// 获取前端路径分组列表
	paths, err := c.UIConfigService.GetFrontendPaths()
	if err != nil {
		logs.Error("[GetFrontendPaths] 获取前端路径分组列表失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	result.OK(c.Ctx, paths)
}

// 获取带有默认值的整数参数
func (c *UIConfigController) GetIntWithDefault(key string, def int) int {
	valStr := c.GetString(key)
	if valStr == "" {
		return def
	}

	val, err := strconv.Atoi(valStr)
	if err != nil {
		return def
	}

	return val
}
