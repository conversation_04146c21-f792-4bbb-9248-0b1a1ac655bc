/**
 * 公共DTO定义
 *
 * 该文件定义了被其他DTO文件共同引用的数据结构，防止循环引用
 */

package dto

// SimpleUIConfigDTO 简化版的UI配置DTO，用于防止循环引用
type SimpleUIConfigDTO struct {
	ID           int    `json:"id"`           // 配置ID
	FrontendPath string `json:"frontend_path"` // 前端路径
	ConfigType   string `json:"config_type"`   // 配置类型
	ConfigKey    string `json:"config_key"`    // 配置唯一标识
	Module       string `json:"module"`        // 模块名称
	Title        string `json:"title"`         // 配置标题
	Status       int    `json:"status"`        // 状态
}

// SimpleGridInfoDTO 简化版的网格布局DTO，用于防止循环引用
type SimpleGridInfoDTO struct {
	ID       int         `json:"id"`       // 网格布局ID
	Name     string      `json:"name"`     // 组件名称
	Position interface{} `json:"position"` // 位置信息
	Status   int         `json:"status"`   // 状态
}
