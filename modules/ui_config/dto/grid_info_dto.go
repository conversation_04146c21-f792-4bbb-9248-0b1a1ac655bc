/**
 * GridInfo DTO
 *
 * 该文件定义了用于前端交互的网格布局配置数据传输对象
 */

package dto

import "time"

// PositionInfoDTO 位置信息DTO，用于Position字段的JSON序列化和反序列化
type PositionInfoDTO struct {
	// UI最后位置信息
	LastUiPosition *LastUiPositionDTO `json:"_lastUiPosition,omitempty"` // 组件在UI中的最后位置
}

// LastUiPositionDTO UI位置信息DTO，用于记录组件在UI中的精确位置
type LastUiPositionDTO struct {
	Left float64 `json:"left"` // 左侧位置
	Top  float64 `json:"top"`  // 顶部位置
}

// GridInfoDTO 用于前端交互的网格布局数据传输对象
type GridInfoDTO struct {
	ID         int         `json:"id"`           // 网格布局ID
	UIConfigID int         `json:"ui_config_id"` // 关联的UI配置ID
	Name       string      `json:"name"`         // 组件名称
	Content    string      `json:"content"`      // 组件内容配置，JSON格式
	Position   interface{} `json:"position"`     // 位置信息，包含x,y,w,h等自定义字段
	Step       interface{} `json:"step"`         // 步骤，数字数组，如[0]或[1,2,3]
	Permission []int       `json:"permission"`   // 权限ID列表
	API        string      `json:"api"`          // 数据API地址
	DTO        string      `json:"dto"`          // DTO配置，JSON格式
	Remark     string      `json:"remark"`       // 备注说明
	Status     int         `json:"status"`       // 状态：1-启用，0-禁用
	CreatedAt  time.Time   `json:"created_at"`   // 创建时间
	UpdatedAt  time.Time   `json:"updated_at"`   // 更新时间
}

// GridInfoResponseDTO 用于前端响应的网格布局数据传输对象
type GridInfoResponseDTO struct {
	ID         int         `json:"id"`           // 网格布局ID
	UIConfigID int         `json:"ui_config_id"` // 关联的UI配置ID
	Name       string      `json:"name"`         // 组件名称
	Content    interface{} `json:"content"`      // 解析后的组件内容配置
	Position   interface{} `json:"position"`     // 位置信息，包含x,y,w,h等自定义字段
	// 以下字段保留用于向后兼容，但在新代码中应使用Position字段
	// X            int         `json:"x"`             // X坐标
	// Y            int         `json:"y"`             // Y坐标
	// W            int         `json:"w"`             // 宽度
	// H            int         `json:"h"`             // 高度
	// MinW         int         `json:"min_w"`         // 最小宽度
	// MinH         int         `json:"min_h"`         // 最小高度
	// MaxW         int         `json:"max_w"`         // 最大宽度
	// MaxH         int         `json:"max_h"`         // 最大高度
	// Locked       bool        `json:"locked"`        // 是否锁定
	// NoMove       bool        `json:"no_move"`       // 禁止移动
	// NoResize     bool        `json:"no_resize"`     // 禁止调整大小
	// AutoPosition bool        `json:"auto_position"` // 自动定位
	API    string      `json:"api"`    // 数据API地址
	DTO    interface{} `json:"dto"`    // 解析后的DTO数据
	Remark string      `json:"remark"` // 备注说明
	Status int         `json:"status"` // 状态：1-启用，0-禁用
}

// GridInfoCreateDTO 用于创建网格布局的数据传输对象
type GridInfoCreateDTO struct {
	UIConfigID int    `json:"ui_config_id" valid:"Required"` // 关联的UI配置ID，必填
	Name       string `json:"name" valid:"Required"`         // 组件名称，必填
	Content    string `json:"content" valid:"Required"`      // 组件内容配置，JSON格式，必填
	Position   string `json:"position"`                      // 位置信息，JSON格式，包含x,y,w,h等自定义字段
	// 以下字段保留用于向后兼容，但在新代码中应使用Position字段
	// X            int    `json:"x"`                         // X坐标
	// Y            int    `json:"y"`                         // Y坐标
	// W            int    `json:"w" valid:"Required"`        // 宽度，必填
	// H            int    `json:"h" valid:"Required"`        // 高度，必填
	// MinW         int    `json:"min_w"`                     // 最小宽度
	// MinH         int    `json:"min_h"`                     // 最小高度
	// MaxW         int    `json:"max_w"`                     // 最大宽度
	// MaxH         int    `json:"max_h"`                     // 最大高度
	// Locked       bool   `json:"locked"`                    // 是否锁定
	// NoMove       bool   `json:"no_move"`                   // 禁止移动
	// NoResize     bool   `json:"no_resize"`                 // 禁止调整大小
	// AutoPosition bool   `json:"auto_position"`             // 自动定位
	API    string `json:"api"`                       // 数据API地址
	DTO    string `json:"dto"`                       // DTO配置，JSON格式
	Remark string `json:"remark"`                    // 备注说明
	Status int    `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
}

// GridInfoUpdateDTO 用于更新网格布局的数据传输对象
type GridInfoUpdateDTO struct {
	Name     string `json:"name"`     // 组件名称
	Content  string `json:"content"`  // 组件内容配置，JSON格式
	Position string `json:"position"` // 位置信息，JSON格式，包含x,y,w,h等自定义字段
	// 以下字段保留用于向后兼容，但在新代码中应使用Position字段
	// X            int    `json:"x"`                         // X坐标
	// Y            int    `json:"y"`                         // Y坐标
	// W            int    `json:"w"`                         // 宽度
	// H            int    `json:"h"`                         // 高度
	// MinW         int    `json:"min_w"`                     // 最小宽度
	// MinH         int    `json:"min_h"`                     // 最小高度
	// MaxW         int    `json:"max_w"`                     // 最大宽度
	// MaxH         int    `json:"max_h"`                     // 最大高度
	// Locked       bool   `json:"locked"`                    // 是否锁定
	// NoMove       bool   `json:"no_move"`                   // 禁止移动
	// NoResize     bool   `json:"no_resize"`                 // 禁止调整大小
	// AutoPosition bool   `json:"auto_position"`             // 自动定位
	API    string `json:"api"`                       // 数据API地址
	DTO    string `json:"dto"`                       // DTO配置，JSON格式
	Remark string `json:"remark"`                    // 备注说明
	Status int    `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
}

// GridInfoQueryDTO 用于查询网格布局的数据传输对象
type GridInfoQueryDTO struct {
	UIConfigID int    `json:"ui_config_id" form:"ui_config_id"` // 关联的UI配置ID
	Name       string `json:"name" form:"name"`                 // 组件名称
	Status     int    `json:"status" form:"status"`             // 状态：1-启用，0-禁用
	Page       int    `json:"page" form:"page"`                 // 页码
	PageSize   int    `json:"pageSize" form:"pageSize"`         // 每页数量
}

// GridInfoBatchUpdatePositionDTO 批量更新网格布局位置的数据传输对象
type GridInfoBatchUpdatePositionDTO struct {
	Items []GridInfoPositionItem `json:"items" valid:"Required"` // 要更新的网格布局项目列表
}

// GridInfoPositionItem 网格布局位置项
type GridInfoPositionItem struct {
	ID int `json:"id" valid:"Required"` // 网格布局ID
	X  int `json:"x"`                   // X坐标
	Y  int `json:"y"`                   // Y坐标
	W  int `json:"w"`                   // 宽度
	H  int `json:"h"`                   // 高度
}

// GridInfoRepositoryDTO 仓库层使用的网格布局数据传输对象
type GridInfoRepositoryDTO struct {
	ID          int         `json:"id"`                      // 网格布局ID
	UIConfigID  int         `json:"ui_config_id,omitempty"`  // 关联的UI配置ID
	UIConfigIDs []int       `json:"ui_config_ids,omitempty"` // 关联的UI配置ID列表
	Position    interface{} `json:"position,omitempty"`      // 位置信息，JSON格式
	Name        string      `json:"name"`                    // 组件名称
	Content     interface{} `json:"content"`                 // 组件内容配置
	// X            int            `json:"x"`                         // X坐标(已废弃)
	// Y            int            `json:"y"`                         // Y坐标(已废弃)
	// W            int            `json:"w"`                         // 宽度(已废弃)
	// H            int            `json:"h"`                         // 高度(已废弃)
	// MinW         int            `json:"min_w"`                     // 最小宽度(已废弃)
	// MinH         int            `json:"min_h"`                     // 最小高度(已废弃)
	// MaxW         int            `json:"max_w"`                     // 最大宽度(已废弃)
	// MaxH         int            `json:"max_h"`                     // 最大高度(已废弃)
	// Locked       bool           `json:"locked"`                    // 是否锁定(已废弃)
	// NoMove       bool           `json:"no_move"`                   // 禁止移动(已废弃)
	// NoResize     bool           `json:"no_resize"`                 // 禁止调整大小(已废弃)
	// AutoPosition bool           `json:"auto_position"`             // 自动定位(已废弃)
	API       string               `json:"api"`                  // 数据API地址
	DTO       interface{}          `json:"dto"`                  // DTO配置
	Remark    string               `json:"remark"`               // 备注说明
	Status    int                  `json:"status"`               // 状态：1-启用，0-禁用
	CreatedAt time.Time            `json:"created_at"`           // 创建时间
	UpdatedAt time.Time            `json:"updated_at"`           // 更新时间
	UIConfigs []*SimpleUIConfigDTO `json:"ui_configs,omitempty"` // 关联的UI配置
}

// GridInfoCreateParams 仓库层网格布局创建参数
type GridInfoCreateParams struct {
	UIConfigID  int    `json:"ui_config_id,omitempty"`
	UIConfigIDs []int  `json:"ui_config_ids,omitempty"`
	Name        string `json:"name"`
	Content     string `json:"content"`
	Position    string `json:"position"`
	// X            int    `json:"x"`
	// Y            int    `json:"y"`
	// W            int    `json:"w"`
	// H            int    `json:"h"`
	// MinW         int    `json:"min_w"`
	// MinH         int    `json:"min_h"`
	// MaxW         int    `json:"max_w"`
	// MaxH         int    `json:"max_h"`
	// Locked       bool   `json:"locked"`
	// NoMove       bool   `json:"no_move"`
	// NoResize     bool   `json:"no_resize"`
	// AutoPosition bool   `json:"auto_position"`
	API    string `json:"api"`
	DTO    string `json:"dto"`
	Remark string `json:"remark"`
	Status int    `json:"status"`
}

// GridInfoUpdateParams 仓库层网格布局更新参数
type GridInfoUpdateParams struct {
	UIConfigID  int    `json:"ui_config_id"`       // 单个UI配置ID
	UIConfigIDs []int  `json:"ui_config_ids,omitempty"`
	Name        string `json:"name"`
	Content     string `json:"content"`
	Position    string `json:"position"`
	Step        string `json:"step"`          // 步骤，JSON数组格式，如[0]或[1,2,3]
	Permission  string `json:"permission"`    // 权限IDs，JSON格式字符串
	API         string `json:"api"`
	DTO         string `json:"dto"`
	Remark      string `json:"remark"`
	Status      int    `json:"status"`
}

// GridInfoRepositoryPositionItem 仓库层网格布局位置项目
type GridInfoRepositoryPositionItem struct {
	ID         int    `json:"id"`         // 网格布局ID
	UIConfigID int    `json:"uiConfigId"` // 关联的UI配置ID
	Position   string `json:"position"`   // 位置信息，JSON格式
}
