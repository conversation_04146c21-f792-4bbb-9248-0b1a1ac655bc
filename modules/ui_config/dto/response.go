/**
 * 响应数据处理模块
 *
 * 该文件包含处理UI配置模块响应数据的结构体和方法
 */

package dto

// ListUIConfigsResponse 列表查询响应结构体
type ListUIConfigsResponse struct {
	List  []*UIConfigDTO `json:"list"`  // 配置列表
	Total int64          `json:"total"` // 总数
}

// EnsureGridItemsField 确保每个ListUIConfigsResponse对象中的所有UIConfigDTO都有grid_items字段
func (r *ListUIConfigsResponse) EnsureGridItemsField() {
	for _, config := range r.List {
		if config.GridItems == nil {
			config.GridItems = make([]*GridInfoDTO, 0)
		}
	}
}
