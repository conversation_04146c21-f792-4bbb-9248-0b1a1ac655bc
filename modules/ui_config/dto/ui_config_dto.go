/**
 * UI配置DTO
 *
 * 该文件定义了用于前端交互的UI配置数据传输对象
 */

package dto

import (
	"encoding/json"
	"time"
)

// UIConfigDTO 用于前端交互的UI配置数据传输对象
type UIConfigDTO struct {
	ID            int            `json:"id"`             // 配置ID
	FrontendPath  string         `json:"frontend_path"`  // 前端路径
	VersionHash   string         `json:"version_hash"`   // 版本识别号
	ConfigType    string         `json:"config_type"`    // 配置类型：service, form, table, info, search，tools
	ConfigKey     string         `json:"config_key"`     // 配置唯一标识
	ConfigContent string         `json:"config_content"` // JSON格式的配置内容
	Status        int            `json:"status"`         // 状态：1-启用，0-禁用
	Remark        string         `json:"remark"`         // 备注说明
	CreatedAt     time.Time      `json:"created_at"`     // 创建时间
	UpdatedAt     time.Time      `json:"updated_at"`     // 更新时间
	Module        string         `json:"module"`         // 模块名称
	Title         string         `json:"title"`          // 配置标题
	Group         string         `json:"group"`          // 配置分组
	Version       string         `json:"version"`        // 配置版本
	Icon          string         `json:"icon"`           // 图标名称
	DTO           string         `json:"dto"`            // DTO数据，JSON格式
	Draggable    bool           `json:"draggable"`    // 是否可拖拽
	Resizable    bool           `json:"resizable"`    // 是否可调整大小
	GridItems     []*GridInfoDTO `json:"grid_items"`     // 关联的网格布局项目
}

// UIConfigCreateDTO 用于创建UI配置的数据传输对象
type UIConfigCreateDTO struct {
	FrontendPath  string `json:"frontend_path" valid:"Required"`                                                    // 前端路径，必填
	ConfigType    string `json:"config_type" valid:"Required;Match(^(service|form|table|info|search|tools|page)$)"` // 配置类型：service, form, table, info, search，tools，必填
	ConfigKey     string `json:"config_key" valid:"Required"`                                                       // 配置唯一标识，必填
	ConfigContent string `json:"config_content" valid:"Required"`                                                   // JSON格式的配置内容，必填
	Status        int    `json:"status" valid:"Range(0,1)"`                                                         // 状态：1-启用，0-禁用
	Remark        string `json:"remark"`                                                                            // 备注说明
	Module        string `json:"module" valid:"Required"`                                                           // 模块名称，必填
	Title         string `json:"title" valid:"Required"`                                                            // 配置标题，必填
	Group         string `json:"group"`                                                                             // 配置分组
	Version       string `json:"version" valid:"Required"`                                                          // 配置版本，必填
	Icon          string `json:"icon"`                                                                              // 图标名称
	DTO           string `json:"dto"`                                                                               // DTO数据，JSON格式
	Draggable    bool   `json:"draggable"`    // 是否可拖拽
	Resizable    bool   `json:"resizable"`    // 是否可调整大小
	Step          int    `json:"step"`                                                                              // 步骤
	Permission    []int  `json:"permission"`                                                                        // 权限ID列表
}

// UIConfigUpdateDTO 用于更新UI配置的数据传输对象
type UIConfigUpdateDTO struct {
	FrontendPath  string `json:"frontend_path"`             // 前端路径
	ConfigContent string `json:"config_content"`            // JSON格式的配置内容
	Status        int    `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
	Remark        string `json:"remark"`                    // 备注说明
	Title         string `json:"title"`                     // 配置标题
	Group         string `json:"group"`                     // 配置分组
	Version       string `json:"version"`                   // 配置版本
	Icon          string `json:"icon"`                      // 图标名称
	DTO           string `json:"dto"`                       // DTO数据，JSON格式
	Draggable    bool   `json:"draggable"`    // 是否可拖拽
	Resizable    bool   `json:"resizable"`    // 是否可调整大小
}

// UIConfigQueryDTO 用于查询UI配置的数据传输对象
type UIConfigQueryDTO struct {
	FrontendPath string `json:"frontend_path" form:"frontend_path"` // 前端路径
	ConfigType   string `json:"config_type" form:"config_type"`     // 配置类型：service, form, table, info, search，tools
	ConfigKey    string `json:"config_key" form:"config_key"`       // 配置唯一标识
	Status       int    `json:"status" form:"status"`               // 状态：1-启用，0-禁用
	Module       string `json:"module" form:"module"`               // 模块名称
	Group        string `json:"group" form:"group"`                 // 配置分组
	Page         int    `json:"page" form:"page"`                   // 页码
	PageSize     int    `json:"pageSize" form:"pageSize"`           // 每页数量
	VersionHash  string `json:"version_hash" form:"version_hash"`   // 版本号
}

// UIConfigResponseDTO 前端获取UI配置的响应对象
type UIConfigResponseDTO struct {
	FrontendPath  string      `json:"frontend_path"`  // 前端路径
	VersionHash   string      `json:"version_hash"`   // 版本识别号
	ConfigType    string      `json:"config_type"`    // 配置类型：service, form, table, info, search，tools
	ConfigKey     string      `json:"config_key"`     // 配置唯一标识
	ConfigContent interface{} `json:"config_content"` // 解析后的配置内容
	Module        string      `json:"module"`         // 模块名称
	Title         string      `json:"title"`          // 配置标题
	Group         string      `json:"group"`          // 配置分组
	Version       string      `json:"version"`        // 配置版本
	Icon          string      `json:"icon"`           // 图标名称
	DTO           interface{} `json:"dto,omitempty"`  // 解析后的DTO数据
}

// FrontendPathItem 前端路径项
type FrontendPathItem struct {
	ID          int    `json:"id"`           // 配置ID
	Path        string `json:"path"`         // 路径
	Title       string `json:"title"`        // 标题
	ConfigType  string `json:"config_type"`  // 配置类型
	Module      string `json:"module"`       // 所属模块
	Group       string `json:"group"`        // 配置分组
	Icon        string `json:"icon"`         // 图标名称
	ConfigKey   string `json:"config_key"`   // 配置Key
	VersionHash string `json:"version_hash"` // 版本号hash
	Version     string `json:"version"`      // 版本号
}

// FrontendPathGroupDTO 前端路径分组DTO
type FrontendPathGroupDTO struct {
	Module string              `json:"module"` // 模块
	Paths  []*FrontendPathItem `json:"paths"`  // 路径列表
}

// UIConfigRepositoryDTO 仓库层使用的UI配置数据传输对象
type UIConfigRepositoryDTO struct {
	ID            int            `json:"id"`             // 配置ID
	FrontendPath  string         `json:"frontend_path"`  // 前端路径
	VersionHash   string         `json:"version_hash"`   // 版本识别号
	ConfigType    string         `json:"config_type"`    // 配置类型：service, form, table, info, search，tools
	ConfigKey     string         `json:"config_key"`     // 配置唯一标识
	ConfigContent string         `json:"config_content"` // JSON格式的配置内容
	Status        int            `json:"status"`         // 状态：1-启用，0-禁用
	Remark        string         `json:"remark"`         // 备注说明
	CreatedAt     time.Time      `json:"created_at"`     // 创建时间
	UpdatedAt     time.Time      `json:"updated_at"`     // 更新时间
	Module        string         `json:"module"`         // 模块名称
	Title         string         `json:"title"`          // 配置标题
	Group         string         `json:"group"`          // 配置分组
	Version       string         `json:"version"`        // 配置版本
	Icon          string         `json:"icon"`           // 图标名称
	DTO           string         `json:"dto"`            // DTO数据，JSON格式
	Draggable    bool           `json:"draggable"`    // 是否可拖拽
	Resizable    bool           `json:"resizable"`    // 是否可调整大小
	GridItems     []*SimpleGridInfoDTO `json:"grid_items,omitempty"` // 关联的网格布局项目
}

// UIConfigCreateParams 仓库层创建UI配置参数
type UIConfigCreateParams struct {
	FrontendPath  string `json:"frontend_path"`
	ConfigType    string `json:"config_type"`
	ConfigKey     string `json:"config_key"`
	ConfigContent string `json:"config_content"`
	Status        int    `json:"status"`
	Remark        string `json:"remark"`
	Module        string `json:"module"`
	Title         string `json:"title"`
	Group         string `json:"group"`
	Version       string `json:"version"`
	Icon          string `json:"icon"`
	DTO           string `json:"dto"`
	Draggable    bool   `json:"draggable"`    // 是否可拖拽
	Resizable    bool   `json:"resizable"`    // 是否可调整大小
	Step          int    `json:"step"`             // 步骤
	Permission    string `json:"permission"`       // 权限IDs，JSON格式字符串
}

// UIConfigUpdateParams 仓库层更新UI配置参数
type UIConfigUpdateParams struct {
	FrontendPath  string `json:"frontend_path"`
	ConfigContent string `json:"config_content"`
	Status        int    `json:"status"`
	Remark        string `json:"remark"`
	Title         string `json:"title"`
	Group         string `json:"group"`
	Version       string `json:"version"`
	Icon          string `json:"icon"`
	DTO           string `json:"dto"`
	Draggable    bool   `json:"draggable"`    // 是否可拖拽
	Resizable    bool   `json:"resizable"`    // 是否可调整大小
}

// MarshalJSON 自定义UIConfigDTO的JSON序列化方法，确保GridItems字段始终存在
func (u *UIConfigDTO) MarshalJSON() ([]byte, error) {
	// 确保GridItems不为nil
	if u.GridItems == nil {
		u.GridItems = make([]*GridInfoDTO, 0)
	}

	// 创建临时结构体，避免无限递归调用MarshalJSON
	type Alias UIConfigDTO
	return json.Marshal(&struct {
		*Alias
	}{
		Alias: (*Alias)(u),
	})
}
