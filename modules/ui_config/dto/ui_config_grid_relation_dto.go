/**
 * UI配置与网格布局关联关系 DTO
 *
 * 该文件定义了用于前端交互的UI配置与网格布局关联关系数据传输对象
 */

package dto

import "time"

// GridItemWithPositionDTO 带位置信息的GridInfo数据传输对象
type GridItemWithPositionDTO struct {
	ID       int         `json:"id"`       // 网格布局ID
	Name     string      `json:"name"`     // 组件名称
	Content  interface{} `json:"content"`  // 组件内容配置
	API      string      `json:"api"`      // 数据API地址
	DTO      interface{} `json:"dto"`      // DTO配置
	Remark   string      `json:"remark"`   // 备注说明
	Status   int         `json:"status"`   // 状态：1-启用，0-禁用
	Position interface{} `json:"position"` // 位置信息，包含x,y,w,h等自定义字段
	Step     interface{} `json:"step"`     // 步骤，数字数组，如[0]或[1,2,3]
}

// UIConfigGridRelationDTO UI配置与网格布局关联关系数据传输对象
type UIConfigGridRelationDTO struct {
	ID         int         `json:"id"`           // 关联关系ID
	UIConfigID int         `json:"ui_config_id"` // UI配置ID
	GridInfoID int         `json:"grid_info_id"` // 网格布局ID
	Position   interface{} `json:"position"`     // 位置信息，包含x,y,w,h等自定义字段
	Step       interface{} `json:"step"`         // 步骤，数字数组，如[0]或[1,2,3]
	Status     int         `json:"status"`       // 状态：1-启用，0-禁用
	CreatedAt  time.Time   `json:"created_at"`   // 创建时间
	UpdatedAt  time.Time   `json:"updated_at"`   // 更新时间
}

// UIConfigGridRelationCreateDTO 创建UI配置与网格布局关联关系的数据传输对象
type UIConfigGridRelationCreateDTO struct {
	UIConfigID int         `json:"ui_config_id" valid:"Required"` // UI配置ID，必填
	GridInfoID int         `json:"grid_info_id" valid:"Required"` // 网格布局ID，必填
	Position   interface{} `json:"position"`                      // 位置信息，包含x,y,w,h等自定义字段
	Step       interface{} `json:"step"`                          // 步骤，数字数组，如[0]或[1,2,3]
	Status     int         `json:"status" valid:"Range(0,1)"`     // 状态：1-启用，0-禁用
}

// UIConfigGridRelationUpdateDTO 更新UI配置与网格布局关联关系的数据传输对象
type UIConfigGridRelationUpdateDTO struct {
	Position interface{} `json:"position"`                  // 位置信息，包含x,y,w,h等自定义字段
	Step     interface{} `json:"step"`                      // 步骤，数字数组，如[0]或[1,2,3]
	Status   int         `json:"status" valid:"Range(0,1)"` // 状态：1-启用，0-禁用
}

// UIConfigGridItemsUpdateDTO 更新UI配置关联的网格布局项目的数据传输对象
type UIConfigGridItemsUpdateDTO struct {
	UIConfigID int                             `json:"ui_config_id" valid:"Required"` // UI配置ID，必填
	GridItems  []GridInfoWithPositionCreateDTO `json:"grid_items" valid:"Required"`   // 网格布局项目列表，必填
}

// GridInfoWithPositionCreateDTO 创建带位置信息的网格布局的数据传输对象
type GridInfoWithPositionCreateDTO struct {
	GridInfoID int         `json:"grid_info_id" valid:"Required"` // 网格布局ID，必填
	Position   interface{} `json:"position" valid:"Required"`     // 位置信息，包含x,y,w,h等自定义字段，必填
	Step       interface{} `json:"step"`                          // 步骤，数字数组，如[0]或[1,2,3]
}

// BatchUpdateGridInfoPositionDTO 批量更新网格布局位置的数据传输对象
type BatchUpdateGridInfoPositionDTO struct {
	UIConfigID int                             `json:"ui_config_id" valid:"Required"` // UI配置ID，必填
	Items      []GridInfoWithPositionUpdateDTO `json:"items" valid:"Required"`        // 要更新的网格布局项目列表，必填
}

// GridInfoWithPositionUpdateDTO 更新带位置信息的网格布局的数据传输对象
type GridInfoWithPositionUpdateDTO struct {
	GridInfoID int         `json:"grid_info_id" valid:"Required"` // 网格布局ID，必填
	Position   interface{} `json:"position" valid:"Required"`     // 位置信息，包含x,y,w,h等自定义字段，必填
	Step       interface{} `json:"step"`                          // 步骤，数字数组，如[0]或[1,2,3]
}
