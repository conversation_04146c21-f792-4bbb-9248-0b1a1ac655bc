/**
 * GridInfo模型
 *
 * 该文件定义了用于存储网格布局配置的数据模型
 * 每个GridInfo记录对应一个UI网格布局中的组件
 * 与UIConfig是多对多的关系，一个GridInfo可以被多个UIConfig共享使用
 * 每个UIConfig使用GridInfo时都可以有自己独立的位置信息（位置信息已迁移至UIConfigGridRelation模型）
 */

package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// GridInfo 网格布局信息模型
// 注意：原Position字段（组件位置信息）已迁移至UIConfigGridRelation模型，避免冗余
// 如需获取或设置组件位置，请通过UIConfigGridRelation进行操作
// 2025-04-18 by zhangerhao
type GridInfo struct {
	ID      int    `orm:"column(id);auto;pk" json:"id"`              // ID
	Name    string `orm:"column(name);size(100)" json:"name"`        // 组件名称，可重复
	Content string `orm:"column(content);type(text)" json:"content"` // 组件内容配置，JSON格式
	// Position  string      `orm:"column(position);type(text)" json:"position"`                        // 默认位置信息，JSON格式（已废弃，使用UIConfigGridRelation）
	API       string      `orm:"column(api);size(255);null" json:"api"`                              // 数据API地址
	DTO       string      `orm:"column(dto);type(text);null" json:"dto"`                             // DTO配置，JSON格式
	Remark    string      `orm:"column(remark);size(500);null" json:"remark"`                        // 备注说明
	Status    int         `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"` // 状态：1-启用，0-禁用
	CreatedAt time.Time   `orm:"column(created_at);auto_now_add;type(datetime)" json:"created_at"`   // 创建时间
	UpdatedAt time.Time   `orm:"column(updated_at);auto_now;type(datetime)" json:"updated_at"`       // 更新时间
	UIConfigs []*UIConfig `orm:"-" json:"ui_configs,omitempty"`                                      // 关联的UI配置（多对多关系）
}

// PositionInfo 位置信息结构体，用于Position字段的JSON序列化和反序列化（已废弃，使用UIConfigGridRelation）
// type PositionInfo struct {
// 	// UI最后位置信息
// 	LastUiPosition *LastUiPositionInfo `json:"lastUiPosition,omitempty"` // 组件在UI中的最后位置
// 	// 可以添加其他自定义字段
// }

// LastUiPositionInfo UI位置信息结构体，用于记录组件在UI中的精确位置（已废弃，使用UIConfigGridRelation）
// type LastUiPositionInfo struct {
// 	Left float64 `json:"left"` // 左侧位置
// 	Top  float64 `json:"top"`  // 顶部位置
// }

// TableName 设置GridInfo表名
func (g *GridInfo) TableName() string {
	return "grid_info"
}

// LoadUIConfigs 加载关联的UI配置列表
func (g *GridInfo) LoadUIConfigs(o orm.Ormer) error {
	if o == nil {
		o = orm.NewOrm()
	}

	// 查询中间表获取关联的UIConfig ID列表
	var relations []*UIConfigGridRelation
	_, err := o.QueryTable(new(UIConfigGridRelation)).Filter("grid_info_id", g.ID).Filter("status", 1).All(&relations)
	if err != nil {
		return err
	}

	// 如果没有关联的UIConfig，直接返回
	if len(relations) == 0 {
		g.UIConfigs = []*UIConfig{}
		return nil
	}

	// 提取所有关联的UIConfig ID
	var uiConfigIDs []int
	for _, rel := range relations {
		uiConfigIDs = append(uiConfigIDs, rel.UIConfigID)
	}

	// 查询所有关联的UIConfig
	var uiConfigs []*UIConfig
	_, err = o.QueryTable(new(UIConfig)).Filter("id__in", uiConfigIDs).Filter("status", 1).All(&uiConfigs)
	if err != nil {
		return err
	}

	g.UIConfigs = uiConfigs
	return nil
}

// AddUIConfig 添加关联的UI配置
func (g *GridInfo) AddUIConfig(o orm.Ormer, uiConfigID int) error {
	if o == nil {
		o = orm.NewOrm()
	}

	// 检查UIConfig是否存在
	uiConfig := &UIConfig{ID: uiConfigID}
	err := o.Read(uiConfig)
	if err != nil {
		return err
	}

	// 检查关联是否已存在
	relation := &UIConfigGridRelation{
		UIConfigID: uiConfigID,
		GridInfoID: g.ID,
	}
	err = o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).Filter("grid_info_id", g.ID).One(relation)
	if err == nil {
		// 关联已存在，更新position和status
		relation.Status = 1
		// if posInfo != nil {
		// 	relation.SetPositionInfo(posInfo)
		// }
		_, err = o.Update(relation)
		return err
	}

	// 创建新的关联记录
	relation = &UIConfigGridRelation{
		UIConfigID: uiConfigID,
		GridInfoID: g.ID,
		Status:     1,
	}

	// 设置位置信息
	// if posInfo != nil {
	// 	relation.SetPositionInfo(posInfo)
	// }

	_, err = o.Insert(relation)
	return err
}

// RemoveUIConfig 移除关联的UI配置
func (g *GridInfo) RemoveUIConfig(o orm.Ormer, uiConfigID int) error {
	if o == nil {
		o = orm.NewOrm()
	}

	// 将关联状态设置为0（禁用）
	_, err := o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).Filter("grid_info_id", g.ID).Update(orm.Params{
		"status": 0,
	})
	return err
}

// GetAllUIConfigs 获取所有关联的UI配置（包括状态为0的）
func (g *GridInfo) GetAllUIConfigs(o orm.Ormer) ([]*UIConfig, error) {
	if o == nil {
		o = orm.NewOrm()
	}

	// 查询中间表获取关联的UIConfig ID列表
	var relations []*UIConfigGridRelation
	_, err := o.QueryTable(new(UIConfigGridRelation)).Filter("grid_info_id", g.ID).All(&relations)
	if err != nil {
		return nil, err
	}

	// 如果没有关联的UIConfig，直接返回
	if len(relations) == 0 {
		return []*UIConfig{}, nil
	}

	// 提取所有关联的UIConfig ID
	var uiConfigIDs []int
	for _, rel := range relations {
		uiConfigIDs = append(uiConfigIDs, rel.UIConfigID)
	}

	// 查询所有关联的UIConfig
	var uiConfigs []*UIConfig
	_, err = o.QueryTable(new(UIConfig)).Filter("id__in", uiConfigIDs).All(&uiConfigs)
	if err != nil {
		return nil, err
	}

	return uiConfigs, nil
}

// GetUIConfigRelation 获取与特定UIConfig的关联关系
func (g *GridInfo) GetUIConfigRelation(o orm.Ormer, uiConfigID int) (*UIConfigGridRelation, error) {
	if o == nil {
		o = orm.NewOrm()
	}

	relation := &UIConfigGridRelation{}
	err := o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).Filter("grid_info_id", g.ID).One(relation)
	if err != nil {
		return nil, err
	}

	return relation, nil
}

func init() {
	// 注册模型到ORM
	orm.RegisterModel(new(GridInfo))
	// 移除对PositionInfo的注册，因为它不是数据库模型
}
