/**
 * UI配置模型初始化
 *
 * 该文件负责初始化UI配置相关的数据库表和索引
 */

package models

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// Init 初始化UI配置模块
func Init() {
	// 注意：数据库表同步已在main.go中统一处理，这里不再重复同步

	// 创建索引
	o := orm.NewOrm()

	// 检查旧索引是否存在，存在则删除
	var indexCount int
	err := o.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'ui_config' AND index_name = 'idx_module_config_type'").QueryRow(&indexCount)
	if err != nil {
		logs.Error("检查索引是否存在失败:", err)
		return
	}

	// 如果旧索引存在，则删除
	if indexCount > 0 {
		_, err = o.Raw(`
			ALTER TABLE ui_config 
			DROP INDEX idx_module_config_type
		`).Exec()
		if err != nil {
			logs.Error("删除旧索引idx_module_config_type失败:", err)
			return
		}
		logs.Info("成功删除旧索引idx_module_config_type")
	}

	// 检查旧索引idx_module_frontend_path是否存在，存在则删除
	err = o.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'ui_config' AND index_name = 'idx_module_frontend_path'").QueryRow(&indexCount)
	if err != nil {
		logs.Error("检查索引是否存在失败:", err)
		return
	}

	// 如果旧索引存在，则删除
	if indexCount > 0 {
		_, err = o.Raw(`
			ALTER TABLE ui_config 
			DROP INDEX idx_module_frontend_path
		`).Exec()
		if err != nil {
			logs.Error("删除旧索引idx_module_frontend_path失败:", err)
			return
		}
		logs.Info("成功删除旧索引idx_module_frontend_path")
	}

	// 检查新索引是否存在
	err = o.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'ui_config' AND index_name = 'idx_module_version'").QueryRow(&indexCount)
	if err != nil {
		logs.Error("检查索引是否存在失败:", err)
		return
	}

	// 只有当新索引不存在时才创建
	if indexCount == 0 {
		// 创建module和version的组合唯一索引
		_, err = o.Raw(`
			ALTER TABLE ui_config 
			ADD UNIQUE INDEX idx_module_version (module, version)
		`).Exec()
		if err != nil {
			logs.Error("创建module和version组合索引失败:", err)
			return
		}
		logs.Info("成功创建索引idx_module_version")
	} else {
		logs.Info("索引idx_module_version已存在，跳过创建")
	}

	// 检查第二个索引是否存在
	err = o.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'ui_config' AND index_name = 'idx_module_group'").QueryRow(&indexCount)
	if err != nil {
		logs.Error("检查索引是否存在失败:", err)
		return
	}

	// 只有当索引不存在时才创建
	if indexCount == 0 {
		// 创建module和group的组合索引
		_, err = o.Raw(`
			ALTER TABLE ui_config 
			ADD INDEX idx_module_group (module, group_name)
		`).Exec()
		if err != nil {
			logs.Error("创建module和group组合索引失败:", err)
			return
		}
	} else {
		logs.Info("索引idx_module_group已存在，跳过创建")
	}

	logs.Info("UI配置模块初始化完成")
}
