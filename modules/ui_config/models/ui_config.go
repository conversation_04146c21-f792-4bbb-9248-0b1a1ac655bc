/**
 * UI配置模型
 *
 * 该文件定义了用于存储UI配置的数据模型
 * 包括页面配置、表单配置、表格配置等
 */

package models

import (
	"encoding/json"
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"strconv"
	"time"
)

// UIConfig 表单和表格的UI配置模型
type UIConfig struct {
	ID            int       `orm:"column(id);auto;pk" json:"id"`                                       // 配置ID
	FrontendPath  string    `orm:"column(frontend_path);size(255)" json:"frontend_path"`               // 前端路径
	VersionHash   string    `orm:"column(version_hash);size(64)" json:"version_hash"`                  // 版本识别号，用于同步前端缓存
	ConfigType    string    `orm:"column(config_type);size(50)" json:"config_type"`                    // 配置类型：service, form, table, info, search, tools, page
	ConfigKey     string    `orm:"column(config_key);size(100);unique" json:"config_key"`              // 配置唯一标识
	ConfigContent string    `orm:"column(config_content);type(text)" json:"config_content"`            // JSON 格式的配置内容
	Status        int       `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"` // 状态：1-启用，0-禁用
	Remark        string    `orm:"column(remark);size(500);null" json:"remark"`                        // 备注说明
	CreatedAt     time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"created_at"`   // 创建时间
	UpdatedAt     time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updated_at"`       // 更新时间
	Module        string    `orm:"column(module);size(50);index" json:"module"`                        // 模块名称
	Title         string    `orm:"column(title);size(100)" json:"title"`                               // 配置标题
	Group         string    `orm:"column(group_name);size(50);null" json:"group"`                      // 配置分组
	Version       string    `orm:"column(version);size(20)" json:"version"`                            // 配置版本
	Icon          string    `orm:"column(icon);size(50);null" json:"icon"`                             // 图标名称
	DTO           string    `orm:"column(dto);type(text);null" json:"dto"`                             // DTO数据，JSON格式
	Step          int       `orm:"column(step);default(0)" json:"step"`                                // 步骤
	Permission    string    `orm:"column(permission);type(text);null" json:"permission"`               // 权限ID列表，JSON格式
	/**
	 * 是否可拖拽
	 */
	Draggable bool `orm:"column(draggable);default(true)" json:"draggable"` // 是否可拖拽
	/**
	 * 是否可调整大小
	 */
	Resizable bool                    `orm:"column(resizable);default(true)" json:"resizable"` // 是否可调整大小
	GridItems []*GridInfoWithPosition `orm:"-" json:"grid_items,omitempty"`                    // 关联的网格布局项目（多对多关系），包含位置信息
}

// GridInfoWithPosition 带位置信息的GridInfo数据结构
type GridInfoWithPosition struct {
	*GridInfo // 嵌入GridInfo结构体
	// PositionInfo *PositionInfo `json:"position"`   // 位置信息
	Position   string `json:"position"`   // 位置信息
	Step       string `json:"step"`       // 步骤，JSON数组格式，如[0]或[1,2,3]
	Permission []int  `json:"permission"` // 权限ID列表
}

// TableName 设置UIConfig表名
func (u *UIConfig) TableName() string {
	return "ui_config"
}

// UnmarshalTableConfig 将配置内容解析为表格配置
func (u *UIConfig) UnmarshalTableConfig() (map[string]interface{}, error) {
	if u.ConfigType != "table" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(u.ConfigContent), &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// UnmarshalFormConfig 将配置内容解析为表单配置
func (u *UIConfig) UnmarshalFormConfig() (map[string]interface{}, error) {
	if u.ConfigType != "form" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(u.ConfigContent), &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// UnmarshalServiceConfig 将配置内容解析为服务配置
func (u *UIConfig) UnmarshalServiceConfig() (map[string]interface{}, error) {
	if u.ConfigType != "service" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(u.ConfigContent), &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// UnmarshalInfoConfig 将配置内容解析为详情配置
func (u *UIConfig) UnmarshalInfoConfig() (map[string]interface{}, error) {
	if u.ConfigType != "info" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(u.ConfigContent), &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// UnmarshalSearchConfig 将配置内容解析为搜索配置
func (u *UIConfig) UnmarshalSearchConfig() (map[string]interface{}, error) {
	if u.ConfigType != "search" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(u.ConfigContent), &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// UnmarshalToolsConfig 将配置内容解析为工具栏配置
func (u *UIConfig) UnmarshalToolsConfig() (map[string]interface{}, error) {
	if u.ConfigType != "tools" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(u.ConfigContent), &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// UnmarshalPageConfig 将配置内容解析为页面配置
func (u *UIConfig) UnmarshalPageConfig() (map[string]interface{}, error) {
	if u.ConfigType != "page" {
		return nil, nil
	}

	var config map[string]interface{}
	err := json.Unmarshal([]byte(u.ConfigContent), &config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// UnmarshalDTO 将DTO内容解析为对象
func (u *UIConfig) UnmarshalDTO() (map[string]interface{}, error) {
	if u.DTO == "" {
		return nil, nil
	}

	var dtoData map[string]interface{}
	err := json.Unmarshal([]byte(u.DTO), &dtoData)
	if err != nil {
		return nil, err
	}

	return dtoData, nil
}

// GenerateVersionHash 生成版本识别号
func (u *UIConfig) GenerateVersionHash() {
	// 使用时间戳和配置内容生成唯一的版本识别号
	timestamp := time.Now().UnixNano()
	// 简单实现，实际可以使用更复杂的哈希算法
	u.VersionHash = time.Now().Format("20060102150405") + "-" + strconv.FormatInt(timestamp%1000, 10)
}

// GenerateConfigKey 生成配置唯一标识
func (u *UIConfig) GenerateConfigKey() {
	// 使用模块名、配置类型和版本生成唯一标识
	u.ConfigKey = u.Module + ":" + u.ConfigType + ":" + u.Version
}

// LoadGridItems 加载关联的网格布局项目
func (u *UIConfig) LoadGridItems(o orm.Ormer) error {
	logs.Debug("[LoadGridItems] 当前UIConfig.ID: %d", u.ID)
	if o == nil {
		o = orm.NewOrm()
	}

	// 查询中间表获取关联的GridInfo ID列表
	var relations []*UIConfigGridRelation
	_, err := o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", u.ID).Filter("status", 1).All(&relations)
	if err != nil {
		return err
	}

	// 如果没有关联的GridInfo，直接返回
	if len(relations) == 0 {
		u.GridItems = []*GridInfoWithPosition{}
		return nil
	}

	// 提取所有关联的GridInfo ID
	var gridInfoIDs []int
	for _, rel := range relations {
		gridInfoIDs = append(gridInfoIDs, rel.GridInfoID)
	}

	// 查询所有关联的GridInfo
	var gridInfos []*GridInfo
	_, err = o.QueryTable(new(GridInfo)).Filter("id__in", gridInfoIDs).Filter("status", 1).All(&gridInfos)
	if err != nil {
		return err
	}

	// 构建ID到GridInfo的映射以便快速查找
	gridInfoMap := make(map[int]*GridInfo)
	for _, gridInfo := range gridInfos {
		gridInfoMap[gridInfo.ID] = gridInfo
	}

	// 构建最终的GridItems列表，包含位置信息
	var gridItems []*GridInfoWithPosition
	for _, rel := range relations {
		gridInfo, exists := gridInfoMap[rel.GridInfoID]
		if !exists {
			continue
		}

		// 获取位置信息
		// posInfo, err := rel.GetPositionInfo()
		// if err != nil {
		// 	continue
		// }

		// 解析权限字段
		var permissions []int
		if rel.Permission != "" {
			err := json.Unmarshal([]byte(rel.Permission), &permissions)
			if err != nil {
				// 解析失败则使用空数组
				permissions = []int{}
			}
		}

		// 创建带位置信息的GridInfo
		gridItemWithPos := &GridInfoWithPosition{
			GridInfo:   gridInfo,
			Position:   rel.Position,
			Step:       rel.Step,    // 添加步骤字段
			Permission: permissions, // 添加权限字段
		}

		gridItems = append(gridItems, gridItemWithPos)
	}

	u.GridItems = gridItems
	return nil
}

// AddGridItem 添加关联的网格布局项目
func (u *UIConfig) AddGridItem(o orm.Ormer, gridInfoID int) error {
	if o == nil {
		o = orm.NewOrm()
	}

	// 检查GridInfo是否存在
	gridInfo := &GridInfo{ID: gridInfoID}
	err := o.Read(gridInfo)
	if err != nil {
		return err
	}

	// 检查关联是否已存在
	relation := &UIConfigGridRelation{
		UIConfigID: u.ID,
		GridInfoID: gridInfoID,
	}
	err = o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", u.ID).Filter("grid_info_id", gridInfoID).One(relation)
	if err == nil {
		// 关联已存在，更新position和status
		relation.Status = 1
		// if posInfo != nil {
		// 	relation.SetPositionInfo(posInfo)
		// }
		_, err = o.Update(relation)
		return err
	}

	// 创建新的关联记录
	relation = &UIConfigGridRelation{
		UIConfigID: u.ID,
		GridInfoID: gridInfoID,
		Status:     1,
	}

	// 设置位置信息
	// if posInfo != nil {
	// 	relation.SetPositionInfo(posInfo)
	// }

	_, err = o.Insert(relation)
	return err
}

// RemoveGridItem 移除关联的网格布局项目
func (u *UIConfig) RemoveGridItem(o orm.Ormer, gridInfoID int) error {
	if o == nil {
		o = orm.NewOrm()
	}

	// 将关联状态设置为0（禁用）
	_, err := o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", u.ID).Filter("grid_info_id", gridInfoID).Update(orm.Params{
		"status": 0,
	})
	return err
}

// GetAllGridItems 获取所有关联的网格布局项目（包括状态为0的）
func (u *UIConfig) GetAllGridItems(o orm.Ormer) ([]*GridInfoWithPosition, error) {
	if o == nil {
		o = orm.NewOrm()
	}

	// 查询中间表获取关联的GridInfo ID列表
	var relations []*UIConfigGridRelation
	_, err := o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", u.ID).All(&relations)
	if err != nil {
		return nil, err
	}

	// 如果没有关联的GridInfo，直接返回
	if len(relations) == 0 {
		return []*GridInfoWithPosition{}, nil
	}

	// 提取所有关联的GridInfo ID
	var gridInfoIDs []int
	for _, rel := range relations {
		gridInfoIDs = append(gridInfoIDs, rel.GridInfoID)
	}

	// 查询所有关联的GridInfo
	var gridInfos []*GridInfo
	_, err = o.QueryTable(new(GridInfo)).Filter("id__in", gridInfoIDs).All(&gridInfos)
	if err != nil {
		return nil, err
	}

	// 构建ID到GridInfo的映射以便快速查找
	gridInfoMap := make(map[int]*GridInfo)
	for _, gridInfo := range gridInfos {
		gridInfoMap[gridInfo.ID] = gridInfo
	}

	// 构建最终的GridItems列表，包含位置信息
	var gridItems []*GridInfoWithPosition
	for _, rel := range relations {
		gridInfo, exists := gridInfoMap[rel.GridInfoID]
		if !exists {
			continue
		}

		// 获取位置信息
		// posInfo, err := rel.GetPositionInfo()
		// if err != nil {
		// 	continue
		// }

		// 解析权限字段
		var permissions []int
		if rel.Permission != "" {
			err := json.Unmarshal([]byte(rel.Permission), &permissions)
			if err != nil {
				// 解析失败则使用空数组
				permissions = []int{}
			}
		}

		// 创建带位置信息的GridInfo
		gridItemWithPos := &GridInfoWithPosition{
			GridInfo:   gridInfo,
			Position:   rel.Position,
			Step:       rel.Step,    // 添加步骤字段
			Permission: permissions, // 添加权限字段
		}

		gridItems = append(gridItems, gridItemWithPos)
	}

	return gridItems, nil
}

// GetGridItemRelation 获取与特定GridInfo的关联关系
func (u *UIConfig) GetGridItemRelation(o orm.Ormer, gridInfoID int) (*UIConfigGridRelation, error) {
	if o == nil {
		o = orm.NewOrm()
	}

	relation := &UIConfigGridRelation{}
	err := o.QueryTable(new(UIConfigGridRelation)).Filter("ui_config_id", u.ID).Filter("grid_info_id", gridInfoID).One(relation)
	if err != nil {
		return nil, err
	}

	return relation, nil
}

func init() {
	// 注册模型到ORM
	orm.RegisterModel(new(UIConfig))
}
