/**
 * UIConfigGridRelation模型
 *
 * 该文件定义了UIConfig和GridInfo之间的多对多关系模型
 * 用于存储UI配置和网格布局项之间的多对多关联关系
 * 同时存储每个关联对应的位置信息、步骤和权限信息
 */

/**
 * UIConfigGridRelation模型文件
 *
 * 该文件定义了UIConfigGridRelation模型的结构和方法
 * 包括模型的定义、表名设置、位置信息获取和设置等
 */

package models

import (
	//"encoding/json"
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// UIConfigGridRelation UI配置与网格布局的关联关系模型
type UIConfigGridRelation struct {
	ID         int       `orm:"column(id);auto;pk" json:"id"`                                       // ID
	UIConfigID int       `orm:"column(ui_config_id);index" json:"ui_config_id"`                     // 关联的UI配置ID
	GridInfoID int       `orm:"column(grid_info_id);index" json:"grid_info_id"`                     // 关联的网格布局ID
	Position   string    `orm:"column(position);type(text)" json:"position"`                        // 位置信息，JSON格式，包含x,y,w,h,minW,minH,maxW,maxH,Locked等字段
	Step       string    `orm:"column(step);type(text)" json:"step"`                                // 步骤，JSON数组格式，如[0]或[1,2,3]
	Permission string    `orm:"column(permission);type(text)" json:"permission"`                    // 权限IDs，JSON数组格式
	Status     int       `orm:"column(status);default(1)" json:"status" description:"状态：1-启用，0-禁用"` // 状态：1-启用，0-禁用
	CreatedAt  time.Time `orm:"column(created_at);auto_now_add;type(datetime)" json:"created_at"`   // 创建时间
	UpdatedAt  time.Time `orm:"column(updated_at);auto_now;type(datetime)" json:"updated_at"`       // 更新时间
}

// TableName 设置UIConfigGridRelation表名
func (r *UIConfigGridRelation) TableName() string {
	return "ui_config_grid_relation"
}

// GetPositionInfo 获取位置信息对象
// func (r *UIConfigGridRelation) GetPositionInfo() (*PositionInfo, error) {
// 	if r.Position == "" {
// 		// 如果Position为空，则返回默认位置信息
// 		return &PositionInfo{}, nil
// 	}

// 	// 从Position字段解析
// 	var posInfo PositionInfo
// 	err := json.Unmarshal([]byte(r.Position), &posInfo)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return &posInfo, nil
// }

// SetPositionInfo 设置位置信息对象
// func (r *UIConfigGridRelation) SetPositionInfo(posInfo *PositionInfo) error {
// 	posData, err := json.Marshal(posInfo)
// 	if err != nil {
// 		return err
// 	}

// 	r.Position = string(posData)
// 	return nil
// }

// UpdatePositionByUIConfigIdAndGridInfoId 根据 uiConfigId 和 gridInfoId 更新 position 字段
// uiConfigId: UI配置ID
// gridInfoId: 网格布局ID
// position: 新的位置信息（json字符串）
func UpdatePositionByUIConfigIdAndGridInfoId(uiConfigId, gridInfoId int, position string) error {
	// 使用beego ORM
	o := orm.NewOrm()
	_, err := o.QueryTable(new(UIConfigGridRelation)).
		Filter("ui_config_id", uiConfigId).
		Filter("grid_info_id", gridInfoId).
		Update(orm.Params{"position": position, "updated_at": time.Now()})
	return err
}

func init() {
	// 注册模型到ORM
	orm.RegisterModel(new(UIConfigGridRelation))
}
