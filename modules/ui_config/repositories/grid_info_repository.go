/**
 * grid_info_repository.go
 * 网格布局仓库实现
 *
 * 该文件实现了网格布局的仓库接口，负责数据库操作
 * 支持UIConfig和GridInfo的多对多关系管理
 */

package repositories

import (
	"context"
	"encoding/json"
	"fmt"
	"o_mall_backend/modules/ui_config/dto"
	"o_mall_backend/modules/ui_config/models"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// GridInfoRepositoryImpl 网格布局仓库实现
type GridInfoRepositoryImpl struct{}

// NewGridInfoRepository 创建网格布局仓库实例
func NewGridInfoRepository() GridInfoRepository {
	return &GridInfoRepositoryImpl{}
}

// ListGridInfos 获取网格布局列表
func (r *GridInfoRepositoryImpl) ListGridInfos(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.GridInfoRepositoryDTO, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.GridInfo))

	// 应用查询条件
	for k, v := range query {
		if v != nil && v != "" {
			qs = qs.Filter(k, v)
		}
	}

	// 首先获取总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("查询网格布局总数失败: %v", err)
		return nil, 0, err
	}

	// 如果没有数据，直接返回空列表
	if total == 0 {
		return []*dto.GridInfoRepositoryDTO{}, 0, nil
	}

	// 分页查询，不再按照Y、X坐标排序，改为按照ID排序
	var gridItems []*models.GridInfo
	_, err = qs.Limit(pageSize, (page-1)*pageSize).OrderBy("id").All(&gridItems)
	if err != nil {
		return nil, 0, err
	}

	// 转换为 DTO
	dtos := make([]*dto.GridInfoRepositoryDTO, 0, len(gridItems))
	for _, item := range gridItems {
		// 加载关联的UIConfigIDs
		dto := convertToGridInfoRepositoryDTO(item)
		r.loadUIConfigIDsForGridInfo(o, item.ID, dto)
		dtos = append(dtos, dto)
	}

	return dtos, total, nil
}

// GetGridInfo 根据ID获取网格布局详情
func (r *GridInfoRepositoryImpl) GetGridInfo(ctx context.Context, id int) (*dto.GridInfoRepositoryDTO, error) {
	o := orm.NewOrm()
	gridItem := &models.GridInfo{ID: id}
	err := o.Read(gridItem)
	if err != nil {
		logs.Error("根据ID获取网格布局详情失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	dto := convertToGridInfoRepositoryDTO(gridItem)

	// 加载关联的UIConfigIDs
	err = r.loadUIConfigIDsForGridInfo(o, id, dto)
	if err != nil {
		logs.Error("加载网格布局关联的UI配置失败: %v", err)
		return nil, err
	}

	return dto, nil
}

// CreateGridInfo 创建网格布局
func (r *GridInfoRepositoryImpl) CreateGridInfo(ctx context.Context, gridInfo *dto.GridInfoCreateParams) (int, error) {
	o := orm.NewOrm()

	// 构建网格布局模型
	gridItem := &models.GridInfo{
		Name:    gridInfo.Name,
		Content: gridInfo.Content,
		API:     gridInfo.API,
		DTO:     gridInfo.DTO,
		Remark:  gridInfo.Remark,
		Status:  gridInfo.Status,
	}

	// Position字段已从GridInfo模型移除，现在存储在UIConfigGridRelation中

	// 插入数据
	id, err := o.Insert(gridItem)
	if err != nil {
		logs.Error("[CreateGridInfo] 创建失败: %v", err)
		return 0, err
	}

	// 处理UIConfig关联关系
	// 向后兼容：如果提供了UIConfigID，将其添加到UIConfigIDs
	if gridInfo.UIConfigID > 0 {
		if gridInfo.UIConfigIDs == nil {
			gridInfo.UIConfigIDs = []int{gridInfo.UIConfigID}
		} else if !containsInt(gridInfo.UIConfigIDs, gridInfo.UIConfigID) {
			gridInfo.UIConfigIDs = append(gridInfo.UIConfigIDs, gridInfo.UIConfigID)
		}
	}

	// 创建UIConfig关联关系
	if len(gridInfo.UIConfigIDs) > 0 {
		for _, uiConfigID := range gridInfo.UIConfigIDs {
			relation := &models.UIConfigGridRelation{
				UIConfigID: uiConfigID,
				GridInfoID: int(id),
				Status:     1,
				Step:       "[0]", // 设置默认Step为[0]
			}

			// 如果提供了Position，添加到关联关系中
			if gridInfo.Position != "" {
				var tmp interface{}
				err := json.Unmarshal([]byte(gridInfo.Position), &tmp)
				if err == nil {
					relation.Position = gridInfo.Position
				} else {
					logs.Error("[CreateGridInfo] Position 不是合法JSON字符串: %v", err)
				}
			} else {
				// 如果没有提供Position，设置默认值
				relation.Position = fmt.Sprintf(`{"i":%d,"x":0,"y":0,"w":6,"h":4}`, id)
				logs.Info("[CreateGridInfo] 使用默认Position: %s", relation.Position)
			}
			
			_, err = o.Insert(relation)
			if err != nil {
				logs.Error("[CreateGridInfo] 创建关联关系失败: %v", err)
				// 这里我们不回滚主记录，因为我们没有使用事务
				// 在多对多关系中，关联表的记录可以通过后续的操作来修复
				logs.Warning("[CreateGridInfo] 创建关联关系失败，但主记录已创建，ID: %d", id)
			}
		}
	}

	return int(id), nil
}

// UpdateGridInfo 更新网格布局
func (r *GridInfoRepositoryImpl) UpdateGridInfo(ctx context.Context, id int, gridInfo *dto.GridInfoUpdateParams) error {
	o := orm.NewOrm()

	// 添加调试日志，查看传入的gridInfo参数
	logs.Info("[UpdateGridInfo] 收到的参数：ID=%d, Name=%s, Step=%s", 
		id, gridInfo.Name, gridInfo.Step)

	// 获取现有网格布局
	gridItem := &models.GridInfo{ID: id}
	err := o.Read(gridItem)
	if err == orm.ErrNoRows {
		return nil
	}
	if err != nil {
		logs.Error("[UpdateGridInfo] 查询失败: %v", err)
		return err
	}

	// 更新字段
	if gridInfo.Name != "" {
		gridItem.Name = gridInfo.Name
	}
	if gridInfo.Content != "" {
		gridItem.Content = gridInfo.Content
	}
	// Position字段已从GridInfo模型移除，现在存储在UIConfigGridRelation中
	if gridInfo.API != "" {
		gridItem.API = gridInfo.API
	}
	if gridInfo.DTO != "" {
		gridItem.DTO = gridInfo.DTO
	}
	if gridInfo.Remark != "" {
		gridItem.Remark = gridInfo.Remark
	}
	if gridInfo.Status != 0 {
		gridItem.Status = gridInfo.Status
	}

	// 保存更新
	_, err = o.Update(gridItem)
	if err != nil {
		logs.Error("[UpdateGridInfo] 更新失败: %v", err)
		return err
	}

	// 添加日志，查看UIConfigIDs
	logs.Info("[UpdateGridInfo] UIConfigIDs: %v", gridInfo.UIConfigIDs)

	// 处理特殊情况：前端只传递了单个UIConfigID
	// 如果有Step或Position字段，但没有UIConfigIDs，尝试根据grid_info_id查找相关的关联记录并更新
	if (gridInfo.Step != "" || gridInfo.Position != "") && len(gridInfo.UIConfigIDs) == 0 {
		logs.Info("[UpdateGridInfo] 检测到Step或Position字段更新，但没有UIConfigIDs，尝试查找关联记录")
		
		// 构建查询条件
		queryFilter := orm.NewCondition().And("grid_info_id", id)
		
		// 如果有UIConfigID，只更新特定UIConfigID的关联记录
		if gridInfo.UIConfigID > 0 {
			logs.Info("[UpdateGridInfo] 指定了UIConfigID=%d，只更新该配置的关联记录", gridInfo.UIConfigID)
			queryFilter = queryFilter.And("ui_config_id", gridInfo.UIConfigID)
		}
		
		// 查找符合条件的关联记录
		var relations []*models.UIConfigGridRelation
		_, err = o.QueryTable(new(models.UIConfigGridRelation)).SetCond(queryFilter).All(&relations)
		if err != nil {
			logs.Error("[UpdateGridInfo] 查询关联关系失败: %v", err)
			// 继续执行，不中断流程
		} else {
			logs.Info("[UpdateGridInfo] 找到 %d 个符合条件的关联记录", len(relations))
			
			// 如果没有找到记录但有UIConfigID，则创建一条新记录
			if len(relations) == 0 && gridInfo.UIConfigID > 0 {
				logs.Info("[UpdateGridInfo] 未找到UIConfigID=%d与GridInfoID=%d的关联记录，将创建新记录", 
					gridInfo.UIConfigID, id)
				
				// 创建默认position
				defaultPosition := fmt.Sprintf(`{"i":%d,"x":0,"y":0,"w":6,"h":4}`, id)
				
				// 创建默认step
				defaultStep := "[0]"
				
				// 创建新记录
				newRelation := &models.UIConfigGridRelation{
					UIConfigID: gridInfo.UIConfigID,
					GridInfoID: id,
					Position:   defaultPosition,
					Step:       defaultStep,
					Status:     1, // 启用状态
				}
				
				// 插入新记录
				_, err = o.Insert(newRelation)
				if err != nil {
					logs.Error("[UpdateGridInfo] 创建新关联记录失败: %v", err)
				} else {
					logs.Info("[UpdateGridInfo] 成功创建新关联记录，UIConfigID=%d，GridInfoID=%d", 
						gridInfo.UIConfigID, id)
					// 将新创建的记录添加到relations中，以便后续处理
					relations = append(relations, newRelation)
				}
			}
			
			// 更新所有关联记录
			for _, rel := range relations {
				updated := false
				if gridInfo.Position != "" {
					var tmp interface{}
					err := json.Unmarshal([]byte(gridInfo.Position), &tmp)
					if err == nil {
						rel.Position = gridInfo.Position
						updated = true
					}
				}
				
				if gridInfo.Step != "" {
					rel.Step = gridInfo.Step
					updated = true
					logs.Info("[UpdateGridInfo] 为关联记录 ID=%d 设置Step=%s", rel.ID, gridInfo.Step)
				}
				
				if gridInfo.Permission != "" {
					rel.Permission = gridInfo.Permission
					updated = true
				}
				
				if updated {
					_, err = o.Update(rel, "Position", "Step", "Permission", "UpdatedAt")
					if err != nil {
						logs.Error("[UpdateGridInfo] 更新关联关系字段失败: %v", err)
					} else {
						logs.Info("[UpdateGridInfo] 成功更新关联关系 ID=%d，UIConfigID=%d, GridInfoID=%d", 
							rel.ID, rel.UIConfigID, rel.GridInfoID)
					}
				}
			}
		}
	}

	// 如果提供了UIConfigIDs，更新关联关系（原有逻辑）
	if len(gridInfo.UIConfigIDs) > 0 {
		// 获取现有关联关系
		var relations []*models.UIConfigGridRelation
		_, err = o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", id).All(&relations)
		if err != nil {
			logs.Error("[UpdateGridInfo] 查询关联关系失败: %v", err)
			return err
		}

		// 构建现有UIConfigID映射
		existingUIConfigIDs := make(map[int]*models.UIConfigGridRelation)
		for _, rel := range relations {
			existingUIConfigIDs[rel.UIConfigID] = rel
		}

		// 添加新的关联关系，更新已有的关联关系状态
		for _, uiConfigID := range gridInfo.UIConfigIDs {
			if rel, exists := existingUIConfigIDs[uiConfigID]; exists {
				// 如果已存在但状态为0，则更新为1
				if rel.Status == 0 {
					rel.Status = 1
					_, err = o.Update(rel, "Status", "UpdatedAt")
					if err != nil {
						logs.Error("[UpdateGridInfo] 更新关联关系状态失败: %v", err)
						// 不需要中断整个更新流程
					}
				}

				// 更新关联关系中的Position字段
				if gridInfo.Position != "" {
					var tmp interface{}
					err := json.Unmarshal([]byte(gridInfo.Position), &tmp)
					if err == nil {
						rel.Position = gridInfo.Position
						// 添加对Step字段的处理
						if gridInfo.Step != "" {
							rel.Step = gridInfo.Step
							logs.Info("[UpdateGridInfo] 更新Step字段为: %s, uiConfigID: %d, gridInfoID: %d", 
								gridInfo.Step, rel.UIConfigID, rel.GridInfoID)
						} else {
							logs.Info("[UpdateGridInfo] Step字段为空，不进行更新, uiConfigID: %d, gridInfoID: %d", 
								rel.UIConfigID, rel.GridInfoID)
						}
						// 添加对Permission字段的处理
						if gridInfo.Permission != "" {
							rel.Permission = gridInfo.Permission
							logs.Info("[UpdateGridInfo] 更新Permission字段为: %s", gridInfo.Permission)
						}
						_, err = o.Update(rel, "Position", "Step", "Permission", "UpdatedAt")
						if err != nil {
							logs.Error("[UpdateGridInfo] 更新关联关系字段失败: %v", err)
						} else {
							logs.Info("[UpdateGridInfo] 成功更新关联关系，uiConfigID: %d, gridInfoID: %d", 
								rel.UIConfigID, rel.GridInfoID)
						}
					} else {
						logs.Error("[UpdateGridInfo] Position 不是合法JSON字符串: %v", err)
					}
				}
				if gridInfo.Step != "" {
					rel.Step = gridInfo.Step
					o.Update(rel, "Step", "UpdatedAt")
				}
				if gridInfo.Permission != "" {
					rel.Permission = gridInfo.Permission
					o.Update(rel, "Permission", "UpdatedAt")
				}
			} else {
				// 创建新的关联关系
				newRel := &models.UIConfigGridRelation{
					UIConfigID: uiConfigID,
					GridInfoID: id,
					Status:     1,
				}

				// 赋值前校验Position为合法JSON字符串（无结构体解析）
				if gridInfo.Position != "" {
					var tmp interface{}
					err := json.Unmarshal([]byte(gridInfo.Position), &tmp)
					if err == nil {
						newRel.Position = gridInfo.Position
					} else {
						logs.Error("[UpdateGridInfo] Position 不是合法JSON字符串: %v", err)
					}
				}
				if gridInfo.Step != "" {
					newRel.Step = gridInfo.Step
				}
				if gridInfo.Permission != "" {
					newRel.Permission = gridInfo.Permission
				}

				_, err = o.Insert(newRel)
				if err != nil {
					logs.Error("[UpdateGridInfo] 创建关联关系失败: %v", err)
					// 不需要中断整个更新流程
				}
			}
		}

		// 软删除不再关联的UIConfigID（将状态设置为0）
		for _, rel := range existingUIConfigIDs {
			rel.Status = 0
			_, err = o.Update(rel, "Status", "UpdatedAt")
			if err != nil {
				logs.Error("[UpdateGridInfo] 软删除关联关系失败: %v", err)
				// 不需要中断整个更新流程
			}
		}
	}

	return nil
}

// DeleteGridInfo 删除网格布局
func (r *GridInfoRepositoryImpl) DeleteGridInfo(ctx context.Context, id int) error {
	o := orm.NewOrm()

	// 软删除关联关系（将状态设置为0）
	_, err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", id).Update(orm.Params{
		"status": 0,
	})
	if err != nil {
		logs.Error("[DeleteGridInfo] 软删除关联关系失败: %v", err)
		// 即使软删除关联关系失败，我们仍然可以尝试删除主记录
	}

	// 删除网格布局
	_, err = o.Delete(&models.GridInfo{ID: id})
	if err != nil {
		logs.Error("[DeleteGridInfo] 删除失败: %v", err)
		return err
	}

	return nil
}

// UpdateGridInfoStatus 更新网格布局状态
func (r *GridInfoRepositoryImpl) UpdateGridInfoStatus(ctx context.Context, id int, status int) error {
	o := orm.NewOrm()

	// 获取现有网格布局
	gridItem := &models.GridInfo{ID: id}
	err := o.Read(gridItem)
	if err == orm.ErrNoRows {
		return nil
	}
	if err != nil {
		logs.Error("[UpdateGridInfoStatus] 查询失败: %v", err)
		return err
	}

	// 更新状态
	gridItem.Status = status

	// 保存更新
	_, err = o.Update(gridItem, "Status")
	if err != nil {
		logs.Error("[UpdateGridInfoStatus] 更新状态失败: %v", err)
		return err
	}

	return nil
}

// BatchUpdateGridInfoPosition 批量更新网格布局位置
func (r *GridInfoRepositoryImpl) BatchUpdateGridInfoPosition(ctx context.Context, items []*dto.GridInfoRepositoryPositionItem) error {
	o := orm.NewOrm()

	// 批量更新网格布局位置
	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("开启事务失败: %v", err)
		return err
	}

	for _, item := range items {
		gridItem := &models.GridInfo{ID: item.ID}
		err := o.Read(gridItem)
		if err != nil {
			logs.Error("查询网格布局失败: %v", err)
			tx.Rollback()
			return err
		}

		// Position字段已从GridInfo模型移除，现在只存储在UIConfigGridRelation中
		var positionStr string
		if item.Position != "" {
			var tmp interface{}
			err := json.Unmarshal([]byte(item.Position), &tmp)
			if err == nil {
				logs.Info("更新网格布局position: %s", item.Position)
				positionStr = item.Position
			} else {
				logs.Error("[BatchUpdateGridInfoPosition] Position 不是合法JSON字符串: %v", err)
			}
		}

		// 如果存在UIConfigID，需要更新UIConfigGridRelation表中的position
		if item.UIConfigID > 0 {
			// 检查UIConfigGridRelation是否存在
			relation := &models.UIConfigGridRelation{
				UIConfigID: item.UIConfigID,
				GridInfoID: item.ID,
			}
			err = o.Read(relation, "ui_config_id", "grid_info_id")
			if err != nil && err != orm.ErrNoRows {
				logs.Error("查询UI配置与网格布局关系失败: %v", err)
				tx.Rollback()
				return err
			}

			if err == orm.ErrNoRows {
				// 如果关联关系不存在，则创建
				relation.Status = 1
				relation.Position = positionStr // 使用最新的position
				_, err = o.Insert(relation)
				if err != nil {
					tx.Rollback()
					logs.Error("创建UI配置与网格布局关系失败: %v", err)
					return err
				}
				logs.Info("创建UI配置与网格布局关系成功，ui_config_id: %d, grid_info_id: %d", item.UIConfigID, item.ID)
			} else {
				// 关联关系存在，更新position
				relation.Position = positionStr // 使用最新的position
				_, err = o.Update(relation, "position")
				if err != nil {
					tx.Rollback()
					logs.Error("更新UI配置与网格布局关系位置信息失败: %v", err)
					return err
				}
				logs.Info("更新UI配置与网格布局关系位置信息成功，ui_config_id: %d, grid_info_id: %d", item.UIConfigID, item.ID)
			}
		}

		// GridInfo模型中已不再存储position字段，无需更新
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// ListGridInfosByUIConfigID 根据UI配置ID获取关联的网格布局列表
func (r *GridInfoRepositoryImpl) ListGridInfosByUIConfigID(ctx context.Context, uiConfigID int) ([]*dto.GridInfoRepositoryDTO, error) {
	o := orm.NewOrm()

	// 查询关联表获取GridInfo的ID列表
	var relations []*models.UIConfigGridRelation
	_, err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).Filter("status", 1).All(&relations)
	if err != nil {
		logs.Error("查询UI配置关联的网格布局关系失败: %v", err)
		return nil, err
	}

	if len(relations) == 0 {
		return []*dto.GridInfoRepositoryDTO{}, nil
	}

	// 获取网格布局ID列表
	var gridIDs []int
	for _, relation := range relations {
		gridIDs = append(gridIDs, relation.GridInfoID)
	}

	// 查询网格布局详情
	var gridItems []*models.GridInfo
	_, err = o.QueryTable(new(models.GridInfo)).Filter("id__in", gridIDs).All(&gridItems)
	if err != nil {
		logs.Error("查询网格布局详情失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	dtos := make([]*dto.GridInfoRepositoryDTO, 0, len(gridItems))
	for _, item := range gridItems {
		dto := convertToGridInfoRepositoryDTO(item)
		// 为每个GridInfo加载关联的UIConfigIDs
		r.loadUIConfigIDsForGridInfo(o, item.ID, dto)
		dtos = append(dtos, dto)
	}

	return dtos, nil
}

// ListUIConfigsByGridInfoID 根据网格布局ID获取关联的UI配置列表
func (r *GridInfoRepositoryImpl) ListUIConfigsByGridInfoID(ctx context.Context, gridInfoID int) ([]*dto.SimpleUIConfigDTO, error) {
	o := orm.NewOrm()

	// 查询关联表获取UIConfig的ID列表
	var relations []*models.UIConfigGridRelation
	_, err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", gridInfoID).Filter("status", 1).All(&relations)
	if err != nil {
		logs.Error("查询网格布局关联的UI配置关系失败: %v", err)
		return nil, err
	}

	if len(relations) == 0 {
		return []*dto.SimpleUIConfigDTO{}, nil
	}

	// 获取UI配置ID列表
	uiConfigIDs := make([]int, 0, len(relations))
	for _, relation := range relations {
		uiConfigIDs = append(uiConfigIDs, relation.UIConfigID)
	}

	// 查询UI配置详情
	var uiConfigs []*models.UIConfig
	_, err = o.QueryTable(new(models.UIConfig)).Filter("id__in", uiConfigIDs).All(&uiConfigs)
	if err != nil {
		logs.Error("查询UI配置详情失败: %v", err)
		return nil, err
	}

	// 转换为SimpleUIConfigDTO
	dtos := make([]*dto.SimpleUIConfigDTO, 0, len(uiConfigs))
	for _, uiConfig := range uiConfigs {
		dto := &dto.SimpleUIConfigDTO{
			ID:           uiConfig.ID,
			FrontendPath: uiConfig.FrontendPath,
			ConfigType:   uiConfig.ConfigType,
			ConfigKey:    uiConfig.ConfigKey,
			Module:       uiConfig.Module,
			Title:        uiConfig.Title,
			Status:       uiConfig.Status,
		}
		dtos = append(dtos, dto)
	}

	return dtos, nil
}

// AddUIConfigsToGridInfo 为网格布局添加UI配置关联
func (r *GridInfoRepositoryImpl) AddUIConfigsToGridInfo(ctx context.Context, gridInfoID int, uiConfigIDs []int) error {
	if len(uiConfigIDs) == 0 {
		return nil
	}

	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[AddUIConfigsToGridInfo] 开启事务失败: %v", err)
		return err
	}

	// 查询已有的关联关系
	var existingRelations []*models.UIConfigGridRelation
	_, err = o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", gridInfoID).Filter("ui_config_id__in", uiConfigIDs).All(&existingRelations)
	if err != nil {
		tx.Rollback()
		logs.Error("[AddUIConfigsToGridInfo] 查询已有关联关系失败: %v", err)
		return err
	}

	// 筛选出需要新增的关联关系
	existingMap := make(map[int]bool)
	for _, relation := range existingRelations {
		existingMap[relation.UIConfigID] = true
	}

	// 创建新的关联关系
	for _, uiConfigID := range uiConfigIDs {
		if !existingMap[uiConfigID] {
			relation := &models.UIConfigGridRelation{
				UIConfigID: uiConfigID,
				GridInfoID: gridInfoID,
				Status:     1,
			}
			_, err = o.Insert(relation)
			if err != nil {
				tx.Rollback()
				logs.Error("[AddUIConfigsToGridInfo] 创建关联关系失败: %v", err)
				return err
			}
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[AddUIConfigsToGridInfo] 提交事务失败: %v", err)
		return err
	}

	return nil
}

// RemoveUIConfigsFromGridInfo 从网格布局移除UI配置关联
func (r *GridInfoRepositoryImpl) RemoveUIConfigsFromGridInfo(ctx context.Context, gridInfoID int, uiConfigIDs []int) error {
	if len(uiConfigIDs) == 0 {
		return nil
	}

	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[RemoveUIConfigsFromGridInfo] 开启事务失败: %v", err)
		return err
	}

	// 删除关联关系
	_, err = o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", gridInfoID).Filter("ui_config_id__in", uiConfigIDs).Delete()
	if err != nil {
		tx.Rollback()
		logs.Error("[RemoveUIConfigsFromGridInfo] 删除关联关系失败: %v", err)
		return err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[RemoveUIConfigsFromGridInfo] 提交事务失败: %v", err)
		return err
	}

	return nil
}

// UpdateGridInfoUIConfigs 更新网格布局关联的UI配置
func (r *GridInfoRepositoryImpl) UpdateGridInfoUIConfigs(ctx context.Context, gridInfoID int, uiConfigIDs []int) error {
	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[UpdateGridInfoUIConfigs] 开启事务失败: %v", err)
		return err
	}

	// 删除所有原有关联关系
	_, err = o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", gridInfoID).Delete()
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateGridInfoUIConfigs] 删除原有关联关系失败: %v", err)
		return err
	}

	// 如果没有需要添加的关联关系，直接提交事务
	if len(uiConfigIDs) == 0 {
		err = tx.Commit()
		if err != nil {
			tx.Rollback()
			logs.Error("[UpdateGridInfoUIConfigs] 提交事务失败: %v", err)
			return err
		}
		return nil
	}

	// 创建新的关联关系
	for _, uiConfigID := range uiConfigIDs {
		relation := &models.UIConfigGridRelation{
			UIConfigID: uiConfigID,
			GridInfoID: gridInfoID,
			Status:     1,
		}
		_, err = o.Insert(relation)
		if err != nil {
			tx.Rollback()
			logs.Error("[UpdateGridInfoUIConfigs] 创建关联关系失败: %v", err)
			return err
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateGridInfoUIConfigs] 提交事务失败: %v", err)
		return err
	}

	return nil
}

// DeleteUIConfigGridRelation 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
// @param ctx 上下文
// @param gridInfoID 网格布局ID
// @param uiConfigID UI配置ID
// @return error 操作错误信息
func (r *GridInfoRepositoryImpl) DeleteUIConfigGridRelation(ctx context.Context, gridInfoID int, uiConfigID int) error {
	o := orm.NewOrm()
	num, err := o.QueryTable(new(models.UIConfigGridRelation)).
		Filter("grid_info_id", gridInfoID).
		Filter("ui_config_id", uiConfigID).
		Delete()
	logs.Info("[DeleteUIConfigGridRelation] gridInfoID=%d, uiConfigID=%d, 删除影响行数: %d", gridInfoID, uiConfigID, num)
	if err != nil {
		logs.Error("[DeleteUIConfigGridRelation] 删除UIConfigGridRelation失败: %v", err)
	}
	return err
}

// UpdateBatchUIConfigGridRelation 批量设置UI配置与多个网格布局的关联关系
// @description 将一个UIConfigId批量关联到多个GridInfoId
func (r *GridInfoRepositoryImpl) UpdateBatchUIConfigGridRelation(ctx context.Context, uiConfigId int, gridInfoIds []int) error {
	o := orm.NewOrm()
	for _, gridInfoId := range gridInfoIds {
		// 检查关联是否已存在
		relation := &models.UIConfigGridRelation{
			UIConfigID: uiConfigId,
			GridInfoID: gridInfoId,
		}
		err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("ui_config_id", uiConfigId).Filter("grid_info_id", gridInfoId).One(relation)
		if err == nil {
			// 已存在则激活状态
			relation.Status = 1
			_, err = o.Update(relation)
			if err != nil {
				logs.Error("[UpdateBatchUIConfigGridRelation] 更新已存在关联失败: %v", err)
				return err
			}
			continue
		}
		// 不存在则新建
		relation = &models.UIConfigGridRelation{
			UIConfigID: uiConfigId,
			GridInfoID: gridInfoId,
			Status:     1,
			Step:       "[0]", // 设置默认的step为[0]
			Position:   fmt.Sprintf(`{"i":%d,"x":0,"y":0,"w":6,"h":4}`, gridInfoId), // 设置默认的position
		}
		_, err = o.Insert(relation)
		if err != nil {
			logs.Error("[UpdateBatchUIConfigGridRelation] 插入新关联失败: %v", err)
			return err
		}
	}
	return nil
}

// !!! 请勿在此文件重复声明GridInfoRepository接口，接口已在interfaces.go中定义，无需再次声明 !!!

// loadUIConfigIDsForGridInfo 加载GridInfo关联的UIConfigIDs
func (r *GridInfoRepositoryImpl) loadUIConfigIDsForGridInfo(o orm.Ormer, gridInfoID int, dto *dto.GridInfoRepositoryDTO) error {
	// 查询中间表获取关联的UIConfig ID列表
	var relations []*models.UIConfigGridRelation
	_, err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", gridInfoID).Filter("status", 1).All(&relations)
	if err != nil {
		logs.Error("查询网格布局关联的UI配置关系失败: %v", err)
		return err
	}

	// 如果存在关联关系，则设置UIConfigIDs
	if len(relations) > 0 {
		var uiConfigIDs []int
		for _, relation := range relations {
			uiConfigIDs = append(uiConfigIDs, relation.UIConfigID)
		}
		dto.UIConfigIDs = uiConfigIDs
	}

	return nil
}

// convertToGridInfoRepositoryDTO 转换网格布局模型为DTO
func convertToGridInfoRepositoryDTO(gridItem *models.GridInfo) *dto.GridInfoRepositoryDTO {
	dtoObj := &dto.GridInfoRepositoryDTO{
		ID:   gridItem.ID,
		Name: gridItem.Name,
		// Position字段已从GridInfo模型移除，现在存储在UIConfigGridRelation中
		// Position:  gridItem.Position,
		API:       gridItem.API,
		Remark:    gridItem.Remark,
		Status:    gridItem.Status,
		CreatedAt: gridItem.CreatedAt,
		UpdatedAt: gridItem.UpdatedAt,
	}

	// 只传递字符串类型的Content
	if gridItem.Content != "" {
		// 尝试解析Content为JSON对象
		var contentObj interface{}
		if err := json.Unmarshal([]byte(gridItem.Content), &contentObj); err == nil {
			dtoObj.Content = contentObj
		} else {
			dtoObj.Content = gridItem.Content
		}
	}

	if gridItem.DTO != "" {
		// 尝试解析DTO为JSON对象
		var dtoContentObj interface{}
		if err := json.Unmarshal([]byte(gridItem.DTO), &dtoContentObj); err == nil {
			dtoObj.DTO = dtoContentObj
		} else {
			dtoObj.DTO = gridItem.DTO
		}
	}

	// 加载最新的UIConfigGridRelation中的Position信息
	o := orm.NewOrm()
	var relation models.UIConfigGridRelation
	err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("grid_info_id", gridItem.ID).Filter("status", 1).OrderBy("-id").One(&relation)
	if err == nil && relation.Position != "" {
		// 解析Position JSON字符串为对象
		var positionObj interface{}
		if err := json.Unmarshal([]byte(relation.Position), &positionObj); err == nil {
			dtoObj.Position = positionObj
		} else {
			logs.Error("解析Position JSON字符串失败: %v", err)
		}
	}

	return dtoObj
}

// GetUIConfigGridRelation 获取UI配置与网格布局的关联关系
func (r *GridInfoRepositoryImpl) GetUIConfigGridRelation(ctx context.Context, uiConfigID int, gridInfoID int) (*models.UIConfigGridRelation, error) {
	o := orm.NewOrm()
	relation := &models.UIConfigGridRelation{UIConfigID: uiConfigID, GridInfoID: gridInfoID}

	err := o.Read(relation, "ui_config_id", "grid_info_id")
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没有找到关联关系，返回nil
		}
		logs.Error("获取UI配置与网格布局关联关系失败: %v", err)
		return nil, err
	}

	return relation, nil
}

// containsInt 判断整数切片是否包含特定值
func containsInt(slice []int, item int) bool {
	for _, val := range slice {
		if val == item {
			return true
		}
	}
	return false
}
