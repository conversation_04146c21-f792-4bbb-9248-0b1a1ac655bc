/**
 * interfaces.go
 * UI 配置和网格布局仓库接口
 *
 * 该文件定义了UI配置和网格布局的仓库接口，用于数据层操作
 */

package repositories

import (
	"context"
	"o_mall_backend/modules/ui_config/dto"
	"o_mall_backend/modules/ui_config/models"
)

// UIConfigRepository UI配置仓库接口
type UIConfigRepository interface {
	// ListUIConfigs 获取UI配置列表
	ListUIConfigs(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.UIConfigRepositoryDTO, int64, error)

	// GetUIConfig 根据ID获取UI配置详情
	GetUIConfig(ctx context.Context, id int) (*dto.UIConfigRepositoryDTO, error)

	// CreateUIConfig 创建UI配置
	CreateUIConfig(ctx context.Context, config *dto.UIConfigCreateParams) (int, error)

	// UpdateUIConfig 更新UI配置
	UpdateUIConfig(ctx context.Context, id int, config *dto.UIConfigUpdateParams) error

	// DeleteUIConfig 删除UI配置
	DeleteUIConfig(ctx context.Context, id int) error

	// UpdateVersion 更新配置版本号
	UpdateVersion(ctx context.Context, id int) (string, error)

	// GetUIConfigByKey 根据配置Key获取UI配置详情
	GetUIConfigByKey(ctx context.Context, configKey string) (*dto.UIConfigRepositoryDTO, error)

	// GetConfigByPath 根据前端路径获取所有相关配置
	GetConfigByPath(ctx context.Context, frontendPath string) ([]*dto.UIConfigRepositoryDTO, error)
}

// GridInfoRepository 网格布局仓库接口
type GridInfoRepository interface {
	// ListGridInfos 获取网格布局列表
	ListGridInfos(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.GridInfoRepositoryDTO, int64, error)

	// GetGridInfo 根据ID获取网格布局详情
	GetGridInfo(ctx context.Context, id int) (*dto.GridInfoRepositoryDTO, error)

	// CreateGridInfo 创建网格布局
	CreateGridInfo(ctx context.Context, gridInfo *dto.GridInfoCreateParams) (int, error)

	// UpdateGridInfo 更新网格布局
	UpdateGridInfo(ctx context.Context, id int, gridInfo *dto.GridInfoUpdateParams) error

	// DeleteGridInfo 删除网格布局
	DeleteGridInfo(ctx context.Context, id int) error

	// UpdateGridInfoStatus 更新网格布局状态
	UpdateGridInfoStatus(ctx context.Context, id int, status int) error

	// BatchUpdateGridInfoPosition 批量更新网格布局位置
	BatchUpdateGridInfoPosition(ctx context.Context, items []*dto.GridInfoRepositoryPositionItem) error

	// ListGridInfosByUIConfigID 根据UI配置ID获取关联的网格布局列表
	ListGridInfosByUIConfigID(ctx context.Context, uiConfigID int) ([]*dto.GridInfoRepositoryDTO, error)

	// ListUIConfigsByGridInfoID 根据网格布局ID获取关联的UI配置列表
	ListUIConfigsByGridInfoID(ctx context.Context, gridInfoID int) ([]*dto.SimpleUIConfigDTO, error)

	// AddUIConfigsToGridInfo 为网格布局添加UI配置关联
	AddUIConfigsToGridInfo(ctx context.Context, gridInfoID int, uiConfigIDs []int) error

	// RemoveUIConfigsFromGridInfo 从网格布局移除UI配置关联
	RemoveUIConfigsFromGridInfo(ctx context.Context, gridInfoID int, uiConfigIDs []int) error

	// UpdateGridInfoUIConfigs 更新网格布局关联的UI配置
	UpdateGridInfoUIConfigs(ctx context.Context, gridInfoID int, uiConfigIDs []int) error

	// DeleteUIConfigGridRelation 删除指定GridInfoID和UIConfigID的UIConfigGridRelation记录
	DeleteUIConfigGridRelation(ctx context.Context, gridInfoID int, uiConfigID int) error

	// GetUIConfigGridRelation 获取UI配置与网格布局的关联关系
	GetUIConfigGridRelation(ctx context.Context, uiConfigID int, gridInfoID int) (*models.UIConfigGridRelation, error)

	// UpdateBatchUIConfigGridRelation 批量设置UI配置与多个网格布局的关联关系
	// @description 将一个UIConfigId批量关联到多个GridInfoId
	UpdateBatchUIConfigGridRelation(ctx context.Context, uiConfigId int, gridInfoIds []int) error
}
