/**
 * ui_config_repository.go
 * UI配置仓库实现
 *
 * 该文件实现了UI配置的仓库接口，负责UI配置的数据库操作
 */

package repositories

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"o_mall_backend/modules/ui_config/dto"
	"o_mall_backend/modules/ui_config/models"
	"strings"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
)

// UIConfigRepositoryImpl UI配置仓库实现
type UIConfigRepositoryImpl struct{}

// NewUIConfigRepository 创建UI配置仓库
func NewUIConfigRepository() UIConfigRepository {
	return &UIConfigRepositoryImpl{}
}

// ListUIConfigs 获取UI配置列表
func (r *UIConfigRepositoryImpl) ListUIConfigs(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*dto.UIConfigRepositoryDTO, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.UIConfig))

	// 应用查询条件
	for k, v := range query {
		if v != nil && v != "" {
			qs = qs.Filter(k, v)
		}
	}

	// 处理draggable和resizable字段
	if v, ok := query["draggable"]; ok {
		qs = qs.Filter("draggable", v)
	}
	if v, ok := query["resizable"]; ok {
		qs = qs.Filter("resizable", v)
	}

	// 计算总数
	var total int64
	var err error
	total, err = qs.Count()
	if err != nil {
		logs.Error("[ListUIConfigs] 获取总数失败: %v", err)
		return nil, 0, err
	}

	// 如果没有数据，直接返回空列表
	if total == 0 {
		return []*dto.UIConfigRepositoryDTO{}, 0, nil
	}

	// 分页查询
	var uiConfigs []*models.UIConfig
	_, err = qs.OrderBy("-id").Limit(pageSize, (page-1)*pageSize).All(&uiConfigs)
	if err != nil {
		logs.Error("[ListUIConfigs] 查询失败: %v", err)
		return nil, 0, err
	}

	// 转换为DTO
	dtos := make([]*dto.UIConfigRepositoryDTO, 0, len(uiConfigs))
	for _, uiConfig := range uiConfigs {
		// 转换为DTO
		dtoItem := convertToUIConfigRepositoryDTO(uiConfig)

		// 获取关联的网格布局
		gridItems, err := r.getGridItemsByUIConfigID(ctx, o, uiConfig.ID)
		if err != nil {
			logs.Error("获取UI配置关联的网格布局失败: %v", err)
		}
		dtoItem.GridItems = gridItems

		dtos = append(dtos, dtoItem)
	}

	return dtos, total, nil
}

// GetUIConfig 根据ID获取UI配置详情
func (r *UIConfigRepositoryImpl) GetUIConfig(ctx context.Context, id int) (*dto.UIConfigRepositoryDTO, error) {
	o := orm.NewOrm()
	uiConfig := &models.UIConfig{ID: id}
	err := o.Read(uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("根据ID获取UI配置详情失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	dto := convertToUIConfigRepositoryDTO(uiConfig)

	// 获取关联的网格布局
	gridItems, err := r.getGridItemsByUIConfigID(ctx, o, id)
	if err != nil {
		logs.Error("获取UI配置关联的网格布局失败: %v", err)
		return nil, err
	}
	dto.GridItems = gridItems

	return dto, nil
}

// CreateUIConfig 创建UI配置
func (r *UIConfigRepositoryImpl) CreateUIConfig(ctx context.Context, config *dto.UIConfigCreateParams) (int, error) {
	o := orm.NewOrm()

	// 构建UI配置模型
	draggable := true
	if config.Draggable != false {
		draggable = config.Draggable
	}
	resizable := true
	if config.Resizable != false {
		resizable = config.Resizable
	}
	uiConfig := models.UIConfig{
		FrontendPath:  config.FrontendPath,
		ConfigType:    config.ConfigType,
		ConfigKey:     config.ConfigKey,
		ConfigContent: config.ConfigContent,
		Status:        config.Status,
		Remark:        config.Remark,
		Module:        config.Module,
		Title:         config.Title,
		Group:         config.Group,
		Version:       config.Version,
		Icon:          config.Icon,
		DTO:           config.DTO,
		Step:          config.Step,
		Permission:    config.Permission,
		Draggable:     draggable,
		Resizable:     resizable,
	}

	// 生成版本识别号和配置键
	uiConfig.GenerateVersionHash()
	uiConfig.GenerateConfigKey()

	// 插入数据
	id, err := o.Insert(uiConfig)
	if err != nil {
		logs.Error("[CreateUIConfig] 创建失败: %v", err)
		return 0, err
	}

	return int(id), nil
}

// UpdateUIConfig 更新UI配置
func (r *UIConfigRepositoryImpl) UpdateUIConfig(ctx context.Context, id int, config *dto.UIConfigUpdateParams) error {
	o := orm.NewOrm()

	// 获取现有配置
	uiConfig := &models.UIConfig{ID: id}
	err := o.Read(uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil
		}
		logs.Error("[UpdateUIConfig] 查询失败: %v", err)
		return err
	}

	// 更新字段
	if config.FrontendPath != "" {
		uiConfig.FrontendPath = config.FrontendPath
	}
	if config.ConfigContent != "" {
		uiConfig.ConfigContent = config.ConfigContent
	}
	if config.Status != 0 {
		uiConfig.Status = config.Status
	}
	if config.Remark != "" {
		uiConfig.Remark = config.Remark
	}
	if config.Title != "" {
		uiConfig.Title = config.Title
	}
	if config.Group != "" {
		uiConfig.Group = config.Group
	}
	if config.Version != "" {
		uiConfig.Version = config.Version
		// 版本变更需要重新生成版本识别号和配置键
		uiConfig.GenerateVersionHash()
		uiConfig.GenerateConfigKey()
	}
	if config.Icon != "" {
		uiConfig.Icon = config.Icon
	}
	if config.DTO != "" {
		uiConfig.DTO = config.DTO
	}
	if config.Draggable != false {
		uiConfig.Draggable = config.Draggable
	}
	if config.Resizable != false {
		uiConfig.Resizable = config.Resizable
	}

	// 保存更新
	_, err = o.Update(uiConfig)
	if err != nil {
		logs.Error("[UpdateUIConfig] 更新失败: %v", err)
		return err
	}

	return nil
}

// DeleteUIConfig 删除UI配置
func (r *UIConfigRepositoryImpl) DeleteUIConfig(ctx context.Context, id int) error {
	o := orm.NewOrm()

	// 删除关联的网格布局
	_, err := o.QueryTable(new(models.GridInfo)).Filter("ui_config_id", id).Delete()
	if err != nil {
		logs.Error("[DeleteUIConfig] 删除关联网格布局失败: %v", err)
		return err
	}

	// 删除UI配置
	_, err = o.Delete(&models.UIConfig{ID: id})
	if err != nil {
		logs.Error("[DeleteUIConfig] 删除失败: %v", err)
		return err
	}

	return nil
}

// UpdateUIConfigStatus 更新UI配置状态
func (r *UIConfigRepositoryImpl) UpdateUIConfigStatus(ctx context.Context, id int, status int) error {
	o := orm.NewOrm()

	// 获取现有配置
	uiConfig := &models.UIConfig{ID: id}
	err := o.Read(uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil
		}
		logs.Error("[UpdateUIConfigStatus] 查询失败: %v", err)
		return err
	}

	// 更新状态
	uiConfig.Status = status

	// 保存更新
	_, err = o.Update(uiConfig, "Status")
	if err != nil {
		logs.Error("[UpdateUIConfigStatus] 更新状态失败: %v", err)
		return err
	}

	return nil
}

// GetUIConfigByKey 根据配置Key获取UI配置详情
func (r *UIConfigRepositoryImpl) GetUIConfigByKey(ctx context.Context, configKey string) (*dto.UIConfigRepositoryDTO, error) {
	o := orm.NewOrm()
	uiConfig := new(models.UIConfig)
	err := o.QueryTable(new(models.UIConfig)).Filter("config_key", configKey).One(uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		logs.Error("根据配置Key获取UI配置详情失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	dto := convertToUIConfigRepositoryDTO(uiConfig)

	// 获取关联的网格布局
	gridItems, err := r.getGridItemsByUIConfigID(ctx, o, uiConfig.ID)
	if err != nil {
		logs.Error("获取UI配置关联的网格布局失败: %v", err)
	}
	dto.GridItems = gridItems

	return dto, nil
}

// GetConfigByPath 根据前端路径获取所有相关配置
func (r *UIConfigRepositoryImpl) GetConfigByPath(ctx context.Context, frontendPath string) ([]*dto.UIConfigRepositoryDTO, error) {
	o := orm.NewOrm()
	var uiConfigs []*models.UIConfig

	// 获取所有与指定前端路径匹配的UI配置
	_, err := o.QueryTable(new(models.UIConfig)).
		Filter("frontend_path", frontendPath).
		Filter("status", 1).
		OrderBy("id").
		All(&uiConfigs)

	if err != nil {
		logs.Error("根据前端路径获取UI配置列表失败: %v", err)
		return nil, err
	}

	// 如果没有匹配的配置，尝试进行更模糊的路径匹配
	if len(uiConfigs) == 0 {
		// 如果路径包含参数，如/user/:id，尝试匹配通配模式
		if strings.Contains(frontendPath, "/") {
			pathParts := strings.Split(frontendPath, "/")
			for i := len(pathParts) - 1; i >= 0; i-- {
				// 构造通配模式，如将/user/123替换为/user/*
				wildcardPath := strings.Join(pathParts[:i], "/") + "/*"
				if wildcardPath == "/*" {
					break
				}

				var wildcardConfigs []*models.UIConfig
				_, err = o.QueryTable(new(models.UIConfig)).
					Filter("frontend_path", wildcardPath).
					Filter("status", 1).
					OrderBy("id").
					All(&wildcardConfigs)

				if err != nil {
					logs.Error("根据通配路径获取UI配置列表失败: %v", err)
					continue
				}

				if len(wildcardConfigs) > 0 {
					uiConfigs = append(uiConfigs, wildcardConfigs...)
				}
			}
		}
	}

	// 转换为DTO列表
	dtos := make([]*dto.UIConfigRepositoryDTO, 0, len(uiConfigs))
	for _, uiConfig := range uiConfigs {
		dtoItem := convertToUIConfigRepositoryDTO(uiConfig)

		// 获取关联的网格布局
		gridItems, err := r.getGridItemsByUIConfigID(ctx, o, uiConfig.ID)
		if err != nil {
			logs.Error("获取UI配置关联的网格布局失败: %v", err)
		}
		dtoItem.GridItems = gridItems

		dtos = append(dtos, dtoItem)
	}

	return dtos, nil
}

// getGridItemsByUIConfigID 根据UI配置ID获取关联的网格布局
func (r *UIConfigRepositoryImpl) getGridItemsByUIConfigID(ctx context.Context, o orm.Ormer, uiConfigID int) ([]*dto.SimpleGridInfoDTO, error) {
	var relations []*models.UIConfigGridRelation
	_, err := o.QueryTable(new(models.UIConfigGridRelation)).
		Filter("ui_config_id", uiConfigID).
		Filter("status", 1).
		All(&relations)

	if err != nil {
		logs.Error("查询UI配置关联的网格布局关系失败: %v", err)
		return nil, err
	}

	if len(relations) == 0 {
		return []*dto.SimpleGridInfoDTO{}, nil
	}

	// 获取所有关联的GridInfo ID
	var gridInfoIDs []int
	for _, relation := range relations {
		gridInfoIDs = append(gridInfoIDs, relation.GridInfoID)
	}

	// 查询所有关联的GridInfo
	var gridItems []*models.GridInfo
	_, err = o.QueryTable(new(models.GridInfo)).
		Filter("id__in", gridInfoIDs).
		Filter("status", 1).
		All(&gridItems)

	if err != nil {
		logs.Error("查询网格布局详情失败: %v", err)
		return nil, err
	}

	// 转换为SimpleGridInfoDTO
	dtos := make([]*dto.SimpleGridInfoDTO, 0, len(gridItems))
	for _, item := range gridItems {
		// 查找该GridInfo与当前UIConfig的关联关系以获取position
		var position string
		for _, relation := range relations {
			if relation.GridInfoID == item.ID {
				// 从关联关系中获取position
				position = relation.Position
				break
			}
		}

		// 解析Position
		var positionObj interface{}
		if position != "" {
			err = json.Unmarshal([]byte(position), &positionObj)
			if err != nil {
				logs.Warn("解析网格布局Position失败: %v", err)
				positionObj = position
			}
		}

		dto := &dto.SimpleGridInfoDTO{
			ID:       item.ID,
			Name:     item.Name,
			Position: positionObj,
			Status:   item.Status,
		}
		dtos = append(dtos, dto)
	}

	return dtos, nil
}

// UpdateVersion 更新配置版本号
func (r *UIConfigRepositoryImpl) UpdateVersion(ctx context.Context, id int) (string, error) {
	o := orm.NewOrm()
	uiConfig := &models.UIConfig{ID: id}
	err := o.Read(uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return "", fmt.Errorf("UI配置不存在")
		}
		logs.Error("查询UI配置失败: %v", err)
		return "", err
	}

	// 更新版本号
	now := time.Now()
	uiConfig.Version = fmt.Sprintf("%d.%d.%d", now.Year(), now.Month(), now.Day())

	// 重新生成版本识别号和配置键
	uiConfig.GenerateVersionHash()
	uiConfig.GenerateConfigKey()

	// 更新数据
	_, err = o.Update(uiConfig, "version", "version_hash", "config_key")
	if err != nil {
		logs.Error("更新UI配置版本号失败: %v", err)
		return "", err
	}

	return uiConfig.VersionHash, nil
}

// convertToUIConfigRepositoryDTO 将UI配置模型转换为DTO
func convertToUIConfigRepositoryDTO(uiConfig *models.UIConfig) *dto.UIConfigRepositoryDTO {
	dto := &dto.UIConfigRepositoryDTO{
		ID:            uiConfig.ID,
		FrontendPath:  uiConfig.FrontendPath,
		VersionHash:   uiConfig.VersionHash,
		ConfigType:    uiConfig.ConfigType,
		ConfigKey:     uiConfig.ConfigKey,
		ConfigContent: uiConfig.ConfigContent,
		Status:        uiConfig.Status,
		Remark:        uiConfig.Remark,
		CreatedAt:     uiConfig.CreatedAt,
		UpdatedAt:     uiConfig.UpdatedAt,
		Module:        uiConfig.Module,
		Title:         uiConfig.Title,
		Group:         uiConfig.Group,
		Version:       uiConfig.Version,
		Icon:          uiConfig.Icon,
		DTO:           uiConfig.DTO,
		GridItems:     nil,
		Draggable:     uiConfig.Draggable,
		Resizable:     uiConfig.Resizable,
	}

	return dto
}
