/**
 * UI配置模块路由
 *
 * 该文件定义了UI配置模块的API路由
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/ui_config/controllers"
)

// Init 初始化UI配置模块路由
func Init() {
	// 创建UI配置控制器实例
	uiConfigController := &controllers.UIConfigController{}
	//创建命名空间
	ns := web.NewNamespace("/api/v1/ui-config",
		web.NSRouter("/ui-configs", uiConfigController, "post:CreateUIConfig;get:ListUIConfigs"),
		web.NSRouter("/ui-configs/:id", uiConfigController, "get:GetUIConfig;put:UpdateUIConfig;delete:DeleteUIConfig"),
		web.NSRouter("/ui-configs/key", uiConfigController, "get:GetUIConfigByKey"),
		web.NSRouter("/ui-configs/path", uiConfigController, "get:GetConfigByPath"),
		web.NSRouter("/ui-configs/:id/version", uiConfigController, "put:UpdateVersion"),
		web.NSRouter("/ui-configs/frontend-paths", uiConfigController, "get:GetFrontendPaths"),
	)
	web.AddNamespace(ns)
}
