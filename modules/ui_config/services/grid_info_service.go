/**
 * 网格布局配置服务接口
 *
 * 该文件定义了网格布局相关的服务接口
 */

package services

import (
	"context"
	"o_mall_backend/modules/ui_config/dto"
)

// GridInfoService 网格布局服务接口
type GridInfoService interface {
	// CreateGridInfo 创建网格布局
	CreateGridInfo(ctx context.Context, createDTO *dto.GridInfoCreateDTO) (int, error)

	// UpdateGridInfo 更新网格布局
	UpdateGridInfo(ctx context.Context, id int, updateDTO *dto.GridInfoUpdateDTO) error

	// DeleteGridInfo 删除网格布局
	DeleteGridInfo(ctx context.Context, id int) error

	// GetGridInfo 根据ID获取网格布局
	GetGridInfo(ctx context.Context, id int) (*dto.GridInfoResponseDTO, error)

	// ListGridInfos 分页查询网格布局
	ListGridInfos(ctx context.Context, queryDTO *dto.GridInfoQueryDTO) ([]*dto.GridInfoDTO, int64, error)

	// GetGridInfosByUIConfigID 获取指定UI配置ID的所有网格布局
	GetGridInfosByUIConfigID(ctx context.Context, uiConfigID int) ([]*dto.GridInfoResponseDTO, error)

	// BatchUpdateGridInfoPosition 批量更新网格布局位置
	BatchUpdateGridInfoPosition(ctx context.Context, batchDTO *dto.GridInfoBatchUpdatePositionDTO) error

	// UpdateGridInfoStatus 更新网格布局状态
	UpdateGridInfoStatus(ctx context.Context, id int, status int) error

	// ValidateGridInfoContent 验证网格布局内容是否符合JSON格式
	ValidateGridInfoContent(ctx context.Context, content string) error
}
