// ui_config_service_impl.go
// 该文件包含UIConfig相关服务的具体实现，包括UI配置的增删改查、网格布局与关联关系的处理等。

package impl

import (
	"encoding/json"
	"errors"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/ui_config/dto"
	"o_mall_backend/modules/ui_config/models"
	"o_mall_backend/modules/ui_config/services"
)

// UIConfigServiceImpl UI配置服务实现
type UIConfigServiceImpl struct{}

// NewUIConfigService 创建一个新的UI配置服务实例
func NewUIConfigService() services.UIConfigService {
	return &UIConfigServiceImpl{}
}

// CreateUIConfig 创建UI配置
func (s *UIConfigServiceImpl) CreateUIConfig(createDTO *dto.UIConfigCreateDTO) (int, error) {
	// 打印调试信息
	logs.Debug("[CreateUIConfig] 接收到的配置信息: Module=%s, ConfigType=%s, Version=%s, FrontendPath=%s, ConfigKey=%s",
		createDTO.Module, createDTO.ConfigType, createDTO.Version, createDTO.FrontendPath, createDTO.ConfigKey)

	// 验证配置内容
	err := s.ValidateConfig(createDTO.ConfigType, createDTO.ConfigContent)
	if err != nil {
		return 0, fmt.Errorf("配置内容无效: %v", err)
	}

	// 处理 permission 字段，将 []int 转换为 JSON 字符串
	var permissionStr string
	if len(createDTO.Permission) > 0 {
		permBytes, err := json.Marshal(createDTO.Permission)
		if err == nil {
			permissionStr = string(permBytes)
		} else {
			logs.Error("[CreateUIConfig] 序列化权限列表失败: %v", err)
		}
	}

	// 创建新配置
	uiConfig := models.UIConfig{
		FrontendPath:  createDTO.FrontendPath,
		ConfigType:    createDTO.ConfigType,
		ConfigContent: createDTO.ConfigContent,
		Status:        createDTO.Status,
		Remark:        createDTO.Remark,
		Module:        createDTO.Module,
		Title:         createDTO.Title,
		Group:         createDTO.Group,
		Version:       createDTO.Version,
		DTO:           createDTO.DTO,
		Step:          createDTO.Step, // 添加 step 字段
		Permission:    permissionStr,  // 添加处理后的 permission 字段
	}

	// 重新生成配置键，确保使用最新的组合逻辑
	uiConfig.GenerateConfigKey()
	logs.Debug("[CreateUIConfig] 重新生成的配置键: %s", uiConfig.ConfigKey)

	// 检查module和Version组合是否已存在
	o := orm.NewOrm()
	var configs []models.UIConfig
	_, err = o.QueryTable(new(models.UIConfig)).Filter("module", createDTO.Module).Filter("version", createDTO.Version).All(&configs)
	if err == nil && len(configs) > 0 {
		logs.Debug("[CreateUIConfig] 该模块下已存在相同版本的配置, 模块: %s, 版本: %s, 找到 %d 条记录",
			createDTO.Module, createDTO.Version, len(configs))
		return 0, fmt.Errorf("该模块(%s)下已存在相同版本(%s)的配置，请考虑更新现有配置或使用不同的版本", createDTO.Module, createDTO.Version)
	}

	// 检查重新生成的配置键是否已存在
	existNew := models.UIConfig{ConfigKey: uiConfig.ConfigKey}
	err = o.Read(&existNew, "ConfigKey")
	if err == nil {
		logs.Debug("[CreateUIConfig] 重新生成的配置键已存在: %s, 关联模块: %s, 版本: %s",
			existNew.ConfigKey, existNew.Module, existNew.Version)
		return 0, fmt.Errorf("该模块(%s)的该类型(%s)配置在指定版本(%s)下已存在，请使用不同的版本",
			createDTO.Module, createDTO.ConfigType, createDTO.Version)
	}

	// 生成版本识别号
	uiConfig.GenerateVersionHash()

	// 保存到数据库
	id, err := o.Insert(&uiConfig)
	if err != nil {
		logs.Error("[CreateUIConfig] 插入数据失败: %v", err)
		return 0, fmt.Errorf("创建配置失败: %v", err)
	}

	return int(id), nil
}

// UpdateUIConfig 更新UI配置
func (s *UIConfigServiceImpl) UpdateUIConfig(id int, updateDTO *dto.UIConfigUpdateDTO) error {
	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[UpdateUIConfig] 开启事务失败: %v", err)
		return fmt.Errorf("更新配置失败: %v", err)
	}

	// 使用事务对象
	uiConfig := models.UIConfig{ID: id}
	err = tx.Read(&uiConfig)
	if err != nil {
		tx.Rollback()
		if err == orm.ErrNoRows {
			return errors.New("配置不存在")
		}
		logs.Error("[UpdateUIConfig] 读取数据失败: %v", err)
		return fmt.Errorf("更新配置失败: %v", err)
	}

	// 如果更新配置内容，需要验证
	if updateDTO.ConfigContent != "" {
		err = s.ValidateConfig(uiConfig.ConfigType, updateDTO.ConfigContent)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("配置内容无效: %v", err)
		}
		uiConfig.ConfigContent = updateDTO.ConfigContent
		// 更新版本识别号
		uiConfig.GenerateVersionHash()
	}

	// 更新其他字段
	if updateDTO.FrontendPath != "" {
		uiConfig.FrontendPath = updateDTO.FrontendPath
	}
	if updateDTO.Status >= 0 {
		uiConfig.Status = updateDTO.Status
	}
	if updateDTO.Remark != "" {
		uiConfig.Remark = updateDTO.Remark
	}
	if updateDTO.Title != "" {
		uiConfig.Title = updateDTO.Title
	}
	if updateDTO.Icon != "" {
		uiConfig.Icon = updateDTO.Icon
	}
	if updateDTO.Group != "" {
		uiConfig.Group = updateDTO.Group
	}
	if updateDTO.Version != "" {
		uiConfig.Version = updateDTO.Version
	}
	if updateDTO.DTO != "" {
		uiConfig.DTO = updateDTO.DTO
	}
	uiConfig.Draggable = updateDTO.Draggable
	uiConfig.Resizable = updateDTO.Resizable

	// 如果设置当前记录为启用状态，则需要将同module同frontendpath的其他记录设置为禁用
	if updateDTO.Status == 1 {
		// 构建查询条件：同module同frontendpath，但不包括当前ID
		var otherConfigs []*models.UIConfig
		_, err = tx.QueryTable(new(models.UIConfig)).
			Filter("module", uiConfig.Module).
			Filter("frontend_path", uiConfig.FrontendPath).
			Exclude("id", uiConfig.ID).
			All(&otherConfigs)

		if err != nil {
			tx.Rollback()
			logs.Error("[UpdateUIConfig] 查询同组配置失败: %v", err)
			return fmt.Errorf("更新配置失败: %v", err)
		}

		// 将其他记录设置为禁用状态
		for _, config := range otherConfigs {
			if config.Status == 1 { // 只更新启用状态的记录
				config.Status = 0
				_, err = tx.Update(config, "Status")
				if err != nil {
					tx.Rollback()
					logs.Error("[UpdateUIConfig] 禁用其他配置失败: %v", err)
					return fmt.Errorf("更新配置失败: %v", err)
				}
			}
		}
	}

	// 保存更新
	_, err = tx.Update(&uiConfig)
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateUIConfig] 更新数据失败: %v", err)
		return fmt.Errorf("更新配置失败: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdateUIConfig] 提交事务失败: %v", err)
		return fmt.Errorf("更新配置失败: %v", err)
	}

	return nil
}

// DeleteUIConfig 删除UI配置
func (s *UIConfigServiceImpl) DeleteUIConfig(id int) error {
	o := orm.NewOrm()
	uiConfig := models.UIConfig{ID: id}
	err := o.Read(&uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return errors.New("配置不存在")
		}
		logs.Error("[DeleteUIConfig] 读取数据失败: %v", err)
		return fmt.Errorf("删除配置失败: %v", err)
	}

	_, err = o.Delete(&uiConfig)
	if err != nil {
		logs.Error("[DeleteUIConfig] 删除数据失败: %v", err)
		return fmt.Errorf("删除配置失败: %v", err)
	}

	return nil
}

// GetUIConfig 根据ID获取UI配置
func (s *UIConfigServiceImpl) GetUIConfig(id int) (*dto.UIConfigDTO, error) {
	o := orm.NewOrm()
	uiConfig := models.UIConfig{ID: id}
	err := o.Read(&uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("配置不存在")
		}
		logs.Error("[GetUIConfig] 读取数据失败: %v", err)
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	// 加载关联的网格布局项
	err = uiConfig.LoadGridItems(o)
	if err != nil {
		logs.Error("[GetUIConfig] 加载网格布局项失败: %v", err)
		// 不影响主流程，继续执行
	}
	logs.Debug("[GetUIConfig] 加载到GridItems数量: %d", len(uiConfig.GridItems))
	// 将GridInfo转换为GridInfoDTO
	gridItemDTOs := make([]*dto.GridInfoDTO, 0)
	if uiConfig.GridItems != nil && len(uiConfig.GridItems) > 0 {
		for _, item := range uiConfig.GridItems {
			// 从关联关系中获取位置信息
			relation, err := s.GetGridItemRelation(uiConfig.ID, item.ID)
			if err == nil && relation != nil {
				// 解析Position JSON字符串为对象
				var positionObj interface{}
				if relation.Position != "" {
					if err := json.Unmarshal([]byte(relation.Position), &positionObj); err != nil {
						logs.Error("[GetUIConfig] 解析Position JSON字符串失败: %v", err)
					}
				}
				
				gridItemDTO := &dto.GridInfoDTO{
					ID:         item.ID,
					Name:       item.Name,
					Content:    item.Content,
					API:        item.API,
					DTO:        item.DTO,
					Remark:     item.Remark,
					Status:     item.Status,
					CreatedAt:  item.CreatedAt,
					UpdatedAt:  item.UpdatedAt,
					Position:   positionObj, // 使用解析后的对象
					Step:       item.Step,       // 添加 step 字段
					Permission: item.Permission, // 添加 permission 字段
				}
				gridItemDTOs = append(gridItemDTOs, gridItemDTO)
			}
		}
	}

	return &dto.UIConfigDTO{
		ID:            uiConfig.ID,
		Icon:          uiConfig.Icon,
		FrontendPath:  uiConfig.FrontendPath,
		VersionHash:   uiConfig.VersionHash,
		ConfigType:    uiConfig.ConfigType,
		ConfigKey:     uiConfig.ConfigKey,
		ConfigContent: uiConfig.ConfigContent,
		Status:        uiConfig.Status,
		Remark:        uiConfig.Remark,
		CreatedAt:     uiConfig.CreatedAt,
		UpdatedAt:     uiConfig.UpdatedAt,
		Module:        uiConfig.Module,
		Title:         uiConfig.Title,
		Group:         uiConfig.Group,
		Version:       uiConfig.Version,
		DTO:           uiConfig.DTO,
		GridItems:     gridItemDTOs,
	}, nil
}

// GetUIConfigByKey 根据配置Key获取UI配置
func (s *UIConfigServiceImpl) GetUIConfigByKey(configKey string) (*dto.UIConfigResponseDTO, error) {
	o := orm.NewOrm()
	uiConfig := models.UIConfig{ConfigKey: configKey}
	err := o.Read(&uiConfig, "ConfigKey")
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("配置不存在")
		}
		logs.Error("[GetUIConfigByKey] 读取数据失败: %v", err)
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	// 检查状态是否启用
	if uiConfig.Status != 1 {
		return nil, errors.New("配置已禁用")
	}

	// 解析配置内容
	var configContent interface{}
	err = json.Unmarshal([]byte(uiConfig.ConfigContent), &configContent)
	if err != nil {
		logs.Error("[GetUIConfigByKey] 解析配置内容失败: %v", err)
		return nil, fmt.Errorf("解析配置内容失败: %v", err)
	}

	// 解析DTO
	var dtoData interface{}
	if uiConfig.DTO != "" {
		err = json.Unmarshal([]byte(uiConfig.DTO), &dtoData)
		if err != nil {
			logs.Error("[GetUIConfigByKey] 解析DTO数据失败: %v", err)
			// DTO解析失败不影响主要功能
		}
	}

	return &dto.UIConfigResponseDTO{
		FrontendPath:  uiConfig.FrontendPath,
		VersionHash:   uiConfig.VersionHash,
		ConfigType:    uiConfig.ConfigType,
		ConfigKey:     uiConfig.ConfigKey,
		ConfigContent: configContent,
		Module:        uiConfig.Module,
		Title:         uiConfig.Title,
		Group:         uiConfig.Group,
		Version:       uiConfig.Version,
		DTO:           dtoData,
	}, nil
}

// ListUIConfigs 分页查询UI配置
func (s *UIConfigServiceImpl) ListUIConfigs(queryDTO *dto.UIConfigQueryDTO) ([]*dto.UIConfigDTO, int64, error) {
	o := orm.NewOrm()               // 获取 ORM 实例
	qs := o.QueryTable("ui_config") // 获取 ui_config 表的查询集

	// 添加查询条件 (根据 queryDTO)
	if queryDTO.FrontendPath != "" {
		qs = qs.Filter("frontend_path", queryDTO.FrontendPath)
	}
	if queryDTO.ConfigType != "" {
		qs = qs.Filter("config_type", queryDTO.ConfigType)
	}
	if queryDTO.ConfigKey != "" {
		qs = qs.Filter("config_key__contains", queryDTO.ConfigKey) // 使用模糊查询
	}
	if queryDTO.Module != "" {
		qs = qs.Filter("module", queryDTO.Module)
	}
	if queryDTO.Group != "" {
		// 注意：模型中 Group 字段名为 Group，数据库字段可能是 group_name 或 group，请根据实际情况调整
		// 假设数据库字段是 group_name
		qs = qs.Filter("group_name", queryDTO.Group)
	}
	if queryDTO.Status >= 0 { // 允许查询特定状态 (0 或 1)
		qs = qs.Filter("status", queryDTO.Status)
	}

	// 获取总数
	count, err := qs.Count()
	if err != nil {
		logs.Error("[ListUIConfigs] 查询总数失败: %v", err)
		return nil, 0, fmt.Errorf("查询配置失败: %v", err)
	}

	// 处理分页参数
	page := queryDTO.Page
	if page <= 0 {
		page = 1 // 默认第一页
	}
	pageSize := queryDTO.PageSize
	if pageSize <= 0 {
		pageSize = 10 // 默认每页10条
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询数据
	var configs []*models.UIConfig                                          // 用于存储从数据库查询到的原始配置模型列表
	_, err = qs.OrderBy("-id").Offset(offset).Limit(pageSize).All(&configs) // 按ID降序排序，应用分页
	if err != nil {
		logs.Error("[ListUIConfigs] 查询数据失败: %v", err)
		return nil, 0, fmt.Errorf("查询配置失败: %v", err)
	}

	// 转换为DTO (Data Transfer Object)
	var result []*dto.UIConfigDTO // 初始化最终返回的 DTO 列表

	// 遍历查询到的每个配置模型
	for _, config := range configs { // 开始外层循环 (遍历每个 UIConfig)
		// 加载关联的网格布局项 (GridItems)
		// 注意: LoadGridItems 方法需要一个 orm.Ormer 参数，确保在循环外或内创建 o
		err = config.LoadGridItems(o) // 尝试加载与当前 config 关联的 GridItems
		logs.Debug("[ListUIConfigs] 加载到GridItems数量: %d", len(config.GridItems))
		if err != nil {
			// 如果加载失败，记录错误，但通常不中断整个列表的获取
			logs.Error("[ListUIConfigs] 加载网格布局项失败: %v, UIConfigID: %d", err, config.ID)
			// 可以选择 'continue' 跳过这个 config，或者像现在这样继续处理（GridItems 为空）
		}

		// 将 GridInfo (数据库模型) 转换为 GridInfoDTO (传输对象)
		gridItemDTOs := make([]*dto.GridInfoDTO, 0)               // 为当前 config 初始化一个空的 GridInfoDTO 切片
		if config.GridItems != nil && len(config.GridItems) > 0 { // 检查是否有 GridItems 需要处理
			// 遍历当前 config 的每个 GridItem 模型
			for _, item := range config.GridItems { // 开始内层循环 (遍历每个 GridItem)
				// 创建基础的 GridInfoDTO
				gridItemDTO := &dto.GridInfoDTO{
					ID:         item.ID,
					Name:       item.Name,
					Content:    item.Content,
					API:        item.API,
					DTO:        item.DTO, // GridItem 的 DTO 字段
					Remark:     item.Remark,
					Status:     item.Status,
					CreatedAt:  item.CreatedAt,
					UpdatedAt:  item.UpdatedAt,
					Step:       item.Step,       // 添加 step 字段
					Permission: item.Permission, // 添加 permission 字段
					// Position字段已从GridInfo模型移除，现在从UIConfigGridRelation关联表中获取
				}

				// 从关联关系中获取位置信息
				relation, err := s.GetGridItemRelation(config.ID, item.ID)
				if err == nil && relation != nil {
					// 解析Position JSON字符串为对象
					var positionObj interface{}
					if relation.Position != "" {
						if err := json.Unmarshal([]byte(relation.Position), &positionObj); err != nil {
							logs.Error("[ListUIConfigs] 解析Position JSON字符串失败: %v", err)
						}
					}
					gridItemDTO.Position = positionObj
					// 使用 parseStepField 保证 step 字段为数组
					gridItemDTO.Step = parseStepField(relation.Step)
					if relation.Permission != "" {
						var permissions []int
						err := json.Unmarshal([]byte(relation.Permission), &permissions)
						if err == nil {
							gridItemDTO.Permission = permissions
						} else {
							// 解析失败则使用空数组
							gridItemDTO.Permission = []int{}
						}
					} else {
						// permission 字段为空则使用空数组
						gridItemDTO.Permission = []int{}
					}
				}

				// 将转换后的 gridItemDTO 添加到当前 config 的 DTO 列表中
				gridItemDTOs = append(gridItemDTOs, gridItemDTO)

			} // 结束内层循环 (遍历每个 GridItem)
		} // 结束 if 条件 (检查 GridItems 是否存在)

		// 创建 UIConfigDTO，并将处理好的 GridItems DTO 列表赋值给它
		result = append(result, &dto.UIConfigDTO{
			ID:            config.ID,
			Icon:          config.Icon,
			Draggable:     config.Draggable,
			Resizable:     config.Resizable,
			FrontendPath:  config.FrontendPath,
			VersionHash:   config.VersionHash,
			ConfigType:    config.ConfigType,
			ConfigKey:     config.ConfigKey,
			ConfigContent: config.ConfigContent, // ConfigContent 保持为原始字符串，如果需要在列表接口解析，则在这里处理
			Status:        config.Status,
			Remark:        config.Remark,
			CreatedAt:     config.CreatedAt,
			UpdatedAt:     config.UpdatedAt,
			Module:        config.Module,
			Title:         config.Title,
			Group:         config.Group, // 模型中的 Group 字段
			Version:       config.Version,
			DTO:           config.DTO,   // UIConfig 的 DTO 字段，保持原始字符串
			GridItems:     gridItemDTOs, // 赋值转换后的 GridItem DTO 列表
		})

	} // 结束外层循环 (遍历每个 UIConfig)

	// 返回结果列表、总数和 nil 错误
	return result, count, nil
}

// GetConfigByPath 根据前端路径获取所有相关配置
func (s *UIConfigServiceImpl) GetConfigByPath(frontendPath string) ([]*dto.UIConfigResponseDTO, error) {
	o := orm.NewOrm()
	var configs []*models.UIConfig
	_, err := o.QueryTable("ui_config").Filter("frontend_path", frontendPath).Filter("status", 1).All(&configs)
	if err != nil {
		logs.Error("[GetConfigByPath] 查询数据失败: %v", err)
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	var result []*dto.UIConfigResponseDTO
	for _, config := range configs {
		// 解析配置内容
		var configContent interface{}
		err = json.Unmarshal([]byte(config.ConfigContent), &configContent)
		if err != nil {
			logs.Error("[GetConfigByPath] 解析配置内容失败: %v", err)
			continue
		}

		// 解析DTO内容
		var dtoContent interface{}
		if config.DTO != "" {
			err = json.Unmarshal([]byte(config.DTO), &dtoContent)
			if err != nil {
				logs.Error("[GetConfigByPath] 解析DTO内容失败: %v", err)
				// DTO解析失败不影响正常使用，继续处理
			}
		}

		result = append(result, &dto.UIConfigResponseDTO{
			FrontendPath:  config.FrontendPath,
			VersionHash:   config.VersionHash,
			ConfigType:    config.ConfigType,
			ConfigKey:     config.ConfigKey,
			ConfigContent: configContent,
			Module:        config.Module,
			Title:         config.Title,
			Group:         config.Group,
			Version:       config.Version,
			DTO:           dtoContent,
		})
	}

	return result, nil
}

// UpdateVersion 更新配置版本号
func (s *UIConfigServiceImpl) UpdateVersion(id int) (string, error) {
	o := orm.NewOrm()
	uiConfig := models.UIConfig{ID: id}
	err := o.Read(&uiConfig)
	if err != nil {
		if err == orm.ErrNoRows {
			return "", errors.New("配置不存在")
		}
		logs.Error("[UpdateVersion] 读取数据失败: %v", err)
		return "", fmt.Errorf("更新版本号失败: %v", err)
	}

	// 更新版本识别号
	uiConfig.GenerateVersionHash()

	// 保存更新
	_, err = o.Update(&uiConfig, "VersionHash")
	if err != nil {
		logs.Error("[UpdateVersion] 更新版本号失败: %v", err)
		return "", fmt.Errorf("更新版本号失败: %v", err)
	}

	return uiConfig.VersionHash, nil
}

// ValidateConfig 验证配置内容是否符合JSON格式
func (s *UIConfigServiceImpl) ValidateConfig(configType string, configContent string) error {
	// 检查JSON格式
	var jsonData interface{}
	err := json.Unmarshal([]byte(configContent), &jsonData)
	if err != nil {
		return fmt.Errorf("JSON格式无效: %v", err)
	}

	// 根据配置类型进行特定验证
	switch configType {
	case "form":
		// 验证form配置的必要字段
		formConfig, ok := jsonData.(map[string]interface{})
		if !ok {
			return errors.New("form配置必须是一个对象")
		}

		// 检查表单必要字段
		_, hasSchema := formConfig["schema"]
		_, hasUiSchema := formConfig["uiSchema"]

		if !hasSchema {
			return errors.New("form配置缺少必要字段: schema")
		}
		if !hasUiSchema {
			return errors.New("form配置缺少必要字段: uiSchema")
		}

	case "table":
		// 验证table配置的必要字段
		tableConfig, ok := jsonData.(map[string]interface{})
		if !ok {
			return errors.New("table配置必须是一个对象")
		}

		// 检查表格必要字段
		_, hasColumnDefs := tableConfig["columnDefs"]

		if !hasColumnDefs {
			return errors.New("table配置缺少必要字段: columnDefs")
		}

	case "service", "info", "search", "tools", "page":
		// 这些类型只验证JSON格式，不做特定字段验证
		_, ok := jsonData.(map[string]interface{})
		if !ok {
			return fmt.Errorf("%s配置必须是一个对象", configType)
		}

	default:
		return fmt.Errorf("不支持的配置类型: %s", configType)
	}

	return nil
}

// GetUIConfigByModuleAndType 根据模块和配置类型获取UI配置
func (s *UIConfigServiceImpl) GetUIConfigByModuleAndType(module string, configType string) (*dto.UIConfigResponseDTO, error) {
	o := orm.NewOrm()
	uiConfig := models.UIConfig{
		Module:     module,
		ConfigType: configType,
	}
	err := o.Read(&uiConfig, "Module", "ConfigType")
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("配置不存在")
		}
		logs.Error("[GetUIConfigByModuleAndType] 读取数据失败: %v", err)
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	// 检查状态是否启用
	if uiConfig.Status != 1 {
		return nil, errors.New("配置已禁用")
	}

	// 解析配置内容
	var configContent interface{}
	err = json.Unmarshal([]byte(uiConfig.ConfigContent), &configContent)
	if err != nil {
		logs.Error("[GetUIConfigByModuleAndType] 解析配置内容失败: %v", err)
		return nil, fmt.Errorf("解析配置内容失败: %v", err)
	}

	// 解析DTO
	var dtoData interface{}
	if uiConfig.DTO != "" {
		err = json.Unmarshal([]byte(uiConfig.DTO), &dtoData)
		if err != nil {
			logs.Error("[GetUIConfigByModuleAndType] 解析DTO数据失败: %v", err)
			// DTO解析失败不影响主要功能
		}
	}

	return &dto.UIConfigResponseDTO{
		FrontendPath:  uiConfig.FrontendPath,
		VersionHash:   uiConfig.VersionHash,
		ConfigType:    uiConfig.ConfigType,
		ConfigKey:     uiConfig.ConfigKey,
		ConfigContent: configContent,
		Module:        uiConfig.Module,
		Title:         uiConfig.Title,
		Group:         uiConfig.Group,
		Version:       uiConfig.Version,
		DTO:           dtoData,
	}, nil
}

// GetUIConfigsByModule 获取指定模块的所有UI配置
func (s *UIConfigServiceImpl) GetUIConfigsByModule(module string) ([]*dto.UIConfigResponseDTO, error) {
	o := orm.NewOrm()
	qs := o.QueryTable("ui_config").Filter("module", module).Filter("status", 1)

	var configs []*models.UIConfig
	_, err := qs.All(&configs)
	if err != nil {
		logs.Error("[GetUIConfigsByModule] 查询数据失败: %v", err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}

	var result []*dto.UIConfigResponseDTO
	for _, config := range configs {
		var configContent interface{}
		err = json.Unmarshal([]byte(config.ConfigContent), &configContent)
		if err != nil {
			logs.Error("[GetUIConfigsByModule] 解析配置内容失败: %v", err)
			continue
		}

		// 解析DTO
		var dtoData interface{}
		if config.DTO != "" {
			err = json.Unmarshal([]byte(config.DTO), &dtoData)
			if err != nil {
				logs.Error("[GetUIConfigsByModule] 解析DTO数据失败: %v", err)
				// DTO解析失败不影响主要功能
			}
		}

		result = append(result, &dto.UIConfigResponseDTO{
			FrontendPath:  config.FrontendPath,
			VersionHash:   config.VersionHash,
			ConfigType:    config.ConfigType,
			ConfigKey:     config.ConfigKey,
			ConfigContent: configContent,
			Module:        config.Module,
			Title:         config.Title,
			Group:         config.Group,
			Version:       config.Version,
			DTO:           dtoData,
		})
	}

	return result, nil
}

// GetUIConfigsByModuleAndGroup 获取指定模块和分组的UI配置
func (s *UIConfigServiceImpl) GetUIConfigsByModuleAndGroup(module string, group string) ([]*dto.UIConfigResponseDTO, error) {
	o := orm.NewOrm()
	qs := o.QueryTable("ui_config").Filter("module", module).Filter("group_name", group).Filter("status", 1)

	var configs []*models.UIConfig
	_, err := qs.All(&configs)
	if err != nil {
		logs.Error("[GetUIConfigsByModuleAndGroup] 查询数据失败: %v", err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}

	var result []*dto.UIConfigResponseDTO
	for _, config := range configs {
		var configContent interface{}
		err = json.Unmarshal([]byte(config.ConfigContent), &configContent)
		if err != nil {
			logs.Error("[GetUIConfigsByModuleAndGroup] 解析配置内容失败: %v", err)
			continue
		}

		// 解析DTO
		var dtoData interface{}
		if config.DTO != "" {
			err = json.Unmarshal([]byte(config.DTO), &dtoData)
			if err != nil {
				logs.Error("[GetUIConfigsByModuleAndGroup] 解析DTO数据失败: %v", err)
				// DTO解析失败不影响主要功能
			}
		}

		result = append(result, &dto.UIConfigResponseDTO{
			FrontendPath:  config.FrontendPath,
			VersionHash:   config.VersionHash,
			ConfigType:    config.ConfigType,
			ConfigKey:     config.ConfigKey,
			ConfigContent: configContent,
			Module:        config.Module,
			Title:         config.Title,
			Group:         config.Group,
			Version:       config.Version,
			DTO:           dtoData,
		})
	}

	return result, nil
}

// GetUIConfigsByModuleAndType 获取指定模块和配置类型的所有UI配置
func (s *UIConfigServiceImpl) GetUIConfigsByModuleAndType(module string, configType string) ([]*dto.UIConfigResponseDTO, error) {
	o := orm.NewOrm()
	qs := o.QueryTable("ui_config").Filter("module", module).Filter("config_type", configType).Filter("status", 1)

	var configs []*models.UIConfig
	_, err := qs.All(&configs)
	if err != nil {
		logs.Error("[GetUIConfigsByModuleAndType] 查询数据失败: %v", err)
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}

	var result []*dto.UIConfigResponseDTO
	for _, config := range configs {
		var configContent interface{}
		err = json.Unmarshal([]byte(config.ConfigContent), &configContent)
		if err != nil {
			logs.Error("[GetUIConfigsByModuleAndType] 解析配置内容失败: %v", err)
			continue
		}

		// 解析DTO
		var dtoData interface{}
		if config.DTO != "" {
			err = json.Unmarshal([]byte(config.DTO), &dtoData)
			if err != nil {
				logs.Error("[GetUIConfigsByModuleAndType] 解析DTO数据失败: %v", err)
				// DTO解析失败不影响主要功能
			}
		}

		result = append(result, &dto.UIConfigResponseDTO{
			FrontendPath:  config.FrontendPath,
			VersionHash:   config.VersionHash,
			ConfigType:    config.ConfigType,
			ConfigKey:     config.ConfigKey,
			ConfigContent: configContent,
			Module:        config.Module,
			Title:         config.Title,
			Group:         config.Group,
			Version:       config.Version,
			DTO:           dtoData,
		})
	}

	return result, nil
}

// GetFrontendPaths 获取所有前端路径列表
func (s *UIConfigServiceImpl) GetFrontendPaths() ([]*dto.FrontendPathGroupDTO, error) {
	o := orm.NewOrm()

	// 查询所有启用状态的不同模块名称
	var modules []string
	_, err := o.Raw("SELECT DISTINCT module FROM ui_config WHERE status = 1 ORDER BY module").QueryRows(&modules)
	if err != nil {
		logs.Error("[GetFrontendPaths] 查询模块列表失败: %v", err)
		return nil, fmt.Errorf("查询前端路径失败: %v", err)
	}

	var result []*dto.FrontendPathGroupDTO

	// 对每个模块查询配置
	for _, module := range modules {
		var configs []*models.UIConfig

		// 查询该模块下所有启用的配置
		_, err := o.Raw(`
			SELECT id, frontend_path, title, config_type, module, group_name, icon, config_key, version_hash, version
			FROM ui_config 
			WHERE module = ? AND status = 1 
			ORDER BY frontend_path, config_type
		`, module).QueryRows(&configs)

		if err != nil {
			logs.Error("[GetFrontendPaths] 查询模块 %s 的配置失败: %v", module, err)
			continue
		}

		// 没有配置则跳过
		if len(configs) == 0 {
			continue
		}

		// 创建模块分组
		moduleGroup := &dto.FrontendPathGroupDTO{
			Module: module,
			Paths:  make([]*dto.FrontendPathItem, 0, len(configs)),
		}

		// 添加每条配置记录
		for _, config := range configs {
			moduleGroup.Paths = append(moduleGroup.Paths, &dto.FrontendPathItem{
				Path:        config.FrontendPath,
				Title:       config.Title,
				ConfigType:  config.ConfigType,
				Group:       config.Group,
				Icon:        config.Icon,
				ConfigKey:   config.ConfigKey,
				Version:     config.Version,
				VersionHash: config.VersionHash,
			})
		}

		result = append(result, moduleGroup)
	}

	return result, nil
}

// AddGridItem 添加关联的网格布局项目（带位置信息）
func (s *UIConfigServiceImpl) AddGridItem(uiConfigID int, gridInfoID int, positionJSON string) error {
	o := orm.NewOrm()

	// 检查UIConfig是否存在
	uiConfig := &models.UIConfig{ID: uiConfigID}
	err := o.Read(uiConfig)
	if err != nil {
		logs.Error("[AddGridItem] 读取UI配置失败: %v", err)
		return fmt.Errorf("UI配置不存在: %v", err)
	}

	// 检查GridInfo是否存在
	gridInfo := &models.GridInfo{ID: gridInfoID}
	err = o.Read(gridInfo)
	if err != nil {
		logs.Error("[AddGridItem] 读取网格布局失败: %v", err)
		return fmt.Errorf("网格布局不存在: %v", err)
	}

	// 添加关联关系
	err = uiConfig.AddGridItem(o, gridInfoID)
	if err != nil {
		logs.Error("[AddGridItem] 添加关联关系失败: %v", err)
		return fmt.Errorf("添加关联关系失败: %v", err)
	}

	// 如果提供了位置信息，则更新位置
	if positionJSON != "" {
		// 获取关联关系
		relation, err := s.GetGridItemRelation(uiConfigID, gridInfoID)
		if err != nil {
			logs.Error("[AddGridItem] 获取关联关系失败: %v", err)
			return fmt.Errorf("添加关联关系失败: %v", err)
		}

		// 更新位置信息
		relation.Position = positionJSON
		
		// 默认步骤为[0]
		stepArray := []int{0}
		stepJSON, err := json.Marshal(stepArray)
		if err != nil {
			logs.Error("[AddGridItem] 序列化步骤信息失败: %v", err)
			return fmt.Errorf("添加关联关系失败: %v", err)
		}
		relation.Step = string(stepJSON)
		
		_, err = o.Update(relation, "Position", "Step")
		if err != nil {
			logs.Error("[AddGridItem] 更新位置和步骤信息失败: %v", err)
			return fmt.Errorf("添加关联关系失败: %v", err)
		}
	}

	return nil
}

// BatchUpdateGridItems 批量更新UI配置关联的网格布局项目
func (s *UIConfigServiceImpl) BatchUpdateGridItems(uiConfigID int, items []dto.GridInfoWithPositionCreateDTO) error {
	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		logs.Error("[BatchUpdateGridItems] 开启事务失败: %v", err)
		return fmt.Errorf("批量更新网格布局项目失败: %v", err)
	}

	// 检查UIConfig是否存在
	uiConfig := &models.UIConfig{ID: uiConfigID}
	err = tx.Read(uiConfig)
	if err != nil {
		tx.Rollback()
		logs.Error("[BatchUpdateGridItems] 读取UI配置失败: %v", err)
		return fmt.Errorf("UI配置不存在: %v", err)
	}

	// 先获取所有现有的关联关系
	var relations []*models.UIConfigGridRelation
	_, err = tx.QueryTable(new(models.UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).All(&relations)
	if err != nil {
		tx.Rollback()
		logs.Error("[BatchUpdateGridItems] 查询现有关联关系失败: %v", err)
		return fmt.Errorf("批量更新网格布局项目失败: %v", err)
	}

	// 将所有现有的关联关系状态设置为0（禁用）
	for _, rel := range relations {
		rel.Status = 0
		_, err = tx.Update(rel, "Status", "UpdatedAt")
		if err != nil {
			tx.Rollback()
			logs.Error("[BatchUpdateGridItems] 禁用现有关联关系失败: %v", err)
			return fmt.Errorf("批量更新网格布局项目失败: %v", err)
		}
	}

	// 为每个新的项目创建或更新关联关系
	for _, item := range items {
		// 检查GridInfo是否存在
		gridInfo := &models.GridInfo{ID: item.GridInfoID}
		err = tx.Read(gridInfo)
		if err != nil {
			tx.Rollback()
			logs.Error("[BatchUpdateGridItems] 读取网格布局失败, ID: %d, 错误: %v", item.GridInfoID, err)
			return fmt.Errorf("网格布局不存在, ID: %d", item.GridInfoID)
		}

		// 构建位置信息
		positionData, ok := item.Position.(map[string]interface{})
		if !ok {
			tx.Rollback()
			logs.Error("[BatchUpdateGridItems] 位置信息格式不正确, GridInfoID: %d", item.GridInfoID)
			return fmt.Errorf("位置信息格式不正确, GridInfoID: %d", item.GridInfoID)
		}

		// 由于models.PositionInfo结构体中已移除了X、Y、W、H等字段
		// 这里我们创建一个临时的匿名结构体来保存位置信息
		// 稍后会将其转换为JSON格式存储到数据库中
		var tempPosInfo struct {
			X            int  `json:"x"`
			Y            int  `json:"y"`
			W            int  `json:"w"`
			H            int  `json:"h"`
			MinW         int  `json:"min_w,omitempty"`
			MinH         int  `json:"min_h,omitempty"`
			MaxW         int  `json:"max_w,omitempty"`
			MaxH         int  `json:"max_h,omitempty"`
			Locked       bool `json:"locked,omitempty"`
			NoMove       bool `json:"no_move,omitempty"`
			NoResize     bool `json:"no_resize,omitempty"`
			AutoPosition bool `json:"auto_position,omitempty"`
			//LastUiPosition *models.LastUiPositionInfo `json:"lastUiPosition,omitempty"`
		}

		// 从positionData中提取值
		if x, ok := positionData["x"].(float64); ok {
			tempPosInfo.X = int(x)
		}
		if y, ok := positionData["y"].(float64); ok {
			tempPosInfo.Y = int(y)
		}
		if w, ok := positionData["w"].(float64); ok {
			tempPosInfo.W = int(w)
		}
		if h, ok := positionData["h"].(float64); ok {
			tempPosInfo.H = int(h)
		}
		if minW, ok := positionData["min_w"].(float64); ok {
			tempPosInfo.MinW = int(minW)
		}
		if minH, ok := positionData["min_h"].(float64); ok {
			tempPosInfo.MinH = int(minH)
		}
		if maxW, ok := positionData["max_w"].(float64); ok {
			tempPosInfo.MaxW = int(maxW)
		}
		if maxH, ok := positionData["max_h"].(float64); ok {
			tempPosInfo.MaxH = int(maxH)
		}
		if locked, ok := positionData["locked"].(bool); ok {
			tempPosInfo.Locked = locked
		}
		if noMove, ok := positionData["no_move"].(bool); ok {
			tempPosInfo.NoMove = noMove
		}
		if noResize, ok := positionData["no_resize"].(bool); ok {
			tempPosInfo.NoResize = noResize
		}
		if autoPosition, ok := positionData["auto_position"].(bool); ok {
			tempPosInfo.AutoPosition = autoPosition
		}

		// 转换为模型中的PositionInfo
		// posInfo := &models.PositionInfo{}

		// 查找lastUiPosition
		// if lastPos, ok := positionData["lastUiPosition"].(map[string]interface{}); ok {
		// 	posInfo.LastUiPosition = &models.LastUiPositionInfo{}
		// 	if left, ok := lastPos["left"].(float64); ok {
		// 		posInfo.LastUiPosition.Left = left
		// 	}
		// 	if top, ok := lastPos["top"].(float64); ok {
		// 		posInfo.LastUiPosition.Top = top
		// 	}
		// }

		// 查找是否已存在关联关系
		relation := &models.UIConfigGridRelation{
			UIConfigID: uiConfigID,
			GridInfoID: item.GridInfoID,
		}
		err = tx.QueryTable(new(models.UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).Filter("grid_info_id", item.GridInfoID).One(relation)
		if err == nil {
			// 关联关系已存在，更新位置和状态
			relation.Status = 1

			// 将临时结构体转换为JSON字符串
			tempPosInfoBytes, err := json.Marshal(tempPosInfo)
			if err != nil {
				tx.Rollback()
				logs.Error("[BatchUpdateGridItems] 序列化位置信息失败, GridInfoID: %d, 错误: %v", item.GridInfoID, err)
				return fmt.Errorf("序列化位置信息失败, GridInfoID: %d", item.GridInfoID)
			}
			relation.Position = string(tempPosInfoBytes)

			// 将步骤信息转换为JSON字符串
			stepArray := item.Step
			stepJSON, err := json.Marshal(stepArray)
			if err != nil {
				tx.Rollback()
				logs.Error("[BatchUpdateGridItems] 序列化步骤信息失败, GridInfoID: %d, 错误: %v", item.GridInfoID, err)
				return fmt.Errorf("序列化步骤信息失败, GridInfoID: %d", item.GridInfoID)
			}
			relation.Step = string(stepJSON)

			_, err = tx.Update(relation)
			if err != nil {
				tx.Rollback()
				logs.Error("[BatchUpdateGridItems] 更新关联关系失败, GridInfoID: %d, 错误: %v", item.GridInfoID, err)
				return fmt.Errorf("更新关联关系失败, GridInfoID: %d", item.GridInfoID)
			}
		} else {
			// 创建新的关联关系
			relation = &models.UIConfigGridRelation{
				UIConfigID: uiConfigID,
				GridInfoID: item.GridInfoID,
				Status:     1,
			}

			// 将临时结构体转换为JSON字符串
			tempPosInfoBytes, err := json.Marshal(tempPosInfo)
			if err != nil {
				tx.Rollback()
				logs.Error("[BatchUpdateGridItems] 序列化位置信息失败, GridInfoID: %d, 错误: %v", item.GridInfoID, err)
				return fmt.Errorf("序列化位置信息失败, GridInfoID: %d", item.GridInfoID)
			}
			relation.Position = string(tempPosInfoBytes)

			// 将步骤信息转换为JSON字符串
			stepArray := item.Step
			stepJSON, err := json.Marshal(stepArray)
			if err != nil {
				tx.Rollback()
				logs.Error("[BatchUpdateGridItems] 序列化步骤信息失败, GridInfoID: %d, 错误: %v", item.GridInfoID, err)
				return fmt.Errorf("序列化步骤信息失败, GridInfoID: %d", item.GridInfoID)
			}
			relation.Step = string(stepJSON)

			_, err = tx.Insert(relation)
			if err != nil {
				tx.Rollback()
				logs.Error("[BatchUpdateGridItems] 创建关联关系失败, GridInfoID: %d, 错误: %v", item.GridInfoID, err)
				return fmt.Errorf("创建关联关系失败, GridInfoID: %d", item.GridInfoID)
			}
		}
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		logs.Error("[BatchUpdateGridItems] 提交事务失败: %v", err)
		return fmt.Errorf("批量更新网格布局项目失败: %v", err)
	}

	return nil
}

// UpdateGridItemPosition 更新UI配置关联的网格布局项目位置
func (s *UIConfigServiceImpl) UpdateGridItemPosition(uiConfigID int, gridInfoID int, positionJSON string) error {
	o := orm.NewOrm()

	// 查找关联关系
	relation := &models.UIConfigGridRelation{}
	err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).Filter("grid_info_id", gridInfoID).One(relation)
	if err != nil {
		logs.Error("[UpdateGridItemPosition] 查询关联关系失败: %v", err)
		return fmt.Errorf("关联关系不存在: %v", err)
	}

	// 验证positionJSON是否为有效的JSON
	if positionJSON != "" {
		var jsonObj interface{}
		err = json.Unmarshal([]byte(positionJSON), &jsonObj)
		if err != nil {
			logs.Error("[UpdateGridItemPosition] 位置信息不是有效的JSON: %v", err)
			return fmt.Errorf("位置信息格式无效: %v", err)
		}
	}

	// 直接设置位置信息
	relation.Position = positionJSON

	// 保存更新
	_, err = o.Update(relation, "Position")
	if err != nil {
		logs.Error("[UpdateGridItemPosition] 更新关联关系失败: %v", err)
		return fmt.Errorf("更新位置信息失败: %v", err)
	}

	return nil
}

// GetGridItemRelation 获取UI配置与网格布局的关联关系
func (s *UIConfigServiceImpl) GetGridItemRelation(uiConfigID int, gridInfoID int) (*models.UIConfigGridRelation, error) {
	o := orm.NewOrm()

	// 查找关联关系
	relation := &models.UIConfigGridRelation{}
	err := o.QueryTable(new(models.UIConfigGridRelation)).Filter("ui_config_id", uiConfigID).Filter("grid_info_id", gridInfoID).One(relation)
	if err != nil {
		logs.Error("[GetGridItemRelation] 查询关联关系失败: %v", err)
		return nil, fmt.Errorf("关联关系不存在: %v", err)
	}

	return relation, nil
}

// parseStepField 负责将数据库中的 Step 字段（JSON字符串或数字字符串）反序列化为 int 数组
// 支持格式如 "[0,1,2]"、"0" 等，返回 []int
func parseStepField(stepStr string) []int {
	var stepArr []int
	if stepStr == "" {
		return stepArr
	}
	// 先尝试解析为数组
	if err := json.Unmarshal([]byte(stepStr), &stepArr); err == nil {
		return stepArr
	}
	// 如果解析失败，兼容老数据，尝试解析为单个数字
	var single int
	if err := json.Unmarshal([]byte(stepStr), &single); err == nil {
		return []int{single}
	}
	// 解析失败则返回空数组
	return []int{}
}
