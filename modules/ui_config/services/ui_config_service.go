/**
 * UI配置服务接口
 *
 * 该文件定义了UI配置相关的服务接口
 */

package services

import (
	"o_mall_backend/modules/ui_config/dto"
	"o_mall_backend/modules/ui_config/models"
)

// UIConfigService UI配置服务接口
type UIConfigService interface {
	// CreateUIConfig 创建UI配置
	CreateUIConfig(createDTO *dto.UIConfigCreateDTO) (int, error)

	// UpdateUIConfig 更新UI配置
	UpdateUIConfig(id int, updateDTO *dto.UIConfigUpdateDTO) error

	// DeleteUIConfig 删除UI配置
	DeleteUIConfig(id int) error

	// GetUIConfig 根据ID获取UI配置
	GetUIConfig(id int) (*dto.UIConfigDTO, error)

	// GetUIConfigByKey 根据配置Key获取UI配置
	GetUIConfigByKey(configKey string) (*dto.UIConfigResponseDTO, error)

	// ListUIConfigs 分页查询UI配置
	ListUIConfigs(queryDTO *dto.UIConfigQueryDTO) ([]*dto.UIConfigDTO, int64, error)

	// GetConfigByPath 根据前端路径获取所有相关配置
	GetConfigByPath(frontendPath string) ([]*dto.UIConfigResponseDTO, error)

	// UpdateVersion 更新配置版本号
	UpdateVersion(id int) (string, error)

	// ValidateConfig 验证配置内容是否符合JSON格式
	ValidateConfig(configType string, configContent string) error

	// GetUIConfigByModuleAndType 根据模块和配置类型获取UI配置
	GetUIConfigByModuleAndType(module string, configType string) (*dto.UIConfigResponseDTO, error)

	// GetUIConfigsByModule 获取指定模块的所有UI配置
	GetUIConfigsByModule(module string) ([]*dto.UIConfigResponseDTO, error)

	// GetUIConfigsByModuleAndGroup 获取指定模块和分组的UI配置
	GetUIConfigsByModuleAndGroup(module string, group string) ([]*dto.UIConfigResponseDTO, error)

	// GetUIConfigsByModuleAndType 获取指定模块和配置类型的所有UI配置
	GetUIConfigsByModuleAndType(module string, configType string) ([]*dto.UIConfigResponseDTO, error)

	// GetFrontendPaths 获取所有前端路径分组列表
	GetFrontendPaths() ([]*dto.FrontendPathGroupDTO, error)

	// AddGridItem 添加关联的网格布局项目（带位置信息）
	AddGridItem(uiConfigID int, gridInfoID int, positionJSON string) error

	// BatchUpdateGridItems 批量更新UI配置关联的网格布局项目
	BatchUpdateGridItems(uiConfigID int, items []dto.GridInfoWithPositionCreateDTO) error

	// UpdateGridItemPosition 更新UI配置关联的网格布局项目位置
	UpdateGridItemPosition(uiConfigID int, gridInfoID int, positionJSON string) error

	// GetGridItemRelation 获取UI配置与网格布局的关联关系
	GetGridItemRelation(uiConfigID int, gridInfoID int) (*models.UIConfigGridRelation, error)
}
