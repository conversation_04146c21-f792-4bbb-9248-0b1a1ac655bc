/**
 * 地址控制器
 *
 * 该文件实现了地址相关的API接口控制器，处理收货地址的添加、修改、删除等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/core/validation"

	"o_mall_backend/common/result"
	userDto "o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// AddressController 地址控制器
type AddressController struct {
	web.Controller
	addressService services.AddressService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *AddressController) Prepare() {
	c.addressService = services.NewAddressService()
}

// ParseRequest 通用请求参数解析方法
func (c *AddressController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// AddAddress 添加收货地址
// @Title 添加收货地址
// @Description 为当前登录用户添加一个新的收货地址
// @Param	body	body	userDto.AddAddressRequest	true	"地址信息"
// @Success 200 {object} dto.Response 成功返回地址ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [post]
func (c *AddressController) AddAddress() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体
	var req userDto.AddAddressRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层添加地址
	addressID, err := c.addressService.AddAddress(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, addressID)
}

// UpdateAddress 更新收货地址
// @Title 更新收货地址
// @Description 更新当前登录用户的收货地址信息
// @Param	id	path	int	true	"地址ID"
// @Param	body	body	userDto.UpdateAddressRequest	true	"地址信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [put]
func (c *AddressController) UpdateAddress() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 从URL路径获取地址ID
	idStr := c.Ctx.Input.Param(":id")
	addressID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的地址ID")
		return
	}

	// 解析请求体
	var req userDto.UpdateAddressRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 使用URL中的ID覆盖请求体中的ID
	req.ID = addressID

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层更新地址
	err = c.addressService.UpdateAddress(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// DeleteAddress 删除收货地址
// @Title 删除收货地址
// @Description 删除当前登录用户的收货地址
// @Param	id	path	int	true	"地址ID"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [delete]
func (c *AddressController) DeleteAddress() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取地址ID
	idStr := c.Ctx.Input.Param(":id")
	addressID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的地址ID")
		return
	}

	// 调用服务层删除地址
	err = c.addressService.DeleteAddress(c.Ctx.Request.Context(), userID, addressID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetAddress 获取收货地址详情
// @Title 获取收货地址详情
// @Description 获取当前登录用户的收货地址详情
// @Param	id	path	int	true	"地址ID"
// @Success 200 {object} dto.Response 成功返回地址信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id [get]
func (c *AddressController) GetAddress() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取地址ID
	idStr := c.Ctx.Input.Param(":id")
	addressID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的地址ID")
		return
	}

	// 调用服务层获取地址详情
	address, err := c.addressService.GetAddressByID(c.Ctx.Request.Context(), userID, addressID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, address)
}

// Options 处理OPTIONS预检请求
// @Summary 处理OPTIONS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Tags 地址管理
// @Accept json
// @Produce json
// @Success 200 {string} string "OK"
// @Router /api/v1/user/secured/addresses [options]
// @Router /api/v1/user/secured/addresses/{id} [options]
// @Router /api/v1/user/secured/addresses/{id}/default [options]
func (c *AddressController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}

// ListAddresses 获取收货地址列表
// @Title 获取收货地址列表
// @Description 获取当前登录用户的收货地址列表
// @Param	page	query	int	false	"页码"
// @Param	pageSize	query	int	false	"每页数量"
// @Success 200 {object} dto.Response 成功返回地址列表和总数
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router / [get]
func (c *AddressController) ListAddresses() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 获取分页参数
	pageStr := c.GetString("page", "1")
	pageSizeStr := c.GetString("pageSize", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	// 创建查询请求
	req := &userDto.AddressQueryRequest{
		Page:     page,
		PageSize: pageSize,
	}

	// 调用服务层获取地址列表
	addresses, total, err := c.addressService.ListAddresses(c.Ctx.Request.Context(), userID, req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应带分页
	result.OKWithPagination(c.Ctx, addresses, total, page, pageSize)
}

// SetDefaultAddress 设置默认收货地址
// @Title 设置默认收货地址
// @Description 设置当前登录用户的默认收货地址
// @Param       id   path   int  true  "地址ID"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /:id/default [put]
func (c *AddressController) SetDefaultAddress() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 从 URL 路径参数中获取地址 ID
	addressID, err := c.GetInt64(":id")
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的地址ID")
		return
	}

	// 构造请求对象
	req := userDto.SetDefaultAddressRequest{
		ID: addressID,
	}

	// 调用服务层设置默认地址
	err = c.addressService.SetDefaultAddress(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetDefaultAddress 获取默认收货地址
// @Title 获取默认收货地址
// @Description 获取当前登录用户的默认收货地址
// @Success 200 {object} dto.Response 成功返回默认地址信息
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /default [get]
func (c *AddressController) GetDefaultAddress() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 调用服务层获取默认地址
	address, err := c.addressService.GetDefaultAddress(c.Ctx.Request.Context(), userID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, address)
}
