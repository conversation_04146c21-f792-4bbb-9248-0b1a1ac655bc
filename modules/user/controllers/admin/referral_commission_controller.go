// Package admin 管理员相关控制器
package admin

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/services"
	"o_mall_backend/utils"
)

// ReferralCommissionController 分销佣金管理控制器
type ReferralCommissionController struct {
	web.Controller
	referralConfigService services.ReferralConfigService
	userReferralService   services.UserReferralService
}

// NewReferralCommissionController 创建分销佣金控制器实例
// 移除单例模式，确保每次正确注入依赖
func NewReferralCommissionController(
	referralConfigService services.ReferralConfigService,
	userReferralService services.UserReferralService,
) *ReferralCommissionController {
	// 直接创建新实例，确保依赖正确
	logs.Info("创建分销佣金控制器，referralConfigService是否为nil: %v", referralConfigService == nil)
	
	return &ReferralCommissionController{
		referralConfigService: referralConfigService,
		userReferralService:   userReferralService,
	}
}

// GetCommission 获取佣金比例配置
// @Title 获取佣金比例配置
// @Description 获取系统设置的分销佣金比例配置
// @Success 200 {object} dto.CommissionRateConfig "佣金比例配置"
// @Failure 500 {object} utils.AppError "服务器内部错误"
// @router /commission [get]
func (c *ReferralCommissionController) GetCommission() {
	ctx := context.Background()
	
	logs.Info("========= 进入 GetCommission 接口 ==========")
	
	// 如果服务实例为nil，返回错误
	if c.referralConfigService == nil {
		logs.Error("referralConfigService 服务实例为 nil")
		result.HandleError(c.Ctx, result.ErrInternalError, "服务未初始化")
		return
	}

	logs.Info("服务实例检查成功，开始获取佣金比例配置")
	
	// 使用defer+recover捕获可能的panic
	defer func() {
		if r := recover(); r != nil {
			logs.Error("GetCommission 发生panic: %v", r)
			result.HandleError(c.Ctx, result.ErrInternalError, fmt.Sprintf("服务内部错误: %v", r))
		}
	}()

	// 获取佣金比例配置
	commissionRates, err := c.referralConfigService.GetCommissionRates(ctx)
	if err != nil {
		logs.Error("GetCommission 出错: %v", err)
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	logs.Info("获取佣金比例配置成功: %+v", commissionRates)
	
	// 返回结果
	c.Data["json"] = commissionRates
	c.ServeJSON()
}

// UpdateCommission 更新佣金比例配置
// @Title 更新佣金比例配置
// @Description 更新系统设置的分销佣金比例配置
// @Param body body dto.CommissionRateConfig true "佣金比例配置"
// @Success 200 {object} utils.GeneralResponse "操作成功"
// @Failure 400 {object} utils.AppError "参数错误"
// @Failure 500 {object} utils.AppError "服务器内部错误"
// @router /commission [put]
func (c *ReferralCommissionController) UpdateCommission() {
	ctx := context.Background()
	
	// 如果服务实例为nil，返回错误
	if c.referralConfigService == nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "服务未初始化")
		return
	}

	// 解析请求参数
	var req dto.CommissionRateConfig
	if err := c.ParseForm(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "参数解析失败: "+err.Error())
		return
	}

	// 更新佣金比例配置
	if err := c.referralConfigService.UpdateCommissionRates(ctx, &req); err != nil {
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	// 返回成功响应
	data := map[string]interface{}{
		"message": "佣金比例配置更新成功",
	}
	result.OK(c.Ctx, data)
	c.ServeJSON()
}

// GetCommissionSummary 获取佣金汇总信息
// @Title 获取佣金汇总信息
// @Description 获取系统所有用户的佣金汇总数据
// @Success 200 {object} dto.CommissionSummaryResponse "佣金汇总信息"
// @Failure 500 {object} utils.AppError "服务器内部错误"
// @router /commission-summary [get]
func (c *ReferralCommissionController) GetCommissionSummary() {
	ctx := context.Background()
	
	logs.Info("========= 进入 GetCommissionSummary 接口 ==========")
	
	// 如果服务实例为nil，返回错误
	if c.userReferralService == nil {
		logs.Error("userReferralService 服务实例为 nil")
		result.HandleError(c.Ctx, result.ErrInternalError, "服务未初始化")
		return
	}

	logs.Info("服务实例检查成功，开始获取佣金汇总信息")
	
	// 使用defer+recover捕获可能的panic
	defer func() {
		if r := recover(); r != nil {
			logs.Error("GetCommissionSummary 发生panic: %v", r)
			result.HandleError(c.Ctx, result.ErrInternalError, fmt.Sprintf("服务内部错误: %v", r))
		}
	}()

	// 获取佣金汇总信息
	summary, err := c.userReferralService.GetCommissionSummary(ctx)
	if err != nil {
		logs.Error("GetCommissionSummary 出错: %v", err)
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	logs.Info("获取佣金汇总信息成功: %+v", summary)
	
	// 返回结果
	c.Data["json"] = summary
	c.ServeJSON()
}

// GetCommissionStatistics 获取佣金统计信息
// @Title 获取佣金统计信息
// @Description 获取佣金统计数据，包括按时间、级别等维度的统计
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Success 200 {object} dto.CommissionStatisticsResponse "佣金统计信息"
// @Failure 400 {object} utils.AppError "参数错误"
// @Failure 500 {object} utils.AppError "服务器内部错误"
// @router /commission-statistics [get]
func (c *ReferralCommissionController) GetCommissionStatistics() {
	ctx := context.Background()
	
	// 如果服务实例为nil，返回错误
	if c.userReferralService == nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "服务未初始化")
		return
	}

	// 解析查询参数
	startDateStr := c.GetString("start_date")
	endDateStr := c.GetString("end_date")
	
	var startDate, endDate time.Time
	var err error
	
	// 解析开始日期
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			result.HandleError(c.Ctx, result.ErrInvalidParams, "开始日期格式错误")
			return
		}
	} else {
		// 默认为当前日期的30天前
		startDate = time.Now().AddDate(0, 0, -30)
	}
	
	// 解析结束日期
	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			result.HandleError(c.Ctx, result.ErrInvalidParams, "结束日期格式错误")
			return
		}
	} else {
		// 默认为当前日期
		endDate = time.Now()
	}

	// 创建查询请求
	req := &dto.CommissionStatisticsRequest{
		StartDate: startDate,
		EndDate:   endDate,
	}

	// 获取佣金统计信息
	statistics, err := c.userReferralService.GetCommissionStatistics(ctx, req)
	if err != nil {
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回结果
	c.Data["json"] = statistics
	c.ServeJSON()
}
