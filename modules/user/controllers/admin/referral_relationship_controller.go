/**
 * 管理员分销关系控制器
 *
 * 该文件实现了管理员管理分销关系的HTTP接口，包括查看、创建和删除分销关系等功能。
 * 提供管理员管理整个分销体系的API接口。
 */

package admin

import (
	"strconv"
	"sync"
	"time"

	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// 全局服务实例
var (
	userReferralServiceInstance services.UserReferralService
	serviceInitOnce            sync.Once
	serviceMutex               sync.Mutex
)

// SetUserReferralService 设置全局分销服务实例，确保线程安全的初始化
func SetUserReferralService(service services.UserReferralService) {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()
	userReferralServiceInstance = service
}

// GetUserReferralService 获取全局分销服务实例
func GetUserReferralService() services.UserReferralService {
	return userReferralServiceInstance
}

// ReferralRelationshipController 管理员分销关系控制器
type ReferralRelationshipController struct {
	web.Controller
	userReferralService services.UserReferralService
}

// NewReferralRelationshipController 创建管理员分销关系控制器实例
func NewReferralRelationshipController(userReferralService services.UserReferralService) *ReferralRelationshipController {
	// 设置全局服务实例，以便后续路由注册创建的控制器实例可以使用
	SetUserReferralService(userReferralService)
	return &ReferralRelationshipController{
		userReferralService: userReferralService,
	}
}

// Prepare 在每个请求处理前执行，确保服务实例正确初始化
func (c *ReferralRelationshipController) Prepare() {
	// 如果当前控制器实例的服务为nil，尝试从全局变量获取
	if c.userReferralService == nil {
		c.userReferralService = GetUserReferralService()
	}
}

// ParseRequest 通用请求参数解析方法
func (c *ReferralRelationshipController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// GetRelationships 获取分销关系列表
// @Title 获取分销关系列表
// @Description 管理员获取所有用户的分销关系列表，支持分页和筛选
// @Param   Authorization  header  string  true   "管理员令牌"
// @Param   referrer_id    query   int64   false  "推荐人ID筛选"
// @Param   referee_id     query   int64   false  "被推荐人ID筛选"
// @Param   status         query   int     false  "状态筛选"
// @Param   level          query   int     false  "分销等级筛选"
// @Param   page           query   int     false  "页码，默认1"
// @Param   page_size      query   int     false  "每页记录数，默认20"
// @Success 200  {object}  result.PaginationResponse{list=[]dto.ReferralResponse}
// @Failure 400  {object}  result.Response
// @Failure 401  {object}  result.Response
// @Failure 500  {object}  result.Response
// @router /relationships [get]
func (c *ReferralRelationshipController) GetRelationships() {
	// 检查服务是否初始化
	if c.userReferralService == nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "用户分销服务未初始化")
		return
	}
	
	// 解析查询参数
	referrerID, _ := c.GetInt64("referrer_id", 0)
	refereeID, _ := c.GetInt64("referee_id", 0)
	status, _ := c.GetInt("status", -1)
	level, _ := c.GetInt("level", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)

	// 参数验证
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询请求 - 这里需要扩展原有的查询请求结构
	req := &dto.AdminReferralQueryRequest{
		ReferrerID: referrerID,
		RefereeID:  refereeID,
		Status:     status,
		Level:      level,
		Page:       page,
		PageSize:   pageSize,
	}

	// 调用服务层获取分销关系列表
	referrals, total, err := c.userReferralService.GetAllReferrals(c.Ctx.Request.Context(), req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回分页数据
	result.OKWithPagination(c.Ctx, referrals, total, page, pageSize)
}

// CreateRelationship 创建分销关系
// @Title 创建分销关系
// @Description 管理员创建用户分销关系
// @Param   Authorization  header  string                     true  "管理员令牌"
// @Param   body          body    dto.ReferralCreateRequest  true  "分销关系创建参数"
// @Success 200  {object}  result.Response{data=dto.ReferralResponse}
// @Failure 400  {object}  result.Response
// @Failure 401  {object}  result.Response
// @Failure 500  {object}  result.Response
// @router /relationships [post]
func (c *ReferralRelationshipController) CreateRelationship() {
	// 检查服务是否初始化
	if c.userReferralService == nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "用户分销服务未初始化")
		return
	}
	
	// 解析请求体
	var req dto.ReferralCreateRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, err.Error())
		return
	}

	// 验证请求参数
	valid := validation.Validation{}
	if b, err := valid.Valid(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, err.Error())
		return
	} else if !b {
		errMsg := valid.Errors[0].Key + " " + valid.Errors[0].Message
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层创建分销关系
	// 直接使用请求中的参数创建分销关系
	referralID, err := c.userReferralService.CreateReferral(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}
	
	// 获取分销关系对象
	// 由于接口没有提供GetReferralByID方法，我们只返回ID和基本信息
	now := time.Now()
	referral := &dto.ReferralResponse{
		ID:           referralID,
		UserID:       req.UserID,
		ReferrerID:   req.ReferrerID,
		Level:        req.Level,
		Status:       1, // 默认有效状态
		ReferralTime: now,
		CreatedAt:    now,
	}

	// 返回成功响应
	result.OK(c.Ctx, referral)
}

// DeleteRelationship 删除分销关系
// @Title 删除分销关系
// @Description 管理员删除指定的分销关系
// @Param   Authorization  header  string  true  "管理员令牌"
// @Param   id             path    int64   true  "分销关系ID"
// @Success 200  {object}  result.Response
// @Failure 400  {object}  result.Response
// @Failure 401  {object}  result.Response
// @Failure 404  {object}  result.Response
// @Failure 500  {object}  result.Response
// @router /relationships/:id [delete]
func (c *ReferralRelationshipController) DeleteRelationship() {
	// 检查服务是否初始化
	if c.userReferralService == nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "用户分销服务未初始化")
		return
	}
	
	// 获取分销关系ID
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的分销关系ID")
		return
	}

	// 调用服务层删除分销关系
	err = c.userReferralService.DeleteReferral(c.Ctx.Request.Context(), id)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetStatistics 获取分销统计信息
// @Title 获取分销统计信息
// @Description 管理员获取整个系统的分销统计信息
// @Param   Authorization  header  string  true  "管理员令牌"
// @Success 200  {object}  result.Response{data=dto.AdminReferralStatisticsResponse}
// @Failure 401  {object}  result.Response
// @Failure 500  {object}  result.Response
// @router /statistics [get]
func (c *ReferralRelationshipController) GetStatistics() {
	// 检查服务是否初始化
	if c.userReferralService == nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "用户分销服务未初始化")
		return
	}
	
	// 获取系统分销统计信息
	stats, err := c.userReferralService.GetAdminReferralStatistics(c.Ctx.Request.Context())
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	result.OK(c.Ctx, stats)
}

// GetTopReferrers 获取TOP推荐人数据
// @Title 获取TOP推荐人数据
// @Description 管理员获取推荐人排行榜数据，支持时间范围和数量限制
// @Param   Authorization  header  string  true   "管理员令牌"
// @Param   start_date     query   string  false  "开始日期，格式：2006-01-02"
// @Param   end_date       query   string  false  "结束日期，格式：2006-01-02"
// @Param   limit          query   int     false  "返回数量限制，默认10"
// @Success 200  {object}  result.Response{data=[]dto.TopReferrerResponse}
// @Failure 400  {object}  result.Response
// @Failure 401  {object}  result.Response
// @Failure 500  {object}  result.Response
// @router /top-referrers [get]
func (c *ReferralRelationshipController) GetTopReferrers() {
	// 检查服务是否初始化
	if c.userReferralService == nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "用户分销服务未初始化")
		return
	}

	// 手动解析GET请求的查询参数
	var req dto.TopReferrerRequest
	req.StartDate = c.GetString("start_date")
	req.EndDate = c.GetString("end_date")

	// 解析limit参数，默认为10
	limitStr := c.GetString("limit")
	if limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			req.Limit = limit
		} else {
			result.HandleError(c.Ctx, result.ErrInvalidParam, "limit参数格式错误")
			return
		}
	} else {
		req.Limit = 10 // 默认值
	}

	// 参数验证
	valid := validation.Validation{}
	if b, err := valid.Valid(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数验证失败: "+err.Error())
		return
	} else if !b {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "参数验证失败: "+valid.Errors[0].Message)
		return
	}

	// 调用服务层获取TOP推荐人数据
	topReferrers, err := c.userReferralService.GetTopReferrers(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	result.OK(c.Ctx, topReferrers)
}
