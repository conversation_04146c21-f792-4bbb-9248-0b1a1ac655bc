/**
 * 控制器基类
 *
 * 该文件实现了控制器基类，为其他控制器提供通用功能支持。
 * 包括请求解析、错误处理等基础方法。
 */

package controllers

import (
	"github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"

	"o_mall_backend/utils/common"
)

// BaseController 控制器基类
// 继承自Beego的Controller，为子类控制器提供通用功能
type BaseController struct {
	web.Controller
}

// ParseRequest 通用请求参数解析方法
// 参数:
//   - req: 请求参数结构体指针
//
// 返回:
//   - error: 解析错误
func (c *BaseController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// Init 初始化控制器
// 实现web.ControllerInterface接口的方法
// 参数:
//   - ctx: 上下文对象
//   - controllerName: 控制器名称
//   - actionName: 操作名称
//   - app: 应用实例
func (c *BaseController) Init(ctx *context.Context, controllerName, actionName string, app interface{}) {
	c.Controller.Init(ctx, controllerName, actionName, app)
}
