/**
 * 手机号绑定控制器
 *
 * 该文件实现了手机号绑定和修改相关的API接口控制器。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	userDto "o_mall_backend/modules/user/dto"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// MobileController 手机号控制器
type MobileController struct {
	web.Controller
	userController *UserController
}

// NewMobileController 创建手机号控制器实例
func NewMobileController() *MobileController {
	logs.Info("[MobileController] 创建控制器实例")
	
	controller := &MobileController{
		userController: UserControllerInstance,
	}
	
	return controller
}

// ParseRequest 通用请求参数解析方法
func (c *MobileController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// BindMobile 绑定手机号
// @Title 绑定手机号
// @Description 为当前用户绑定手机号
// @Param	body	body	userDto.BindMobileRequest	true	"绑定手机号信息"
// @Success 200 {object} dto.Response{data=userDto.BindMobileResponse} 成功返回绑定结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /bind [post]
func (c *MobileController) BindMobile() {
	// 确保用户控制器实例可用
	if c.userController == nil || c.userController.userService == nil {
		logs.Error("[MobileController] 严重错误：userController或userService为nil")
		result.HandleError(c.Ctx, result.ErrInternalError, "服务初始化错误")
		return
	}

	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体
	var req userDto.BindMobileRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层处理绑定手机号业务
	resp, err := c.userController.userService.BindMobile(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// ChangeMobile 修改绑定手机号
// @Title 修改绑定手机号
// @Description 修改当前用户绑定的手机号
// @Param	body	body	userDto.ChangeMobileRequest	true	"修改手机号信息"
// @Success 200 {object} dto.Response{data=userDto.ChangeMobileResponse} 成功返回修改结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /change [post]
func (c *MobileController) ChangeMobile() {
	// 确保用户控制器实例可用
	if c.userController == nil || c.userController.userService == nil {
		logs.Error("[MobileController] 严重错误：userController或userService为nil")
		result.HandleError(c.Ctx, result.ErrInternalError, "服务初始化错误")
		return
	}

	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体
	var req userDto.ChangeMobileRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层处理修改手机号业务
	resp, err := c.userController.userService.ChangeMobile(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// SendMobileBindCode 发送手机号绑定验证码
// @Title 发送手机号绑定验证码
// @Description 为绑定新手机号发送验证码
// @Param	body	body	userDto.SendVerificationCodeRequest	true	"手机号信息"
// @Success 200 {object} dto.Response{data=userDto.SendVerificationCodeResponse} 成功返回验证码发送状态
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /send-bind-code [post]
func (c *MobileController) SendMobileBindCode() {
	// 确保用户控制器实例可用
	if c.userController == nil || c.userController.userService == nil {
		logs.Error("[MobileController] 严重错误：userController或userService为nil")
		result.HandleError(c.Ctx, result.ErrInternalError, "服务初始化错误")
		return
	}

	// 解析请求体
	var req userDto.SendVerificationCodeRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层发送验证码
	resp, err := c.userController.userService.SendVerificationCode(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// SendMobileChangeCode 发送手机号变更验证码
// @Title 发送手机号变更验证码
// @Description 为修改手机号发送验证码，验证原手机号
// @Param	body	body	userDto.SendVerificationCodeRequest	true	"手机号信息"
// @Success 200 {object} dto.Response{data=userDto.SendVerificationCodeResponse} 成功返回验证码发送状态
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /send-change-code [post]
func (c *MobileController) SendMobileChangeCode() {
	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}
	
	// 确保用户控制器实例可用
	if c.userController == nil || c.userController.userService == nil {
		logs.Error("[MobileController] 严重错误：userController或userService为nil")
		result.HandleError(c.Ctx, result.ErrInternalError, "服务初始化错误")
		return
	}

	// 解析请求体
	var req userDto.SendVerificationCodeRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 获取用户信息
	userInfo, err := c.userController.userService.GetUserByID(c.Ctx.Request.Context(), userID)
	if err != nil {
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}
	
	// 校验手机号是否与请求相符
	if userInfo.Mobile != req.Mobile {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "请求的手机号与当前用户绑定的手机号不一致")
		return
	}

	// 调用服务层发送验证码
	resp, err := c.userController.userService.SendVerificationCode(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}
