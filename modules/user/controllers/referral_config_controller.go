/**
 * 分销配置控制器
 *
 * 该文件实现了分销配置相关的HTTP接口，包括分销级别配置、佣金比例配置等功能。
 * 提供管理员管理分销规则的API接口。
 */

package controllers

import (
	"strconv"

	"github.com/beego/beego/v2/server/web"
	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/repositories"
	"o_mall_backend/modules/user/services"
)

// ReferralConfigController 分销配置控制器
type ReferralConfigController struct {
	web.Controller
	referralConfigService services.ReferralConfigService
}

// NewReferralConfigController 创建分销配置控制器实例
func NewReferralConfigController(referralConfigService services.ReferralConfigService) *ReferralConfigController {
	return &ReferralConfigController{
		referralConfigService: referralConfigService,
	}
}

// GetLevelConfig 获取分销级别配置
// @Title 获取分销级别配置
// @Description 获取当前的分销级别配置信息，包括最大级别、启用级别和佣金比例
// @Success 200 {object} dto.ReferralLevelConfigResponse
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router /level-config [get]
func (c *ReferralConfigController) GetLevelConfig() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	config, err := c.referralConfigService.GetLevelConfig(ctx)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "获取分销级别配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"message": "获取成功",
		"data": config,
	}
	c.ServeJSON()
}

// InitializeConfigs 初始化分销配置
// @Title 初始化分销配置
// @Description 初始化系统默认的分销配置数据，包括分销等级、佣金比例等基础配置
// @Success 200 {object} map[string]interface{}
// @Failure 500 服务器内部错误
// @router /initialize [post]
func (c *ReferralConfigController) InitializeConfigs() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	err := c.referralConfigService.InitializeConfigs(ctx)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "初始化分销配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code":    200,
		"message": "分销配置初始化成功",
		"data":    nil,
	}
	c.ServeJSON()
}

// UpdateLevelConfig 更新分销级别配置
// @Title 更新分销级别配置
// @Description 更新分销级别配置，包括最大级别、启用级别和佣金比例
// @Param body body dto.ReferralLevelConfigRequest true "分销级别配置请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router /level-config [put]
func (c *ReferralConfigController) UpdateLevelConfig() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	var req dto.ReferralLevelConfigRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "参数解析失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 参数验证
	if req.MaxLevels < 1 || req.MaxLevels > 3 {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "最大分销级别必须在1-3之间",
		}
		c.ServeJSON()
		return
	}

	if req.EnabledLevels < 1 || req.EnabledLevels > req.MaxLevels {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "启用分销级别必须在1到最大级别之间",
		}
		c.ServeJSON()
		return
	}

	if req.Level1Rate < 0 || req.Level1Rate > 1 ||
		req.Level2Rate < 0 || req.Level2Rate > 1 ||
		req.Level3Rate < 0 || req.Level3Rate > 1 {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "佣金比例必须在0-1之间",
		}
		c.ServeJSON()
		return
	}

	err := c.referralConfigService.UpdateLevelConfig(ctx, &req)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "更新分销级别配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code":    200,
		"message": "更新成功",
	}
	c.ServeJSON()
}

// GetCommissionRates 获取佣金比例配置
// @Title 获取佣金比例配置
// @Description 获取各级分销的佣金比例配置
// @Success 200 {object} dto.CommissionRateConfig
// @Failure 500 服务器内部错误
// @router /commission-rates [get]
func (c *ReferralConfigController) GetCommissionRates() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	rates, err := c.referralConfigService.GetCommissionRates(ctx)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "获取佣金比例配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"message": "获取成功",
		"data": rates,
	}
	c.ServeJSON()
}

// UpdateCommissionRates 更新佣金比例配置
// @Title 更新佣金比例配置
// @Description 更新各级分销的佣金比例配置
// @Param body body dto.CommissionRateConfig true "佣金比例配置请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router /commission-rates [put]
func (c *ReferralConfigController) UpdateCommissionRates() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	var req dto.CommissionRateConfig
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "参数解析失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	// 参数验证
	if req.Level1 < 0 || req.Level1 > 1 ||
		req.Level2 < 0 || req.Level2 > 1 ||
		req.Level3 < 0 || req.Level3 > 1 {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "佣金比例必须在0-1之间",
		}
		c.ServeJSON()
		return
	}

	err := c.referralConfigService.UpdateCommissionRates(ctx, &req)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "更新佣金比例配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code":    200,
		"message": "更新成功",
	}
	c.ServeJSON()
}

// GetConfigs 获取配置列表
// @Title 获取配置列表
// @Description 分页获取分销配置列表
// @Param config_type query string false "配置类型"
// @Param status query int false "配置状态"
// @Param page query int false "页码"
// @Param page_size query int false "每页记录数"
// @Success 200 {object} map[string]interface{}
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router / [get]
func (c *ReferralConfigController) GetConfigs() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	var req dto.ReferralConfigQueryRequest
	req.ConfigType = c.GetString("config_type")

	if statusStr := c.GetString("status"); statusStr != "" {
		if status, err := strconv.Atoi(statusStr); err == nil {
			req.Status = &status
		}
	}

	if pageStr := c.GetString("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}
	if req.Page <= 0 {
		req.Page = 1
	}

	if pageSizeStr := c.GetString("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			req.PageSize = pageSize
		}
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	configs, total, err := c.referralConfigService.GetConfigs(ctx, &req)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "获取配置列表失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"message": "获取成功",
		"data": map[string]interface{}{
			"list":      configs,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	}
	c.ServeJSON()
}

// CreateConfig 创建配置
// @Title 创建配置
// @Description 创建新的分销配置
// @Param body body dto.ReferralConfigCreateRequest true "创建配置请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router / [post]
func (c *ReferralConfigController) CreateConfig() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	var req dto.ReferralConfigCreateRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "参数解析失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	id, err := c.referralConfigService.CreateConfig(ctx, &req)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "创建配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"message": "创建成功",
		"data": map[string]interface{}{
			"id": id,
		},
	}
	c.ServeJSON()
}

// UpdateConfig 更新配置
// @Title 更新配置
// @Description 更新指定的分销配置
// @Param id path int true "配置ID"
// @Param body body dto.ReferralConfigUpdateRequest true "更新配置请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router /:id [put]
func (c *ReferralConfigController) UpdateConfig() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "无效的配置ID",
		}
		c.ServeJSON()
		return
	}

	var req dto.ReferralConfigUpdateRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "参数解析失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	err = c.referralConfigService.UpdateConfig(ctx, id, &req)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "更新配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code":    200,
		"message": "更新成功",
	}
	c.ServeJSON()
}

// DeleteConfig 删除配置
// @Title 删除配置
// @Description 删除指定的分销配置
// @Param id path int true "配置ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router /:id [delete]
func (c *ReferralConfigController) DeleteConfig() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "无效的配置ID",
		}
		c.ServeJSON()
		return
	}

	err = c.referralConfigService.DeleteConfig(ctx, id)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "删除配置失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code":    200,
		"message": "删除成功",
	}
	c.ServeJSON()
}

// GetConfigByID 获取配置详情
// @Title 获取配置详情
// @Description 根据ID获取分销配置详情
// @Param id path int true "配置ID"
// @Success 200 {object} dto.ReferralConfigResponse
// @Failure 400 参数错误
// @Failure 500 服务器内部错误
// @router /:id [get]
func (c *ReferralConfigController) GetConfigByID() {
	ctx := c.Ctx.Request.Context()

	// 检查服务是否已初始化，如果没有则手动初始化
	if c.referralConfigService == nil {
		referralConfigRepo := repositories.NewReferralConfigRepository()
		c.referralConfigService = services.NewReferralConfigService(referralConfigRepo)
	}

	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    400,
			"message": "无效的配置ID",
		}
		c.ServeJSON()
		return
	}

	config, err := c.referralConfigService.GetConfigByID(ctx, id)
	if err != nil {
		c.Data["json"] = map[string]interface{}{
			"code":    500,
			"message": "获取配置详情失败: " + err.Error(),
		}
		c.ServeJSON()
		return
	}

	c.Data["json"] = map[string]interface{}{
		"code": 200,
		"message": "获取成功",
		"data": config,
	}
	c.ServeJSON()
}