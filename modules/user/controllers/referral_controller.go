/**
 * 用户分销控制器
 *
 * 该文件实现了用户分销功能的控制器，处理分销相关的HTTP请求。
 * 包括创建分销关系、查询分销关系、获取分销统计数据等功能。
 * 
 * 注意：beego框架会为每个请求创建新的控制器实例，因此使用全局服务实例+Prepare方法来实现依赖注入。
 */

package controllers

import (
	"fmt"
	"sync"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/services"
	"o_mall_backend/utils"
)

// 全局服务实例，用于控制器依赖注入
var (
	globalUserReferralService services.UserReferralService
	serviceInitOnce          sync.Once
)

// InitGlobalUserReferralService 初始化全局用户分销服务实例
func InitGlobalUserReferralService(service services.UserReferralService) {
	serviceInitOnce.Do(func() {
		globalUserReferralService = service
		serviceAddress := fmt.Sprintf("%p", globalUserReferralService)
		logs.Info("[用户分销] 全局UserReferralService初始化完成，实例地址: %s", serviceAddress)
	})
}

// ReferralController 用户分销控制器
type ReferralController struct {
	web.Controller
	userReferralService services.UserReferralService
}

// NewReferralController 创建用户分销控制器实例
// 注意：现在这个方法主要用于初始化全局服务，而不是为每个请求创建控制器实例
func NewReferralController(userReferralService services.UserReferralService) *ReferralController {
	// 初始化全局服务实例，仅执行一次
	InitGlobalUserReferralService(userReferralService)
	
	// 返回一个空实例，真正的初始化将在Prepare中完成
	controller := &ReferralController{}
	address := fmt.Sprintf("%p", controller)
	logs.Info("[用户分销] 创建新的ReferralController实例: %s", address)
	return controller
}

// Prepare 请求预处理方法，在每个请求处理前执行
// 用于从全局服务实例中获取依赖注入到控制器实例中
func (c *ReferralController) Prepare() {
	ctrlAddress := fmt.Sprintf("%p", c)
	
	// 注入全局服务实例
	c.userReferralService = globalUserReferralService
	
	// 记录依赖注入日志
	serviceAddress := "nil"
	if c.userReferralService != nil {
		serviceAddress = fmt.Sprintf("%p", c.userReferralService)
		logs.Info("[用户分销] 控制器Prepare: 实例%s成功注入service: %s", ctrlAddress, serviceAddress)
	} else {
		logs.Error("[用户分销] 控制器Prepare: 实例%s依赖注入失败，globalUserReferralService为nil", ctrlAddress)
	}
}

// ParseRequest 通用请求参数解析方法
func (c *ReferralController) ParseRequest(req interface{}) error {
	return c.Ctx.BindJSON(req)
}

// CreateReferral 创建分销关系
// @Title 创建分销关系
// @Description 创建用户分销关系
// @Param   Authorization  header  string                  true  "用户令牌"
// @Param   body           body    dto.ReferralCreateRequest  true  "分销关系参数"
// @Success 200  {object}  dto.Response{data=int64}
// @Failure 400  {object}  dto.Response
// @Failure 401  {object}  dto.Response
// @Failure 500  {object}  dto.Response
// @router /create [post]
func (c *ReferralController) CreateReferral() {
	// 解析请求体
	var req dto.ReferralCreateRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 获取当前登录用户ID
	// 在这里不需要获取用户ID，因为请求参数中已包含用户ID和推荐人ID
	
	// 创建分销关系
	id, err := c.userReferralService.CreateReferral(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}
	
	// 返回成功响应
	result.OK(c.Ctx, id)
}

// GetReferrer 获取用户的推荐人
// @Title 获取推荐人
// @Description 获取用户的推荐人信息
// @Param   Authorization  header  string  true  "用户令牌"
// @Success 200  {object}  dto.Response{data=dto.ReferralResponse}
// @Failure 401  {object}  dto.Response
// @Failure 500  {object}  dto.Response
// @router /referrer [get]
func (c *ReferralController) GetReferrer() {
	// 获取当前登录用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}
	
	// 获取用户的推荐人
	referrer, err := c.userReferralService.GetReferrer(c.Ctx.Request.Context(), userID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}
	
	result.OK(c.Ctx, referrer)
}

// GetReferrals 获取推荐的用户列表
// @Title 获取推荐用户
// @Description 获取用户推荐的用户列表
// @Param   Authorization  header  string  true   "用户令牌"
// @Param   level          query   int     false  "分销等级"
// @Param   page           query   int     false  "页码"
// @Param   page_size      query   int     false  "每页记录数"
// @Param   only_direct    query   bool    false  "是否只查询直接推荐的用户"
// @Success 200  {object}  dto.Response{data=result.PaginationResponse{list=[]dto.ReferralResponse}}
// @Failure 400  {object}  dto.Response
// @Failure 401  {object}  dto.Response
// @Failure 500  {object}  dto.Response
// @router /referrals [get]
func (c *ReferralController) GetReferrals() {
	// 添加调试日志，检查控制器实例和service是否正确注入
	ctrlAddress := fmt.Sprintf("%p", c)
	serviceAddress := "nil"
	if c.userReferralService != nil {
		serviceAddress = fmt.Sprintf("%p", c.userReferralService)
	} else {
		logs.Error("[用户分销] GetReferrals方法中userReferralService为nil，控制器实例: %s", ctrlAddress)
	}
	logs.Info("[用户分销] GetReferrals方法被调用，控制器实例: %s，service实例: %s", ctrlAddress, serviceAddress)

	// 获取当前登录用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}
	
	// 解析查询参数
	level, _ := c.GetInt("level", 0)
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 10)
	onlyDirect, _ := c.GetBool("only_direct", false)
	
	// 构建查询请求
	req := &dto.ReferralQueryRequest{
		UserID:     userID,
		Level:      level,
		Page:       page,
		PageSize:   pageSize,
		OnlyDirect: onlyDirect,
	}
	
	// 查询推荐的用户列表
	referrals, total, err := c.userReferralService.GetReferrals(c.Ctx.Request.Context(), req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}
	
	// 返回分页数据
	result.OKWithPagination(c.Ctx, referrals, total, page, pageSize)
}

// GetReferralStatistics 获取分销统计信息
// @Title 获取分销统计
// @Description 获取用户分销统计信息
// @Param   Authorization  header  string  true  "用户令牌"
// @Success 200  {object}  dto.Response{data=dto.ReferralStatisticsResponse}
// @Failure 401  {object}  dto.Response
// @Failure 500  {object}  dto.Response
// @router /statistics [get]
func (c *ReferralController) GetReferralStatistics() {
	// 获取当前登录用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}
	
	// 获取分销统计信息
	stats, err := c.userReferralService.GetReferralStatistics(c.Ctx.Request.Context(), userID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}
	
	result.OK(c.Ctx, stats)
}

// GetReferralInfo 获取用户邀请信息
// @Title 获取邀请信息
// @Description 获取用户邀请码和邀请链接
// @Param   Authorization  header  string  true  "用户令牌"
// @Success 200  {object}  dto.Response{data=dto.ReferralInfoResponse}
// @Failure 401  {object}  dto.Response
// @Failure 500  {object}  dto.Response
// @router /info [get]
func (c *ReferralController) GetReferralInfo() {
	// 添加调试日志
	ctrlAddress := fmt.Sprintf("%p", c)
	serviceAddress := "nil"
	if c.userReferralService != nil {
		serviceAddress = fmt.Sprintf("%p", c.userReferralService)
	} else {
		logs.Error("[用户分销] GetReferralInfo方法中userReferralService为nil，控制器实例: %s", ctrlAddress)
	}
	logs.Info("[用户分销] GetReferralInfo方法被调用，控制器实例: %s，service实例: %s", ctrlAddress, serviceAddress)

	// 获取当前登录用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}
	
	// 从UserService获取用户信息
	// 获取用户服务实例
	userService := services.GetUserService()
	if userService == nil {
		result.HandleError(c.Ctx, result.ErrInternal, "用户服务初始化失败")
		return
	}
	
	// 获取用户信息
	user, err := userService.GetUserByID(c.Ctx.Request.Context(), userID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, "获取用户信息失败")
		return
	}
	
	if user == nil {
		result.HandleError(c.Ctx, result.ErrNotFound, "用户不存在")
		return
	}
	
	// 构建邀请链接
	baseURL := web.AppConfig.DefaultString("app_invite_url", "https://o-mall.example.com/register?referrer=")
	inviteLink := baseURL + user.ReferralCode
	
	// 构建二维码URL（可选，如果有二维码服务）
	qrcodeURL := ""
	
	// 构建响应
	response := &dto.ReferralInfoResponse{
		ReferralCode: user.ReferralCode,
		InviteLink:   inviteLink,
		QRCodeURL:    qrcodeURL,
	}
	
	result.OK(c.Ctx, response)
}

// Options 处理OPTIONS预检请求
// @Summary 处理OPTIONS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Tags 分销管理
// @Accept json
// @Produce json
// @Success 200 {string} string "OK"
// @Router /api/v1/user/secured/referral/create [options]
// @Router /api/v1/user/secured/referral/referrer [options]
// @Router /api/v1/user/secured/referral/referrals [options]
// @Router /api/v1/user/secured/referral/statistics [options]
// @Router /api/v1/user/secured/referral/info [options]
func (c *ReferralController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}
