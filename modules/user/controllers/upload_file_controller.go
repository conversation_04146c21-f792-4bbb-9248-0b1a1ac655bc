/*
 * 用户文件上传控制器
 *
 * 该文件处理用户模块的文件上传相关HTTP请求，基于通用上传控制器实现。
 * 主要用于用户头像、个人资料等文件的上传功能。
 */

package controllers

import (
	"github.com/beego/beego/v2/server/web"
	adminServices "o_mall_backend/modules/admin/services"
	commonControllers "o_mall_backend/common/controllers"
)

// UploadFileController 用户文件上传控制器
type UploadFileController struct {
	web.Controller
	uploadService adminServices.UploadFileService
	commonUpload  *commonControllers.CommonUploadController
}

// Prepare 预处理方法
func (c *UploadFileController) Prepare() {
	c.uploadService = adminServices.NewUploadFileService()
	c.commonUpload = commonControllers.NewCommonUploadController("user")
	// 设置通用控制器的上下文
	c.commonUpload.Ctx = c.Ctx
}

// Upload 文件上传
// @Title 文件上传
// @Description 上传文件到服务器，支持用户头像等文件类型
// @Param file formData file true "上传的文件"
// @Param file_usage formData string false "文件用途，默认为avatar"
// @Success 200 {object} adminDto.UploadFileResponse
// @Failure 400 {object} response.ErrorResponse
// @router /upload [post]
func (c *UploadFileController) Upload() {
	// 委托给通用上传控制器处理
	c.commonUpload.Upload()
}

// GetFile 获取文件信息
// @Title 获取文件信息
// @Description 根据文件ID获取文件详细信息
// @Param id path int true "文件ID"
// @Success 200 {object} adminDto.UploadFileResponse
// @Failure 400 {object} response.ErrorResponse
// @router /:id [get]
func (c *UploadFileController) GetFile() {
	// 委托给通用上传控制器处理
	c.commonUpload.GetFile()
}

// List 获取文件列表
// @Title 获取文件列表
// @Description 根据条件查询文件列表
// @Param file_usage query string false "文件用途"
// @Param user_type query string false "上传者类型"
// @Param user_id query int false "上传者ID"
// @Param is_anonymous query bool false "是否匿名上传"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} adminDto.UploadFileListResponse
// @Failure 400 {object} response.ErrorResponse
// @router /list [get]
func (c *UploadFileController) List() {
	// 委托给通用上传控制器处理
	c.commonUpload.List()
}

// Delete 删除文件
// @Title 删除文件
// @Description 根据文件ID删除文件
// @Param id path int true "文件ID"
// @Success 200 {object} response.SuccessResponse
// @Failure 400 {object} response.ErrorResponse
// @router /:id [delete]
func (c *UploadFileController) Delete() {
	// 委托给通用上传控制器处理
	c.commonUpload.Delete()
}

// GetConfig 获取上传配置
// @Title 获取上传配置
// @Description 获取上传配置信息
// @Success 200 {object} adminDto.UploadConfigResponse
// @Failure 400 {object} response.ErrorResponse
// @router /config [get]
func (c *UploadFileController) GetConfig() {
	// 委托给通用上传控制器处理
	c.commonUpload.GetConfig()
}

// IsAdmin 判断当前用户是否为管理员
func (c *UploadFileController) IsAdmin() bool {
	// 用户模块中，普通用户不是管理员
	return false
}

// Options 处理OPTIONS预检请求
// @Summary 处理OPTIONS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Tags 文件上传
// @Accept json
// @Produce json
// @Success 200 {string} string "OK"
// @Router /api/v1/user/secured/upload [options]
// @Router /api/v1/user/secured/upload/config [options]
// @Router /api/v1/user/secured/upload/list [options]
// @Router /api/v1/user/secured/upload/{id} [options]
func (c *UploadFileController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}