/**
 * 用户控制器
 *
 * 该文件实现了用户相关的API接口控制器，处理用户注册、登录、信息管理等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"fmt"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/result"
	userDto "o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/services"
	"o_mall_backend/utils"
	"o_mall_backend/utils/common"
)

// 全局控制器实例，用于解决Beego框架Controller实例化问题
var (
	// UserControllerInstance 全局用户控制器实例
	UserControllerInstance *UserController
)

// UserController 用户控制器
type UserController struct {
	web.Controller
	userService services.UserService
}

// NewUserController 创建用户控制器实例
// 创建并保存到全局实例中，以便在GetUserController中返回
func NewUserController(userService services.UserService) *UserController {
	controllerId := fmt.Sprintf("%p", &userService)
	logs.Info("[UserController] 创建控制器实例，controllerID: %s", controllerId)

	if userService == nil {
		logs.Error("[UserController] 严重错误：创建控制器时传入的userService为nil")
	}

	controller := &UserController{
		userService: userService,
	}

	logs.Info("[UserController] 控制器实例创建完成, instanceAddr: %p, userServiceAddr: %p", controller, &controller.userService)
	// 保存到全局变量
	UserControllerInstance = controller
	return controller
}

// ParseRequest 通用请求参数解析方法
func (c *UserController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// ensureUserService 确保userService已初始化
// Beego框架会为每个请求创建新的控制器实例，需要从全局实例获取服务
func (c *UserController) ensureUserService() {
	if c.userService == nil {
		if UserControllerInstance != nil && UserControllerInstance.userService != nil {
			c.userService = UserControllerInstance.userService
			logs.Info("[UserController] 从全局实例获取userService成功")
		} else {
			logs.Error("[UserController] 严重错误：全局控制器实例或userService为nil")
		}
	}
}

// Register 用户注册
// @Title 用户注册
// @Description 创建新用户账号
// @Param	body	body	userDto.RegisterRequest	true	"注册信息"
// @Success 200 {object} dto.Response 成功返回用户ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /register [post]
func (c *UserController) Register() {
	// 确保userService已初始化
	c.ensureUserService()

	// 解析请求体
	var req userDto.RegisterRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 获取注册IP
	registerIP := c.Ctx.Input.IP()

	// 调用服务层处理注册逻辑
	userID, err := c.userService.Register(c.Ctx.Request.Context(), &req, registerIP)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, userID)
}

// Login 用户登录
// @Title 用户登录
// @Description 用户账号登录，获取认证Token
// @Param	body	body	userDto.LoginRequest	true	"登录信息"
// @Success 200 {object} dto.Response 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /login [post]
func (c *UserController) Login() {
	// 确保userService已初始化
	c.ensureUserService()

	// 解析请求体
	var req userDto.LoginRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数验证
	if req.Username == "" || req.Password == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "用户名和密码不能为空")
		return
	}

	// 获取客户端IP
	loginIP := c.Ctx.Input.IP()

	// 调用服务层进行登录
	response, err := c.userService.Login(c.Ctx.Request.Context(), &req, loginIP)
	if err != nil {
		result.HandleError(c.Ctx, err, "登录失败")
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, response)
}

// LoginByVerifyCode 验证码登录
// @Title 验证码登录
// @Description 用户手机号+验证码登录，获取认证Token，如果手机号不存在则自动创建用户
// @Param	body	body	userDto.UserVerifyCodeLoginRequest	true	"登录信息"
// @Success 200 {object} dto.Response 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /login/verify-code [post]
func (c *UserController) LoginByVerifyCode() {
	// 确保userService已初始化
	c.ensureUserService()

	// 添加调试日志，记录当前控制器实例信息
	controllerId := fmt.Sprintf("%p", c)
	logs.Info("[UserController.LoginByVerifyCode] 开始处理验证码登录请求，控制器实例ID: %s, 全局实例ID: %p", controllerId, UserControllerInstance)

	// 解析请求体
	var req userDto.UserVerifyCodeLoginRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	logs.Info("[UserController.LoginByVerifyCode] 请求参数解析成功: 手机号=%s", req.Mobile)

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 获取登录IP
	loginIP := c.Ctx.Input.IP()

	logs.Info("[UserController.LoginByVerifyCode] 登录IP: %s", loginIP)

	// 调试信息：检查userService是否为nil
	if c.userService == nil {
		logs.Error("[UserController.LoginByVerifyCode] userService为空，控制器未正确初始化，控制器实例ID: %s", controllerId)
		// 输出控制器实例内存地址信息
		logs.Error("[UserController.LoginByVerifyCode] 控制器实例地址: %p", c)
		// 输出当前Ctx信息，可能提供更多线索
		logs.Error("[UserController.LoginByVerifyCode] 当前请求路径: %s，方法: %s", c.Ctx.Request.URL.Path, c.Ctx.Request.Method)

		// 尝试使用全局控制器实例中的userService
		if UserControllerInstance != nil && UserControllerInstance.userService != nil {
			logs.Info("[UserController.LoginByVerifyCode] 尝试使用全局控制器实例的userService")
			// 使用全局控制器实例的userService
			c.userService = UserControllerInstance.userService
			logs.Info("[UserController.LoginByVerifyCode] 已使用全局控制器实例的userService，userService地址: %p", c.userService)
		} else {
			logs.Error("[UserController.LoginByVerifyCode] 全局控制器实例不可用，无法修复")
			result.HandleError(c.Ctx, utils.NewAppError(result.CodeInternalError, "服务未正确初始化", nil), "服务未正确初始化")
			return
		}
	}
	logs.Info("[LoginByVerifyCode] userService 已正确初始化，开始处理验证码登录请求")

	// 调用服务层处理验证码登录逻辑
	resp, err := c.userService.LoginByVerifyCode(c.Ctx.Request.Context(), &req, loginIP)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		// 对于登录错误，统一返回401状态码
		if appErr.Code == 500 { // 未知错误
			appErr = utils.NewAppError(result.CodeUnauthorized, appErr.Message, appErr.Details)
		}
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// RefreshToken 刷新访问令牌
// @Title 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌和刷新令牌对
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param body body userDto.RefreshTokenRequest true "刷新令牌请求参数"
// @Success 200 {object} dto.Response{data=userDto.TokenResponse} "成功返回新的令牌对"
// @Failure 400 {object} dto.Response "参数错误"
// @Failure 401 {object} dto.Response "刷新令牌无效或已过期"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @Router /api/v1/user/refresh-token [post]
func (c *UserController) RefreshToken() {
	// 解析请求体
	var req userDto.RefreshTokenRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用刷新token接口
	tokenPair, err := utils.RefreshUserTokens(req.RefreshToken)
	if err != nil {
		var appErr *utils.AppError
		switch err {
		case utils.ErrTokenExpired:
			appErr = utils.NewAppError(result.CodeUnauthorized, "令牌已过期", err.Error())
		case utils.ErrTokenInvalid, utils.ErrInvalidTokenType:
			appErr = utils.NewAppError(result.CodeUnauthorized, "无效的令牌", err.Error())
		case utils.ErrTokenNotFound:
			appErr = utils.NewAppError(result.CodeUnauthorized, "令牌不存在", err.Error())
		default:
			appErr = utils.NewAppError(result.CodeError, "刷新令牌失败", err.Error())
		}
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 创建响应
	resp := &userDto.TokenResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
		TokenType:    "Bearer",
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// Logout 用户登出
// @Title 用户登出
// @Description 退出登录，使当前用户的所有令牌失效
// @Tags 用户认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.Response "成功退出登录"
// @Failure 401 {object} dto.Response "未认证或认证令牌无效"
// @Failure 500 {object} dto.Response "服务器内部错误"
// @Router /api/v1/user/secured/logout [post]
func (c *UserController) Logout() {
	// 从请求中获取令牌
	token := utils.GetTokenFromRequest(c.Ctx)
	if token == "" {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未提供认证令牌")
		return
	}

	// 解析token，获取用户ID
	claims, err := utils.ParseToken(token)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "无效的认证令牌")
		return
	}

	// 撤销所有token
	err = utils.RevokeAllUserTokens(claims.UserID, token)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "登出失败："+err.Error())
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// GetAccountTransactions 获取账户变动记录
// @Title 获取账户变动记录
// @Description 获取当前用户的账户变动记录列表
// @Param	transaction_type	query	string	false	"交易类型，可选值：recharge,withdraw,payment,refund,adjust"
// @Param	page	query	int	false	"页码，从1开始，默认1"
// @Param	page_size	query	int	false	"每页数量，1-100，默认20"
// @Success 200 {object} dto.Response{data=userDto.AccountTransactionListResponse} 成功返回账户变动记录
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /account/transactions [get]
func (c *UserController) GetAccountTransactions() {
	// 确保userService已初始化
	c.ensureUserService()

	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析查询参数（已移除transaction_type参数）
	var req userDto.AccountTransactionRequest

	req.Page, _ = c.GetInt("page", 1)
	req.PageSize, _ = c.GetInt("page_size", 20)

	logs.Info("[DEBUG] Controller - Page: %d, PageSize: %d", req.Page, req.PageSize)

	// 参数校验
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 调用服务层获取账户变动记录
	response, err := c.userService.GetAccountTransactions(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, response)
}

// GetAccountInfo 获取账户信息
// @Title 获取账户信息
// @Description 获取当前用户的账户详细信息
// @Success 200 {object} dto.Response{data=userDto.UserAccountResponse} 成功返回账户信息
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /account/info [get]
func (c *UserController) GetAccountInfo() {
	// 确保userService已初始化
	c.ensureUserService()

	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 调用服务层获取账户信息
	response, err := c.userService.GetAccountInfo(c.Ctx.Request.Context(), userID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, response)
}

// Options 处理OPTIONS预检请求
// @Summary 处理OPTIONS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {string} string "OK"
// @Router /api/v1/user/login [options]
// @Router /api/v1/user/register [options]
// @Router /api/v1/user/refresh-token [options]
func (c *UserController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}

// SendVerificationCode 发送登录验证码
// @Title 发送登录验证码
// @Description 发送用户登录的手机验证码
// @Param	body	body	userDto.SendVerificationCodeRequest	true	"手机号信息"
// @Success 200 {object} dto.Response 成功返回验证码发送状态
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /send-verification-code [post]
func (c *UserController) SendVerificationCode() {
	// 确保userService已初始化
	c.ensureUserService()

	// 解析请求体
	var req userDto.SendVerificationCodeRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层发送验证码
	resp, err := c.userService.SendVerificationCode(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// WxMiniLogin 微信小程序一键登录
// @Title 微信小程序一键登录
// @Description 使用微信小程序一键登录或注册账号
// @Param	body	body	userDto.WechatMiniLoginRequest	true	"微信小程序登录信息"
// @Success 200 {object} dto.Response{data=utils.TokenPair} 成功返回登录信息
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /wx/mini/login [post]
func (c *UserController) WxMiniLogin() {
	// 确保userService已初始化
	c.ensureUserService()

	// 解析请求体
	var req userDto.WechatMiniLoginRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 获取登录IP
	loginIP := c.Ctx.Input.IP()
	req.LoginIP = loginIP

	// 调用服务层处理微信登录逻辑
	response, err := c.userService.WxMiniLogin(c.Ctx.Request.Context(), &req, loginIP)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, response)
}

// SendRegisterVerificationCode 发送注册验证码
// @Title 发送注册验证码
// @Description 发送用户注册的手机验证码
// @Param	body	body	userDto.SendVerificationCodeRequest	true	"手机号信息"
// @Success 200 {object} dto.Response 成功返回验证码发送状态
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /register/send-code [post]
func (c *UserController) SendRegisterVerificationCode() {
	// 确保userService已初始化
	c.ensureUserService()

	// 解析请求体
	var req userDto.SendVerificationCodeRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 检查手机号是否已注册
	exist, err := c.userService.CheckMobileExists(c.Ctx.Request.Context(), req.Mobile)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}
	if exist {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "该手机号已注册，请直接登录或找回密码")
		return
	}

	// 调用服务层发送注册验证码
	resp, err := c.userService.SendRegisterVerificationCode(c.Ctx.Request.Context(), &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// RegisterByVerifyCode 手机号验证码注册
// @Title 手机号验证码注册
// @Description 使用手机号和短信验证码注册新用户
// @Param	body	body	userDto.RegisterByVerifyCodeRequest	true	"注册信息"
// @Success 200 {object} dto.Response 成功返回用户ID
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /register/verify-code [post]
func (c *UserController) RegisterByVerifyCode() {
	// 确保userService已初始化
	c.ensureUserService()

	// 解析请求体
	var req userDto.RegisterByVerifyCodeRequest
	if err := c.ParseRequest(&req); err != nil {
		logs.Error("[UserController] 解析请求参数失败: %v", err)
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 输出原始请求数据用于调试
	logs.Info("[UserController] 注册请求原始数据: %#v", req)

	// 参数校验
	// valid := validation.Validation{}
	// b, err := valid.Valid(&req)
	// if err != nil {
	// 	result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
	// 	return
	// }
	// if !b {
	// 	// 获取验证错误信息
	// 	var errMsg string
	// 	for _, err := range valid.Errors {
	// 		errMsg = err.Message
	// 		break
	// 	}
	// 	result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
	// 	return
	// }

	// 如果昵称为空，自动生成昵称（使用"user_"前缀加上手机号后四位）
	if req.Nickname == "" {
		mobileLen := len(req.Mobile)
		if mobileLen >= 4 {
			// 取手机号的后四位
			lastFour := req.Mobile[mobileLen-4:]
			req.Nickname = "user_" + lastFour
			logs.Info("[UserController] 自动生成昵称: %s，手机号: %s", req.Nickname, req.Mobile)
		} else {
			// 手机号长度异常情况下，使用完整手机号
			req.Nickname = "user_" + req.Mobile
			logs.Info("[UserController] 手机号长度异常，使用完整手机号生成昵称: %s", req.Nickname)
		}
	}

	// 获取注册IP
	registerIP := c.Ctx.Input.IP()

	// 添加调试日志，详细记录请求参数
	logs.Info("[UserController] 开始处理手机号验证码注册请求，手机号: %s, 验证码: %s, 推荐人: %d", req.Mobile, req.Code, req.ReferrerID)

	// 调用服务层处理注册逻辑
	logs.Info("[UserController] 即将调用服务层处理注册逻辑，手机号: %s", req.Mobile)
	
	// 如果email为空，则设置为nil，确保数据库中存储为null而不是空字符串
	if req.Email == "" {
		req.Email = "__NULL__"  // 使用特殊标记，在service层识别并转为null
		logs.Info("[UserController] Email为空，将设置为null")
	}

	userID, err := c.userService.RegisterByVerifyCode(c.Ctx.Request.Context(), &req, registerIP)
	if err != nil {
		// 详细记录错误信息
		logs.Error("[UserController] 手机号验证码注册失败, 错误类型: %T, 错误信息: %v", err, err)

		// 详细记录错误信息
		logs.Error("[UserController] 错误内容: %v", err)

		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		logs.Error("[UserController] 错误转换结果: 错误代码=%d, 消息=%s", appErr.Code, appErr.Message)

		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 注册成功日志
	logs.Info("[UserController] 手机号验证码注册成功, 用户ID: %d", userID)

	// 返回成功响应
	result.OK(c.Ctx, userID)
}

// GetUserInfo 获取用户信息
// @Title 获取用户信息
// @Description 获取当前登录用户的详细信息
// @Success 200 {object} dto.Response 成功返回用户信息
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /info [get]
func (c *UserController) GetUserInfo() {
	// 确保userService已初始化
	c.ensureUserService()

	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 调用服务层获取用户信息
	userInfo, err := c.userService.GetUserByID(c.Ctx.Request.Context(), userID)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, userInfo)
}

// UpdateUserInfo 更新用户信息
// @Title 更新用户信息
// @Description 更新当前登录用户的基本信息
// @Param	body	body	userDto.UpdateUserRequest	true	"用户信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /info [put]
func (c *UserController) UpdateUserInfo() {
	// 确保userService已初始化
	c.ensureUserService()

	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体
	var req userDto.UpdateUserRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层更新用户信息
	err = c.userService.UpdateUserInfo(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}

// ChangePassword 修改密码
// @Title 修改密码
// @Description 修改当前登录用户的密码
// @Param	body	body	userDto.ChangePasswordRequest	true	"密码信息"
// @Success 200 {object} dto.Response 成功返回
// @Failure 400 {object} dto.Response 参数错误
// @Failure 401 {object} dto.Response 未认证
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /password [put]
func (c *UserController) ChangePassword() {
	// 确保userService已初始化
	c.ensureUserService()

	// 从上下文获取当前用户ID
	userID, ok := c.Ctx.Input.GetData("userID").(int64)
	if !ok {
		result.HandleError(c.Ctx, result.ErrUnauthorized, "未认证的请求")
		return
	}

	// 解析请求体
	var req userDto.ChangePasswordRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	b, err := valid.Valid(&req)
	if err != nil {
		result.HandleError(c.Ctx, result.ErrInternalError, "参数验证失败")
		return
	}
	if !b {
		// 获取验证错误信息
		var errMsg string
		for _, err := range valid.Errors {
			errMsg = err.Message
			break
		}
		result.HandleError(c.Ctx, result.ErrInvalidParam, errMsg)
		return
	}

	// 调用服务层修改密码
	err = c.userService.ChangePassword(c.Ctx.Request.Context(), userID, &req)
	if err != nil {
		// 转换为通用错误响应
		appErr := utils.ConvertToAppError(err)
		result.HandleError(c.Ctx, appErr, appErr.Message)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, nil)
}
