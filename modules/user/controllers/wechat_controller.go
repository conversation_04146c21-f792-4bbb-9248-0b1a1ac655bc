/**
 * 微信登录控制器
 *
 * 该文件实现了微信扫码登录相关的API控制器，包括获取二维码、查询状态和处理登录。
 */

package controllers

import (
	"encoding/json"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/services"
)

// WechatController 微信登录控制器
// 处理微信扫码登录相关的接口请求
type WechatController struct {
	BaseController
	userService services.UserService
}

// Init 初始化控制器
// 初始化依赖的服务实例
func (c *WechatController) Init(ctx *context.Context, controllerName, actionName string, app interface{}) {
	// 调用父类的Init方法
	c.BaseController.Init(ctx, controllerName, actionName, app)
	// 初始化服务
	c.userService = services.GetUserService()
}

// QrCode 获取微信登录二维码
// @Title 获取微信登录二维码
// @Description 获取用于微信扫码登录的二维码信息
// @Param	body	body	dto.WechatQrCodeRequest	false	"重定向URL参数（可选）"
// @Success 200 {object} dto.Response{data=dto.WechatQrCodeResponse} 成功返回二维码信息
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /qrcode [post]
func (c *WechatController) QrCode() {
	// 确保服务已初始化
	c.ensureUserService()

	// 解析请求体
	var req dto.WechatQrCodeRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 调用服务层获取二维码
	resp, err := c.userService.GetWechatQrCode(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("获取微信登录二维码失败: %v", err)
		result.HandleError(c.Ctx, err, "获取微信登录二维码失败")
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// QrCodeStatus 查询微信登录二维码状态
// @Title 查询微信登录二维码状态
// @Description 查询微信扫码登录的二维码当前状态
// @Param	body	body	dto.WechatQrCodeStatusRequest	true	"二维码ID"
// @Success 200 {object} dto.Response{data=dto.WechatQrCodeStatusResponse} 成功返回二维码状态
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /qrcode/status [post]
func (c *WechatController) QrCodeStatus() {
	// 确保服务已初始化
	c.ensureUserService()

	// 解析请求体
	var req dto.WechatQrCodeStatusRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "无效的请求参数")
		return
	}

	// 参数校验
	if req.QrCodeID == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "二维码ID不能为空")
		return
	}

	// 调用服务层查询状态
	resp, err := c.userService.CheckWechatQrCodeStatus(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("查询微信登录二维码状态失败: %v", err)
		result.HandleError(c.Ctx, err, "查询微信登录二维码状态失败")
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// WebLogin 处理微信网页授权登录回调
// @Title 微信网页授权登录
// @Description 处理微信网页授权登录回调请求
// @Param	code	query	string	true	"微信授权码"
// @Param	state	query	string	true	"状态参数"
// @Success 200 {object} dto.Response{data=dto.WechatWebLoginResponse} 成功返回登录信息
// @Failure 400 {object} dto.Response 请求参数错误
// @Failure 401 {object} dto.Response 认证失败
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /web/login [get]
func (c *WechatController) WebLogin() {
	// 确保服务已初始化
	c.ensureUserService()

	// 获取URL参数
	code := c.GetString("code")
	state := c.GetString("state")

	if code == "" || state == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParam, "缺少必要的参数")
		return
	}

	// 构建请求对象
	req := dto.WechatWebLoginRequest{
		Code:     code,
		State:    state,
		ClientIP: c.Ctx.Input.IP(),
	}

	// 调用服务层处理登录
	resp, err := c.userService.WxWebLogin(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("微信网页授权登录失败: %v", err)
		result.HandleError(c.Ctx, err, "微信登录失败")
		return
	}

	// 返回成功响应或重定向
	result.OK(c.Ctx, resp)
}

// WebCallback 处理微信网页授权回调（HTML页面版）
// @Title 微信网页授权回调
// @Description 处理微信网页授权回调，并返回HTML页面进行前端处理
// @Param	code	query	string	true	"微信授权码"
// @Param	state	query	string	true	"状态参数"
// @Success 200 {string} string HTML回调页面
// @Failure 400 {string} string 错误页面
// @router /web/callback [get]
func (c *WechatController) WebCallback() {
	// 确保服务已初始化
	c.ensureUserService()

	// 获取URL参数
	code := c.GetString("code")
	state := c.GetString("state")

	if code == "" || state == "" {
		c.renderErrorPage("缺少必要的参数")
		return
	}

	// 构建请求对象
	req := dto.WechatWebLoginRequest{
		Code:     code,
		State:    state,
		ClientIP: c.Ctx.Input.IP(),
	}

	// 调用服务层处理登录
	resp, err := c.userService.WxWebLogin(c.Ctx.Request.Context(), &req)
	if err != nil {
		logs.Error("微信网页授权回调处理失败: %v", err)
		c.renderErrorPage("微信登录失败: " + err.Error())
		return
	}

	// 将登录结果转为JSON字符串
	resultJSON, err := json.Marshal(resp)
	if err != nil {
		logs.Error("序列化登录结果失败: %v", err)
		c.renderErrorPage("处理登录结果失败")
		return
	}

	// 渲染成功页面，将登录结果嵌入HTML
	c.renderSuccessPage(string(resultJSON))
}

// 确保userService已初始化
func (c *WechatController) ensureUserService() {
	if c.userService == nil {
		c.userService = services.GetUserService()
	}
}

// renderSuccessPage 渲染成功页面
func (c *WechatController) renderSuccessPage(resultJSON string) {
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信登录成功</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            text-align: center;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .success-icon {
            color: #07C160;
            font-size: 60px;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #07C160;
        }
        p {
            margin-bottom: 30px;
            color: #666;
        }
        .closing {
            font-size: 14px;
            color: #999;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>登录成功</h1>
        <p>您已成功完成微信授权登录</p>
        <div class="closing">页面即将关闭...</div>
    </div>
    <script>
        // 将登录结果存储到本地，供前端应用使用
        try {
            const loginResult = ` + resultJSON + `;
            localStorage.setItem('wxLoginResult', JSON.stringify(loginResult));
            
            // 如果是从指定页面打开的，则通知父窗口登录成功
            if (window.opener) {
                window.opener.postMessage({
                    type: 'WX_LOGIN_SUCCESS',
                    data: loginResult
                }, '*');
                
                // 3秒后关闭窗口
                setTimeout(() => {
                    window.close();
                }, 3000);
            } else {
                // 如果没有父窗口，则尝试重定向到首页
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            }
        } catch (e) {
            console.error('处理登录结果出错', e);
        }
    </script>
</body>
</html>`

	c.Ctx.Output.Header("Content-Type", "text/html; charset=utf-8")
	c.Ctx.WriteString(html)
}

// renderErrorPage 渲染错误页面
func (c *WechatController) renderErrorPage(errorMsg string) {
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信登录失败</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            text-align: center;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .error-icon {
            color: #fa5151;
            font-size: 60px;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #fa5151;
        }
        p {
            margin-bottom: 30px;
            color: #666;
        }
        .error-message {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            color: #fa5151;
        }
        .closing {
            font-size: 14px;
            color: #999;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">✕</div>
        <h1>登录失败</h1>
        <div class="error-message">` + errorMsg + `</div>
        <p>微信授权登录过程中出现问题，请重试</p>
        <div class="closing">页面即将关闭...</div>
    </div>
    <script>
        // 通知父窗口登录失败
        if (window.opener) {
            window.opener.postMessage({
                type: 'WX_LOGIN_ERROR',
                error: '` + errorMsg + `'
            }, '*');
            
            // 5秒后关闭窗口
            setTimeout(() => {
                window.close();
            }, 5000);
        }
    </script>
</body>
</html>`

	c.Ctx.Output.Header("Content-Type", "text/html; charset=utf-8")
	c.Ctx.WriteString(html)
}
