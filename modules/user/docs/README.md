# 用户模块文档

## 模块简介

用户模块是O_Mall系统的核心模块之一，负责处理用户账户管理、身份认证、个人信息管理、收货地址管理以及分销推荐等功能。该模块为系统提供了完整的用户生命周期管理，从注册、登录到信息更新、账户安全和分销推荐等各个方面。

## 文档索引

### 核心模块文档
- [模块架构设计](./architecture.md) - 用户模块的总体架构设计与技术选型说明
- [数据模型说明](./data_models.md) - 用户相关数据库表结构与字段说明
- [API接口文档](./api_reference.md) - 用户模块对外提供的API接口详细说明
- [业务流程说明](./business_flows.md) - 主要业务流程的详细说明与流程图
- [安全机制说明](./security.md) - 用户认证与数据安全保护机制

### 分销模块文档
- [分销模块总览](./referral_overview.md) - 分销功能概述与文档导航
- [分销架构设计](./referral_module_architecture.md) - 分销模块的架构设计与实现原理
- [分销API接口](./referral_api_reference.md) - 分销相关API接口详细说明
- [分销数据库设计](./referral_database_design.md) - 分销相关数据库表结构设计
- [分销业务流程](./referral_business_process.md) - 分销业务流程与规则说明
- [分销配置使用](./referral_config_usage.md) - 分销配置管理与使用指南
- [分销前端指南](./referral_frontend_guide.md) - 分销功能前端开发指南
- [分销测试指南](./referral_testing_guide.md) - 分销功能测试策略与用例
- [分销部署指南](./referral_deployment_guide.md) - 分销功能部署与运维指南

## 主要功能

用户模块提供以下核心功能：

### 基础功能
1. **用户账户管理**：用户注册、登录、登出、密码修改等
2. **身份认证与授权**：JWT认证、令牌刷新与失效管理
3. **个人信息管理**：用户基本资料维护与查询
4. **收货地址管理**：添加、编辑、删除与查询收货地址
5. **安全审计日志**：记录用户敏感操作，便于安全审计
6. **账户财务管理**：余额与积分管理

### 分销功能
7. **分销关系管理**：建立和管理用户推荐关系，支持多级分销
8. **推荐码系统**：为每个用户生成唯一推荐码，支持推荐注册
9. **佣金计算与管理**：根据分销等级自动计算和分配佣金
10. **分销统计分析**：提供详细的分销数据统计和分析报告
11. **分销配置管理**：灵活配置分销等级、佣金比例等参数
12. **分销审计日志**：记录所有分销相关操作，确保数据可追溯

## 技术栈

- 后端框架：Beego v2
- 数据库交互：使用Beego ORM
- 认证机制：JWT (JSON Web Token)
- 密码加密：使用安全哈希算法
- 数据验证：使用Beego validation进行输入验证

## 依赖关系

用户模块是系统基础模块，其它多个业务模块都依赖于用户模块提供的用户身份与权限服务。
