# 账户变动API调试指南

## 问题分析

### 原始问题
- API `/api/user/account/transactions` 返回 null
- 数据库中存在相关记录

### 根本原因
数据库表字段命名与Go模型字段映射不匹配：

**数据库表字段（下划线命名）：**
- `i_d` (主键)
- `user_i_d` (用户ID)
- `account_i_d` (账户ID)
- `related_i_d` (关联ID)
- `operator_i_d` (操作人ID)
- `client_i_p` (客户端IP)

**Go模型字段（驼峰命名）：**
- `ID`
- `UserID`
- `AccountID`
- `RelatedID`
- `OperatorID`
- `ClientIP`

### 修复方案

1. **模型字段映射修复**
   - 在 `UserAccountTransaction` 模型中添加 `column` 标签
   - 确保ORM能正确映射数据库字段名

2. **查询条件修复**
   - 将 `Filter("UserID", userID)` 改为 `Filter("user_i_d", userID)`
   - 将 `OrderBy("-id")` 改为 `OrderBy("-i_d")`

3. **调试日志添加**
   - 在服务层和仓储层添加详细的调试日志
   - 记录查询参数、结果数量和具体数据

## 修复后的代码变更

### 1. 模型字段映射 (`models/user_account.go`)

```go
type UserAccountTransaction struct {
    ID            int64     `orm:"pk;auto;column(i_d);description(交易ID)" json:"id"`
    UserID        int64     `orm:"index;column(user_i_d);description(用户ID)" json:"user_id"`
    AccountID     int64     `orm:"index;column(account_i_d);description(账户ID)" json:"account_id"`
    // ... 其他字段
}
```

### 2. 查询条件修复 (`repositories/user_account_repository.go`)

```go
qs := o.QueryTable(new(models.UserAccountTransaction)).Filter("user_i_d", userID)
// ...
_, err = qs.OrderBy("-i_d").Limit(pageSize, offset).All(&transactions)
```

### 3. 调试日志

在服务层和仓储层添加了详细的调试日志，包括：
- 查询参数记录
- 查询结果统计
- 具体数据内容输出

## 测试验证

### 1. 直接API测试

```bash
# 获取账户变动记录
curl -X GET "http://localhost:8080/api/user/account/transactions?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取账户信息
curl -X GET "http://localhost:8080/api/user/account/info" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 查看调试日志

启动服务后，调用API时会在控制台输出详细的调试信息：

```
[DEBUG] GetAccountTransactions - UserID: 1, TransactionType: , Page: 1, PageSize: 10
[DEBUG] Repository GetUserTransactions - UserID: 1, TransactionType: , Page: 1, PageSize: 10
[DEBUG] Total count: 5
[DEBUG] Query returned 5 transactions
[DEBUG] Transaction 0: ID=1, UserID=1, Amount=100.00, Type=recharge
[DEBUG] Transaction 1: ID=2, UserID=1, Amount=-50.00, Type=payment
...
```

### 3. 数据库验证

```sql
-- 验证数据是否存在
SELECT COUNT(*) FROM user_account_transaction WHERE user_i_d = 1;

-- 查看具体数据
SELECT * FROM user_account_transaction WHERE user_i_d = 1 ORDER BY i_d DESC LIMIT 10;
```

## 常见问题排查

### 1. 仍然返回空数据
- 检查用户ID是否正确
- 确认数据库连接正常
- 查看调试日志中的查询参数

### 2. 字段映射错误
- 确认数据库表结构与模型定义一致
- 检查 `column` 标签是否正确设置

### 3. 权限问题
- 确认用户已正确登录
- 检查JWT token是否有效
- 验证用户ID提取逻辑

## 性能优化建议

1. **索引优化**
   - 确保 `user_i_d` 字段有索引
   - 考虑添加复合索引 `(user_i_d, type, i_d)`

2. **查询优化**
   - 限制单次查询的最大记录数
   - 考虑使用缓存机制

3. **日志管理**
   - 生产环境中移除详细的调试日志
   - 使用结构化日志记录关键信息

## 后续改进

1. **统一命名规范**
   - 建议统一使用驼峰命名或下划线命名
   - 避免混合使用不同的命名风格

2. **自动化测试**
   - 添加单元测试覆盖账户相关功能
   - 集成测试验证API完整流程

3. **错误处理**
   - 完善错误码定义
   - 提供更友好的错误信息