# 用户账户变动API前端开发指导

## 概述

本文档为前端开发者提供用户账户变动相关API的详细使用指南，包括API接口说明、请求参数、响应格式、错误处理以及前端实现示例。

## API接口列表

### 1. 获取账户信息

**接口地址：** `GET /api/user/account/info`

**接口描述：** 获取当前用户的账户详细信息，包括余额、冻结金额、充值消费统计等。

**请求头：**
```http
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：** 无

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "user_id": 123,
    "balance": "1000.50",
    "frozen_balance": "100.00",
    "total_recharge": "5000.00",
    "total_consume": "3899.50",
    "status": 1,
    "last_recharge": "2024-01-15 10:30:00",
    "last_consume": "2024-01-16 14:20:00",
    "updated_at": "2024-01-16 14:20:00",
    "created_at": "2024-01-01 09:00:00"
  }
}
```

**字段说明：**
- `id`: 账户记录ID
- `user_id`: 用户ID
- `balance`: 可用余额
- `frozen_balance`: 冻结金额
- `total_recharge`: 累计充值金额
- `total_consume`: 累计消费金额
- `status`: 账户状态（1：正常，0：冻结）
- `last_recharge`: 最后充值时间
- `last_consume`: 最后消费时间
- `updated_at`: 更新时间
- `created_at`: 创建时间

### 2. 获取账户变动记录

**接口地址：** `GET /api/user/account/transactions`

**接口描述：** 获取用户的账户变动记录列表，支持分页查询和类型筛选。

**请求头：**
```http
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| page_size | int | 否 | 20 | 每页数量 |
| transaction_type | int | 否 | 0 | 交易类型筛选（0：全部，1：充值，2：消费，3：退款，4：提现，5：转账） |

**请求示例：**
```http
GET /api/user/account/transactions?page=1&page_size=10&transaction_type=1
```

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "transaction_no": "TXN202401160001",
        "related_id": 1001,
        "related_type": "order",
        "amount": "100.00",
        "before_balance": "900.50",
        "after_balance": "1000.50",
        "type": 1,
        "operation": 1,
        "status": 1,
        "description": "订单充值",
        "remark": "充值成功",
        "client_ip": "*************",
        "created_at": "2024-01-16 10:30:00"
      }
    ],
    "total": 50,
    "page": 1,
    "size": 10
  }
}
```

**字段说明：**
- `id`: 交易记录ID
- `transaction_no`: 交易流水号
- `related_id`: 关联业务ID
- `related_type`: 关联业务类型
- `amount`: 交易金额
- `before_balance`: 交易前余额
- `after_balance`: 交易后余额
- `type`: 交易类型（1：充值，2：消费，3：退款，4：提现，5：转账）
- `operation`: 操作类型（1：增加，2：减少）
- `status`: 交易状态（1：成功，2：失败，3：处理中）
- `description`: 交易描述
- `remark`: 备注信息
- `client_ip`: 客户端IP
- `created_at`: 创建时间

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 前端实现示例

### Vue.js 实现示例

#### 1. API服务封装

```javascript
// api/account.js
import request from '@/utils/request'

// 获取账户信息
export function getAccountInfo() {
  return request({
    url: '/api/user/account/info',
    method: 'get'
  })
}

// 获取账户变动记录
export function getAccountTransactions(params) {
  return request({
    url: '/api/user/account/transactions',
    method: 'get',
    params
  })
}
```

#### 2. 账户信息组件

```vue
<template>
  <div class="account-info">
    <div class="balance-card">
      <h3>账户余额</h3>
      <div class="balance-amount">¥{{ accountInfo.balance }}</div>
      <div class="balance-details">
        <div class="detail-item">
          <span>冻结金额：</span>
          <span>¥{{ accountInfo.frozen_balance }}</span>
        </div>
        <div class="detail-item">
          <span>累计充值：</span>
          <span>¥{{ accountInfo.total_recharge }}</span>
        </div>
        <div class="detail-item">
          <span>累计消费：</span>
          <span>¥{{ accountInfo.total_consume }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAccountInfo } from '@/api/account'

export default {
  name: 'AccountInfo',
  data() {
    return {
      accountInfo: {
        balance: '0.00',
        frozen_balance: '0.00',
        total_recharge: '0.00',
        total_consume: '0.00'
      },
      loading: false
    }
  },
  mounted() {
    this.fetchAccountInfo()
  },
  methods: {
    async fetchAccountInfo() {
      try {
        this.loading = true
        const response = await getAccountInfo()
        if (response.code === 200) {
          this.accountInfo = response.data
        }
      } catch (error) {
        console.error('获取账户信息失败:', error)
        this.$message.error('获取账户信息失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
```

#### 3. 账户变动记录组件

```vue
<template>
  <div class="transaction-list">
    <!-- 筛选条件 -->
    <div class="filter-bar">
      <select v-model="filters.transaction_type" @change="handleFilterChange">
        <option value="0">全部类型</option>
        <option value="1">充值</option>
        <option value="2">消费</option>
        <option value="3">退款</option>
        <option value="4">提现</option>
        <option value="5">转账</option>
      </select>
    </div>

    <!-- 交易记录列表 -->
    <div class="transaction-items">
      <div 
        v-for="item in transactionList" 
        :key="item.id" 
        class="transaction-item"
      >
        <div class="transaction-info">
          <div class="transaction-desc">{{ item.description }}</div>
          <div class="transaction-time">{{ item.created_at }}</div>
          <div class="transaction-no">流水号：{{ item.transaction_no }}</div>
        </div>
        <div class="transaction-amount" :class="getAmountClass(item.operation)">
          {{ item.operation === 1 ? '+' : '-' }}¥{{ item.amount }}
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <button 
        :disabled="pagination.page <= 1" 
        @click="handlePageChange(pagination.page - 1)"
      >
        上一页
      </button>
      <span>{{ pagination.page }} / {{ totalPages }}</span>
      <button 
        :disabled="pagination.page >= totalPages" 
        @click="handlePageChange(pagination.page + 1)"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script>
import { getAccountTransactions } from '@/api/account'

export default {
  name: 'TransactionList',
  data() {
    return {
      transactionList: [],
      filters: {
        transaction_type: 0
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      loading: false
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.pagination.total / this.pagination.size)
    }
  },
  mounted() {
    this.fetchTransactions()
  },
  methods: {
    async fetchTransactions() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.page,
          page_size: this.pagination.size,
          transaction_type: this.filters.transaction_type
        }
        
        const response = await getAccountTransactions(params)
        if (response.code === 200) {
          this.transactionList = response.data.list
          this.pagination.total = response.data.total
        }
      } catch (error) {
        console.error('获取交易记录失败:', error)
        this.$message.error('获取交易记录失败')
      } finally {
        this.loading = false
      }
    },
    
    handleFilterChange() {
      this.pagination.page = 1
      this.fetchTransactions()
    },
    
    handlePageChange(page) {
      this.pagination.page = page
      this.fetchTransactions()
    },
    
    getAmountClass(operation) {
      return operation === 1 ? 'amount-increase' : 'amount-decrease'
    }
  }
}
</script>

<style scoped>
.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.transaction-info {
  flex: 1;
}

.transaction-desc {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.transaction-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

.transaction-no {
  font-size: 12px;
  color: #666;
}

.transaction-amount {
  font-size: 18px;
  font-weight: bold;
}

.amount-increase {
  color: #52c41a;
}

.amount-decrease {
  color: #f5222d;
}

.filter-bar {
  padding: 15px;
  background: #f8f9fa;
  margin-bottom: 10px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  gap: 10px;
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  cursor: pointer;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
```

### React 实现示例

#### 1. API服务封装

```javascript
// services/accountService.js
import axios from 'axios'

const API_BASE_URL = '/api/user/account'

// 获取账户信息
export const getAccountInfo = async () => {
  const response = await axios.get(`${API_BASE_URL}/info`)
  return response.data
}

// 获取账户变动记录
export const getAccountTransactions = async (params) => {
  const response = await axios.get(`${API_BASE_URL}/transactions`, { params })
  return response.data
}
```

#### 2. 账户信息组件

```jsx
// components/AccountInfo.jsx
import React, { useState, useEffect } from 'react'
import { getAccountInfo } from '../services/accountService'

const AccountInfo = () => {
  const [accountInfo, setAccountInfo] = useState({
    balance: '0.00',
    frozen_balance: '0.00',
    total_recharge: '0.00',
    total_consume: '0.00'
  })
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetchAccountInfo()
  }, [])

  const fetchAccountInfo = async () => {
    try {
      setLoading(true)
      const response = await getAccountInfo()
      if (response.code === 200) {
        setAccountInfo(response.data)
      }
    } catch (error) {
      console.error('获取账户信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="account-info">
      <div className="balance-card">
        <h3>账户余额</h3>
        <div className="balance-amount">¥{accountInfo.balance}</div>
        <div className="balance-details">
          <div className="detail-item">
            <span>冻结金额：</span>
            <span>¥{accountInfo.frozen_balance}</span>
          </div>
          <div className="detail-item">
            <span>累计充值：</span>
            <span>¥{accountInfo.total_recharge}</span>
          </div>
          <div className="detail-item">
            <span>累计消费：</span>
            <span>¥{accountInfo.total_consume}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AccountInfo
```

## 最佳实践建议

### 1. 错误处理

- 统一处理API错误响应
- 提供用户友好的错误提示
- 实现重试机制

### 2. 数据缓存

- 账户信息可以适当缓存，减少频繁请求
- 交易记录支持本地分页缓存

### 3. 用户体验

- 添加加载状态提示
- 实现下拉刷新功能
- 支持无限滚动加载

### 4. 安全考虑

- 敏感金额信息加密传输
- 实现请求防重放机制
- 添加操作日志记录

### 5. 性能优化

- 合理设置分页大小
- 使用虚拟滚动处理大量数据
- 实现图片懒加载

## 常见问题

### Q1: 如何处理金额精度问题？
A: 后端返回的金额都是字符串格式，前端直接显示即可，避免浮点数精度问题。

### Q2: 交易记录如何实现实时更新？
A: 可以使用WebSocket或定时轮询的方式实现实时更新。

### Q3: 如何优化大量交易记录的加载性能？
A: 建议使用虚拟滚动技术，只渲染可视区域的数据。

### Q4: 账户余额显示不一致怎么办？
A: 检查缓存策略，确保关键操作后及时刷新账户信息。

## 联系支持

如有技术问题，请联系后端开发团队或查看项目文档。