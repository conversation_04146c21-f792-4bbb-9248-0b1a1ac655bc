# 用户模块API接口文档

本文档详细说明了用户模块对外提供的所有API接口，包括请求格式、参数说明和响应结构。

## 接口概览

### 用户认证相关

| 接口 | 请求方法 | 接口路径 | 描述 | 认证要求 |
| --- | --- | --- | --- | --- |
| 用户注册 | POST | /api/v1/user/register | 创建新用户账号 | 无需认证 |
| 用户登录 | POST | /api/v1/user/login | 用户账号登录，获取认证Token | 无需认证 |
| 发送验证码 | POST | /api/v1/user/send-verification-code | 发送手机验证码 | 无需认证 |
| 验证码登录 | POST | /api/v1/user/login/verify-code | 手机号+验证码登录，获取认证Token | 无需认证 |
| 刷新令牌 | POST | /api/v1/user/refresh-token | 使用刷新令牌获取新的访问令牌 | 无需认证 |
| 用户登出 | POST | /api/v1/user/secured/logout | 退出登录，使令牌失效 | 需要认证 |

### 用户信息相关

| 接口 | 请求方法 | 接口路径 | 描述 | 认证要求 |
| --- | --- | --- | --- | --- |
| 获取用户信息 | GET | /api/v1/user/secured/info | 获取当前登录用户的详细信息 | 需要认证 |
| 更新用户信息 | PUT | /api/v1/user/secured/info | 更新当前登录用户的基本信息 | 需要认证 |
| 修改密码 | PUT | /api/v1/user/secured/password | 修改当前登录用户的密码 | 需要认证 |

### 地址管理相关

| 接口 | 请求方法 | 接口路径 | 描述 | 认证要求 |
| --- | --- | --- | --- | --- |
| 添加收货地址 | POST | /api/v1/user/secured/addresses | 为当前用户添加新的收货地址 | 需要认证 |
| 获取地址列表 | GET | /api/v1/user/secured/addresses | 获取当前用户的收货地址列表 | 需要认证 |
| 获取地址详情 | GET | /api/v1/user/secured/addresses/:id | 获取特定收货地址的详情 | 需要认证 |
| 更新收货地址 | PUT | /api/v1/user/secured/addresses | 更新特定收货地址的信息 | 需要认证 |
| 删除收货地址 | DELETE | /api/v1/user/secured/addresses/:id | 删除特定收货地址 | 需要认证 |
| 设置默认地址 | PUT | /api/v1/user/secured/addresses/:id/default | 设置某个地址为默认收货地址 | 需要认证 |
| 获取默认地址 | GET | /api/v1/user/secured/addresses/default | 获取当前用户的默认收货地址 | 需要认证 |

### 账户管理相关

| 接口 | 请求方法 | 接口路径 | 描述 | 认证要求 |
| --- | --- | --- | --- | --- |
| 获取账户信息 | GET | /api/v1/user/secured/account/info | 获取当前用户的账户详细信息 | 需要认证 |
| 获取账户变动记录 | GET | /api/v1/user/secured/account/transactions | 获取当前用户的账户变动记录列表 | 需要认证 |

### 分销管理相关

| 接口 | 请求方法 | 接口路径 | 描述 | 认证要求 |
| --- | --- | --- | --- | --- |
| 创建分销关系 | POST | /api/v1/user/secured/referral | 建立用户与推荐人的分销关系 | 需要认证 |
| 获取推荐用户列表 | GET | /api/v1/user/secured/referral/users | 获取当前用户推荐的用户列表 | 需要认证 |
| 获取分销统计 | GET | /api/v1/user/secured/referral/statistics | 获取当前用户的分销统计信息 | 需要认证 |
| 获取推荐人信息 | GET | /api/v1/user/secured/referral/referrer | 获取当前用户的推荐人信息 | 需要认证 |

### 分销配置相关

| 接口 | 请求方法 | 接口路径 | 描述 | 认证要求 |
| --- | --- | --- | --- | --- |
| 获取分销等级配置 | GET | /api/v1/user/secured/referral/config/levels | 获取分销等级配置信息 | 需要认证 |
| 更新分销等级配置 | PUT | /api/v1/user/secured/referral/config/levels | 更新分销等级配置 | 需要认证 |
| 获取佣金比例配置 | GET | /api/v1/user/secured/referral/config/commission-rates | 获取佣金比例配置信息 | 需要认证 |
| 更新佣金比例配置 | PUT | /api/v1/user/secured/referral/config/commission-rates | 更新佣金比例配置 | 需要认证 |

## 通用说明

### 请求格式

- 所有POST和PUT请求的Content-Type均为`application/json`
- 请求和响应编码均为UTF-8

### 认证方式

需要认证的接口通过Bearer Token方式进行认证：

```
Authorization: Bearer {access_token}
```

### 响应格式

所有API的响应均使用统一的JSON格式：

```json
{
  "code": 200,         // 状态码，200表示成功，其他表示错误
  "message": "操作成功", // 响应消息
  "data": {           // 响应数据，成功时包含返回数据，失败时可能为null
    // 具体数据结构
  },
  "pagination": {     // 分页信息，只在返回列表数据时存在
    "page": 1,        // 当前页码
    "page_size": 10,   // 每页数量
    "total": 100       // 总记录数
  }
}
```

### 状态码说明

| 状态码 | 说明 |
| --- | --- |
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 接口详细说明

### 用户注册

创建新用户账号。

- 请求路径：`/api/v1/user/register`
- 请求方法：`POST`
- 请求参数：

```json
{
  "username": "zhangsan",           // 用户名，必填，长度5-20，只能包含字母、数字和下划线
  "password": "Password123",       // 密码，必填，长度8-20，必须包含大小写字母和数字
  "confirm_password": "Password123", // 确认密码，必填，必须与password一致
  "mobile": "13800138000",         // 手机号，必填，合法的中国大陆手机号
  "email": "<EMAIL>",  // 邮箱，选填，合法的电子邮箱格式
  "nickname": "张三"                // 昵称，必填，长度2-20
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "注册成功",
  "data": 10001  // 用户ID
}
```

- 错误情况：
  - 用户名已存在
  - 手机号已被注册
  - 邮箱已被注册
  - 密码强度不够
  - 参数格式不正确

### 用户登录

用户账号登录，获取认证Token。

- 请求路径：`/api/v1/user/login`
- 请求方法：`POST`
- 请求参数：

```json
{
  "username": "zhangsan",     // 用户名，与mobile二选一
  "mobile": "13800138000",   // 手机号，与username二选一
  "password": "Password123"   // 密码，必填
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user_id": 10001,
    "username": "zhangsan",
    "nickname": "张三",
    "avatar": "https://example.com/avatar/default.png",
    "token": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",  // 访问令牌
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 刷新令牌
      "expires_in": 7200  // 访问令牌有效期（秒）
    }
  }
}
```

- 错误情况：
  - 用户名/手机号不存在
  - 密码错误
  - 账户被禁用

### 发送验证码

发送手机验证码用于登录验证。

- 请求路径：`/api/v1/user/send-verification-code`
- 请求方法：`POST`
- 请求参数：

```json
{
  "mobile": "13800138000",  // 手机号，必填，11位中国大陆手机号
  "purpose": "login"        // 用途，必填，固定值"login"
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {
    "sent": true,
    "expires_in": 300,  // 验证码有效期（秒）
    "retry_after": 60   // 重新发送间隔（秒）
  }
}
```

- 错误情况：
  - 手机号格式不正确
  - 发送频率过快（需等待重试间隔）
  - 短信服务异常

### 验证码登录

使用手机号和验证码进行登录，如果手机号未注册则自动创建新用户。

- 请求路径：`/api/v1/user/login/verify-code`
- 请求方法：`POST`
- 请求参数：

```json
{
  "mobile": "13800138000",    // 手机号，必填
  "verify_code": "123456"    // 验证码，必填，6位数字
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user_id": 10001,
    "username": "user_13800138000",  // 自动生成的用户名
    "nickname": "用户13800138000",   // 自动生成的昵称
    "mobile": "138****8000",        // 脱敏处理的手机号
    "avatar": "https://example.com/avatar/default.png",
    "is_new_user": true,            // 是否为新注册用户
    "token": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",  // 访问令牌
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 刷新令牌
      "expires_in": 7200  // 访问令牌有效期（秒）
    }
  }
}
```

- 错误情况：
  - 验证码错误或已过期
  - 手机号格式不正确
  - 验证码已使用

### 刷新令牌

使用刷新令牌获取新的访问令牌和刷新令牌对。

- 请求路径：`/api/v1/user/refresh-token`
- 请求方法：`POST`
- 请求参数：

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."  // 刷新令牌，必填
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",  // 新的访问令牌
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // 新的刷新令牌
    "expires_in": 7200  // 访问令牌有效期（秒）
  }
}
```

- 错误情况：
  - 刷新令牌无效
  - 刷新令牌已过期

### 用户登出

退出登录，使当前用户的令牌失效。

- 请求路径：`/api/v1/user/secured/logout`
- 请求方法：`POST`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无
- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "退出成功",
  "data": null
}
```

### 获取用户信息

获取当前登录用户的详细信息。

- 请求路径：`/api/v1/user/secured/info`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无
- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 10001,
    "username": "zhangsan",
    "nickname": "张三",
    "avatar": "https://example.com/avatar/default.png",
    "mobile": "138****8000",  // 手机号部分隐藏处理
    "email": "zh****@example.com",  // 邮箱部分隐藏处理
    "gender": 1,  // 0-未知，1-男，2-女
    "birthday": "1990-01-01",
    "balance": 9999.99,
    "points": 1000,
    "level": 1,  // 0-普通用户,1-VIP用户,2-SVIP用户
    "status": 1,  // 0-禁用,1-正常
    "last_login_at": "2023-01-01 12:00:00",
    "created_at": "2022-01-01 12:00:00"
  }
}
```

### 更新用户信息

更新当前登录用户的基本信息。

- 请求路径：`/api/v1/user/secured/info`
- 请求方法：`PUT`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "nickname": "张三丰",   // 昵称，选填
  "avatar": "https://example.com/avatar/user1.png",  // 头像URL，选填
  "gender": 1,          // 性别，选填，0-未知，1-男，2-女
  "birthday": "1990-01-01",  // 生日，选填，格式：YYYY-MM-DD
  "email": "<EMAIL>"  // 邮箱，选填
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 修改密码

修改当前登录用户的密码。

- 请求路径：`/api/v1/user/secured/password`
- 请求方法：`PUT`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "old_password": "OldPassword123",      // 原密码，必填
  "new_password": "NewPassword456",      // 新密码，必填，长度8-20，必须包含大小写字母和数字
  "confirm_password": "NewPassword456"   // 确认密码，必填，必须与new_password一致
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

- 错误情况：
  - 原密码错误
  - 新密码强度不够
  - 新密码与原密码相同

### 添加收货地址

为当前用户添加新的收货地址。

- 请求路径：`/api/v1/user/secured/addresses`
- 请求方法：`POST`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "receiver_name": "张三",           // 收货人姓名，必填
  "receiver_mobile": "13800138000",  // 收货人手机号，必填
  "province": "广东省",              // 省份，必填
  "city": "深圳市",                 // 城市，必填
  "district": "南山区",              // 区/县，必填
  "detailed_address": "科技园路1号",  // 详细地址，必填
  "postal_code": "518000",          // 邮政编码，选填
  "is_default": true,               // 是否设为默认地址，选填，默认false
  "address_tag": "公司",            // 地址标签，选填
  "location_longitude": 114.05786,   // 经度，选填
  "location_latitude": 22.54286     // 纬度，选填
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "添加成功",
  "data": 1001  // 地址ID
}
```

### 获取地址列表

获取当前用户的收货地址列表。

- 请求路径：`/api/v1/user/secured/addresses`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数（Query参数）：
  - `page`: 页码，选填，默认1
  - `page_size`: 每页数量，选填，默认10
- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": [
    {
      "id": 1001,
      "receiver_name": "张三",
      "receiver_mobile": "13800138000",
      "province": "广东省",
      "city": "深圳市",
      "district": "南山区",
      "detailed_address": "科技园路1号",
      "postal_code": "518000",
      "is_default": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

- 错误情况：
  - 用户没有默认地址

## 分销管理接口详细说明

### 创建分销关系

建立用户与推荐人的分销关系。

- 请求路径：`/api/v1/user/secured/referral`
- 请求方法：`POST`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "referrer_id": 10001,        // 推荐人用户ID，必填
  "referral_code": "ABC123"   // 推荐码，选填，与referrer_id二选一
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "分销关系创建成功",
  "data": {
    "id": 2001,
    "user_id": 10002,
    "referrer_id": 10001,
    "level": 1,
    "status": 1,
    "commission": 0.00,
    "referral_time": "2024-01-15T10:30:00Z"
  }
}
```

- 错误情况：
  - 推荐人不存在
  - 用户已有推荐人
  - 不能推荐自己
  - 推荐码无效

### 获取推荐用户列表

获取当前用户推荐的用户列表。

- 请求路径：`/api/v1/user/secured/referral/users`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数（查询参数）：
  - `page`: 页码，默认1
  - `page_size`: 每页数量，默认10，最大100
  - `level`: 分销等级筛选，可选值：1,2,3
  - `status`: 状态筛选，可选值：0,1

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "list": [
      {
        "id": 2001,
        "user_id": 10002,
        "level": 1,
        "status": 1,
        "commission": 156.80,
        "referral_time": "2024-01-15T10:30:00Z",
        "user": {
          "id": 10002,
          "username": "lisi",
          "nickname": "李四",
          "avatar": "https://example.com/avatar/lisi.png"
        }
      }
    ]
  },
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 25
  }
}
```

### 获取分销统计

获取当前用户的分销统计信息。

- 请求路径：`/api/v1/user/secured/referral/statistics`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "total_referrals": 25,           // 总推荐用户数
    "level_1_referrals": 15,         // 一级推荐用户数
    "level_2_referrals": 8,          // 二级推荐用户数
    "level_3_referrals": 2,          // 三级推荐用户数
    "total_commission": 1256.80,     // 累计佣金
    "today_commission": 45.60,       // 今日佣金
    "month_commission": 356.80,      // 本月佣金
    "year_commission": 1256.80       // 本年佣金
  }
}
```

### 获取推荐人信息

获取当前用户的推荐人信息。

- 请求路径：`/api/v1/user/secured/referral/referrer`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 2001,
    "referrer_id": 10001,
    "level": 1,
    "status": 1,
    "referral_time": "2024-01-15T10:30:00Z",
    "referrer": {
      "id": 10001,
      "username": "zhangsan",
      "nickname": "张三",
      "avatar": "https://example.com/avatar/zhangsan.png"
    }
  }
}
```

- 错误情况：
  - 用户没有推荐人

## 分销配置接口详细说明

### 获取分销等级配置

获取分销等级配置信息。

- 请求路径：`/api/v1/user/secured/referral/config/levels`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "max_levels": 3,
    "level_1_enabled": true,
    "level_2_enabled": true,
    "level_3_enabled": false
  }
}
```

### 更新分销等级配置

更新分销等级配置。

- 请求路径：`/api/v1/user/secured/referral/config/levels`
- 请求方法：`PUT`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "max_levels": 3,              // 最大分销等级，必填
  "level_1_enabled": true,      // 一级分销是否启用，必填
  "level_2_enabled": true,      // 二级分销是否启用，必填
  "level_3_enabled": false      // 三级分销是否启用，必填
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "配置更新成功",
  "data": null
}
```

### 获取佣金比例配置

获取佣金比例配置信息。

- 请求路径：`/api/v1/user/secured/referral/config/commission-rates`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "level_1": 0.05,    // 一级分销佣金比例 5%
    "level_2": 0.03,    // 二级分销佣金比例 3%
    "level_3": 0.01     // 三级分销佣金比例 1%
  }
}
```

### 更新佣金比例配置

更新佣金比例配置。

- 请求路径：`/api/v1/user/secured/referral/config/commission-rates`
- 请求方法：`PUT`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "level_1": 0.08,    // 一级分销佣金比例，必填，范围：0-1
  "level_2": 0.05,    // 二级分销佣金比例，必填，范围：0-1
  "level_3": 0.02     // 三级分销佣金比例，必填，范围：0-1
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "配置更新成功",
  "data": null
}
```

- 错误情况：
  - 佣金比例超出范围
  - 权限不足（需要管理员权限）
      "address_tag": "公司",
      "location_longitude": 114.05786,
      "location_latitude": 22.54286,
      "created_at": "2023-01-01 12:00:00"
    },
    // 更多地址...
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 2
  }
}
```

### 获取地址详情

获取特定收货地址的详情。

- 请求路径：`/api/v1/user/secured/addresses/:id`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 路径参数：
  - `id`: 地址ID
- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 1001,
    "receiver_name": "张三",
    "receiver_mobile": "13800138000",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "detailed_address": "科技园路1号",
    "postal_code": "518000",
    "is_default": true,
    "address_tag": "公司",
    "location_longitude": 114.05786,
    "location_latitude": 22.54286,
    "created_at": "2023-01-01 12:00:00",
    "updated_at": "2023-01-01 12:00:00"
  }
}
```

- 错误情况：
  - 地址不存在
  - 地址不属于当前用户

### 更新收货地址

更新特定收货地址的信息。

- 请求路径：`/api/v1/user/secured/addresses`
- 请求方法：`PUT`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "id": 1001,                       // 地址ID，必填
  "receiver_name": "李四",           // 收货人姓名，选填
  "receiver_mobile": "13900139000",  // 收货人手机号，选填
  "province": "广东省",              // 省份，选填
  "city": "广州市",                 // 城市，选填
  "district": "天河区",              // 区/县，选填
  "detailed_address": "体育西路1号",   // 详细地址，选填
  "postal_code": "510000",          // 邮政编码，选填
  "is_default": false,              // 是否设为默认地址，选填
  "address_tag": "家",              // 地址标签，选填
  "location_longitude": 113.32754,   // 经度，选填
  "location_latitude": 23.13559     // 纬度，选填
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

- 错误情况：
  - 地址不存在
  - 地址不属于当前用户

### 删除收货地址

删除特定收货地址。

- 请求路径：`/api/v1/user/secured/addresses/:id`
- 请求方法：`DELETE`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 路径参数：
  - `id`: 地址ID
- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

- 错误情况：
  - 地址不存在
  - 地址不属于当前用户

### 设置默认地址

设置某个地址为默认收货地址。

- 请求路径：`/api/v1/user/secured/addresses/:id/default`
- 请求方法：`PUT`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "address_id": 1001  // 要设为默认的地址ID，必填
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "设置成功",
  "data": null
}
```

- 错误情况：
  - 地址不存在
  - 地址不属于当前用户

### 获取默认地址

获取当前用户的默认收货地址。

- 请求路径：`/api/v1/user/secured/addresses/default`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无
- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 1001,
    "receiver_name": "张三",
    "receiver_mobile": "13800138000",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "detailed_address": "科技园路1号",
    "postal_code": "518000",
    "is_default": true,
    "address_tag": "公司",
    "location_longitude": 114.05786,
    "location_latitude": 22.54286,
    "created_at": "2023-01-01 12:00:00",
    "updated_at": "2023-01-01 12:00:00"
  }
}
```

- 如果用户没有设置默认地址，则返回：

```json
{
  "code": 200,
  "message": "成功",
  "data": null
}
```

### 获取账户信息

获取当前用户的账户详细信息，包括余额、冻结金额、充值消费统计等。

- 请求路径：`/api/v1/user/secured/account/info`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：无
- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "id": 1,
    "user_id": 123,
    "balance": "1000.50",
    "frozen_balance": "100.00",
    "total_recharge": "5000.00",
    "total_consume": "3899.50",
    "status": 1,
    "last_recharge": "2024-01-15 10:30:00",
    "last_consume": "2024-01-16 14:20:00",
    "updated_at": "2024-01-16 14:20:00",
    "created_at": "2024-01-01 09:00:00"
  }
}
```

- 字段说明：
  - `id`: 账户记录ID
  - `user_id`: 用户ID
  - `balance`: 可用余额
  - `frozen_balance`: 冻结金额
  - `total_recharge`: 累计充值金额
  - `total_consume`: 累计消费金额
  - `status`: 账户状态（1：正常，0：冻结）
  - `last_recharge`: 最后充值时间
  - `last_consume`: 最后消费时间
  - `updated_at`: 更新时间
  - `created_at`: 创建时间

### 获取账户变动记录

获取用户的账户变动记录列表，支持分页查询和类型筛选。

- 请求路径：`/api/v1/user/secured/account/transactions`
- 请求方法：`GET`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| page_size | int | 否 | 20 | 每页数量 |
| transaction_type | int | 否 | 0 | 交易类型筛选（0：全部，1：充值，2：消费，3：退款，4：提现，5：转账） |

- 请求示例：

```
GET /api/v1/user/secured/account/transactions?page=1&page_size=10&transaction_type=1
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "list": [
      {
        "id": 1,
        "transaction_no": "TXN202401160001",
        "related_id": 1001,
        "related_type": "order",
        "amount": "100.00",
        "before_balance": "900.50",
        "after_balance": "1000.50",
        "type": 1,
        "operation": 1,
        "status": 1,
        "description": "订单充值",
        "remark": "充值成功",
        "client_ip": "*************",
        "created_at": "2024-01-16 10:30:00"
      }
    ],
    "total": 50,
    "page": 1,
    "size": 10
  }
}
```

- 字段说明：
  - `id`: 交易记录ID
  - `transaction_no`: 交易流水号
  - `related_id`: 关联业务ID
  - `related_type`: 关联业务类型
  - `amount`: 交易金额
  - `before_balance`: 交易前余额
  - `after_balance`: 交易后余额
  - `type`: 交易类型（1：充值，2：消费，3：退款，4：提现，5：转账）
  - `operation`: 操作类型（1：增加，2：减少）
  - `status`: 交易状态（1：成功，2：失败，3：处理中）
  - `description`: 交易描述
  - `remark`: 备注信息
  - `client_ip`: 客户端IP
  - `created_at`: 创建时间
  - `total`: 总记录数
  - `page`: 当前页码
  - `size`: 每页数量
