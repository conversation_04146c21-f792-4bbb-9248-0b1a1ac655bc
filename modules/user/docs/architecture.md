# 用户模块架构设计

## 总体架构

用户模块采用经典的三层架构设计（控制层-服务层-数据访问层），符合Beego框架的MVC设计思想，每一层都有明确的职责边界：

```
+----------------+     +----------------+     +----------------+     +----------------+
|                |     |                |     |                |     |                |
|  Controllers   | --> |    Services    | --> | Repositories   | --> |    Database    |
|                |     |                |     |                |     |                |
+----------------+     +----------------+     +----------------+     +----------------+
     请求处理            业务逻辑            数据访问            存储层
       控制层            服务层             仓库层
```

## 模块组件

用户模块包含以下重要组件：

### 1. 控制器（Controllers）

控制器负责接收和处理HTTP请求，包括：
- **UserController**: 处理用户账户相关的请求，如注册、登录、信息管理等
- **AddressController**: 处理用户收货地址管理的请求
- **ReferralController**: 处理分销关系管理的请求，如创建分销关系、查询推荐用户等
- **ReferralConfigController**: 处理分销配置管理的请求，如分销等级配置、佣金比例配置等

控制器负责：
- 请求参数解析与验证
- 调用相应的服务层方法
- 将处理结果返回给客户端

### 2. 服务层（Services）

服务层实现业务逻辑，包括：
- **UserService**: 实现用户账户管理相关的业务逻辑
- **AddressService**: 实现地址管理相关的业务逻辑
- **UserLogService**: 实现用户操作日志相关的业务逻辑
- **UserReferralService**: 实现分销关系管理的业务逻辑，包括创建分销关系、统计分析、佣金计算等
- **ReferralConfigService**: 实现分销配置管理的业务逻辑，包括等级配置、佣金比例配置等

服务层使用接口定义，实现与调用方的解耦，所有可能的业务错误都在这一层处理。

### 3. 数据访问层（Repositories）

仓库层负责数据访问与持久化，包括：
- **UserRepository**: 实现用户数据的CRUD操作
- **AddressRepository**: 实现地址数据的CRUD操作
- **UserLogRepository**: 实现用户日志数据的CRUD操作
- **UserReferralRepository**: 实现分销关系数据的CRUD操作，包括推荐关系查询、统计等
- **ReferralConfigRepository**: 实现分销配置数据的CRUD操作

仓库层基于Beego ORM实现，封装了对数据库表的操作。

### 4. 数据模型（Models）

数据模型定义了业务实体和数据库表映射：
- **User**: 用户模型，存储用户基本信息、认证信息、推荐码等
- **Address**: 收货地址模型，存储用户的收货地址信息
- **UserLog**: 用户日志模型，记录用户敏感操作日志
- **UserReferral**: 分销关系模型，存储用户之间的推荐关系、佣金信息等
- **ReferralConfig**: 分销配置模型，存储分销系统的各种配置参数

### 5. 传输对象（DTOs）

传输对象用于对外的数据交互：
- **请求DTO**: 定义客户端发送到服务器的数据结构
- **响应DTO**: 定义服务器返回给客户端的数据结构

### 6. 路由（Routers）

路由模块定义了API端点与控制器方法的映射关系，配置了URL路径、HTTP方法以及中间件等。

## 交互流程

以用户登录为例，导出完整的调用链路：

```
1. 客户端 → POST /api/v1/user/login (发送用户名和密码)
2. Router → 路由到 UserController.Login 方法
3. UserController → 解析请求参数并验证
4. UserController → 调用 UserService.Login 方法
5. UserService → 处理登录业务逻辑（验证密码、生成Token等）
6. UserService → 调用 UserRepository 获取用户数据
7. UserRepository → 访问数据库查询用户记录
8. UserService → 调用 UserLogService 记录登录日志
9. UserController → 将登录结果返回给客户端
```

## 模块依赖

用户模块主要依赖如下：

1. **框架相关**
   - Beego Web框架: 提供路由、控制器等基础设施
   - Beego ORM: 数据库交互
   - Beego Validation: 输入验证

2. **应用程序库**
   - JWT 库: 处理JSON Web Token的生成与验证
   - 密码加密库: 安全存储用户密码

3. **其他应用模块**
   - 中间件模块: 提供认证与日志中间件
   - 共通模块: 提供通用工具和常量

## 扩展与扩展性

用户模块的设计考虑了以下扩展性因素：

1. **接口定义与实现分离**：服务层通过接口定义，使得实现可替换
2. **单一职责原则**：组件功能明确，职责单一，便于维护和扩展
3. **DTO层分离**：使外部接口与内部模型解耦，可独立变化
4. **可测试性**：清晰的层次结构和依赖注入模式便于单元测试

未来可扩展的功能包括：

- 第三方登录集成（如微信、微博等）
- 基于角色的权限管理
- 多因素认证
- 更多级别的分销体系（当前支持3级）
- 分销佣金的多种计算模式
- 分销数据的实时统计和分析
- 分销活动和奖励机制
