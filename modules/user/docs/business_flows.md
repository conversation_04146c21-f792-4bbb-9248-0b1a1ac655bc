# 用户模块业务流程说明

本文档详细描述了用户模块中的主要业务流程，包括流程图、步骤说明和业务规则，帮助开发人员和维护人员理解系统的工作方式。

## 用户注册流程

### 流程图

```
+-------------+     +----------------+     +-------------------+
|  客户端提交  |     | 服务端参数验证  |     |  检查用户是否存在  |
|  注册信息   +---->+  并格式化数据   +---->+                   |
+-------------+     +----------------+     +--------+----------+
                                                    |
                                                    | 不存在
                                                    v
+----------------+     +----------------+     +--------------+
|                |     |                |     |              |
|  返回用户信息   +<----+  生成登录Token  |<----+  创建用户记录  |
|                |     |                |     |              |
+----------------+     +----------------+     +--------------+
```

### 流程说明

1. **客户端提交注册信息**
   - 用户在客户端填写注册表单，包括用户名、密码、手机号等信息
   - 客户端发送POST请求到 `/api/v1/user/register` 接口

2. **服务端参数验证**
   - 服务端接收注册请求，进行参数解析
   - 验证必填字段是否完整
   - 验证用户名、密码、手机号等格式是否正确
   - 验证两次密码输入是否一致

3. **检查用户是否存在**
   - 根据用户名、手机号和邮箱（如果提供）检查是否已经存在注册用户
   - 如果已存在，返回相应的错误信息

4. **创建用户记录**
   - 对密码进行加密处理
   - 创建新的用户记录并保存到数据库
   - 记录注册IP和注册时间
   - 创建用户注册操作日志

5. **返回用户信息**
   - 返回用户ID和基本信息给客户端
   - 注册成功提示

### 业务规则

- 用户名长度为5-20个字符，只能包含字母、数字和下划线
- 密码长度为8-20个字符，必须包含大小写字母和数字
- 手机号必须是有效的中国大陆手机号
- 邮箱必须符合标准邮箱格式
- 用户名、手机号和邮箱必须唯一
- 新用户默认状态为正常（status=1）
- 新用户默认等级为普通用户（level=0）

## 用户登录流程

### 流程图

```
+-------------+     +----------------+     +-------------------+
|  客户端提交  |     | 服务端参数验证  |     |  查询用户信息     |
|  登录信息   +---->+                +---->+                   |
+-------------+     +----------------+     +--------+----------+
                                                    |
                                                    v
+----------------+     +----------------+     +--------------+
|                |     |                |     |              |
|  返回用户信息   +<----+  生成登录Token  |<----+  验证密码     |
|  和Token信息   |     |  并记录登录日志  |     |              |
+----------------+     +----------------+     +--------------+
```

### 流程说明

1. **客户端提交登录信息**
   - 用户在客户端填写登录表单，提供用户名/手机号和密码
   - 客户端发送POST请求到 `/api/v1/user/login` 接口

2. **服务端参数验证**
   - 服务端接收登录请求，进行参数解析
   - 验证必填字段是否完整

3. **查询用户信息**
   - 根据用户名或手机号查询用户是否存在
   - 如果用户不存在，返回错误信息
   - 检查用户状态是否正常，如果被禁用则拒绝登录

4. **验证密码**
   - 将提交的密码与数据库中存储的加密密码进行比对
   - 如果密码不匹配，返回错误信息

5. **生成登录Token并记录日志**
   - 生成JWT访问令牌（access_token）和刷新令牌（refresh_token）
   - 更新用户最后登录时间和IP
   - 记录登录操作日志

6. **返回用户信息和Token信息**
   - 返回用户基本信息、访问令牌和刷新令牌给客户端
   - 返回令牌有效期信息

### 业务规则

- 用户可以使用用户名或手机号进行登录
- 密码验证失败累计达到一定次数（如5次）后，账户会被临时锁定一段时间
- 登录成功后，访问令牌有效期通常为2小时
- 刷新令牌有效期通常为30天
- 用户状态为禁用（status=0）时无法登录
- 每次登录都会更新最后登录时间和IP

## 用户信息更新流程

### 流程图

```
+-------------+     +----------------+     +-------------------+
|  客户端提交  |     | 认证用户身份   |     |   参数验证        |
|  更新请求   +---->+                +---->+                   |
+-------------+     +----------------+     +--------+----------+
                                                    |
                                                    v
+----------------+     +-----------------------+
|                |     |                       |
|  返回更新结果   +<----+  更新数据并记录操作日志  |
|                |     |                       |
+----------------+     +-----------------------+
```

### 流程说明

1. **客户端提交更新请求**
   - 用户在客户端填写要更新的个人信息
   - 客户端发送PUT请求到 `/api/v1/user/secured/info` 接口，请求头包含认证Token

2. **认证用户身份**
   - 服务端验证认证Token的有效性
   - 从Token中提取用户ID

3. **参数验证**
   - 验证提交参数的格式是否正确
   - 验证邮箱等唯一字段是否与其他用户冲突

4. **更新数据并记录操作日志**
   - 更新用户记录
   - 记录信息更新操作日志

5. **返回更新结果**
   - 返回操作成功或失败信息给客户端

### 业务规则

- 用户只能更新自己的个人信息
- 昵称、性别、生日、头像等信息可以自由更新
- 更新邮箱时需要确保新邮箱未被其他用户使用
- 敏感信息如手机号的更新可能需要额外验证（如短信验证码）
- 每次信息更新都会记录到用户操作日志中

## 密码修改流程

### 流程图

```
+-------------+     +----------------+     +-------------------+
|  客户端提交  |     | 认证用户身份   |     |   参数验证        |
|  密码修改   +---->+                +---->+                   |
|  请求       |     |                |     |                   |
+-------------+     +----------------+     +--------+----------+
                                                    |
                                                    v
+----------------+     +----------------+     +--------------+
|                |     |                |     |              |
|  返回操作结果   +<----+  更新密码      |<----+  验证原密码   |
|                |     |  记录操作日志  |     |              |
+----------------+     +----------------+     +--------------+
```

### 流程说明

1. **客户端提交密码修改请求**
   - 用户在客户端填写原密码和新密码
   - 客户端发送PUT请求到 `/api/v1/user/secured/password` 接口，请求头包含认证Token

2. **认证用户身份**
   - 服务端验证认证Token的有效性
   - 从Token中提取用户ID

3. **参数验证**
   - 验证原密码、新密码和确认密码字段是否完整
   - 验证新密码格式是否符合要求
   - 验证新密码与确认密码是否一致

4. **验证原密码**
   - 查询用户当前密码
   - 验证提交的原密码是否正确
   - 如果原密码不正确，返回错误信息

5. **更新密码并记录操作日志**
   - 对新密码进行加密处理
   - 更新用户密码
   - 记录密码修改操作日志

6. **返回操作结果**
   - 返回密码修改成功或失败信息给客户端

### 业务规则

- 用户必须提供正确的原密码才能修改密码
- 新密码不能与原密码相同
- 新密码必须符合密码强度要求（长度8-20个字符，包含大小写字母和数字）
- 密码修改成功后，可能需要用户重新登录
- 密码修改操作会被记录到用户操作日志中

## 收货地址管理流程

### 地址添加流程

1. **客户端提交地址信息**
   - 用户填写收货人信息、联系方式、详细地址等
   - 客户端发送POST请求到 `/api/v1/user/secured/addresses` 接口

2. **认证和参数验证**
   - 验证用户认证Token
   - 验证地址信息必填字段是否完整
   - 验证手机号等格式是否正确

3. **保存地址信息**
   - 创建新的地址记录并关联到当前用户
   - 如果设置为默认地址，需要将用户其他地址设为非默认

4. **返回结果**
   - 返回新创建的地址ID

### 默认地址设置流程

1. **客户端发送默认地址设置请求**
   - 发送PUT请求到 `/api/v1/user/secured/addresses/:id/default` 接口

2. **验证地址归属**
   - 验证目标地址是否属于当前用户

3. **更新默认状态**
   - 将指定地址设为默认地址（is_default=true）
   - 将用户其他所有地址设为非默认地址（is_default=false）

4. **返回操作结果**

### 业务规则

- 每个用户最多可添加20个收货地址
- 用户必须至少提供收货人姓名、手机号和详细地址
- 每个用户只能有一个默认收货地址
- 当设置新的默认地址时，原默认地址自动变为非默认
- 用户只能管理自己的收货地址
