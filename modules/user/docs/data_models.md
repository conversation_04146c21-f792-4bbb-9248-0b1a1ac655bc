# 用户模块数据模型说明

本文档详细描述了用户模块中的数据库表结构和字段定义，帮助开发人员和维护人员理解数据模型的设计与关系。

## 数据表概述

用户模块包含以下主要数据表：

1. **users** - 存储用户基本信息和认证信息
2. **user_addresses** - 存储用户收货地址信息
3. **user_log** - 存储用户操作日志

## 用户表 (users)

用户表存储系统中所有用户的基本信息、认证信息和账户状态等。

### 表结构

| 字段名         | 类型         | 说明                           | 约束              |
|--------------|------------|-------------------------------|-------------------|
| id           | int64      | 用户ID                         | 主键，自动递增       |
| username     | varchar(50)| 用户名                         | 唯一，不为空         |
| password     | varchar(255)| 加密后的密码                    | 不为空              |
| nickname     | varchar(50)| 用户昵称                       | 不为空              |
| avatar       | varchar(255)| 用户头像URL                     | 可为空              |
| mobile       | varchar(20)| 手机号                         | 唯一，不为空         |
| email        | varchar(100)| 邮箱                          | 唯一，可为空         |
| gender       | int        | 性别：0-未知，1-男，2-女           | 默认0              |
| birthday     | date       | 生日                          | 可为空              |
| balance      | decimal    | 账户余额                       | 默认0，精度(10,2)    |
| points       | int64      | 账户积分                       | 默认0              |
| level        | int        | 用户等级：0-普通，1-VIP，2-SVIP    | 默认0              |
| status       | int        | 用户状态：0-禁用，1-正常           | 默认1              |
| last_login_at| datetime   | 最后登录时间                    | 可为空              |
| last_login_ip| varchar(50)| 最后登录IP                     | 可为空              |
| register_ip  | varchar(50)| 注册IP                        | 不为空              |
| created_at   | datetime   | 创建时间                       | 自动设置当前时间      |
| updated_at   | datetime   | 更新时间                       | 自动更新为当前时间    |

### 索引

- PRIMARY KEY (`id`)
- UNIQUE KEY `idx_username` (`username`)
- UNIQUE KEY `idx_mobile` (`mobile`)
- UNIQUE KEY `idx_email` (`email`)

## 地址表 (user_addresses)

地址表存储用户的收货地址信息，与用户表是一对多关系。

### 表结构

| 字段名           | 类型          | 说明                       | 约束               |
|----------------|-------------|---------------------------|--------------------|
| id             | int64       | 地址ID                     | 主键，自动递增        |
| user_id        | int64       | 关联的用户ID                 | 外键，不为空          |
| receiver_name  | varchar(50) | 收货人姓名                   | 不为空               |
| receiver_mobile| varchar(20) | 收货人手机号                  | 不为空               |
| province       | varchar(50) | 省份                       | 不为空               |
| city           | varchar(50) | 城市                       | 不为空               |
| district       | varchar(50) | 区/县                      | 不为空               |
| detailed_address| varchar(255)| 详细地址                    | 不为空               |
| postal_code    | varchar(20) | 邮政编码                    | 可为空               |
| is_default     | boolean     | 是否默认地址                  | 默认false           |
| address_tag    | varchar(50) | 地址标签(如"家"，"公司")     | 可为空               |
| location_longitude| decimal    | 经度                       | 可为空，精度(12,8)     |
| location_latitude | decimal    | 纬度                       | 可为空，精度(12,8)     |
| created_at     | datetime    | 创建时间                    | 自动设置当前时间       |
| updated_at     | datetime    | 更新时间                    | 自动更新为当前时间     |

### 索引

- PRIMARY KEY (`id`)
- KEY `idx_user_id` (`user_id`)
- KEY `idx_is_default` (`user_id`, `is_default`)

## 用户日志表 (user_log)

用户日志表记录用户的关键操作，用于安全审计和问题排查。

### 表结构

| 字段名           | 类型          | 说明                      | 约束               |
|----------------|-------------|--------------------------|--------------------|
| id             | int64       | 日志ID                    | 主键，自动递增        |
| user_id        | int64       | 用户ID                    | 不为空               |
| username       | varchar(100)| 用户名                    | 不为空               |
| operation_type | int         | 操作类型                   | 不为空               |
| content        | text        | 操作内容                   | 不为空               |
| request_url    | varchar(255)| 请求URL                   | 不为空               |
| request_data   | text        | 请求数据(JSON格式)          | 可为空               |
| ip             | varchar(50) | 操作IP                    | 不为空               |
| user_agent     | varchar(255)| 用户代理                   | 可为空               |
| status         | int         | 操作状态：0-失败,1-成功       | 默认1                |
| remark         | varchar(255)| 备注信息                   | 可为空               |
| created_at     | datetime    | 创建时间                   | 自动设置当前时间       |

### 索引

- PRIMARY KEY (`id`)
- KEY `idx_user_id` (`user_id`)
- KEY `idx_operation_type` (`operation_type`)
- KEY `idx_created_at` (`created_at`)

## 操作类型常量

用户日志的操作类型定义如下：

```
UserLogTypeLogin          = 1  // 登录
UserLogTypeLogout         = 2  // 登出
UserLogTypeRegister       = 3  // 注册
UserLogTypeUpdateInfo     = 4  // 更新信息
UserLogTypeChangePassword = 5  // 修改密码
UserLogTypeResetPassword  = 6  // 重置密码
UserLogTypeSetDefault     = 7  // 设置默认项
UserLogTypeConsume        = 8  // 消费
```

## 数据关系

### 实体关系图

```
+-------------+       +------------------+
|             |       |                  |
|    User     |------>| UserAddress      |
|             |   1:n |                  |
+-------------+       +------------------+
      |  1
      |
      |  n
      v
+-------------+
|             |
|   UserLog   |
|             |
+-------------+
```

### 关系说明

1. **用户-地址关系**: 一个用户可以有多个收货地址，一个地址只属于一个用户。
   - 外键: `user_addresses.user_id` 引用 `users.id`
   - 关系类型: 一对多

2. **用户-日志关系**: 一个用户可以有多条操作日志，一条日志只属于一个用户。
   - 外键: `user_log.user_id` 引用 `users.id`
   - 关系类型: 一对多
