# 用户模块小程序示例代码

本目录包含了基于微信小程序平台实现的用户模块前端示例代码，展示了如何与O_Mall后端用户模块API进行交互。

## 目录结构

```
/demo
  ├── README.md             # 说明文档
  ├── app.js                # 小程序入口文件
  ├── app.json              # 小程序全局配置
  ├── app.wxss              # 小程序全局样式
  ├── project.config.json   # 项目配置文件
  ├── utils/
  │   ├── api.js            # API接口封装
  │   ├── request.js        # 网络请求工具
  │   └── auth.js           # 认证工具
  └── pages/
      ├── index/            # 首页
      ├── login/            # 登录页面
      ├── register/         # 注册页面
      ├── profile/          # 个人中心
      └── address/          # 地址管理
```

## 功能清单

1. **用户认证**
   - 用户注册
   - 用户登录
   - 用户注销
   - Token管理与自动刷新

2. **个人中心**
   - 用户信息展示与编辑
   - 密码修改
   - 账户安全设置

3. **地址管理**
   - 地址列表展示
   - 新增地址
   - 编辑地址
   - 设置默认地址
   - 删除地址

## 使用说明

1. 将本示例代码导入到微信开发者工具中
2. 配置API地址：修改`utils/request.js`中的BASE_URL为实际API地址
3. 编译运行项目

## 注意事项

- 本代码仅作为示例，实际项目中需要根据具体需求进行调整
- 示例代码中包含了基本的错误处理和用户体验优化
- 实际开发中需加入更完善的数据校验和错误处理机制
