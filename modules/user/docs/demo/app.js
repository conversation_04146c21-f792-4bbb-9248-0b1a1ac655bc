/**
 * 小程序入口文件
 * 
 * 负责小程序的初始化、全局状态管理和登录状态检查
 */

const auth = require('./utils/auth.js')
const api = require('./utils/api.js')

App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    token: null
  },
  
  onLaunch: function() {
    // 启动时检查登录状态
    this.checkLoginStatus()
    
    // 检查更新
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate(function(res) {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(function() {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: function(res) {
                if (res.confirm) {
                  updateManager.applyUpdate()
                }
              }
            })
          })
        }
      })
    }
  },
  
  /**
   * 检查登录状态
   * 如果本地有有效token则自动登录
   */
  checkLoginStatus: function() {
    const that = this
    // 检查是否有保存的token
    const token = auth.getToken()
    
    if (token) {
      // 验证token是否有效
      api.getUserInfo().then(res => {
        if (res.code === 200) {
          // 登录状态有效
          that.globalData.userInfo = res.data
          that.globalData.isLoggedIn = true
          that.globalData.token = token
        } else {
          // token已失效，尝试用刷新令牌获取新token
          auth.refreshToken().then(refreshRes => {
            if (refreshRes) {
              that.globalData.isLoggedIn = true
              // 重新获取用户信息
              that.fetchUserInfo()
            } else {
              // 刷新失败，需要重新登录
              auth.clearAuth()
            }
          })
        }
      }).catch(() => {
        // 请求失败，可能是网络问题
        console.log('获取用户信息失败，请检查网络连接')
      })
    }
  },
  
  /**
   * 获取用户信息
   */
  fetchUserInfo: function() {
    const that = this
    return new Promise((resolve, reject) => {
      api.getUserInfo().then(res => {
        if (res.code === 200) {
          that.globalData.userInfo = res.data
          resolve(res.data)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  },
  
  /**
   * 登录处理
   */
  doLogin: function(username, password) {
    const that = this
    return new Promise((resolve, reject) => {
      api.login(username, password).then(res => {
        if (res.code === 200) {
          // 保存认证信息
          auth.setToken(res.data.token.access_token, res.data.token.refresh_token)
          that.globalData.isLoggedIn = true
          that.globalData.userInfo = res.data
          that.globalData.token = res.data.token.access_token
          resolve(res.data)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  },
  
  /**
   * 注销处理
   */
  doLogout: function() {
    const that = this
    return new Promise((resolve, reject) => {
      api.logout().then(res => {
        // 无论后端结果如何，前端都清除登录状态
        auth.clearAuth()
        that.globalData.isLoggedIn = false
        that.globalData.userInfo = null
        that.globalData.token = null
        resolve(res)
      }).catch(err => {
        // 即使请求失败也清除本地登录状态
        auth.clearAuth()
        that.globalData.isLoggedIn = false
        that.globalData.userInfo = null
        that.globalData.token = null
        reject(err)
      })
    })
  }
})
