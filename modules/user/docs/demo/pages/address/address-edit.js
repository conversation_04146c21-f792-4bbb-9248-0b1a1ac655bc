/**
 * 地址编辑页面逻辑
 */

const api = require('../../utils/api')

Page({
  data: {
    id: null,            // 地址ID，新增时为空
    isEdit: false,      // 是否为编辑模式
    isLoading: false,   // 加载中
    isSaving: false,    // 保存中
    
    // 表单数据
    receiverName: '',   // 收件人姓名
    receiverMobile: '', // 收件人手机号
    province: '',       // 省份
    city: '',           // 城市
    district: '',       // 区/县
    address: '',        // 详细地址
    isDefault: false,   // 是否默认地址
    
    // 区域选择器数据
    regionArray: [[], [], []],  // 省市区列表
    regionIndex: [0, 0, 0],     // 当前选择的省市区下标
    regionVisible: false,       // 区域选择器是否显示
    provinceList: [],           // 省份列表
    cityList: [],               // 城市列表
    districtList: [],           // 区县列表
    
    // 错误提示
    errorMsg: ''
  },
  
  onLoad: function(options) {
    // 获取地址ID，判断是新增还是编辑
    const id = options.id
    const isEdit = !!id
    
    this.setData({
      id: id,
      isEdit: isEdit
    })
    
    // 加载区域数据
    this.loadRegionData()
    
    // 如果是编辑模式，加载地址详情
    if (isEdit) {
      this.loadAddressDetail(id)
    }
  },
  
  /**
   * u52a0u8f7du533au57dfu6570u636euff08u7701u5e02u533auff09
   */
  loadRegionData: function() {
    // u5728u5b9eu9645u5e94u7528u4e2duff0cu8fd9u91ccu9700u8981u52a0u8f7du771fu5b9eu7684u533au57dfu6570u636e
    // u8fd9u91ccu7b80u5316u5904u7406uff0cu4f7fu7528u6a21u62dfu6570u636e
    const provinces = [
      { code: '110000', name: 'u5317u4eacu5e02' },
      { code: '120000', name: 'u5929u6d25u5e02' },
      { code: '130000', name: 'u6cb3u5317u7701' },
      { code: '440000', name: 'u5e7fu4e1cu7701' },
      { code: '510000', name: 'u56dbu5dddu7701' }
    ]
    
    const cities = {
      '110000': [{ code: '110100', name: 'u5317u4eacu5e02' }],
      '120000': [{ code: '120100', name: 'u5929u6d25u5e02' }],
      '130000': [
        { code: '130100', name: 'u77f3u5bb6u5e84u5e02' },
        { code: '130200', name: 'u5510u5c71u5e02' },
        { code: '130300', name: 'u79e6u7687u5c9bu5e02' }
      ],
      '440000': [
        { code: '440100', name: 'u5e7fu5ddeu5e02' },
        { code: '440300', name: 'u6df1u5733u5e02' },
        { code: '440400', name: 'u73e0u6d77u5e02' }
      ],
      '510000': [
        { code: '510100', name: 'u6210u90fdu5e02' },
        { code: '510300', name: 'u81eau8d21u5e02' },
        { code: '510600', name: 'u5fb7u9633u5e02' }
      ]
    }
    
    const districts = {
      '110100': [
        { code: '110101', name: 'u4e1cu57ceu533a' },
        { code: '110102', name: 'u897fu57ceu533a' },
        { code: '110105', name: 'u671du9633u533a' }
      ],
      '120100': [
        { code: '120101', name: 'u548cu5e73u533a' },
        { code: '120102', name: 'u6cb3u4e1cu533a' },
        { code: '120103', name: 'u6cb3u897fu533a' }
      ],
      '130100': [
        { code: '130102', name: 'u957fu5b89u533a' },
        { code: '130104', name: 'u6865u897fu533a' },
        { code: '130105', name: 'u65b0u534eu533a' }
      ],
      '440100': [
        { code: '440103', name: 'u8354u6e7eu533a' },
        { code: '440104', name: 'u8d8au79c0u533a' },
        { code: '440105', name: 'u6d77u73e0u533a' }
      ],
      '510100': [
        { code: '510104', name: 'u9526u6c5fu533a' },
        { code: '510105', name: 'u9752u7f8au533a' },
        { code: '510106', name: 'u91d1u725bu533a' }
      ]
    }
    
    this.setData({
      provinceList: provinces,
      regionArray: [
        provinces.map(item => item.name),
        cities[provinces[0].code].map(item => item.name),
        districts[cities[provinces[0].code][0].code].map(item => item.name)
      ]
    })
    
    // u4fddu5b58u57ceu5e02u548cu533au53bfu6570u636eu4f9bu540eu7eedu4f7fu7528
    this.cities = cities
    this.districts = districts
  },
  
  /**
   * u52a0u8f7du5730u5740u8be6u60c5
   */
  loadAddressDetail: function(id) {
    this.setData({ isLoading: true })
    
    api.getAddressDetail(id).then(res => {
      if (res.code === 200) {
        const data = res.data
        
        // u66f4u65b0u8868u5355u6570u636e
        this.setData({
          receiverName: data.receiver_name,
          receiverMobile: data.receiver_mobile,
          province: data.province,
          city: data.city,
          district: data.district,
          address: data.address,
          isDefault: data.is_default === 1,
          isLoading: false
        })
        
        // u66f4u65b0u533au57dfu9009u62e9u5668u7684u503c
        this.updateRegionIndexByNames(data.province, data.city, data.district)
      } else {
        wx.showToast({
          title: res.message || 'u52a0u8f7du5730u5740u5931u8d25',
          icon: 'none'
        })
        this.setData({ isLoading: false })
      }
    }).catch(err => {
      console.error('u52a0u8f7du5730u5740u8be6u60c5u5931u8d25', err)
      wx.showToast({
        title: 'u52a0u8f7du5730u5740u5931u8d25',
        icon: 'none'
      })
      this.setData({ isLoading: false })
    })
  },
  
  /**
   * u6839u636eu7701u5e02u533au540du79f0u66f4u65b0u9009u62e9u5668u7d22u5f15
   */
  updateRegionIndexByNames: function(province, city, district) {
    // u5728u771fu5b9eu5e94u7528u4e2duff0cu8fd9u91ccu9700u8981u67e5u627eu5bf9u5e94u7684u7d22u5f15
    // u7b80u5316u5904u7406uff0cu8fd9u91ccu4f7fu7528u7b2cu4e00u4e2au9ed8u8ba4u7d22u5f15
    this.setData({
      regionIndex: [0, 0, 0]
    })
  },
  
  /**
   * u7701u5e02u533au9009u62e9u5668u53d8u5316u5904u7406
   */
  bindRegionChange: function(e) {
    const value = e.detail.value
    const column = e.detail.column
    const provinceIndex = column === 0 ? value : this.data.regionIndex[0]
    const cityIndex = column === 1 ? value : column === 0 ? 0 : this.data.regionIndex[1]
    
    // u66f4u65b0u57ceu5e02u548cu533au53bfu6570u636e
    if (column === 0) {
      // u7701u4efdu53d8u5316uff0cu9700u8981u66f4u65b0u57ceu5e02u548cu533au53bf
      const provinceCode = this.data.provinceList[provinceIndex].code
      this.cityList = this.cities[provinceCode]
      this.districtList = this.districts[this.cityList[0].code]
      
      const regionArray = this.data.regionArray
      regionArray[1] = this.cityList.map(item => item.name)
      regionArray[2] = this.districtList.map(item => item.name)
      
      this.setData({
        regionArray: regionArray,
        regionIndex: [provinceIndex, 0, 0]
      })
      
      // u66f4u65b0u7701u5e02u533au6570u636e
      this.setData({
        province: this.data.provinceList[provinceIndex].name,
        city: this.cityList[0].name,
        district: this.districtList[0].name
      })
    } else if (column === 1) {
      // u57ceu5e02u53d8u5316uff0cu9700u8981u66f4u65b0u533au53bf
      const cityCode = this.cityList[cityIndex].code
      this.districtList = this.districts[cityCode]
      
      const regionArray = this.data.regionArray
      regionArray[2] = this.districtList.map(item => item.name)
      
      this.setData({
        regionArray: regionArray,
        regionIndex: [provinceIndex, cityIndex, 0]
      })
      
      // u66f4u65b0u57ceu5e02u548cu533au53bfu6570u636e
      this.setData({
        city: this.cityList[cityIndex].name,
        district: this.districtList[0].name
      })
    } else if (column === 2) {
      // u533au53bfu53d8u5316
      this.setData({
        regionIndex: [provinceIndex, cityIndex, value],
        district: this.districtList[value].name
      })
    }
    
    return this.data.regionArray
  },
  
  /**
   * u6253u5f00u533au57dfu9009u62e9u5668
   */
  showRegionPicker: function() {
    this.setData({
      regionVisible: true
    })
  },
  
  /**
   * u786eu8ba4u9009u62e9u533au57df
   */
  confirmRegion: function(e) {
    const regionIndex = e.detail.value
    const provinceIndex = regionIndex[0]
    const cityIndex = regionIndex[1]
    const districtIndex = regionIndex[2]
    
    this.setData({
      regionIndex: regionIndex,
      regionVisible: false,
      province: this.data.provinceList[provinceIndex].name,
      city: this.cityList[cityIndex].name,
      district: this.districtList[districtIndex].name
    })
  },
  
  /**
   * u53d6u6d88u533au57dfu9009u62e9
   */
  cancelRegion: function() {
    this.setData({
      regionVisible: false
    })
  },
  
  /**
   * u8868u5355u8f93u5165u5904u7406
   */
  inputReceiverName: function(e) {
    this.setData({
      receiverName: e.detail.value,
      errorMsg: ''
    })
  },
  
  inputReceiverMobile: function(e) {
    this.setData({
      receiverMobile: e.detail.value,
      errorMsg: ''
    })
  },
  
  inputAddress: function(e) {
    this.setData({
      address: e.detail.value,
      errorMsg: ''
    })
  },
  
  toggleDefault: function() {
    this.setData({
      isDefault: !this.data.isDefault
    })
  },
  
  /**
   * u8868u5355u9a8cu8bc1
   */
  validateForm: function() {
    if (!this.data.receiverName) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u6536u4ef6u4ebau59d3u540d' })
      return false
    }
    
    if (!this.data.receiverMobile) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u6536u4ef6u4ebau624bu673au53f7' })
      return false
    }
    
    // u624bu673au53f7u683cu5f0fu9a8cu8bc1
    const mobileReg = /^1[3-9]\d{9}$/
    if (!mobileReg.test(this.data.receiverMobile)) {
      this.setData({ errorMsg: 'u624bu673au53f7u683cu5f0fu4e0du6b63u786e' })
      return false
    }
    
    if (!this.data.province || !this.data.city || !this.data.district) {
      this.setData({ errorMsg: 'u8bf7u9009u62e9u6240u5728u5730u533a' })
      return false
    }
    
    if (!this.data.address) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u8be6u7ec6u5730u5740' })
      return false
    }
    
    return true
  },
  
  /**
   * u4fddu5b58u5730u5740
   */
  saveAddress: function() {
    // u9a8cu8bc1u8868u5355
    if (!this.validateForm()) {
      return
    }
    
    this.setData({ isSaving: true })
    
    // u6784u9020u8bf7u6c42u6570u636e
    const addressData = {
      receiver_name: this.data.receiverName,
      receiver_mobile: this.data.receiverMobile,
      province: this.data.province,
      city: this.data.city,
      district: this.data.district,
      address: this.data.address,
      is_default: this.data.isDefault ? 1 : 0
    }
    
    // u7f16u8f91u6a21u5f0fu9700u8981u4f20u5165ID
    if (this.data.isEdit) {
      addressData.id = this.data.id
    }
    
    // u8c03u7528u4e0du540cu7684u63a5u53e3
    const apiCall = this.data.isEdit ? 
      api.updateAddress(addressData) : 
      api.addAddress(addressData)
    
    apiCall.then(res => {
      if (res.code === 200) {
        wx.showToast({
          title: this.data.isEdit ? 'u7f16u8f91u6210u529f' : 'u6dfbu52a0u6210u529f',
          icon: 'success'
        })
        
        // u8fd4u56deu4e0au4e00u9875
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: res.message || 'u4fddu5b58u5931u8d25',
          icon: 'none'
        })
        this.setData({ isSaving: false })
      }
    }).catch(err => {
      console.error('u4fddu5b58u5730u5740u5931u8d25', err)
      wx.showToast({
        title: 'u4fddu5b58u5730u5740u5931u8d25',
        icon: 'none'
      })
      this.setData({ isSaving: false })
    })
  }
})
