<!-- 地址编辑页面模板 -->
<view class="container address-edit-container">
  <view class="page-title">
    <text>{{isEdit ? '编辑地址' : '新增地址'}}</text>
  </view>
  
  <view class="form-container">
    <!-- 收件人信息 -->
    <view class="form-section">
      <view class="form-item">
        <view class="form-label">收件人</view>
        <view class="form-input">
          <input 
            type="text" 
            placeholder="请输入收件人姓名" 
            value="{{receiverName}}" 
            bindinput="inputReceiverName"
            maxlength="15"
          />
        </view>
      </view>
      
      <view class="form-item">
        <view class="form-label">手机号</view>
        <view class="form-input">
          <input 
            type="number" 
            placeholder="请输入收件人手机号" 
            value="{{receiverMobile}}" 
            bindinput="inputReceiverMobile"
            maxlength="11"
          />
        </view>
      </view>
    </view>
    
    <!-- 地区信息 -->
    <view class="form-section">
      <view class="form-item region-picker" bindtap="showRegionPicker">
        <view class="form-label">所在地区</view>
        <view class="form-input region-input {{province && city && district ? '' : 'placeholder'}}">
          <text wx:if="{{province && city && district}}">{{province}} {{city}} {{district}}</text>
          <text wx:else>请选择所在省/市/区</text>
          <view class="arrow-right">›</view>
        </view>
      </view>
      
      <!-- u533au57dfu9009u62e9u5668 -->
      <picker-view
        class="region-picker-view {{regionVisible ? 'visible' : ''}}"
        indicator-style="height: 88rpx;"
        value="{{regionIndex}}"
        bindchange="bindRegionChange"
      >
        <view class="picker-header">
          <view class="picker-action" bindtap="cancelRegion">取消</view>
          <view class="picker-title">选择地区</view>
          <view class="picker-action confirm" bindtap="confirmRegion">确定</view>
        </view>
        <picker-view-column>
          <view wx:for="{{regionArray[0]}}" wx:key="index" class="picker-item">{{item}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{regionArray[1]}}" wx:key="index" class="picker-item">{{item}}</view>
        </picker-view-column>
        <picker-view-column>
          <view wx:for="{{regionArray[2]}}" wx:key="index" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
      
      <view class="form-item">
        <view class="form-label">详细地址</view>
        <view class="form-input">
          <textarea
            placeholder="请输入详细地址信息，如街道、门牌号、小区、楼栋号等"
            value="{{address}}"
            bindinput="inputAddress"
            maxlength="100"
            auto-height
          ></textarea>
        </view>
      </view>
    </view>
    
    <!-- 设置默认地址 -->
    <view class="form-section">
      <view class="form-item switch-item">
        <view class="form-label">设为默认地址</view>
        <switch checked="{{isDefault}}" bindchange="toggleDefault" color="#3a86ff" />
      </view>
      <view class="form-tip">每次下单时，默认使用该地址</view>
    </view>
    
    <!-- 错误提示 -->
    <view class="error-msg" wx:if="{{errorMsg}}">
      <text>{{errorMsg}}</text>
    </view>
    
    <!-- 保存按钮 -->
    <view class="button-container">
      <button 
        class="save-button" 
        bindtap="saveAddress" 
        disabled="{{isSaving}}"
        loading="{{isSaving}}"
      >
        {{isEdit ? '保存修改' : '保存地址'}}
      </button>
    </view>
  </view>
  
  <!-- 遮罩层 -->
  <view class="mask" wx:if="{{regionVisible}}" bindtap="cancelRegion"></view>
</view>
