/**
 * 地址编辑页面样式
 */

.address-edit-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单容器 */
.form-container {
  padding: 0 40rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  padding: 30rpx 0;
  display: flex;
  align-items: flex-start;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 180rpx;
  font-size: 30rpx;
  color: #333333;
  padding-top: 8rpx;
}

.form-input {
  flex: 1;
}

.form-input input, .form-input textarea {
  width: 100%;
  font-size: 30rpx;
  color: #333333;
}

.form-input textarea {
  min-height: 120rpx;
  line-height: 1.5;
}

.placeholder {
  color: #999999;
}

/* 地区选择器 */
.region-picker {
  position: relative;
  align-items: center;
}

.region-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow-right {
  font-size: 36rpx;
  color: #cccccc;
  margin-left: 20rpx;
}

.region-picker-view {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: #ffffff;
  z-index: 1000;
  transition: height 0.3s ease;
  overflow: hidden;
}

.region-picker-view.visible {
  height: 600rpx;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}

.picker-action {
  font-size: 30rpx;
  color: #666666;
  padding: 10rpx 20rpx;
}

.picker-action.confirm {
  color: #3a86ff;
}

.picker-item {
  line-height: 88rpx;
  text-align: center;
  font-size: 30rpx;
  color: #333333;
}

/* 切换开关 */
.switch-item {
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 24rpx;
  color: #999999;
  padding: 0 0 20rpx 0;
}

/* 错误提示 */
.error-msg {
  color: #ff4d4f;
  font-size: 26rpx;
  padding: 20rpx 0;
  text-align: center;
}

/* 保存按钮 */
.button-container {
  margin-top: 60rpx;
  padding: 0 40rpx;
}

.save-button {
  background-color: #3a86ff;
  color: #ffffff;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
  width: 100%;
}

.save-button[disabled] {
  background-color: #a7c6fa;
  color: #ffffff;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
