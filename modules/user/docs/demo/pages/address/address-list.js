/**
 * 地址列表页面逻辑
 */

const api = require('../../utils/api')

Page({
  data: {
    addressList: [],
    isLoading: false,
    isEmpty: false,
    page: 1,
    pageSize: 10,
    hasMore: true
  },
  
  onLoad: function() {
    this.loadAddressList(true)
  },
  
  onShow: function() {
    // 当页面重新显示时，刷新地址列表
    this.loadAddressList(true)
  },
  
  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadAddressList(true).finally(() => {
      wx.stopPullDownRefresh()
    })
  },
  
  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadMoreAddresses()
    }
  },
  
  /**
   * 加载地址列表
   * @param {Boolean} refresh 是否刷新列表(重置到第一页)
   */
  loadAddressList: function(refresh = false) {
    // 如果是刷新，重置分页参数
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        addressList: []
      })
    }
    
    // 如果没有更多数据了，就不再请求
    if (!this.data.hasMore) {
      return Promise.resolve()
    }
    
    this.setData({ isLoading: true })
    
    return api.getAddressList(this.data.page, this.data.pageSize)
      .then(res => {
        if (res.code === 200) {
          const list = res.data
          const pagination = res.pagination || {}
          const total = pagination.total || 0
          
          // 判断是否有更多数据
          const hasMore = this.data.page * this.data.pageSize < total
          
          // 判断列表是否为空
          const isEmpty = refresh && list.length === 0
          
          // 更新数据源
          this.setData({
            addressList: refresh ? list : this.data.addressList.concat(list),
            hasMore: hasMore,
            isEmpty: isEmpty,
            isLoading: false
          })
        } else {
          wx.showToast({
            title: res.message || '加载地址失败',
            icon: 'none'
          })
          this.setData({ isLoading: false })
        }
      })
      .catch(err => {
        console.error('加载地址列表失败', err)
        wx.showToast({
          title: '加载地址失败',
          icon: 'none'
        })
        this.setData({ isLoading: false })
      })
  },
  
  /**
   * 加载更多地址
   */
  loadMoreAddresses: function() {
    this.setData({
      page: this.data.page + 1
    })
    this.loadAddressList()
  },
  
  /**
   * 添加新地址
   */
  addAddress: function() {
    wx.navigateTo({
      url: '/pages/address/address-edit'
    })
  },
  
  /**
   * 编辑地址
   */
  editAddress: function(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/address/address-edit?id=${id}`
    })
  },
  
  /**
   * 删除地址
   */
  deleteAddress: function(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '提示',
      content: '确定要删除该地址吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...'
          })
          
          api.deleteAddress(id)
            .then(res => {
              if (res.code === 200) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                // 刷新列表
                this.loadAddressList(true)
              } else {
                wx.showToast({
                  title: res.message || '删除失败',
                  icon: 'none'
                })
              }
            })
            .catch(err => {
              console.error('删除地址失败', err)
              wx.showToast({
                title: '删除地址失败',
                icon: 'none'
              })
            })
            .finally(() => {
              wx.hideLoading()
            })
        }
      }
    })
  },
  
  /**
   * 设置默认地址
   */
  setDefault: function(e) {
    const id = e.currentTarget.dataset.id
    
    wx.showLoading({
      title: '设置中...'
    })
    
    api.setDefaultAddress(id)
      .then(res => {
        if (res.code === 200) {
          wx.showToast({
            title: '设置成功',
            icon: 'success'
          })
          // u5237u65b0u5217u8868
          this.loadAddressList(true)
        } else {
          wx.showToast({
            title: res.message || '设置失败',
            icon: 'none'
          })
        }
      })
      .catch(err => {
        console.error('设置默认地址失败', err)
        wx.showToast({
          title: '设置默认地址失败',
          icon: 'none'
        })
      })
      .finally(() => {
        wx.hideLoading()
      })
  }
})
