<!-- 地址列表页面模板 -->
<view class="container address-list-container">
  <view class="page-title">
    <text>收货地址管理</text>
  </view>
  
  <!-- 地址列表 -->
  <block wx:if="{{!isEmpty}}">
    <view class="address-list">
      <view 
        class="address-item {{item.is_default ? 'default' : ''}}" 
        wx:for="{{addressList}}" 
        wx:key="id"
      >
        <!-- 默认标记 -->
        <view class="default-tag" wx:if="{{item.is_default}}">
          <text>默认</text>
        </view>
        
        <!-- 地址内容 -->
        <view class="address-content" data-id="{{item.id}}" bindtap="editAddress">
          <view class="address-header">
            <text class="name">{{item.receiver_name}}</text>
            <text class="mobile">{{item.receiver_mobile}}</text>
          </view>
          <view class="address-detail">
            <text>{{item.province}} {{item.city}} {{item.district}} {{item.address}}</text>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="address-actions">
          <view class="action-divider"></view>
          <view class="action-buttons">
            <view 
              class="action-button {{item.is_default ? 'disabled' : ''}}" 
              catchtap="{{!item.is_default ? 'setDefault' : ''}}" 
              data-id="{{item.id}}"
            >
              <image class="action-icon" src="/static/images/icon-default{{item.is_default ? '-active' : ''}}.png"></image>
              <text>设为默认</text>
            </view>
            <view class="action-divider-vertical"></view>
            <view class="action-button" catchtap="deleteAddress" data-id="{{item.id}}">
              <image class="action-icon" src="/static/images/icon-delete.png"></image>
              <text>删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty}}">
    <image class="empty-icon" src="/static/images/empty-address.png"></image>
    <text class="empty-text">您还没有添加收货地址</text>
  </view>
  
  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{isLoading && !isEmpty}}">
    <view class="loading-icon"></view>
    <text>加载中...</text>
  </view>
  
  <!-- 加载完成提示 -->
  <view class="loaded-all" wx:if="{{!hasMore && !isEmpty && !isLoading && addressList.length > 0}}">
    <text>没有更多地址了</text>
  </view>
  
  <!-- 底部按钮 -->
  <view class="add-button-container">
    <button class="add-button" bindtap="addAddress">
      <text class="add-icon">+</text>
      <text>新增收货地址</text>
    </button>
  </view>
</view>
