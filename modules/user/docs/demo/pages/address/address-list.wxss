/**
 * 地址列表页面样式
 */

.address-list-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
  box-sizing: border-box;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 地址列表 */
.address-list {
  margin-top: 20rpx;
}

.address-item {
  position: relative;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.address-item.default {
  border-left: 8rpx solid #3a86ff;
}

.default-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #3a86ff;
  color: #ffffff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-bottom-left-radius: 12rpx;
  z-index: 10;
}

.address-content {
  padding: 30rpx 40rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 20rpx;
}

.mobile {
  font-size: 28rpx;
  color: #666666;
}

.address-detail {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

/* 操作按钮 */
.address-actions {
  display: flex;
  flex-direction: column;
}

.action-divider {
  height: 2rpx;
  background-color: #f5f5f5;
}

.action-buttons {
  display: flex;
  height: 90rpx;
}

.action-button {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 28rpx;
  color: #666666;
}

.action-button.disabled {
  color: #cccccc;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.action-divider-vertical {
  width: 2rpx;
  height: 40rpx;
  background-color: #f5f5f5;
  align-self: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* 加载更多 */
.loading-more, .loaded-all {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-icon {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
  border-top-color: #3a86ff;
  margin-right: 10rpx;
  animation: rotate 0.8s infinite linear;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 添加按钮 */
.add-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 40rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.add-button {
  background-color: #3a86ff;
  color: #ffffff;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
}
