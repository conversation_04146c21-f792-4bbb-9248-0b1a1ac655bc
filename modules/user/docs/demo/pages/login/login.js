/**
 * 登录页面逻辑
 */

const app = getApp()
const api = require('../../utils/api')

Page({
  data: {
    username: '',
    password: '',
    isLoading: false,
    errorMsg: ''
  },
  
  // 输入框事件处理
  usernameInput: function(e) {
    this.setData({
      username: e.detail.value,
      errorMsg: ''
    })
  },
  
  passwordInput: function(e) {
    this.setData({
      password: e.detail.value,
      errorMsg: ''
    })
  },
  
  // 清除输入
  clearUsername: function() {
    this.setData({
      username: '',
      errorMsg: ''
    })
  },
  
  clearPassword: function() {
    this.setData({
      password: '',
      errorMsg: ''
    })
  },
  
  // 提交登录
  submitLogin: function() {
    // 表单验证
    if (!this.data.username) {
      this.setData({ errorMsg: '请输入用户名' })
      return
    }
    
    if (!this.data.password) {
      this.setData({ errorMsg: '请输入密码' })
      return
    }
    
    // 显示加载状态
    this.setData({ isLoading: true, errorMsg: '' })
    
    // 调用登录API
    app.doLogin(this.data.username, this.data.password)
      .then(res => {
        // 登录成功
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        })
        
        // 跳转到首页或来源页面
        setTimeout(() => {
          const pages = getCurrentPages()
          if (pages.length > 1) {
            wx.navigateBack()
          } else {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }
        }, 1500)
      })
      .catch(err => {
        // 登录失败
        this.setData({
          errorMsg: err.message || '登录失败，请检查用户名和密码',
          isLoading: false
        })
      })
  },
  
  // 跳转到注册页面
  navigateToRegister: function() {
    wx.navigateTo({
      url: '/pages/register/register'
    })
  }
})
