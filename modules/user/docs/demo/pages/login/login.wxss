/**
 * u767bu5f55u9875u9762u6837u5f0f
 */

.login-container {
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 100rpx;
  margin-bottom: 80rpx;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.form-container {
  width: 100%;
}

.input-item {
  margin-bottom: 40rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.input-control {
  position: relative;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #dddddd;
  padding: 16rpx 0;
}

.input-control input {
  flex: 1;
  height: 60rpx;
  font-size: 32rpx;
}

.clear-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999999;
  font-size: 40rpx;
}

.error-msg {
  color: #ff4d4f;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.login-btn {
  background-color: #3a86ff;
  color: #ffffff;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 60rpx;
}

.login-btn[disabled] {
  background-color: #a7c6fa;
  color: #ffffff;
}

.register-link {
  margin-top: 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666666;
}

.link {
  color: #3a86ff;
  margin-left: 10rpx;
}
