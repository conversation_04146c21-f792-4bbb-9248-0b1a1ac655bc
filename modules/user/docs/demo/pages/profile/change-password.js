/**
 * 修改密码页面逻辑
 */

const api = require('../../utils/api')
const app = getApp()

Page({
  data: {
    currentPassword: '',   // 当前密码
    newPassword: '',      // 新密码
    confirmPassword: '',  // 确认新密码
    
    isSubmitting: false,  // 提交中状态
    errorMsg: '',         // 错误信息
    
    passwordVisible: {     // 密码可见性控制
      current: false,
      new: false,
      confirm: false
    }
  },
  
  onLoad: function() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '您尚未登录，请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
    }
  },
  
  /**
   * 输入框事件处理
   */
  inputCurrentPassword: function(e) {
    this.setData({
      currentPassword: e.detail.value,
      errorMsg: ''
    })
  },
  
  inputNewPassword: function(e) {
    this.setData({
      newPassword: e.detail.value,
      errorMsg: ''
    })
  },
  
  inputConfirmPassword: function(e) {
    this.setData({
      confirmPassword: e.detail.value,
      errorMsg: ''
    })
  },
  
  /**
   * 切换密码可见性
   */
  togglePasswordVisibility: function(e) {
    const field = e.currentTarget.dataset.field
    const key = `passwordVisible.${field}`
    
    // 切换指定字段的可见性
    this.setData({
      [key]: !this.data.passwordVisible[field]
    })
  },
  
  /**
   * 表单验证
   */
  validateForm: function() {
    if (!this.data.currentPassword) {
      this.setData({ errorMsg: '请输入当前密码' })
      return false
    }
    
    if (!this.data.newPassword) {
      this.setData({ errorMsg: '请输入新密码' })
      return false
    }
    
    // 密码强度验证 (至少8位，包含大小写字母和数字)
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,20}$/
    if (!passwordRegex.test(this.data.newPassword)) {
      this.setData({ errorMsg: '新密码必须为8-20位，且包含大小写字母和数字' })
      return false
    }
    
    if (!this.data.confirmPassword) {
      this.setData({ errorMsg: '请确认新密码' })
      return false
    }
    
    if (this.data.newPassword !== this.data.confirmPassword) {
      this.setData({ errorMsg: '两次输入的密码不一致' })
      return false
    }
    
    if (this.data.currentPassword === this.data.newPassword) {
      this.setData({ errorMsg: '新密码不能与当前密码相同' })
      return false
    }
    
    return true
  },
  
  /**
   * 提交修改密码
   */
  submitChangePassword: function() {
    // 表单验证
    if (!this.validateForm()) {
      return
    }
    
    this.setData({ isSubmitting: true })
    
    // 构造请求数据
    const passwordData = {
      current_password: this.data.currentPassword,
      new_password: this.data.newPassword,
      confirm_password: this.data.confirmPassword
    }
    
    api.changePassword(passwordData)
      .then(res => {
        if (res.code === 200) {
          wx.showToast({
            title: '密码修改成功',
            icon: 'success'
          })
          
          // 清空表单
          this.setData({
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
            errorMsg: ''
          })
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          this.setData({ 
            errorMsg: res.message || '密码修改失败',
            isSubmitting: false
          })
        }
      })
      .catch(err => {
        console.error('修改密码失败', err)
        this.setData({ 
          errorMsg: '修改密码失败，请稍后重试',
          isSubmitting: false
        })
      })
  }
})
