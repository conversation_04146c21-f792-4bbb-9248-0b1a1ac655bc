<!-- 修改密码页面模板 -->
<view class="container change-password-container">
  <view class="page-title">
    <text>修改密码</text>
  </view>
  
  <view class="form-container">
    <!-- 密码表单 -->
    <view class="form-section">
      <!-- 当前密码 -->
      <view class="form-item">
        <view class="form-label">当前密码</view>
        <view class="form-input">
          <input 
            type="{{passwordVisible.current ? 'text' : 'password'}}" 
            placeholder="请输入当前密码" 
            value="{{currentPassword}}" 
            bindinput="inputCurrentPassword"
            maxlength="20"
          />
          <view 
            class="password-toggle" 
            bindtap="togglePasswordVisibility" 
            data-field="current"
          >
            <image 
              class="eye-icon" 
              src="/static/images/icon-{{passwordVisible.current ? 'eye-open' : 'eye-close'}}.png"
            ></image>
          </view>
        </view>
      </view>
      
      <!-- 新密码 -->
      <view class="form-item">
        <view class="form-label">新密码</view>
        <view class="form-input">
          <input 
            type="{{passwordVisible.new ? 'text' : 'password'}}" 
            placeholder="请输入新密码" 
            value="{{newPassword}}" 
            bindinput="inputNewPassword"
            maxlength="20"
          />
          <view 
            class="password-toggle" 
            bindtap="togglePasswordVisibility" 
            data-field="new"
          >
            <image 
              class="eye-icon" 
              src="/static/images/icon-{{passwordVisible.new ? 'eye-open' : 'eye-close'}}.png"
            ></image>
          </view>
        </view>
      </view>
      <view class="password-tip">
        <text>密码长度为8-20位，必须包含大小写字母和数字</text>
      </view>
      
      <!-- 确认新密码 -->
      <view class="form-item">
        <view class="form-label">确认新密码</view>
        <view class="form-input">
          <input 
            type="{{passwordVisible.confirm ? 'text' : 'password'}}" 
            placeholder="请再次输入新密码" 
            value="{{confirmPassword}}" 
            bindinput="inputConfirmPassword"
            maxlength="20"
          />
          <view 
            class="password-toggle" 
            bindtap="togglePasswordVisibility" 
            data-field="confirm"
          >
            <image 
              class="eye-icon" 
              src="/static/images/icon-{{passwordVisible.confirm ? 'eye-open' : 'eye-close'}}.png"
            ></image>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 错误提示 -->
    <view class="error-msg" wx:if="{{errorMsg}}">
      <text>{{errorMsg}}</text>
    </view>
    
    <!-- 提交按钮 -->
    <view class="button-container">
      <button 
        class="submit-button" 
        bindtap="submitChangePassword" 
        disabled="{{isSubmitting}}"
        loading="{{isSubmitting}}"
      >
        确认修改
      </button>
    </view>
  </view>
</view>
