/**
 * 修改密码页面样式
 */

.change-password-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 表单容器 */
.form-container {
  padding: 0 40rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-top: 20rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  padding: 25rpx 0;
  display: flex;
  align-items: flex-start;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 180rpx;
  font-size: 30rpx;
  color: #333333;
  padding-top: 8rpx;
}

.form-input {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.form-input input {
  width: 100%;
  font-size: 30rpx;
  color: #333333;
  padding-right: 70rpx;
}

/* 密码可见性切换按钮 */
.password-toggle {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eye-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
}

/* 密码提示文本 */
.password-tip {
  font-size: 24rpx;
  color: #999999;
  padding: 10rpx 0 20rpx 180rpx;
  line-height: 1.4;
}

/* 错误提示 */
.error-msg {
  color: #ff4d4f;
  font-size: 26rpx;
  padding: 20rpx 0;
  text-align: center;
}

/* 提交按钮 */
.button-container {
  margin-top: 60rpx;
}

.submit-button {
  background-color: #3a86ff;
  color: #ffffff;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
  width: 100%;
}

.submit-button[disabled] {
  background-color: #a7c6fa;
  color: #ffffff;
}
