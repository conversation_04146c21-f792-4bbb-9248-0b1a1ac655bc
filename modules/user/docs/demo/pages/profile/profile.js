/**
 * 个人中心页面逻辑
 */

const app = getApp()
const api = require('../../utils/api')

Page({
  data: {
    isLoggedIn: false,
    userInfo: null,
    menuList: [
      {
        id: 'address',
        name: '收货地址管理',
        icon: '/static/images/icon-address.png',
        url: '/pages/address/address-list'
      },
      {
        id: 'password',
        name: '修改密码',
        icon: '/static/images/icon-password.png',
        url: '/pages/profile/change-password'
      },
      {
        id: 'about',
        name: '关于我们',
        icon: '/static/images/icon-about.png',
        url: '/pages/about/about'
      }
    ]
  },
  
  onLoad: function() {
    // 页面加载时检查登录状态
    this.checkLoginStatus()
  },
  
  onShow: function() {
    // 页面显示时检查登录状态，处理从其他页面返回的情况
    this.checkLoginStatus()
  },
  
  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.refreshUserInfo().finally(() => {
      wx.stopPullDownRefresh()
    })
  },
  
  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    // 获取全局登录状态
    const isLoggedIn = app.globalData.isLoggedIn
    const userInfo = app.globalData.userInfo
    
    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo
    })
    
    if (isLoggedIn && !userInfo) {
      // 已登录但没有用户信息，尝试获取
      this.refreshUserInfo()
    }
  },
  
  /**
   * 刷新用户信息
   */
  refreshUserInfo: function() {
    return new Promise((resolve, reject) => {
      if (!app.globalData.isLoggedIn) {
        // 未登录状态
        resolve()
        return
      }
      
      wx.showLoading({
        title: '加载中...'
      })
      
      api.getUserInfo().then(res => {
        if (res.code === 200) {
          app.globalData.userInfo = res.data
          this.setData({
            userInfo: res.data
          })
          resolve(res.data)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      }).finally(() => {
        wx.hideLoading()
      })
    })
  },
  
  /**
   * 跳转到登录页
   */
  navigateToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },
  
  /**
   * 退出登录
   */
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出中...'
          })
          
          app.doLogout().then(() => {
            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            })
            
            this.setData({
              isLoggedIn: false,
              userInfo: null
            })
          }).catch(err => {
            console.error('退出登录失败', err)
            wx.showToast({
              title: '退出失败',
              icon: 'error'
            })
          }).finally(() => {
            wx.hideLoading()
          })
        }
      }
    })
  },
  
  /**
   * 点击菜单项
   */
  onMenuItemTap: function(e) {
    const item = e.currentTarget.dataset.item
    
    // 检查是否需要登录
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      
      setTimeout(() => {
        this.navigateToLogin()
      }, 1000)
      return
    }
    
    // 跳转到对应页面
    wx.navigateTo({
      url: item.url
    })
  }
})
