<!-- 个人中心页面模板 -->
<view class="container profile-container">
  <!-- 未登录状态 -->
  <block wx:if="{{!isLoggedIn}}">
    <view class="not-login-container">
      <image class="avatar-placeholder" src="/static/images/avatar-default.png" mode="aspectFill"></image>
      <view class="login-tips">
        <text>您尚未登录</text>
        <text>登录后可查看个人信息</text>
      </view>
      <button class="login-btn" bindtap="navigateToLogin">立即登录/注册</button>
    </view>
  </block>
  
  <!-- 已登录状态 -->
  <block wx:else>
    <view class="user-info-container">
      <view class="user-info-header">
        <image class="user-avatar" 
               src="{{userInfo.avatar || '/static/images/avatar-default.png'}}" 
               mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-nickname">{{userInfo.nickname}}</text>
          <text class="user-level">{{userInfo.level == 0 ? '普通用户' : userInfo.level == 1 ? 'VIP用户' : 'SVIP用户'}}</text>
        </view>
      </view>
      
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-value">{{userInfo.balance || '0.00'}}</text>
          <text class="stat-label">账户余额</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{userInfo.points || '0'}}</text>
          <text class="stat-label">积分</text>
        </view>
      </view>
    </view>
  </block>
  
  <!-- 菜单列表 -->
  <view class="menu-container">
    <view 
      class="menu-item" 
      wx:for="{{menuList}}" 
      wx:key="id" 
      bindtap="onMenuItemTap"
      data-item="{{item}}"
    >
      <view class="menu-item-left">
        <image class="menu-icon" src="{{item.icon}}"></image>
        <text class="menu-name">{{item.name}}</text>
      </view>
      <view class="menu-item-right">
        <text class="menu-arrow">〉</text>
      </view>
    </view>
  </view>
  
  <!-- 退出登录按钮 -->
  <view class="logout-container" wx:if="{{isLoggedIn}}">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
  
  <!-- 版本信息 -->
  <view class="app-info">
    <text>O_Mall v1.0.0</text>
  </view>
</view>
