/**
 * 个人中心页面样式
 */

/* 页面容器 */
.profile-container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 未登录状态 */
.not-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.avatar-placeholder {
  width: 150rpx;
  height: 150rpx;
  border-radius: 75rpx;
  margin-bottom: 30rpx;
}

.login-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.login-tips text {
  color: #666666;
  font-size: 28rpx;
  line-height: 1.8;
}

.login-btn {
  background-color: #3a86ff;
  color: #ffffff;
  font-size: 32rpx;
  width: 320rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

/* 已登录状态 */
.user-info-container {
  background-color: #ffffff;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.user-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 2rpx solid #eeeeee;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.user-level {
  font-size: 24rpx;
  color: #ff9900;
  background-color: rgba(255, 153, 0, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  display: inline-block;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eeeeee;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666666;
}

.stat-divider {
  width: 2rpx;
  height: 50rpx;
  background-color: #eeeeee;
}

/* 菜单列表 */
.menu-container {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 30rpx;
}

.menu-name {
  font-size: 30rpx;
  color: #333333;
}

.menu-item-right {
  color: #cccccc;
  font-size: 30rpx;
}

/* 退出登录 */
.logout-container {
  padding: 30rpx 40rpx;
  margin-top: 20rpx;
}

.logout-btn {
  background-color: #ffffff;
  color: #ff4d4f;
  font-size: 32rpx;
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 12rpx;
}

/* 版本信息 */
.app-info {
  text-align: center;
  margin-top: 60rpx;
  margin-bottom: 40rpx;
  font-size: 24rpx;
  color: #999999;
}
