/**
 * 注册页面逻辑
 */

const api = require('../../utils/api')

Page({
  data: {
    username: '',
    password: '',
    confirmPassword: '',
    mobile: '',
    email: '',
    nickname: '',
    isLoading: false,
    errorMsg: ''
  },
  
  // 输入框事件处理
  usernameInput: function(e) {
    this.setData({
      username: e.detail.value,
      errorMsg: ''
    })
  },
  
  passwordInput: function(e) {
    this.setData({
      password: e.detail.value,
      errorMsg: ''
    })
  },
  
  confirmPasswordInput: function(e) {
    this.setData({
      confirmPassword: e.detail.value,
      errorMsg: ''
    })
  },
  
  mobileInput: function(e) {
    this.setData({
      mobile: e.detail.value,
      errorMsg: ''
    })
  },
  
  emailInput: function(e) {
    this.setData({
      email: e.detail.value,
      errorMsg: ''
    })
  },
  
  nicknameInput: function(e) {
    this.setData({
      nickname: e.detail.value,
      errorMsg: ''
    })
  },
  
  // 清除输入
  clearInput: function(field) {
    const data = {}
    data[field] = ''
    data.errorMsg = ''
    this.setData(data)
  },
  
  // 提交注册
  submitRegister: function() {
    // 表单验证
    if (!this.data.username) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u7528u6237u540d' })
      return
    }
    
    if (this.data.username.length < 5 || this.data.username.length > 20) {
      this.setData({ errorMsg: 'u7528u6237u540du957fu5ea6u5e94u4e3a5-20u4e2au5b57u7b26' })
      return
    }
    
    if (!this.data.password) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u5bc6u7801' })
      return
    }
    
    if (this.data.password.length < 8 || this.data.password.length > 20) {
      this.setData({ errorMsg: 'u5bc6u7801u957fu5ea6u5e94u4e3a8-20u4e2au5b57u7b26' })
      return
    }
    
    // u5bc6u7801u5f3au5ea6u9a8cu8bc1uff08u5927u5c0fu5199u5b57u6bcd+u6570u5b57uff09
    const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/
    if (!passwordPattern.test(this.data.password)) {
      this.setData({ errorMsg: 'u5bc6u7801u5fc5u987bu5305u542bu5927u5c0fu5199u5b57u6bcdu548cu6570u5b57' })
      return
    }
    
    if (!this.data.confirmPassword) {
      this.setData({ errorMsg: 'u8bf7u786eu8ba4u5bc6u7801' })
      return
    }
    
    if (this.data.password !== this.data.confirmPassword) {
      this.setData({ errorMsg: 'u4e24u6b21u8f93u5165u7684u5bc6u7801u4e0du4e00u81f4' })
      return
    }
    
    if (!this.data.mobile) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u624bu673au53f7' })
      return
    }
    
    // u624bu673au53f7u683cu5f0fu9a8cu8bc1
    const mobilePattern = /^1[3-9]\d{9}$/
    if (!mobilePattern.test(this.data.mobile)) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u6709u6548u7684u624bu673au53f7' })
      return
    }
    
    if (this.data.email) {
      // u90aeu7bb1u683cu5f0fu9a8cu8bc1uff08u9009u586buff09
      const emailPattern = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
      if (!emailPattern.test(this.data.email)) {
        this.setData({ errorMsg: 'u8bf7u8f93u5165u6709u6548u7684u90aeu7bb1u5730u5740' })
        return
      }
    }
    
    if (!this.data.nickname) {
      this.setData({ errorMsg: 'u8bf7u8f93u5165u6635u79f0' })
      return
    }
    
    if (this.data.nickname.length < 2 || this.data.nickname.length > 20) {
      this.setData({ errorMsg: 'u6635u79f0u957fu5ea6u5e94u4e3a2-20u4e2au5b57u7b26' })
      return
    }
    
    // u663eu793au52a0u8f7du72b6u6001
    this.setData({ isLoading: true, errorMsg: '' })
    
    // u6784u9020u6ce8u518cu6570u636e
    const registerData = {
      username: this.data.username,
      password: this.data.password,
      confirm_password: this.data.confirmPassword,
      mobile: this.data.mobile,
      email: this.data.email || undefined,  // u5982u679cu4e3au7a7auff0cu4e0du53d1u9001u8be5u5b57u6bb5
      nickname: this.data.nickname
    }
    
    // u8c03u7528u6ce8u518cAPI
    api.register(registerData)
      .then(res => {
        // u6ce8u518cu6210u529f
        wx.showToast({
          title: 'u6ce8u518cu6210u529f',
          icon: 'success',
          duration: 1500
        })
        
        // u8df3u8f6cu5230u767bu5f55u9875u9762
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }, 1500)
      })
      .catch(err => {
        // u6ce8u518cu5931u8d25
        this.setData({
          errorMsg: err.message || 'u6ce8u518cu5931u8d25uff0cu8bf7u68c0u67e5u8f93u5165u4fe1u606f',
          isLoading: false
        })
      })
  },
  
  // u8df3u8f6cu5230u767bu5f55u9875u9762
  navigateToLogin: function() {
    wx.navigateBack()
  }
})
