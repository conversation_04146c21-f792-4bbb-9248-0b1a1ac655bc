<!-- 注册页面模板 -->
<view class="container register-container">
  <view class="page-title">
    <text>创建新账号</text>
  </view>
  
  <!-- 注册表单 -->
  <view class="form-container">
    <!-- 用户名输入框 -->
    <view class="input-item">
      <view class="input-label">用户名</view>
      <view class="input-control">
        <input 
          type="text" 
          placeholder="请输入用户名(5-20个字符)" 
          value="{{username}}" 
          bindinput="usernameInput"
        />
        <view class="clear-icon" wx:if="{{username}}" bindtap="clearInput" data-field="username">×</view>
      </view>
    </view>
    
    <!-- 密码输入框 -->
    <view class="input-item">
      <view class="input-label">密码</view>
      <view class="input-control">
        <input 
          type="password" 
          placeholder="请输入密码(8-20个字符)" 
          value="{{password}}" 
          bindinput="passwordInput"
        />
        <view class="clear-icon" wx:if="{{password}}" bindtap="clearInput" data-field="password">×</view>
      </view>
      <view class="input-tip">密码必须包含大小写字母和数字</view>
    </view>
    
    <!-- 确认密码输入框 -->
    <view class="input-item">
      <view class="input-label">确认密码</view>
      <view class="input-control">
        <input 
          type="password" 
          placeholder="请再次输入密码" 
          value="{{confirmPassword}}" 
          bindinput="confirmPasswordInput"
        />
        <view class="clear-icon" wx:if="{{confirmPassword}}" bindtap="clearInput" data-field="confirmPassword">×</view>
      </view>
    </view>
    
    <!-- 手机号输入框 -->
    <view class="input-item">
      <view class="input-label">手机号</view>
      <view class="input-control">
        <input 
          type="number" 
          placeholder="请输入手机号" 
          value="{{mobile}}" 
          bindinput="mobileInput"
          maxlength="11"
        />
        <view class="clear-icon" wx:if="{{mobile}}" bindtap="clearInput" data-field="mobile">×</view>
      </view>
    </view>
    
    <!-- 邮箱输入框 -->
    <view class="input-item">
      <view class="input-label">邮箱 <text class="optional">(选填)</text></view>
      <view class="input-control">
        <input 
          type="text" 
          placeholder="请输入邮箱地址" 
          value="{{email}}" 
          bindinput="emailInput"
        />
        <view class="clear-icon" wx:if="{{email}}" bindtap="clearInput" data-field="email">×</view>
      </view>
    </view>
    
    <!-- 昵称输入框 -->
    <view class="input-item">
      <view class="input-label">昵称</view>
      <view class="input-control">
        <input 
          type="text" 
          placeholder="请输入昵称(2-20个字符)" 
          value="{{nickname}}" 
          bindinput="nicknameInput"
        />
        <view class="clear-icon" wx:if="{{nickname}}" bindtap="clearInput" data-field="nickname">×</view>
      </view>
    </view>
    
    <!-- 错误信息提示 -->
    <view class="error-msg" wx:if="{{errorMsg}}">
      {{errorMsg}}
    </view>
    
    <!-- 注册按钮 -->
    <button 
      class="register-btn" 
      bindtap="submitRegister" 
      disabled="{{isLoading}}"
      loading="{{isLoading}}"
    >
      注册
    </button>
    
    <!-- 登录链接 -->
    <view class="login-link">
      <text>已有账号？</text>
      <text class="link" bindtap="navigateToLogin">立即登录</text>
    </view>
  </view>
</view>
