/**
 * 注册页面样式
 */

.register-container {
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin: 60rpx 0 40rpx;
}

.form-container {
  width: 100%;
}

.input-item {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.optional {
  font-size: 24rpx;
  color: #999999;
}

.input-control {
  position: relative;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #dddddd;
  padding: 16rpx 0;
}

.input-control input {
  flex: 1;
  height: 60rpx;
  font-size: 32rpx;
}

.input-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
}

.clear-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999999;
  font-size: 40rpx;
}

.error-msg {
  color: #ff4d4f;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.register-btn {
  background-color: #3a86ff;
  color: #ffffff;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-top: 60rpx;
}

.register-btn[disabled] {
  background-color: #a7c6fa;
  color: #ffffff;
}

.login-link {
  margin-top: 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666666;
}

.link {
  color: #3a86ff;
  margin-left: 10rpx;
}
