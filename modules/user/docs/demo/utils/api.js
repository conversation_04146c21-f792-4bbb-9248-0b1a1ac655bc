/**
 * API接口封装
 * 
 * 封装与后端接口的交互，所有网络请求应通过此文件进行调用
 */

const request = require('./request.js')

// 用户相关接口
const userApi = {
  /**
   * 用户注册
   * @param {Object} data - 注册数据
   */
  register: (data) => {
    return request.post('/user/register', data)
  },
  
  /**
   * 用户登录
   * @param {String} username - 用户名或手机号
   * @param {String} password - 密码
   */
  login: (username, password) => {
    return request.post('/user/login', {
      username,
      password
    })
  },
  
  /**
   * 用户登出
   */
  logout: () => {
    return request.post('/user/secured/logout', {}, true)
  },
  
  /**
   * 获取用户信息
   */
  getUserInfo: () => {
    return request.get('/user/secured/info', {}, true)
  },
  
  /**
   * 更新用户信息
   * @param {Object} data - 用户信息数据
   */
  updateUserInfo: (data) => {
    return request.put('/user/secured/info', data, true)
  },
  
  /**
   * 修改密码
   * @param {String} oldPassword - 旧密码
   * @param {String} newPassword - 新密码
   * @param {String} confirmPassword - 确认密码
   */
  changePassword: (oldPassword, newPassword, confirmPassword) => {
    return request.put('/user/secured/password', {
      old_password: oldPassword,
      new_password: newPassword,
      confirm_password: confirmPassword
    }, true)
  }
}

// 地址相关接口
const addressApi = {
  /**
   * 获取地址列表
   * @param {Number} page - 页码
   * @param {Number} pageSize - 每页数量
   */
  getAddressList: (page = 1, pageSize = 10) => {
    return request.get('/user/secured/addresses', {
      page,
      pageSize
    }, true)
  },
  
  /**
   * 获取地址详情
   * @param {Number} id - 地址ID
   */
  getAddress: (id) => {
    return request.get(`/user/secured/addresses/${id}`, {}, true)
  },
  
  /**
   * 添加地址
   * @param {Object} data - 地址数据
   */
  addAddress: (data) => {
    return request.post('/user/secured/addresses', data, true)
  },
  
  /**
   * 更新地址
   * @param {Object} data - 地址数据
   */
  updateAddress: (data) => {
    return request.put('/user/secured/addresses', data, true)
  },
  
  /**
   * 删除地址
   * @param {Number} id - 地址ID
   */
  deleteAddress: (id) => {
    return request.delete(`/user/secured/addresses/${id}`, {}, true)
  },
  
  /**
   * 设置默认地址
   * @param {Number} id - 地址ID
   */
  setDefaultAddress: (id) => {
    return request.put(`/user/secured/addresses/${id}/default`, {
      address_id: id
    }, true)
  },
  
  /**
   * 获取默认地址
   */
  getDefaultAddress: () => {
    return request.get('/user/secured/addresses/default', {}, true)
  }
}

module.exports = {
  ...userApi,
  ...addressApi
}
