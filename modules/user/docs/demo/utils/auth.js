/**
 * 认证工具类
 * 
 * 负责Token的存储、获取、刷新和清除等认证相关操作
 */

const request = require('./request.js')

// 存储键名
const ACCESS_TOKEN_KEY = 'access_token'  // 访问令牌键名
const REFRESH_TOKEN_KEY = 'refresh_token'  // 刷新令牌键名

/**
 * 保存认证Token
 * @param {String} accessToken - 访问令牌
 * @param {String} refreshToken - 刷新令牌
 */
function setToken(accessToken, refreshToken) {
  // 使用微信小程序的同步存储API
  try {
    wx.setStorageSync(ACCESS_TOKEN_KEY, accessToken)
    wx.setStorageSync(REFRESH_TOKEN_KEY, refreshToken)
    return true
  } catch (e) {
    console.error('保存Token失败', e)
    return false
  }
}

/**
 * 获取访问令牌
 * @returns {String} 访问令牌，不存在则返回null
 */
function getToken() {
  try {
    return wx.getStorageSync(ACCESS_TOKEN_KEY)
  } catch (e) {
    console.error('获取Token失败', e)
    return null
  }
}

/**
 * 获取刷新令牌
 * @returns {String} 刷新令牌，不存在则返回null
 */
function getRefreshToken() {
  try {
    return wx.getStorageSync(REFRESH_TOKEN_KEY)
  } catch (e) {
    console.error('获取刷新Token失败', e)
    return null
  }
}

/**
 * 清除所有认证信息
 */
function clearAuth() {
  try {
    wx.removeStorageSync(ACCESS_TOKEN_KEY)
    wx.removeStorageSync(REFRESH_TOKEN_KEY)
    return true
  } catch (e) {
    console.error('清除认证信息失败', e)
    return false
  }
}

/**
 * 刷新Token
 * @returns {Promise<Boolean>} 刷新结果，成功为true，失败为false
 */
function refreshToken() {
  return new Promise((resolve) => {
    const refreshToken = getRefreshToken()
    
    if (!refreshToken) {
      // 无刷新令牌，直接返回失败
      return resolve(false)
    }
    
    // 使用未引用的原始request模块避免循环引用
    wx.request({
      url: 'https://api.example.com/api/v1/user/refresh-token',
      method: 'POST',
      data: {
        refresh_token: refreshToken
      },
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data && res.data.code === 200) {
          // 刷新成功，保存新Token
          const newAccessToken = res.data.data.access_token
          const newRefreshToken = res.data.data.refresh_token
          
          if (setToken(newAccessToken, newRefreshToken)) {
            resolve(true)
          } else {
            resolve(false)
          }
        } else {
          // 刷新失败
          clearAuth() // 清除无效Token
          resolve(false)
        }
      },
      fail: () => {
        // 请求失败
        resolve(false)
      }
    })
  })
}

/**
 * 检查是否已登录
 * @returns {Boolean} 是否已登录
 */
function isLoggedIn() {
  return !!getToken()
}

module.exports = {
  setToken,
  getToken,
  getRefreshToken,
  clearAuth,
  refreshToken,
  isLoggedIn
}
