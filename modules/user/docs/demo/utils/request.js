/**
 * 网络请求工具
 * 
 * 封装网络请求方法，处理请求拦截、响应成功/失败处理、Token处理等
 */

const auth = require('./auth.js')

// API基础地址
// 注意：实际开发时应使用环境变量或配置文件管理不同环境的地址
const BASE_URL = 'https://api.example.com/api/v1'

/**
 * 通用请求方法
 * @param {String} url - 接口路径
 * @param {String} method - 请求方法 GET、POST、PUT、DELETE
 * @param {Object} data - 请求参数
 * @param {Boolean} needToken - 是否需要认证
 */
const request = (url, method, data = {}, needToken = false) => {
  return new Promise((resolve, reject) => {
    // 请求头配置
    const header = {
      'Content-Type': 'application/json'
    }
    
    // 如果需要Token验证，则添加认证头
    if (needToken) {
      const token = auth.getToken()
      if (token) {
        header['Authorization'] = `Bearer ${token}`
      } else {
        // 无token时跳转登录
        wx.navigateTo({
          url: '/pages/login/login'
        })
        return reject({ code: 401, message: '未登录或认证已过期' })
      }
    }
    
    // 显示加载提示
    wx.showLoading({
      title: '加载中...'
    })
    
    wx.request({
      url: `${BASE_URL}${url}`,
      method: method,
      data: data,
      header: header,
      success: (res) => {
        // 根据响应状态码处理
        if (res.statusCode === 200) {
          // 请求成功并返回数据
          resolve(res.data)
        } else if (res.statusCode === 401) {
          // 认证失败，刷新Token
          auth.refreshToken().then(success => {
            if (success) {
              // 刷新成功，重新请求
              request(url, method, data, needToken).then(resolve).catch(reject)
            } else {
              // 刷新失败，跳转登录
              wx.navigateTo({
                url: '/pages/login/login'
              })
              reject({ code: 401, message: '认证已过期，请重新登录' })
            }
          })
        } else {
          // 其他错误情况
          reject(res.data || { code: res.statusCode, message: '请求失败' })
        }
      },
      fail: (err) => {
        // 网络错误或其他问题
        reject({ code: -1, message: '网络异常，请检查网络连接', err: err })
      },
      complete: () => {
        wx.hideLoading()
      }
    })
  })
}

// u5c01u88c5u5404u79cdu8bf7u6c42u65b9u6cd5
module.exports = {
  get: (url, data = {}, needToken = false) => {
    return request(url, 'GET', data, needToken)
  },
  post: (url, data = {}, needToken = false) => {
    return request(url, 'POST', data, needToken)
  },
  put: (url, data = {}, needToken = false) => {
    return request(url, 'PUT', data, needToken)
  },
  delete: (url, data = {}, needToken = false) => {
    return request(url, 'DELETE', data, needToken)
  }
}
