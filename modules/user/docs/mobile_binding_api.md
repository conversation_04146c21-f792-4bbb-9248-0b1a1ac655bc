# 手机号绑定和修改API文档

本文档详细说明了用户模块中手机号绑定和修改相关的API接口，包括请求格式、参数说明和响应结构。

## 接口概览

| 接口 | 请求方法 | 接口路径 | 描述 | 认证要求 |
| --- | --- | --- | --- | --- |
| 绑定手机号 | POST | /api/v1/user/secured/mobile/bind | 为当前用户绑定手机号 | 需要认证 |
| 修改绑定手机号 | POST | /api/v1/user/secured/mobile/change | 修改当前用户已绑定的手机号 | 需要认证 |
| 发送手机号绑定验证码 | POST | /api/v1/user/secured/mobile/send-bind-code | 发送手机号绑定验证码 | 需要认证 |
| 发送手机号修改验证码 | POST | /api/v1/user/secured/mobile/send-change-code | 发送手机号修改验证码（验证原手机号） | 需要认证 |

## 接口详细说明

### 绑定手机号

为当前未绑定手机号的用户绑定一个新手机号。

- 请求路径：`/api/v1/user/secured/mobile/bind`
- 请求方法：`POST`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "mobile": "13800138000",  // 要绑定的手机号，必填，符合手机号格式
  "code": "123456"          // 验证码，必填，6位数字
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "手机号绑定成功"
  }
}
```

- 响应示例（失败）：

```json
{
  "code": 400,
  "message": "操作失败",
  "data": {
    "success": false,
    "message": "该手机号已被其他账号使用"
  }
}
```

- 可能的错误：
  - 手机号格式不正确
  - 验证码不正确或已过期
  - 该手机号已被其他账号使用
  - 用户已经绑定了手机号

### 修改绑定手机号

修改当前用户已绑定的手机号为新手机号。

- 请求路径：`/api/v1/user/secured/mobile/change`
- 请求方法：`POST`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "old_mobile": "13800138000",  // 原手机号，必填，必须与当前用户绑定的手机号一致
  "old_code": "123456",         // 原手机号验证码，必填，6位数字
  "new_mobile": "13900139000",  // 新手机号，必填，符合手机号格式，必须与原手机号不同
  "new_code": "456789"          // 新手机号验证码，必填，6位数字
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "手机号修改成功"
  }
}
```

- 响应示例（失败）：

```json
{
  "code": 400,
  "message": "操作失败",
  "data": {
    "success": false,
    "message": "原手机号验证码不正确"
  }
}
```

- 可能的错误：
  - 手机号格式不正确
  - 原手机号与当前用户绑定的手机号不一致
  - 原手机号验证码不正确或已过期
  - 新手机号验证码不正确或已过期
  - 新手机号已被其他账号使用
  - 新手机号与原手机号相同
  - 用户未绑定手机号

### 发送手机号绑定验证码

为绑定手机号发送验证码。

- 请求路径：`/api/v1/user/secured/mobile/send-bind-code`
- 请求方法：`POST`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "mobile": "13800138000",       // 要绑定的手机号，必填，符合手机号格式
  "verify_type": "mobile_bind"   // 验证类型，必填，固定值为mobile_bind
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "验证码发送成功"
  }
}
```

- 可能的错误：
  - 手机号格式不正确
  - 发送验证码过于频繁
  - 短信服务异常

### 发送手机号修改验证码

为修改已绑定的手机号发送验证码（用于验证原手机号）。

- 请求路径：`/api/v1/user/secured/mobile/send-change-code`
- 请求方法：`POST`
- 请求头：
  - `Authorization: Bearer {access_token}`
- 请求参数：

```json
{
  "mobile": "13800138000",        // 原手机号，必填，必须与当前用户绑定的手机号一致
  "verify_type": "mobile_change"  // 验证类型，必填，固定值为mobile_change
}
```

- 响应示例（成功）：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "验证码发送成功"
  }
}
```

- 可能的错误：
  - 手机号格式不正确
  - 手机号与当前用户绑定的手机号不一致
  - 发送验证码过于频繁
  - 短信服务异常

## 业务流程说明

### 绑定手机号流程

1. 用户进入绑定手机号页面
2. 用户输入需要绑定的手机号
3. 调用"发送手机号绑定验证码"API发送验证码
4. 用户输入收到的验证码
5. 调用"绑定手机号"API完成手机号绑定

### 修改绑定手机号流程

1. 用户进入修改手机号页面
2. 系统显示用户当前绑定的手机号
3. 调用"发送手机号修改验证码"API向原手机号发送验证码
4. 用户输入原手机号收到的验证码
5. 用户输入新手机号
6. 调用"发送手机号绑定验证码"API向新手机号发送验证码
7. 用户输入新手机号收到的验证码
8. 调用"修改绑定手机号"API完成手机号修改

## 验证码注意事项

- 验证码有效期为5分钟
- 同一手机号60秒内只能发送一次验证码
- 手机号绑定和修改操作都需要通过验证码验证
- 验证码使用Redis存储，使用后立即失效
