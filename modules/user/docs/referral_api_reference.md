# 分销模块API接口文档

## 概述

分销模块提供了完整的多级分销功能，包括分销关系管理、推荐用户查询、分销统计等核心功能。所有接口都需要用户认证，基础路径为 `/api/v1/user/secured`。

## 接口列表

### 1. 创建分销关系

**接口地址**: `POST /api/v1/user/secured/referrals`

**接口描述**: 创建用户与推荐人之间的分销关系

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "user_id": 123,
  "referrer_id": 456,
  "level": 1
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int64 | 是 | 被推荐用户ID |
| referrer_id | int64 | 是 | 推荐人用户ID |
| level | int | 否 | 分销等级，1-3，默认为1 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 789
  }
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "该用户已有推荐关系，不能重复设置",
  "data": null
}
```

**业务规则**:
- 用户不能推荐自己
- 每个用户只能有一个推荐人
- 推荐人必须是有效用户
- 分销等级范围为1-3

---

### 2. 获取推荐用户列表

**接口地址**: `GET /api/v1/user/secured/referrals`

**接口描述**: 获取当前用户推荐的用户列表，支持分页和筛选

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| level | int | 否 | 分销等级筛选，不传则查询所有等级 |
| page | int | 否 | 页码，从1开始，默认1 |
| page_size | int | 否 | 每页记录数，最多100条，默认10 |
| only_direct | bool | 否 | 是否只查询直接推荐的用户，默认false |

**请求示例**:
```
GET /api/v1/user/secured/referrals?level=1&page=1&page_size=10&only_direct=true
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "user_id": 123,
        "referrer_id": 456,
        "level": 1,
        "status": 1,
        "commission": 150.50,
        "referral_time": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-15T10:30:00Z",
        "user": {
          "id": 123,
          "username": "user123",
          "nickname": "小明",
          "avatar": "https://example.com/avatar.jpg",
          "mobile": "138****1234",
          "level": 1
        }
      }
    ],
    "total": 25,
    "page": 1,
    "page_size": 10
  }
}
```

---

### 3. 获取分销统计信息

**接口地址**: `GET /api/v1/user/secured/referrals/statistics`

**接口描述**: 获取当前用户的分销统计数据，包括推荐用户数量和佣金统计

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_referrals": 50,
    "level1_referrals": 30,
    "level2_referrals": 15,
    "level3_referrals": 5,
    "total_commission": 2580.50,
    "today_commission": 120.00,
    "this_month_commission": 850.30,
    "this_year_commission": 2580.50
  }
}
```

**字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_referrals | int64 | 推荐用户总数 |
| level1_referrals | int64 | 一级推荐用户数 |
| level2_referrals | int64 | 二级推荐用户数 |
| level3_referrals | int64 | 三级推荐用户数 |
| total_commission | float64 | 累计佣金 |
| today_commission | float64 | 今日佣金 |
| this_month_commission | float64 | 本月佣金 |
| this_year_commission | float64 | 本年佣金 |

---

### 4. 获取推荐人信息

**接口地址**: `GET /api/v1/user/secured/referrals/referrer`

**接口描述**: 获取当前用户的推荐人信息

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "user_id": 123,
    "referrer_id": 456,
    "level": 1,
    "status": 1,
    "commission": 0,
    "referral_time": "2024-01-15T10:30:00Z",
    "created_at": "2024-01-15T10:30:00Z",
    "referrer": {
      "id": 456,
      "username": "referrer456",
      "nickname": "推荐人",
      "avatar": "https://example.com/referrer_avatar.jpg",
      "mobile": "139****5678",
      "level": 2
    }
  }
}
```

**无推荐人响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 常见错误信息

### 创建分销关系错误
- `"用户不存在"` - 被推荐用户ID无效
- `"推荐人不存在"` - 推荐人用户ID无效
- `"不能自己推荐自己"` - 用户ID和推荐人ID相同
- `"该用户已有推荐关系，不能重复设置"` - 用户已经有推荐人
- `"参数错误"` - 请求参数格式不正确或缺少必填参数

### 查询错误
- `"用户未登录"` - Authorization头缺失或token无效
- `"权限不足"` - 用户无权限访问该资源

## 使用示例

### JavaScript/Axios 示例

```javascript
// 创建分销关系
const createReferral = async (userId, referrerId, level = 1) => {
  try {
    const response = await axios.post('/api/v1/user/secured/referrals', {
      user_id: userId,
      referrer_id: referrerId,
      level: level
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.code === 200) {
      console.log('分销关系创建成功:', response.data.data.id);
    }
  } catch (error) {
    console.error('创建失败:', error.response.data.message);
  }
};

// 获取推荐用户列表
const getReferrals = async (page = 1, pageSize = 10, level = null, onlyDirect = false) => {
  try {
    const params = {
      page,
      page_size: pageSize,
      only_direct: onlyDirect
    };
    
    if (level) {
      params.level = level;
    }
    
    const response = await axios.get('/api/v1/user/secured/referrals', {
      params,
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    }
  } catch (error) {
    console.error('获取推荐列表失败:', error.response.data.message);
  }
};

// 获取分销统计
const getReferralStatistics = async () => {
  try {
    const response = await axios.get('/api/v1/user/secured/referrals/statistics', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    }
  } catch (error) {
    console.error('获取统计信息失败:', error.response.data.message);
  }
};

// 获取推荐人信息
const getReferrer = async () => {
  try {
    const response = await axios.get('/api/v1/user/secured/referrals/referrer', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    }
  } catch (error) {
    console.error('获取推荐人信息失败:', error.response.data.message);
  }
};
```

### cURL 示例

```bash
# 创建分销关系
curl -X POST "http://localhost:8080/api/v1/user/secured/referrals" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "referrer_id": 456,
    "level": 1
  }'

# 获取推荐用户列表
curl -X GET "http://localhost:8080/api/v1/user/secured/referrals?page=1&page_size=10&level=1" \
  -H "Authorization: Bearer your_token_here"

# 获取分销统计
curl -X GET "http://localhost:8080/api/v1/user/secured/referrals/statistics" \
  -H "Authorization: Bearer your_token_here"

# 获取推荐人信息
curl -X GET "http://localhost:8080/api/v1/user/secured/referrals/referrer" \
  -H "Authorization: Bearer your_token_here"
```

## 注意事项

1. **认证要求**: 所有接口都需要有效的JWT token
2. **分页限制**: 每页最多返回100条记录
3. **分销等级**: 目前支持1-3级分销
4. **数据一致性**: 分销关系一旦建立不可修改，只能通过状态字段管理
5. **佣金计算**: 佣金统计基于订单完成时间，不是分销关系创建时间
6. **性能考虑**: 大量数据查询时建议使用分页，避免一次性加载过多数据