# 分销模块业务流程文档

## 概述

本文档详细描述了分销模块的各种业务流程，包括用户注册推荐、分销关系建立、佣金计算与结算、统计报表生成等核心业务场景。通过流程图和详细说明，帮助开发人员和业务人员理解分销系统的运作机制。

## 核心业务流程

### 1. 用户推荐注册流程

#### 1.1 流程概述
用户通过推荐链接或推荐码注册成为新用户，系统自动建立分销关系。

#### 1.2 流程图
```mermaid
flowchart TD
    A[推荐人生成邀请链接] --> B[分享邀请链接给好友]
    B --> C[好友点击邀请链接]
    C --> D[跳转到注册页面]
    D --> E[填写注册信息]
    E --> F[提交注册]
    F --> G{验证推荐人是否有效}
    G -->|有效| H[创建用户账号]
    G -->|无效| I[正常注册，无推荐关系]
    H --> J[建立一级分销关系]
    J --> K[检查推荐人的推荐人]
    K --> L{是否存在二级推荐人}
    L -->|存在| M[建立二级分销关系]
    L -->|不存在| N[完成注册]
    M --> O[检查二级推荐人的推荐人]
    O --> P{是否存在三级推荐人}
    P -->|存在| Q[建立三级分销关系]
    P -->|不存在| N
    Q --> N
    I --> N
    N --> R[发送注册成功通知]
    R --> S[记录用户日志]
```

#### 1.3 详细步骤

**步骤1: 生成邀请链接**
- 推荐人登录系统
- 访问分销中心页面
- 系统生成包含推荐人ID的邀请链接
- 格式: `https://domain.com/register?referrer={referrer_id}`

**步骤2: 分享邀请链接**
- 推荐人通过微信、QQ、微博等社交平台分享
- 或生成二维码供线下分享
- 系统记录分享行为（可选）

**步骤3: 好友注册**
- 好友点击邀请链接访问注册页面
- 系统从URL参数中提取推荐人ID
- 在注册表单中隐藏推荐人信息

**步骤4: 验证和创建账号**
- 验证推荐人ID的有效性
- 创建新用户账号
- 发送验证码验证手机号

**步骤5: 建立分销关系**
- 创建一级分销关系（新用户 -> 推荐人）
- 递归查找推荐人的推荐人，建立多级关系
- 最多建立三级分销关系

#### 1.4 业务规则
- 每个用户只能有一个推荐人
- 推荐关系一旦建立不可修改
- 用户不能推荐自己
- 最多支持三级分销
- 推荐人必须是有效用户（状态为启用）

#### 1.5 异常处理
- 推荐人不存在：正常注册，不建立分销关系
- 推荐人已被禁用：正常注册，不建立分销关系
- 用户已存在：提示用户已注册，引导登录
- 系统异常：记录错误日志，稍后重试建立关系

---

### 2. 订单佣金计算流程

#### 2.1 流程概述
用户下单购买商品后，系统根据分销关系计算并分配佣金给相关推荐人。

#### 2.2 流程图
```mermaid
flowchart TD
    A[用户下单] --> B[订单支付成功]
    B --> C[获取用户的分销关系]
    C --> D{是否有推荐人}
    D -->|无| E[结束流程]
    D -->|有| F[获取分销配置]
    F --> G[计算一级佣金]
    G --> H[创建一级佣金记录]
    H --> I[更新推荐人累计佣金]
    I --> J{是否有二级推荐人}
    J -->|无| K[发送佣金通知]
    J -->|有| L[计算二级佣金]
    L --> M[创建二级佣金记录]
    M --> N[更新二级推荐人累计佣金]
    N --> O{是否有三级推荐人}
    O -->|无| K
    O -->|有| P[计算三级佣金]
    P --> Q[创建三级佣金记录]
    Q --> R[更新三级推荐人累计佣金]
    R --> K
    K --> S[记录操作日志]
    S --> T[结束流程]
```

#### 2.3 详细步骤

**步骤1: 订单触发**
- 用户完成订单支付
- 订单状态变更为"已支付"
- 触发佣金计算事件

**步骤2: 获取分销关系**
```sql
SELECT * FROM user_referrals 
WHERE user_id = ? AND status = 1 
ORDER BY level ASC;
```

**步骤3: 获取佣金配置**
```json
{
  "level1_rate": 0.05,  // 一级佣金比例 5%
  "level2_rate": 0.03,  // 二级佣金比例 3%
  "level3_rate": 0.01,  // 三级佣金比例 1%
  "min_order_amount": 1.00,  // 最小订单金额
  "max_commission": 1000.00  // 单笔最大佣金
}
```

**步骤4: 佣金计算公式**
```
佣金金额 = 订单金额 × 佣金比例
实际佣金 = MIN(佣金金额, 最大佣金限制)
```

**步骤5: 创建佣金记录**
- 插入到 `referral_commissions` 表
- 状态设置为"待结算"
- 记录订单ID、用户ID、推荐人ID等信息

**步骤6: 更新累计佣金**
- 更新 `user_referrals` 表的 `commission` 字段
- 使用事务确保数据一致性

#### 2.4 业务规则
- 只有已支付的订单才计算佣金
- 订单金额必须大于最小金额限制
- 佣金比例按分销等级递减
- 单笔佣金不能超过最大限制
- 退款订单需要扣减相应佣金

#### 2.5 异常处理
- 订单金额异常：记录错误日志，不计算佣金
- 分销关系异常：跳过该级别，继续计算下级
- 数据库异常：使用事务回滚，稍后重试
- 配置缺失：使用默认配置或停止计算

---

### 3. 佣金结算流程

#### 3.1 流程概述
定期将待结算的佣金转换为可提现余额，支持批量结算和手动结算。

#### 3.2 流程图
```mermaid
flowchart TD
    A[定时任务启动] --> B[获取待结算佣金]
    B --> C{是否有待结算记录}
    C -->|无| D[结束任务]
    C -->|有| E[按用户分组]
    E --> F[遍历每个用户]
    F --> G[计算用户总佣金]
    G --> H[检查结算条件]
    H --> I{是否满足结算条件}
    I -->|否| J[跳过该用户]
    I -->|是| K[创建结算记录]
    K --> L[更新佣金状态为已结算]
    L --> M[增加用户余额]
    M --> N[发送结算通知]
    N --> O[记录结算日志]
    O --> P{是否还有用户}
    P -->|是| F
    P -->|否| Q[生成结算报告]
    J --> P
    Q --> R[发送管理员通知]
    R --> S[结束任务]
    D --> S
```

#### 3.3 详细步骤

**步骤1: 获取待结算佣金**
```sql
SELECT referrer_id, SUM(amount) as total_amount, COUNT(*) as count
FROM referral_commissions 
WHERE status = 0 
AND created_at <= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY referrer_id
HAVING total_amount >= 10.00;  -- 最小结算金额
```

**步骤2: 结算条件检查**
- 佣金记录创建时间超过结算周期（默认7天）
- 累计佣金金额达到最小结算金额（默认10元）
- 用户账号状态正常
- 没有违规行为

**步骤3: 执行结算**
```sql
-- 开始事务
START TRANSACTION;

-- 更新佣金状态
UPDATE referral_commissions 
SET status = 1, settled_at = NOW() 
WHERE referrer_id = ? AND status = 0;

-- 增加用户余额
UPDATE users 
SET balance = balance + ? 
WHERE id = ?;

-- 创建余额变更记录
INSERT INTO user_balance_logs (...) VALUES (...);

-- 提交事务
COMMIT;
```

**步骤4: 通知发送**
- 站内消息通知
- 短信通知（可选）
- 邮件通知（可选）
- 微信公众号通知（可选）

#### 3.4 业务规则
- 结算周期：7天（可配置）
- 最小结算金额：10元（可配置）
- 结算时间：每日凌晨2点执行
- 结算状态：待结算(0) -> 已结算(1)
- 异常处理：失败的结算会在下次任务中重试

#### 3.5 手动结算
- 管理员可以手动触发结算
- 支持指定用户结算
- 支持紧急结算（跳过时间限制）
- 需要记录操作人和操作原因

---

### 4. 分销统计报表流程

#### 4.1 流程概述
生成各种分销统计报表，包括实时统计和定期报表。

#### 4.2 实时统计流程
```mermaid
flowchart TD
    A[用户请求统计数据] --> B[检查缓存]
    B --> C{缓存是否存在且有效}
    C -->|是| D[返回缓存数据]
    C -->|否| E[查询数据库]
    E --> F[计算统计指标]
    F --> G[格式化数据]
    G --> H[更新缓存]
    H --> I[返回统计数据]
    D --> J[记录访问日志]
    I --> J
    J --> K[结束]
```

#### 4.3 定期报表流程
```mermaid
flowchart TD
    A[定时任务启动] --> B[确定报表周期]
    B --> C[查询基础数据]
    C --> D[计算各项指标]
    D --> E[生成报表数据]
    E --> F[保存报表文件]
    F --> G[发送报表邮件]
    G --> H[更新报表状态]
    H --> I[清理过期报表]
    I --> J[结束任务]
```

#### 4.4 统计指标

**用户维度统计**
- 推荐用户总数
- 各级推荐用户数量
- 累计佣金收益
- 今日/本月/本年佣金
- 推荐转化率

**系统维度统计**
- 总分销用户数
- 总佣金支出
- 平均推荐数量
- 活跃推荐人数
- 分销渗透率

**时间维度统计**
- 日报：每日新增推荐、佣金统计
- 周报：周度趋势分析
- 月报：月度业绩汇总
- 年报：年度分销总结

#### 4.5 缓存策略
```
# 用户统计缓存（5分钟过期）
referral:user_stats:{user_id} -> {
  "total_referrals": 50,
  "today_commission": 120.00,
  "total_commission": 2580.50,
  "cache_time": 1640995200
}

# 系统统计缓存（1小时过期）
referral:system_stats -> {
  "total_users": 10000,
  "total_commission": 500000.00,
  "active_referrers": 2000,
  "cache_time": 1640995200
}
```

---

### 5. 分销关系管理流程

#### 5.1 关系查询流程
```mermaid
flowchart TD
    A[用户查询分销关系] --> B[验证用户权限]
    B --> C{权限验证通过}
    C -->|否| D[返回权限错误]
    C -->|是| E[查询用户分销关系]
    E --> F[获取关联用户信息]
    F --> G[计算统计数据]
    G --> H[格式化返回数据]
    H --> I[返回查询结果]
    D --> J[记录访问日志]
    I --> J
    J --> K[结束]
```

#### 5.2 关系修改流程
```mermaid
flowchart TD
    A[管理员修改分销关系] --> B[验证管理员权限]
    B --> C{权限验证通过}
    C -->|否| D[返回权限错误]
    C -->|是| E[验证修改参数]
    E --> F{参数验证通过}
    F -->|否| G[返回参数错误]
    F -->|是| H[检查业务规则]
    H --> I{业务规则检查通过}
    I -->|否| J[返回业务错误]
    I -->|是| K[执行修改操作]
    K --> L[记录操作日志]
    L --> M[发送变更通知]
    M --> N[返回操作结果]
    D --> O[结束]
    G --> O
    J --> O
    N --> O
```

#### 5.3 关系失效流程
```mermaid
flowchart TD
    A[触发关系失效事件] --> B[确定失效原因]
    B --> C[查询相关分销关系]
    C --> D[更新关系状态为无效]
    D --> E[处理未结算佣金]
    E --> F[通知相关用户]
    F --> G[记录失效日志]
    G --> H[更新统计数据]
    H --> I[结束流程]
```

---

### 6. 异常处理流程

#### 6.1 数据异常处理
```mermaid
flowchart TD
    A[检测到数据异常] --> B[记录异常日志]
    B --> C[分析异常类型]
    C --> D{是否为系统异常}
    D -->|是| E[触发系统告警]
    D -->|否| F[标记为业务异常]
    E --> G[通知技术团队]
    F --> H[通知业务团队]
    G --> I[执行修复操作]
    H --> J[执行业务处理]
    I --> K[验证修复结果]
    J --> L[验证处理结果]
    K --> M{修复是否成功}
    L --> N{处理是否成功}
    M -->|是| O[更新异常状态]
    M -->|否| P[升级异常等级]
    N -->|是| O
    N -->|否| P
    O --> Q[发送处理完成通知]
    P --> R[启动应急预案]
    Q --> S[结束流程]
    R --> S
```

#### 6.2 常见异常类型

**数据一致性异常**
- 分销关系与佣金记录不匹配
- 用户余额与佣金记录不一致
- 统计数据与明细数据不符

**业务逻辑异常**
- 用户自己推荐自己
- 分销等级超过限制
- 佣金比例配置错误

**系统异常**
- 数据库连接失败
- 缓存服务异常
- 第三方服务调用失败

#### 6.3 异常恢复策略

**自动恢复**
- 重试机制：失败操作自动重试3次
- 降级策略：关键服务异常时启用备用方案
- 数据修复：定时任务检查并修复数据异常

**手动干预**
- 人工审核：重要异常需要人工确认
- 数据回滚：严重异常时回滚到正常状态
- 紧急处理：关键业务异常的紧急处理流程

---

## 业务规则总结

### 1. 分销关系规则
- 每个用户最多只能有一个推荐人
- 分销关系一旦建立不可修改（除非管理员操作）
- 最多支持三级分销关系
- 用户不能推荐自己
- 推荐人必须是有效用户

### 2. 佣金计算规则
- 只有已支付订单才计算佣金
- 佣金比例按分销等级递减
- 单笔佣金不能超过最大限制
- 退款订单需要扣减相应佣金
- 佣金计算精确到分

### 3. 结算规则
- 佣金记录创建7天后才能结算
- 最小结算金额为10元
- 每日凌晨2点执行自动结算
- 结算失败的记录会在下次任务中重试
- 管理员可以手动触发结算

### 4. 统计规则
- 实时统计数据缓存5分钟
- 系统统计数据缓存1小时
- 报表数据每日凌晨生成
- 历史数据保留2年
- 统计异常时使用备用计算方法

### 5. 权限规则
- 用户只能查看自己的分销数据
- 管理员可以查看所有用户数据
- 分销关系修改需要管理员权限
- 敏感操作需要二次确认
- 所有操作都有详细的日志记录

---

## 性能优化建议

### 1. 数据库优化
- 合理设计索引，提高查询性能
- 使用分区表处理大数据量
- 定期清理历史数据
- 读写分离，统计查询使用从库

### 2. 缓存优化
- 热点数据使用Redis缓存
- 合理设置缓存过期时间
- 使用缓存预热提高命中率
- 缓存雪崩和穿透防护

### 3. 业务优化
- 异步处理佣金计算
- 批量处理结算操作
- 定时任务错峰执行
- 限流防止接口被刷

### 4. 监控优化
- 关键指标实时监控
- 异常情况及时告警
- 性能瓶颈定期分析
- 用户行为数据收集

通过以上详细的业务流程设计，分销模块能够稳定、高效地运行，为用户提供优质的分销服务体验。