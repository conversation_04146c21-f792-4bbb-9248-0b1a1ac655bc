# 分销配置使用指南

## 概述

本文档介绍如何使用新的分销配置功能，实现动态调整分销级别和佣金比例。系统支持最高三级分销，可以通过配置灵活开启不同级别的分销。

## 功能特性

- **动态配置**: 支持运行时动态调整分销级别，无需重启服务
- **多级分销**: 支持1-3级分销，可单独启用或禁用每个级别
- **佣金配置**: 支持为每个分销级别设置不同的佣金比例
- **配置验证**: 创建分销关系时自动验证级别是否启用
- **管理工具**: 提供命令行工具快速管理配置

## API接口

### 1. 获取分销级别配置

```http
GET /api/v1/user/secured/referral/config/levels
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "max_levels": 1,
    "enabled_levels": [
      {
        "level": 1,
        "enabled": true
      }
    ]
  }
}
```

### 2. 更新分销级别配置

```http
PUT /api/v1/user/secured/referral/config/levels
Authorization: Bearer <token>
Content-Type: application/json

{
  "max_levels": 3,
  "enabled_levels": [
    {"level": 1, "enabled": true},
    {"level": 2, "enabled": true},
    {"level": 3, "enabled": false}
  ]
}
```

### 3. 获取佣金比例配置

```http
GET /api/v1/user/secured/referral/config/commission-rates
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "rates": [
      {"level": 1, "rate": 0.05},
      {"level": 2, "rate": 0.03},
      {"level": 3, "rate": 0.02}
    ]
  }
}
```

### 4. 更新佣金比例配置

```http
PUT /api/v1/user/secured/referral/config/commission-rates
Authorization: Bearer <token>
Content-Type: application/json

{
  "rates": [
    {"level": 1, "rate": 0.08},
    {"level": 2, "rate": 0.05},
    {"level": 3, "rate": 0.03}
  ]
}
```

## 使用场景

### 场景1: 开启三级分销

当前系统默认只开启一级分销，如需开启三级分销：

1. **通过API开启:**
```bash
curl -X PUT "http://localhost:8080/api/v1/user/secured/referral/config/levels" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "max_levels": 3,
    "enabled_levels": [
      {"level": 1, "enabled": true},
      {"level": 2, "enabled": true},
      {"level": 3, "enabled": true}
    ]
  }'
```

2. **通过管理工具开启:**
```bash
cd modules/user/scripts
go run manage_referral_config.go -action=set-levels -levels=1,2,3
```

### 场景2: 调整佣金比例

调整各级分销的佣金比例：

```bash
go run manage_referral_config.go -action=set-commission -rates='{"level_1":0.08,"level_2":0.05,"level_3":0.03}'
```

### 场景3: 查看当前配置

```bash
go run manage_referral_config.go -action=get-config
```

## 数据库配置

### 配置表结构

分销配置存储在 `referral_configs` 表中：

```sql
CREATE TABLE `referral_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text NOT NULL,
  `config_type` varchar(50) NOT NULL DEFAULT 'string',
  `description` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
);
```

### 关键配置项

| 配置键 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| `max_levels` | int | 最大分销等级 | 1 |
| `level_1_enabled` | string | 一级分销是否启用 | true |
| `level_2_enabled` | string | 二级分销是否启用 | false |
| `level_3_enabled` | string | 三级分销是否启用 | false |
| `commission_rates` | json | 各级佣金比例 | {"level_1": 0.05, "level_2": 0.03, "level_3": 0.02} |

## 部署说明

### 1. 数据库迁移

执行迁移脚本创建配置表：

```bash
mysql -u root -p o_mall < modules/user/migrations/create_referral_configs_table.sql
```

### 2. 服务重启

更新代码后重启服务以加载新的路由和服务：

```bash
# 停止服务
pkill -f o_mall_backend

# 重新编译和启动
go build -o o_mall_backend main.go
./o_mall_backend
```

### 3. 配置验证

验证配置是否生效：

```bash
# 查看当前配置
go run modules/user/scripts/manage_referral_config.go -action=get-config

# 测试API接口
curl -X GET "http://localhost:8080/api/v1/user/secured/referral/config/levels" \
  -H "Authorization: Bearer <token>"
```

## 注意事项

1. **权限控制**: 分销配置相关接口需要管理员权限，建议添加权限验证中间件
2. **配置缓存**: 高频访问的配置建议添加缓存机制
3. **数据一致性**: 修改配置时注意保持数据一致性
4. **向下兼容**: 现有分销关系不受配置变更影响
5. **监控告警**: 建议对配置变更添加监控和告警

## 故障排查

### 常见问题

1. **分销关系创建失败**
   - 检查分销级别是否启用
   - 验证级别是否超过最大允许级别

2. **配置更新失败**
   - 检查数据库连接
   - 验证配置格式是否正确

3. **API接口访问失败**
   - 检查路由是否正确注册
   - 验证JWT token是否有效

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log | grep referral

# 查看数据库日志
tail -f /var/log/mysql/mysql.log
```

## 扩展功能

### 未来可扩展的功能

1. **自动升级**: 根据推荐人数自动升级分销等级
2. **时间限制**: 为分销关系设置有效期
3. **地域限制**: 根据地域设置不同的分销策略
4. **动态佣金**: 根据业绩动态调整佣金比例
5. **分销报表**: 提供详细的分销数据分析报表

### 性能优化建议

1. **配置缓存**: 使用Redis缓存热点配置
2. **批量操作**: 支持批量更新分销关系
3. **异步处理**: 大量数据操作使用异步处理
4. **数据库优化**: 添加合适的索引和分区