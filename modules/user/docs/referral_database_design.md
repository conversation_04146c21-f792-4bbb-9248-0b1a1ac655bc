# 分销模块数据库设计文档

## 概述

本文档详细描述了分销模块的数据库设计，包括表结构、索引设计、约束条件、性能优化等方面。分销模块主要涉及用户分销关系的管理，支持多级分销体系和佣金计算。

## 表结构设计

### 1. 用户分销关系表 (user_referrals)

**表名**: `user_referrals`

**表描述**: 存储用户之间的分销推荐关系，支持多级分销体系

**字段定义**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|----------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，分销关系唯一标识 |
| user_id | BIGINT | - | NOT NULL | - | 被推荐用户ID，外键关联users表 |
| referrer_id | BIGINT | - | NOT NULL | - | 推荐人用户ID，外键关联users表 |
| level | TINYINT | - | NOT NULL | 1 | 分销等级：1-一级，2-二级，3-三级 |
| status | TINYINT | - | NOT NULL | 1 | 关系状态：0-无效，1-有效 |
| commission | DECIMAL | 10,2 | NOT NULL | 0.00 | 累计佣金金额 |
| referral_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 推荐建立时间 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 记录更新时间 |

**建表SQL**:
```sql
CREATE TABLE `user_referrals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，分销关系唯一标识',
  `user_id` bigint(20) NOT NULL COMMENT '被推荐用户ID',
  `referrer_id` bigint(20) NOT NULL COMMENT '推荐人用户ID',
  `level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分销等级：1-一级，2-二级，3-三级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '关系状态：0-无效，1-有效',
  `commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计佣金金额',
  `referral_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '推荐建立时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_referrer` (`user_id`, `referrer_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_level_status` (`level`, `status`),
  KEY `idx_referral_time` (`referral_time`),
  KEY `idx_status_created` (`status`, `created_at`),
  CONSTRAINT `fk_user_referrals_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_referrals_referrer_id` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户分销关系表';
```

### 2. 分销佣金记录表 (referral_commissions)

**表名**: `referral_commissions`

**表描述**: 记录分销佣金的详细变更历史，便于追踪和对账

**字段定义**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|----------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，佣金记录唯一标识 |
| referral_id | BIGINT | - | NOT NULL | - | 分销关系ID，外键关联user_referrals表 |
| order_id | BIGINT | - | NULL | - | 关联订单ID，外键关联orders表 |
| user_id | BIGINT | - | NOT NULL | - | 产生佣金的用户ID |
| referrer_id | BIGINT | - | NOT NULL | - | 获得佣金的推荐人ID |
| amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 佣金金额 |
| commission_rate | DECIMAL | 5,4 | NOT NULL | 0.0000 | 佣金比例 |
| order_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 订单金额 |
| commission_type | TINYINT | - | NOT NULL | 1 | 佣金类型：1-订单佣金，2-注册奖励，3-活动奖励 |
| status | TINYINT | - | NOT NULL | 1 | 佣金状态：0-待结算，1-已结算，2-已取消 |
| description | VARCHAR | 255 | NULL | - | 佣金描述 |
| settled_at | DATETIME | - | NULL | - | 结算时间 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 记录更新时间 |

**建表SQL**:
```sql
CREATE TABLE `referral_commissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，佣金记录唯一标识',
  `referral_id` bigint(20) NOT NULL COMMENT '分销关系ID',
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联订单ID',
  `user_id` bigint(20) NOT NULL COMMENT '产生佣金的用户ID',
  `referrer_id` bigint(20) NOT NULL COMMENT '获得佣金的推荐人ID',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '佣金金额',
  `commission_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `commission_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '佣金类型：1-订单佣金，2-注册奖励，3-活动奖励',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '佣金状态：0-待结算，1-已结算，2-已取消',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '佣金描述',
  `settled_at` datetime DEFAULT NULL COMMENT '结算时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_referral_id` (`referral_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_status_created` (`status`, `created_at`),
  KEY `idx_commission_type` (`commission_type`),
  KEY `idx_settled_at` (`settled_at`),
  CONSTRAINT `fk_referral_commissions_referral_id` FOREIGN KEY (`referral_id`) REFERENCES `user_referrals` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_referral_commissions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_referral_commissions_referrer_id` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销佣金记录表';
```

### 3. 分销配置表 (referral_configs)

**表名**: `referral_configs`

**表描述**: 存储分销系统的配置参数，如佣金比例、等级设置等

**字段定义**:

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|----------|------|----------|--------|---------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，配置唯一标识 |
| config_key | VARCHAR | 100 | NOT NULL | - | 配置键名 |
| config_value | TEXT | - | NOT NULL | - | 配置值（JSON格式） |
| config_type | VARCHAR | 50 | NOT NULL | - | 配置类型：commission_rate, level_config, etc |
| description | VARCHAR | 255 | NULL | - | 配置描述 |
| status | TINYINT | - | NOT NULL | 1 | 配置状态：0-禁用，1-启用 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 记录更新时间 |

**建表SQL**:
```sql
CREATE TABLE `referral_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，配置唯一标识',
  `config_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键名',
  `config_value` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值（JSON格式）',
  `config_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置类型',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '配置状态：0-禁用，1-启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销配置表';
```

## 索引设计

### 1. 主键索引
- 所有表都使用自增BIGINT作为主键
- 确保数据的唯一性和查询性能

### 2. 唯一索引
- `user_referrals.uk_user_referrer`: 防止重复的分销关系
- `referral_configs.uk_config_key`: 确保配置键名唯一

### 3. 普通索引

#### user_referrals表索引
- `idx_user_id`: 快速查找用户的推荐人
- `idx_referrer_id`: 快速查找推荐人的下级用户
- `idx_level_status`: 按等级和状态查询
- `idx_referral_time`: 按推荐时间排序
- `idx_status_created`: 按状态和创建时间查询

#### referral_commissions表索引
- `idx_referral_id`: 关联分销关系查询
- `idx_order_id`: 关联订单查询
- `idx_user_id`: 按用户查询佣金记录
- `idx_referrer_id`: 按推荐人查询佣金记录
- `idx_status_created`: 按状态和时间查询
- `idx_commission_type`: 按佣金类型查询
- `idx_settled_at`: 按结算时间查询

### 4. 复合索引设计原则
- 将查询频率高的字段放在前面
- 考虑查询的选择性，选择性高的字段优先
- 避免过多的复合索引，平衡查询性能和写入性能

## 约束条件

### 1. 外键约束
- 确保数据的引用完整性
- 使用CASCADE删除策略，避免孤立数据

### 2. 检查约束
```sql
-- 分销等级约束
ALTER TABLE user_referrals ADD CONSTRAINT chk_level CHECK (level BETWEEN 1 AND 3);

-- 状态约束
ALTER TABLE user_referrals ADD CONSTRAINT chk_status CHECK (status IN (0, 1));

-- 佣金金额约束
ALTER TABLE user_referrals ADD CONSTRAINT chk_commission CHECK (commission >= 0);
ALTER TABLE referral_commissions ADD CONSTRAINT chk_amount CHECK (amount >= 0);

-- 佣金比例约束
ALTER TABLE referral_commissions ADD CONSTRAINT chk_commission_rate CHECK (commission_rate BETWEEN 0 AND 1);

-- 防止自己推荐自己
ALTER TABLE user_referrals ADD CONSTRAINT chk_not_self_referral CHECK (user_id != referrer_id);
```

### 3. 业务约束
- 每个用户只能有一个有效的推荐人
- 分销等级不能超过3级
- 佣金金额和比例必须为非负数

## 数据初始化

### 1. 分销配置初始化
```sql
-- 插入默认分销配置
INSERT INTO referral_configs (config_key, config_value, config_type, description) VALUES
('commission_rates', '{"level1": 0.05, "level2": 0.03, "level3": 0.01}', 'commission_rate', '各级分销佣金比例'),
('max_levels', '3', 'level_config', '最大分销等级'),
('register_reward', '10.00', 'reward_config', '注册奖励金额'),
('min_order_amount', '1.00', 'order_config', '最小订单金额'),
('commission_settle_days', '7', 'settle_config', '佣金结算天数');
```

### 2. 测试数据
```sql
-- 插入测试分销关系（仅用于开发环境）
INSERT INTO user_referrals (user_id, referrer_id, level, status, commission, referral_time) VALUES
(2, 1, 1, 1, 150.50, '2024-01-15 10:30:00'),
(3, 1, 1, 1, 89.20, '2024-01-16 14:20:00'),
(4, 2, 2, 1, 45.30, '2024-01-17 09:15:00'),
(5, 3, 2, 1, 67.80, '2024-01-18 16:45:00');
```

## 性能优化

### 1. 查询优化

#### 常用查询及优化
```sql
-- 查询用户的直接推荐列表（已优化）
SELECT ur.*, u.username, u.nickname, u.avatar 
FROM user_referrals ur 
JOIN users u ON ur.user_id = u.id 
WHERE ur.referrer_id = ? AND ur.level = 1 AND ur.status = 1 
ORDER BY ur.created_at DESC 
LIMIT ?, ?;

-- 查询用户的分销统计（已优化）
SELECT 
  level,
  COUNT(*) as count,
  SUM(commission) as total_commission
FROM user_referrals 
WHERE referrer_id = ? AND status = 1 
GROUP BY level;

-- 查询用户的佣金明细（已优化）
SELECT rc.*, ur.level 
FROM referral_commissions rc 
JOIN user_referrals ur ON rc.referral_id = ur.id 
WHERE rc.referrer_id = ? AND rc.status = 1 
ORDER BY rc.created_at DESC 
LIMIT ?, ?;
```

### 2. 分区策略

#### 按时间分区（适用于大数据量）
```sql
-- 对佣金记录表按月分区
ALTER TABLE referral_commissions 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
  PARTITION p202401 VALUES LESS THAN (202402),
  PARTITION p202402 VALUES LESS THAN (202403),
  PARTITION p202403 VALUES LESS THAN (202404),
  -- 继续添加更多分区
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. 缓存策略

#### Redis缓存设计
```
# 用户分销统计缓存
referral:stats:{user_id} -> {
  "total_referrals": 50,
  "level1_referrals": 30,
  "level2_referrals": 15,
  "level3_referrals": 5,
  "total_commission": 2580.50
}

# 用户推荐关系缓存
referral:relation:{user_id} -> {
  "referrer_id": 123,
  "level": 1,
  "status": 1
}

# 分销配置缓存
referral:config:{config_key} -> config_value
```

### 4. 读写分离
- 统计查询使用只读从库
- 写操作使用主库
- 通过中间件实现自动路由

## 数据备份与恢复

### 1. 备份策略
```bash
# 每日全量备份
mysqldump --single-transaction --routines --triggers \
  --databases mall_db \
  --tables user_referrals referral_commissions referral_configs \
  > referral_backup_$(date +%Y%m%d).sql

# 增量备份（基于binlog）
mysqlbinlog --start-datetime="2024-01-01 00:00:00" \
  --stop-datetime="2024-01-01 23:59:59" \
  mysql-bin.000001 > incremental_backup.sql
```

### 2. 恢复策略
```bash
# 恢复全量备份
mysql -u root -p mall_db < referral_backup_20240101.sql

# 恢复增量备份
mysql -u root -p mall_db < incremental_backup.sql
```

## 监控与维护

### 1. 性能监控
```sql
-- 监控慢查询
SELECT 
  query_time,
  lock_time,
  rows_sent,
  rows_examined,
  sql_text
FROM mysql.slow_log 
WHERE sql_text LIKE '%user_referrals%' 
ORDER BY query_time DESC;

-- 监控表大小
SELECT 
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
  table_rows
FROM information_schema.tables 
WHERE table_schema = 'mall_db' 
AND table_name IN ('user_referrals', 'referral_commissions', 'referral_configs');
```

### 2. 数据清理
```sql
-- 清理过期的佣金记录（保留2年）
DELETE FROM referral_commissions 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR) 
AND status = 2;

-- 清理无效的分销关系
UPDATE user_referrals 
SET status = 0 
WHERE user_id IN (
  SELECT id FROM users WHERE status = 0
) OR referrer_id IN (
  SELECT id FROM users WHERE status = 0
);
```

### 3. 数据一致性检查
```sql
-- 检查分销关系数据一致性
SELECT 
  ur.id,
  ur.user_id,
  ur.referrer_id,
  ur.commission,
  COALESCE(SUM(rc.amount), 0) as calculated_commission
FROM user_referrals ur
LEFT JOIN referral_commissions rc ON ur.id = rc.referral_id AND rc.status = 1
GROUP BY ur.id
HAVING ABS(ur.commission - calculated_commission) > 0.01;

-- 检查孤立的佣金记录
SELECT rc.*
FROM referral_commissions rc
LEFT JOIN user_referrals ur ON rc.referral_id = ur.id
WHERE ur.id IS NULL;
```

## 扩展性考虑

### 1. 水平分片
- 按用户ID进行分片
- 使用一致性哈希算法
- 考虑跨分片查询的复杂性

### 2. 垂直拆分
- 将历史数据迁移到归档表
- 按业务功能拆分表结构
- 使用视图统一查询接口

### 3. 新功能扩展
- 预留扩展字段
- 使用JSON字段存储灵活配置
- 考虑向后兼容性

## 安全性考虑

### 1. 数据加密
- 敏感字段使用AES加密
- 数据库连接使用SSL
- 定期更换加密密钥

### 2. 访问控制
- 最小权限原则
- 定期审计数据库访问日志
- 使用专用数据库用户

### 3. 数据脱敏
- 开发环境使用脱敏数据
- 生产数据导出时自动脱敏
- 日志中避免记录敏感信息