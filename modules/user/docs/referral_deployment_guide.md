# 分销模块部署运维指南

## 概述

本文档提供了分销模块的完整部署和运维指南，包括环境配置、部署流程、监控告警、性能优化、故障处理等内容。确保分销系统在生产环境中稳定、高效地运行。

## 部署架构

### 1. 系统架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx/ALB]
    end
    
    subgraph "应用层"
        APP1[应用实例1]
        APP2[应用实例2]
        APP3[应用实例3]
    end
    
    subgraph "缓存层"
        REDIS1[Redis主]
        REDIS2[Redis从]
    end
    
    subgraph "数据库层"
        DB1[MySQL主库]
        DB2[MySQL从库1]
        DB3[MySQL从库2]
    end
    
    subgraph "消息队列"
        MQ[RabbitMQ/Kafka]
    end
    
    subgraph "监控层"
        PROM[Prometheus]
        GRAF[Grafana]
        ALERT[AlertManager]
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> REDIS1
    APP2 --> REDIS1
    APP3 --> REDIS1
    
    REDIS1 --> REDIS2
    
    APP1 --> DB1
    APP2 --> DB1
    APP3 --> DB1
    
    DB1 --> DB2
    DB1 --> DB3
    
    APP1 --> MQ
    APP2 --> MQ
    APP3 --> MQ
    
    PROM --> APP1
    PROM --> APP2
    PROM --> APP3
    PROM --> DB1
    PROM --> REDIS1
    
    GRAF --> PROM
    ALERT --> PROM
```

### 2. 部署环境规划

#### 2.1 环境分类

| 环境 | 用途 | 配置 | 数据库 | 缓存 |
|------|------|------|--------|------|
| 开发环境 | 日常开发测试 | 2C4G | MySQL单机 | Redis单机 |
| 测试环境 | 功能测试验证 | 4C8G | MySQL主从 | Redis单机 |
| 预发环境 | 生产前验证 | 8C16G | MySQL主从 | Redis主从 |
| 生产环境 | 线上服务 | 16C32G | MySQL集群 | Redis集群 |

#### 2.2 服务器配置要求

**应用服务器**
- CPU: 8核以上
- 内存: 16GB以上
- 磁盘: SSD 100GB以上
- 网络: 千兆网卡
- 操作系统: CentOS 7.x / Ubuntu 18.04+

**数据库服务器**
- CPU: 16核以上
- 内存: 32GB以上
- 磁盘: SSD 500GB以上
- 网络: 万兆网卡
- 操作系统: CentOS 7.x

**缓存服务器**
- CPU: 8核以上
- 内存: 32GB以上
- 磁盘: SSD 100GB以上
- 网络: 千兆网卡
- 操作系统: CentOS 7.x

---

## 环境配置

### 1. 基础环境安装

#### 1.1 Go 环境安装
```bash
#!/bin/bash
# install_go.sh - Go环境安装脚本

# 下载Go
wget https://golang.org/dl/go1.19.linux-amd64.tar.gz

# 解压安装
sudo tar -C /usr/local -xzf go1.19.linux-amd64.tar.gz

# 配置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
echo 'export GOPROXY=https://goproxy.cn,direct' >> ~/.bashrc

source ~/.bashrc

# 验证安装
go version
```

#### 1.2 MySQL 安装配置
```bash
#!/bin/bash
# install_mysql.sh - MySQL安装脚本

# 安装MySQL 8.0
sudo yum install -y mysql-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码
sudo grep 'temporary password' /var/log/mysqld.log

# 安全配置
sudo mysql_secure_installation
```

**MySQL 配置文件 (/etc/my.cnf)**
```ini
[mysqld]
# 基础配置
port = 3306
socket = /var/lib/mysql/mysql.sock
datadir = /var/lib/mysql
log-error = /var/log/mysqld.log
pid-file = /var/run/mysqld/mysqld.pid

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# InnoDB配置
innodb_buffer_pool_size = 16G
innodb_log_file_size = 1G
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1

# 连接配置
max_connections = 1000
max_connect_errors = 1000
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7

# 主从复制配置
server-id = 1
log-slave-updates = 1
gtid-mode = ON
enforce-gtid-consistency = ON
```

#### 1.3 Redis 安装配置
```bash
#!/bin/bash
# install_redis.sh - Redis安装脚本

# 安装Redis
sudo yum install -y epel-release
sudo yum install -y redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

**Redis 配置文件 (/etc/redis.conf)**
```conf
# 网络配置
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 300
tcp-keepalive 300

# 内存配置
maxmemory 8gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 安全配置
requirepass your_redis_password

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log
```

### 2. 应用配置

#### 2.1 配置文件模板
```yaml
# config/production.yaml - 生产环境配置
server:
  port: 8080
  mode: release
  read_timeout: 30s
  write_timeout: 30s
  max_header_bytes: 1048576

database:
  driver: mysql
  master:
    host: mysql-master.internal
    port: 3306
    username: app_user
    password: ${DB_PASSWORD}
    database: mall_db
    charset: utf8mb4
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600s
  slaves:
    - host: mysql-slave1.internal
      port: 3306
      username: app_user
      password: ${DB_PASSWORD}
      database: mall_db
      charset: utf8mb4
      max_idle_conns: 5
      max_open_conns: 50
      conn_max_lifetime: 3600s
    - host: mysql-slave2.internal
      port: 3306
      username: app_user
      password: ${DB_PASSWORD}
      database: mall_db
      charset: utf8mb4
      max_idle_conns: 5
      max_open_conns: 50
      conn_max_lifetime: 3600s

redis:
  master:
    host: redis-master.internal
    port: 6379
    password: ${REDIS_PASSWORD}
    database: 0
    pool_size: 100
    min_idle_conns: 10
    dial_timeout: 5s
    read_timeout: 3s
    write_timeout: 3s
    pool_timeout: 4s
  slaves:
    - host: redis-slave1.internal
      port: 6379
      password: ${REDIS_PASSWORD}
      database: 0
      pool_size: 50
      min_idle_conns: 5

referral:
  commission_rates:
    level1: 0.05  # 一级佣金比例 5%
    level2: 0.03  # 二级佣金比例 3%
    level3: 0.01  # 三级佣金比例 1%
  settlement:
    min_amount: 10.00      # 最小结算金额
    settlement_days: 7     # 结算周期（天）
    max_commission: 1000.00 # 单笔最大佣金
  cache:
    user_stats_ttl: 300    # 用户统计缓存时间（秒）
    system_stats_ttl: 3600 # 系统统计缓存时间（秒）

logging:
  level: info
  format: json
  output: /var/log/app/app.log
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: true

monitoring:
  metrics:
    enabled: true
    path: /metrics
  health:
    enabled: true
    path: /health
  pprof:
    enabled: false
    path: /debug/pprof
```

#### 2.2 环境变量配置
```bash
# .env.production - 生产环境变量

# 数据库配置
DB_PASSWORD=your_secure_db_password
DB_ENCRYPT_KEY=your_32_char_encryption_key_here

# Redis配置
REDIS_PASSWORD=your_secure_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE_HOURS=24

# 短信配置
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key
SMS_SIGN_NAME=your_sms_sign

# 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# 监控配置
PROMETHEUS_PUSHGATEWAY=http://prometheus-pushgateway:9091

# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_LOG_LEVEL=info
```

---

## 部署流程

### 1. 自动化部署脚本

#### 1.1 构建脚本
```bash
#!/bin/bash
# build.sh - 应用构建脚本

set -e

echo "开始构建应用..."

# 设置变量
APP_NAME="mall-backend"
VERSION=${1:-$(git rev-parse --short HEAD)}
BUILD_TIME=$(date +"%Y-%m-%d %H:%M:%S")
GIT_COMMIT=$(git rev-parse HEAD)

# 清理旧的构建文件
rm -rf dist/
mkdir -p dist/

# 下载依赖
echo "下载依赖..."
go mod download
go mod verify

# 运行测试
echo "运行测试..."
go test -v ./...

# 构建应用
echo "构建应用..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags "-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT}" \
    -o dist/${APP_NAME} \
    ./cmd/server

# 复制配置文件
cp -r config/ dist/
cp -r migrations/ dist/

# 创建部署包
echo "创建部署包..."
tar -czf dist/${APP_NAME}-${VERSION}.tar.gz -C dist/ .

echo "构建完成: ${APP_NAME}-${VERSION}.tar.gz"
```

#### 1.2 部署脚本
```bash
#!/bin/bash
# deploy.sh - 应用部署脚本

set -e

# 配置变量
APP_NAME="mall-backend"
APP_USER="app"
APP_DIR="/opt/${APP_NAME}"
SERVICE_NAME="${APP_NAME}"
VERSION=${1:-"latest"}
ENVIRONMENT=${2:-"production"}

echo "开始部署 ${APP_NAME} 版本 ${VERSION} 到 ${ENVIRONMENT} 环境..."

# 检查参数
if [ -z "$VERSION" ]; then
    echo "错误: 请指定版本号"
    exit 1
fi

# 下载部署包
echo "下载部署包..."
wget -O /tmp/${APP_NAME}-${VERSION}.tar.gz \
    "https://releases.example.com/${APP_NAME}/${VERSION}/${APP_NAME}-${VERSION}.tar.gz"

# 停止服务
echo "停止服务..."
sudo systemctl stop ${SERVICE_NAME} || true

# 备份当前版本
if [ -d "${APP_DIR}" ]; then
    echo "备份当前版本..."
    sudo mv ${APP_DIR} ${APP_DIR}.backup.$(date +%Y%m%d_%H%M%S)
fi

# 创建应用目录
echo "创建应用目录..."
sudo mkdir -p ${APP_DIR}
sudo chown ${APP_USER}:${APP_USER} ${APP_DIR}

# 解压部署包
echo "解压部署包..."
sudo -u ${APP_USER} tar -xzf /tmp/${APP_NAME}-${VERSION}.tar.gz -C ${APP_DIR}

# 设置权限
sudo chmod +x ${APP_DIR}/${APP_NAME}

# 复制环境配置
echo "复制环境配置..."
sudo cp /etc/${APP_NAME}/.env.${ENVIRONMENT} ${APP_DIR}/.env
sudo chown ${APP_USER}:${APP_USER} ${APP_DIR}/.env

# 运行数据库迁移
echo "运行数据库迁移..."
sudo -u ${APP_USER} ${APP_DIR}/${APP_NAME} migrate --config ${APP_DIR}/config/${ENVIRONMENT}.yaml

# 启动服务
echo "启动服务..."
sudo systemctl start ${SERVICE_NAME}
sudo systemctl enable ${SERVICE_NAME}

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 健康检查
echo "执行健康检查..."
for i in {1..30}; do
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "服务启动成功!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "错误: 服务启动失败"
        sudo systemctl status ${SERVICE_NAME}
        exit 1
    fi
    echo "等待服务启动... ($i/30)"
    sleep 2
done

# 清理临时文件
rm -f /tmp/${APP_NAME}-${VERSION}.tar.gz

echo "部署完成!"
```

### 2. Systemd 服务配置

```ini
# /etc/systemd/system/mall-backend.service
[Unit]
Description=Mall Backend Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=app
Group=app
WorkingDirectory=/opt/mall-backend
ExecStart=/opt/mall-backend/mall-backend --config /opt/mall-backend/config/production.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StartLimitInterval=0

# 环境变量
EnvironmentFile=/opt/mall-backend/.env

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/mall-backend/logs

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mall-backend

[Install]
WantedBy=multi-user.target
```

### 3. Nginx 配置

```nginx
# /etc/nginx/sites-available/mall-backend
upstream mall_backend {
    least_conn;
    server *********:8080 max_fails=3 fail_timeout=30s;
    server *********:8080 max_fails=3 fail_timeout=30s;
    server *********:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 80;
    server_name api.example.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.example.com;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/api.example.com.crt;
    ssl_certificate_key /etc/ssl/private/api.example.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/mall-backend.access.log main;
    error_log /var/log/nginx/mall-backend.error.log;
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    # 客户端配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 代理配置
    location / {
        proxy_pass http://mall_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://mall_backend;
        access_log off;
    }
    
    # 监控指标
    location /metrics {
        proxy_pass http://mall_backend;
        allow 10.0.0.0/8;
        deny all;
    }
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

---

## 监控告警

### 1. Prometheus 配置

#### 1.1 Prometheus 配置文件
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 应用监控
  - job_name: 'mall-backend'
    static_configs:
      - targets:
        - '*********:8080'
        - '*********:8080'
        - '*********:8080'
    metrics_path: '/metrics'
    scrape_interval: 30s
    
  # MySQL监控
  - job_name: 'mysql'
    static_configs:
      - targets:
        - '*********:9104'
        - '*********:9104'
    
  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets:
        - '*********:9121'
        - '*********:9121'
    
  # 系统监控
  - job_name: 'node'
    static_configs:
      - targets:
        - '*********:9100'
        - '*********:9100'
        - '*********:9100'
        - '*********:9100'
        - '*********:9100'
        - '*********:9100'
        - '*********:9100'
```

#### 1.2 告警规则配置
```yaml
# rules/referral_alerts.yml
groups:
  - name: referral_module
    rules:
      # 应用可用性告警
      - alert: ReferralServiceDown
        expr: up{job="mall-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: referral
        annotations:
          summary: "分销服务不可用"
          description: "实例 {{ $labels.instance }} 已经下线超过1分钟"
      
      # 响应时间告警
      - alert: ReferralHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="mall-backend",handler=~"/api/v1/user/referral.*"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: referral
        annotations:
          summary: "分销接口响应时间过高"
          description: "95%的请求响应时间超过1秒，当前值: {{ $value }}秒"
      
      # 错误率告警
      - alert: ReferralHighErrorRate
        expr: rate(http_requests_total{job="mall-backend",handler=~"/api/v1/user/referral.*",status=~"5.."}[5m]) / rate(http_requests_total{job="mall-backend",handler=~"/api/v1/user/referral.*"}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: referral
        annotations:
          summary: "分销接口错误率过高"
          description: "5xx错误率超过5%，当前值: {{ $value | humanizePercentage }}"
      
      # 数据库连接告警
      - alert: DatabaseConnectionHigh
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "数据库连接数过高"
          description: "数据库连接使用率超过80%，当前值: {{ $value | humanizePercentage }}"
      
      # Redis内存告警
      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过90%，当前值: {{ $value | humanizePercentage }}"
      
      # 分销业务告警
      - alert: ReferralCommissionCalculationFailed
        expr: increase(referral_commission_calculation_errors_total[5m]) > 10
        for: 1m
        labels:
          severity: critical
          service: referral
        annotations:
          summary: "佣金计算失败次数过多"
          description: "5分钟内佣金计算失败{{ $value }}次"
      
      - alert: ReferralSettlementFailed
        expr: increase(referral_settlement_errors_total[5m]) > 5
        for: 1m
        labels:
          severity: critical
          service: referral
        annotations:
          summary: "佣金结算失败次数过多"
          description: "5分钟内佣金结算失败{{ $value }}次"
```

### 2. Grafana 仪表板

#### 2.1 分销模块仪表板配置
```json
{
  "dashboard": {
    "id": null,
    "title": "分销模块监控",
    "tags": ["referral", "business"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "分销接口QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{handler=~\"/api/v1/user/referral.*\"}[5m])",
            "legendFormat": "{{handler}}"
          }
        ],
        "yAxes": [
          {
            "label": "请求/秒"
          }
        ]
      },
      {
        "id": 2,
        "title": "分销接口响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{handler=~\"/api/v1/user/referral.*\"}[5m]))",
            "legendFormat": "P50"
          },
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{handler=~\"/api/v1/user/referral.*\"}[5m]))",
            "legendFormat": "P95"
          },
          {
            "expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{handler=~\"/api/v1/user/referral.*\"}[5m]))",
            "legendFormat": "P99"
          }
        ],
        "yAxes": [
          {
            "label": "秒"
          }
        ]
      },
      {
        "id": 3,
        "title": "分销业务指标",
        "type": "singlestat",
        "targets": [
          {
            "expr": "referral_total_users",
            "legendFormat": "总分销用户数"
          },
          {
            "expr": "referral_total_commission",
            "legendFormat": "总佣金金额"
          },
          {
            "expr": "rate(referral_new_users_total[1h])",
            "legendFormat": "新增分销用户/小时"
          }
        ]
      },
      {
        "id": 4,
        "title": "数据库性能",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(mysql_global_status_queries[5m])",
            "legendFormat": "查询QPS"
          },
          {
            "expr": "mysql_global_status_slow_queries",
            "legendFormat": "慢查询数"
          }
        ]
      },
      {
        "id": 5,
        "title": "Redis性能",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(redis_commands_processed_total[5m])",
            "legendFormat": "命令执行率"
          },
          {
            "expr": "redis_connected_clients",
            "legendFormat": "连接数"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

### 3. 应用指标埋点

#### 3.1 业务指标定义
```go
// metrics/referral_metrics.go
package metrics

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // 分销用户总数
    ReferralTotalUsers = promauto.NewGauge(prometheus.GaugeOpts{
        Name: "referral_total_users",
        Help: "Total number of referral users",
    })
    
    // 总佣金金额
    ReferralTotalCommission = promauto.NewGauge(prometheus.GaugeOpts{
        Name: "referral_total_commission",
        Help: "Total commission amount",
    })
    
    // 新增分销用户计数器
    ReferralNewUsersTotal = promauto.NewCounter(prometheus.CounterOpts{
        Name: "referral_new_users_total",
        Help: "Total number of new referral users",
    })
    
    // 佣金计算错误计数器
    ReferralCommissionCalculationErrors = promauto.NewCounter(prometheus.CounterOpts{
        Name: "referral_commission_calculation_errors_total",
        Help: "Total number of commission calculation errors",
    })
    
    // 佣金结算错误计数器
    ReferralSettlementErrors = promauto.NewCounter(prometheus.CounterOpts{
        Name: "referral_settlement_errors_total",
        Help: "Total number of settlement errors",
    })
    
    // 分销接口响应时间
    ReferralAPIResponseTime = promauto.NewHistogramVec(prometheus.HistogramOpts{
        Name: "referral_api_response_time_seconds",
        Help: "Response time of referral APIs",
        Buckets: prometheus.DefBuckets,
    }, []string{"method", "endpoint"})
    
    // 数据库操作响应时间
    ReferralDBResponseTime = promauto.NewHistogramVec(prometheus.HistogramOpts{
        Name: "referral_db_response_time_seconds",
        Help: "Response time of database operations",
        Buckets: prometheus.DefBuckets,
    }, []string{"operation", "table"})
)

// UpdateReferralMetrics 更新分销指标
func UpdateReferralMetrics(totalUsers int, totalCommission float64) {
    ReferralTotalUsers.Set(float64(totalUsers))
    ReferralTotalCommission.Set(totalCommission)
}

// RecordNewReferralUser 记录新增分销用户
func RecordNewReferralUser() {
    ReferralNewUsersTotal.Inc()
}

// RecordCommissionCalculationError 记录佣金计算错误
func RecordCommissionCalculationError() {
    ReferralCommissionCalculationErrors.Inc()
}

// RecordSettlementError 记录结算错误
func RecordSettlementError() {
    ReferralSettlementErrors.Inc()
}
```

#### 3.2 中间件集成
```go
// middleware/metrics.go
package middleware

import (
    "time"
    "github.com/gin-gonic/gin"
    "your-project/metrics"
)

// MetricsMiddleware 指标收集中间件
func MetricsMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start)
        method := c.Request.Method
        endpoint := c.FullPath()
        
        // 记录API响应时间
        if strings.Contains(endpoint, "/referral") {
            metrics.ReferralAPIResponseTime.WithLabelValues(method, endpoint).Observe(duration.Seconds())
        }
    }
}
```

---

## 性能优化

### 1. 数据库优化

#### 1.1 索引优化
```sql
-- 分销关系表索引优化
CREATE INDEX idx_user_referrals_user_id ON user_referrals(user_id);
CREATE INDEX idx_user_referrals_referrer_id ON user_referrals(referrer_id);
CREATE INDEX idx_user_referrals_level_status ON user_referrals(level, status);
CREATE INDEX idx_user_referrals_created_at ON user_referrals(created_at);

-- 佣金记录表索引优化
CREATE INDEX idx_referral_commissions_referrer_id ON referral_commissions(referrer_id);
CREATE INDEX idx_referral_commissions_order_id ON referral_commissions(order_id);
CREATE INDEX idx_referral_commissions_status ON referral_commissions(status);
CREATE INDEX idx_referral_commissions_created_at ON referral_commissions(created_at);

-- 复合索引
CREATE INDEX idx_user_referrals_referrer_level ON user_referrals(referrer_id, level);
CREATE INDEX idx_referral_commissions_referrer_status ON referral_commissions(referrer_id, status);
```

#### 1.2 查询优化
```sql
-- 优化前：查询用户的所有推荐关系
SELECT * FROM user_referrals WHERE referrer_id = 1;

-- 优化后：只查询必要字段，添加条件
SELECT user_id, level, commission, created_at 
FROM user_referrals 
WHERE referrer_id = 1 AND status = 1 
ORDER BY created_at DESC 
LIMIT 20;

-- 优化前：统计分销数据
SELECT 
    COUNT(*) as total_referrals,
    SUM(commission) as total_commission
FROM user_referrals 
WHERE referrer_id = 1;

-- 优化后：使用覆盖索引
SELECT 
    COUNT(*) as total_referrals,
    SUM(commission) as total_commission
FROM user_referrals 
WHERE referrer_id = 1 AND status = 1;
```

#### 1.3 分区策略
```sql
-- 按时间分区的佣金记录表
CREATE TABLE referral_commissions (
    id BIGINT AUTO_INCREMENT,
    referrer_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    order_id BIGINT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    level INT NOT NULL,
    status TINYINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2. 缓存优化

#### 2.1 多级缓存策略
```go
// cache/referral_cache.go
package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    "github.com/go-redis/redis/v8"
)

type ReferralCache struct {
    redis  *redis.Client
    local  *LocalCache  // 本地缓存
}

// GetUserStats 获取用户统计（多级缓存）
func (c *ReferralCache) GetUserStats(userID uint) (*UserStats, error) {
    key := fmt.Sprintf("referral:user_stats:%d", userID)
    
    // 1. 先查本地缓存
    if stats, ok := c.local.Get(key); ok {
        return stats.(*UserStats), nil
    }
    
    // 2. 查Redis缓存
    data, err := c.redis.Get(context.Background(), key).Result()
    if err == nil {
        var stats UserStats
        if err := json.Unmarshal([]byte(data), &stats); err == nil {
            // 写入本地缓存
            c.local.Set(key, &stats, 1*time.Minute)
            return &stats, nil
        }
    }
    
    return nil, err
}

// SetUserStats 设置用户统计缓存
func (c *ReferralCache) SetUserStats(userID uint, stats *UserStats) error {
    key := fmt.Sprintf("referral:user_stats:%d", userID)
    
    // 序列化数据
    data, err := json.Marshal(stats)
    if err != nil {
        return err
    }
    
    // 写入Redis
    err = c.redis.Set(context.Background(), key, data, 5*time.Minute).Err()
    if err != nil {
        return err
    }
    
    // 写入本地缓存
    c.local.Set(key, stats, 1*time.Minute)
    
    return nil
}
```

#### 2.2 缓存预热
```go
// cache/warmup.go
package cache

import (
    "context"
    "log"
    "time"
)

// WarmupCache 缓存预热
func (c *ReferralCache) WarmupCache() {
    log.Println("开始缓存预热...")
    
    // 预热热点用户数据
    hotUsers := c.getHotUsers() // 获取活跃用户列表
    
    for _, userID := range hotUsers {
        go func(uid uint) {
            // 预热用户统计数据
            stats, err := c.calculateUserStats(uid)
            if err == nil {
                c.SetUserStats(uid, stats)
            }
            
            // 预热用户推荐列表
            referrals, err := c.getUserReferrals(uid)
            if err == nil {
                c.SetUserReferrals(uid, referrals)
            }
        }(userID)
    }
    
    // 预热系统统计数据
    go func() {
        systemStats, err := c.calculateSystemStats()
        if err == nil {
            c.SetSystemStats(systemStats)
        }
    }()
    
    log.Println("缓存预热完成")
}

// ScheduleWarmup 定时缓存预热
func (c *ReferralCache) ScheduleWarmup() {
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            c.WarmupCache()
        }
    }
}
```

### 3. 应用层优化

#### 3.1 连接池优化
```go
// config/database.go
package config

import (
    "time"
    "gorm.io/gorm"
    "gorm.io/driver/mysql"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
    MaxIdleConns    int           `yaml:"max_idle_conns"`
    MaxOpenConns    int           `yaml:"max_open_conns"`
    ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
    ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time"`
}

// InitDatabase 初始化数据库连接
func InitDatabase(config *DatabaseConfig) (*gorm.DB, error) {
    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
        // 禁用默认事务
        SkipDefaultTransaction: true,
        // 预编译语句
        PrepareStmt: true,
    })
    if err != nil {
        return nil, err
    }
    
    sqlDB, err := db.DB()
    if err != nil {
        return nil, err
    }
    
    // 连接池配置
    sqlDB.SetMaxIdleConns(config.MaxIdleConns)       // 最大空闲连接数
    sqlDB.SetMaxOpenConns(config.MaxOpenConns)       // 最大打开连接数
    sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime) // 连接最大生存时间
    sqlDB.SetConnMaxIdleTime(config.ConnMaxIdleTime) // 连接最大空闲时间
    
    return db, nil
}
```

#### 3.2 异步处理优化
```go
// async/commission_processor.go
package async

import (
    "context"
    "log"
    "time"
)

// CommissionProcessor 佣金处理器
type CommissionProcessor struct {
    queue   chan *CommissionTask
    workers int
}

// CommissionTask 佣金计算任务
type CommissionTask struct {
    OrderID   uint
    UserID    uint
    Amount    float64
    Timestamp time.Time
}

// NewCommissionProcessor 创建佣金处理器
func NewCommissionProcessor(workers int) *CommissionProcessor {
    return &CommissionProcessor{
        queue:   make(chan *CommissionTask, 1000),
        workers: workers,
    }
}

// Start 启动处理器
func (p *CommissionProcessor) Start(ctx context.Context) {
    for i := 0; i < p.workers; i++ {
        go p.worker(ctx, i)
    }
}

// worker 工作协程
func (p *CommissionProcessor) worker(ctx context.Context, id int) {
    log.Printf("佣金处理工作协程 %d 启动", id)
    
    for {
        select {
        case task := <-p.queue:
            if err := p.processCommission(task); err != nil {
                log.Printf("佣金计算失败: %v", err)
                // 重试逻辑
                p.retryTask(task)
            }
        case <-ctx.Done():
            log.Printf("佣金处理工作协程 %d 停止", id)
            return
        }
    }
}

// AddTask 添加任务
func (p *CommissionProcessor) AddTask(task *CommissionTask) {
    select {
    case p.queue <- task:
        // 任务添加成功
    default:
        // 队列满了，记录日志
        log.Printf("佣金处理队列已满，丢弃任务: %+v", task)
    }
}
```

---

## 故障处理

### 1. 常见故障及处理方案

#### 1.1 服务不可用

**故障现象**
- 健康检查失败
- 接口返回5xx错误
- 服务进程不存在

**排查步骤**
```bash
# 1. 检查服务状态
sudo systemctl status mall-backend

# 2. 查看服务日志
sudo journalctl -u mall-backend -f

# 3. 检查端口占用
sudo netstat -tlnp | grep :8080

# 4. 检查资源使用
top -p $(pgrep mall-backend)

# 5. 检查磁盘空间
df -h

# 6. 检查内存使用
free -h
```

**处理方案**
```bash
# 重启服务
sudo systemctl restart mall-backend

# 如果重启失败，检查配置文件
sudo -u app /opt/mall-backend/mall-backend --config /opt/mall-backend/config/production.yaml --check-config

# 回滚到上一个版本
sudo /opt/scripts/rollback.sh
```

#### 1.2 数据库连接异常

**故障现象**
- 数据库连接超时
- 连接池耗尽
- 慢查询增多

**排查步骤**
```sql
-- 检查连接数
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
SHOW VARIABLES LIKE 'max_connections';

-- 检查慢查询
SHOW STATUS LIKE 'Slow_queries';
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 检查锁等待
SELECT * FROM information_schema.innodb_trx;
SELECT * FROM information_schema.innodb_locks;
SELECT * FROM information_schema.innodb_lock_waits;
```

**处理方案**
```bash
# 重启应用释放连接
sudo systemctl restart mall-backend

# 优化数据库配置
sudo vim /etc/my.cnf
# 增加 max_connections = 2000
# 优化 innodb_buffer_pool_size

# 重启MySQL（谨慎操作）
sudo systemctl restart mysqld

# 杀死长时间运行的查询
# mysql> KILL <process_id>;
```

#### 1.3 Redis 缓存异常

**故障现象**
- Redis连接失败
- 缓存命中率下降
- 内存使用率过高

**排查步骤**
```bash
# 检查Redis状态
redis-cli ping
redis-cli info

# 检查内存使用
redis-cli info memory

# 检查连接数
redis-cli info clients

# 检查慢查询
redis-cli slowlog get 10
```

**处理方案**
```bash
# 清理过期键
redis-cli --scan --pattern "expired:*" | xargs redis-cli del

# 重启Redis（谨慎操作）
sudo systemctl restart redis

# 调整内存策略
redis-cli config set maxmemory-policy allkeys-lru
```

### 2. 应急预案

#### 2.1 服务降级
```go
// fallback/referral_fallback.go
package fallback

import (
    "context"
    "errors"
)

// ReferralFallback 分销服务降级
type ReferralFallback struct {
    enabled bool
}

// CreateReferral 降级版本的创建分销关系
func (f *ReferralFallback) CreateReferral(ctx context.Context, req *CreateReferralRequest) error {
    if !f.enabled {
        return errors.New("服务暂时不可用，请稍后重试")
    }
    
    // 简化版本：只记录到消息队列，异步处理
    return f.enqueueReferralTask(req)
}

// GetReferralStatistics 降级版本的获取统计
func (f *ReferralFallback) GetReferralStatistics(ctx context.Context, userID uint) (*ReferralStatistics, error) {
    if !f.enabled {
        return nil, errors.New("服务暂时不可用，请稍后重试")
    }
    
    // 返回缓存数据或默认值
    return f.getCachedStats(userID)
}
```

#### 2.2 熔断器
```go
// circuit/breaker.go
package circuit

import (
    "context"
    "errors"
    "sync"
    "time"
)

// CircuitBreaker 熔断器
type CircuitBreaker struct {
    mu           sync.Mutex
    state        State
    failureCount int
    threshold    int
    timeout      time.Duration
    lastFailTime time.Time
}

type State int

const (
    StateClosed State = iota
    StateOpen
    StateHalfOpen
)

// Call 执行调用
func (cb *CircuitBreaker) Call(ctx context.Context, fn func() error) error {
    cb.mu.Lock()
    defer cb.mu.Unlock()
    
    switch cb.state {
    case StateOpen:
        if time.Since(cb.lastFailTime) > cb.timeout {
            cb.state = StateHalfOpen
            cb.failureCount = 0
        } else {
            return errors.New("circuit breaker is open")
        }
    case StateHalfOpen:
        // 允许一个请求通过
    }
    
    err := fn()
    if err != nil {
        cb.onFailure()
        return err
    }
    
    cb.onSuccess()
    return nil
}

// onFailure 处理失败
func (cb *CircuitBreaker) onFailure() {
    cb.failureCount++
    cb.lastFailTime = time.Now()
    
    if cb.failureCount >= cb.threshold {
        cb.state = StateOpen
    }
}

// onSuccess 处理成功
func (cb *CircuitBreaker) onSuccess() {
    cb.failureCount = 0
    cb.state = StateClosed
}
```

### 3. 故障恢复

#### 3.1 数据一致性检查
```sql
-- 检查分销关系数据一致性
SELECT 
    ur.user_id,
    ur.referrer_id,
    COUNT(rc.id) as commission_count,
    SUM(rc.amount) as total_commission,
    ur.commission as recorded_commission
FROM user_referrals ur
LEFT JOIN referral_commissions rc ON ur.user_id = rc.user_id
WHERE ur.status = 1
GROUP BY ur.user_id, ur.referrer_id, ur.commission
HAVING ABS(total_commission - recorded_commission) > 0.01;

-- 修复数据不一致
UPDATE user_referrals ur
SET commission = (
    SELECT COALESCE(SUM(amount), 0)
    FROM referral_commissions rc
    WHERE rc.user_id = ur.user_id AND rc.status = 1
)
WHERE ur.status = 1;
```

#### 3.2 缓存重建
```bash
#!/bin/bash
# rebuild_cache.sh - 缓存重建脚本

echo "开始重建缓存..."

# 清空Redis缓存
redis-cli FLUSHDB

# 重启应用触发缓存预热
sudo systemctl restart mall-backend

# 等待服务启动
sleep 30

# 调用预热接口
curl -X POST http://localhost:8080/admin/cache/warmup

echo "缓存重建完成"
```

---

## 安全配置

### 1. 网络安全

#### 1.1 防火墙配置
```bash
# 配置iptables规则
sudo iptables -A INPUT -p tcp --dport 22 -s 10.0.0.0/8 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8080 -s 10.0.0.0/8 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 3306 -s 10.0.0.0/8 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 6379 -s 10.0.0.0/8 -j ACCEPT
sudo iptables -A INPUT -j DROP

# 保存规则
sudo iptables-save > /etc/iptables/rules.v4
```

#### 1.2 SSL/TLS 配置
```bash
# 生成SSL证书（Let's Encrypt）
sudo certbot --nginx -d api.example.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 2. 应用安全

#### 2.1 JWT 安全配置
```go
// security/jwt.go
package security

import (
    "crypto/rand"
    "encoding/base64"
    "time"
    "github.com/golang-jwt/jwt/v4"
)

// JWTConfig JWT配置
type JWTConfig struct {
    SecretKey     string        `yaml:"secret_key"`
    ExpireTime    time.Duration `yaml:"expire_time"`
    RefreshTime   time.Duration `yaml:"refresh_time"`
    Issuer        string        `yaml:"issuer"`
}

// GenerateSecretKey 生成安全的密钥
func GenerateSecretKey() (string, error) {
    bytes := make([]byte, 32)
    if _, err := rand.Read(bytes); err != nil {
        return "", err
    }
    return base64.URLEncoding.EncodeToString(bytes), nil
}

// ValidateToken 验证Token
func (c *JWTConfig) ValidateToken(tokenString string) (*jwt.Token, error) {
    return jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
        if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
            return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
        }
        return []byte(c.SecretKey), nil
    })
}
```

#### 2.2 输入验证
```go
// validation/referral_validator.go
package validation

import (
    "errors"
    "regexp"
)

// ReferralValidator 分销参数验证器
type ReferralValidator struct {
    mobileRegex *regexp.Regexp
}

// NewReferralValidator 创建验证器
func NewReferralValidator() *ReferralValidator {
    return &ReferralValidator{
        mobileRegex: regexp.MustCompile(`^1[3-9]\d{9}$`),
    }
}

// ValidateCreateRequest 验证创建分销关系请求
func (v *ReferralValidator) ValidateCreateRequest(req *CreateReferralRequest) error {
    if req.UserID <= 0 {
        return errors.New("用户ID无效")
    }
    
    if req.ReferrerID <= 0 {
        return errors.New("推荐人ID无效")
    }
    
    if req.UserID == req.ReferrerID {
        return errors.New("不能推荐自己")
    }
    
    return nil
}

// ValidateMobile 验证手机号
func (v *ReferralValidator) ValidateMobile(mobile string) error {
    if !v.mobileRegex.MatchString(mobile) {
        return errors.New("手机号格式无效")
    }
    return nil
}
```

### 3. 数据安全

#### 3.1 数据加密
```go
// encryption/data_encryption.go
package encryption

import (
    "crypto/aes"
    "crypto/cipher"
    "crypto/rand"
    "encoding/base64"
    "errors"
    "io"
)

// DataEncryption 数据加密器
type DataEncryption struct {
    key []byte
}

// NewDataEncryption 创建加密器
func NewDataEncryption(key string) (*DataEncryption, error) {
    keyBytes, err := base64.StdEncoding.DecodeString(key)
    if err != nil {
        return nil, err
    }
    
    if len(keyBytes) != 32 {
        return nil, errors.New("密钥长度必须为32字节")
    }
    
    return &DataEncryption{key: keyBytes}, nil
}

// Encrypt 加密数据
func (e *DataEncryption) Encrypt(plaintext string) (string, error) {
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }
    
    ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt 解密数据
func (e *DataEncryption) Decrypt(ciphertext string) (string, error) {
    data, err := base64.StdEncoding.DecodeString(ciphertext)
    if err != nil {
        return "", err
    }
    
    block, err := aes.NewCipher(e.key)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonceSize := gcm.NonceSize()
    if len(data) < nonceSize {
        return "", errors.New("密文太短")
    }
    
    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
    if err != nil {
        return "", err
    }
    
    return string(plaintext), nil
}
```

#### 3.2 敏感数据脱敏
```go
// masking/data_masking.go
package masking

import (
    "strings"
)

// MaskMobile 手机号脱敏
func MaskMobile(mobile string) string {
    if len(mobile) != 11 {
        return mobile
    }
    return mobile[:3] + "****" + mobile[7:]
}

// MaskEmail 邮箱脱敏
func MaskEmail(email string) string {
    parts := strings.Split(email, "@")
    if len(parts) != 2 {
        return email
    }
    
    username := parts[0]
    domain := parts[1]
    
    if len(username) <= 2 {
        return email
    }
    
    masked := username[:1] + strings.Repeat("*", len(username)-2) + username[len(username)-1:]
    return masked + "@" + domain
}

// MaskIDCard 身份证脱敏
func MaskIDCard(idCard string) string {
    if len(idCard) != 18 {
        return idCard
    }
    return idCard[:6] + "********" + idCard[14:]
}
```

---

## 备份恢复

### 1. 数据库备份

#### 1.1 自动备份脚本
```bash
#!/bin/bash
# backup_database.sh - 数据库备份脚本

set -e

# 配置变量
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="backup_user"
DB_PASS="backup_password"
DB_NAME="mall_db"
BACKUP_DIR="/backup/mysql"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/${DB_NAME}_${DATE}.sql"
RETENTION_DAYS=7

echo "开始备份数据库: ${DB_NAME}"

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 执行备份
mysqldump -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --hex-blob \
    --opt \
    ${DB_NAME} > ${BACKUP_FILE}

# 压缩备份文件
gzip ${BACKUP_FILE}

echo "备份完成: ${BACKUP_FILE}.gz"

# 清理过期备份
find ${BACKUP_DIR} -name "${DB_NAME}_*.sql.gz" -mtime +${RETENTION_DAYS} -delete

echo "清理完成，保留最近${RETENTION_DAYS}天的备份"

# 验证备份文件
if [ -f "${BACKUP_FILE}.gz" ]; then
    SIZE=$(stat -c%s "${BACKUP_FILE}.gz")
    if [ $SIZE -gt 1024 ]; then
        echo "备份验证成功，文件大小: ${SIZE} bytes"
    else
        echo "警告: 备份文件过小，可能备份失败"
        exit 1
    fi
else
    echo "错误: 备份文件不存在"
    exit 1
fi
```

#### 1.2 定时备份配置
```bash
# 添加到crontab
# 每天凌晨2点执行全量备份
0 2 * * * /opt/scripts/backup_database.sh >> /var/log/backup.log 2>&1

# 每4小时执行增量备份
0 */4 * * * /opt/scripts/backup_binlog.sh >> /var/log/backup.log 2>&1
```

### 2. 数据恢复

#### 2.1 数据库恢复脚本
```bash
#!/bin/bash
# restore_database.sh - 数据库恢复脚本

set -e

# 配置变量
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASS="root_password"
DB_NAME="mall_db"
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <备份文件路径>"
    exit 1
fi

if [ ! -f "$BACKUP_FILE" ]; then
    echo "错误: 备份文件不存在: $BACKUP_FILE"
    exit 1
fi

echo "开始恢复数据库: ${DB_NAME}"
echo "备份文件: ${BACKUP_FILE}"

# 确认操作
read -p "这将覆盖现有数据库，确认继续? (y/N): " confirm
if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "操作已取消"
    exit 0
fi

# 停止应用服务
echo "停止应用服务..."
sudo systemctl stop mall-backend

# 创建数据库备份（以防万一）
echo "创建当前数据库备份..."
mysqldump -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} \
    --single-transaction ${DB_NAME} > "/tmp/${DB_NAME}_before_restore_$(date +%Y%m%d_%H%M%S).sql"

# 删除现有数据库
echo "删除现有数据库..."
mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} -e "DROP DATABASE IF EXISTS ${DB_NAME};"

# 创建新数据库
echo "创建新数据库..."
mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} -e "CREATE DATABASE ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 恢复数据
echo "恢复数据..."
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c $BACKUP_FILE | mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME}
else
    mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} < $BACKUP_FILE
fi

# 启动应用服务
echo "启动应用服务..."
sudo systemctl start mall-backend

# 等待服务启动
sleep 10

# 验证恢复
echo "验证数据恢复..."
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "数据库恢复成功，服务正常运行"
else
    echo "警告: 服务启动异常，请检查日志"
    sudo systemctl status mall-backend
fi
```

### 3. Redis 备份恢复

#### 3.1 Redis 备份脚本
```bash
#!/bin/bash
# backup_redis.sh - Redis备份脚本

set -e

# 配置变量
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASS="redis_password"
BACKUP_DIR="/backup/redis"
DATE=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=7

echo "开始备份Redis数据"

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 执行BGSAVE
redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} -a ${REDIS_PASS} BGSAVE

# 等待备份完成
while [ $(redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} -a ${REDIS_PASS} LASTSAVE) -eq $(redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} -a ${REDIS_PASS} LASTSAVE) ]; do
    echo "等待备份完成..."
    sleep 1
done

# 复制RDB文件
cp /var/lib/redis/dump.rdb ${BACKUP_DIR}/dump_${DATE}.rdb

# 压缩备份文件
gzip ${BACKUP_DIR}/dump_${DATE}.rdb

echo "Redis备份完成: ${BACKUP_DIR}/dump_${DATE}.rdb.gz"

# 清理过期备份
find ${BACKUP_DIR} -name "dump_*.rdb.gz" -mtime +${RETENTION_DAYS} -delete

echo "清理完成，保留最近${RETENTION_DAYS}天的备份"
```

---

## 总结

本部署运维指南涵盖了分销模块从开发到生产的完整部署流程，包括：

### 核心要点

1. **环境配置**：详细的服务器配置要求和软件安装步骤
2. **自动化部署**：完整的构建、部署和回滚脚本
3. **监控告警**：全面的监控指标和告警规则配置
4. **性能优化**：数据库、缓存和应用层的优化策略
5. **故障处理**：常见问题的排查和处理方案
6. **安全配置**：网络、应用和数据安全的最佳实践
7. **备份恢复**：完整的数据备份和恢复流程

### 最佳实践

1. **分层部署**：应用、数据库、缓存分离部署
2. **负载均衡**：多实例部署提高可用性
3. **监控先行**：完善的监控体系确保及时发现问题
4. **自动化运维**：减少人工操作，提高效率
5. **安全第一**：多层次安全防护
6. **定期备份**：确保数据安全

### 运维建议

1. **定期演练**：定期进行故障演练和恢复测试
2. **文档更新**：及时更新运维文档和流程
3. **团队培训**：确保团队熟悉运维流程
4. **持续优化**：根据监控数据持续优化系统性能

通过遵循本指南，可以确保分销模块在生产环境中稳定、高效、安全地运行。