# 用户分销模块架构文档

## 概述

用户分销模块是商城系统的核心功能之一，实现了多级分销体系，支持用户通过推荐其他用户获得佣金收益。该模块采用分层架构设计，包含控制器层、服务层、仓储层和数据模型层。

## 模块结构

```
modules/user/
├── controllers/
│   └── referral_controller.go     # 分销控制器
├── services/
│   └── user_referral_service.go   # 分销服务层
├── repositories/
│   ├── interface.go                # 仓储接口定义
│   └── user_referral_repository.go # 分销仓储实现
├── models/
│   └── user_referral.go           # 分销数据模型
├── dto/
│   └── referral_dto.go            # 分销数据传输对象
└── routers/
    └── router.go                   # 路由配置
```

## 核心功能

### 1. 分销关系管理
- **创建分销关系**: 建立用户与推荐人的分销关系
- **查询分销关系**: 获取用户的推荐人信息
- **分销层级管理**: 支持三级分销体系
- **关系验证**: 防止循环推荐和重复建立关系

### 2. 分销统计
- **推荐用户统计**: 统计各级推荐用户数量
- **佣金统计**: 计算累计佣金、今日佣金、本月佣金、本年佣金
- **分级统计**: 分别统计一级、二级、三级推荐数据

### 3. 佣金管理
- **佣金计算**: 根据订单金额和分销等级计算佣金
- **佣金更新**: 实时更新推荐人的佣金收益
- **佣金记录**: 记录所有佣金变更的操作日志

## 数据模型

### UserReferral 分销关系模型

```go
type UserReferral struct {
    ID           int64     `orm:"auto;pk" json:"id"`
    UserID       int64     `orm:"column(user_id)" json:"user_id"`
    ReferrerID   int64     `orm:"column(referrer_id)" json:"referrer_id"`
    Level        int       `orm:"column(level)" json:"level"`
    Status       int       `orm:"column(status)" json:"status"`
    Commission   float64   `orm:"column(commission);digits(10);decimals(2)" json:"commission"`
    ReferralTime time.Time `orm:"column(referral_time);type(datetime)" json:"referral_time"`
    CreatedAt    time.Time `orm:"auto_now_add;type(datetime)" json:"created_at"`
    UpdatedAt    time.Time `orm:"auto_now;type(datetime)" json:"updated_at"`
}
```

### 字段说明
- **ID**: 分销关系唯一标识
- **UserID**: 被推荐用户ID
- **ReferrerID**: 推荐人用户ID
- **Level**: 分销等级（1-一级，2-二级，3-三级）
- **Status**: 关系状态（0-无效，1-有效）
- **Commission**: 累计佣金
- **ReferralTime**: 推荐时间
- **CreatedAt**: 创建时间
- **UpdatedAt**: 更新时间

### 常量定义

```go
// 分销等级常量
const (
    ReferralLevelOne   = 1  // 一级分销
    ReferralLevelTwo   = 2  // 二级分销
    ReferralLevelThree = 3  // 三级分销
)

// 分销状态常量
const (
    ReferralStatusInvalid = 0  // 无效
    ReferralStatusValid   = 1  // 有效
)
```

## 架构层次

### 1. 控制器层 (Controller)
- **职责**: 处理HTTP请求，参数验证，调用服务层
- **文件**: `referral_controller.go`
- **主要方法**:
  - `CreateReferral`: 创建分销关系
  - `GetReferrals`: 获取推荐用户列表
  - `GetReferralStatistics`: 获取分销统计信息

### 2. 服务层 (Service)
- **职责**: 业务逻辑处理，数据转换，调用仓储层
- **文件**: `user_referral_service.go`
- **主要方法**:
  - `CreateReferral`: 创建分销关系业务逻辑
  - `GetReferrer`: 获取推荐人信息
  - `GetReferrals`: 获取推荐用户列表
  - `GetReferralStatistics`: 获取分销统计
  - `UpdateReferralCommission`: 更新分销佣金

### 3. 仓储层 (Repository)
- **职责**: 数据访问，数据库操作
- **文件**: `user_referral_repository.go`
- **接口**: `UserReferralRepository`
- **主要方法**:
  - `Create`: 创建分销关系
  - `Update`: 更新分销关系
  - `GetReferrerByUserID`: 获取用户的推荐人
  - `GetDirectReferrals`: 获取直接推荐用户
  - `GetAllReferrals`: 获取所有层级推荐用户
  - `CheckReferralExists`: 检查分销关系是否存在
  - `UpdateCommission`: 更新佣金

### 4. 数据传输对象 (DTO)
- **职责**: 定义API请求和响应的数据结构
- **文件**: `referral_dto.go`
- **主要结构**:
  - `ReferralCreateRequest`: 创建分销关系请求
  - `ReferralQueryRequest`: 查询分销关系请求
  - `ReferralResponse`: 分销关系响应
  - `ReferralStatisticsResponse`: 分销统计响应
  - `UserBriefResponse`: 用户简要信息响应

## 业务流程

### 1. 创建分销关系流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as 控制器
    participant Service as 服务层
    participant Repository as 仓储层
    participant DB as 数据库
    
    Client->>Controller: POST /api/v1/user/secured/referrals
    Controller->>Controller: 参数验证
    Controller->>Service: CreateReferral()
    Service->>Repository: 检查用户是否存在
    Service->>Repository: 检查推荐人是否存在
    Service->>Repository: 检查是否已有推荐关系
    Service->>Repository: Create()
    Repository->>DB: INSERT INTO user_referrals
    DB-->>Repository: 返回ID
    Repository-->>Service: 返回ID
    Service->>Service: 记录操作日志
    Service-->>Controller: 返回结果
    Controller-->>Client: 返回响应
```

### 2. 查询分销统计流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as 控制器
    participant Service as 服务层
    participant Repository as 仓储层
    participant DB as 数据库
    
    Client->>Controller: GET /api/v1/user/secured/referrals/statistics
    Controller->>Service: GetReferralStatistics()
    Service->>Repository: GetDirectReferrals()
    Repository->>DB: 查询一级推荐
    Service->>Repository: GetAllReferrals(level=2)
    Repository->>DB: 查询二级推荐
    Service->>Repository: GetAllReferrals(level=3)
    Repository->>DB: 查询三级推荐
    Service->>Service: 计算统计数据
    Service-->>Controller: 返回统计结果
    Controller-->>Client: 返回响应
```

## 设计原则

### 1. 单一职责原则
- 每个层次只负责自己的职责
- 控制器只处理HTTP相关逻辑
- 服务层只处理业务逻辑
- 仓储层只处理数据访问

### 2. 依赖倒置原则
- 服务层依赖仓储接口，而不是具体实现
- 便于单元测试和模块替换

### 3. 开闭原则
- 通过接口定义，便于扩展新功能
- 不修改现有代码的情况下增加新特性

### 4. 数据一致性
- 使用事务确保数据操作的原子性
- 通过状态字段管理数据的有效性

## 扩展性考虑

### 1. 分销等级扩展
- 当前支持三级分销，可通过修改常量支持更多级别
- 数据库设计支持任意级别的分销关系

### 2. 佣金计算策略
- 可扩展不同的佣金计算规则
- 支持按商品类别、用户等级等维度计算佣金

### 3. 统计维度扩展
- 可增加更多统计维度，如按时间段、按商品分类等
- 支持实时统计和定时统计两种模式

### 4. 性能优化
- 可引入缓存机制提高查询性能
- 支持分页查询处理大量数据
- 可考虑读写分离优化数据库性能