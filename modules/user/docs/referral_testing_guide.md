# 分销模块测试指南

## 概述

本文档提供了分销模块的全面测试指南，包括单元测试、集成测试、性能测试和端到端测试。通过系统化的测试策略，确保分销功能的稳定性、可靠性和性能。

## 测试策略

### 1. 测试金字塔

```
    /\     E2E Tests (10%)
   /  \    端到端测试
  /____\   
 /      \  Integration Tests (20%)
/________\ 集成测试
\        /
 \______/  Unit Tests (70%)
  单元测试
```

### 2. 测试分类

- **单元测试 (70%)**：测试单个函数和方法
- **集成测试 (20%)**：测试模块间的交互
- **端到端测试 (10%)**：测试完整的业务流程

### 3. 测试环境

- **开发环境**：本地开发测试
- **测试环境**：自动化测试执行
- **预发环境**：生产前验证
- **生产环境**：线上监控测试

---

## 单元测试

### 1. 测试框架配置

#### Go 测试框架
```go
// referral_test.go
package user

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "github.com/stretchr/testify/suite"
)

// ReferralTestSuite 分销测试套件
type ReferralTestSuite struct {
    suite.Suite
    service *UserReferralService
    mockRepo *MockUserReferralRepository
    mockUserRepo *MockUserRepository
}

// SetupTest 测试前置设置
func (suite *ReferralTestSuite) SetupTest() {
    suite.mockRepo = new(MockUserReferralRepository)
    suite.mockUserRepo = new(MockUserRepository)
    suite.service = NewUserReferralService(suite.mockRepo, suite.mockUserRepo)
}

// TestReferralTestSuite 运行测试套件
func TestReferralTestSuite(t *testing.T) {
    suite.Run(t, new(ReferralTestSuite))
}
```

### 2. 服务层测试

#### 2.1 创建分销关系测试
```go
// TestCreateReferral_Success 测试成功创建分销关系
func (suite *ReferralTestSuite) TestCreateReferral_Success() {
    // Arrange
    userID := uint(1)
    referrerID := uint(2)
    
    user := &User{ID: userID, Status: 1}
    referrer := &User{ID: referrerID, Status: 1}
    
    suite.mockUserRepo.On("GetByID", userID).Return(user, nil)
    suite.mockUserRepo.On("GetByID", referrerID).Return(referrer, nil)
    suite.mockRepo.On("GetReferrerByUserID", userID).Return(nil, nil)
    suite.mockRepo.On("Create", mock.AnythingOfType("*UserReferral")).Return(nil)
    
    request := &ReferralCreateRequest{
        UserID:     userID,
        ReferrerID: referrerID,
    }
    
    // Act
    err := suite.service.CreateReferral(request)
    
    // Assert
    assert.NoError(suite.T(), err)
    suite.mockRepo.AssertExpectations(suite.T())
    suite.mockUserRepo.AssertExpectations(suite.T())
}

// TestCreateReferral_UserNotFound 测试用户不存在的情况
func (suite *ReferralTestSuite) TestCreateReferral_UserNotFound() {
    // Arrange
    userID := uint(999)
    referrerID := uint(2)
    
    suite.mockUserRepo.On("GetByID", userID).Return(nil, errors.New("user not found"))
    
    request := &ReferralCreateRequest{
        UserID:     userID,
        ReferrerID: referrerID,
    }
    
    // Act
    err := suite.service.CreateReferral(request)
    
    // Assert
    assert.Error(suite.T(), err)
    assert.Contains(suite.T(), err.Error(), "用户不存在")
}

// TestCreateReferral_SelfReferral 测试自己推荐自己的情况
func (suite *ReferralTestSuite) TestCreateReferral_SelfReferral() {
    // Arrange
    userID := uint(1)
    
    request := &ReferralCreateRequest{
        UserID:     userID,
        ReferrerID: userID,
    }
    
    // Act
    err := suite.service.CreateReferral(request)
    
    // Assert
    assert.Error(suite.T(), err)
    assert.Contains(suite.T(), err.Error(), "不能推荐自己")
}

// TestCreateReferral_AlreadyHasReferrer 测试用户已有推荐人的情况
func (suite *ReferralTestSuite) TestCreateReferral_AlreadyHasReferrer() {
    // Arrange
    userID := uint(1)
    referrerID := uint(2)
    existingReferrer := uint(3)
    
    user := &User{ID: userID, Status: 1}
    referrer := &User{ID: referrerID, Status: 1}
    existingReferral := &UserReferral{
        UserID:     userID,
        ReferrerID: existingReferrer,
        Level:      1,
        Status:     1,
    }
    
    suite.mockUserRepo.On("GetByID", userID).Return(user, nil)
    suite.mockUserRepo.On("GetByID", referrerID).Return(referrer, nil)
    suite.mockRepo.On("GetReferrerByUserID", userID).Return(existingReferral, nil)
    
    request := &ReferralCreateRequest{
        UserID:     userID,
        ReferrerID: referrerID,
    }
    
    // Act
    err := suite.service.CreateReferral(request)
    
    // Assert
    assert.Error(suite.T(), err)
    assert.Contains(suite.T(), err.Error(), "用户已有推荐人")
}
```

#### 2.2 获取分销统计测试
```go
// TestGetReferralStatistics_Success 测试成功获取分销统计
func (suite *ReferralTestSuite) TestGetReferralStatistics_Success() {
    // Arrange
    userID := uint(1)
    
    directReferrals := []*UserReferral{
        {UserID: 2, ReferrerID: userID, Level: 1, Commission: 100.50},
        {UserID: 3, ReferrerID: userID, Level: 1, Commission: 200.30},
    }
    
    allReferrals := []*UserReferral{
        {UserID: 2, ReferrerID: userID, Level: 1, Commission: 100.50},
        {UserID: 3, ReferrerID: userID, Level: 1, Commission: 200.30},
        {UserID: 4, ReferrerID: 2, Level: 2, Commission: 50.20},
    }
    
    suite.mockRepo.On("GetDirectReferrals", userID).Return(directReferrals, nil)
    suite.mockRepo.On("GetAllReferrals", userID).Return(allReferrals, nil)
    
    // Act
    stats, err := suite.service.GetReferralStatistics(userID)
    
    // Assert
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), stats)
    assert.Equal(suite.T(), 2, stats.DirectReferrals)
    assert.Equal(suite.T(), 3, stats.TotalReferrals)
    assert.Equal(suite.T(), 351.0, stats.TotalCommission)
    assert.Equal(suite.T(), 300.8, stats.DirectCommission)
}

// TestGetReferralStatistics_NoReferrals 测试无推荐用户的情况
func (suite *ReferralTestSuite) TestGetReferralStatistics_NoReferrals() {
    // Arrange
    userID := uint(1)
    
    suite.mockRepo.On("GetDirectReferrals", userID).Return([]*UserReferral{}, nil)
    suite.mockRepo.On("GetAllReferrals", userID).Return([]*UserReferral{}, nil)
    
    // Act
    stats, err := suite.service.GetReferralStatistics(userID)
    
    // Assert
    assert.NoError(suite.T(), err)
    assert.NotNil(suite.T(), stats)
    assert.Equal(suite.T(), 0, stats.DirectReferrals)
    assert.Equal(suite.T(), 0, stats.TotalReferrals)
    assert.Equal(suite.T(), 0.0, stats.TotalCommission)
}
```

### 3. 仓储层测试

#### 3.1 数据库操作测试
```go
// TestUserReferralRepository 仓储层测试
func TestUserReferralRepository(t *testing.T) {
    // 使用测试数据库
    db := setupTestDB()
    defer cleanupTestDB(db)
    
    repo := NewUserReferralRepository(db)
    
    t.Run("Create", func(t *testing.T) {
        referral := &UserReferral{
            UserID:      1,
            ReferrerID:  2,
            Level:       1,
            Status:      1,
            Commission:  0,
            ReferralTime: time.Now(),
        }
        
        err := repo.Create(referral)
        assert.NoError(t, err)
        assert.NotZero(t, referral.ID)
    })
    
    t.Run("GetReferrerByUserID", func(t *testing.T) {
        // 先创建测试数据
        referral := &UserReferral{
            UserID:      3,
            ReferrerID:  4,
            Level:       1,
            Status:      1,
            Commission:  0,
            ReferralTime: time.Now(),
        }
        repo.Create(referral)
        
        // 测试查询
        result, err := repo.GetReferrerByUserID(3)
        assert.NoError(t, err)
        assert.NotNil(t, result)
        assert.Equal(t, uint(4), result.ReferrerID)
    })
    
    t.Run("GetDirectReferrals", func(t *testing.T) {
        // 创建测试数据
        referrals := []*UserReferral{
            {UserID: 5, ReferrerID: 1, Level: 1, Status: 1, Commission: 100},
            {UserID: 6, ReferrerID: 1, Level: 1, Status: 1, Commission: 200},
            {UserID: 7, ReferrerID: 5, Level: 2, Status: 1, Commission: 50},
        }
        
        for _, r := range referrals {
            r.ReferralTime = time.Now()
            repo.Create(r)
        }
        
        // 测试查询直接推荐
        results, err := repo.GetDirectReferrals(1)
        assert.NoError(t, err)
        assert.Len(t, results, 2)
    })
}
```

### 4. 控制器测试

#### 4.1 HTTP 接口测试
```go
// TestReferralController HTTP接口测试
func TestReferralController(t *testing.T) {
    // 设置测试环境
    gin.SetMode(gin.TestMode)
    
    mockService := new(MockUserReferralService)
    controller := NewReferralController(mockService)
    
    router := gin.New()
    router.POST("/api/v1/user/referral", controller.CreateReferral)
    router.GET("/api/v1/user/referral/statistics", controller.GetReferralStatistics)
    
    t.Run("CreateReferral_Success", func(t *testing.T) {
        // Arrange
        mockService.On("CreateReferral", mock.AnythingOfType("*ReferralCreateRequest")).Return(nil)
        
        requestBody := `{
            "user_id": 1,
            "referrer_id": 2
        }`
        
        req, _ := http.NewRequest("POST", "/api/v1/user/referral", strings.NewReader(requestBody))
        req.Header.Set("Content-Type", "application/json")
        
        w := httptest.NewRecorder()
        
        // Act
        router.ServeHTTP(w, req)
        
        // Assert
        assert.Equal(t, http.StatusOK, w.Code)
        
        var response map[string]interface{}
        json.Unmarshal(w.Body.Bytes(), &response)
        assert.Equal(t, float64(200), response["code"])
        assert.Equal(t, "创建成功", response["message"])
    })
    
    t.Run("CreateReferral_InvalidJSON", func(t *testing.T) {
        // Arrange
        requestBody := `{invalid json}`
        
        req, _ := http.NewRequest("POST", "/api/v1/user/referral", strings.NewReader(requestBody))
        req.Header.Set("Content-Type", "application/json")
        
        w := httptest.NewRecorder()
        
        // Act
        router.ServeHTTP(w, req)
        
        // Assert
        assert.Equal(t, http.StatusBadRequest, w.Code)
        
        var response map[string]interface{}
        json.Unmarshal(w.Body.Bytes(), &response)
        assert.Equal(t, float64(400), response["code"])
    })
}
```

---

## 集成测试

### 1. 数据库集成测试

#### 1.1 测试数据库配置
```go
// integration_test.go
package user

import (
    "testing"
    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
)

// setupIntegrationTest 设置集成测试环境
func setupIntegrationTest() (*gorm.DB, func()) {
    // 使用内存SQLite数据库
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    if err != nil {
        panic("failed to connect database")
    }
    
    // 自动迁移
    db.AutoMigrate(&User{}, &UserReferral{})
    
    // 插入测试数据
    seedTestData(db)
    
    cleanup := func() {
        // 清理测试数据
        db.Exec("DELETE FROM user_referrals")
        db.Exec("DELETE FROM users")
    }
    
    return db, cleanup
}

// seedTestData 插入测试数据
func seedTestData(db *gorm.DB) {
    users := []User{
        {ID: 1, Mobile: "13800000001", Status: 1},
        {ID: 2, Mobile: "13800000002", Status: 1},
        {ID: 3, Mobile: "13800000003", Status: 1},
        {ID: 4, Mobile: "13800000004", Status: 1},
    }
    
    for _, user := range users {
        db.Create(&user)
    }
}
```

#### 1.2 完整业务流程测试
```go
// TestReferralIntegration_CompleteFlow 测试完整的分销流程
func TestReferralIntegration_CompleteFlow(t *testing.T) {
    db, cleanup := setupIntegrationTest()
    defer cleanup()
    
    // 初始化服务
    userRepo := NewUserRepository(db)
    referralRepo := NewUserReferralRepository(db)
    referralService := NewUserReferralService(referralRepo, userRepo)
    
    t.Run("Complete_Referral_Flow", func(t *testing.T) {
        // 1. 创建一级分销关系
        createReq1 := &ReferralCreateRequest{
            UserID:     2,
            ReferrerID: 1,
        }
        err := referralService.CreateReferral(createReq1)
        assert.NoError(t, err)
        
        // 2. 创建二级分销关系
        createReq2 := &ReferralCreateRequest{
            UserID:     3,
            ReferrerID: 2,
        }
        err = referralService.CreateReferral(createReq2)
        assert.NoError(t, err)
        
        // 3. 创建三级分销关系
        createReq3 := &ReferralCreateRequest{
            UserID:     4,
            ReferrerID: 3,
        }
        err = referralService.CreateReferral(createReq3)
        assert.NoError(t, err)
        
        // 4. 验证分销关系
        // 验证用户2的推荐人是用户1
        referrer2, err := referralService.GetReferrer(2)
        assert.NoError(t, err)
        assert.Equal(t, uint(1), referrer2.ReferrerID)
        
        // 验证用户1的直接推荐用户
        directReferrals, err := referralService.GetReferrals(1, 1, 10)
        assert.NoError(t, err)
        assert.Len(t, directReferrals.Items, 1)
        assert.Equal(t, uint(2), directReferrals.Items[0].UserID)
        
        // 5. 验证统计数据
        stats, err := referralService.GetReferralStatistics(1)
        assert.NoError(t, err)
        assert.Equal(t, 1, stats.DirectReferrals)
        assert.Equal(t, 3, stats.TotalReferrals) // 包括间接推荐
        
        // 6. 测试佣金更新
        err = referralService.UpdateReferralCommission(2, 100.50)
        assert.NoError(t, err)
        
        // 验证佣金更新
        updatedStats, err := referralService.GetReferralStatistics(1)
        assert.NoError(t, err)
        assert.Equal(t, 100.50, updatedStats.DirectCommission)
    })
}
```

### 2. API 集成测试

#### 2.1 完整 API 流程测试
```go
// TestReferralAPI_Integration API集成测试
func TestReferralAPI_Integration(t *testing.T) {
    // 设置测试服务器
    db, cleanup := setupIntegrationTest()
    defer cleanup()
    
    app := setupTestApp(db)
    server := httptest.NewServer(app)
    defer server.Close()
    
    client := &http.Client{}
    baseURL := server.URL
    
    t.Run("API_Integration_Flow", func(t *testing.T) {
        // 1. 创建分销关系
        createURL := baseURL + "/api/v1/user/referral"
        createBody := `{"user_id": 2, "referrer_id": 1}`
        
        req, _ := http.NewRequest("POST", createURL, strings.NewReader(createBody))
        req.Header.Set("Content-Type", "application/json")
        
        resp, err := client.Do(req)
        assert.NoError(t, err)
        assert.Equal(t, http.StatusOK, resp.StatusCode)
        
        // 2. 查询分销统计
        statsURL := baseURL + "/api/v1/user/referral/statistics?user_id=1"
        
        req, _ = http.NewRequest("GET", statsURL, nil)
        resp, err = client.Do(req)
        assert.NoError(t, err)
        assert.Equal(t, http.StatusOK, resp.StatusCode)
        
        var statsResp map[string]interface{}
        json.NewDecoder(resp.Body).Decode(&statsResp)
        
        data := statsResp["data"].(map[string]interface{})
        assert.Equal(t, float64(1), data["direct_referrals"])
        
        // 3. 查询推荐用户列表
        listURL := baseURL + "/api/v1/user/referral/list?user_id=1&page=1&page_size=10"
        
        req, _ = http.NewRequest("GET", listURL, nil)
        resp, err = client.Do(req)
        assert.NoError(t, err)
        assert.Equal(t, http.StatusOK, resp.StatusCode)
        
        var listResp map[string]interface{}
        json.NewDecoder(resp.Body).Decode(&listResp)
        
        data = listResp["data"].(map[string]interface{})
        items := data["items"].([]interface{})
        assert.Len(t, items, 1)
    })
}
```

---

## 性能测试

### 1. 基准测试

#### 1.1 服务层性能测试
```go
// BenchmarkReferralService 分销服务性能测试
func BenchmarkReferralService(b *testing.B) {
    db, cleanup := setupIntegrationTest()
    defer cleanup()
    
    userRepo := NewUserRepository(db)
    referralRepo := NewUserReferralRepository(db)
    service := NewUserReferralService(referralRepo, userRepo)
    
    // 预先创建测试数据
    for i := 1; i <= 1000; i++ {
        user := &User{
            ID:     uint(i),
            Mobile: fmt.Sprintf("138%08d", i),
            Status: 1,
        }
        db.Create(user)
    }
    
    b.Run("CreateReferral", func(b *testing.B) {
        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            req := &ReferralCreateRequest{
                UserID:     uint(i%500 + 501), // 避免重复
                ReferrerID: uint(i%500 + 1),
            }
            service.CreateReferral(req)
        }
    })
    
    b.Run("GetReferralStatistics", func(b *testing.B) {
        // 先创建一些分销关系
        for i := 1; i <= 100; i++ {
            req := &ReferralCreateRequest{
                UserID:     uint(i + 500),
                ReferrerID: uint(i),
            }
            service.CreateReferral(req)
        }
        
        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            service.GetReferralStatistics(uint(i%100 + 1))
        }
    })
}
```

#### 1.2 数据库查询性能测试
```go
// BenchmarkReferralRepository 仓储层性能测试
func BenchmarkReferralRepository(b *testing.B) {
    db, cleanup := setupIntegrationTest()
    defer cleanup()
    
    repo := NewUserReferralRepository(db)
    
    // 创建大量测试数据
    for i := 1; i <= 10000; i++ {
        referral := &UserReferral{
            UserID:       uint(i),
            ReferrerID:   uint((i-1)/10 + 1),
            Level:        1,
            Status:       1,
            Commission:   float64(i * 10),
            ReferralTime: time.Now(),
        }
        db.Create(referral)
    }
    
    b.Run("GetDirectReferrals", func(b *testing.B) {
        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            repo.GetDirectReferrals(uint(i%1000 + 1))
        }
    })
    
    b.Run("GetAllReferrals", func(b *testing.B) {
        b.ResetTimer()
        for i := 0; i < b.N; i++ {
            repo.GetAllReferrals(uint(i%1000 + 1))
        }
    })
}
```

### 2. 压力测试

#### 2.1 并发创建分销关系测试
```go
// TestReferralConcurrency 并发测试
func TestReferralConcurrency(t *testing.T) {
    db, cleanup := setupIntegrationTest()
    defer cleanup()
    
    userRepo := NewUserRepository(db)
    referralRepo := NewUserReferralRepository(db)
    service := NewUserReferralService(referralRepo, userRepo)
    
    // 创建测试用户
    for i := 1; i <= 1000; i++ {
        user := &User{
            ID:     uint(i),
            Mobile: fmt.Sprintf("138%08d", i),
            Status: 1,
        }
        db.Create(user)
    }
    
    t.Run("Concurrent_Create_Referral", func(t *testing.T) {
        var wg sync.WaitGroup
        concurrency := 100
        
        for i := 0; i < concurrency; i++ {
            wg.Add(1)
            go func(index int) {
                defer wg.Done()
                
                req := &ReferralCreateRequest{
                    UserID:     uint(index + 501),
                    ReferrerID: uint(index + 1),
                }
                
                err := service.CreateReferral(req)
                assert.NoError(t, err)
            }(i)
        }
        
        wg.Wait()
        
        // 验证创建结果
        var count int64
        db.Model(&UserReferral{}).Count(&count)
        assert.Equal(t, int64(concurrency), count)
    })
}
```

#### 2.2 API 压力测试脚本
```bash
#!/bin/bash
# load_test.sh - API压力测试脚本

# 安装依赖
# go install github.com/rakyll/hey@latest

echo "开始API压力测试..."

# 测试创建分销关系接口
echo "测试创建分销关系接口"
hey -n 1000 -c 50 -m POST \
    -H "Content-Type: application/json" \
    -d '{"user_id": 2, "referrer_id": 1}' \
    http://localhost:8080/api/v1/user/referral

# 测试查询统计接口
echo "测试查询统计接口"
hey -n 1000 -c 50 \
    http://localhost:8080/api/v1/user/referral/statistics?user_id=1

# 测试查询列表接口
echo "测试查询列表接口"
hey -n 1000 -c 50 \
    "http://localhost:8080/api/v1/user/referral/list?user_id=1&page=1&page_size=10"

echo "压力测试完成"
```

---

## 端到端测试

### 1. 用户注册推荐流程测试

#### 1.1 Selenium 自动化测试
```go
// e2e_test.go
package e2e

import (
    "testing"
    "time"
    "github.com/tebeka/selenium"
    "github.com/stretchr/testify/assert"
)

// TestReferralE2E 端到端测试
func TestReferralE2E(t *testing.T) {
    // 启动 WebDriver
    driver, err := selenium.NewRemote(selenium.Capabilities{
        "browserName": "chrome",
    }, "http://localhost:4444/wd/hub")
    if err != nil {
        t.Skip("WebDriver not available")
    }
    defer driver.Quit()
    
    baseURL := "http://localhost:3000" // 前端应用地址
    
    t.Run("User_Registration_With_Referral", func(t *testing.T) {
        // 1. 推荐人登录并获取邀请链接
        err := driver.Get(baseURL + "/login")
        assert.NoError(t, err)
        
        // 输入登录信息
        mobileInput, _ := driver.FindElement(selenium.ByName, "mobile")
        mobileInput.SendKeys("13800000001")
        
        codeInput, _ := driver.FindElement(selenium.ByName, "code")
        codeInput.SendKeys("123456")
        
        loginBtn, _ := driver.FindElement(selenium.ByXPATH, "//button[@type='submit']")
        loginBtn.Click()
        
        // 等待登录完成
        time.Sleep(2 * time.Second)
        
        // 2. 访问分销中心
        err = driver.Get(baseURL + "/referral")
        assert.NoError(t, err)
        
        // 获取邀请链接
        inviteLink, _ := driver.FindElement(selenium.ByID, "invite-link")
        linkText, _ := inviteLink.GetAttribute("value")
        assert.Contains(t, linkText, "referrer=")
        
        // 3. 模拟好友点击邀请链接注册
        err = driver.Get(linkText)
        assert.NoError(t, err)
        
        // 填写注册信息
        mobileInput, _ = driver.FindElement(selenium.ByName, "mobile")
        mobileInput.SendKeys("13800000002")
        
        codeInput, _ = driver.FindElement(selenium.ByName, "verify_code")
        codeInput.SendKeys("123456")
        
        registerBtn, _ := driver.FindElement(selenium.ByXPATH, "//button[@type='submit']")
        registerBtn.Click()
        
        // 等待注册完成
        time.Sleep(3 * time.Second)
        
        // 4. 验证注册成功
        successMsg, _ := driver.FindElement(selenium.ByClassName, "success-message")
        msgText, _ := successMsg.Text()
        assert.Contains(t, msgText, "注册成功")
        
        // 5. 验证分销关系建立
        // 推荐人重新登录查看统计
        err = driver.Get(baseURL + "/referral")
        assert.NoError(t, err)
        
        // 检查推荐用户数量
        referralCount, _ := driver.FindElement(selenium.ByID, "referral-count")
        countText, _ := referralCount.Text()
        assert.Equal(t, "1", countText)
    })
}
```

### 2. 佣金计算流程测试

#### 2.1 完整业务流程测试
```go
// TestCommissionCalculationE2E 佣金计算端到端测试
func TestCommissionCalculationE2E(t *testing.T) {
    // 设置测试环境
    testEnv := setupE2ETestEnvironment()
    defer testEnv.Cleanup()
    
    t.Run("Complete_Commission_Flow", func(t *testing.T) {
        // 1. 创建测试用户和分销关系
        users := createTestUsers(testEnv.DB, 4)
        createReferralChain(testEnv.DB, users)
        
        // 2. 模拟用户下单
        order := createTestOrder(testEnv.DB, users[3].ID, 1000.00)
        
        // 3. 触发佣金计算
        commissionService := testEnv.CommissionService
        err := commissionService.CalculateCommission(order.ID)
        assert.NoError(t, err)
        
        // 4. 验证佣金记录
        commissions := getCommissionsByOrder(testEnv.DB, order.ID)
        assert.Len(t, commissions, 3) // 三级分销
        
        // 验证一级佣金
        level1Commission := findCommissionByLevel(commissions, 1)
        assert.NotNil(t, level1Commission)
        assert.Equal(t, 50.00, level1Commission.Amount) // 5%
        
        // 验证二级佣金
        level2Commission := findCommissionByLevel(commissions, 2)
        assert.NotNil(t, level2Commission)
        assert.Equal(t, 30.00, level2Commission.Amount) // 3%
        
        // 验证三级佣金
        level3Commission := findCommissionByLevel(commissions, 3)
        assert.NotNil(t, level3Commission)
        assert.Equal(t, 10.00, level3Commission.Amount) // 1%
        
        // 5. 验证用户分销统计更新
        stats1, _ := testEnv.ReferralService.GetReferralStatistics(users[0].ID)
        assert.Equal(t, 90.00, stats1.TotalCommission) // 50+30+10
        
        stats2, _ := testEnv.ReferralService.GetReferralStatistics(users[1].ID)
        assert.Equal(t, 40.00, stats2.TotalCommission) // 30+10
        
        stats3, _ := testEnv.ReferralService.GetReferralStatistics(users[2].ID)
        assert.Equal(t, 10.00, stats3.TotalCommission) // 10
    })
}
```

---

## 测试数据管理

### 1. 测试数据工厂

```go
// test_factory.go
package user

import (
    "time"
    "gorm.io/gorm"
)

// UserFactory 用户工厂
type UserFactory struct {
    db *gorm.DB
}

// NewUserFactory 创建用户工厂
func NewUserFactory(db *gorm.DB) *UserFactory {
    return &UserFactory{db: db}
}

// CreateUser 创建测试用户
func (f *UserFactory) CreateUser(mobile string) *User {
    user := &User{
        Mobile:    mobile,
        Status:    1,
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }
    f.db.Create(user)
    return user
}

// CreateUsers 批量创建用户
func (f *UserFactory) CreateUsers(count int) []*User {
    users := make([]*User, count)
    for i := 0; i < count; i++ {
        mobile := fmt.Sprintf("138%08d", i+1)
        users[i] = f.CreateUser(mobile)
    }
    return users
}

// ReferralFactory 分销关系工厂
type ReferralFactory struct {
    db *gorm.DB
}

// NewReferralFactory 创建分销关系工厂
func NewReferralFactory(db *gorm.DB) *ReferralFactory {
    return &ReferralFactory{db: db}
}

// CreateReferral 创建分销关系
func (f *ReferralFactory) CreateReferral(userID, referrerID uint, level int) *UserReferral {
    referral := &UserReferral{
        UserID:       userID,
        ReferrerID:   referrerID,
        Level:        level,
        Status:       1,
        Commission:   0,
        ReferralTime: time.Now(),
        CreatedAt:    time.Now(),
        UpdatedAt:    time.Now(),
    }
    f.db.Create(referral)
    return referral
}

// CreateReferralChain 创建分销链
func (f *ReferralFactory) CreateReferralChain(users []*User) {
    for i := 1; i < len(users); i++ {
        f.CreateReferral(users[i].ID, users[i-1].ID, 1)
    }
}
```

### 2. 测试数据清理

```go
// test_cleanup.go
package user

import "gorm.io/gorm"

// TestCleaner 测试数据清理器
type TestCleaner struct {
    db *gorm.DB
}

// NewTestCleaner 创建测试清理器
func NewTestCleaner(db *gorm.DB) *TestCleaner {
    return &TestCleaner{db: db}
}

// CleanAll 清理所有测试数据
func (c *TestCleaner) CleanAll() {
    c.db.Exec("DELETE FROM referral_commissions")
    c.db.Exec("DELETE FROM user_referrals")
    c.db.Exec("DELETE FROM users")
    c.db.Exec("DELETE FROM orders")
}

// CleanReferrals 清理分销相关数据
func (c *TestCleaner) CleanReferrals() {
    c.db.Exec("DELETE FROM referral_commissions")
    c.db.Exec("DELETE FROM user_referrals")
}

// CleanUsers 清理用户数据
func (c *TestCleaner) CleanUsers() {
    c.db.Exec("DELETE FROM users WHERE mobile LIKE '138%'")
}
```

---

## 测试执行和报告

### 1. 测试执行脚本

```bash
#!/bin/bash
# run_tests.sh - 测试执行脚本

set -e

echo "开始执行分销模块测试..."

# 设置测试环境变量
export GO_ENV=test
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=test_db
export DB_USER=test
export DB_PASS=test123

# 1. 运行单元测试
echo "执行单元测试..."
go test -v -race -coverprofile=coverage.out ./modules/user/...

# 2. 运行集成测试
echo "执行集成测试..."
go test -v -tags=integration ./modules/user/tests/integration/...

# 3. 运行性能测试
echo "执行性能测试..."
go test -v -bench=. -benchmem ./modules/user/tests/benchmark/...

# 4. 生成测试报告
echo "生成测试报告..."
go tool cover -html=coverage.out -o coverage.html

# 5. 运行端到端测试（可选）
if [ "$RUN_E2E" = "true" ]; then
    echo "执行端到端测试..."
    go test -v -tags=e2e ./modules/user/tests/e2e/...
fi

echo "测试执行完成！"
echo "测试覆盖率报告: coverage.html"
```

### 2. CI/CD 集成

```yaml
# .github/workflows/test.yml
name: 分销模块测试

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root123
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: 设置 Go 环境
      uses: actions/setup-go@v3
      with:
        go-version: 1.19
    
    - name: 安装依赖
      run: go mod download
    
    - name: 运行单元测试
      run: |
        go test -v -race -coverprofile=coverage.out ./modules/user/...
        go tool cover -func=coverage.out
    
    - name: 运行集成测试
      env:
        DB_HOST: localhost
        DB_PORT: 3306
        DB_NAME: test_db
        DB_USER: root
        DB_PASS: root123
      run: go test -v -tags=integration ./modules/user/tests/integration/...
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
```

### 3. 测试报告模板

```markdown
# 分销模块测试报告

## 测试概览

- **测试时间**: 2024-01-15 10:30:00
- **测试版本**: v1.2.0
- **测试环境**: 测试环境
- **执行人员**: 张三

## 测试结果统计

| 测试类型 | 总数 | 通过 | 失败 | 跳过 | 通过率 |
|---------|------|------|------|------|--------|
| 单元测试 | 156  | 154  | 2    | 0    | 98.7%  |
| 集成测试 | 45   | 43   | 2    | 0    | 95.6%  |
| 性能测试 | 12   | 12   | 0    | 0    | 100%   |
| E2E测试  | 8    | 6    | 2    | 0    | 75.0%  |

## 代码覆盖率

- **总体覆盖率**: 85.2%
- **服务层覆盖率**: 92.1%
- **仓储层覆盖率**: 88.5%
- **控制器覆盖率**: 76.3%

## 失败测试分析

### 单元测试失败

1. **TestCreateReferral_DatabaseError**
   - 失败原因: 数据库连接超时
   - 影响程度: 低
   - 修复建议: 增加重试机制

2. **TestGetReferralStatistics_LargeDataset**
   - 失败原因: 查询超时
   - 影响程度: 中
   - 修复建议: 优化查询性能

### 集成测试失败

1. **TestReferralAPI_ConcurrentCreate**
   - 失败原因: 并发创建时出现死锁
   - 影响程度: 高
   - 修复建议: 优化事务处理

## 性能测试结果

| 测试项目 | QPS | 平均响应时间 | 95%响应时间 | 内存使用 |
|---------|-----|-------------|-------------|----------|
| 创建分销关系 | 1,200 | 15ms | 45ms | 2.1MB |
| 查询统计信息 | 3,500 | 8ms | 25ms | 1.8MB |
| 查询推荐列表 | 2,800 | 12ms | 35ms | 2.5MB |

## 建议和改进

1. **性能优化**
   - 添加数据库查询缓存
   - 优化复杂查询的SQL
   - 考虑使用读写分离

2. **测试改进**
   - 增加边界条件测试
   - 完善异常场景测试
   - 提高E2E测试稳定性

3. **代码质量**
   - 提高控制器层测试覆盖率
   - 增加错误处理测试
   - 完善日志记录
```

通过以上全面的测试指南，可以确保分销模块的质量和稳定性，为生产环境的部署提供可靠保障。