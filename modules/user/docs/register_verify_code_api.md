# 手机号验证码注册API文档

## API概述

该API实现用户通过手机号和短信验证码进行注册，是系统用户注册流程中的一个重要环节。通过验证用户手机号的有效性，提高用户账号的安全性和真实性。注册成功后，系统会自动为用户创建默认密码，便于用户后续登录。

## 基本信息

- **接口URL**: `/api/v1/user/register/verify-code`
- **请求方法**: `POST`
- **数据格式**: `application/json`
- **认证要求**: 无需认证（公开接口）

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 校验规则 |
|-------|-----|------|------|---------|
| mobile | string | 是 | 用户手机号 | 必填，符合手机号格式校验 |
| code | string | 是 | 短信验证码 | 必填，通常为6位数字 |
| nickname | string | 否 | 用户昵称 | 可选，最大长度50个字符，如不提供则自动生成 |
| gender | int | 否 | 用户性别 | 范围0-2：0-未知，1-男，2-女 |
| email | string | 否 | 用户邮箱 | 符合邮箱格式校验（如提供） |
| referrer_id | int64 | 否 | 推荐人ID | 可选，用于推荐关系绑定 |

## 请求示例

```json
{
  "mobile": "13800138000",
  "code": "123456",
  "nickname": "张三",
  "gender": 1,
  "email": "<EMAIL>",
  "referrer_id": 10086
}
```

## 响应参数

### 成功响应

- **状态码**: 200
- **返回数据**: 包含新创建用户的ID

```json
{
  "code": 0,
  "msg": "success",
  "data": 10010  // 新创建的用户ID
}
```

### 错误响应

#### 参数错误（400）

```json
{
  "code": 40000,
  "msg": "无效的请求参数",
  "data": null
}
```

#### 验证码错误（400）

```json
{
  "code": 40001,
  "msg": "验证码错误或已过期",
  "data": null
}
```

#### 手机号已存在（400）

```json
{
  "code": 40002,
  "msg": "该手机号已注册",
  "data": null
}
```

#### 服务器内部错误（500）

```json
{
  "code": 50000,
  "msg": "服务器内部错误",
  "data": null
}
```

## 业务流程

1. 客户端通过之前的发送验证码API获取短信验证码
2. 用户填写注册信息，包括手机号、收到的验证码及其他必要信息
3. 客户端提交注册请求到本API
4. 服务器验证参数的有效性
5. 验证短信验证码是否正确且在有效期内
6. 检查手机号是否已被注册
7. 创建新用户账号
8. 如提供推荐人ID，建立推荐关系
9. 返回新创建用户的ID

## 注意事项

1. 手机号必须是有效的中国大陆手机号
2. 验证码通常有效期为10分钟，过期需重新获取
3. 邮箱虽为可选项，但如提供则必须符合邮箱格式
4. 该接口已配置CORS，允许跨域请求
5. 推荐人ID如果提供，系统会验证该ID是否存在有效用户
6. **默认密码设置**: 系统会自动为新用户设置默认密码为 `123456`，用户可以在注册后使用手机号+密码的方式登录，并可以通过「修改密码」功能更改初始密码

## 相关接口

- **发送注册验证码**: `/api/v1/user/register/send-code`
- **用户登录**: `/api/v1/user/login`
- **验证码登录**: `/api/v1/user/login/verify-code`

## 安全考量

- 接口实现了请求频率限制，防止恶意注册
- 验证码有次数限制，同一手机号每日获取次数有上限
- API请求记录完整日志，便于安全审计和问题排查
