# 用户模块安全机制说明

本文档详细描述了用户模块中实现的安全机制和数据保护措施，帮助开发人员和安全审计人员了解系统的安全设计。

## 认证机制

### JWT认证

用户模块采用JSON Web Token (JWT) 作为认证机制，用于保护API接口安全：

#### 令牌类型

系统使用两种令牌：

1. **访问令牌(Access Token)**
   - 用于访问受保护的API资源
   - 有效期较短（默认2小时），减少被盗用的危害
   - 包含用户ID、用户名等基本信息
   - 使用HMAC-SHA256算法签名

2. **刷新令牌(Refresh Token)**
   - 用于获取新的访问令牌
   - 有效期较长（默认30天），支持长期登录状态维持
   - 一次性使用，使用后立即失效
   - 存储在服务器端数据库中，可被手动撤销

#### 令牌结构

访问令牌的JWT结构示例：

```
{  
  "header": {  
    "alg": "HS256",  
    "typ": "JWT"  
  },  
  "payload": {  
    "user_id": 10001,  
    "username": "zhangsan",  
    "exp": 1643803200,  
    "iat": 1643796000,  
    "jti": "f47ac10b-58cc-4372-a567-0e02b2c3d479"  
  },  
  "signature": "..."  
}  
```

### 认证流程

1. **登录流程**
   - 用户提供用户名/手机号和密码
   - 服务端验证凭据并生成访问令牌和刷新令牌
   - 返回令牌对给客户端

2. **API访问认证**
   - 客户端在请求头中添加`Authorization: Bearer {access_token}`
   - 服务端验证令牌有效性、签名和过期时间
   - 请求被路由到相应的控制器方法

3. **令牌刷新流程**
   - 访问令牌过期时，客户端发送刷新令牌
   - 服务端验证刷新令牌并生成新的令牌对
   - 旧的刷新令牌被废弃

4. **登出流程**
   - 将刷新令牌加入黑名单
   - 客户端销毁本地存储的令牌

## 密码安全

### 密码存储

系统采用以下措施确保密码安全：

1. **密码哈希**
   - 使用bcrypt算法进行密码哈希
   - 自动包含随机盐值，防止彩虹表攻击
   - 使用适当的工作因子(cost factor)平衡安全性和性能

2. **密码规则**
   - 密码长度要求：最少8个字符
   - 复杂性要求：必须同时包含大写字母、小写字母和数字
   - 禁止使用常见弱密码

### 密码验证流程

```
+-------------+     +----------------+     +-------------------+
|  获取用户记录  |     |   提取密码哈希    |     |    比较密码哈希     |
|              +---->+                +---->+                   |
+-------------+     +----------------+     +--------+----------+
                                                    |
                                                    v
+----------------+     +----------------+
|                |     |                |
|  更新登录记录     +<----+  返回验证结果    |
|                |     |                |
+----------------+     +----------------+
```

### 密码恢复

系统提供安全的密码恢复机制：

1. **基于手机验证码的重置**
   - 发送一次性验证码到用户注册手机
   - 验证码有效期短（通常5分钟）
   - 验证通过后允许设置新密码

2. **基于邮箱的重置**
   - 发送重置链接到用户邮箱
   - 链接包含加密的一次性令牌
   - 链接有效期限制（通常24小时）

## 访问控制

### 中间件实现

系统使用多层中间件实现访问控制：

1. **认证中间件**
   - 验证JWT令牌有效性
   - 从令牌中提取用户信息
   - 对无效令牌返回401错误

2. **日志中间件**
   - 记录敏感操作的访问日志
   - 捕获请求IP、用户代理等信息
   - 支持安全审计和异常检测

### 数据访问控制

1. **资源隔离**
   - 用户只能访问和修改自己的资源（如地址信息）
   - 所有资源操作都会检查资源所属关系

2. **敏感字段保护**
   - 密码等敏感字段永不返回给前端
   - 手机号、邮箱等在返回时进行部分遮蔽

## 登录保护

### 异常登录检测

系统实现了以下异常登录检测机制：

1. **IP变化检测**
   - 记录用户历史登录IP
   - 检测异常的登录位置变化

2. **登录频率限制**
   - 实现基于IP的请求频率限制
   - 防止暴力破解和字典攻击

3. **登录失败处理**
   - 连续多次登录失败后账户临时锁定
   - 锁定时间随失败次数增加而增加

## 数据保护

### 传输安全

1. **TLS加密**
   - 所有API通信通过HTTPS进行
   - 最低支持TLS 1.2协议
   - 使用强密码套件

2. **敏感数据处理**
   - 身份证号等高敏感信息存储时加密
   - 密码等敏感信息不记录到日志

### 日志审计

系统实现了全面的安全审计日志：

1. **用户操作日志**
   - 记录所有敏感操作（登录、修改密码等）
   - 记录操作发生的时间、IP和设备信息
   - 日志数据不可篡改

2. **异常行为日志**
   - 记录认证失败、权限违规等异常
   - 支持安全事件追溯和取证

## 安全最佳实践

### 开发安全

1. **输入验证**
   - 所有用户输入经过严格验证
   - 防止SQL注入、XSS等常见攻击
   - 使用参数化查询操作数据库

2. **安全配置**
   - 敏感配置参数（如JWT密钥）通过环境变量提供
   - 不在代码或配置文件中硬编码密钥

### 集成安全

1. **依赖安全**
   - 定期更新依赖库版本
   - 检查第三方组件的已知漏洞

2. **安全测试**
   - 实施定期安全代码审查
   - 执行自动化安全扫描
   - 进行渗透测试验证安全策略有效性

## 安全改进计划

以下是未来计划实施的安全增强措施：

1. **多因素认证**
   - 为关键操作增加二次验证
   - 支持SMS、邮件或认证器应用

2. **风险评分系统**
   - 基于用户行为和环境因素动态评估风险
   - 根据风险级别调整安全策略

3. **基于角色的访问控制**
   - 实现细粒度的权限管理
   - 支持管理员分配不同级别权限
