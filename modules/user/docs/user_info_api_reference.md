# 用户信息管理 API 详细文档

## 概述

本文档详细描述了用户模块中用户信息管理相关的API接口，包括获取用户信息和更新用户信息两个核心功能。这些API位于 `/Users/<USER>/Documents/2025works/o_-mall_backend/modules/user/routers/router.go` 文件的第104-105行。

## API 路由信息

**路由定义位置**: `router.go#L104-105`
```go
web.NSRouter("/info", userController, "get:GetUserInfo;put:UpdateUserInfo;options:Options")
```

**完整访问路径**: `/api/v1/user/secured/info`

**认证要求**: 需要JWT认证（通过 `middlewares.JWTFilter` 中间件保护）

## 1. 获取用户信息 API

### 基本信息
- **HTTP方法**: `GET`
- **路径**: `/api/v1/user/secured/info`
- **控制器方法**: `UserController.GetUserInfo`
- **认证**: 必需（JWT Token）

### 请求参数

#### Headers
```http
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

#### 请求体
无需请求体，用户ID从JWT Token中获取。

### 响应格式

#### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "username": "user123",
    "nickname": "用户昵称",
    "avatar": "https://example.com/avatar.jpg",
    "mobile": "13800138000",
    "email": "<EMAIL>",
    "gender": 1,
    "birthday": "1990-01-01",
    "balance": 100.50,
    "points": 1000,
    "level": 1,
    "status": 1,
    "last_login_at": "2024-01-01T10:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "referral_code": "ABC123"
  }
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `id` | int64 | 用户唯一标识 | 1 |
| `username` | string | 用户名 | "user123" |
| `nickname` | string | 用户昵称 | "用户昵称" |
| `avatar` | string | 头像URL | "https://example.com/avatar.jpg" |
| `mobile` | string | 手机号 | "13800138000" |
| `email` | string | 邮箱地址 | "<EMAIL>" |
| `gender` | int | 性别（0:未知, 1:男, 2:女） | 1 |
| `birthday` | string | 生日（YYYY-MM-DD格式） | "1990-01-01" |
| `balance` | float64 | 账户余额 | 100.50 |
| `points` | int64 | 积分 | 1000 |
| `level` | int | 用户等级 | 1 |
| `status` | int | 用户状态（1:正常, 0:禁用） | 1 |
| `last_login_at` | time.Time | 最后登录时间 | "2024-01-01T10:00:00Z" |
| `created_at` | time.Time | 注册时间 | "2024-01-01T00:00:00Z" |
| `referral_code` | string | 推荐码 | "ABC123" |

#### 错误响应

**401 Unauthorized - 未认证**
```json
{
  "code": 401,
  "message": "未授权访问"
}
```

**404 Not Found - 用户不存在**
```json
{
  "code": 404,
  "message": "用户不存在"
}
```

**500 Internal Server Error - 服务器错误**
```json
{
  "code": 500,
  "message": "服务器内部错误"
}
```

### 业务逻辑流程

1. **认证验证**: JWT中间件验证Token有效性
2. **用户ID提取**: 从JWT Token中提取用户ID
3. **数据查询**: 调用 `userService.GetUserByID()` 查询用户信息
4. **数据转换**: 通过 `convertToUserResponse()` 转换为响应格式
5. **响应返回**: 返回用户信息或错误信息

### 代码实现位置
- **控制器**: `/controllers/user_controller.go` - `GetUserInfo` 方法
- **服务层**: `/services/user_service.go` - `GetUserByID` 方法
- **数据转换**: `/services/user_service.go` - `convertToUserResponse` 方法

## 2. 更新用户信息 API

### 基本信息
- **HTTP方法**: `PUT`
- **路径**: `/api/v1/user/secured/info`
- **控制器方法**: `UserController.UpdateUserInfo`
- **认证**: 必需（JWT Token）

### 请求参数

#### Headers
```http
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

#### 请求体
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new-avatar.jpg",
  "email": "<EMAIL>",
  "gender": 2,
  "birthday": "1995-05-15"
}
```

#### 请求字段说明

| 字段名 | 类型 | 必填 | 验证规则 | 说明 | 示例值 |
|--------|------|------|----------|------|--------|
| `nickname` | string | 否 | 长度1-50字符 | 用户昵称 | "新昵称" |
| `avatar` | string | 否 | 有效URL格式 | 头像URL | "https://example.com/avatar.jpg" |
| `email` | string | 否 | 有效邮箱格式 | 邮箱地址 | "<EMAIL>" |
| `gender` | int | 否 | 0,1,2 | 性别（0:未知, 1:男, 2:女） | 1 |
| `birthday` | string | 否 | YYYY-MM-DD格式 | 生日 | "1990-01-01" |

#### 验证规则详细说明

**Nickname 验证**:
- 使用 `validate:"omitempty,min=1,max=50"` 标签
- 可选字段，如果提供则长度必须在1-50字符之间

**Avatar 验证**:
- 使用 `validate:"omitempty,url"` 标签
- 可选字段，如果提供则必须是有效的URL格式

**Email 验证**:
- 使用 `validate:"omitempty,email"` 标签
- 可选字段，如果提供则必须是有效的邮箱格式
- 系统会检查邮箱是否已被其他用户使用

**Gender 验证**:
- 使用 `validate:"omitempty,oneof=0 1 2"` 标签
- 可选字段，如果提供则必须是0、1或2中的一个

**Birthday 验证**:
- 使用 `validate:"omitempty"` 标签
- 可选字段，格式为YYYY-MM-DD

### 响应格式

#### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "用户信息更新成功",
  "data": null
}
```

#### 错误响应

**400 Bad Request - 参数验证失败**
```json
{
  "code": 400,
  "message": "参数验证失败: 昵称长度不能超过50个字符"
}
```

**400 Bad Request - 邮箱已存在**
```json
{
  "code": 400,
  "message": "邮箱已被其他用户使用"
}
```

**401 Unauthorized - 未认证**
```json
{
  "code": 401,
  "message": "未授权访问"
}
```

**404 Not Found - 用户不存在**
```json
{
  "code": 404,
  "message": "用户不存在"
}
```

**500 Internal Server Error - 服务器错误**
```json
{
  "code": 500,
  "message": "服务器内部错误"
}
```

### 业务逻辑流程

1. **认证验证**: JWT中间件验证Token有效性
2. **用户ID提取**: 从JWT Token中提取用户ID
3. **请求解析**: 解析请求体为 `userDto.UpdateUserRequest` 结构
4. **参数验证**: 使用validator进行字段验证
5. **邮箱唯一性检查**: 如果更新邮箱，检查是否已被其他用户使用
6. **数据更新**: 调用 `userService.UpdateUserInfo()` 更新用户信息
7. **响应返回**: 返回成功信息或错误信息

### 代码实现位置
- **控制器**: `/controllers/user_controller.go` - `UpdateUserInfo` 方法
- **服务层**: `/services/user_service.go` - `UpdateUserInfo` 方法
- **DTO定义**: `/dto/user_dto.go` - `UpdateUserRequest` 结构体

## 3. 数据模型定义

### User 模型 (models/user.go)

```go
type User struct {
    ID           int64     `gorm:"primaryKey;autoIncrement" json:"id"`
    Username     string    `gorm:"uniqueIndex;size:50;not null" json:"username"`
    Password     string    `gorm:"size:255;not null" json:"-"`
    Nickname     string    `gorm:"size:50" json:"nickname"`
    Avatar       string    `gorm:"size:255" json:"avatar"`
    Mobile       string    `gorm:"uniqueIndex;size:20" json:"mobile"`
    Email        string    `gorm:"uniqueIndex;size:100" json:"email"`
    Gender       int       `gorm:"default:0;comment:性别 0未知 1男 2女" json:"gender"`
    Birthday     string    `gorm:"size:10" json:"birthday"`
    Balance      float64   `gorm:"type:decimal(10,2);default:0.00" json:"balance"`
    Points       int64     `gorm:"default:0" json:"points"`
    Level        int       `gorm:"default:1" json:"level"`
    Status       int       `gorm:"default:1;comment:状态 1正常 0禁用" json:"status"`
    ReferralCode string    `gorm:"uniqueIndex;size:20" json:"referral_code"`
    WechatOpenID string    `gorm:"uniqueIndex;size:100" json:"wechat_open_id"`
    WechatUnionID string   `gorm:"uniqueIndex;size:100" json:"wechat_union_id"`
    LastLoginAt  time.Time `json:"last_login_at"`
    LastLoginIP  string    `gorm:"size:45" json:"last_login_ip"`
    RegisterIP   string    `gorm:"size:45" json:"register_ip"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

### UpdateUserRequest DTO (dto/user_dto.go)

```go
type UpdateUserRequest struct {
    Nickname string `json:"nickname" validate:"omitempty,min=1,max=50" description:"昵称"`
    Avatar   string `json:"avatar" validate:"omitempty,url" description:"头像URL"`
    Email    string `json:"email" validate:"omitempty,email" description:"邮箱"`
    Gender   int    `json:"gender" validate:"omitempty,oneof=0 1 2" description:"性别 0未知 1男 2女"`
    Birthday string `json:"birthday" validate:"omitempty" description:"生日 YYYY-MM-DD"`
}
```

### UserResponse DTO (dto/user_dto.go)

```go
type UserResponse struct {
    ID           int64     `json:"id"`
    Username     string    `json:"username"`
    Nickname     string    `json:"nickname"`
    Avatar       string    `json:"avatar"`
    Mobile       string    `json:"mobile"`
    Email        string    `json:"email"`
    Gender       int       `json:"gender"`
    Birthday     string    `json:"birthday"`
    Balance      float64   `json:"balance"`
    Points       int64     `json:"points"`
    Level        int       `json:"level"`
    Status       int       `json:"status"`
    LastLoginAt  time.Time `json:"last_login_at"`
    CreatedAt    time.Time `json:"created_at"`
    ReferralCode string    `json:"referral_code"`
}
```

## 4. 中间件和安全

### JWT认证中间件
- **位置**: `middlewares.JWTFilter`
- **功能**: 验证JWT Token的有效性，提取用户信息
- **Token格式**: `Bearer <JWT_TOKEN>`

### 用户日志中间件
- **位置**: `middlewares.UserLogMiddleware()`
- **功能**: 记录用户敏感操作日志
- **应用范围**: 整个用户模块的API

## 5. 错误处理

### 常见错误码

| 错误码 | 说明 | 可能原因 |
|--------|------|----------|
| 400 | 请求参数错误 | 参数验证失败、邮箱已存在等 |
| 401 | 未授权 | JWT Token无效或过期 |
| 404 | 资源不存在 | 用户不存在 |
| 500 | 服务器内部错误 | 数据库连接失败、系统异常等 |

### 日志记录
- 所有错误都会记录到系统日志中
- 使用 `logs.Error()` 记录详细错误信息
- 敏感操作会通过用户日志中间件记录

## 6. 使用示例

### 获取用户信息示例

```bash
curl -X GET \
  http://localhost:8080/api/v1/user/secured/info \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json'
```

### 更新用户信息示例

```bash
curl -X PUT \
  http://localhost:8080/api/v1/user/secured/info \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "nickname": "新昵称",
    "email": "<EMAIL>",
    "gender": 1
  }'
```

## 7. 注意事项

1. **认证要求**: 所有API都需要有效的JWT Token
2. **参数验证**: 严格按照DTO定义的验证规则进行参数校验
3. **邮箱唯一性**: 更新邮箱时会检查是否已被其他用户使用
4. **密码安全**: 密码字段在响应中不会返回（使用 `json:"-"` 标签）
5. **时间格式**: 时间字段使用ISO 8601格式
6. **错误处理**: 所有错误都有详细的错误信息和错误码
7. **日志记录**: 重要操作会记录到用户日志中

## 8. 相关文档

- [用户模块架构文档](./architecture.md)
- [数据模型文档](./data_models.md)
- [安全文档](./security.md)
- [API参考文档](./api_reference.md)

---

**文档版本**: v1.0  
**最后更新**: 2024-01-01  
**维护者**: 开发团队