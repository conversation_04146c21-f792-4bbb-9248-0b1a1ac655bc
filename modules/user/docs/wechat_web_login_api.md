# 微信网页扫码登录 API 文档

## 概述

本文档描述了微信网页扫码登录相关的 API 接口设计和使用说明。此功能允许用户通过扫描网页上显示的二维码完成微信授权登录。

## API 接口列表

| 接口名称 | 请求方法 | 接口路径 | 功能描述 |
| ------- | ------- | ------- | ------- |
| 获取微信登录二维码 | POST | /api/v1/user/wx/qrcode | 获取用于微信扫码登录的二维码信息 |
| 查询微信登录二维码状态 | POST | /api/v1/user/wx/qrcode/status | 查询二维码的当前登录状态 |
| 微信网页授权登录 | GET | /api/v1/user/wx/web/login | 处理微信授权码登录 |
| 微信网页授权回调 | GET | /api/v1/user/wx/web/callback | 处理微信授权回调并返回HTML页面 |

## 接口详细说明

### 1. 获取微信登录二维码

#### 接口信息

- **接口路径**：`/api/v1/user/wx/qrcode`
- **请求方法**：`POST`
- **接口描述**：获取用于微信扫码登录的二维码信息，前端需要根据返回信息生成二维码供用户扫描。

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| redirectURL | string | 否 | 登录成功后的重定向URL，如不提供则使用默认回调地址 |

#### 请求示例

```json
{
  "redirectURL": "https://www.example.com/auth-success"
}
```

#### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态信息 |
| data | object | 响应数据 |
| data.qrCodeURL | string | 生成二维码的URL，用户通过微信扫描此URL完成授权 |
| data.qrCodeID | string | 二维码唯一标识，用于后续查询状态 |
| data.expireTime | number | 二维码过期时间戳（秒） |
| data.qrContent | string | 二维码内容，可供前端直接生成二维码 |

#### 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "qrCodeURL": "https://open.weixin.qq.com/connect/qrconnect?appid=wx123456&redirect_uri=https%3A%2F%2Fapi.example.com%2Fwx-callback&response_type=code&scope=snsapi_login&state=random_state#wechat_redirect",
    "qrCodeID": "8a7b6c5d4e3f2g1h",
    "expireTime": 1623456789,
    "qrContent": "https://open.weixin.qq.com/connect/qrconnect?appid=wx123456&redirect_uri=https%3A%2F%2Fapi.example.com%2Fwx-callback&response_type=code&scope=snsapi_login&state=random_state#wechat_redirect"
  }
}
```

### 2. 查询微信登录二维码状态

#### 接口信息

- **接口路径**：`/api/v1/user/wx/qrcode/status`
- **请求方法**：`POST`
- **接口描述**：查询二维码的当前登录状态，前端可以轮询此接口获取登录进度。

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| qrCodeID | string | 是 | 二维码唯一标识 |

#### 请求示例

```json
{
  "qrCodeID": "8a7b6c5d4e3f2g1h"
}
```

#### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态信息 |
| data | object | 响应数据 |
| data.status | string | 二维码状态：waiting（等待扫码）、scanned（已扫码待确认）、success（登录成功）、expired（已过期）、canceled（已取消） |
| data.userInfo | object/null | 登录成功时返回的用户信息，其他状态为null |
| data.updateTime | string | 状态更新时间 |

#### 响应示例

**等待扫描状态**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "status": "waiting",
    "userInfo": null,
    "updateTime": "2023-06-12T12:34:56Z"
  }
}
```

**登录成功状态**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "status": "success",
    "userInfo": {
      "userID": 12345,
      "username": "wxuser_abc123",
      "nickname": "微信用户",
      "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/xxx/132",
      "token": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 3600,
        "tokenType": "Bearer"
      }
    },
    "updateTime": "2023-06-12T12:35:30Z"
  }
}
```

### 3. 微信网页授权登录

#### 接口信息

- **接口路径**：`/api/v1/user/wx/web/login`
- **请求方法**：`GET`
- **接口描述**：处理微信授权码登录，通常在微信授权后由微信服务器回调。

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| code | string | 是 | 微信授权码 |
| state | string | 是 | 状态参数，防CSRF攻击用 |

#### 响应参数

| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| code | int | 状态码，0表示成功 |
| message | string | 状态信息 |
| data | object | 响应数据 |
| data.userID | number | 用户ID |
| data.username | string | 用户名 |
| data.nickname | string | 用户昵称 |
| data.avatar | string | 用户头像URL |
| data.token | object | 用户登录令牌信息 |
| data.token.accessToken | string | 访问令牌 |
| data.token.refreshToken | string | 刷新令牌 |
| data.token.expiresIn | number | 访问令牌有效期（秒） |
| data.token.tokenType | string | 令牌类型，固定为"Bearer" |

#### 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "userID": 12345,
    "username": "wxuser_abc123",
    "nickname": "微信用户",
    "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/xxx/132",
    "token": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600,
      "tokenType": "Bearer"
    }
  }
}
```

### 4. 微信网页授权回调

#### 接口信息

- **接口路径**：`/api/v1/user/wx/web/callback`
- **请求方法**：`GET`
- **接口描述**：处理微信授权回调，并返回HTML页面进行前端处理。用户扫码授权成功后会重定向到此页面。

#### 请求参数

| 参数名 | 类型 | 是否必须 | 描述 |
| ----- | ---- | ------- | ---- |
| code | string | 是 | 微信授权码 |
| state | string | 是 | 状态参数，防CSRF攻击用 |

#### 响应格式

返回HTML页面，页面会自动处理登录结果：
1. 如果登录成功，将用户信息存储到localStorage中
2. 如果有父窗口(opener)，会通过postMessage将登录结果传递给父窗口
3. 根据情况自动关闭页面或跳转到首页

## 前端集成指南

### 微信扫码登录流程

1. 调用获取二维码接口，获取二维码信息
2. 使用qrContent生成二维码（可使用qrcode.js等库）
3. 轮询查询二维码状态接口（建议2-3秒一次）
4. 根据状态更新界面提示：
   - waiting：等待用户扫码
   - scanned：用户已扫码，等待确认
   - success：用户已授权登录成功，使用返回的token完成登录
   - expired：二维码已过期，提示用户刷新二维码
   - canceled：用户取消了授权，提示用户重试

### 示例代码

```javascript
// 获取微信登录二维码
async function getWechatQrCode() {
  const response = await fetch('/api/v1/user/wx/qrcode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      redirectURL: window.location.origin + '/auth-callback'
    })
  });
  
  const result = await response.json();
  if (result.code === 0) {
    // 生成二维码并显示
    generateQrCode(result.data.qrContent);
    // 开始轮询状态
    pollQrCodeStatus(result.data.qrCodeID);
  }
}

// 轮询二维码状态
async function pollQrCodeStatus(qrCodeID) {
  const polling = setInterval(async () => {
    const response = await fetch('/api/v1/user/wx/qrcode/status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        qrCodeID: qrCodeID
      })
    });
    
    const result = await response.json();
    if (result.code === 0) {
      updateQrCodeStatus(result.data.status);
      
      if (result.data.status === 'success') {
        // 登录成功，保存用户信息和令牌
        saveUserInfo(result.data.userInfo);
        clearInterval(polling);
      } else if (['expired', 'canceled'].includes(result.data.status)) {
        // 二维码失效，停止轮询
        clearInterval(polling);
      }
    }
  }, 2000); // 每2秒轮询一次
}
```

## 注意事项

1. 二维码有效期为5分钟，超时需要重新获取
2. 查询状态接口建议2-3秒轮询一次，避免过于频繁
3. 微信授权成功后会跳转到配置的回调地址，需确保回调地址可正常访问
4. 微信扫码登录需要在微信开放平台注册并配置相关参数
5. 用户首次登录会自动注册新账号，再次登录会关联到已有账号
