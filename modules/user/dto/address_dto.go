/**
 * 收货地址数据传输对象
 *
 * 该文件定义了用户收货地址相关的数据传输对象(DTO)，用于控制器和服务层之间传递数据。
 * 包括地址的添加、更新、查询等操作所需的请求和响应结构。
 */

package dto

// 添加收货地址请求
type AddAddressRequest struct {
	ReceiverName      string  `json:"receiver_name" valid:"Required;MaxSize(50)"`
	ReceiverMobile    string  `json:"receiver_mobile" valid:"Required;Mobile"`
	Province          string  `json:"province" valid:"Required;MaxSize(50)"`
	City              string  `json:"city" valid:"Required;MaxSize(50)"`
	District          string  `json:"district" valid:"Required;MaxSize(50)"`
	DetailedAddress   string  `json:"detailed_address" valid:"Required;MaxSize(255)"`
	PostalCode        string  `json:"postal_code" valid:"MaxSize(20)"`
	IsDefault         bool    `json:"is_default"`
	AddressTag        string  `json:"address_tag" valid:"MaxSize(50)"`
	LocationLongitude float64 `json:"location_longitude"`
	LocationLatitude  float64 `json:"location_latitude"`
}

// 更新收货地址请求
type UpdateAddressRequest struct {
	ID                int64   `json:"id" valid:"Required"`
	ReceiverName      string  `json:"receiver_name" valid:"Required;MaxSize(50)"`
	ReceiverMobile    string  `json:"receiver_mobile" valid:"Required;Mobile"`
	Province          string  `json:"province" valid:"Required;MaxSize(50)"`
	City              string  `json:"city" valid:"Required;MaxSize(50)"`
	District          string  `json:"district" valid:"Required;MaxSize(50)"`
	DetailedAddress   string  `json:"detailed_address" valid:"Required;MaxSize(255)"`
	PostalCode        string  `json:"postal_code" valid:"MaxSize(20)"`
	IsDefault         bool    `json:"is_default"`
	AddressTag        string  `json:"address_tag" valid:"MaxSize(50)"`
	LocationLongitude float64 `json:"location_longitude"`
	LocationLatitude  float64 `json:"location_latitude"`
}

// 设置默认地址请求
type SetDefaultAddressRequest struct {
	ID int64 `json:"id" valid:"Required"`
}

// 地址查询请求
type AddressQueryRequest struct {
	Page     int `json:"page" valid:"Min(1)"`
	PageSize int `json:"pageSize" valid:"Range(10,100)"`
}

// 收货地址响应
type AddressResponse struct {
	ID                int64   `json:"id"`
	ReceiverName      string  `json:"receiver_name"`
	ReceiverMobile    string  `json:"receiver_mobile"`
	Province          string  `json:"province"`
	City              string  `json:"city"`
	District          string  `json:"district"`
	DetailedAddress   string  `json:"detailed_address"`
	PostalCode        string  `json:"postal_code"`
	IsDefault         bool    `json:"is_default"`
	AddressTag        string  `json:"address_tag"`
	LocationLongitude float64 `json:"location_longitude,omitempty"`
	LocationLatitude  float64 `json:"location_latitude,omitempty"`
	FullAddress       string  `json:"full_address"` // 完整地址（省市区+详细地址）
}
