/**
 * 手机号绑定数据传输对象
 *
 * 该文件定义了与手机号绑定和修改相关的数据传输对象(DTO)，用于控制器和服务层之间传递数据。
 */

package dto

// BindMobileRequest 绑定手机号请求
type BindMobileRequest struct {
	Mobile string `json:"mobile" valid:"Required;Mobile" description:"手机号"` // 手机号
	Code   string `json:"code" valid:"Required" description:"验证码"`        // 验证码
}

// BindMobileResponse 绑定手机号响应
type BindMobileResponse struct {
	Success bool   `json:"success" description:"绑定结果"`  // 绑定结果
	Message string `json:"message" description:"结果描述"` // 结果描述
}

// ChangeMobileRequest 修改绑定手机号请求
type ChangeMobileRequest struct {
	OldMobile    string `json:"old_mobile" valid:"Required;Mobile" description:"原手机号"`     // 原手机号
	OldCode      string `json:"old_code" valid:"Required" description:"原手机号验证码"`         // 原手机号验证码
	NewMobile    string `json:"new_mobile" valid:"Required;Mobile" description:"新手机号"`     // 新手机号
	NewCode      string `json:"new_code" valid:"Required" description:"新手机号验证码"`         // 新手机号验证码
}

// ChangeMobileResponse 修改绑定手机号响应
type ChangeMobileResponse struct {
	Success bool   `json:"success" description:"修改结果"`  // 修改结果
	Message string `json:"message" description:"结果描述"` // 结果描述
}
