// Package dto 数据传输对象
package dto

import "time"

// CommissionSummaryResponse 佣金汇总响应
type CommissionSummaryResponse struct {
	// 总佣金金额
	TotalCommission float64 `json:"total_commission"`
	// 待结算佣金金额
	PendingCommission float64 `json:"pending_commission"`
	// 已结算佣金金额
	SettledCommission float64 `json:"settled_commission"`
	// 分销用户数量
	ReferralUserCount int64 `json:"referral_user_count"`
	// 一级分销佣金
	Level1Commission float64 `json:"level1_commission"`
	// 二级分销佣金
	Level2Commission float64 `json:"level2_commission"`
	// 三级分销佣金
	Level3Commission float64 `json:"level3_commission"`
	// 今日新增佣金
	TodayCommission float64 `json:"today_commission"`
	// 本月新增佣金
	MonthCommission float64 `json:"month_commission"`
}

// CommissionStatisticsRequest 佣金统计请求
type CommissionStatisticsRequest struct {
	// 开始日期
	StartDate time.Time `json:"start_date"`
	// 结束日期
	EndDate time.Time `json:"end_date"`
}

// DailyCommissionStat 每日佣金统计
type DailyCommissionStat struct {
	// 日期
	Date string `json:"date"`
	// 佣金金额
	Commission float64 `json:"commission"`
	// 订单数量
	OrderCount int64 `json:"order_count"`
}

// LevelCommissionStat 分销级别佣金统计
type LevelCommissionStat struct {
	// 分销级别
	Level int `json:"level"`
	// 佣金金额
	Commission float64 `json:"commission"`
	// 占比
	Percentage float64 `json:"percentage"`
}

// CommissionStatisticsResponse 佣金统计响应
type CommissionStatisticsResponse struct {
	// 统计时间段
	Period struct {
		StartDate string `json:"start_date"`
		EndDate   string `json:"end_date"`
	} `json:"period"`
	
	// 总计
	Summary struct {
		TotalCommission float64 `json:"total_commission"`
		OrderCount      int64   `json:"order_count"`
		UserCount       int64   `json:"user_count"`
		AvgCommission   float64 `json:"avg_commission"`
	} `json:"summary"`
	
	// 按天统计
	DailyStats []DailyCommissionStat `json:"daily_stats"`
	
	// 按级别统计
	LevelStats []LevelCommissionStat `json:"level_stats"`
	
	// 按时段统计 (早上/下午/晚上)
	TimeStats struct {
		Morning   float64 `json:"morning"`    // 6:00-12:00
		Afternoon float64 `json:"afternoon"`  // 12:00-18:00
		Evening   float64 `json:"evening"`    // 18:00-6:00
	} `json:"time_stats"`
}
