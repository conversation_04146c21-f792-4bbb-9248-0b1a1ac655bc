/**
 * 分销配置DTO定义
 *
 * 该文件定义了分销配置相关的数据传输对象，用于API请求和响应的数据结构。
 * 包括配置的创建、更新、查询等操作的请求和响应结构。
 */

package dto

import (
	"time"
)

// ReferralConfigCreateRequest 创建分销配置请求
type ReferralConfigCreateRequest struct {
	ConfigKey   string `json:"config_key" valid:"Required;MaxSize(100)" description:"配置键名"`
	ConfigValue string `json:"config_value" valid:"Required" description:"配置值(JSON格式)"`
	ConfigType  string `json:"config_type" valid:"Required;MaxSize(50)" description:"配置类型"`
	Description string `json:"description" valid:"MaxSize(255)" description:"配置描述"`
}

// ReferralConfigUpdateRequest 更新分销配置请求
type ReferralConfigUpdateRequest struct {
	ConfigValue string `json:"config_value" valid:"Required" description:"配置值(JSON格式)"`
	Description string `json:"description" valid:"MaxSize(255)" description:"配置描述"`
	Status      *int   `json:"status" valid:"Range(0,1)" description:"配置状态:0-禁用,1-启用"`
}

// ReferralConfigQueryRequest 查询分销配置请求
type ReferralConfigQueryRequest struct {
	ConfigType string `json:"config_type" description:"配置类型"`
	Status     *int   `json:"status" valid:"Range(0,1)" description:"配置状态"`
	Page       int    `json:"page" valid:"Min(1)" description:"页码，从1开始"`
	PageSize   int    `json:"page_size" valid:"Range(1,100)" description:"每页记录数，最多100条"`
}

// ReferralConfigResponse 分销配置响应
type ReferralConfigResponse struct {
	ID          int64     `json:"id"`
	ConfigKey   string    `json:"config_key"`
	ConfigValue string    `json:"config_value"`
	ConfigType  string    `json:"config_type"`
	Description string    `json:"description"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ReferralLevelConfigRequest 分销级别配置请求
type ReferralLevelConfigRequest struct {
	MaxLevels     int     `json:"max_levels" valid:"Range(1,3)" description:"最大分销等级(1-3)"`
	EnabledLevels int     `json:"enabled_levels" valid:"Range(1,3)" description:"当前启用的分销等级(1-3)"`
	Level1Rate    float64 `json:"level1_rate" valid:"Range(0,1)" description:"一级分销佣金比例(0-1)"`
	Level2Rate    float64 `json:"level2_rate" valid:"Range(0,1)" description:"二级分销佣金比例(0-1)"`
	Level3Rate    float64 `json:"level3_rate" valid:"Range(0,1)" description:"三级分销佣金比例(0-1)"`
}

// ReferralLevelConfigResponse 分销级别配置响应
type ReferralLevelConfigResponse struct {
	MaxLevels     int     `json:"max_levels"`
	EnabledLevels int     `json:"enabled_levels"`
	Level1Rate    float64 `json:"level1_rate"`
	Level2Rate    float64 `json:"level2_rate"`
	Level3Rate    float64 `json:"level3_rate"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// CommissionRateConfig 佣金比例配置结构
type CommissionRateConfig struct {
	Level1 float64 `json:"level1"`
	Level2 float64 `json:"level2"`
	Level3 float64 `json:"level3"`
}

// LevelConfig 分销级别配置结构
type LevelConfig struct {
	MaxLevels     int `json:"max_levels"`
	EnabledLevels int `json:"enabled_levels"`
}