/**
 * 用户分销数据传输对象
 *
 * 该文件定义了用户分销功能相关的数据传输对象(DTO)，用于控制器和服务层之间传递数据。
 * 包含分销关系创建、查询和统计等相关DTO。
 */

package dto

import (
	"time"
)

// ReferralCreateRequest 分销关系创建请求
type ReferralCreateRequest struct {
	UserID     int64 `json:"user_id" valid:"Required" description:"用户ID"`         // 用户ID
	ReferrerID int64 `json:"referrer_id" valid:"Required" description:"推荐人ID"` // 推荐人ID
	Level      int   `json:"level" valid:"Range(1,3)" description:"分销等级"`      // 分销等级
}

// ReferralQueryRequest 分销关系查询请求
type ReferralQueryRequest struct {
	UserID     int64 `json:"user_id" valid:"Required" description:"用户ID"`             // 用户ID (推荐人ID)
	Level      int   `json:"level" description:"分销等级，不传则查询所有等级"`                    // 分销等级，不传则查询所有等级
	Page       int   `json:"page" valid:"Min(1)" description:"页码，从1开始"`              // 页码，从1开始
	PageSize   int   `json:"page_size" valid:"Range(1,100)" description:"每页记录数，最多100条"` // 每页记录数，最多100条
	OnlyDirect bool  `json:"only_direct" description:"是否只查询直接推荐的用户"`                 // 是否只查询直接推荐的用户
}

// ReferralResponse 分销关系响应
type ReferralResponse struct {
	ID           int64     `json:"id" description:"分销关系ID"`                     // 分销关系ID
	UserID       int64     `json:"user_id" description:"用户ID"`                   // 用户ID
	ReferrerID   int64     `json:"referrer_id" description:"推荐人ID"`             // 推荐人ID
	Level        int       `json:"level" description:"分销等级，1-一级分销，2-二级分销，3-三级分销"` // 分销等级
	Status       int       `json:"status" description:"状态:0-无效,1-有效"`           // 状态
	Commission   float64   `json:"commission" description:"累计佣金"`                // 累计佣金
	ReferralTime time.Time `json:"referral_time" description:"推荐时间"`            // 推荐时间
	CreatedAt    time.Time `json:"created_at" description:"创建时间"`                // 创建时间
	
	// 关联的用户信息
	User     *UserBriefResponse `json:"user,omitempty" description:"用户信息"`         // 用户信息
	Referrer *UserBriefResponse `json:"referrer,omitempty" description:"推荐人信息"` // 推荐人信息
}

// UserBriefResponse 用户简要信息响应
type UserBriefResponse struct {
	ID       int64  `json:"id" description:"用户ID"`       // 用户ID
	Username string `json:"username" description:"用户名"`  // 用户名
	Nickname string `json:"nickname" description:"昵称"`   // 昵称
	Avatar   string `json:"avatar" description:"头像"`     // 头像
	Mobile   string `json:"mobile" description:"手机号"`    // 手机号
	Level    int    `json:"level" description:"用户等级"`    // 用户等级
}

// ReferralStatisticsResponse 分销统计响应
type ReferralStatisticsResponse struct {
	TotalReferrals      int64   `json:"total_referrals" description:"推荐用户总数"`           // 推荐用户总数
	Level1Referrals     int64   `json:"level1_referrals" description:"一级推荐用户数"`        // 一级推荐用户数
	Level2Referrals     int64   `json:"level2_referrals" description:"二级推荐用户数"`        // 二级推荐用户数
	Level3Referrals     int64   `json:"level3_referrals" description:"三级推荐用户数"`        // 三级推荐用户数
	TotalCommission     float64 `json:"total_commission" description:"累计佣金"`           // 累计佣金
	TodayCommission     float64 `json:"today_commission" description:"今日佣金"`          // 今日佣金
	ThisMonthCommission float64 `json:"this_month_commission" description:"本月佣金"` // 本月佣金
	ThisYearCommission  float64 `json:"this_year_commission" description:"本年佣金"`  // 本年佣金
}

// AdminReferralQueryRequest 管理员分销关系查询请求
type AdminReferralQueryRequest struct {
	ReferrerID int64 `json:"referrer_id" description:"推荐人ID筛选"`     // 推荐人ID筛选
	RefereeID  int64 `json:"referee_id" description:"被推荐人ID筛选"`     // 被推荐人ID筛选
	Status     int   `json:"status" description:"状态筛选，-1表示不筛选"`     // 状态筛选，-1表示不筛选
	Level      int   `json:"level" description:"分销等级筛选，0表示不筛选"`     // 分销等级筛选，0表示不筛选
	Page       int   `json:"page" valid:"Min(1)" description:"页码，从1开始"` // 页码，从1开始
	PageSize   int   `json:"page_size" valid:"Range(1,100)" description:"每页记录数，最多100条"` // 每页记录数，最多100条
}

// AdminReferralStatisticsResponse 管理员分销统计响应
type AdminReferralStatisticsResponse struct {
	// 总体统计
	TotalUsers          int64   `json:"total_users" description:"系统总用户数"`            // 系统总用户数
	TotalReferrals      int64   `json:"total_referrals" description:"总分销关系数"`        // 总分销关系数
	ActiveReferrals     int64   `json:"active_referrals" description:"有效分销关系数"`      // 有效分销关系数
	InactiveReferrals   int64   `json:"inactive_referrals" description:"无效分销关系数"`    // 无效分销关系数
	
	// 分级统计
	Level1Referrals     int64   `json:"level1_referrals" description:"一级分销关系数"`      // 一级分销关系数
	Level2Referrals     int64   `json:"level2_referrals" description:"二级分销关系数"`      // 二级分销关系数
	Level3Referrals     int64   `json:"level3_referrals" description:"三级分销关系数"`      // 三级分销关系数
	
	// 佣金统计
	TotalCommission     float64 `json:"total_commission" description:"系统总佣金"`        // 系统总佣金
	TodayCommission     float64 `json:"today_commission" description:"今日佣金"`         // 今日佣金
	ThisMonthCommission float64 `json:"this_month_commission" description:"本月佣金"`   // 本月佣金
	ThisYearCommission  float64 `json:"this_year_commission" description:"本年佣金"`    // 本年佣金
	
	// 时间范围统计
	TodayNewReferrals   int64   `json:"today_new_referrals" description:"今日新增分销关系"`  // 今日新增分销关系
	ThisMonthNewReferrals int64 `json:"this_month_new_referrals" description:"本月新增分销关系"` // 本月新增分销关系
	ThisYearNewReferrals  int64 `json:"this_year_new_referrals" description:"本年新增分销关系"` // 本年新增分销关系
}

// TopReferrerRequest TOP推荐人查询请求
type TopReferrerRequest struct {
	StartDate string `json:"start_date" form:"start_date" description:"开始日期，格式：2006-01-02"` // 开始日期
	EndDate   string `json:"end_date" form:"end_date" description:"结束日期，格式：2006-01-02"`     // 结束日期
	Limit     int    `json:"limit" form:"limit" description:"返回数量限制，默认10"`                // 返回数量限制
}

// TopReferrerResponse TOP推荐人响应
type TopReferrerResponse struct {
	UserID          int64   `json:"user_id" description:"用户ID"`                    // 用户ID
	Username        string  `json:"username" description:"用户名"`                   // 用户名
	Nickname        string  `json:"nickname" description:"昵称"`                    // 昵称
	Avatar          string  `json:"avatar" description:"头像"`                      // 头像
	Phone           string  `json:"phone" description:"手机号"`                     // 手机号
	ReferralCount   int64   `json:"referral_count" description:"推荐人数"`           // 推荐人数
	TotalCommission float64 `json:"total_commission" description:"总佣金"`          // 总佣金
	Level1Count     int64   `json:"level1_count" description:"一级推荐人数"`          // 一级推荐人数
	Level2Count     int64   `json:"level2_count" description:"二级推荐人数"`          // 二级推荐人数
	Level3Count     int64   `json:"level3_count" description:"三级推荐人数"`          // 三级推荐人数
	Rank            int     `json:"rank" description:"排名"`                       // 排名
}
