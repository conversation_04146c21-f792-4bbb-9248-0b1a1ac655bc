/**
 * 用户数据传输对象
 *
 * 该文件定义了用户模块相关的数据传输对象(DTO)，用于控制器和服务层之间传递数据。
 * DTO与模型分离，可以根据API需求灵活定制返回字段，避免直接暴露模型属性。
 */

package dto

import (
	"time"
)

// 用户登录请求
type LoginRequest struct {
	Username string `json:"username" valid:"Required" description:"用户名"` // 用户名
	Password string `json:"password" valid:"Required" description:"密码"`  // 密码
}

// 用户注册请求
type RegisterRequest struct {
	Username     string `json:"username" valid:"Required;MinSize(4);MaxSize(20)" description:"用户名"` // 用户名
	Password     string `json:"password" valid:"Required;MinSize(6);MaxSize(20)" description:"密码"`  // 密码
	Nickname     string `json:"nickname" valid:"Required;MaxSize(50)" description:"昵称"`             // 昵称
	Mobile       string `json:"mobile" valid:"Required;Mobile" description:"手机号"`                   // 手机号
	Email        string `json:"email" valid:"Email" description:"邮箱"`                               // 邮箱
	Gender       int    `json:"gender" valid:"Range(0,2)" description:"性别：0-未知,1-男,2-女"`            // 性别：0-未知,1-男,2-女
	ReferralCode string `json:"referral_code" description:"推荐码，可选"`   // 推荐码，可选
}

// 用户信息更新请求
type UpdateUserRequest struct {
	Nickname string    `json:"nickname" valid:"MaxSize(50)" description:"昵称"`           // 昵称
	Avatar   string    `json:"avatar" valid:"MaxSize(255)" description:"头像"`            // 头像
	Gender   int       `json:"gender" valid:"Range(0,2)" description:"性别：0-未知,1-男,2-女"` // 性别：0-未知,1-男,2-女
	Birthday time.Time `json:"birthday" description:"生日"`                               // 生日
	Email    string    `json:"email" valid:"Email" description:"邮箱"`                    // 邮箱
}

// 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" valid:"Required" description:"旧密码"`                        // 旧密码
	NewPassword string `json:"new_password" valid:"Required;MinSize(6);MaxSize(20)" description:"新密码"` // 新密码
}

// Token刷新请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" valid:"Required" description:"刷新令牌"` // 刷新令牌
}

// 用户信息响应
type UserResponse struct {
	ID          int64     `json:"id" description:"用户ID"`                          // 用户ID
	Username    string    `json:"username" description:"用户名"`                     // 用户名
	Nickname    string    `json:"nickname" description:"昵称"`                      // 昵称
	Avatar      string    `json:"avatar" description:"头像"`                        // 头像
	Mobile      string    `json:"mobile" description:"手机号"`                       // 手机号
	Email       string    `json:"email" description:"邮箱"`                         // 邮箱
	Gender      int       `json:"gender" description:"性别：0-未知,1-男,2-女"`           // 性别：0-未知,1-男,2-女
	Birthday    time.Time `json:"birthday" description:"生日"`                      // 生日
	Balance     float64   `json:"balance" description:"余额"`                       // 余额
	Points      int64     `json:"points" description:"积分"`                        // 积分
	Level       int       `json:"level" description:"等级：0-普通用户,1-VIP用户,2-SVIP用户"` // 等级：0-普通用户,1-VIP用户,2-SVIP用户
	Status      int       `json:"status" description:"状态：0-禁用,1-正常"`              // 状态：0-禁用,1-正常
	LastLoginAt time.Time `json:"last_login_at" description:"最后登录时间"`             // 最后登录时间
	CreatedAt   time.Time `json:"created_at" description:"创建时间"`                  // 创建时间
	ReferralCode string    `json:"referral_code" description:"推荐码"`              // 推荐码
}

// TokenResponse Token响应结构
type TokenResponse struct {
	AccessToken  string `json:"access_token" description:"访问令牌"`         // 访问令牌
	RefreshToken string `json:"refresh_token" description:"刷新令牌"`        // 刷新令牌
	ExpiresIn    int64  `json:"expires_in" description:"过期时间（秒）"`        // 过期时间（秒）
	TokenType    string `json:"token_type" description:"令牌类型，固定为Bearer"` // 令牌类型，固定为Bearer
}

// 登录响应
type LoginResponse struct {
	TokenInfo TokenResponse `json:"token_info" description:"令牌信息"` // 令牌信息
	User      UserResponse  `json:"user" description:"用户信息"`       // 用户信息
}

// ResetPasswordRequest 重置密码请求
type ResetPasswordRequest struct {
	Mobile      string `form:"mobile" valid:"Required;Mobile" description:"手机号"`                       // 手机号
	VerifyCode  string `form:"verify_code" valid:"Required" description:"验证码"`                         // 验证码
	NewPassword string `form:"new_password" valid:"Required;MinSize(6);MaxSize(20)" description:"新密码"` // 新密码
}

// SendVerificationCodeRequest 发送验证码请求
type SendVerificationCodeRequest struct {
	Mobile string `json:"mobile" valid:"Required;Mobile" description:"手机号"` // 手机号
}

// SendVerificationCodeResponse 发送验证码响应
type SendVerificationCodeResponse struct {
	ExpireTime int `json:"expire_time" description:"验证码有效期（分钟）"` // 验证码有效期（分钟）
}

// AccountTransactionRequest 账户变动查询请求
type AccountTransactionRequest struct {
	TransactionType string `json:"transaction_type" description:"交易类型，可选值：recharge,withdraw,payment,refund,adjust"` // 交易类型
	Page            int    `json:"page" valid:"Min(1)" description:"页码，从1开始"`                                      // 页码
	PageSize        int    `json:"page_size" valid:"Range(1,100)" description:"每页数量，1-100"`                      // 每页数量
}

// AccountTransactionResponse 账户变动响应
type AccountTransactionResponse struct {
	ID            int64   `json:"id" description:"交易ID"`                    // 交易ID
	TransactionNo string  `json:"transaction_no" description:"交易流水号"`      // 交易流水号
	RelatedID     int64   `json:"related_id" description:"关联ID，如订单ID"`     // 关联ID
	RelatedType   string  `json:"related_type" description:"关联类型，如order"` // 关联类型
	Amount        float64 `json:"amount" description:"交易金额"`               // 交易金额
	BeforeBalance float64 `json:"before_balance" description:"交易前余额"`     // 交易前余额
	AfterBalance  float64 `json:"after_balance" description:"交易后余额"`      // 交易后余额
	Type          string  `json:"type" description:"交易类型"`                 // 交易类型
	Operation     string  `json:"operation" description:"操作类型:增加/减少"`     // 操作类型
	Status        int     `json:"status" description:"交易状态:1成功,0失败,-1处理中"` // 交易状态
	Description   string  `json:"description" description:"交易描述"`         // 交易描述
	Remark        string  `json:"remark" description:"备注"`                 // 备注
	ClientIP      string  `json:"client_ip" description:"客户端IP"`           // 客户端IP
	CreatedAt     string  `json:"created_at" description:"创建时间"`           // 创建时间
}

// AccountTransactionListResponse 账户变动列表响应
type AccountTransactionListResponse struct {
	List  []*AccountTransactionResponse `json:"list" description:"交易记录列表"`  // 交易记录列表
	Total int64                         `json:"total" description:"总记录数"`   // 总记录数
	Page  int                           `json:"page" description:"当前页码"`    // 当前页码
	Size  int                           `json:"size" description:"每页数量"`    // 每页数量
}

// UserAccountResponse 用户账户信息响应
type UserAccountResponse struct {
	ID            int64   `json:"id" description:"账户ID"`         // 账户ID
	UserID        int64   `json:"user_id" description:"用户ID"`   // 用户ID
	Balance       float64 `json:"balance" description:"账户余额"`   // 账户余额
	FrozenBalance float64 `json:"frozen_balance" description:"冻结金额"` // 冻结金额
	TotalRecharge float64 `json:"total_recharge" description:"总充值金额"` // 总充值金额
	TotalConsume  float64 `json:"total_consume" description:"总消费金额"` // 总消费金额
	Status        int     `json:"status" description:"账户状态:1正常,0锁定"` // 账户状态
	LastRecharge  string  `json:"last_recharge" description:"最后充值时间"` // 最后充值时间
	LastConsume   string  `json:"last_consume" description:"最后消费时间"` // 最后消费时间
	UpdatedAt     string  `json:"updated_at" description:"更新时间"`   // 更新时间
	CreatedAt     string  `json:"created_at" description:"创建时间"`   // 创建时间
}

// UserVerifyCodeLoginRequest 用户验证码登录请求
type UserVerifyCodeLoginRequest struct {
	Mobile string `json:"mobile" valid:"Required;Mobile" description:"手机号"` // 手机号
	Code   string `json:"code" valid:"Required" description:"验证码"`      // 验证码
}

// RegisterByVerifyCodeRequest 短信验证码注册请求
type RegisterByVerifyCodeRequest struct {
	Mobile       string `json:"mobile,phone" valid:"Required;Mobile" description:"手机号"`         // 手机号
	Code         string `json:"code" valid:"Required" description:"验证码"`                // 验证码
	Nickname     string `json:"nickname" valid:"MaxSize(50)" description:"昵称（可选，若不提供则自动生成）"`   // 昵称（可选，若不提供则自动生成）
	Gender       int    `json:"gender" valid:"Range(0,2)" description:"性别：0-未知,1-男,2-女"` // 性别
	Email        string `json:"email" valid:"Email;Optional" description:"邮箱（可选）"`        // 邮箱（可选）
	ReferrerID   int64  `json:"referrer_id" description:"推荐人ID（可选）"`                     // 推荐人ID
}
