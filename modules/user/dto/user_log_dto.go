/**
 * 用户日志DTO
 *
 * 该文件定义了用户日志相关的数据传输对象，用于服务层与控制层之间的数据交换。
 * 包括日志创建请求、查询请求以及响应数据结构等。
 */

package dto

import (
	"time"
)

// UserLogCreateRequest 用户日志创建请求
type UserLogCreateRequest struct {
	UserID       int64  `json:"user_id"`        // 用户ID
	Username     string `json:"username"`       // 用户名
	OperationType int   `json:"operation_type"` // 操作类型
	Content      string `json:"content"`        // 操作内容
	RequestURL   string `json:"request_url"`    // 请求URL
	RequestData  string `json:"request_data"`   // 请求数据（JSON格式）
	IP           string `json:"ip"`             // 操作IP
	UserAgent    string `json:"user_agent"`     // 用户代理
	Status       int    `json:"status"`         // 操作状态(0:失败,1:成功)
	Remark       string `json:"remark"`         // 备注信息
}

// UserLogQueryRequest 用户日志查询请求
type UserLogQueryRequest struct {
	UserID        int64     `json:"user_id"`        // 用户ID
	Username      string    `json:"username"`       // 用户名
	OperationType int       `json:"operation_type"` // 操作类型
	IP            string    `json:"ip"`             // 操作IP
	Status        int       `json:"status"`         // 状态
	StartTime     time.Time `json:"start_time"`     // 开始时间
	EndTime       time.Time `json:"end_time"`       // 结束时间
	Page          int       `json:"page"`           // 页码
	PageSize      int       `json:"pageSize"`       // 每页数量
}

// UserLogResponse 用户日志响应
type UserLogResponse struct {
	ID            int64     `json:"id"`             // 日志ID
	UserID        int64     `json:"user_id"`        // 用户ID
	Username      string    `json:"username"`       // 用户名
	OperationName string    `json:"operation_name"` // 操作类型名称
	OperationType int       `json:"operation_type"` // 操作类型
	Content       string    `json:"content"`        // 操作内容
	RequestURL    string    `json:"request_url"`    // 请求URL
	RequestData   string    `json:"request_data"`   // 请求数据（JSON格式）
	IP            string    `json:"ip"`             // 操作IP
	UserAgent     string    `json:"user_agent"`     // 用户代理
	Status        int       `json:"status"`         // 操作状态
	StatusText    string    `json:"status_text"`    // 操作状态文本
	Remark        string    `json:"remark"`         // 备注信息
	CreatedAt     time.Time `json:"created_at"`     // 创建时间
}

// UserLogListResponse 用户日志列表响应
type UserLogListResponse struct {
	Total int64              `json:"total"` // 总数
	List  []*UserLogResponse `json:"list"`  // 列表
}
