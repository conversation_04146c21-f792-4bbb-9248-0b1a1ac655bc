/**
 * 微信登录数据传输对象
 *
 * 该文件定义了微信小程序登录相关的请求和响应数据结构。
 */

package dto

// WechatMiniLoginRequest 微信小程序登录请求
type WechatMiniLoginRequest struct {
	Code          string `json:"code" valid:"Required"`                    // 小程序登录凭证
	NickName      string `json:"nickName" valid:"MaxSize(50)"`             // 用户昵称
	AvatarUrl     string `json:"avatarUrl" valid:"MaxSize(255)"`           // 用户头像
	Gender        int    `json:"gender" valid:"Range(0,2)"`                // 性别：0-未知，1-男，2-女
	ReferralCode  string `json:"referralCode" valid:"MaxSize(10)"`         // 推荐人邀请码（可选）
	EncryptedData string `json:"encryptedData" valid:"MaxSize(2000)"`      // 加密数据（可选）
	Iv            string `json:"iv" valid:"MaxSize(50)"`                   // 加密初始向量（可选）
	LoginIP       string `json:"-"`                                        // 登录IP（由服务器填充）
}

// WechatBindRequest 微信绑定请求
type WechatBindRequest struct {
	Code     string `json:"code" valid:"Required"` // 小程序登录凭证
	Mobile   string `json:"mobile" valid:"Mobile"` // 手机号（可选）
	Password string `json:"password" valid:""`     // 密码（可选）
}

// WechatUserInfo 微信用户信息
type WechatUserInfo struct {
	OpenID     string `json:"openid"`     // 用户唯一标识
	UnionID    string `json:"unionid"`    // 用户在开放平台的唯一标识（如果有）
	SessionKey string `json:"sessionKey"` // 会话密钥
	NickName   string `json:"nickName"`   // 用户昵称
	AvatarUrl  string `json:"avatarUrl"`  // 用户头像
	Gender     int    `json:"gender"`     // 性别：0-未知，1-男，2-女
}
