/**
 * 微信网页扫码登录数据传输对象
 *
 * 该文件定义了微信网页扫码登录相关的请求和响应DTO结构体。
 */

package dto

import "time"

// 微信扫码登录请求状态常量
const (
	WxQrLoginStatusWaiting = 1 // 等待扫码
	WxQrLoginStatusScanned = 2 // 已扫码
	WxQrLoginStatusSuccess = 3 // 登录成功
	WxQrLoginStatusExpired = 4 // 二维码过期
	WxQrLoginStatusCanceled = 5 // 用户取消
)

// WechatWebLoginRequest 微信扫码登录请求参数
type WechatWebLoginRequest struct {
	Code      string `json:"code" valid:"Required"` // 微信授权码
	State     string `json:"state" valid:"Required"` // 用于防止CSRF攻击的state参数
	ClientIP  string `json:"-"`                     // 客户端IP，内部填充
}

// WechatWebLoginResponse 微信扫码登录响应
type WechatWebLoginResponse struct {
	UserID   int64   `json:"user_id"`            // 用户ID
	Username string  `json:"username"`           // 用户名
	Nickname string  `json:"nickname"`           // 昵称
	Avatar   string  `json:"avatar"`             // 头像URL
	Token    TokenResponse `json:"token"`        // 令牌信息
}

// WechatQrCodeRequest 获取微信登录二维码请求
type WechatQrCodeRequest struct {
	RedirectURL string `json:"redirect_url,omitempty"` // 可选的登录成功后跳转地址
}

// WechatQrCodeResponse 获取微信登录二维码响应
type WechatQrCodeResponse struct {
	QrCodeURL   string `json:"qrcode_url"`    // 二维码图片地址
	QrCodeID    string `json:"qrcode_id"`     // 二维码唯一标识
	ExpireTime  int64  `json:"expire_time"`   // 过期时间戳(秒)
	QrContent   string `json:"qr_content"`    // 二维码内容(用于前端自行生成二维码)
}

// WechatQrCodeStatusRequest 查询微信登录二维码状态请求
type WechatQrCodeStatusRequest struct {
	QrCodeID string `json:"qrcode_id" valid:"Required"` // 二维码唯一标识
}

// WechatQrCodeStatusResponse 查询微信登录二维码状态响应
type WechatQrCodeStatusResponse struct {
	Status     int       `json:"status"`                // 状态: 1=等待扫码, 2=已扫码, 3=登录成功, 4=过期, 5=取消
	UserInfo   *WechatWebLoginResponse `json:"user_info,omitempty"` // 用户信息(仅在登录成功时返回)
	UpdateTime time.Time `json:"update_time"`           // 状态更新时间
}

// WechatWebUserInfo 微信网页用户信息
type WechatWebUserInfo struct {
	OpenID     string `json:"openid"`      // 用户开放ID
	UnionID    string `json:"unionid"`     // 开放平台唯一ID
	Nickname   string `json:"nickname"`    // 用户昵称
	Sex        int    `json:"sex"`         // 性别(1=男,2=女,0=未知)
	Province   string `json:"province"`    // 省份
	City       string `json:"city"`        // 城市
	Country    string `json:"country"`     // 国家
	HeadImgURL string `json:"headimgurl"`  // 头像URL
	Privilege  []string `json:"privilege"` // 用户特权信息
}
