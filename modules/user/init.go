/**
 * user模块初始化
 *
 * 本文件负责user模块的初始化工作，包括注册模型、初始化路由等。
 * 在应用启动时被调用，确保user模块的功能正常启动。
 */

package user

import (
	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/routers"
)

// Init 初始化user模块
func Init() {
	logs.Info("初始化user模块...")

	// 注册模型
	registerModels()

	// 初始化路由
	routers.InitRouters()

	logs.Info("user模块初始化完成")
}

// registerModels 注册模型
func registerModels() {
	// 注册用户模型
	orm.RegisterModel(new(models.User))
	// 注册地址模型
	orm.RegisterModel(new(models.Address))
	// 注册用户账户模型
	orm.RegisterModel(new(models.UserAccount))
	// 注册用户账户交易记录模型
	orm.RegisterModel(new(models.UserAccountTransaction))
	// 注册用户分销配置模型
	orm.RegisterModel(new(models.ReferralConfig))
	// 注册用户分销关系模型
	orm.RegisterModel(new(models.UserReferral))
	// 注册用户日志模型
	orm.RegisterModel(new(models.UserLog))
}
