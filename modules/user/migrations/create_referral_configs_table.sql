-- 创建分销配置表
CREATE TABLE IF NOT EXISTS `referral_configs` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `config_key` varchar(100) NOT NULL COMMENT '配置键名',
    `config_value` text NOT NULL COMMENT '配置值',
    `config_type` varchar(50) NOT NULL DEFAULT 'string' COMMENT '配置类型：string, int, float, json',
    `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`),
    KEY `idx_config_type` (`config_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分销配置表';

-- 插入初始配置数据
INSERT INTO `referral_configs` (`config_key`, `config_value`, `config_type`, `description`, `status`) VALUES
('max_levels', '1', 'int', '最大分销等级，当前仅开启一级分销', 1),
('level_1_enabled', 'true', 'string', '一级分销是否启用', 1),
('level_2_enabled', 'false', 'string', '二级分销是否启用', 1),
('level_3_enabled', 'false', 'string', '三级分销是否启用', 1),
('commission_rates', '{"level_1": 0.05, "level_2": 0.03, "level_3": 0.02}', 'json', '各级分销佣金比例配置', 1),
('referral_code_length', '8', 'int', '推荐码长度', 1),
('referral_code_charset', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 'string', '推荐码字符集', 1),
('auto_upgrade_enabled', 'false', 'string', '是否启用自动升级分销等级', 1),
('min_referrals_for_upgrade', '10', 'int', '升级到下一级所需的最少推荐人数', 1),
('commission_settlement_period', '30', 'int', '佣金结算周期（天）', 1);