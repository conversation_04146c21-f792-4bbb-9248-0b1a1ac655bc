/**
 * 收货地址模型
 * 
 * 该文件定义了用户收货地址的数据模型，包括收货人信息、联系方式、详细地址等。
 * 收货地址与用户是一对多的关系，一个用户可以有多个收货地址。
 */

package models

import (
	"time"
)

// Address 收货地址模型
type Address struct {
	ID               int64     `json:"id" orm:"pk;auto;column(id);description(收货地址唯一标识)"`
	UserID           int64     `json:"user_id" orm:"index;column(user_id);description(关联的用户ID)"`                // 关联用户ID
	ReceiverName     string    `json:"receiver_name" orm:"size(50);column(receiver_name);description(收货人的真实姓名)"`       // 收货人姓名
	ReceiverMobile   string    `json:"receiver_mobile" orm:"size(20);column(receiver_mobile);description(收货人的联系电话)"`     // 收货人手机号
	Province         string    `json:"province" orm:"size(50);column(province);description(收货地址所在省份)"`            // 省份
	City             string    `json:"city" orm:"size(50);column(city);description(收货地址所在城市)"`                // 城市
	District         string    `json:"district" orm:"size(50);column(district);description(收货地址所在区县)"`            // 区/县
	DetailedAddress  string    `json:"detailed_address" orm:"size(255);column(detailed_address);description(街道门牌号等详细地址信息)"`   // 详细地址
	PostalCode       string    `json:"postal_code" orm:"size(20);null;column(postal_code);description(收货地址的邮政编码)"`    // 邮政编码
	IsDefault        bool      `json:"is_default" orm:"default(false);column(is_default);description(是否为用户的默认收货地址)"`    // 是否默认地址
	AddressTag       string    `json:"address_tag" orm:"size(50);null;column(address_tag);description(地址标签，如家、公司等)"`    // 地址标签（如"家"、"公司"）
	LocationLongitude float64   `json:"location_longitude" orm:"digits(12);decimals(8);null;column(location_longitude);description(经度)"` // 经度
	LocationLatitude  float64   `json:"location_latitude" orm:"digits(12);decimals(8);null;column(location_latitude);description(纬度)"`  // 纬度
	CreatedAt        time.Time `json:"created_at" orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)"`
	UpdatedAt        time.Time `json:"updated_at" orm:"auto_now;type(datetime);column(updated_at);description(更新时间)"`
}

// TableName 设置表名
func (a *Address) TableName() string {
	return "user_addresses"
}
