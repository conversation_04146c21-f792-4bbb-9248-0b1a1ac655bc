/**
 * 分销配置模型
 *
 * 该文件定义了分销配置的数据模型，用于存储分销系统的各种配置参数。
 * 支持动态配置分销级别、佣金比例等参数，提供灵活的分销规则管理。
 */

package models

import (
	"time"
)

// ReferralConfig 分销配置模型
type ReferralConfig struct {
	ID          int64     `json:"id" orm:"pk;auto;column(id);description(配置ID)"`
	ConfigKey   string    `json:"config_key" orm:"column(config_key);size(100);unique;description(配置键名)"`
	ConfigValue string    `json:"config_value" orm:"column(config_value);type(text);description(配置值JSON格式)"`
	ConfigType  string    `json:"config_type" orm:"column(config_type);size(50);index;description(配置类型)"`
	Description string    `json:"description" orm:"column(description);size(255);null;description(配置描述)"`
	Status      int       `json:"status" orm:"default(1);column(status);description(配置状态:0-禁用,1-启用)"`
	CreatedAt   time.Time `json:"created_at" orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)"`
	UpdatedAt   time.Time `json:"updated_at" orm:"auto_now;type(datetime);column(updated_at);description(更新时间)"`
}

// TableName 设置表名
func (rc *ReferralConfig) TableName() string {
	return "referral_configs"
}

// 配置状态常量
const (
	ReferralConfigStatusDisabled = 0 // 禁用
	ReferralConfigStatusEnabled  = 1 // 启用
)

// 配置类型常量
const (
	ReferralConfigTypeCommissionRate = "commission_rate" // 佣金比例配置
	ReferralConfigTypeLevelConfig    = "level_config"    // 分销级别配置
	ReferralConfigTypeRewardConfig   = "reward_config"   // 奖励配置
	ReferralConfigTypeOrderConfig    = "order_config"    // 订单配置
	ReferralConfigTypeSettleConfig   = "settle_config"   // 结算配置
)

// 预定义配置键名常量
const (
	ReferralConfigKeyCommissionRates = "commission_rates" // 各级分销佣金比例
	ReferralConfigKeyMaxLevels       = "max_levels"       // 最大分销等级
	ReferralConfigKeyEnabledLevels   = "enabled_levels"   // 当前启用的分销等级
	ReferralConfigKeyRegisterReward  = "register_reward"  // 注册奖励金额
	ReferralConfigKeyMinOrderAmount  = "min_order_amount" // 最小订单金额
	ReferralConfigKeySettleDays      = "commission_settle_days" // 佣金结算天数
)