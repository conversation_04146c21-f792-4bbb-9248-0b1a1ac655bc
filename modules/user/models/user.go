/**
 * 用户模型
 *
 * 该文件定义了用户相关的数据模型，包括用户基本信息、认证信息等。
 * 用户模型是系统的核心模型之一，与多个模块存在关联。
 */

package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID           int64     `json:"id" orm:"pk;auto;column(id);description(用户ID)"`
	Username     string    `json:"username" orm:"size(50);unique;column(username);description(用户名)"`
	Password     string    `json:"-" orm:"size(255);column(password);description(密码)"` // 密码不返回给前端
	Nickname     string    `json:"nickname" orm:"size(50);column(nickname);description(昵称)"`
	Avatar       string    `json:"avatar" orm:"size(255);null;column(avatar);description(头像)"`
	Mobile       string    `json:"mobile" orm:"size(20);unique;column(mobile);description(手机号)"`
	Email        string    `json:"email" orm:"size(100);unique;null;column(email);description(邮箱)"`
	Gender       int       `json:"gender" orm:"default(0);column(gender);description(性别:0-未知,1-男,2-女)"` // 0:未知 1:男 2:女
	Birthday     time.Time `json:"birthday" orm:"type(date);null;column(birthday);description(生日)"`
	Balance      float64   `json:"balance" orm:"digits(10);decimals(2);default(0);column(balance);description(账户余额)"`
	Points       int64     `json:"points" orm:"default(0);column(points);description(积分)"` // 积分
	Level        int       `json:"level" orm:"default(1);column(level);description(用户等级:0-普通用户,1-VIP用户,2-SVIP用户)"` // 用户等级：0-普通用户,1-VIP用户,2-SVIP用户
	Status       int       `json:"status" orm:"default(1);column(status);description(状态:0-禁用,1-正常)"` // 状态：0-禁用,1-正常
	ReferralCode string    `json:"referral_code" orm:"size(16);unique;column(referral_code);description(推荐码)"` // 推荐码
	// 微信小程序相关字段
	WxOpenID     string    `json:"wx_open_id" orm:"size(50);null;unique;column(wx_open_id);description(微信OpenID)"`     // 微信小程序OpenID
	WxUnionID    string    `json:"wx_union_id" orm:"size(50);null;index;column(wx_union_id);description(微信UnionID)"`    // 微信UnionID
	LastLoginAt  time.Time `json:"last_login_at" orm:"type(datetime);null;column(last_login_at);description(最后登录时间)"`
	LastLoginIP  string    `json:"last_login_ip" orm:"size(50);null;column(last_login_ip);description(最后登录IP)"`
	RegisterIP   string    `json:"register_ip" orm:"size(50);column(register_ip);description(注册IP)"`
	CreatedAt    time.Time `json:"created_at" orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)"`
	UpdatedAt    time.Time `json:"updated_at" orm:"auto_now;type(datetime);column(updated_at);description(更新时间)"`
}

// TableName 设置表名
func (u *User) TableName() string {
	return "users"
}

// 用户状态常量
const (
	UserStatusDisabled = 0 // 禁用
	UserStatusEnabled  = 1 // 正常
	UserStatusNormal   = 1 // 正常（别名，与UserStatusEnabled相同）
)

// 性别常量
const (
	GenderUnknown = 0 // 未知
	GenderMale    = 1 // 男
	GenderFemale  = 2 // 女
)

// 注册来源常量
const (
	RegisterSourceWeb      = 1 // 网站注册
	RegisterSourceApp      = 2 // APP注册
	RegisterSourceWechat   = 3 // 微信注册
	RegisterSourceWechatWeb = 4 // 微信网页扫码注册
)
