/**
 * user_account.go
 * 用户账户模型定义
 * 包含用户账户余额、交易记录等相关模型
 */

package models

import (
	"time"
)

// 账户交易类型
const (
	// TransactionTypeRecharge 充值
	TransactionTypeRecharge = "recharge"
	// TransactionTypeWithdraw 提现
	TransactionTypeWithdraw = "withdraw"
	// TransactionTypePayment 支付
	TransactionTypePayment = "payment"
	// TransactionTypeRefund 退款
	TransactionTypeRefund = "refund"
	// TransactionTypeAdjust 调整
	TransactionTypeAdjust = "adjust"
)

// 操作类型
const (
	// OperationTypeIncrease 增加
	OperationTypeIncrease = "increase"
	// OperationTypeDecrease 减少
	OperationTypeDecrease = "decrease"
)

// UserAccount 用户账户信息
type UserAccount struct {
	ID            int64     `orm:"pk;auto;column(i_d);description(账户ID)" json:"id"`
	UserID        int64     `orm:"index;column(user_i_d);description(用户ID)" json:"user_id"` 
	Balance       float64   `orm:"digits(12);decimals(2);description(账户余额)" json:"balance"`
	FrozenBalance float64   `orm:"digits(12);decimals(2);description(冻结金额)" json:"frozen_balance"`
	TotalRecharge float64   `orm:"digits(12);decimals(2);description(总充值金额)" json:"total_recharge"`
	TotalConsume  float64   `orm:"digits(12);decimals(2);description(总消费金额)" json:"total_consume"`
	Status        int       `orm:"default(1);description(账户状态:1正常,0锁定)" json:"status"`
	LastRecharge  time.Time `orm:"null;type(datetime);description(最后充值时间)" json:"last_recharge"`
	LastConsume   time.Time `orm:"null;type(datetime);description(最后消费时间)" json:"last_consume"`
	UpdatedAt     time.Time `orm:"auto_now;type(datetime);description(更新时间)" json:"updated_at"`
	CreatedAt     time.Time `orm:"auto_now_add;type(datetime);description(创建时间)" json:"created_at"`
}

// TableName 设置表名
func (ua *UserAccount) TableName() string {
	return "user_account"
}

// UserAccountTransaction 用户账户交易记录
type UserAccountTransaction struct {
	ID            int64     `orm:"pk;auto;column(i_d);description(交易ID)" json:"id"`
	UserID        int64     `orm:"index;column(user_i_d);description(用户ID)" json:"user_id"`
	AccountID     int64     `orm:"index;column(account_i_d);description(账户ID)" json:"account_id"`
	TransactionNo string    `orm:"size(64);unique;description(交易流水号)" json:"transaction_no"`
	RelatedID     int64     `orm:"default(0);column(related_i_d);description(关联ID，如订单ID)" json:"related_id"`
	RelatedType   string    `orm:"size(32);description(关联类型，如order)" json:"related_type"`
	Amount        float64   `orm:"digits(12);decimals(2);description(交易金额)" json:"amount"`
	BeforeBalance float64   `orm:"digits(12);decimals(2);description(交易前余额)" json:"before_balance"`
	AfterBalance  float64   `orm:"digits(12);decimals(2);description(交易后余额)" json:"after_balance"`
	Type          string    `orm:"size(32);description(交易类型)" json:"type"`
	Operation     string    `orm:"size(32);description(操作类型:增加/减少)" json:"operation"`
	Status        int       `orm:"default(1);description(交易状态:1成功,0失败,-1处理中)" json:"status"`
	Description   string    `orm:"size(255);description(交易描述)" json:"description"`
	Remark        string    `orm:"size(255);null;description(备注)" json:"remark"`
	OperatorID    int64     `orm:"default(0);column(operator_i_d);description(操作人ID)" json:"operator_id"`
	ClientIP      string    `orm:"size(40);column(client_i_p);description(客户端IP)" json:"client_ip"`
	CreatedAt     time.Time `orm:"auto_now_add;type(datetime);description(创建时间)" json:"created_at"`
	UpdatedAt     time.Time `orm:"auto_now;type(datetime);description(更新时间)" json:"updated_at"`
}

// TableName 设置表名
func (uat *UserAccountTransaction) TableName() string {
	return "user_account_transaction"
}
