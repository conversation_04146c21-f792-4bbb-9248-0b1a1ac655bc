/**
 * 用户日志模型
 *
 * 该文件定义了用户敏感操作的日志模型，用于记录用户的敏感操作。
 * 包括密码修改、信息更新、登录登出等安全审计信息。
 */

package models

import (
	"time"
)

// 用户日志操作类型常量
const (
	UserLogTypeLogin          = 1 // 登录
	UserLogTypeLogout         = 2 // 登出
	UserLogTypeRegister       = 3 // 注册
	UserLogTypeUpdateInfo     = 4 // 更新信息
	UserLogTypeChangePassword = 5 // 修改密码
	UserLogTypeResetPassword  = 6 // 重置密码
	UserLogTypeSetDefault     = 7 // 设置默认项
	UserLogTypeConsume        = 8 // 消费
)

// UserLog 用户日志模型
type UserLog struct {
	ID           int64     `orm:"pk;auto;column(id)" json:"id"`                              // 日志ID
	UserID       int64     `orm:"column(user_id)" json:"user_id"`                            // 用户ID
	Username     string    `orm:"size(100);column(username)" json:"username"`                // 用户名
	OperationType int      `orm:"column(operation_type)" json:"operation_type"`              // 操作类型
	Content      string    `orm:"type(text);column(content)" json:"content"`                 // 操作内容
	RequestURL   string    `orm:"size(255);column(request_url)" json:"request_url"`          // 请求URL
	RequestData  string    `orm:"type(text);column(request_data)" json:"request_data"`       // 请求数据（JSON格式）
	IP           string    `orm:"size(50);column(ip)" json:"ip"`                             // 操作IP
	UserAgent    string    `orm:"size(255);column(user_agent)" json:"user_agent"`            // 用户代理
	Status       int       `orm:"column(status);default(1)" json:"status"`                   // 操作状态(0:失败,1:成功)
	Remark       string    `orm:"size(255);column(remark)" json:"remark"`                    // 备注信息
	CreatedAt    time.Time `orm:"auto_now_add;type(datetime);column(created_at)" json:"created_at"` // 创建时间
}

// TableName 指定表名
func (l *UserLog) TableName() string {
	return "user_log"
}

// 为了更好的描述操作类型，提供操作类型名称映射
var UserLogTypeNames = map[int]string{
	UserLogTypeLogin:          "登录",
	UserLogTypeLogout:         "登出",
	UserLogTypeRegister:       "注册",
	UserLogTypeUpdateInfo:     "更新信息",
	UserLogTypeChangePassword: "修改密码",
	UserLogTypeResetPassword:  "重置密码",
	UserLogTypeSetDefault:     "设置默认项",
	UserLogTypeConsume:        "消费",
}
