/**
 * 用户分销关系模型
 *
 * 该文件定义了用户分销关系的数据模型，用于记录用户间的推荐关系。
 * 通过该模型可以实现多级分销结构，支持分销佣金计算和分销网络查询。
 */

package models

import (
	"time"
)

// UserReferral 用户分销关系模型
type UserReferral struct {
	ID           int64     `json:"id" orm:"pk;auto;column(id);description(分销关系ID)"`
	UserID       int64     `json:"user_id" orm:"column(user_id);index;description(用户ID)"`
	ReferrerID   int64     `json:"referrer_id" orm:"column(referrer_id);index;description(推荐人ID)"`
	Level        int       `json:"level" orm:"default(1);column(level);description(分销等级，1-一级分销，2-二级分销，最多支持到3级)"`
	Status       int       `json:"status" orm:"default(1);column(status);description(状态:0-无效,1-有效)"`
	Commission   float64   `json:"commission" orm:"digits(10);decimals(2);default(0);column(commission);description(累计佣金)"`
	ReferralTime time.Time `json:"referral_time" orm:"auto_now_add;type(datetime);column(referral_time);description(推荐时间)"`
	CreatedAt    time.Time `json:"created_at" orm:"auto_now_add;type(datetime);column(created_at);description(创建时间)"`
	UpdatedAt    time.Time `json:"updated_at" orm:"auto_now;type(datetime);column(updated_at);description(更新时间)"`
}

// TableName 设置表名
func (ur *UserReferral) TableName() string {
	return "user_referrals"
}

// 分销关系状态常量
const (
	ReferralStatusInvalid = 0 // 无效
	ReferralStatusValid   = 1 // 有效
)

// 分销等级常量
const (
	ReferralLevelOne   = 1 // 一级分销
	ReferralLevelTwo   = 2 // 二级分销
	ReferralLevelThree = 3 // 三级分销
)
