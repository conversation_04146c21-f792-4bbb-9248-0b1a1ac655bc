/**
 * 地址仓库实现
 *
 * 该文件实现了地址仓库接口，负责用户收货地址数据的存储和检索。
 * 使用Beego ORM与数据库进行交互，实现地址相关的CRUD操作。
 */

package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/models"
)

// AddressRepositoryImpl 地址仓库实现
type AddressRepositoryImpl struct {
	ormer orm.Ormer
}

// NewAddressRepository 创建地址仓库实例
func NewAddressRepository() AddressRepository {
	return &AddressRepositoryImpl{
		ormer: orm.NewOrm(),
	}
}

// Create 创建收货地址
func (r *AddressRepositoryImpl) Create(ctx context.Context, address *models.Address) (int64, error) {
	// 设置创建时间
	address.CreatedAt = time.Now()
	address.UpdatedAt = time.Now()

	// 开启事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	// 检查事务是否开启成功
	if err != nil {
		logs.Error("[Create] 开启事务失败: %v", err)
		return 0, fmt.Errorf("操作失败: %v", err)
	}

	// 如果是默认地址，先重置用户其他地址为非默认
	if address.IsDefault {
		if err := r.resetDefaultInTx(tx, address.UserID); err != nil {
			tx.Rollback()
			return 0, err
		}
	}

	// 插入数据
	id, err := tx.Insert(address)
	if err != nil {
		tx.Rollback()
		logs.Error("[Create] 创建收货地址失败: %v", err)
		return 0, fmt.Errorf("操作失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		tx.Rollback()
		logs.Error("[Create] 提交事务失败: %v", err)
		return 0, fmt.Errorf("操作失败: %v", err)
	}

	return id, nil
}

// Update 更新收货地址
func (r *AddressRepositoryImpl) Update(ctx context.Context, address *models.Address) error {
	// 更新时间
	address.UpdatedAt = time.Now()

	// 开启事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	// 检查事务是否开启成功
	if err != nil {
		logs.Error("[Update] 开启事务失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	// 如果是默认地址，先重置用户其他地址为非默认
	if address.IsDefault {
		if err := r.resetDefaultInTx(tx, address.UserID); err != nil {
			tx.Rollback()
			return fmt.Errorf("操作失败: %v", err)
		}
	}

	// 更新数据
	_, err = tx.Update(address)
	if err != nil {
		tx.Rollback()
		logs.Error("[Update] 更新收货地址失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		tx.Rollback()
		logs.Error("[Update] 提交事务失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	return nil
}

// Delete 删除收货地址
func (r *AddressRepositoryImpl) Delete(ctx context.Context, id int64, userID int64) error {
	// 查询地址是否存在且属于该用户
	address := &models.Address{ID: id, UserID: userID}
	if err := r.ormer.Read(address, "ID", "UserID"); err != nil {
		if err == orm.ErrNoRows {
			return errors.New("地址不存在或不属于该用户")
		}
		logs.Error("查询地址失败: %v", err)
		return err
	}

	// 删除地址
	_, err := r.ormer.Delete(address)
	if err != nil {
		logs.Error("删除地址失败: %v", err)
		return err
	}

	return nil
}

// GetByID 根据ID获取收货地址
func (r *AddressRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.Address, error) {
	address := &models.Address{ID: id}
	if err := r.ormer.Read(address); err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 地址不存在返回nil
		}
		logs.Error("查询地址失败: %v", err)
		return nil, err
	}

	return address, nil
}

// ListByUserID 获取用户的收货地址列表
func (r *AddressRepositoryImpl) ListByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*models.Address, int64, error) {
	var addresses []*models.Address

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询总数
	count, err := r.ormer.QueryTable(new(models.Address)).Filter("UserID", userID).Count()
	if err != nil {
		logs.Error("查询地址总数失败: %v", err)
		return nil, 0, err
	}

	// 查询数据，默认地址排在前面
	_, err = r.ormer.QueryTable(new(models.Address)).
		Filter("UserID", userID).
		OrderBy("-IsDefault", "-UpdatedAt").
		Limit(pageSize, offset).
		All(&addresses)

	if err != nil {
		logs.Error("查询地址列表失败: %v", err)
		return nil, 0, err
	}

	return addresses, count, nil
}

// GetDefaultAddress 获取用户的默认收货地址
func (r *AddressRepositoryImpl) GetDefaultAddress(ctx context.Context, userID int64) (*models.Address, error) {
	address := &models.Address{}
	err := r.ormer.QueryTable(new(models.Address)).
		Filter("UserID", userID).
		Filter("IsDefault", true).
		One(address)

	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没有默认地址返回nil
		}
		logs.Error("查询默认地址失败: %v", err)
		return nil, err
	}

	return address, nil
}

// SetDefault 设置默认收货地址
func (r *AddressRepositoryImpl) SetDefault(ctx context.Context, id int64, userID int64) error {
	// 开启事务
	o := orm.NewOrm()
	tx, err := o.Begin()
	// 检查事务是否开启成功
	if err != nil {
		logs.Error("[SetDefault] 开启事务失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	// 查询地址是否存在且属于该用户
	address := &models.Address{ID: id, UserID: userID}
	if err := tx.Read(address, "ID", "UserID"); err != nil {
		tx.Rollback()
		if err == orm.ErrNoRows {
			return errors.New("地址不存在或不属于该用户")
		}
		logs.Error("查询地址失败: %v", err)
		return err
	}

	// 重置用户所有地址为非默认
	if err := r.resetDefaultInTx(tx, userID); err != nil {
		tx.Rollback()
		return err
	}

	// 设置当前地址为默认
	address.IsDefault = true
	address.UpdatedAt = time.Now()
	_, err = tx.Update(address, "IsDefault", "UpdatedAt")
	if err != nil {
		tx.Rollback()
		logs.Error("设置默认地址失败: %v", err)
		return err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logs.Error("提交事务失败: %v", err)
		return err
	}

	return nil
}

// ResetDefault 重置用户的默认地址
func (r *AddressRepositoryImpl) ResetDefault(ctx context.Context, userID int64) error {
	_, err := r.ormer.QueryTable(new(models.Address)).
		Filter("UserID", userID).
		Filter("IsDefault", true).
		Update(orm.Params{
			"IsDefault": false,
			"UpdatedAt": time.Now(),
		})

	if err != nil {
		logs.Error("重置默认地址失败: %v", err)
		return err
	}

	return nil
}

// CountByUserID 统计用户的地址数量
func (r *AddressRepositoryImpl) CountByUserID(ctx context.Context, userID int64) (int64, error) {
	count, err := r.ormer.QueryTable(new(models.Address)).Filter("UserID", userID).Count()
	if err != nil {
		logs.Error("统计地址数量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// resetDefaultInTx 在事务中重置用户的默认地址（辅助方法）
func (r *AddressRepositoryImpl) resetDefaultInTx(tx orm.TxOrmer, userID int64) error {
	_, err := tx.QueryTable(new(models.Address)).
		Filter("UserID", userID).
		Filter("IsDefault", true).
		Update(orm.Params{
			"IsDefault": false,
			"UpdatedAt": time.Now(),
		})

	if err != nil {
		logs.Error("重置默认地址失败: %v", err)
		return err
	}

	return nil
}
