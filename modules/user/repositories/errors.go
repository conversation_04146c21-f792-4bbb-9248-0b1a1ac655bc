/**
 * errors.go
 * 用户模块错误常量定义
 * 定义仓储层可能返回的各种错误
 */

package repositories

import (
	"errors"
)

var (
	// ErrInsufficientBalance 余额不足错误
	ErrInsufficientBalance = errors.New("用户余额不足")
	
	// ErrInsufficientFrozenBalance 冻结余额不足错误
	ErrInsufficientFrozenBalance = errors.New("用户冻结余额不足")
	
	// ErrUserNotFound 用户不存在错误
	ErrUserNotFound = errors.New("用户不存在")
	
	// ErrAccountNotFound 账户不存在错误
	ErrAccountNotFound = errors.New("用户账户不存在")
	
	// ErrAccountFrozen 账户被冻结
	ErrAccountFrozen = errors.New("用户账户已被冻结")
)
