/**
 * 用户仓库接口定义
 *
 * 该文件定义了用户模块的仓库层接口，声明了数据访问所需的方法。
 * 通过接口定义与实现分离，便于单元测试和功能扩展。
 */

package repositories

import (
	"context"
	"time"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
)

// UserRepository 用户仓库接口
type UserRepository interface {
	// 用户相关方法
	Create(ctx context.Context, user *models.User) (int64, error)
	Update(ctx context.Context, user *models.User) error
	Delete(ctx context.Context, id int64) error
	GetByID(ctx context.Context, id int64) (*models.User, error)
	GetByUsername(ctx context.Context, username string) (*models.User, error)
	GetByMobile(ctx context.Context, mobile string) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	GetByReferralCode(ctx context.Context, referralCode string) (*models.User, error)
	GetByWxOpenID(ctx context.Context, wxOpenID string) (*models.User, error)
	// GetByWechatUnionID 根据微信UnionID获取用户（微信网页登录使用）
	GetByWechatUnionID(ctx context.Context, unionID string) (*models.User, error)
	// GetByWechatOpenID 根据微信网页OpenID获取用户（微信网页登录使用）
	GetByWechatOpenID(ctx context.Context, openID string) (*models.User, error)
	// Get 通用获取用户方法
	Get(ctx context.Context, user *models.User, fields ...string) error
	// UpdateFields 更新用户指定字段
	UpdateFields(ctx context.Context, user *models.User, fields ...string) error
	List(ctx context.Context, page, pageSize int) ([]*models.User, int64, error)
	UpdatePassword(ctx context.Context, id int64, password string) error
	UpdateStatus(ctx context.Context, id int64, status int) error
	UpdateLoginInfo(ctx context.Context, id int64, loginIP string) error
	UpdateBalance(ctx context.Context, id int64, amount float64) error
	UpdatePoints(ctx context.Context, id int64, points int64) error
	UpdateReferralCode(ctx context.Context, id int64, referralCode string) error
	// 获取总用户数
	GetTotalCount(ctx context.Context) (int64, error)
}

// AddressRepository 地址仓库接口
type AddressRepository interface {
	// 地址相关方法
	Create(ctx context.Context, address *models.Address) (int64, error)
	Update(ctx context.Context, address *models.Address) error
	Delete(ctx context.Context, id int64, userID int64) error
	GetByID(ctx context.Context, id int64) (*models.Address, error)
	ListByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*models.Address, int64, error)
	GetDefaultAddress(ctx context.Context, userID int64) (*models.Address, error)
	SetDefault(ctx context.Context, id int64, userID int64) error
	ResetDefault(ctx context.Context, userID int64) error
	CountByUserID(ctx context.Context, userID int64) (int64, error)
}

// UserReferralRepository 用户分销关系仓库接口
type UserReferralRepository interface {
	// 创建分销关系
	Create(ctx context.Context, referral *models.UserReferral) (int64, error)
	// 更新分销关系
	Update(ctx context.Context, referral *models.UserReferral) error
	// 删除分销关系
	Delete(ctx context.Context, id int64) error
	// 获取用户的推荐人
	GetReferrerByUserID(ctx context.Context, userID int64) (*models.UserReferral, error)
	// 获取用户直接推荐的用户列表
	GetDirectReferrals(ctx context.Context, referrerID int64, page, pageSize int) ([]*models.UserReferral, int64, error)
	// 获取用户所有层级的推荐用户列表
	GetAllReferrals(ctx context.Context, referrerID int64, level int, page, pageSize int) ([]*models.UserReferral, int64, error)
	// 检查是否已存在推荐关系
	CheckReferralExists(ctx context.Context, userID int64) (bool, error)
	// 更新分销佣金
	UpdateCommission(ctx context.Context, id int64, amount float64) error
	// 获取分销关系详情
	GetByID(ctx context.Context, id int64) (*models.UserReferral, error)
	// 根据用户ID和推荐人ID获取分销关系
	GetByUserAndReferrer(ctx context.Context, userID, referrerID int64) (*models.UserReferral, error)
	
	// 管理员相关方法
	// 获取所有分销关系（管理员）
	GetAllReferralsForAdmin(ctx context.Context, req *dto.AdminReferralQueryRequest) ([]*models.UserReferral, int64, error)
	// 获取总分销关系数
	GetTotalCount(ctx context.Context) (int64, error)
	// 根据状态获取分销关系数
	GetCountByStatus(ctx context.Context, status int) (int64, error)
	// 根据级别获取分销关系数
	GetCountByLevel(ctx context.Context, level int) (int64, error)
	// 获取总佣金
	GetTotalCommission(ctx context.Context) (float64, error)
	// 根据日期范围获取佣金
	GetCommissionByDateRange(ctx context.Context, startTime, endTime time.Time) (float64, error)
	// 根据日期范围获取分销关系数
	GetCountByDateRange(ctx context.Context, startTime, endTime time.Time) (int64, error)
	// 获取TOP推荐人数据
	GetTopReferrers(ctx context.Context, startTime, endTime time.Time, limit int) ([]*dto.TopReferrerResponse, error)
	// 获取按级别分类的佣金总额
	GetCommissionByLevel(ctx context.Context, level int) (float64, error)
	// 获取按级别和日期范围分类的佣金
	GetCommissionByLevelAndDateRange(ctx context.Context, level int, startTime, endTime time.Time) (float64, error)
	// 获取每日佣金统计数据
	GetDailyCommissionStats(ctx context.Context, startTime, endTime time.Time) ([]dto.DailyCommissionStat, error)
	// 获取分销用户数量
	GetReferralUserCount(ctx context.Context) (int64, error)
	// 获取待结算佣金总额
	GetPendingCommission(ctx context.Context) (float64, error)
	// 获取已结算佣金总额
	GetSettledCommission(ctx context.Context) (float64, error)
	// 获取不同时段的佣金统计
	GetCommissionByTimeRanges(ctx context.Context, startDate, endDate time.Time) (morning, afternoon, evening float64, err error)
	// 获取指定日期范围内的订单数量
	GetOrderCountByDateRange(ctx context.Context, startDate, endDate time.Time) (int64, error)
}

// ReferralConfigRepository 分销配置仓库接口
type ReferralConfigRepository interface {
	// 创建配置
	Create(ctx context.Context, config *models.ReferralConfig) (int64, error)
	// 更新配置
	Update(ctx context.Context, config *models.ReferralConfig) error
	// 删除配置
	Delete(ctx context.Context, id int64) error
	// 根据ID获取配置
	GetByID(ctx context.Context, id int64) (*models.ReferralConfig, error)
	// 根据配置键获取配置
	GetByKey(ctx context.Context, configKey string) (*models.ReferralConfig, error)
	// 根据配置类型获取配置列表
	GetByType(ctx context.Context, configType string) ([]*models.ReferralConfig, error)
	// 获取所有启用的配置
	GetEnabledConfigs(ctx context.Context) ([]*models.ReferralConfig, error)
	// 分页查询配置
	List(ctx context.Context, configType string, status *int, page, pageSize int) ([]*models.ReferralConfig, int64, error)
	// 更新配置状态
	UpdateStatus(ctx context.Context, id int64, status int) error
}
