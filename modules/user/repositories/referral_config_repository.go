/**
 * 分销配置仓库实现
 *
 * 该文件实现了分销配置的数据访问层，提供配置的增删改查功能。
 * 支持按配置键、配置类型、状态等条件查询配置信息。
 */

package repositories

import (
	"context"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"o_mall_backend/modules/user/models"
)

// referralConfigRepository 分销配置仓库实现
type referralConfigRepository struct{}

// NewReferralConfigRepository 创建分销配置仓库实例
func NewReferralConfigRepository() ReferralConfigRepository {
	return &referralConfigRepository{}
}

// Create 创建配置
func (r *referralConfigRepository) Create(ctx context.Context, config *models.ReferralConfig) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(config)
	if err != nil {
		return 0, fmt.Errorf("创建分销配置失败: %w", err)
	}
	return id, nil
}

// Update 更新配置
func (r *referralConfigRepository) Update(ctx context.Context, config *models.ReferralConfig) error {
	o := orm.NewOrm()
	_, err := o.Update(config)
	if err != nil {
		return fmt.Errorf("更新分销配置失败: %w", err)
	}
	return nil
}

// Delete 删除配置
func (r *referralConfigRepository) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	config := &models.ReferralConfig{ID: id}
	_, err := o.Delete(config)
	if err != nil {
		return fmt.Errorf("删除分销配置失败: %w", err)
	}
	return nil
}

// GetByID 根据ID获取配置
func (r *referralConfigRepository) GetByID(ctx context.Context, id int64) (*models.ReferralConfig, error) {
	o := orm.NewOrm()
	config := &models.ReferralConfig{ID: id}
	err := o.Read(config)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("获取分销配置失败: %w", err)
	}
	return config, nil
}

// GetByKey 根据配置键获取配置
func (r *referralConfigRepository) GetByKey(ctx context.Context, configKey string) (*models.ReferralConfig, error) {
	o := orm.NewOrm()
	config := &models.ReferralConfig{}
	err := o.QueryTable(config.TableName()).Filter("config_key", configKey).Filter("status", models.ReferralConfigStatusEnabled).One(config)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("根据配置键获取分销配置失败: %w", err)
	}
	return config, nil
}

// GetByType 根据配置类型获取配置列表
func (r *referralConfigRepository) GetByType(ctx context.Context, configType string) ([]*models.ReferralConfig, error) {
	o := orm.NewOrm()
	var configs []*models.ReferralConfig
	_, err := o.QueryTable((&models.ReferralConfig{}).TableName()).Filter("config_type", configType).Filter("status", models.ReferralConfigStatusEnabled).OrderBy("created_at").All(&configs)
	if err != nil {
		return nil, fmt.Errorf("根据配置类型获取分销配置失败: %w", err)
	}
	return configs, nil
}

// GetEnabledConfigs 获取所有启用的配置
func (r *referralConfigRepository) GetEnabledConfigs(ctx context.Context) ([]*models.ReferralConfig, error) {
	o := orm.NewOrm()
	var configs []*models.ReferralConfig
	_, err := o.QueryTable((&models.ReferralConfig{}).TableName()).Filter("status", models.ReferralConfigStatusEnabled).OrderBy("config_type", "created_at").All(&configs)
	if err != nil {
		return nil, fmt.Errorf("获取启用的分销配置失败: %w", err)
	}
	return configs, nil
}

// List 分页查询配置
func (r *referralConfigRepository) List(ctx context.Context, configType string, status *int, page, pageSize int) ([]*models.ReferralConfig, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable((&models.ReferralConfig{}).TableName())

	// 添加过滤条件
	if configType != "" {
		qs = qs.Filter("config_type", configType)
	}
	if status != nil {
		qs = qs.Filter("status", *status)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, fmt.Errorf("获取分销配置总数失败: %w", err)
	}

	// 分页查询
	var configs []*models.ReferralConfig
	offset := (page - 1) * pageSize
	_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&configs)
	if err != nil {
		return nil, 0, fmt.Errorf("分页查询分销配置失败: %w", err)
	}

	return configs, total, nil
}

// UpdateStatus 更新配置状态
func (r *referralConfigRepository) UpdateStatus(ctx context.Context, id int64, status int) error {
	o := orm.NewOrm()
	config := &models.ReferralConfig{ID: id}
	if err := o.Read(config); err != nil {
		return fmt.Errorf("配置不存在: %w", err)
	}

	config.Status = status
	_, err := o.Update(config, "status", "updated_at")
	if err != nil {
		return fmt.Errorf("更新分销配置状态失败: %w", err)
	}
	return nil
}