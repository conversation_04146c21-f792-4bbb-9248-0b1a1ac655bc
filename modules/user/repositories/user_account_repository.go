/**
 * user_account_repository.go
 * 用户账户仓储接口定义
 * 负责用户账户数据的增删改查操作
 */

package repositories

import (
	"fmt"
	"github.com/beego/beego/v2/client/orm"

	"o_mall_backend/modules/user/models"
)

// UserAccountRepository 用户账户仓储接口
type UserAccountRepository interface {
	// 通过用户ID获取账户信息
	GetUserAccountByUserID(userID int64) (*models.UserAccount, error)
	// 创建用户账户
	CreateUserAccount(account *models.UserAccount) (int64, error)
	// 更新用户账户
	UpdateUserAccount(account *models.UserAccount) error
	// 更新用户余额事务
	UpdateUserBalanceTx(o orm.TxOrmer, userID int64, amount float64, isDecrease bool) (*models.UserAccount, error)
	// 冻结用户余额
	FreezeUserBalance(userID int64, amount float64) error
	// 解冻用户余额
	UnfreezeUserBalance(userID int64, amount float64) error
	// 创建用户账户交易记录
	CreateTransaction(transaction *models.UserAccountTransaction) (int64, error)
	// 创建事务内交易记录
	CreateTransactionTx(o orm.TxOrmer, transaction *models.UserAccountTransaction) (int64, error)
	// 查询用户交易记录
	GetUserTransactions(userID int64, transactionType string, page, pageSize int) ([]*models.UserAccountTransaction, int64, error)
	// 查询交易记录详情
	GetTransactionByID(id int64) (*models.UserAccountTransaction, error)
	// 查询交易记录通过流水号
	GetTransactionByNo(transactionNo string) (*models.UserAccountTransaction, error)
}

// UserAccountRepositoryImpl 用户账户仓储实现
type UserAccountRepositoryImpl struct{}

// NewUserAccountRepository 创建用户账户仓储
func NewUserAccountRepository() UserAccountRepository {
	return &UserAccountRepositoryImpl{}
}

// GetUserAccountByUserID 通过用户ID获取账户信息
func (r *UserAccountRepositoryImpl) GetUserAccountByUserID(userID int64) (*models.UserAccount, error) {
	o := orm.NewOrm()
	account := &models.UserAccount{}
	
	// 使用字段名称 UserID 而非表字段名 user_id，让orm根据模型结构进行映射
	err := o.QueryTable(new(models.UserAccount)).Filter("UserID", userID).One(account)
	if err != nil {
		// 正确传递 ErrNoRows 错误，以便调用者可以区分记录不存在和其他错误
		return nil, err
	}
	
	return account, nil
}

// CreateUserAccount 创建用户账户
func (r *UserAccountRepositoryImpl) CreateUserAccount(account *models.UserAccount) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(account)
	if err != nil {
		return 0, err
	}
	return id, nil
}

// UpdateUserAccount 更新用户账户
func (r *UserAccountRepositoryImpl) UpdateUserAccount(account *models.UserAccount) error {
	o := orm.NewOrm()
	_, err := o.Update(account)
	return err
}

// UpdateUserBalanceTx 更新用户余额事务
func (r *UserAccountRepositoryImpl) UpdateUserBalanceTx(o orm.TxOrmer, userID int64, amount float64, isDecrease bool) (*models.UserAccount, error) {
	account := &models.UserAccount{}
	
	err := o.QueryTable(new(models.UserAccount)).Filter("UserID", userID).ForUpdate().One(account)
	if err != nil {
		return nil, err
	}
	
	// 根据操作类型更新余额
	if isDecrease {
		// 余额不足
		if account.Balance < amount {
			return nil, ErrInsufficientBalance
		}
		account.Balance -= amount
		account.TotalConsume += amount
	} else {
		account.Balance += amount
		account.TotalRecharge += amount
	}
	
	_, err = o.Update(account)
	if err != nil {
		return nil, err
	}
	
	return account, nil
}

// FreezeUserBalance 冻结用户余额
func (r *UserAccountRepositoryImpl) FreezeUserBalance(userID int64, amount float64) error {
	o := orm.NewOrm()
	txOrm, err := o.Begin()
	if err != nil {
		return err
	}
	
	account := &models.UserAccount{}
	err = txOrm.QueryTable(new(models.UserAccount)).Filter("UserID", userID).ForUpdate().One(account)
	if err != nil {
		txOrm.Rollback()
		return err
	}
	
	if account.Balance < amount {
		txOrm.Rollback()
		return ErrInsufficientBalance
	}
	
	account.Balance -= amount
	account.FrozenBalance += amount
	
	_, err = txOrm.Update(account)
	if err != nil {
		txOrm.Rollback()
		return err
	}
	
	return txOrm.Commit()
}

// UnfreezeUserBalance 解冻用户余额
func (r *UserAccountRepositoryImpl) UnfreezeUserBalance(userID int64, amount float64) error {
	o := orm.NewOrm()
	txOrm, err := o.Begin()
	if err != nil {
		return err
	}
	
	account := &models.UserAccount{}
	err = txOrm.QueryTable(new(models.UserAccount)).Filter("UserID", userID).ForUpdate().One(account)
	if err != nil {
		txOrm.Rollback()
		return err
	}
	
	if account.FrozenBalance < amount {
		txOrm.Rollback()
		return ErrInsufficientFrozenBalance
	}
	
	account.Balance += amount
	account.FrozenBalance -= amount
	
	_, err = txOrm.Update(account)
	if err != nil {
		txOrm.Rollback()
		return err
	}
	
	return txOrm.Commit()
}

// CreateTransaction 创建用户账户交易记录
func (r *UserAccountRepositoryImpl) CreateTransaction(transaction *models.UserAccountTransaction) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(transaction)
	if err != nil {
		return 0, err
	}
	return id, nil
}

// CreateTransactionTx 创建事务内交易记录
func (r *UserAccountRepositoryImpl) CreateTransactionTx(o orm.TxOrmer, transaction *models.UserAccountTransaction) (int64, error) {
	id, err := o.Insert(transaction)
	if err != nil {
		return 0, err
	}
	return id, nil
}

// GetUserTransactions 查询用户交易记录
func (r *UserAccountRepositoryImpl) GetUserTransactions(userID int64, transactionType string, page, pageSize int) ([]*models.UserAccountTransaction, int64, error) {
	o := orm.NewOrm()
	// 开启SQL调试
	orm.Debug = true
	
	var transactions []*models.UserAccountTransaction
	
	// 添加调试日志
	fmt.Printf("[DEBUG] Repository GetUserTransactions - UserID: %d, TransactionType: %s, Page: %d, PageSize: %d\n", userID, transactionType, page, pageSize)
	
	// 先测试直接SQL查询
	sql := "SELECT COUNT(*) FROM user_account_transaction WHERE user_i_d = ?"
	var directCount int64
	err := o.Raw(sql, userID).QueryRow(&directCount)
	if err != nil {
		fmt.Printf("[ERROR] Direct SQL count failed: %v\n", err)
	} else {
		fmt.Printf("[DEBUG] Direct SQL count result: %d\n", directCount)
	}
	
	// 移除type查询条件，只按userID查询
	qs := o.QueryTable(new(models.UserAccountTransaction)).Filter("UserID", userID)
	
	total, err := qs.Count()
	if err != nil {
		fmt.Printf("[ERROR] Count query failed: %v\n", err)
		return nil, 0, err
	}
	
	fmt.Printf("[DEBUG] ORM count result: %d\n", total)
	
	offset := (page - 1) * pageSize
	_, err = qs.OrderBy("-ID").Limit(pageSize, offset).All(&transactions)
	if err != nil {
		fmt.Printf("[ERROR] Query transactions failed: %v\n", err)
		return nil, 0, err
	}
	
	fmt.Printf("[DEBUG] Query returned %d transactions\n", len(transactions))
	for i, tx := range transactions {
		fmt.Printf("[DEBUG] Transaction %d: ID=%d, UserID=%d, Amount=%.2f, Type=%s\n", i, tx.ID, tx.UserID, tx.Amount, tx.Type)
	}
	
	return transactions, total, nil
}

// GetTransactionByID 查询交易记录详情
func (r *UserAccountRepositoryImpl) GetTransactionByID(id int64) (*models.UserAccountTransaction, error) {
	o := orm.NewOrm()
	transaction := &models.UserAccountTransaction{ID: id}
	err := o.Read(transaction)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return transaction, nil
}

// GetTransactionByNo 查询交易记录通过流水号
func (r *UserAccountRepositoryImpl) GetTransactionByNo(transactionNo string) (*models.UserAccountTransaction, error) {
	o := orm.NewOrm()
	transaction := &models.UserAccountTransaction{}
	err := o.QueryTable(new(models.UserAccountTransaction)).Filter("TransactionNo", transactionNo).One(transaction)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return transaction, nil
}
