/**
 * 用户日志仓库
 *
 * 该文件定义了用户日志仓库接口及其实现，用于用户日志数据的持久化和查询操作。
 * 提供了创建、查询等基本的数据访问功能。
 */

package repositories

import (
	"context"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/models"
)

// UserLogRepository 用户日志仓库接口
type UserLogRepository interface {
	// Create 创建日志
	Create(ctx context.Context, log *models.UserLog) (int64, error)
	
	// GetByID 根据ID获取日志
	GetByID(ctx context.Context, id int64) (*models.UserLog, error)
	
	// List 查询日志列表
	List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.UserLog, int64, error)
	
	// GetLatestByUserID 获取用户最近的日志
	GetLatestByUserID(ctx context.Context, userID int64, limit int) ([]*models.UserLog, error)
}

// UserLogRepositoryImpl 用户日志仓库实现
type UserLogRepositoryImpl struct{}

// NewUserLogRepository 创建用户日志仓库
func NewUserLogRepository() UserLogRepository {
	return &UserLogRepositoryImpl{}
}

// Create 创建日志
func (r *UserLogRepositoryImpl) Create(ctx context.Context, log *models.UserLog) (int64, error) {
	o := orm.NewOrm()
	id, err := o.Insert(log)
	if err != nil {
		logs.Error("创建用户日志失败: %v", err)
		return 0, fmt.Errorf("创建用户日志失败: %v", err)
	}
	return id, nil
}

// GetByID 根据ID获取日志
func (r *UserLogRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.UserLog, error) {
	o := orm.NewOrm()
	log := &models.UserLog{ID: id}
	err := o.Read(log)
	if err == orm.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		logs.Error("获取用户日志失败: %v", err)
		return nil, fmt.Errorf("获取用户日志失败: %v", err)
	}
	return log, nil
}

// List 查询日志列表
func (r *UserLogRepositoryImpl) List(ctx context.Context, query map[string]interface{}, page, pageSize int) ([]*models.UserLog, int64, error) {
	o := orm.NewOrm()
	qs := o.QueryTable(new(models.UserLog))
	
	// 应用查询条件
	for key, value := range query {
		if value != nil && value != "" && value != 0 {
			qs = qs.Filter(key, value)
		}
	}
	
	// 统计总数
	total, err := qs.Count()
	if err != nil {
		logs.Error("统计用户日志数量失败: %v", err)
		return nil, 0, fmt.Errorf("统计用户日志数量失败: %v", err)
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	var logList []*models.UserLog
	_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&logList)
	if err != nil {
		logs.Error("查询用户日志列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询用户日志列表失败: %v", err)
	}
	
	return logList, total, nil
}

// GetLatestByUserID 获取用户最近的日志
func (r *UserLogRepositoryImpl) GetLatestByUserID(ctx context.Context, userID int64, limit int) ([]*models.UserLog, error) {
	o := orm.NewOrm()
	var logList []*models.UserLog
	
	_, err := o.QueryTable(new(models.UserLog)).
		Filter("user_id", userID).
		OrderBy("-created_at").
		Limit(limit).
		All(&logList)
	
	if err != nil {
		logs.Error("获取用户最近日志失败: %v", err)
		return nil, fmt.Errorf("获取用户最近日志失败: %v", err)
	}
	
	return logList, nil
}
