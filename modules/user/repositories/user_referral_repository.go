/**
 * 用户分销关系仓库实现
 *
 * 该文件实现了用户分销关系的数据访问层，提供对用户分销关系表的增删改查操作。
 * 实现了UserReferralRepository接口中定义的所有方法。
 */

package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/utils/common"
)

// userReferralRepository 用户分销关系仓库实现
type userReferralRepository struct{}

// NewUserReferralRepository 创建用户分销关系仓库实例
func NewUserReferralRepository() UserReferralRepository {
	return &userReferralRepository{}
}

// Create 创建分销关系
func (r *userReferralRepository) Create(ctx context.Context, referral *models.UserReferral) (int64, error) {
	o := orm.NewOrm()
	return o.Insert(referral)
}

// Update 更新分销关系
func (r *userReferralRepository) Update(ctx context.Context, referral *models.UserReferral) error {
	o := orm.NewOrm()
	_, err := o.Update(referral)
	return err
}

// GetReferrerByUserID 获取用户的推荐人
func (r *userReferralRepository) GetReferrerByUserID(ctx context.Context, userID int64) (*models.UserReferral, error) {
	o := orm.NewOrm()
	var referral models.UserReferral
	err := o.QueryTable(new(models.UserReferral)).Filter("user_id", userID).Filter("status", models.ReferralStatusValid).One(&referral)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &referral, nil
}

// GetDirectReferrals 获取用户直接推荐的用户列表
func (r *userReferralRepository) GetDirectReferrals(ctx context.Context, referrerID int64, page, pageSize int) ([]*models.UserReferral, int64, error) {
	o := orm.NewOrm()
	var referrals []*models.UserReferral
	
	qs := o.QueryTable(new(models.UserReferral)).Filter("referrer_id", referrerID).Filter("level", models.ReferralLevelOne).Filter("status", models.ReferralStatusValid)
	
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}
	
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&referrals)
	} else {
		_, err = qs.OrderBy("-created_at").All(&referrals)
	}
	
	if err != nil {
		return nil, 0, err
	}
	
	return referrals, total, nil
}

// GetAllReferrals 获取用户所有层级的推荐用户列表
func (r *userReferralRepository) GetAllReferrals(ctx context.Context, referrerID int64, level int, page, pageSize int) ([]*models.UserReferral, int64, error) {
	o := orm.NewOrm()
	var referrals []*models.UserReferral
	
	qs := o.QueryTable(new(models.UserReferral)).Filter("referrer_id", referrerID).Filter("status", models.ReferralStatusValid)
	
	if level > 0 {
		qs = qs.Filter("level", level)
	}
	
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}
	
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		_, err = qs.OrderBy("-created_at").Limit(pageSize, offset).All(&referrals)
	} else {
		_, err = qs.OrderBy("-created_at").All(&referrals)
	}
	
	if err != nil {
		return nil, 0, err
	}
	
	return referrals, total, nil
}

// CheckReferralExists 检查是否已存在推荐关系
func (r *userReferralRepository) CheckReferralExists(ctx context.Context, userID int64) (bool, error) {
	o := orm.NewOrm()
	exists := o.QueryTable(new(models.UserReferral)).Filter("user_id", userID).Filter("status", models.ReferralStatusValid).Exist()
	return exists, nil
}

// UpdateCommission 更新分销佣金
func (r *userReferralRepository) UpdateCommission(ctx context.Context, id int64, amount float64) error {
	o := orm.NewOrm()
	referral := models.UserReferral{ID: id}
	
	if err := o.Read(&referral); err != nil {
		return err
	}
	
	referral.Commission += amount
	_, err := o.Update(&referral, "commission", "updated_at")
	return err
}

// GetByID 获取分销关系详情
func (r *userReferralRepository) GetByID(ctx context.Context, id int64) (*models.UserReferral, error) {
	o := orm.NewOrm()
	referral := models.UserReferral{ID: id}
	
	if err := o.Read(&referral); err != nil {
		if err == orm.ErrNoRows {
			return nil, errors.New("分销关系不存在")
		}
		return nil, err
	}
	
	return &referral, nil
}

// GetByUserAndReferrer 根据用户ID和推荐人ID获取分销关系
func (r *userReferralRepository) GetByUserAndReferrer(ctx context.Context, userID, referrerID int64) (*models.UserReferral, error) {
	o := orm.NewOrm()
	var referral models.UserReferral
	
	err := o.QueryTable(new(models.UserReferral)).Filter("user_id", userID).Filter("referrer_id", referrerID).One(&referral)
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	
	return &referral, nil
}

// Delete 删除分销关系
func (r *userReferralRepository) Delete(ctx context.Context, id int64) error {
	o := orm.NewOrm()
	referral := models.UserReferral{ID: id}
	_, err := o.Delete(&referral)
	return err
}

// GetAllReferralsForAdmin 获取所有分销关系（管理员）
func (r *userReferralRepository) GetAllReferralsForAdmin(ctx context.Context, req *dto.AdminReferralQueryRequest) ([]*models.UserReferral, int64, error) {
	o := orm.NewOrm()
	var referrals []*models.UserReferral
	
	qs := o.QueryTable(new(models.UserReferral))
	
	// 添加筛选条件
	if req.ReferrerID > 0 {
		qs = qs.Filter("referrer_id", req.ReferrerID)
	}
	if req.RefereeID > 0 {
		qs = qs.Filter("user_id", req.RefereeID)
	}
	if req.Status >= 0 {
		qs = qs.Filter("status", req.Status)
	}
	if req.Level > 0 {
		qs = qs.Filter("level", req.Level)
	}
	
	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		_, err = qs.OrderBy("-created_at").Limit(req.PageSize, offset).All(&referrals)
	} else {
		_, err = qs.OrderBy("-created_at").All(&referrals)
	}
	
	if err != nil {
		return nil, 0, err
	}
	
	return referrals, total, nil
}

// GetTotalCount 获取总分销关系数
func (r *userReferralRepository) GetTotalCount(ctx context.Context) (int64, error) {
	o := orm.NewOrm()
	return o.QueryTable(new(models.UserReferral)).Count()
}

// GetCountByStatus 根据状态获取分销关系数
func (r *userReferralRepository) GetCountByStatus(ctx context.Context, status int) (int64, error) {
	o := orm.NewOrm()
	return o.QueryTable(new(models.UserReferral)).Filter("status", status).Count()
}

// GetCountByLevel 根据级别获取分销关系数
func (r *userReferralRepository) GetCountByLevel(ctx context.Context, level int) (int64, error) {
	o := orm.NewOrm()
	return o.QueryTable(new(models.UserReferral)).Filter("level", level).Count()
}

// GetTotalCommission 获取总佣金
func (r *userReferralRepository) GetTotalCommission(ctx context.Context) (float64, error) {
	o := orm.NewOrm()
	var results []orm.Params
	_, err := o.Raw("SELECT SUM(commission) as total FROM user_referrals").Values(&results)
	if err != nil {
		return 0, err
	}
	
	if len(results) == 0 || results[0]["total"] == nil {
		return 0, nil
	}
	
	total, ok := results[0]["total"].(float64)
	if !ok {
		return 0, errors.New("佣金总额类型转换失败")
	}
	
	return total, nil
}

// GetCommissionByDateRange 根据日期范围获取佣金
func (r *userReferralRepository) GetCommissionByDateRange(ctx context.Context, startTime, endTime time.Time) (float64, error) {
	o := orm.NewOrm()
	var results []orm.Params
	_, err := o.Raw("SELECT SUM(commission) as total FROM user_referrals WHERE updated_at >= ? AND updated_at <= ?", startTime, endTime).Values(&results)
	if err != nil {
		return 0, err
	}
	
	if len(results) == 0 || results[0]["total"] == nil {
		return 0, nil
	}
	
	total, ok := results[0]["total"].(float64)
	if !ok {
		return 0, errors.New("佣金总额类型转换失败")
	}
	
	return total, nil
}

// GetCountByDateRange 根据日期范围获取分销关系数
func (r *userReferralRepository) GetCountByDateRange(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	o := orm.NewOrm()
	return o.QueryTable(new(models.UserReferral)).Filter("created_at__gte", startTime).Filter("created_at__lte", endTime).Count()
}

// GetTopReferrers 获取TOP推荐人数据
func (r *userReferralRepository) GetTopReferrers(ctx context.Context, startTime, endTime time.Time, limit int) ([]*dto.TopReferrerResponse, error) {
	o := orm.NewOrm()
	var results []orm.Params
	
	// 构建SQL查询
	sql := `
		SELECT 
			u.id as user_id,
			u.username,
			u.nickname,
			u.avatar,
			u.mobile as phone,
			COUNT(ur.id) as referral_count,
			SUM(ur.commission) as total_commission,
			SUM(CASE WHEN ur.level = 1 THEN 1 ELSE 0 END) as level1_count,
			SUM(CASE WHEN ur.level = 2 THEN 1 ELSE 0 END) as level2_count,
			SUM(CASE WHEN ur.level = 3 THEN 1 ELSE 0 END) as level3_count
		FROM users u
		INNER JOIN user_referrals ur ON u.id = ur.referrer_id
		WHERE ur.status = 1`
	
	args := []interface{}{}
	
	// 添加时间范围条件
	if !startTime.IsZero() {
		sql += " AND ur.created_at >= ?"
		args = append(args, startTime)
	}

	if !endTime.IsZero() {
		sql += " AND ur.created_at <= ?"
		args = append(args, endTime)
	}

	// 添加分组和排序
	sql += ` GROUP BY u.id ORDER BY referral_count DESC, total_commission DESC LIMIT ?`
	args = append(args, limit)

	// 执行查询
	num, err := o.Raw(sql, args...).Values(&results)
	if err != nil {
		return nil, err
	}

	// 处理结果
	topReferrers := make([]*dto.TopReferrerResponse, 0, num)
	for _, row := range results {
		var userID, referralCount, level1Count, level2Count, level3Count int64
		var totalCommission float64

		if v, ok := row["user_id"]; ok {
			userID = common.ParseInt64(fmt.Sprintf("%v", v), 0)
		}
		if v, ok := row["referral_count"]; ok {
			referralCount = common.ParseInt64(fmt.Sprintf("%v", v), 0)
		}
		if v, ok := row["total_commission"]; ok {
			totalCommission = common.ParseFloat64(fmt.Sprintf("%v", v), 0)
		}
		if v, ok := row["level1_count"]; ok {
			level1Count = common.ParseInt64(fmt.Sprintf("%v", v), 0)
		}
		if v, ok := row["level2_count"]; ok {
			level2Count = common.ParseInt64(fmt.Sprintf("%v", v), 0)
		}
		if v, ok := row["level3_count"]; ok {
			level3Count = common.ParseInt64(fmt.Sprintf("%v", v), 0)
		}

		topReferrer := &dto.TopReferrerResponse{
			UserID:          userID,
			Username:        fmt.Sprintf("%v", row["username"]),
			Nickname:        fmt.Sprintf("%v", row["nickname"]),
			Avatar:          fmt.Sprintf("%v", row["avatar"]),
			Phone:           fmt.Sprintf("%v", row["phone"]),
			ReferralCount:   referralCount,
			TotalCommission: totalCommission,
			Level1Count:     level1Count,
			Level2Count:     level2Count,
			Level3Count:     level3Count,
		}

		topReferrers = append(topReferrers, topReferrer)
	}

	return topReferrers, nil
}

// GetCommissionByLevel 获取按级别分类的佣金总额
func (r *userReferralRepository) GetCommissionByLevel(ctx context.Context, level int) (float64, error) {
	var commission float64
	sql := `SELECT COALESCE(SUM(commission), 0) FROM user_referrals WHERE level = ?`
	err := orm.NewOrm().Raw(sql, level).QueryRow(&commission)
	if err != nil && err != orm.ErrNoRows {
		return 0, err
	}
	return commission, nil
}

// GetCommissionByLevelAndDateRange 获取按级别和日期范围分类的佣金
func (r *userReferralRepository) GetCommissionByLevelAndDateRange(ctx context.Context, level int, startTime, endTime time.Time) (float64, error) {
	var commission float64
	sql := `SELECT COALESCE(SUM(commission), 0) FROM user_referrals WHERE level = ? AND created_at BETWEEN ? AND ?`
	err := orm.NewOrm().Raw(sql, level, startTime, endTime).QueryRow(&commission)
	if err != nil && err != orm.ErrNoRows {
		return 0, err
	}
	return commission, nil
}

// GetDailyCommissionStats 获取每日佣金统计数据
func (r *userReferralRepository) GetDailyCommissionStats(ctx context.Context, startTime, endTime time.Time) ([]dto.DailyCommissionStat, error) {
	var stats []dto.DailyCommissionStat

	sql := `
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m-%d') as date,
            COALESCE(SUM(commission), 0) as commission,
            COUNT(DISTINCT id) as order_count
        FROM 
            user_referrals
        WHERE 
            created_at BETWEEN ? AND ?
        GROUP BY 
            DATE_FORMAT(created_at, '%Y-%m-%d')
        ORDER BY 
            date
    `

	_, err := orm.NewOrm().Raw(sql, startTime, endTime).QueryRows(&stats)
	if err != nil && err != orm.ErrNoRows {
		return nil, err
	}

	return stats, nil
}

// GetReferralUserCount 获取分销用户数量
func (r *userReferralRepository) GetReferralUserCount(ctx context.Context) (int64, error) {
	var count int64
	sql := `SELECT COUNT(DISTINCT user_id) FROM user_referrals`
	err := orm.NewOrm().Raw(sql).QueryRow(&count)
	if err != nil && err != orm.ErrNoRows {
		return 0, err
	}
	return count, nil
}

// GetPendingCommission 获取待结算佣金总额
func (r *userReferralRepository) GetPendingCommission(ctx context.Context) (float64, error) {
	var commission float64
	sql := `SELECT COALESCE(SUM(commission), 0) FROM user_referrals WHERE status = 0`
	err := orm.NewOrm().Raw(sql).QueryRow(&commission)
	if err != nil && err != orm.ErrNoRows {
		return 0, err
	}
	return commission, nil
}

// GetSettledCommission 获取已结算佣金总额
func (r *userReferralRepository) GetSettledCommission(ctx context.Context) (float64, error) {
	var commission float64
	sql := `SELECT COALESCE(SUM(commission), 0) FROM user_referrals WHERE status = 1`
	err := orm.NewOrm().Raw(sql).QueryRow(&commission)
	if err != nil && err != orm.ErrNoRows {
		return 0, err
	}
	return commission, nil
}

// GetCommissionByTimeRanges 获取不同时段的佣金统计
func (r *userReferralRepository) GetCommissionByTimeRanges(ctx context.Context, startDate, endDate time.Time) (morning, afternoon, evening float64, err error) {
	sql := `
        SELECT 
            COALESCE(SUM(CASE WHEN HOUR(created_at) >= 6 AND HOUR(created_at) < 12 THEN commission ELSE 0 END), 0) as morning,
            COALESCE(SUM(CASE WHEN HOUR(created_at) >= 12 AND HOUR(created_at) < 18 THEN commission ELSE 0 END), 0) as afternoon,
            COALESCE(SUM(CASE WHEN HOUR(created_at) < 6 OR HOUR(created_at) >= 18 THEN commission ELSE 0 END), 0) as evening
        FROM 
            user_referrals
        WHERE 
            created_at BETWEEN ? AND ?
    `

	err = orm.NewOrm().Raw(sql, startDate, endDate).QueryRow(&morning, &afternoon, &evening)
	if err != nil && err != orm.ErrNoRows {
		return 0, 0, 0, err
	}

	return morning, afternoon, evening, nil
}

// GetOrderCountByDateRange 获取指定日期范围内的订单数量
func (r *userReferralRepository) GetOrderCountByDateRange(ctx context.Context, startDate, endDate time.Time) (int64, error) {
	var count int64
	sql := `SELECT COUNT(DISTINCT id) FROM user_referrals WHERE created_at BETWEEN ? AND ?`
	err := orm.NewOrm().Raw(sql, startDate, endDate).QueryRow(&count)
	if err != nil && err != orm.ErrNoRows {
		return 0, err
	}

	return count, nil
}
