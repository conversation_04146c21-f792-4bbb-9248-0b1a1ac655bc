/**
 * 用户仓库实现
 *
 * 该文件实现了用户仓库接口，负责用户数据的存储和检索。
 * 使用Beego ORM与数据库进行交互，实现用户相关的CRUD操作。
 */

package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/models"
)

// UserRepositoryImpl 用户仓库实现
type UserRepositoryImpl struct {
	ormer orm.Ormer
}

// NewUserRepository 创建用户仓库实例
func NewUserRepository() UserRepository {
	return &UserRepositoryImpl{
		ormer: orm.NewOrm(),
	}
}

// Create 创建用户
func (r *UserRepositoryImpl) Create(ctx context.Context, user *models.User) (int64, error) {
	// 设置创建时间
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	// 插入数据
	id, err := r.ormer.Insert(user)
	if err != nil {
		logs.Error("创建用户失败: %v", err)
		return 0, err
	}

	return id, nil
}

// Update 更新用户信息
func (r *UserRepositoryImpl) Update(ctx context.Context, user *models.User) error {
	// 更新时间
	user.UpdatedAt = time.Now()

	// 只更新特定字段
	_, err := r.ormer.Update(user, "Nickname", "Avatar", "Mobile", "Email", "Gender", "Birthday", "UpdatedAt")
	if err != nil {
		logs.Error("更新用户信息失败: %v", err)
		return err
	}

	return nil
}

// Delete 删除用户(软删除，更改状态)
func (r *UserRepositoryImpl) Delete(ctx context.Context, id int64) error {
	user := &models.User{ID: id}
	if err := r.ormer.Read(user, "ID"); err != nil {
		if err == orm.ErrNoRows {
			return errors.New("用户不存在")
		}
		logs.Error("查询用户失败: %v", err)
		return err
	}

	// 设置状态为禁用
	user.Status = models.UserStatusDisabled
	user.UpdatedAt = time.Now()

	_, err := r.ormer.Update(user, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("删除用户失败: %v", err)
		return err
	}

	return nil
}

// GetByID 根据ID获取用户
func (r *UserRepositoryImpl) GetByID(ctx context.Context, id int64) (*models.User, error) {
	user := &models.User{ID: id}
	if err := r.ormer.Read(user); err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 用户不存在返回nil
		}
		logs.Error("查询用户失败: %v", err)
		return nil, err
	}

	return user, nil
}

// GetByUsername 根据用户名获取用户
func (r *UserRepositoryImpl) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	user := &models.User{Username: username}
	if err := r.ormer.Read(user, "Username"); err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 用户不存在返回nil
		}
		logs.Error("查询用户失败: %v", err)
		return nil, err
	}

	return user, nil
}

// GetByMobile 根据手机号获取用户
func (r *UserRepositoryImpl) GetByMobile(ctx context.Context, mobile string) (*models.User, error) {
	user := &models.User{Mobile: mobile}
	if err := r.ormer.Read(user, "Mobile"); err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 用户不存在返回nil
		}
		logs.Error("查询用户失败: %v", err)
		return nil, err
	}

	return user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *UserRepositoryImpl) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	user := &models.User{Email: email}
	if err := r.ormer.Read(user, "Email"); err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 用户不存在返回nil
		}
		logs.Error("查询用户失败: %v", err)
		return nil, err
	}

	return user, nil
}

// GetByWxOpenID 根据微信OpenID获取用户
func (r *UserRepositoryImpl) GetByWxOpenID(ctx context.Context, wxOpenID string) (*models.User, error) {
	user := &models.User{WxOpenID: wxOpenID}
	if err := r.ormer.Read(user, "WxOpenID"); err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 用户不存在返回nil
		}
		logs.Error("根据微信OpenID查询用户失败: %v", err)
		return nil, err
	}

	return user, nil
}

// List 获取用户列表
func (r *UserRepositoryImpl) List(ctx context.Context, page, pageSize int) ([]*models.User, int64, error) {
	var users []*models.User

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 查询总数
	count, err := r.ormer.QueryTable(new(models.User)).Count()
	if err != nil {
		logs.Error("查询用户总数失败: %v", err)
		return nil, 0, err
	}

	// 查询数据
	_, err = r.ormer.QueryTable(new(models.User)).
		Limit(pageSize, offset).
		OrderBy("-CreatedAt").
		All(&users)

	if err != nil {
		logs.Error("查询用户列表失败: %v", err)
		return nil, 0, err
	}

	return users, count, nil
}

// UpdatePassword 更新用户密码
func (r *UserRepositoryImpl) UpdatePassword(ctx context.Context, id int64, password string) error {
	user := &models.User{ID: id}
	if err := r.ormer.Read(user, "ID"); err != nil {
		if err == orm.ErrNoRows {
			return errors.New("用户不存在")
		}
		logs.Error("查询用户失败: %v", err)
		return err
	}

	user.Password = password
	user.UpdatedAt = time.Now()

	_, err := r.ormer.Update(user, "Password", "UpdatedAt")
	if err != nil {
		logs.Error("更新用户密码失败: %v", err)
		return err
	}

	return nil
}

// UpdateStatus 更新用户状态
func (r *UserRepositoryImpl) UpdateStatus(ctx context.Context, id int64, status int) error {
	user := &models.User{ID: id}
	if err := r.ormer.Read(user, "ID"); err != nil {
		if err == orm.ErrNoRows {
			return errors.New("用户不存在")
		}
		logs.Error("查询用户失败: %v", err)
		return err
	}

	user.Status = status
	user.UpdatedAt = time.Now()

	_, err := r.ormer.Update(user, "Status", "UpdatedAt")
	if err != nil {
		logs.Error("更新用户状态失败: %v", err)
		return err
	}

	return nil
}

// UpdateLoginInfo 更新用户登录信息
func (r *UserRepositoryImpl) UpdateLoginInfo(ctx context.Context, id int64, loginIP string) error {
	user := &models.User{ID: id}
	if err := r.ormer.Read(user, "ID"); err != nil {
		if err == orm.ErrNoRows {
			return errors.New("用户不存在")
		}
		logs.Error("查询用户失败: %v", err)
		return err
	}

	user.LastLoginAt = time.Now()
	user.LastLoginIP = loginIP
	user.UpdatedAt = time.Now()

	_, err := r.ormer.Update(user, "LastLoginAt", "LastLoginIP", "UpdatedAt")
	if err != nil {
		logs.Error("更新用户登录信息失败: %v", err)
		return err
	}

	return nil
}

// UpdateBalance 更新用户余额
func (r *UserRepositoryImpl) UpdateBalance(ctx context.Context, id int64, amount float64) error {
	// 使用事务确保余额更新的原子性
	o := orm.NewOrm()
	tx, err := o.Begin()
	// 检查事务是否开启成功
	if err != nil {
		logs.Error("[UpdateBalance] 开启事务失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	// 查询用户
	user := &models.User{ID: id}
	if err := tx.Read(user, "ID"); err != nil {
		tx.Rollback()
		if err == orm.ErrNoRows {
			return errors.New("用户不存在")
		}
		logs.Error("查询用户失败: %v", err)
		return err
	}

	// 更新余额
	user.Balance += amount
	if user.Balance < 0 {
		tx.Rollback()
		return errors.New("余额不足")
	}

	user.UpdatedAt = time.Now()

	_, err = tx.Update(user, "Balance", "UpdatedAt")
	if err != nil {
		tx.Rollback()
		logs.Error("更新用户余额失败: %v", err)
		return err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		tx.Rollback()
		logs.Error("[UpdateBalance] 提交事务失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	return nil
}

// UpdatePoints 更新用户积分
func (r *UserRepositoryImpl) UpdatePoints(ctx context.Context, id int64, points int64) error {
	// 使用事务确保积分更新的原子性
	o := orm.NewOrm()
	tx, err := o.Begin()
	// 检查事务是否开启成功
	if err != nil {
		logs.Error("[UpdatePoints] 开启事务失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	// 查询用户
	user := &models.User{ID: id}
	if err := tx.Read(user, "ID"); err != nil {
		tx.Rollback()
		if err == orm.ErrNoRows {
			return errors.New("用户不存在")
		}
		logs.Error("[UpdatePoints] 查询用户失败: %v", err)
		return err
	}

	// 更新积分
	user.Points += points
	if user.Points < 0 {
		tx.Rollback()
		return errors.New("积分不足")
	}

	user.UpdatedAt = time.Now()

	_, err = tx.Update(user, "Points", "UpdatedAt")
	if err != nil {
		tx.Rollback()
		logs.Error("[UpdatePoints] 更新用户积分失败: %v", err)
		return err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		tx.Rollback()
		logs.Error("[UpdatePoints] 提交事务失败: %v", err)
		return fmt.Errorf("操作失败: %v", err)
	}

	return nil
}

// GetTotalCount 获取总用户数
func (r *UserRepositoryImpl) GetTotalCount(ctx context.Context) (int64, error) {
	return r.ormer.QueryTable(new(models.User)).Count()
}
