/**
 * 用户仓库扩展实现
 *
 * 该文件实现了用户仓库接口的扩展方法，特别是微信网页扫码登录相关方法。
 * 作为user_repository.go的补充，提供了更多针对微信用户的查询和更新操作。
 */

package repositories

import (
	"context"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/models"
)

// GetByWechatUnionID 根据微信UnionID获取用户
func (r *UserRepositoryImpl) GetByWechatUnionID(ctx context.Context, unionID string) (*models.User, error) {
	user := &models.User{WxUnionID: unionID}
	if err := r.ormer.Read(user, "WxUnionID"); err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 用户不存在返回nil
		}
		logs.Error("根据微信UnionID查询用户失败: %v", err)
		return nil, err
	}

	return user, nil
}

// GetByWechatOpenID 根据微信网页OpenID获取用户
// 注: 微信网页OpenID也存储在WxOpenID字段中
func (r *UserRepositoryImpl) GetByWechatOpenID(ctx context.Context, openID string) (*models.User, error) {
	// 使用WxOpenID字段查询
	return r.GetByWxOpenID(ctx, openID)
}

// Get 通用获取用户方法
func (r *UserRepositoryImpl) Get(ctx context.Context, user *models.User, fields ...string) error {
	if len(fields) == 0 {
		// 如果没有指定字段，默认使用ID
		return r.ormer.Read(user)
	}
	return r.ormer.Read(user, fields...)
}

// UpdateFields 更新用户指定字段
func (r *UserRepositoryImpl) UpdateFields(ctx context.Context, user *models.User, fields ...string) error {
	if len(fields) == 0 {
		logs.Warn("未指定要更新的字段")
		return fmt.Errorf("未指定要更新的字段")
	}

	// 确保更新时间字段存在
	hasUpdatedAt := false
	for _, field := range fields {
		if field == "UpdatedAt" {
			hasUpdatedAt = true
			break
		}
	}
	
	// 如果字段列表中没有UpdatedAt，则添加它
	if !hasUpdatedAt {
		fieldsWithTime := append(fields, "UpdatedAt")
		fields = fieldsWithTime
	}

	// 执行更新
	_, err := r.ormer.Update(user, fields...)
	if err != nil {
		logs.Error("更新用户字段失败: %v", err)
		return err
	}

	return nil
}
