/**
 * 用户仓库推荐码相关实现
 *
 * 该文件实现了用户仓库接口中与推荐码相关的功能，
 * 包括获取和更新用户推荐码的方法。
 */

package repositories

import (
	"context"
	"time"
	
	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/user/models"
)

// GetByReferralCode 根据推荐码获取用户
func (r *UserRepositoryImpl) GetByReferralCode(ctx context.Context, referralCode string) (*models.User, error) {
	user := &models.User{}
	err := r.ormer.QueryTable(user).Filter("referral_code", referralCode).Filter("status", models.UserStatusEnabled).One(user)
	if err != nil {
		if err.Error() == "<QuerySeter> no row found" {
			return nil, nil
		}
		logs.Error("根据推荐码获取用户失败: %v", err)
		return nil, err
	}
	return user, nil
}

// UpdateReferralCode 更新用户推荐码
func (r *UserRepositoryImpl) UpdateReferralCode(ctx context.Context, id int64, referralCode string) error {
	// 首先检查用户是否存在
	user := &models.User{ID: id}
	if err := r.ormer.Read(user); err != nil {
		logs.Error("获取用户失败: %v", err)
		return err
	}
	
	// 更新推荐码
	user.ReferralCode = referralCode
	user.UpdatedAt = ctx.Value("currentTime").([]time.Time)[0] // 从上下文获取当前时间，如果有的话
	
	// 如果上下文中没有当前时间，则使用当前系统时间
	if user.UpdatedAt.IsZero() {
		user.UpdatedAt = time.Now()
	}
	
	// 执行更新
	if _, err := r.ormer.Update(user, "referral_code", "updated_at"); err != nil {
		logs.Error("更新用户推荐码失败: %v", err)
		return err
	}
	
	return nil
}
