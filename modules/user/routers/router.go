/**
 * 用户模块路由注册
 *
 * 该文件实现了用户模块的路由注册，将API接口与控制器方法关联。
 * 路由定义了API的访问路径、HTTP方法以及是否需要认证。
 */

package routers

import (
	"fmt"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"time"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/user/controllers"
	"o_mall_backend/modules/user/repositories"
	"o_mall_backend/modules/user/services"
)

// InitRouters 初始化用户模块路由
func InitRouters() {
	uuid := fmt.Sprintf("%d", time.Now().UnixNano())
	logs.Info("===== [用户模块] 初始化路由开始 (实例: %s) =====", uuid)
	logs.Info("[用户模块] 开始初始化路由... (实例: %s)", uuid)
	// 初始化用户相关依赖
	userRepo := repositories.NewUserRepository()
	logs.Info("[用户模块] userRepo 创建完成: %v", userRepo != nil)

	userLogService := services.NewUserLogService()
	logs.Info("[用户模块] userLogService 创建完成: %v", userLogService != nil)

	userReferralRepo := repositories.NewUserReferralRepository()
	logs.Info("[用户模块] userReferralRepo 创建完成: %v", userReferralRepo != nil)

	userReferralConfigRepo := repositories.NewReferralConfigRepository()
	logs.Info("[用户模块] userReferralConfigRepo 创建完成: %v", userReferralConfigRepo != nil)

	referralConfigService := services.NewReferralConfigService(userReferralConfigRepo)
	logs.Info("[用户模块] referralConfigService 创建完成: %v", referralConfigService != nil)

	userService := services.NewUserService(referralConfigService)
	userServiceId := fmt.Sprintf("%p", userService)
	logs.Info("[用户模块] userService 创建完成: %v (服务实例ID: %s)", userService != nil, userServiceId)

	userController := controllers.NewUserController(userService)
	userControllerId := fmt.Sprintf("%p", userController)
	logs.Info("[用户模块] userController 创建完成: %v (控制器实例ID: %s)", userController != nil, userControllerId)

	// 初始化分销控制器
	userReferralService := services.NewUserReferralService(userRepo, userReferralRepo, userLogService, referralConfigService)
	userReferralServiceId := fmt.Sprintf("%p", userReferralService)
	logs.Info("[用户模块] userReferralService 创建完成: %v (服务实例 ID: %s)", userReferralService != nil, userReferralServiceId)

	// 创建控制器实例并初始化全局服务
	referralController := controllers.NewReferralController(userReferralService)
	referralControllerId := fmt.Sprintf("%p", referralController)
	logs.Info("[用户模块] referralController 创建完成: %v (控制器实例 ID: %s)", referralController != nil, referralControllerId)

	// 初始化地址控制器（使用依赖注入方式）
	addressController := &controllers.AddressController{}

	// 初始化上传文件控制器（使用依赖注入方式）
	uploadController := &controllers.UploadFileController{}
	
	// 初始化手机号控制器
	mobileController := controllers.NewMobileController()
	mobileControllerId := fmt.Sprintf("%p", mobileController)
	logs.Info("[用户模块] mobileController 创建完成: %v (控制器实例ID: %s)", mobileController != nil, mobileControllerId)
	
	// 初始化微信控制器
	wechatController := &controllers.WechatController{}
	wechatControllerId := fmt.Sprintf("%p", wechatController)
	logs.Info("[用户模块] wechatController 创建完成: %v (控制器实例ID: %s)", wechatController != nil, wechatControllerId)
	// 创建用户命名空间
	userNS := web.NewNamespace("/api/v1/user",
		// 添加用户日志中间件（只记录敏感操作）
		web.NSBefore(middlewares.UserLogMiddleware()),

		// 无需认证的路由
		web.NSRouter("/register", userController, "post:Register;options:Options"),
		web.NSRouter("/register/send-code", userController, "post:SendRegisterVerificationCode;options:Options"),
		web.NSRouter("/register/verify-code", userController, "post:RegisterByVerifyCode;options:Options"),
		web.NSRouter("/login", userController, "post:Login;options:Options"),
		web.NSRouter("/refresh-token", userController, "post:RefreshToken;options:Options"),
		web.NSRouter("/send-verification-code", userController, "post:SendVerificationCode;options:Options"),
		web.NSRouter("/login/verify-code", userController, "post:LoginByVerifyCode;options:Options"),
		// 微信小程序一键登录
		web.NSRouter("/wx-login", userController, "post:WxMiniLogin;options:Options"),
		
		// 微信打开扫码登录
		web.NSRouter("/wx/qrcode", wechatController, "post:QrCode;options:Options"),
		web.NSRouter("/wx/qrcode/status", wechatController, "post:QrCodeStatus;options:Options"),
		web.NSRouter("/wx/web/login", wechatController, "get:WebLogin;options:Options"),
		web.NSRouter("/wx/web/callback", wechatController, "get:WebCallback;options:Options"),

		// 需要认证的路由
		web.NSNamespace("/secured",
			// 添加JWT中间件
			web.NSBefore(middlewares.JWTFilter),

			// 用户信息相关
			web.NSRouter("/info", userController, "get:GetUserInfo;put:UpdateUserInfo;options:Options"),
			web.NSRouter("/password", userController, "put:ChangePassword;options:Options"),
			web.NSRouter("/logout", userController, "post:Logout;options:Options"),

			// 账户管理
			web.NSRouter("/account/info", userController, "get:GetAccountInfo;options:Options"),
			web.NSRouter("/account/transactions", userController, "get:GetAccountTransactions;options:Options"),

			// web.NSRouter("/avatar", &controllers.UserController{}, "post:UploadAvatar"), // 暂时注释掉，因为方法未实现

			// 收货地址相关
			web.NSRouter("/addresses", addressController, "post:AddAddress;get:ListAddresses;options:Options"),
			web.NSRouter("/addresses/:id", addressController, "get:GetAddress;put:UpdateAddress;delete:DeleteAddress;options:Options"),
			web.NSRouter("/addresses/:id/default", addressController, "put:SetDefaultAddress;options:Options"),

			// 分销关系相关
			web.NSRouter("/referral/create", &controllers.ReferralController{}, "post:CreateReferral;options:Options"),
			web.NSRouter("/referral/referrer", &controllers.ReferralController{}, "get:GetReferrer;options:Options"),
			web.NSRouter("/referral/referrals", &controllers.ReferralController{}, "get:GetReferrals;options:Options"),
			web.NSRouter("/referral/statistics", &controllers.ReferralController{}, "get:GetReferralStatistics;options:Options"),
			web.NSRouter("/referral/info", &controllers.ReferralController{}, "get:GetReferralInfo;options:Options"),

			// 添加前端需要的路由别名 - 注意这里使用的是新的控制器实例
			// 不要使用变量引用，因为beego会为每个请求创建新实例
			web.NSRouter("/referral/users", &controllers.ReferralController{}, "get:GetReferrals;options:Options"),

			// 文件上传相关
			web.NSRouter("/upload", uploadController, "post:Upload;options:Options"),
			web.NSRouter("/upload/config", uploadController, "get:GetConfig;options:Options"),
			web.NSRouter("/upload/list", uploadController, "get:List;options:Options"),
			web.NSRouter("/upload/:id", uploadController, "get:GetFile;delete:Delete;options:Options"),

			// 手机号相关
			web.NSRouter("/mobile/bind", mobileController, "post:BindMobile;options:Options"),
			web.NSRouter("/mobile/change", mobileController, "post:ChangeMobile;options:Options"),
			web.NSRouter("/mobile/send-bind-code", mobileController, "post:SendMobileBindCode;options:Options"),
			web.NSRouter("/mobile/send-change-code", mobileController, "post:SendMobileChangeCode;options:Options"),
		),
	)

	// 注册命名空间
	web.AddNamespace(userNS)

	logs.Info("===== [用户模块] 初始化路由完成 (实例: %s) =====\n", uuid)
}
