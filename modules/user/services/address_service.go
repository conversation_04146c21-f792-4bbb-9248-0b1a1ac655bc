/**
 * 地址服务实现
 *
 * 该文件实现了地址服务接口，提供收货地址管理相关的业务逻辑。
 * 地址服务负责用户收货地址的添加、修改、删除和查询等功能。
 */

package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/repositories"
)

// AddressServiceImpl 地址服务实现
type AddressServiceImpl struct {
	addressRepo repositories.AddressRepository
	userRepo    repositories.UserRepository
}

// NewAddressService 创建地址服务实例
func NewAddressService() AddressService {
	return &AddressServiceImpl{
		addressRepo: repositories.NewAddressRepository(),
		userRepo:    repositories.NewUserRepository(),
	}
}

// AddAddress 添加收货地址
// 为用户添加新的收货地址，如果是默认地址，会重置用户其他地址为非默认
func (s *AddressServiceImpl) AddAddress(ctx context.Context, userID int64, req *dto.AddAddressRequest) (int64, error) {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return 0, err
	}
	if user == nil {
		return 0, errors.New("用户不存在")
	}

	// 检查地址数量限制（一个用户最多允许20个地址）
	count, err := s.addressRepo.CountByUserID(ctx, userID)
	if err != nil {
		logs.Error("统计用户地址数量失败: %v", err)
		return 0, err
	}
	if count >= 20 {
		return 0, errors.New("收货地址数量已达上限")
	}

	// 如果是第一个地址，自动设为默认地址
	isDefault := req.IsDefault
	if count == 0 {
		isDefault = true
	}

	// 创建地址对象
	address := &models.Address{
		UserID:            userID,
		ReceiverName:      req.ReceiverName,
		ReceiverMobile:    req.ReceiverMobile,
		Province:          req.Province,
		City:              req.City,
		District:          req.District,
		DetailedAddress:   req.DetailedAddress,
		PostalCode:        req.PostalCode,
		IsDefault:         isDefault,
		AddressTag:        req.AddressTag,
		LocationLongitude: req.LocationLongitude,
		LocationLatitude:  req.LocationLatitude,
	}

	// 保存地址
	addressID, err := s.addressRepo.Create(ctx, address)
	if err != nil {
		logs.Error("创建地址失败: %v", err)
		return 0, err
	}

	return addressID, nil
}

// UpdateAddress 更新收货地址
// 更新用户的收货地址信息，如果设为默认地址，会重置用户其他地址为非默认
func (s *AddressServiceImpl) UpdateAddress(ctx context.Context, userID int64, req *dto.UpdateAddressRequest) error {
	// 检查地址是否存在且属于该用户
	address, err := s.addressRepo.GetByID(ctx, req.ID)
	if err != nil {
		logs.Error("查询地址失败: %v", err)
		return err
	}
	if address == nil || address.UserID != userID {
		return errors.New("地址不存在或不属于该用户")
	}

	// 更新地址信息
	address.ReceiverName = req.ReceiverName
	address.ReceiverMobile = req.ReceiverMobile
	address.Province = req.Province
	address.City = req.City
	address.District = req.District
	address.DetailedAddress = req.DetailedAddress
	address.PostalCode = req.PostalCode
	address.IsDefault = req.IsDefault
	address.AddressTag = req.AddressTag
	address.LocationLongitude = req.LocationLongitude
	address.LocationLatitude = req.LocationLatitude

	// 保存更新
	if err := s.addressRepo.Update(ctx, address); err != nil {
		logs.Error("更新地址失败: %v", err)
		return err
	}

	return nil
}

// DeleteAddress 删除收货地址
// 删除用户的收货地址，如果删除的是默认地址，不会自动设置新的默认地址
func (s *AddressServiceImpl) DeleteAddress(ctx context.Context, userID int64, addressID int64) error {
	// 直接调用仓库方法删除地址，仓库层会检查地址是否存在且属于该用户
	if err := s.addressRepo.Delete(ctx, addressID, userID); err != nil {
		logs.Error("删除地址失败: %v", err)
		return err
	}

	return nil
}

// GetAddressByID 获取收货地址详情
// 根据ID获取用户的收货地址详情，会检查地址是否属于该用户
func (s *AddressServiceImpl) GetAddressByID(ctx context.Context, userID int64, addressID int64) (*dto.AddressResponse, error) {
	// 查询地址
	address, err := s.addressRepo.GetByID(ctx, addressID)
	if err != nil {
		logs.Error("查询地址失败: %v", err)
		return nil, err
	}
	if address == nil || address.UserID != userID {
		return nil, errors.New("地址不存在或不属于该用户")
	}

	// 转换为响应对象
	return convertToAddressResponse(address), nil
}

// ListAddresses 获取收货地址列表
// 分页获取用户的收货地址列表
func (s *AddressServiceImpl) ListAddresses(ctx context.Context, userID int64, req *dto.AddressQueryRequest) ([]*dto.AddressResponse, int64, error) {
	// 查询地址列表
	addresses, total, err := s.addressRepo.ListByUserID(ctx, userID, req.Page, req.PageSize)
	if err != nil {
		logs.Error("查询地址列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	addressResponses := make([]*dto.AddressResponse, 0, len(addresses))
	for _, address := range addresses {
		addressResponses = append(addressResponses, convertToAddressResponse(address))
	}

	return addressResponses, total, nil
}

// SetDefaultAddress 设置默认收货地址
// 将指定地址设为默认地址，同时将用户的其他地址设为非默认
func (s *AddressServiceImpl) SetDefaultAddress(ctx context.Context, userID int64, req *dto.SetDefaultAddressRequest) error {
	// 直接调用仓库方法设置默认地址
	if err := s.addressRepo.SetDefault(ctx, req.ID, userID); err != nil {
		logs.Error("设置默认地址失败: %v", err)
		return err
	}

	return nil
}

// GetDefaultAddress 获取默认收货地址
// 获取用户的默认收货地址，如果没有默认地址返回nil
func (s *AddressServiceImpl) GetDefaultAddress(ctx context.Context, userID int64) (*dto.AddressResponse, error) {
	// 查询默认地址
	address, err := s.addressRepo.GetDefaultAddress(ctx, userID)
	if err != nil {
		logs.Error("查询默认地址失败: %v", err)
		return nil, err
	}
	if address == nil {
		return nil, nil // 没有默认地址返回nil
	}

	// 转换为响应对象
	return convertToAddressResponse(address), nil
}

// convertToAddressResponse 将地址模型转换为响应对象
// 内部辅助方法，用于转换数据格式
func convertToAddressResponse(address *models.Address) *dto.AddressResponse {
	if address == nil {
		return nil
	}

	// 生成完整地址
	fullAddress := fmt.Sprintf("%s %s %s %s", address.Province, address.City, address.District, address.DetailedAddress)

	return &dto.AddressResponse{
		ID:                address.ID,
		ReceiverName:      address.ReceiverName,
		ReceiverMobile:    address.ReceiverMobile,
		Province:          address.Province,
		City:              address.City,
		District:          address.District,
		DetailedAddress:   address.DetailedAddress,
		PostalCode:        address.PostalCode,
		IsDefault:         address.IsDefault,
		AddressTag:        address.AddressTag,
		LocationLongitude: address.LocationLongitude,
		LocationLatitude:  address.LocationLatitude,
		FullAddress:       fullAddress,
	}
}
