/**
 * 用户服务接口定义
 *
 * 该文件定义了用户模块的服务层接口，声明了用户和地址相关的业务方法。
 * 通过接口定义与实现分离，便于单元测试和功能扩展。
 */

package services

import (
	"context"

	"o_mall_backend/modules/user/dto"
)

// UserService 用户服务接口
type UserService interface {
	// 用户认证相关
	Register(ctx context.Context, req *dto.RegisterRequest, registerIP string) (int64, error)
	Login(ctx context.Context, req *dto.LoginRequest, loginIP string) (*dto.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*dto.TokenResponse, error)
	Logout(ctx context.Context, userID int64) error
	
	// 微信小程序登录相关
	WxMiniLogin(ctx context.Context, req *dto.WechatMiniLoginRequest, loginIP string) (*dto.LoginResponse, error)
	
	// 微信扫码登录相关
	GetWechatQrCode(ctx context.Context, req *dto.WechatQrCodeRequest) (*dto.WechatQrCodeResponse, error)
	CheckWechatQrCodeStatus(ctx context.Context, req *dto.WechatQrCodeStatusRequest) (*dto.WechatQrCodeStatusResponse, error)
	WxWebLogin(ctx context.Context, req *dto.WechatWebLoginRequest) (*dto.WechatWebLoginResponse, error)
	
	// 验证码登录相关
	SendVerificationCode(ctx context.Context, req *dto.SendVerificationCodeRequest) (*dto.SendVerificationCodeResponse, error)
	LoginByVerifyCode(ctx context.Context, req *dto.UserVerifyCodeLoginRequest, loginIP string) (*dto.LoginResponse, error)

	// 手机号验证码注册相关
	SendRegisterVerificationCode(ctx context.Context, req *dto.SendVerificationCodeRequest) (*dto.SendVerificationCodeResponse, error)
	CheckMobileExists(ctx context.Context, mobile string) (bool, error)
	RegisterByVerifyCode(ctx context.Context, req *dto.RegisterByVerifyCodeRequest, registerIP string) (int64, error)

	// 用户信息相关
	GetUserByID(ctx context.Context, id int64) (*dto.UserResponse, error)
	UpdateUserInfo(ctx context.Context, id int64, req *dto.UpdateUserRequest) error
	ChangePassword(ctx context.Context, id int64, req *dto.ChangePasswordRequest) error
	// 手机号绑定相关
	BindMobile(ctx context.Context, id int64, req *dto.BindMobileRequest) (*dto.BindMobileResponse, error)
	ChangeMobile(ctx context.Context, id int64, req *dto.ChangeMobileRequest) (*dto.ChangeMobileResponse, error)

	// 用户管理相关
	ListUsers(ctx context.Context, page, pageSize int) ([]*dto.UserResponse, int64, error)
	DisableUser(ctx context.Context, id int64) error
	EnableUser(ctx context.Context, id int64) error

	// 账户相关
	UpdateBalance(ctx context.Context, id int64, amount float64, description string) error
	UpdatePoints(ctx context.Context, id int64, points int64, description string) error
	
	// 推荐码相关
	GenerateReferralCode(ctx context.Context, userID int64) (string, error)
	GetUserByReferralCode(ctx context.Context, referralCode string) (*dto.UserResponse, error)
	
	// 账户变动相关
	GetAccountTransactions(ctx context.Context, userID int64, req *dto.AccountTransactionRequest) (*dto.AccountTransactionListResponse, error)
	GetAccountInfo(ctx context.Context, userID int64) (*dto.UserAccountResponse, error)
}

// AddressService 地址服务接口
type AddressService interface {
	// 地址相关
	AddAddress(ctx context.Context, userID int64, req *dto.AddAddressRequest) (int64, error)
	UpdateAddress(ctx context.Context, userID int64, req *dto.UpdateAddressRequest) error
	DeleteAddress(ctx context.Context, userID int64, addressID int64) error
	GetAddressByID(ctx context.Context, userID int64, addressID int64) (*dto.AddressResponse, error)
	ListAddresses(ctx context.Context, userID int64, req *dto.AddressQueryRequest) ([]*dto.AddressResponse, int64, error)
	SetDefaultAddress(ctx context.Context, userID int64, req *dto.SetDefaultAddressRequest) error
	GetDefaultAddress(ctx context.Context, userID int64) (*dto.AddressResponse, error)
}

// UserLogService 用户日志服务接口
type UserLogService interface {
	// CreateLog 创建用户日志
	CreateLog(ctx context.Context, req *dto.UserLogCreateRequest) (int64, error)
	
	// GetLogByID 获取日志详情
	GetLogByID(ctx context.Context, id int64) (*dto.UserLogResponse, error)
	
	// ListLogs 获取日志列表
	ListLogs(ctx context.Context, req *dto.UserLogQueryRequest) ([]*dto.UserLogResponse, int64, error)
	
	// GetLatestLogsByUserID 获取用户最近的日志
	GetLatestLogsByUserID(ctx context.Context, userID int64, limit int) ([]*dto.UserLogResponse, error)
}

// UserReferralService 用户分销服务接口
type UserReferralService interface {
	// 创建分销关系
	CreateReferral(ctx context.Context, req *dto.ReferralCreateRequest) (int64, error)
	// 获取用户的推荐人
	GetReferrer(ctx context.Context, userID int64) (*dto.ReferralResponse, error)
	// 获取推荐用户列表
	GetReferrals(ctx context.Context, req *dto.ReferralQueryRequest) ([]*dto.ReferralResponse, int64, error)
	// 获取分销统计信息
	GetReferralStatistics(ctx context.Context, userID int64) (*dto.ReferralStatisticsResponse, error)
	// 更新分销佣金
	UpdateReferralCommission(ctx context.Context, userID int64, amount float64, description string) error
	
	// 管理员相关方法
	// 获取所有分销关系（管理员）
	GetAllReferrals(ctx context.Context, req *dto.AdminReferralQueryRequest) ([]*dto.ReferralResponse, int64, error)
	// 删除分销关系（管理员）
	DeleteReferral(ctx context.Context, id int64) error
	// 获取管理员分销统计信息
	GetAdminReferralStatistics(ctx context.Context) (*dto.AdminReferralStatisticsResponse, error)
	// 获取TOP推荐人数据（管理员）
	GetTopReferrers(ctx context.Context, req *dto.TopReferrerRequest) ([]*dto.TopReferrerResponse, error)
	// 获取佣金汇总信息（管理员）
	GetCommissionSummary(ctx context.Context) (*dto.CommissionSummaryResponse, error)
	// 获取佣金统计信息（管理员）
	GetCommissionStatistics(ctx context.Context, req *dto.CommissionStatisticsRequest) (*dto.CommissionStatisticsResponse, error)
}

// ReferralConfigService 分销配置服务接口
type ReferralConfigService interface {
	// 获取分销级别配置
	GetLevelConfig(ctx context.Context) (*dto.ReferralLevelConfigResponse, error)
	// 更新分销级别配置
	UpdateLevelConfig(ctx context.Context, req *dto.ReferralLevelConfigRequest) error
	// 获取佣金比例配置
	GetCommissionRates(ctx context.Context) (*dto.CommissionRateConfig, error)
	// 更新佣金比例配置
	UpdateCommissionRates(ctx context.Context, rates *dto.CommissionRateConfig) error
	// 获取配置列表
	GetConfigs(ctx context.Context, req *dto.ReferralConfigQueryRequest) ([]*dto.ReferralConfigResponse, int64, error)
	// 创建配置
	CreateConfig(ctx context.Context, req *dto.ReferralConfigCreateRequest) (int64, error)
	// 更新配置
	UpdateConfig(ctx context.Context, id int64, req *dto.ReferralConfigUpdateRequest) error
	// 删除配置
	DeleteConfig(ctx context.Context, id int64) error
	// 获取配置详情
	GetConfigByID(ctx context.Context, id int64) (*dto.ReferralConfigResponse, error)
	// 获取配置值
	GetConfigValue(ctx context.Context, configKey string) (string, error)
	// 检查分销级别是否启用
	IsLevelEnabled(ctx context.Context, level int) (bool, error)
	// 获取当前启用的最大分销级别
	GetEnabledMaxLevel(ctx context.Context) (int, error)
	// 初始化分销配置
	InitializeConfigs(ctx context.Context) error
}
