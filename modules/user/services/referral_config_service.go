/**
 * 分销配置服务实现
 *
 * 该文件实现了分销配置服务的业务逻辑，包括分销级别配置、佣金比例配置等功能。
 * 提供动态配置分销规则的能力，支持实时调整分销策略。
 */

package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/repositories"
)

// referralConfigService 分销配置服务实现
type referralConfigService struct {
	configRepo repositories.ReferralConfigRepository
}

// NewReferralConfigService 创建分销配置服务实例
func NewReferralConfigService(
	configRepo repositories.ReferralConfigRepository,
) ReferralConfigService {
	return &referralConfigService{
		configRepo: configRepo,
	}
}

// GetLevelConfig 获取分销级别配置
func (s *referralConfigService) GetLevelConfig(ctx context.Context) (*dto.ReferralLevelConfigResponse, error) {
	// 获取最大级别配置
	maxLevelsConfig, err := s.configRepo.GetByKey(ctx, models.ReferralConfigKeyMaxLevels)
	if err != nil {
		return nil, fmt.Errorf("获取最大分销级别配置失败: %w", err)
	}

	// 获取启用级别配置
	enabledLevelsConfig, err := s.configRepo.GetByKey(ctx, models.ReferralConfigKeyEnabledLevels)
	if err != nil {
		return nil, fmt.Errorf("获取启用分销级别配置失败: %w", err)
	}

	// 获取佣金比例配置
	commissionRatesConfig, err := s.configRepo.GetByKey(ctx, models.ReferralConfigKeyCommissionRates)
	if err != nil {
		return nil, fmt.Errorf("获取佣金比例配置失败: %w", err)
	}

	// 解析配置值
	maxLevels := 3 // 默认值
	enabledLevels := 1 // 默认值
	var rates dto.CommissionRateConfig

	if maxLevelsConfig != nil {
		if val, err := strconv.Atoi(maxLevelsConfig.ConfigValue); err == nil {
			maxLevels = val
		}
	}

	if enabledLevelsConfig != nil {
		if val, err := strconv.Atoi(enabledLevelsConfig.ConfigValue); err == nil {
			enabledLevels = val
		}
	}

	if commissionRatesConfig != nil {
		if err := json.Unmarshal([]byte(commissionRatesConfig.ConfigValue), &rates); err != nil {
			// 使用默认佣金比例
			rates = dto.CommissionRateConfig{
				Level1: 0.05,
				Level2: 0.03,
				Level3: 0.01,
			}
		}
	} else {
		// 使用默认佣金比例
		rates = dto.CommissionRateConfig{
			Level1: 0.05,
			Level2: 0.03,
			Level3: 0.01,
		}
	}

	updatedAt := time.Now()
	if commissionRatesConfig != nil {
		updatedAt = commissionRatesConfig.UpdatedAt
	}

	return &dto.ReferralLevelConfigResponse{
		MaxLevels:     maxLevels,
		EnabledLevels: enabledLevels,
		Level1Rate:    rates.Level1,
		Level2Rate:    rates.Level2,
		Level3Rate:    rates.Level3,
		UpdatedAt:     updatedAt,
	}, nil
}

// UpdateLevelConfig 更新分销级别配置
func (s *referralConfigService) UpdateLevelConfig(ctx context.Context, req *dto.ReferralLevelConfigRequest) error {
	// 验证参数
	if req.EnabledLevels > req.MaxLevels {
		return errors.New("启用级别不能大于最大级别")
	}

	// 更新最大级别配置
	if err := s.updateOrCreateConfig(ctx, models.ReferralConfigKeyMaxLevels, strconv.Itoa(req.MaxLevels), models.ReferralConfigTypeLevelConfig, "最大分销等级"); err != nil {
		return fmt.Errorf("更新最大分销级别失败: %w", err)
	}

	// 更新启用级别配置
	if err := s.updateOrCreateConfig(ctx, models.ReferralConfigKeyEnabledLevels, strconv.Itoa(req.EnabledLevels), models.ReferralConfigTypeLevelConfig, "当前启用的分销等级"); err != nil {
		return fmt.Errorf("更新启用分销级别失败: %w", err)
	}

	// 更新佣金比例配置
	rates := dto.CommissionRateConfig{
		Level1: req.Level1Rate,
		Level2: req.Level2Rate,
		Level3: req.Level3Rate,
	}
	ratesJSON, _ := json.Marshal(rates)
	if err := s.updateOrCreateConfig(ctx, models.ReferralConfigKeyCommissionRates, string(ratesJSON), models.ReferralConfigTypeCommissionRate, "各级分销佣金比例"); err != nil {
		return fmt.Errorf("更新佣金比例配置失败: %w", err)
	}

	return nil
}

// GetCommissionRates 获取佣金比例配置
func (s *referralConfigService) GetCommissionRates(ctx context.Context) (*dto.CommissionRateConfig, error) {
	config, err := s.configRepo.GetByKey(ctx, models.ReferralConfigKeyCommissionRates)
	if err != nil {
		return nil, fmt.Errorf("获取佣金比例配置失败: %w", err)
	}

	if config == nil {
		// 返回默认配置
		return &dto.CommissionRateConfig{
			Level1: 0.05,
			Level2: 0.03,
			Level3: 0.01,
		}, nil
	}

	var rates dto.CommissionRateConfig
	if err := json.Unmarshal([]byte(config.ConfigValue), &rates); err != nil {
		return nil, fmt.Errorf("解析佣金比例配置失败: %w", err)
	}

	return &rates, nil
}

// UpdateCommissionRates 更新佣金比例配置
func (s *referralConfigService) UpdateCommissionRates(ctx context.Context, rates *dto.CommissionRateConfig) error {
	// 验证佣金比例
	if rates.Level1 < 0 || rates.Level1 > 1 {
		return fmt.Errorf("一级分销佣金比例必须在0-1之间")
	}
	if rates.Level2 < 0 || rates.Level2 > 1 {
		return fmt.Errorf("二级分销佣金比例必须在0-1之间")
	}
	if rates.Level3 < 0 || rates.Level3 > 1 {
		return fmt.Errorf("三级分销佣金比例必须在0-1之间")
	}

	// 序列化配置
	configValue, err := json.Marshal(rates)
	if err != nil {
		return fmt.Errorf("序列化佣金比例配置失败: %w", err)
	}

	// 更新配置
	return s.updateOrCreateConfig(ctx, models.ReferralConfigKeyCommissionRates, string(configValue), models.ReferralConfigTypeCommissionRate, "分销佣金比例配置")
}

// GetConfigs 获取配置列表
func (s *referralConfigService) GetConfigs(ctx context.Context, req *dto.ReferralConfigQueryRequest) ([]*dto.ReferralConfigResponse, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	configs, total, err := s.configRepo.List(ctx, req.ConfigType, req.Status, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	var responses []*dto.ReferralConfigResponse
	for _, config := range configs {
		responses = append(responses, s.convertToConfigResponse(config))
	}

	return responses, total, nil
}

// CreateConfig 创建配置
func (s *referralConfigService) CreateConfig(ctx context.Context, req *dto.ReferralConfigCreateRequest) (int64, error) {
	// 检查配置键是否已存在
	existingConfig, err := s.configRepo.GetByKey(ctx, req.ConfigKey)
	if err != nil {
		return 0, err
	}
	if existingConfig != nil {
		return 0, errors.New("配置键已存在")
	}

	config := &models.ReferralConfig{
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		ConfigType:  req.ConfigType,
		Description: req.Description,
		Status:      models.ReferralConfigStatusEnabled,
	}

	return s.configRepo.Create(ctx, config)
}

// UpdateConfig 更新配置
func (s *referralConfigService) UpdateConfig(ctx context.Context, id int64, req *dto.ReferralConfigUpdateRequest) error {
	config, err := s.configRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if config == nil {
		return errors.New("配置不存在")
	}

	config.ConfigValue = req.ConfigValue
	config.Description = req.Description
	if req.Status != nil {
		config.Status = *req.Status
	}

	return s.configRepo.Update(ctx, config)
}

// DeleteConfig 删除配置
func (s *referralConfigService) DeleteConfig(ctx context.Context, id int64) error {
	return s.configRepo.Delete(ctx, id)
}

// GetConfigByID 获取配置详情
func (s *referralConfigService) GetConfigByID(ctx context.Context, id int64) (*dto.ReferralConfigResponse, error) {
	config, err := s.configRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if config == nil {
		return nil, errors.New("配置不存在")
	}

	return s.convertToConfigResponse(config), nil
}

// GetConfigValue 获取配置值
func (s *referralConfigService) GetConfigValue(ctx context.Context, configKey string) (string, error) {
	config, err := s.configRepo.GetByKey(ctx, configKey)
	if err != nil {
		return "", err
	}
	if config == nil {
		return "", errors.New("配置不存在")
	}

	return config.ConfigValue, nil
}

// IsLevelEnabled 检查分销级别是否启用
func (s *referralConfigService) IsLevelEnabled(ctx context.Context, level int) (bool, error) {
	enabledLevel, err := s.GetEnabledMaxLevel(ctx)
	if err != nil {
		return false, err
	}

	return level <= enabledLevel, nil
}

// GetEnabledMaxLevel 获取当前启用的最大分销级别
func (s *referralConfigService) GetEnabledMaxLevel(ctx context.Context) (int, error) {
	maxLevelsConfig, err := s.configRepo.GetByKey(ctx, models.ReferralConfigKeyMaxLevels)
	if err != nil {
		return 0, fmt.Errorf("获取最大分销级别配置失败: %w", err)
	}

	maxLevels, err := strconv.Atoi(maxLevelsConfig.ConfigValue)
	if err != nil {
		return 0, fmt.Errorf("解析最大分销级别失败: %w", err)
	}

	return maxLevels, nil
}

// InitializeConfigs 初始化分销配置
func (s *referralConfigService) InitializeConfigs(ctx context.Context) error {
	// 定义初始配置数据
	initialConfigs := []models.ReferralConfig{
		{
			ConfigKey:   "max_levels",
			ConfigValue: "1",
			ConfigType:  "int",
			Description: "最大分销等级，当前仅开启一级分销",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "level_1_enabled",
			ConfigValue: "true",
			ConfigType:  "string",
			Description: "一级分销是否启用",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "level_2_enabled",
			ConfigValue: "false",
			ConfigType:  "string",
			Description: "二级分销是否启用",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "level_3_enabled",
			ConfigValue: "false",
			ConfigType:  "string",
			Description: "三级分销是否启用",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "commission_rates",
			ConfigValue: `{"level_1": 0.05, "level_2": 0.03, "level_3": 0.02}`,
			ConfigType:  "json",
			Description: "各级分销佣金比例配置",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "referral_code_length",
			ConfigValue: "8",
			ConfigType:  "int",
			Description: "推荐码长度",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "referral_code_charset",
			ConfigValue: "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
			ConfigType:  "string",
			Description: "推荐码字符集",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "auto_upgrade_enabled",
			ConfigValue: "false",
			ConfigType:  "string",
			Description: "是否启用自动升级分销等级",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "min_referrals_for_upgrade",
			ConfigValue: "10",
			ConfigType:  "int",
			Description: "升级到下一级所需的最少推荐人数",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ConfigKey:   "commission_settlement_period",
			ConfigValue: "30",
			ConfigType:  "int",
			Description: "佣金结算周期（天）",
			Status:      1,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// 批量创建配置
	for _, config := range initialConfigs {
		// 检查配置是否已存在
		existingConfig, err := s.configRepo.GetByKey(ctx, config.ConfigKey)
		if err == nil && existingConfig != nil {
			// 配置已存在，跳过
			continue
		}

		// 创建新配置
		_, err = s.configRepo.Create(ctx, &config)
		if err != nil {
			return fmt.Errorf("创建配置 %s 失败: %w", config.ConfigKey, err)
		}
	}

	return nil
}

// updateOrCreateConfig 更新或创建配置
func (s *referralConfigService) updateOrCreateConfig(ctx context.Context, configKey, configValue, configType, description string) error {
	config, err := s.configRepo.GetByKey(ctx, configKey)
	if err != nil {
		return err
	}

	if config == nil {
		// 创建新配置
		newConfig := &models.ReferralConfig{
			ConfigKey:   configKey,
			ConfigValue: configValue,
			ConfigType:  configType,
			Description: description,
			Status:      models.ReferralConfigStatusEnabled,
		}
		_, err = s.configRepo.Create(ctx, newConfig)
		return err
	} else {
		// 更新现有配置
		config.ConfigValue = configValue
		config.Description = description
		return s.configRepo.Update(ctx, config)
	}
}

// convertToConfigResponse 转换为配置响应对象
func (s *referralConfigService) convertToConfigResponse(config *models.ReferralConfig) *dto.ReferralConfigResponse {
	return &dto.ReferralConfigResponse{
		ID:          config.ID,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		ConfigType:  config.ConfigType,
		Description: config.Description,
		Status:      config.Status,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}
}