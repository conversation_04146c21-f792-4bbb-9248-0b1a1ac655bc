/**
 * 用户日志服务实现
 *
 * 该文件实现了用户日志服务接口，提供日志记录和查询功能。
 * 用于记录用户的敏感操作，如密码修改、登录登出等，便于安全审计。
 */

package services

import (
	"context"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/repositories"
)

// UserLogServiceImpl 用户日志服务实现
type UserLogServiceImpl struct {
	userLogRepo repositories.UserLogRepository
}

// NewUserLogService 创建用户日志服务实例
func NewUserLogService() UserLogService {
	return &UserLogServiceImpl{
		userLogRepo: repositories.NewUserLogRepository(),
	}
}

// CreateLog 创建用户日志
func (s *UserLogServiceImpl) CreateLog(ctx context.Context, req *dto.UserLogCreateRequest) (int64, error) {
	// 创建日志对象
	userLog := &models.UserLog{
		UserID:       req.UserID,
		Username:     req.Username,
		OperationType: req.OperationType,
		Content:      req.Content,
		RequestURL:   req.RequestURL,
		RequestData:  req.RequestData,
		IP:           req.IP,
		UserAgent:    req.UserAgent,
		Status:       req.Status,
		Remark:       req.Remark,
		CreatedAt:    time.Now(),
	}

	// 调用仓库创建日志
	id, err := s.userLogRepo.Create(ctx, userLog)
	if err != nil {
		logs.Error("创建用户日志失败: %v", err)
		return 0, fmt.Errorf("创建日志失败: %v", err)
	}

	return id, nil
}

// GetLogByID 获取日志详情
func (s *UserLogServiceImpl) GetLogByID(ctx context.Context, id int64) (*dto.UserLogResponse, error) {
	// 调用仓库获取日志
	userLog, err := s.userLogRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("获取用户日志失败: %v", err)
		return nil, fmt.Errorf("获取日志失败: %v", err)
	}

	if userLog == nil {
		return nil, fmt.Errorf("日志不存在")
	}

	// 转换为响应对象
	return convertToUserLogResponse(userLog), nil
}

// ListLogs 获取日志列表
func (s *UserLogServiceImpl) ListLogs(ctx context.Context, req *dto.UserLogQueryRequest) ([]*dto.UserLogResponse, int64, error) {
	// 构建查询条件
	query := make(map[string]interface{})
	if req.UserID > 0 {
		query["user_id"] = req.UserID
	}
	if req.Username != "" {
		query["username"] = req.Username
	}
	if req.OperationType > 0 {
		query["operation_type"] = req.OperationType
	}
	if req.IP != "" {
		query["ip"] = req.IP
	}
	if req.Status > -1 { // -1表示全部
		query["status"] = req.Status
	}

	// 时间范围查询
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		query["created_at__gte"] = req.StartTime
		query["created_at__lte"] = req.EndTime
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询数据
	logs, total, err := s.userLogRepo.List(ctx, query, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日志列表失败: %v", err)
	}

	// 转换为响应对象
	var responses []*dto.UserLogResponse
	for _, userLog := range logs {
		responses = append(responses, convertToUserLogResponse(userLog))
	}

	return responses, total, nil
}

// GetLatestLogsByUserID 获取用户最近的日志
func (s *UserLogServiceImpl) GetLatestLogsByUserID(ctx context.Context, userID int64, limit int) ([]*dto.UserLogResponse, error) {
	if limit <= 0 {
		limit = 10
	}

	// 调用仓库查询
	logs, err := s.userLogRepo.GetLatestByUserID(ctx, userID, limit)
	if err != nil {
		return nil, fmt.Errorf("获取最近日志失败: %v", err)
	}

	// 转换为响应对象
	var responses []*dto.UserLogResponse
	for _, userLog := range logs {
		responses = append(responses, convertToUserLogResponse(userLog))
	}

	return responses, nil
}

// convertToUserLogResponse 将日志模型转换为响应对象
func convertToUserLogResponse(log *models.UserLog) *dto.UserLogResponse {
	if log == nil {
		return nil
	}

	// 获取操作类型名称
	operationName := getOperationTypeName(log.OperationType)

	// 状态描述
	statusText := "成功"
	if log.Status == 0 {
		statusText = "失败"
	}

	// 返回响应对象
	return &dto.UserLogResponse{
		ID:            log.ID,
		UserID:        log.UserID,
		Username:      log.Username,
		OperationType: log.OperationType,
		OperationName: operationName,
		Content:       log.Content,
		RequestURL:    log.RequestURL,
		RequestData:   log.RequestData,
		IP:            log.IP,
		UserAgent:     log.UserAgent,
		Status:        log.Status,
		StatusText:    statusText,
		Remark:        log.Remark,
		CreatedAt:     log.CreatedAt,
	}
}

// getOperationTypeName 获取操作类型名称
func getOperationTypeName(operationType int) string {
	if name, ok := models.UserLogTypeNames[operationType]; ok {
		return name
	}
	return "未知操作"
}
