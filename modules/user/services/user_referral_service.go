/**
 * 用户分销服务实现
 *
 * 该文件实现了用户分销服务的业务逻辑，包括创建分销关系、查询分销关系、统计分销数据等功能。
 * 遵循接口与实现分离的设计原则，便于单元测试和功能扩展。
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/repositories"
)

// userReferralService 用户分销服务实现
type userReferralService struct {
	userRepo          repositories.UserRepository
	userReferralRepo  repositories.UserReferralRepository
	userLogService    UserLogService
	referralConfigService ReferralConfigService
}

// NewUserReferralService 创建用户分销服务实例
func NewUserReferralService(
	userRepo repositories.UserRepository,
	userReferralRepo repositories.UserReferralRepository,
	userLogService UserLogService,
	referralConfigService ReferralConfigService,
) UserReferralService {
	return &userReferralService{
		userRepo:          userRepo,
		userReferralRepo:  userReferralRepo,
		userLogService:    userLogService,
		referralConfigService: referralConfigService,
	}
}

// CreateReferral 创建分销关系
func (s *userReferralService) CreateReferral(ctx context.Context, req *dto.ReferralCreateRequest) (int64, error) {
	// 验证用户是否存在
	user, err := s.userRepo.GetByID(ctx, req.UserID)
	if err != nil {
		return 0, fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user == nil {
		return 0, errors.New("用户不存在")
	}

	// 验证推荐人是否存在
	referrer, err := s.userRepo.GetByID(ctx, req.ReferrerID)
	if err != nil {
		return 0, fmt.Errorf("获取推荐人信息失败: %w", err)
	}
	if referrer == nil {
		return 0, errors.New("推荐人不存在")
	}

	// 不能自己推荐自己
	if req.UserID == req.ReferrerID {
		return 0, errors.New("不能自己推荐自己")
	}

	// 检查用户是否已有推荐关系
	exists, err := s.userReferralRepo.CheckReferralExists(ctx, req.UserID)
	if err != nil {
		return 0, fmt.Errorf("检查用户推荐关系失败: %w", err)
	}
	if exists {
		return 0, errors.New("用户已有推荐关系")
	}

	// 设置默认级别
	level := req.Level
	if level == 0 {
		level = models.ReferralLevelOne
	}

	// 验证分销级别是否启用
	isEnabled, err := s.referralConfigService.IsLevelEnabled(ctx, level)
	if err != nil {
		return 0, fmt.Errorf("检查分销级别配置失败: %w", err)
	}
	if !isEnabled {
		return 0, fmt.Errorf("分销级别 %d 未启用", level)
	}

	// 获取当前启用的最大分销级别
	maxLevel, err := s.referralConfigService.GetEnabledMaxLevel(ctx)
	if err != nil {
		return 0, fmt.Errorf("获取最大分销级别失败: %w", err)
	}
	if level > maxLevel {
		return 0, fmt.Errorf("分销级别 %d 超过最大允许级别 %d", level, maxLevel)
	}

	// 创建分销关系
	referral := &models.UserReferral{
		UserID:       req.UserID,
		ReferrerID:   req.ReferrerID,
		Level:        level,
		Status:       models.ReferralStatusValid,
		Commission:   0,
		ReferralTime: time.Now(),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	id, err := s.userReferralRepo.Create(ctx, referral)
	if err != nil {
		return 0, fmt.Errorf("创建分销关系失败: %w", err)
	}

	// 记录操作日志
	logReq := &dto.UserLogCreateRequest{
		UserID:        req.UserID,
		Username:      user.Username,
		OperationType: 1,
		Content:       "创建分销关系",
		IP:            "",
		RequestData:   fmt.Sprintf(`{"referrer_id":%d,"level":%d}`, req.ReferrerID, level),
		Status:        1,
		Remark:        "成功建立推荐关系",
	}
	_, err = s.userLogService.CreateLog(ctx, logReq)
	if err != nil {
		// 日志记录失败不影响主流程
		log.Printf("记录分销创建日志失败: %v", err)
	}

	return id, nil
}

// GetReferrer 获取用户的推荐人
func (s *userReferralService) GetReferrer(ctx context.Context, userID int64) (*dto.ReferralResponse, error) {
	referral, err := s.userReferralRepo.GetReferrerByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	
	if referral == nil {
		return nil, nil
	}
	
	response := s.convertToReferralResponse(ctx, referral)
	
	// 获取推荐人信息
	referrer, err := s.userRepo.GetByID(ctx, referral.ReferrerID)
	if err == nil && referrer != nil {
		response.Referrer = &dto.UserBriefResponse{
			ID:       referrer.ID,
			Username: referrer.Username,
			Nickname: referrer.Nickname,
			Avatar:   referrer.Avatar,
			Mobile:   referrer.Mobile,
			Level:    referrer.Level,
		}
	}
	
	return response, nil
}

// GetReferrals 获取用户推荐的用户列表
func (s *userReferralService) GetReferrals(ctx context.Context, req *dto.ReferralQueryRequest) ([]*dto.ReferralResponse, int64, error) {
	var referrals []*models.UserReferral
	var total int64
	var err error
	
	if req.OnlyDirect {
		// 只查询直接推荐的用户
		referrals, total, err = s.userReferralRepo.GetDirectReferrals(ctx, req.UserID, req.Page, req.PageSize)
	} else {
		// 查询所有层级的推荐用户
		referrals, total, err = s.userReferralRepo.GetAllReferrals(ctx, req.UserID, req.Level, req.Page, req.PageSize)
	}
	
	if err != nil {
		return nil, 0, err
	}
	
	responses := make([]*dto.ReferralResponse, 0, len(referrals))
	for _, referral := range referrals {
		response := s.convertToReferralResponse(ctx, referral)
		
		// 获取被推荐用户信息
		user, err := s.userRepo.GetByID(ctx, referral.UserID)
		if err == nil && user != nil {
			response.User = &dto.UserBriefResponse{
				ID:       user.ID,
				Username: user.Username,
				Nickname: user.Nickname,
				Avatar:   user.Avatar,
				Mobile:   user.Mobile,
				Level:    user.Level,
			}
		}
		
		responses = append(responses, response)
	}
	
	return responses, total, nil
}

// GetReferralStatistics 获取用户分销统计信息
func (s *userReferralService) GetReferralStatistics(ctx context.Context, userID int64) (*dto.ReferralStatisticsResponse, error) {
	// 获取直接推荐的用户数量
	level1Referrals, _, err := s.userReferralRepo.GetDirectReferrals(ctx, userID, 0, 0)
	if err != nil {
		return nil, err
	}
	
	// 获取二级推荐的用户数量
	level2Referrals, _, err := s.userReferralRepo.GetAllReferrals(ctx, userID, models.ReferralLevelTwo, 0, 0)
	if err != nil {
		return nil, err
	}
	
	// 获取三级推荐的用户数量
	level3Referrals, _, err := s.userReferralRepo.GetAllReferrals(ctx, userID, models.ReferralLevelThree, 0, 0)
	if err != nil {
		return nil, err
	}
	
	// 计算总佣金
	var totalCommission float64
	for _, referral := range level1Referrals {
		totalCommission += referral.Commission
	}
	
	// 获取今日佣金
	todayStart := time.Now().Truncate(24 * time.Hour)
	var todayCommission float64
	for _, referral := range level1Referrals {
		if referral.UpdatedAt.After(todayStart) {
			todayCommission += referral.Commission
		}
	}
	
	// 获取本月佣金
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var thisMonthCommission float64
	for _, referral := range level1Referrals {
		if referral.UpdatedAt.After(monthStart) {
			thisMonthCommission += referral.Commission
		}
	}
	
	// 获取本年佣金
	yearStart := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	var thisYearCommission float64
	for _, referral := range level1Referrals {
		if referral.UpdatedAt.After(yearStart) {
			thisYearCommission += referral.Commission
		}
	}
	
	// 构建响应
	stats := &dto.ReferralStatisticsResponse{
		TotalReferrals:      int64(len(level1Referrals) + len(level2Referrals) + len(level3Referrals)),
		Level1Referrals:     int64(len(level1Referrals)),
		Level2Referrals:     int64(len(level2Referrals)),
		Level3Referrals:     int64(len(level3Referrals)),
		TotalCommission:     totalCommission,
		TodayCommission:     todayCommission,
		ThisMonthCommission: thisMonthCommission,
		ThisYearCommission:  thisYearCommission,
	}
	
	return stats, nil
}

// UpdateReferralCommission 更新分销佣金
func (s *userReferralService) UpdateReferralCommission(ctx context.Context, userID int64, amount float64, description string) error {
	// 获取用户的推荐关系
	referral, err := s.userReferralRepo.GetReferrerByUserID(ctx, userID)
	if err != nil {
		return err
	}
	
	if referral == nil {
		return errors.New("用户没有推荐关系")
	}
	
	// 更新佣金
	err = s.userReferralRepo.UpdateCommission(ctx, referral.ID, amount)
	if err != nil {
		return err
	}
	
	// 记录用户日志
	logReq := &dto.UserLogCreateRequest{
		UserID:        referral.ReferrerID,
		Username:      "",
		OperationType: 2, // 佣金变更操作类型
		Content:       description,
		IP:            "",
		RequestData:   fmt.Sprintf(`{"user_id":%d,"amount":%.2f}`, userID, amount),
		Status:        1,
		Remark:        "佣金更新成功",
	}
	s.userLogService.CreateLog(ctx, logReq)
	
	return nil
}

// GetAllReferrals 获取所有分销关系（管理员）
func (s *userReferralService) GetAllReferrals(ctx context.Context, req *dto.AdminReferralQueryRequest) ([]*dto.ReferralResponse, int64, error) {
	// 调用仓库层获取分销关系列表
	referrals, total, err := s.userReferralRepo.GetAllReferralsForAdmin(ctx, req)
	if err != nil {
		return nil, 0, fmt.Errorf("获取分销关系列表失败: %w", err)
	}

	// 转换为响应DTO
	responses := make([]*dto.ReferralResponse, 0, len(referrals))
	for _, referral := range referrals {
		response := s.convertToReferralResponse(ctx, referral)

		// 获取用户信息
		user, err := s.userRepo.GetByID(ctx, referral.UserID)
		if err == nil && user != nil {
			response.User = &dto.UserBriefResponse{
				ID:       user.ID,
				Username: user.Username,
				Nickname: user.Nickname,
				Avatar:   user.Avatar,
				Mobile:   user.Mobile,
				Level:    user.Level,
			}
		}

		// 获取推荐人信息
		referrer, err := s.userRepo.GetByID(ctx, referral.ReferrerID)
		if err == nil && referrer != nil {
			response.Referrer = &dto.UserBriefResponse{
				ID:       referrer.ID,
				Username: referrer.Username,
				Nickname: referrer.Nickname,
				Avatar:   referrer.Avatar,
				Mobile:   referrer.Mobile,
				Level:    referrer.Level,
			}
		}

		responses = append(responses, response)
	}

	return responses, total, nil
}

// DeleteReferral 删除分销关系（管理员）
func (s *userReferralService) DeleteReferral(ctx context.Context, id int64) error {
	// 检查分销关系是否存在
	referral, err := s.userReferralRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取分销关系失败: %w", err)
	}
	if referral == nil {
		return errors.New("分销关系不存在")
	}

	// 删除分销关系
	err = s.userReferralRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("删除分销关系失败: %w", err)
	}

	// 记录操作日志
	logReq := &dto.UserLogCreateRequest{
		UserID:        referral.UserID,
		Username:      "",
		OperationType: 3, // 删除操作类型
		Content:       "管理员删除分销关系",
		IP:            "",
		RequestData:   fmt.Sprintf(`{"referral_id":%d}`, id),
		Status:        1,
		Remark:        "管理员删除分销关系成功",
	}
	_, err = s.userLogService.CreateLog(ctx, logReq)
	if err != nil {
		// 日志记录失败不影响主流程
		log.Printf("记录分销删除日志失败: %v", err)
	}

	return nil
}

// GetAdminReferralStatistics 获取管理员分销统计信息
func (s *userReferralService) GetAdminReferralStatistics(ctx context.Context) (*dto.AdminReferralStatisticsResponse, error) {
	// 获取系统总用户数
	totalUsers, err := s.userRepo.GetTotalCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取总用户数失败: %w", err)
	}

	// 获取总分销关系数
	totalReferrals, err := s.userReferralRepo.GetTotalCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取总分销关系数失败: %w", err)
	}

	// 获取有效分销关系数
	activeReferrals, err := s.userReferralRepo.GetCountByStatus(ctx, models.ReferralStatusValid)
	if err != nil {
		return nil, fmt.Errorf("获取有效分销关系数失败: %w", err)
	}

	// 获取无效分销关系数
	inactiveReferrals := totalReferrals - activeReferrals

	// 获取各级别分销关系数
	level1Referrals, err := s.userReferralRepo.GetCountByLevel(ctx, models.ReferralLevelOne)
	if err != nil {
		return nil, fmt.Errorf("获取一级分销关系数失败: %w", err)
	}

	level2Referrals, err := s.userReferralRepo.GetCountByLevel(ctx, models.ReferralLevelTwo)
	if err != nil {
		return nil, fmt.Errorf("获取二级分销关系数失败: %w", err)
	}

	level3Referrals, err := s.userReferralRepo.GetCountByLevel(ctx, models.ReferralLevelThree)
	if err != nil {
		return nil, fmt.Errorf("获取三级分销关系数失败: %w", err)
	}

	// 获取佣金统计
	totalCommission, err := s.userReferralRepo.GetTotalCommission(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取总佣金失败: %w", err)
	}

	// 获取今日佣金
	todayStart := time.Now().Truncate(24 * time.Hour)
	todayCommission, err := s.userReferralRepo.GetCommissionByDateRange(ctx, todayStart, time.Now())
	if err != nil {
		return nil, fmt.Errorf("获取今日佣金失败: %w", err)
	}

	// 获取本月佣金
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	thisMonthCommission, err := s.userReferralRepo.GetCommissionByDateRange(ctx, monthStart, now)
	if err != nil {
		return nil, fmt.Errorf("获取本月佣金失败: %w", err)
	}

	// 获取本年佣金
	yearStart := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	thisYearCommission, err := s.userReferralRepo.GetCommissionByDateRange(ctx, yearStart, now)
	if err != nil {
		return nil, fmt.Errorf("获取本年佣金失败: %w", err)
	}

	// 获取今日新增分销关系
	todayNewReferrals, err := s.userReferralRepo.GetCountByDateRange(ctx, todayStart, now)
	if err != nil {
		return nil, fmt.Errorf("获取今日新增分销关系失败: %w", err)
	}

	// 获取本月新增分销关系
	thisMonthNewReferrals, err := s.userReferralRepo.GetCountByDateRange(ctx, monthStart, now)
	if err != nil {
		return nil, fmt.Errorf("获取本月新增分销关系失败: %w", err)
	}

	// 获取本年新增分销关系
	thisYearNewReferrals, err := s.userReferralRepo.GetCountByDateRange(ctx, yearStart, now)
	if err != nil {
		return nil, fmt.Errorf("获取本年新增分销关系失败: %w", err)
	}

	// 构建响应
	stats := &dto.AdminReferralStatisticsResponse{
		TotalUsers:            totalUsers,
		TotalReferrals:        totalReferrals,
		ActiveReferrals:       activeReferrals,
		InactiveReferrals:     inactiveReferrals,
		Level1Referrals:       level1Referrals,
		Level2Referrals:       level2Referrals,
		Level3Referrals:       level3Referrals,
		TotalCommission:       totalCommission,
		TodayCommission:       todayCommission,
		ThisMonthCommission:   thisMonthCommission,
		ThisYearCommission:    thisYearCommission,
		TodayNewReferrals:     todayNewReferrals,
		ThisMonthNewReferrals: thisMonthNewReferrals,
		ThisYearNewReferrals:  thisYearNewReferrals,
	}

	return stats, nil
}

// GetTopReferrers 获取TOP推荐人数据（管理员）
func (s *userReferralService) GetTopReferrers(ctx context.Context, req *dto.TopReferrerRequest) ([]*dto.TopReferrerResponse, error) {
	// 参数验证
	if req.Limit <= 0 {
		req.Limit = 10 // 默认返回10条
	}
	if req.Limit > 100 {
		req.Limit = 100 // 最多返回100条
	}

	// 解析日期
	var startDate, endDate time.Time
	var err error

	if req.StartDate != "" {
		startDate, err = time.Parse("2006-01-02", req.StartDate)
		if err != nil {
			return nil, fmt.Errorf("开始日期格式错误: %w", err)
		}
	}

	if req.EndDate != "" {
		endDate, err = time.Parse("2006-01-02", req.EndDate)
		if err != nil {
			return nil, fmt.Errorf("结束日期格式错误: %w", err)
		}
		// 设置为当天的23:59:59
		endDate = endDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
	}

	// 调用仓库层获取TOP推荐人数据
	topReferrers, err := s.userReferralRepo.GetTopReferrers(ctx, startDate, endDate, req.Limit)
	if err != nil {
		return nil, fmt.Errorf("获取TOP推荐人数据失败: %w", err)
	}

	return topReferrers, nil
}

// GetCommissionSummary 获取佣金汇总信息（管理员）
func (s *userReferralService) GetCommissionSummary(ctx context.Context) (*dto.CommissionSummaryResponse, error) {
	// 根据已有的管理员统计接口提取相关数据
	adminStats, err := s.GetAdminReferralStatistics(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取管理员统计数据失败: %w", err)
	}

	// 注意：佣金等级配置在CommissionSummary接口中返回，此处不再需要

	// 按等级获取每个等级的佣金总额
	level1Commission, err := s.userReferralRepo.GetCommissionByLevel(ctx, models.ReferralLevelOne)
	if err != nil {
		return nil, fmt.Errorf("获取一级佣金失败: %w", err)
	}

	level2Commission, err := s.userReferralRepo.GetCommissionByLevel(ctx, models.ReferralLevelTwo)
	if err != nil {
		return nil, fmt.Errorf("获取二级佣金失败: %w", err)
	}

	level3Commission, err := s.userReferralRepo.GetCommissionByLevel(ctx, models.ReferralLevelThree)
	if err != nil {
		return nil, fmt.Errorf("获取三级佣金失败: %w", err)
	}

	// 获取待结算和已结算佣金
	pendingCommission, err := s.userReferralRepo.GetPendingCommission(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取待结算佣金失败: %w", err)
	}

	settledCommission, err := s.userReferralRepo.GetSettledCommission(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取已结算佣金失败: %w", err)
	}

	// 获取分销用户数量
	referralUserCount, err := s.userReferralRepo.GetReferralUserCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取分销用户数量失败: %w", err)
	}

	// 构建响应
	return &dto.CommissionSummaryResponse{
		TotalCommission:    adminStats.TotalCommission,
		PendingCommission:  pendingCommission,
		SettledCommission:  settledCommission,
		ReferralUserCount:  referralUserCount,
		Level1Commission:   level1Commission,
		Level2Commission:   level2Commission,
		Level3Commission:   level3Commission,
		TodayCommission:    adminStats.TodayCommission,
		MonthCommission:    adminStats.ThisMonthCommission,
	}, nil
}

// GetCommissionStatistics 获取佣金统计信息（管理员）
func (s *userReferralService) GetCommissionStatistics(ctx context.Context, req *dto.CommissionStatisticsRequest) (*dto.CommissionStatisticsResponse, error) {
	// 参数验证
	if req.StartDate.IsZero() {
		// 默认为30天前
		req.StartDate = time.Now().AddDate(0, 0, -30)
	}

	if req.EndDate.IsZero() {
		// 默认为当前时间
		req.EndDate = time.Now()
	}

	// 确保开始日期不晚于结束日期
	if req.StartDate.After(req.EndDate) {
		return nil, fmt.Errorf("开始日期不能晚于结束日期")
	}

	// 获取日期区间内的佣金统计
	totalCommission, err := s.userReferralRepo.GetCommissionByDateRange(ctx, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("获取日期区间内的佣金总额失败: %w", err)
	}

	// 获取按等级的统计数据
	level1Commission, err := s.userReferralRepo.GetCommissionByLevelAndDateRange(ctx, models.ReferralLevelOne, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("获取一级佣金统计失败: %w", err)
	}

	level2Commission, err := s.userReferralRepo.GetCommissionByLevelAndDateRange(ctx, models.ReferralLevelTwo, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("获取二级佣金统计失败: %w", err)
	}

	level3Commission, err := s.userReferralRepo.GetCommissionByLevelAndDateRange(ctx, models.ReferralLevelThree, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("获取三级佣金统计失败: %w", err)
	}

	// 获取日期区间内每天的佣金数据
	dailyStats, err := s.userReferralRepo.GetDailyCommissionStats(ctx, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("获取每日佣金数据失败: %w", err)
	}

	// 计算订单数量
	orderCount, err := s.userReferralRepo.GetOrderCountByDateRange(ctx, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("获取订单数量失败: %w", err)
	}

	// 获取用户数量
	userCount, err := s.userReferralRepo.GetReferralUserCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取分销用户数量失败: %w", err)
	}

	// 计算平均佣金
	avgCommission := float64(0)
	if orderCount > 0 {
		avgCommission = totalCommission / float64(orderCount)
	}

	// 构建级别统计
	totalLevelCommission := level1Commission + level2Commission + level3Commission
	levelStats := make([]dto.LevelCommissionStat, 0, 3)

	// 添加各级别佣金统计
	if totalLevelCommission > 0 {
		if level1Commission > 0 {
			levelStats = append(levelStats, dto.LevelCommissionStat{
				Level:      1,
				Commission: level1Commission,
				Percentage: level1Commission / totalLevelCommission * 100,
			})
		}
		if level2Commission > 0 {
			levelStats = append(levelStats, dto.LevelCommissionStat{
				Level:      2,
				Commission: level2Commission,
				Percentage: level2Commission / totalLevelCommission * 100,
			})
		}
		if level3Commission > 0 {
			levelStats = append(levelStats, dto.LevelCommissionStat{
				Level:      3,
				Commission: level3Commission,
				Percentage: level3Commission / totalLevelCommission * 100,
			})
		}
	}

	// 获取按时段统计
	morningCommission, afternoonCommission, eveningCommission, err := s.userReferralRepo.GetCommissionByTimeRanges(ctx, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("获取时段统计失败: %w", err)
	}

	// 构建响应
	return &dto.CommissionStatisticsResponse{
		Period: struct {
			StartDate string `json:"start_date"`
			EndDate   string `json:"end_date"`
		}{
			StartDate: req.StartDate.Format("2006-01-02"),
			EndDate:   req.EndDate.Format("2006-01-02"),
		},
		Summary: struct {
			TotalCommission float64 `json:"total_commission"`
			OrderCount      int64   `json:"order_count"`
			UserCount       int64   `json:"user_count"`
			AvgCommission   float64 `json:"avg_commission"`
		}{
			TotalCommission: totalCommission,
			OrderCount:      orderCount,
			UserCount:       userCount,
			AvgCommission:   avgCommission,
		},
		DailyStats: dailyStats,
		LevelStats: levelStats,
		TimeStats: struct {
			Morning   float64 `json:"morning"`
			Afternoon float64 `json:"afternoon"`
			Evening   float64 `json:"evening"`
		}{
			Morning:   morningCommission,
			Afternoon: afternoonCommission,
			Evening:   eveningCommission,
		},
	}, nil
}

// convertToReferralResponse 将模型转换为响应DTO
func (s *userReferralService) convertToReferralResponse(ctx context.Context, referral *models.UserReferral) *dto.ReferralResponse {
	return &dto.ReferralResponse{
		ID:           referral.ID,
		UserID:       referral.UserID,
		ReferrerID:   referral.ReferrerID,
		Level:        referral.Level,
		Status:       referral.Status,
		Commission:   referral.Commission,
		ReferralTime: referral.ReferralTime,
		CreatedAt:    referral.CreatedAt,
	}
}
