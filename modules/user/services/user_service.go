/**
 * 用户服务实现
 *
 * 该文件实现了用户服务接口，提供用户注册、登录、信息管理等业务逻辑。
 * 用户服务是系统的核心服务之一，负责用户身份认证和用户信息维护。
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/repositories"
	userUtils "o_mall_backend/modules/user/utils" // 用户模块的工具函数
	"o_mall_backend/utils" // 全局工具函数
	"o_mall_backend/utils/redis"
	"o_mall_backend/utils/sms"
	"o_mall_backend/utils/wechat"
)

// 全局服务实例和同步锁，用于懒加载单例模式
var (
	globalUserService UserService
	userServiceOnce   sync.Once
)

// GetUserService 获取用户服务实例
// 使用懒加载单例模式，确保全局只有一个用户服务实例
func GetUserService() UserService {
	userServiceOnce.Do(func() {
		if globalUserService == nil {
			// 初始化依赖的ReferralConfigRepository
			configRepo := repositories.NewReferralConfigRepository()
			
			// 初始化依赖的ReferralConfigService
			referralConfigService := NewReferralConfigService(configRepo)
			
			// 创建UserService实例
			globalUserService = NewUserService(referralConfigService)
			
			serviceAddress := fmt.Sprintf("%p", globalUserService)
			logs.Info("[用户模块] 全局UserService初始化完成，实例地址: %s", serviceAddress)
		}
	})
	
	return globalUserService
}

// UserServiceImpl 用户服务实现
type UserServiceImpl struct {
	userRepo        repositories.UserRepository
	userReferralRepo repositories.UserReferralRepository
	userLogService  UserLogService
	referralConfigService ReferralConfigService
}

// NewUserService 创建用户服务实例
func NewUserService(referralConfigService ReferralConfigService) UserService {
	return &UserServiceImpl{
		userRepo:        repositories.NewUserRepository(),
		userReferralRepo: repositories.NewUserReferralRepository(),
		userLogService:  NewUserLogService(),
		referralConfigService: referralConfigService,
	}
}

// Register 用户注册
// 处理用户注册流程，包括数据验证、密码加密、用户信息存储和推荐关系建立
func (s *UserServiceImpl) Register(ctx context.Context, req *dto.RegisterRequest, registerIP string) (int64, error) {
	// 检查用户名是否已存在
	existUser, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		logs.Error("查询用户名是否存在失败: %v", err)
		return 0, err
	}
	if existUser != nil {
		return 0, errors.New("用户名已存在")
	}

	// 检查手机号是否已存在
	existUser, err = s.userRepo.GetByMobile(ctx, req.Mobile)
	if err != nil {
		logs.Error("查询手机号是否存在失败: %v", err)
		return 0, err
	}
	if existUser != nil {
		return 0, errors.New("手机号已被使用")
	}

	// 检查邮箱是否已存在（如果提供了邮箱）
	if req.Email != "" {
		existUser, err = s.userRepo.GetByEmail(ctx, req.Email)
		if err != nil {
			logs.Error("查询邮箱是否存在失败: %v", err)
			return 0, err
		}
		if existUser != nil {
			return 0, errors.New("邮箱已被使用")
		}
	}

	// 检查推荐码是否有效（如果提供了推荐码）
	var referrer *models.User
	if req.ReferralCode != "" {
		// 验证推荐码格式
		if !userUtils.IsValidReferralCode(req.ReferralCode) {
			return 0, errors.New("无效的推荐码格式")
		}
		
		// 查询推荐人
		referrer, err = s.userRepo.GetByReferralCode(ctx, req.ReferralCode)
		if err != nil {
			logs.Error("查询推荐码失败: %v", err)
			return 0, err
		}
		if referrer == nil {
			return 0, errors.New("推荐码不存在或已失效")
		}
	}

	// 加密密码
	hashedPassword, err := utils.EncryptPassword(req.Password)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return 0, err
	}

	// 生成推荐码
	referralCode, err := userUtils.GenerateReferralCode()
	if err != nil {
		logs.Error("生成推荐码失败: %v", err)
		return 0, err
	}
	
	// 创建用户对象
	user := &models.User{
		Username:     req.Username,
		Password:     hashedPassword,
		Nickname:     req.Nickname,
		Mobile:       req.Mobile,
		Email:        req.Email,
		Gender:       req.Gender,
		Status:       models.UserStatusEnabled,
		RegisterIP:   registerIP,
		ReferralCode: referralCode, // 设置用户的推荐码
	}

	// 保存用户信息
	userID, err := s.userRepo.Create(ctx, user)
	if err != nil {
		logs.Error("创建用户失败: %v", err)
		return 0, err
	}

	// 如果有推荐人，创建推荐关系
	if req.ReferralCode != "" && referrer != nil {
		err = s.handleReferral(ctx, userID, req.ReferralCode)
		if err != nil {
			logs.Error("创建推荐关系失败: %v", err)
			// 这里我们选择不回滚用户创建，而是只记录错误，让用户注册成功
			// 可以考虑后续通过任务队列重试创建推荐关系
		}
	}

	return userID, nil
}

// Login 用户登录
// 处理用户登录流程，包括身份验证和JWT令牌生成
func (s *UserServiceImpl) Login(ctx context.Context, req *dto.LoginRequest, loginIP string) (*dto.LoginResponse, error) {
	// 查询用户
	user, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return nil, err
	}
	if user == nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 检查用户状态
	if user.Status != models.UserStatusEnabled {
		return nil, errors.New("用户已被禁用")
	}

	// 验证密码
	if !utils.ValidatePassword(req.Password, user.Password) {
		return nil, errors.New("用户名或密码错误")
	}

	// 更新登录信息
	if err := s.userRepo.UpdateLoginInfo(ctx, user.ID, loginIP); err != nil {
		logs.Error("更新登录信息失败: %v", err)
		// 登录信息更新失败不影响登录流程，继续执行
	}

	// 生成JWT令牌对
	tokenPair, err := utils.GenerateTokenPair(user.ID, user.Username, "user")
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 存储refresh token
	err = utils.StoreRefreshToken(user.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储refresh token失败: %v", err)
		return nil, err
	}

	// 构建响应
	userResp := s.convertToUserResponse(user)
	loginResp := &dto.LoginResponse{
		TokenInfo: dto.TokenResponse{
			AccessToken:  tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresIn:    tokenPair.ExpiresIn,
			TokenType:    "Bearer",
		},
		User: *userResp,
	}

	return loginResp, nil
}

// GetUserByID 获取用户信息
// 根据用户ID获取用户详细信息
func (s *UserServiceImpl) GetUserByID(ctx context.Context, id int64) (*dto.UserResponse, error) {
	// 查询用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return nil, err
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	// 转换为响应对象
	return s.convertToUserResponse(user), nil
}

// UpdateUserInfo 更新用户信息
// 更新用户的基本信息，如昵称、头像、性别等
func (s *UserServiceImpl) UpdateUserInfo(ctx context.Context, id int64, req *dto.UpdateUserRequest) error {
	// 查询用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != user.Email {
		existUser, err := s.userRepo.GetByEmail(ctx, req.Email)
		if err != nil {
			logs.Error("查询邮箱是否存在失败: %v", err)
			return err
		}
		if existUser != nil && existUser.ID != id {
			return errors.New("邮箱已被使用")
		}
	}

	// 更新用户信息
	user.Nickname = req.Nickname
	user.Avatar = req.Avatar
	user.Gender = req.Gender
	user.Birthday = req.Birthday
	user.Email = req.Email

	// 保存更新
	if err := s.userRepo.Update(ctx, user); err != nil {
		logs.Error("更新用户信息失败: %v", err)
		return err
	}

	return nil
}

// ChangePassword 修改密码
// 修改用户密码，需要验证原密码
func (s *UserServiceImpl) ChangePassword(ctx context.Context, id int64, req *dto.ChangePasswordRequest) error {
	// 查询用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if !utils.ValidatePassword(req.OldPassword, user.Password) {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := utils.EncryptPassword(req.NewPassword)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return err
	}

	// 更新密码
	if err := s.userRepo.UpdatePassword(ctx, id, hashedPassword); err != nil {
		logs.Error("更新密码失败: %v", err)
		return err
	}

	return nil
}

// ListUsers 获取用户列表
// 分页获取用户列表，用于管理员查看
func (s *UserServiceImpl) ListUsers(ctx context.Context, page, pageSize int) ([]*dto.UserResponse, int64, error) {
	// 查询用户列表
	users, total, err := s.userRepo.List(ctx, page, pageSize)
	if err != nil {
		logs.Error("查询用户列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为响应对象
	userResponses := make([]*dto.UserResponse, 0, len(users))
	for _, user := range users {
		userResponses = append(userResponses, s.convertToUserResponse(user))
	}

	return userResponses, total, nil
}

// DisableUser 禁用用户
// 将用户状态设置为禁用
func (s *UserServiceImpl) DisableUser(ctx context.Context, id int64) error {
	// 查询用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 更新状态
	if err := s.userRepo.UpdateStatus(ctx, id, models.UserStatusDisabled); err != nil {
		logs.Error("禁用用户失败: %v", err)
		return err
	}

	return nil
}

// EnableUser 启用用户
// 将用户状态设置为正常
func (s *UserServiceImpl) EnableUser(ctx context.Context, id int64) error {
	// 查询用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 更新状态
	if err := s.userRepo.UpdateStatus(ctx, id, models.UserStatusEnabled); err != nil {
		logs.Error("启用用户失败: %v", err)
		return err
	}

	return nil
}

// UpdateBalance 更新用户余额
// 增加或减少用户余额，需要提供操作描述
func (s *UserServiceImpl) UpdateBalance(ctx context.Context, id int64, amount float64, description string) error {
	// 余额更新直接调用仓库方法
	if err := s.userRepo.UpdateBalance(ctx, id, amount); err != nil {
		logs.Error("更新用户余额失败: %v", err)
		return err
	}

	// TODO: 记录余额变动日志

	return nil
}

// UpdatePoints 更新用户积分
// 增加或减少用户积分，需要提供操作描述
func (s *UserServiceImpl) UpdatePoints(ctx context.Context, id int64, points int64, description string) error {
	// 积分更新直接调用仓库方法
	if err := s.userRepo.UpdatePoints(ctx, id, points); err != nil {
		logs.Error("更新用户积分失败: %v", err)
		return err
	}

	// TODO: 记录积分变动日志

	return nil
}

// RefreshToken 刷新访问令牌
// 使用刷新令牌获取新的访问令牌和刷新令牌对
func (s *UserServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*dto.TokenResponse, error) {
	// 调用工具函数刷新令牌
	tokenPair, err := utils.RefreshUserTokens(refreshToken)
	if err != nil {
		logs.Error("刷新令牌失败: %v", err)
		return nil, err
	}

	// 构建响应
	resp := &dto.TokenResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresIn:    tokenPair.ExpiresIn,
		TokenType:    "Bearer",
	}

	return resp, nil
}

// Logout 用户登出
// 使当前用户的所有令牌失效
func (s *UserServiceImpl) Logout(ctx context.Context, userID int64) error {
	// 调用工具函数撤销所有令牌
	err := utils.RevokeAllUserTokens(userID, "")
	if err != nil {
		logs.Error("撤销用户令牌失败: %v", err)
		return err
	}

	return nil
}

// SendVerificationCode 发送手机验证码
// 为用户登录发送手机验证码，并将验证码保存到Redis
func (s *UserServiceImpl) SendVerificationCode(ctx context.Context, req *dto.SendVerificationCodeRequest) (*dto.SendVerificationCodeResponse, error) {
	// 生成随机验证码
	code := sms.GenerateVerificationCode(6) // 生成6位验证码

	// 获取验证码有效期（分钟）
	expireTime, _ := web.AppConfig.Int("sms::sms_code_expire_time")
	if expireTime <= 0 {
		expireTime = 15 // 默认15分钟
	}

	// 将验证码保存到Redis
	redisKey := fmt.Sprintf("sms:login:user:%s", req.Mobile)
	// 设置验证码和过期时间
	err := redis.Set(redisKey, code, time.Duration(expireTime)*time.Minute)
	if err != nil {
		logs.Error("保存验证码到Redis失败: %v", err)
		return nil, errors.New("验证码生成失败，请稍后再试")
	}

	// 发送验证码短信
	err = sms.SendVerificationCode(req.Mobile, code, sms.UsageLogin)
	if err != nil {
		logs.Error("发送验证码短信失败: %v", err)
		// 如果发送失败，删除Redis中的验证码
		_, _ = redis.Del(redisKey)
		return nil, errors.New("验证码发送失败，请稍后再试")
	}

	// 返回响应
	resp := &dto.SendVerificationCodeResponse{
		ExpireTime: expireTime,
	}

	logs.Info("用户登录验证码已发送到手机: %s", req.Mobile)
	return resp, nil
}

// SendRegisterVerificationCode 发送注册验证码
// 为用户注册发送手机验证码，并将验证码保存到Redis
func (s *UserServiceImpl) SendRegisterVerificationCode(ctx context.Context, req *dto.SendVerificationCodeRequest) (*dto.SendVerificationCodeResponse, error) {
	// 生成随机验证码
	code := sms.GenerateVerificationCode(6) // 生成6位验证码

	// 获取验证码有效期（分钟）
	expireTime, _ := web.AppConfig.Int("sms::sms_code_expire_time")
	if expireTime <= 0 {
		expireTime = 15 // 默认15分钟
	}

	// 将验证码保存到Redis（注册专用的key前缀）
	redisKey := fmt.Sprintf("sms:register:user:%s", req.Mobile)
	// 设置验证码和过期时间
	err := redis.Set(redisKey, code, time.Duration(expireTime)*time.Minute)
	if err != nil {
		logs.Error("保存注册验证码到Redis失败: %v", err)
		return nil, errors.New("验证码生成失败，请稍后再试")
	}

	// 发送验证码短信，使用注册用途
	err = sms.SendVerificationCode(req.Mobile, code, sms.UsageRegister)
	if err != nil {
		logs.Error("发送注册验证码短信失败: %v", err)
		// 如果发送失败，删除Redis中的验证码
		_, _ = redis.Del(redisKey)
		return nil, errors.New("验证码发送失败，请稍后再试")
	}

	// 返回响应
	resp := &dto.SendVerificationCodeResponse{
		ExpireTime: expireTime,
	}

	logs.Info("用户注册验证码已发送到手机: %s", req.Mobile)
	return resp, nil
}

// CheckMobileExists 检查手机号是否已注册
// 检查手机号是否已经被注册使用
func (s *UserServiceImpl) CheckMobileExists(ctx context.Context, mobile string) (bool, error) {
	// 调用仓储层查询手机号
	user, err := s.userRepo.GetByMobile(ctx, mobile)
	if err != nil {
		logs.Error("查询手机号是否存在失败: %v", err)
		return false, err
	}
	
	// 如果用户存在，返回true
	return user != nil, nil
}

// RegisterByVerifyCode 使用手机号和验证码注册新用户
// 验证短信验证码并创建新用户
func (s *UserServiceImpl) RegisterByVerifyCode(ctx context.Context, req *dto.RegisterByVerifyCodeRequest, registerIP string) (int64, error) {
	// 首先检查手机号是否已经被注册
	exist, err := s.CheckMobileExists(ctx, req.Mobile)
	if err != nil {
		logs.Error("检查手机号是否存在失败: %v", err)
		return 0, err
	}
	if exist {
		return 0, errors.New("该手机号已被注册")
	}
	
	// 验证手机验证码（同时支持register和login两种用途的验证码）
	// 首先尝试获取注册验证码
	registerRedisKey := fmt.Sprintf("sms:register:user:%s", req.Mobile)
	loginRedisKey := fmt.Sprintf("sms:login:user:%s", req.Mobile)
	
	// 先尝试获取注册验证码
	storeCode, err := redis.Get(registerRedisKey)
	if err != nil {
		// 注册验证码不存在，尝试获取登录验证码
		logs.Info("注册验证码不存在，尝试获取登录验证码，手机号: %s", req.Mobile)
		storeCode, err = redis.Get(loginRedisKey)
		if err != nil {
			logs.Error("获取验证码失败（注册验证码和登录验证码均不存在）: %v", err)
			return 0, errors.New("验证码已过期或不存在，请重新获取")
		}
		// 使用登录验证码成功，记录日志
		logs.Info("成功获取登录验证码用于注册流程，手机号: %s", req.Mobile)
	}
	
	// 验证码比对
	if storeCode != req.Code {
		return 0, errors.New("验证码错误")
	}
	
	// 验证码正确，删除Redis中的两种验证码，防止重复使用
	_, _ = redis.Del(registerRedisKey)
	_, _ = redis.Del(loginRedisKey)
	
	// 生成默认用户名（使用手机号）
	username := req.Mobile
	
	// 检查用户名是否已存在，如果存在则添加后缀
	existUser, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		logs.Error("查询用户名是否存在失败: %v", err)
		return 0, err
	}
	if existUser != nil {
		// 如果手机号作为用户名已存在，添加时间戳后缀
		username = fmt.Sprintf("%s_%d", req.Mobile, time.Now().Unix())
	}
	
	// 生成默认密码（用户可以在注册后修改）
	defaultPassword := "123456" // 默认初始密码
	hashedPassword, err := utils.EncryptPassword(defaultPassword)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return 0, err
	}
	
	// 生成推荐码
	referralCode, err := userUtils.GenerateReferralCode()
	if err != nil {
		logs.Error("生成推荐码失败: %v", err)
		return 0, err
	}
	
	// 创建用户对象
	logs.Info("开始创建用户对象，手机号: %s, 昵称: %s", req.Mobile, req.Nickname)
	
	// 处理Email字段，如果是特殊标记__NULL__则设置为空字符串（对应数据库中的NULL）
	emailValue := req.Email
	if emailValue == "__NULL__" {
		emailValue = "" // 在ORM中，空字符串会被转换为NULL
		logs.Info("检测到特殊Email标记，将设置为数据库NULL值")
	}
	
	user := &models.User{
		Username:     username,
		Password:     hashedPassword,
		Nickname:     req.Nickname,                  // 使用用户提供的昵称
		Mobile:       req.Mobile,
		Email:        emailValue,                    // 处理后的Email值
		Gender:       req.Gender,                   // 用户指定性别
		Status:       models.UserStatusEnabled,
		RegisterIP:   registerIP,
		ReferralCode: referralCode,
	}
	
	logs.Info("成功创建用户对象: %+v", user)
	
	// 保存用户信息
	userID, err := s.userRepo.Create(ctx, user)
	if err != nil {
		logs.Error("创建用户失败: %v", err)
		return 0, err
	}
	
	// 如果有推荐人 ID，则建立推荐关系
	if req.ReferrerID > 0 {
		// 首先查询推荐人是否存在
		referrer, err := s.userRepo.GetByID(ctx, req.ReferrerID)
		if err != nil {
			logs.Error("查询推荐人失败: %v", err)
			// 推荐人查询失败不影响注册流程，继续执行
		} else if referrer == nil {
			logs.Warn("推荐人不存在，ID: %d", req.ReferrerID)
		} else {
			// 创建推荐关系
			referral := &models.UserReferral{
				UserID:       userID,
				ReferrerID:   req.ReferrerID,
				Level:        models.ReferralLevelOne,  // 一级推荐
				Status:       models.ReferralStatusValid, // 有效状态
				ReferralTime: time.Now(),              // 推荐时间
			}
			
			// 创建推荐关系
			_, err := s.userReferralRepo.Create(ctx, referral)
			if err != nil {
				logs.Error("创建推荐关系失败: %v", err)
				// 推荐关系创建失败不影响注册流程，继续执行
			} else {
				logs.Info("成功创建推荐关系，用户ID: %d, 推荐人ID: %d", userID, req.ReferrerID)
			}
		}
	}
	
	logs.Info("用户手机验证码注册成功, 用户ID: %d, 手机号: %s, 初始密码: %s", userID, req.Mobile, defaultPassword)
	return userID, nil
}

// LoginByVerifyCode 用户验证码登录
// 使用手机号和验证码登录用户账号，如果手机号不存在则自动创建用户
func (s *UserServiceImpl) LoginByVerifyCode(ctx context.Context, req *dto.UserVerifyCodeLoginRequest, loginIP string) (*dto.LoginResponse, error) {
	// 验证手机验证码
	redisKey := fmt.Sprintf("sms:login:user:%s", req.Mobile)
	storeCode, err := redis.Get(redisKey)
	if err != nil {
		logs.Error("获取验证码失败: %v", err)
		return nil, errors.New("验证码已过期或不存在，请重新获取")
	}

	// 验证码比对
	if storeCode != req.Code {
		return nil, errors.New("验证码错误")
	}

	// 验证码正确，删除Redis中的验证码，防止重复使用
	_, _ = redis.Del(redisKey)

	// 查询用户是否存在
	user, err := s.userRepo.GetByMobile(ctx, req.Mobile)
	if err != nil {
		logs.Error("查询用户失败: %v", err)
		return nil, err
	}

	// 如果用户不存在，则创建新用户
	if user == nil {
		logs.Info("手机号 %s 对应的用户不存在，开始创建新用户", req.Mobile)
		userID, err := s.createUserByMobile(ctx, req.Mobile, loginIP)
		if err != nil {
			logs.Error("创建新用户失败: %v", err)
			return nil, err
		}
		
		// 重新查询创建的用户
		user, err = s.userRepo.GetByID(ctx, userID)
		if err != nil {
			logs.Error("查询新创建的用户失败: %v", err)
			return nil, err
		}
	}

	// 检查用户状态
	if user.Status != models.UserStatusEnabled {
		return nil, errors.New("用户已被禁用")
	}

	// 更新登录信息
	if err := s.userRepo.UpdateLoginInfo(ctx, user.ID, loginIP); err != nil {
		logs.Error("更新登录信息失败: %v", err)
		// 登录信息更新失败不影响登录流程，继续执行
	}

	// 生成JWT令牌对
	tokenPair, err := utils.GenerateTokenPair(user.ID, user.Username, "user")
	if err != nil {
		logs.Error("生成JWT令牌失败: %v", err)
		return nil, err
	}

	// 存储refresh token
	err = utils.StoreRefreshToken(user.ID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("存储refresh token失败: %v", err)
		return nil, err
	}

	// 构建响应
	userResp := s.convertToUserResponse(user)
	loginResp := &dto.LoginResponse{
		TokenInfo: dto.TokenResponse{
			AccessToken:  tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresIn:    tokenPair.ExpiresIn,
			TokenType:    "Bearer",
		},
		User: *userResp,
	}

	logs.Info("用户验证码登录成功, 用户ID: %d, 手机号: %s", user.ID, req.Mobile)
	return loginResp, nil
}

// createUserByMobile 根据手机号创建新用户
// 内部辅助方法，用于验证码登录时自动创建用户
func (s *UserServiceImpl) createUserByMobile(ctx context.Context, mobile, registerIP string) (int64, error) {
	// 生成默认用户名（使用手机号）
	username := mobile
	
	// 检查用户名是否已存在，如果存在则添加后缀
	existUser, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		logs.Error("查询用户名是否存在失败: %v", err)
		return 0, err
	}
	if existUser != nil {
		// 如果手机号作为用户名已存在，添加时间戳后缀
		username = fmt.Sprintf("%s_%d", mobile, time.Now().Unix())
	}

	// 生成默认密码（可以是随机密码，用户后续可以修改）
	defaultPassword := "123456" // 默认密码，建议用户首次登录后修改
	hashedPassword, err := utils.EncryptPassword(defaultPassword)
	if err != nil {
		logs.Error("密码加密失败: %v", err)
		return 0, err
	}

	// 生成推荐码
	referralCode, err := userUtils.GenerateReferralCode()
	if err != nil {
		logs.Error("生成推荐码失败: %v", err)
		return 0, err
	}

	// 创建用户对象
	user := &models.User{
		Username:     username,
		Password:     hashedPassword,
		Nickname:     fmt.Sprintf("用户%s", mobile[len(mobile)-4:]), // 使用手机号后4位作为默认昵称
		Mobile:       mobile,
		Gender:       0, // 默认未知
		Status:       models.UserStatusEnabled,
		RegisterIP:   registerIP,
		ReferralCode: referralCode,
	}

	// 保存用户信息
	userID, err := s.userRepo.Create(ctx, user)
	if err != nil {
		logs.Error("创建用户失败: %v", err)
		return 0, err
	}

	logs.Info("通过手机号验证码登录自动创建用户成功, 用户ID: %d, 手机号: %s", userID, mobile)
	return userID, nil
}

// GetAccountTransactions 获取账户变动记录
// 获取用户的账户交易记录列表，支持分页（已移除类型筛选）
func (s *UserServiceImpl) GetAccountTransactions(ctx context.Context, userID int64, req *dto.AccountTransactionRequest) (*dto.AccountTransactionListResponse, error) {
	// 添加调试日志
	logs.Info("[DEBUG] GetAccountTransactions - UserID: %d, Page: %d, PageSize: %d", userID, req.Page, req.PageSize)
	
	// 创建用户账户仓储实例
	userAccountRepo := repositories.NewUserAccountRepository()
	
	// 查询交易记录（不再使用transactionType过滤）
	transactions, total, err := userAccountRepo.GetUserTransactions(userID, "", req.Page, req.PageSize)
	if err != nil {
		logs.Error("[ERROR] GetUserTransactions failed: %v", err)
		logs.Error("查询用户账户交易记录失败: %v", err)
		return nil, fmt.Errorf("查询账户变动记录失败: %v", err)
	}

	logs.Info("[DEBUG] Found %d transactions, total: %d", len(transactions), total)
	
	// 转换为响应对象
	var transactionList []*dto.AccountTransactionResponse
	for _, transaction := range transactions {
		transactionList = append(transactionList, s.convertToAccountTransactionResponse(transaction))
	}

	return &dto.AccountTransactionListResponse{
		List:  transactionList,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// WxMiniLogin 微信小程序登录
// 处理微信小程序登录流程，包括获取微信用户信息、创建或更新用户信息、生成JWT令牌
func (s *UserServiceImpl) WxMiniLogin(ctx context.Context, req *dto.WechatMiniLoginRequest, loginIP string) (*dto.LoginResponse, error) {
	// 创建微信小程序服务实例
	wxMini, err := wechat.NewWechatMiniApp()
	if err != nil {
		logs.Error("创建微信小程序服务实例失败: %v", err)
		return nil, errors.New("微信登录服务不可用")
	}

	if !wxMini.IsEnabled() {
		return nil, errors.New("微信小程序登录功能未开启")
	}

	// 使用code换取用户的session信息
	session, err := wxMini.Code2Session(req.Code)
	if err != nil {
		logs.Error("微信code2session失败: %v", err)
		return nil, errors.New("验证微信登录凭证失败")
	}

	// 查询用户是否已存在
	user, err := s.userRepo.GetByWxOpenID(ctx, session.OpenID)
	if err != nil {
		logs.Error("查询微信用户失败: %v", err)
		return nil, errors.New("查询用户信息失败")
	}

	// 如果用户不存在，创建新用户
	if user == nil {
		// 生成用户名
		username := "wx_" + utils.RandString(10)

		// 生成随机密码
		password := utils.RandString(16)
		hashedPassword, err := utils.EncryptPassword(password)
		if err != nil {
			logs.Error("密码加密失败: %v", err)
			return nil, err
		}

		// 生成推荐码
		referralCode, err := userUtils.GenerateReferralCode()
		if err != nil {
			logs.Error("生成推荐码失败: %v", err)
			return nil, err
		}

		// 创建新用户
		user = &models.User{
			Username:     username,
			Password:     hashedPassword,
			Nickname:     req.NickName,
			Avatar:       req.AvatarUrl,
			Gender:       req.Gender,
			WxOpenID:     session.OpenID,
			WxUnionID:    session.UnionID,
			Status:       models.UserStatusEnabled,
			RegisterIP:   loginIP,
			LastLoginIP:  loginIP,
			LastLoginAt:  time.Now(),
			ReferralCode: referralCode,
			// 设置Email为sql.NullString{Valid:false}，确保数据库中存储为NULL而不是空字符串
			Email:        "", // ORM会处理空字符串为NULL
			Mobile:       "", // ORM会处理空字符串为NULL
		}

		// 处理推荐关系
		if req.ReferralCode != "" {
			// 验证推荐码格式
			if !userUtils.IsValidReferralCode(req.ReferralCode) {
				logs.Warn("无效的推荐码格式: %s", req.ReferralCode)
				// 不中断注册流程，只是不建立推荐关系
			} else {
				// 查询推荐人
				referrer, err := s.userRepo.GetByReferralCode(ctx, req.ReferralCode)
				if err != nil {
					logs.Error("查询推荐码失败: %v", err)
					// 不中断注册流程
				} else if referrer != nil {
					// 将用户保存到数据库中
					userID, err := s.userRepo.Create(ctx, user)
					if err != nil {
						logs.Error("创建用户失败: %v", err)
						return nil, errors.New("创建用户失败")
					}
					user.ID = userID

					// 创建推荐关系
					referral := &models.UserReferral{
						UserID:     userID,
						ReferrerID: referrer.ID,
						Level:      1, // 直接推荐关系为1级
						Status:     models.ReferralStatusValid,
						CreatedAt:  time.Now(),
					}
					_, err = s.userReferralRepo.Create(ctx, referral)
					if err != nil {
						logs.Error("创建推荐关系失败: %v", err)
						// 不中断登录流程
					}
				}
			}
		}

		// 如果前面的代码没有创建用户（比如推荐关系处理中提前创建了用户）
		if user.ID == 0 {
			userID, err := s.userRepo.Create(ctx, user)
			if err != nil {
				logs.Error("创建用户失败: %v", err)
				return nil, errors.New("创建用户失败")
			}
			user.ID = userID
		}

		// 记录日志
		_, _ = s.userLogService.CreateLog(ctx, &dto.UserLogCreateRequest{
			UserID:      user.ID,
			OperationType: 1, // 注册操作
			Content:      "微信小程序注册",
			IP:          loginIP,
			UserAgent:   "",
		})
	} else {
		// 用户已存在，更新登录信息
		err = s.userRepo.UpdateLoginInfo(ctx, user.ID, loginIP)
		if err != nil {
			logs.Error("更新用户登录信息失败: %v", err)
			// 不中断登录流程
		}

		// 更新用户微信信息（如果有变化）
		if req.NickName != "" && req.NickName != user.Nickname || 
		   req.AvatarUrl != "" && req.AvatarUrl != user.Avatar || 
		   req.Gender != 0 && req.Gender != user.Gender {
			
			if req.NickName != "" {
				user.Nickname = req.NickName
			}
			if req.AvatarUrl != "" {
				user.Avatar = req.AvatarUrl
			}
			if req.Gender != 0 {
				user.Gender = req.Gender
			}
			
			err = s.userRepo.Update(ctx, user)
			if err != nil {
				logs.Error("更新用户信息失败: %v", err)
				// 不中断登录流程
			}
		}

		// 记录日志
		_, _ = s.userLogService.CreateLog(ctx, &dto.UserLogCreateRequest{
			UserID:      user.ID,
			OperationType: 2, // 登录操作
			Content:      "微信小程序登录",
			IP:          loginIP,
			UserAgent:   "",
		})
	}

	// 生成JWT令牌
	tokenResponse, err := utils.GenerateTokenPair(user.ID, user.Username, "user")
	if err != nil {
		logs.Error("生成用户令牌失败: %v", err)
		return nil, errors.New("登录失败")
	}

	// 返回登录响应
	// 获取用户信息响应对象
	userResp := s.convertToUserResponse(user)
	
	// 创建 TokenResponse 对象，从 TokenPair 转换
	tokenResp := dto.TokenResponse{
		AccessToken:  tokenResponse.AccessToken,
		RefreshToken: tokenResponse.RefreshToken,
		ExpiresIn:    tokenResponse.ExpiresIn,
		TokenType:    "Bearer", // TokenResponse 需要这个字段，而 TokenPair 没有
	}
	
	// 这里要确保返回值类型为dto.UserResponse（非指针），而不是*dto.UserResponse
	return &dto.LoginResponse{
		User:      *userResp,     // 解引用，将*dto.UserResponse转为dto.UserResponse
		TokenInfo: tokenResp,     // 使用转换后的TokenResponse对象
	}, nil
}

// GetAccountInfo 获取账户信息
// 获取用户的账户详细信息
func (s *UserServiceImpl) GetAccountInfo(ctx context.Context, userID int64) (*dto.UserAccountResponse, error) {
	// 创建用户账户仓储实例
	userAccountRepo := repositories.NewUserAccountRepository()
	
	// 查询账户信息
	account, err := userAccountRepo.GetUserAccountByUserID(userID)
	if err != nil {
		logs.Error("查询用户账户信息失败: %v", err)
		return nil, err
	}
	
	if account == nil {
		return nil, errors.New("账户信息不存在")
	}

	// 转换为响应对象
	return s.convertToUserAccountResponse(account), nil
}

// convertToAccountTransactionResponse 将交易记录模型转换为响应对象
// 内部辅助方法，用于转换数据格式
func (s *UserServiceImpl) convertToAccountTransactionResponse(transaction *models.UserAccountTransaction) *dto.AccountTransactionResponse {
	if transaction == nil {
		return nil
	}

	return &dto.AccountTransactionResponse{
		ID:            transaction.ID,
		TransactionNo: transaction.TransactionNo,
		RelatedID:     transaction.RelatedID,
		RelatedType:   transaction.RelatedType,
		Amount:        transaction.Amount,
		BeforeBalance: transaction.BeforeBalance,
		AfterBalance:  transaction.AfterBalance,
		Type:          transaction.Type,
		Operation:     transaction.Operation,
		Status:        transaction.Status,
		Description:   transaction.Description,
		Remark:        transaction.Remark,
		ClientIP:      transaction.ClientIP,
		CreatedAt:     transaction.CreatedAt.Format("2006-01-02 15:04:05"),
	}
}

// convertToUserAccountResponse 将账户模型转换为响应对象
// 内部辅助方法，用于转换数据格式
func (s *UserServiceImpl) convertToUserAccountResponse(account *models.UserAccount) *dto.UserAccountResponse {
	if account == nil {
		return nil
	}

	// 格式化时间字段
	lastRecharge := ""
	if !account.LastRecharge.IsZero() {
		lastRecharge = account.LastRecharge.Format("2006-01-02 15:04:05")
	}
	
	lastConsume := ""
	if !account.LastConsume.IsZero() {
		lastConsume = account.LastConsume.Format("2006-01-02 15:04:05")
	}

	return &dto.UserAccountResponse{
		ID:            account.ID,
		UserID:        account.UserID,
		Balance:       account.Balance,
		FrozenBalance: account.FrozenBalance,
		TotalRecharge: account.TotalRecharge,
		TotalConsume:  account.TotalConsume,
		Status:        account.Status,
		LastRecharge:  lastRecharge,
		LastConsume:   lastConsume,
		UpdatedAt:     account.UpdatedAt.Format("2006-01-02 15:04:05"),
		CreatedAt:     account.CreatedAt.Format("2006-01-02 15:04:05"),
	}
}

// convertToUserResponse 将用户模型转换为响应对象
// 内部辅助方法，用于转换数据格式
func (s *UserServiceImpl) convertToUserResponse(user *models.User) *dto.UserResponse {
	if user == nil {
		return nil
	}

	return &dto.UserResponse{
		ID:          user.ID,
		Username:    user.Username,
		Nickname:    user.Nickname,
		Avatar:      user.Avatar,
		Mobile:      user.Mobile,
		Email:       user.Email,
		Gender:      user.Gender,
		Birthday:    user.Birthday,
		Balance:     user.Balance,
		Points:      user.Points,
		Level:       user.Level,
		Status:      user.Status,
		LastLoginAt: user.LastLoginAt,
		CreatedAt:   user.CreatedAt,
		ReferralCode: user.ReferralCode, // 添加推荐码字段
	}
}
