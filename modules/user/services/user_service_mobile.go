/**
 * 用户手机号服务实现
 *
 * 该文件实现了用户手机号绑定和修改相关的业务逻辑。
 * 包括手机号初次绑定和已绑定手机号的修改功能。
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/user/dto"
	"o_mall_backend/utils/redis"
)

// 短信验证码Redis前缀常量
const (
	// 用于新手机号绑定验证
	RedisMobileBindCodePrefix = "user:mobile:bind:"
	// 用于手机号变更验证
	RedisMobileChangeCodePrefix = "user:mobile:change:"
)

// BindMobile 绑定手机号
// 为当前用户绑定手机号，要求用户提供验证过的手机号和验证码
func (s *UserServiceImpl) BindMobile(ctx context.Context, userID int64, req *dto.BindMobileRequest) (*dto.BindMobileResponse, error) {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		logs.Error("[用户服务] 绑定手机号时查询用户失败: %v", err)
		return nil, err
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	// 检查用户是否已经绑定手机号
	if user.Mobile != "" {
		return &dto.BindMobileResponse{
			Success: false,
			Message: "用户已经绑定了手机号，如需修改请使用修改手机号接口",
		}, nil
	}

	// 检查手机号是否已被其他账号使用
	existUser, err := s.userRepo.GetByMobile(ctx, req.Mobile)
	if err != nil {
		logs.Error("[用户服务] 检查手机号是否被使用失败: %v", err)
		return nil, err
	}
	if existUser != nil {
		return &dto.BindMobileResponse{
			Success: false,
			Message: "该手机号已被其他账号使用",
		}, nil
	}

	// 验证码校验
	verifyKey := fmt.Sprintf("%s%s", RedisMobileBindCodePrefix, req.Mobile)
	verifyCode, err := redis.Get(verifyKey)
	if err != nil || verifyCode == "" {
		return &dto.BindMobileResponse{
			Success: false,
			Message: "验证码已过期，请重新获取",
		}, nil
	}

	if verifyCode != req.Code {
		return &dto.BindMobileResponse{
			Success: false,
			Message: "验证码不正确",
		}, nil
	}

	// 更新用户手机号
	user.Mobile = req.Mobile
	user.UpdatedAt = time.Now()
	
	err = s.userRepo.Update(ctx, user)
	if err != nil {
		logs.Error("[用户服务] 更新用户手机号失败: %v", err)
		return nil, err
	}

	// 删除已使用的验证码
	redis.Del(verifyKey)

	// 记录用户日志
	logReq := &dto.UserLogCreateRequest{
		UserID:       userID,
		OperationType: 1, // 1表示绑定手机号操作
		Content:      "绑定手机号",
		IP:           "", // 此处应该从上下文获取，但当前传递不到
		Remark:       fmt.Sprintf("绑定手机号: %s", req.Mobile),
		Status:       1, // 成功
	}
	_, _ = s.userLogService.CreateLog(ctx, logReq)

	return &dto.BindMobileResponse{
		Success: true,
		Message: "手机号绑定成功",
	}, nil
}

// ChangeMobile 修改绑定手机号
// 修改用户已绑定的手机号，需要同时验证旧手机号和新手机号
func (s *UserServiceImpl) ChangeMobile(ctx context.Context, userID int64, req *dto.ChangeMobileRequest) (*dto.ChangeMobileResponse, error) {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		logs.Error("[用户服务] 修改手机号时查询用户失败: %v", err)
		return nil, err
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	// 检查用户是否已经绑定手机号
	if user.Mobile == "" {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "用户未绑定手机号，请先绑定手机号",
		}, nil
	}

	// 验证原手机号是否匹配
	if user.Mobile != req.OldMobile {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "原手机号不匹配",
		}, nil
	}

	// 验证原手机号验证码
	oldVerifyKey := fmt.Sprintf("%s%s", RedisMobileChangeCodePrefix, req.OldMobile)
	oldVerifyCode, err := redis.Get(oldVerifyKey)
	if err != nil || oldVerifyCode == "" {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "原手机号验证码已过期，请重新获取",
		}, nil
	}

	if oldVerifyCode != req.OldCode {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "原手机号验证码不正确",
		}, nil
	}

	// 检查新手机号是否已被其他账号使用
	if req.OldMobile == req.NewMobile {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "新手机号与原手机号相同",
		}, nil
	}

	existUser, err := s.userRepo.GetByMobile(ctx, req.NewMobile)
	if err != nil {
		logs.Error("[用户服务] 检查新手机号是否被使用失败: %v", err)
		return nil, err
	}
	if existUser != nil {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "该手机号已被其他账号使用",
		}, nil
	}

	// 验证新手机号验证码
	newVerifyKey := fmt.Sprintf("%s%s", RedisMobileBindCodePrefix, req.NewMobile)
	newVerifyCode, err := redis.Get(newVerifyKey)
	if err != nil || newVerifyCode == "" {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "新手机号验证码已过期，请重新获取",
		}, nil
	}

	if newVerifyCode != req.NewCode {
		return &dto.ChangeMobileResponse{
			Success: false,
			Message: "新手机号验证码不正确",
		}, nil
	}

	// 更新用户手机号
	oldMobile := user.Mobile
	user.Mobile = req.NewMobile
	user.UpdatedAt = time.Now()
	
	err = s.userRepo.Update(ctx, user)
	if err != nil {
		logs.Error("[用户服务] 更新用户手机号失败: %v", err)
		return nil, err
	}

	// 删除已使用的验证码
	redis.Del(oldVerifyKey)
	redis.Del(newVerifyKey)

	// 记录用户日志
	logReq := &dto.UserLogCreateRequest{
		UserID:       userID,
		OperationType: 2, // 2表示修改手机号操作
		Content:      "修改绑定手机号",
		IP:           "", // 此处应该从上下文获取，但当前传递不到
		Remark:       fmt.Sprintf("手机号变更: %s -> %s", oldMobile, req.NewMobile),
		Status:       1, // 成功
	}
	_, _ = s.userLogService.CreateLog(ctx, logReq)

	return &dto.ChangeMobileResponse{
		Success: true,
		Message: "手机号修改成功",
	}, nil
}
