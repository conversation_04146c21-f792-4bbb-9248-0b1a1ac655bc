/**
 * 用户服务推荐码相关实现
 *
 * 该文件实现了用户服务接口中与推荐码相关的功能，
 * 包括生成推荐码、通过推荐码获取用户以及在注册时处理推荐关系等。
 */

package services

import (
	"context"
	"errors"
	
	"github.com/beego/beego/v2/core/logs"
	
	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	userUtils "o_mall_backend/modules/user/utils"
)

// GenerateReferralCode 为用户生成推荐码
func (s *UserServiceImpl) GenerateReferralCode(ctx context.Context, userID int64) (string, error) {
	// 获取用户信息
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return "", err
	}
	
	if user == nil {
		return "", errors.New("用户不存在")
	}
	
	// 如果用户已有推荐码，直接返回
	if user.ReferralCode != "" {
		return user.ReferralCode, nil
	}
	
	// 生成新的推荐码
	code, err := userUtils.GenerateReferralCode()
	if err != nil {
		logs.Error("生成推荐码失败: %v", err)
		return "", err
	}
	
	// 检查推荐码是否已被使用
	existingUser, err := s.userRepo.GetByReferralCode(ctx, code)
	if err != nil {
		return "", err
	}
	
	// 如果推荐码已存在，重新生成
	if existingUser != nil {
		return s.GenerateReferralCode(ctx, userID) // 递归调用直到生成唯一推荐码
	}
	
	// 更新用户推荐码
	err = s.userRepo.UpdateReferralCode(ctx, userID, code)
	if err != nil {
		return "", err
	}
	
	return code, nil
}

// GetUserByReferralCode 通过推荐码获取用户信息
func (s *UserServiceImpl) GetUserByReferralCode(ctx context.Context, referralCode string) (*dto.UserResponse, error) {
	// 验证推荐码格式
	if !userUtils.IsValidReferralCode(referralCode) {
		return nil, errors.New("无效的推荐码格式")
	}
	
	// 查询用户
	user, err := s.userRepo.GetByReferralCode(ctx, referralCode)
	if err != nil {
		return nil, err
	}
	
	if user == nil {
		return nil, errors.New("推荐码不存在或已失效")
	}
	
	// 转换为DTO
	return s.convertToUserResponse(user), nil
}

// 处理用户注册时的推荐关系
func (s *UserServiceImpl) handleReferral(ctx context.Context, userID int64, referralCode string) error {
	// 如果没有推荐码，不处理推荐关系
	if referralCode == "" {
		return nil
	}
	
	// 获取推荐人
	referrer, err := s.userRepo.GetByReferralCode(ctx, referralCode)
	if err != nil {
		return err
	}
	
	if referrer == nil {
		return errors.New("推荐码无效")
	}
	
	// 不能自己推荐自己
	if referrer.ID == userID {
		return errors.New("不能使用自己的推荐码")
	}
	
	// 创建分销关系
	userReferralService := NewUserReferralService(s.userRepo, s.userReferralRepo, s.userLogService, s.referralConfigService)
	
	// 准备创建请求
	req := &dto.ReferralCreateRequest{
		UserID:     userID,       // 被推荐人ID
		ReferrerID: referrer.ID,  // 推荐人ID
		Level:      models.ReferralLevelOne, // 默认为一级分销
	}
	
	// 创建分销关系
	_, err = userReferralService.CreateReferral(ctx, req)
	return err
}
