/**
 * 微信网页扫码登录服务实现
 *
 * 该文件实现了与微信网页扫码登录相关的服务方法，包括生成二维码、查询状态和登录逻辑。
 */

package services

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/utils"
	"o_mall_backend/utils/redis"
	"o_mall_backend/utils/wechat"
)

// 微信网页扫码登录相关常量
const (
	// 微信二维码Redis存储相关常量
	WxQrCodeKeyPrefix        = "wx_qrcode:"  // 二维码信息存储前缀
	WxQrCodeExpiration       = 5 * 60        // 二维码有效期(秒)
	WxQrAuthStateKeyPrefix   = "wx_auth_state:" // 微信授权state前缀
	WxQrAuthStateExpiration  = 10 * 60       // 授权state有效期(秒)
)

// GetWechatQrCode 获取微信登录二维码
// 生成用于微信扫码登录的二维码信息
func (s *UserServiceImpl) GetWechatQrCode(ctx context.Context, req *dto.WechatQrCodeRequest) (*dto.WechatQrCodeResponse, error) {
	// 创建微信网页授权工具实例
	wxWeb, err := wechat.NewWechatWeb()
	if err != nil {
		logs.Error("创建微信网页授权工具实例失败: %v", err)
		return nil, err
	}

	// 检查微信网页功能是否启用
	if !wxWeb.IsEnabled() {
		return nil, errors.New("微信网页授权功能未启用")
	}

	// 生成唯一的二维码ID
	qrcodeID, err := generateUniqueID()
	if err != nil {
		logs.Error("生成二维码ID失败: %v", err)
		return nil, err
	}

	// 生成用于防止CSRF攻击的state参数
	state, err := generateUniqueID()
	if err != nil {
		logs.Error("生成state参数失败: %v", err)
		return nil, err
	}

	// 记录state与qrcodeID的关联关系，便于回调时验证
	stateKey := WxQrAuthStateKeyPrefix + state
	err = redis.SetWithExpire(stateKey, qrcodeID, WxQrAuthStateExpiration)
	if err != nil {
		logs.Error("存储state关联关系失败: %v", err)
		return nil, err
	}

	// 创建二维码状态信息
	qrStatus := &dto.WechatQrCodeStatusResponse{
		Status:     dto.WxQrLoginStatusWaiting,
		UserInfo:   nil,
		UpdateTime: time.Now(),
	}

	// 序列化状态信息
	statusJson, err := json.Marshal(qrStatus)
	if err != nil {
		logs.Error("序列化二维码状态失败: %v", err)
		return nil, err
	}

	// 将二维码信息存入Redis
	qrCodeKey := WxQrCodeKeyPrefix + qrcodeID
	err = redis.SetWithExpire(qrCodeKey, string(statusJson), WxQrCodeExpiration)
	if err != nil {
		logs.Error("存储二维码状态失败: %v", err)
		return nil, err
	}

	// 生成微信授权URL
	redirectURI := req.RedirectURL
	authURL, err := wxWeb.GetAuthorizeURL(state, redirectURI)
	if err != nil {
		logs.Error("生成微信授权URL失败: %v", err)
		return nil, err
	}

	// 构建响应
	expireTime := time.Now().Unix() + WxQrCodeExpiration
	return &dto.WechatQrCodeResponse{
		QrCodeURL:  authURL,      // 微信授权URL直接作为二维码内容
		QrCodeID:   qrcodeID,     // 二维码唯一标识
		ExpireTime: expireTime,   // 过期时间
		QrContent:  authURL,      // 二维码内容，可供前端直接生成二维码
	}, nil
}

// CheckWechatQrCodeStatus 检查微信登录二维码状态
// 查询当前二维码的登录状态
func (s *UserServiceImpl) CheckWechatQrCodeStatus(ctx context.Context, req *dto.WechatQrCodeStatusRequest) (*dto.WechatQrCodeStatusResponse, error) {
	if req.QrCodeID == "" {
		return nil, errors.New("二维码ID不能为空")
	}

	// 从Redis获取二维码状态
	qrCodeKey := WxQrCodeKeyPrefix + req.QrCodeID
	statusStr, err := redis.Get(qrCodeKey)
	if err != nil {
		logs.Error("获取二维码状态失败: %v", err)
		return nil, errors.New("二维码状态查询失败")
	}

	// 如果状态不存在，说明二维码已过期或不存在
	if statusStr == "" {
		return &dto.WechatQrCodeStatusResponse{
			Status:     dto.WxQrLoginStatusExpired,
			UserInfo:   nil,
			UpdateTime: time.Now(),
		}, nil
	}

	// 解析状态JSON
	var status dto.WechatQrCodeStatusResponse
	err = json.Unmarshal([]byte(statusStr), &status)
	if err != nil {
		logs.Error("解析二维码状态JSON失败: %v", err)
		return nil, errors.New("二维码状态数据错误")
	}

	return &status, nil
}

// WxWebLogin 处理微信网页授权登录
// 处理微信授权回调，完成用户登录或注册
func (s *UserServiceImpl) WxWebLogin(ctx context.Context, req *dto.WechatWebLoginRequest) (*dto.WechatWebLoginResponse, error) {
	// 创建微信网页授权工具实例
	wxWeb, err := wechat.NewWechatWeb()
	if err != nil {
		logs.Error("创建微信网页授权工具实例失败: %v", err)
		return nil, err
	}

	// 检查微信网页功能是否启用
	if !wxWeb.IsEnabled() {
		return nil, errors.New("微信网页授权功能未启用")
	}

	// 验证state参数
	if req.State == "" {
		return nil, errors.New("state参数不能为空")
	}

	// 从Redis获取state对应的qrcodeID
	stateKey := WxQrAuthStateKeyPrefix + req.State
	qrcodeID, err := redis.Get(stateKey)
	if err != nil || qrcodeID == "" {
		logs.Error("验证state参数失败: %v", err)
		return nil, errors.New("state参数无效或已过期")
	}

	// 获取微信访问令牌
	tokenResp, err := wxWeb.GetAccessToken(req.Code)
	if err != nil {
		logs.Error("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 获取用户信息
	userInfo, err := wxWeb.GetUserInfo(tokenResp.AccessToken, tokenResp.OpenID)
	if err != nil {
		logs.Error("获取微信用户信息失败: %v", err)
		return nil, err
	}

	// 查询是否有匹配的用户（通过OpenID或UnionID）
	var user *models.User
	if userInfo.UnionID != "" {
		// 优先使用UnionID查询，这样可以跨平台关联账号（小程序、公众号等）
		user, err = s.userRepo.GetByWechatUnionID(ctx, userInfo.UnionID)
		if err != nil {
			logs.Error("通过UnionID查询用户失败: %v", err)
			return nil, err
		}
	}

	// 如果没有通过UnionID找到，则通过OpenID查询
	if user == nil {
		user, err = s.userRepo.GetByWechatOpenID(ctx, userInfo.OpenID)
		if err != nil {
			logs.Error("通过OpenID查询用户失败: %v", err)
			return nil, err
		}
	}

	// 处理用户注册或信息更新
	if user == nil {
		// 新用户，创建账号
		user, err = s.createUserFromWechat(ctx, userInfo, req.ClientIP)
		if err != nil {
			logs.Error("创建微信用户失败: %v", err)
			return nil, err
		}
	} else {
		// 已存在用户，更新微信信息
		err = s.updateUserWechatInfo(ctx, user.ID, userInfo)
		if err != nil {
			logs.Error("更新微信用户信息失败: %v", err)
			// 这里只记录错误，不影响登录流程
		}
	}

	// 生成token对并返回登录响应
	tokenPair, err := utils.GenerateTokenPair(user.ID, user.Username, "user")
	if err != nil {
		logs.Error("生成Token失败: %v", err)
		return nil, errors.New("生成登录凭证失败")
	}

	// 更新用户最后登录时间
	user.LastLoginIP = req.ClientIP
	user.LastLoginAt = time.Now()
	err = s.userRepo.UpdateLoginInfo(ctx, user.ID, req.ClientIP)
	if err != nil {
		logs.Error("更新用户登录信息失败: %v", err)
		// 这里只记录错误，不影响登录流程
	}

	// 记录登录日志
	s.createLoginLog(ctx, user.ID, "微信扫码登录", req.ClientIP, true, "")

	// 更新二维码状态为登录成功
	userResponse := &dto.WechatWebLoginResponse{
		UserID:   user.ID,
		Username: user.Username,
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
		Token: dto.TokenResponse{
			AccessToken:  tokenPair.AccessToken,
			RefreshToken: tokenPair.RefreshToken,
			ExpiresIn:    tokenPair.ExpiresIn,
			TokenType:    "Bearer",
		},
	}

	// 更新Redis中的二维码状态
	qrStatus := &dto.WechatQrCodeStatusResponse{
		Status:     dto.WxQrLoginStatusSuccess,
		UserInfo:   userResponse,
		UpdateTime: time.Now(),
	}

	// 序列化并存储状态
	statusJson, err := json.Marshal(qrStatus)
	if err == nil {
		qrCodeKey := WxQrCodeKeyPrefix + qrcodeID
		_ = redis.SetWithExpire(qrCodeKey, string(statusJson), WxQrCodeExpiration)
	}

	return userResponse, nil
}

// createUserFromWechat 根据微信用户信息创建新用户
// 内部辅助方法，用于创建微信登录用户
func (s *UserServiceImpl) createUserFromWechat(ctx context.Context, wxUserInfo *dto.WechatWebUserInfo, registerIP string) (*models.User, error) {
	// 生成随机用户名，格式为：wx_用户ID
	randomStr, _ := generateUniqueID()
	username := fmt.Sprintf("wx_%s", randomStr[:8])
	
	// 使用微信昵称作为用户昵称，如果为空则使用默认昵称
	nickname := wxUserInfo.Nickname
	if nickname == "" {
		nickname = "微信用户"
	}

	// 生成随机密码（用户无需知道，但系统需要存储）
	randomPassword, err := generateUniqueID()
	if err != nil {
		return nil, err
	}
	hashedPassword, err := utils.EncryptPassword(randomPassword)
	if err != nil {
		return nil, err
	}

	// 生成推荐码
	referralCode := utils.RandString(8)

	// 创建用户模型
	user := &models.User{
		Username:       username,
		Password:       hashedPassword,
		Nickname:       nickname,
		Avatar:         wxUserInfo.HeadImgURL,
		Gender:         wxUserInfo.Sex,
		Status:         models.UserStatusNormal,
		WxOpenID:       wxUserInfo.OpenID,
		WxUnionID:      wxUserInfo.UnionID,
		ReferralCode:   referralCode,
		RegisterIP:     registerIP,
		LastLoginIP:    registerIP,
		LastLoginAt:    time.Now(),
		// 注册来源字段不存在于User模型中，暂时移除
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 保存用户到数据库
	userID, err := s.userRepo.Create(ctx, user)
	if err != nil {
		return nil, err
	}
	user.ID = userID

	// 记录注册日志
	s.createRegisterLog(ctx, user.ID, "微信扫码注册", registerIP)

	// 返回创建的用户
	return user, nil
}

// updateUserWechatInfo 更新用户的微信相关信息
// 内部辅助方法，用于更新已有用户的微信信息
func (s *UserServiceImpl) updateUserWechatInfo(ctx context.Context, userID int64, wxUserInfo *dto.WechatWebUserInfo) error {
	// 构建更新数据
	updates := map[string]interface{}{
		"wechat_open_id": wxUserInfo.OpenID,
		"updated_at":     time.Now(),
	}
	
	// 如果有UnionID也进行更新
	if wxUserInfo.UnionID != "" {
		updates["wechat_union_id"] = wxUserInfo.UnionID
	}
	
	// 获取当前用户信息
	user := &models.User{ID: userID}
	err := s.userRepo.Get(ctx, user)
	if err != nil {
		return err
	}
	
	if user.Avatar == "" && wxUserInfo.HeadImgURL != "" {
		updates["avatar"] = wxUserInfo.HeadImgURL
	}
	
	// 如果用户没有设置昵称，则使用微信昵称
	if user.Nickname == "" && wxUserInfo.Nickname != "" {
		updates["nickname"] = wxUserInfo.Nickname
	}
	
	// 如果用户没有设置性别，则使用微信性别
	if user.Gender == 0 && wxUserInfo.Sex > 0 {
		updates["gender"] = wxUserInfo.Sex
	}
	
	// 构建用户对象进行更新
	updateUser := &models.User{ID: userID}
	
	// 设置更新字段的值
	if wxUserInfo.OpenID != "" {
		updateUser.WxOpenID = wxUserInfo.OpenID
	}
	if wxUserInfo.UnionID != "" {
		updateUser.WxUnionID = wxUserInfo.UnionID
	}
	if user.Avatar == "" && wxUserInfo.HeadImgURL != "" {
		updateUser.Avatar = wxUserInfo.HeadImgURL
	}
	if user.Nickname == "" && wxUserInfo.Nickname != "" {
		updateUser.Nickname = wxUserInfo.Nickname
	}
	if user.Gender == 0 && wxUserInfo.Sex > 0 {
		updateUser.Gender = wxUserInfo.Sex
	}
	updateUser.UpdatedAt = time.Now()
	
	// 确定需要更新的字段
	fields := []string{}
	if wxUserInfo.OpenID != "" {
		fields = append(fields, "WxOpenID")
	}
	if wxUserInfo.UnionID != "" {
		fields = append(fields, "WxUnionID")
	}
	if user.Avatar == "" && wxUserInfo.HeadImgURL != "" {
		fields = append(fields, "Avatar")
	}
	if user.Nickname == "" && wxUserInfo.Nickname != "" {
		fields = append(fields, "Nickname")
	}
	if user.Gender == 0 && wxUserInfo.Sex > 0 {
		fields = append(fields, "Gender")
	}
	fields = append(fields, "UpdatedAt")
	
	// 更新到数据库
	return s.userRepo.UpdateFields(ctx, updateUser, fields...)
}

// createLoginLog 创建用户登录日志
// 内部辅助方法，用于记录用户登录日志
func (s *UserServiceImpl) createLoginLog(ctx context.Context, userID int64, loginType string, clientIP string, success bool, failReason string) {
	// 准备日志内容
	content := fmt.Sprintf("用户 [ID:%d] 使用 %s 登录系统", userID, loginType)
	if !success {
		content += fmt.Sprintf("，失败原因：%s", failReason)
	}
	
	// 构建日志请求
	logReq := &dto.UserLogCreateRequest{
		UserID:       userID,
		OperationType: 1, // 登录操作类型
		Content:      content,
		IP:           clientIP,
		Status:       1, // 1表示成功，0表示失败
	}
	
	// 如果登录失败，设置状态为失败
	if !success {
		logReq.Status = 0
		logReq.Remark = failReason
	}
	
	// 创建日志
	_, err := s.userLogService.CreateLog(ctx, logReq)
	if err != nil {
		logs.Error("创建登录日志失败: %v", err)
	}
}

// createRegisterLog 创建用户注册日志
// 内部辅助方法，用于记录用户注册日志
func (s *UserServiceImpl) createRegisterLog(ctx context.Context, userID int64, registerType string, clientIP string) {
	// 准备日志内容
	content := fmt.Sprintf("用户 [ID:%d] 通过 %s 注册成功", userID, registerType)
	
	// 构建日志请求
	logReq := &dto.UserLogCreateRequest{
		UserID:       userID,
		OperationType: 2, // 注册操作类型
		Content:      content,
		IP:           clientIP,
		Status:       1, // 1表示成功
	}
	
	// 创建日志
	_, err := s.userLogService.CreateLog(ctx, logReq)
	if err != nil {
		logs.Error("创建注册日志失败: %v", err)
	}
}

// generateUniqueID 生成唯一的随机标识
// 内部辅助方法，用于生成随机ID
func generateUniqueID() (string, error) {
	// 生成16字节的随机数据
	bytes := make([]byte, 16)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	
	// 使用base64编码，并移除可能的特殊字符
	id := base64.URLEncoding.EncodeToString(bytes)
	return id, nil
}
