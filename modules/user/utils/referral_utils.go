/**
 * 分销推荐工具函数
 *
 * 该文件提供推荐码生成和验证相关的工具函数，
 * 包括生成随机推荐码、验证推荐码有效性等功能。
 */

package utils

import (
	"crypto/rand"
	"math/big"
	"strings"
)

// 推荐码字符集
const (
	ReferralCodeCharset = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789" // 去掉了容易混淆的字符（0,1,I,O）
	ReferralCodeLength  = 8                                 // 推荐码长度
)

// GenerateReferralCode 生成随机推荐码
// 生成一个8位的随机字符串作为推荐码，包含大写字母和数字，去掉容易混淆的字符
func GenerateReferralCode() (string, error) {
	charsetLength := big.NewInt(int64(len(ReferralCodeCharset)))
	codeBuilder := strings.Builder{}
	codeBuilder.Grow(ReferralCodeLength)
	
	// 生成8位随机字符
	for i := 0; i < ReferralCodeLength; i++ {
		// 安全地生成随机索引
		randomIndex, err := rand.Int(rand.Reader, charsetLength)
		if err != nil {
			return "", err
		}
		
		// 从字符集中选择字符
		codeBuilder.WriteByte(ReferralCodeCharset[randomIndex.Int64()])
	}
	
	return codeBuilder.String(), nil
}

// IsValidReferralCode 验证推荐码格式是否有效
func IsValidReferralCode(code string) bool {
	if len(code) != ReferralCodeLength {
		return false
	}
	
	// 检查每个字符是否在字符集中
	for _, char := range code {
		if !strings.ContainsRune(ReferralCodeCharset, char) {
			return false
		}
	}
	
	return true
}
