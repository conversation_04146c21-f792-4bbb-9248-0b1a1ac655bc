2025/07/01 17:42:19.519 [1;34m[I][0m [init.go:17]  [API文档模块] 自动注册数据模型...
2025/07/01 17:42:19.519 [1;34m[I][0m [init.go:22]  [API文档模块] 数据模型自动注册完成
2025/07/01 17:42:19.519 [1;33m[W][0m [main.go:41]  未配置日志级别或配置错误，使用默认值：Debug
2025/07/01 17:42:19.522 [1;34m[I][0m [main.go:49]  日志系统初始化完成，日志级别：7
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:53]  当前工作目录: /Users/<USER>/Documents/2025works/o_-mall_backend
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:61]  检测到配置文件: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/app.conf
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:69]  配置文件大小: 1955 字节
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:722]  开始解析配置文件: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/app.conf
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: appname = O_Mall
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: appname = O_Mall
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: httpport = 8181
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: httpport = 8181
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: runmode = dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: runmode = dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: autorender = false
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: autorender = false
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: copyrequestbody = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: copyrequestbody = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: EnableDocs = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: EnableDocs = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: dev_mode = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: dev_mode = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: api_prefix = api
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: api_prefix = api
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: api_version = v1
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: api_version = v1
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: flushapidoc = false
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: flushapidoc = false
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: mysqluser = o_mall_dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: mysqluser = o_mall_dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: mysqlpass = 4wk2HTRWkxKxwhHX
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: mysqlpass = ***
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: mysqlhost = *************
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: mysqlhost = *************
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: mysqlport = 3306
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: mysqlport = 3306
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: mysqldb = o_mall_dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: mysqldb = o_mall_dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: runmodeabc = dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: runmodeabc = dev
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: redishost = 127.0.0.1
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: redishost = 127.0.0.1
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: redisport = 6379
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: redisport = 6379
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: redispass = CnCmQFCiYS34ox
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: redispass = ***
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: redisdb = 1
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: redisdb = 1
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: logfilepath = "./logs/o_mall.log"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: logfilepath = "./logs/o_mall.log"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: loglevel = "debug"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: loglevel = "debug"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: jwtSecret = "o_mall_secret_key"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: jwtSecret = ***
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: jwtExpireHour = 24
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: jwtExpireHour = 24
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: uploadPath = "./uploads"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: uploadPath = "./uploads"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: uploadMaxSize = 10485760
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: uploadMaxSize = 10485760
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: runnerBaseFee = 5
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: runnerBaseFee = 5
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:195]  解析到全局配置项: runnerKmFee = 2
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: runnerKmFee = 2
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:169]  切换到配置节: [cors]
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:193]  将配置项解析到节 [cors]: allow_origins = "*"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: cors:allow_origins = "*"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:193]  将配置项解析到节 [cors]: allow_methods = "GET,POST,PUT,DELETE,OPTIONS,PATCH"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: cors:allow_methods = "GET,POST,PUT,DELETE,OPTIONS,PATCH"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:193]  将配置项解析到节 [cors]: allow_headers = "Origin,Content-Type,Accept,Authorization,X-Requested-With,platform"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: cors:allow_headers = "Origin,Content-Type,Accept,Authorization,X-Requested-With,platform"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:193]  将配置项解析到节 [cors]: allow_credentials = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: cors:allow_credentials = true
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:193]  将配置项解析到节 [cors]: max_age = 86400
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: cors:max_age = 86400
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:193]  将配置项解析到节 [cors]: expose_headers = "Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type"
2025/07/01 17:42:19.522 [1;44m[D][0m [config.go:207]  解析到配置项: cors:expose_headers = "Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type"
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:703]  关键配置解析结果:
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    runmode = dev
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    mysqluser = o_mall_dev
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    mysqlhost = *************
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    mysqlport = 3306
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    mysqldb = o_mall_dev
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    redishost = 127.0.0.1
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    redisport = 6379
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:707]    redisdb = 1
2025/07/01 17:42:19.522 [1;34m[I][0m [config.go:75]  已解析配置文件内容到全局配置映射, 共 34 项配置
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:86]  成功加载主配置文件
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:101]  基础配置验证: appname=O_Mall, runmode=dev
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:297]  模块配置文件内容 (/Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/storage.conf):
# 存储模块配置
# 
# 本配置文件定义了存储相关的配置项，包括存储模式、OSS、COS等
# 支持的存储模式: local（本地存储）, oss（阿里云OSS）, cos（腾讯云COS）, s3（AWS S3）, qiniu（七牛云）
#

# # 存储基本配置
# [storage]
# mode = qiniu
# enable_cdn = false

# # 七牛云存储配置（当mode=qiniu时使用）
# [qiniu]
# access_key = oKYsL8bDTr2W-FM0tysAxiS5X5IpDcTgDV2ZUXcC
# secret_key = aGiQNOi29_yKU1dWp_kDGeoctiHiByPc3MfWtrNw
# bucket = omail-img
# domain = omallimg.qwyx.shop
# zone = z2 # 存储区域，可选值: z0(华东), z1(华北), z2(华南), na0(北美), as0(东南亚)
# use_https = false # 是否使用HTTPS

2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:722]  开始解析配置文件: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/storage.conf
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:703]  关键配置解析结果:
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    runmode = dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqluser = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqlhost = *************
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqlport = 3306
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqldb = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redishost = 127.0.0.1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redisport = 6379
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redisdb = 1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:301]  添加模块配置到 include 并解析: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/storage.conf
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:297]  模块配置文件内容 (/Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/payment.conf):
# 支付服务配置文件
# 此文件管理所有与支付服务相关的配置

# 标记配置是否已导入
imported = true

# 支付方式配置
# 可选值: alipay, wechat, paypal, stripe, custom
payment_methods = alipay,wechat

# 阿里支付配置
alipay_app_id = your_app_id
alipay_private_key = your_private_key
alipay_public_key = your_public_key
alipay_notify_url = https://example.com/api/v1/payment/alipay/notify
alipay_return_url = https://example.com/payment/callback
alipay_gateway = https://openapi.alipay.com/gateway.do
alipay_sandbox = true

# 微信支付配置
wechat_app_id = your_app_id
wechat_mch_id = your_mch_id
wechat_api_key = your_api_key
wechat_notify_url = https://example.com/api/v1/payment/wechat/notify
wechat_sandbox = true

# PayPal配置
paypal_client_id = your_client_id
paypal_client_secret = your_client_secret
paypal_mode = sandbox # 可选: sandbox, live
paypal_return_url = https://example.com/payment/success
paypal_cancel_url = https://example.com/payment/cancel

# Stripe配置
stripe_api_key = your_api_key
stripe_webhook_secret = your_webhook_secret
stripe_public_key = your_public_key
stripe_success_url = https://example.com/payment/success
stripe_cancel_url = https://example.com/payment/cancel

2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:722]  开始解析配置文件: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/payment.conf
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: imported = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: imported = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: payment_methods = alipay,wechat
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: payment_methods = alipay,wechat
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: alipay_app_id = your_app_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: alipay_app_id = your_app_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: alipay_private_key = your_private_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: alipay_private_key = your_private_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: alipay_public_key = your_public_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: alipay_public_key = your_public_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: alipay_notify_url = https://example.com/api/v1/payment/alipay/notify
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: alipay_notify_url = https://example.com/api/v1/payment/alipay/notify
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: alipay_return_url = https://example.com/payment/callback
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: alipay_return_url = https://example.com/payment/callback
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: alipay_gateway = https://openapi.alipay.com/gateway.do
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: alipay_gateway = https://openapi.alipay.com/gateway.do
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: alipay_sandbox = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: alipay_sandbox = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: wechat_app_id = your_app_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: wechat_app_id = your_app_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: wechat_mch_id = your_mch_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: wechat_mch_id = your_mch_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: wechat_api_key = your_api_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: wechat_api_key = your_api_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: wechat_notify_url = https://example.com/api/v1/payment/wechat/notify
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: wechat_notify_url = https://example.com/api/v1/payment/wechat/notify
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: wechat_sandbox = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: wechat_sandbox = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: paypal_client_id = your_client_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: paypal_client_id = your_client_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: paypal_client_secret = your_client_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: paypal_client_secret = your_client_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: paypal_mode = sandbox
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: paypal_mode = sandbox
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: paypal_return_url = https://example.com/payment/success
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: paypal_return_url = https://example.com/payment/success
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: paypal_cancel_url = https://example.com/payment/cancel
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: paypal_cancel_url = https://example.com/payment/cancel
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: stripe_api_key = your_api_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: stripe_api_key = your_api_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: stripe_webhook_secret = your_webhook_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: stripe_webhook_secret = your_webhook_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: stripe_public_key = your_public_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: stripe_public_key = your_public_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: stripe_success_url = https://example.com/payment/success
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: stripe_success_url = https://example.com/payment/success
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: stripe_cancel_url = https://example.com/payment/cancel
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: stripe_cancel_url = https://example.com/payment/cancel
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:703]  关键配置解析结果:
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    runmode = dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqluser = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqlhost = *************
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqlport = 3306
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqldb = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redishost = 127.0.0.1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redisport = 6379
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redisdb = 1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:301]  添加模块配置到 include 并解析: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/payment.conf
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:297]  模块配置文件内容 (/Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/push.conf):
# 推送通知配置文件
# 此文件管理所有与推送通知服务相关的配置

# 标记配置是否已导入
imported = true

# 推送通知提供商配置
# 可选值: jpush, getui, umeng, firebase, apns, custom
push_provider = jpush

# 极光推送配置
jpush_app_key = your_app_key
jpush_master_secret = your_master_secret
jpush_production = false # 是否为生产环境

# 个推配置
getui_app_id = your_app_id
getui_app_key = your_app_key
getui_master_secret = your_master_secret
getui_production = false # 是否为生产环境

# 友盟推送配置
umeng_android_app_key = your_android_app_key
umeng_android_app_secret = your_android_app_secret
umeng_ios_app_key = your_ios_app_key
umeng_ios_app_secret = your_ios_app_secret
umeng_production = false # 是否为生产环境

# Firebase推送配置
firebase_server_key = your_server_key
firebase_sender_id = your_sender_id
firebase_service_account_json = /path/to/service-account.json

# 苹果APNS推送配置
apns_cert_file = /path/to/cert.p12
apns_cert_password = your_cert_password
apns_is_production = false # 是否为生产环境
apns_topic = your.bundle.id

# 自定义推送API配置
custom_push_api_url = https://api.custom-push.com/send
custom_push_api_method = POST
custom_push_api_headers = Content-Type:application/json,Authorization:Bearer your_token
custom_push_api_body_template = {"token":"{token}","title":"{title}","body":"{body}","data":"{data}"}
custom_push_api_success_code = 200

2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:722]  开始解析配置文件: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/push.conf
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: imported = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: imported = true
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: push_provider = jpush
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: push_provider = jpush
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: jpush_app_key = your_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: jpush_app_key = your_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: jpush_master_secret = your_master_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: jpush_master_secret = your_master_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: jpush_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: jpush_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: getui_app_id = your_app_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: getui_app_id = your_app_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: getui_app_key = your_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: getui_app_key = your_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: getui_master_secret = your_master_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: getui_master_secret = your_master_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: getui_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: getui_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: umeng_android_app_key = your_android_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: umeng_android_app_key = your_android_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: umeng_android_app_secret = your_android_app_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: umeng_android_app_secret = your_android_app_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: umeng_ios_app_key = your_ios_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: umeng_ios_app_key = your_ios_app_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: umeng_ios_app_secret = your_ios_app_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: umeng_ios_app_secret = your_ios_app_secret
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: umeng_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: umeng_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: firebase_server_key = your_server_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: firebase_server_key = your_server_key
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: firebase_sender_id = your_sender_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: firebase_sender_id = your_sender_id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: firebase_service_account_json = /path/to/service-account.json
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: firebase_service_account_json = /path/to/service-account.json
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: apns_cert_file = /path/to/cert.p12
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: apns_cert_file = /path/to/cert.p12
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: apns_cert_password = your_cert_password
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: apns_cert_password = your_cert_password
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: apns_is_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: apns_is_production = false
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: apns_topic = your.bundle.id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: apns_topic = your.bundle.id
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: custom_push_api_url = https://api.custom-push.com/send
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: custom_push_api_url = https://api.custom-push.com/send
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: custom_push_api_method = POST
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: custom_push_api_method = POST
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: custom_push_api_headers = Content-Type:application/json,Authorization:Bearer your_token
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: custom_push_api_headers = Content-Type:application/json,Authorization:Bearer your_token
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: custom_push_api_body_template = {"token":"{token}","title":"{title}","body":"{body}","data":"{data}"}
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: custom_push_api_body_template = {"token":"{token}","title":"{title}","body":"{body}","data":"{data}"}
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:195]  解析到全局配置项: custom_push_api_success_code = 200
2025/07/01 17:42:19.534 [1;44m[D][0m [config.go:207]  解析到配置项: custom_push_api_success_code = 200
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:703]  关键配置解析结果:
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    runmode = dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqluser = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqlhost = *************
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqlport = 3306
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    mysqldb = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redishost = 127.0.0.1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redisport = 6379
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:707]    redisdb = 1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:301]  添加模块配置到 include 并解析: /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/push.conf
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:318]  生成的 include 临时文件内容:
[include]
include = /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/storage.conf
include = /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/payment.conf
include = /Users/<USER>/Documents/2025works/o_-mall_backend/conf/modules/push.conf

2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:324]  成功加载 include 配置
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:509]  开始将 globalConfig 中的配置设置到 web.AppConfig
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: runmode = dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: mysqluser = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: mysqlhost = *************
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: mysqlport = 3306
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: mysqldb = o_mall_dev
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: mysqlpass = ***
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: redishost = 127.0.0.1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: redisport = 6379
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: redisdb = 1
2025/07/01 17:42:19.534 [1;34m[I][0m [config.go:542]  成功设置关键配置项: redispass = ***
2025/07/01 17:42:19.534 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: qiniu:access_key
2025/07/01 17:42:19.534 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: qiniu:secret_key
2025/07/01 17:42:19.534 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: qiniu:bucket
2025/07/01 17:42:19.534 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: qiniu:domain
2025/07/01 17:42:19.534 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: qiniu:zone
2025/07/01 17:42:19.534 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: qiniu:use_https
2025/07/01 17:42:19.534 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: storage:mode
2025/07/01 17:42:19.535 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: storage:enable_cdn
2025/07/01 17:42:19.535 [1;33m[W][0m [config.go:559]  globalConfig 中不存在模块配置项: sms:sms_provider
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: alipay_sandbox = true
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: paypal_cancel_url = https://example.com/payment/cancel
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: getui_production = false
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: firebase_server_key = your_server_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: uploadMaxSize = 10485760
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: runnerKmFee = 2
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: paypal_client_secret = yo****et
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: apns_cert_password = your_cert_password
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: copyrequestbody = true
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: alipay_gateway = https://openapi.alipay.com/gateway.do
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: umeng_android_app_key = your_android_app_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: apns_cert_file = /path/to/cert.p12
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: alipay_public_key = your_public_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: paypal_mode = sandbox
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: stripe_public_key = your_public_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: getui_master_secret = yo****et
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: api_version = v1
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: logfilepath = "./logs/o_mall.log"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:640]  成功设置其他模块配置项: cors.allow_headers = "Origin,Content-Type,Accept,Authorization,X-Requested-With,platform"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: runmodeabc = dev
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: jwtSecret = "o_mall_secret_key"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: imported = true
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: stripe_success_url = https://example.com/payment/success
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: push_provider = jpush
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: custom_push_api_body_template = {"token":"{token}","title":"{title}","body":"{body}","data":"{data}"}
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: httpport = 8181
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: custom_push_api_method = POST
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:640]  成功设置其他模块配置项: cors.allow_origins = "*"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: wechat_app_id = your_app_id
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: paypal_client_id = your_client_id
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: umeng_ios_app_key = your_ios_app_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: apns_is_production = false
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: apns_topic = your.bundle.id
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: custom_push_api_success_code = 200
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: appname = O_Mall
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: stripe_webhook_secret = yo****et
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: firebase_sender_id = your_sender_id
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: custom_push_api_url = https://api.custom-push.com/send
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: loglevel = "debug"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: jwtExpireHour = 24
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: uploadPath = "./uploads"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:640]  成功设置其他模块配置项: cors.max_age = 86400
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: alipay_return_url = https://example.com/payment/callback
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: wechat_mch_id = your_mch_id
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: wechat_sandbox = true
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: autorender = false
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: dev_mode = true
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: api_prefix = api
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: alipay_private_key = your_private_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: jpush_app_key = your_app_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: umeng_ios_app_secret = yo****et
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: umeng_production = false
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:640]  成功设置其他模块配置项: cors.allow_methods = "GET,POST,PUT,DELETE,OPTIONS,PATCH"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:640]  成功设置其他模块配置项: cors.allow_credentials = true
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: payment_methods = alipay,wechat
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: alipay_app_id = your_app_id
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: paypal_return_url = https://example.com/payment/success
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: jpush_master_secret = yo****et
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: flushapidoc = false
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: runnerBaseFee = 5
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: jpush_production = false
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: umeng_android_app_secret = yo****et
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: custom_push_api_headers = Content-Type:application/json,Authorization:Bearer your_token
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: wechat_notify_url = https://example.com/api/v1/payment/wechat/notify
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: stripe_api_key = your_api_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: firebase_service_account_json = /path/to/service-account.json
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: EnableDocs = true
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:640]  成功设置其他模块配置项: cors.expose_headers = "Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type"
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: alipay_notify_url = https://example.com/api/v1/payment/alipay/notify
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: getui_app_id = your_app_id
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: getui_app_key = your_app_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: wechat_api_key = your_api_key
2025/07/01 17:42:19.535 [1;44m[D][0m [config.go:656]  成功设置其他配置项: stripe_cancel_url = https://example.com/payment/cancel
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:660]  globalConfig 中的配置已成功设置到 web.AppConfig
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:675]  配置最终验证:
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:676]    基础配置: runmode=dev, mysqluser=o_mall_dev, redishost=127.0.0.1
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:677]    模块配置(点格式): qiniu.access_key=****, storage.mode=, sms.sms_provider=
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:679]    模块配置(冒号格式): qiniu::access_key=****, storage::mode=, sms::sms_provider=
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:125]  全局配置映射: map[EnableDocs:true alipay_app_id:your_app_id alipay_gateway:https://openapi.alipay.com/gateway.do alipay_notify_url:https://example.com/api/v1/payment/alipay/notify alipay_private_key:your_private_key alipay_public_key:your_public_key alipay_return_url:https://example.com/payment/callback alipay_sandbox:true api_prefix:api api_version:v1 apns_cert_file:/path/to/cert.p12 apns_cert_password:your_cert_password apns_is_production:false apns_topic:your.bundle.id appname:O_Mall autorender:false copyrequestbody:true cors:allow_credentials:true cors:allow_headers:"Origin,Content-Type,Accept,Authorization,X-Requested-With,platform" cors:allow_methods:"GET,POST,PUT,DELETE,OPTIONS,PATCH" cors:allow_origins:"*" cors:expose_headers:"Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type" cors:max_age:86400 custom_push_api_body_template:{"token":"{token}","title":"{title}","body":"{body}","data":"{data}"} custom_push_api_headers:Content-Type:application/json,Authorization:Bearer your_token custom_push_api_method:POST custom_push_api_success_code:200 custom_push_api_url:https://api.custom-push.com/send dev_mode:true firebase_sender_id:your_sender_id firebase_server_key:your_server_key firebase_service_account_json:/path/to/service-account.json flushapidoc:false getui_app_id:your_app_id getui_app_key:your_app_key getui_master_secret:your_master_secret getui_production:false httpport:8181 imported:true jpush_app_key:your_app_key jpush_master_secret:your_master_secret jpush_production:false jwtExpireHour:24 jwtSecret:"o_mall_secret_key" logfilepath:"./logs/o_mall.log" loglevel:"debug" mysqldb:o_mall_dev mysqlhost:************* mysqlpass:4wk2HTRWkxKxwhHX mysqlport:3306 mysqluser:o_mall_dev payment_methods:alipay,wechat paypal_cancel_url:https://example.com/payment/cancel paypal_client_id:your_client_id paypal_client_secret:your_client_secret paypal_mode:sandbox paypal_return_url:https://example.com/payment/success push_provider:jpush redisdb:1 redishost:127.0.0.1 redispass:CnCmQFCiYS34ox redisport:6379 runmode:dev runmodeabc:dev runnerBaseFee:5 runnerKmFee:2 stripe_api_key:your_api_key stripe_cancel_url:https://example.com/payment/cancel stripe_public_key:your_public_key stripe_success_url:https://example.com/payment/success stripe_webhook_secret:your_webhook_secret umeng_android_app_key:your_android_app_key umeng_android_app_secret:your_android_app_secret umeng_ios_app_key:your_ios_app_key umeng_ios_app_secret:your_ios_app_secret umeng_production:false uploadMaxSize:10485760 uploadPath:"./uploads" wechat_api_key:your_api_key wechat_app_id:your_app_id wechat_mch_id:your_mch_id wechat_notify_url:https://example.com/api/v1/payment/wechat/notify wechat_sandbox:true]
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:134]  七牛云配置最终验证: accessKey=, secretKey=, bucket=, domain=, zone=, useHTTPS=false
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:141]  数据库配置最终验证: user=o_mall_dev, host=*************, port=3306, db=o_mall_dev
2025/07/01 17:42:19.535 [1;34m[I][0m [config.go:146]  Redis配置最终验证: host=127.0.0.1, port=6379, db=1
2025/07/01 17:42:19.535 [1;34m[I][0m [main.go:359]  配置初始化完成
2025/07/01 17:42:19.535 [1;34m[I][0m [main.go:363]  主程序获取到的运行模式: dev
2025/07/01 17:42:19.636 [1;34m[I][0m [storage.go:65]  先从静态配置初始化存储提供者...
2025/07/01 17:42:19.636 [1;34m[I][0m [storage.go:219]  从静态配置文件初始化存储提供者
2025/07/01 17:42:19.636 [1;34m[I][0m [storage.go:224]  静态配置: 模式=local, 启用CDN=false
2025/07/01 17:42:19.636 [1;34m[I][0m [storage.go:231]  已初始化本地存储提供者，上传路径："./uploads"
2025/07/01 17:42:19.636 [1;34m[I][0m [main.go:377]  存储模块初始化完成
2025/07/01 17:42:19.636 [1;34m[I][0m [main.go:56]  initDatabase: 当前运行模式 = dev
2025/07/01 17:42:19.636 [1;34m[I][0m [main.go:69]  数据库配置(从配置文件): user=[o_mall_dev], host=[*************], port=[3306], dbName=[o_mall_dev]
2025/07/01 17:42:19.842 [1;34m[I][0m [main.go:205]  数据库连接成功!
2025/07/01 17:42:19.842 [1;34m[I][0m [config.go:378]  数据库初始化完成标志已设置
2025/07/01 17:42:19.842 [1;34m[I][0m [main.go:222]  数据库连接初始化完成，DSN: o_mall_dev:4wk2HTRWkxKxwhHX@tcp(*************:3306)/o_mall_dev?charset=utf8mb4&parseTime=true&loc=Local
2025/07/01 17:42:19.842 [1;34m[I][0m [redis.go:51]  Redis配置: host=[127.0.0.1], port=[6379], db=[1]
2025/07/01 17:42:19.842 [1;34m[I][0m [redis.go:52]  Redis配置键: redishost, redisport, redispass, redisdb
2025/07/01 17:42:20.013 [1;34m[I][0m [redis.go:76]  Redis客户端初始化成功
2025/07/01 17:42:20.013 [1;34m[I][0m [main.go:241]  Redis客户端初始化完成
2025/07/01 17:42:20.013 [1;34m[I][0m [main.go:229]  CORS中间件注册完成
2025/07/01 17:42:20.013 [1;34m[I][0m [init.go:23]  初始化system模块...
2025/07/01 17:42:20.013 [1;34m[I][0m [init.go:42]  注册system模块ORM模型...
2025/07/01 17:42:20.013 [1;34m[I][0m [init.go:91]  初始化system模块数据库索引...
2025/07/01 17:42:20.013 [1;34m[I][0m [community_address_init.go:21]  [InitCommunityAddressIndexes] 开始创建社区地址表索引...
2025/07/01 17:42:20.013 [1;34m[I][0m [community_address_init.go:58]  [InitCommunityAddressIndexes] 创建索引: idx_community_address_parent_id - parent_id 索引，优化树形查询
2025/07/01 17:42:20.063 [1;34m[I][0m [community_address_init.go:68]  [InitCommunityAddressIndexes] 索引 idx_community_address_parent_id 已存在，跳过创建
2025/07/01 17:42:20.063 [1;34m[I][0m [community_address_init.go:58]  [InitCommunityAddressIndexes] 创建索引: idx_community_address_status - status 索引，优化状态过滤
2025/07/01 17:42:20.113 [1;34m[I][0m [community_address_init.go:68]  [InitCommunityAddressIndexes] 索引 idx_community_address_status 已存在，跳过创建
2025/07/01 17:42:20.113 [1;34m[I][0m [community_address_init.go:58]  [InitCommunityAddressIndexes] 创建索引: idx_community_address_parent_status - parent_id + status 复合索引，优化常用查询组合
2025/07/01 17:42:20.205 [1;34m[I][0m [community_address_init.go:68]  [InitCommunityAddressIndexes] 索引 idx_community_address_parent_status 已存在，跳过创建
2025/07/01 17:42:20.205 [1;34m[I][0m [community_address_init.go:58]  [InitCommunityAddressIndexes] 创建索引: idx_community_address_parent_status_sort - parent_id + status + sort + id 复合索引，优化排序查询
2025/07/01 17:42:20.258 [1;34m[I][0m [community_address_init.go:68]  [InitCommunityAddressIndexes] 索引 idx_community_address_parent_status_sort 已存在，跳过创建
2025/07/01 17:42:20.258 [1;34m[I][0m [community_address_init.go:58]  [InitCommunityAddressIndexes] 创建索引: idx_community_address_level - level 索引，优化级别查询
2025/07/01 17:42:20.307 [1;34m[I][0m [community_address_init.go:68]  [InitCommunityAddressIndexes] 索引 idx_community_address_level 已存在，跳过创建
2025/07/01 17:42:20.307 [1;34m[I][0m [community_address_init.go:82]  [InitCommunityAddressIndexes] 社区地址表索引创建完成
2025/07/01 17:42:20.307 [1;34m[I][0m [init.go:97]  社区地址表索引初始化成功
2025/07/01 17:42:20.307 [1;34m[I][0m [init.go:106]  system模块数据库索引初始化完成
2025/07/01 17:42:20.307 [1;34m[I][0m [init.go:58]  初始化system模块默认配置数据...
2025/07/01 17:42:20.332 [1;34m[I][0m [system_config_service_impl.go:309]  [NeedInitDefaultConfig] 系统配置表已有数据，无需初始化默认配置
2025/07/01 17:42:20.332 [1;34m[I][0m [init.go:37]  system模块初始化完成
2025/07/01 17:42:20.332 [1;34m[I][0m [init.go:20]  正在初始化购物车模块...
2025/07/01 17:42:20.332 [1;34m[I][0m [init.go:33]  注册购物车数据模型
2025/07/01 17:42:20.332 [1;34m[I][0m [init.go:28]  购物车模块初始化完成
2025/07/01 17:42:20.332 [1;34m[I][0m [init.go:21]  初始化admin模块...
2025/07/01 17:42:20.332 [1;34m[I][0m [init.go:37]  注册admin模块ORM模型...
2025/07/01 17:42:20.332 [1;34m[I][0m [router.go:199]  注册管理员系统中间件...
2025/07/01 17:42:20.332 [1;34m[I][0m [init.go:49]  初始化admin模块AI配置...
2025/07/01 17:42:20.332 [1;34m[I][0m [ai_service_impl.go:55]  开始获取AI配置
2025/07/01 17:42:20.332 [1;34m[I][0m [ai_config.go:164]  确保AI配置表存在: system_ai_config
2025/07/01 17:42:20.409 [1;34m[I][0m [ai_config.go:201]  表 system_ai_config 已存在或成功创建
2025/07/01 17:42:20.409 [1;34m[I][0m [ai_service_impl.go:77]  缓存中没有AI配置，尝试从数据库获取
2025/07/01 17:42:20.409 [1;34m[I][0m [ai_config.go:87]  查询AI配置，表名: system_ai_config, 提供商: deepseek
2025/07/01 17:42:20.409 [1;34m[I][0m [ai_config.go:164]  确保AI配置表存在: system_ai_config
2025/07/01 17:42:20.486 [1;34m[I][0m [ai_config.go:201]  表 system_ai_config 已存在或成功创建
2025/07/01 17:42:20.535 [1;34m[I][0m [ai_config.go:112]  成功获取AI配置, ID: 1
2025/07/01 17:42:20.535 [1;34m[I][0m [ai_service_impl.go:106]  成功从数据库获取AI配置: deepseek-chat
2025/07/01 17:42:20.535 [1;34m[I][0m [ai_service_impl.go:119]  AI配置已缓存
2025/07/01 17:42:20.535 [1;34m[I][0m [init.go:59]  AI配置初始化成功: deepseek-chat
2025/07/01 17:42:20.535 [1;34m[I][0m [init.go:32]  admin模块初始化完成
2025/07/01 17:42:20.535 [1;34m[I][0m [init.go:20]  初始化user模块...
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:25]  ===== [用户模块] 初始化路由开始 (实例: 1751362940536052000) =====
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:26]  [用户模块] 开始初始化路由... (实例: 1751362940536052000)
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:29]  [用户模块] userRepo 创建完成: true
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:32]  [用户模块] userLogService 创建完成: true
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:35]  [用户模块] userReferralRepo 创建完成: true
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:38]  [用户模块] userReferralConfigRepo 创建完成: true
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:41]  [用户模块] referralConfigService 创建完成: true
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:45]  [用户模块] userService 创建完成: true (服务实例ID: 0x140001bf240)
2025/07/01 17:42:20.536 [1;34m[I][0m [user_controller.go:39]  [UserController] 创建控制器实例，controllerID: 0x1400011ece0
2025/07/01 17:42:20.536 [1;34m[I][0m [user_controller.go:49]  [UserController] 控制器实例创建完成, instanceAddr: 0x140001f60e0, userServiceAddr: 0x140001f61b0
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:49]  [用户模块] userController 创建完成: true (控制器实例ID: 0x140001f60e0)
2025/07/01 17:42:20.536 [1;34m[I][0m [router.go:111]  ===== [用户模块] 初始化路由完成 (实例: 1751362940536052000) =====

2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:28]  user模块初始化完成
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:20]  初始化runner模块...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:28]  runner模块初始化完成
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:20]  初始化merchant模块...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:33]  注册merchant模块ORM模型...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:28]  merchant模块初始化完成
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:20]  初始化permission模块...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:33]  注册permission模块ORM模型...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:28]  permission模块初始化完成
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:20]  正在初始化商品模块...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:33]  注册商品数据模型
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:28]  商品模块初始化完成
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:20]  初始化订单模块...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:33]  注册订单模块ORM模型...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:28]  订单模块初始化完成
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:20]  初始化payment模块...
2025/07/01 17:42:20.536 [1;34m[I][0m [init.go:33]  注册payment模块ORM模型...
2025/07/01 17:42:20.537 [1;34m[I][0m [router.go:18]  初始化支付模块路由...
2025/07/01 17:42:20.537 [1;34m[I][0m [router.go:35]  支付模块路由初始化完成
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:28]  payment模块初始化完成
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:20]  初始化配送模块...
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:33]  注册配送模块ORM模型...
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:28]  配送模块初始化完成
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:20]  初始化points模块...
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:33]  注册points模块ORM模型...
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:28]  points模块初始化完成
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:20]  初始化giftcard模块...
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:33]  注册giftcard模块ORM模型...
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:28]  giftcard模块初始化完成
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:18]  初始化install模块...
2025/07/01 17:42:20.537 [1;34m[I][0m [init.go:23]  install模块初始化完成
2025/07/01 17:42:20.610 [1;34m[I][0m [init.go:82]  索引idx_module_version已存在，跳过创建
2025/07/01 17:42:20.635 [1;34m[I][0m [init.go:104]  索引idx_module_group已存在，跳过创建
2025/07/01 17:42:20.635 [1;34m[I][0m [init.go:107]  UI配置模块初始化完成
2025/07/01 17:42:20.637 [1;34m[I][0m [init.go:24]  UI配置模块初始化完成
2025/07/01 17:42:20.637 [1;34m[I][0m [init.go:27]  初始化API文档模块...
2025/07/01 17:42:20.637 [1;34m[I][0m [init.go:38]  API文档模块初始化完成
2025/07/01 17:42:20.637 [1;34m[I][0m [init.go:23]  初始化外卖模块...
2025/07/01 17:42:20.637 [1;34m[I][0m [init.go:56]  Registering takeout models...
2025/07/01 17:42:20.639 [1;34m[I][0m [router.go:20]  初始化外卖模块路由...
2025/07/01 17:42:20.639 [1;34m[I][0m [router.go:30]  配置外卖模块路由...
2025/07/01 17:42:20.639 [1;34m[I][0m [router.go:223]  注册外卖模块中间件...
2025/07/01 17:42:20.639 [1;34m[I][0m [router.go:25]  外卖模块路由初始化完成
2025/07/01 17:42:20.639 [1;34m[I][0m [init.go:39]  注册外卖支付回调处理器...
2025/07/01 17:42:20.639 [1;34m[I][0m [init.go:48]  外卖支付回调处理器注册成功
2025/07/01 17:42:20.639 [1;34m[I][0m [init.go:34]  外卖模块初始化完成
2025/07/01 17:42:20.639 [1;34m[I][0m [init.go:22]  [Scheduler] 初始化定时执行模块
2025/07/01 17:42:20.639 [1;34m[I][0m [init.go:55]  [Scheduler] 注册定时任务模块ORM模型...
2025/07/01 17:42:20.639 [1;34m[I][0m [start_scheduler.go:15]  [StartScheduler] 启动定时任务调度器...
2025/07/01 17:42:20.639 [1;34m[I][0m [scheduler.go:79]  [Scheduler.RegisterTaskHandler] 注册任务处理器: merchant_open
2025/07/01 17:42:20.639 [1;34m[I][0m [scheduler.go:79]  [Scheduler.RegisterTaskHandler] 注册任务处理器: merchant_close
2025/07/01 17:42:20.639 [1;34m[I][0m [scheduler.go:79]  [Scheduler.RegisterTaskHandler] 注册任务处理器: order_notification
2025/07/01 17:42:20.639 [1;34m[I][0m [scheduler.go:79]  [Scheduler.RegisterTaskHandler] 注册任务处理器: order_cleanup
2025/07/01 17:42:20.639 [1;34m[I][0m [scheduler.go:84]  [Scheduler.Start] 启动定时任务调度器
2025/07/01 17:42:20.639 [1;34m[I][0m [scheduler.go:135]  [Scheduler.loadTasks] 加载持久化的任务
2025/07/01 17:42:20.639 [1;34m[I][0m [task_repository_impl.go:137]  [TaskRepositoryImpl.GetAll] 获取所有定时任务
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_0_10:00, 类型=merchant_open, 时间=2025-07-06 09:59:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_0_18:00, 类型=merchant_close, 时间=2025-07-06 18:01:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_1_10:00, 类型=merchant_open, 时间=2025-07-07 09:59:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_1_18:00, 类型=merchant_close, 时间=2025-07-07 18:01:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_2_10:00, 类型=merchant_open, 时间=2025-07-08 09:59:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_2_18:00, 类型=merchant_close, 时间=2025-07-01 18:01:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_3_10:00, 类型=merchant_open, 时间=2025-07-02 09:59:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_3_18:00, 类型=merchant_close, 时间=2025-07-02 18:01:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_4_10:00, 类型=merchant_open, 时间=2025-07-03 09:59:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_4_18:00, 类型=merchant_close, 时间=2025-07-03 18:01:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_5_10:00, 类型=merchant_open, 时间=2025-07-04 09:59:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_5_18:00, 类型=merchant_close, 时间=2025-07-04 18:01:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_6_10:00, 类型=merchant_open, 时间=2025-07-05 09:59:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_6_18:00, 类型=merchant_close, 时间=2025-07-05 18:01:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362191390175000_recurrent_next_1751362201492483000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360713322389807_recurrent_next_1751362201496634000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751359674989411000_recurrent_next_1751360405072578000_next_1751362201583092000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361073529769574_recurrent_next_1751362201587382000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361667677457591_recurrent_next_1751362201593275000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361632707046000_recurrent_next_1751362201601849000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361669857405311_recurrent_next_1751362201605185000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361133495886079_recurrent_next_1751362201610998000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361493500750182_recurrent_next_1751362201611647000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361013502661104_recurrent_next_1751362201743045000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360953506935866_recurrent_next_1751362201761620000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360893505910207_recurrent_next_1751362201797805000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360833502178402_recurrent_next_1751362201809369000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360773513254397_recurrent_next_1751362201824143000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360713777395651_recurrent_next_1751362201842183000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360712908834231_recurrent_next_1751362201850745000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360712465394767_recurrent_next_1751362201863816000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360711622992299_recurrent_next_1751362201992014000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360710733178951_recurrent_next_1751362202009634000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360710300519411_recurrent_next_1751362202029896000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360711178230061_recurrent_next_1751362202027392000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360709896837806_recurrent_next_1751362202054181000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360709368754564_recurrent_next_1751362202083954000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360708891776078_recurrent_next_1751362202090443000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360675100815882_recurrent_next_1751362202097725000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360708075035160_recurrent_next_1751362202116510000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360707643855458_recurrent_next_1751362202121238000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360707237262667_recurrent_next_1751362202218950000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360705046040786_recurrent_next_1751362202241668000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360705454167427_recurrent_next_1751362202247533000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360705910636388_recurrent_next_1751362202279721000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360675515932991_recurrent_next_1751362202293967000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360706341862347_recurrent_next_1751362202320918000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360704622032470_recurrent_next_1751362202330195000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361630365637000_recurrent_next_1751362202343895000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361624149446000_recurrent_next_1751362202364488000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361613508700345_recurrent_next_1751362202372441000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361610083416000_recurrent_next_1751362202427331000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361553505206778_recurrent_next_1751362202467495000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361668532095192_recurrent_next_1751362202494013000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361668994121624_recurrent_next_1751362202505680000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358020581489000_recurrent_next_1751358604324465000_next_1751360405487893000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361668086644537_recurrent_next_1751362202558633000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360703733198121_recurrent_next_1751362202569096000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358244225700000_recurrent_next_1751358604324260000_next_1751360405447213000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360592946566562_recurrent_next_1751362202636726402, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360662616122582_recurrent_next_1751362202650763763, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360703293093479_recurrent_next_1751362202586801000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751359674989411000_recurrent_next_1751360405072578000_next_1751362202653771251, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360702879142710_recurrent_next_1751362202611749000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358818364655000_recurrent_next_1751360405084803000_next_1751362202681439609, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358201213065000_recurrent_next_1751358604443338000_next_1751360405079592000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358130867748000_recurrent_next_1751358604699733000_next_1751360405499655000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360702454665896_recurrent_next_1751362202623819000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360701944231064_recurrent_next_1751362202633754000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358656113262000_recurrent_next_1751360405303301000_next_1751362202697625895, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751357985212942000_recurrent_next_1751358604434578000_next_1751360405282245000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358130103152000_recurrent_next_1751358604322613000_next_1751360405083620000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358662484477000_recurrent_next_1751360405304710000_next_1751362202738486217, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360675936731679_recurrent_next_1751362202683751000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360701085481880_recurrent_next_1751362202728789000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360700648643472_recurrent_next_1751362202744472000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360700216811786_recurrent_next_1751362202789762000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360706772058892_recurrent_next_1751362202806641000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360698084378461_recurrent_next_1751362202823385000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360697604216247_recurrent_next_1751362202829266000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360697188132623_recurrent_next_1751362202840550000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360676371046859_recurrent_next_1751362202859614000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360696315184436_recurrent_next_1751362202875106000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360695866929660_recurrent_next_1751362202899179000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360695425253045_recurrent_next_1751362202957358000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360694978127220_recurrent_next_1751362202995314000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360693725000071_recurrent_next_1751362203024482000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360692846259907_recurrent_next_1751362203049747000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360693297999167_recurrent_next_1751362203046462000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360691979330412_recurrent_next_1751362203086991000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360691532236656_recurrent_next_1751362203108502000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360676796671870_recurrent_next_1751362203125092000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360691120653093_recurrent_next_1751362203128283000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360690250849540_recurrent_next_1751362203182357000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360708492102531_recurrent_next_1751362203245028000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361725226471000_recurrent_next_1751362203256012000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360689392607087_recurrent_next_1751362203258200000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360677220023221_recurrent_next_1751362203284987000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361995154748000_recurrent_next_1751362203315983000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361666797810746_recurrent_next_1751362203340685000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360688553797627_recurrent_next_1751362203348931000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361667206531307_recurrent_next_1751362203354811000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361313544264243_recurrent_next_1751362203378433000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361373498672936_recurrent_next_1751362203463467000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361677725788000_recurrent_next_1751362203492940000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361670313493962_recurrent_next_1751362203505013000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361670723071182_recurrent_next_1751362203530546000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360687696505889_recurrent_next_1751362203556101000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360683319892716_recurrent_next_1751362203559537000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360687270936112_recurrent_next_1751362203597480000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360686776104992_recurrent_next_1751362203616778000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360686348142021_recurrent_next_1751362203630570000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360685891073204_recurrent_next_1751362203655027000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361669430625597_recurrent_next_1751362203677797000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360685050119716_recurrent_next_1751362203737471000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360684618645582_recurrent_next_1751362203751266000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361652702949000_recurrent_next_1751362203773582000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360684182193499_recurrent_next_1751362203769502000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360680758398247_recurrent_next_1751362203805159000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360682912116492_recurrent_next_1751362203822288000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360678125004022_recurrent_next_1751362203885058000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360677709450203_recurrent_next_1751362203879982000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360704164221008_recurrent_next_1751362203883981000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360678564052129_recurrent_next_1751362203999555000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360674654644889_recurrent_next_1751362204046770000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360674203905056_recurrent_next_1751362204055095000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360673793440374_recurrent_next_1751362204093050000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360672936376776_recurrent_next_1751362204120535000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360701516973868_recurrent_next_1751362204130249000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360698500073976_recurrent_next_1751362204147415000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360672019505326_recurrent_next_1751362204209391000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360696768269502_recurrent_next_1751362204245935000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360671198324280_recurrent_next_1751362204250265000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360670743424234_recurrent_next_1751362204254474000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360670302985064_recurrent_next_1751362204280045000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360692403310624_recurrent_next_1751362204289261000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360669415103885_recurrent_next_1751362204301671000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360690689203664_recurrent_next_1751362204337979000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360689843677691_recurrent_next_1751362204382135000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360688986041456_recurrent_next_1751362204411887000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360667314827355_recurrent_next_1751362204444185000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360666904959794_recurrent_next_1751362204488298000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360666466439288_recurrent_next_1751362204492840000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361253510699298_recurrent_next_1751362204505356000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360685466085038_recurrent_next_1751362204504988000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360665578176170_recurrent_next_1751362204511148000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360683753134891_recurrent_next_1751362204531220000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360664720512204_recurrent_next_1751362204554158000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360682043529293_recurrent_next_1751362204632451000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360662616122582_recurrent_next_1751362204679056000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360592946566562_recurrent_next_1751362204677632000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358818364655000_recurrent_next_1751360405084803000_next_1751362204718321000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360665167589038_recurrent_next_1751362204730337000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360664290363689_recurrent_next_1751362204738122000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360666058027902_recurrent_next_1751362204772621000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360673356563084_recurrent_next_1751362204882801000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360672471860662_recurrent_next_1751362204914071000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358656113262000_recurrent_next_1751360405303301000_next_1751362204925461000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360671624517708_recurrent_next_1751362204942728000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360669864321679_recurrent_next_1751362204954568000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360669004388331_recurrent_next_1751362204963613000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360668557973530_recurrent_next_1751362204984439000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360682484652899_recurrent_next_1751362205016160000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360667735738364_recurrent_next_1751362205132745000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358662484477000_recurrent_next_1751360405304710000_next_1751362205179710000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360681173448481_recurrent_next_1751362205199910000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361433519116887_recurrent_next_1751362205200376000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361666308019115_recurrent_next_1751362205231358000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361894014507000_recurrent_next_1751362205258423000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361193534679294_recurrent_next_1751362205339463000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360679433313033_recurrent_next_1751362205383469000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360680349453960_recurrent_next_1751362205384773000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360679898958085_recurrent_next_1751362205405235000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360678988819900_recurrent_next_1751362205417279000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360712068045884_recurrent_next_1751362205439968000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360681596767731_recurrent_next_1751362205470059000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361914027559000_recurrent_next_1751362205478103000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361941891601000_recurrent_next_1751362205501117000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360688126603008_recurrent_next_1751362205502545000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362369731891000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362462452799000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362544169694000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362561727095000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362590350167000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362620021739000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362628014599000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362648794608000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362730724353000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362747100712000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362760101914000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362768123624000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362895326619000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:156]  [Scheduler.loadTasks] 已加载 192 个待执行任务
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:92]  [Scheduler.Start] 定时任务调度器已启动
2025/07/01 17:42:20.739 [1;34m[I][0m [start_scheduler.go:23]  [StartScheduler] 定时任务调度器已启动
2025/07/01 17:42:20.739 [1;34m[I][0m [init.go:38]  [Scheduler] 启动订单清理调度器
2025/07/01 17:42:20.739 [1;34m[I][0m [order_cleanup_service.go:80]  [OrderCleanupService.StartOrderCleanupScheduler] 启动订单清理调度器
2025/07/01 17:42:20.739 [1;34m[I][0m [task_service.go:71]  [TaskServiceImpl.CreateRecurrentTask] 创建周期性任务: 类型=order_cleanup, cron表达式=0 */30 * * * *, 业务ID=0, 业务类型=system
2025/07/01 17:42:20.739 [1;34m[I][0m [task_repository_impl.go:81]  [TaskRepositoryImpl.Create] 创建定时任务: order_cleanup_0_1751362940739176000_recurrent
2025/07/01 17:42:20.739 [1;34m[I][0m [scheduler.go:161]  [Scheduler.scheduleLoop] 启动调度循环
2025/07/01 17:42:20.789 [1;34m[I][0m [order_cleanup_service.go:115]  [OrderCleanupService.StartOrderCleanupScheduler] 订单清理调度器启动成功，任务ID: order_cleanup_0_1751362940739176000_recurrent，Cron表达式: 0 */30 * * * *
2025/07/01 17:42:20.789 [1;34m[I][0m [init.go:50]  [Scheduler] 订单清理调度器启动成功
2025/07/01 17:42:20.789 [1;34m[I][0m [init.go:33]  [Scheduler] 定时执行模块初始化完成
2025/07/01 17:42:20.789 [1;34m[I][0m [scheduler.go:179]  [Scheduler.scheduleLoop] 收到新任务通知，重新加载任务
2025/07/01 17:42:20.789 [1;34m[I][0m [scheduler.go:135]  [Scheduler.loadTasks] 加载持久化的任务
2025/07/01 17:42:20.789 [1;34m[I][0m [task_repository_impl.go:137]  [TaskRepositoryImpl.GetAll] 获取所有定时任务
2025/07/01 17:42:20.789 [1;34m[I][0m [router.go:56]  [Chat Router] 所有服务实例已保存到服务容器
2025/07/01 17:42:20.790 [1;34m[I][0m [init.go:24]  聊天模块初始化完成
2025/07/01 17:42:20.790 [1;34m[I][0m [main.go:418]  检查并自动同步数据库表结构...
2025/07/01 17:42:20.790 [1;34m[I][0m [main.go:420]  同步表结构时的运行模式: dev
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_0_10:00, 类型=merchant_open, 时间=2025-07-06 09:59:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_0_18:00, 类型=merchant_close, 时间=2025-07-06 18:01:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_1_10:00, 类型=merchant_open, 时间=2025-07-07 09:59:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_1_18:00, 类型=merchant_close, 时间=2025-07-07 18:01:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_2_10:00, 类型=merchant_open, 时间=2025-07-08 09:59:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_2_18:00, 类型=merchant_close, 时间=2025-07-01 18:01:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_3_10:00, 类型=merchant_open, 时间=2025-07-02 09:59:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_3_18:00, 类型=merchant_close, 时间=2025-07-02 18:01:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_4_10:00, 类型=merchant_open, 时间=2025-07-03 09:59:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_4_18:00, 类型=merchant_close, 时间=2025-07-03 18:01:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_5_10:00, 类型=merchant_open, 时间=2025-07-04 09:59:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_5_18:00, 类型=merchant_close, 时间=2025-07-04 18:01:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_open_5_6_10:00, 类型=merchant_open, 时间=2025-07-05 09:59:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=merchant_close_5_6_18:00, 类型=merchant_close, 时间=2025-07-05 18:01:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362191390175000_recurrent_next_1751362201492483000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360713322389807_recurrent_next_1751362201496634000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751359674989411000_recurrent_next_1751360405072578000_next_1751362201583092000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361073529769574_recurrent_next_1751362201587382000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361667677457591_recurrent_next_1751362201593275000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361632707046000_recurrent_next_1751362201601849000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361669857405311_recurrent_next_1751362201605185000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361133495886079_recurrent_next_1751362201610998000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361493500750182_recurrent_next_1751362201611647000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361013502661104_recurrent_next_1751362201743045000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360953506935866_recurrent_next_1751362201761620000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360893505910207_recurrent_next_1751362201797805000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360833502178402_recurrent_next_1751362201809369000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360773513254397_recurrent_next_1751362201824143000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360713777395651_recurrent_next_1751362201842183000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360712908834231_recurrent_next_1751362201850745000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360712465394767_recurrent_next_1751362201863816000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360711622992299_recurrent_next_1751362201992014000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360710733178951_recurrent_next_1751362202009634000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360710300519411_recurrent_next_1751362202029896000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360711178230061_recurrent_next_1751362202027392000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360709896837806_recurrent_next_1751362202054181000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360709368754564_recurrent_next_1751362202083954000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360708891776078_recurrent_next_1751362202090443000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360675100815882_recurrent_next_1751362202097725000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360708075035160_recurrent_next_1751362202116510000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360707643855458_recurrent_next_1751362202121238000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360707237262667_recurrent_next_1751362202218950000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360705046040786_recurrent_next_1751362202241668000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360705454167427_recurrent_next_1751362202247533000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360705910636388_recurrent_next_1751362202279721000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360675515932991_recurrent_next_1751362202293967000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360706341862347_recurrent_next_1751362202320918000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360704622032470_recurrent_next_1751362202330195000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361630365637000_recurrent_next_1751362202343895000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361624149446000_recurrent_next_1751362202364488000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361613508700345_recurrent_next_1751362202372441000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361610083416000_recurrent_next_1751362202427331000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361553505206778_recurrent_next_1751362202467495000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361668532095192_recurrent_next_1751362202494013000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361668994121624_recurrent_next_1751362202505680000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358020581489000_recurrent_next_1751358604324465000_next_1751360405487893000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361668086644537_recurrent_next_1751362202558633000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360703733198121_recurrent_next_1751362202569096000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358244225700000_recurrent_next_1751358604324260000_next_1751360405447213000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360592946566562_recurrent_next_1751362202636726402, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360662616122582_recurrent_next_1751362202650763763, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360703293093479_recurrent_next_1751362202586801000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751359674989411000_recurrent_next_1751360405072578000_next_1751362202653771251, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360702879142710_recurrent_next_1751362202611749000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358818364655000_recurrent_next_1751360405084803000_next_1751362202681439609, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358201213065000_recurrent_next_1751358604443338000_next_1751360405079592000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358130867748000_recurrent_next_1751358604699733000_next_1751360405499655000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360702454665896_recurrent_next_1751362202623819000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360701944231064_recurrent_next_1751362202633754000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358656113262000_recurrent_next_1751360405303301000_next_1751362202697625895, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751357985212942000_recurrent_next_1751358604434578000_next_1751360405282245000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358130103152000_recurrent_next_1751358604322613000_next_1751360405083620000_next, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358662484477000_recurrent_next_1751360405304710000_next_1751362202738486217, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360675936731679_recurrent_next_1751362202683751000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360701085481880_recurrent_next_1751362202728789000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360700648643472_recurrent_next_1751362202744472000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360700216811786_recurrent_next_1751362202789762000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360706772058892_recurrent_next_1751362202806641000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360698084378461_recurrent_next_1751362202823385000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360697604216247_recurrent_next_1751362202829266000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360697188132623_recurrent_next_1751362202840550000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360676371046859_recurrent_next_1751362202859614000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360696315184436_recurrent_next_1751362202875106000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360695866929660_recurrent_next_1751362202899179000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360695425253045_recurrent_next_1751362202957358000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360694978127220_recurrent_next_1751362202995314000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360693725000071_recurrent_next_1751362203024482000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360692846259907_recurrent_next_1751362203049747000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360693297999167_recurrent_next_1751362203046462000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360691979330412_recurrent_next_1751362203086991000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360691532236656_recurrent_next_1751362203108502000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360676796671870_recurrent_next_1751362203125092000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360691120653093_recurrent_next_1751362203128283000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360690250849540_recurrent_next_1751362203182357000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360708492102531_recurrent_next_1751362203245028000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361725226471000_recurrent_next_1751362203256012000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360689392607087_recurrent_next_1751362203258200000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360677220023221_recurrent_next_1751362203284987000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361995154748000_recurrent_next_1751362203315983000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361666797810746_recurrent_next_1751362203340685000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360688553797627_recurrent_next_1751362203348931000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361667206531307_recurrent_next_1751362203354811000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361313544264243_recurrent_next_1751362203378433000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361373498672936_recurrent_next_1751362203463467000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361677725788000_recurrent_next_1751362203492940000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361670313493962_recurrent_next_1751362203505013000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361670723071182_recurrent_next_1751362203530546000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360687696505889_recurrent_next_1751362203556101000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360683319892716_recurrent_next_1751362203559537000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360687270936112_recurrent_next_1751362203597480000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360686776104992_recurrent_next_1751362203616778000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360686348142021_recurrent_next_1751362203630570000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360685891073204_recurrent_next_1751362203655027000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361669430625597_recurrent_next_1751362203677797000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360685050119716_recurrent_next_1751362203737471000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360684618645582_recurrent_next_1751362203751266000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361652702949000_recurrent_next_1751362203773582000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360684182193499_recurrent_next_1751362203769502000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360680758398247_recurrent_next_1751362203805159000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360682912116492_recurrent_next_1751362203822288000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360678125004022_recurrent_next_1751362203885058000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360677709450203_recurrent_next_1751362203879982000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360704164221008_recurrent_next_1751362203883981000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360678564052129_recurrent_next_1751362203999555000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360674654644889_recurrent_next_1751362204046770000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360674203905056_recurrent_next_1751362204055095000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360673793440374_recurrent_next_1751362204093050000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360672936376776_recurrent_next_1751362204120535000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360701516973868_recurrent_next_1751362204130249000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360698500073976_recurrent_next_1751362204147415000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360672019505326_recurrent_next_1751362204209391000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360696768269502_recurrent_next_1751362204245935000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360671198324280_recurrent_next_1751362204250265000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360670743424234_recurrent_next_1751362204254474000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360670302985064_recurrent_next_1751362204280045000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360692403310624_recurrent_next_1751362204289261000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360669415103885_recurrent_next_1751362204301671000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360690689203664_recurrent_next_1751362204337979000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360689843677691_recurrent_next_1751362204382135000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360688986041456_recurrent_next_1751362204411887000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360667314827355_recurrent_next_1751362204444185000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360666904959794_recurrent_next_1751362204488298000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360666466439288_recurrent_next_1751362204492840000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361253510699298_recurrent_next_1751362204505356000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360685466085038_recurrent_next_1751362204504988000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360665578176170_recurrent_next_1751362204511148000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360683753134891_recurrent_next_1751362204531220000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360664720512204_recurrent_next_1751362204554158000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360682043529293_recurrent_next_1751362204632451000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360662616122582_recurrent_next_1751362204679056000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360592946566562_recurrent_next_1751362204677632000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358818364655000_recurrent_next_1751360405084803000_next_1751362204718321000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360665167589038_recurrent_next_1751362204730337000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360664290363689_recurrent_next_1751362204738122000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360666058027902_recurrent_next_1751362204772621000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360673356563084_recurrent_next_1751362204882801000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360672471860662_recurrent_next_1751362204914071000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358656113262000_recurrent_next_1751360405303301000_next_1751362204925461000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360671624517708_recurrent_next_1751362204942728000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360669864321679_recurrent_next_1751362204954568000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360669004388331_recurrent_next_1751362204963613000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360668557973530_recurrent_next_1751362204984439000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360682484652899_recurrent_next_1751362205016160000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360667735738364_recurrent_next_1751362205132745000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751358662484477000_recurrent_next_1751360405304710000_next_1751362205179710000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360681173448481_recurrent_next_1751362205199910000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361433519116887_recurrent_next_1751362205200376000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361666308019115_recurrent_next_1751362205231358000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361894014507000_recurrent_next_1751362205258423000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361193534679294_recurrent_next_1751362205339463000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360679433313033_recurrent_next_1751362205383469000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360680349453960_recurrent_next_1751362205384773000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360679898958085_recurrent_next_1751362205405235000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360678988819900_recurrent_next_1751362205417279000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360712068045884_recurrent_next_1751362205439968000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360681596767731_recurrent_next_1751362205470059000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361914027559000_recurrent_next_1751362205478103000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751361941891601000_recurrent_next_1751362205501117000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751360688126603008_recurrent_next_1751362205502545000, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362369731891000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362462452799000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362544169694000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362561727095000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362590350167000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362620021739000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362628014599000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362648794608000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362730724353000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362747100712000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362760101914000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362768123624000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362895326619000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:151]  [Scheduler.loadTasks] 加载任务: ID=order_cleanup_0_1751362940739176000_recurrent, 类型=order_cleanup, 时间=2025-07-01 18:00:00 +0800 CST
2025/07/01 17:42:20.838 [1;34m[I][0m [scheduler.go:156]  [Scheduler.loadTasks] 已加载 193 个待执行任务
table `upload_files` already exists, skip
table `api_info` already exists, skip
table `dto_info` already exists, skip
table `controller_info` already exists, skip
table `chat_blacklist` already exists, skip
table `chat_friend` already exists, skip
table `chat_friend_request` already exists, skip
table `chat_group` already exists, skip
table `chat_group_member` already exists, skip
table `chat_message` already exists, skip
2025/07/01 17:42:21.637 [1;34m[I][0m [storage.go:76]  尝试从数据库加载存储配置...
table `chat_session` already exists, skip
2025/07/01 17:42:21.661 [1;34m[I][0m [storage.go:97]  从数据库成功加载存储配置，正在重新初始化存储提供者...
2025/07/01 17:42:21.661 [1;34m[I][0m [storage.go:119]  初始化存储提供者，存储模式: qiniu, CDN启用: false
2025/07/01 17:42:21.661 [1;34m[I][0m [storage.go:167]  正在获取七牛云存储配置...
2025/07/01 17:42:21.661 [1;34m[I][0m [storage.go:181]  七牛云配置验证通过: AccessKey=UK****hk, Bucket=omallimg, Domain=omimg.qwyx.shop, Zone=z2
2025/07/01 17:42:21.661 [1;34m[I][0m [qiniu_storage.go:47]  七牛云配置详情 -> AccessKey:UK****hk SecretKey:iG****qI Bucket:omallimg Domain:omimg.qwyx.shop Zone:z2 HTTPS:false
2025/07/01 17:42:21.661 [1;34m[I][0m [storage.go:192]  已初始化七牛云存储提供者，Bucket：omallimg
table `takeout_refunds` already exists, skip
table `grid_info` already exists, skip
table `ui_config` already exists, skip
table `ui_config_grid_relation` already exists, skip
table `system_config` already exists, skip
table `system_notice` already exists, skip
table `system_sms_config` already exists, skip
table `system_upload_config` already exists, skip
table `system_ai_config` already exists, skip
table `system_community_address` already exists, skip
table `system_file_usage_config` already exists, skip
table `carts` already exists, skip
table `cart_items` already exists, skip
table `cart_item_logs` already exists, skip
table `admin` already exists, skip
table `admin_log` already exists, skip
table `users` already exists, skip
table `user_addresses` already exists, skip
table `user_account` already exists, skip
table `user_account_transaction` already exists, skip
table `referral_configs` already exists, skip
table `user_referrals` already exists, skip
table `runner` already exists, skip
table `runner_order` already exists, skip
table `runner_location` already exists, skip
table `runner_apply` already exists, skip
table `runner_income_log` already exists, skip
table `runner_withdrawal` already exists, skip
table `merchant` already exists, skip
table `merchant_log` already exists, skip
table `merchant_category` already exists, skip
table `merchant_settlement` already exists, skip
table `merchant_document` already exists, skip
table `permissions` already exists, skip
table `roles` already exists, skip
table `role_permissions` already exists, skip
table `user_roles` already exists, skip
table `product` already exists, skip
table `product_sku` already exists, skip
table `product_images` already exists, skip
table `product_category` already exists, skip
table `product_specification` already exists, skip
table `product_comment` already exists, skip
table `order` already exists, skip
table `order_item` already exists, skip
table `order_address` already exists, skip
table `order_log` already exists, skip
table `order_payment` already exists, skip
table `payment` already exists, skip
table `payment_account` already exists, skip
table `refund` already exists, skip
table `payment_log` already exists, skip
table `payment_bill` already exists, skip
table `delivery_area` already exists, skip
table `delivery_method` already exists, skip
table `delivery_rule` already exists, skip
table `delivery_time_slot` already exists, skip
table `delivery_order` already exists, skip
table `delivery_tracking` already exists, skip
table `points_account` already exists, skip
table `points_transaction` already exists, skip
table `points_rule` already exists, skip
table `points_exchange` already exists, skip
table `gift_card` already exists, skip
table `gift_card_transaction` already exists, skip
table `gift_card_batch` already exists, skip
table `takeout_food` already exists, skip
table `takeout_food_variant` already exists, skip
table `takeout_combo_item` already exists, skip
table `takeout_combo_option` already exists, skip
table `takeout_category` already exists, skip
table `takeout_order_rating` already exists, skip
table `takeout_order_item` already exists, skip
table `takeout_order_log` already exists, skip
table `takeout_order_extension` already exists, skip
table `global_category` already exists, skip
table `takeout_coupon` already exists, skip
table `takeout_cart_item` already exists, skip
table `takeout_cart_item_log` already exists, skip
table `takeout_promotion` already exists, skip
table `scheduler_task` already exists, skip
2025/07/01 17:42:28.734 [1;34m[I][0m [main.go:427]  数据库表结构同步完成
2025/07/01 17:42:28.734 [1;34m[I][0m [sms.go:62]  开始初始化短信服务 (使用数据库配置)...
2025/07/01 17:42:28.758 [1;34m[I][0m [sms.go:81]  成功获取短信配置: Provider=submail, AccessKey=80121
2025/07/01 17:42:28.758 [1;34m[I][0m [sms.go:115]  已初始化赛邮短信服务 (使用数据库配置)
2025/07/01 17:42:28.758 [1;34m[I][0m [sms.go:131]  短信服务提供商 'submail' 初始化完成。
2025/07/01 17:42:28.758 [1;34m[I][0m [init.go:27]  初始化API文档模块...
2025/07/01 17:42:28.758 [1;34m[I][0m [init.go:38]  API文档模块初始化完成
2025/07/01 17:42:28.758 [1;34m[I][0m [main.go:459]  O_Mall 服务准备启动...
2025/07/01 17:42:28.762 [1;34m[I][0m [server.go:280]  http server Running on http://:8181
signal: interrupt
