/**
 * 路由配置文件
 *
 * 该文件负责注册系统所有的API路由，将请求映射到对应的控制器处理函数。
 * 所有模块的路由都集中在此处注册，保持路由结构清晰。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	// 导入系统模块
	//"o_mall_backend/modules/runner"
	"o_mall_backend/modules/favorites"
	"o_mall_backend/modules/system"

	// 导入外卖模块
	"o_mall_backend/modules/takeout"
	// 导入购物车模块
	"o_mall_backend/modules/cart"
	// 导入配送模块
	"o_mall_backend/modules/delivery"
	// 导入安装模块
	"o_mall_backend/modules/install"
	// 导入积分模块
	"o_mall_backend/modules/points"
	// 导入礼品卡模块
	"o_mall_backend/modules/giftcard"

	// 导入跑腿模块
	"o_mall_backend/modules/runner/routers"
	// 导入管理员模块
	"o_mall_backend/modules/admin"
	// 导入用户模块
	"o_mall_backend/modules/user"
	// 导入商家模块
	"o_mall_backend/modules/merchant"
	// 导入权限模块
	"o_mall_backend/modules/permission"
	// 导入商品模块
	"o_mall_backend/modules/product"
	// 导入订单模块
	"o_mall_backend/modules/order"
	// 导入支付模块
	"o_mall_backend/modules/payment"
	// 导入UI配置模块
	"o_mall_backend/modules/ui_config"
	// 导入API文档模块
	"o_mall_backend/modules/apidoc"
	// 导入安装模块
	"o_mall_backend/modules/scheduler"
	// 导入聊天模块
	"o_mall_backend/modules/chat"
	// 导入跑腿模块
	"o_mall_backend/modules/runner"
	// 导入历史记录模块
	"o_mall_backend/modules/history"
	// 导入收藏模块
)

// Init 初始化所有路由
func Init() {

	// 初始化系统模块
	system.Init()

	// 初始化购物车模块
	cart.Init()

	// 初始化跑腿模块路由
	routers.InitRouters()

	// 初始化管理员模块
	admin.Init()

	// 初始化用户模块
	user.Init()

	// 初始化跑腿模块
	runner.Init()

	// 初始化商家模块
	merchant.Init()

	// 初始化权限模块
	permission.Init()

	// 初始化商品模块
	product.Init()

	// 初始化订单模块
	order.Init()

	// 初始化支付模块
	payment.Init()

	//初始化配送模块
	delivery.Init()

	//初始化积分模块
	points.Init()

	//初始化礼品卡模块
	giftcard.Init()

	//初始化安装模块
	install.Init()

	//初始化UI配置模块
	ui_config.Init()

	//初始化API文档模块
	apidoc.Init()

	takeout.Init()

	scheduler.Init()

	chat.Init()

	// 初始化历史记录模块
	history.Init()

	// 初始化收藏模块
	favorites.Init()

	// 注意：各个模块已经在自己的Init()函数中注册了具体的路由
	// 这里不需要再注册空的命名空间，避免路由冲突

	// 注册API文档
	if web.BConfig.RunMode == "dev" {
		web.Router("/swagger/*", &web.Controller{})
	}
}
