#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外卖食品过滤问题分析脚本

根据调试结果，发现数据库中有3条记录，但API只返回1条
这个脚本专门分析可能的过滤条件和缓存问题

使用方法:
    python3 scripts/analyze_food_filter_issue.py
"""

import pymysql
import sys
from typing import List, Dict, Any

# 数据库配置信息
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': '4wk2HTRWkxKxwhHX',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

def connect_database():
    """
    连接到MySQL数据库
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def analyze_query_conditions(connection, merchant_id=1):
    """
    分析各种查询条件下的结果
    """
    print(f"\n🔍 分析查询条件 (商家ID: {merchant_id})")
    print("=" * 80)
    
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 1. 基础查询 - 所有食品
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s ORDER BY id"
        cursor.execute(sql, (merchant_id,))
        all_foods = cursor.fetchall()
        print(f"1. 所有食品 (merchant_id = {merchant_id}): {len(all_foods)} 条")
        
        # 2. 按状态过滤
        status_queries = {
            "上架 (status = 1)": "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 1",
            "下架 (status = 2)": "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 2",
            "售罄 (status = 3)": "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 3",
            "草稿 (status = 4)": "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 4",
        }
        
        for desc, sql in status_queries.items():
            cursor.execute(sql, (merchant_id,))
            results = cursor.fetchall()
            print(f"2. {desc}: {len(results)} 条")
            if results:
                for food in results:
                    print(f"   - ID: {food['id']}, 名称: {food['name']}, 状态: {food['status']}")
        
        # 3. 按审核状态过滤
        audit_queries = {
            "审核通过 (audit_status = 1)": "SELECT * FROM takeout_food WHERE merchant_id = %s AND audit_status = 1",
            "待审核 (audit_status = 0)": "SELECT * FROM takeout_food WHERE merchant_id = %s AND audit_status = 0",
            "审核拒绝 (audit_status = 2)": "SELECT * FROM takeout_food WHERE merchant_id = %s AND audit_status = 2",
        }
        
        for desc, sql in audit_queries.items():
            cursor.execute(sql, (merchant_id,))
            results = cursor.fetchall()
            print(f"3. {desc}: {len(results)} 条")
            if results:
                for food in results:
                    print(f"   - ID: {food['id']}, 名称: {food['name']}, 审核状态: {food['audit_status']}")
        
        # 4. 组合条件 - 上架且审核通过
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 1 AND audit_status = 1"
        cursor.execute(sql, (merchant_id,))
        results = cursor.fetchall()
        print(f"4. 上架且审核通过 (status = 1 AND audit_status = 1): {len(results)} 条")
        if results:
            for food in results:
                print(f"   - ID: {food['id']}, 名称: {food['name']}")
        
        # 5. 检查sold_out字段
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s AND sold_out = 0"
        cursor.execute(sql, (merchant_id,))
        results = cursor.fetchall()
        print(f"5. 未售罄 (sold_out = 0): {len(results)} 条")
        
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s AND sold_out = 1"
        cursor.execute(sql, (merchant_id,))
        results = cursor.fetchall()
        print(f"6. 已售罄 (sold_out = 1): {len(results)} 条")
        
        # 7. 模拟前端可能的查询条件
        print(f"\n🎯 模拟前端查询条件:")
        
        # 可能的查询1: 只返回上架商品
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 1 ORDER BY sort_order ASC LIMIT 10 OFFSET 0"
        cursor.execute(sql, (merchant_id,))
        results = cursor.fetchall()
        print(f"查询1 - 只返回上架商品 (分页): {len(results)} 条")
        
        # 可能的查询2: 上架且未售罄
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 1 AND sold_out = 0 ORDER BY sort_order ASC LIMIT 10 OFFSET 0"
        cursor.execute(sql, (merchant_id,))
        results = cursor.fetchall()
        print(f"查询2 - 上架且未售罄 (分页): {len(results)} 条")
        
        # 可能的查询3: 上架、审核通过且未售罄
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 1 AND audit_status = 1 AND sold_out = 0 ORDER BY sort_order ASC LIMIT 10 OFFSET 0"
        cursor.execute(sql, (merchant_id,))
        results = cursor.fetchall()
        print(f"查询3 - 上架、审核通过且未售罄 (分页): {len(results)} 条")
        
        return all_foods

def check_table_structure(connection):
    """
    检查表结构，特别关注可能影响查询的字段
    """
    print(f"\n🏗️  检查表结构")
    print("=" * 80)
    
    with connection.cursor() as cursor:
        sql = "DESCRIBE takeout_food"
        cursor.execute(sql)
        columns = cursor.fetchall()
        
        important_fields = ['status', 'audit_status', 'sold_out', 'merchant_id', 'category_id']
        
        print(f"重要字段信息:")
        for column in columns:
            field, type_info, null, key, default, extra = column
            if field in important_fields:
                print(f"  {field}: {type_info}, 默认值: {default}, 是否为空: {null}")

def analyze_default_query_behavior(connection, merchant_id=1):
    """
    分析默认查询行为 - 模拟Go代码中的查询逻辑
    """
    print(f"\n🔧 分析默认查询行为")
    print("=" * 80)
    
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 模拟Go代码中的查询逻辑
        # 基于代码分析，当status = -1时，不会添加status条件
        
        print(f"模拟前端请求参数:")
        print(f"  merchant_id: {merchant_id}")
        print(f"  category_id: 0 (不过滤分类)")
        print(f"  keyword: '' (无关键词)")
        print(f"  status: -1 (不过滤状态)")
        print(f"  page: 1")
        print(f"  page_size: 10")
        
        # 构建查询 - 只有merchant_id条件
        sql = """
        SELECT * FROM takeout_food 
        WHERE merchant_id = %s 
        ORDER BY sort_order ASC 
        LIMIT 10 OFFSET 0
        """
        
        cursor.execute(sql, (merchant_id,))
        results = cursor.fetchall()
        
        print(f"\n查询结果: {len(results)} 条")
        for i, food in enumerate(results, 1):
            print(f"  {i}. ID: {food['id']}, 名称: {food['name']}, 状态: {food['status']}, 审核: {food['audit_status']}, 售罄: {food['sold_out']}")
        
        # 检查总数
        count_sql = "SELECT COUNT(*) as total FROM takeout_food WHERE merchant_id = %s"
        cursor.execute(count_sql, (merchant_id,))
        count_result = cursor.fetchone()
        print(f"\n总数: {count_result['total']} 条")
        
        return results

def main():
    """
    主函数
    """
    print("🔍 外卖食品过滤问题深度分析")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 检查表结构
        check_table_structure(connection)
        
        # 分析查询条件
        merchant_id = 1
        all_foods = analyze_query_conditions(connection, merchant_id)
        
        # 分析默认查询行为
        default_results = analyze_default_query_behavior(connection, merchant_id)
        
        print(f"\n" + "=" * 80)
        print(f"📊 分析总结:")
        print(f"数据库总记录数: {len(all_foods)}")
        print(f"默认查询返回数: {len(default_results)}")
        
        if len(all_foods) != len(default_results):
            print(f"⚠️  发现数据不一致！")
            print(f"\n可能的原因:")
            print(f"1. 缓存验证逻辑过滤了某些记录")
            print(f"2. 前端传递了隐含的过滤参数")
            print(f"3. 商家ID获取不正确")
            print(f"4. 缓存中的数据与数据库不一致")
        else:
            print(f"✅ 数据一致")
        
        print(f"\n💡 建议:")
        print(f"1. 检查前端实际传递的参数")
        print(f"2. 检查商家ID的获取逻辑")
        print(f"3. 清理缓存后重新测试")
        print(f"4. 检查缓存验证服务的过滤逻辑")
        
    finally:
        connection.close()
        print(f"\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()