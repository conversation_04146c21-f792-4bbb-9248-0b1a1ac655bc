#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家推荐接口深度分析脚本

此脚本用于分析 /api/v1/merchant/recommended-merchants 接口
查询merchant表数据，分析推荐商家逻辑，解决输出记录不符合预期的问题

使用方法:
    python3 scripts/analyze_merchant_recommended_api.py

依赖:
    pip install pymysql requests
"""

import pymysql
import sys
import json
import requests
from typing import List, Dict, Any

# 数据库配置信息
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': '4wk2HTRWkxKxwhHX',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

# API配置
API_BASE_URL = "http://localhost:8181"
RECOMMENDED_MERCHANTS_API = "/api/v1/merchant/recommended-merchants"

def connect_database():
    """
    连接到MySQL数据库
    
    Returns:
        pymysql.Connection: 数据库连接对象
    
    Raises:
        Exception: 数据库连接失败时抛出异常
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def analyze_merchant_table(connection):
    """
    分析merchant商家表的数据
    
    Args:
        connection: 数据库连接对象
    """
    print(f"\n🏪 分析merchant商家表数据")
    print("=" * 80)
    
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 1. 查看表结构
        print(f"\n1. merchant表结构:")
        cursor.execute("DESCRIBE merchant")
        columns = cursor.fetchall()
        for column in columns:
            field, type_info, null, key, default, extra = column
            print(f"   {field}: {type_info}, 默认值: {default}, 是否为空: {null}")
        
        # 2. 查询所有商家数据
        print(f"\n2. 所有商家数据:")
        sql = "SELECT * FROM merchant ORDER BY id"
        cursor.execute(sql)
        all_merchants = cursor.fetchall()
        print(f"   总商家数: {len(all_merchants)}")
        
        for merchant in all_merchants:
            print(f"   商家ID: {merchant['id']}, 名称: {merchant['name']}, 状态: {merchant.get('status', 'N/A')}, 营业状态: {merchant.get('operation_status', 'N/A')}, 推荐: {merchant.get('is_recommended', 'N/A')}")
        
        # 3. 按状态分组统计
        print(f"\n3. 按状态分组统计:")
        status_sql = "SELECT status, COUNT(*) as count FROM merchant GROUP BY status"
        cursor.execute(status_sql)
        status_stats = cursor.fetchall()
        for stat in status_stats:
            print(f"   状态 {stat['status']}: {stat['count']} 个商家")
        
        # 4. 按营业状态分组统计
        print(f"\n4. 按营业状态分组统计:")
        operation_sql = "SELECT operation_status, COUNT(*) as count FROM merchant GROUP BY operation_status"
        cursor.execute(operation_sql)
        operation_stats = cursor.fetchall()
        for stat in operation_stats:
            print(f"   营业状态 {stat['operation_status']}: {stat['count']} 个商家")
        
        # 5. 按推荐状态分组统计
        print(f"\n5. 按推荐状态分组统计:")
        recommended_sql = "SELECT is_recommended, COUNT(*) as count FROM merchant GROUP BY is_recommended"
        cursor.execute(recommended_sql)
        recommended_stats = cursor.fetchall()
        for stat in recommended_stats:
            print(f"   推荐状态 {stat['is_recommended']}: {stat['count']} 个商家")
        
        # 6. 查询符合推荐条件的商家
        print(f"\n6. 符合推荐条件的商家分析:")
        
        # 只看推荐商家
        recommended_only_sql = "SELECT * FROM merchant WHERE is_recommended = 1 ORDER BY id"
        cursor.execute(recommended_only_sql)
        recommended_merchants = cursor.fetchall()
        print(f"   推荐商家总数: {len(recommended_merchants)}")
        
        for merchant in recommended_merchants:
            print(f"   - ID: {merchant['id']}, 名称: {merchant['name']}, 状态: {merchant.get('status', 'N/A')}, 营业状态: {merchant.get('operation_status', 'N/A')}")
        
        # 推荐且营业中的商家
        active_recommended_sql = "SELECT * FROM merchant WHERE is_recommended = 1 AND operation_status = 1 ORDER BY id"
        cursor.execute(active_recommended_sql)
        active_recommended = cursor.fetchall()
        print(f"\n   推荐且营业中的商家: {len(active_recommended)} 个")
        
        for merchant in active_recommended:
            print(f"   - ID: {merchant['id']}, 名称: {merchant['name']}, 状态: {merchant.get('status', 'N/A')}")
        
        return all_merchants, recommended_merchants, active_recommended

def test_recommended_merchants_api():
    """
    测试推荐商家API
    """
    print(f"\n🔧 测试推荐商家API")
    print("=" * 80)
    
    url = f"{API_BASE_URL}{RECOMMENDED_MERCHANTS_API}"
    
    try:
        # 发送GET请求
        response = requests.get(url, timeout=10)
        print(f"\n请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 分析响应数据
            if 'data' in data:
                merchants = data['data']
                print(f"\n📊 API返回分析:")
                print(f"   数据类型: {type(merchants)}")
                
                if isinstance(merchants, list):
                    print(f"   返回商家数量: {len(merchants)}")
                    
                    for i, merchant in enumerate(merchants, 1):
                        if isinstance(merchant, dict):
                            print(f"   {i}. ID: {merchant.get('id', 'N/A')}, 名称: {merchant.get('name', 'N/A')}, 营业状态: {merchant.get('operation_status', 'N/A')}, 推荐: {merchant.get('is_recommended', 'N/A')}")
                        else:
                            print(f"   {i}. 数据格式异常: {type(merchant)} - {merchant}")
                else:
                    print(f"   数据不是列表格式: {merchants}")
            
            return data
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return None

def compare_database_and_api(db_recommended, api_data):
    """
    对比数据库和API返回的数据
    
    Args:
        db_recommended: 数据库中的推荐商家数据
        api_data: API返回的数据
    """
    print(f"\n🔍 对比数据库和API数据")
    print("=" * 80)
    
    if not api_data or 'data' not in api_data:
        print(f"❌ API数据无效，无法对比")
        return
    
    api_merchants = api_data['data']
    
    print(f"\n数据对比:")
    print(f"   数据库推荐商家数量: {len(db_recommended)}")
    
    # 检查API数据格式
    if not isinstance(api_merchants, list):
        print(f"   ❌ API返回数据格式错误，不是列表: {type(api_merchants)}")
        print(f"   API数据内容: {api_merchants}")
        return
    
    # 过滤出有效的字典对象
    valid_api_merchants = [m for m in api_merchants if isinstance(m, dict)]
    invalid_api_data = [m for m in api_merchants if not isinstance(m, dict)]
    
    print(f"   API返回商家数量: {len(api_merchants)}")
    print(f"   有效商家数据: {len(valid_api_merchants)}")
    
    if invalid_api_data:
        print(f"   ⚠️  发现无效数据: {len(invalid_api_data)} 条")
        for i, invalid in enumerate(invalid_api_data[:3]):  # 只显示前3条
            print(f"     {i+1}. {type(invalid)} - {invalid}")
    
    if len(db_recommended) != len(valid_api_merchants):
        print(f"\n⚠️  数据不一致！")
        
        # 分析差异
        db_ids = {merchant['id'] for merchant in db_recommended}
        api_ids = {merchant.get('id') for merchant in valid_api_merchants if merchant.get('id')}
        
        missing_in_api = db_ids - api_ids
        extra_in_api = api_ids - db_ids
        
        if missing_in_api:
            print(f"   数据库中有但API中缺失的商家ID: {missing_in_api}")
        
        if extra_in_api:
            print(f"   API中有但数据库中不应该有的商家ID: {extra_in_api}")
    else:
        print(f"✅ 数据数量一致")
    
    # 检查每个商家的详细信息
    print(f"\n详细对比:")
    for db_merchant in db_recommended:
        db_id = db_merchant['id']
        api_merchant = next((m for m in valid_api_merchants if m.get('id') == db_id), None)
        
        if api_merchant:
            print(f"   商家ID {db_id}: ✅ 在API中找到")
            # 检查关键字段
            if db_merchant.get('operation_status') != api_merchant.get('operation_status'):
                print(f"     ⚠️  营业状态不一致: DB={db_merchant.get('operation_status')}, API={api_merchant.get('operation_status')}")
        else:
            print(f"   商家ID {db_id}: ❌ 在API中缺失")

def analyze_api_logic():
    """
    分析API逻辑，提供调试建议
    """
    print(f"\n💡 API逻辑分析和调试建议")
    print("=" * 80)
    
    print(f"\n根据之前的分析，推荐商家API可能存在以下问题:")
    print(f"\n1. 🔍 过滤条件问题:")
    print(f"   - 检查是否正确应用了 is_recommended = 1 条件")
    print(f"   - 检查是否正确应用了 operation_status = 1 条件")
    print(f"   - 检查是否有其他隐含的过滤条件")
    
    print(f"\n2. 🏗️  SQL查询问题:")
    print(f"   - 检查JOIN条件是否正确")
    print(f"   - 检查ORDER BY和LIMIT条件")
    print(f"   - 检查是否有子查询影响结果")
    
    print(f"\n3. 🔄 缓存问题:")
    print(f"   - 检查是否使用了过期的缓存数据")
    print(f"   - 检查缓存键是否正确")
    print(f"   - 尝试清除缓存后重新测试")
    
    print(f"\n4. 🐛 代码逻辑问题:")
    print(f"   - 检查controller中的参数处理")
    print(f"   - 检查service中的业务逻辑")
    print(f"   - 检查repository中的数据访问逻辑")
    
    print(f"\n建议的调试步骤:")
    print(f"1. 在merchant_controller.go中添加日志，记录查询参数")
    print(f"2. 在merchant_service.go中添加日志，记录SQL查询和结果")
    print(f"3. 在merchant_repository.go中添加日志，记录实际执行的SQL")
    print(f"4. 检查数据库连接和事务状态")
    print(f"5. 使用数据库查询日志确认实际执行的SQL")

def main():
    """
    主函数
    """
    print("🚀 开始分析推荐商家接口问题...")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 分析merchant表数据
        all_merchants, recommended_merchants, active_recommended = analyze_merchant_table(connection)
        
        # 测试API
        print(f"\n🔍 开始测试推荐商家API...")
        api_data = test_recommended_merchants_api()
        
        if api_data is None:
            print(f"❌ API测试失败，无法获取数据")
        else:
            print(f"✅ API测试成功")
        
        # 对比数据
        print(f"\n🔄 开始对比数据库和API数据...")
        compare_database_and_api(active_recommended, api_data)
        
        # 分析API逻辑
        analyze_api_logic()
        
        print(f"\n" + "=" * 80)
        print(f"📊 分析总结:")
        print(f"\n数据库统计:")
        print(f"   总商家数: {len(all_merchants)}")
        print(f"   推荐商家数: {len(recommended_merchants)}")
        print(f"   推荐且营业中的商家数: {len(active_recommended)}")
        
        if api_data and 'data' in api_data:
            api_merchants = api_data['data']
            api_count = len(api_merchants) if isinstance(api_merchants, list) else 0
            valid_count = len([m for m in api_merchants if isinstance(m, dict)]) if isinstance(api_merchants, list) else 0
            
            print(f"\nAPI返回:")
            print(f"   返回商家数: {api_count}")
            print(f"   有效商家数: {valid_count}")
            
            if valid_count != len(active_recommended):
                print(f"\n🔥 问题确认: API返回有效数量({valid_count})与预期数量({len(active_recommended)})不符！")
                print(f"\n需要检查的重点:")
                print(f"1. merchant_controller.go 中的查询逻辑")
                print(f"2. merchant_service.go 中的过滤条件")
                print(f"3. merchant_repository.go 中的SQL查询")
                print(f"4. 缓存机制是否影响了结果")
                print(f"5. API返回数据的序列化问题")
            else:
                print(f"\n✅ API返回数量正确")
        
    finally:
        connection.close()
        print(f"\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()