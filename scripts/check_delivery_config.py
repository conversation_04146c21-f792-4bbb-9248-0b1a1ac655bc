#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查配送费配置脚本

此脚本用于查询system_config表中的配送费相关配置
连接到o_mall_dev数据库并显示配送费配置信息

使用方法:
    python3 scripts/check_delivery_config.py

依赖:
    pip install pymysql
"""

import pymysql
import sys
import json
from typing import List, Dict, Any

# 数据库配置信息（从app.conf获取）
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': '4wk2HTRWkxKxwhHX',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

def connect_database():
    """
    连接到MySQL数据库
    
    Returns:
        pymysql.Connection: 数据库连接对象
    
    Raises:
        Exception: 数据库连接失败时抛出异常
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def check_delivery_config(connection):
    """
    检查配送费相关配置
    
    Args:
        connection: 数据库连接对象
    """
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询所有system_config数据
            print("\n🔍 查询所有system_config表数据:")
            sql = "SELECT * FROM system_config ORDER BY id"
            cursor.execute(sql)
            all_configs = cursor.fetchall()
            
            print(f"总配置数: {len(all_configs)}")
            print("-" * 100)
            
            for config in all_configs:
                print(f"ID: {config['id']}, Key: {config['config_key']}, Category: {config['category']}, Value: {config['config_value'][:100]}...")
            
            # 查询deliveryFee分类的配置
            print("\n🚚 查询deliveryFee分类的配置:")
            sql = "SELECT * FROM system_config WHERE category = 'deliveryFee'"
            cursor.execute(sql)
            delivery_configs = cursor.fetchall()
            
            if delivery_configs:
                print(f"找到 {len(delivery_configs)} 条配送费配置:")
                for config in delivery_configs:
                    print(f"\n配置详情:")
                    print(f"  ID: {config['id']}")
                    print(f"  配置键: {config['config_key']}")
                    print(f"  配置值: {config['config_value']}")
                    print(f"  配置类型: {config['config_type']}")
                    print(f"  分类: {config['category']}")
                    print(f"  描述: {config['description']}")
                    print(f"  状态: {config['status']}")
                    print(f"  创建时间: {config['created_at']}")
                    print(f"  更新时间: {config['updated_at']}")
                    
                    # 尝试解析JSON配置值
                    if config['config_type'] == 'json':
                        try:
                            parsed_value = json.loads(config['config_value'])
                            print(f"  解析后的配置:")
                            for key, value in parsed_value.items():
                                print(f"    {key}: {value}")
                        except json.JSONDecodeError as e:
                            print(f"  ❌ JSON解析失败: {e}")
            else:
                print("❌ 没有找到deliveryFee分类的配置")
                
            # 查询所有分类
            print("\n📂 查询所有配置分类:")
            sql = "SELECT DISTINCT category FROM system_config"
            cursor.execute(sql)
            categories = cursor.fetchall()
            
            print("现有分类:")
            for cat in categories:
                print(f"  - {cat['category']}")
                
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def main():
    """
    主函数
    """
    print("🚀 开始检查配送费配置...")
    print("=" * 100)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 检查配送费配置
        check_delivery_config(connection)
        
        print("\n" + "=" * 100)
        print("✅ 检查完成！")
        
    finally:
        connection.close()
        print("\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()