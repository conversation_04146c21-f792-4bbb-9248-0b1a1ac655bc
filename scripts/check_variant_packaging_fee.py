#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外卖规格包装费检查脚本

此脚本用于查询takeout_food_variant表中的包装费数据
分析包装费计算不准确的问题

使用方法:
    python3 scripts/check_variant_packaging_fee.py

依赖:
    pip install pymysql
"""

import pymysql
import sys
from typing import List, Dict, Any

# 数据库配置信息
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': '4wk2HTRWkxKxwhHX',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

def connect_database():
    """
    连接到MySQL数据库
    
    Returns:
        pymysql.Connection: 数据库连接对象
    
    Raises:
        Exception: 数据库连接失败时抛出异常
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def check_variant_packaging_fee(connection):
    """
    检查规格的包装费数据
    
    Args:
        connection: 数据库连接对象
    """
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询规格包装费数据
            sql = """
            SELECT 
                v.id as variant_id,
                v.food_id,
                v.name as variant_name,
                v.price as variant_price,
                v.packaging_fee as variant_packaging_fee,
                f.name as food_name,
                f.packaging_fee as food_packaging_fee
            FROM takeout_food_variant v
            LEFT JOIN takeout_food f ON v.food_id = f.id
            ORDER BY v.id
            LIMIT 20
            """
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"\n📊 规格包装费检查结果:")
            print(f"总记录数: {len(results)}")
            print("-" * 120)
            print(f"{'规格ID':<8} {'商品ID':<8} {'规格名称':<15} {'规格价格':<10} {'规格包装费':<12} {'商品名称':<20} {'商品包装费':<12}")
            print("-" * 120)
            
            for result in results:
                print(f"{result['variant_id']:<8} {result['food_id']:<8} {result['variant_name']:<15} {result['variant_price']:<10.2f} {result['variant_packaging_fee']:<12.2f} {result['food_name']:<20} {result['food_packaging_fee']:<12.2f}")
            
            return results
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []

def check_packaging_fee_statistics(connection):
    """
    检查包装费统计信息
    
    Args:
        connection: 数据库连接对象
    """
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 统计包装费分布
            sql = """
            SELECT 
                COUNT(*) as total_variants,
                COUNT(CASE WHEN packaging_fee = 0 THEN 1 END) as zero_packaging_fee,
                COUNT(CASE WHEN packaging_fee > 0 THEN 1 END) as positive_packaging_fee,
                MIN(packaging_fee) as min_packaging_fee,
                MAX(packaging_fee) as max_packaging_fee,
                AVG(packaging_fee) as avg_packaging_fee
            FROM takeout_food_variant
            """
            cursor.execute(sql)
            stats = cursor.fetchone()
            
            print(f"\n📈 包装费统计信息:")
            print("-" * 60)
            print(f"总规格数量: {stats['total_variants']}")
            print(f"包装费为0的规格数量: {stats['zero_packaging_fee']}")
            print(f"包装费大于0的规格数量: {stats['positive_packaging_fee']}")
            print(f"最小包装费: {stats['min_packaging_fee']:.2f}")
            print(f"最大包装费: {stats['max_packaging_fee']:.2f}")
            print(f"平均包装费: {stats['avg_packaging_fee']:.2f}")
            
            # 检查具体的包装费分布
            sql2 = """
            SELECT 
                packaging_fee,
                COUNT(*) as count
            FROM takeout_food_variant
            GROUP BY packaging_fee
            ORDER BY packaging_fee
            """
            cursor.execute(sql2)
            distribution = cursor.fetchall()
            
            print(f"\n📊 包装费分布:")
            print("-" * 30)
            print(f"{'包装费':<10} {'数量':<10}")
            print("-" * 30)
            for item in distribution:
                print(f"{item['packaging_fee']:<10.2f} {item['count']:<10}")
            
    except Exception as e:
        print(f"❌ 统计查询失败: {e}")

def check_recent_cart_items(connection):
    """
    检查最近的购物车项和对应的规格包装费
    
    Args:
        connection: 数据库连接对象
    """
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询最近的购物车项
            sql = """
            SELECT 
                tc.id as cart_item_id,
                tc.food_id,
                tc.variant_id,
                tc.quantity,
                v.name as variant_name,
                v.packaging_fee as variant_packaging_fee,
                f.name as food_name,
                f.packaging_fee as food_packaging_fee
            FROM takeout_cart_item tc
            LEFT JOIN takeout_food_variant v ON tc.variant_id = v.id
            LEFT JOIN takeout_food f ON tc.food_id = f.id
            ORDER BY tc.id DESC
            LIMIT 10
            """
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"\n🛒 最近购物车项包装费检查:")
            print("-" * 140)
            print(f"{'购物车ID':<10} {'商品ID':<8} {'规格ID':<8} {'数量':<6} {'规格名称':<15} {'规格包装费':<12} {'商品名称':<20} {'商品包装费':<12}")
            print("-" * 140)
            
            for result in results:
                variant_id = result['variant_id'] if result['variant_id'] else 'N/A'
                variant_name = result['variant_name'] if result['variant_name'] else 'N/A'
                variant_fee = result['variant_packaging_fee'] if result['variant_packaging_fee'] else 0.0
                
                print(f"{result['cart_item_id']:<10} {result['food_id']:<8} {variant_id:<8} {result['quantity']:<6} {variant_name:<15} {variant_fee:<12.2f} {result['food_name']:<20} {result['food_packaging_fee']:<12.2f}")
            
    except Exception as e:
        print(f"❌ 购物车项查询失败: {e}")

def main():
    """
    主函数
    """
    print("🔍 开始检查规格包装费数据...")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 检查规格包装费数据
        check_variant_packaging_fee(connection)
        
        # 检查包装费统计信息
        check_packaging_fee_statistics(connection)
        
        # 检查最近的购物车项
        check_recent_cart_items(connection)
        
        print("\n" + "=" * 80)
        print("✅ 检查完成！")
        print("\n💡 分析建议:")
        print("   1. 检查包装费为0的规格是否正确")
        print("   2. 验证包装费计算逻辑")
        print("   3. 确认数据库中的包装费设置")
        print("   4. 检查代码中的包装费获取逻辑")
        
    finally:
        connection.close()
        print("\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()