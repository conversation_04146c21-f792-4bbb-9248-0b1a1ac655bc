#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外卖食品列表API调试脚本
用于测试API调用并查看调试日志输出
"""

import requests
import json
import time

def test_food_list_api():
    """
    测试外卖食品列表API
    """
    print("=== 外卖食品列表API调试测试 ===")
    
    # API基础URL（需要根据实际情况调整）
    base_url = "http://localhost:8181"
    api_endpoint = "/api/v1/merchant/takeout/foods"
    
    # 测试用的商家认证token（需要根据实际情况调整）
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6Im1lbG9naW4iLCJyb2xlIjoibWVyY2hhbnQiLCJ0eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ5MTc5OTg3LCJpYXQiOjE3NDkxNzkzODcsImlzcyI6Im9fbWFsbCJ9.G2Gk6s5WNwq-1m4zK203aVd3ijMUkaJlWstoZdgQfUs",  # 暂时注释掉
        # "X-Merchant-ID": "1"  # 暂时注释掉
    }
    
    # 测试场景1: 默认查询（无过滤条件）
    print("\n--- 测试场景1: 默认查询 ---")
    params1 = {
        "page": 1,
        "pageSize": 20
    }
    
    try:
        response1 = requests.get(f"{base_url}{api_endpoint}", 
                                headers=headers, 
                                params=params1,
                                timeout=10)
        print(f"状态码: {response1.status_code}")
        if response1.status_code == 200:
            data1 = response1.json()
            print(f"返回数据: {json.dumps(data1, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response1.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    time.sleep(1)
    
    # 测试场景2: 查询上架商品
    print("\n--- 测试场景2: 查询上架商品 ---")
    params2 = {
        "page": 1,
        "pageSize": 20,
        "status": 1  # 上架状态
    }
    
    try:
        response2 = requests.get(f"{base_url}{api_endpoint}", 
                                headers=headers, 
                                params=params2,
                                timeout=10)
        print(f"状态码: {response2.status_code}")
        if response2.status_code == 200:
            data2 = response2.json()
            print(f"返回数据: {json.dumps(data2, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response2.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    time.sleep(1)
    
    # 测试场景3: 查询下架商品
    print("\n--- 测试场景3: 查询下架商品 ---")
    params3 = {
        "page": 1,
        "pageSize": 20,
        "status": 2  # 下架状态
    }
    
    try:
        response3 = requests.get(f"{base_url}{api_endpoint}", 
                                headers=headers, 
                                params=params3,
                                timeout=10)
        print(f"状态码: {response3.status_code}")
        if response3.status_code == 200:
            data3 = response3.json()
            print(f"返回数据: {json.dumps(data3, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response3.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    time.sleep(1)
    
    # 测试场景4: 多次调用同一接口（测试缓存行为）
    print("\n--- 测试场景4: 多次调用测试缓存 ---")
    for i in range(3):
        print(f"\n第{i+1}次调用:")
        try:
            response = requests.get(f"{base_url}{api_endpoint}", 
                                   headers=headers, 
                                   params=params1,
                                   timeout=10)
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"返回商品数量: {len(data.get('data', {}).get('list', []))}")
                print(f"总数: {data.get('data', {}).get('total', 0)}")
            else:
                print(f"错误响应: {response.text}")
        except Exception as e:
            print(f"请求失败: {e}")
        
        time.sleep(2)  # 等待2秒再次调用
    
    print("\n=== 测试完成 ===")
    print("请查看Go应用的日志输出，关注以下调试信息:")
    print("1. 商家ID获取情况")
    print("2. 缓存验证过程")
    print("3. 数据库查询结果")
    print("4. 最终返回的数据")

def print_usage():
    """
    打印使用说明
    """
    print("使用说明:")
    print("1. 确保Go应用正在运行（通常在端口8080）")
    print("2. 修改脚本中的base_url为实际的API地址")
    print("3. 替换headers中的Authorization token为有效的商家token")
    print("4. 运行脚本: python3 debug_food_list_api.py")
    print("5. 观察Go应用的日志输出，查看调试信息")
    print("\n注意: 如果没有有效的token，API调用可能会失败")
    print("但这个脚本的主要目的是触发API调用以产生调试日志")

if __name__ == "__main__":
    print_usage()
    print("\n按Enter键开始测试，或Ctrl+C退出...")
    try:
        input()
        test_food_list_api()
    except KeyboardInterrupt:
        print("\n测试已取消")