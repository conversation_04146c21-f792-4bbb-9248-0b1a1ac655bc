#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试外卖订单ProductImage字段为空的问题

本脚本用于调试外卖订单创建过程中ProductImage字段丢失的问题。
将模拟从购物车到订单创建的完整流程，检查每个环节的数据传递。
"""

import pymysql
import json
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'o_mall',
    'charset': 'utf8mb4',
    'connect_timeout': 30,
    'read_timeout': 30,
    'write_timeout': 30
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def debug_product_image_flow():
    """调试ProductImage字段流转过程"""
    connection = get_db_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        print("🔍 开始调试ProductImage字段流转过程...")
        print("=" * 80)
        
        # 1. 检查takeout_food表中的image字段
        print("\n📋 步骤1: 检查takeout_food表中的image字段")
        print("-" * 50)
        
        cursor.execute("""
            SELECT id, name, image, merchant_id
            FROM takeout_food 
            WHERE status = 1 
            ORDER BY id DESC 
            LIMIT 5
        """)
        
        foods = cursor.fetchall()
        print(f"找到 {len(foods)} 个上架商品:")
        for food in foods:
            image_status = "✅ 有图片" if food['image'] else "❌ 无图片"
            print(f"  商品ID: {food['id']}, 名称: {food['name']}, 图片: {image_status}")
            if food['image']:
                print(f"    图片URL: {food['image']}")
        
        # 2. 检查购物车项中的数据
        print("\n📋 步骤2: 检查购物车项中的数据")
        print("-" * 50)
        
        cursor.execute("""
            SELECT tci.id, tci.cart_item_id, tci.food_id, tf.name as food_name, tf.image as food_image
            FROM takeout_cart_item tci
            LEFT JOIN takeout_food tf ON tci.food_id = tf.id
            ORDER BY tci.id DESC
            LIMIT 5
        """)
        
        cart_items = cursor.fetchall()
        print(f"找到 {len(cart_items)} 个购物车项:")
        for item in cart_items:
            image_status = "✅ 有图片" if item['food_image'] else "❌ 无图片"
            print(f"  购物车项ID: {item['id']}, 商品ID: {item['food_id']}, 商品名: {item['food_name']}, 图片: {image_status}")
            if item['food_image']:
                print(f"    图片URL: {item['food_image']}")
        
        # 3. 检查订单项中的product_image字段
        print("\n📋 步骤3: 检查订单项中的product_image字段")
        print("-" * 50)
        
        cursor.execute("""
            SELECT oi.id, oi.order_id, oi.product_id, oi.product_name, oi.product_image,
                   o.order_type, o.created_at
            FROM order_item oi
            LEFT JOIN `order` o ON oi.order_id = o.id
            WHERE o.order_type = 2  -- 外卖订单
            ORDER BY oi.id DESC
            LIMIT 10
        """)
        
        order_items = cursor.fetchall()
        print(f"找到 {len(order_items)} 个外卖订单项:")
        
        empty_image_count = 0
        has_image_count = 0
        
        for item in order_items:
            if item['product_image']:
                has_image_count += 1
                image_status = "✅ 有图片"
            else:
                empty_image_count += 1
                image_status = "❌ 无图片"
            
            print(f"  订单项ID: {item['id']}, 订单ID: {item['order_id']}, 商品ID: {item['product_id']}")
            print(f"    商品名: {item['product_name']}, 图片: {image_status}")
            print(f"    创建时间: {item['created_at']}")
            if item['product_image']:
                print(f"    图片URL: {item['product_image']}")
            print()
        
        print(f"📊 统计结果:")
        print(f"  总订单项数: {len(order_items)}")
        print(f"  有图片的订单项: {has_image_count}")
        print(f"  无图片的订单项: {empty_image_count}")
        print(f"  无图片比例: {empty_image_count/len(order_items)*100:.1f}%" if order_items else "0%")
        
        # 4. 检查最近的订单创建情况
        print("\n📋 步骤4: 检查最近的订单创建情况")
        print("-" * 50)
        
        cursor.execute("""
            SELECT o.id, o.user_id, o.created_at, COUNT(oi.id) as item_count,
                   SUM(CASE WHEN oi.product_image IS NOT NULL AND oi.product_image != '' THEN 1 ELSE 0 END) as items_with_image
            FROM `order` o
            LEFT JOIN order_item oi ON o.id = oi.order_id
            WHERE o.order_type = 2  -- 外卖订单
            AND o.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT 10
        """)
        
        recent_orders = cursor.fetchall()
        print(f"最近7天的 {len(recent_orders)} 个外卖订单:")
        
        for order in recent_orders:
            image_ratio = f"{order['items_with_image']}/{order['item_count']}"
            print(f"  订单ID: {order['id']}, 用户ID: {order['user_id']}, 创建时间: {order['created_at']}")
            print(f"    订单项数: {order['item_count']}, 有图片的项: {image_ratio}")
        
        # 5. 分析可能的原因
        print("\n📋 步骤5: 分析可能的原因")
        print("-" * 50)
        
        # 检查是否有商品图片为空的情况
        cursor.execute("""
            SELECT COUNT(*) as total_foods,
                   SUM(CASE WHEN image IS NULL OR image = '' THEN 1 ELSE 0 END) as foods_without_image
            FROM takeout_food
            WHERE status = 1
        """)
        
        food_stats = cursor.fetchone()
        print(f"商品图片统计:")
        print(f"  上架商品总数: {food_stats['total_foods']}")
        print(f"  无图片商品数: {food_stats['foods_without_image']}")
        
        if food_stats['foods_without_image'] > 0:
            print(f"  ⚠️  发现 {food_stats['foods_without_image']} 个商品没有图片")
        
        # 检查订单项中product_id对应的商品是否存在图片
        cursor.execute("""
            SELECT oi.product_id, tf.image as current_food_image, COUNT(*) as order_count
            FROM order_item oi
            LEFT JOIN takeout_food tf ON oi.product_id = tf.id
            LEFT JOIN `order` o ON oi.order_id = o.id
            WHERE o.order_type = 2
            AND (oi.product_image IS NULL OR oi.product_image = '')
            GROUP BY oi.product_id, tf.image
            ORDER BY order_count DESC
            LIMIT 5
        """)
        
        problematic_products = cursor.fetchall()
        if problematic_products:
            print(f"\n❌ 发现问题商品 (订单项中无图片但商品表中有图片):")
            for product in problematic_products:
                current_image_status = "有图片" if product['current_food_image'] else "无图片"
                print(f"  商品ID: {product['product_id']}, 当前商品图片: {current_image_status}, 影响订单项数: {product['order_count']}")
                if product['current_food_image']:
                    print(f"    当前图片URL: {product['current_food_image']}")
        
        print("\n🔍 调试完成!")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 查询过程中出现错误: {e}")
    finally:
        connection.close()
        print("🔐 数据库连接已关闭")

if __name__ == "__main__":
    debug_product_image_flow()