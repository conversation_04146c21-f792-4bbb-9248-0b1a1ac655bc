#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询订单项表中的ProductImage字段
用于调试外卖订单API中image字段为空的问题
"""

import pymysql
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'rm-cn-x0r3iqw4w0016hco.rwlb.rds.aliyuncs.com',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': 'Omall2024!',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4',
    'connect_timeout': 10,
    'read_timeout': 30,
    'write_timeout': 30
}

def connect_db():
    """连接数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("🔗 数据库连接成功！")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def query_order_items(connection):
    """查询订单项表"""
    try:
        with connection.cursor() as cursor:
            # 查询订单项表结构
            print("\n📋 订单项表结构:")
            cursor.execute("DESCRIBE order_item")
            columns = cursor.fetchall()
            for col in columns:
                print(f"   {col[0]} - {col[1]} - {col[2]}")
            
            # 查询最近的订单项数据
            print("\n📦 最近的订单项数据:")
            query = """
            SELECT 
                id, order_id, product_id, product_name, product_image, 
                sku_id, quantity, price, created_at
            FROM order_item 
            WHERE item_type = 'takeout'
            ORDER BY created_at DESC 
            LIMIT 10
            """
            cursor.execute(query)
            items = cursor.fetchall()
            
            if not items:
                print("   ❌ 没有找到外卖订单项数据")
                return
            
            for item in items:
                print(f"\n📦 订单项 #{item[0]}:")
                print(f"   订单ID: {item[1]}")
                print(f"   商品ID: {item[2]}")
                print(f"   商品名称: {item[3]}")
                print(f"   商品图片: {item[4] if item[4] else 'N/A'}")
                print(f"   SKU ID: {item[5]}")
                print(f"   数量: {item[6]}")
                print(f"   价格: ¥{item[7]:.2f}")
                print(f"   创建时间: {item[8]}")
                print("-" * 60)
            
            # 统计ProductImage字段的情况
            print("\n📊 ProductImage字段统计:")
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(product_image) as has_image_count,
                    COUNT(*) - COUNT(product_image) as null_image_count
                FROM order_item 
                WHERE item_type = 'takeout'
            """)
            stats = cursor.fetchone()
            print(f"   总订单项数: {stats[0]}")
            print(f"   有图片的订单项: {stats[1]}")
            print(f"   图片为空的订单项: {stats[2]}")
            
            # 查询图片为空的订单项
            if stats[2] > 0:
                print("\n🔍 图片为空的订单项详情:")
                cursor.execute("""
                    SELECT id, order_id, product_id, product_name, created_at
                    FROM order_item 
                    WHERE item_type = 'takeout' AND (product_image IS NULL OR product_image = '')
                    ORDER BY created_at DESC
                    LIMIT 5
                """)
                empty_items = cursor.fetchall()
                for item in empty_items:
                    print(f"   订单项ID: {item[0]}, 订单ID: {item[1]}, 商品ID: {item[2]}, 商品名称: {item[3]}, 创建时间: {item[4]}")
                    
    except Exception as e:
        print(f"❌ 查询订单项失败: {e}")

def main():
    """主函数"""
    print("🚀 开始查询订单项表...")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_db()
    if not connection:
        sys.exit(1)
    
    try:
        # 查询订单项数据
        query_order_items(connection)
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
    finally:
        # 关闭数据库连接
        connection.close()
        print("\n🔐 数据库连接已关闭")
    
    print("\n✅ 查询完成！")
    print("\n💡 下一步建议:")
    print("   1. 检查订单创建时ProductImage字段是否正确设置")
    print("   2. 检查API返回数据的映射逻辑")
    print("   3. 验证数据库字段定义")

if __name__ == "__main__":
    main()