#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外卖商品数据查询脚本

此脚本用于查询takeout_food表中的所有数据
连接到o_mall_dev数据库并显示所有外卖商品信息

使用方法:
    python3 scripts/query_takeout_food.py

依赖:
    pip install pymysql
"""

import pymysql
import sys
from typing import List, Dict, Any

# 数据库配置信息（从app.conf获取）
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': '4wk2HTRWkxKxwhHX',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

def connect_database():
    """
    连接到MySQL数据库
    
    Returns:
        pymysql.Connection: 数据库连接对象
    
    Raises:
        Exception: 数据库连接失败时抛出异常
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def query_takeout_food_table(connection) -> List[Dict[str, Any]]:
    """
    查询takeout_food表的所有数据
    
    Args:
        connection: 数据库连接对象
    
    Returns:
        List[Dict[str, Any]]: 查询结果列表
    """
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询takeout_food表的所有数据
            sql = "SELECT * FROM takeout_food ORDER BY id"
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"\n📊 takeout_food表查询结果:")
            print(f"总记录数: {len(results)}")
            print("-" * 80)
            
            return results
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []

def display_food_data(food_list: List[Dict[str, Any]]):
    """
    格式化显示外卖商品数据
    
    Args:
        food_list: 外卖商品数据列表
    """
    if not food_list:
        print("📭 没有找到任何外卖商品数据")
        return
    
    for i, food in enumerate(food_list, 1):
        print(f"\n🍽️  商品 #{i}:")
        print(f"   ID: {food.get('id', 'N/A')}")
        print(f"   名称: {food.get('name', 'N/A')}")
        print(f"   商家ID: {food.get('merchant_id', 'N/A')}")
        print(f"   分类ID: {food.get('category_id', 'N/A')}")
        print(f"   价格: ¥{food.get('price', 'N/A')}")
        print(f"   库存: {food.get('stock', 'N/A')}")
        print(f"   状态: {food.get('status', 'N/A')}")
        print(f"   描述: {food.get('description', 'N/A')}")
        print(f"   图片URL: {food.get('image', 'N/A')}")
        print(f"   创建时间: {food.get('created_at', 'N/A')}")
        print(f"   更新时间: {food.get('updated_at', 'N/A')}")
        
        if i < len(food_list):
            print("-" * 60)

def get_table_structure(connection):
    """
    获取takeout_food表的结构信息
    
    Args:
        connection: 数据库连接对象
    """
    try:
        with connection.cursor() as cursor:
            sql = "DESCRIBE takeout_food"
            cursor.execute(sql)
            columns = cursor.fetchall()
            
            print(f"\n🏗️  takeout_food表结构:")
            print("-" * 80)
            print(f"{'字段名':<20} {'类型':<20} {'是否为空':<10} {'键':<10} {'默认值':<15} {'额外':<15}")
            print("-" * 80)
            
            for column in columns:
                field, type_info, null, key, default, extra = column
                print(f"{field:<20} {type_info:<20} {null:<10} {key:<10} {str(default):<15} {extra:<15}")
    except Exception as e:
        print(f"❌ 获取表结构失败: {e}")

def main():
    """
    主函数
    """
    print("🚀 开始查询外卖商品数据...")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 获取表结构
        get_table_structure(connection)
        
        # 查询数据
        food_data = query_takeout_food_table(connection)
        
        # 显示数据
        display_food_data(food_data)
        
        print("\n" + "=" * 80)
        print("✅ 查询完成！")
        print("\n💡 下一步建议:")
        print("   1. 检查数据完整性")
        print("   2. 分析商品分布情况")
        print("   3. 检查是否有异常数据")
        print("   4. 验证缓存数据一致性")
        
    finally:
        connection.close()
        print("\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()