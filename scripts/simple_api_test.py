#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本
用于测试推荐商家API而不打断后端服务
"""

import requests
import json

def test_api():
    """测试推荐商家API"""
    url = "http://localhost:8181/api/v1/merchant/recommended-merchants"
    
    try:
        print(f"🔍 测试API: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n✅ API响应成功:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 分析数据
            if 'data' in data and isinstance(data['data'], list):
                merchants = data['data']
                print(f"\n📊 返回商家数量: {len(merchants)}")
                
                for i, merchant in enumerate(merchants, 1):
                    if isinstance(merchant, dict):
                        print(f"  {i}. ID: {merchant.get('id')}, 名称: {merchant.get('name')}, 营业状态: {merchant.get('operation_status')}, 推荐: {merchant.get('is_recommended')}")
                    else:
                        print(f"  {i}. 异常数据: {type(merchant)} - {merchant}")
            else:
                print(f"\n⚠️ 数据格式异常: {type(data.get('data'))}")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_api()