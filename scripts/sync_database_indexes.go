/**
 * 数据库索引同步脚本
 * 
 * 用于在生产环境中安全地同步数据库结构和索引
 * 基于Beego v2框架的ORM模型定义
 */

package main

import (
	"flag"
	"fmt"
	"os"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/config"
	"github.com/beego/beego/v2/core/logs"
	_ "github.com/go-sql-driver/mysql"

	// 导入模型
	"o_mall_backend/modules/cart/models"
	takeoutModels "o_mall_backend/modules/takeout/models"
)

var (
	configFile = flag.String("config", "conf/app.conf", "配置文件路径")
	dryRun     = flag.Bool("dry-run", false, "只检查不执行")
	force      = flag.Bool("force", false, "强制执行（跳过确认）")
)

func main() {
	flag.Parse()

	fmt.Println("=== 购物车模块数据库索引同步工具 ===")
	fmt.Printf("配置文件: %s\n", *configFile)
	fmt.Printf("预演模式: %t\n", *dryRun)
	fmt.Printf("强制执行: %t\n", *force)
	fmt.Println()

	// 加载配置
	if err := loadConfig(*configFile); err != nil {
		logs.Error("加载配置失败: %v", err)
		os.Exit(1)
	}

	// 初始化数据库连接
	if err := initDatabase(); err != nil {
		logs.Error("初始化数据库失败: %v", err)
		os.Exit(1)
	}

	// 注册模型
	registerModels()

	// 检查当前索引状态
	if err := checkCurrentIndexes(); err != nil {
		logs.Error("检查当前索引失败: %v", err)
		os.Exit(1)
	}

	// 执行同步
	if *dryRun {
		fmt.Println("预演模式：只检查不执行实际同步")
		if err := previewSync(); err != nil {
			logs.Error("预演失败: %v", err)
			os.Exit(1)
		}
	} else {
		if !*force {
			if !confirmSync() {
				fmt.Println("用户取消操作")
				os.Exit(0)
			}
		}

		if err := executeSync(); err != nil {
			logs.Error("同步失败: %v", err)
			os.Exit(1)
		}
	}

	fmt.Println("操作完成！")
}

// loadConfig 加载配置文件
func loadConfig(configPath string) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	appConfig, err := config.NewConfig("ini", configPath)
	if err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置日志级别
	logLevel := appConfig.DefaultString("log_level", "info")
	logs.SetLevel(logs.LevelInfo)
	if logLevel == "debug" {
		logs.SetLevel(logs.LevelDebug)
	}

	return nil
}

// initDatabase 初始化数据库连接
func initDatabase() error {
	// 这里应该从配置文件读取数据库连接信息
	// 为了示例，使用默认配置
	dataSource := "root:password@tcp(localhost:3306)/o_mall?charset=utf8mb4&parseTime=true&loc=Local"
	
	fmt.Printf("连接数据库: %s\n", maskPassword(dataSource))
	
	err := orm.RegisterDataBase("default", "mysql", dataSource)
	if err != nil {
		return fmt.Errorf("注册数据库失败: %v", err)
	}

	// 测试连接
	o := orm.NewOrm()
	if err := o.Raw("SELECT 1").QueryRow(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	fmt.Println("数据库连接成功")
	return nil
}

// registerModels 注册模型
func registerModels() {
	fmt.Println("注册模型...")
	
	// 注册购物车相关模型
	orm.RegisterModel(new(models.CartItem))
	orm.RegisterModel(new(models.CartItemLog))
	
	// 注册外卖购物车相关模型
	orm.RegisterModel(new(takeoutModels.TakeoutCartItem))
	orm.RegisterModel(new(takeoutModels.TakeoutCartItemLog))
	
	fmt.Println("模型注册完成")
}

// checkCurrentIndexes 检查当前索引状态
func checkCurrentIndexes() error {
	fmt.Println("检查当前数据库索引状态...")
	
	o := orm.NewOrm()
	
	tables := []string{"cart_items", "takeout_cart_item", "takeout_cart_item_log"}
	
	for _, table := range tables {
		fmt.Printf("\n表: %s\n", table)
		
		var indexes []map[string]interface{}
		_, err := o.Raw("SHOW INDEX FROM " + table).Values(&indexes)
		if err != nil {
			logs.Warn("获取表 %s 索引失败: %v", table, err)
			continue
		}
		
		fmt.Printf("  现有索引数量: %d\n", len(indexes))
		for _, index := range indexes {
			if keyName, ok := index["Key_name"].(string); ok && keyName != "PRIMARY" {
				columnName := index["Column_name"].(string)
				fmt.Printf("  - %s (%s)\n", keyName, columnName)
			}
		}
	}
	
	return nil
}

// previewSync 预览同步操作
func previewSync() error {
	fmt.Println("\n=== 预览同步操作 ===")
	fmt.Println("将要执行的操作:")
	fmt.Println("1. 同步 cart_items 表结构和索引")
	fmt.Println("2. 同步 takeout_cart_item 表结构和索引")
	fmt.Println("3. 同步 takeout_cart_item_log 表结构和索引")
	fmt.Println()
	fmt.Println("新增索引:")
	fmt.Println("cart_items:")
	fmt.Println("  - idx_cart_items_user_status_selected (user_id, status, selected)")
	fmt.Println("  - idx_cart_items_user_id_status (user_id, id, status)")
	fmt.Println("  - idx_cart_items_user_status_created (user_id, status, created_at)")
	fmt.Println()
	fmt.Println("takeout_cart_item:")
	fmt.Println("  - idx_takeout_cart_item_cart_id (cart_item_id)")
	fmt.Println("  - idx_takeout_cart_item_food_id (food_id)")
	fmt.Println("  - idx_takeout_cart_item_variant_id (variant_id)")
	fmt.Println("  - idx_takeout_cart_item_food_variant (food_id, variant_id)")
	fmt.Println("  - idx_takeout_cart_item_created (created_at)")
	fmt.Println()
	fmt.Println("takeout_cart_item_log:")
	fmt.Println("  - idx_takeout_cart_log_user_id (user_id)")
	fmt.Println("  - idx_takeout_cart_log_cart_id (cart_item_id)")
	fmt.Println("  - idx_takeout_cart_log_food_id (food_id)")
	fmt.Println("  - idx_takeout_cart_log_user_created (user_id, created_at)")
	fmt.Println("  - idx_takeout_cart_log_action_created (action, created_at)")
	fmt.Println("  - idx_takeout_cart_log_created (created_at)")
	
	return nil
}

// confirmSync 确认同步操作
func confirmSync() bool {
	fmt.Println("\n=== 确认同步操作 ===")
	fmt.Println("⚠️  警告: 此操作将修改数据库结构")
	fmt.Println("⚠️  建议在维护窗口期间执行")
	fmt.Println("⚠️  请确保已备份数据库")
	fmt.Println()
	fmt.Print("确认执行同步操作? (yes/no): ")
	
	var input string
	fmt.Scanln(&input)
	
	return input == "yes" || input == "y" || input == "YES" || input == "Y"
}

// executeSync 执行同步操作
func executeSync() error {
	fmt.Println("\n=== 执行数据库同步 ===")
	
	start := time.Now()
	
	// 执行同步
	err := orm.RunSyncdb("default", false, true)
	if err != nil {
		return fmt.Errorf("数据库同步失败: %v", err)
	}
	
	duration := time.Since(start)
	fmt.Printf("数据库同步完成，耗时: %v\n", duration)
	
	// 验证索引创建
	if err := verifyIndexes(); err != nil {
		logs.Warn("索引验证失败: %v", err)
	}
	
	return nil
}

// verifyIndexes 验证索引创建
func verifyIndexes() error {
	fmt.Println("\n验证索引创建...")
	
	o := orm.NewOrm()
	
	// 检查关键索引是否存在
	checkIndexes := map[string][]string{
		"cart_items": {
			"idx_cart_items_user_status_selected",
			"idx_cart_items_user_id_status",
			"idx_cart_items_user_status_created",
		},
		"takeout_cart_item": {
			"idx_takeout_cart_item_cart_id",
			"idx_takeout_cart_item_food_id",
		},
		"takeout_cart_item_log": {
			"idx_takeout_cart_log_user_id",
			"idx_takeout_cart_log_created",
		},
	}
	
	for table, expectedIndexes := range checkIndexes {
		var indexes []map[string]interface{}
		_, err := o.Raw("SHOW INDEX FROM " + table).Values(&indexes)
		if err != nil {
			return fmt.Errorf("获取表 %s 索引失败: %v", table, err)
		}
		
		existingIndexes := make(map[string]bool)
		for _, index := range indexes {
			if keyName, ok := index["Key_name"].(string); ok {
				existingIndexes[keyName] = true
			}
		}
		
		fmt.Printf("表 %s:\n", table)
		for _, expectedIndex := range expectedIndexes {
			if existingIndexes[expectedIndex] {
				fmt.Printf("  ✅ %s\n", expectedIndex)
			} else {
				fmt.Printf("  ❌ %s (缺失)\n", expectedIndex)
			}
		}
	}
	
	return nil
}

// maskPassword 隐藏密码
func maskPassword(dsn string) string {
	// 简单的密码隐藏逻辑
	// 实际项目中应该使用更安全的方法
	return "user:***@tcp(localhost:3306)/database"
}
