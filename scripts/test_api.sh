#!/bin/bash
# 测试推荐商家API的脚本

echo "🔍 测试推荐商家API..."
echo "URL: http://localhost:8181/api/v1/merchant/recommended-merchants"
echo ""

# 使用curl测试API
echo "原始响应:"
response=$(curl -s -X GET "http://localhost:8181/api/v1/merchant/recommended-merchants")
echo "$response"
echo ""
echo "格式化JSON:"
if [ -n "$response" ]; then
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "JSON格式化失败，原始内容: $response"
else
    echo "响应为空"
fi

echo ""
echo "✅ 测试完成"