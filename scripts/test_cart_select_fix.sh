#!/bin/bash

# 购物车选择API修复验证脚本
# 用于测试修复后的API是否正常工作

echo "=== 购物车选择API修复验证 ==="

# 配置参数
API_URL="http://localhost:8080/api/v1/user/takeout/cart/select"
USER_TOKEN="your_test_token_here"

echo "API地址: $API_URL"
echo ""

# 测试数据
TEST_CART_ITEM_IDS='[181,178,175,51]'

echo "=== 测试1: 选中购物车商品 ==="
echo "发送请求: 选中商品 $TEST_CART_ITEM_IDS"

SELECT_DATA='{
  "cart_item_ids": '$TEST_CART_ITEM_IDS',
  "selected": true
}'

echo "请求数据: $SELECT_DATA"

RESPONSE=$(curl -s -w "\n%{http_code}" \
  -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -d "$SELECT_DATA")

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
  echo "✅ 选中操作成功"
else
  echo "❌ 选中操作失败"
fi

echo ""

echo "=== 测试2: 取消选中购物车商品 ==="
echo "发送请求: 取消选中商品 $TEST_CART_ITEM_IDS"

UNSELECT_DATA='{
  "cart_item_ids": '$TEST_CART_ITEM_IDS',
  "selected": false
}'

echo "请求数据: $UNSELECT_DATA"

RESPONSE=$(curl -s -w "\n%{http_code}" \
  -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -d "$UNSELECT_DATA")

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
  echo "✅ 取消选中操作成功"
else
  echo "❌ 取消选中操作失败"
fi

echo ""

echo "=== 测试3: 空数组测试 ==="
echo "发送请求: 空购物车项数组"

EMPTY_DATA='{
  "cart_item_ids": [],
  "selected": true
}'

echo "请求数据: $EMPTY_DATA"

RESPONSE=$(curl -s -w "\n%{http_code}" \
  -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -d "$EMPTY_DATA")

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
  echo "✅ 空数组处理成功"
else
  echo "❌ 空数组处理失败"
fi

echo ""

echo "=== 测试4: 无效ID测试 ==="
echo "发送请求: 不存在的购物车项ID"

INVALID_DATA='{
  "cart_item_ids": [99999, 88888],
  "selected": true
}'

echo "请求数据: $INVALID_DATA"

RESPONSE=$(curl -s -w "\n%{http_code}" \
  -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -d "$INVALID_DATA")

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "400" ] || [ "$HTTP_CODE" = "404" ]; then
  echo "✅ 无效ID处理正确"
else
  echo "❌ 无效ID处理异常"
fi

echo ""

echo "=== 测试5: 性能测试 ==="
echo "测试API响应时间..."

START_TIME=$(date +%s%N)

for i in {1..5}; do
  curl -s -o /dev/null \
    -X POST "$API_URL" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $USER_TOKEN" \
    -d "$SELECT_DATA"
done

END_TIME=$(date +%s%N)
TOTAL_TIME=$((END_TIME - START_TIME))
AVG_TIME=$((TOTAL_TIME / 5 / 1000000)) # 转换为毫秒

echo "5次请求平均响应时间: ${AVG_TIME}ms"

if [ $AVG_TIME -lt 500 ]; then
  echo "✅ 响应时间优秀 (< 500ms)"
elif [ $AVG_TIME -lt 1000 ]; then
  echo "⚠️  响应时间良好 (500-1000ms)"
else
  echo "❌ 响应时间需要优化 (> 1000ms)"
fi

echo ""

echo "=== 测试总结 ==="
echo "修复验证完成！"
echo ""
echo "修复内容:"
echo "1. ✅ 将硬编码SQL改为Beego ORM QueryTable方式"
echo "2. ✅ 使用 id__in 过滤器进行批量ID查询"
echo "3. ✅ 使用 Filter 链式调用进行条件过滤"
echo "4. ✅ 使用 Update 方法进行批量更新"
echo ""
echo "优势:"
echo "- 避免了硬编码SQL中的列名问题"
echo "- 利用Beego ORM自动处理字段映射"
echo "- 保持了批量操作的性能优势"
echo "- 代码更加简洁和可维护"
