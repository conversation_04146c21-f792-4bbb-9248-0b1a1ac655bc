#!/bin/bash

# 购物车选择API性能测试脚本
# 用于验证优化效果

echo "=== 购物车选择API性能测试 ==="
echo "注意: 请确保已通过Beego v2模型文件部署了数据库索引优化"
echo ""

# 配置参数
API_URL="http://localhost:8080/api/v1/user/takeout/cart/select"
USER_TOKEN="your_test_token_here"
CONCURRENT_USERS=10
TEST_DURATION=60
BATCH_SIZE=20

# 创建测试数据
echo "准备测试数据..."

# 模拟购物车项ID列表
CART_ITEM_IDS='[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]'

# 测试请求体
TEST_DATA='{
  "cart_item_ids": '$CART_ITEM_IDS',
  "selected": true
}'

echo "测试配置:"
echo "- API地址: $API_URL"
echo "- 并发用户: $CONCURRENT_USERS"
echo "- 测试时长: ${TEST_DURATION}秒"
echo "- 批量大小: $BATCH_SIZE"
echo ""

# 单次请求测试
echo "=== 单次请求测试 ==="
echo "发送测试请求..."

RESPONSE_TIME=$(curl -w "%{time_total}" -s -o /dev/null \
  -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -d "$TEST_DATA")

echo "单次请求响应时间: ${RESPONSE_TIME}秒"
echo ""

# 并发测试
echo "=== 并发性能测试 ==="
echo "启动 $CONCURRENT_USERS 个并发用户，测试 $TEST_DURATION 秒..."

# 创建临时结果文件
RESULT_FILE="/tmp/cart_select_test_results.txt"
> "$RESULT_FILE"

# 启动并发测试
for i in $(seq 1 $CONCURRENT_USERS); do
  {
    END_TIME=$(($(date +%s) + TEST_DURATION))
    REQUEST_COUNT=0
    SUCCESS_COUNT=0
    TOTAL_TIME=0
    
    while [ $(date +%s) -lt $END_TIME ]; do
      # 随机选择操作（选中或取消选中）
      SELECTED=$([ $((RANDOM % 2)) -eq 0 ] && echo "true" || echo "false")
      
      # 构造请求数据
      REQUEST_DATA='{
        "cart_item_ids": '$CART_ITEM_IDS',
        "selected": '$SELECTED'
      }'
      
      # 发送请求并测量时间
      START_TIME=$(date +%s%N)
      HTTP_CODE=$(curl -w "%{http_code}" -s -o /dev/null \
        -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $USER_TOKEN" \
        -d "$REQUEST_DATA")
      END_TIME_NS=$(date +%s%N)
      
      # 计算响应时间（毫秒）
      RESPONSE_TIME_MS=$(( (END_TIME_NS - START_TIME) / 1000000 ))
      
      REQUEST_COUNT=$((REQUEST_COUNT + 1))
      TOTAL_TIME=$((TOTAL_TIME + RESPONSE_TIME_MS))
      
      if [ "$HTTP_CODE" = "200" ]; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
      fi
      
      # 模拟用户思考时间
      sleep 0.1
    done
    
    # 计算平均响应时间
    if [ $REQUEST_COUNT -gt 0 ]; then
      AVG_TIME=$((TOTAL_TIME / REQUEST_COUNT))
    else
      AVG_TIME=0
    fi
    
    # 输出用户结果
    echo "User$i: $REQUEST_COUNT $SUCCESS_COUNT $AVG_TIME" >> "$RESULT_FILE"
  } &
done

# 等待所有并发用户完成
wait

echo "并发测试完成，正在分析结果..."

# 分析测试结果
TOTAL_REQUESTS=0
TOTAL_SUCCESS=0
TOTAL_AVG_TIME=0
USER_COUNT=0

while read -r line; do
  if [[ $line =~ User[0-9]+:\ ([0-9]+)\ ([0-9]+)\ ([0-9]+) ]]; then
    REQUESTS=${BASH_REMATCH[1]}
    SUCCESS=${BASH_REMATCH[2]}
    AVG_TIME=${BASH_REMATCH[3]}
    
    TOTAL_REQUESTS=$((TOTAL_REQUESTS + REQUESTS))
    TOTAL_SUCCESS=$((TOTAL_SUCCESS + SUCCESS))
    TOTAL_AVG_TIME=$((TOTAL_AVG_TIME + AVG_TIME))
    USER_COUNT=$((USER_COUNT + 1))
  fi
done < "$RESULT_FILE"

# 计算总体统计
if [ $USER_COUNT -gt 0 ]; then
  OVERALL_AVG_TIME=$((TOTAL_AVG_TIME / USER_COUNT))
else
  OVERALL_AVG_TIME=0
fi

if [ $TOTAL_REQUESTS -gt 0 ]; then
  SUCCESS_RATE=$((TOTAL_SUCCESS * 100 / TOTAL_REQUESTS))
  QPS=$((TOTAL_REQUESTS / TEST_DURATION))
else
  SUCCESS_RATE=0
  QPS=0
fi

# 输出测试结果
echo ""
echo "=== 性能测试结果 ==="
echo "总请求数: $TOTAL_REQUESTS"
echo "成功请求数: $TOTAL_SUCCESS"
echo "成功率: ${SUCCESS_RATE}%"
echo "平均响应时间: ${OVERALL_AVG_TIME}ms"
echo "吞吐量: ${QPS} QPS"
echo ""

# 性能评估
echo "=== 性能评估 ==="
if [ $OVERALL_AVG_TIME -lt 200 ]; then
  echo "✅ 响应时间优秀 (< 200ms)"
elif [ $OVERALL_AVG_TIME -lt 500 ]; then
  echo "⚠️  响应时间良好 (200-500ms)"
else
  echo "❌ 响应时间需要优化 (> 500ms)"
fi

if [ $SUCCESS_RATE -gt 99 ]; then
  echo "✅ 成功率优秀 (> 99%)"
elif [ $SUCCESS_RATE -gt 95 ]; then
  echo "⚠️  成功率良好 (95-99%)"
else
  echo "❌ 成功率需要优化 (< 95%)"
fi

if [ $QPS -gt 50 ]; then
  echo "✅ 吞吐量优秀 (> 50 QPS)"
elif [ $QPS -gt 20 ]; then
  echo "⚠️  吞吐量良好 (20-50 QPS)"
else
  echo "❌ 吞吐量需要优化 (< 20 QPS)"
fi

# 清理临时文件
rm -f "$RESULT_FILE"

echo ""
echo "性能测试完成！"
