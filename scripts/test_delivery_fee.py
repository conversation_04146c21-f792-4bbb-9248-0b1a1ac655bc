#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配送费计算脚本

此脚本用于测试创建订单API中的配送费计算功能
验证配送费是否正确从数据库中获取

使用方法:
    python3 scripts/test_delivery_fee.py

依赖:
    pip install requests
"""

import requests
import json
import sys

# API配置
API_BASE_URL = "http://localhost:8181/api/v1"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def get_jwt_token():
    """
    获取JWT token用于认证
    """
    # 尝试用户登录获取token
    login_data = {
        "phone": "13800138000",
        "password": "123456"
    }
    
    try:
        # 尝试用户登录
        login_url = f"{API_BASE_URL}/user/login"
        response = requests.post(login_url, json=login_data, headers=HEADERS, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200 and 'data' in result:
                token = result['data'].get('token')
                if token:
                    print(f"✅ 成功获取用户JWT token")
                    return token
        
        # 如果用户登录失败，尝试管理员登录
        admin_login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        admin_login_url = f"{API_BASE_URL}/admin/login"
        response = requests.post(admin_login_url, json=admin_login_data, headers=HEADERS, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200 and 'data' in result:
                token = result['data'].get('token')
                if token:
                    print(f"✅ 成功获取管理员JWT token")
                    return token
        
        print(f"⚠️  无法获取JWT token，将尝试无认证测试")
        return None
        
    except Exception as e:
        print(f"⚠️  获取JWT token失败: {e}")
        return None

def test_create_takeout_order():
    """
    测试创建外卖订单API
    """
    print("🚀 开始测试创建外卖订单API...")
    print("=" * 80)
    
    # 获取JWT token
    token = get_jwt_token()
    
    # 准备请求头
    test_headers = HEADERS.copy()
    if token:
        test_headers["Authorization"] = f"Bearer {token}"
    
    # 构造测试订单数据
    order_data = {
        "userID": 1,
        "deliveryAddress": {
            "id": 1,
            "address": "测试地址",
            "latitude": 39.9042,
            "longitude": 116.4074
        },
        "items": [
            {
                "foodID": 1,
                "variantID": 1,
                "quantity": 2,
                "price": 15.0,
                "merchantID": 1,
                "note": "测试备注"
            }
        ],
        "paymentMethod": "wechat",
        "note": "测试订单"
    }
    
    try:
        # 发送创建订单请求
        url = f"{API_BASE_URL}/user/takeout/order/create"
        print(f"📡 发送请求到: {url}")
        print(f"📦 请求数据: {json.dumps(order_data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=order_data, headers=test_headers, timeout=10)
        
        print(f"\n📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        
        # 解析响应
        try:
            response_data = response.json()
            print(f"\n📋 响应数据:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            # 检查配送费
            if response.status_code == 200 and 'data' in response_data:
                order_info = response_data['data']
                if 'deliveryFee' in order_info:
                    delivery_fee = order_info['deliveryFee']
                    print(f"\n💰 配送费: {delivery_fee} 元")
                    
                    # 验证配送费是否为预期值（数据库中配置的2元）
                    expected_fee = 2.0
                    if abs(delivery_fee - expected_fee) < 0.01:
                        print(f"✅ 配送费正确！预期: {expected_fee} 元，实际: {delivery_fee} 元")
                        return True
                    else:
                        print(f"❌ 配送费错误！预期: {expected_fee} 元，实际: {delivery_fee} 元")
                        return False
                else:
                    print("❌ 响应中没有找到配送费信息")
                    return False
            else:
                print(f"❌ 创建订单失败: {response_data.get('message', '未知错误')}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ 解析响应JSON失败: {e}")
            print(f"原始响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_delivery_fee_calculation():
    """
    测试配送费计算API（如果存在）
    """
    print("\n🧮 测试配送费计算API...")
    print("-" * 60)
    
    # 构造配送费计算请求数据
    calc_data = {
        "merchantID": 1,
        "totalAmount": 30.0,
        "deliveryAddress": {
            "latitude": 39.9042,
            "longitude": 116.4074
        }
    }
    
    try:
        # 尝试调用配送费计算API
        url = f"{API_BASE_URL}/delivery/rules/calculate-fee"
        print(f"📡 发送请求到: {url}")
        
        response = requests.post(url, json=calc_data, headers=HEADERS, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📋 响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️  配送费计算API不可用或返回错误: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"⚠️  配送费计算API请求失败: {e}")

def main():
    """
    主函数
    """
    print("🔧 配送费计算测试工具")
    print("=" * 80)
    
    # 检查服务是否运行
    try:
        health_url = f"{API_BASE_URL}/health"
        response = requests.get(health_url, timeout=5)
        if response.status_code != 200:
            print(f"⚠️  服务健康检查失败，但继续测试...")
    except:
        print(f"⚠️  无法连接到服务，请确保服务正在运行在 {API_BASE_URL}")
        print(f"💡 提示: 请先启动服务 'go run main.go'")
        # 继续测试，可能健康检查接口不存在
    
    # 测试创建订单
    success = test_create_takeout_order()
    
    # 测试配送费计算（可选）
    test_delivery_fee_calculation()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ 测试完成！配送费计算功能正常")
        print("\n💡 总结:")
        print("   - 配送费正确从数据库获取")
        print("   - 配送费计算逻辑正常")
        print("   - API响应正确")
    else:
        print("❌ 测试失败！配送费计算存在问题")
        print("\n🔍 建议检查:")
        print("   1. 数据库中的配送费配置状态")
        print("   2. 配送费计算逻辑")
        print("   3. API响应格式")
        print("   4. 服务日志中的错误信息")

if __name__ == "__main__":
    main()