#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试配送费计算逻辑

此脚本通过直接查询数据库来验证配送费配置是否正确获取
"""

import pymysql
import json

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': 'o_mall_dev_2024!@#',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

def test_delivery_config_query():
    """
    测试配送费配置查询
    """
    print("🔍 测试配送费配置查询...")
    print("=" * 80)
    
    try:
        # 连接数据库
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        print("✅ 成功连接到数据库")
        
        # 测试1: 查询状态为1的配送费配置（原始逻辑）
        print("\n📋 测试1: 查询状态为1的配送费配置（原始逻辑）")
        query1 = """
        SELECT * FROM system_config 
        WHERE category = 'deliveryFee' AND status = 1
        """
        cursor.execute(query1)
        result1 = cursor.fetchall()
        
        print(f"查询结果数量: {len(result1)}")
        for config in result1:
            print(f"ID: {config['id']}, 状态: {config['status']}, 配置: {config['config_value']}")
        
        # 测试2: 查询所有状态的配送费配置（修改后逻辑）
        print("\n📋 测试2: 查询所有状态的配送费配置（修改后逻辑）")
        query2 = """
        SELECT * FROM system_config 
        WHERE category = 'deliveryFee'
        """
        cursor.execute(query2)
        result2 = cursor.fetchall()
        
        print(f"查询结果数量: {len(result2)}")
        for config in result2:
            print(f"ID: {config['id']}, 状态: {config['status']}, 配置: {config['config_value']}")
            
            # 尝试解析JSON配置
            try:
                config_json = json.loads(config['config_value'])
                print(f"  解析后的配置:")
                for key, value in config_json.items():
                    print(f"    {key}: {value}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
        
        # 测试3: 模拟Go代码中的查询逻辑
        print("\n📋 测试3: 模拟Go代码查询逻辑")
        
        # 原始逻辑（status=1）
        print("\n  原始逻辑（status=1）:")
        if len(result1) > 0:
            config = result1[0]
            try:
                config_json = json.loads(config['config_value'])
                delivery_base_fee = config_json.get('deliveryBaseFee', 0)
                print(f"    ✅ 获取到配送费: {delivery_base_fee} 元")
            except:
                print(f"    ❌ 配置解析失败，使用默认值")
        else:
            print(f"    ❌ 未找到配置，使用默认值")
        
        # 修改后逻辑（所有状态）
        print("\n  修改后逻辑（所有状态）:")
        if len(result2) > 0:
            config = result2[0]
            try:
                config_json = json.loads(config['config_value'])
                delivery_base_fee = config_json.get('deliveryBaseFee', 0)
                print(f"    ✅ 获取到配送费: {delivery_base_fee} 元")
            except:
                print(f"    ❌ 配置解析失败，使用默认值")
        else:
            print(f"    ❌ 未找到配置，使用默认值")
        
        cursor.close()
        connection.close()
        
        # 总结
        print("\n" + "=" * 80)
        print("📊 测试总结:")
        print(f"  - 状态为1的配置数量: {len(result1)}")
        print(f"  - 所有状态的配置数量: {len(result2)}")
        
        if len(result1) == 0 and len(result2) > 0:
            print("  ✅ 修改生效！原始逻辑无法获取配置，修改后逻辑可以获取配置")
            return True
        elif len(result1) > 0:
            print("  ⚠️  原始逻辑也能获取配置，可能数据库中有状态为1的配置")
            return True
        else:
            print("  ❌ 两种逻辑都无法获取配置")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接或查询失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("🔧 配送费配置查询测试工具")
    print("=" * 80)
    
    success = test_delivery_config_query()
    
    if success:
        print("\n✅ 测试完成！配送费配置查询功能正常")
    else:
        print("\n❌ 测试失败！配送费配置查询存在问题")

if __name__ == "__main__":
    main()