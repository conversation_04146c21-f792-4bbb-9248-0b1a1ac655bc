package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
)

// 测试执行商家关店任务
func main() {
	// 从命令行参数获取任务ID
	if len(os.Args) < 2 {
		fmt.Println("请提供任务ID，例如: go run scripts/test_merchant_close_task.go merchant_close_1_5_14:20")
		return
	}
	taskID := os.Args[1]

	// 构建请求URL
	url := fmt.Sprintf("http://localhost:8181/api/v1/scheduler/tasks/%s/execute", taskID)

	// 创建PUT请求
	req, err := http.NewRequest("PUT", url, nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求执行失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应体
	body, _ := io.ReadAll(resp.Body)

	// 输出响应
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))
}
