#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商家上下文和缓存问题测试脚本

这个脚本用于测试商家ID获取和缓存验证的问题
根据分析，问题可能出现在:
1. 商家ID从上下文获取失败
2. 缓存验证逻辑过滤了数据
3. 前端传递的参数有问题

使用方法:
    python3 scripts/test_merchant_context.py
"""

import pymysql
import sys
import json
from typing import List, Dict, Any

# 数据库配置信息
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': '4wk2HTRWkxKxwhHX',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

def connect_database():
    """
    连接到MySQL数据库
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def check_merchant_data(connection):
    """
    检查商家相关数据
    """
    print(f"\n🏪 检查商家数据")
    print("=" * 80)
    
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 检查商家表
        try:
            sql = "SELECT id, name, status FROM merchant LIMIT 10"
            cursor.execute(sql)
            merchants = cursor.fetchall()
            print(f"商家表记录数: {len(merchants)}")
            for merchant in merchants:
                print(f"  商家ID: {merchant['id']}, 名称: {merchant['name']}, 状态: {merchant['status']}")
        except Exception as e:
            print(f"查询商家表失败: {e}")
        
        # 检查每个商家的食品数量
        try:
            sql = "SELECT merchant_id, COUNT(*) as food_count FROM takeout_food GROUP BY merchant_id"
            cursor.execute(sql)
            food_counts = cursor.fetchall()
            print(f"\n各商家食品数量:")
            for count in food_counts:
                print(f"  商家ID: {count['merchant_id']}, 食品数量: {count['food_count']}")
        except Exception as e:
            print(f"查询食品统计失败: {e}")

def simulate_api_query_logic(connection, merchant_id=1):
    """
    模拟API查询逻辑，特别是缓存验证部分
    """
    print(f"\n🔧 模拟API查询逻辑 (商家ID: {merchant_id})")
    print("=" * 80)
    
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 1. 模拟基础查询（无任何过滤）
        print(f"1. 基础查询 - 所有食品:")
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s ORDER BY sort_order ASC"
        cursor.execute(sql, (merchant_id,))
        all_foods = cursor.fetchall()
        print(f"   结果: {len(all_foods)} 条")
        for food in all_foods:
            print(f"   - ID: {food['id']}, 名称: {food['name']}, 状态: {food['status']}, 审核: {food['audit_status']}")
        
        # 2. 模拟缓存验证逻辑中的随机抽样
        print(f"\n2. 模拟缓存验证 - 随机抽样检查:")
        if all_foods:
            # 假设抽样第一个食品进行验证
            sample_food = all_foods[0]
            print(f"   抽样食品: ID {sample_food['id']}, 名称: {sample_food['name']}")
            
            # 验证食品是否存在
            sql = "SELECT * FROM takeout_food WHERE id = %s AND merchant_id = %s"
            cursor.execute(sql, (sample_food['id'], merchant_id))
            verify_result = cursor.fetchone()
            
            if verify_result:
                print(f"   ✅ 验证通过: 食品存在")
                print(f"   状态一致性: 缓存状态={sample_food['status']}, 数据库状态={verify_result['status']}")
                if sample_food['status'] != verify_result['status']:
                    print(f"   ⚠️  状态不一致！这可能导致缓存失效")
            else:
                print(f"   ❌ 验证失败: 食品不存在")
        
        # 3. 模拟前端可能的查询参数组合
        print(f"\n3. 模拟前端查询参数组合:")
        
        query_scenarios = [
            {
                "name": "默认查询 (无过滤)",
                "conditions": "merchant_id = %s",
                "params": (merchant_id,)
            },
            {
                "name": "过滤下架商品",
                "conditions": "merchant_id = %s AND status != 2",
                "params": (merchant_id,)
            },
            {
                "name": "只显示上架商品",
                "conditions": "merchant_id = %s AND status = 1",
                "params": (merchant_id,)
            },
            {
                "name": "上架且审核通过",
                "conditions": "merchant_id = %s AND status = 1 AND audit_status = 1",
                "params": (merchant_id,)
            },
            {
                "name": "上架、审核通过且未售罄",
                "conditions": "merchant_id = %s AND status = 1 AND audit_status = 1 AND sold_out = 0",
                "params": (merchant_id,)
            }
        ]
        
        for scenario in query_scenarios:
            sql = f"SELECT * FROM takeout_food WHERE {scenario['conditions']} ORDER BY sort_order ASC LIMIT 10"
            cursor.execute(sql, scenario['params'])
            results = cursor.fetchall()
            print(f"   {scenario['name']}: {len(results)} 条")
            
            if len(results) == 1 and results[0]['status'] == 2:
                print(f"   🎯 这个查询结果与前端返回一致！")
                print(f"   返回的食品: ID {results[0]['id']}, 名称: {results[0]['name']}")

def check_cache_related_tables(connection):
    """
    检查缓存相关的表或配置
    """
    print(f"\n💾 检查缓存相关信息")
    print("=" * 80)
    
    with connection.cursor() as cursor:
        # 检查是否有缓存相关的表
        sql = "SHOW TABLES LIKE '%cache%'"
        cursor.execute(sql)
        cache_tables = cursor.fetchall()
        
        if cache_tables:
            print(f"发现缓存相关表: {cache_tables}")
        else:
            print(f"未发现缓存相关表（可能使用Redis等外部缓存）")
        
        # 检查配置表
        try:
            sql = "SHOW TABLES LIKE '%config%'"
            cursor.execute(sql)
            config_tables = cursor.fetchall()
            if config_tables:
                print(f"发现配置相关表: {config_tables}")
        except:
            pass

def analyze_problem_patterns(connection, merchant_id=1):
    """
    分析问题模式
    """
    print(f"\n🔍 问题模式分析")
    print("=" * 80)
    
    with connection.cursor(pymysql.cursors.DictCursor) as cursor:
        # 检查是否存在只返回一条下架商品的查询模式
        sql = "SELECT * FROM takeout_food WHERE merchant_id = %s AND status = 2 ORDER BY id DESC LIMIT 1"
        cursor.execute(sql, (merchant_id,))
        latest_offline = cursor.fetchone()
        
        if latest_offline:
            print(f"最新的下架商品:")
            print(f"  ID: {latest_offline['id']}, 名称: {latest_offline['name']}")
            print(f"  创建时间: {latest_offline['created_at']}")
            print(f"  更新时间: {latest_offline['updated_at']}")
            
            # 检查这个商品是否是最近更新的
            sql = "SELECT * FROM takeout_food WHERE merchant_id = %s ORDER BY updated_at DESC LIMIT 1"
            cursor.execute(sql, (merchant_id,))
            latest_updated = cursor.fetchone()
            
            if latest_updated and latest_updated['id'] == latest_offline['id']:
                print(f"  🎯 这个下架商品是最近更新的！")
                print(f"  这可能解释了为什么缓存中只有这一条记录")
        
        # 检查排序字段
        print(f"\n排序字段分析:")
        sql = "SELECT id, name, sort_order, created_at, updated_at FROM takeout_food WHERE merchant_id = %s ORDER BY sort_order ASC"
        cursor.execute(sql, (merchant_id,))
        sorted_foods = cursor.fetchall()
        
        for food in sorted_foods:
            print(f"  ID: {food['id']}, 排序: {food['sort_order']}, 名称: {food['name']}")

def main():
    """
    主函数
    """
    print("🔍 商家上下文和缓存问题深度分析")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 检查商家数据
        check_merchant_data(connection)
        
        # 模拟API查询逻辑
        simulate_api_query_logic(connection, 1)
        
        # 检查缓存相关表
        check_cache_related_tables(connection)
        
        # 分析问题模式
        analyze_problem_patterns(connection, 1)
        
        print(f"\n" + "=" * 80)
        print(f"📊 分析结论:")
        print(f"\n根据前端只返回一条状态为2的下架商品，可能的原因:")
        print(f"1. 🔥 缓存验证失败，导致只返回了最近更新的一条记录")
        print(f"2. 🔥 商家ID获取错误，实际查询的不是商家ID=1")
        print(f"3. 🔥 前端传递了隐含的过滤参数（如只查询最近更新的）")
        print(f"4. 🔥 缓存中的数据过期或损坏")
        
        print(f"\n💡 建议的调试步骤:")
        print(f"1. 在Go代码中添加日志，记录实际获取到的商家ID")
        print(f"2. 在缓存验证服务中添加日志，记录验证过程")
        print(f"3. 检查前端实际发送的请求参数")
        print(f"4. 清理所有缓存，强制从数据库重新加载")
        print(f"5. 检查isValidCachedData方法的抽样逻辑")
        
    finally:
        connection.close()
        print(f"\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()