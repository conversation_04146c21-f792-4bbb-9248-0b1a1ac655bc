#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试包装费计算逻辑脚本

此脚本用于验证修改后的包装费计算是否正确
包装费应该统一使用商品的packaging_fee，而不是规格的packaging_fee
计算公式：商品包装费 * 数量

使用方法:
    python3 scripts/test_packaging_fee_calculation.py

依赖:
    pip install requests
"""

import requests
import json
import sys
from typing import Dict, Any

# API配置
API_BASE_URL = "http://localhost:8181"
API_ENDPOINTS = {
    "login": "/api/user/login",
    "cart_add": "/api/takeout/cart/add",
    "cart_list": "/api/takeout/cart/list",
    "order_create": "/api/takeout/order/create",
    "food_list": "/api/takeout/food/list"
}

# 测试用户信息
TEST_USER = {
    "phone": "13800138000",
    "password": "123456"
}

class PackagingFeeTest:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.user_id = None
        
    def login(self) -> bool:
        """
        用户登录获取token
        
        Returns:
            bool: 登录是否成功
        """
        try:
            url = f"{API_BASE_URL}{API_ENDPOINTS['login']}"
            data = {
                "phone": TEST_USER["phone"],
                "password": TEST_USER["password"]
            }
            
            response = self.session.post(url, json=data)
            print(f"🔐 登录请求: {url}")
            print(f"📤 请求数据: {json.dumps(data, ensure_ascii=False)}")
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📥 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get("code") == 200:
                    self.token = result["data"]["token"]
                    self.user_id = result["data"]["user_id"]
                    self.session.headers.update({"Authorization": f"Bearer {self.token}"})
                    print(f"✅ 登录成功，用户ID: {self.user_id}")
                    return True
                else:
                    print(f"❌ 登录失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 登录请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_food_list(self) -> list:
        """
        获取商品列表
        
        Returns:
            list: 商品列表
        """
        try:
            url = f"{API_BASE_URL}{API_ENDPOINTS['food_list']}"
            params = {"page": 1, "page_size": 10}
            
            response = self.session.get(url, params=params)
            print(f"\n🍔 获取商品列表: {url}")
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    foods = result["data"]["list"]
                    print(f"✅ 获取到 {len(foods)} 个商品")
                    
                    # 显示商品信息
                    print("\n📋 商品列表:")
                    print("-" * 100)
                    print(f"{'ID':<5} {'名称':<15} {'价格':<8} {'包装费':<8} {'有规格':<8} {'商家ID':<8}")
                    print("-" * 100)
                    
                    for food in foods:
                        print(f"{food['id']:<5} {food['name']:<15} {food['price']:<8.2f} {food['packaging_fee']:<8.2f} {food['has_variants']:<8} {food['merchant_id']:<8}")
                    
                    return foods
                else:
                    print(f"❌ 获取商品列表失败: {result.get('message', '未知错误')}")
                    return []
            else:
                print(f"❌ 获取商品列表请求失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取商品列表异常: {e}")
            return []
    
    def add_to_cart(self, food_id: int, variant_id: int = 0, quantity: int = 1) -> bool:
        """
        添加商品到购物车
        
        Args:
            food_id: 商品ID
            variant_id: 规格ID，0表示无规格
            quantity: 数量
            
        Returns:
            bool: 添加是否成功
        """
        try:
            url = f"{API_BASE_URL}{API_ENDPOINTS['cart_add']}"
            data = {
                "food_id": food_id,
                "quantity": quantity
            }
            
            if variant_id > 0:
                data["variant_id"] = variant_id
            
            response = self.session.post(url, json=data)
            print(f"\n🛒 添加到购物车: {url}")
            print(f"📤 请求数据: {json.dumps(data, ensure_ascii=False)}")
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📥 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get("code") == 200:
                    print(f"✅ 添加到购物车成功")
                    return True
                else:
                    print(f"❌ 添加到购物车失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 添加到购物车请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 添加到购物车异常: {e}")
            return False
    
    def get_cart_list(self) -> list:
        """
        获取购物车列表
        
        Returns:
            list: 购物车项列表
        """
        try:
            url = f"{API_BASE_URL}{API_ENDPOINTS['cart_list']}"
            
            response = self.session.get(url)
            print(f"\n🛒 获取购物车列表: {url}")
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    cart_items = result["data"]
                    print(f"✅ 获取到 {len(cart_items)} 个购物车项")
                    
                    # 显示购物车信息
                    print("\n📋 购物车列表:")
                    print("-" * 120)
                    print(f"{'购物车ID':<10} {'商品ID':<8} {'商品名称':<15} {'规格ID':<8} {'规格名称':<15} {'数量':<6} {'单价':<8} {'小计':<8}")
                    print("-" * 120)
                    
                    for item in cart_items:
                        variant_name = item.get('variant_name', '无规格')
                        subtotal = item['price'] * item['quantity']
                        print(f"{item['cart_item_id']:<10} {item['food_id']:<8} {item['food_name']:<15} {item.get('variant_id', 0):<8} {variant_name:<15} {item['quantity']:<6} {item['price']:<8.2f} {subtotal:<8.2f}")
                    
                    return cart_items
                else:
                    print(f"❌ 获取购物车列表失败: {result.get('message', '未知错误')}")
                    return []
            else:
                print(f"❌ 获取购物车列表请求失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 获取购物车列表异常: {e}")
            return []
    
    def create_order(self, cart_item_ids: list) -> Dict[str, Any]:
        """
        创建订单
        
        Args:
            cart_item_ids: 购物车项ID列表
            
        Returns:
            Dict[str, Any]: 订单信息
        """
        try:
            url = f"{API_BASE_URL}{API_ENDPOINTS['order_create']}"
            data = {
                "merchant_orders": [
                    {
                        "merchant_id": 1,  # 假设商家ID为1
                        "cart_item_ids": cart_item_ids,
                        "coupon_id": 0,
                        "promotion_ids": [],
                        "delivery_time": "",
                        "remark": "测试包装费计算"
                    }
                ],
                "address_id": 1,  # 假设地址ID为1
                "payment_method": 1
            }
            
            response = self.session.post(url, json=data)
            print(f"\n📦 创建订单: {url}")
            print(f"📤 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            print(f"📥 响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📥 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get("code") == 200:
                    print(f"✅ 订单创建成功")
                    return result["data"]
                else:
                    print(f"❌ 订单创建失败: {result.get('message', '未知错误')}")
                    return {}
            else:
                print(f"❌ 订单创建请求失败，状态码: {response.status_code}")
                return {}
                
        except Exception as e:
            print(f"❌ 订单创建异常: {e}")
            return {}
    
    def analyze_packaging_fee(self, order_data: Dict[str, Any], cart_items: list, foods: list) -> None:
        """
        分析包装费计算是否正确
        
        Args:
            order_data: 订单数据
            cart_items: 购物车项列表
            foods: 商品列表
        """
        print("\n" + "=" * 80)
        print("📊 包装费计算分析")
        print("=" * 80)
        
        # 创建商品映射
        food_map = {food['id']: food for food in foods}
        
        if 'orders' in order_data and len(order_data['orders']) > 0:
            order = order_data['orders'][0]
            
            print(f"\n📋 订单信息:")
            print(f"订单ID: {order.get('order_id', 'N/A')}")
            print(f"订单总金额: {order.get('total_amount', 0):.2f}")
            print(f"实付金额: {order.get('actual_amount', 0):.2f}")
            print(f"配送费: {order.get('delivery_fee', 0):.2f}")
            print(f"包装费: {order.get('packaging_fee', 0):.2f}")
            
            # 计算预期包装费
            expected_packaging_fee = 0.0
            print(f"\n🧮 包装费计算验证:")
            print("-" * 80)
            print(f"{'商品名称':<15} {'商品包装费':<10} {'数量':<6} {'小计包装费':<12}")
            print("-" * 80)
            
            for cart_item in cart_items:
                food_id = cart_item['food_id']
                quantity = cart_item['quantity']
                
                if food_id in food_map:
                    food = food_map[food_id]
                    food_packaging_fee = food['packaging_fee']
                    item_packaging_fee = food_packaging_fee * quantity
                    expected_packaging_fee += item_packaging_fee
                    
                    print(f"{food['name']:<15} {food_packaging_fee:<10.2f} {quantity:<6} {item_packaging_fee:<12.2f}")
            
            print("-" * 80)
            print(f"预期包装费总计: {expected_packaging_fee:.2f}")
            print(f"实际包装费总计: {order.get('packaging_fee', 0):.2f}")
            
            # 验证结果
            if abs(expected_packaging_fee - order.get('packaging_fee', 0)) < 0.01:
                print("\n✅ 包装费计算正确！")
            else:
                print("\n❌ 包装费计算错误！")
                print(f"差异: {abs(expected_packaging_fee - order.get('packaging_fee', 0)):.2f}")
        else:
            print("❌ 订单数据不完整，无法分析包装费")
    
    def run_test(self) -> None:
        """
        运行完整测试
        """
        print("🧪 开始包装费计算测试")
        print("=" * 80)
        
        # 1. 登录
        if not self.login():
            print("❌ 登录失败，测试终止")
            return
        
        # 2. 获取商品列表
        foods = self.get_food_list()
        if not foods:
            print("❌ 获取商品列表失败，测试终止")
            return
        
        # 3. 选择测试商品（选择前3个商品）
        test_foods = foods[:3]
        print(f"\n🎯 选择测试商品: {[food['name'] for food in test_foods]}")
        
        # 4. 添加到购物车
        for food in test_foods:
            if not self.add_to_cart(food['id'], 0, 2):  # 每个商品添加2个
                print(f"❌ 添加商品 {food['name']} 到购物车失败")
                return
        
        # 5. 获取购物车列表
        cart_items = self.get_cart_list()
        if not cart_items:
            print("❌ 获取购物车列表失败，测试终止")
            return
        
        # 6. 创建订单
        cart_item_ids = [item['cart_item_id'] for item in cart_items]
        order_data = self.create_order(cart_item_ids)
        if not order_data:
            print("❌ 创建订单失败，测试终止")
            return
        
        # 7. 分析包装费计算
        self.analyze_packaging_fee(order_data, cart_items, foods)
        
        print("\n" + "=" * 80)
        print("🎉 测试完成！")
        print("=" * 80)

def main():
    """
    主函数
    """
    test = PackagingFeeTest()
    test.run_test()

if __name__ == "__main__":
    main()