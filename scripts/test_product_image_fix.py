#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ProductImage字段修复效果

本脚本用于测试修复后的外卖订单创建流程，验证ProductImage字段是否正确传递和保存。
"""

import requests
import json
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8181"

# 测试用户信息
TEST_USER_ID = 1
TEST_TOKEN = "your_test_token_here"  # 需要替换为实际的测试token

def test_cart_summary():
    """测试购物车汇总API，检查FoodImage字段"""
    print("🔍 测试购物车汇总API...")
    print("-" * 50)
    
    url = f"{BASE_URL}/api/takeout/cart/summary"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {TEST_TOKEN}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                cart_data = data.get("data", {})
                items = cart_data.get("items", [])
                
                print(f"购物车项数量: {len(items)}")
                
                for i, item in enumerate(items):
                    food_image = item.get("food_image", "")
                    food_name = item.get("food_name", "")
                    image_status = "✅ 有图片" if food_image else "❌ 无图片"
                    
                    print(f"  项目 {i+1}: {food_name}")
                    print(f"    图片状态: {image_status}")
                    if food_image:
                        print(f"    图片URL: {food_image}")
                
                return items
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"HTTP错误: {response.text}")
    
    except Exception as e:
        print(f"请求失败: {e}")
    
    return []

def test_create_order():
    """测试创建订单API，检查ProductImage字段传递"""
    print("\n🔍 测试创建订单API...")
    print("-" * 50)
    
    url = f"{BASE_URL}/api/takeout/orders"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {TEST_TOKEN}"
    }
    
    # 构造测试订单数据
    order_data = {
        "takeout_address_id": 1,  # 需要替换为实际的地址ID
        "payment_method": 1,
        "remark": "测试ProductImage字段修复",
        "coupon_id": 0
    }
    
    try:
        response = requests.post(url, headers=headers, json=order_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                order_info = data.get("data", {})
                order_id = order_info.get("id")
                
                print(f"✅ 订单创建成功!")
                print(f"订单ID: {order_id}")
                
                # 获取订单详情检查ProductImage
                return test_order_detail(order_id)
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"HTTP错误: {response.text}")
    
    except Exception as e:
        print(f"请求失败: {e}")
    
    return None

def test_order_detail(order_id):
    """测试订单详情API，检查ProductImage字段"""
    print(f"\n🔍 测试订单详情API (订单ID: {order_id})...")
    print("-" * 50)
    
    url = f"{BASE_URL}/api/takeout/orders/{order_id}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {TEST_TOKEN}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                order_data = data.get("data", {})
                items = order_data.get("items", [])
                
                print(f"订单项数量: {len(items)}")
                
                success_count = 0
                total_count = len(items)
                
                for i, item in enumerate(items):
                    product_image = item.get("product_image", "")
                    product_name = item.get("product_name", "")
                    image_status = "✅ 有图片" if product_image else "❌ 无图片"
                    
                    if product_image:
                        success_count += 1
                    
                    print(f"  订单项 {i+1}: {product_name}")
                    print(f"    图片状态: {image_status}")
                    if product_image:
                        print(f"    图片URL: {product_image}")
                
                print(f"\n📊 修复效果统计:")
                print(f"  总订单项数: {total_count}")
                print(f"  有图片的项: {success_count}")
                print(f"  修复成功率: {success_count/total_count*100:.1f}%" if total_count > 0 else "0%")
                
                if success_count == total_count and total_count > 0:
                    print("\n🎉 修复完全成功！所有订单项都有ProductImage字段！")
                elif success_count > 0:
                    print(f"\n⚠️  部分修复成功，还有 {total_count - success_count} 个订单项缺少图片")
                else:
                    print("\n❌ 修复失败，所有订单项仍然缺少ProductImage字段")
                
                return order_data
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"HTTP错误: {response.text}")
    
    except Exception as e:
        print(f"请求失败: {e}")
    
    return None

def test_recent_orders():
    """测试最近订单列表，检查ProductImage字段"""
    print("\n🔍 测试最近订单列表...")
    print("-" * 50)
    
    url = f"{BASE_URL}/api/takeout/orders"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {TEST_TOKEN}"
    }
    
    params = {
        "page": 1,
        "page_size": 5
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                orders_data = data.get("data", {})
                orders = orders_data.get("orders", [])
                
                print(f"最近订单数量: {len(orders)}")
                
                total_items = 0
                items_with_image = 0
                
                for i, order in enumerate(orders):
                    order_id = order.get("id")
                    items = order.get("items", [])
                    
                    order_items_with_image = 0
                    for item in items:
                        total_items += 1
                        if item.get("product_image"):
                            items_with_image += 1
                            order_items_with_image += 1
                    
                    print(f"  订单 {i+1} (ID: {order_id}): {order_items_with_image}/{len(items)} 项有图片")
                
                print(f"\n📊 整体统计:")
                print(f"  总订单项数: {total_items}")
                print(f"  有图片的项: {items_with_image}")
                print(f"  图片完整率: {items_with_image/total_items*100:.1f}%" if total_items > 0 else "0%")
                
            else:
                print(f"API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"HTTP错误: {response.text}")
    
    except Exception as e:
        print(f"请求失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试ProductImage字段修复效果")
    print("=" * 80)
    
    # 测试购物车汇总
    cart_items = test_cart_summary()
    
    if cart_items:
        # 测试创建订单
        test_create_order()
    else:
        print("\n⚠️  购物车为空，跳过订单创建测试")
    
    # 测试最近订单
    test_recent_orders()
    
    print("\n🏁 测试完成！")
    print("=" * 80)
    
    print("\n💡 如果修复成功，你应该看到:")
    print("  1. 购物车项中有food_image字段")
    print("  2. 新创建的订单项中有product_image字段")
    print("  3. 订单详情API返回的订单项包含完整的图片信息")
    
    print("\n🔧 如果仍有问题，请检查:")
    print("  1. 数据库中takeout_food表的image字段是否有数据")
    print("  2. 购物车服务是否正确获取了商品图片")
    print("  3. 订单创建流程中数据传递是否正确")

if __name__ == "__main__":
    main()