/**
 * 七牛云上传测试工具
 * 用于测试七牛云配置和上传功能
 */
package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
)

// 七牛云配置
type QiniuConfig struct {
	AccessKey string
	SecretKey string
	Bucket    string
	Domain    string
	Zone      string
	UseHTTPS  bool
}

// 测试七牛云上传功能
func testQiniuUpload() {
	// 配置信息（请根据实际情况修改）
	config := QiniuConfig{
		AccessKey: "UK****hk", // 请填入完整的AccessKey
		SecretKey: "iG****qI", // 请填入完整的SecretKey
		Bucket:    "omallimg",
		Domain:    "omimg.qwyx.shop",
		Zone:      "z2",
		UseHTTPS:  false,
	}

	fmt.Println("=== 七牛云上传测试开始 ===")
	fmt.Printf("Bucket: %s\n", config.Bucket)
	fmt.Printf("Domain: %s\n", config.Domain)
	fmt.Printf("Zone: %s\n", config.Zone)
	fmt.Printf("HTTPS: %v\n", config.UseHTTPS)

	// 创建鉴权对象
	mac := qbox.NewMac(config.AccessKey, config.SecretKey)

	// 配置存储区域
	var cfg *storage.Config
	switch config.Zone {
	case "z0":
		cfg = &storage.Config{Zone: &storage.ZoneHuadong}
	case "z1":
		cfg = &storage.Config{Zone: &storage.ZoneHuabei}
	case "z2":
		cfg = &storage.Config{Zone: &storage.ZoneHuanan}
	case "na0":
		cfg = &storage.Config{Zone: &storage.ZoneBeimei}
	case "as0":
		cfg = &storage.Config{Zone: &storage.ZoneXinjiapo}
	default:
		fmt.Printf("警告: 未知的存储区域 %s，使用默认配置\n", config.Zone)
		cfg = &storage.Config{}
	}

	// 创建上传策略
	putPolicy := storage.PutPolicy{
		Scope: config.Bucket,
	}
	upToken := putPolicy.UploadToken(mac)
	fmt.Println("✓ 上传凭证生成成功")

	// 创建测试文件内容
	testContent := "这是一个测试文件，用于验证七牛云上传功能。\n测试时间: " + fmt.Sprintf("%d", os.Getpid())
	testFileName := "test_upload.txt"
	testFilePath := "test/" + testFileName

	// 创建上传表单对象
	formUploader := storage.NewFormUploader(cfg)
	ret := storage.PutRet{}

	// 上传数据
	putExtra := storage.PutExtra{
		Params: map[string]string{
			"x:name": testFileName,
		},
	}

	fmt.Printf("开始上传测试文件: %s\n", testFilePath)
	dataLen := int64(len(testContent))
	err := formUploader.Put(context.Background(), &ret, upToken, testFilePath, 
		strings.NewReader(testContent), dataLen, &putExtra)

	if err != nil {
		fmt.Printf("❌ 上传失败: %v\n", err)
		fmt.Println("\n可能的原因:")
		fmt.Println("1. AccessKey/SecretKey 错误")
		fmt.Println("2. Bucket 名称错误或不存在")
		fmt.Println("3. 存储区域配置错误")
		fmt.Println("4. 网络连接问题")
		fmt.Println("5. Bucket 权限配置问题")
		return
	}

	fmt.Printf("✓ 上传成功!\n")
	fmt.Printf("  Key: %s\n", ret.Key)
	fmt.Printf("  Hash: %s\n", ret.Hash)

	// 生成访问URL
	protocol := "http"
	if config.UseHTTPS {
		protocol = "https"
	}

	if config.Domain != "" {
		fileURL := fmt.Sprintf("%s://%s/%s", protocol, config.Domain, testFilePath)
		fmt.Printf("  访问URL: %s\n", fileURL)
	} else {
		fmt.Println("  警告: 未配置自定义域名，无法生成访问URL")
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("如果看到此消息，说明七牛云配置正确，上传功能正常。")
}

func main() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	testQiniuUpload()
}