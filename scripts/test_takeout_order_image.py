#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试外卖订单API中的image字段
验证修改MainImage为Image后，订单项是否正确返回图片信息
"""

import requests
import json

def test_takeout_orders():
    """测试外卖订单列表API"""
    # 测试用户ID，需要确保该用户有订单数据
    user_id = 1
    url = f"http://localhost:8181/api/v1/takeout/orders/user/{user_id}"
    
    try:
        print(f"🔍 测试外卖订单API: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n✅ API响应成功:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 分析订单数据中的image字段
            if 'data' in data and isinstance(data['data'], list):
                orders = data['data']
                print(f"\n📊 返回订单数量: {len(orders)}")
                
                for i, order in enumerate(orders, 1):
                    if isinstance(order, dict):
                        print(f"\n🛒 订单 #{i}:")
                        print(f"  订单ID: {order.get('id')}")
                        print(f"  订单状态: {order.get('status')}")
                        print(f"  总金额: ¥{order.get('total_amount')}")
                        
                        # 检查订单项中的image字段
                        items = order.get('items', [])
                        print(f"  订单项数量: {len(items)}")
                        
                        for j, item in enumerate(items, 1):
                            print(f"    项目 #{j}:")
                            print(f"      商品名称: {item.get('food_name')}")
                            print(f"      商品价格: ¥{item.get('price')}")
                            print(f"      数量: {item.get('quantity')}")
                            print(f"      图片URL: {item.get('image', 'N/A')}")
                            
                            # 重点检查image字段
                            if 'image' in item and item['image']:
                                print(f"      ✅ image字段存在且有值")
                            else:
                                print(f"      ❌ image字段缺失或为空")
                    else:
                        print(f"  {i}. 异常订单数据: {type(order)} - {order}")
            else:
                print(f"\n⚠️ 数据格式异常: {type(data.get('data'))}")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_takeout_food_list():
    """测试外卖商品列表API，验证商品的image字段"""
    url = "http://localhost:8181/api/v1/takeout/foods"
    
    try:
        print(f"\n🔍 测试外卖商品列表API: {url}")
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n✅ 商品列表API响应成功")
            
            # 分析商品数据中的image字段
            if 'data' in data and isinstance(data['data'], dict) and 'list' in data['data']:
                foods = data['data']['list']
                print(f"\n📊 返回商品数量: {len(foods)}")
                
                for i, food in enumerate(foods[:3], 1):  # 只显示前3个商品
                    if isinstance(food, dict):
                        print(f"\n🍽️ 商品 #{i}:")
                        print(f"  商品ID: {food.get('id')}")
                        print(f"  商品名称: {food.get('name')}")
                        print(f"  商品价格: ¥{food.get('price')}")
                        print(f"  图片URL: {food.get('image', 'N/A')}")
                        
                        # 重点检查image字段
                        if 'image' in food and food['image']:
                            print(f"  ✅ image字段存在且有值")
                        else:
                            print(f"  ❌ image字段缺失或为空")
            else:
                print(f"\n⚠️ 商品数据格式异常")
                
        else:
            print(f"❌ 商品列表API请求失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 商品列表请求异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试外卖订单和商品的image字段...")
    print("=" * 80)
    
    # 测试商品列表API
    test_takeout_food_list()
    
    # 测试订单列表API
    test_takeout_orders()
    
    print("\n" + "=" * 80)
    print("✅ 测试完成！")