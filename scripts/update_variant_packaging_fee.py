#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新外卖规格包装费脚本

此脚本用于更新takeout_food_variant表中的包装费数据
解决包装费计算不准确的问题

使用方法:
    python3 scripts/update_variant_packaging_fee.py

依赖:
    pip install pymysql
"""

import pymysql
import sys
from typing import List, Dict, Any

# 数据库配置信息
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'o_mall_dev',
    'password': '4wk2HTRWkxKxwhHX',
    'database': 'o_mall_dev',
    'charset': 'utf8mb4'
}

def connect_database():
    """
    连接到MySQL数据库
    
    Returns:
        pymysql.Connection: 数据库连接对象
    
    Raises:
        Exception: 数据库连接失败时抛出异常
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def update_variant_packaging_fee(connection):
    """
    更新规格的包装费数据
    
    Args:
        connection: 数据库连接对象
    """
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 首先查看当前的规格数据
            sql_select = """
            SELECT 
                v.id as variant_id,
                v.food_id,
                v.name as variant_name,
                v.price as variant_price,
                v.packaging_fee as current_packaging_fee,
                f.name as food_name,
                f.packaging_fee as food_packaging_fee
            FROM takeout_food_variant v
            LEFT JOIN takeout_food f ON v.food_id = f.id
            ORDER BY v.id
            """
            cursor.execute(sql_select)
            variants = cursor.fetchall()
            
            print(f"\n📊 当前规格数据:")
            print("-" * 120)
            print(f"{'规格ID':<8} {'商品ID':<8} {'规格名称':<15} {'规格价格':<10} {'当前包装费':<12} {'商品名称':<20} {'商品包装费':<12}")
            print("-" * 120)
            
            for variant in variants:
                print(f"{variant['variant_id']:<8} {variant['food_id']:<8} {variant['variant_name']:<15} {variant['variant_price']:<10.2f} {variant['current_packaging_fee']:<12.2f} {variant['food_name']:<20} {variant['food_packaging_fee']:<12.2f}")
            
            # 更新规格包装费
            # 策略：根据规格大小设置不同的包装费
            print(f"\n🔄 开始更新规格包装费...")
            
            update_count = 0
            for variant in variants:
                variant_id = variant['variant_id']
                variant_name = variant['variant_name'].lower()
                food_packaging_fee = variant['food_packaging_fee']
                
                # 根据规格名称设置包装费
                # 转换为float类型避免Decimal运算错误
                food_packaging_fee_float = float(food_packaging_fee) if food_packaging_fee else 0.0
                new_packaging_fee = food_packaging_fee_float  # 默认使用商品的包装费
                
                if '小' in variant_name or 'small' in variant_name:
                    new_packaging_fee = max(1.0, food_packaging_fee_float * 0.8)  # 小规格包装费稍低
                elif '中' in variant_name or 'medium' in variant_name:
                    new_packaging_fee = max(1.5, food_packaging_fee_float)  # 中规格使用商品包装费
                elif '大' in variant_name or 'large' in variant_name:
                    new_packaging_fee = max(2.0, food_packaging_fee_float * 1.2)  # 大规格包装费稍高
                elif '特大' in variant_name or '超大' in variant_name or 'xl' in variant_name:
                    new_packaging_fee = max(2.5, food_packaging_fee_float * 1.5)  # 特大规格包装费更高
                else:
                    # 如果没有明确的规格标识，使用商品包装费，但至少1元
                    new_packaging_fee = max(1.0, food_packaging_fee_float)
                
                # 更新数据库
                sql_update = """
                UPDATE takeout_food_variant 
                SET packaging_fee = %s, updated_at = NOW() 
                WHERE id = %s
                """
                cursor.execute(sql_update, (new_packaging_fee, variant_id))
                update_count += 1
                
                print(f"✅ 更新规格ID {variant_id} ({variant_name}): {variant['current_packaging_fee']:.2f} -> {new_packaging_fee:.2f}")
            
            # 提交事务
            connection.commit()
            print(f"\n✅ 成功更新 {update_count} 个规格的包装费")
            
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        connection.rollback()
        raise

def verify_update(connection):
    """
    验证更新结果
    
    Args:
        connection: 数据库连接对象
    """
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 查询更新后的数据
            sql = """
            SELECT 
                v.id as variant_id,
                v.name as variant_name,
                v.packaging_fee as packaging_fee,
                f.name as food_name
            FROM takeout_food_variant v
            LEFT JOIN takeout_food f ON v.food_id = f.id
            ORDER BY v.id
            """
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"\n📊 更新后的规格包装费:")
            print("-" * 80)
            print(f"{'规格ID':<8} {'规格名称':<15} {'包装费':<10} {'商品名称':<20}")
            print("-" * 80)
            
            for result in results:
                print(f"{result['variant_id']:<8} {result['variant_name']:<15} {result['packaging_fee']:<10.2f} {result['food_name']:<20}")
            
            # 统计信息
            sql_stats = """
            SELECT 
                COUNT(*) as total_variants,
                COUNT(CASE WHEN packaging_fee = 0 THEN 1 END) as zero_packaging_fee,
                COUNT(CASE WHEN packaging_fee > 0 THEN 1 END) as positive_packaging_fee,
                MIN(packaging_fee) as min_packaging_fee,
                MAX(packaging_fee) as max_packaging_fee,
                AVG(packaging_fee) as avg_packaging_fee
            FROM takeout_food_variant
            """
            cursor.execute(sql_stats)
            stats = cursor.fetchone()
            
            print(f"\n📈 更新后统计信息:")
            print("-" * 60)
            print(f"总规格数量: {stats['total_variants']}")
            print(f"包装费为0的规格数量: {stats['zero_packaging_fee']}")
            print(f"包装费大于0的规格数量: {stats['positive_packaging_fee']}")
            print(f"最小包装费: {stats['min_packaging_fee']:.2f}")
            print(f"最大包装费: {stats['max_packaging_fee']:.2f}")
            print(f"平均包装费: {stats['avg_packaging_fee']:.2f}")
            
    except Exception as e:
        print(f"❌ 验证查询失败: {e}")

def main():
    """
    主函数
    """
    print("🔧 开始更新规格包装费数据...")
    print("=" * 80)
    
    # 连接数据库
    connection = connect_database()
    
    try:
        # 更新规格包装费
        update_variant_packaging_fee(connection)
        
        # 验证更新结果
        verify_update(connection)
        
        print("\n" + "=" * 80)
        print("✅ 更新完成！")
        print("\n💡 下一步:")
        print("   1. 重启服务器测试包装费计算")
        print("   2. 验证API返回的包装费是否正确")
        print("   3. 检查订单创建时的包装费计算")
        
    except Exception as e:
        print(f"\n❌ 更新过程中发生错误: {e}")
        sys.exit(1)
    finally:
        connection.close()
        print("\n🔐 数据库连接已关闭")

if __name__ == "__main__":
    main()