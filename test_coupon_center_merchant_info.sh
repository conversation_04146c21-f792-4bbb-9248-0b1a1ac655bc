#!/bin/bash

# 优惠券中心商家信息测试脚本

echo "=== 优惠券中心商家信息测试 ==="

# 配置
BASE_URL="http://localhost:8181"
USER_TOKEN="your_user_jwt_token_here"

echo "🔍 测试优惠券中心API..."
echo "请求: GET ${BASE_URL}/api/v1/user/takeout/coupons/center"

# 测试优惠券中心API
response=$(curl -s -X GET "${BASE_URL}/api/v1/user/takeout/coupons/center?page=1&page_size=5" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "HTTPSTATUS:%{http_code}")

# 分离HTTP状态码和响应体
http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
response_body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

echo "状态码: $http_code"
echo "响应内容:"
echo $response_body | jq '.'

echo ""
echo "🔍 检查商家信息字段..."

# 检查是否包含商家信息
merchant_name_count=$(echo $response_body | jq '.data.list[].merchant_name' 2>/dev/null | grep -v null | wc -l)
merchant_logo_count=$(echo $response_body | jq '.data.list[].merchant_logo' 2>/dev/null | grep -v null | wc -l)

echo "包含 merchant_name 的优惠券数量: $merchant_name_count"
echo "包含 merchant_logo 的优惠券数量: $merchant_logo_count"

if [ "$merchant_name_count" -gt 0 ]; then
    echo "✅ merchant_name 字段存在"
    echo "商家名称示例:"
    echo $response_body | jq '.data.list[0].merchant_name' 2>/dev/null
else
    echo "❌ merchant_name 字段缺失"
fi

if [ "$merchant_logo_count" -gt 0 ]; then
    echo "✅ merchant_logo 字段存在"
    echo "商家Logo示例:"
    echo $response_body | jq '.data.list[0].merchant_logo' 2>/dev/null
else
    echo "❌ merchant_logo 字段缺失"
fi

echo ""
echo "📋 完整的优惠券信息结构:"
echo $response_body | jq '.data.list[0]' 2>/dev/null

echo ""
echo "=== 测试完成 ==="

# 检查点说明
echo ""
echo "🎯 验证要点："
echo "1. HTTP状态码应该是 200"
echo "2. 响应中应包含 data.list 数组"
echo "3. 每个优惠券项目应包含："
echo "   - merchant_id: 商家ID"
echo "   - merchant_name: 商家名称"
echo "   - merchant_logo: 商家Logo URL"
echo "   - can_claim: 是否可领取"
echo "   - claim_status_text: 领取状态文本"
echo ""
echo "4. 如果商家信息获取失败，应显示："
echo "   - merchant_name: \"未知商家\""
echo "   - merchant_logo: \"\""
