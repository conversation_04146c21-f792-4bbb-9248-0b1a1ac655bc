<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券中心状态筛选测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #eee;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .status-filter {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }
        
        .filter-tabs {
            display: flex;
            gap: 20px;
        }
        
        .filter-tab {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s;
            background: #f5f5f5;
        }
        
        .filter-tab.active {
            background: #fff0e6;
            color: #ff5500;
        }
        
        .filter-tab:hover {
            opacity: 0.8;
        }
        
        .tab-text {
            font-size: 14px;
            font-weight: 500;
        }
        
        .tab-count {
            background: #e0e0e0;
            color: #666;
            font-size: 12px;
            min-width: 18px;
            height: 18px;
            border-radius: 9px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 6px;
            font-weight: 600;
        }
        
        .filter-tab.active .tab-count {
            background: #ff5500;
            color: white;
        }
        
        .coupon-list {
            padding: 20px;
        }
        
        .coupon-item-wrapper {
            position: relative;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .coupon-item-wrapper.claimed {
            opacity: 0.7;
        }
        
        .coupon-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #eee;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .coupon-amount {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
        }
        
        .amount-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff5500;
        }
        
        .amount-unit {
            font-size: 12px;
            color: #666;
        }
        
        .coupon-info {
            flex: 1;
        }
        
        .coupon-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .coupon-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .merchant-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .merchant-logo {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        
        .merchant-name {
            font-size: 11px;
            color: #ff5500;
            font-weight: 500;
        }
        
        .claim-btn {
            padding: 8px 16px;
            background: #ff5500;
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .claim-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .claimed-overlay {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 10;
        }
        
        .claimed-badge {
            display: flex;
            align-items: center;
            gap: 4px;
            background: rgba(0, 200, 81, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 0 12px 0 12px;
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 200, 81, 0.3);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .test-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .test-btn {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            padding: 8px 16px;
            background: #007aff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-btn:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">优惠券中心</div>
        </div>

        <!-- 状态筛选标签 -->
        <div class="status-filter">
            <div class="filter-tabs">
                <div class="filter-tab active" data-status="available">
                    <span class="tab-text">可领取</span>
                    <span class="tab-count" id="available-count">3</span>
                </div>
                <div class="filter-tab" data-status="claimed">
                    <span class="tab-text">已领取</span>
                    <span class="tab-count" id="claimed-count">2</span>
                </div>
            </div>
        </div>

        <!-- 优惠券列表 -->
        <div class="coupon-list" id="coupon-list">
            <!-- 优惠券项目将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
        <button class="test-btn" onclick="simulateClaimCoupon()">模拟领取优惠券</button>
        <button class="test-btn" onclick="resetTestData()">重置测试数据</button>
        <button class="test-btn" onclick="toggleAllStatus()">切换全部状态</button>
    </div>

    <script>
        // 模拟优惠券数据
        let coupons = [
            {
                id: 1,
                name: '满50减10优惠券',
                description: '满50元减10元，适用于所有商品',
                amount: 10,
                min_order_amount: 50,
                merchant_name: '美味餐厅',
                merchant_logo: 'https://via.placeholder.com/16x16/ff5500/ffffff?text=M',
                can_claim: true,
                claim_status_text: '立即领取'
            },
            {
                id: 2,
                name: '满30减5优惠券',
                description: '满30元减5元，新用户专享',
                amount: 5,
                min_order_amount: 30,
                merchant_name: '快餐小店',
                merchant_logo: 'https://via.placeholder.com/16x16/00c851/ffffff?text=K',
                can_claim: true,
                claim_status_text: '立即领取'
            },
            {
                id: 3,
                name: '满100减20优惠券',
                description: '满100元减20元，限时优惠',
                amount: 20,
                min_order_amount: 100,
                merchant_name: '精品餐厅',
                merchant_logo: 'https://via.placeholder.com/16x16/ffa500/ffffff?text=J',
                can_claim: true,
                claim_status_text: '立即领取'
            },
            {
                id: 4,
                name: '满80减15优惠券',
                description: '满80元减15元，已领取',
                amount: 15,
                min_order_amount: 80,
                merchant_name: '咖啡时光',
                merchant_logo: 'https://via.placeholder.com/16x16/8e44ad/ffffff?text=K',
                can_claim: false,
                claim_status_text: '已领取'
            },
            {
                id: 5,
                name: '满60减8优惠券',
                description: '满60元减8元，已领取',
                amount: 8,
                min_order_amount: 60,
                merchant_name: '甜品屋',
                merchant_logo: 'https://via.placeholder.com/16x16/e74c3c/ffffff?text=T',
                can_claim: false,
                claim_status_text: '已领取'
            }
        ];

        let currentStatus = 'available';

        // 渲染优惠券列表
        function renderCoupons() {
            const couponList = document.getElementById('coupon-list');
            const filteredCoupons = coupons.filter(coupon => {
                if (currentStatus === 'available') {
                    return coupon.can_claim === true;
                } else if (currentStatus === 'claimed') {
                    return coupon.can_claim === false;
                }
                return true;
            });

            if (filteredCoupons.length === 0) {
                couponList.innerHTML = `
                    <div class="empty-state">
                        <div>暂无${currentStatus === 'available' ? '可领取' : '已领取'}的优惠券</div>
                    </div>
                `;
                return;
            }

            couponList.innerHTML = filteredCoupons.map(coupon => `
                <div class="coupon-item-wrapper ${!coupon.can_claim ? 'claimed' : ''}">
                    <div class="coupon-card">
                        <div class="coupon-amount">
                            <div class="amount-value">¥${coupon.amount}</div>
                            <div class="amount-unit">满${coupon.min_order_amount}可用</div>
                        </div>
                        <div class="coupon-info">
                            <div class="coupon-name">${coupon.name}</div>
                            <div class="coupon-desc">${coupon.description}</div>
                            <div class="merchant-info">
                                <img src="${coupon.merchant_logo}" class="merchant-logo" alt="商家Logo">
                                <span class="merchant-name">${coupon.merchant_name}</span>
                            </div>
                        </div>
                        <button 
                            class="claim-btn" 
                            ${!coupon.can_claim ? 'disabled' : ''}
                            onclick="claimCoupon(${coupon.id})"
                        >
                            ${coupon.claim_status_text}
                        </button>
                    </div>
                    ${!coupon.can_claim ? `
                        <div class="claimed-overlay">
                            <div class="claimed-badge">
                                <span>✓</span>
                                <span>已领取</span>
                            </div>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        // 更新统计数量
        function updateCounts() {
            const availableCount = coupons.filter(c => c.can_claim).length;
            const claimedCount = coupons.filter(c => !c.can_claim).length;
            
            document.getElementById('available-count').textContent = availableCount;
            document.getElementById('claimed-count').textContent = claimedCount;
        }

        // 切换状态标签
        function switchStatus(status) {
            currentStatus = status;
            
            // 更新标签样式
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-status="${status}"]`).classList.add('active');
            
            // 重新渲染列表
            renderCoupons();
        }

        // 领取优惠券
        function claimCoupon(couponId) {
            const coupon = coupons.find(c => c.id === couponId);
            if (coupon && coupon.can_claim) {
                coupon.can_claim = false;
                coupon.claim_status_text = '已领取';
                
                updateCounts();
                renderCoupons();
                
                // 显示成功提示
                alert('优惠券领取成功！');
            }
        }

        // 测试函数
        function simulateClaimCoupon() {
            const availableCoupons = coupons.filter(c => c.can_claim);
            if (availableCoupons.length > 0) {
                claimCoupon(availableCoupons[0].id);
            } else {
                alert('没有可领取的优惠券了！');
            }
        }

        function resetTestData() {
            coupons.forEach(coupon => {
                if (coupon.id <= 3) {
                    coupon.can_claim = true;
                    coupon.claim_status_text = '立即领取';
                } else {
                    coupon.can_claim = false;
                    coupon.claim_status_text = '已领取';
                }
            });
            
            updateCounts();
            renderCoupons();
            alert('测试数据已重置！');
        }

        function toggleAllStatus() {
            coupons.forEach(coupon => {
                coupon.can_claim = !coupon.can_claim;
                coupon.claim_status_text = coupon.can_claim ? '立即领取' : '已领取';
            });
            
            updateCounts();
            renderCoupons();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定状态标签点击事件
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const status = this.getAttribute('data-status');
                    switchStatus(status);
                });
            });

            // 初始渲染
            updateCounts();
            renderCoupons();
            
            console.log('🎯 优惠券中心状态筛选测试页面已加载');
        });
    </script>
</body>
</html>
