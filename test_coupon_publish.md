# 优惠券发布API测试文档

## API信息
- **URL**: `/api/v1/merchant/takeout/coupons/{id}/publish`
- **方法**: POST
- **描述**: 发布商家优惠券

## 功能说明

### 1. 优惠券状态管理
- **待发布 (0)**: 优惠券创建后的初始状态
- **已发布 (1)**: 优惠券发布后，用户可以领取和使用
- **已使用 (2)**: 优惠券已被使用
- **已过期 (3)**: 优惠券已过期
- **已禁用 (4)**: 优惠券被禁用

### 2. 发布条件验证
- 优惠券状态必须为"待发布"
- 商户必须拥有该优惠券的权限
- 优惠券开始时间不能早于当前时间
- 优惠券结束时间不能早于当前时间
- 结束时间不能早于开始时间

### 3. 业务流程
1. 商家创建优惠券（状态：待发布）
2. 商家发布优惠券（状态：已发布）
3. 用户可以领取和使用优惠券

## 测试用例

### 成功发布
```bash
curl -X POST \
  http://localhost:8080/api/v1/merchant/takeout/coupons/2/publish \
  -H 'Authorization: Bearer {merchant_jwt_token}' \
  -H 'Content-Type: application/json'
```

**期望响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 错误情况

#### 1. 优惠券不存在
```json
{
  "code": 400,
  "message": "优惠券不存在",
  "data": null
}
```

#### 2. 无权限发布
```json
{
  "code": 400,
  "message": "无权发布此优惠券",
  "data": null
}
```

#### 3. 状态不允许发布
```json
{
  "code": 400,
  "message": "只有待发布状态的优惠券才能发布",
  "data": null
}
```

#### 4. 时间验证失败
```json
{
  "code": 400,
  "message": "开始时间不能早于当前时间",
  "data": null
}
```

## 相关API

### 创建优惠券
```bash
POST /api/v1/merchant/takeout/coupons
```

### 获取优惠券列表
```bash
GET /api/v1/merchant/takeout/coupons
```

### 获取优惠券详情
```bash
GET /api/v1/merchant/takeout/coupons/{id}
```

## 注意事项

1. **权限验证**: 只有优惠券所属的商家才能发布该优惠券
2. **状态管理**: 发布后的优惠券状态会从"待发布"变为"已发布"
3. **时间验证**: 确保优惠券的时间设置合理
4. **日志记录**: 发布成功后会记录相关日志

## 数据库变更

发布优惠券会更新 `takeout_coupon` 表中的 `status` 字段：
```sql
UPDATE takeout_coupon SET status = 1 WHERE id = {coupon_id}
```

## 相关文件

- 控制器: `modules/takeout/controllers/merchant_takeout_coupon_controller.go`
- 服务层: `modules/takeout/services/takeout_coupon_service.go`
- 路由配置: `modules/takeout/routers/router.go`
- 数据模型: `modules/takeout/models/takeout_coupon.go`
- DTO: `modules/takeout/dto/coupon_dto.go`
