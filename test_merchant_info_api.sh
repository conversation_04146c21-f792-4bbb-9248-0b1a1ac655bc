#!/bin/bash

# 优惠券中心商家信息API测试脚本

echo "=== 优惠券中心商家信息API测试 ==="

# 配置
BASE_URL="http://localhost:8181"
USER_TOKEN="your_user_jwt_token_here"
MERCHANT_TOKEN="your_merchant_jwt_token_here"

echo "1. 测试优惠券中心API..."
echo "请求: GET ${BASE_URL}/api/v1/user/takeout/coupons/center"

curl -X GET "${BASE_URL}/api/v1/user/takeout/coupons/center?page=1&page_size=5" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.'

echo ""
echo "2. 测试优惠券领取API..."
echo "请求: POST ${BASE_URL}/api/v1/user/takeout/coupons/claim"

curl -X POST "${BASE_URL}/api/v1/user/takeout/coupons/claim" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"coupon_id": 1}' \
  -w "\n状态码: %{http_code}\n" \
  -s | jq '.'

echo ""
echo "=== 测试完成 ==="

# 检查点说明
echo ""
echo "🔍 检查点："
echo "1. 优惠券中心API响应中应包含："
echo "   - merchant_name: 商家名称"
echo "   - merchant_logo: 商家Logo URL"
echo ""
echo "2. 优惠券领取API响应中应包含："
echo "   - data.coupon.merchant_name: 商家名称"
echo "   - data.coupon.merchant_logo: 商家Logo URL"
echo ""
echo "3. 如果商家信息获取失败，应显示："
echo "   - merchant_name: \"未知商家\""
echo "   - merchant_logo: \"\""
