#!/bin/bash

# 测试促销活动过滤功能
# 验证 /api/v1/user/takeout/merchants/promotions-coupons API 是否正确过滤不可用的促销活动

BASE_URL="http://localhost:8181/api/v1/user/takeout"
USER_TOKEN="your_user_token_here"  # 需要替换为实际的用户token

echo "=== 促销活动过滤功能测试 ==="
echo ""

# 测试参数
MERCHANT_IDS="[1]"
USER_ID=2

echo "测试场景1: 查看用户的促销使用记录"
echo "----------------------------------------"
curl -s -X GET "${BASE_URL}/promotions/user-usage?user_id=${USER_ID}&promotion_id=1" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "测试场景2: 调用商家促销和优惠券API（应该过滤掉不可用的促销活动）"
echo "----------------------------------------"
curl -s -X POST "${BASE_URL}/merchants/promotions-coupons" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -d "{\"merchant_ids\": ${MERCHANT_IDS}}" | jq '.'

echo ""
echo "测试场景3: 清除用户的促销使用记录"
echo "----------------------------------------"
curl -s -X DELETE "${BASE_URL}/promotions/clear-user-usage?user_id=${USER_ID}&promotion_id=1" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "测试场景4: 再次调用商家促销和优惠券API（现在应该包含促销活动）"
echo "----------------------------------------"
curl -s -X POST "${BASE_URL}/merchants/promotions-coupons" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -d "{\"merchant_ids\": ${MERCHANT_IDS}}" | jq '.'

echo ""
echo "测试完成！"
echo ""
echo "预期结果："
echo "- 场景1: 显示用户已使用促销活动的记录"
echo "- 场景2: 返回的促销活动列表应该为空或不包含已达到使用限制的促销活动"
echo "- 场景3: 成功清除用户的促销使用记录"
echo "- 场景4: 返回的促销活动列表应该包含可用的促销活动"
