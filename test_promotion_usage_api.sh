#!/bin/bash

# 促销活动使用记录管理API测试脚本
# 用于测试和管理用户的促销活动使用记录

BASE_URL="http://localhost:8181/api/v1/takeout"
USER_ID=2
PROMOTION_ID=1

echo "=== 促销活动使用记录管理API测试 ==="
echo ""

# 1. 查看用户的促销使用记录
echo "1. 查看用户ID ${USER_ID} 对促销活动ID ${PROMOTION_ID} 的使用记录："
curl -s -X GET "${BASE_URL}/promotions/user-usage?user_id=${USER_ID}&promotion_id=${PROMOTION_ID}" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "----------------------------------------"
echo ""

# 2. 清除用户的促销使用记录（仅用于测试）
echo "2. 清除用户ID ${USER_ID} 对促销活动ID ${PROMOTION_ID} 的使用记录："
curl -s -X DELETE "${BASE_URL}/promotions/clear-user-usage?user_id=${USER_ID}&promotion_id=${PROMOTION_ID}" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "----------------------------------------"
echo ""

# 3. 再次查看用户的促销使用记录（应该为空）
echo "3. 清除后再次查看用户的促销使用记录："
curl -s -X GET "${BASE_URL}/promotions/user-usage?user_id=${USER_ID}&promotion_id=${PROMOTION_ID}" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "----------------------------------------"
echo ""

echo "测试完成！"
echo ""
echo "说明："
echo "- 如果第1步显示 usage_count > 0，说明用户已经使用过该促销活动"
echo "- 第2步会清除用户的使用记录"
echo "- 第3步应该显示 usage_count = 0，说明记录已被清除"
echo "- 清除记录后，用户应该可以重新使用该促销活动"
