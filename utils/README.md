# Utils 工具包

本目录包含项目中使用的各种工具函数和服务。

## 目录结构

- `/utils` - 核心工具包
  - `errors.go` - 错误处理工具
  - `jwt.go` - JWT认证工具
    - `GenerateToken` - 生成JWT令牌
    - `ParseToken` - 解析JWT令牌
    - `ExtractClaims` - 提取JWT令牌中的声明信息
    - `GetTokenFromRequest` - 从HTTP请求中获取Token
    - `SetUserContext` - 设置用户上下文信息
  - `password.go` - 密码加密工具
  - `/config` - 配置管理工具
  - `/sms` - 短信服务工具
  - `/storage` - 文件存储工具
  - `/common` - 通用工具包
    - `string_utils.go` - 字符串处理工具
    - `error_codes.go` - 错误码定义
    - `service_utils.go` - 服务工具
    - `utils.go` - 通用工具函数
    - `http_utils.go` - HTTP请求处理工具
      - `MapFormToStruct` - 表单数据映射函数
      - `ParseRequest` - Beego框架请求解析函数
      - `ParseHttpRequest` - 标准HTTP请求解析函数

## 使用说明

1. 核心工具包引用方式：`import "o_mall_backend/utils"`
2. 通用工具包引用方式：`import "o_mall_backend/utils/common"`
3. 其他子包引用方式：`import "o_mall_backend/utils/[子包名]"`

## JWT工具函数使用示例

```go
// 生成JWT令牌
token, err := utils.GenerateToken(123, "username", "admin")
if err != nil {
    // 处理错误
}

// 解析JWT令牌
claims, err := utils.ParseToken(token)
if err != nil {
    // 处理错误
}

// 在Beego控制器或中间件中验证令牌
func SomeMiddleware(ctx *context.Context) {
    token := utils.GetTokenFromRequest(ctx)
    claims, err := utils.ExtractClaims(token)
    if err != nil {
        // 处理认证错误
        return
    }
    
    // 设置用户信息到上下文
    utils.SetUserContext(ctx, claims)
}
```

## HTTP工具函数使用示例

```go
// 在Beego控制器中使用ParseRequest
func (c *SomeController) Action() {
    req := &MyRequest{}
    if err := common.ParseRequest(c.Ctx, req); err != nil {
        // 处理错误
        return
    }
    // 使用请求数据...
}

// 在标准HTTP处理函数中使用ParseHttpRequest
func Handler(w http.ResponseWriter, r *http.Request) {
    req := &MyRequest{}
    if err := common.ParseHttpRequest(r, req); err != nil {
        // 处理错误
        return
    }
    // 使用请求数据...
}
```

## 注意事项

1. 所有工具函数应该有明确的注释说明用途和使用方法
2. 避免在工具函数中包含业务逻辑
3. 保持函数的单一职责和清晰的接口设计 