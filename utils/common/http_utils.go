/**
 * http_utils.go
 * HTTP工具函数
 *
 * 本文件提供HTTP相关的工具函数，包括请求处理、表单映射等功能
 */

package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strconv"
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"
)

// ParseRequest 通用请求参数解析方法
//
// 根据Content-Type自动处理不同格式的请求参数，支持application/json、
// application/x-www-form-urlencoded和multipart/form-data格式。
//
// 参数:
//   - ctx: Beego上下文对象
//   - req: 目标结构体指针，用于存储解析后的数据
//
// 示例:
//
//	type UserRequest struct {
//	    Name string `json:"name"`
//	    Age  int    `json:"age"`
//	}
//
//	func (c *UserController) CreateUser() {
//	    req := &UserRequest{}
//	    if err := ParseRequest(c.Ctx, req); err != nil {
//	        // 处理错误
//	        return
//	    }
//	    // 使用解析后的请求数据
//	}
//
// 返回:
//   - 如果解析过程中发生错误，返回对应的error
//   - 如果解析成功，返回nil
func ParseRequest(ctx *context.Context, req interface{}) error {
	// 根据Content-Type处理不同格式的请求
	contentType := ctx.Request.Header.Get("Content-Type")

	// 添加调试日志
	logs.Info("请求Content-Type: %s", contentType)

	if strings.Contains(contentType, "application/json") {
		// 处理JSON格式请求
		logs.Info("开始解析JSON请求体")

		// 尝试使用 Beego 的 RequestBody 属性
		var bodyBytes []byte
		var err error

		if len(ctx.Input.RequestBody) > 0 {
			// 如果 Beego 已经读取了请求体，使用缓存的数据
			bodyBytes = ctx.Input.RequestBody
			logs.Info("使用 Beego 缓存的请求体")
		} else {
			// 否则直接从请求体读取
			if ctx.Request.Body == nil {
				logs.Error("请求体为空")
				return errors.New("请求体为空")
			}

			bodyBytes, err = io.ReadAll(ctx.Request.Body)
			if err != nil {
				logs.Error("读取请求体失败: %v", err)
				return err
			}
			logs.Info("从请求体直接读取")
		}

		logs.Info("请求体内容: %s", string(bodyBytes))
		logs.Info("请求体长度: %d", len(bodyBytes))

		if len(bodyBytes) == 0 {
			logs.Error("请求体内容为空")
			return errors.New("请求体内容为空")
		}

		// 解析JSON
		if err := json.Unmarshal(bodyBytes, req); err != nil {
			logs.Error("JSON解析错误: %v", err)
			return err
		}

		logs.Info("JSON解析成功")
	} else if strings.Contains(contentType, "multipart/form-data") {
		// 处理multipart/form-data格式请求
		// 先确保已解析multipart表单
		if err := ctx.Request.ParseMultipartForm(32 << 20); err != nil {
			logs.Error("ParseMultipartForm错误: %v", err)
			return err
		}

		// 打印表单数据以便调试
		logs.Info("表单数据: %v", ctx.Request.Form)

		// 手动将表单数据映射到结构体
		formData := ctx.Request.Form
		if err := MapFormToStruct(formData, req); err != nil {
			// 如果手动映射失败，尝试使用Bind方法
			if err := ctx.Input.Bind(req, "form"); err != nil {
				logs.Error("Bind错误: %v", err)
				return err
			}
		}
	} else {
		// 处理x-www-form-urlencoded格式请求
		// 先尝试使用Request.ParseForm解析原始表单数据
		if err := ctx.Request.ParseForm(); err != nil {
			logs.Error("Request.ParseForm错误: %v", err)
		}

		// 打印表单数据以便调试
		logs.Info("表单数据: %v", ctx.Request.Form)

		// 尝试手动将表单数据映射到结构体
		formData := ctx.Request.Form
		if err := MapFormToStruct(formData, req); err != nil {
			// 如果手动映射失败，尝试使用Bind方法
			if err := ctx.Input.Bind(req, "form"); err != nil {
				logs.Error("Bind错误: %v", err)
				return err
			}
		}
	}

	// 打印解析后的请求对象，用于调试
	logs.Info("解析后的请求: %+v", req)
	return nil
}

// ParseHttpRequest 通用HTTP请求参数解析方法
//
// 与ParseRequest功能类似，但适用于标准的http.Request请求，不依赖于Beego框架。
// 根据Content-Type自动处理不同格式的请求参数。
//
// 参数:
//   - r: 标准HTTP请求对象
//   - req: 目标结构体指针，用于存储解析后的数据
//
// 示例:
//
//	func HandleRequest(w http.ResponseWriter, r *http.Request) {
//	    req := &UserRequest{}
//	    if err := ParseHttpRequest(r, req); err != nil {
//	        // 处理错误
//	        return
//	    }
//	    // 使用解析后的请求数据
//	}
//
// 返回:
//   - 如果解析过程中发生错误，返回对应的error
//   - 如果解析成功，返回nil
func ParseHttpRequest(r *http.Request, req interface{}) error {
	// 根据Content-Type处理不同格式的请求
	contentType := r.Header.Get("Content-Type")

	// 添加调试日志
	logs.Info("请求Content-Type: %s", contentType)

	if strings.Contains(contentType, "application/json") {
		// 处理JSON格式请求
		if err := json.NewDecoder(r.Body).Decode(req); err != nil {
			logs.Error("JSON解析错误: %v", err)
			return err
		}
	} else if strings.Contains(contentType, "multipart/form-data") {
		// 处理multipart/form-data格式请求
		// 先确保已解析multipart表单
		if err := r.ParseMultipartForm(32 << 20); err != nil {
			logs.Error("ParseMultipartForm错误: %v", err)
			return err
		}

		// 打印表单数据以便调试
		logs.Info("表单数据: %v", r.Form)

		// 手动将表单数据映射到结构体
		formData := r.Form
		if err := MapFormToStruct(formData, req); err != nil {
			logs.Error("表单映射错误: %v", err)
			return err
		}
	} else {
		// 处理x-www-form-urlencoded格式请求
		// 先尝试使用Request.ParseForm解析原始表单数据
		if err := r.ParseForm(); err != nil {
			logs.Error("Request.ParseForm错误: %v", err)
		}

		// 打印表单数据以便调试
		logs.Info("表单数据: %v", r.Form)

		// 尝试手动将表单数据映射到结构体
		formData := r.Form
		if err := MapFormToStruct(formData, req); err != nil {
			logs.Error("表单映射错误: %v", err)
			return err
		}
	}

	// 打印解析后的请求对象，用于调试
	logs.Info("解析后的请求: %+v", req)
	return nil
}

// MapFormToStruct 将表单数据映射到结构体
//
// 该函数使用反射机制，将map[string][]string类型的表单数据映射到指定的结构体上。
// 表单字段的key需要与结构体字段的json标签匹配。
//
// 参数:
//   - formData: 表单数据，通常来自http.Request.Form或MultipartForm.Value
//   - dest: 目标结构体的指针，表单数据将被映射到该结构体上
//
// 支持的结构体字段类型:
//   - string
//   - int, int8, int16, int32, int64
//   - uint, uint8, uint16, uint32, uint64
//   - float32, float64
//   - bool
//
// 示例:
//
//	type User struct {
//	    Name  string `json:"name"`
//	    Age   int    `json:"age"`
//	    Email string `json:"email"`
//	}
//
//	func HandleRequest(w http.ResponseWriter, r *http.Request) {
//	    r.ParseForm()
//	    user := &User{}
//	    err := MapFormToStruct(r.Form, user)
//	    // 处理user和err
//	}
//
// 返回:
//   - 如果映射过程中发生错误，返回对应的error
//   - 如果映射成功，返回nil
func MapFormToStruct(formData map[string][]string, dest interface{}) error {
	// 使用反射获取目标结构体的值和类型
	v := reflect.ValueOf(dest)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return fmt.Errorf("目标必须是非空指针")
	}

	// 获取指针指向的值
	v = v.Elem()
	if v.Kind() != reflect.Struct {
		return fmt.Errorf("目标必须是结构体指针")
	}

	// 遍历结构体字段
	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		if !field.CanSet() {
			continue // 跳过不可设置的字段
		}

		// 获取字段的json标签
		typeField := t.Field(i)
		tag := typeField.Tag.Get("json")
		if tag == "" {
			continue // 跳过没有json标签的字段
		}

		// 处理json标签中可能的选项
		parts := strings.Split(tag, ",")
		tag = parts[0]

		// 查找表单中对应的值
		values, ok := formData[tag]
		if !ok || len(values) == 0 {
			continue // 表单中没有对应的值
		}

		value := values[0]
		logs.Debug("映射字段 %s 的值为 %s", tag, value)

		// 根据字段类型设置值
		switch field.Kind() {
		case reflect.String:
			field.SetString(value)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
				field.SetInt(intValue)
			} else {
				logs.Warn("将值 %s 转换为整数时出错: %v", value, err)
			}
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			if uintValue, err := strconv.ParseUint(value, 10, 64); err == nil {
				field.SetUint(uintValue)
			} else {
				logs.Warn("将值 %s 转换为无符号整数时出错: %v", value, err)
			}
		case reflect.Float32, reflect.Float64:
			if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
				field.SetFloat(floatValue)
			} else {
				logs.Warn("将值 %s 转换为浮点数时出错: %v", value, err)
			}
		case reflect.Bool:
			if boolValue, err := strconv.ParseBool(value); err == nil {
				field.SetBool(boolValue)
			} else {
				logs.Warn("将值 %s 转换为布尔值时出错: %v", value, err)
			}
		case reflect.Slice:
			// 处理切片类型 - 使用表单中的所有值
			elemKind := field.Type().Elem().Kind()
			switch elemKind {
			case reflect.String:
				// 字符串切片
				strSlice := reflect.MakeSlice(field.Type(), len(values), len(values))
				for j, val := range values {
					strSlice.Index(j).SetString(val)
				}
				field.Set(strSlice)
			case reflect.Int64:
				// int64切片 - 支持字符串数字转换
				intSlice := reflect.MakeSlice(field.Type(), 0, len(values))
				for _, val := range values {
					if intValue, err := strconv.ParseInt(val, 10, 64); err == nil {
						intSlice = reflect.Append(intSlice, reflect.ValueOf(intValue))
					} else {
						logs.Warn("将值 %s 转换为int64时出错: %v", val, err)
					}
				}
				field.Set(intSlice)
			case reflect.Int:
				// int切片 - 支持字符串数字转换
				intSlice := reflect.MakeSlice(field.Type(), 0, len(values))
				for _, val := range values {
					if intValue, err := strconv.ParseInt(val, 10, 64); err == nil {
						intSlice = reflect.Append(intSlice, reflect.ValueOf(int(intValue)))
					} else {
						logs.Warn("将值 %s 转换为int时出错: %v", val, err)
					}
				}
				field.Set(intSlice)
			}
			// 可以扩展支持其他类型的切片
		}
	}

	return nil
}
