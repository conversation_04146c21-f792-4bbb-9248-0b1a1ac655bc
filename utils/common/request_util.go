/**
 * request_util.go
 * 请求处理工具
 *
 * 该文件提供了处理HTTP请求的实用工具函数
 */

package common

import (
	"encoding/json"

	"github.com/beego/beego/v2/server/web/context"
)

// ParseFormRequest 解析请求参数到结构体
// 先尝试解析表单参数，再尝试解析JSON请求体
func ParseFormRequest(ctx *context.Context, req interface{}) error {
	// 先尝试解析表单参数
	if err := ctx.Request.ParseForm(); err != nil {
		return err
	}

	// 尝试将表单数据绑定到结构体
	if err := ctx.Input.Bind(req, ""); err != nil {
		// 如果表单解析失败，尝试解析JSON
		if err = json.Unmarshal(ctx.Input.RequestBody, req); err != nil {
			return err
		}
	}
	return nil
}
