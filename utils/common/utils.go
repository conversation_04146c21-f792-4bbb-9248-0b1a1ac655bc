/**
 * utils.go
 * 通用工具函数
 *
 * 本文件提供各种通用工具函数，供系统各个模块使用。
 * 包含字符串处理、时间处理、数据转换等常用功能。
 */

package common

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	redisUtil "o_mall_backend/utils/redis"
)

// =====================================
// 字符串相关函数
// =====================================

// IsNumeric 判断字符串是否为数字
func IsNumeric(s string) bool {
	_, err := strconv.ParseInt(s, 10, 64)
	return err == nil
}

// ParseInt64 将字符串转换为int64类型，失败时返回默认值
func ParseInt64(s string, defaultVal int64) int64 {
	if s == "" {
		return defaultVal
	}

	val, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return defaultVal
	}

	return val
}

// ParseInt 将字符串转换为int类型，失败时返回默认值
func ParseInt(s string, defaultVal int) int {
	if s == "" {
		return defaultVal
	}

	val, err := strconv.Atoi(s)
	if err != nil {
		return defaultVal
	}

	return val
}

// ParseFloat64 将字符串转换为float64类型，失败时返回默认值
func ParseFloat64(s string, defaultVal float64) float64 {
	if s == "" {
		return defaultVal
	}

	val, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return defaultVal
	}

	return val
}

// GetStringValue 从map中获取字符串值，不存在时返回默认值
func GetStringValue(m map[string]interface{}, key string, defaultVal string) string {
	if m == nil {
		return defaultVal
	}

	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}

	return defaultVal
}

// GetInt64Value 从map中获取int64值，不存在时返回默认值
func GetInt64Value(m map[string]interface{}, key string, defaultVal int64) int64 {
	if m == nil {
		return defaultVal
	}

	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case int64:
			return v
		case int:
			return int64(v)
		case float64:
			return int64(v)
		case string:
			return ParseInt64(v, defaultVal)
		}
	}

	return defaultVal
}

// GetBoolValue 从map中获取bool值，不存在时返回默认值
func GetBoolValue(m map[string]interface{}, key string, defaultVal bool) bool {
	if m == nil {
		return defaultVal
	}

	if val, ok := m[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}

		// 尝试将字符串转换为bool
		if str, ok := val.(string); ok {
			if str == "true" || str == "1" || str == "yes" {
				return true
			} else if str == "false" || str == "0" || str == "no" {
				return false
			}
		}
	}

	return defaultVal
}

// TruncateString 截断字符串到指定长度，并添加省略号
func TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}

	return s[:maxLen] + "..."
}

// ContainsIgnoreCase 忽略大小写判断字符串是否包含子串
func ContainsIgnoreCase(s, substr string) bool {
	pattern := "(?i)" + regexp.QuoteMeta(substr)
	match, _ := regexp.MatchString(pattern, s)
	return match
}

// SafeSubstring 安全地获取子字符串，避免索引越界
func SafeSubstring(s string, start, end int) string {
	length := len(s)

	if start < 0 {
		start = 0
	}

	if start > length {
		return ""
	}

	if end > length {
		end = length
	}

	if end < start {
		return ""
	}

	return s[start:end]
}

// IsEmpty 检查字符串是否为空
func IsEmpty(s string) bool {
	return len(s) == 0
}

// IsBlank 检查字符串是否为空白
func IsBlank(s string) bool {
	return len(strings.TrimSpace(s)) == 0
}

// DefaultIfEmpty 如果字符串为空则返回默认值
func DefaultIfEmpty(s string, defaultValue string) string {
	if IsEmpty(s) {
		return defaultValue
	}
	return s
}

// =====================================
// 时间相关函数
// =====================================

// FormatTime 按照指定格式格式化时间
func FormatTime(t time.Time, layout string) string {
	if layout == "" {
		layout = "2006-01-02 15:04:05"
	}
	return t.Format(layout)
}

// Now 获取当前时间
func Now() time.Time {
	return time.Now()
}

// TimestampToTime 将时间戳转换为Time对象
func TimestampToTime(timestamp int64) time.Time {
	return time.Unix(timestamp, 0)
}

// TimeToTimestamp 将Time对象转换为时间戳
func TimeToTimestamp(t time.Time) int64 {
	return t.Unix()
}

// GetFirstDayOfMonth 获取指定时间所在月份的第一天
func GetFirstDayOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// GetLastDayOfMonth 获取指定时间所在月份的最后一天
func GetLastDayOfMonth(t time.Time) time.Time {
	firstDay := GetFirstDayOfMonth(t)
	return firstDay.AddDate(0, 1, -1)
}

// =====================================
// 集合相关函数
// =====================================

// SliceContains 检查切片中是否包含某个元素
func SliceContains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// Contains 检查切片是否包含某个元素
func Contains[T comparable](slice []T, element T) bool {
	for _, item := range slice {
		if item == element {
			return true
		}
	}
	return false
}

// RemoveDuplicates 移除切片中的重复元素
func RemoveDuplicates[T comparable](slice []T) []T {
	seen := make(map[T]bool)
	result := make([]T, 0)

	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// =====================================
// 其他工具函数
// =====================================

// GenerateOrderNo 生成订单号
func GenerateOrderNo(prefix string) string {
	now := time.Now()
	timestamp := now.Format("20060102150405")
	random := rand.New(rand.NewSource(now.UnixNano()))
	randomNum := random.Intn(10000)
	return prefix + timestamp + fmt.Sprintf("%04d", randomNum)
}

// GetMD5 计算MD5哈希值
func GetMD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// GetRedisCacheInfo 获取Redis缓存信息
// 根据缓存键获取有关该键的缓存信息，包括过期时间、大小、最后更新时间等
func GetRedisCacheInfo(cacheKey string) (map[string]interface{}, error) {
	// 构建结果
	result := make(map[string]interface{})

	// 获取缓存数据
	cacheData, err := redisUtil.Get(cacheKey)
	if err != nil {
		return result, err
	}

	// 设置基本信息
	result["exists"] = cacheData != ""
	result["size"] = len(cacheData)

	// 获取过期时间
	ttl, err := redisUtil.TTL(cacheKey)
	if err == nil {
		result["ttl"] = ttl
	}

	// 尝试获取最后刷新时间
	lastRefreshKey := cacheKey + ":last_refresh"
	lastRefreshTime, err := redisUtil.Get(lastRefreshKey)
	if err == nil && lastRefreshTime != "" {
		timestamp, err := strconv.ParseInt(lastRefreshTime, 10, 64)
		if err == nil {
			result["last_refresh"] = timestamp
		}
	} else {
		// 如果没有专门记录最后刷新时间，则使用当前时间
		result["last_refresh"] = time.Now().Unix()
	}

	// 尝试解析JSON数据以获取键信息
	if cacheData != "" {
		var jsonData interface{}
		if err := json.Unmarshal([]byte(cacheData), &jsonData); err == nil {
			// 如果是map类型，获取键列表
			if mapData, ok := jsonData.(map[string]interface{}); ok {
				keys := make([]string, 0, len(mapData))
				for k := range mapData {
					keys = append(keys, k)
				}
				result["keys"] = keys
				result["count"] = len(keys)
			}
		}
	}

	return result, nil
}
