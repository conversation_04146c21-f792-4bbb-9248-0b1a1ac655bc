/**
 * validation_utils.go
 * 验证工具函数
 *
 * 本文件提供通用的验证工具函数，包括自定义验证规则、常用验证逻辑等
 */

package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"

	"github.com/beego/beego/v2/core/validation"
)

// 初始化自定义验证规则
func init() {
	// 添加自定义验证器
	_ = validation.AddCustomFunc("ConfigKey", ValidateConfigKey)
	_ = validation.AddCustomFunc("JsonFormat", ValidateJsonFormat)
	_ = validation.AddCustomFunc("ConfigType", ValidateConfigType)
	_ = validation.AddCustomFunc("ValidUrl", ValidateUrl)
	_ = validation.AddCustomFunc("NoSpecialChars", ValidateNoSpecialChars)
	_ = validation.AddCustomFunc("Domain", ValidateDomain)
}

// ValidateConfigKey 验证配置键名格式
// 规则：只允许字母、数字、下划线和点号，长度在2-50之间
func ValidateConfigKey(v *validation.Validation, obj interface{}, key string) {
	if str, ok := obj.(string); ok {
		if str == "" {
			return // 如果为空，跳过验证（使用Required单独验证）
		}

		if len(str) < 2 || len(str) > 50 {
			_ = v.SetError(key, "配置键名长度必须在2-50之间")
			return
		}

		pattern := `^[a-zA-Z0-9_\.]+$`
		matched, _ := regexp.MatchString(pattern, str)
		if !matched {
			_ = v.SetError(key, "配置键名只能包含字母、数字、下划线和点号")
		}
	} else {
		_ = v.SetError(key, "无效的配置键名类型")
	}
}

// ValidateJsonFormat 验证JSON格式
func ValidateJsonFormat(v *validation.Validation, obj interface{}, key string) {
	if str, ok := obj.(string); ok {
		if str == "" {
			return // 如果为空，跳过验证（使用Required单独验证）
		}

		var js interface{}
		err := json.Unmarshal([]byte(str), &js)
		if err != nil {
			_ = v.SetError(key, "无效的JSON格式")
		}
	} else {
		_ = v.SetError(key, "无效的JSON字符串")
	}
}

// ValidateConfigType 验证配置类型
// 规则：限制配置类型为预定义的有效类型
func ValidateConfigType(v *validation.Validation, obj interface{}, key string) {
	if str, ok := obj.(string); ok {
		validTypes := []string{"system", "payment", "delivery", "user", "shop", "product", "order", "service", "notification", "content", "api"}
		isValid := false
		for _, validType := range validTypes {
			if str == validType {
				isValid = true
				break
			}
		}

		if !isValid {
			_ = v.SetError(key, "无效的配置类型")
		}
	} else {
		_ = v.SetError(key, "无效的配置类型格式")
	}
}

// ValidateUrl 验证URL格式
func ValidateUrl(v *validation.Validation, obj interface{}, key string) {
	if str, ok := obj.(string); ok {
		if str == "" {
			return // 如果为空，跳过验证（使用Required单独验证）
		}

		// 简单的URL格式验证，实际项目中可能需要更复杂的规则
		pattern := `^(https?|ftp)://[^\s/$.?#].[^\s]*$`
		matched, _ := regexp.MatchString(pattern, str)
		if !matched {
			_ = v.SetError(key, "无效的URL格式")
		}
	} else {
		_ = v.SetError(key, "无效的URL类型")
	}
}

// ValidateNoSpecialChars 验证无特殊字符
// 规则：只允许字母、数字、中文和常用标点
func ValidateNoSpecialChars(v *validation.Validation, obj interface{}, key string) {
	if str, ok := obj.(string); ok {
		if str == "" {
			return // 如果为空，跳过验证（使用Required单独验证）
		}

		// 允许字母、数字、中文、空格和基本标点
		pattern := `^[a-zA-Z0-9\p{Han}\s\.,，。？！：；''""《》\(\)\[\]【】\-_]+$`
		matched, _ := regexp.MatchString(pattern, str)
		if !matched {
			_ = v.SetError(key, "不允许包含特殊字符")
		}
	} else {
		_ = v.SetError(key, "无效的字符串类型")
	}
}

// ValidateDomain 验证域名格式
func ValidateDomain(v *validation.Validation, obj interface{}, key string) {
	if str, ok := obj.(string); ok {
		if str == "" {
			return // 如果为空，跳过验证（使用Required单独验证）
		}

		// 域名格式验证
		pattern := `^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`
		matched, _ := regexp.MatchString(pattern, str)
		if !matched {
			_ = v.SetError(key, "无效的域名格式")
		}
	} else {
		_ = v.SetError(key, "无效的域名类型")
	}
}

// ValidateConfigContent 验证配置内容
// 根据配置类型验证配置内容是否有效
func ValidateConfigContent(configType string, configContent string) error {
	if configType == "" || configContent == "" {
		return errors.New("配置类型和内容不能为空")
	}

	// 首先验证是否为有效的JSON
	var jsonData interface{}
	err := json.Unmarshal([]byte(configContent), &jsonData)
	if err != nil {
		return fmt.Errorf("无效的JSON格式: %v", err)
	}

	// 根据不同的配置类型进行特定验证
	switch configType {
	case "system":
		// 系统配置验证
		_, ok := jsonData.(map[string]interface{})
		if !ok {
			return errors.New("系统配置必须是一个对象")
		}

		// 验证必要的系统配置字段
		// 这里可以根据实际需求添加特定字段的验证

	case "payment":
		// 支付配置验证
		paymentConfig, ok := jsonData.(map[string]interface{})
		if !ok {
			return errors.New("支付配置必须是一个对象")
		}

		// 验证支付配置必要字段
		if _, hasMethod := paymentConfig["method"]; !hasMethod {
			return errors.New("支付配置缺少必要字段: method")
		}

	case "notification":
		// 通知配置验证
		notificationConfig, ok := jsonData.(map[string]interface{})
		if !ok {
			return errors.New("通知配置必须是一个对象")
		}

		// 验证通知配置必要字段
		if _, hasType := notificationConfig["type"]; !hasType {
			return errors.New("通知配置缺少必要字段: type")
		}

	// 可以根据需要添加更多配置类型的验证
	default:
		// 默认只验证JSON格式
	}

	return nil
}
