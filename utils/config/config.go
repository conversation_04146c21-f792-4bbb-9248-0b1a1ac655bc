/**
 * config.go
 * 配置管理工具
 *
 * 本文件提供配置加载、解析和获取的功能，支持从多种来源加载配置
 * 包括主配置文件、环境特定配置和模块特定配置，并支持环境变量覆盖
 */

package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// 配置模块常量
const (
	// 配置文件路径常量
	MainConfigFile = "conf/app.conf"
	//EnvConfigDir     = "conf/env"
	ModulesConfigDir = "conf/modules"

	// 配置模块名称常量
	StorageModule = "storage"
	SmsModule     = "sms"
	PaymentModule = "payment"
	PushModule    = "push"
)

// 存储全局配置的映射表
var globalConfig = make(map[string]string)

// 初始化配置
// 按照以下顺序加载配置：
// 1. 加载主配置文件 app.conf
// 2. 根据 runmode 加载环境特定配置文件
// 3. 加载模块特定配置文件
// 4. 环境变量覆盖（优先级最高）
func InitConfig() error {
	// 获取当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		logs.Error("获取工作目录失败: %v", err)
		return fmt.Errorf("获取工作目录失败: %v", err)
	}
	logs.Info("当前工作目录: %s", workDir)

	// 检查配置文件是否存在
	configPath := filepath.Join(workDir, MainConfigFile)
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		logs.Error("配置文件不存在: %s", configPath)
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}
	logs.Info("检测到配置文件: %s", configPath)

	// 尝试读取配置文件内容以验证
	configData, err := os.ReadFile(configPath)
	if err != nil {
		logs.Error("无法读取配置文件内容: %v", err)
		return fmt.Errorf("无法读取配置文件内容: %v", err)
	}
	logs.Info("配置文件大小: %d 字节", len(configData))

	// 直接从配置文件内容解析到全局配置映射
	if len(configData) > 0 {
		//parseConfigFromContent(string(configData))
		parseConfigFile(configPath) // 替换原有的 parseConfigFromContent 调用
		logs.Info("已解析配置文件内容到全局配置映射, 共 %d 项配置", len(globalConfig))
	}

	// 使用重试机制加载主配置文件到 BeegoAppConfig
	maxRetries := 3
	var loadErr error

	for i := 0; i < maxRetries; i++ {
		// 加载主配置文件
		loadErr = web.LoadAppConfig("ini", MainConfigFile)
		if loadErr == nil {
			logs.Info("成功加载主配置文件")
			break
		}

		logs.Error("加载主配置文件失败，第%d次尝试: %v", i+1, loadErr)
		time.Sleep(time.Second)
	}

	if loadErr != nil {
		return fmt.Errorf("无法加载主配置文件，达到最大重试次数: %v", loadErr)
	}

	// 第一步：验证基本配置是否正确加载到 BeegoAppConfig
	runMode := web.AppConfig.DefaultString("runmode", "")
	appName := web.AppConfig.DefaultString("appname", "")
	logs.Info("基础配置验证: appname=%s, runmode=%s", appName, runMode)

	// 第二步：加载模块特定配置文件
	loadModuleConfigs()

	// // 确保 runmode 正确设置
	// finalRunMode := web.AppConfig.DefaultString("runmode", "")
	// if finalRunMode == "" && globalConfig["runmode"] != "" {
	// 	logs.Error("runmode 未能正确设置到 BeegoAppConfig，尝试强制设置")
	// 	if err := web.AppConfig.Set("runmode", globalConfig["runmode"]); err != nil {
	// 		logs.Error("强制设置 runmode 失败: %v", err)
	// 	} else {
	// 		logs.Info("成功强制设置 runmode = %s", globalConfig["runmode"])
	// 	}
	// }

	// 第三步：加载环境变量覆盖配置
	loadEnvVariables()

	// 第四步：确保全局配置映射中的值被正确设置到 BeegoAppConfig 中
	// 这一步非常重要，确保所有配置都可以通过 web.AppConfig 访问
	SetGlobalConfigToAppConfig()

	// 打印全局配置映射
	logs.Info("全局配置映射: %v", globalConfig)

	accessKey := web.AppConfig.DefaultString("qiniu.access_key", "")
	secretKey := web.AppConfig.DefaultString("qiniu.secret_key", "")
	bucket := web.AppConfig.DefaultString("qiniu.bucket", "")
	domain := web.AppConfig.DefaultString("qiniu.domain", "")
	zone := web.AppConfig.DefaultString("qiniu.zone", "")
	useHTTPS := web.AppConfig.DefaultBool("qiniu.use_https", false)

	logs.Info("七牛云配置最终验证: accessKey=%s, secretKey=%s, bucket=%s, domain=%s, zone=%s, useHTTPS=%v", accessKey, secretKey, bucket, domain, zone, useHTTPS)

	// 第五步：再次验证关键配置是否正确设置
	dbUser := web.AppConfig.DefaultString("mysqluser", "")
	dbHost := web.AppConfig.DefaultString("mysqlhost", "")
	dbPort := web.AppConfig.DefaultString("mysqlport", "")
	dbName := web.AppConfig.DefaultString("mysqldb", "")
	logs.Info("数据库配置最终验证: user=%s, host=%s, port=%s, db=%s", dbUser, dbHost, dbPort, dbName)

	redisHost := web.AppConfig.DefaultString("redishost", "")
	redisPort := web.AppConfig.DefaultString("redisport", "")
	redisDB := web.AppConfig.DefaultString("redisdb", "")
	logs.Info("Redis配置最终验证: host=%s, port=%s, db=%s", redisHost, redisPort, redisDB)

	// 第六步：加载微信小程序配置
	wxMiniAppEnabled, _ := web.AppConfig.Bool("wechat.miniapp.enabled")
	wxMiniAppAppID := web.AppConfig.DefaultString("wechat.miniapp.app_id", "")
	wxMiniAppAppSecret := web.AppConfig.DefaultString("wechat.miniapp.app_secret", "")
	logs.Info("微信小程序配置最终验证: enabled=%v, app_id=%s, app_secret=%s", wxMiniAppEnabled, wxMiniAppAppID, wxMiniAppAppSecret)

	return nil
}

// 从配置文件内容直接解析关键配置项
func parseConfigFromContent(content string) {
	lines := strings.Split(content, "\n")
	section := "" // 当前配置节

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过注释和空行
		if line == "" || strings.HasPrefix(line, "#") || strings.HasPrefix(line, ";") {
			continue
		}

		// 修复节名解析：严格匹配 [section] 格式
		if strings.HasPrefix(line, "[") && strings.HasSuffix(line, "]") {
			newSection := strings.TrimSpace(strings.TrimSuffix(strings.TrimPrefix(line, "["), "]"))
			if newSection != section {
				section = newSection
				logs.Debug("切换到配置节: [%s]", section)
			}
			continue
		}

		// 解析键值对
		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			logs.Warn("无法解析配置行: %s", line)
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		// 去掉尾部注释
		if commentIdx := strings.Index(value, "#"); commentIdx >= 0 {
			value = strings.TrimSpace(value[:commentIdx])
		}

		// 在节中的键使用 section::key 格式
		configKey := key
		if section != "" {
			configKey = section + ":" + key
			logs.Debug("将配置项解析到节 [%s]: %s = %s", section, key, value)
		} else {
			logs.Debug("解析到全局配置项: %s = %s", key, value)
		}

		// 存储到全局映射表
		globalConfig[configKey] = value

		// 对于敏感信息，不记录实际值
		displayValue := value
		if key == "mysqlpass" || key == "redispass" || key == "jwtSecret" {
			displayValue = "***"
		}

		logs.Debug("解析到配置项: %s = %s", configKey, displayValue)
	}

	// 记录关键配置解析结果
	logCriticalConfigs()
}

// 确保配置项值不为空
/*

func ensureConfig(key, value string) {
	if value == "" {
		return
	}

	currentValue := web.AppConfig.DefaultString(key, "")
	if currentValue == "" {
		err := web.AppConfig.Set(key, value)
		if err != nil {
			logs.Error("设置配置项失败 %s: %v", key, err)
		} else {

				displayValue := value
				if key == "mysqlpass" || key == "redispass" {
					displayValue = "***"
				}
				logs.Info("手动设置配置项: %s = %s", key, displayValue)


			// 双重验证
			newValue := web.AppConfig.DefaultString(key, "")
			if newValue == "" {
				//logs.Error("设置配置项后验证失败，尝试备用方法: %s", key)

				// 备用方法：通过环境变量设置（可能会更可靠）
				if key == "mysqluser" {
					os.Setenv("MYSQL_USER", value)
				} else if key == "mysqlhost" {
					os.Setenv("MYSQL_HOST", value)
				} else if key == "mysqlport" {
					os.Setenv("MYSQL_PORT", value)
				} else if key == "mysqldb" {
					os.Setenv("MYSQL_DB", value)
				} else if key == "mysqlpass" {
					os.Setenv("MYSQL_PASS", value)
				} else if key == "redishost" {
					os.Setenv("REDIS_HOST", value)
				} else if key == "redisport" {
					os.Setenv("REDIS_PORT", value)
				} else if key == "redisdb" {
					os.Setenv("REDIS_DB", value)
				} else if key == "redispass" {
					os.Setenv("REDIS_PASS", value)
				}
			}
		}
	}
*/
// 加载所有模块配置文件
func loadModuleConfigs() {
	// 获取当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		logs.Error("获取工作目录失败: %v", err)
		return
	}

	// 构建模块配置目录的绝对路径
	modulesAbsDir := filepath.Join(workDir, ModulesConfigDir)

	// 检查模块配置目录是否存在
	if _, err := os.Stat(modulesAbsDir); os.IsNotExist(err) {
		logs.Warn("模块配置目录不存在: %s", modulesAbsDir)
		return
	}

	// 生成包含绝对路径的 include 配置
	includeConfig := "[include]\n"
	modules := []string{StorageModule, SmsModule, PaymentModule, PushModule}

	for _, module := range modules {
		configFile := filepath.Join(modulesAbsDir, module+".conf")
		if _, err := os.Stat(configFile); err == nil {
			// 使用绝对路径
			includeConfig += fmt.Sprintf("include = %s\n", configFile)
			// 读取并打印配置文件内容
			configData, err := os.ReadFile(configFile)
			if err != nil {
				logs.Error("读取模块配置文件内容失败: %s, 错误: %v", configFile, err)
			} else {
				logs.Debug("模块配置文件内容 (%s):\n%s", configFile, string(configData))
			}
			// 确保配置文件内容被解析到全局配置映射
			parseConfigFile(configFile)
			logs.Info("添加模块配置到 include 并解析: %s", configFile)
		}
	}

	// 将 include 配置写入临时文件
	tmpFile := filepath.Join(os.TempDir(), "o_mall_include.conf")
	if err := os.WriteFile(tmpFile, []byte(includeConfig), 0644); err != nil {
		logs.Error("创建临时 include 文件失败: %v", err)
		return
	}
	defer func() {
		if err := os.Remove(tmpFile); err != nil {
			logs.Warn("删除临时文件失败: %v", err)
		}
	}()

	// 打印临时文件内容用于调试
	logs.Info("生成的 include 临时文件内容:\n%s", includeConfig)

	// 加载临时配置文件（追加模式，不覆盖已有配置）
	if err := web.LoadAppConfig("ini", tmpFile); err != nil {
		logs.Error("加载模块配置文件失败: %v", err)
	} else {
		logs.Info("成功加载 include 配置")
	}
}

// 从环境变量加载配置，并覆盖配置文件中的值
// 环境变量命名格式: O_MALL_[SECTION]_KEY
// 例如: O_MALL_DB_HOST 对应 [database]::db_host
func loadEnvVariables() {
	prefix := "O_MALL_"
	for _, env := range os.Environ() {
		if strings.HasPrefix(env, prefix) {
			parts := strings.SplitN(env, "=", 2)
			if len(parts) != 2 {
				continue
			}

			key := parts[0]
			//value := parts[1]

			// 移除前缀，转换为小写
			configKey := strings.ToLower(strings.TrimPrefix(key, prefix))

			// 转换为配置键格式（将_转为.）
			configKey = strings.Replace(configKey, "_", ".", -1)

			// 第一个点前的部分作为段落名
			//var sectionKey string
			if idx := strings.Index(configKey, "."); idx > 0 {
				//section := configKey[:idx]
				//itemKey := configKey[idx+1:]
				//sectionKey = section + "::" + itemKey
			} else {
				//sectionKey = configKey
			}

			// 设置配置项
			/*
				err := web.AppConfig.Set(sectionKey, value)
				if err != nil {
					logs.Error("通过环境变量设置配置项失败 %s: %v", sectionKey, err)
				} else {
					logs.Info("通过环境变量设置配置项: %s", sectionKey)
				}
			*/
		}
	}
}

// 全局用于记录数据库是否初始化完成的标志
var dbInitialized bool = false

// SetDBInitialized 设置数据库初始化完成标志
func SetDBInitialized() {
	dbInitialized = true
	logs.Info("数据库初始化完成标志已设置")
}

// GetStorageConfig 获取存储配置
// mode: 获取存储模式（local、oss、cos、s3）
// enableCDN: 是否启用CDN加速
func GetStorageConfig() (mode string, enableCDN bool) {
	// 默认值
	mode = "local"
	enableCDN = false

	// 查看是否已初始化数据库，如果没有，直接使用配置文件
	if !dbInitialized {
		// 数据库未初始化，使用配置文件配置
		mode = web.AppConfig.DefaultString("storage.mode", "local")
		enableCDN, _ = web.AppConfig.Bool("storage.enable_cdn")

		logs.Info("数据库尚未初始化，使用配置文件中的存储模式: mode=%s, enableCDN=%v", mode, enableCDN)
		return
	}

	// 尝试从数据库获取配置
	try := func() bool {
		defer func() {
			if r := recover(); r != nil {
				logs.Error("访问数据库获取存储配置时发生错误: %v", r)
			}
		}()

		// 使用原生SQL查询，避免循环依赖
		o := orm.NewOrm()

		// 查询启用状态的存储配置
		var config struct {
			StorageMode string
			EnableCdn   int8
		}

		err := o.Raw("SELECT storage_mode, enable_cdn FROM system_upload_config WHERE status = 1 LIMIT 1").QueryRow(&config)
		if err == nil {
			// 数据库查询成功，使用数据库配置
			mode = config.StorageMode
			enableCDN = config.EnableCdn == 1

			logs.Info("从数据库获取存储配置: mode=%s, enableCDN=%v", mode, enableCDN)
			return true
		} else {
			logs.Warn("从数据库获取存储配置失败: %v", err)
			return false
		}
	}

	// 如果数据库查询失败，使用配置文件配置
	if !try() {
		mode = web.AppConfig.DefaultString("storage.mode", "local")
		enableCDN, _ = web.AppConfig.Bool("storage.enable_cdn")
		logs.Info("回退到配置文件中的存储模式: mode=%s, enableCDN=%v", mode, enableCDN)
	}

	// 记录从不同方式读取的存储模式值，以便调试
	storageVal1 := web.AppConfig.DefaultString("storage.mode", "<<未设置>>")
	storageVal2 := web.AppConfig.DefaultString("storage::mode", "<<未设置>>")
	storageVal3 := globalConfig["storage::mode"]

	logs.Info("存储模式获取来源比较: DB=%s, storage.mode=%s, storage::mode=%s, globalConfig[storage::mode]=%s",
		mode, storageVal1, storageVal2, storageVal3)

	return
}

// GetCDNDomain 获取CDN域名
func GetCDNDomain() string {
	// 使用正确的键名格式
	return web.AppConfig.DefaultString("storage.cdn_domain", "")
}

// GetSMSProvider 获取短信服务提供商
func GetSMSProvider() string {
	// 先尝试从 sms::sms_provider 读取（模块配置文件格式）
	provider := web.AppConfig.DefaultString("sms::sms_provider", "")
	if provider != "" {
		return provider
	}

	// 兼容旧的键名格式
	return web.AppConfig.DefaultString("sms.sms_provider", "aliyun")
}

// GetSMSTemplate 获取短信模板
// provider: 服务提供商
// usage: 模板用途（register、login、reset_pwd等）
func GetSMSTemplate(provider, usage string) string {
	// 先尝试从模块配置文件读取（sms::）
	key1 := fmt.Sprintf("sms::%s_template_%s", provider, usage)
	template := web.AppConfig.DefaultString(key1, "")
	if template != "" {
		logs.Info("从模块配置文件中读取到SMS模板，键名: %s", key1)
		return template
	}

	// 兼容旧的键名格式
	key2 := fmt.Sprintf("sms.%s_template_%s", provider, usage)
	template = web.AppConfig.DefaultString(key2, "")
	if template != "" {
		logs.Info("从主配置文件中读取到SMS模板，键名: %s", key2)
		return template
	}

	logs.Warn("未找到用途 %s 的 %s 短信模板", usage, provider)
	return ""
}

// 配置热重载
// 只重新加载指定模块的配置文件
func ReloadModuleConfig(module string) error {
	configFile := filepath.Join(ModulesConfigDir, module+".conf")
	if _, err := os.Stat(configFile); err != nil {
		return fmt.Errorf("模块配置文件不存在: %s", configFile)
	}

	err := web.LoadAppConfig("ini", configFile)
	if err != nil {
		return fmt.Errorf("重新加载模块配置文件失败: %v", err)
	}

	logs.Info("已重新加载模块配置文件: %s", configFile)
	return nil
}

// 将 globalConfig 中的配置项设置到 web.AppConfig 中
func SetGlobalConfigToAppConfig() {
	logs.Info("开始将 globalConfig 中的配置设置到 web.AppConfig")

	// 关键配置列表，这些配置必须确保正确设置
	criticalConfigs := []string{
		"runmode",
		"mysqluser", "mysqlhost", "mysqlport", "mysqldb", "mysqlpass",
		"redishost", "redisport", "redisdb", "redispass",
	}

	// 关键模块配置列表，明确定义每个模块的关键配置
	moduleConfigs := map[string][]string{
		"qiniu":   {"access_key", "secret_key", "bucket", "domain", "zone", "use_https"},
		"storage": {"mode", "enable_cdn"},
		"sms":     {"sms_provider"},
	}

	// 先设置基础关键配置
	for _, key := range criticalConfigs {
		value, exists := globalConfig[key]
		if !exists {
			logs.Warn("globalConfig 中不存在关键配置项: %s", key)
			continue
		}

		err := web.AppConfig.Set(key, value)
		if err != nil {
			logs.Error("设置关键配置项失败 %s: %v", key, err)
		} else {
			// 敏感信息不显示值
			displayValue := value
			if key == "mysqlpass" || key == "redispass" {
				displayValue = "***"
			}
			logs.Info("成功设置关键配置项: %s = %s", key, displayValue)

			// 验证配置是否正确设置
			verification := web.AppConfig.DefaultString(key, "")
			if verification != value && !(key == "mysqlpass" || key == "redispass") {
				logs.Error("关键配置项设置后验证失败: %s (期望 %s, 实际 %s)", key, value, verification)
			}
		}
	}

	// 设置模块关键配置
	for module, keys := range moduleConfigs {
		for _, key := range keys {
			// 模块配置格式：module::key
			fullKey := module + ":" + key
			value, exists := globalConfig[fullKey]
			if !exists {
				logs.Warn("globalConfig 中不存在模块配置项: %s", fullKey)
				continue
			}

			// 对于Beego配置，需要转换成section.key格式
			beegoKey := module + "." + key

			// 同时设置两种格式的键，确保兼容性
			err1 := web.AppConfig.Set(beegoKey, value)
			err2 := web.AppConfig.Set(fullKey, value)

			if err1 != nil {
				logs.Error("设置模块配置项失败 %s: %v", beegoKey, err1)
			} else {
				displayValue := maskSensitiveValue(fullKey, value)
				logs.Info("成功设置模块配置项: %s = %s", beegoKey, displayValue)
			}

			if err2 != nil {
				logs.Error("设置原始格式模块配置项失败 %s: %v", fullKey, err2)
			}

			// 验证配置是否正确设置
			verification := web.AppConfig.DefaultString(beegoKey, "")
			if verification != value && !strings.Contains(fullKey, "secret") && !strings.Contains(fullKey, "pass") {
				logs.Error("模块配置项设置后验证失败: %s (期望 %s, 实际 %s)", beegoKey, value, verification)
			}
		}
	}

	// 设置其余配置项
	for key, value := range globalConfig {
		// 跳过已处理的关键配置
		isCritical := false
		for _, critKey := range criticalConfigs {
			if key == critKey {
				isCritical = true
				break
			}
		}
		if isCritical {
			continue
		}

		// 跳过已处理的模块配置
		isModuleConfig := false
		for module, keys := range moduleConfigs {
			modulePrefix := module + ":"
			if strings.HasPrefix(key, modulePrefix) {
				// 检查是否为已处理的模块关键配置
				configKey := strings.TrimPrefix(key, modulePrefix)
				for _, moduleKey := range keys {
					if configKey == moduleKey {
						isModuleConfig = true
						break
					}
				}
				if isModuleConfig {
					break
				}
			}
		}
		if isModuleConfig {
			continue
		}

		// 处理其余配置项
		// 检查是否为模块配置(带有::分隔符)
		if strings.Contains(key, ":") {
			parts := strings.SplitN(key, ":", 2)
			if len(parts) == 2 {
				// 转换为Beego配置格式: 从 "module::key" 到 "module.key"
				beegoKey := parts[0] + "." + parts[1]

				// 同时设置两种格式的键，确保兼容性
				err1 := web.AppConfig.Set(beegoKey, value)
				err2 := web.AppConfig.Set(key, value)

				if err1 != nil {
					logs.Error("设置其他模块配置项失败 %s: %v", beegoKey, err1)
				} else {
					logs.Debug("成功设置其他模块配置项: %s = %s", beegoKey, maskSensitiveValue(key, value))
				}

				if err2 != nil {
					logs.Error("设置原始格式其他模块配置项失败 %s: %v", key, err2)
				}

				continue
			}
		}

		// 对于普通配置，直接设置
		err := web.AppConfig.Set(key, value)
		if err != nil {
			logs.Error("设置其他配置项失败 %s: %v", key, err)
		} else {
			logs.Debug("成功设置其他配置项: %s = %s", key, maskSensitiveValue(key, value))
		}
	}

	logs.Info("globalConfig 中的配置已成功设置到 web.AppConfig")

	// 最终验证关键配置
	runmode := web.AppConfig.DefaultString("runmode", "")
	mysqlUser := web.AppConfig.DefaultString("mysqluser", "")
	redisHost := web.AppConfig.DefaultString("redishost", "")

	// 验证模块配置，同时检查两种格式
	qiniuDotFormat := web.AppConfig.DefaultString("qiniu.access_key", "")
	qiniuColonFormat := web.AppConfig.DefaultString("qiniu::access_key", "")
	storageDotFormat := web.AppConfig.DefaultString("storage.mode", "")
	storageColonFormat := web.AppConfig.DefaultString("storage::mode", "")
	smsDotFormat := web.AppConfig.DefaultString("sms.sms_provider", "")
	smsColonFormat := web.AppConfig.DefaultString("sms::sms_provider", "")

	logs.Info("配置最终验证:")
	logs.Info("  基础配置: runmode=%s, mysqluser=%s, redishost=%s", runmode, mysqlUser, redisHost)
	logs.Info("  模块配置(点格式): qiniu.access_key=%s, storage.mode=%s, sms.sms_provider=%s",
		maskSensitiveValue("qiniu.access_key", qiniuDotFormat), storageDotFormat, smsDotFormat)
	logs.Info("  模块配置(冒号格式): qiniu::access_key=%s, storage::mode=%s, sms::sms_provider=%s",
		maskSensitiveValue("qiniu::access_key", qiniuColonFormat), storageColonFormat, smsColonFormat)
}

// 敏感信息掩码函数
func maskSensitiveValue(key, value string) string {
	if strings.Contains(key, "secret") || strings.Contains(key, "access_key") {
		if len(value) > 4 {
			return value[:2] + "****" + value[len(value)-2:]
		}
		return "****"
	}
	return value
}

// 记录关键配置解析结果
func logCriticalConfigs() {
	// 关键配置列表
	criticalConfigs := []string{
		"runmode",
		"mysqluser", "mysqlhost", "mysqlport", "mysqldb",
		"redishost", "redisport", "redisdb",
	}

	logs.Info("关键配置解析结果:")
	for _, key := range criticalConfigs {
		value, exists := globalConfig[key]
		if exists {
			logs.Info("  %s = %s", key, value)
		} else {
			logs.Warn("  %s 未找到", key)
		}
	}
}

// parseConfigFile 解析指定配置文件到 globalConfig
func parseConfigFile(configPath string) {
	content, err := os.ReadFile(configPath)
	if err != nil {
		logs.Error("读取配置文件失败: %s, 错误: %v", configPath, err)
		return
	}

	logs.Info("开始解析配置文件: %s", configPath)
	parseConfigFromContent(string(content))
}
