/**
 * 文件上传配置
 *
 * 该文件提供了文件上传相关的配置项，包括允许的文件类型、大小限制、匿名上传配置等。
 * 用于统一管理文件上传的各项配置参数。
 * 
 * 更新：配置从数据库中获取，而非配置文件。
 */

package config

import (
	"strings"
	"sync"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// 默认允许的文件类型
var defaultAllowedFileTypes = []string{
	"image/jpeg", "image/png", "image/gif", "image/webp",
	"application/pdf",
	"application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	"application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
	"application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
	"text/plain",
}

// 默认匿名上传允许的用途类型
var defaultAnonymousUsageTypes = []string{
	"complaint",
}

// 上传配置缓存结构
type configCache struct {
	allowAnonymous      bool
	maxFileSize         int64
	allowedFileTypes    []string
	anonymousUsageTypes []string
	lastUpdate          int64
}

// 缓存实例
var uploadCache *configCache
var cacheMutex sync.RWMutex

// 获取上传配置（从数据库或缓存）
func getConfigFromDB() *configCache {
	cacheMutex.RLock()
	// 检查是否有缓存，且缓存在1分钟内
	if uploadCache != nil && time.Now().Unix()-uploadCache.lastUpdate < 60 {
		cache := uploadCache
		cacheMutex.RUnlock()
		return cache
	}
	cacheMutex.RUnlock()

	// 获取锁，防止多线程并发更新缓存
	cacheMutex.Lock()
	defer cacheMutex.Unlock()

	// 双重检查，避免其他线程在获取锁之前已经更新了缓存
	if uploadCache != nil && time.Now().Unix()-uploadCache.lastUpdate < 60 {
		logs.Info("使用缓存的上传配置，匿名用途类型: %v", uploadCache.anonymousUsageTypes)
		return uploadCache
	}

	// 初始化缓存对象，预设默认值
	result := &configCache{
		allowAnonymous:      true, // 默认值
		maxFileSize:         10 * 1024 * 1024, // 默认10MB
		allowedFileTypes:    defaultAllowedFileTypes,
		anonymousUsageTypes: defaultAnonymousUsageTypes,
		lastUpdate:          time.Now().Unix(),
	}

	// 查看是否已初始化数据库，如果没有，则使用默认配置
	if !dbInitialized {
		logs.Info("数据库尚未初始化，使用上传的默认配置")
		
		// 尝试从配置文件读取一些配置
		allowAnonymousConfig, err := web.AppConfig.Bool("upload::allow_anonymous")
		if err == nil {
			result.allowAnonymous = allowAnonymousConfig
		}
		
		maxFileSizeMB, err := web.AppConfig.Int64("upload::max_file_size")
		if err == nil && maxFileSizeMB > 0 {
			result.maxFileSize = maxFileSizeMB * 1024 * 1024 // 转换为字节
		}
		
		// 设置缓存
		uploadCache = result
		return result
	}

	// 数据库已初始化，尝试从数据库获取配置
	success := false
	func() {
		// 使用匿名函数+defer recover防止数据库异常导致崩溃
		defer func() {
			if r := recover(); r != nil {
				logs.Error("访问数据库获取上传配置时发生异常: %v", r)
			}
		}()
		
		o := orm.NewOrm()
		var config struct {
			StorageMode       string
			MaxSize           int64
			AllowedExtensions string
			EnableCdn         int8
			Status            int8
		}
		
		// 仅查询启用状态的配置
		err := o.Raw("SELECT storage_mode, max_size, allowed_extensions, enable_cdn, status FROM system_upload_config WHERE status = 1 LIMIT 1").QueryRow(&config)
		
		if err == nil && config.Status == 1 {
			// 从数据库获取允许匿名上传的用途类型
			var anonymousUsageTypes []string
			_, err2 := o.Raw("SELECT usage_code FROM system_file_usage_config WHERE status = 1 AND allow_anonymous = 1").QueryRows(&anonymousUsageTypes)
			if err2 == nil {
				result.anonymousUsageTypes = anonymousUsageTypes
				logs.Info("从数据库获取匿名用途类型: %v", anonymousUsageTypes)
			} else {
				logs.Warn("从数据库获取匿名用途类型失败: %v，使用默认配置", err2)
			}
			// 设置最大文件大小
			if config.MaxSize > 0 {
				result.maxFileSize = config.MaxSize
			}
	
			// 处理允许的文件扩展名
			if config.AllowedExtensions != "" {
				extensions := strings.Split(config.AllowedExtensions, ",")
				// 将扩展名转换为MIME类型
				mimeTypes := []string{}
				
				// 保留默认MIME类型
				mimeTypes = append(mimeTypes, defaultAllowedFileTypes...)
				
				// 添加扩展名对应的MIME类型
				for _, ext := range extensions {
					ext = strings.TrimSpace(ext)
					if ext == "" {
						continue
					}
					
					// 简单映射一些常见扩展名
					switch strings.ToLower(ext) {
					case "jpg", "jpeg":
						mimeTypes = append(mimeTypes, "image/jpeg")
					case "png":
						mimeTypes = append(mimeTypes, "image/png")
					case "gif":
						mimeTypes = append(mimeTypes, "image/gif")
					case "pdf":
						mimeTypes = append(mimeTypes, "application/pdf")
					case "doc", "docx":
						mimeTypes = append(mimeTypes, "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
					case "xls", "xlsx":
						mimeTypes = append(mimeTypes, "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
					}
				}
				
				// 去重
				unique := make(map[string]bool)
				for _, mime := range mimeTypes {
					unique[mime] = true
				}
				
				// 转回切片
				result.allowedFileTypes = []string{}
				for mime := range unique {
					result.allowedFileTypes = append(result.allowedFileTypes, mime)
				}
			}
			
			logs.Info("从数据库加载上传配置成功：最大文件大小=%dMB, 允许的文件类型=%v", result.maxFileSize/(1024*1024), result.allowedFileTypes)
			success = true
		}
	}()

	if !success {
		// 数据库查询失败，使用默认值并尝试从配置文件读取
		logs.Warn("从数据库获取上传配置失败，尝试使用配置文件")
		
		// 尝试从配置文件读取
		maxFileSizeMB, err := web.AppConfig.Int64("upload::max_file_size")
		if err == nil && maxFileSizeMB > 0 {
			result.maxFileSize = maxFileSizeMB * 1024 * 1024
		}
		
		// 获取配置文件中的允许匿名上传设置
		allowAnonymous, err := web.AppConfig.Bool("upload::allow_anonymous")
		if err == nil {
			result.allowAnonymous = allowAnonymous
		}
		
		logs.Info("从配置文件加载上传配置：allowAnonymous=%v, maxFileSize=%dMB", result.allowAnonymous, result.maxFileSize/(1024*1024))
	}

	// 更新缓存
	uploadCache = result
	return result
}

// GetUploadConfig 获取文件上传配置
// 保持与原函数相同的接口，便于平滑切换到数据库配置
func GetUploadConfig() (bool, int64, []string, []string) {
	// 从数据库获取配置
	config := getConfigFromDB()
	
	return config.allowAnonymous, config.maxFileSize, config.allowedFileTypes, config.anonymousUsageTypes
}

// ClearUploadConfigCache 清除上传配置缓存（用于开发调试）
func ClearUploadConfigCache() {
	cacheMutex.Lock()
	defer cacheMutex.Unlock()
	uploadCache = nil
	logs.Info("上传配置缓存已清除")
}

// IsAnonymousUsageAllowed 检查是否允许匿名上传指定用途的文件
func IsAnonymousUsageAllowed(fileUsage string) bool {
	allowAnonymous, _, _, anonymousUsageTypes := GetUploadConfig()
	if !allowAnonymous {
		return false
	}

	for _, usageType := range anonymousUsageTypes {
		if usageType == fileUsage {
			return true
		}
	}
	return false
}
