/**
 * 上下文工具函数
 *
 * 该文件提供与请求上下文相关的工具函数，包括创建上下文对象。
 * 主要用于异步操作中创建新的上下文。
 */

package utils

import (
	"context"
	"time"
)

// CreateContext 创建上下文
// 用于异步操作中创建上下文，避免使用已过期或取消的上下文
func CreateContext() context.Context {
	// 创建一个带30秒超时的上下文
	ctx, _ := context.WithTimeout(context.Background(), 30*time.Second)
	return ctx
}

// CreateContextWithTimeout 创建带有指定超时时间的上下文对象
func CreateContextWithTimeout(timeout time.Duration) context.Context {
	ctx, _ := context.WithTimeout(context.Background(), timeout)
	return ctx
}

// CreateContextWithCancel 创建带有取消功能的上下文对象
func CreateContextWithCancel() (context.Context, context.CancelFunc) {
	return context.WithCancel(context.Background())
}
