/**
 * 错误处理工具
 *
 * 该文件提供统一的错误处理机制，用于创建、包装和处理系统中的各种错误。
 * 通过标准化的错误结构，方便错误传递和处理。
 */

package utils

import (
	"fmt"
	"runtime"

	"o_mall_backend/common/result"

	"github.com/beego/beego/v2/core/logs"
)

// AppError 应用错误结构
type AppError struct {
	Code    int         // 错误码
	Message string      // 错误消息
	Details interface{} // 错误详情
	Stack   string      // 错误堆栈
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ResponseSuccess 返回成功响应
func ResponseSuccess(data interface{}) *Response {
	return &Response{
		Code:    200,
		Message: "success",
		Data:    data,
	}
}

// ResponseError 返回错误响应
func ResponseError(message string, code int) *Response {
	if code == 0 {
		code = 500
	}
	return &Response{
		Code:    code,
		Message: message,
	}
}

// Error 实现error接口
func (e *AppError) Error() string {
	return fmt.Sprintf("错误码: %d, 错误信息: %s", e.Code, e.Message)
}

// NewAppError 创建应用错误
func NewAppError(code int, message string, details interface{}) *AppError {
	err := &AppError{
		Code:    code,
		Message: message,
		Details: details,
	}

	// 获取错误堆栈
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	err.Stack = string(buf[:n])

	// 记录错误日志
	logs.Error("业务错误: %s, 详情: %v, 堆栈: %s", err.Error(), details, err.Stack)

	return err
}

// ToResponse 将错误转换为标准响应格式
func (e *AppError) ToResponse() *result.Result {
	return &result.Result{
		Code:    e.Code,
		Message: e.Message,
	}
}

// WrapError 包装标准错误为应用错误
func WrapError(err error, code int, message string) *AppError {
	if err == nil {
		return nil
	}

	if appErr, ok := err.(*AppError); ok {
		return appErr
	}

	details := err.Error()
	return NewAppError(code, message, details)
}

// ConvertToAppError 将错误转换为应用错误
func ConvertToAppError(err error) *AppError {
	if err == nil {
		return nil
	}

	if appErr, ok := err.(*AppError); ok {
		return appErr
	}

	return NewAppError(result.CodeError, err.Error(), nil)
}
