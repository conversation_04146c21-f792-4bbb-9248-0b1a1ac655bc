/**
 * 文件处理工具
 *
 * 该文件提供与文件处理相关的工具函数，包括获取客户端IP、获取上传路径等。
 */

package utils

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/beego/beego/v2/server/web"
	beecontext "github.com/beego/beego/v2/server/web/context"
)

// GetClientIP 获取客户端IP地址
// 从上下文中获取客户端IP地址，支持多种上下文类型
func GetClientIP(ctx context.Context) string {
	// 如果是 Beego Context 类型的上下文
	if beeCtx, ok := ctx.Value("beegoContext").(*beecontext.Context); ok {
		return beeCtx.Input.IP()
	}

	// 如果是 http.Request 类型的上下文
	if req, ok := ctx.Value("request").(*http.Request); ok {
		return getIPFromRequest(req)
	}

	// 默认返回本地IP
	return "127.0.0.1"
}

// getIPFromRequest 从HTTP请求中获取客户端IP
func getIPFromRequest(req *http.Request) string {
	// 尝试从X-Forwarded-For头获取真实IP
	if xff := req.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 尝试从X-Real-IP头获取
	if xrip := req.Header.Get("X-Real-IP"); xrip != "" {
		return xrip
	}

	// 从请求RemoteAddr获取
	if req.RemoteAddr != "" {
		ip := strings.Split(req.RemoteAddr, ":")[0]
		return ip
	}

	return "127.0.0.1"
}

// GetUploadPath 获取文件上传根路径
func GetUploadPath() (string, error) {
	// 获取配置中的上传路径
	uploadPath, err := web.AppConfig.String("upload_path")
	if err != nil || uploadPath == "" {
		// 如果配置未设置，使用默认路径
		uploadPath = "uploads"
	}

	// 如果路径不是绝对路径，则相对于应用根目录
	if !filepath.IsAbs(uploadPath) {
		appPath, err := os.Getwd()
		if err != nil {
			return "", fmt.Errorf("获取应用根目录失败: %v", err)
		}
		uploadPath = filepath.Join(appPath, uploadPath)
	}

	// 确保目录存在
	if err := os.MkdirAll(uploadPath, 0755); err != nil {
		return "", fmt.Errorf("创建上传目录失败: %v", err)
	}

	return uploadPath, nil
}

// GetFileExtension 从文件名获取扩展名（包含点，如 .jpg）
func GetFileExtension(filename string) string {
	return strings.ToLower(filepath.Ext(filename))
}

// GetFileSizeString 将文件大小转换为易读的字符串
func GetFileSizeString(size int64) string {
	const (
		B  = 1
		KB = 1024 * B
		MB = 1024 * KB
		GB = 1024 * MB
	)

	switch {
	case size >= GB:
		return fmt.Sprintf("%.2f GB", float64(size)/float64(GB))
	case size >= MB:
		return fmt.Sprintf("%.2f MB", float64(size)/float64(MB))
	case size >= KB:
		return fmt.Sprintf("%.2f KB", float64(size)/float64(KB))
	default:
		return fmt.Sprintf("%d B", size)
	}
}
