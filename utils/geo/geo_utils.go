// Package geo 提供地理位置相关的工具函数
package geo

import (
	"math"
)

// 地球半径，单位米
const earthRadiusKm = 6371.0

// CalculateDistance 使用Haversine公式计算两个地理坐标点之间的距离（公里）
// lat1, lng1: 第一个点的纬度和经度
// lat2, lng2: 第二个点的纬度和经度
// 返回值: 两点之间的距离，单位公里
func CalculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	// 转换为弧度
	lat1 = lat1 * math.Pi / 180.0
	lng1 = lng1 * math.Pi / 180.0
	lat2 = lat2 * math.Pi / 180.0
	lng2 = lng2 * math.Pi / 180.0

	// Haversine公式
	dLat := lat2 - lat1
	dLng := lng2 - lng1
	a := math.Sin(dLat/2)*math.Sin(dLat/2) + math.Cos(lat1)*math.Cos(lat2)*math.Sin(dLng/2)*math.Sin(dLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	distance := earthRadiusKm * c

	// 保留两位小数
	distance = math.Round(distance*100) / 100

	return distance
}

// IsValidCoordinate 检查经纬度坐标是否有效
// lat: 纬度，有效范围 -90 到 90
// lng: 经度，有效范围 -180 到 180
// 返回值: 坐标是否有效
func IsValidCoordinate(lat, lng float64) bool {
	return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180
}
