/**
 * JWT工具
 *
 * 该文件提供JWT（JSON Web Token）的生成和验证功能。
 * JWT用于用户认证，支持双token刷新模式：
 * - Access Token：短期有效，用于API访问
 * - Refresh Token：长期有效，用于获取新的Access Token
 */

package utils

import (
	"errors"
	"strings"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
	"github.com/dgrijalva/jwt-go"
)

// TokenType 定义token类型
type TokenType string

const (
	// TokenTypeAccess 访问令牌类型
	TokenTypeAccess TokenType = "access"
	// TokenTypeRefresh 刷新令牌类型
	TokenTypeRefresh TokenType = "refresh"
)

// TokenClaims 定义JWT的payload部分
type TokenClaims struct {
	UserID   int64     `json:"user_id"`
	Username string    `json:"username"`
	Role     string    `json:"role"`
	Type     TokenType `json:"type"`
	jwt.StandardClaims
}

// TokenPair 定义token对，包含访问令牌和刷新令牌
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"` // 访问令牌过期时间（秒）
}

// JWT相关错误定义
var (
	ErrTokenExpired     = errors.New("令牌已过期")
	ErrTokenInvalid     = errors.New("无效的令牌")
	ErrTokenNotProvided = errors.New("未提供令牌")
	ErrSecretNotConfig  = errors.New("JWT密钥未配置")
	ErrInvalidTokenType = errors.New("无效的令牌类型")
)

// GetJWTSecret 获取JWT密钥
func GetJWTSecret() (string, error) {
	// 先尝试获取jwtSecret配置
	jwtSecret, err := web.AppConfig.String("jwtSecret")
	logs.Info("jwtSecret: %s", jwtSecret)
	if err == nil && jwtSecret != "" {
		return jwtSecret, nil
	}

	// 如果失败，尝试获取jwt::secret配置
	jwtSecret, err = web.AppConfig.String("jwt::secret")
	if err == nil && jwtSecret != "" {
		return jwtSecret, nil
	}

	return "", ErrSecretNotConfig
}

// GenerateAccessToken 生成访问令牌
// 参数：
//   - userID: 用户ID
//   - username: 用户名
//   - role: 用户角色
//
// 返回：
//   - token: 生成的访问令牌
//   - expiresIn: 过期时间（秒）
//   - err: 错误信息
func GenerateAccessToken(userID int64, username, role string) (string, int64, error) {
	// 获取JWT密钥
	jwtSecret, err := GetJWTSecret()
	if err != nil {
		return "", 0, err
	}

	// 获取过期时间
	expireMinutes, err := web.AppConfig.Int("jwt::access_token_expire_minutes")
	if err != nil {
		expireMinutes = 10 // 默认10分钟
	}

	// 计算过期时间
	expiresAt := time.Now().Add(time.Minute * time.Duration(expireMinutes))
	expiresIn := int64(expiresAt.Sub(time.Now()).Seconds())

	// 设置载荷
	claims := TokenClaims{
		UserID:   userID,
		Username: username,
		Role:     role,
		Type:     TokenTypeAccess,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expiresAt.Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "o_mall",
		},
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名token
	tokenString, err := token.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", 0, err
	}

	return tokenString, expiresIn, nil
}

// GenerateRefreshToken 生成刷新令牌
// 参数：
//   - userID: 用户ID
//   - username: 用户名
//   - role: 用户角色
//
// 返回：
//   - token: 生成的刷新令牌
//   - err: 错误信息
func GenerateRefreshToken(userID int64, username, role string) (string, error) {
	// 获取JWT密钥
	jwtSecret, err := GetJWTSecret()
	if err != nil {
		return "", err
	}

	// 获取过期时间
	expireDays, err := web.AppConfig.Int("jwt::refresh_token_expire_days")
	if err != nil {
		expireDays = 30 // 默认30天
	}

	// 设置载荷
	claims := TokenClaims{
		UserID:   userID,
		Username: username,
		Role:     role,
		Type:     TokenTypeRefresh,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(time.Hour * 24 * time.Duration(expireDays)).Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "o_mall",
		},
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名token
	tokenString, err := token.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// GenerateTokenPair 生成token对（访问令牌和刷新令牌）
// 参数：
//   - userID: 用户ID
//   - username: 用户名
//   - role: 用户角色
//
// 返回：
//   - tokenPair: 包含访问令牌和刷新令牌的结构
//   - err: 错误信息
func GenerateTokenPair(userID int64, username, role string) (*TokenPair, error) {
	// 生成访问令牌
	accessToken, expiresIn, err := GenerateAccessToken(userID, username, role)
	if err != nil {
		return nil, err
	}

	// 生成刷新令牌
	refreshToken, err := GenerateRefreshToken(userID, username, role)
	if err != nil {
		return nil, err
	}

	// 返回token对
	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    expiresIn,
	}, nil
}

// GenerateToken 生成JWT令牌（为了兼容旧代码，内部调用GenerateAccessToken）
// 参数：
//   - userID: 用户ID
//   - username: 用户名
//   - role: 用户角色
//
// 返回：
//   - token: 生成的JWT令牌
//   - err: 错误信息
func GenerateToken(userID int64, username, role string) (string, error) {
	token, _, err := GenerateAccessToken(userID, username, role)
	return token, err
}

// ParseToken 解析JWT令牌
// 参数：
//   - tokenString: JWT令牌字符串
//
// 返回：
//   - claims: 解析出的JWT声明
//   - err: 错误信息
func ParseToken(tokenString string) (*TokenClaims, error) {
	// 获取JWT密钥
	jwtSecret, err := GetJWTSecret()
	if err != nil {
		return nil, err
	}

	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtSecret), nil
	})

	if err != nil {
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorExpired != 0 {
				return nil, ErrTokenExpired
			}
		}
		return nil, err
	}

	// 验证token
	if claims, ok := token.Claims.(*TokenClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, ErrTokenInvalid
}

// ParseTokenWithType 解析指定类型的JWT令牌
// 参数：
//   - tokenString: JWT令牌字符串
//   - tokenType: 令牌类型
//
// 返回：
//   - claims: 解析出的JWT声明
//   - err: 错误信息
func ParseTokenWithType(tokenString string, tokenType TokenType) (*TokenClaims, error) {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 验证token类型
	if claims.Type != tokenType {
		return nil, ErrInvalidTokenType
	}

	return claims, nil
}

// ExtractClaims 从JWT令牌中提取声明信息
// 参数：
//   - tokenString: JWT令牌字符串
//
// 返回：
//   - claims: 提取的声明信息，以map形式返回
//   - err: 错误信息
func ExtractClaims(tokenString string) (map[string]interface{}, error) {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 将claims转换为map
	return map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"role":     claims.Role,
		"type":     claims.Type,
	}, nil
}

// GetTokenFromRequest 从请求中获取Token
// 参数：
//   - ctx: Beego上下文对象
//
// 返回：
//   - token: 从请求中提取的令牌
func GetTokenFromRequest(ctx *context.Context) string {
	// 从Header中获取
	token := ctx.Input.Header("Authorization")
	if token != "" {
		if strings.HasPrefix(token, "Bearer ") {
			return strings.TrimPrefix(token, "Bearer ")
		}
		return token
	}

	// 从Query参数中获取
	token = ctx.Input.Query("token")
	if token != "" {
		return token
	}

	// 从Cookie中获取
	tokenCookieName, err := web.AppConfig.String("auth::token_cookie_name")
	if err != nil {
		tokenCookieName = "token"
	}

	cookie := ctx.Input.Cookie(tokenCookieName)
	return string(cookie)
}

// SetUserContext 设置用户上下文信息
// 参数：
//   - ctx: Beego上下文对象
//   - claims: 用户声明信息
func SetUserContext(ctx *context.Context, claims map[string]interface{}) {
	ctx.Input.SetData("user_id", claims["user_id"])
	ctx.Input.SetData("username", claims["username"])
	ctx.Input.SetData("role", claims["role"])
}
