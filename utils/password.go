/**
 * 密码处理工具
 *
 * 该文件提供了密码加密和验证的工具函数。
 * 使用bcrypt算法进行密码加密。
 */

package utils

import (
	"golang.org/x/crypto/bcrypt"
)

// EncryptPassword 加密密码
// 使用bcrypt算法对密码进行加密，返回加密后的密码哈希值
func EncryptPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// ValidatePassword 验证密码
// 比较明文密码和加密后的密码，返回是否匹配
func ValidatePassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
