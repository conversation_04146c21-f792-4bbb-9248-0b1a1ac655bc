/**
 * 随机工具函数
 *
 * 本文件提供生成随机字符串、数字等工具函数
 */

package utils

import (
	"math/rand"
	"time"
)

// 随机字符集
const (
	Letters       = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	Digits        = "0123456789"
	LetterDigits  = Letters + Digits
	SpecialChars  = "!@#$%^&*()-_=+[]{}|;:,.<>?/"
	AllChars      = LetterDigits + SpecialChars
)

// 初始化随机数生成器
func init() {
	rand.Seed(time.Now().UnixNano())
}

// RandString 生成指定长度的随机字符串
// 参数:
//   - length: 字符串长度
//   - charset: 字符集（默认为字母和数字）
// 返回值: 生成的随机字符串
func RandString(length int, charset ...string) string {
	chars := LetterDigits
	if len(charset) > 0 {
		chars = charset[0]
	}
	
	// 确保字符集不为空
	if chars == "" {
		chars = LetterDigits
	}
	
	// 生成随机字符串
	result := make([]byte, length)
	for i := range result {
		result[i] = chars[rand.Intn(len(chars))]
	}
	
	return string(result)
}

// RandInt 生成指定范围的随机整数
// 参数:
//   - min: 最小值（包含）
//   - max: 最大值（包含）
// 返回值: 生成的随机整数
func RandInt(min, max int) int {
	// 确保范围有效
	if min > max {
		min, max = max, min
	}
	
	return min + rand.Intn(max-min+1)
}
