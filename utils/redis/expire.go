/**
 * Redis过期时间相关工具方法
 *
 * 提供设置带过期时间的键值对方法
 */

package redis

import (
	"time"

	"github.com/beego/beego/v2/core/logs"
)

// SetWithExpire 设置字符串值并指定过期时间（秒）
// 这个方法与Set类似，但接受整数秒作为过期时间参数
func SetWithExpire(key string, value interface{}, seconds int) error {
	expiration := time.Duration(seconds) * time.Second
	err := Set(key, value, expiration)
	if err != nil {
		logs.Error("Redis设置带过期时间的键[%s]失败: %v", key, err)
		return err
	}
	return nil
}

// SetWithExpireTime 设置字符串值并指定到期时间点
// 计算当前时间到指定时间点的间隔作为过期时间
func SetWithExpireTime(key string, value interface{}, expireAt time.Time) error {
	// 计算从现在到过期时间的时间间隔
	expiration := time.Until(expireAt)
	if expiration <= 0 {
		logs.Warn("Redis设置键[%s]的过期时间已过，键将立即过期", key)
		expiration = 1 * time.Second // 设置为1秒后过期
	}
	
	err := Set(key, value, expiration)
	if err != nil {
		logs.Error("Redis设置带过期时间点的键[%s]失败: %v", key, err)
		return err
	}
	return nil
}
