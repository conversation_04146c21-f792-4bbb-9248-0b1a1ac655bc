/**
 * Redis客户端工具
 *
 * 本模块提供Redis连接和操作的工具函数，封装了Redis的常用操作
 * 并提供统一的错误处理和日志记录。
 */

package redis

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	"github.com/redis/go-redis/v9"
)

var (
	// 全局Redis客户端
	client *redis.Client
	// 上下文
	ctx = context.Background()
)

// Init 初始化Redis客户端
func Init() error {
	var (
		host, port, pass, dbStr string
		db                      int
		err                     error
	)

	// 读取配置文件中的Redis配置
	host = web.AppConfig.DefaultString("redishost", "")
	port = web.AppConfig.DefaultString("redisport", "")
	pass = web.AppConfig.DefaultString("redispass", "")
	dbStr = web.AppConfig.DefaultString("redisdb", "0")

	// 将db转为整数
	if dbStr != "" {
		db, err = strconv.Atoi(dbStr)
		if err != nil {
			logs.Error("Redis数据库编号不是有效的整数: %v", err)
			db = 0
		}
	}

	logs.Info("Redis配置: host=[%s], port=[%s], db=[%d]", host, port, db)
	logs.Info("Redis配置键: %s, %s, %s, %s", "redishost", "redisport", "redispass", "redisdb")

	// 构建Redis客户端选项
	opt := &redis.Options{
		Addr:         fmt.Sprintf("%s:%s", host, port),
		Password:     pass,
		DB:           db,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 5,
	}

	// 创建Redis客户端
	client = redis.NewClient(opt)

	// 测试连接
	_, err = client.Ping(ctx).Result()
	if err != nil {
		logs.Error("Redis连接测试失败: %v", err)
		return err
	}

	logs.Info("Redis客户端初始化成功")
	return nil
}

// Close 关闭Redis连接
func Close() error {
	if client != nil {
		return client.Close()
	}
	return nil
}

// Get 获取字符串值
func Get(key string) (string, error) {
	return client.Get(ctx, key).Result()
}

// Set 设置字符串值
func Set(key string, value interface{}, expiration time.Duration) error {
	return client.Set(ctx, key, value, expiration).Err()
}

// Del 删除键
func Del(keys ...string) (int64, error) {
	return client.Del(ctx, keys...).Result()
}

// Exists 检查键是否存在
func Exists(keys ...string) (int64, error) {
	return client.Exists(ctx, keys...).Result()
}

// TTL 获取键的过期时间
func TTL(key string) (time.Duration, error) {
	return client.TTL(ctx, key).Result()
}

// Expire 设置键的过期时间
func Expire(key string, expiration time.Duration) (bool, error) {
	return client.Expire(ctx, key, expiration).Result()
}

// HGet 获取哈希表中的字段值
func HGet(key, field string) (string, error) {
	return client.HGet(ctx, key, field).Result()
}

// HSet 设置哈希表中的字段值
func HSet(key string, values ...interface{}) (int64, error) {
	return client.HSet(ctx, key, values...).Result()
}

// HGetAll 获取哈希表中的所有字段和值
func HGetAll(key string) (map[string]string, error) {
	return client.HGetAll(ctx, key).Result()
}

// HDel 删除哈希表中的字段
func HDel(key string, fields ...string) (int64, error) {
	return client.HDel(ctx, key, fields...).Result()
}

// LPush 将一个或多个值插入到列表头部
func LPush(key string, values ...interface{}) (int64, error) {
	return client.LPush(ctx, key, values...).Result()
}

// RPush 将一个或多个值插入到列表尾部
func RPush(key string, values ...interface{}) (int64, error) {
	return client.RPush(ctx, key, values...).Result()
}

// LPop 移除并返回列表的第一个元素
func LPop(key string) (string, error) {
	return client.LPop(ctx, key).Result()
}

// RPop 移除并返回列表的最后一个元素
func RPop(key string) (string, error) {
	return client.RPop(ctx, key).Result()
}

// LRange 获取列表指定范围内的元素
func LRange(key string, start, stop int64) ([]string, error) {
	return client.LRange(ctx, key, start, stop).Result()
}

// SAdd 将一个或多个成员元素加入到集合中
func SAdd(key string, members ...interface{}) (int64, error) {
	return client.SAdd(ctx, key, members...).Result()
}

// SMembers 返回集合中的所有成员
func SMembers(key string) ([]string, error) {
	return client.SMembers(ctx, key).Result()
}

// SRem 移除集合中一个或多个成员
func SRem(key string, members ...interface{}) (int64, error) {
	return client.SRem(ctx, key, members...).Result()
}

// ZAdd 将一个或多个成员元素及其分数值加入到有序集当中
func ZAdd(key string, members ...redis.Z) (int64, error) {
	return client.ZAdd(ctx, key, members...).Result()
}

// ZRange 返回有序集中指定区间内的成员
func ZRange(key string, start, stop int64) ([]string, error) {
	return client.ZRange(ctx, key, start, stop).Result()
}

// ZRangeWithScores 返回有序集中指定区间内的成员和分数
func ZRangeWithScores(key string, start, stop int64) ([]redis.Z, error) {
	return client.ZRangeWithScores(ctx, key, start, stop).Result()
}

// ZRem 移除有序集中的一个或多个成员
func ZRem(key string, members ...interface{}) (int64, error) {
	return client.ZRem(ctx, key, members...).Result()
}

// Incr 将 key 中储存的数字值增一
func Incr(key string) (int64, error) {
	return client.Incr(ctx, key).Result()
}

// IncrBy 将 key 中储存的数字值增加指定增量值
func IncrBy(key string, value int64) (int64, error) {
	return client.IncrBy(ctx, key, value).Result()
}

// Decr 将 key 中储存的数字值减一
func Decr(key string) (int64, error) {
	return client.Decr(ctx, key).Result()
}

// DecrBy 将 key 中储存的数字值减少指定减量值
func DecrBy(key string, value int64) (int64, error) {
	return client.DecrBy(ctx, key, value).Result()
}

// GetClient 获取原始的Redis客户端
func GetClient() *redis.Client {
	return client
}
