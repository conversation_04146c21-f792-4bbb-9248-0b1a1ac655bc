/**
 * sms.go
 * 短信服务工具
 *
 * 本文件提供短信发送功能，支持多种短信服务提供商。
 * 配置信息从 system 模块的数据库中动态获取。
 */

package sms

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"time"

	"github.com/beego/beego/v2/core/logs"

	sysmodels "o_mall_backend/modules/system/models"
	sysservices "o_mall_backend/modules/system/services"
	sysimpl "o_mall_backend/modules/system/services/impl"
)

// 短信服务提供商常量
const (
	ProviderAliyun  = "aliyun"
	ProviderTencent = "tencent"
	ProviderYunpian = "yunpian"
	ProviderCustom  = "custom"
	ProviderSubmail = "submail"
)

// 短信用途常量
const (
	UsageRegister = "register"
	UsageLogin    = "login"
	UsageResetPwd = "reset_pwd"
	UsageNotice   = "notice" // 通用通知类，对应模型中的 TemplateCodeNotice
)

// SMSProvider 短信提供者接口
type SMSProvider interface {
	// 发送短信
	Send(phone, code, usage string) error
}

// 短信提供者实例
var provider SMSProvider
var smsConfigService sysservices.SmsConfigService // Package-level service instance

var _ sysmodels.SmsConfig // Lint fix: use sysmodels import

// Init 初始化短信服务 (从数据库配置)
// 此函数应在应用启动时调用，并在短信配置变更时可被再次调用以刷新。
func Init() {
	logs.Info("开始初始化短信服务 (使用数据库配置)...")

	if smsConfigService == nil {
		smsConfigService = sysimpl.NewSmsConfigServiceImpl()
	}

	dbConfig, err := smsConfigService.GetSmsConfig(context.Background())
	if err != nil {
		logs.Error("初始化短信服务失败: 获取短信配置错误: %v", err)
		provider = nil // 确保在配置失败时 provider 为 nil
		return
	}

	if dbConfig == nil || dbConfig.Status == 0 { // 检查配置是否存在且已启用
		logs.Warn("初始化短信服务: 短信配置未找到或已禁用。短信功能将不可用。")
		provider = nil // 确保 provider 为 nil
		return
	}

	logs.Info("成功获取短信配置: Provider=%s, AccessKey=%s", dbConfig.Provider, dbConfig.AccessKey)

	switch dbConfig.Provider {
	case ProviderAliyun:
		provider = NewAliyunSMS(
			dbConfig.AccessKey,
			dbConfig.AccessSecret,
			dbConfig.SignName,
			dbConfig.TemplateCodeRegister,
			dbConfig.TemplateCodeLogin,
			dbConfig.TemplateCodeResetPwd,
			dbConfig.TemplateCodeNotice,
		)
		logs.Info("已初始化阿里云短信服务 (使用数据库配置)")
	case ProviderTencent:
		logs.Warn("腾讯云短信服务提供商当前未完全实现。短信功能可能受限。")
		// provider = NewTencentSMS(...) // 实际实现时取消注释并传入配置
		provider = nil // 或设置为一个模拟/空操作的提供者
		logs.Info("已初始化腾讯云短信服务 (使用数据库配置) - 待实现")
	case ProviderYunpian:
		logs.Warn("云片短信服务提供商当前未完全实现。短信功能可能受限。")
		// provider = NewYunpianSMS(...) // 实际实现时取消注释并传入配置
		provider = nil // 或设置为一个模拟/空操作的提供者
		logs.Info("已初始化云片短信服务 (使用数据库配置) - 待实现")
	case ProviderSubmail:
		provider = NewSubmailSMS(
			dbConfig.AccessKey,    // 对应赛邮的 app_id
			dbConfig.AccessSecret, // 对应赛邮的 app_key
			dbConfig.SignName,
			dbConfig.TemplateCodeRegister,
			dbConfig.TemplateCodeLogin,
			dbConfig.TemplateCodeResetPwd,
			dbConfig.TemplateCodeNotice,
		)
		logs.Info("已初始化赛邮短信服务 (使用数据库配置)")
	case ProviderCustom:
		// 当前 sysmodels.SmsConfig 不包含自定义短信所需的 apiURL, apiMethod 等字段。
		// 如果需要通过数据库配置自定义短信，需要扩展 SmsConfig 模型。
		logs.Error("自定义短信服务 (ProviderCustom) 当前的数据库模型不支持其详细配置。短信功能可能受限或不可用。")
		// 可以选择初始化一个具有默认参数或空操作的 CustomSMS 提供者
		// provider = NewCustomSMS("http://default.invalid/api", "POST", "Content-Type:application/json", "{\"phone\":\"{phone}\",\"code\":\"{code}\"}", 200)
		provider = nil
	default:
		logs.Warn("未知的短信服务提供商来自数据库配置: '%s'。短信功能将不可用。", dbConfig.Provider)
		provider = nil
	}

	if provider == nil {
		logs.Error("短信服务提供商 '%s' 未能成功初始化。短信功能将不可用。", dbConfig.Provider)
	} else {
		logs.Info("短信服务提供商 '%s' 初始化完成。", dbConfig.Provider)
	}
}

// ReInitWithConfig 允许外部在配置更改后重新初始化短信服务
// 例如，在 system 模块更新短信配置后调用此函数。
func ReInitWithConfig() {
	logs.Info("接收到重新初始化短信服务的请求...")
	Init()
}

// SendVerificationCode 发送验证码
func SendVerificationCode(phone, code, usage string) error {
	if provider == nil {
		logs.Warn("短信服务提供者未初始化，尝试重新初始化...")
		Init() // 尝试初始化
		if provider == nil { // 再次检查
			return errors.New("短信服务提供者未初始化或初始化失败")
		}
	}
	logs.Info("发送短信验证码，手机号：%s，用途：%s", phone, usage)
	return provider.Send(phone, code, usage)
}

// AliyunSMS 阿里云短信提供者
type AliyunSMS struct {
	accessKey            string
	accessSecret         string
	signName             string
	templateCodeRegister string
	templateCodeLogin    string
	templateCodeResetPwd string
	templateCodeNotice   string
}

// NewAliyunSMS 创建阿里云短信提供者
func NewAliyunSMS(accessKey, accessSecret, signName, tplRegister, tplLogin, tplResetPwd, tplNotice string) *AliyunSMS {
	return &AliyunSMS{
		accessKey:            accessKey,
		accessSecret:         accessSecret,
		signName:             signName,
		templateCodeRegister: tplRegister,
		templateCodeLogin:    tplLogin,
		templateCodeResetPwd: tplResetPwd,
		templateCodeNotice:   tplNotice,
	}
}

// Send 发送阿里云短信
func (as *AliyunSMS) Send(phone, code, usage string) error {
	var templateCode string
	switch usage {
	case UsageRegister:
		templateCode = as.templateCodeRegister
	case UsageLogin:
		templateCode = as.templateCodeLogin
	case UsageResetPwd:
		templateCode = as.templateCodeResetPwd
	case UsageNotice:
		templateCode = as.templateCodeNotice
	default:
		return fmt.Errorf("阿里云短信不支持的用途: %s", usage)
	}

	if templateCode == "" {
		return fmt.Errorf("阿里云短信未配置用途 '%s' 的模板ID", usage)
	}

	// TODO: 在实际项目中实现阿里云短信发送逻辑 (使用阿里云SDK)
	// 此处为模拟发送
	logs.Info("模拟发送阿里云短信，手机号：%s，验证码：%s，模板ID：%s, AccessKey: %s, SignName: %s",
		phone, code, templateCode, as.accessKey, as.signName)
	
	// 示例阿里云SDK调用 (需要引入 github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi 等)
	// client, err := dysmsapi.NewClientWithAccessKey("cn-hangzhou", as.accessKey, as.accessSecret)
	// if err != nil {
	// 	 return fmt.Errorf("创建阿里云客户端失败: %v", err)
	// }
	// request := dysmsapi.CreateSendSmsRequest()
	// request.Scheme = "https"
	// request.PhoneNumbers = phone
	// request.SignName = as.signName
	// request.TemplateCode = templateCode
	// request.TemplateParam = fmt.Sprintf("{\"code\":\"%s\"}", code)
	// response, err := client.SendSms(request)
	// if err != nil {
	// 	 return fmt.Errorf("发送阿里云短信失败: %v", err)
	// }
	// if response.Code != "OK" {
	// 	 return fmt.Errorf("阿里云短信发送返回错误: Code=%s, Message=%s", response.Code, response.Message)
	// }
	// logs.Info("成功发送阿里云短信, RequestId: %s", response.RequestId)

	return nil // 模拟成功
}

// CustomSMS 自定义HTTP接口短信提供者
type CustomSMS struct {
	apiURL       string
	apiMethod    string
	apiHeaders   map[string]string
	bodyTemplate string
	successCode  int
}

// NewCustomSMS 创建自定义短信提供者
func NewCustomSMS(apiURL, apiMethod, headersStr, bodyTemplate string, successCode int) *CustomSMS {
	headers := make(map[string]string)
	if headersStr != "" {
		headerPairs := strings.Split(headersStr, ";")
		for _, pair := range headerPairs {
			kv := strings.SplitN(pair, ":", 2)
			if len(kv) == 2 {
				headers[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
			}
		}
	}
	return &CustomSMS{
		apiURL:       apiURL,
		apiMethod:    apiMethod,
		apiHeaders:   headers,
		bodyTemplate: bodyTemplate,
		successCode:  successCode,
	}
}

// Send 发送自定义短信
func (cs *CustomSMS) Send(phone, code, usage string) error {
	if cs.apiURL == "" || cs.apiURL == "http://default.invalid/api" { // 检查是否为未配置或默认无效配置
		logs.Error("自定义短信服务未正确配置API URL，无法发送短信。")
		return errors.New("自定义短信服务未正确配置API URL")
	}
	body := cs.bodyTemplate
	body = strings.Replace(body, "{phone}", phone, -1)
	body = strings.Replace(body, "{code}", code, -1)
	body = strings.Replace(body, "{type}", usage, -1)

	req, err := http.NewRequest(cs.apiMethod, cs.apiURL, bytes.NewBuffer([]byte(body)))
	if err != nil {
		return fmt.Errorf("创建自定义短信HTTP请求失败：%v", err)
	}
	for key, value := range cs.apiHeaders {
		req.Header.Set(key, value)
	}
	client := &http.Client{Timeout: time.Second * 10}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送自定义短信HTTP请求失败：%v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != cs.successCode {
		respBodyBytes, _ := io.ReadAll(resp.Body)
		logs.Error("自定义短信发送失败，状态码：%d, 响应: %s", resp.StatusCode, string(respBodyBytes))
		return fmt.Errorf("自定义短信发送失败，状态码：%d", resp.StatusCode)
	}
	logs.Info("成功发送自定义短信，手机号：%s，验证码：%s", phone, code)
	return nil
}

// SubmailSMS 赛邮短信提供者
type SubmailSMS struct {
	appID                string // 对应数据库配置中的 AccessKey
	appKey               string // 对应数据库配置中的 AccessSecret
	signName             string // 数据库配置中的 SignName, 赛邮项目通常自带签名
	templateCodeRegister string // 注册项目ID
	templateCodeLogin    string // 登录项目ID
	templateCodeResetPwd string // 重置密码项目ID
	templateCodeNotice   string // 通知项目ID
}

// NewSubmailSMS 创建赛邮短信提供者
func NewSubmailSMS(appID, appKey, signName, tplRegister, tplLogin, tplResetPwd, tplNotice string) *SubmailSMS {
	return &SubmailSMS{
		appID:                appID,
		appKey:               appKey,
		signName:             signName,
		templateCodeRegister: tplRegister,
		templateCodeLogin:    tplLogin,
		templateCodeResetPwd: tplResetPwd,
		templateCodeNotice:   tplNotice,
	}
}

// Send 发送赛邮短信
func (ss *SubmailSMS) Send(phone, code, usage string) error {
	var projectID string
	switch usage {
	case UsageRegister:
		projectID = ss.templateCodeRegister
	case UsageLogin:
		projectID = ss.templateCodeLogin
	case UsageResetPwd:
		projectID = ss.templateCodeResetPwd
	case UsageNotice:
		projectID = ss.templateCodeNotice
	default:
		return fmt.Errorf("赛邮短信不支持的用途: %s", usage)
	}

	if projectID == "" {
		return fmt.Errorf("赛邮短信未配置用途 '%s' 的项目ID", usage)
	}

	vars := map[string]string{"code": code}
	logs.Info("将使用赛邮项目ID '%s' 发送短信到 %s，验证码: %s", projectID, phone, code)
	return ss.sendTemplateSMS(phone, projectID, vars)
}

// sendTemplateSMS 发送赛邮模板短信 (XSend API)
func (ss *SubmailSMS) sendTemplateSMS(phoneNumber, projectID string, vars map[string]string) error {
	appKeyPreview := "****"
	if len(ss.appKey) >= 4 {
		appKeyPreview = ss.appKey[:4] + "****"
	}
	logs.Info("赛邮短信发送配置: AppID=%s, AppKey(Preview)=%s, SignName=%s (SignName from DB, Submail project may override)",
		ss.appID, appKeyPreview, ss.signName)

	apiURL := "https://api-v4.mysubmail.com/sms/xsend"
	data := make(map[string]interface{})
	data["appid"] = ss.appID
	data["to"] = phoneNumber
	data["project"] = projectID
	data["signature"] = ss.appKey // XSend API 使用 appKey 作为 signature 字段的值

	if len(vars) > 0 {
		data["vars"] = vars
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("赛邮请求数据JSON序列化失败: %v", err)
	}
	logs.Info("赛邮短信API请求数据: %s", string(jsonData))

	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建赛邮HTTP请求失败：%v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: time.Second * 10}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送赛邮HTTP请求失败：%v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取赛邮响应体失败：%v", err)
	}

	var smsResp struct {
		Status  string `json:"status"`
		SendID  string `json:"send_id"`
		Fee     int    `json:"fee"`
		Code    int    `json:"code"`    // 错误码 (如果 status 是 error)
		Message string `json:"msg"`     // 错误信息 (如果 status 是 error)
	}
	if err := json.Unmarshal(body, &smsResp); err != nil {
		logs.Error("解析赛邮响应JSON失败: %v, 原始响应: %s", err, string(body))
		return fmt.Errorf("解析赛邮响应JSON失败: %v", err)
	}

	if smsResp.Status == "error" {
		logs.Error("赛邮短信发送失败: Code=%d, Message=%s. 请求数据: %s", smsResp.Code, smsResp.Message, string(jsonData))
		return fmt.Errorf("赛邮短信发送失败: [%d] %s", smsResp.Code, smsResp.Message)
	}

	logs.Info("成功发送赛邮短信，手机号：%s，SendID：%s，项目ID：%s", phoneNumber, smsResp.SendID, projectID)
	return nil
}

// GenerateVerificationCode 生成验证码
func GenerateVerificationCode(length int) string {
	if length <= 0 {
		length = 6
	}
	digits := "0123456789"

	// Go 1.20+ math/rand uses a global random generator that is automatically seeded.
	// For older versions, explicit seeding (e.g., rand.Seed(time.Now().UnixNano())) might be needed once globally.
	// Avoid frequent seeding.

	code := make([]byte, length)
	for i := 0; i < length; i++ {
		code[i] = digits[rand.Intn(len(digits))]
	}
	codeStr := string(code)
	logs.Info("生成随机验证码: %s", codeStr)
	return codeStr
}
