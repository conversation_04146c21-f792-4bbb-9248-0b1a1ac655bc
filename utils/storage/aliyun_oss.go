/**
 * aliyun_oss.go
 * 阿里云OSS存储实现
 *
 * 本文件提供阿里云OSS存储服务的具体实现
 */

package storage

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/beego/beego/v2/core/logs"
)

// AliyunOSS 阿里云OSS存储提供者
type AliyunOSS struct {
	endpoint     string
	bucket       string
	accessKey    string
	accessSecret string
	domain       string
	client       *oss.Client
	bucketObj    *oss.Bucket
}

// NewAliyunOSS 创建阿里云OSS存储提供者
func NewAliyunOSS(endpoint string, bucket string, accessKey string, accessSecret string, domain string) *AliyunOSS {
	ossProvider := &AliyunOSS{
		endpoint:     endpoint,
		bucket:       bucket,
		accessKey:    accessKey,
		accessSecret: accessSecret,
		domain:       domain,
	}
	
	// 初始化OSS客户端
	client, err := oss.New(endpoint, accessKey, accessSecret)
	if err != nil {
		logs.Error("初始化阿里云OSS客户端失败: %v", err)
		return ossProvider
	}
	
	// 获取Bucket对象
	bucketObj, err := client.Bucket(bucket)
	if err != nil {
		logs.Error("获取阿里云OSS Bucket失败: %v", err)
		return ossProvider
	}
	
	ossProvider.client = client
	ossProvider.bucketObj = bucketObj
	return ossProvider
}

// Save 保存文件到阿里云OSS
func (aos *AliyunOSS) Save(file multipart.File, fileName string, fileDir string) (string, error) {
	if aos.bucketObj == nil {
		return "", fmt.Errorf("阿里云OSS未初始化成功")
	}
	
	// 准备OSS上的存储路径
	filePath := filepath.Join(fileDir, fileName)
	
	// 读取文件内容到内存
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("读取文件内容失败: %w", err)
	}

	// 上传到OSS
	err = aos.bucketObj.PutObject(filePath, bytes.NewReader(fileBytes))
	if err != nil {
		return "", fmt.Errorf("上传文件到阿里云OSS失败: %w", err)
	}
	
	logs.Info("文件已上传到阿里云OSS: %s", filePath)
	return filePath, nil
}

// GetURL 获取阿里云OSS文件访问URL
func (aos *AliyunOSS) GetURL(filePath string) string {
	if aos.domain == "" {
		// 使用默认URL
		return fmt.Sprintf("https://%s.%s/%s", aos.bucket, aos.endpoint, filePath)
	}
	
	// 使用自定义域名
	protocol := "https"
	return fmt.Sprintf("%s://%s/%s", protocol, aos.domain, filePath)
}

// Delete 删除阿里云OSS文件
func (aos *AliyunOSS) Delete(filePath string) error {
	if aos.bucketObj == nil {
		return fmt.Errorf("阿里云OSS未初始化成功")
	}
	
	// 执行删除操作
	err := aos.bucketObj.DeleteObject(filePath)
	if err != nil {
		return fmt.Errorf("从阿里云OSS删除文件失败: %w", err)
	}
	
	logs.Info("文件已从阿里云OSS删除: %s", filePath)
	return nil
}
