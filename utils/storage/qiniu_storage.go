/**
 * qiniu_storage.go
 * 七牛云存储实现
 *
 * 本文件提供七牛云存储服务的具体实现
 */

package storage

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"

	"github.com/beego/beego/v2/core/logs"
	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
)

// QiniuStorage 七牛云存储提供者
type QiniuStorage struct {
	accessKey string
	secretKey string
	bucket    string
	domain    string
	zone      string
	useHTTPS  bool
	mac       *qbox.Mac
	config    *storage.Config
}

// 存储区域映射
var zoneMap = map[string]*storage.Region{
	"z0":             &storage.ZoneHuadong,          // 华东
	"z1":             &storage.ZoneHuabei,           // 华北
	"z2":             &storage.ZoneHuanan,           // 华南
	"na0":            &storage.ZoneBeimei,           // 北美
	"as0":            &storage.ZoneXinjiapo,         // 新加坡(东南亚)
	"ap-northeast-1": &storage.ZoneHuadongZheJiang2, // 华东-浙江2
}

// NewQiniuStorage 创建七牛云存储提供者
func NewQiniuStorage(accessKey string, secretKey string, bucket string, domain string, zone string, useHTTPS bool) *QiniuStorage {
	logs.Info("七牛云配置详情 -> AccessKey:%s SecretKey:%s Bucket:%s Domain:%s Zone:%s HTTPS:%v",
		maskSensitive(accessKey), // 敏感信息掩码
		maskSensitive(secretKey),
		bucket,
		domain,
		zone,
		useHTTPS)
	// 创建鉴权对象
	mac := qbox.NewMac(accessKey, secretKey)

	// 获取存储区域配置
	region, ok := zoneMap[zone]
	if !ok {
		logs.Warn("未知的七牛云存储区域: %s，将使用华南区域(z2)", zone)
		region = &storage.ZoneHuanan
	}

	// 配置参数
	cfg := &storage.Config{
		Region:        region,
		UseHTTPS:      useHTTPS,
		UseCdnDomains: false,
	}

	return &QiniuStorage{
		accessKey: accessKey,
		secretKey: secretKey,
		bucket:    bucket,
		domain:    domain,
		zone:      zone,
		useHTTPS:  useHTTPS,
		mac:       mac,
		config:    cfg,
	}
}

// Save 保存文件到七牛云
func (qs *QiniuStorage) Save(file multipart.File, fileName string, fileDir string) (string, error) {
	logs.Info("开始上传文件到七牛云: fileName=%s, fileDir=%s, bucket=%s", fileName, fileDir, qs.bucket)
	
	if qs.mac == nil {
		logs.Error("七牛云存储未初始化成功")
		return "", fmt.Errorf("七牛云存储未初始化成功")
	}

	// 准备上传策略
	putPolicy := storage.PutPolicy{
		Scope: qs.bucket,
	}
	upToken := putPolicy.UploadToken(qs.mac)
	logs.Debug("生成上传凭证成功，Scope: %s", qs.bucket)

	// 创建上传表单对象
	formUploader := storage.NewFormUploader(qs.config)
	ret := storage.PutRet{}

	// 准备七牛云上的存储路径
	filePath := filepath.Join(fileDir, fileName)
	logs.Debug("七牛云存储路径: %s", filePath)

	// 读取文件内容到内存
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		logs.Error("读取文件内容失败: fileName=%s, error=%v", fileName, err)
		return "", fmt.Errorf("读取文件内容失败: %w", err)
	}
	logs.Debug("文件内容读取成功，大小: %d bytes", len(fileBytes))

	// 上传数据
	putExtra := storage.PutExtra{
		Params: map[string]string{
			"x:name": fileName,
		},
	}

	dataLen := int64(len(fileBytes))
	logs.Debug("开始执行七牛云上传，文件大小: %d bytes", dataLen)
	err = formUploader.Put(context.Background(), &ret, upToken, filePath, bytes.NewReader(fileBytes), dataLen, &putExtra)
	if err != nil {
		logs.Error("上传文件到七牛云失败: filePath=%s, fileName=%s, bucket=%s, error=%v", filePath, fileName, qs.bucket, err)
		return "", fmt.Errorf("上传文件到七牛云失败: %w", err)
	}

	logs.Info("文件已成功上传到七牛云: filePath=%s, key=%s, hash=%s", filePath, ret.Key, ret.Hash)
	return filePath, nil
}

// GetURL 获取七牛云文件访问URL
func (qs *QiniuStorage) GetURL(filePath string) string {
	protocol := "http"
	if qs.useHTTPS {
		protocol = "https"
	}

	// 使用自定义域名
	if qs.domain != "" {
		return fmt.Sprintf("%s://%s/%s", protocol, qs.domain, filePath)
	}

	// 若未设置自定义域名，返回错误提示（七牛云必须设置域名才能访问）
	logs.Error("七牛云存储需要设置自定义域名")
	return ""
}

// Delete 删除七牛云文件
func (qs *QiniuStorage) Delete(filePath string) error {
	if qs.mac == nil {
		return fmt.Errorf("七牛云存储未初始化成功")
	}

	// 创建管理操作对象
	bucketManager := storage.NewBucketManager(qs.mac, qs.config)

	// 执行删除操作
	err := bucketManager.Delete(qs.bucket, filePath)
	if err != nil {
		return fmt.Errorf("从七牛云删除文件失败: %w", err)
	}

	logs.Info("文件已从七牛云删除: %s", filePath)
	return nil
}
