/**
 * storage.go
 * 存储工具
 *
 * 本文件提供文件存储和CDN加速的功能，支持本地存储和云存储（OSS、COS、S3、七牛云）
 * 更新：从数据库配置中读取存储配置信息，而非配置文件
 */

package storage

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/system/models"
	"o_mall_backend/modules/system/services"
	"o_mall_backend/modules/system/services/impl"
	"o_mall_backend/utils"
)

// 存储模式常量
const (
	StorageModeLocal = "local"
	StorageModeOSS   = "oss"
	StorageModeCOS   = "cos"
	StorageModeS3    = "s3"
	StorageModeQiniu = "qiniu"
)

// 存储提供者接口
type StorageProvider interface {
	// 保存文件到存储
	Save(file multipart.File, fileName string, fileDir string) (string, error)
	// 获取文件访问URL
	GetURL(filePath string) string
	// 删除文件
	Delete(filePath string) error
}

// 存储提供者实例
// provider is initialized by Init() and used by storage functions.
var provider StorageProvider

// 上传配置服务
var uploadConfigService services.UploadConfigService

// IsProviderInitialized checks if the storage provider has been initialized.
func IsProviderInitialized() bool {
	return provider != nil
}

// 初始化存储提供者
func Init() {
	// 首先直接尝试从静态配置文件加载配置
	// 这样可以确保即使Redis不可用也能初始化存储模块
	logs.Info("先从静态配置初始化存储提供者...")
	initFromStaticConfig()
	
	// 然后尝试从数据库加载配置并重新初始化
	// 如果下面的加载失败，已经通过静态配置初始化了存储提供者
	// 所以不会导致程序崩溃
	go func() {
		// 增加小延时，等待Redis和数据库初始化完成
		time.Sleep(2 * time.Second)
		
		// 初始化上传配置服务
		logs.Info("尝试从数据库加载存储配置...")
		uploadConfigService = impl.NewUploadConfigServiceImpl()
		
		// 使用上下文
		ctx := context.Background()
		
		// 注意：这里使用recover防止可能的panic
		defer func() {
			if r := recover(); r != nil {
				logs.Error("从数据库加载存储配置时发生panic: %v", r)
			}
		}()
		
		// 从上传配置服务获取配置
		config, err := uploadConfigService.GetUploadConfig(ctx)
		if err != nil {
			logs.Error("获取上传配置失败: %v", err)
			return
		}
		
		// 使用获取到的配置重新初始化存储提供者
		logs.Info("从数据库成功加载存储配置，正在重新初始化存储提供者...")
		initWithConfig(config)
	}()
}

// ReInitWithConfig 使用指定的配置重新初始化存储提供者
// 用于配置热更新
func ReInitWithConfig(config *models.UploadConfig) {
	if config == nil {
		logs.Error("重新初始化存储提供者失败: 配置为空")
		return
	}
	
	initWithConfig(config)
	logs.Info("存储提供者已使用新配置重新初始化: 模式=%s", config.StorageMode)
}

// initWithConfig 使用指定的配置初始化存储提供者
func initWithConfig(config *models.UploadConfig) {
	mode := config.StorageMode
	enableCDN := config.EnableCdn == 1
	
	logs.Info("初始化存储提供者，存储模式: %s, CDN启用: %v", mode, enableCDN)
	
	// 根据配置的存储模式初始化不同的存储提供者
	switch mode {
	case StorageModeLocal:
		// 从配置获取上传路径，如果没有则使用默认值
		uploadPath := web.AppConfig.DefaultString("uploadPath", "./uploads")
		provider = NewLocalStorage(uploadPath, enableCDN)
		logs.Info("已初始化本地存储提供者，上传路径：%s", uploadPath)
	
	case StorageModeOSS:
		// 获取阿里云OSS配置
		ossConfig, err := config.GetOssConfig()
		if err != nil {
			logs.Error("获取阿里云OSS配置失败: %v", err)
			return
		}
		
		provider = NewAliyunOSS(
			ossConfig.Endpoint,
			ossConfig.Bucket,
			ossConfig.AccessKey,
			ossConfig.AccessSecret,
			ossConfig.Domain,
		)
		logs.Info("已初始化阿里云OSS存储提供者，Endpoint：%s，Bucket：%s", 
			ossConfig.Endpoint, ossConfig.Bucket)
	
	case StorageModeCOS:
		// 获取腾讯云COS配置
		cosConfig, err := config.GetCosConfig()
		if err != nil {
			logs.Error("获取腾讯云COS配置失败: %v", err)
			return
		}
		
		provider = NewTencentCOS(
			cosConfig.Region,
			cosConfig.Bucket,
			cosConfig.SecretId,
			cosConfig.SecretKey,
			cosConfig.Domain,
		)
		logs.Info("已初始化腾讯云COS存储提供者，Region：%s，Bucket：%s", 
			cosConfig.Region, cosConfig.Bucket)
	
	case StorageModeQiniu:
		// 获取七牛云存储配置
		logs.Info("正在获取七牛云存储配置...")
		qiniuConfig, err := config.GetQiniuConfig()
		if err != nil {
			logs.Error("获取七牛云存储配置失败: %v", err)
			return
		}
		
		// 验证配置完整性
		if qiniuConfig.AccessKey == "" || qiniuConfig.SecretKey == "" || qiniuConfig.Bucket == "" {
			logs.Error("七牛云配置不完整: AccessKey=%s, SecretKey=%s, Bucket=%s", 
				maskSensitive(qiniuConfig.AccessKey), maskSensitive(qiniuConfig.SecretKey), qiniuConfig.Bucket)
			return
		}
		
		logs.Info("七牛云配置验证通过: AccessKey=%s, Bucket=%s, Domain=%s, Zone=%s", 
			maskSensitive(qiniuConfig.AccessKey), qiniuConfig.Bucket, qiniuConfig.Domain, qiniuConfig.Zone)
		
		provider = NewQiniuStorage(
			qiniuConfig.AccessKey,
			qiniuConfig.SecretKey,
			qiniuConfig.Bucket,
			qiniuConfig.Domain,
			qiniuConfig.Zone,
			qiniuConfig.UseHTTPS,
		)
		logs.Info("已初始化七牛云存储提供者，Bucket：%s", qiniuConfig.Bucket)
	
	case StorageModeS3:
		// 获取AWS S3配置
		s3Config, err := config.GetS3Config()
		if err != nil {
			logs.Error("获取AWS S3配置失败: %v", err)
			return
		}
		
		// AWS S3 存储尚未实现
		logs.Error("AWS S3 存储尚未实现，请实现 AWS S3 存储提供者后再使用该模式。")
		// 临时使用本地存储作为替代
		uploadPath := web.AppConfig.DefaultString("uploadPath", "./uploads")
		provider = NewLocalStorage(uploadPath, enableCDN)
		logs.Warn("已临时使用本地存储替代 AWS S3 存储，配置信息: Region=%s, Bucket=%s", 
			s3Config.Region, s3Config.Bucket)
	
	default:
		logs.Error("未知的存储模式: %s, 将使用本地存储作为默认值", mode)
		uploadPath := web.AppConfig.DefaultString("uploadPath", "./uploads")
		provider = NewLocalStorage(uploadPath, enableCDN)
	}
}

// 从静态配置文件初始化（作为回退方案）
func initFromStaticConfig() {
	logs.Info("从静态配置文件初始化存储提供者")
	
	// 获取存储模式和CDN配置（从配置文件）
	mode := web.AppConfig.DefaultString("storage.mode", "local")
	enableCDN, _ := web.AppConfig.Bool("storage.enable_cdn")
	logs.Info("静态配置: 模式=%s, 启用CDN=%v", mode, enableCDN)
	
	// 根据配置的存储模式初始化不同的存储提供者
	switch mode {
	case StorageModeLocal:
		uploadPath := web.AppConfig.DefaultString("uploadPath", "./uploads")
		provider = NewLocalStorage(uploadPath, enableCDN)
		logs.Info("已初始化本地存储提供者，上传路径：%s", uploadPath)
	
	case StorageModeOSS:
		endpoint, _ := web.AppConfig.String("oss.endpoint")
		bucket, _ := web.AppConfig.String("oss.bucket")
		accessKey, _ := web.AppConfig.String("oss.access_key")
		accessSecret, _ := web.AppConfig.String("oss.access_secret")
		domain, _ := web.AppConfig.String("oss.domain")

		provider = NewAliyunOSS(endpoint, bucket, accessKey, accessSecret, domain)
		logs.Info("已初始化阿里云OSS存储提供者，Endpoint：%s，Bucket：%s", endpoint, bucket)
	
	case StorageModeCOS:
		region, _ := web.AppConfig.String("cos.region")
		bucket, _ := web.AppConfig.String("cos.bucket")
		secretID, _ := web.AppConfig.String("cos.secret_id")
		secretKey, _ := web.AppConfig.String("cos.secret_key")
		domain, _ := web.AppConfig.String("cos.domain")

		provider = NewTencentCOS(region, bucket, secretID, secretKey, domain)
		logs.Info("已初始化腾讯云COS存储提供者，Region：%s，Bucket：%s", region, bucket)
	
	case StorageModeQiniu:
		accessKey, _ := web.AppConfig.String("qiniu.access_key")
		secretKey, _ := web.AppConfig.String("qiniu.secret_key")
		bucket, _ := web.AppConfig.String("qiniu.bucket")
		domain, _ := web.AppConfig.String("qiniu.domain")
		zone, _ := web.AppConfig.String("qiniu.zone")
		useHTTPS, _ := web.AppConfig.Bool("qiniu.use_https")

		provider = NewQiniuStorage(accessKey, secretKey, bucket, domain, zone, useHTTPS)
		logs.Info("已初始化七牛云存储提供者，Bucket：%s", bucket)
	
	case StorageModeS3:
		region, _ := web.AppConfig.String("s3.region")
		bucket, _ := web.AppConfig.String("s3.bucket")
		// 读取配置但暂不使用
		_, _ = web.AppConfig.String("s3.access_key")
		_, _ = web.AppConfig.String("s3.secret_key")
		_, _ = web.AppConfig.String("s3.domain")

		// AWS S3 存储尚未实现
		logs.Error("AWS S3 存储尚未实现，将使用本地存储作为替代。")
		uploadPath := web.AppConfig.DefaultString("uploadPath", "./uploads")
		provider = NewLocalStorage(uploadPath, enableCDN)
		logs.Warn("已临时使用本地存储替代 AWS S3 存储，配置信息: Region=%s, Bucket=%s", region, bucket)
	
	default:
		logs.Error("未知的存储模式: %s", mode)
		uploadPath := web.AppConfig.DefaultString("uploadPath", "./uploads")
		provider = NewLocalStorage(uploadPath, enableCDN)
	}
}

// SaveFile 保存文件到存储系统
// file: 文件内容
// fileExt: 文件扩展名（包含.，如 .jpg）
// dir: 存储子目录
// 返回值: 文件存储路径, 错误信息
func SaveFile(file io.Reader, fileExt string, dir string) (string, error) {
	if provider == nil {
		logs.Error("存储提供者未初始化")
		Init() // 尝试初始化
	}
	
	if provider == nil {
		return "", fmt.Errorf("存储提供者初始化失败")
	}
	
	// 生成唯一文件名
	fileName := generateFileName(fileExt)
	
	// 创建临时文件
	tempFile, err := os.CreateTemp("", "upload_*"+fileExt)
	if err != nil {
		logs.Error("创建临时文件失败: %v", err)
		return "", err
	}
	defer os.Remove(tempFile.Name()) // 确保函数退出时删除临时文件
	defer tempFile.Close()
	
	// 将文件内容写入临时文件
	_, err = io.Copy(tempFile, file)
	if err != nil {
		logs.Error("写入临时文件失败: %v", err)
		return "", err
	}
	
	// 将文件指针移到开始位置
	_, err = tempFile.Seek(0, 0)
	if err != nil {
		logs.Error("重置文件指针失败: %v", err)
		return "", err
	}
	
	// 保存文件到存储
	return provider.Save(tempFile, fileName, dir)
}

// GetFileURL 获取文件URL
// filePath: 文件存储路径
// 返回值: 文件访问URL
func GetFileURL(filePath string) string {
	if provider == nil {
		logs.Error("存储提供者未初始化")
		Init() // 尝试初始化
	}
	
	// 使用存储提供者获取URL
	return provider.GetURL(filePath)
}

// DeleteFile 删除存储的文件
// filePath: 文件存储路径
// 返回值: 错误信息
func DeleteFile(filePath string) error {
	if provider == nil {
		logs.Error("存储提供者未初始化")
		Init() // 尝试初始化
	}
	
	// 使用存储提供者删除文件
	return provider.Delete(filePath)
}

// 生成唯一文件名
func generateFileName(fileExt string) string {
	// 使用当前时间戳和随机字符串生成唯一文件名
	timestamp := time.Now().UnixNano()
	randomStr := utils.RandString(8)
	return fmt.Sprintf("%d_%s%s", timestamp, randomStr, fileExt)
}

// Save 保存文件到存储系统
// file: multipart.File类型的文件
// fileName: 文件名
// dir: 存储子目录
// 返回值: 文件存储路径, 错误信息
func Save(file multipart.File, fileName string, dir string) (string, error) {
	if provider == nil {
		logs.Error("存储提供者未初始化")
		Init() // 尝试初始化
	}
	
	// 使用存储提供者保存文件
	return provider.Save(file, fileName, dir)
}

// SaveFromLocal 保存本地文件到存储系统
// filePath: 本地文件路径
// dir: 存储子目录
// 返回值: 文件存储路径, 错误信息
func SaveFromLocal(filePath string, dir string) (string, error) {
	if provider == nil {
		logs.Error("存储提供者未初始化")
		Init() // 尝试初始化
	}
	
	// 打开本地文件
	file, err := os.Open(filePath)
	if err != nil {
		logs.Error("打开本地文件失败: %v", err)
		return "", err
	}
	defer file.Close()
	
	// 获取文件名
	fileName := filepath.Base(filePath)
	
	// 使用存储提供者保存文件
	return provider.Save(file, fileName, dir)
}

// LocalStorage 本地存储提供者
type LocalStorage struct {
	uploadPath string
	enableCDN  bool
	cdnDomain  string
}

// NewLocalStorage 创建本地存储提供者
func NewLocalStorage(uploadPath string, enableCDN bool) *LocalStorage {
	// 从配置或环境中获取CDN域名
	cdnDomain := ""
	if enableCDN {
		// 尝试从上下文获取配置
		if uploadConfigService != nil {
			config, err := uploadConfigService.GetUploadConfig(context.Background())
			if err == nil && config.EnableCdn == 1 {
				cdnDomain = config.CdnDomain
			}
		}
		// 如果未能从配置获取，尝试从配置文件获取
		if cdnDomain == "" {
			cdnDomain, _ = web.AppConfig.String("storage.cdn_domain")
		}
	}
	
	return &LocalStorage{
		uploadPath: uploadPath,
		enableCDN:  enableCDN,
		cdnDomain:  cdnDomain,
	}
}

// Save 保存文件到本地存储
func (ls *LocalStorage) Save(file multipart.File, fileName string, fileDir string) (string, error) {
	// 创建目录（如果不存在）
	fullDir := filepath.Join(ls.uploadPath, fileDir)
	if err := os.MkdirAll(fullDir, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败：%v", err)
	}

	// 创建目标文件
	filePath := filepath.Join(fullDir, fileName)
	dst, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败：%v", err)
	}
	defer dst.Close()

	// 复制文件内容
	if _, err = io.Copy(dst, file); err != nil {
		return "", fmt.Errorf("保存文件内容失败：%v", err)
	}

	// 返回相对路径
	return filepath.Join(fileDir, fileName), nil
}

// GetURL 获取本地文件访问URL
func (ls *LocalStorage) GetURL(filePath string) string {
	// 如果启用CDN，使用CDN域名构建URL
	if ls.enableCDN && ls.cdnDomain != "" {
		// 确保路径以斜杠开头
		if !strings.HasPrefix(filePath, "/") {
			filePath = "/" + filePath
		}

		// 构建CDN URL
		return fmt.Sprintf("https://%s%s", ls.cdnDomain, filePath)
	}

	// 未启用CDN，使用相对路径
	return "/" + filepath.Join("uploads", filePath)
}

// Delete 删除本地文件
func (ls *LocalStorage) Delete(filePath string) error {
	// 构建完整的文件路径
	fullPath := filepath.Join(ls.uploadPath, filePath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在：%s", fullPath)
	}

	// 删除文件
	return os.Remove(fullPath)
}

// 阿里云OSS存储提供者在aliyun_oss.go中实现

// 腾讯云COS存储提供者在tencent_cos.go中实现

// 七牛云存储提供者在qiniu_storage.go中实现

// AWS S3存储提供者尚未实现

// 掩码敏感信息，用于日志记录
func maskSensitive(s string) string {
	if len(s) > 4 {
		return s[:2] + "****" + s[len(s)-2:]
	}
	return "****"
}
