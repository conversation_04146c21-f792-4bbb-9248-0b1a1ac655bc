/**
 * tencent_cos.go
 * 腾讯云COS存储实现
 *
 * 本文件提供腾讯云COS存储服务的具体实现
 */

package storage

import (
	"context"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/url"
	"path/filepath"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// TencentCOS 腾讯云COS存储提供者
type TencentCOS struct {
	region    string
	bucket    string
	secretID  string
	secretKey string
	domain    string
	client    *cos.Client
}

// NewTencentCOS 创建腾讯云COS存储提供者
func NewTencentCOS(region string, bucket string, secretID string, secretKey string, domain string) *TencentCOS {
	cosProvider := &TencentCOS{
		region:    region,
		bucket:    bucket,
		secretID:  secretID,
		secretKey: secretKey,
		domain:    domain,
	}

	// 初始化COS客户端
	u, _ := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", bucket, region))
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  secretID,
			SecretKey: secretKey,
		},
		Timeout: 30 * time.Second,
	})

	cosProvider.client = client
	logs.Info("腾讯云COS客户端初始化成功")

	return cosProvider
}

// Save 保存文件到腾讯云COS
func (tcos *TencentCOS) Save(file multipart.File, fileName string, fileDir string) (string, error) {
	if tcos.client == nil {
		return "", fmt.Errorf("腾讯云COS未初始化成功")
	}

	// 准备COS上的存储路径
	filePath := filepath.Join(fileDir, fileName)

	// 上传到COS
	_, err := tcos.client.Object.Put(context.Background(), filePath, file, nil)
	if err != nil {
		return "", fmt.Errorf("上传文件到腾讯云COS失败: %w", err)
	}

	logs.Info("文件已上传到腾讯云COS: %s", filePath)
	return filePath, nil
}

// GetURL 获取腾讯云COS文件访问URL
func (tcos *TencentCOS) GetURL(filePath string) string {
	if tcos.domain == "" {
		// 使用默认URL
		return fmt.Sprintf("https://%s.cos.%s.myqcloud.com/%s", tcos.bucket, tcos.region, filePath)
	}

	// 使用自定义域名
	protocol := "https"
	return fmt.Sprintf("%s://%s/%s", protocol, tcos.domain, filePath)
}

// Delete 删除腾讯云COS文件
func (tcos *TencentCOS) Delete(filePath string) error {
	if tcos.client == nil {
		return fmt.Errorf("腾讯云COS未初始化成功")
	}

	// 执行删除操作
	_, err := tcos.client.Object.Delete(context.Background(), filePath)
	if err != nil {
		return fmt.Errorf("从腾讯云COS删除文件失败: %w", err)
	}

	logs.Info("文件已从腾讯云COS删除: %s", filePath)
	return nil
}
