/**
 * Token存储模块
 *
 * 该文件提供Token存储功能，主要用于管理refresh token和token黑名单。
 * 使用Redis作为存储，支持token的存储、验证、撤销等功能。
 */

package utils

import (
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/utils/redis"
)

const (
	// RefreshTokenKeyPrefix refresh token的Redis键前缀
	RefreshTokenKeyPrefix = "refresh_token:"
	// TokenBlacklistKeyPrefix token黑名单的Redis键前缀
	TokenBlacklistKeyPrefix = "token_blacklist:"
	// RefreshTokenExpiration refresh token的过期时间（30天）
	RefreshTokenExpiration = 30 * 24 * time.Hour
	// BlacklistTokenExpiration 黑名单token的过期时间（比access token的有效期略长）
	BlacklistTokenExpiration = 30 * time.Minute
)

// Token存储相关的错误定义
var (
	ErrTokenNotFound     = errors.New("token不存在")
	ErrTokenInBlacklist  = errors.New("token已被撤销")
	ErrStoreTokenFailed  = errors.New("存储token失败")
	ErrRevokeTokenFailed = errors.New("撤销token失败")
)

// StoreRefreshToken 存储refresh token
// 参数:
//   - userID: 用户ID
//   - refreshToken: 刷新令牌
//
// 返回:
//   - error: 错误信息
func StoreRefreshToken(userID int64, refreshToken string) error {
	key := fmt.Sprintf("%s%d", RefreshTokenKeyPrefix, userID)
	err := redis.Set(key, refreshToken, RefreshTokenExpiration)
	if err != nil {
		logs.Error("存储refresh token失败: %v", err)
		return ErrStoreTokenFailed
	}
	return nil
}

// GetRefreshToken 获取用户的refresh token
// 参数:
//   - userID: 用户ID
//
// 返回:
//   - string: 刷新令牌
//   - error: 错误信息
func GetRefreshToken(userID int64) (string, error) {
	key := fmt.Sprintf("%s%d", RefreshTokenKeyPrefix, userID)
	token, err := redis.Get(key)
	if err != nil {
		logs.Error("获取refresh token失败: %v", err)
		return "", ErrTokenNotFound
	}
	return token, nil
}

// DeleteRefreshToken 删除用户的refresh token
// 参数:
//   - userID: 用户ID
//
// 返回:
//   - error: 错误信息
func DeleteRefreshToken(userID int64) error {
	key := fmt.Sprintf("%s%d", RefreshTokenKeyPrefix, userID)
	_, err := redis.Del(key)
	if err != nil {
		logs.Error("删除refresh token失败: %v", err)
		return err
	}
	return nil
}

// RevokeAccessToken 将access token加入黑名单
// 参数:
//   - token: 访问令牌
//   - expiration: 黑名单过期时间
//
// 返回:
//   - error: 错误信息
func RevokeAccessToken(token string, expiration time.Duration) error {
	key := fmt.Sprintf("%s%s", TokenBlacklistKeyPrefix, token)
	err := redis.Set(key, "1", expiration)
	if err != nil {
		logs.Error("将token加入黑名单失败: %v", err)
		return ErrRevokeTokenFailed
	}
	return nil
}

// IsTokenRevoked 检查token是否在黑名单中
// 参数:
//   - token: 访问令牌
//
// 返回:
//   - bool: 是否在黑名单中
//   - error: 错误信息
func IsTokenRevoked(token string) (bool, error) {
	key := fmt.Sprintf("%s%s", TokenBlacklistKeyPrefix, token)
	_, err := redis.Get(key)
	if err != nil {
		// 如果是key不存在的错误，则表示token不在黑名单中
		if err.Error() == "redis: nil" {
			return false, nil
		}
		logs.Error("检查token是否在黑名单中失败: %v", err)
		return false, err
	}
	return true, nil
}

// RefreshUserTokens 刷新用户的token对
// 参数:
//   - refreshToken: 用户提供的刷新令牌
//
// 返回:
//   - *TokenPair: 新的token对
//   - error: 错误信息
func RefreshUserTokens(refreshToken string) (*TokenPair, error) {
	// 1. 验证refresh token
	claims, err := ParseTokenWithType(refreshToken, TokenTypeRefresh)
	if err != nil {
		logs.Error("验证refresh token失败: %v", err)
		return nil, err
	}

	// 2. 验证token是否在存储中
	storedToken, err := GetRefreshToken(claims.UserID)
	if err != nil {
		logs.Error("获取存储的refresh token失败: %v", err)
		return nil, err
	}

	// 3. 验证token是否一致
	if refreshToken != storedToken {
		logs.Error("提供的refresh token与存储的不一致")
		return nil, ErrTokenInvalid
	}

	// 4. 生成新的token对
	tokenPair, err := GenerateTokenPair(claims.UserID, claims.Username, claims.Role)
	if err != nil {
		logs.Error("生成新的token对失败: %v", err)
		return nil, err
	}

	// 5. 更新refresh token存储
	err = StoreRefreshToken(claims.UserID, tokenPair.RefreshToken)
	if err != nil {
		logs.Error("更新refresh token存储失败: %v", err)
		return nil, err
	}

	return tokenPair, nil
}

// RevokeAllUserTokens 撤销用户的所有token
// 参数:
//   - userID: 用户ID
//   - currentAccessToken: 当前的访问令牌（将被加入黑名单）
//
// 返回:
//   - error: 错误信息
func RevokeAllUserTokens(userID int64, currentAccessToken string) error {
	// 1. 获取当前token的过期时间
	claims, err := ParseToken(currentAccessToken)
	if err != nil {
		logs.Error("解析access token失败: %v", err)
		// 继续执行，因为我们仍然需要删除refresh token
	}

	// 2. 加入黑名单
	if claims != nil {
		// 计算token的剩余时间
		expiresAt := time.Unix(claims.ExpiresAt, 0)
		now := time.Now()
		remainingTime := expiresAt.Sub(now)
		if remainingTime > 0 {
			err = RevokeAccessToken(currentAccessToken, remainingTime)
			if err != nil {
				logs.Error("将access token加入黑名单失败: %v", err)
				// 继续执行，因为我们仍然需要删除refresh token
			}
		}
	} else {
		// 如果无法解析，使用默认的过期时间
		err = RevokeAccessToken(currentAccessToken, BlacklistTokenExpiration)
		if err != nil {
			logs.Error("将access token加入黑名单失败: %v", err)
		}
	}

	// 3. 删除refresh token
	err = DeleteRefreshToken(userID)
	if err != nil {
		logs.Error("删除refresh token失败: %v", err)
		return err
	}

	return nil
}
