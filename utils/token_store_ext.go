/**
 * Token存储扩展模块
 *
 * 该文件提供Token存储的扩展功能，包括验证刷新令牌、检查刷新令牌、删除所有刷新令牌等功能。
 * 配合token_store.go使用，为RefreshToken和Logout功能提供支持。
 */

package utils

import (
	"github.com/beego/beego/v2/core/logs"
)

// ValidateRefreshToken 验证刷新令牌
// 参数:
//   - refreshToken: 刷新令牌
//
// 返回:
//   - *TokenClaims: 令牌中的声明信息
//   - error: 错误信息
func ValidateRefreshToken(refreshToken string) (*TokenClaims, error) {
	// 验证刷新令牌
	claims, err := ParseTokenWithType(refreshToken, TokenTypeRefresh)
	if err != nil {
		logs.Error("验证刷新令牌失败: %v", err)
		return nil, err
	}
	return claims, nil
}

// CheckRefreshToken 检查刷新令牌是否存在于存储中
// 参数:
//   - userID: 用户ID
//   - refreshToken: 刷新令牌
//
// 返回:
//   - bool: 是否有效
//   - error: 错误信息
func CheckRefreshToken(userID int64, refreshToken string) (bool, error) {
	// 获取存储的刷新令牌
	storedToken, err := GetRefreshToken(userID)
	if err != nil {
		logs.Error("获取存储的刷新令牌失败: %v", err)
		return false, err
	}

	// 验证令牌是否一致
	if refreshToken != storedToken {
		logs.Error("提供的刷新令牌与存储的不一致")
		return false, nil
	}

	return true, nil
}

// DeleteAllRefreshTokens 删除用户的所有刷新令牌
// 参数:
//   - userID: 用户ID
//
// 返回:
//   - error: 错误信息
func DeleteAllRefreshTokens(userID int64) error {
	// 目前实现只需要删除用户的刷新令牌
	return DeleteRefreshToken(userID)
}
