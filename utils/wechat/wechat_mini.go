/**
 * 微信小程序工具
 *
 * 该文件提供微信小程序登录相关的工具函数，包括code2session、加密数据解析等功能。
 */

package wechat

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
)

// 微信小程序接口相关常量
const (
	// Code2SessionURL 微信小程序登录凭证校验接口
	Code2SessionURL = "https://api.weixin.qq.com/sns/jscode2session"
	// 请求超时时间
	requestTimeout = 5 * time.Second
)

// MiniappConfig 微信小程序配置结构
type MiniappConfig struct {
	Enabled   bool
	AppID     string
	AppSecret string
}

// SessionResponse 微信code2session接口返回结构
type SessionResponse struct {
	OpenID     string `json:"openid"`      // 用户唯一标识
	SessionKey string `json:"session_key"` // 会话密钥
	UnionID    string `json:"unionid"`     // 用户在开放平台的唯一标识
	ErrCode    int    `json:"errcode"`     // 错误码
	ErrMsg     string `json:"errmsg"`      // 错误信息
}

// WechatMiniApp 微信小程序服务
type WechatMiniApp struct {
	config *MiniappConfig
}

// NewWechatMiniApp 创建微信小程序服务实例
func NewWechatMiniApp() (*WechatMiniApp, error) {
	// 从配置文件加载配置
	var config MiniappConfig

	// 读取配置
	enabled, err := web.AppConfig.Bool("wechat.miniapp.enabled")
	if err != nil {
		logs.Error("读取微信小程序配置失败: %v", err)
		return nil, errors.New("微信小程序配置不存在或格式错误")
	}
	config.Enabled = enabled

	// 如果不启用，直接返回
	if !config.Enabled {
		return &WechatMiniApp{config: &config}, nil
	}

	// 读取AppID和AppSecret
	appID, err := web.AppConfig.String("wechat.miniapp.app_id")
	if err != nil {
		logs.Error("读取微信小程序AppID失败: %v", err)
		return nil, errors.New("微信小程序AppID不存在")
	}
	config.AppID = appID

	appSecret, err := web.AppConfig.String("wechat.miniapp.app_secret")
	if err != nil {
		logs.Error("读取微信小程序AppSecret失败: %v", err)
		return nil, errors.New("微信小程序AppSecret不存在")
	}
	config.AppSecret = appSecret

	return &WechatMiniApp{config: &config}, nil
}

// Code2Session 使用code换取用户的session信息
func (w *WechatMiniApp) Code2Session(code string) (*SessionResponse, error) {
	if !w.config.Enabled {
		return nil, errors.New("微信小程序功能未启用")
	}

	if code == "" {
		return nil, errors.New("code不能为空")
	}

	// 构建请求URL
	reqURL, err := url.Parse(Code2SessionURL)
	if err != nil {
		return nil, err
	}

	// 添加查询参数
	query := reqURL.Query()
	query.Add("appid", w.config.AppID)
	query.Add("secret", w.config.AppSecret)
	query.Add("js_code", code)
	query.Add("grant_type", "authorization_code")
	reqURL.RawQuery = query.Encode()

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: requestTimeout,
	}

	// 发送请求
	resp, err := client.Get(reqURL.String())
	if err != nil {
		logs.Error("请求微信code2session接口失败: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取微信code2session响应内容失败: %v", err)
		return nil, err
	}

	// 解析JSON响应
	var sessionResp SessionResponse
	if err := json.Unmarshal(body, &sessionResp); err != nil {
		logs.Error("解析微信code2session响应内容失败: %v, raw: %s", err, string(body))
		return nil, err
	}

	// 检查响应是否成功
	if sessionResp.ErrCode != 0 {
		logs.Error("微信code2session接口返回错误: code=%d, msg=%s", sessionResp.ErrCode, sessionResp.ErrMsg)
		return nil, fmt.Errorf("微信接口错误: %s (错误码: %d)", sessionResp.ErrMsg, sessionResp.ErrCode)
	}

	return &sessionResp, nil
}

// IsEnabled 判断微信小程序功能是否启用
func (w *WechatMiniApp) IsEnabled() bool {
	return w.config != nil && w.config.Enabled
}
