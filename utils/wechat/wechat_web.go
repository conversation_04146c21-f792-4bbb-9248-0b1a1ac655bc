/**
 * 微信网页授权工具
 *
 * 该文件提供微信网页授权相关的工具函数，包括网页授权、获取用户信息等功能。
 */

package wechat

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"
	
	"o_mall_backend/modules/user/dto"
)

// 微信网页授权接口相关常量
const (
	// QrConnectURL 微信扫码登录授权URL
	QrConnectURL = "https://open.weixin.qq.com/connect/qrconnect"
	// AccessTokenURL 获取access_token的URL
	AccessTokenURL = "https://api.weixin.qq.com/sns/oauth2/access_token"
	// RefreshTokenURL 刷新access_token的URL
	RefreshTokenURL = "https://api.weixin.qq.com/sns/oauth2/refresh_token"
	// UserInfoURL 获取用户信息的URL
	UserInfoURL = "https://api.weixin.qq.com/sns/userinfo"
	// 请求超时时间
	webRequestTimeout = 10 * time.Second
)

// WebConfig 微信网页授权配置结构
type WebConfig struct {
	Enabled    bool
	AppID      string
	AppSecret  string
	RedirectURI string
	Scope      string
}

// AccessTokenResponse 微信access_token接口返回结构
type AccessTokenResponse struct {
	AccessToken  string `json:"access_token"`  // 接口调用凭证
	ExpiresIn    int    `json:"expires_in"`    // access_token接口调用凭证超时时间，单位（秒）
	RefreshToken string `json:"refresh_token"` // 用户刷新access_token
	OpenID       string `json:"openid"`        // 用户唯一标识
	Scope        string `json:"scope"`         // 用户授权的作用域，使用逗号分隔
	UnionID      string `json:"unionid"`       // 用户在开放平台的唯一标识符（如果当前应用已绑定到微信开放平台账号）
	ErrCode      int    `json:"errcode"`       // 错误码
	ErrMsg       string `json:"errmsg"`        // 错误信息
}

// WechatWeb 微信网页授权服务
type WechatWeb struct {
	config *WebConfig
}

// NewWechatWeb 创建微信网页授权服务实例
func NewWechatWeb() (*WechatWeb, error) {
	// 从配置文件加载配置
	var config WebConfig

	// 读取配置
	enabled, err := web.AppConfig.Bool("wechat.web.enabled")
	if err != nil {
		logs.Error("读取微信网页授权配置失败: %v", err)
		return nil, errors.New("微信网页授权配置不存在或格式错误")
	}
	config.Enabled = enabled

	// 如果不启用，直接返回
	if !config.Enabled {
		return &WechatWeb{config: &config}, nil
	}

	// 读取AppID和AppSecret
	appID, err := web.AppConfig.String("wechat.web.app_id")
	if err != nil {
		logs.Error("读取微信网页授权AppID失败: %v", err)
		return nil, errors.New("微信网页授权AppID不存在")
	}
	config.AppID = appID

	appSecret, err := web.AppConfig.String("wechat.web.app_secret")
	if err != nil {
		logs.Error("读取微信网页授权AppSecret失败: %v", err)
		return nil, errors.New("微信网页授权AppSecret不存在")
	}
	config.AppSecret = appSecret

	// 读取RedirectURI
	redirectURI, err := web.AppConfig.String("wechat.web.redirect_uri")
	if err != nil {
		logs.Error("读取微信网页授权RedirectURI失败: %v", err)
		return nil, errors.New("微信网页授权RedirectURI不存在")
	}
	config.RedirectURI = redirectURI

	// 读取授权作用域，默认为snsapi_login
	scope, err := web.AppConfig.String("wechat.web.scope")
	if err != nil {
		logs.Info("未配置微信网页授权Scope，使用默认值snsapi_login")
		scope = "snsapi_login"
	}
	config.Scope = scope

	return &WechatWeb{config: &config}, nil
}

// IsEnabled 判断微信网页授权功能是否启用
func (w *WechatWeb) IsEnabled() bool {
	return w.config != nil && w.config.Enabled
}

// GetAuthorizeURL 获取微信授权URL
func (w *WechatWeb) GetAuthorizeURL(state string, redirectURI string) (string, error) {
	if !w.config.Enabled {
		return "", errors.New("微信网页授权功能未启用")
	}

	if state == "" {
		return "", errors.New("state参数不能为空")
	}

	// 使用传入的redirectURI，如果为空则使用配置中的
	finalRedirectURI := redirectURI
	if finalRedirectURI == "" {
		finalRedirectURI = w.config.RedirectURI
	}

	// 构建请求URL
	reqURL, err := url.Parse(QrConnectURL)
	if err != nil {
		return "", err
	}

	// 添加查询参数
	query := reqURL.Query()
	query.Add("appid", w.config.AppID)
	query.Add("redirect_uri", finalRedirectURI)
	query.Add("response_type", "code")
	query.Add("scope", w.config.Scope)
	query.Add("state", state)
	reqURL.RawQuery = query.Encode()

	return reqURL.String() + "#wechat_redirect", nil
}

// GetAccessToken 使用授权码获取access_token
func (w *WechatWeb) GetAccessToken(code string) (*AccessTokenResponse, error) {
	if !w.config.Enabled {
		return nil, errors.New("微信网页授权功能未启用")
	}

	if code == "" {
		return nil, errors.New("授权码不能为空")
	}

	// 构建请求URL
	reqURL, err := url.Parse(AccessTokenURL)
	if err != nil {
		return nil, err
	}

	// 添加查询参数
	query := reqURL.Query()
	query.Add("appid", w.config.AppID)
	query.Add("secret", w.config.AppSecret)
	query.Add("code", code)
	query.Add("grant_type", "authorization_code")
	reqURL.RawQuery = query.Encode()

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: webRequestTimeout,
	}

	// 发送请求
	resp, err := client.Get(reqURL.String())
	if err != nil {
		logs.Error("请求微信access_token接口失败: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取微信access_token响应内容失败: %v", err)
		return nil, err
	}

	// 解析JSON响应
	var tokenResp AccessTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		logs.Error("解析微信access_token响应内容失败: %v, raw: %s", err, string(body))
		return nil, err
	}

	// 检查响应是否成功
	if tokenResp.ErrCode != 0 {
		logs.Error("微信access_token接口返回错误: code=%d, msg=%s", tokenResp.ErrCode, tokenResp.ErrMsg)
		return nil, fmt.Errorf("微信接口错误: %s (错误码: %d)", tokenResp.ErrMsg, tokenResp.ErrCode)
	}

	return &tokenResp, nil
}

// GetUserInfo 获取微信用户信息
func (w *WechatWeb) GetUserInfo(accessToken, openID string) (*dto.WechatWebUserInfo, error) {
	if !w.config.Enabled {
		return nil, errors.New("微信网页授权功能未启用")
	}

	if accessToken == "" || openID == "" {
		return nil, errors.New("access_token和openid不能为空")
	}

	// 构建请求URL
	reqURL, err := url.Parse(UserInfoURL)
	if err != nil {
		return nil, err
	}

	// 添加查询参数
	query := reqURL.Query()
	query.Add("access_token", accessToken)
	query.Add("openid", openID)
	query.Add("lang", "zh_CN")
	reqURL.RawQuery = query.Encode()

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: webRequestTimeout,
	}

	// 发送请求
	resp, err := client.Get(reqURL.String())
	if err != nil {
		logs.Error("请求微信userinfo接口失败: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.Error("读取微信userinfo响应内容失败: %v", err)
		return nil, err
	}

	// 解析JSON响应
	var userInfo dto.WechatWebUserInfo
	if err := json.Unmarshal(body, &userInfo); err != nil {
		logs.Error("解析微信userinfo响应内容失败: %v, raw: %s", err, string(body))
		return nil, err
	}

	// 检查是否有错误
	if userInfo.OpenID == "" {
		var errResp struct {
			ErrCode int    `json:"errcode"`
			ErrMsg  string `json:"errmsg"`
		}
		if err := json.Unmarshal(body, &errResp); err == nil && errResp.ErrCode != 0 {
			logs.Error("微信userinfo接口返回错误: code=%d, msg=%s", errResp.ErrCode, errResp.ErrMsg)
			return nil, fmt.Errorf("微信接口错误: %s (错误码: %d)", errResp.ErrMsg, errResp.ErrCode)
		}
		return nil, errors.New("获取微信用户信息失败")
	}

	return &userInfo, nil
}
