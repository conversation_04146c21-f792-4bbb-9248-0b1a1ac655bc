# 前端优惠券功能详细分析文档

## 📋 目录
1. [功能概述](#功能概述)
2. [系统架构](#系统架构)
3. [核心功能模块](#核心功能模块)
4. [技术实现](#技术实现)
5. [用户体验设计](#用户体验设计)
6. [数据流转](#数据流转)
7. [性能优化](#性能优化)
8. [问题与改进建议](#问题与改进建议)

## 🎯 功能概述

前端优惠券系统是一个完整的优惠券管理和使用平台，为用户提供优惠券的浏览、领取、管理和使用功能。系统采用Vue 3 + TypeScript + Pinia的技术栈，结合uni-app框架实现跨平台兼容。

### 主要功能特性
- ✅ **优惠券中心** - 浏览和领取可用优惠券
- ✅ **我的优惠券** - 管理个人优惠券，支持状态筛选
- ✅ **优惠券详情** - 查看优惠券详细信息和使用规则
- ✅ **即将过期提醒** - 智能提醒即将过期的优惠券
- ✅ **使用记录** - 查看优惠券使用历史
- ✅ **智能推荐** - 根据订单推荐最优优惠券
- ✅ **实时验证** - 订单结算时实时验证优惠券可用性

## 🏗️ 系统架构

### 技术栈
```
前端框架: Vue 3 + TypeScript
状态管理: Pinia
UI组件: Wot Design Uni
跨平台: uni-app
构建工具: Vite
```

### 目录结构
```
src/
├── api/
│   ├── coupon.ts              # 优惠券API接口
│   └── coupon.typings.ts      # 优惠券类型定义
├── components/
│   └── coupon/
│       ├── CouponCard.vue     # 优惠券卡片组件
│       └── CouponSelector.vue # 优惠券选择器
├── pages/
│   └── coupon/
│       ├── center.vue         # 优惠券中心
│       ├── my-coupons.vue     # 我的优惠券
│       ├── detail.vue         # 优惠券详情
│       ├── expiring-soon.vue  # 即将过期
│       └── usage-history.vue  # 使用记录
├── store/
│   └── coupon.ts              # 优惠券状态管理
└── utils/
    └── coupon.ts              # 优惠券工具函数
```

## 🔧 核心功能模块

### 1. 优惠券中心 (center.vue)

**功能特点:**
- 📊 **统计展示** - 显示可领取、已拥有、即将过期的优惠券数量
- 🎯 **分类筛选** - 支持按状态和分类筛选优惠券
- 🔍 **搜索功能** - 支持关键词搜索优惠券
- 🎨 **横幅展示** - 支持活动横幅轮播
- ♾️ **无限滚动** - 支持分页加载和触底刷新

**核心代码片段:**
```vue
<!-- 统计信息卡片 -->
<view class="stats-cards">
  <view class="stats-card">
    <text class="stats-number">{{ totalCoupons }}</text>
    <text class="stats-label">可领取</text>
  </view>
  <view class="stats-card" @click="goToMyCoupons">
    <text class="stats-number">{{ myCouponCount }}</text>
    <text class="stats-label">我的优惠券</text>
  </view>
</view>

<!-- 状态筛选 -->
<view class="status-filter">
  <view
    v-for="tab in statusTabs"
    :key="tab.value"
    class="filter-tab"
    :class="{ active: selectedStatus === tab.value }"
    @click="selectStatus(tab.value)"
  >
    <text>{{ tab.label }}</text>
  </view>
</view>
```

### 2. 我的优惠券 (my-coupons.vue)

**功能特点:**
- 📈 **数据统计** - 展示未使用、已使用、已节省金额等统计信息
- 🚀 **快捷操作** - 提供优惠券中心、即将过期、使用记录的快捷入口
- 🏷️ **状态筛选** - 支持全部、未使用、已使用、已过期状态筛选
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **实时更新** - 支持下拉刷新和上拉加载

**核心实现:**
```typescript
// 状态选项
const statusTabs = computed(() => [
  {
    value: null,
    label: '全部',
    count: couponStore.myCoupons?.length || 0,
  },
  {
    value: CouponStatus.UNUSED,
    label: '未使用',
    count: couponStore.unusedCoupons?.length || 0,
  },
  {
    value: CouponStatus.USED,
    label: '已使用',
    count: couponStore.usedCoupons?.length || 0,
  },
  {
    value: CouponStatus.EXPIRED,
    label: '已过期',
    count: couponStore.expiredCoupons?.length || 0,
  },
])

// 筛选优惠券
const coupons = computed(() => {
  if (selectedStatus.value === null) {
    return couponStore.myCoupons || []
  }
  return couponStore.myCoupons?.filter((coupon) => 
    coupon.status === selectedStatus.value
  ) || []
})
```

### 3. 优惠券卡片组件 (CouponCard.vue)

**设计特点:**
- 🎨 **视觉设计** - 采用卡片式设计，支持多种优惠券类型的视觉区分
- 🏷️ **智能标签** - 自动显示"新人专享"、"热门"、"限量"、"即将过期"等标签
- 📊 **进度显示** - 限量券显示领取进度条
- 🎯 **状态适配** - 根据优惠券状态显示不同的操作按钮
- 🏪 **商家信息** - 显示商家Logo和名称

**类型支持:**
```typescript
enum CouponType {
  DISCOUNT = 1,      // 满减券 - 橙色主题
  PERCENTAGE = 2,    // 折扣券 - 绿色主题  
  FREE_DELIVERY = 3, // 免配送费券 - 蓝色主题
  GIFT = 4,          // 赠品券 - 粉色主题
  CASHBACK = 5,      // 返现券 - 紫色主题
}
```

**样式系统:**
```scss
.coupon-card {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &.can-claim {
    border: 2px solid #ff5500;
  }
  
  &.used, &.expired {
    opacity: 0.6;
    background: #f5f5f5;
  }
  
  .coupon-amount {
    &.discount {
      background: linear-gradient(135deg, #ff5500, #ff7700);
    }
    &.percentage {
      background: linear-gradient(135deg, #00c851, #00a843);
    }
  }
}
```

## 📊 数据流转

### API接口设计

**1. 获取我的优惠券**
```typescript
export const getMyCoupons = (params: {
  status?: number // 1:未使用 2:已使用 3:已过期
  merchant_id?: number
  page?: number
  page_size?: number
}) => {
  return http.get<ICouponListResponse>('/api/v1/user/takeout/coupons/my-list', params)
}
```

**2. 获取优惠券中心**
```typescript
export const getCouponCenter = (params: {
  category?: string
  page?: number
  page_size?: number
}) => {
  return http.get<ICouponCenterResponse>('/api/v1/user/takeout/coupons/center', params)
}
```

**3. 领取优惠券**
```typescript
export const claimCoupon = (data: { coupon_id: number }) => {
  return http.post<ICouponClaimResponse>('/api/v1/user/takeout/coupons/claim', data)
}
```

### 状态管理 (Pinia Store)

**状态结构:**
```typescript
interface CouponState {
  myCoupons: IUserCoupon[]           // 我的优惠券
  centerCoupons: ICoupon[]           // 优惠券中心
  availableCoupons: IUserCoupon[]    // 可用优惠券
  selectedCoupons: Record<number, IUserCoupon | null> // 选中的优惠券
  couponStats: ICouponStats | null   // 优惠券统计
  loading: {
    myCoupons: boolean
    centerCoupons: boolean
    claiming: boolean
  }
  pagination: {
    myCoupons: PaginationInfo
    centerCoupons: PaginationInfo
  }
}
```

**核心Actions:**
```typescript
// 加载我的优惠券
async fetchMyCoupons(params: {
  status?: CouponStatus
  refresh?: boolean
}) {
  // 实现分页加载逻辑
  // 更新统计信息
  // 错误处理
}

// 领取优惠券
async claimCoupon(couponId: number) {
  // 调用API
  // 更新本地状态
  // 显示成功提示
  // 刷新优惠券列表
}
```

## 🎨 用户体验设计

### 1. 交互设计

**加载状态:**
- 骨架屏加载效果
- 分页加载指示器
- 下拉刷新动画

**空状态处理:**
```vue
<view v-else-if="coupons.length === 0" class="empty-state">
  <wd-icon name="coupon" size="60" color="#ddd" />
  <text class="empty-text">{{ getEmptyText() }}</text>
  <view class="empty-actions">
    <view class="action-btn" @click="goToCouponCenter">
      <text>去领券</text>
    </view>
  </view>
</view>
```

**错误处理:**
- 网络错误提示
- API错误处理
- 用户友好的错误信息

### 2. 视觉设计

**颜色系统:**
```scss
:root {
  --coupon-primary: #ff5500;
  --coupon-success: #00c851;
  --coupon-info: #007bff;
  --coupon-warning: #ffa500;
  --coupon-danger: #f44336;
}
```

**动画效果:**
```scss
.coupon-card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}
```

## 🚀 性能优化

### 1. 数据优化
- **分页加载** - 避免一次性加载大量数据
- **缓存策略** - 合理缓存API响应数据
- **防抖处理** - 搜索输入防抖优化

### 2. 渲染优化
- **虚拟滚动** - 大列表使用虚拟滚动
- **图片懒加载** - 商家Logo懒加载
- **组件复用** - CouponCard组件高度复用

### 3. 网络优化
- **请求合并** - 批量操作API
- **错误重试** - 网络错误自动重试
- **离线缓存** - 关键数据离线缓存

## 🔧 工具函数

### 优惠券工具类 (utils/coupon.ts)

**核心功能:**
```typescript
// 格式化优惠券金额
export const formatCouponAmount = (coupon: ICoupon | IUserCoupon) => {
  const actualCoupon = 'coupon' in coupon ? coupon.coupon : coupon
  const { type, amount } = actualCoupon
  
  switch (type) {
    case CouponType.DISCOUNT:
      return `¥${amount}`
    case CouponType.PERCENTAGE:
      return `${amount * 10}折`
    case CouponType.FREE_DELIVERY:
      return '免配送费'
    default:
      return `¥${amount}`
  }
}

// 计算优惠券折扣金额
export const calculateCouponDiscount = (
  coupon: ICoupon | IUserCoupon,
  orderAmount: number,
): number => {
  // 实现折扣计算逻辑
}

// 检查优惠券是否可用
export const isCouponAvailable = (
  coupon: ICoupon | IUserCoupon,
  orderAmount: number,
  merchantId?: number,
  productIds?: number[],
): { available: boolean; reason?: string } => {
  // 实现可用性检查逻辑
}
```

## 📱 响应式设计

### 移动端适配
- **触摸友好** - 按钮大小符合移动端点击标准
- **滑动操作** - 支持左右滑动切换状态
- **底部安全区** - 适配iPhone底部安全区域

### 多端兼容
- **H5端** - 支持浏览器环境
- **小程序** - 支持微信小程序
- **App端** - 支持原生App

## 🔍 问题与改进建议

### 当前问题
1. **性能问题** - 大量优惠券时渲染性能有待优化
2. **缓存策略** - 缺少智能缓存更新机制
3. **错误处理** - 部分边界情况处理不够完善

### 改进建议
1. **引入虚拟滚动** - 优化长列表性能
2. **完善缓存策略** - 实现智能缓存失效机制
3. **增强错误处理** - 提供更友好的错误提示
4. **添加单元测试** - 提高代码质量和稳定性
5. **优化动画效果** - 提升用户体验

## 📈 数据统计

### 关键指标
- **优惠券领取率** - 用户领取优惠券的比例
- **优惠券使用率** - 已领取优惠券的使用比例
- **用户活跃度** - 优惠券功能的使用频率
- **转化效果** - 优惠券对订单转化的影响

### 埋点设计
```typescript
// 优惠券领取埋点
trackEvent('coupon_claim', {
  coupon_id: couponId,
  coupon_type: couponType,
  source: 'coupon_center'
})

// 优惠券使用埋点
trackEvent('coupon_use', {
  coupon_id: couponId,
  order_amount: orderAmount,
  discount_amount: discountAmount
})
```

## 🔄 业务流程详解

### 1. 优惠券领取流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant S as 后端服务
    participant D as 数据库

    U->>F: 点击领取优惠券
    F->>F: 显示加载状态
    F->>S: POST /api/v1/user/takeout/coupons/claim
    S->>D: 检查优惠券状态
    S->>D: 检查用户领取限制
    S->>D: 创建用户优惠券记录
    S->>F: 返回领取结果
    F->>F: 更新本地状态
    F->>U: 显示成功提示
    F->>F: 刷新优惠券列表
```

### 2. 优惠券使用流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant S as 后端服务
    participant O as 订单系统

    U->>F: 选择优惠券
    F->>S: 验证优惠券可用性
    S->>F: 返回验证结果
    F->>U: 显示优惠金额
    U->>F: 确认下单
    F->>O: 提交订单(包含优惠券ID)
    O->>S: 使用优惠券
    S->>F: 返回使用结果
    F->>U: 显示订单成功
```

## 📋 详细功能清单

### 优惠券中心功能
- [x] **优惠券列表展示**
  - 支持分页加载
  - 支持下拉刷新
  - 支持触底加载更多
- [x] **优惠券筛选**
  - 按状态筛选（可领取/已领取）
  - 按分类筛选
  - 关键词搜索
- [x] **优惠券领取**
  - 单个领取
  - 批量领取
  - 领取状态实时更新
- [x] **统计信息展示**
  - 可领取数量
  - 我的优惠券数量
  - 即将过期数量

### 我的优惠券功能
- [x] **优惠券管理**
  - 按状态分类显示
  - 支持状态筛选
  - 支持搜索功能
- [x] **统计数据展示**
  - 未使用数量
  - 已使用数量
  - 总节省金额
  - 即将过期数量
- [x] **快捷操作**
  - 跳转优惠券中心
  - 查看即将过期
  - 查看使用记录
- [x] **优惠券使用**
  - 点击跳转到适用页面
  - 智能推荐使用场景

### 优惠券详情功能
- [x] **详细信息展示**
  - 优惠券基本信息
  - 使用规则说明
  - 适用商家/商品
  - 有效期信息
- [x] **使用指引**
  - 使用步骤说明
  - 注意事项提醒
  - 常见问题解答

## 🎯 核心组件详解

### CouponCard 组件

**Props 接口:**
```typescript
interface Props {
  coupon: ICoupon | IUserCoupon    // 优惠券数据
  canClaim?: boolean               // 是否可领取
  canUse?: boolean                 // 是否可使用
  disabledReason?: string          // 不可用原因
  showTags?: boolean               // 是否显示标签
  showProgress?: boolean           // 是否显示进度条
  mode?: 'claim' | 'use' | 'select' | 'view' // 显示模式
}
```

**Events 接口:**
```typescript
interface Emits {
  (e: 'click', coupon: ICoupon | IUserCoupon): void
  (e: 'claim', coupon: ICoupon | IUserCoupon): void
  (e: 'use', coupon: ICoupon | IUserCoupon): void
}
```

**核心计算属性:**
```typescript
// 格式化金额显示
const formatAmount = computed(() => {
  const { type, amount } = actualCoupon.value
  switch (type) {
    case CouponType.DISCOUNT:
      return amount.toString()
    case CouponType.PERCENTAGE:
      return (amount * 10).toString()
    case CouponType.FREE_DELIVERY:
      return '免费'
    default:
      return amount.toString()
  }
})

// 过期时间文本
const expireText = computed(() => {
  const expireTime = userCoupon.value?.coupon?.end_time || actualCoupon.value.end_time
  if (!expireTime) return ''

  const date = new Date(expireTime)
  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return '已过期'
  if (diffDays === 0) return '今日过期'
  if (diffDays <= 3) return `${diffDays}天后过期`
  return `有效期至${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
})
```

### CouponSelector 组件

**功能特点:**
- 支持单选/多选模式
- 智能推荐最优优惠券
- 实时计算优惠金额
- 支持优惠券组合使用

## 🔧 技术实现细节

### 1. 状态管理优化

**Pinia Store 设计模式:**
```typescript
export const useCouponStore = defineStore('coupon', {
  state: (): CouponState => ({
    // 状态定义
  }),

  getters: {
    // 计算属性，支持缓存
    unusedCoupons: (state) => {
      return state.myCoupons?.filter(coupon =>
        coupon.status === CouponStatus.UNUSED
      ) || []
    },

    // 按商家分组
    couponsByMerchant: (state) => {
      const groups: Record<number, IUserCoupon[]> = {}
      state.myCoupons?.forEach((coupon) => {
        const merchantId = coupon.coupon.merchant_id || 0
        if (!groups[merchantId]) groups[merchantId] = []
        groups[merchantId].push(coupon)
      })
      return groups
    }
  },

  actions: {
    // 异步操作，支持错误处理
    async fetchMyCoupons(params) {
      try {
        this.loading.myCoupons = true
        const response = await getMyCoupons(params)
        // 处理响应数据
        this.updateCouponStats()
      } catch (error) {
        console.error('加载失败:', error)
        throw error
      } finally {
        this.loading.myCoupons = false
      }
    }
  }
})
```

### 2. API 请求封装

**HTTP 拦截器:**
```typescript
// 请求拦截器
http.interceptors.request.use((config) => {
  // 添加认证头
  const token = getToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 统一处理响应格式
    if (response.data.code !== 0) {
      throw new Error(response.data.message)
    }
    return response.data
  },
  (error) => {
    // 统一错误处理
    handleApiError(error)
    return Promise.reject(error)
  }
)
```

### 3. 类型安全保障

**完整的 TypeScript 类型定义:**
```typescript
// 基础优惠券接口
export interface ICoupon {
  id: number
  name: string
  description: string
  type: CouponType
  amount: number
  min_order_amount: number
  max_discount_amount?: number
  merchant_id?: number
  merchant_name?: string
  merchant_logo?: string
  start_time: string
  end_time: string
  total_quantity: number
  claimed_quantity: number
  per_user_limit: number
  is_active: boolean
}

// 用户优惠券接口
export interface IUserCoupon {
  id: number
  user_id: number
  coupon_id: number
  coupon: ICoupon
  status: CouponStatus
  claimed_at: string
  used_at?: string
  expire_time: string
  order_id?: number
  can_use: boolean
  reason?: string
  discount_amount?: number
  days_to_expire?: number
  is_new?: boolean
  is_read?: boolean
}
```

## 🎨 UI/UX 设计规范

### 1. 设计系统

**颜色规范:**
```scss
// 主色调
$primary-color: #ff5500;
$primary-light: #ff7700;
$primary-dark: #cc4400;

// 功能色
$success-color: #00c851;
$warning-color: #ffa500;
$danger-color: #f44336;
$info-color: #007bff;

// 中性色
$text-primary: #333333;
$text-secondary: #666666;
$text-placeholder: #999999;
$border-color: #e0e0e0;
$background-color: #f5f5f5;
```

**字体规范:**
```scss
// 字体大小
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 字体权重
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
```

**间距规范:**
```scss
// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;
```

### 2. 组件设计原则

**一致性原则:**
- 统一的视觉风格
- 一致的交互行为
- 标准化的组件接口

**可访问性原则:**
- 合适的对比度
- 清晰的焦点状态
- 语义化的HTML结构

**响应式原则:**
- 移动优先设计
- 弹性布局系统
- 适配多种屏幕尺寸

## 📊 性能监控与优化

### 1. 性能指标

**关键性能指标:**
- **首屏加载时间** - 页面首次渲染时间
- **列表滚动性能** - 长列表滚动帧率
- **API响应时间** - 接口请求响应时间
- **内存使用情况** - 页面内存占用

**监控实现:**
```typescript
// 性能监控
const performanceObserver = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    if (entry.entryType === 'navigation') {
      console.log('页面加载时间:', entry.loadEventEnd - entry.loadEventStart)
    }
  })
})

performanceObserver.observe({ entryTypes: ['navigation'] })
```

### 2. 优化策略

**代码分割:**
```typescript
// 路由懒加载
const CouponCenter = () => import('@/pages/coupon/center.vue')
const MyCoupons = () => import('@/pages/coupon/my-coupons.vue')
```

**图片优化:**
```vue
<!-- 图片懒加载 -->
<image
  :src="merchantLogo"
  class="merchant-logo"
  mode="aspectFill"
  lazy-load
  @error="handleImageError"
/>
```

**缓存策略:**
```typescript
// 内存缓存
const cache = new Map()

const getCachedData = (key: string) => {
  const cached = cache.get(key)
  if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
    return cached.data
  }
  return null
}
```

## 🧪 测试策略

### 1. 单元测试

**组件测试示例:**
```typescript
import { mount } from '@vue/test-utils'
import CouponCard from '@/components/coupon/CouponCard.vue'
import { CouponType, CouponStatus } from '@/api/coupon.typings'

describe('CouponCard', () => {
  const mockCoupon = {
    id: 1,
    name: '满50减10优惠券',
    type: CouponType.DISCOUNT,
    amount: 10,
    min_order_amount: 50,
    start_time: '2024-01-01',
    end_time: '2024-12-31'
  }

  it('应该正确渲染优惠券信息', () => {
    const wrapper = mount(CouponCard, {
      props: { coupon: mockCoupon }
    })

    expect(wrapper.find('.coupon-name').text()).toBe('满50减10优惠券')
    expect(wrapper.find('.amount').text()).toBe('10')
    expect(wrapper.find('.coupon-desc').text()).toBe('满¥50减¥10')
  })

  it('应该在点击时触发事件', async () => {
    const wrapper = mount(CouponCard, {
      props: { coupon: mockCoupon }
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
    expect(wrapper.emitted('click')[0]).toEqual([mockCoupon])
  })
})
```

**Store 测试示例:**
```typescript
import { setActivePinia, createPinia } from 'pinia'
import { useCouponStore } from '@/store/coupon'

describe('CouponStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('应该正确初始化状态', () => {
    const store = useCouponStore()
    expect(store.myCoupons).toEqual([])
    expect(store.loading.myCoupons).toBe(false)
  })

  it('应该正确筛选未使用的优惠券', () => {
    const store = useCouponStore()
    store.myCoupons = [
      { id: 1, status: CouponStatus.UNUSED },
      { id: 2, status: CouponStatus.USED },
      { id: 3, status: CouponStatus.UNUSED }
    ]

    expect(store.unusedCoupons).toHaveLength(2)
    expect(store.unusedCoupons[0].id).toBe(1)
    expect(store.unusedCoupons[1].id).toBe(3)
  })
})
```

### 2. 集成测试

**API 集成测试:**
```typescript
import { getMyCoupons, claimCoupon } from '@/api/coupon'

describe('Coupon API', () => {
  it('应该能够获取我的优惠券列表', async () => {
    const response = await getMyCoupons({ page: 1, page_size: 10 })

    expect(response.code).toBe(0)
    expect(response.data).toHaveProperty('list')
    expect(response.data).toHaveProperty('total')
    expect(Array.isArray(response.data.list)).toBe(true)
  })

  it('应该能够领取优惠券', async () => {
    const response = await claimCoupon({ coupon_id: 1 })

    expect(response.code).toBe(0)
    expect(response.data).toHaveProperty('id')
    expect(response.data).toHaveProperty('coupon_id')
  })
})
```

### 3. E2E 测试

**端到端测试场景:**
```typescript
describe('优惠券功能 E2E 测试', () => {
  it('用户应该能够完整地领取和使用优惠券', () => {
    // 访问优惠券中心
    cy.visit('/pages/coupon/center')

    // 领取优惠券
    cy.get('.coupon-card').first().find('.claim-btn').click()
    cy.get('.uni-toast').should('contain', '领取成功')

    // 查看我的优惠券
    cy.get('.my-coupons-btn').click()
    cy.url().should('include', '/pages/coupon/my-coupons')

    // 验证优惠券出现在列表中
    cy.get('.coupon-items').should('contain', '满50减10优惠券')

    // 使用优惠券
    cy.get('.coupon-card').first().click()
    cy.get('.use-btn').click()

    // 验证跳转到使用页面
    cy.url().should('include', '/pages/takeout/merchant-detail')
  })
})
```

## 🔒 安全考虑

### 1. 前端安全

**输入验证:**
```typescript
// 搜索关键词验证
const validateSearchKeyword = (keyword: string) => {
  // 防止XSS攻击
  const sanitized = keyword.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  // 长度限制
  return sanitized.slice(0, 50)
}

// 优惠券ID验证
const validateCouponId = (id: any): number => {
  const numId = Number(id)
  if (!Number.isInteger(numId) || numId <= 0) {
    throw new Error('无效的优惠券ID')
  }
  return numId
}
```

**敏感信息保护:**
```typescript
// 不在前端存储敏感信息
const sanitizeCouponData = (coupon: any) => {
  const { internal_notes, admin_remarks, ...publicData } = coupon
  return publicData
}
```

### 2. API 安全

**请求签名:**
```typescript
// API 请求签名
const generateSignature = (params: any, timestamp: number, nonce: string) => {
  const sortedParams = Object.keys(params).sort().map(key => `${key}=${params[key]}`).join('&')
  const signString = `${sortedParams}&timestamp=${timestamp}&nonce=${nonce}&key=${API_SECRET}`
  return crypto.createHash('md5').update(signString).digest('hex')
}
```

**防重放攻击:**
```typescript
// 添加时间戳和随机数
const addSecurityHeaders = (config: any) => {
  const timestamp = Date.now()
  const nonce = Math.random().toString(36).substr(2, 15)

  config.headers['X-Timestamp'] = timestamp
  config.headers['X-Nonce'] = nonce
  config.headers['X-Signature'] = generateSignature(config.data, timestamp, nonce)

  return config
}
```

## 📱 多端适配

### 1. 响应式布局

**断点系统:**
```scss
// 断点定义
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}

// 使用示例
.coupon-card {
  width: 100%;

  @include respond-to(md) {
    width: 48%;
  }

  @include respond-to(lg) {
    width: 32%;
  }
}
```

### 2. 平台差异处理

**条件编译:**
```vue
<template>
  <view class="coupon-center">
    <!-- #ifdef H5 -->
    <view class="h5-header">
      <text class="page-title">优惠券中心</text>
    </view>
    <!-- #endif -->

    <!-- #ifdef MP-WEIXIN -->
    <view class="mp-header">
      <text class="page-title">优惠券中心</text>
    </view>
    <!-- #endif -->

    <!-- 通用内容 -->
    <view class="coupon-list">
      <!-- 优惠券列表 -->
    </view>
  </view>
</template>
```

**平台特定样式:**
```scss
.coupon-card {
  /* #ifdef H5 */
  cursor: pointer;
  &:hover {
    transform: translateY(-2px);
  }
  /* #endif */

  /* #ifdef MP-WEIXIN */
  &:active {
    opacity: 0.8;
  }
  /* #endif */
}
```

## 🚀 部署与发布

### 1. 构建配置

**Vite 配置优化:**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'coupon-vendor': ['@/api/coupon', '@/store/coupon', '@/utils/coupon'],
          'coupon-components': ['@/components/coupon/CouponCard.vue', '@/components/coupon/CouponSelector.vue']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  optimizeDeps: {
    include: ['pinia', '@vueuse/core']
  }
})
```

### 2. 环境配置

**多环境配置:**
```typescript
// config/index.ts
const config = {
  development: {
    apiBaseUrl: 'http://localhost:8080',
    enableMock: true,
    logLevel: 'debug'
  },
  staging: {
    apiBaseUrl: 'https://staging-api.example.com',
    enableMock: false,
    logLevel: 'info'
  },
  production: {
    apiBaseUrl: 'https://api.example.com',
    enableMock: false,
    logLevel: 'error'
  }
}

export default config[process.env.NODE_ENV || 'development']
```

### 3. 监控与日志

**错误监控:**
```typescript
// 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('Vue Error:', error)
  console.error('Component:', instance)
  console.error('Info:', info)

  // 发送错误报告
  reportError({
    error: error.message,
    stack: error.stack,
    component: instance?.$options.name,
    info
  })
}

// API 错误监控
const reportApiError = (error: any, config: any) => {
  const errorInfo = {
    url: config.url,
    method: config.method,
    status: error.response?.status,
    message: error.message,
    timestamp: Date.now()
  }

  // 发送到监控系统
  sendToMonitoring('api_error', errorInfo)
}
```

## 📚 开发规范

### 1. 代码规范

**ESLint 配置:**
```json
{
  "extends": [
    "@vue/typescript/recommended",
    "@vue/prettier",
    "@vue/prettier/@typescript-eslint"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "vue/component-name-in-template-casing": ["error", "PascalCase"],
    "vue/no-unused-vars": "error"
  }
}
```

**Prettier 配置:**
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100,
  "endOfLine": "lf"
}
```

### 2. Git 工作流

**提交信息规范:**
```
feat(coupon): 添加优惠券批量领取功能
fix(coupon): 修复优惠券过期时间显示错误
docs(coupon): 更新优惠券API文档
style(coupon): 优化优惠券卡片样式
refactor(coupon): 重构优惠券状态管理逻辑
test(coupon): 添加优惠券组件单元测试
```

**分支管理:**
```
main          # 主分支，用于生产环境
develop       # 开发分支，用于集成测试
feature/*     # 功能分支，如 feature/coupon-batch-claim
hotfix/*      # 热修复分支，如 hotfix/coupon-expire-bug
release/*     # 发布分支，如 release/v1.2.0
```

---

*本文档详细分析了前端优惠券功能的各个方面，为开发团队提供了全面的技术参考和改进方向。通过遵循本文档的指导原则和最佳实践，可以构建出高质量、可维护、用户体验优秀的优惠券系统。*
