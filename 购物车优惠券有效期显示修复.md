# 🕒 购物车优惠券有效期显示修复

## 🔍 问题对比分析

### 现象对比
| 页面 | 有效期显示 | 状态 |
|------|------------|------|
| **我的优惠券页面** | ✅ 正常显示："有效期至2024-12-31" | 正常 |
| **购物车优惠券弹窗** | ❌ 显示："nan-nan" | 异常 |

### 根本原因分析

#### 1. 组件使用差异
**我的优惠券页面**:
```vue
<CouponCard
  v-for="coupon in coupons"
  :key="coupon.id"
  :coupon="coupon"
  :can-use="coupon.can_use"
  mode="use"
/>
```
- ✅ 使用完整的CouponCard组件
- ✅ CouponCard内部有完善的时间处理逻辑

**购物车优惠券弹窗**:
```vue
<text class="expire-time">有效期至: {{ formatDate(coupon.expire_time) }}</text>
```
- ❌ 自定义简单的时间显示
- ❌ formatDate方法过于简单，没有处理边界情况

#### 2. 时间字段获取差异

**CouponCard的时间获取逻辑**:
```typescript
// 多层级获取，更加健壮
const expireTime = userCoupon.value?.expire_time || 
                   userCoupon.value?.coupon?.end_time || 
                   actualCoupon.value.end_time
```

**CouponSelector的原始逻辑**:
```typescript
// 单一字段获取，容易出错
formatDate(coupon.expire_time)
```

#### 3. 数据处理差异

**CouponCard的处理**:
- ✅ 多种时间格式支持
- ✅ 无效日期检查
- ✅ 相对时间显示（"3天后过期"）
- ✅ 详细的错误处理

**CouponSelector的原始处理**:
- ❌ 简单的Date构造
- ❌ 没有无效日期检查
- ❌ 没有错误处理

## 🛠️ 修复方案

### 方案1: 统一使用CouponCard组件（推荐）
将CouponSelector中的自定义优惠券卡片替换为CouponCard组件，保持一致性。

### 方案2: 完善CouponSelector的时间处理逻辑（已实施）
在CouponSelector中实现与CouponCard相同的时间处理逻辑。

## 🔧 具体修复实施

### 修复1: 替换时间显示方法

**修复前**:
```vue
<text class="expire-time">有效期至: {{ formatDate(coupon.expire_time) }}</text>
```

**修复后**:
```vue
<text class="expire-time">{{ getExpireText(coupon) }}</text>
```

### 修复2: 实现完善的时间处理逻辑

**新增getExpireText方法**:
```typescript
const getExpireText = (coupon: any) => {
  // 优先使用用户优惠券的过期时间，如果没有则使用优惠券模板的结束时间
  const expireTime = coupon.expire_time || coupon.coupon?.end_time
  
  console.log('🕒 CouponSelector 时间调试:', {
    coupon,
    expireTime,
    couponExpireTime: coupon.expire_time,
    couponEndTime: coupon.coupon?.end_time
  })
  
  if (!expireTime) {
    console.warn('⚠️ CouponSelector 没有过期时间')
    return '时间未知'
  }

  // 处理不同的时间格式
  let date: Date
  if (typeof expireTime === 'string') {
    date = new Date(expireTime)
  } else if (typeof expireTime === 'number') {
    // 处理时间戳，检查是秒还是毫秒
    date = expireTime > 1000000000000 ? new Date(expireTime) : new Date(expireTime * 1000)
  } else {
    date = new Date(expireTime)
  }
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.error('❌ CouponSelector 无效的日期格式:', expireTime)
    return '时间格式错误'
  }
  
  const now = new Date()
  const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return '已过期'
  } else if (diffDays === 0) {
    return '今日过期'
  } else if (diffDays <= 3) {
    return `${diffDays}天后过期`
  } else {
    return `有效期至${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }
}
```

### 修复特性

#### ✅ 多层级时间字段获取
```typescript
const expireTime = coupon.expire_time || coupon.coupon?.end_time
```
- 优先使用`coupon.expire_time`
- 备用`coupon.coupon?.end_time`
- 与CouponCard保持一致

#### ✅ 多种时间格式支持
```typescript
if (typeof expireTime === 'string') {
  date = new Date(expireTime)
} else if (typeof expireTime === 'number') {
  date = expireTime > 1000000000000 ? new Date(expireTime) : new Date(expireTime * 1000)
}
```
- 支持字符串格式
- 支持时间戳格式（秒级和毫秒级）

#### ✅ 无效日期检查
```typescript
if (isNaN(date.getTime())) {
  return '时间格式错误'
}
```
- 检查日期有效性
- 返回友好的错误信息

#### ✅ 相对时间显示
```typescript
if (diffDays < 0) {
  return '已过期'
} else if (diffDays === 0) {
  return '今日过期'
} else if (diffDays <= 3) {
  return `${diffDays}天后过期`
} else {
  return `有效期至${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}
```
- 过期状态："已过期"
- 当天过期："今日过期"
- 即将过期："3天后过期"
- 正常状态："有效期至2024-12-31"

#### ✅ 详细调试日志
```typescript
console.log('🕒 CouponSelector 时间调试:', {
  coupon,
  expireTime,
  couponExpireTime: coupon.expire_time,
  couponEndTime: coupon.coupon?.end_time
})
```
- 完整的数据结构日志
- 时间字段获取过程
- 计算结果验证

## 🧪 测试验证

### 测试步骤
1. **访问购物车页面**: `http://localhost:9002/h5/#/pages/cart/index`
2. **添加商品到购物车**
3. **点击"选择优惠券"**打开弹窗
4. **查看优惠券有效期显示**

### 预期结果
- ✅ 不再显示"nan-nan"
- ✅ 显示正确的有效期格式
- ✅ 与"我的优惠券"页面显示一致
- ✅ 支持相对时间显示

### 调试验证
**在浏览器控制台查看**:
```javascript
🕒 CouponSelector 时间调试: {
  coupon: { expire_time: "2024-12-31T23:59:59Z", ... },
  expireTime: "2024-12-31T23:59:59Z",
  couponExpireTime: "2024-12-31T23:59:59Z",
  couponEndTime: "2024-12-31T23:59:59Z"
}

📅 CouponSelector 时间计算结果: {
  expireTime: "2024-12-31T23:59:59Z",
  date: "2024-12-31T23:59:59.000Z",
  diffDays: 45
}
```

## ✅ 修复效果

### 修复前
- ❌ 购物车优惠券弹窗显示："有效期至: nan-nan"
- ❌ 用户无法了解优惠券的实际有效期
- ❌ 影响用户选择优惠券的决策

### 修复后
- ✅ 购物车优惠券弹窗正确显示："有效期至2024-12-31"
- ✅ 支持相对时间显示："3天后过期"
- ✅ 与"我的优惠券"页面保持一致的显示效果
- ✅ 完善的错误处理和调试支持

---

*通过以上修复，购物车中的优惠券有效期显示问题已经解决，现在与"我的优惠券"页面保持一致的显示效果。*
